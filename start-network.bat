@echo off
REM SmartPOS Network Startup Script for Windows
REM سكريبت تشغيل SmartPOS للشبكة المحلية - ويندوز

echo 🚀 Starting SmartPOS for Network Access
echo =======================================

REM Check if we're in the right directory
if not exist "backend" (
    echo ❌ Error: Please run this script from the SmartPOS root directory
    pause
    exit /b 1
)

if not exist "frontend" (
    echo ❌ Error: Please run this script from the SmartPOS root directory
    pause
    exit /b 1
)

REM Get local IP address
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do (
    set "LOCAL_IP=%%a"
    goto :found_ip
)

:found_ip
REM Remove leading spaces
for /f "tokens=* delims= " %%a in ("%LOCAL_IP%") do set LOCAL_IP=%%a

if "%LOCAL_IP%"=="" set LOCAL_IP=localhost

echo 🌐 Detected Local IP: %LOCAL_IP%
echo.

REM Start backend
echo 🔧 Starting Backend Server...
cd backend
start "SmartPOS Backend" cmd /k "python main.py"
cd ..

REM Wait a moment for backend to start
timeout /t 3 /nobreak >nul

REM Start frontend
echo 🎨 Starting Frontend Server...
cd frontend
start "SmartPOS Frontend" cmd /k "set NODE_ENV=development && npm run dev:network"
cd ..

echo.
echo ✅ SmartPOS Started Successfully!
echo =================================
echo 🌐 Local Access:
echo    Frontend: http://localhost:5175
echo    Backend:  http://localhost:8002
echo.
echo 🌍 Network Access:
echo    Frontend: http://%LOCAL_IP%:5175
echo    Backend:  http://%LOCAL_IP%:8002
echo.
echo 📱 Access from other devices using: http://%LOCAL_IP%:5175
echo.
echo 🛑 To stop the servers, close the opened command windows
echo.
pause
