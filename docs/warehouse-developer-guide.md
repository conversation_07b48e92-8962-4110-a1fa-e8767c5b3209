# 👨‍💻 دليل المطور - نظام إدارة المستودعات

## 🏗️ البنية المعمارية

### نمط التصميم
النظام يتبع نمط **Clean Architecture** مع فصل الطبقات:

```
┌─────────────────┐
│   Presentation  │ ← React Components, Pages
├─────────────────┤
│   Application   │ ← Services, Stores (Zustand)
├─────────────────┤
│   Domain        │ ← Business Logic, Entities
├─────────────────┤
│   Infrastructure│ ← Database, External APIs
└─────────────────┘
```

### مبادئ التصميم المطبقة

#### 1. Single Responsibility Principle (SRP)
كل كلاس له مسؤولية واحدة فقط:
```python
# ✅ صحيح
class WarehouseService:
    """مسؤول فقط عن عمليات المستودعات"""
    
class WarehouseInventoryService:
    """مسؤول فقط عن عمليات المخزون"""
```

#### 2. Dependency Injection
```python
class WarehouseService:
    def __init__(self, db_session: Session):
        self.db_session = db_session
```

#### 3. Singleton Pattern
```typescript
export class WarehouseService {
    private static instance: WarehouseService;
    
    public static getInstance(): WarehouseService {
        if (!WarehouseService.instance) {
            WarehouseService.instance = new WarehouseService();
        }
        return WarehouseService.instance;
    }
}
```

## 🔧 إعداد بيئة التطوير

### Backend Setup

#### 1. إنشاء البيئة الافتراضية
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate     # Windows
```

#### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

#### 3. إعداد قاعدة البيانات
```bash
# تشغيل migrations
python migrations/create_warehouse_tables.py

# التحقق من الجداول
python -c "from database.base import engine; print(engine.table_names())"
```

#### 4. تشغيل الخادم
```bash
python main.py
```

### Frontend Setup

#### 1. تثبيت المتطلبات
```bash
cd frontend
npm install
```

#### 2. تشغيل الخادم
```bash
npm start
```

## 📁 هيكل المشروع

### Backend Structure
```
backend/
├── models/
│   └── warehouse.py          # نماذج قاعدة البيانات
├── services/
│   ├── warehouse_service.py
│   ├── warehouse_inventory_service.py
│   ├── warehouse_movement_service.py
│   └── transfer_request_service.py
├── routers/
│   ├── warehouse.py
│   ├── warehouse_inventory.py
│   ├── warehouse_movement.py
│   └── transfer_request.py
├── schemas/
│   └── warehouse.py          # Pydantic schemas
├── migrations/
│   └── create_warehouse_tables.py
└── main.py
```

### Frontend Structure
```
frontend/src/
├── services/
│   ├── warehouseService.ts
│   ├── warehouseInventoryService.ts
│   ├── warehouseMovementService.ts
│   └── transferRequestService.ts
├── stores/
│   ├── warehouseStore.ts
│   ├── warehouseInventoryStore.ts
│   └── warehouseMovementStore.ts
├── components/
│   └── warehouse/
│       ├── CreateWarehouseModal.tsx
│       └── WarehouseInventoryTable.tsx
├── pages/
│   └── WarehousesPage.tsx
└── types/
    └── warehouse.ts
```

## 🔄 تدفق البيانات

### 1. إنشاء مستودع جديد
```mermaid
sequenceDiagram
    participant UI as React Component
    participant Store as Zustand Store
    participant Service as TypeScript Service
    participant API as FastAPI Router
    participant BL as Business Logic
    participant DB as Database

    UI->>Store: createWarehouse(data)
    Store->>Service: warehouseService.createWarehouse(data)
    Service->>API: POST /api/warehouses
    API->>BL: WarehouseService.create_warehouse(data)
    BL->>DB: INSERT INTO warehouses
    DB-->>BL: warehouse_id
    BL-->>API: success response
    API-->>Service: HTTP 201
    Service-->>Store: success result
    Store-->>UI: updated state
```

### 2. تحديث المخزون
```mermaid
sequenceDiagram
    participant UI as Inventory Table
    participant Store as Inventory Store
    participant Service as Inventory Service
    participant API as Inventory API
    participant BL as Inventory Logic
    participant DB as Database

    UI->>Store: updateStockLevels(warehouseId, productId, data)
    Store->>Service: updateStockLevels(warehouseId, productId, data)
    Service->>API: PUT /api/warehouse-inventory/warehouse/{id}/product/{id}
    API->>BL: WarehouseInventoryService.update_stock_levels()
    BL->>DB: UPDATE warehouse_inventory
    DB-->>BL: updated record
    BL-->>API: success response
    API-->>Service: HTTP 200
    Service-->>Store: updated inventory
    Store-->>UI: re-render with new data
```

## 🧪 الاختبار

### Backend Testing

#### 1. اختبار الوحدة (Unit Tests)
```python
# tests/test_warehouse_service.py
import pytest
from services.warehouse_service import WarehouseService

class TestWarehouseService:
    def test_create_warehouse_success(self, db_session):
        service = WarehouseService(db_session)
        data = {
            'name': 'مستودع تجريبي',
            'code': 'TEST-001',
            'is_active': True
        }
        result = service.create_warehouse(data)
        assert result['success'] is True
        assert result['warehouse']['name'] == 'مستودع تجريبي'
    
    def test_create_warehouse_duplicate_code(self, db_session):
        service = WarehouseService(db_session)
        # إنشاء مستودع أول
        service.create_warehouse({'name': 'مستودع 1', 'code': 'TEST-001'})
        # محاولة إنشاء مستودع بنفس الكود
        result = service.create_warehouse({'name': 'مستودع 2', 'code': 'TEST-001'})
        assert result['success'] is False
        assert 'يوجد مستودع بنفس الكود' in result['error']
```

#### 2. اختبار التكامل (Integration Tests)
```python
# tests/test_warehouse_api.py
from fastapi.testclient import TestClient

def test_create_warehouse_api(client: TestClient, auth_headers):
    data = {
        'name': 'مستودع API',
        'code': 'API-001',
        'is_active': True
    }
    response = client.post('/api/warehouses', json=data, headers=auth_headers)
    assert response.status_code == 201
    assert response.json()['success'] is True
```

### Frontend Testing

#### 1. اختبار المكونات
```typescript
// __tests__/WarehousesPage.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import WarehousesPage from '../pages/WarehousesPage';

describe('WarehousesPage', () => {
  test('renders warehouse list', () => {
    render(<WarehousesPage />);
    expect(screen.getByText('إدارة المستودعات')).toBeInTheDocument();
  });

  test('opens create modal when button clicked', () => {
    render(<WarehousesPage />);
    fireEvent.click(screen.getByText('إضافة مستودع'));
    expect(screen.getByText('إضافة مستودع جديد')).toBeInTheDocument();
  });
});
```

#### 2. اختبار الخدمات
```typescript
// __tests__/warehouseService.test.ts
import { warehouseService } from '../services/warehouseService';

describe('WarehouseService', () => {
  test('validates warehouse data correctly', () => {
    const invalidData = { name: '', code: '' };
    const result = warehouseService.validateWarehouseData(invalidData);
    expect(result.valid).toBe(false);
    expect(result.errors).toContain('اسم المستودع مطلوب');
  });
});
```

## 🔒 الأمان

### 1. مصادقة API
```python
from auth.dependencies import get_current_user

@router.post("/warehouses")
async def create_warehouse(
    warehouse_data: WarehouseCreate,
    current_user: User = Depends(get_current_user)
):
    # التحقق من الأذونات
    if not current_user.has_permission('warehouse.create'):
        raise HTTPException(status_code=403, detail="غير مصرح")
```

### 2. التحقق من البيانات
```python
from pydantic import BaseModel, validator

class WarehouseCreate(BaseModel):
    name: str
    code: str
    
    @validator('code')
    def validate_code(cls, v):
        if not v.replace('-', '').replace('_', '').isalnum():
            raise ValueError('كود المستودع يجب أن يحتوي على أحرف وأرقام فقط')
        return v.upper()
```

### 3. SQL Injection Prevention
```python
# ✅ استخدام ORM آمن
warehouse = session.query(Warehouse).filter(Warehouse.id == warehouse_id).first()

# ❌ تجنب SQL خام
# cursor.execute(f"SELECT * FROM warehouses WHERE id = {warehouse_id}")
```

## 📊 الأداء

### 1. فهرسة قاعدة البيانات
```sql
-- فهارس محسنة للأداء
CREATE INDEX idx_warehouse_inventory_warehouse_id ON warehouse_inventory(warehouse_id);
CREATE INDEX idx_warehouse_inventory_product_id ON warehouse_inventory(product_id);
CREATE UNIQUE INDEX idx_warehouse_inventory_unique ON warehouse_inventory(warehouse_id, product_id);
```

### 2. تحسين الاستعلامات
```python
# ✅ استعلام محسن
def get_warehouse_inventory(self, warehouse_id: int):
    return self.db_session.query(
        WarehouseInventory,
        Product.name.label('product_name')
    ).join(
        Product, WarehouseInventory.product_id == Product.id
    ).filter(
        WarehouseInventory.warehouse_id == warehouse_id
    ).all()
```

### 3. تخزين مؤقت Frontend
```typescript
// استخدام Zustand للتخزين المؤقت
export const useWarehouseStore = create<WarehouseState>()(
  devtools(
    (set, get) => ({
      // ...
    }),
    {
      name: 'warehouse-store',
      partialize: (state) => ({
        warehouses: state.warehouses,
        selectedWarehouse: state.selectedWarehouse
      })
    }
  )
);
```

## 🐛 تسجيل الأخطاء

### Backend Logging
```python
import logging

logger = logging.getLogger(__name__)

class WarehouseService:
    def create_warehouse(self, data):
        try:
            # منطق الإنشاء
            logger.info(f"تم إنشاء مستودع جديد: {warehouse.name}")
            return {'success': True, 'warehouse': warehouse}
        except Exception as e:
            logger.error(f"خطأ في إنشاء المستودع: {e}")
            return {'success': False, 'error': str(e)}
```

### Frontend Error Handling
```typescript
export class WarehouseService {
  async createWarehouse(data: WarehouseCreate): Promise<ApiResponse<Warehouse>> {
    try {
      const response = await axios.post('/api/warehouses', data);
      return response.data;
    } catch (error: any) {
      console.error('خطأ في إنشاء المستودع:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في إنشاء المستودع'
      };
    }
  }
}
```

## 🚀 النشر

### 1. إعداد الإنتاج
```bash
# Backend
pip install gunicorn
gunicorn main:app --workers 4 --bind 0.0.0.0:8000

# Frontend
npm run build
```

### 2. متغيرات البيئة
```bash
# .env
DATABASE_URL=postgresql://user:pass@localhost/smartpos
SECRET_KEY=your-secret-key
DEBUG=False
```

### 3. Docker
```dockerfile
# Dockerfile.backend
FROM python:3.9
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["gunicorn", "main:app", "--bind", "0.0.0.0:8000"]
```

## 📈 المراقبة

### 1. مقاييس الأداء
- زمن الاستجابة للAPI
- استخدام الذاكرة
- عدد الاستعلامات لقاعدة البيانات

### 2. تنبيهات النظام
- فشل في العمليات الحرجة
- استخدام مرتفع للموارد
- أخطاء قاعدة البيانات

## 🔄 التطوير المستقبلي

### ميزات مخططة
1. تقارير متقدمة مع الرسوم البيانية
2. تكامل مع أنظمة ERP خارجية
3. تطبيق موبايل للمستودعات
4. نظام إشعارات فوري
5. تحليلات ذكية للمخزون

### تحسينات تقنية
1. تحويل إلى Microservices
2. استخدام Redis للتخزين المؤقت
3. تطبيق GraphQL
4. إضافة WebSocket للتحديثات الفورية

---

**للمساهمة في التطوير:**
1. Fork المشروع
2. إنشاء branch جديد للميزة
3. كتابة الاختبارات
4. إرسال Pull Request
