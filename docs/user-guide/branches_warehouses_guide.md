# 🏢 دليل المستخدم - إدارة الفروع والمستودعات

> **تاريخ الإنشاء:** 2025-01-11  
> **الإصدار:** 1.0  
> **للمستخدمين:** مديري النظام، مديري الفروع، مديري المستودعات  

## 📖 مقدمة

نظام إدارة الفروع والمستودعات يوفر لك إدارة شاملة ومرنة لجميع فروع شركتك ومستودعاتها، مع إمكانية ربط كل فرع بعدة مستودعات حسب احتياجاتك التشغيلية.

## 🎯 الأهداف الرئيسية

- **إدارة مركزية** لجميع الفروع والمستودعات
- **مرونة في التوزيع** - ربط أي فرع بأي مستودع
- **تحكم ذكي** في أولويات التوريد
- **تتبع شامل** لجميع العمليات والتغييرات
- **تقارير متقدمة** عن أداء الفروع والمستودعات

## 🏗️ هيكل النظام

### **المفاهيم الأساسية:**

#### 🏢 **الفرع (Branch)**
- نقطة بيع أو خدمة تابعة للشركة
- له معلومات كاملة (العنوان، المدير، ساعات العمل)
- يمكن ربطه بعدة مستودعات
- له حالة نشاط (نشط/غير نشط)

#### 🏭 **المستودع (Warehouse)**
- مكان تخزين البضائع والمنتجات
- له سعة تخزينية محددة
- يمكن أن يخدم عدة فروع
- له أولوية مختلفة لكل فرع

#### 🔗 **الربط (Link)**
- العلاقة بين الفرع والمستودع
- يحدد أولوية المستودع للفرع
- يحدد ما إذا كان المستودع أساسي للفرع

## 📋 إدارة الفروع

### **1. إنشاء فرع جديد**

#### **الخطوات:**
1. انتقل إلى قسم "إدارة الفروع"
2. اضغط على "إضافة فرع جديد"
3. املأ البيانات المطلوبة:

#### **البيانات الأساسية:**
- **اسم الفرع** *(مطلوب)*: مثل "فرع طرابلس الوسط"
- **كود الفرع** *(مطلوب)*: كود فريد مثل "TRP-001"
- **العنوان**: العنوان الكامل للفرع
- **رقم الهاتف**: رقم التواصل مع الفرع
- **اسم المدير**: اسم مدير الفرع
- **البريد الإلكتروني**: إيميل الفرع

#### **معلومات إضافية:**
- **المدينة**: المدينة التي يقع فيها الفرع
- **المنطقة**: المنطقة أو الحي
- **الرمز البريدي**: إن وجد

#### **إعدادات التشغيل:**
- **ساعات العمل**: من الساعة - إلى الساعة (مثل: 08:00 - 18:00)
- **الحد الأقصى للمبيعات اليومية**: رقم اختياري
- **حالة النشاط**: نشط/غير نشط
- **فرع رئيسي**: هل هذا الفرع الرئيسي للشركة

### **2. تحديث بيانات الفرع**

#### **الخطوات:**
1. ابحث عن الفرع المطلوب
2. اضغط على "تحرير"
3. عدّل البيانات المطلوبة
4. احفظ التغييرات

#### **ملاحظات مهمة:**
- ⚠️ **كود الفرع** يجب أن يكون فريداً
- ⚠️ لا يمكن حذف **الفرع الرئيسي**
- ⚠️ تأكد من صحة **ساعات العمل** (صيغة HH:MM)

### **3. إدارة حالة الفرع**

#### **تفعيل/إلغاء تفعيل الفرع:**
- اضغط على زر "تبديل الحالة" بجانب الفرع
- الفروع غير النشطة لا تظهر في عمليات البيع
- لا يمكن إلغاء تفعيل الفرع الرئيسي

#### **تعيين فرع رئيسي:**
- اضغط على "تعيين كرئيسي" بجانب الفرع المطلوب
- سيتم إلغاء الرئيسية من الفرع السابق تلقائياً
- الفرع الرئيسي له أولوية في العمليات

### **4. البحث في الفروع**

#### **يمكنك البحث بـ:**
- اسم الفرع
- كود الفرع
- العنوان
- اسم المدير
- المدينة
- المنطقة

#### **خيارات البحث:**
- ☑️ **تضمين الفروع غير النشطة** في النتائج
- 🔍 **البحث المتقدم** بعدة معايير

## 🏭 إدارة المستودعات

### **المستودعات الموجودة:**
يمكنك رؤية جميع المستودعات المتاحة مع معلوماتها:
- الاسم والكود
- السعة الإجمالية والمستخدمة
- نسبة الاستخدام
- حالة النشاط

### **ربط المستودعات بالفروع:**
كل مستودع يمكن ربطه بعدة فروع، وكل فرع يمكن ربطه بعدة مستودعات.

## 🔗 إدارة العلاقات بين الفروع والمستودعات

### **1. ربط فرع بمستودع**

#### **الخطوات:**
1. انتقل إلى "إدارة العلاقات"
2. اختر "ربط فرع بمستودع"
3. اختر الفرع من القائمة
4. اختر المستودع من القائمة المتاحة
5. حدد الإعدادات:
   - **مستودع أساسي**: ☑️ إذا كان هذا المستودع الأساسي للفرع
   - **الأولوية**: رقم من 1-100 (1 = أعلى أولوية)

#### **مثال عملي:**
```
فرع طرابلس الوسط:
├── مستودع الرئيسي (أساسي ⭐ - أولوية: 1)
├── مستودع الظهرة (أولوية: 2)
└── مستودع الزاوية (أولوية: 3)
```

### **2. إدارة الأولويات**

#### **مفهوم الأولوية:**
- **الأولوية 1**: المستودع الأول للتوريد
- **الأولوية 2**: المستودع البديل الأول
- **الأولوية 3**: المستودع البديل الثاني
- وهكذا...

#### **تحديث الأولوية:**
1. اذهب إلى "عرض مستودعات الفرع"
2. اضغط على "تحديث الأولوية"
3. أدخل الرقم الجديد (1-100)
4. احفظ التغيير

### **3. تعيين المستودع الأساسي**

#### **المستودع الأساسي:**
- هو المستودع الرئيسي للفرع
- يُستخدم افتراضياً في عمليات البيع
- فرع واحد = مستودع أساسي واحد فقط

#### **تغيير المستودع الأساسي:**
1. اذهب إلى مستودعات الفرع
2. اضغط على "تعيين كأساسي" بجانب المستودع المطلوب
3. سيتم إلغاء الأساسية من المستودع السابق تلقائياً

### **4. إلغاء ربط فرع من مستودع**

#### **الخطوات:**
1. اذهب إلى "عرض مستودعات الفرع"
2. اضغط على "إلغاء الربط" بجانب المستودع
3. أكد العملية

#### **⚠️ تحذيرات:**
- تأكد من عدم وجود عمليات معلقة
- لا يمكن إلغاء ربط المستودع الأساسي إلا بعد تعيين مستودع أساسي آخر

## 📊 التقارير والاستعلامات

### **1. تقرير الفروع**
- عدد الفروع النشطة/غير النشطة
- توزيع الفروع حسب المدن
- الفروع بدون مستودعات مرتبطة

### **2. تقرير المستودعات**
- استخدام السعة لكل مستودع
- المستودعات المرتبطة بكل فرع
- المستودعات غير المستخدمة

### **3. تقرير العلاقات**
- خريطة الربط بين الفروع والمستودعات
- الأولويات والمستودعات الأساسية
- إحصائيات الاستخدام

## 🔧 نصائح وأفضل الممارسات

### **1. تنظيم الفروع:**
- استخدم نظام ترقيم واضح للأكواد (مثل: TRP-001, ZAW-001)
- حدد ساعات العمل بدقة لكل فرع
- حافظ على تحديث معلومات المديرين

### **2. إدارة المستودعات:**
- اربط كل فرع بمستودع أساسي على الأقل
- استخدم الأولويات بذكاء (قرب المسافة، توفر المخزون)
- راقب نسب استخدام السعة باستمرار

### **3. التوزيع الذكي:**
- ضع المستودع الأقرب جغرافياً كأولوية أولى
- اعتبر سعة المستودع عند تحديد الأولويات
- راجع الأولويات دورياً حسب التغييرات التشغيلية

### **4. الصيانة الدورية:**
- راجع بيانات الفروع شهرياً
- تحقق من صحة الروابط بين الفروع والمستودعات
- احذف الفروع غير المستخدمة (بعد التأكد)

## ❓ الأسئلة الشائعة

### **س: هل يمكن لفرع واحد أن يرتبط بعدة مستودعات؟**
ج: نعم، هذا هو الهدف من النظام. يمكن لأي فرع الارتباط بعدة مستودعات مع تحديد الأولويات.

### **س: ماذا يحدث إذا لم أحدد مستودع أساسي للفرع؟**
ج: سيستخدم النظام المستودع ذو الأولوية الأعلى (رقم 1) كمستودع افتراضي.

### **س: هل يمكن تغيير كود الفرع بعد إنشائه؟**
ج: نعم، لكن تأكد من عدم استخدام الكود الجديد في فرع آخر.

### **س: كيف أعرف أي مستودع يخدم فرع معين؟**
ج: اذهب إلى "عرض مستودعات الفرع" وستجد قائمة كاملة مع الأولويات.

### **س: هل يمكن حذف الفرع الرئيسي؟**
ج: لا، يجب تعيين فرع آخر كرئيسي أولاً، ثم يمكن حذف الفرع السابق.

## 🆘 الدعم والمساعدة

### **في حالة وجود مشاكل:**
1. تحقق من صحة البيانات المدخلة
2. تأكد من وجود الصلاحيات المطلوبة
3. راجع سجل الأخطاء في النظام
4. تواصل مع فريق الدعم التقني

### **معلومات الاتصال:**
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: 021-1234567
- **ساعات الدعم**: 8:00 - 18:00 (الأحد - الخميس)

---

> **📝 ملاحظة:** هذا الدليل يغطي الوظائف الأساسية. للميزات المتقدمة، راجع دليل المطورين أو تواصل مع فريق الدعم.
