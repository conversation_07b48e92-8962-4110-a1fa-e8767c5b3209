# 🔌 Warehouse Management API Documentation

## Base URL
```
http://localhost:8000/api
```

## Authentication
جميع endpoints تتطلب مصادقة Bearer Token:
```
Authorization: Bearer <your_token>
```

## 📦 Warehouses API

### GET /warehouses
الحصول على جميع المستودعات

**Parameters:**
- `include_inactive` (boolean, optional): تضمين المستودعات غير النشطة

**Response:**
```json
{
  "success": true,
  "warehouses": [
    {
      "id": 1,
      "name": "المستودع الرئيسي",
      "code": "MAIN-001",
      "address": "المقر الرئيسي",
      "phone": "+218-21-1234567",
      "manager_name": "أحمد محمد",
      "email": "<EMAIL>",
      "is_main": true,
      "is_active": true,
      "capacity_limit": 10000,
      "current_capacity": 7500,
      "created_at": "2024-12-01T10:00:00Z",
      "updated_at": "2024-12-01T10:00:00Z"
    }
  ],
  "total_count": 1
}
```

### POST /warehouses
إنشاء مستودع جديد

**Request Body:**
```json
{
  "name": "مستودع فرعي",
  "code": "SUB-001",
  "address": "العنوان",
  "phone": "+218-21-1234567",
  "manager_name": "محمد أحمد",
  "email": "<EMAIL>",
  "is_main": false,
  "is_active": true,
  "capacity_limit": 5000,
  "current_capacity": 0
}
```

**Response:**
```json
{
  "success": true,
  "warehouse": {
    "id": 2,
    "name": "مستودع فرعي",
    "code": "SUB-001",
    // ... باقي البيانات
  }
}
```

### GET /warehouses/{warehouse_id}
الحصول على مستودع بالمعرف

### PUT /warehouses/{warehouse_id}
تحديث بيانات المستودع

### DELETE /warehouses/{warehouse_id}
حذف المستودع

### POST /warehouses/{warehouse_id}/set-main
تعيين المستودع الرئيسي

### GET /warehouses/{warehouse_id}/capacity
الحصول على حالة سعة المستودع

**Response:**
```json
{
  "success": true,
  "capacity_status": {
    "warehouse_id": 1,
    "warehouse_name": "المستودع الرئيسي",
    "capacity_limit": 10000,
    "current_capacity": 7500,
    "total_items": 1250,
    "usage_percentage": 75,
    "available_capacity": 2500,
    "status": "warning"
  }
}
```

### GET /warehouses/summary/overview
الحصول على ملخص المستودعات

**Response:**
```json
{
  "success": true,
  "summary": {
    "total_warehouses": 3,
    "active_warehouses": 2,
    "inactive_warehouses": 1,
    "main_warehouse": {
      "id": 1,
      "name": "المستودع الرئيسي",
      "code": "MAIN-001"
    },
    "capacity": {
      "total_capacity": 15000,
      "used_capacity": 10000,
      "available_capacity": 5000,
      "usage_percentage": 66.67
    }
  }
}
```

## 📊 Warehouse Inventory API

### GET /warehouse-inventory/product/{product_id}
الحصول على مخزون المنتج في جميع المستودعات

**Response:**
```json
{
  "success": true,
  "product": {
    "id": 123,
    "name": "منتج تجريبي",
    "barcode": "1234567890"
  },
  "inventory": [
    {
      "warehouse_id": 1,
      "warehouse_name": "المستودع الرئيسي",
      "warehouse_code": "MAIN-001",
      "is_main_warehouse": true,
      "quantity": 100,
      "reserved_quantity": 10,
      "available_quantity": 90,
      "min_stock_level": 20,
      "max_stock_level": 200,
      "location_code": "A1-B2",
      "last_updated": "2024-12-01T10:00:00Z",
      "stock_status": "normal"
    }
  ],
  "summary": {
    "total_quantity": 100,
    "total_reserved": 10,
    "total_available": 90,
    "warehouses_count": 1
  }
}
```

### GET /warehouse-inventory/warehouse/{warehouse_id}
الحصول على مخزون المستودع

**Parameters:**
- `low_stock_only` (boolean): المنتجات قليلة المخزون فقط
- `search` (string): البحث في اسم المنتج أو الباركود
- `min_quantity` (number): الحد الأدنى للكمية
- `max_quantity` (number): الحد الأقصى للكمية

### PUT /warehouse-inventory/warehouse/{warehouse_id}/product/{product_id}
تحديث مستويات المخزون

**Request Body:**
```json
{
  "quantity": 150,
  "reserved_quantity": 5,
  "min_stock_level": 25,
  "max_stock_level": 300,
  "location_code": "A1-B3"
}
```

### GET /warehouse-inventory/warehouse/{warehouse_id}/product/{product_id}/availability
التحقق من توفر المخزون

**Parameters:**
- `quantity` (number, required): الكمية المطلوبة

**Response:**
```json
{
  "success": true,
  "available": true,
  "current_quantity": 100,
  "reserved_quantity": 10,
  "available_quantity": 90,
  "requested_quantity": 50,
  "shortage": 0,
  "message": "متوفر"
}
```

### GET /warehouse-inventory/warehouse/{warehouse_id}/low-stock
الحصول على المنتجات قليلة المخزون

### POST /warehouse-inventory/warehouse/{warehouse_id}/product/{product_id}/reserve
حجز المخزون

**Parameters:**
- `quantity` (number, required): الكمية المراد حجزها

### POST /warehouse-inventory/warehouse/{warehouse_id}/product/{product_id}/release
إلغاء حجز المخزون

**Parameters:**
- `quantity` (number, required): الكمية المراد إلغاء حجزها

## 🔄 Warehouse Movements API

### POST /warehouse-movements
تسجيل حركة مستودع

**Request Body:**
```json
{
  "movement_type": "IN",
  "to_warehouse_id": 1,
  "product_id": 123,
  "quantity": 100,
  "unit_cost": 10.50,
  "total_cost": 1050.00,
  "reference_type": "PURCHASE",
  "reference_id": 456,
  "notes": "شراء جديد"
}
```

**Movement Types:**
- `IN`: دخول
- `OUT`: خروج  
- `TRANSFER`: تحويل
- `ADJUSTMENT`: تعديل

**Reference Types:**
- `PURCHASE`: مشتريات
- `SALE`: مبيعات
- `TRANSFER`: تحويل
- `ADJUSTMENT`: تعديل
- `RETURN`: مرتجعات

### GET /warehouse-movements/warehouse/{warehouse_id}
الحصول على حركات المستودع

**Parameters:**
- `movement_type` (string): نوع الحركة
- `product_id` (number): معرف المنتج
- `date_from` (datetime): من تاريخ
- `date_to` (datetime): إلى تاريخ
- `reference_type` (string): نوع المرجع
- `page` (number): رقم الصفحة
- `per_page` (number): عدد العناصر في الصفحة

### GET /warehouse-movements/product/{product_id}/history
الحصول على تاريخ حركات المنتج

### POST /warehouse-movements/adjustment
معالجة تعديل المخزون

**Request Body:**
```json
{
  "warehouse_id": 1,
  "product_id": 123,
  "new_quantity": 150,
  "reason": "تعديل الجرد السنوي"
}
```

### GET /warehouse-movements/warehouse/{warehouse_id}/summary
الحصول على ملخص حركات المستودع

**Parameters:**
- `date_from` (datetime): من تاريخ
- `date_to` (datetime): إلى تاريخ

## 🚚 Transfer Requests API

### POST /transfer-requests
إنشاء طلب تحويل جديد

**Request Body:**
```json
{
  "from_warehouse_id": 1,
  "to_warehouse_id": 2,
  "notes": "تحويل للمستودع الفرعي",
  "items": [
    {
      "product_id": 123,
      "requested_quantity": 50,
      "unit_cost": 10.50,
      "notes": "منتج عالي الطلب"
    }
  ]
}
```

### GET /transfer-requests/pending
الحصول على طلبات التحويل المعلقة

### GET /transfer-requests/history
الحصول على تاريخ طلبات التحويل

**Parameters:**
- `status_filter` (string): فلتر الحالة
- `from_warehouse_id` (number): معرف المستودع المصدر
- `to_warehouse_id` (number): معرف المستودع الوجهة
- `date_from` (datetime): من تاريخ
- `date_to` (datetime): إلى تاريخ
- `page` (number): رقم الصفحة
- `per_page` (number): عدد العناصر في الصفحة

### POST /transfer-requests/{request_id}/approve
الموافقة على طلب التحويل

**Request Body (Optional):**
```json
{
  "items": [
    {
      "product_id": 123,
      "approved_quantity": 45
    }
  ]
}
```

### POST /transfer-requests/{request_id}/process
معالجة التحويل (تغيير الحالة إلى في الطريق)

### POST /transfer-requests/{request_id}/complete
إكمال التحويل

### POST /transfer-requests/{request_id}/cancel
إلغاء طلب التحويل

**Parameters:**
- `reason` (string, required): سبب الإلغاء

## 📋 Status Codes

- `200` - نجح الطلب
- `201` - تم الإنشاء بنجاح
- `400` - خطأ في البيانات المرسلة
- `401` - غير مصرح
- `403` - ممنوع
- `404` - غير موجود
- `500` - خطأ داخلي في الخادم

## 🔍 Error Response Format

```json
{
  "success": false,
  "error": "رسالة الخطأ",
  "detail": "تفاصيل إضافية عن الخطأ"
}
```

## 📝 Notes

1. جميع التواريخ بصيغة ISO 8601
2. جميع الأرقام العشرية بدقة 2 خانة عشرية
3. النصوص تدعم UTF-8 للغة العربية
4. الاستجابات مرتبة حسب التاريخ (الأحدث أولاً)
5. التصفح يبدأ من الصفحة 1
