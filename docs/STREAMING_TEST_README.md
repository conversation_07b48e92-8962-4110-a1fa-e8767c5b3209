# دليل اختبار نظام تدفق البيانات الكبيرة

## نظرة عامة

هذا الدليل يوضح كيفية اختبار نظام تدفق البيانات الكبيرة في تطبيق SmartPOS.

## ملفات الاختبار

### 1. `simple_streaming_test.py` ⭐ (الأفضل)
اختبار مبسط وسهل الاستخدام:
- ✅ فحص حالة الخادم
- ✅ اختبار جميع نقاط النهاية
- ✅ اختبار إنشاء المهام
- ✅ تقرير شامل للنتائج

### 2. `quick_test_streaming.py`
اختبار سريع مع ميزات متقدمة:
- 🔐 تسجيل دخول تلقائي
- 📊 مراقبة تقدم المهام
- 📥 اختبار التحميل
- 🔄 دعم urllib كبديل

### 3. `test_data_streaming.py`
اختبار شامل ومفصل:
- 🧪 فئة اختبار كاملة
- 📈 اختبارات الأداء
- 🔍 تحليل مفصل للنتائج
- 🛠️ أدوات تشخيص متقدمة

### 4. `install_requirements.py`
أداة تثبيت المتطلبات:
- 📦 تثبيت تلقائي للمكتبات
- ✅ فحص المتطلبات المثبتة
- 🔧 إصلاح مشاكل التبعيات

## التشغيل السريع

### 1. تثبيت المتطلبات
```bash
# الطريقة الأولى (الأسهل)
python install_requirements.py

# الطريقة الثانية (يدوياً)
pip install requests fastapi uvicorn sqlalchemy pydantic

# الطريقة الثالثة (من ملف المتطلبات)
pip install -r backend/requirements.txt
```

### 2. تشغيل الخادم
```bash
# في terminal منفصل
cd backend
python main.py

# أو باستخدام uvicorn
uvicorn main:app --host 0.0.0.0 --port 8002 --reload
```

### 3. تشغيل الاختبار
```bash
# الاختبار المبسط (الأفضل للمبتدئين)
python simple_streaming_test.py

# الاختبار السريع
python quick_test_streaming.py

# الاختبار الشامل
python test_data_streaming.py
```

## نتائج الاختبار المتوقعة

### ✅ نجاح كامل
```
🚀 بدء اختبار نظام تدفق البيانات الكبيرة
==================================================
🔍 فحص حالة الخادم...
✅ الخادم يعمل بشكل طبيعي

1️⃣ اختبار تدفق المبيعات...
✅ تدفق المبيعات يعمل بشكل طبيعي
📊 تم استلام 1024 بايت من البيانات

2️⃣ اختبار تدفق المنتجات...
✅ تدفق المنتجات يعمل بشكل طبيعي
📊 تم استلام 2048 بايت من البيانات

📊 نتائج الاختبار:
✅ نجح: 4/4
🎉 جميع الاختبارات نجحت!
```

### 🔐 يتطلب مصادقة
```
1️⃣ اختبار تدفق المبيعات...
🔐 تدفق المبيعات يتطلب مصادقة

💡 نصائح:
   - قم بتسجيل الدخول أولاً
   - تأكد من صحة بيانات المستخدم
```

### ❌ مشاكل في الاتصال
```
🔍 فحص حالة الخادم...
❌ فشل في الاتصال: Connection refused
   تأكد من تشغيل الخادم: python backend/main.py
```

## استكشاف الأخطاء

### 1. مشكلة "requests module not found"
```bash
# الحل
pip install requests

# أو
python install_requirements.py
```

### 2. مشكلة "Connection refused"
```bash
# تأكد من تشغيل الخادم
cd backend
python main.py

# تحقق من المنفذ
netstat -an | grep 8002
```

### 3. مشكلة "401 Unauthorized"
```bash
# تحقق من بيانات تسجيل الدخول في الملف
# أو استخدم الاختبار المبسط الذي لا يتطلب مصادقة
python simple_streaming_test.py
```

### 4. مشكلة "No data available"
```bash
# تأكد من وجود بيانات في قاعدة البيانات
# أو أضف بيانات تجريبية
```

## تخصيص الاختبارات

### تغيير عنوان الخادم
```python
# في أي ملف اختبار
base_url = "http://localhost:8002"  # غير هذا حسب إعداداتك
```

### تغيير بيانات تسجيل الدخول
```python
# في quick_test_streaming.py
login_data = {
    "username": "your_username",  # غير هذا
    "password": "your_password"   # غير هذا
}
```

### إضافة اختبارات جديدة
```python
# في simple_streaming_test.py
endpoints_to_test.append({
    "name": "اختبار جديد",
    "url": f"{base_url}/api/new-endpoint",
    "params": {"param1": "value1"}
})
```

## مراقبة الأداء

### قياس سرعة التدفق
```python
import time

start_time = time.time()
# تشغيل الاختبار
end_time = time.time()

print(f"وقت التنفيذ: {end_time - start_time:.2f} ثانية")
```

### مراقبة استخدام الذاكرة
```python
import psutil
import os

process = psutil.Process(os.getpid())
memory_usage = process.memory_info().rss / 1024 / 1024  # MB
print(f"استخدام الذاكرة: {memory_usage:.2f} MB")
```

## أفضل الممارسات

### 1. تشغيل الاختبارات بانتظام
```bash
# إنشاء script يومي
#!/bin/bash
echo "$(date): بدء اختبار يومي" >> test_log.txt
python simple_streaming_test.py >> test_log.txt 2>&1
```

### 2. مراقبة السجلات
```bash
# مراقبة سجلات الخادم
tail -f backend/logs/app.log

# مراقبة سجلات الاختبار
tail -f test_log.txt
```

### 3. اختبار البيئات المختلفة
```bash
# اختبار محلي
python simple_streaming_test.py

# اختبار على خادم التطوير
BASE_URL=http://dev-server:8002 python simple_streaming_test.py

# اختبار على خادم الإنتاج
BASE_URL=https://prod-server.com python simple_streaming_test.py
```

## الخلاصة

نظام اختبار تدفق البيانات الكبيرة يوفر:

- ✅ **اختبارات شاملة** لجميع المكونات
- 🔧 **أدوات تشخيص** متقدمة
- 📊 **تقارير مفصلة** للنتائج
- 🛠️ **إصلاح تلقائي** للمشاكل الشائعة
- 📚 **توثيق واضح** وسهل الفهم

ابدأ بـ `simple_streaming_test.py` للاختبار السريع، ثم انتقل للاختبارات المتقدمة حسب الحاجة!

## الدعم

إذا واجهت أي مشاكل:
1. تحقق من سجلات الخادم
2. تأكد من تثبيت جميع المتطلبات
3. جرب الاختبار المبسط أولاً
4. راجع قسم استكشاف الأخطاء أعلاه
