# 👨‍💻 دليل المطور - الواجهة الجديدة SmartPOS

## 🏗️ البنية المعمارية

### نظرة عامة على التصميم
```
frontend/src/
├── components/
│   ├── Sidebar.tsx          # الشريط الجانبي الجديد
│   ├── Topbar.tsx           # الشريط العلوي المحدث
│   └── NewLayout.tsx        # التخطيط الجديد
├── stores/
│   └── sidebarStore.ts      # إدارة حالة الشريط الجانبي
└── styles/
    └── index.css            # أنماط مخصصة
```

### تدفق البيانات
```
User Action → Zustand Store → Component Re-render → UI Update
```

## 🔧 إعداد البيئة التطويرية

### المتطلبات الأساسية
```json
{
  "react": "^18.2.0",
  "typescript": "^4.9.5",
  "tailwindcss": "^3.3.0",
  "zustand": "^4.4.1",
  "react-icons": "^4.11.0",
  "react-router-dom": "^6.15.0"
}
```

### إعداد TypeScript
```typescript
// tsconfig.json
{
  "compilerOptions": {
    "strict": true,
    "jsx": "react-jsx",
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true
  }
}
```

### إعداد Tailwind CSS
```javascript
// tailwind.config.js
module.exports = {
  content: ["./src/**/*.{js,jsx,ts,tsx}"],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        primary: { /* ألوان مخصصة */ },
        secondary: { /* ألوان ثانوية */ }
      }
    }
  }
}
```

## 📦 إدارة الحالة مع Zustand

### إنشاء Store جديد
```typescript
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface MyState {
  count: number;
  increment: () => void;
  decrement: () => void;
}

export const useMyStore = create<MyState>()(
  persist(
    (set) => ({
      count: 0,
      increment: () => set((state) => ({ count: state.count + 1 })),
      decrement: () => set((state) => ({ count: state.count - 1 })),
    }),
    {
      name: 'my-storage',
    }
  )
);
```

### استخدام Store في المكونات
```typescript
import { useMyStore } from '../stores/myStore';

function Counter() {
  const { count, increment, decrement } = useMyStore();
  
  return (
    <div>
      <span>{count}</span>
      <button onClick={increment}>+</button>
      <button onClick={decrement}>-</button>
    </div>
  );
}
```

## 🎨 تطوير المكونات

### هيكل المكون الأساسي
```typescript
import React from 'react';

interface MyComponentProps {
  title: string;
  children?: React.ReactNode;
  className?: string;
}

const MyComponent: React.FC<MyComponentProps> = ({ 
  title, 
  children, 
  className = '' 
}) => {
  return (
    <div className={`base-styles ${className}`}>
      <h2>{title}</h2>
      {children}
    </div>
  );
};

export default MyComponent;
```

### استخدام Hooks مخصصة
```typescript
// hooks/useLocalStorage.ts
import { useState, useEffect } from 'react';

export function useLocalStorage<T>(key: string, initialValue: T) {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      return initialValue;
    }
  });

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(error);
    }
  };

  return [storedValue, setValue] as const;
}
```

## 🎯 إضافة ميزات جديدة

### إضافة قائمة جديدة للشريط الجانبي

#### 1. تحديث النوع
```typescript
// stores/sidebarStore.ts
const newMenuItem: MenuItem = {
  id: 'analytics',
  name: 'التحليلات المتقدمة',
  path: '/analytics',
  icon: 'FiTrendingUp',
  iconColor: 'text-pink-500 dark:text-pink-400',
  subItems: [
    { id: 'sales-analytics', name: 'تحليل المبيعات', path: '/analytics/sales' },
    { id: 'customer-analytics', name: 'تحليل العملاء', path: '/analytics/customers' }
  ]
};
```

#### 2. إضافة الأيقونة
```typescript
// components/Sidebar.tsx
import { FiTrendingUp } from 'react-icons/fi';

const iconMap = {
  // ... الأيقونات الموجودة
  FiTrendingUp
};
```

#### 3. إضافة المسار
```typescript
// App.tsx
<Route
  path="/analytics/*"
  element={
    <ProtectedRoute>
      <NewLayout>
        <Analytics />
      </NewLayout>
    </ProtectedRoute>
  }
/>
```

### إضافة زر جديد للشريط العلوي

```typescript
// components/Topbar.tsx
const newButton = (
  <button
    onClick={handleNewAction}
    className="p-2 rounded-lg text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
    aria-label="إجراء جديد"
  >
    <FiNewIcon className="w-4 h-4" />
  </button>
);
```

## 🎨 تخصيص التصميم

### إضافة ألوان جديدة
```css
/* index.css */
:root {
  --color-custom-primary: #your-color;
  --color-custom-secondary: #your-color;
}

.dark {
  --color-custom-primary: #your-dark-color;
  --color-custom-secondary: #your-dark-color;
}
```

### إنشاء أنيميشن مخصص
```css
@keyframes customSlide {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-custom-slide {
  animation: customSlide 0.3s ease-out;
}
```

### استخدام متغيرات CSS
```typescript
const customStyles = {
  '--sidebar-width': isOpen ? '256px' : '64px',
  '--transition-duration': '300ms'
} as React.CSSProperties;

<div style={customStyles} className="sidebar">
  {/* محتوى الشريط الجانبي */}
</div>
```

## 🧪 الاختبار

### اختبار المكونات
```typescript
// __tests__/Sidebar.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import Sidebar from '../components/Sidebar';

const renderSidebar = () => {
  return render(
    <BrowserRouter>
      <Sidebar />
    </BrowserRouter>
  );
};

describe('Sidebar', () => {
  test('يعرض القوائم الأساسية', () => {
    renderSidebar();
    expect(screen.getByText('لوحة التحكم')).toBeInTheDocument();
    expect(screen.getByText('نقطة البيع')).toBeInTheDocument();
  });

  test('يتوسع عند النقر على قائمة فرعية', () => {
    renderSidebar();
    const salesMenu = screen.getByText('المبيعات');
    fireEvent.click(salesMenu);
    expect(screen.getByText('قائمة المبيعات')).toBeInTheDocument();
  });
});
```

### اختبار Zustand Store
```typescript
// __tests__/sidebarStore.test.ts
import { renderHook, act } from '@testing-library/react';
import { useSidebarStore } from '../stores/sidebarStore';

describe('sidebarStore', () => {
  test('يبدل حالة الشريط الجانبي', () => {
    const { result } = renderHook(() => useSidebarStore());
    
    expect(result.current.isOpen).toBe(true);
    
    act(() => {
      result.current.toggleSidebar();
    });
    
    expect(result.current.isOpen).toBe(false);
  });
});
```

## 🚀 الأداء والتحسين

### تحسين الرسم
```typescript
import { memo, useMemo, useCallback } from 'react';

const OptimizedComponent = memo(({ items, onItemClick }) => {
  const sortedItems = useMemo(() => 
    items.sort((a, b) => a.name.localeCompare(b.name)), 
    [items]
  );

  const handleClick = useCallback((id: string) => {
    onItemClick(id);
  }, [onItemClick]);

  return (
    <div>
      {sortedItems.map(item => (
        <Item key={item.id} item={item} onClick={handleClick} />
      ))}
    </div>
  );
});
```

### Lazy Loading للمكونات
```typescript
import { lazy, Suspense } from 'react';

const LazyComponent = lazy(() => import('./HeavyComponent'));

function App() {
  return (
    <Suspense fallback={<div>جاري التحميل...</div>}>
      <LazyComponent />
    </Suspense>
  );
}
```

## 🔍 تتبع الأخطاء

### Error Boundary
```typescript
import React from 'react';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends React.Component<
  React.PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('خطأ في المكون:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-fallback">
          <h2>حدث خطأ غير متوقع</h2>
          <details>
            {this.state.error && this.state.error.toString()}
          </details>
        </div>
      );
    }

    return this.props.children;
  }
}
```

## 📝 أفضل الممارسات

### تنظيم الكود
```typescript
// ✅ جيد
const SIDEBAR_WIDTH = {
  OPEN: 256,
  CLOSED: 64
} as const;

// ❌ سيء
const sidebarWidth = isOpen ? 256 : 64;
```

### تسمية المتغيرات
```typescript
// ✅ جيد
const isUserAuthenticated = checkUserAuth();
const handleMenuItemClick = (itemId: string) => {};

// ❌ سيء
const auth = checkUserAuth();
const click = (id: string) => {};
```

### إدارة الحالة
```typescript
// ✅ جيد - حالة محلية للمكون
const [isLoading, setIsLoading] = useState(false);

// ✅ جيد - حالة عامة في Store
const { user, setUser } = useAuthStore();

// ❌ سيء - استخدام Store للحالة المحلية
const { tempValue, setTempValue } = useGlobalStore();
```

## 🔧 أدوات التطوير

### VS Code Extensions المفيدة
- ES7+ React/Redux/React-Native snippets
- Tailwind CSS IntelliSense
- TypeScript Importer
- Auto Rename Tag
- Prettier - Code formatter

### Scripts مفيدة
```json
{
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "lint": "eslint src --ext ts,tsx",
    "lint:fix": "eslint src --ext ts,tsx --fix"
  }
}
```

---

**🎯 نصائح للمطورين الجدد:**
1. ابدأ بفهم بنية المشروع
2. اقرأ الكود الموجود قبل الإضافة
3. اتبع نمط التسمية المستخدم
4. اختبر تغييراتك على جميع الشاشات
5. اكتب تعليقات واضحة للكود المعقد

*دليل المطور - إعداد Augment Agent 2025*
