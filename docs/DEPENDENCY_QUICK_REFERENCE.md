# 📋 مرجع سريع لإدارة التبعيات - Dependency Quick Reference

## 🚀 أوامر التثبيت الأساسية

### تثبيت آمن (الطريقة المفضلة)
```bash
# تثبيت مع ضمان devDependencies
npm install --include=dev

# تثبيت للإنتاج فقط
npm install --production
```

### تثبيت تبعيات جديدة
```bash
# تبعية إنتاج
npm install package-name

# تبعية تطوير
npm install --save-dev package-name

# إصدار محدد
npm install package-name@1.2.3
```

## 🔧 حل المشاكل السريع

### المشكلة: devDependencies لا تُثبت
```bash
# الحل السريع
npm install --include=dev

# الحل الجذري
rm -rf node_modules package-lock.json
npm cache clean --force
npm install --include=dev
```

### المشكلة: npm run build يفشل
```bash
# فحص الأدوات المطلوبة
ls node_modules/.bin/ | grep -E "(tsc|vite)"

# إذا كانت مفقودة:
npm install --include=dev
```

### المشكلة: cache npm تالف
```bash
# تنظيف cache
npm cache clean --force
npm cache verify
```

## 🔍 أوامر التشخيص

### فحص التبعيات
```bash
# عرض جميع التبعيات
npm ls --depth=0

# عرض devDependencies فقط
npm ls --dev --depth=0

# عد الأدوات المثبتة
ls node_modules/.bin/ | wc -l
```

### فحص إعدادات npm
```bash
npm config get production
npm config get NODE_ENV
npm config list
```

## ✅ قائمة فحص سريعة

### قبل البدء في التطوير:
- [ ] `npm install --include=dev`
- [ ] `ls node_modules/.bin/ | grep -E "(tsc|vite)"` ✅ موجودان
- [ ] `npm run build` ✅ ناجح
- [ ] `npm run dev` ✅ يعمل

### عند مواجهة مشاكل:
- [ ] `npm cache clean --force`
- [ ] `rm -rf node_modules package-lock.json`
- [ ] `npm install --include=dev`
- [ ] اختبار البناء فوراً

### عند إضافة تبعيات:
- [ ] حدد النوع (production/development)
- [ ] استخدم الأمر المناسب
- [ ] اختبر البناء بعد التثبيت
- [ ] تحقق من عدم وجود تضارب

## 🚨 تحذيرات مهمة

### ❌ تجنب هذه الأوامر
```bash
# خطر! قد يكسر التوافق
npm audit fix --force
npm update --force

# غير آمن للإنتاج
npm install --legacy-peer-deps
```

### ✅ استخدم هذه البدائل
```bash
# آمن للتحديث
npm update package-name
npm install package-name@latest

# آمن للإصلاح
npm audit fix
```

## 📊 مؤشرات الصحة

### علامات التثبيت الناجح:
- ✅ `node_modules/.bin/` يحتوي على 50+ أداة
- ✅ `npm ls --depth=0` بدون أخطاء
- ✅ `npm run build` ينجح في أقل من 5 ثوان
- ✅ حجم `node_modules/` حوالي 200-500 MB

### علامات وجود مشاكل:
- ❌ `node_modules/.bin/` فارغ أو يحتوي على أدوات قليلة
- ❌ `npm ls` يظهر أخطاء peer dependency
- ❌ `npm run build` يفشل مع أخطاء TypeScript
- ❌ تثبيت بطيء جداً (أكثر من 5 دقائق)

## 🔗 روابط سريعة

### ملفات مهمة:
- [دليل إدارة التبعيات الشامل](./guides/DEPENDENCY_MANAGEMENT_GUIDE.md)
- [إصلاح مشاكل البناء](./updates/BUILD_ISSUES_RADICAL_FIX_UPDATE.md)
- [قواعد النظام](../SYSTEM_MEMORY.md)

### أدوات مفيدة:
- [npm-check-updates](https://www.npmjs.com/package/npm-check-updates)
- [depcheck](https://www.npmjs.com/package/depcheck)
- [bundlephobia](https://bundlephobia.com/)

---

**📞 للدعم التقني**: راجع [BUILD_ISSUES_RADICAL_FIX_UPDATE.md](./updates/BUILD_ISSUES_RADICAL_FIX_UPDATE.md)  
**🔄 آخر تحديث**: 30 يونيو 2025
