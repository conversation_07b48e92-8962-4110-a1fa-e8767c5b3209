# 📋 مرجع الأوامر السريع - نظام تدفق البيانات الكبيرة

## 🚀 البدء السريع

### 1. التثبيت والإعداد
```bash
# تثبيت المتطلبات
python install_requirements.py

# أو يدوياً
pip install requests fastapi uvicorn sqlalchemy pydantic

# تشغيل سريع شامل
python تشغيل_سريع.py
```

### 2. تشغيل الخادم
```bash
# الطريقة الأولى
cd backend
python main.py

# الطريقة الثانية
uvicorn main:app --host 0.0.0.0 --port 8002 --reload
```

### 3. تشغيل الاختبارات
```bash
# الاختبار المبسط (الأفضل)
python simple_streaming_test.py

# الاختبار السريع
python quick_test_streaming.py

# الاختبار الشامل
python test_data_streaming.py
```

## 🌐 API Endpoints

### 📊 تدفق المبيعات
```bash
# تصدير جميع المبيعات (JSON)
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:8002/api/data-streaming/sales/stream"

# تصدير المبيعات (CSV)
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:8002/api/data-streaming/sales/stream?format_type=csv"

# تصدير مبيعات فترة معينة
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:8002/api/data-streaming/sales/stream?start_date=2024-01-01&end_date=2024-01-31"

# تصدير مبيعات مستخدم معين
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:8002/api/data-streaming/sales/stream?user_id=1"

# تصدير مضغوط
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:8002/api/data-streaming/sales/stream?compress=true"
```

### 📦 تدفق المنتجات
```bash
# تصدير جميع المنتجات
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:8002/api/data-streaming/products/stream"

# تصدير منتجات فئة معينة
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:8002/api/data-streaming/products/stream?category=electronics"

# تصدير المنتجات قليلة المخزون
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:8002/api/data-streaming/products/stream?low_stock=true"
```

### 👥 تدفق العملاء
```bash
# تصدير جميع العملاء
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:8002/api/data-streaming/customers/stream"

# تصدير العملاء مع بيانات الديون
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:8002/api/data-streaming/customers/stream?with_debts=true"
```

### 🔄 إدارة المهام
```bash
# إنشاء مهمة تصدير
curl -X POST \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{"task_type":"sales_export","parameters":{"format_type":"json","compress":true}}' \
     "http://localhost:8002/api/data-streaming/tasks/create"

# مراقبة تقدم المهمة
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:8002/api/data-streaming/tasks/TASK_ID/progress"

# إلغاء مهمة
curl -X DELETE \
     -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:8002/api/data-streaming/tasks/TASK_ID/cancel"
```

### 📈 المقاييس والإحصائيات
```bash
# الحصول على المقاييس (للإدارة فقط)
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:8002/api/data-streaming/metrics"

# إحصائيات الاستخدام
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:8002/api/data-streaming/usage-stats"
```

### 📥 التصدير المجمع
```bash
# تصدير عدة جداول
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:8002/api/data-streaming/bulk-export?tables=sales,products,customers"

# تصدير مجمع مضغوط
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:8002/api/data-streaming/bulk-export?tables=sales,products&compress=true"
```

## 🐍 أمثلة Python

### 1. تصدير بسيط
```python
import requests

# الحصول على التوكن
login_response = requests.post("http://localhost:8002/api/auth/login", 
                              json={"username": "admin", "password": "admin123"})
token = login_response.json()["access_token"]

# تصدير المبيعات
response = requests.get(
    "http://localhost:8002/api/data-streaming/sales/stream",
    headers={"Authorization": f"Bearer {token}"},
    params={"format_type": "json"},
    stream=True
)

# حفظ البيانات
with open("sales_export.json", "w", encoding="utf-8") as f:
    for chunk in response.iter_content(chunk_size=8192, decode_unicode=True):
        f.write(chunk)
```

### 2. إنشاء ومراقبة مهمة
```python
import requests
import time

# إنشاء مهمة
task_response = requests.post(
    "http://localhost:8002/api/data-streaming/tasks/create",
    headers={"Authorization": f"Bearer {token}"},
    json={
        "task_type": "sales_export",
        "parameters": {"format_type": "csv", "compress": True}
    }
)

task_id = task_response.json()["task_id"]
print(f"تم إنشاء المهمة: {task_id}")

# مراقبة التقدم
while True:
    progress_response = requests.get(
        f"http://localhost:8002/api/data-streaming/tasks/{task_id}/progress",
        headers={"Authorization": f"Bearer {token}"}
    )
    
    progress = progress_response.json()
    status = progress["status"]
    percentage = progress.get("progress_percentage", 0)
    
    print(f"الحالة: {status} - التقدم: {percentage:.1f}%")
    
    if status in ["completed", "failed"]:
        break
    
    time.sleep(2)

print("انتهت المهمة!")
```

### 3. تصدير مع معالجة الأخطاء
```python
import requests
from requests.exceptions import RequestException

def safe_export(endpoint, params=None, token=None):
    """تصدير آمن مع معالجة الأخطاء"""
    try:
        headers = {"Authorization": f"Bearer {token}"} if token else {}
        
        response = requests.get(
            f"http://localhost:8002/api/data-streaming/{endpoint}",
            headers=headers,
            params=params or {},
            stream=True,
            timeout=30
        )
        
        response.raise_for_status()
        return response
        
    except RequestException as e:
        print(f"خطأ في التصدير: {e}")
        return None

# استخدام الدالة
response = safe_export("sales/stream", {"format_type": "json"}, token)
if response:
    print("تم التصدير بنجاح!")
else:
    print("فشل التصدير")
```

## 🔧 استكشاف الأخطاء

### 1. مشاكل الاتصال
```bash
# فحص حالة الخادم
curl -I http://localhost:8002/

# فحص المنفذ
netstat -an | grep 8002

# فحص العمليات
ps aux | grep python
```

### 2. مشاكل المصادقة
```bash
# اختبار تسجيل الدخول
curl -X POST \
     -H "Content-Type: application/json" \
     -d '{"username":"admin","password":"admin123"}' \
     "http://localhost:8002/api/auth/login"

# فحص صحة التوكن
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:8002/api/auth/me"
```

### 3. مشاكل قاعدة البيانات
```bash
# إعادة تهيئة قاعدة البيانات
cd backend
python init_db.py

# إنشاء بيانات تجريبية
python create_sample_data.py

# فحص الجداول
sqlite3 smartpos.db ".tables"
```

## 📊 مراقبة الأداء

### 1. مراقبة استخدام الذاكرة
```python
import psutil
import os

process = psutil.Process(os.getpid())
memory_usage = process.memory_info().rss / 1024 / 1024  # MB
print(f"استخدام الذاكرة: {memory_usage:.2f} MB")
```

### 2. قياس سرعة التصدير
```python
import time
import requests

start_time = time.time()

response = requests.get(
    "http://localhost:8002/api/data-streaming/sales/stream",
    headers={"Authorization": f"Bearer {token}"},
    stream=True
)

data_size = 0
for chunk in response.iter_content(chunk_size=8192):
    data_size += len(chunk)

end_time = time.time()
duration = end_time - start_time
speed = (data_size / 1024 / 1024) / duration  # MB/s

print(f"حجم البيانات: {data_size / 1024 / 1024:.2f} MB")
print(f"الوقت المستغرق: {duration:.2f} ثانية")
print(f"السرعة: {speed:.2f} MB/s")
```

## 🔐 الأمان

### 1. إدارة التوكنات
```python
import jwt
from datetime import datetime, timedelta

# إنشاء توكن مخصص (للتطوير فقط)
payload = {
    "user_id": 1,
    "username": "admin",
    "role": "admin",
    "exp": datetime.utcnow() + timedelta(hours=24)
}

token = jwt.encode(payload, "your-secret-key", algorithm="HS256")
print(f"التوكن: {token}")
```

### 2. فحص الصلاحيات
```bash
# فحص صلاحيات المستخدم
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:8002/api/auth/permissions"

# فحص دور المستخدم
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:8002/api/auth/role"
```

## 📝 نصائح مهمة

### 1. أفضل الممارسات
- **استخدم التدفق**: `stream=True` للملفات الكبيرة
- **فعل الضغط**: `compress=true` لتوفير عرض النطاق
- **راقب التقدم**: للمهام الطويلة
- **معالج الأخطاء**: دائماً استخدم try/except

### 2. حدود النظام
- **الحد الأقصى للطلبات**: 60 طلب/دقيقة
- **حجم التصدير الأقصى**: 1000 ميجابايت
- **مهلة الاستجابة**: 300 ثانية
- **حجم الدفعة**: 1000-5000 سجل

### 3. تحسين الأداء
- **استخدم المرشحات**: لتقليل حجم البيانات
- **فعل التخزين المؤقت**: للاستعلامات المتكررة
- **استخدم الفهارس**: في قاعدة البيانات
- **راقب الذاكرة**: لتجنب نفادها

---

**🎯 هذا المرجع يوفر جميع الأوامر والأمثلة اللازمة للتعامل مع نظام تدفق البيانات الكبيرة بكفاءة!**
