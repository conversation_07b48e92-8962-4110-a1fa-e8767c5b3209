# تحديثات فاتورة المبيعات - نظام العملاء والمديونية

## 📋 نظرة عامة

تم تحديث فاتورة المبيعات لتشمل نظام العملاء والمديونية الجديد مع تحسينات شاملة في العرض والوظائف.

## 🆕 الميزات الجديدة

### 1. معلومات العميل
- **عرض تفاصيل العميل**: الاسم، الهاتف، البريد الإلكتروني، العنوان
- **إجمالي المديونية**: عرض المديونية الإجمالية للعميل
- **إخفاء العميل المباشر**: لا يتم عرض قسم العميل للعملاء المباشرين

### 2. معلومات الدفع المحسنة
- **حالة الدفع**: مدفوع، جزئي، آجل
- **المبلغ المدفوع الفعلي**: عرض المبلغ المدفوع فعلياً
- **المديونية المتبقية**: عرض المبلغ المتبقي كمديونية
- **مؤشرات بصرية**: ألوان مختلفة لحالات الدفع المختلفة

### 3. QR Code محسن
- **معلومات العميل**: تشمل اسم وهاتف العميل
- **تفاصيل الدفع**: المبلغ المدفوع والمديونية
- **معلومات شاملة**: جميع تفاصيل الفاتورة في رمز واحد

## 🎨 التحسينات البصرية

### الألوان والمؤشرات
- **✓ مدفوع بالكامل**: أخضر (#16a34a)
- **◐ مدفوع جزئياً**: برتقالي (#ea580c)
- **○ آجل**: أحمر (#dc2626)
- **المديونية**: خلفية حمراء فاتحة مع حدود

### التخطيط
- **قسم معلومات العميل**: تصميم منفصل ومميز
- **تحسين المساحات**: توزيع أفضل للمعلومات
- **الوضع المظلم**: دعم كامل للوضع المظلم

## 📊 هيكل البيانات الجديد

### واجهة SaleDetail المحدثة
```typescript
interface SaleDetail {
  // الحقول الأساسية
  id: number;
  total_amount: number;
  payment_method: string;
  tax_amount: number;
  discount_amount: number;
  
  // حقول العميل الجديدة
  customer_id: number | null;
  customer_name: string | null;
  
  // حقول الدفع الجديدة
  amount_paid: number;
  payment_status: string; // 'paid', 'partial', 'credit'
  
  // معلومات العميل
  customer?: {
    id: number;
    name: string;
    phone?: string;
    email?: string;
    address?: string;
    total_debt?: number;
  };
}
```

## 🔧 التحديثات التقنية

### 1. جلب البيانات
- **معلومات العميل**: جلب تلقائي من API العملاء
- **معالجة الأخطاء**: تعامل مع عدم توفر بيانات العميل
- **التوافق العكسي**: دعم الفواتير القديمة

### 2. حساب المبالغ
- **المبلغ المدفوع**: استخدام `amount_paid` الفعلي
- **المديونية**: حساب الفرق بين المجموع النهائي والمبلغ المدفوع
- **حالة الدفع**: تحديد تلقائي بناءً على المبالغ

### 3. QR Code
- **بيانات ديناميكية**: تتغير حسب نوع العميل وحالة الدفع
- **معلومات شاملة**: تشمل جميع التفاصيل المهمة
- **تنسيق محسن**: عرض منظم وواضح

## 📱 التوافق

### أحجام الفاتورة
- **صغير (58mm)**: عرض مبسط للمعلومات الأساسية
- **متوسط (80mm)**: عرض كامل مع QR Code
- **A4**: عرض شامل مع جميع التفاصيل والختم

### الأجهزة
- **سطح المكتب**: عرض كامل مع جميع الميزات
- **الجوال**: تخطيط متجاوب ومحسن
- **الطباعة**: تحسينات خاصة للطباعة

## 🚀 كيفية الاستخدام

### 1. فاتورة مع عميل
```
- يتم عرض قسم "معلومات العميل"
- تظهر تفاصيل الاتصال والمديونية
- QR Code يشمل معلومات العميل
```

### 2. فاتورة مباشرة
```
- لا يتم عرض قسم العميل
- التركيز على تفاصيل المنتجات والدفع
- QR Code يشمل معلومات المؤسسة فقط
```

### 3. دفع جزئي
```
- عرض المبلغ المدفوع والمتبقي
- مؤشر بصري لحالة الدفع
- تسجيل المديونية تلقائياً
```

## 🔄 التوافق العكسي

- **الفواتير القديمة**: تعمل بدون مشاكل
- **البيانات المفقودة**: قيم افتراضية آمنة
- **API القديم**: دعم كامل للهياكل السابقة

## 📝 ملاحظات التطوير

### الملفات المحدثة
- `frontend/src/pages/Receipt.tsx`: المنطق الأساسي
- `frontend/src/pages/Receipt.css`: الأنماط والتصميم

### التبعيات
- لا توجد تبعيات جديدة مطلوبة
- استخدام المكتبات الموجودة فقط

### الاختبار
- اختبار مع عملاء مختلفين
- اختبار حالات الدفع المختلفة
- اختبار أحجام الفاتورة المختلفة
