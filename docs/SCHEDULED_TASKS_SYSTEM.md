# نظام جدولة المهام للنسخ الاحتياطية

## 📋 نظرة عامة

تم تطوير نظام شامل لجدولة المهام التلقائية في SmartPOS، مع التركيز على النسخ الاحتياطية وصيانة النظام. يستخدم النظام مكتبة APScheduler لإدارة المهام في الخلفية ويوفر واجهة مستخدم سهلة لإدارة هذه المهام.

## 🏗️ البنية التقنية

### الباك إند

#### 1. المودلز (Models)
- **ScheduledTask**: نموذج قاعدة البيانات لحفظ المهام المجدولة
- **TaskType**: أنواع المهام (نسخ احتياطي، تنظيف، صيانة)
- **TaskStatus**: حالات المهام (نشط، متوقف، معطل)

#### 2. الخدمات (Services)
- **SchedulerService**: خدمة APScheduler لإدارة المهام
- **DatabaseBackup**: خدمة النسخ الاحتياطي المحدثة

#### 3. واجهة API
- **GET /api/scheduled-tasks**: جلب قائمة المهام
- **POST /api/scheduled-tasks**: إنشاء مهمة جديدة
- **PUT /api/scheduled-tasks/{id}**: تحديث مهمة
- **DELETE /api/scheduled-tasks/{id}**: حذف مهمة
- **POST /api/scheduled-tasks/{id}/toggle**: تفعيل/إيقاف مهمة
- **POST /api/scheduled-tasks/{id}/run-now**: تشغيل مهمة فوراً

### الفرونت إند

#### 1. المكونات (Components)
- **ScheduledTasksManager**: مكون إدارة المهام الرئيسي
- **CronBuilder**: مكون بناء تعبيرات Cron بصرياً

#### 2. الميزات
- واجهة سهلة لإنشاء وتعديل المهام
- منشئ Cron بصري مع قوالب محددة مسبقاً
- عرض حالة المهام ومعلومات التنفيذ
- إدارة أخطاء التنفيذ

## 🎯 أنواع المهام المتاحة

### 1. نسخ احتياطي لقاعدة البيانات
- **الوصف**: إنشاء نسخة احتياطية من قاعدة البيانات
- **المعاملات**: مسار النسخ الاحتياطي
- **الافتراضي**: يومياً في الساعة 2:00 صباحاً

### 2. تنظيف النسخ الاحتياطية القديمة
- **الوصف**: حذف النسخ الاحتياطية القديمة والاحتفاظ بعدد محدد
- **المعاملات**: عدد النسخ للاحتفاظ بها (افتراضي: 30)
- **الافتراضي**: أسبوعياً يوم الأحد في الساعة 3:00 صباحاً

## 📅 تعبيرات Cron

### القوالب المحددة مسبقاً
- **كل دقيقة**: `* * * * *`
- **كل ساعة**: `0 * * * *`
- **يومياً (2:00 ص)**: `0 2 * * *`
- **أسبوعياً (الأحد 3:00 ص)**: `0 3 * * 0`
- **شهرياً (اليوم الأول 4:00 ص)**: `0 4 1 * *`
- **أيام العمل (8:00 ص)**: `0 8 * * 1-5`

### تنسيق Cron
```
دقيقة ساعة يوم شهر يوم_الأسبوع
```

## 🚀 كيفية الاستخدام

### 1. الوصول إلى النظام
1. اذهب إلى **صفحة الإعدادات**
2. اختر قسم **"النسخ الاحتياطية"**
3. ستجد مكون **"المهام المجدولة"** أسفل إعدادات المسار

### 2. إنشاء مهمة جديدة
1. انقر على **"إضافة مهمة"**
2. أدخل **اسم المهمة** ووصفها
3. اختر **نوع المهمة**
4. حدد **جدولة التشغيل** باستخدام CronBuilder
5. انقر **"إنشاء المهمة"**

### 3. إدارة المهام الموجودة
- **تعديل**: انقر على أيقونة التعديل
- **تفعيل/إيقاف**: انقر على أيقونة التشغيل/الإيقاف
- **تشغيل فوراً**: انقر على أيقونة التشغيل
- **حذف**: انقر على أيقونة الحذف (للمهام غير الأساسية)

## 🔧 الإعداد والتثبيت

### متطلبات الباك إند
```bash
pip install apscheduler>=3.10.0
```

### إنشاء الجداول والمهام الافتراضية
```bash
cd backend
python scripts/create_scheduled_tasks_table.py
```

### بدء الخدمة
الخدمة تبدأ تلقائياً مع تشغيل الخادم الخلفي.

## 📊 مراقبة المهام

### معلومات المهمة
- **الحالة**: نشط، متوقف، معطل
- **آخر تشغيل**: تاريخ ووقت آخر تنفيذ
- **التشغيل التالي**: تاريخ ووقت التنفيذ القادم
- **عدد التشغيلات**: إجمالي مرات التنفيذ
- **عدد الأخطاء**: عدد مرات الفشل
- **آخر خطأ**: تفاصيل آخر خطأ حدث

### حالات المهام
- **🟢 نشط**: المهمة تعمل حسب الجدولة
- **🟡 متوقف**: المهمة متوقفة مؤقتاً
- **🔴 معطل**: المهمة معطلة نهائياً

## 🛡️ الأمان والصلاحيات

- **صلاحيات المدير فقط**: جميع عمليات إدارة المهام
- **حماية المهام الأساسية**: لا يمكن حذف مهام النظام
- **تشفير المعاملات**: معاملات المهام محفوظة بصيغة JSON آمنة

## 🔍 استكشاف الأخطاء

### مشاكل شائعة
1. **المهمة لا تعمل**: تحقق من حالة المهمة وتعبير Cron
2. **خطأ في المسار**: تأكد من صحة مسار النسخ الاحتياطي
3. **فشل النسخ الاحتياطي**: تحقق من صلاحيات الكتابة في المجلد

### السجلات
```bash
# عرض سجلات الخادم
tail -f backend/logs/app.log
```

## 📈 التطوير المستقبلي

### ميزات مخططة
- [ ] إشعارات البريد الإلكتروني عند فشل المهام
- [ ] تقارير أداء المهام
- [ ] نسخ احتياطي للملفات والمجلدات
- [ ] مهام صيانة قاعدة البيانات
- [ ] واجهة مراقبة متقدمة

### تحسينات تقنية
- [ ] دعم المهام الموزعة
- [ ] تحسين أداء المجدول
- [ ] دعم أولويات المهام
- [ ] آلية إعادة المحاولة المتقدمة

## 📞 الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. راجع هذا الدليل أولاً
2. تحقق من السجلات
3. تواصل مع فريق التطوير

---

**تم التطوير بواسطة**: فريق SmartPOS  
**آخر تحديث**: ديسمبر 2024  
**الإصدار**: 1.0.0
