# 🔧 حل مشكلة عدم حفظ خط العرض (store_latitude)

## 📋 المشكلة

يتم حفظ خط الطول (store_longitude) فقط في قاعدة البيانات، بينما لا يتم حفظ خط العرض (store_latitude).

## 🔍 الأسباب المحتملة

### 1. **حقل store_latitude غير موجود في قاعدة البيانات**
- قد يكون الحقل لم يتم إنشاؤه بعد
- أو تم حذفه بطريق الخطأ

### 2. **مشكلة في ترتيب العمليات**
- قد تكون هناك مشكلة في تسلسل حفظ البيانات

### 3. **مشكلة في الكود**
- خطأ في منطق الحفظ

## ✅ الحلول المطبقة

### 1. **إضافة تسجيل مفصل**
تم إضافة تسجيل شامل لتتبع العملية:

```javascript
// في handleAddressSelect
console.log('🔍 الإحداثيات المستلمة:', coordinates);
console.log('💾 حفظ store_latitude:', latitude);
console.log('💾 حفظ store_longitude:', longitude);

// في saveSettings
console.log('📍 سيتم تحديث store_latitude إلى:', latitudeUpdate.value);
console.log('📍 سيتم تحديث store_longitude إلى:', longitudeUpdate.value);
```

### 2. **سكريبت إضافة الحقل**
تم إنشاء سكريپت SQL لضمان وجود الحقل:

```sql
-- إضافة store_latitude إذا لم يكن موجوداً
INSERT OR IGNORE INTO settings (key, value, description) 
VALUES ('store_latitude', '32.8872', 'خط العرض لموقع المتجر (Latitude)');
```

### 3. **التحقق من صحة البيانات**
تم إضافة فحص شامل للإحداثيات قبل الحفظ.

## 🧪 خطوات التشخيص

### 1. **فحص قاعدة البيانات**
```sql
-- التحقق من وجود الحقول
SELECT key, value, description 
FROM settings 
WHERE key IN ('store_latitude', 'store_longitude');
```

### 2. **فحص Console Logs**
1. افتح Developer Tools (F12)
2. انتقل إلى Console
3. افتح نافذة اختيار العنوان
4. اختر موقع جديد
5. راقب الرسائل في Console

### 3. **فحص Network Requests**
1. افتح Developer Tools → Network
2. احفظ الإعدادات
3. ابحث عن طلب `/api/settings/batch`
4. تحقق من البيانات المرسلة

## 🔧 خطوات الإصلاح

### الخطوة 1: إضافة الحقل المفقود
```sql
-- تشغيل هذا السكريپت على قاعدة البيانات
INSERT OR IGNORE INTO settings (key, value, description) 
VALUES ('store_latitude', '32.8872', 'خط العرض لموقع المتجر (Latitude)');
```

### الخطوة 2: اختبار النظام
1. افتح الإعدادات → معلومات المؤسسة
2. افتح Developer Console
3. اضغط على زر الخريطة
4. اختر موقع جديد
5. راقب الرسائل في Console
6. احفظ الإعدادات
7. تحقق من قاعدة البيانات

### الخطوة 3: التحقق من النتيجة
```sql
-- التحقق من حفظ الإحداثيات
SELECT 
    key,
    value,
    CASE 
        WHEN key = 'store_latitude' AND value IS NOT NULL AND value != '' 
        THEN '✅ محفوظ'
        WHEN key = 'store_longitude' AND value IS NOT NULL AND value != '' 
        THEN '✅ محفوظ'
        ELSE '❌ غير محفوظ'
    END as status
FROM settings 
WHERE key IN ('store_latitude', 'store_longitude');
```

## 📊 رسائل Console المتوقعة

### عند اختيار الموقع:
```
🔍 الإحداثيات المستلمة: {lat: 32.887200, lng: 13.183883}
💾 حفظ store_latitude: 32.887200
💾 حفظ store_longitude: 13.183883
✅ تم تحديث إحداثيات المتجر في الحالة المحلية
```

### عند حفظ الإعدادات:
```
💾 الإعدادات المراد تحديثها: [
  {key: "store_latitude", value: "32.887200"},
  {key: "store_longitude", value: "13.183883"}
]
📍 سيتم تحديث store_latitude إلى: 32.887200
📍 سيتم تحديث store_longitude إلى: 13.183883
🗺️ سيتم تحديث الإحداثيات الكاملة: {lat: "32.887200", lng: "13.183883"}
```

## ⚠️ رسائل الخطأ المحتملة

### إذا لم يتم حفظ store_latitude:
```
⚠️ سيتم تحديث إحداثية واحدة فقط! {latitude: undefined, longitude: "13.183883"}
```

### إذا كانت الإحداثيات غير صحيحة:
```
❌ إحداثيات غير صحيحة: {lat: NaN, lng: 13.183883}
```

## 🎯 النتيجة المطلوبة

بعد تطبيق الإصلاحات، يجب أن تظهر في قاعدة البيانات:

| key | value | description |
|-----|-------|-------------|
| store_latitude | 32.887200 | خط العرض لموقع المتجر |
| store_longitude | 13.183883 | خط الطول لموقع المتجر |

## 🚀 الخطوات التالية

1. **تشغيل سكريپت إضافة الحقل**
2. **اختبار النظام مع Console مفتوح**
3. **التحقق من قاعدة البيانات**
4. **تأكيد عمل النظام بشكل صحيح**

---

**ملاحظة**: إذا استمرت المشكلة، يرجى مشاركة رسائل Console للمساعدة في التشخيص الدقيق.

**تاريخ الإنشاء**: يوليو 2025  
**الإصدار**: 1.3.2  
**المطور**: SmartPOS Team
