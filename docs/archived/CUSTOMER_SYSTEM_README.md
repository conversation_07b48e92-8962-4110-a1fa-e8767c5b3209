# نظام إدارة العملاء والمديونية - SmartPOS

## نظرة عامة

تم إضافة نظام شامل لإدارة العملاء والمديونية إلى تطبيق SmartPOS. يتيح هذا النظام:

- إدارة بيانات العملاء
- تتبع المديونية والدفعات
- ربط المبيعات بالعملاء
- إدارة دفعات المديونية

## الميزات الجديدة

### 1. إدارة العملاء
- إضافة وتعديل وحذف العملاء
- تخزين معلومات الاتصال (الهاتف، البريد الإلكتروني، العنوان)
- تتبع إجمالي المبيعات والمديونية لكل عميل
- البحث والفلترة

### 2. إدارة المديونية
- إضافة مديونيات جديدة للعملاء
- تتبع المبلغ الإجمالي والمبلغ المتبقي
- تسجيل دفعات المديونية
- طرق دفع متعددة (نقدي، بطاقة، تحويل بنكي، شيك)

### 3. ربط المبيعات بالعملاء
- اختيار العميل عند إنشاء مبيعة جديدة
- عرض معلومات العميل في تفاصيل المبيعة
- تتبع تاريخ المبيعات لكل عميل

## التثبيت والإعداد

### 1. تشغيل Migration قاعدة البيانات

```bash
cd backend
python run_customer_migration.py
```

### 2. إعادة تشغيل الخادم

```bash
cd backend
python main.py
```

### 3. إعادة تشغيل الواجهة الأمامية

```bash
cd frontend
npm start
```

## استخدام النظام

### صفحة العملاء (`/customers`)

1. **إضافة عميل جديد:**
   - انقر على "إضافة عميل جديد"
   - أدخل البيانات المطلوبة (الاسم مطلوب)
   - احفظ البيانات

2. **البحث والفلترة:**
   - استخدم مربع البحث للبحث بالاسم أو الهاتف أو البريد الإلكتروني
   - استخدم زر "النشطون فقط" لعرض العملاء النشطين فقط

3. **تعديل العملاء:**
   - انقر على أيقونة التعديل
   - عدل البيانات واحفظ

4. **حذف العملاء:**
   - انقر على أيقونة الحذف
   - تأكد من الحذف (لا يمكن حذف عميل له مديونيات غير مدفوعة)

### صفحة المديونية (`/debts`)

1. **إضافة مديونية جديدة:**
   - انقر على "إضافة مديونية جديدة"
   - اختر العميل والمبلغ
   - أضف وصف اختياري

2. **تسجيل دفعة:**
   - انقر على أيقونة البطاقة بجانب المديونية غير المدفوعة
   - أدخل مبلغ الدفعة وطريقة الدفع
   - احفظ الدفعة

3. **الفلترة:**
   - فلتر حسب العميل
   - عرض المديونيات غير المدفوعة فقط أو جميع المديونيات

### في صفحة POS

1. **اختيار العميل:**
   - استخدم مكون اختيار العميل الجديد
   - ابحث عن العميل أو أضف عميل جديد
   - ستظهر معلومات المديونية إن وجدت

2. **إنشاء مبيعة مع عميل:**
   - اختر المنتجات كالمعتاد
   - اختر العميل
   - أكمل عملية البيع

## API Endpoints الجديدة

### العملاء
- `GET /api/customers` - جلب جميع العملاء
- `POST /api/customers` - إضافة عميل جديد
- `GET /api/customers/{id}` - جلب عميل محدد
- `PUT /api/customers/{id}` - تحديث عميل
- `DELETE /api/customers/{id}` - حذف عميل

### المديونية
- `GET /api/debts` - جلب جميع المديونيات
- `POST /api/debts` - إضافة مديونية جديدة
- `GET /api/debts/{id}` - جلب مديونية محددة
- `PUT /api/debts/{id}` - تحديث مديونية
- `POST /api/debts/{id}/payments` - إضافة دفعة
- `GET /api/debts/customer/{id}/summary` - ملخص مديونية العميل

## قاعدة البيانات

### جداول جديدة:

1. **customers** - بيانات العملاء
2. **customer_debts** - المديونيات
3. **debt_payments** - دفعات المديونية

### تحديثات على الجداول الموجودة:

- إضافة عمود `customer_id` إلى جدول `sales`

## الأمان والصلاحيات

- جميع العمليات تتطلب تسجيل دخول
- المستخدمون العاديون يمكنهم عرض وإدارة العملاء والمديونية
- المديرون لديهم صلاحيات كاملة

## ملاحظات مهمة

1. **النسخ الاحتياطي:** تأكد من عمل نسخة احتياطية من قاعدة البيانات قبل تشغيل Migration
2. **البيانات الموجودة:** المبيعات الموجودة ستبقى كما هي، ولكن لن تكون مرتبطة بعملاء
3. **الأداء:** تم إضافة فهارس لتحسين الأداء
4. **التوافق:** النظام متوافق مع الإصدارات السابقة

## استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في Migration:**
   - تأكد من وجود ملف قاعدة البيانات
   - تحقق من الصلاحيات

2. **عدم ظهور الصفحات الجديدة:**
   - تأكد من إعادة تشغيل الخادم والواجهة الأمامية
   - امسح cache المتصفح

3. **مشاكل في API:**
   - تحقق من logs الخادم
   - تأكد من تشغيل Migration بنجاح

## الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.
