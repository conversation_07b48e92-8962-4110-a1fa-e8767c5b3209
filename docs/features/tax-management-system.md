# 📊 نظام إدارة الضرائب المتقدم - دليل شامل

> **تاريخ الإنشاء**: أغسطس 2025  
> **آخر تحديث**: أغسطس 2025  
> **الإصدار**: 1.0.0  
> **المطور**: Augment Agent  
> **النوع**: نظام إدارة ضرائب متكامل

## 📖 نظرة عامة

تم تطوير نظام إدارة الضرائب المتقدم ليكون جزءاً متكاملاً من نظام SmartPOS، يوفر إدارة شاملة لأنواع الضرائب والقيم الضريبية مع واجهة مستخدم احترافية وتصميم موحد مع باقي النظام.

## 🎯 الأهداف المحققة

### ✅ الميزات الأساسية:
- **إدارة أنواع الضرائب**: إنشاء وتعديل وحذف أنواع الضرائب المختلفة
- **إدارة القيم الضريبية**: تحديد النسب والقيم الضريبية لكل نوع
- **فئات الضرائب المتعددة**: معيارية، مخفضة، صفر، معفاة
- **طرق الحساب المتنوعة**: نسبة مئوية أو مبلغ ثابت
- **الضرائب المركبة**: دعم الضرائب المحسوبة على ضرائب أخرى
- **تواريخ السريان**: تحديد فترات سريان القيم الضريبية
- **نطاق التطبيق**: تطبيق على جميع العناصر أو المنتجات أو الخدمات فقط

### ✅ الواجهة والتصميم:
- **تصميم موحد**: متوافق مع تصميم النظام العام
- **جداول تفاعلية**: عرض البيانات بطريقة منظمة وقابلة للبحث
- **فلاتر متقدمة**: بحث وتصفية حسب معايير متعددة
- **نماذج منبثقة**: إضافة وتعديل البيانات بسهولة
- **دعم الوضع المظلم**: متوافق مع جميع أوضاع العرض

## 🏗️ الهيكل التقني

### Backend (Python/FastAPI):

#### النماذج (Models):
```python
# models/tax_type.py
class TaxType(Base):
    - id: معرف فريد
    - name: الاسم بالإنجليزية
    - name_ar: الاسم بالعربية
    - description: الوصف
    - tax_category: فئة الضريبة
    - calculation_method: طريقة الحساب
    - is_compound: ضريبة مركبة
    - is_active: حالة التفعيل
    - sort_order: ترتيب العرض

# models/tax_rate.py
class TaxRate(Base):
    - id: معرف فريد
    - tax_type_id: معرف نوع الضريبة
    - name: اسم القيمة الضريبية
    - rate_value: القيمة (نسبة أو مبلغ)
    - effective_from: تاريخ بداية السريان
    - effective_to: تاريخ نهاية السريان
    - is_default: القيمة الافتراضية
    - applies_to: نطاق التطبيق
    - min_amount: الحد الأدنى للمبلغ
    - max_amount: الحد الأقصى للمبلغ
```

#### الخدمات (Services):
```python
# services/tax/tax_type_service.py
class TaxTypeService:
    - get_tax_types(): جلب أنواع الضرائب مع الفلاتر
    - create_tax_type(): إنشاء نوع ضريبة جديد
    - update_tax_type(): تحديث نوع ضريبة موجود
    - delete_tax_type(): حذف نوع ضريبة
    - get_tax_type_statistics(): إحصائيات أنواع الضرائب

# services/tax/tax_rate_service.py
class TaxRateService:
    - get_tax_rates(): جلب القيم الضريبية مع الفلاتر
    - create_tax_rate(): إنشاء قيمة ضريبية جديدة
    - update_tax_rate(): تحديث قيمة ضريبية موجودة
    - delete_tax_rate(): حذف قيمة ضريبية
    - calculate_tax(): حساب الضريبة على مبلغ معين
```

#### API Endpoints:
```
GET    /api/tax-types/              # جلب أنواع الضرائب
POST   /api/tax-types/              # إنشاء نوع ضريبة جديد
PUT    /api/tax-types/{id}          # تحديث نوع ضريبة
DELETE /api/tax-types/{id}          # حذف نوع ضريبة
GET    /api/tax-types/statistics    # إحصائيات أنواع الضرائب

GET    /api/tax-rates/              # جلب القيم الضريبية
POST   /api/tax-rates/              # إنشاء قيمة ضريبية جديدة
PUT    /api/tax-rates/{id}          # تحديث قيمة ضريبية
DELETE /api/tax-rates/{id}          # حذف قيمة ضريبية
POST   /api/tax-rates/calculate     # حساب الضريبة
```

### Frontend (React/TypeScript):

#### المكونات (Components):
```typescript
// components/catalog/TaxTypesTab.tsx
- تبويب أنواع الضرائب الرئيسي

// components/catalog/TaxTypesDataTable.tsx
- جدول البيانات التفاعلي لأنواع الضرائب
- البحث والفلترة المتقدمة
- النماذج المنبثقة للإضافة والتعديل
- إدارة الحذف مع التأكيد
```

#### المخازن (Stores):
```typescript
// stores/taxTypeStore.ts
- إدارة حالة أنواع الضرائب
- العمليات CRUD
- معالجة الأخطاء

// stores/taxRateStore.ts
- إدارة حالة القيم الضريبية
- حساب الضرائب
- الفلترة والبحث
```

## 📊 البيانات الافتراضية

### أنواع الضرائب الافتراضية:
1. **ضريبة القيمة المضافة** (VAT) - معيارية - نسبة مئوية
2. **ضريبة الخدمات** - معيارية - نسبة مئوية
3. **ضريبة القيمة المضافة المخفضة** - مخفضة - نسبة مئوية
4. **معدل صفر** - صفر - نسبة مئوية
5. **معفى من الضريبة** - معفاة - نسبة مئوية

### القيم الضريبية الافتراضية:
- **ضريبة القيمة المضافة 15%** - للسلع والخدمات العامة
- **ضريبة الخدمات 5%** - للخدمات المقدمة
- **ضريبة مخفضة 5%** - للسلع الأساسية
- **معدل صفر 0%** - للسلع المعفاة
- **معفى من الضريبة** - للسلع والخدمات المعفاة

## 🚀 التثبيت والإعداد

### 1. تشغيل Migration:
```bash
# من مجلد backend
python run_tax_migration.py
```

### 2. إعادة تشغيل الخادم:
```bash
# إعادة تشغيل FastAPI
python main.py
```

### 3. الوصول للنظام:
- انتقل إلى صفحة إدارة الفهرس
- اختر تبويب "أنواع الضرائب"
- ابدأ في إدارة الضرائب

## 📋 دليل الاستخدام

### إضافة نوع ضريبة جديد:
1. انقر على "إضافة نوع ضريبة"
2. أدخل الاسم بالعربية والإنجليزية
3. اختر فئة الضريبة (معيارية، مخفضة، صفر، معفاة)
4. حدد طريقة الحساب (نسبة مئوية أو مبلغ ثابت)
5. فعّل "ضريبة مركبة" إذا لزم الأمر
6. احفظ التغييرات

### إضافة قيمة ضريبية:
1. اختر نوع الضريبة المطلوب
2. انقر على "إضافة قيمة ضريبية"
3. أدخل اسم القيمة والنسبة/المبلغ
4. حدد تواريخ السريان (اختياري)
5. اختر نطاق التطبيق (جميع العناصر، المنتجات، الخدمات)
6. حدد الحدود الدنيا والعليا للمبلغ (اختياري)
7. احفظ التغييرات

### البحث والفلترة:
- **البحث النصي**: ابحث في أسماء الضرائب والأوصاف
- **فلتر الحالة**: نشط، غير نشط، أو الكل
- **فلتر الفئة**: معيارية، مخفضة، صفر، معفاة
- **فلتر طريقة الحساب**: نسبة مئوية أو مبلغ ثابت

## 🔧 الميزات المتقدمة

### حساب الضريبة:
```typescript
// استخدام API لحساب الضريبة
const result = await taxRateStore.calculateTax({
  tax_rate_id: 1,
  base_amount: 100
});
// النتيجة: { base_amount: 100, tax_amount: 15, total_amount: 115 }
```

### الضرائب المركبة:
- إمكانية تطبيق ضريبة على ضريبة أخرى
- حساب تلقائي للضرائب المتراكمة
- دعم سلاسل الضرائب المعقدة

### تواريخ السريان:
- تحديد فترات زمنية لسريان القيم الضريبية
- تطبيق تلقائي للقيم الصحيحة حسب التاريخ
- إدارة التغييرات الضريبية المجدولة

## 📈 الإحصائيات والتقارير

### إحصائيات أنواع الضرائب:
- إجمالي أنواع الضرائب
- عدد الأنواع النشطة وغير النشطة
- توزيع الأنواع حسب الفئة
- توزيع الأنواع حسب طريقة الحساب

### إحصائيات القيم الضريبية:
- إجمالي القيم الضريبية
- عدد القيم النشطة والسارية
- توزيع القيم حسب نطاق التطبيق
- متوسط النسب الضريبية

## 🔒 الأمان والصلاحيات

### صلاحيات المدير:
- إنشاء وتعديل وحذف أنواع الضرائب
- إنشاء وتعديل وحذف القيم الضريبية
- الوصول لجميع الإحصائيات

### صلاحيات المستخدم العادي:
- عرض أنواع الضرائب والقيم
- استخدام حاسبة الضرائب
- عرض الإحصائيات الأساسية

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### خطأ في تحميل أنواع الضرائب:
```
الحل: تأكد من تشغيل migration وإعادة تشغيل الخادم
```

#### خطأ في حفظ البيانات:
```
الحل: تحقق من صحة البيانات المدخلة والصلاحيات
```

#### مشكلة في عرض التبويب:
```
الحل: تأكد من إضافة المسار في AppContent.tsx
```

## 🔄 التطويرات المستقبلية

### المرحلة القادمة (الإصدار 2.0.0):
1. **تكامل مع المنتجات**: ربط الضرائب بالمنتجات تلقائياً
2. **تكامل مع المبيعات**: تطبيق الضرائب في عمليات البيع
3. **تقارير ضريبية متقدمة**: تقارير مفصلة عن الضرائب المحصلة
4. **استيراد/تصدير**: إمكانية استيراد وتصدير إعدادات الضرائب
5. **قوالب ضريبية**: قوالب جاهزة لدول مختلفة
6. **حاسبة ضرائب متقدمة**: واجهة تفاعلية لحساب الضرائب

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. راجع هذا التوثيق أولاً
2. تحقق من ملفات السجل (logs)
3. تواصل مع فريق التطوير

---

**تم إنشاء هذا التوثيق بواسطة Augment Agent - أغسطس 2025**
