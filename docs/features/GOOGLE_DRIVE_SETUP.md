# دليل إعداد Google Drive للنسخ الاحتياطية

## نظرة عامة

تتيح ميزة Google Drive للنسخ الاحتياطية رفع النسخ الاحتياطية تلقائياً إلى Google Drive، مما يوفر حماية إضافية للبيانات في السحابة.

## طرق الإعداد

### الطريقة الأولى: تسجيل الدخول التفاعلي (الموصى بها) 🌟

هذه الطريقة الأسهل والأكثر أماناً - لا تحتاج لتعامل مع ملفات JSON معقدة!

#### المتطلبات
1. حساب Google
2. مشروع في Google Cloud Console
3. تفعيل Google Drive API
4. OAuth 2.0 Client ID و Client Secret

### الطريقة الثانية: رفع ملف JSON يدوياً

للمستخدمين المتقدمين الذين يفضلون التحكم الكامل في العملية.

## الطريقة الأولى: تسجيل الدخول التفاعلي 🌟

### خطوات الإعداد

#### 1. إنشاء مشروع Google Cloud

1. اذهب إلى [Google Cloud Console](https://console.cloud.google.com/)
2. انقر على "إنشاء مشروع" أو اختر مشروع موجود
3. أدخل اسم المشروع (مثل: SmartPOS Backup)
4. انقر على "إنشاء"

#### 2. تفعيل Google Drive API

1. في Google Cloud Console، اذهب إلى "APIs & Services" > "Library"
2. ابحث عن "Google Drive API"
3. انقر على "Google Drive API"
4. انقر على "تفعيل" (Enable)

#### 3. إعداد OAuth consent screen

1. اذهب إلى "APIs & Services" > "OAuth consent screen"
2. اختر "External" للاستخدام العام
3. املأ المعلومات المطلوبة:
   - اسم التطبيق: SmartPOS
   - البريد الإلكتروني للدعم
   - شعار التطبيق (اختياري)
4. في قسم "Scopes"، أضف: `https://www.googleapis.com/auth/drive.file`
5. احفظ الإعدادات

#### 4. إنشاء OAuth 2.0 Client ID

1. اذهب إلى "APIs & Services" > "Credentials"
2. انقر على "إنشاء بيانات الاعتماد" > "OAuth client ID"
3. اختر "Web application"
4. أدخل اسم للعميل (مثل: SmartPOS Web)
5. في "Authorized redirect URIs"، أضف:
   ```
   http://localhost:8000/api/google-drive/oauth-callback
   ```
6. انقر على "إنشاء"
7. **انسخ Client ID و Client Secret** - ستحتاجهما في الخطوة التالية

#### 5. تكوين SmartPOS

1. افتح SmartPOS
2. اذهب إلى **الإعدادات** > **Google Drive**
3. انقر على **"تسجيل الدخول مع Google"**
4. أدخل **Client ID** و **Client Secret** من الخطوة السابقة
5. انقر على **"تسجيل الدخول"**
6. ستفتح نافذة جديدة لتسجيل الدخول مع Google
7. سجل الدخول بحساب Google الخاص بك
8. امنح الأذونات المطلوبة
9. ستعود تلقائياً إلى SmartPOS مع رسالة نجاح

#### 6. تفعيل النسخ الاحتياطي

1. في إعدادات Google Drive، فعل **"تفعيل Google Drive"**
2. اذهب إلى **الإعدادات** > **النسخ الاحتياطية** > **المهام المجدولة**
3. فعل المهام:
   - **"نسخ احتياطي يومي إلى Google Drive"**
   - **"تنظيف Google Drive أسبوعياً"**

---

## الطريقة الثانية: رفع ملف JSON يدوياً

### خطوات الإعداد (للمستخدمين المتقدمين)

#### 1-3. نفس الخطوات أعلاه

اتبع الخطوات 1-3 من الطريقة الأولى.

#### 4. إنشاء OAuth 2.0 Client ID (مختلف)

1. اذهب إلى "APIs & Services" > "Credentials"
2. انقر على "إنشاء بيانات الاعتماد" > "OAuth client ID"
3. اختر **"Desktop application"** (مختلف عن الطريقة الأولى)
4. أدخل اسم للعميل (مثل: SmartPOS Desktop)
5. انقر على "إنشاء"

#### 5. تحميل ملف الاعتماد

1. بعد إنشاء OAuth client ID، انقر على "تحميل JSON"
2. احفظ الملف في مكان آمن
3. افتح الملف وانسخ محتوياته

### 5. الحصول على رمز التفويض

لأن التطبيق يعمل على الخادم، ستحتاج للحصول على رمز التفويض يدوياً:

#### الطريقة الأولى: استخدام Google OAuth Playground

1. اذهب إلى [Google OAuth 2.0 Playground](https://developers.google.com/oauthplayground/)
2. انقر على الترس في الزاوية اليمنى العلوية
3. ضع علامة على "Use your own OAuth credentials"
4. أدخل OAuth Client ID و OAuth Client Secret من ملف JSON
5. في الجانب الأيسر، ابحث عن "Drive API v3"
6. اختر `https://www.googleapis.com/auth/drive.file`
7. انقر على "Authorize APIs"
8. سجل الدخول بحساب Google الخاص بك
9. امنح الأذونات المطلوبة
10. انقر على "Exchange authorization code for tokens"
11. انسخ الـ refresh_token

#### الطريقة الثانية: استخدام سكريبت Python

```python
from google_auth_oauthlib.flow import InstalledAppFlow

SCOPES = ['https://www.googleapis.com/auth/drive.file']

def get_credentials():
    flow = InstalledAppFlow.from_client_secrets_file(
        'path/to/credentials.json', SCOPES)
    creds = flow.run_local_server(port=0)

    # طباعة بيانات الاعتماد
    print("Credentials JSON:")
    print(creds.to_json())

    return creds

if __name__ == '__main__':
    get_credentials()
```

### 6. تكوين النظام

1. افتح SmartPOS
2. اذهب إلى الإعدادات > Google Drive
3. انقر على "رفع بيانات الاعتماد"
4. الصق بيانات الاعتماد JSON في النموذج
5. انقر على "حفظ"
6. انقر على "اختبار الاتصال" للتأكد من عمل الإعداد

### 7. تفعيل النسخ الاحتياطي التلقائي

1. في إعدادات Google Drive، فعل "تفعيل Google Drive"
2. اذهب إلى قسم "النسخ الاحتياطية"
3. في "المهام المجدولة"، أضف مهمة جديدة:
   - النوع: "نسخ احتياطي إلى Google Drive"
   - الجدولة: حسب احتياجاتك (مثل: يومياً في الساعة 3:00 صباحاً)
   - الحالة: نشط

## بيانات الاعتماد JSON

يجب أن يحتوي ملف JSON على البيانات التالية:

```json
{
  "client_id": "your-client-id.googleusercontent.com",
  "client_secret": "your-client-secret",
  "refresh_token": "your-refresh-token",
  "type": "authorized_user"
}
```

## المهام المتاحة

### 1. نسخ احتياطي إلى Google Drive
- ينشئ نسخة احتياطية محلية أولاً
- يرفع النسخة إلى Google Drive
- يمكن حذف النسخة المحلية بعد الرفع (اختياري)

### 2. تنظيف Google Drive
- يحذف النسخ الاحتياطية القديمة من Google Drive
- يحتفظ بعدد محدد من النسخ (افتراضي: 10)

## استكشاف الأخطاء

### خطأ: "مكتبات Google APIs غير متوفرة"
```bash
pip install google-api-python-client google-auth-httplib2 google-auth-oauthlib
```

### خطأ: "خدمة Google Drive غير مكونة"
- تأكد من رفع بيانات الاعتماد الصحيحة
- تأكد من أن ملف JSON يحتوي على جميع الحقول المطلوبة

### خطأ: "فشل في الاتصال"
- تأكد من اتصال الإنترنت
- تأكد من صحة بيانات الاعتماد
- تأكد من تفعيل Google Drive API في Google Cloud Console

### خطأ: "انتهت صلاحية الرمز المميز"
- سيتم تجديد الرمز تلقائياً باستخدام refresh_token
- إذا فشل التجديد، ستحتاج لإعادة إنشاء بيانات الاعتماد

## الأمان

- احتفظ بملف بيانات الاعتماد في مكان آمن
- لا تشارك بيانات الاعتماد مع أي شخص
- استخدم نطاقات الوصول المحدودة فقط (`drive.file`)
- راجع الأذونات الممنوحة للتطبيق بانتظام

## الدعم

إذا واجهت أي مشاكل في الإعداد، يرجى:
1. التأكد من اتباع جميع الخطوات بالترتيب
2. مراجعة سجلات النظام للحصول على تفاصيل الأخطاء
3. التأكد من تفعيل Google Drive API
4. التأكد من صحة بيانات الاعتماد
