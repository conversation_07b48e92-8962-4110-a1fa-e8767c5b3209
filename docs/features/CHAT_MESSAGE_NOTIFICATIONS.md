# 🔔 نظام تنبيهات الرسائل الجديدة

## 📋 نظرة عامة

نظام تنبيهات الرسائل الجديدة يعرض تنبيهات فورية للمستخدمين عندما يتلقون رسائل جديدة أثناء كونهم متصلين ولكن نافذة المحادثة مغلقة أو يتحدثون مع مستخدم آخر.

## ✨ الميزات الرئيسية

### 🎯 التنبيهات الذكية
- **عرض انتقائي**: يظهر التنبيه فقط عندما يكون المستخدم متصل ونافذة المحادثة مغلقة
- **تجنب التكرار**: لا يظهر تنبيه إذا كانت المحادثة مفتوحة مع نفس المرسل
- **حد أقصى للتنبيهات**: عرض حد أقصى 3 تنبيهات في نفس الوقت

### 🎨 التصميم والواجهة
- **تصميم احترافي**: تنبيهات أنيقة تتماشى مع تصميم النظام
- **دعم الوضع المظلم**: تصميم متجاوب مع الوضع المظلم والفاتح
- **انيميشن سلس**: تأثيرات انتقال سلسة للظهور والإخفاء
- **موضع ذكي**: تنبيهات متدرجة في الزاوية اليمنى العلوية

### ⚡ الوظائف التفاعلية
- **عرض الرسالة**: زر لفتح المحادثة مع المرسل مباشرة
- **إغلاق التنبيه**: إمكانية إغلاق التنبيه يدوياً
- **إخفاء تلقائي**: إخفاء تلقائي بعد 8 ثوان
- **شريط التقدم**: مؤشر بصري لوقت الإخفاء التلقائي

## 🏗️ البنية التقنية

### المكونات الأساسية

#### 1. `ChatMessageAlert.tsx`
مكون عرض التنبيه الفردي:
```typescript
interface ChatMessageAlertProps {
  message: ChatMessage;
  onReply: (senderId: number) => void;
  onDismiss: () => void;
  autoHide?: boolean;
  duration?: number;
}
```

#### 2. `ChatNotificationManager.tsx`
مدير عرض التنبيهات المتعددة:
```typescript
interface ChatNotificationManagerProps {
  onOpenChat: (senderId: number) => void;
  maxNotifications?: number;
}
```

#### 3. `chatNotificationService.ts`
خدمة إدارة منطق التنبيهات:
```typescript
class ChatNotificationService {
  setChatWindowState(isOpen: boolean, currentConversationId: number | null);
  handleNewMessage(message: ChatMessage);
  setNotificationsEnabled(enabled: boolean);
}
```

### تدفق البيانات

```mermaid
graph TD
    A[WebSocket: رسالة جديدة] --> B[useChat Hook]
    B --> C[chatNotificationService]
    C --> D{هل يجب إظهار التنبيه؟}
    D -->|نعم| E[ChatNotificationManager]
    D -->|لا| F[تجاهل]
    E --> G[ChatMessageAlert]
    G --> H[عرض التنبيه]
    H --> I{إجراء المستخدم}
    I -->|عرض الرسالة| J[فتح المحادثة]
    I -->|إغلاق| K[إزالة التنبيه]
    I -->|إخفاء تلقائي| K
```

## 🔧 التكامل مع النظام

### 1. تتبع حالة النافذة
```typescript
// في ChatWindow.tsx
useEffect(() => {
  chatNotificationService.setChatWindowState(isOpen, currentConversation);
}, [isOpen, currentConversation]);
```

### 2. معالجة الرسائل الجديدة
```typescript
// في useChat.ts
const handleNewMessage = (data: any) => {
  const message: ChatMessage = data.message;
  
  // إرسال التنبيه للرسائل الواردة فقط
  if (message.sender_id !== userId) {
    chatNotificationService.handleNewMessage(message);
  }
  
  // باقي منطق معالجة الرسالة...
};
```

### 3. فتح المحادثة من التنبيه
```typescript
// نظام الأحداث العامة
const openChatWithUser = (userId: number) => {
  const event = new CustomEvent('openChatWithUser', {
    detail: { userId }
  });
  window.dispatchEvent(event);
};
```

## 🎛️ الإعدادات والتخصيص

### إعدادات التنبيهات
```typescript
// تفعيل/تعطيل التنبيهات
chatNotificationService.setNotificationsEnabled(true);

// الحد الأقصى للتنبيهات المعروضة
<ChatNotificationManager maxNotifications={3} />

// مدة الإخفاء التلقائي
<ChatMessageAlert duration={8000} autoHide={true} />
```

### تخصيص الألوان
```css
/* ألوان أيقونات المستخدمين */
const getUserAvatarColor = (userId: number): string => {
  const colors = [
    '#0284c7', '#059669', '#7c3aed', '#dc2626', '#ea580c',
    // المزيد من الألوان...
  ];
  return colors[userId % colors.length];
};
```

## 🔍 منطق القرارات

### متى يظهر التنبيه؟
```typescript
private shouldShowNotification(message: ChatMessage): boolean {
  // التنبيهات معطلة
  if (!this.state.notificationsEnabled) return false;
  
  // المستخدم غير نشط
  if (!this.state.isUserActive) return false;
  
  // نافذة المحادثة مفتوحة مع نفس المرسل
  if (this.state.isChatWindowOpen && 
      this.state.currentConversationId === message.sender_id) {
    return false;
  }
  
  // تجنب التنبيهات المكررة
  if (this.activeNotifications.has(message.id)) return false;
  
  return true;
}
```

### مراقبة نشاط المستخدم
- **أحداث النشاط**: `mousedown`, `mousemove`, `keypress`, `scroll`, `touchstart`
- **مهلة عدم النشاط**: 60 ثانية
- **فحص دوري**: كل 10 ثوان

## 🚀 الاستخدام

### 1. التفعيل التلقائي
النظام يعمل تلقائياً عند تسجيل الدخول ولا يحتاج تدخل من المطور.

### 2. التحكم اليدوي
```typescript
// تعطيل التنبيهات مؤقتاً
chatNotificationService.setNotificationsEnabled(false);

// مسح جميع التنبيهات
chatNotificationService.clearAllNotifications();

// الحصول على التنبيهات النشطة
const activeNotifications = chatNotificationService.getActiveNotifications();
```

## 🎯 أفضل الممارسات

### 1. الأداء
- **Throttling**: تأخير 50ms لأحداث التمرير
- **تنظيف الذاكرة**: إزالة التنبيهات القديمة تلقائياً
- **تحسين الرسوم**: استخدام `transform` بدلاً من `position`

### 2. تجربة المستخدم
- **عدم الإزعاج**: حد أقصى 2 تنبيه من نفس المرسل
- **وضوح المعلومات**: عرض اسم المرسل ومعاينة الرسالة
- **سهولة الإجراء**: أزرار واضحة للعرض والإغلاق

### 3. إمكانية الوصول
- **دعم قارئات الشاشة**: `aria-label` و `title` مناسبة
- **تباين الألوان**: ألوان واضحة في الوضعين الفاتح والمظلم
- **حجم الأهداف**: أزرار بحجم مناسب للمس

## 🔧 استكشاف الأخطاء

### مشاكل شائعة
1. **التنبيهات لا تظهر**: تحقق من حالة `notificationsEnabled`
2. **تنبيهات مكررة**: تحقق من منطق `shouldShowNotification`
3. **مشاكل الموضع**: تحقق من CSS و z-index

### تسجيل الأخطاء
```typescript
// تفعيل التسجيل المفصل
console.log('🔔 تم إظهار تنبيه للرسالة الجديدة');
console.log('🔕 لن يتم إظهار تنبيه للرسالة');
```

## 📈 التطوير المستقبلي

### ميزات مقترحة
- **تنبيهات النظام**: استخدام Notification API
- **أصوات التنبيه**: إضافة أصوات اختيارية
- **تجميع التنبيهات**: تجميع رسائل متعددة من نفس المرسل
- **إعدادات شخصية**: تخصيص مدة العرض وموضع التنبيهات

---

## 🔧 إصلاحات حديثة (يوليو 2025)

**تاريخ الإصلاح**: يوليو 2025
**رقم الإصدار**: 1.4.1
**نوع التحديث**: إصلاحات حرجة لنظام التنبيهات في صفحة POS

### 🚨 المشاكل المصححة

#### 1. إصلاح مشكلة التنبيهات في صفحة نقطة البيع
**المشكلة**: كانت التنبيهات لا تظهر في صفحة POS بعد إزالة الزر العائم وإضافة الشريط العلوي.

**الحل المطبق**:
- إزالة `ChatButton` العائم من صفحة POS
- إضافة `ChatHeaderButton` في الشريط العلوي لصفحة POS
- إعادة إضافة `ChatNotificationManager` لصفحة POS
- تحسين `ChatHeaderButton` لإشعار `chatNotificationService` بحالة النافذة

#### 2. إصلاح مشكلة تتبع نشاط المستخدم
**المشكلة**: كان المستخدم يُعتبر غير نشط بعد دقيقة واحدة فقط، مما يمنع ظهور التنبيهات بعد فترات طويلة.

**الحل المطبق**:
```typescript
// تحسين مراقبة النشاط
this.state.isUserActive = inactiveTime < 1800000; // 30 دقيقة بدلاً من دقيقة

// إضافة المزيد من أحداث النشاط
[
  'mousedown', 'mousemove', 'mouseup', 'click',
  'keypress', 'keydown', 'keyup',
  'scroll', 'wheel',
  'touchstart', 'touchmove', 'touchend',
  'focus', 'blur'
].forEach(event => {
  document.addEventListener(event, updateActivity, { passive: true });
});

// تحديث النشاط عند وصول رسائل جديدة
handleNewMessage(message: ChatMessage) {
  this.lastActivityTime = Date.now();
  this.state.isUserActive = true;
  // باقي المنطق...
}
```

#### 3. تحسين ChatHeaderButton لتتبع حالة النافذة
**المشكلة**: `ChatHeaderButton` لم يكن يقوم بإشعار `chatNotificationService` بحالة النافذة بشكل صحيح.

**الحل المطبق**:
```typescript
// في handleToggleChat
if (newIsOpen) {
  chatNotificationService.setChatWindowState(true, null);
} else {
  chatNotificationService.setChatWindowState(false, null);
}

// في openChatWithUser
chatNotificationService.setChatWindowState(true, userId);

// في handleCloseChat
chatNotificationService.setChatWindowState(false, null);
```

### 📊 تأثير الإصلاحات

#### قبل الإصلاح:
- ❌ التنبيهات لا تظهر في صفحة POS
- ❌ المستخدم يُعتبر غير نشط بعد دقيقة واحدة
- ❌ التنبيهات تتوقف عن الظهور بعد 15-20 دقيقة

#### بعد الإصلاح:
- ✅ التنبيهات تعمل بشكل صحيح في صفحة POS
- ✅ المستخدم يُعتبر نشطاً لمدة 30 دقيقة
- ✅ التنبيهات تظهر حتى بعد فترات طويلة من عدم النشاط
- ✅ تحديث تلقائي للنشاط عند وصول رسائل جديدة

### 📁 الملفات المعدلة

```
frontend/src/
├── pages/
│   └── POS.tsx                           # إزالة ChatButton وإضافة ChatHeaderButton
├── components/Chat/
│   ├── ChatHeaderButton.tsx              # تحسين تتبع حالة النافذة
│   └── ChatButton.tsx                    # تحسين تتبع حالة النافذة (للمرجع)
├── services/
│   └── chatNotificationService.ts        # تحسين مراقبة النشاط
└── hooks/
    └── useChat.ts                        # إضافة تحديث النشاط عند الرسائل
```

### 🎯 فوائد الإصلاحات

1. **تجربة موحدة**: نفس تجربة التنبيهات في جميع صفحات التطبيق
2. **موثوقية عالية**: التنبيهات تعمل حتى بعد فترات طويلة من عدم النشاط
3. **تصميم متسق**: استخدام الشريط العلوي بدلاً من الأزرار العائمة
4. **أداء محسن**: مراقبة أكثر ذكاءً لنشاط المستخدم
