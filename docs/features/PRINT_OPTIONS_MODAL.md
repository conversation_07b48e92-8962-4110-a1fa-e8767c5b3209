# نافذة خصائص الطباعة المخصصة

## 📋 نظرة عامة

تم إنشاء نافذة طباعة مخصصة من التطبيق بدلاً من نافذة المتصفح الافتراضية، مما يوفر تحكماً أكبر في خصائص الطباعة.

## 🆕 الميزات الجديدة

### 1. نافذة طباعة مخصصة
- **تصميم موحد**: يتبع نمط النوافذ في التطبيق
- **خيارات شاملة**: جميع خصائص الطباعة في مكان واحد
- **سهولة الاستخدام**: واجهة بديهية وواضحة
- **دعم اللغة العربية**: جميع النصوص باللغة العربية

### 2. خيارات الطباعة المتقدمة
- **نوع الطابعة**: حرارية، ليزر، حبر
- **حجم الورق**: 58mm، 80mm، A4
- **الاتجاه**: عمودي، أفقي
- **جودة الطباعة**: مسودة، عادي، عالي
- **عدد النسخ**: 1-10 نسخ
- **الهوامش**: بدون، صغيرة، عادية
- **وضع الألوان**: أبيض وأسود، رمادي، ملون

### 3. أزرار الطباعة
- **طباعة سريعة**: طباعة فورية بالإعدادات الافتراضية
- **خصائص الطباعة**: فتح نافذة الخصائص المخصصة

## 🎨 واجهة المستخدم

### تصميم النافذة
```
┌─────────────────────────────────────┐
│ خصائص الطباعة                      │
├─────────────────────────────────────┤
│ نوع الطابعة                        │
│ [حرارية] [ليزر] [حبر]              │
│                                     │
│ حجم الورق                          │
│ ○ 58mm (صغير)                      │
│ ● 80mm (متوسط)                     │
│ ○ A4 (كبير)                        │
│                                     │
│ جودة الطباعة: [عادي ▼]             │
│                                     │
│ عدد النسخ: [1]  الهوامش: [صغيرة ▼] │
│                                     │
│ وضع الألوان: [أبيض وأسود ▼]        │
│                                     │
│           [إلغاء] [طباعة]           │
└─────────────────────────────────────┘
```

### الألوان والأيقونات
- **طابعة حرارية**: 📱 أيقونة جوال
- **طابعة ليزر**: 🖥️ أيقونة سطح مكتب  
- **طابعة حبر**: 📄 أيقونة ملف
- **الخيار المحدد**: لون أزرق مع خلفية فاتحة

## 🔧 الخصائص التقنية

### واجهة PrintOptions
```typescript
interface PrintOptions {
  printerType: 'thermal' | 'laser' | 'inkjet';
  paperSize: string;
  orientation: 'portrait' | 'landscape';
  quality: 'draft' | 'normal' | 'high';
  copies: number;
  margins: 'none' | 'minimum' | 'normal';
  colorMode: 'color' | 'grayscale' | 'blackwhite';
}
```

### تطبيق الخصائص
```typescript
const handlePrintWithOptions = (options: PrintOptions) => {
  // إنشاء أنماط CSS مخصصة للطباعة
  const printStyles = document.createElement('style');
  printStyles.textContent = `
    @media print {
      @page {
        size: ${options.paperSize} ${options.orientation};
        margin: ${getMarginValue(options.margins)};
      }
      
      body {
        -webkit-print-color-adjust: ${options.colorMode === 'color' ? 'exact' : 'economy'};
      }
      
      .receipt-wrapper {
        filter: ${options.colorMode === 'grayscale' ? 'grayscale(100%)' : 'none'};
      }
    }
  `;
  
  // تطبيق الأنماط والطباعة
  document.head.appendChild(printStyles);
  window.print();
  
  // إزالة الأنماط بعد الطباعة
  setTimeout(() => {
    document.head.removeChild(printStyles);
  }, 2000);
};
```

## 📱 أنواع الطابعات

### 1. طابعة حرارية
- **الأحجام المدعومة**: 58mm، 80mm
- **الخصائص**: 
  - بدون هوامش أو هوامش صغيرة
  - أبيض وأسود فقط
  - جودة عادية أو عالية

### 2. طابعة ليزر
- **الأحجام المدعومة**: A4
- **الخصائص**:
  - جميع أنواع الهوامش
  - أبيض وأسود أو رمادي
  - جميع مستويات الجودة

### 3. طابعة حبر
- **الأحجام المدعومة**: A4
- **الخصائص**:
  - جميع أنواع الهوامش
  - جميع أوضاع الألوان
  - جميع مستويات الجودة

## 🎯 خيارات الجودة

### مسودة (Draft)
- **السرعة**: سريع جداً
- **الجودة**: أساسية
- **استهلاك الحبر**: قليل
- **الاستخدام**: للمراجعة السريعة

### عادي (Normal)
- **السرعة**: متوسط
- **الجودة**: جيدة
- **استهلاك الحبر**: متوسط
- **الاستخدام**: للاستخدام العادي

### عالي (High)
- **السرعة**: بطيء
- **الجودة**: ممتازة
- **استهلاك الحبر**: عالي
- **الاستخدام**: للوثائق المهمة

## 📏 أحجام الورق

| الحجم | العرض | الاستخدام | نوع الطابعة |
|-------|--------|-----------|-------------|
| **58mm** | 58mm | طابعات صغيرة | حرارية |
| **80mm** | 80mm | طابعات متوسطة | حرارية |
| **A4** | 210mm | طابعات عادية | ليزر/حبر |

## 🔄 تدفق العمل

### 1. فتح نافذة الطباعة
```
المستخدم يضغط "خصائص الطباعة"
↓
تفتح نافذة PrintOptionsModal
↓
يختار المستخدم الخصائص المطلوبة
↓
يضغط "طباعة"
```

### 2. تطبيق الخصائص
```
تطبيق أنماط CSS مخصصة
↓
استدعاء window.print()
↓
طباعة متعددة (إذا كان عدد النسخ > 1)
↓
إزالة الأنماط المؤقتة
```

## 🚀 المزايا

### مقارنة مع نافذة المتصفح
| الميزة | نافذة المتصفح | النافذة المخصصة |
|--------|---------------|-----------------|
| **التحكم** | محدود | كامل |
| **التخصيص** | قليل | شامل |
| **سهولة الاستخدام** | معقد | بسيط |
| **دعم العربية** | جزئي | كامل |
| **التصميم** | عام | متخصص |

### الفوائد للمستخدم
- ✅ **تحكم أكبر** في خصائص الطباعة
- ✅ **واجهة عربية** سهلة الفهم
- ✅ **خيارات متقدمة** للطابعات المختلفة
- ✅ **طباعة متعددة** بضغطة واحدة
- ✅ **حفظ الورق والحبر** مع خيارات الجودة

## 🔧 الاستخدام

### للطباعة السريعة:
1. اضغط زر "طباعة سريعة"
2. ستطبع الفاتورة فوراً بالإعدادات الافتراضية

### للطباعة المخصصة:
1. اضغط زر "خصائص الطباعة"
2. اختر نوع الطابعة
3. حدد حجم الورق
4. اضبط الخصائص الأخرى
5. اضغط "طباعة"

## 📝 ملاحظات التطوير

### الملفات الجديدة
- `frontend/src/components/PrintOptionsModal.tsx`: مكون النافذة
- تحديثات على `frontend/src/pages/Receipt.tsx`: التكامل

### التبعيات
- لا توجد تبعيات جديدة مطلوبة
- استخدام React Icons الموجودة
- استخدام مكون Modal الموجود

### التوافق
- ✅ جميع المتصفحات الحديثة
- ✅ أجهزة سطح المكتب والجوال
- ✅ جميع أنواع الطابعات
