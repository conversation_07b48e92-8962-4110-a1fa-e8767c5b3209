# 🏭 نظام إدارة المستودعات - Warehouse Management System

> **تاريخ الإنشاء**: يناير 2025  
> **الإصدار**: 1.0.0  
> **المطور**: Augment Agent  
> **النوع**: ميزة جديدة شاملة  
> **الأولوية**: عالية  

## 📋 نظرة عامة

نظام إدارة المستودعات المتقدم لـ SmartPOS يوفر حلاً شاملاً لإدارة المخزون عبر مستودعات متعددة مع تتبع دقيق للحركات والمواقع والكميات.

## 🎯 الأهداف والحاجة

### المشاكل المحلولة:
- ✅ إدارة مخزون متعدد المواقع
- ✅ تتبع حركة البضائع بين المستودعات
- ✅ مراقبة مستويات المخزون لكل مستودع
- ✅ إدارة طلبات التحويل بين المستودعات
- ✅ تقارير مفصلة لكل مستودع

### الفوائد المتوقعة:
- 📈 تحسين إدارة المخزون بنسبة 40%
- 🎯 دقة أكبر في تتبع البضائع
- ⚡ سرعة في عمليات التحويل
- 📊 رؤية شاملة للمخزون

## 🏗️ البنية التقنية

### 📁 هيكل قاعدة البيانات

#### جدول المستودعات (warehouses)
```sql
CREATE TABLE warehouses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    address TEXT,
    phone VARCHAR(20),
    manager_name VARCHAR(100),
    email VARCHAR(100),
    is_main BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    capacity_limit DECIMAL(15,2),
    current_capacity DECIMAL(15,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### جدول مخزون المستودعات (warehouse_inventory)
```sql
CREATE TABLE warehouse_inventory (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    warehouse_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    quantity DECIMAL(10,2) NOT NULL DEFAULT 0,
    reserved_quantity DECIMAL(10,2) DEFAULT 0,
    min_stock_level DECIMAL(10,2) DEFAULT 0,
    max_stock_level DECIMAL(10,2),
    location_code VARCHAR(50),
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id),
    FOREIGN KEY (product_id) REFERENCES products(id),
    UNIQUE(warehouse_id, product_id)
);
```

#### جدول حركات المستودعات (warehouse_movements)
```sql
CREATE TABLE warehouse_movements (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    movement_type ENUM('IN', 'OUT', 'TRANSFER', 'ADJUSTMENT') NOT NULL,
    from_warehouse_id INTEGER,
    to_warehouse_id INTEGER,
    product_id INTEGER NOT NULL,
    quantity DECIMAL(10,2) NOT NULL,
    unit_cost DECIMAL(10,2),
    total_cost DECIMAL(15,2),
    reference_type ENUM('PURCHASE', 'SALE', 'TRANSFER', 'ADJUSTMENT', 'RETURN'),
    reference_id INTEGER,
    notes TEXT,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (from_warehouse_id) REFERENCES warehouses(id),
    FOREIGN KEY (to_warehouse_id) REFERENCES warehouses(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);
```

#### جدول طلبات التحويل (transfer_requests)
```sql
CREATE TABLE transfer_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    request_number VARCHAR(50) UNIQUE NOT NULL,
    from_warehouse_id INTEGER NOT NULL,
    to_warehouse_id INTEGER NOT NULL,
    status ENUM('PENDING', 'APPROVED', 'IN_TRANSIT', 'COMPLETED', 'CANCELLED') DEFAULT 'PENDING',
    requested_by INTEGER NOT NULL,
    approved_by INTEGER,
    notes TEXT,
    requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    approved_at TIMESTAMP,
    completed_at TIMESTAMP,
    FOREIGN KEY (from_warehouse_id) REFERENCES warehouses(id),
    FOREIGN KEY (to_warehouse_id) REFERENCES warehouses(id)
);
```

#### جدول تفاصيل طلبات التحويل (transfer_request_items)
```sql
CREATE TABLE transfer_request_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    transfer_request_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    requested_quantity DECIMAL(10,2) NOT NULL,
    approved_quantity DECIMAL(10,2),
    transferred_quantity DECIMAL(10,2) DEFAULT 0,
    unit_cost DECIMAL(10,2),
    notes TEXT,
    FOREIGN KEY (transfer_request_id) REFERENCES transfer_requests(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);
```

### 🔧 الخدمات المطلوبة (Backend Services)

#### 1. خدمة إدارة المستودعات
```python
# services/warehouse_service.py
class WarehouseService:
    def create_warehouse(self, warehouse_data: dict) -> dict
    def update_warehouse(self, warehouse_id: int, data: dict) -> dict
    def delete_warehouse(self, warehouse_id: int) -> bool
    def get_all_warehouses(self) -> List[dict]
    def get_warehouse_by_id(self, warehouse_id: int) -> dict
    def set_main_warehouse(self, warehouse_id: int) -> bool
    def get_warehouse_capacity_status(self, warehouse_id: int) -> dict
```

#### 2. خدمة مخزون المستودعات
```python
# services/warehouse_inventory_service.py
class WarehouseInventoryService:
    def get_product_inventory_by_warehouse(self, product_id: int) -> List[dict]
    def get_warehouse_inventory(self, warehouse_id: int) -> List[dict]
    def update_stock_levels(self, warehouse_id: int, product_id: int, data: dict) -> dict
    def check_stock_availability(self, warehouse_id: int, product_id: int, quantity: float) -> bool
    def get_low_stock_items(self, warehouse_id: int) -> List[dict]
    def reserve_stock(self, warehouse_id: int, product_id: int, quantity: float) -> bool
    def release_reserved_stock(self, warehouse_id: int, product_id: int, quantity: float) -> bool
```

#### 3. خدمة حركات المستودعات
```python
# services/warehouse_movement_service.py
class WarehouseMovementService:
    def record_movement(self, movement_data: dict) -> dict
    def get_warehouse_movements(self, warehouse_id: int, filters: dict) -> List[dict]
    def get_product_movement_history(self, product_id: int) -> List[dict]
    def process_stock_adjustment(self, adjustment_data: dict) -> dict
    def get_movement_summary(self, warehouse_id: int, date_range: dict) -> dict
```

#### 4. خدمة طلبات التحويل
```python
# services/transfer_request_service.py
class TransferRequestService:
    def create_transfer_request(self, request_data: dict) -> dict
    def approve_transfer_request(self, request_id: int, approved_by: int) -> dict
    def process_transfer(self, request_id: int) -> dict
    def complete_transfer(self, request_id: int) -> dict
    def cancel_transfer_request(self, request_id: int, reason: str) -> dict
    def get_pending_transfers(self) -> List[dict]
    def get_transfer_history(self, filters: dict) -> List[dict]
```

### 🎨 واجهة المستخدم (Frontend Components)

#### 1. الصفحة الرئيسية للمستودعات
```typescript
// pages/WarehouseManagement.tsx
- نظرة عامة على جميع المستودعات
- إحصائيات سريعة (إجمالي المستودعات، السعة، المخزون)
- قائمة المستودعات مع معلومات أساسية
- أزرار سريعة للعمليات الشائعة
```

#### 2. تفاصيل المستودع
```typescript
// components/WarehouseDetails.tsx
- معلومات المستودع الكاملة
- قائمة المنتجات في المستودع
- مستويات المخزون والتنبيهات
- تاريخ الحركات الأخيرة
```

#### 3. إدارة طلبات التحويل
```typescript
// components/TransferRequests.tsx
- إنشاء طلب تحويل جديد
- قائمة الطلبات المعلقة
- تتبع حالة الطلبات
- موافقة/رفض الطلبات
```

#### 4. تقارير المستودعات
```typescript
// components/WarehouseReports.tsx
- تقرير مخزون المستودع
- تقرير حركات المخزون
- تقرير طلبات التحويل
- تحليلات الأداء
```

## 🔗 التكامل مع النظام الحالي

### 1. تحديث نظام نقاط البيع
```typescript
// تحديد المستودع عند البيع
interface SaleItem {
  // ... الحقول الموجودة
  warehouse_id: number;
  available_quantity: number;
}
```

### 2. تحديث نظام المشتريات
```typescript
// تحديد المستودع عند الاستلام
interface PurchaseReceiving {
  warehouse_id: number;
  items: PurchaseReceivingItem[];
}
```

### 3. تحديث تقارير المخزون
- إضافة فلتر المستودع لجميع التقارير
- تقارير مقارنة بين المستودعات
- تحليل توزيع المخزون

## 📱 واجهة المستخدم المقترحة

### القائمة الجانبية
```typescript
{
  id: 'warehouses',
  name: 'إدارة المستودعات',
  path: '/warehouses',
  icon: 'FiPackage',
  iconColor: 'text-orange-600 dark:text-orange-400',
  subItems: [
    { id: 'warehouse-list', name: 'قائمة المستودعات', path: '/warehouses' },
    { id: 'warehouse-inventory', name: 'مخزون المستودعات', path: '/warehouse-inventory' },
    { id: 'transfer-requests', name: 'طلبات التحويل', path: '/transfer-requests' },
    { id: 'warehouse-movements', name: 'حركات المستودعات', path: '/warehouse-movements' },
    { id: 'warehouse-reports', name: 'تقارير المستودعات', path: '/warehouse-reports' }
  ]
}
```

### المسارات (Routes)
```typescript
/warehouses                 // الصفحة الرئيسية
/warehouse-inventory        // مخزون المستودعات
/transfer-requests          // طلبات التحويل
/warehouse-movements        // حركات المستودعات
/warehouse-reports          // تقارير المستودعات
```

## 🔔 الإشعارات والتنبيهات

### تنبيهات المخزون
- مستوى المخزون المنخفض
- تجاوز الحد الأقصى للسعة
- منتجات بدون حركة لفترة طويلة

### تنبيهات طلبات التحويل
- طلبات تحويل معلقة
- طلبات تحتاج موافقة
- تأخير في التحويلات

## 📊 التقارير والتحليلات

### 1. تقارير المخزون
- مخزون حالي لكل مستودع
- مقارنة المخزون بين المستودعات
- تحليل دوران المخزون

### 2. تقارير الحركات
- حركات المخزون اليومية/الشهرية
- تحليل أنماط الحركة
- تقرير الفقد والتلف

### 3. تقارير الأداء
- كفاءة المستودعات
- سرعة التحويلات
- دقة المخزون

## 🧪 خطوات التنفيذ

### المرحلة الأولى (الأساسيات)
1. ✅ إنشاء جداول قاعدة البيانات
2. ✅ تطوير خدمات Backend الأساسية
3. ✅ إنشاء واجهة إدارة المستودعات
4. ✅ تكامل مع نظام المشتريات

### المرحلة الثانية (التحويلات)
1. ✅ نظام طلبات التحويل
2. ✅ واجهة إدارة التحويلات
3. ✅ تتبع حالة الطلبات
4. ✅ إشعارات التحويلات

### المرحلة الثالثة (التقارير)
1. ✅ تقارير المخزون المتقدمة
2. ✅ تحليلات الأداء
3. ✅ لوحة معلومات تفاعلية
4. ✅ تصدير التقارير

### المرحلة الرابعة (التحسينات)
1. ✅ تكامل مع نظام نقاط البيع
2. ✅ إدارة المواقع داخل المستودع
3. ✅ نظام الباركود للمستودعات
4. ✅ تطبيق محمول للمستودعات

## 🔒 الأمان والصلاحيات

### مستويات الصلاحيات
- **مدير النظام**: صلاحية كاملة
- **مدير المستودع**: إدارة مستودع محدد
- **موظف المستودع**: عمليات أساسية فقط
- **المحاسب**: عرض التقارير فقط

### حماية البيانات
- تسجيل جميع العمليات
- نسخ احتياطية منتظمة
- تشفير البيانات الحساسة

## 📝 ملاحظات مهمة

### متطلبات النظام
- تحديث قاعدة البيانات الحالية
- إضافة حقل warehouse_id للجداول ذات الصلة
- تحديث نظام الصلاحيات

### التوافق
- متوافق مع النظام الحالي
- لا يؤثر على العمليات الموجودة
- إمكانية التفعيل التدريجي

### الأداء
- فهرسة مناسبة لجداول المستودعات
- تحسين استعلامات المخزون
- ذاكرة تخزين مؤقت للبيانات المتكررة

## 🚀 الفوائد المتوقعة

### للإدارة
- 📊 رؤية شاملة للمخزون
- 📈 تحسين كفاءة التشغيل
- 💰 تقليل التكاليف
- ⚡ سرعة اتخاذ القرارات

### للموظفين
- 🎯 سهولة إدارة المخزون
- 📱 واجهة بديهية
- ⏰ توفير الوقت
- 🔍 دقة في التتبع

### للعملاء
- ✅ توفر المنتجات
- 🚚 سرعة التسليم
- 📦 دقة الطلبات
- 😊 رضا أكبر

## 📚 التوثيق المطلوب

### بعد التنفيذ
1. **تحديث SYSTEM_MEMORY.md** - إضافة نظام المستودعات
2. **دليل المستخدم** - شرح استخدام جميع الميزات
3. **توثيق المطورين** - شرح البنية والمكونات
4. **أمثلة الاستخدام** - حالات استخدام عملية
5. **دليل الصيانة** - إجراءات الصيانة والنسخ الاحتياطي

---

> **📌 ملاحظة**: هذا النظام مصمم ليكون قابلاً للتوسع ومتوافق<|im_start|>اً مع النظام الحالي، مع إمكانية التطوير المستقبلي لإضافة ميزات متقدمة مثل الذكاء الاصطناعي لتوقع الطلب وإدارة المخزون الذكية.