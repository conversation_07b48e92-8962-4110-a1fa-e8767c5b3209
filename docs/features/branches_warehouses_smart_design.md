
# 🏭📦 المنطق الذكي الحديث لربط المستودعات والفروع - SmartPOS

## 📌 الهدف
توضيح تصميم مرن وذكي لإدارة العلاقة بين **المستودعات** و **الفروع** بحيث:
- يمكن لأي مستودع أن يغذي أكثر من فرع.
- يمكن لأي فرع أن يستلم من أكثر من مستودع.
- دعم جميع سيناريوهات التوزيع والتحويلات.

---
        self.db_session = db_session
## 🧠 الفكرة الأساسية
العلاقة بين المستودعات والفروع ليست خطية (One-to-One) ولا أحادية الاتجاه فقط (One-to-Many)، بل **علاقة Many-to-Many**:
- فرع واحد يمكن أن يرتبط بعدة مستودعات.
- مستودع واحد يمكن أن يوزع على عدة فروع.

---

## 🔄 سيناريو تدفق البضائع

```
[ مستودع A ] ----> فرع 1
                ↘  فرع 2

[ مستودع B ] ----> فرع 1
                ↘  فرع 3

[ مستودع C ] ----> فرع 2
                ↘  فرع 3
```

- يمكن لأي فرع أن يستلم من أكثر من مصدر.
- يمكن لأي مستودع أن يوزع على عدة وجهات.

---

## 🗄️ تصميم قاعدة البيانات

### جدول الفروع
```sql
CREATE TABLE branches (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    address TEXT,
    phone VARCHAR(20),
    manager_name VARCHAR(100),
    email VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### جدول المستودعات
```sql
CREATE TABLE warehouses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    address TEXT,
    phone VARCHAR(20),
    manager_name VARCHAR(100),
    email VARCHAR(100),
    is_main BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    capacity_limit DECIMAL(15,2),
    current_capacity DECIMAL(15,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### جدول الربط بين الفروع والمستودعات
```sql
CREATE TABLE branch_warehouses (
    branch_id INTEGER NOT NULL,
    warehouse_id INTEGER NOT NULL,
    PRIMARY KEY (branch_id, warehouse_id),
    FOREIGN KEY (branch_id) REFERENCES branches(id),
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id)
);
```

---

## ⚙️ المنطق التشغيلي

### 1. الإمداد
- عند احتياج فرع لمخزون، يمكنه طلبه من أي مستودع مرتبط به.
- النظام يختار المستودع الأنسب بناءً على:
  - المسافة.
  - توفر المخزون.
  - تكلفة النقل.

### 2. التحويلات
- التحويلات يمكن أن تكون:
  - من مستودع إلى فرع.
  - من فرع إلى مستودع.
  - بين مستودعين.
  - بين فرعين (مباشرة أو عبر مستودع وسيط).

### 3. المبيعات والمشتريات
- عند البيع، النظام يعرف من أي مستودع تم تزويد الفرع.
- عند الشراء، يمكن إدخال البضاعة في مستودع معين، ثم توزيعها على الفروع.

---

## 📊 التقارير المدعومة
- **مخزون كل فرع** (من عدة مستودعات).
- **أداء المستودعات** في تزويد الفروع.
- **تحليل المسار الأمثل للتوزيع**.

---

## 🧠 الذكاء الاصطناعي في التوزيع
- استخدام خوارزميات **التحسين Optimization** لاختيار المستودع الأنسب للتوريد.
- توقع الطلب لكل فرع وتوزيع المخزون بناءً على البيانات التاريخية.

---

## 🗺️ ملخص
> الحل الأكثر منطقية ومرونة هو جعل العلاقة **Many-to-Many** بين الفروع والمستودعات عبر جدول وسيط `branch_warehouses`، مما يسمح لأي فرع أن يستلم من أكثر من مستودع، وأي مستودع أن يغذي أكثر من فرع.
