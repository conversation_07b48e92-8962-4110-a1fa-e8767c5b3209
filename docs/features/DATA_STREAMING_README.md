# نظام تدفق البيانات الكبيرة لتطبيق SmartPOS

## نظرة عامة

تم تطوير نظام تدفق البيانات الكبيرة لتطبيق SmartPOS لحل مشكلة التعامل مع كميات كبيرة من البيانات بكفاءة عالية. يوفر النظام إمكانيات متقدمة للتصدير والتدفق المستمر والضغط.

## الميزات الرئيسية

### 1. تدفق البيانات المستمر (Streaming)
- **تدفق المبيعات**: تصدير بيانات المبيعات بدفعات محسنة
- **تدفق المنتجات**: تصدير المنتجات مع التحليلات
- **تدفق العملاء**: تصدير بيانات العملاء مع الديون
- **التحليلات المتقدمة**: تدفق البيانات التحليلية

### 2. تنسيقات متعددة
- **JSON**: للتطبيقات والواجهات البرمجية
- **CSV**: للجداول البيانية وExcel
- **ضغط GZIP**: لتوفير مساحة التخزين وسرعة النقل

### 3. إدارة المهام
- **إنشاء مهام غير متزامنة**: تنفيذ العمليات في الخلفية
- **مراقبة التقدم**: متابعة حالة المهام في الوقت الفعلي
- **تحميل النتائج**: تحميل ملفات التصدير المكتملة

### 4. الأمان والصلاحيات
- **فحص الصلاحيات**: التحكم في الوصول حسب دور المستخدم
- **تشفير البيانات**: حماية البيانات الحساسة
- **تنظيف تلقائي**: حذف الملفات القديمة تلقائياً

## واجهات برمجة التطبيقات (APIs)

### 1. تدفق المبيعات
```http
GET /api/data-streaming/sales/stream
```

**المعاملات:**
- `start_date`: تاريخ البداية (اختياري)
- `end_date`: تاريخ النهاية (اختياري)
- `user_id`: معرف المستخدم (اختياري)
- `format_type`: نوع التنسيق (json/csv)
- `compress`: ضغط البيانات (true/false)

**مثال:**
```bash
curl -X GET "http://localhost:8002/api/data-streaming/sales/stream?start_date=2024-01-01&format_type=json&compress=true" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. تدفق المنتجات
```http
GET /api/data-streaming/products/stream
```

**المعاملات:**
- `category`: فئة المنتج (اختياري)
- `low_stock`: المنتجات قليلة المخزون فقط
- `format_type`: نوع التنسيق

**مثال:**
```bash
curl -X GET "http://localhost:8002/api/data-streaming/products/stream?category=electronics&format_type=csv" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. تدفق العملاء
```http
GET /api/data-streaming/customers/stream
```

**المعاملات:**
- `with_debts`: تضمين بيانات الديون
- `format_type`: نوع التنسيق

### 4. التصدير المجمع
```http
GET /api/data-streaming/bulk-export
```

**المعاملات:**
- `tables`: الجداول المطلوبة (sales,products,customers)
- `start_date`: تاريخ البداية
- `end_date`: تاريخ النهاية
- `format_type`: نوع التنسيق
- `compress`: ضغط البيانات

**مثال:**
```bash
curl -X GET "http://localhost:8002/api/data-streaming/bulk-export?tables=sales,products&compress=true" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 5. إدارة المهام

#### إنشاء مهمة جديدة
```http
POST /api/data-streaming/tasks/create
```

**مثال:**
```bash
curl -X POST "http://localhost:8002/api/data-streaming/tasks/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "task_type": "sales_export",
    "parameters": {
      "start_date": "2024-01-01",
      "end_date": "2024-12-31",
      "format_type": "json",
      "compress": true
    }
  }'
```

#### مراقبة تقدم المهمة
```http
GET /api/data-streaming/tasks/{task_id}/progress
```

#### تحميل نتيجة المهمة
```http
GET /api/data-streaming/tasks/{task_id}/download
```

### 6. المقاييس والإحصائيات
```http
GET /api/data-streaming/metrics
```

### 7. تنظيف الملفات القديمة
```http
POST /api/data-streaming/cleanup
```

## الاستخدام في الواجهة الأمامية

### JavaScript/TypeScript

```javascript
// تدفق بيانات المبيعات
async function streamSalesData() {
  const response = await fetch('/api/data-streaming/sales/stream?format_type=json', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  const reader = response.body.getReader();
  const decoder = new TextDecoder();
  
  while (true) {
    const { done, value } = await reader.read();
    if (done) break;
    
    const chunk = decoder.decode(value);
    console.log('Received chunk:', chunk);
  }
}

// إنشاء مهمة تصدير
async function createExportTask() {
  const response = await fetch('/api/data-streaming/tasks/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      task_type: 'bulk_export',
      parameters: {
        tables: ['sales', 'products'],
        format_type: 'csv',
        compress: true
      }
    })
  });
  
  const result = await response.json();
  return result.task_id;
}

// مراقبة تقدم المهمة
async function monitorTask(taskId) {
  const interval = setInterval(async () => {
    const response = await fetch(`/api/data-streaming/tasks/${taskId}/progress`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    const progress = await response.json();
    console.log(`Progress: ${progress.progress_percentage}%`);
    
    if (progress.status === 'completed') {
      clearInterval(interval);
      console.log('Task completed!');
      // تحميل النتيجة
      window.open(`/api/data-streaming/tasks/${taskId}/download`);
    }
  }, 1000);
}
```

### React Hook

```jsx
import { useState, useEffect } from 'react';

function useDataStreaming() {
  const [tasks, setTasks] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  
  const createTask = async (taskType, parameters) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/data-streaming/tasks/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ task_type: taskType, parameters })
      });
      
      const result = await response.json();
      setTasks(prev => [...prev, { id: result.task_id, status: 'created' }]);
      return result.task_id;
    } finally {
      setIsLoading(false);
    }
  };
  
  const monitorTask = async (taskId) => {
    const response = await fetch(`/api/data-streaming/tasks/${taskId}/progress`);
    const progress = await response.json();
    
    setTasks(prev => prev.map(task => 
      task.id === taskId ? { ...task, ...progress } : task
    ));
    
    return progress;
  };
  
  return { tasks, createTask, monitorTask, isLoading };
}
```

## التحسينات والأداء

### 1. تحسين قاعدة البيانات
- **فهرسة محسنة**: فهارس على الحقول المستخدمة في التصفية
- **استعلامات محسنة**: استخدام eager loading وpagination
- **تجميع البيانات**: تجميع الاستعلامات لتقليل عدد الطلبات

### 2. إدارة الذاكرة
- **تدفق بدفعات**: معالجة البيانات بدفعات صغيرة
- **تنظيف تلقائي**: حذف الملفات المؤقتة
- **ضغط البيانات**: تقليل استخدام الذاكرة والتخزين

### 3. التخزين المؤقت
- **Redis**: تخزين حالة المهام
- **ملفات مؤقتة**: تخزين النتائج للتحميل السريع

## الأمان

### 1. المصادقة والتفويض
- **JWT Tokens**: مصادقة آمنة
- **فحص الأدوار**: تحكم في الوصول حسب الصلاحيات
- **تشفير البيانات**: حماية البيانات الحساسة

### 2. حماية من الهجمات
- **Rate Limiting**: تحديد عدد الطلبات
- **Input Validation**: التحقق من صحة المدخلات
- **SQL Injection Protection**: حماية من حقن SQL

## المراقبة والسجلات

### 1. السجلات
- **تسجيل العمليات**: تتبع جميع العمليات
- **تسجيل الأخطاء**: تسجيل مفصل للأخطاء
- **مقاييس الأداء**: قياس أوقات الاستجابة

### 2. التنبيهات
- **تنبيهات الأخطاء**: إشعارات عند حدوث أخطاء
- **تنبيهات الأداء**: إشعارات عند تدهور الأداء

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. بطء في التصدير
```bash
# فحص حالة قاعدة البيانات
curl -X GET "http://localhost:8002/api/data-streaming/metrics"

# تنظيف الملفات القديمة
curl -X POST "http://localhost:8002/api/data-streaming/cleanup"
```

#### 2. نفاد مساحة التخزين
```bash
# تنظيف الملفات القديمة
curl -X POST "http://localhost:8002/api/data-streaming/cleanup?max_age_hours=12"
```

#### 3. مهام معلقة
```bash
# فحص حالة المهام
curl -X GET "http://localhost:8002/api/data-streaming/tasks/{task_id}/progress"
```

## التطوير المستقبلي

### 1. ميزات مخططة
- **تصدير إلى السحابة**: رفع مباشر إلى AWS S3, Google Cloud
- **جدولة التصدير**: تصدير تلقائي في أوقات محددة
- **تنسيقات إضافية**: دعم XML, Parquet
- **تحليلات متقدمة**: تقارير ذكية ومخصصة

### 2. تحسينات الأداء
- **معالجة متوازية**: تسريع العمليات الكبيرة
- **ضغط متقدم**: خوارزميات ضغط أفضل
- **تخزين مؤقت ذكي**: تحسين استراتيجيات التخزين المؤقت

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. راجع هذا الدليل أولاً
2. فحص السجلات في `/api/system/logs`
3. تواصل مع فريق التطوير

## التشغيل السريع

### 1. تشغيل النظام مع تدفق البيانات
```bash
# تشغيل كامل مع إعداد تلقائي
./start_with_streaming.sh

# أو تشغيل يدوي
cd backend
python main.py
```

### 2. اختبار النظام
```bash
# اختبار سريع
python3 quick_test_streaming.py

# اختبار شامل
python3 test_data_streaming.py --url http://localhost:8002
```

### 3. إيقاف النظام
```bash
# إيقاف آمن
./stop_system.sh

# إيقاف قسري
./stop_system.sh --force
```

## الملفات المضافة

### الخادم الخلفي (Backend)
- `backend/routers/data_streaming.py` - Router رئيسي لتدفق البيانات
- `backend/services/data_streaming_service.py` - خدمة تدفق البيانات المتقدمة
- `backend/schemas/data_streaming.py` - مخططات البيانات
- `backend/config/streaming_config.py` - إعدادات النظام

### الواجهة الأمامية (Frontend)
- `frontend/src/components/DataStreaming/DataExportManager.tsx` - مدير التصدير
- `frontend/src/components/DataStreaming/StreamingMetrics.tsx` - عرض المقاييس
- `frontend/src/hooks/useDataStreaming.ts` - Hook مخصص

### أدوات الاختبار والتشغيل
- `test_data_streaming.py` - اختبارات شاملة
- `quick_test_streaming.py` - اختبار سريع
- `start_with_streaming.sh` - تشغيل النظام
- `stop_system.sh` - إيقاف النظام

## الخلاصة

نظام تدفق البيانات الكبيرة يوفر حلاً شاملاً ومحسناً للتعامل مع البيانات الكبيرة في تطبيق SmartPOS. يدعم النظام تنسيقات متعددة، ضغط البيانات، إدارة المهام، والأمان المتقدم.

### الميزات المحققة ✅
- ✅ تدفق البيانات المستمر للمبيعات والمنتجات والعملاء
- ✅ دعم تنسيقات JSON و CSV
- ✅ ضغط البيانات بـ GZIP
- ✅ إدارة المهام غير المتزامنة
- ✅ مراقبة التقدم في الوقت الفعلي
- ✅ نظام أمان متقدم مع فحص الصلاحيات
- ✅ تنظيف تلقائي للملفات القديمة
- ✅ مقاييس الأداء والإحصائيات
- ✅ واجهة مستخدم محسنة للتصدير
- ✅ اختبارات شاملة ونصوص تشغيل

### التحسينات المستقبلية 🚀
- 🔄 دعم تنسيقات إضافية (XML, Parquet)
- 🔄 تصدير مجدول تلقائي
- 🔄 تكامل مع خدمات السحابة
- 🔄 تحليلات ذكية متقدمة
- 🔄 إشعارات في الوقت الفعلي
