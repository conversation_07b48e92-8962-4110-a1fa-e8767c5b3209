# تحديث ميزة Google Drive للنسخ الاحتياطية

## نظرة عامة

تم إضافة ميزة جديدة تسمح برفع النسخ الاحتياطية تلقائياً إلى Google Drive، مما يوفر حماية إضافية للبيانات في السحابة.

## الميزات الجديدة

### 1. إعدادات Google Drive
- **تفعيل/إلغاء تفعيل Google Drive**: تحكم في استخدام خدمة Google Drive
- **رفع بيانات الاعتماد**: واجهة آمنة لرفع ملف اعتماد Google API
- **اختبار الاتصال**: التحقق من صحة الإعداد والاتصال
- **تنظيف تلقائي**: حذف النسخ الاحتياطية القديمة تلقائياً
- **إعدادات متقدمة**: تحكم في عدد النسخ للاحتفاظ بها وحذف النسخ المحلية

### 2. مهام مجدولة جديدة
- **نسخ احتياطي إلى Google Drive**: رفع النسخ الاحتياطية تلقائياً
- **تنظيف Google Drive**: حذف النسخ القديمة للحفاظ على مساحة التخزين

### 3. واجهة إدارة Google Drive
- **عرض حالة الخدمة**: التوفر، التكوين، والتفعيل
- **إدارة الملفات**: عرض قائمة النسخ الاحتياطية في Google Drive
- **عمليات سريعة**: رفع، اختبار، وتنظيف بنقرة واحدة

## التحديثات التقنية

### Backend
- **خدمة Google Drive جديدة**: `backend/services/google_drive_service.py`
- **API endpoints جديدة**: `backend/routers/google_drive.py`
- **أنواع مهام جديدة**: دعم مهام Google Drive في المجدول
- **إعدادات جديدة**: 6 إعدادات جديدة لتكوين Google Drive

### Frontend
- **مكون إدارة Google Drive**: `frontend/src/components/GoogleDriveManager.tsx`
- **تحديث صفحة الإعدادات**: إضافة قسم Google Drive
- **تحديث مدير المهام**: دعم أنواع مهام Google Drive الجديدة
- **تحديث محدد نوع المهمة**: إضافة خيارات Google Drive

### Dependencies
- **مكتبات Google APIs جديدة**:
  - `google-api-python-client>=2.100.0`
  - `google-auth-httplib2>=0.1.0`
  - `google-auth-oauthlib>=1.0.0`

## كيفية الاستخدام

### 1. تثبيت المكتبات المطلوبة
```bash
cd backend
pip install -r requirements.txt
```

### 2. إعداد Google Cloud Console
راجع `docs/GOOGLE_DRIVE_SETUP.md` للحصول على دليل مفصل

### 3. تكوين النظام
1. افتح SmartPOS
2. اذهب إلى الإعدادات > Google Drive
3. رفع بيانات الاعتماد
4. اختبار الاتصال
5. تفعيل الخدمة

### 4. إعداد المهام المجدولة
1. اذهب إلى الإعدادات > النسخ الاحتياطية > المهام المجدولة
2. ستجد مهمتين جديدتين متوقفتين:
   - "نسخ احتياطي يومي إلى Google Drive"
   - "تنظيف Google Drive أسبوعياً"
3. فعل المهام بعد تكوين Google Drive

## الأمان والخصوصية

- **تشفير البيانات**: جميع البيانات محمية بتشفير Google Drive
- **نطاقات محدودة**: استخدام نطاق `drive.file` فقط
- **تخزين آمن**: بيانات الاعتماد محفوظة بشكل آمن في قاعدة البيانات
- **تجديد تلقائي**: تجديد رموز الوصول تلقائياً

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **"مكتبات Google APIs غير متوفرة"**
   - تأكد من تثبيت المكتبات المطلوبة
   - أعد تشغيل الخادم بعد التثبيت

2. **"خدمة Google Drive غير مكونة"**
   - تأكد من رفع بيانات الاعتماد الصحيحة
   - تأكد من تفعيل Google Drive API

3. **"فشل في الاتصال"**
   - تحقق من اتصال الإنترنت
   - تأكد من صحة بيانات الاعتماد
   - تحقق من سجلات النظام

## الملفات المضافة/المحدثة

### ملفات جديدة
- `backend/services/google_drive_service.py`
- `backend/routers/google_drive.py`
- `backend/scripts/add_google_drive_settings.py`
- `backend/scripts/create_google_drive_tasks.py`
- `frontend/src/components/GoogleDriveManager.tsx`
- `docs/GOOGLE_DRIVE_SETUP.md`

### ملفات محدثة
- `backend/requirements.txt`
- `backend/main.py`
- `backend/models/scheduled_task.py`
- `backend/services/scheduler_service.py`
- `frontend/src/pages/Settings.tsx`
- `frontend/src/components/ScheduledTasksManager.tsx`
- `frontend/src/components/TaskTypeSelector.tsx`

## الاختبار

### اختبار الميزة
1. **اختبار الإعداد**:
   - رفع بيانات اعتماد صحيحة
   - اختبار الاتصال
   - التحقق من إنشاء مجلد النسخ الاحتياطية

2. **اختبار النسخ الاحتياطي**:
   - تشغيل مهمة النسخ الاحتياطي يدوياً
   - التحقق من رفع الملف إلى Google Drive
   - التحقق من سجلات النظام

3. **اختبار التنظيف**:
   - إنشاء عدة نسخ احتياطية
   - تشغيل مهمة التنظيف
   - التحقق من حذف النسخ القديمة

## الدعم والصيانة

- **مراقبة الأخطاء**: تتبع أخطاء المهام في نظام السجلات
- **تجديد الرموز**: تجديد تلقائي لرموز الوصول
- **نسخ احتياطية محلية**: الاحتفاظ بنسخ محلية كنسخ احتياطية إضافية

## خطط مستقبلية

- **دعم خدمات سحابية أخرى**: Dropbox, OneDrive
- **ضغط متقدم**: ضغط النسخ الاحتياطية قبل الرفع
- **تشفير إضافي**: تشفير النسخ الاحتياطية قبل الرفع
- **مزامنة ثنائية الاتجاه**: استعادة النسخ الاحتياطية من السحابة
