# 🕐 تكامل خدمة التاريخ والوقت مع نظام الفروع والمستودعات

> **تاريخ الإنشاء:** 2025-01-11  
> **الإصدار:** 1.0  
> **المطور:** Augment Agent  

## 📖 نظرة عامة

تم تطوير نظام إدارة الفروع والمستودعات ليتكامل بشكل كامل مع خدمة التاريخ والوقت الموحدة في المشروع، مما يضمن الاتساق في التعامل مع التواريخ والأوقات والمناطق الزمنية عبر جميع أجزاء النظام.

## 🔧 الخدمات المستخدمة

### 1. **خدمة التاريخ والوقت الأساسية**

```python
from utils.datetime_utils import (
    get_tripoli_now,                    # الوقت الحالي بتوقيت طرابلس
    get_current_time_with_settings,     # الوقت الحالي بإعدادات المستخدم
    create_timestamp_with_settings,     # إنشاء timestamp بالإعدادات
    tripoli_timestamp                   # دالة SQL للـ timestamps
)
```

### 2. **خدمة التاريخ والوقت في Frontend**

```typescript
import {
    getCurrentTripoliDateTime,          // الوقت الحالي بتوقيت طرابلس
    formatDateTime,                     // تنسيق التاريخ والوقت
    formatDateTimeWithSettings,         // تنسيق بالإعدادات المخصصة
    fetchDateTimeSettings               // جلب إعدادات التاريخ والوقت
} from '../services/dateTimeService';
```

## 🏗️ التكامل في النماذج (Models)

### نموذج الفروع (Branch)

```python
class Branch(Base):
    # تواريخ النظام مع دعم المنطقة الزمنية
    created_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())
    updated_at = Column(DateTime(timezone=True), onupdate=tripoli_timestamp())
    
    # مراجع المستخدمين
    created_by = Column(Integer, ForeignKey('users.id'))
    updated_by = Column(Integer, ForeignKey('users.id'))
```

### جدول الربط (branch_warehouses)

```python
branch_warehouses = Table(
    'branch_warehouses',
    Base.metadata,
    Column('created_at', DateTime(timezone=True), server_default=tripoli_timestamp()),
    # باقي الأعمدة...
)
```

## 🔄 التكامل في الخدمات (Services)

### 1. خدمة الفروع (BranchService)

```python
class BranchService:
    def _get_current_time(self):
        """الحصول على الوقت الحالي بإعدادات المنطقة الزمنية"""
        return get_current_time_with_settings(self.db_session)
    
    def create_branch(self, branch_data: Dict[str, Any]) -> Dict[str, Any]:
        # استخدام الخدمة الموحدة للتواريخ
        branch = Branch(
            # ... باقي البيانات
            created_at=self._get_current_time(),
            updated_at=self._get_current_time(),
        )
```

### 2. خدمة العلاقات (BranchWarehouseService)

```python
class BranchWarehouseService:
    def link_branch_to_warehouse(self, branch_id: int, warehouse_id: int):
        # استخدام الخدمة الموحدة في SQL المباشر
        self.db_session.execute(
            text("INSERT INTO branch_warehouses (...) VALUES (...)"),
            {"created_at": get_current_time_with_settings(self.db_session)}
        )
```

### 3. خدمة التوزيع الذكي (SmartDistributionService)

```python
class SmartDistributionService:
    def get_branch_distribution_analytics(self, branch_id: int, days_back: int = 30):
        # استخدام الخدمة الموحدة لحساب التواريخ
        from datetime import timedelta
        date_from = get_current_time_with_settings(self.db_session) - timedelta(days=days_back)
```

### 4. خدمة التقارير المتقدمة (AdvancedReportsService)

```python
class AdvancedReportsService:
    def generate_branch_performance_report(self, date_from=None, date_to=None):
        # استخدام الخدمة الموحدة للتواريخ الافتراضية
        if not date_from:
            current_time = get_current_time_with_settings(self.db_session)
            date_from = (current_time - timedelta(days=30)).isoformat()
        
        return {
            'generated_at': get_current_time_with_settings(self.db_session).isoformat()
        }
```

## 📊 التكامل في التقارير

### 1. تقارير الأداء

```python
# جميع التقارير تستخدم الخدمة الموحدة
report_data = {
    'report_type': 'branch_performance',
    'generated_at': get_current_time_with_settings(db_session).isoformat(),
    'date_range': {
        'from': date_from,
        'to': date_to
    }
}
```

### 2. تقارير الاستخدام

```python
# حساب الفترات الزمنية بالخدمة الموحدة
date_30_days_ago = get_current_time_with_settings(db_session) - timedelta(days=30)
```

## 🔧 Migration والتحديثات

### Migration Script

```python
# استخدام الخدمة الموحدة في Migration
from utils.datetime_utils import get_tripoli_now

def _migrate_existing_data(self):
    # إنشاء الفرع الرئيسي مع التاريخ الصحيح
    session.execute(
        insert_main_branch, 
        {"created_at": get_tripoli_now()}
    )
```

### فحص سلامة البيانات

```python
# استخدام الخدمة الموحدة في تسجيل المشاكل
def _add_issue(self, severity, issue_type, description, solution):
    self.issues_found.append({
        'timestamp': get_tripoli_now().isoformat()
    })
```

## 🌐 التكامل مع Frontend

### استخدام خدمة التاريخ في واجهة المستخدم

```typescript
// تنسيق تواريخ الفروع
const formatBranchDate = async (date: string) => {
    return await formatDateTimeWithSettings(date, 'datetime');
};

// عرض تاريخ إنشاء الفرع
const branchCreatedAt = await formatBranchDate(branch.created_at);
```

### تقارير الفروع في Frontend

```typescript
// جلب تقرير أداء الفروع مع التواريخ المنسقة
const branchReport = await api.get('/api/reports/branch-performance');
const formattedDate = await formatDateTime(branchReport.generated_at);
```

## ⚙️ الإعدادات والتخصيص

### 1. إعدادات المنطقة الزمنية

```python
# يمكن تخصيص المنطقة الزمنية من إعدادات قاعدة البيانات
# الخدمة تقرأ تلقائياً من جدول settings
timezone_setting = get_timezone_from_settings(db_session)
```

### 2. تنسيق التواريخ

```typescript
// يمكن تخصيص تنسيق التواريخ من إعدادات المستخدم
const settings = await fetchDateTimeSettings();
const formattedDate = await formatDateWithSettings(date, settings);
```

## 🔍 أفضل الممارسات

### 1. في الخدمات (Backend)

```python
# ✅ استخدم الخدمة الموحدة دائماً
current_time = get_current_time_with_settings(self.db_session)

# ❌ لا تستخدم datetime.now() مباشرة
# current_time = datetime.now()  # خطأ!
```

### 2. في النماذج (Models)

```python
# ✅ استخدم tripoli_timestamp() للقيم الافتراضية
created_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())

# ❌ لا تستخدم func.now() مباشرة
# created_at = Column(DateTime, server_default=func.now())  # خطأ!
```

### 3. في Frontend

```typescript
// ✅ استخدم خدمة التاريخ الموحدة
const currentTime = getCurrentTripoliDateTime();

// ❌ لا تستخدم new Date() مباشرة للعرض
// const currentTime = new Date();  // خطأ!
```

## 🧪 الاختبارات

### اختبار التكامل

```python
def test_branch_creation_with_correct_timezone():
    # التأكد من استخدام المنطقة الزمنية الصحيحة
    branch_service = get_branch_service(db)
    result = branch_service.create_branch(test_data)
    
    # التحقق من التاريخ
    assert result['branch']['created_at'] is not None
    # التحقق من المنطقة الزمنية
    created_time = datetime.fromisoformat(result['branch']['created_at'])
    assert created_time.tzinfo is not None
```

## 📝 ملاحظات مهمة

1. **الاتساق**: جميع التواريخ في النظام تستخدم نفس المنطقة الزمنية
2. **المرونة**: يمكن تغيير المنطقة الزمنية من الإعدادات
3. **التوافق**: النظام متوافق مع إعدادات التاريخ والوقت الموجودة
4. **الأداء**: استخدام cache للإعدادات لتحسين الأداء

## 🔗 المراجع

- [خدمة التاريخ والوقت الأساسية](../backend/utils/datetime_utils.py)
- [خدمة التاريخ في Frontend](../frontend/src/services/dateTimeService.ts)
- [دليل إعدادات التاريخ والوقت](./updates/DATETIME_SETTINGS_UPDATE.md)
- [نظام الفروع والمستودعات](./BRANCH_WAREHOUSE_SYSTEM_README.md)

---

> **📝 ملاحظة:** هذا التكامل يضمن الاتساق الكامل في التعامل مع التواريخ والأوقات عبر جميع أجزاء نظام الفروع والمستودعات.
