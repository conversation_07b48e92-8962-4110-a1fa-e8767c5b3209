# دليل نقل قاعدة البيانات إلى مجلد Backend

## 📋 نظرة عامة

تم نقل قاعدة البيانات `smartpos.db` من المجلد الجذر للمشروع إلى مجلد `backend` لتحسين تنظيم المشروع وتجنب الأخطاء.

## 🔄 التغييرات المنفذة

### 1. نقل ملفات قاعدة البيانات

```bash
# تم نقل الملفات التالية من الجذر إلى backend/
smartpos.db       → backend/smartpos.db
smartpos.db-shm   → backend/smartpos.db-shm
smartpos.db-wal   → backend/smartpos.db-wal
```

### 2. تحديث مسارات قاعدة البيانات

#### `backend/database/session.py`
```python
# قبل التغيير
DB_PATH = Path(__file__).parent.parent.parent / "smartpos.db"

# بعد التغيير
DB_PATH = Path(__file__).parent.parent / "smartpos.db"
```

#### `backend/utils/backup.py`
```python
# قبل التغيير
def __init__(self, db_path: str = "../smartpos.db", backup_dir: str = "backups"):

# بعد التغيير
def __init__(self, db_path: str = "smartpos.db", backup_dir: str = "backups"):
```

#### `backend/scripts/check_database_status.py`
```python
# قبل التغيير
db_path = "smartpos.db"

# بعد التغيير
db_path = "smartpos.db"  # (يعمل من داخل مجلد backend)
```

#### `backend/scripts/fix_debt_data_accuracy.py`
```python
# قبل التغيير
db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '..', 'smartpos.db')

# بعد التغيير
db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'smartpos.db')
```

#### `backend/scripts/add_tax_amount_column.py`
```python
# قبل التغيير
db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '..', 'smartpos.db')

# بعد التغيير
db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'smartpos.db')
```

#### `backend/routers/dashboard.py`
```python
# تم تحديث المسارات المحتملة في 3 مواقع:
possible_paths = [
    "smartpos.db",     # في مجلد backend (المسار الجديد)
    "./smartpos.db",   # في مجلد backend (صريح)
    "../smartpos.db",  # من مجلد backend إلى المجلد الجذر (للتوافق مع النسخ القديمة)
    "../../smartpos.db"  # في حالة كان هناك مجلد إضافي
]
```

## ✅ الاختبارات المنفذة

تم إنشاء سكريپت اختبار شامل `backend/test_database_migration.py` يتحقق من:

1. ✅ وجود ملف قاعدة البيانات في المكان الجديد
2. ✅ استيراد إعدادات قاعدة البيانات
3. ✅ الاتصال بقاعدة البيانات
4. ✅ إنشاء جلسة قاعدة البيانات
5. ✅ استيراد النماذج
6. ✅ عدد السجلات في الجداول
7. ✅ تحميل التطبيق الرئيسي

## 📊 نتائج الاختبار

```
🔍 اختبار نقل قاعدة البيانات...
==================================================
1️⃣ فحص وجود ملف قاعدة البيانات...
   ✅ ملف قاعدة البيانات موجود: smartpos.db (2.33 MB)
2️⃣ اختبار استيراد إعدادات قاعدة البيانات...
   ✅ مسار قاعدة البيانات: sqlite:////home/<USER>/Documents/SmartPOS/SmartPosWeb/backend/smartpos.db
3️⃣ اختبار الاتصال بقاعدة البيانات...
   ✅ عدد الجداول: 11
4️⃣ اختبار جلسة قاعدة البيانات...
   ✅ تم إنشاء جلسة قاعدة البيانات بنجاح
5️⃣ اختبار استيراد النماذج...
   ✅ تم استيراد جميع النماذج بنجاح
6️⃣ اختبار عدد السجلات...
   ✅ المستخدمون: 6
   ✅ المنتجات: 1020
   ✅ العملاء: 515
   ✅ المبيعات: 2085
7️⃣ اختبار التطبيق الرئيسي...
   ✅ تم تحميل التطبيق الرئيسي بنجاح

==================================================
🎉 جميع الاختبارات نجحت!
✅ تم نقل قاعدة البيانات بنجاح إلى مجلد backend
✅ جميع المسارات تعمل بشكل صحيح
```

## 🔧 كيفية تشغيل الاختبار

```bash
cd backend
python test_database_migration.py
```

## 📝 ملاحظات مهمة

1. **التوافق مع النسخ القديمة**: تم الاحتفاظ بالمسارات القديمة في `dashboard.py` للتوافق مع النسخ الاحتياطية القديمة
2. **النسخ الاحتياطية**: جميع النسخ الاحتياطية الموجودة في مجلد `backups` ستعمل بشكل طبيعي
3. **السكريپتات**: جميع السكريپتات في مجلد `scripts` تم تحديثها للعمل مع المسار الجديد
4. **الأداء**: لا يوجد تأثير على أداء النظام، بل تحسن في تنظيم الملفات

## 🚀 الخطوات التالية

1. تشغيل النظام والتأكد من عمل جميع الوظائف
2. إنشاء نسخة احتياطية جديدة للتأكد من عمل نظام النسخ الاحتياطي
3. اختبار جميع العمليات (المبيعات، المنتجات، العملاء، إلخ)

## 📞 الدعم

في حالة وجود أي مشاكل، يمكن تشغيل سكريپت الاختبار للتحقق من حالة النظام:

```bash
cd backend
python test_database_migration.py
```
