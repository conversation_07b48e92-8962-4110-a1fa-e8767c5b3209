# 🛠️ دليل تثبيت وإعداد نظام المحادثة الفورية

## 📋 المتطلبات الأساسية

### Backend Requirements
- Python 3.8+
- FastAPI
- SQLAlchemy
- WebSockets
- Pydantic
- **جديد**: httpx>=0.24.0 (لمعاينة الروابط)
- **جديد**: beautifulsoup4>=4.12.0 (لتحليل HTML)

### Frontend Requirements
- React 18+
- TypeScript
- date-fns
- Zustand (للحالة العامة)

### قاعدة البيانات
- SQLite (الافتراضي)
- أو PostgreSQL/MySQL (للإنتاج)

## 🚀 خطوات التثبيت

### 1. إعداد Backend

#### أ. تفعيل البيئة الافتراضية
```bash
cd backend
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate     # Windows
```

#### ب. تثبيت التبعيات (إذا لم تكن مثبتة)
```bash
# التبعيات الأساسية
pip install fastapi uvicorn websockets sqlalchemy pydantic python-multipart

# التبعيات الجديدة لمعاينة الروابط
pip install httpx beautifulsoup4

# أو تثبيت جميع التبعيات من requirements.txt
pip install -r requirements.txt
```

#### ج. تشغيل Migration لقاعدة البيانات
```bash
python migrations/add_chat_system.py
```

**النتيجة المتوقعة:**
```
🔄 بدء migration لإضافة نظام المحادثة...
✅ تم إضافة عمود is_online لجدول المستخدمين
✅ تم إضافة عمود last_seen لجدول المستخدمين
✅ تم إنشاء جدول chat_messages
✅ تم إنشاء جدول chat_rooms
✅ تم إنشاء جدول chat_room_members
✅ تم إنشاء جدول user_online_status
✅ تم إنشاء الفهارس لتحسين الأداء
✅ تم إنشاء triggers للتحديث التلقائي
🎉 تم إكمال migration نظام المحادثة بنجاح!
```

#### د. تشغيل الخادم
```bash
python main.py
```

**التحقق من نجاح التشغيل:**
- يجب أن ترى رسالة: `✅ تم بدء تشغيل الخادم بنجاح على المنفذ 8002`
- تصفح: `http://localhost:8002/docs` للتأكد من وجود endpoints المحادثة

### 2. إعداد Frontend

#### أ. تثبيت التبعيات الجديدة
```bash
cd frontend
npm install date-fns
```

#### ب. التحقق من الملفات المطلوبة
تأكد من وجود الملفات التالية:
```
frontend/src/
├── services/
│   ├── chatWebSocketService.ts
│   └── chatApiService.ts
├── components/Chat/
│   ├── ChatWindow.tsx
│   ├── ChatButton.tsx
│   ├── ConversationsList.tsx
│   ├── MessagesList.tsx
│   ├── MessageInput.tsx
│   ├── OnlineUsersList.tsx      # جديد
│   ├── AllUsersList.tsx         # جديد
│   ├── UserSearch.tsx
│   ├── ChatWindow.css
│   ├── ChatButton.css
│   ├── OnlineUsersList.css      # جديد
│   ├── AllUsersList.css         # جديد
│   └── UserSearch.css
├── styles/
│   └── scrollbar.css            # شريط التمرير الموحد
└── hooks/
    └── useChat.ts
```

#### ج. تشغيل Frontend
```bash
npm run dev
```

## 🧪 اختبار التثبيت

### 1. اختبار Backend
```bash
cd backend
python test_chat_system.py
```

**النتيجة المتوقعة:**
```
🚀 بدء اختبار نظام المحادثة الفورية
==================================================
🗄️ اختبار جداول قاعدة البيانات...
✅ جدول chat_messages موجود
✅ جدول chat_rooms موجود
✅ جدول chat_room_members موجود
✅ جدول user_online_status موجود
✅ عمود is_online موجود في جدول المستخدمين
✅ عمود last_seen موجود في جدول المستخدمين

🔍 اختبار صحة الخادم...
✅ الخادم يعمل بشكل صحيح

🔐 تسجيل دخول المستخدم: admin
✅ تم تسجيل دخول admin بنجاح

🧪 اختبار endpoints المحادثة للمستخدم: admin
✅ حالة المحادثة: متصل=False
✅ تم جلب 0 محادثة
✅ عدد الرسائل غير المقروءة: 0

==================================================
🎉 تم إكمال اختبار نظام المحادثة الفورية
✅ النظام جاهز للاستخدام!
```

### 2. اختبار Frontend

#### أ. إضافة زر المحادثة لصفحة اختبار
أنشئ ملف `frontend/src/pages/ChatTest.tsx`:
```tsx
import React from 'react';
import { ChatButton } from '../components/Chat/ChatButton';

const ChatTest: React.FC = () => {
  return (
    <div style={{ padding: '20px' }}>
      <h1>اختبار نظام المحادثة</h1>
      <p>يجب أن ترى زر المحادثة في الزاوية السفلية اليمنى</p>
      <ChatButton />
    </div>
  );
};

export default ChatTest;
```

#### ب. اختبار الوظائف الأساسية
1. **تسجيل الدخول**: تأكد من تسجيل دخول مستخدم
2. **ظهور الزر**: يجب أن يظهر زر المحادثة العائم
3. **فتح النافذة**: انقر على الزر لفتح نافذة المحادثة
4. **الاتصال**: تحقق من حالة الاتصال في رأس النافذة

## 🔧 التكوين المتقدم

### 1. إعدادات WebSocket

في `frontend/src/services/chatWebSocketService.ts`:
```typescript
// تخصيص إعدادات الاتصال
const heartbeatInterval = 30000; // فترة heartbeat (30 ثانية)
const maxReconnectAttempts = 5;   // عدد محاولات إعادة الاتصال
const reconnectDelay = 1000;      // تأخير إعادة الاتصال (1 ثانية)
```

### 2. إعدادات الخادم

في `backend/main.py`:
```python
# تخصيص منفذ الخادم
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8002)  # غير المنفذ حسب الحاجة
```

### 3. إعدادات قاعدة البيانات

لاستخدام PostgreSQL بدلاً من SQLite:
```python
# في backend/database/session.py
DATABASE_URL = "postgresql://user:password@localhost/smartpos"
```

## 🔍 استكشاف الأخطاء

### مشاكل Backend شائعة

#### 1. خطأ في قاعدة البيانات
```
sqlite3.OperationalError: no such column: users.is_online
```
**الحل**: تشغيل migration مرة أخرى
```bash
python migrations/add_chat_system.py
```

#### 2. خطأ في WebSocket
```
WebSocketException: Connection failed
```
**الحل**: تحقق من:
- تشغيل الخادم على المنفذ الصحيح
- إعدادات CORS
- جدار الحماية

#### 3. خطأ في المصادقة
```
401 Unauthorized
```
**الحل**: تحقق من:
- صحة JWT token
- انتهاء صلاحية الجلسة
- إعدادات المصادقة

### مشاكل Frontend شائعة

#### 1. خطأ في الاستيراد
```
Module not found: Can't resolve './components/Chat/ChatButton'
```
**الحل**: تحقق من:
- وجود الملفات في المسارات الصحيحة
- صحة أسماء الملفات
- امتدادات الملفات (.tsx)

#### 2. خطأ في date-fns
```
Module not found: Can't resolve 'date-fns'
```
**الحل**: تثبيت التبعية
```bash
npm install date-fns
```

#### 3. خطأ في useAuthStore
```
Cannot find module 'useAuth'
```
**الحل**: استخدام `useAuthStore` بدلاً من `useAuth`
```tsx
import { useAuthStore } from '../stores/authStore';
const { user } = useAuthStore();
```

## 📊 مراقبة الأداء

### 1. مراقبة WebSocket
```javascript
// في Developer Tools -> Network -> WS
// تحقق من:
// - حالة الاتصال
// - رسائل heartbeat
// - رسائل البيانات
```

### 2. مراقبة قاعدة البيانات
```sql
-- فحص عدد الرسائل
SELECT COUNT(*) FROM chat_messages;

-- فحص المستخدمين المتصلين
SELECT COUNT(*) FROM user_online_status WHERE is_online = 1;

-- فحص أحدث الرسائل
SELECT * FROM chat_messages ORDER BY created_at DESC LIMIT 10;
```

### 3. مراقبة الخادم
```bash
# فحص استخدام الذاكرة والمعالج
top -p $(pgrep -f "python main.py")

# فحص الاتصالات النشطة
netstat -an | grep :8002
```

## 🔄 التحديثات المستقبلية

### إضافة ميزات جديدة
1. **إنشاء migration جديد** لأي تغييرات في قاعدة البيانات
2. **تحديث API endpoints** حسب الحاجة
3. **إضافة مكونات UI جديدة** للميزات الجديدة
4. **تحديث التوثيق** لتشمل الميزات الجديدة

### صيانة النظام
1. **تنظيف الرسائل القديمة** دورياً
2. **تحديث الفهارس** لتحسين الأداء
3. **مراقبة استخدام الذاكرة** للاتصالات
4. **نسخ احتياطية منتظمة** لقاعدة البيانات

## 📞 الدعم الفني

### الحصول على المساعدة
1. **مراجعة السجلات**: تحقق من ملفات السجل للأخطاء
2. **اختبار الاتصال**: استخدم أدوات المطور للتحقق من WebSocket
3. **فحص قاعدة البيانات**: تأكد من وجود الجداول والبيانات
4. **إعادة التشغيل**: أعد تشغيل الخادم والمتصفح

### معلومات مفيدة للدعم
- إصدار Python ونظام التشغيل
- رسائل الخطأ الكاملة
- خطوات إعادة إنتاج المشكلة
- حالة قاعدة البيانات والخادم

## 🎨 تحديثات التصميم والواجهة الجديدة

### متطلبات إضافية للتصميم المحدث
- React Icons (مثبت مسبقاً)
- دعم CSS Variables للوضع المظلم
- شريط التمرير المخصص للمشروع

### خطوات التحديث للتصميم الجديد

#### 1. التحقق من ملفات CSS المحدثة
تأكد من وجود الملفات المحدثة:
```bash
# فحص ملفات CSS المحدثة
ls frontend/src/components/Chat/*.css
ls frontend/src/styles/scrollbar.css
```

**الملفات المطلوبة:**
- `ChatWindow.css` - تصميم النافذة الرئيسية
- `ChatButton.css` - تصميم الزر العائم
- `OnlineUsersList.css` - تصميم قائمة المستخدمين المتاحين
- `AllUsersList.css` - تصميم قائمة جميع المستخدمين
- `UserSearch.css` - تصميم البحث
- `scrollbar.css` - شريط التمرير الموحد

#### 2. التحقق من الأيقونات المحدثة
تأكد من استيراد الأيقونات الصحيحة:
```typescript
import {
  FaComments,      // للمحادثات
  FaUserFriends,   // للمستخدمين المتاحين
  FaUsers,         // لجميع المستخدمين
  FaSearch,        // للبحث
  FaCircle,        // لحالة الاتصال
  FaClock,         // لآخر ظهور
  FaTimes,         // للإغلاق
  FaSpinner        // للتحميل
} from 'react-icons/fa';
```

#### 3. التحقق من API Endpoints الجديدة
تأكد من وجود endpoint جديد:
```bash
# اختبار endpoint جديد
curl http://localhost:8002/api/chat/users/all \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 4. اختبار التحسينات الجديدة
```bash
# تشغيل التطبيق واختبار:
npm run dev
```

**اختبر الميزات التالية:**
- ✅ تبويب "المستخدمون المتاحون" مع البحث المدمج
- ✅ تبويب "عرض كل المستخدمين" الجديد
- ✅ بطاقات المستخدمين المحسنة والمضغوطة
- ✅ شريط التمرير المخفي تلقائياً
- ✅ إزالة العناصر المكررة والأزرار غير الضرورية

### التحقق من التوافق الشامل
- ✅ دعم الوضع المظلم والفاتح المحسن
- ✅ أيقونات React Icons موحدة
- ✅ ألوان متوافقة مع نظام المشروع
- ✅ شريط تمرير موحد مع إخفاء تلقائي
- ✅ بطاقات مستخدمين مبسطة ومضغوطة
- ✅ تبويبات منظمة بدون تكرار
- ✅ تصميم احترافي ونظيف
- ✅ أداء محسن مع تقليل العناصر غير الضرورية

### استكشاف أخطاء التحديث الجديد

#### 1. مشكلة في تبويب "عرض كل المستخدمين"
```
Error: Cannot fetch all users
```
**الحل**: تحقق من:
- وجود endpoint `/api/chat/users/all` في الخادم
- صحة المصادقة والصلاحيات
- تشغيل migration قاعدة البيانات

#### 2. مشكلة في شريط التمرير
```
Scrollbar not working properly
```
**الحل**: تحقق من:
- استيراد `scrollbar.css` في `ChatWindow.tsx`
- وجود class `custom-scrollbar-auto` في الحاويات
- تطبيق CSS Variables بشكل صحيح

#### 3. مشكلة في بطاقات المستخدمين
```
User cards layout broken
```
**الحل**: تحقق من:
- تحديث ملفات CSS للمكونات
- استخدام `user-name-container` في `AllUsersList`
- تطبيق التحسينات على جميع المكونات

---

## 🔧 إصلاحات حرجة (الإصدار 1.2.1)

**تاريخ الإصلاح**: ديسمبر 2024
**نوع التحديث**: إصلاحات حرجة لمشاكل أساسية

### المشاكل المصححة في هذا الإصدار

#### 1. إصلاح مشكلة عدم جلب المحادثات السابقة
- **المشكلة**: API لا يجلب المحادثات السابقة بسبب خطأ SQLAlchemy
- **الحل**: تصحيح استيراد واستخدام `case` في `chat_message_service.py`
- **التأثير**: الآن يمكن رؤية جميع المحادثات السابقة

#### 2. إصلاح مشكلة بيانات المستخدم في هيدر المحادثة
- **المشكلة**: عدم ظهور اسم المستخدم وبياناته في هيدر المحادثة
- **الحل**: إضافة آلية لحفظ واستخدام بيانات المستخدم المختار
- **التأثير**: عرض صحيح لاسم المستخدم وحالة الاتصال

### خطوات تطبيق الإصلاحات

#### للمشاريع الموجودة:
```bash
# 1. تحديث ملفات Backend
cd backend
# لا حاجة لـ migration جديد، فقط إعادة تشغيل الخادم
python main.py

# 2. تحديث ملفات Frontend
cd frontend
# لا حاجة لتبعيات جديدة، فقط إعادة تشغيل التطبيق
npm run dev
```

#### للمشاريع الجديدة:
اتبع خطوات التثبيت العادية، الإصلاحات مدمجة في الكود.

### التحقق من نجاح الإصلاحات

#### اختبار Backend:
```bash
# اختبار جلب المحادثات
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8002/api/chat/conversations

# النتيجة المتوقعة: قائمة بالمحادثات (ليس قائمة فارغة)
```

#### اختبار Frontend:
1. ✅ فتح نافذة المحادثة
2. ✅ رؤية المحادثات السابقة في تبويب "المحادثات"
3. ✅ اختيار مستخدم من "المستخدمون المتاحون"
4. ✅ التأكد من ظهور اسم المستخدم في الهيدر
5. ✅ إرسال رسالة والتأكد من عملها

### الملفات المتأثرة بالإصلاحات

```
backend/services/
└── chat_message_service.py    # إصلاح خطأ SQLAlchemy

frontend/src/components/Chat/
├── ChatWindow.tsx             # تحسين إدارة بيانات المستخدم
├── OnlineUsersList.tsx        # تمرير بيانات المستخدم
└── AllUsersList.tsx           # تمرير بيانات المستخدم
```

### مؤشرات نجاح الإصلاحات

#### قبل الإصلاح:
- ❌ قائمة المحادثات فارغة
- ❌ هيدر المحادثة يظهر "آخر ظهور غير معروف"
- ❌ أخطاء في سجلات الخادم

#### بعد الإصلاح:
- ✅ قائمة المحادثات تعرض المحادثات السابقة
- ✅ هيدر المحادثة يعرض اسم المستخدم وحالة الاتصال
- ✅ لا توجد أخطاء في سجلات الخادم
- ✅ نظام محادثة مكتمل وسلس

## 🆕 إعداد الميزات الجديدة (يناير 2025)

### 🔗 إعداد معاينة الروابط

#### 1. التحقق من تثبيت التبعيات الجديدة
```bash
# التحقق من httpx
python -c "import httpx; print('httpx installed:', httpx.__version__)"

# التحقق من beautifulsoup4
python -c "import bs4; print('beautifulsoup4 installed:', bs4.__version__)"
```

#### 2. اختبار API معاينة الروابط
```bash
# اختبار endpoint معاينة الروابط
curl "http://localhost:8002/api/link-preview?url=https://www.google.com"
```

**النتيجة المتوقعة:**
```json
{
  "url": "https://www.google.com",
  "title": "Google",
  "description": "Search the world's information...",
  "domain": "google.com",
  "favicon": "https://www.google.com/s2/favicons?domain=google.com",
  "success": true
}
```

### 😊 إعداد منتقي الإيموجي المحسن

#### 1. التحقق من ملفات CSS الجديدة
```bash
# التأكد من وجود ملف emoji.css
ls frontend/src/styles/emoji.css

# التأكد من تحديث ChatWindow.css
grep -n "link-preview" frontend/src/components/Chat/ChatWindow.css
```

#### 2. اختبار الميزات الجديدة
1. **معاينة الروابط**:
   - أرسل رسالة تحتوي على رابط (مثل: https://www.google.com)
   - يجب أن تظهر معاينة تلقائية تحت الرسالة

2. **مؤشر الكتابة المحسن**:
   - ابدأ بكتابة رسالة
   - يجب أن يظهر مؤشر "يكتب..." للمستخدم الآخر
   - يجب أن يختفي المؤشر بعد 3-5 ثوان من التوقف

3. **منتقي الإيموجي المحسن**:
   - اضغط على أيقونة الإيموجي
   - يجب أن تظهر النافذة في منتصف الأيقونة
   - يجب أن تتكيف مع حجم الشاشة

### 🔧 استكشاف الأخطاء للميزات الجديدة

#### مشاكل معاينة الروابط
```bash
# فحص سجلات الخادم
tail -f backend/logs/app.log | grep "link-preview"

# اختبار الاتصال بالإنترنت
curl -I https://www.google.com
```

#### مشاكل مؤشر الكتابة
```bash
# فحص WebSocket connections
grep "typing" backend/logs/app.log
```

#### مشاكل منتقي الإيموجي
- تأكد من تحديث المتصفح (Ctrl+F5)
- تحقق من console للأخطاء JavaScript
- تأكد من تحميل ملفات CSS الجديدة

### 📊 مؤشرات نجاح الميزات الجديدة

#### معاينة الروابط:
- ✅ الروابط تظهر كروابط قابلة للنقر
- ✅ معاينة تلقائية تظهر تحت الرسائل
- ✅ معلومات الموقع (عنوان، وصف، صورة) تظهر بشكل صحيح
- ✅ النقر على المعاينة يفتح الرابط في نافذة جديدة

#### مؤشر الكتابة المحسن:
- ✅ مؤشر يظهر عند بدء الكتابة
- ✅ مؤشر يختفي بعد التوقف عن الكتابة
- ✅ مؤشر يختفي فوراً عند إرسال رسالة
- ✅ تصميم أنيق مع أيقونة المستخدم

#### منتقي الإيموجي المحسن:
- ✅ نافذة تظهر في المكان الصحيح
- ✅ تصنيفات واضحة للإيموجي
- ✅ بحث سريع يعمل بشكل صحيح
- ✅ تكيف مع أحجام الشاشات المختلفة

**آخر تحديث**: يناير 2025
**الإصدار**: 2.2.0 (معاينة الروابط ومؤشر الكتابة المحسن)
