# 🔧 دليل استكشاف أخطاء نظام المحادثة الفورية

## 📋 نظرة عامة

هذا الدليل يساعدك في تشخيص وحل المشاكل الشائعة في نظام المحادثة الفورية. يغطي مشاكل Backend و Frontend و WebSocket وقاعدة البيانات.

## 🚨 المشاكل الشائعة وحلولها

### 1. مشاكل الاتصال

#### المشكلة: لا يمكن الاتصال بالخادم
**الأعراض:**
- رسالة "Network Error" في المتصفح
- CORS policy errors
- Connection refused

**التشخيص:**
```bash
# فحص حالة الخادم
curl http://localhost:8002/api/system/health

# فحص المنفذ
netstat -an | grep :8002
```

**الحلول:**
1. **تأكد من تشغيل الخادم:**
   ```bash
   cd backend
   ./venv/bin/python main.py
   ```

2. **فحص إعدادات CORS:**
   ```python
   # في main.py
   app.add_middleware(
       CORSMiddleware,
       allow_origins=["*"],  # أو المجالات المحددة
       allow_credentials=True,
       allow_methods=["*"],
       allow_headers=["*"],
   )
   ```

3. **فحص جدار الحماية:**
   ```bash
   # Linux
   sudo ufw allow 8002
   
   # Windows
   # إضافة استثناء في Windows Firewall
   ```

#### المشكلة: WebSocket لا يتصل
**الأعراض:**
- حالة "غير متصل" في نافذة المحادثة
- أخطاء WebSocket في console

**التشخيص:**
```javascript
// في Developer Tools -> Console
const ws = new WebSocket('ws://localhost:8002/ws/chat/1');
ws.onopen = () => console.log('Connected');
ws.onerror = (error) => console.error('WebSocket error:', error);
```

**الحلول:**
1. **تحقق من URL:**
   ```typescript
   // في chatWebSocketService.ts
   const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
   const wsUrl = `${protocol}//${window.location.hostname}:8002/ws/chat/${userId}`;
   ```

2. **تحقق من المصادقة:**
   ```python
   # في main.py WebSocket endpoint
   user = get_user_by_id(db, user_id)
   if not user or not user.is_active:
       await websocket.close(code=4001, reason="مستخدم غير صحيح")
   ```

### 2. مشاكل قاعدة البيانات

#### المشكلة: جداول المحادثة غير موجودة
**الأعراض:**
- `sqlite3.OperationalError: no such table: chat_messages`
- `no such column: users.is_online`

**التشخيص:**
```bash
cd backend
sqlite3 smartpos.db ".tables"
sqlite3 smartpos.db "PRAGMA table_info(users);"
```

**الحل:**
```bash
# تشغيل migration
python migrations/add_chat_system.py
```

#### المشكلة: بيانات تالفة في قاعدة البيانات
**الأعراض:**
- أخطاء في استعلامات قاعدة البيانات
- بيانات مفقودة أو غير صحيحة

**التشخيص:**
```sql
-- فحص سلامة قاعدة البيانات
PRAGMA integrity_check;

-- فحص الجداول
SELECT name FROM sqlite_master WHERE type='table';

-- فحص البيانات
SELECT COUNT(*) FROM chat_messages;
SELECT COUNT(*) FROM user_online_status;
```

**الحل:**
```bash
# نسخ احتياطي
cp smartpos.db smartpos_backup.db

# إعادة تشغيل migration
python migrations/add_chat_system.py
```

### 3. مشاكل المصادقة

#### المشكلة: 401 Unauthorized
**الأعراض:**
- رسالة "Could not validate credentials"
- فشل في تسجيل الدخول

**التشخيص:**
```bash
# اختبار تسجيل الدخول
curl -X POST http://localhost:8002/api/auth/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin&password=admin123"
```

**الحلول:**
1. **تحقق من بيانات المستخدم:**
   ```sql
   SELECT id, username, is_active FROM users WHERE username = 'admin';
   ```

2. **تحقق من JWT token:**
   ```python
   # في utils/auth.py
   try:
       payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
       username: str = payload.get("sub")
   except JWTError:
       raise credentials_exception
   ```

3. **إعادة تعيين كلمة المرور:**
   ```python
   # سكريبت لإعادة تعيين كلمة المرور
   from utils.auth import get_password_hash
   import sqlite3
   
   conn = sqlite3.connect('smartpos.db')
   cursor = conn.cursor()
   new_password = get_password_hash("admin123")
   cursor.execute("UPDATE users SET password_hash = ? WHERE username = ?", 
                  (new_password, "admin"))
   conn.commit()
   ```

### 4. مشاكل Frontend

#### المشكلة: أخطاء TypeScript
**الأعراض:**
- `Property 'user' does not exist on type`
- `Module not found` errors

**الحلول:**
1. **تحقق من الاستيرادات:**
   ```tsx
   // استخدم useAuthStore بدلاً من useAuth
   import { useAuthStore } from '../stores/authStore';
   const { user } = useAuthStore();
   ```

2. **تثبيت التبعيات:**
   ```bash
   npm install date-fns
   npm install @types/node  # إذا لزم الأمر
   ```

3. **تحقق من مسارات الملفات:**
   ```tsx
   // تأكد من الامتدادات الصحيحة
   import ConversationsList from './ConversationsList.tsx';
   ```

#### المشكلة: الرسائل لا تظهر
**الأعراض:**
- نافذة المحادثة فارغة
- لا توجد محادثات في القائمة

**التشخيص:**
```javascript
// في Developer Tools -> Network
// تحقق من:
// 1. طلبات API للمحادثات
// 2. استجابات الخادم
// 3. أخطاء JavaScript في Console
```

**الحلول:**
1. **تحقق من حالة المستخدم:**
   ```tsx
   console.log('Current user:', user);
   console.log('User ID:', user?.id);
   ```

2. **تحقق من API calls:**
   ```tsx
   // في useChat hook
   useEffect(() => {
       console.log('Loading conversations...');
       loadConversations().then(conversations => {
           console.log('Loaded conversations:', conversations);
       });
   }, []);
   ```

### 5. مشاكل الأداء

#### المشكلة: بطء في تحميل الرسائل
**الأعراض:**
- تأخير في ظهور الرسائل
- استهلاك عالي للذاكرة

**التشخيص:**
```sql
-- فحص عدد الرسائل
SELECT COUNT(*) FROM chat_messages;

-- فحص الفهارس
.schema chat_messages

-- فحص الاستعلامات البطيئة
EXPLAIN QUERY PLAN 
SELECT * FROM chat_messages 
WHERE (sender_id = 1 AND receiver_id = 2) 
   OR (sender_id = 2 AND receiver_id = 1) 
ORDER BY created_at DESC LIMIT 50;
```

**الحلول:**
1. **تحسين الاستعلامات:**
   ```sql
   -- إضافة فهارس
   CREATE INDEX IF NOT EXISTS idx_chat_messages_conversation 
   ON chat_messages(sender_id, receiver_id, created_at);
   ```

2. **تقليل حجم البيانات:**
   ```python
   # في chat_message_service.py
   # استخدام pagination
   messages_query = base_query.offset(offset).limit(limit)
   ```

3. **تنظيف البيانات القديمة:**
   ```sql
   -- حذف الرسائل الأقدم من 6 أشهر
   DELETE FROM chat_messages 
   WHERE created_at < datetime('now', '-6 months');
   ```

## 🔍 أدوات التشخيص

### 1. اختبار النظام
```bash
cd backend
python test_chat_system.py
```

### 2. فحص السجلات
```bash
# سجلات الخادم
tail -f backend/logs/app.log

# سجلات قاعدة البيانات
sqlite3 smartpos.db ".log on"
```

### 3. مراقبة WebSocket
```javascript
// في Developer Tools -> Network -> WS
// راقب:
// - حالة الاتصال
// - رسائل heartbeat
// - رسائل البيانات
// - أخطاء الاتصال
```

### 4. فحص الأداء
```bash
# استخدام الذاكرة
ps aux | grep "python main.py"

# الاتصالات النشطة
netstat -an | grep :8002 | wc -l

# استخدام المعالج
top -p $(pgrep -f "python main.py")
```

## 🛠️ أدوات الإصلاح

### 1. إعادة تعيين النظام
```bash
# إيقاف الخادم
pkill -f "python main.py"

# إعادة تشغيل migration
cd backend
python migrations/add_chat_system.py

# إعادة تشغيل الخادم
python main.py
```

### 2. تنظيف قاعدة البيانات
```sql
-- تنظيف الاتصالات القديمة
DELETE FROM user_online_status 
WHERE updated_at < datetime('now', '-1 hour');

-- إعادة تعيين حالة الاتصال
UPDATE users SET is_online = 0;
UPDATE user_online_status SET is_online = 0;
```

### 3. إعادة بناء الفهارس
```sql
-- إعادة بناء الفهارس
REINDEX;

-- تحليل الجداول
ANALYZE;

-- تنظيف قاعدة البيانات
VACUUM;
```

## 📊 مراقبة النظام

### 1. إحصائيات الاستخدام
```sql
-- عدد المستخدمين النشطين
SELECT COUNT(*) FROM user_online_status WHERE is_online = 1;

-- عدد الرسائل اليومية
SELECT DATE(created_at) as date, COUNT(*) as messages
FROM chat_messages 
WHERE created_at >= datetime('now', '-7 days')
GROUP BY DATE(created_at);

-- أكثر المستخدمين نشاطاً
SELECT sender_id, COUNT(*) as message_count
FROM chat_messages 
WHERE created_at >= datetime('now', '-1 day')
GROUP BY sender_id 
ORDER BY message_count DESC 
LIMIT 10;
```

### 2. تنبيهات الأداء
```python
# في chat_websocket_manager.py
def get_performance_stats(self):
    return {
        'active_connections': len(self.active_connections),
        'memory_usage': psutil.Process().memory_info().rss / 1024 / 1024,  # MB
        'uptime': time.time() - self.start_time
    }
```

## 📞 الحصول على المساعدة

### 1. معلومات مفيدة للدعم
عند طلب المساعدة، قدم المعلومات التالية:

```bash
# معلومات النظام
python --version
sqlite3 --version
uname -a  # Linux/Mac
systeminfo  # Windows

# حالة الخادم
curl -I http://localhost:8002/api/system/health

# سجلات الأخطاء
tail -n 50 backend/logs/app.log

# حالة قاعدة البيانات
sqlite3 smartpos.db "SELECT COUNT(*) FROM chat_messages;"
```

### 2. خطوات إعادة الإنتاج
1. وصف المشكلة بالتفصيل
2. خطوات إعادة إنتاج المشكلة
3. النتيجة المتوقعة مقابل النتيجة الفعلية
4. رسائل الخطأ الكاملة
5. لقطات شاشة إذا أمكن

### 3. الموارد المفيدة
- **التوثيق الرئيسي**: `docs/features/REAL_TIME_CHAT_SYSTEM.md`
- **دليل التثبيت**: `docs/guides/CHAT_SYSTEM_INSTALLATION_GUIDE.md`
- **مرجع API**: `docs/api/CHAT_API_REFERENCE.md`
- **اختبارات النظام**: `backend/test_chat_system.py`

## 🎨 مشاكل التصميم والواجهة الجديدة

### 6. مشاكل التبويبات والتنظيم

#### المشكلة: تبويب "عرض كل المستخدمين" لا يعمل
**الأعراض:**
- خطأ "Cannot fetch all users"
- التبويب فارغ أو لا يحمل البيانات
- رسالة خطأ في console

**التشخيص:**
```bash
# اختبار API endpoint الجديد
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8002/api/chat/users/all
```

**الحلول:**
1. **التحقق من وجود endpoint:**
   ```python
   # في backend/routers/chat.py
   @router.get("/users/all", response_model=List[UserOnlineStatusResponse])
   async def get_all_users(...)
   ```

2. **التحقق من خدمة API:**
   ```typescript
   // في chatApiService.ts
   async getAllUsers(): Promise<UserOnlineStatus[]> {
     const response = await api.get(`${this.baseUrl}/users/all`);
     return response.data;
   }
   ```

#### المشكلة: تبويب البحث المكرر لا يزال موجود
**الأعراض:**
- ظهور تبويبين للبحث
- وظائف مكررة في الواجهة

**الحلول:**
1. **إزالة التبويب المكرر:**
   ```tsx
   // في ChatWindow.tsx - إزالة هذا
   // {sidebarView === 'search' && <UserSearch />}
   ```

2. **تحديث حالة التبويبات:**
   ```tsx
   const [sidebarView, setSidebarView] = useState<'conversations' | 'online' | 'all'>('conversations');
   ```

### 7. مشاكل بطاقات المستخدمين

#### المشكلة: بطاقات المستخدمين كبيرة جداً
**الأعراض:**
- ارتفاع مفرط للبطاقات
- نصوص وأيقونات كبيرة
- مساحة مهدرة

**الحلول:**
1. **تحديث CSS للبطاقات:**
   ```css
   .user-item {
     padding: 8px 16px; /* بدلاً من 15px 20px */
   }

   .user-avatar {
     width: 36px;  /* بدلاً من 48px */
     height: 36px;
   }

   .user-name {
     font-size: 13px; /* بدلاً من 16px */
   }
   ```

#### المشكلة: تخطيط اسم المستخدم غير صحيح
**الأعراض:**
- اسم المستخدم (@username) لا يظهر في اليمين
- الاسم الكامل لا يظهر بالكامل

**الحلول:**
1. **استخدام التخطيط الجديد:**
   ```tsx
   <div className="user-name-container">
     <h4 className="user-name">{user.full_name}</h4>
     <span className="user-username">@{user.username}</span>
   </div>
   ```

2. **تحديث CSS:**
   ```css
   .user-name-container {
     display: flex;
     justify-content: space-between;
     align-items: center;
   }

   .user-name {
     flex: 1;
     margin-left: 8px;
     text-overflow: ellipsis;
     overflow: hidden;
   }

   .user-username {
     flex-shrink: 0;
     white-space: nowrap;
   }
   ```

### 8. مشاكل شريط التمرير

#### المشكلة: شريط التمرير لا يختفي تلقائياً
**الأعراض:**
- شريط التمرير يظهر دائماً
- لا يستخدم تصميم المشروع الموحد

**الحلول:**
1. **إضافة class الصحيح:**
   ```tsx
   <div className="users-container custom-scrollbar-auto">
   ```

2. **التحقق من استيراد CSS:**
   ```tsx
   import '../../styles/scrollbar.css';
   ```

3. **التحقق من وجود ملف scrollbar.css:**
   ```bash
   ls frontend/src/styles/scrollbar.css
   ```

### 9. مشاكل الوضع المظلم

#### المشكلة: الألوان لا تتغير في الوضع المظلم
**الأعراض:**
- النصوص غير مرئية في الوضع المظلم
- الخلفيات لا تتغير
- تباين ضعيف

**التشخيص:**
```bash
# فحص متغيرات CSS
grep -r "var(--color" frontend/src/components/Chat/
```

**الحلول:**
1. **التحقق من متغيرات CSS:**
   ```css
   /* في index.css */
   .dark {
     --color-bg-primary: #111827;
     --color-text-primary: #f9fafb;
     /* ... باقي المتغيرات */
   }
   ```

2. **التحقق من استخدام المتغيرات:**
   ```css
   /* استخدام صحيح */
   background: var(--color-card-bg);
   color: var(--color-text-primary);

   /* استخدام خاطئ */
   background: #ffffff; /* لون ثابت */
   ```

### 10. مشاكل الأيقونات المحدثة

#### المشكلة: الأيقونات لا تظهر أو تظهر كإيموجي
**الأعراض:**
- مربعات فارغة بدلاً من الأيقونات
- إيموجي بدلاً من أيقونات React Icons
- أيقونات مكسورة أو مفقودة

**الحلول:**
1. **التحقق من الاستيراد الجديد:**
   ```typescript
   import {
     FaComments,      // للمحادثات
     FaUserFriends,   // للمستخدمين المتاحين
     FaUsers,         // لجميع المستخدمين
     FaSearch,        // للبحث
     FaCircle,        // لحالة الاتصال
     FaClock          // لآخر ظهور
   } from 'react-icons/fa';
   ```

2. **التحقق من الاستخدام الصحيح:**
   ```tsx
   // صحيح - أيقونات React Icons
   <FaComments className="tab-icon" />
   <FaUsers className="tab-icon" />

   // خاطئ - إيموجي
   💬 📱
   ```

3. **إزالة الاستيرادات غير المستخدمة:**
   ```typescript
   // إزالة هذه إذا لم تعد مستخدمة
   // import { FaClock } from 'react-icons/fa';
   ```

### 9. مشاكل التدرجات اللونية

#### المشكلة: ظهور تدرجات لونية غير مرغوبة
**الأعراض:**
- ألوان متدرجة في الخلفيات
- عدم توافق مع تصميم النظام

**الحلول:**
1. **إزالة التدرجات:**
   ```css
   /* خاطئ */
   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

   /* صحيح */
   background: #0284c7;
   ```

2. **استخدام ألوان موحدة:**
   ```css
   background: var(--color-card-bg);
   ```

### 12. مشاكل التوافق مع التصميم الجديد

#### المشكلة: التصميم لا يتماشى مع باقي التطبيق
**الأعراض:**
- ألوان غير متوافقة
- أحجام نصوص مختلفة
- أزرار بتصميم مختلف

**الحلول:**
1. **استخدام نظام الألوان الموحد:**
   ```css
   background: var(--color-card-bg);
   color: var(--color-text-primary);
   border: 1px solid var(--color-border);
   ```

2. **استخدام أحجام النصوص المعيارية:**
   ```css
   .user-name { font-size: 13px; }
   .user-username { font-size: 11px; }
   ```

3. **إزالة التدرجات اللونية:**
   ```css
   /* خاطئ */
   background: linear-gradient(...);

   /* صحيح */
   background: var(--color-primary);
   ```

#### المشكلة: عناصر مكررة أو غير ضرورية
**الأعراض:**
- أزرار "بدء محادثة" مكررة
- نصوص "متصل الآن" مكررة
- تبويبات بحث متعددة

**الحلول:**
1. **إزالة العناصر المكررة:**
   ```tsx
   // إزالة هذه العناصر
   // <button>بدء محادثة</button>
   // <span>متصل الآن</span>
   ```

2. **تبسيط بطاقات المستخدمين:**
   ```tsx
   // الاحتفاظ بالمعلومات الأساسية فقط
   <div className="user-info">
     <div className="user-name-container">
       <h4 className="user-name">{user.full_name}</h4>
       <span className="user-username">@{user.username}</span>
     </div>
   </div>
   ```

### 13. مشاكل الأداء بعد التحديث

#### المشكلة: بطء في تحميل قائمة جميع المستخدمين
**الأعراض:**
- تأخير في تحميل التبويب الجديد
- استهلاك ذاكرة عالي

**الحلول:**
1. **تحسين استعلام قاعدة البيانات:**
   ```python
   # إضافة فهرس للمستخدمين النشطين
   CREATE INDEX IF NOT EXISTS idx_users_active
   ON users(is_active, created_at);
   ```

2. **استخدام pagination:**
   ```python
   # تحديد عدد المستخدمين المعروضين
   users_query = users_query.limit(100)
   ```

---

## 🔧 إصلاحات حرجة حديثة (ديسمبر 2024)

### 14. مشكلة عدم جلب المحادثات السابقة (مُصلحة)

#### المشكلة: قائمة المحادثات فارغة دائماً
**الأعراض:**
- API endpoint `/api/chat/conversations` يعيد قائمة فارغة `[]`
- خطأ في سجلات الخادم: `Function.__init__() got an unexpected keyword argument 'else_'`
- المستخدمون لا يرون محادثاتهم السابقة
- تبويب "المحادثات" فارغ حتى مع وجود رسائل في قاعدة البيانات

**التشخيص:**
```bash
# فحص وجود الرسائل في قاعدة البيانات
cd backend
sqlite3 smartpos.db "SELECT COUNT(*) FROM chat_messages;"

# اختبار API endpoint
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8002/api/chat/conversations

# فحص سجلات الخادم للأخطاء
tail -f logs/app.log | grep "chat_message_service"
```

**السبب الجذري:**
```python
# خطأ في backend/services/chat_message_service.py
func.case(  # خطأ: استخدام func.case
    (ChatMessage.sender_id == user_id, ChatMessage.receiver_id),
    else_=ChatMessage.sender_id
)
```

**الحل المطبق:**
```python
# 1. إضافة استيراد case
from sqlalchemy import select, update, and_, or_, desc, func, case

# 2. تصحيح الاستعلام
case(  # صحيح: استخدام case مباشرة
    (ChatMessage.sender_id == user_id, ChatMessage.receiver_id),
    else_=ChatMessage.sender_id
)
```

**التحقق من الإصلاح:**
```bash
# اختبار API بعد الإصلاح
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8002/api/chat/conversations

# النتيجة المتوقعة: قائمة بالمحادثات
[
  {
    "user_id": 1,
    "username": "admin",
    "full_name": "مدير النظام",
    "is_online": false,
    "last_message": {...},
    "unread_count": 0
  }
]
```

### 15. مشكلة بيانات المستخدم في هيدر المحادثة (مُصلحة)

#### المشكلة: بيانات المستخدم غير مكتملة في الهيدر
**الأعراض:**
- ظهور أيقونة "U" فقط بدلاً من اسم المستخدم
- نص "آخر ظهور غير معروف" حتى للمستخدمين المتصلين
- ظهور "@" بدون اسم مستخدم
- عدم ظهور بيانات المستخدم عند اختيار مستخدم جديد من قائمة المستخدمين المتاحين

**التشخيص:**
```javascript
// في Developer Tools -> Console
console.log('Current conversation:', currentConversation);
console.log('Current user data:', getCurrentConversationUser());
console.log('Conversations list:', conversations);
```

**السبب الجذري:**
- `getCurrentConversationUser()` يبحث فقط في `conversations` الموجودة
- عند اختيار مستخدم جديد بدون محادثة سابقة، لا توجد بيانات
- عدم حفظ بيانات المستخدم المختار من قوائم المستخدمين

**الحل المطبق:**

1. **إضافة state لحفظ بيانات المستخدم:**
```tsx
// في ChatWindow.tsx
const [selectedUserData, setSelectedUserData] = useState<any>(null);
```

2. **تحديث handleUserSelect:**
```tsx
const handleUserSelect = (userId: number, userData?: any) => {
  setCurrentConversation(userId);
  setSidebarView('conversations');

  // حفظ بيانات المستخدم إذا تم تمريرها
  if (userData) {
    setSelectedUserData(userData);
  }
};
```

3. **تحسين getCurrentConversationUser:**
```tsx
const getCurrentConversationUser = () => {
  if (!currentConversation) return null;

  // البحث في المحادثات أولاً
  const conversationUser = conversations.find(conv => conv.user_id === currentConversation);
  if (conversationUser) return conversationUser;

  // استخدام البيانات المحفوظة كبديل
  if (selectedUserData && selectedUserData.user_id === currentConversation) {
    return {
      user_id: selectedUserData.user_id,
      username: selectedUserData.username,
      full_name: selectedUserData.full_name,
      is_online: selectedUserData.is_online,
      last_seen: selectedUserData.last_seen,
      unread_count: 0
    };
  }

  return null;
};
```

4. **تحديث مكونات قوائم المستخدمين:**
```tsx
// في OnlineUsersList.tsx و AllUsersList.tsx
interface OnlineUsersListProps {
  onUserSelect: (userId: number, userData?: UserOnlineStatus) => void;
}

const handleUserClick = (user: UserOnlineStatus) => {
  onUserSelect(user.user_id, user); // تمرير بيانات المستخدم
};
```

**التحقق من الإصلاح:**
1. ✅ اختيار مستخدم من "المستخدمون المتاحون"
2. ✅ التأكد من ظهور اسم المستخدم في الهيدر
3. ✅ التأكد من ظهور حالة الاتصال الصحيحة
4. ✅ اختبار مع مستخدمين جدد بدون محادثات سابقة

### 16. مشاكل SQLAlchemy في خدمة المحادثات

#### المشكلة: أخطاء في استعلامات قاعدة البيانات
**الأعراض:**
- `Function.__init__() got an unexpected keyword argument 'else_'`
- `AttributeError: module 'sqlalchemy.sql.functions' has no attribute 'case'`
- فشل في تحميل قائمة المحادثات

**الحلول الشائعة:**
```python
# خطأ شائع
from sqlalchemy import func
func.case(...)  # خطأ

# الحل الصحيح
from sqlalchemy import case
case(...)  # صحيح

# أو
from sqlalchemy.sql import case
case(...)  # صحيح أيضاً
```

**أفضل الممارسات:**
```python
# استيراد شامل وصحيح
from sqlalchemy import (
    select, update, delete, insert,
    and_, or_, not_, desc, asc,
    func, case, cast, text,
    Integer, String, DateTime, Boolean
)
```

### 17. مشاكل إدارة الحالة في React

#### المشكلة: فقدان بيانات المستخدم عند التنقل
**الأعراض:**
- بيانات المستخدم تختفي عند تغيير التبويبات
- عدم تحديث الهيدر عند اختيار مستخدم جديد
- حالة غير متسقة بين المكونات

**الحلول:**
```tsx
// 1. استخدام useEffect لمراقبة التغييرات
useEffect(() => {
  if (currentConversation && !getCurrentConversationUser()) {
    // إعادة تحميل بيانات المستخدم إذا لزم الأمر
    loadUserData(currentConversation);
  }
}, [currentConversation, conversations]);

// 2. تنظيف الحالة عند الحاجة
useEffect(() => {
  return () => {
    setSelectedUserData(null);
  };
}, []);

// 3. التحقق من صحة البيانات
const getCurrentConversationUser = useCallback(() => {
  // منطق الحصول على بيانات المستخدم
}, [currentConversation, conversations, selectedUserData]);
```

## 🔧 إصلاحات حديثة (يناير 2025)

### 18. مشكلة عدم اختفاء الرسائل المحذوفة فوراً (مُصلحة)

#### المشكلة: الرسائل المحذوفة لا تختفي فوراً عند المستخدم الآخر
**الأعراض:**
- عند حذف رسالة من محادثة، تختفي عند المرسل فقط
- المستخدم الآخر لا يرى حذف الرسالة إلا بعد إعادة تحميل المحادثة
- سلوك مختلف عن تعديل الرسائل الذي يعمل فورياً

**السبب الجذري:**
- عدم وجود معالج للرسائل المحذوفة في `useChat.ts`
- WebSocket يرسل إشعار `message_deleted` لكن لا يتم معالجته في الواجهة

**الحل المطبق:**
```typescript
// في frontend/src/hooks/useChat.ts
const handleMessageDeleted = (data: any) => {
  setState(prev => {
    const updatedMessages = { ...prev.messages };

    // البحث في جميع المحادثات عن الرسالة المحذوفة وإزالتها
    Object.keys(updatedMessages).forEach(conversationId => {
      const conversationMessages = updatedMessages[parseInt(conversationId)];
      if (conversationMessages) {
        updatedMessages[parseInt(conversationId)] = conversationMessages.filter(
          message => message.id !== data.message_id
        );
      }
    });

    return {
      ...prev,
      messages: updatedMessages
    };
  });

  // تحديث المحادثات لتحديث آخر رسالة
  loadConversations();
};

// إضافة المعالج للمستمعين
chatWebSocketService.on('message_deleted', handleMessageDeleted);
chatWebSocketService.off('message_deleted', handleMessageDeleted); // في cleanup
```

**النتيجة**: ✅ الآن تختفي الرسائل المحذوفة فوراً عند جميع المستخدمين

### 19. مشكلة عدم ظهور المحادثات الجديدة فوراً (مُصلحة)

#### المشكلة: المحادثات الجديدة لا تظهر في القائمة فوراً
**الأعراض:**
- عند اختيار مستخدم جديد من قائمة المستخدمين وإرسال رسالة
- المحادثة لا تظهر في قائمة "المحادثات" فوراً
- يتطلب إغلاق النافذة وإعادة فتحها لرؤية المحادثة

**السبب الجذري:**
- عدم تحديث قائمة المحادثات فوراً بعد إرسال رسالة لمستخدم جديد
- الاعتماد على التحديث التلقائي فقط عند استقبال رسائل جديدة

**الحل المطبق:**
```typescript
// في frontend/src/hooks/useChat.ts
const sendMessage = useCallback(async (receiverId: number, content: string) => {
  try {
    const message = await chatApiService.sendMessage({
      receiver_id: receiverId,
      content,
      message_type: 'text'
    });

    // إضافة الرسالة محلياً
    setState(prev => ({
      ...prev,
      messages: {
        ...prev.messages,
        [receiverId]: [...(prev.messages[receiverId] || []), message]
      }
    }));

    // تحديث قائمة المحادثات فوراً لضمان ظهور المحادثة الجديدة
    setTimeout(() => {
      loadConversations();
    }, 100);

    return message;
  } catch (err) {
    setError('فشل في إرسال الرسالة');
    console.error('خطأ في إرسال الرسالة:', err);
    throw err;
  }
}, [loadConversations]);

// تحسين معالج الرسائل الجديدة
const handleNewMessage = (data: any) => {
  const message: ChatMessage = data.message;
  setState(prev => ({
    ...prev,
    messages: {
      ...prev.messages,
      [message.sender_id]: [...(prev.messages[message.sender_id] || []), message]
    }
  }));

  // تحديث المحادثات فوراً لضمان ظهور المحادثة الجديدة
  setTimeout(() => {
    loadConversations();
  }, 50);
};
```

**النتيجة**: ✅ الآن تظهر المحادثات الجديدة فوراً في قائمة المحادثات

### 20. تحسينات إضافية في ChatWindow

#### التحسينات المطبقة:
```typescript
// في frontend/src/components/Chat/ChatWindow.tsx
const handleSendMessage = async (content: string) => {
  if (currentConversation && content.trim()) {
    try {
      await sendMessage(currentConversation, content.trim());

      // تحديث إضافي للمحادثات لضمان ظهور المحادثة الجديدة فوراً
      setTimeout(() => {
        loadConversations();
      }, 200);
    } catch (error) {
      console.error('فشل في إرسال الرسالة:', error);
    }
  }
};

const handleUserSelect = (userId: number, userData?: any) => {
  setCurrentConversation(userId);
  setSidebarView('conversations');

  // حفظ بيانات المستخدم إذا تم تمريرها
  if (userData) {
    setSelectedUserData(userData);
  }

  // تحديث المحادثات لضمان ظهور المحادثة الجديدة إذا لم تكن موجودة
  setTimeout(() => {
    loadConversations();
  }, 100);
};
```

### اختبار الإصلاحات الجديدة

#### اختبار حذف الرسائل:
```bash
# 1. فتح محادثة بين مستخدمين
# 2. إرسال رسالة من المستخدم الأول
# 3. حذف الرسالة من المستخدم الأول
# 4. التحقق من اختفاء الرسالة فوراً عند المستخدم الثاني
```

#### اختبار المحادثات الجديدة:
```bash
# 1. فتح نافذة المحادثة
# 2. اختيار مستخدم جديد من "المستخدمون المتاحون"
# 3. إرسال رسالة للمستخدم الجديد
# 4. التحقق من ظهور المحادثة فوراً في قائمة "المحادثات"
```

## 🆕 مشاكل الميزات الجديدة (يناير 2025)

### 🔗 مشاكل معاينة الروابط

#### المشكلة: معاينة الروابط لا تظهر
**الأعراض:**
- الروابط تظهر كنص عادي فقط
- لا توجد معاينة تحت الرسائل
- رسائل خطأ في console

**التشخيص:**
```bash
# فحص API معاينة الروابط
curl "http://localhost:8002/api/link-preview?url=https://www.google.com"

# فحص سجلات الخادم
tail -f backend/logs/app.log | grep "link-preview"

# فحص المكتبات المطلوبة
python -c "import httpx, bs4; print('Libraries OK')"
```

**الحلول:**
1. **تثبيت المكتبات المفقودة:**
   ```bash
   cd backend
   source venv/bin/activate
   pip install httpx beautifulsoup4
   ```

2. **فحص إعدادات CORS للـ API الجديد:**
   ```python
   # التأكد من إضافة router في main.py
   from routers.link_preview import router as link_preview_router
   app.include_router(link_preview_router)
   ```

3. **فحص اتصال الإنترنت:**
   ```bash
   curl -I https://www.google.com
   ```

#### المشكلة: معاينة الروابط بطيئة
**الأعراض:**
- تأخير في ظهور المعاينة
- timeout errors
- معاينة جزئية فقط

**الحلول:**
1. **زيادة timeout:**
   ```python
   # في link_preview.py
   self.timeout = 15.0  # زيادة من 10 إلى 15 ثانية
   ```

2. **تحسين cache:**
   ```python
   # فحص cache service
   from services.cache_service import cache_service
   print(cache_service.get_stats())
   ```

### ⌨️ مشاكل مؤشر الكتابة

#### المشكلة: مؤشر الكتابة لا يظهر
**الأعراض:**
- لا يظهر "يكتب..." عند كتابة المستخدم الآخر
- مؤشر يظهر ولا يختفي
- مؤشر يظهر للمستخدم نفسه

**التشخيص:**
```bash
# فحص WebSocket messages
grep "typing\|stop_typing" backend/logs/app.log

# فحص JavaScript console
# ابحث عن أخطاء في onStartTyping/onStopTyping
```

**الحلول:**
1. **فحص WebSocket connection:**
   ```javascript
   // في browser console
   console.log('WebSocket state:', window.chatWebSocket?.readyState);
   ```

2. **إعادة تشغيل WebSocket:**
   ```bash
   # إعادة تشغيل الخادم
   cd backend
   ./venv/bin/python main.py
   ```

3. **فحص timeout settings:**
   ```typescript
   // في MessageInput.tsx
   // التأكد من timeout 2000ms للكتابة
   ```

#### المشكلة: مؤشر الكتابة يظهر بشكل متكرر
**الأعراض:**
- إشعارات كتابة متعددة
- أداء بطيء
- استهلاك عالي للموارد

**الحلول:**
1. **فحص debounce logic:**
   ```typescript
   // التأكد من isTypingRef في MessageInput.tsx
   if (!isTypingRef.current) {
     isTypingRef.current = true;
     onStartTyping?.();
   }
   ```

2. **فحص cleanup:**
   ```typescript
   // التأكد من clearTimeout في useEffect
   return () => {
     if (typingTimeoutRef.current) {
       clearTimeout(typingTimeoutRef.current);
     }
   };
   ```

### 😊 مشاكل منتقي الإيموجي

#### المشكلة: نافذة الإيموجي تظهر في مكان خاطئ
**الأعراض:**
- النافذة تظهر خارج الشاشة
- النافذة تظهر بعيداً عن الأيقونة
- النافذة لا تتكيف مع حجم الشاشة

**التشخيص:**
```javascript
// في browser console
const button = document.querySelector('.emoji-btn');
const rect = button.getBoundingClientRect();
console.log('Button position:', rect);
```

**الحلول:**
1. **فحص position calculation:**
   ```typescript
   // في EmojiPicker.tsx
   let left = rect.left + (rect.width / 2) - (pickerWidth / 2);
   ```

2. **فحص CSS positioning:**
   ```css
   .emoji-picker {
     position: fixed; /* يجب أن يكون fixed وليس absolute */
   }
   ```

3. **إعادة تحميل الصفحة:**
   ```bash
   # Hard refresh
   Ctrl + F5 (Windows/Linux)
   Cmd + Shift + R (Mac)
   ```

#### المشكلة: الإيموجي لا تظهر بشكل صحيح
**الأعراض:**
- مربعات فارغة بدلاً من الإيموجي
- إيموجي مقطوعة أو مشوهة
- تصنيفات فارغة

**الحلول:**
1. **فحص font support:**
   ```css
   /* التأكد من دعم emoji fonts */
   font-family: 'Segoe UI Emoji', 'Apple Color Emoji', 'Noto Color Emoji';
   ```

2. **تحديث المتصفح:**
   ```bash
   # تأكد من استخدام متصفح حديث يدعم Unicode 15.0+
   ```

### 🔧 أدوات التشخيص للميزات الجديدة

#### فحص شامل للميزات الجديدة:
```bash
#!/bin/bash
echo "=== فحص الميزات الجديدة ==="

# 1. فحص المكتبات
echo "1. فحص المكتبات..."
python -c "import httpx; print('✅ httpx:', httpx.__version__)" 2>/dev/null || echo "❌ httpx غير مثبت"
python -c "import bs4; print('✅ beautifulsoup4:', bs4.__version__)" 2>/dev/null || echo "❌ beautifulsoup4 غير مثبت"

# 2. فحص API
echo "2. فحص API معاينة الروابط..."
curl -s "http://localhost:8002/api/link-preview?url=https://www.google.com" | jq '.success' 2>/dev/null || echo "❌ API لا يعمل"

# 3. فحص ملفات Frontend
echo "3. فحص ملفات Frontend..."
[ -f "frontend/src/components/Chat/LinkPreview.tsx" ] && echo "✅ LinkPreview.tsx" || echo "❌ LinkPreview.tsx مفقود"
[ -f "frontend/src/components/Chat/MessageTextWithLinks.tsx" ] && echo "✅ MessageTextWithLinks.tsx" || echo "❌ MessageTextWithLinks.tsx مفقود"
[ -f "frontend/src/utils/linkUtils.ts" ] && echo "✅ linkUtils.ts" || echo "❌ linkUtils.ts مفقود"

echo "=== انتهى الفحص ==="
```

#### اختبار الميزات الجديدة:
```bash
# اختبار معاينة الروابط
echo "اختبار معاينة الروابط:"
echo "1. أرسل رسالة تحتوي على: https://www.google.com"
echo "2. يجب أن تظهر معاينة تحت الرسالة"
echo "3. انقر على المعاينة للتأكد من فتح الرابط"

# اختبار مؤشر الكتابة
echo "اختبار مؤشر الكتابة:"
echo "1. افتح محادثة مع مستخدم آخر"
echo "2. ابدأ بكتابة رسالة"
echo "3. يجب أن يظهر 'يكتب...' للمستخدم الآخر"
echo "4. توقف عن الكتابة - يجب أن يختفي المؤشر خلال 3 ثوان"

# اختبار منتقي الإيموجي
echo "اختبار منتقي الإيموجي:"
echo "1. انقر على أيقونة الإيموجي"
echo "2. يجب أن تظهر النافذة في منتصف الأيقونة"
echo "3. جرب البحث عن إيموجي"
echo "4. انقر على إيموجي للإدراج"
```

**آخر تحديث**: يناير 2025
**الإصدار**: 2.2.0 (معاينة الروابط ومؤشر الكتابة المحسن)
