# 📦 دليل إدارة التبعيات - Dependency Management Guide

## 📋 نظرة عامة

هذا الدليل يوضح أفضل الممارسات لإدارة التبعيات في مشروع SmartPOS، مع التركيز على تجنب المشاكل الشائعة وضمان بيئة تطوير مستقرة.

## 🎯 المبادئ الأساسية

### 1. استخدام Package Managers دائماً
```bash
# ✅ الطريقة الصحيحة
npm install package-name
npm install --save-dev package-name

# ❌ الطريقة الخاطئة
# تعديل package.json يدوياً
```

### 2. فهم أنواع التبعيات
```json
{
  "dependencies": {
    // تبعيات الإنتاج - مطلوبة لتشغيل التطبيق
    "react": "^18.2.0",
    "axios": "^1.6.7"
  },
  "devDependencies": {
    // تبعيات التطوير - مطلوبة للبناء والتطوير فقط
    "typescript": "^5.2.2",
    "vite": "^5.1.0"
  }
}
```

## 🔧 أوامر التثبيت الأساسية

### تثبيت جميع التبعيات
```bash
# تثبيت عادي (قد لا يثبت devDependencies في بعض البيئات)
npm install

# تثبيت مع ضمان devDependencies (الطريقة الآمنة)
npm install --include=dev

# تثبيت للإنتاج فقط
npm install --production
```

### تثبيت تبعيات جديدة
```bash
# تبعية إنتاج
npm install package-name

# تبعية تطوير
npm install --save-dev package-name

# تثبيت إصدار محدد
npm install package-name@1.2.3

# تثبيت عالمي
npm install -g package-name
```

## 🚨 حل المشاكل الشائعة

### المشكلة 1: devDependencies لا تُثبت
```bash
# الأعراض:
# - npm run build يفشل
# - TypeScript أو Vite غير موجودين
# - node_modules/.bin/ فارغ أو يحتوي على أدوات قليلة

# الحل:
rm -rf node_modules package-lock.json
npm cache clean --force
npm install --include=dev
```

### المشكلة 2: تضارب في التبعيات
```bash
# الأعراض:
# - أخطاء peer dependency
# - إصدارات متضاربة
# - أخطاء غريبة أثناء البناء

# الحل:
npm install --legacy-peer-deps
# أو
npm install --force
```

### المشكلة 3: cache npm تالف
```bash
# الأعراض:
# - تثبيت بطيء أو فاشل
# - ملفات مفقودة بعد التثبيت
# - أخطاء checksum

# الحل:
npm cache clean --force
npm cache verify
```

### المشكلة 4: package-lock.json تالف
```bash
# الأعراض:
# - تثبيت تبعيات خاطئة
# - إصدارات غير متوقعة
# - أخطاء integrity

# الحل:
rm package-lock.json
npm install
```

## 🔍 التشخيص والفحص

### فحص التبعيات المثبتة
```bash
# عرض جميع التبعيات
npm ls

# عرض التبعيات الرئيسية فقط
npm ls --depth=0

# عرض devDependencies فقط
npm ls --dev --depth=0

# فحص تبعية محددة
npm ls package-name
```

### فحص الأدوات المثبتة
```bash
# عرض الأدوات في .bin
ls node_modules/.bin/

# فحص أدوات محددة
ls node_modules/.bin/ | grep -E "(tsc|vite|eslint)"

# عد الأدوات المثبتة
ls node_modules/.bin/ | wc -l
```

### فحص إعدادات npm
```bash
# فحص الإعدادات المهمة
npm config get production
npm config get NODE_ENV
npm config get registry

# عرض جميع الإعدادات
npm config list
```

## 🛠️ إجراءات الصيانة

### تنظيف دوري
```bash
# تنظيف cache
npm cache clean --force

# إزالة node_modules القديمة
find . -name "node_modules" -type d -prune -exec rm -rf '{}' +

# تنظيف ملفات lock القديمة
find . -name "package-lock.json" -delete
```

### تحديث التبعيات
```bash
# فحص التحديثات المتاحة
npm outdated

# تحديث تبعية محددة
npm update package-name

# تحديث جميع التبعيات
npm update

# تحديث إلى أحدث إصدار (خطر!)
npm install package-name@latest
```

### مراجعة الأمان
```bash
# فحص الثغرات الأمنية
npm audit

# إصلاح الثغرات تلقائياً
npm audit fix

# إصلاح بالقوة (قد يكسر التوافق)
npm audit fix --force
```

## 📋 قائمة فحص للمشاكل

### عند مواجهة مشاكل البناء:
- [ ] تحقق من وجود devDependencies: `npm ls --dev --depth=0`
- [ ] تحقق من الأدوات في .bin: `ls node_modules/.bin/ | grep -E "(tsc|vite)"`
- [ ] نظف cache: `npm cache clean --force`
- [ ] أعد تثبيت التبعيات: `npm install --include=dev`
- [ ] اختبر البناء: `npm run build`

### عند إضافة تبعيات جديدة:
- [ ] حدد نوع التبعية (production vs development)
- [ ] استخدم الأمر المناسب (`--save` أو `--save-dev`)
- [ ] اختبر البناء بعد التثبيت
- [ ] تحقق من عدم وجود تضارب
- [ ] حدث التوثيق إذا لزم الأمر

### عند حذف تبعيات:
- [ ] استخدم `npm uninstall package-name`
- [ ] أزل الاستيرادات من الكود
- [ ] اختبر البناء والتطبيق
- [ ] نظف التبعيات غير المستخدمة: `npm prune`

## 🎯 أفضل الممارسات لـ SmartPOS

### 1. بيئة التطوير
```bash
# تثبيت أولي للمطورين الجدد
cd frontend
npm install --include=dev
npm run build  # اختبار فوري
npm run dev    # تشغيل التطوير
```

### 2. بيئة الإنتاج
```bash
# تثبيت للإنتاج
npm ci --production
npm run build
```

### 3. التحقق من الصحة
```bash
# سكريبت فحص شامل
#!/bin/bash
echo "🔍 فحص التبعيات..."
npm ls --depth=0 > /dev/null && echo "✅ dependencies صحيحة" || echo "❌ مشكلة في dependencies"
npm ls --dev --depth=0 > /dev/null && echo "✅ devDependencies صحيحة" || echo "❌ مشكلة في devDependencies"
ls node_modules/.bin/tsc > /dev/null && echo "✅ TypeScript موجود" || echo "❌ TypeScript مفقود"
ls node_modules/.bin/vite > /dev/null && echo "✅ Vite موجود" || echo "❌ Vite مفقود"
npm run build > /dev/null && echo "✅ البناء ناجح" || echo "❌ البناء فاشل"
```

## 🔗 مراجع مفيدة

### وثائق npm الرسمية
- [npm install](https://docs.npmjs.com/cli/v8/commands/npm-install)
- [package.json](https://docs.npmjs.com/cli/v8/configuring-npm/package-json)
- [npm cache](https://docs.npmjs.com/cli/v8/commands/npm-cache)

### أدوات مساعدة
- [npm-check-updates](https://www.npmjs.com/package/npm-check-updates) - تحديث التبعيات
- [depcheck](https://www.npmjs.com/package/depcheck) - فحص التبعيات غير المستخدمة
- [bundlephobia](https://bundlephobia.com/) - فحص حجم التبعيات

### ملفات المشروع ذات الصلة
- `frontend/package.json` - تكوين التبعيات
- `frontend/package-lock.json` - قفل الإصدارات
- `docs/updates/BUILD_ISSUES_RADICAL_FIX_UPDATE.md` - حل مشاكل البناء
- `SYSTEM_MEMORY.md` - قواعد إدارة التبعيات

---

**تم إعداد الدليل بواسطة**: Najib S Gadamsi  
**تاريخ الإنشاء**: 30 يونيو 2025  
**آخر تحديث**: 30 يونيو 2025
