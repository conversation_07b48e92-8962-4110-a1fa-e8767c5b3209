# فصل خدمات بيانات المبيعات - التوثيق الفني

## نظرة عامة

تم فصل طريقة جلب بيانات المبيعات في صفحة التقارير إلى خدمات منفصلة لضمان دقة البيانات وتجنب التعارض في العرض. هذا التحديث يوفر:

- **دقة عالية في التعامل مع التاريخ والوقت**
- **فصل كامل بين بيانات الفترة الحالية والسابقة**
- **تجنب تعارض البيانات**
- **كود أكثر تنظيماً وقابلية للصيانة**

## الملفات المُنشأة/المُعدلة

### الخلفية (Backend)

#### 1. خدمة الفترة الحالية
**الملف:** `backend/services/current_period_service.py`

```python
class CurrentPeriodService:
    """خدمة جلب بيانات الفترة الحالية"""
    
    def get_current_day_sales(self) -> List[Dict[str, Any]]
    def get_current_week_sales(self) -> List[Dict[str, Any]]
    def get_current_month_sales(self) -> List[Dict[str, Any]]
    def get_current_year_sales(self) -> List[Dict[str, Any]]
    def get_sales_by_period(self, period: str) -> List[Dict[str, Any]]
```

**المميزات:**
- جلب بيانات اليوم الحالي (24 ساعة)
- جلب بيانات الأسبوع الحالي (7 أيام)
- جلب بيانات الشهر الحالي (30 يوم)
- جلب بيانات السنة الحالية (12 شهر)
- التعامل الدقيق مع توقيت طرابلس
- معالجة شاملة للأخطاء

#### 2. خدمة الفترة السابقة
**الملف:** `backend/services/previous_period_service.py`

```python
class PreviousPeriodService:
    """خدمة جلب بيانات الفترة السابقة"""
    
    def get_previous_day_sales(self) -> List[Dict[str, Any]]
    def get_previous_week_sales(self) -> List[Dict[str, Any]]
    def get_previous_month_sales(self) -> List[Dict[str, Any]]
    def get_previous_year_sales(self) -> List[Dict[str, Any]]
    def get_sales_by_period(self, period: str) -> List[Dict[str, Any]]
    def get_previous_period_total(self, period: str) -> float
```

**المميزات:**
- جلب بيانات اليوم السابق (أمس)
- جلب بيانات الأسبوع السابق (7 أيام قبل الحالي)
- جلب بيانات الشهر السابق (30 يوم قبل الحالي)
- جلب بيانات السنة السابقة (12 شهر قبل الحالي)
- حساب إجمالي الفترة السابقة للمقارنة

#### 3. تحديث ملف التقارير
**الملف:** `backend/routers/dashboard.py`

تم تعديل endpoint `/sales-trends/{period}` لاستخدام الخدمات الجديدة:

```python
@router.get("/sales-trends/{period}")
async def get_sales_trends(period: str, previous: bool = False):
    if previous:
        service = PreviousPeriodService(db, current_user)
        sales_data = service.get_sales_by_period(period)
    else:
        service = CurrentPeriodService(db, current_user)
        sales_data = service.get_sales_by_period(period)
```

### الواجهة الأمامية (Frontend)

#### 1. خدمة الفترة الحالية
**الملف:** `frontend/src/services/currentPeriodService.ts`

```typescript
export class CurrentPeriodService {
    async getCurrentDaySales(): Promise<SalesTrendData[]>
    async getCurrentWeekSales(): Promise<SalesTrendData[]>
    async getCurrentMonthSales(): Promise<SalesTrendData[]>
    async getCurrentYearSales(): Promise<SalesTrendData[]>
    async getSalesByPeriod(period: 'day' | 'week' | 'month' | 'year'): Promise<SalesTrendData[]>
    async getCurrentPeriodTotal(period: string): Promise<number>
    validateSalesData(data: SalesTrendData[]): boolean
    formatDataForChart(data: SalesTrendData[], period: string): SalesTrendData[]
}
```

#### 2. خدمة الفترة السابقة
**الملف:** `frontend/src/services/previousPeriodService.ts`

```typescript
export class PreviousPeriodService {
    async getPreviousDaySales(): Promise<SalesTrendData[]>
    async getPreviousWeekSales(): Promise<SalesTrendData[]>
    async getPreviousMonthSales(): Promise<SalesTrendData[]>
    async getPreviousYearSales(): Promise<SalesTrendData[]>
    async getSalesByPeriod(period: 'day' | 'week' | 'month' | 'year'): Promise<SalesTrendData[]>
    async getPreviousPeriodTotal(period: string): Promise<number>
    async compareWithCurrentPeriod(period: string, currentTotal: number)
}
```

#### 3. تحديث متجر التقارير
**الملف:** `frontend/src/stores/reportsStore.ts`

```typescript
// استخدام الخدمات المنفصلة
const [currentData, previousData] = await Promise.all([
    currentPeriodService.getSalesByPeriod(period),
    previousPeriodService.getSalesByPeriod(period)
]);
```

## المبادئ الأساسية للتصميم

### 1. فصل الاهتمامات (Separation of Concerns)
- خدمة منفصلة للفترة الحالية
- خدمة منفصلة للفترة السابقة
- كل خدمة تركز على مسؤوليتها فقط

### 2. التعامل الدقيق مع التاريخ والوقت
- استخدام `utils/datetime_utils.py` للتعامل مع التوقيت
- تحويل دقيق بين UTC وتوقيت طرابلس
- معالجة صحيحة لحدود الفترات الزمنية

### 3. معالجة شاملة للأخطاء
- إرجاع بيانات فارغة في حالة الخطأ
- سجلات مفصلة للتتبع والتشخيص
- آليات احتياطية متعددة المستويات

### 4. نمط Singleton
- مثيل واحد من كل خدمة في الواجهة الأمامية
- تحسين استخدام الذاكرة
- ضمان الاتساق في البيانات

## كيفية عمل النظام

### 1. جلب بيانات الفترة الحالية
```
المستخدم يختار فترة (يوم/أسبوع/شهر/سنة)
    ↓
currentPeriodService.getSalesByPeriod(period)
    ↓
API: /api/dashboard/sales-trends/{period}?previous=false
    ↓
CurrentPeriodService.get_sales_by_period(period)
    ↓
بيانات الفترة الحالية
```

### 2. جلب بيانات الفترة السابقة
```
نفس الفترة المختارة
    ↓
previousPeriodService.getSalesByPeriod(period)
    ↓
API: /api/dashboard/sales-trends/{period}?previous=true
    ↓
PreviousPeriodService.get_sales_by_period(period)
    ↓
بيانات الفترة السابقة
```

### 3. عرض البيانات في المخطط
```
البيانات من الخدمتين
    ↓
تنسيق وتحقق من صحة البيانات
    ↓
عرض في نفس المخطط بخطين منفصلين
    ↓
حساب معدل النمو والمقارنة
```

## الفوائد المحققة

### 1. دقة البيانات
- ✅ فصل كامل بين الفترة الحالية والسابقة
- ✅ تجنب تعارض البيانات
- ✅ معالجة دقيقة للتوقيت

### 2. قابلية الصيانة
- ✅ كود منظم ومقسم حسب المسؤوليات
- ✅ سهولة إضافة ميزات جديدة
- ✅ اختبار منفصل لكل خدمة

### 3. الأداء
- ✅ جلب البيانات بشكل متوازي
- ✅ تخزين مؤقت للبيانات
- ✅ تحسين استعلامات قاعدة البيانات

### 4. تجربة المستخدم
- ✅ عرض سلس للبيانات
- ✅ معالجة أخطاء شفافة
- ✅ بيانات دقيقة وموثوقة

## الاختبار والتحقق

تم اختبار النظام الجديد وتأكيد:
- ✅ عمل جميع الخدمات بشكل صحيح
- ✅ جلب البيانات للفترات المختلفة
- ✅ عرض البيانات في المخطط
- ✅ حساب معدلات النمو
- ✅ معالجة الأخطاء

## ملاحظات للمطورين

1. **إضافة فترات جديدة:** أضف الوظيفة في كلا الخدمتين
2. **تعديل منطق التاريخ:** استخدم `utils/datetime_utils.py`
3. **إضافة تحليلات جديدة:** أضف وظائف في الخدمات المناسبة
4. **اختبار التغييرات:** تأكد من اختبار كلا الخدمتين

## الخلاصة

تم بنجاح فصل طريقة جلب بيانات المبيعات إلى خدمات منفصلة، مما يضمن:
- **دقة عالية في البيانات**
- **عدم تعارض في العرض**
- **كود أكثر تنظيماً**
- **سهولة الصيانة والتطوير**

النظام الآن جاهز للاستخدام ويوفر بيانات دقيقة ونظيفة لجميع الفترات الزمنية.
