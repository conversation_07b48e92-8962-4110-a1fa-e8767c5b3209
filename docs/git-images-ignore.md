# إعدادات Git لتجاهل الصور

## 📁 الملفات المتجاهلة

تم إعداد Git لتجاهل جميع الصور المرفوعة من المستخدمين لتجنب:
- زيادة حجم المستودع
- تضارب الملفات بين المطورين
- رفع محتوى حساس أو شخصي

## 🚫 المجلدات المتجاهلة

### Backend:
- `backend/static/uploads/` - جميع الصور المرفوعة
- `backend/static/uploads/brands/` - صور العلامات التجارية
- `backend/static/uploads/categories/` - صور الفئات
- `backend/static/uploads/products/` - صور المنتجات
- `backend/static/uploads/users/` - صور المستخدمين
- `backend/static/uploads/temp/` - الصور المؤقتة

### Frontend:
- `frontend/public/uploads/` - الصور المرفوعة
- `frontend/public/images/` - صور المحتوى

## 🖼️ أنواع الملفات المتجاهلة

```
*.jpg, *.jpeg, *.png, *.gif, *.bmp
*.webp, *.svg, *.ico, *.tiff, *.tif
```

## ✅ الملفات المحفوظة

### الاستثناءات:
- `frontend/public/icons/` - أيقونات التطبيق
- `frontend/public/logos/` - شعارات النظام
- `frontend/src/assets/` - أصول التطبيق
- `docs/images/` - صور التوثيق
- `docs/screenshots/` - لقطات الشاشة

### الصور النموذجية:
- `**/sample.*` - صور نموذجية
- `**/demo.*` - صور تجريبية
- `**/placeholder.*` - صور بديلة
- `**/default.*` - صور افتراضية

## 📂 هيكل المجلدات

```
backend/static/uploads/
├── .gitkeep          # يحافظ على المجلد
├── brands/
│   └── .gitkeep
├── categories/
│   └── .gitkeep
├── products/
│   └── .gitkeep
├── users/
│   └── .gitkeep
└── temp/
    └── .gitkeep
```

## 🔧 الإعداد

### ملف `.gitignore` الرئيسي:
```gitignore
# Image uploads and static files
backend/static/uploads/
backend/static/uploads/brands/
backend/static/uploads/categories/
backend/static/uploads/products/
backend/static/uploads/users/
backend/static/uploads/temp/

# Image file types
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.webp
*.svg
*.ico
*.tiff
*.tif

# Exceptions
!frontend/public/icons/
!frontend/public/logos/
!frontend/src/assets/
!docs/images/
!**/sample.*
!**/demo.*
!**/placeholder.*
!**/default.*
```

### ملف `backend/static/.gitignore`:
```gitignore
uploads/
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.webp
*.svg
*.ico
*.tiff
*.tif

!uploads/.gitkeep
!uploads/brands/.gitkeep
!uploads/categories/.gitkeep
!uploads/products/.gitkeep
!uploads/users/.gitkeep
!uploads/temp/.gitkeep
```

## 🚀 للمطورين الجدد

عند استنساخ المشروع:

1. **إنشاء المجلدات المطلوبة:**
   ```bash
   mkdir -p backend/static/uploads/{brands,categories,products,users,temp}
   ```

2. **تعيين الصلاحيات:**
   ```bash
   chmod 755 backend/static/uploads/
   chmod 755 backend/static/uploads/*
   ```

3. **اختبار رفع الصور:**
   - انتقل إلى صفحة العلامات التجارية
   - جرب رفع صورة شعار
   - تأكد من حفظ الصورة في المجلد الصحيح

## ⚠️ ملاحظات مهمة

- **لا ترفع صور حقيقية** إلى Git
- **استخدم صور نموذجية** للاختبار فقط
- **تأكد من الصلاحيات** على مجلدات الرفع
- **احذف الصور القديمة** دورياً لتوفير المساحة

## 🔄 إزالة الصور من Git (إذا تم رفعها بالخطأ)

```bash
# إزالة الصور من Git مع الاحتفاظ بها محلياً
git rm --cached backend/static/uploads/ -r

# إزالة صور محددة
git rm --cached backend/static/uploads/brands/*.jpg

# تحديث .gitignore وإرسال التغييرات
git add .gitignore
git commit -m "إضافة تجاهل الصور في Git"
```

## 📊 فوائد هذا الإعداد

- ✅ **حجم مستودع أصغر** - لا صور غير ضرورية
- ✅ **أمان أفضل** - لا تسريب للمحتوى الحساس
- ✅ **أداء أسرع** - clone و pull أسرع
- ✅ **تنظيم أفضل** - فصل الكود عن المحتوى
- ✅ **مرونة أكبر** - كل مطور يمكنه استخدام صوره الخاصة للاختبار
