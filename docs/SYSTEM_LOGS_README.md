# نظام سجلات الأخطاء - SmartPOS

## نظرة عامة

تم إضافة نظام شامل لإدارة سجلات أخطاء النظام في تطبيق SmartPOS. يوفر هذا النظام:

- **تسجيل تلقائي للأخطاء** من الواجهة الأمامية والخلفية
- **واجهة إدارة سجلات الأخطاء** في صفحة التقارير
- **تشخيص النظام التلقائي** وإصلاح المشاكل الشائعة
- **إرسال تقارير الأخطاء للدعم**
- **مراقبة حالة النظام** في الوقت الفعلي

## الميزات الجديدة

### 1. تبويبة سجلات الأخطاء في صفحة التقارير

تم إعادة تنظيم تبويبة النظام في صفحة التقارير لتشمل:

- **تبويبة إجراءات النظام**: تحتوي على الإحصائيات وإجراءات النظام والنسخ الاحتياطي
- **تبويبة سجلات الأخطاء**: نظام إدارة سجلات الأخطاء مع ثلاث تبويبات فرعية:
  - **سجلات الأخطاء**: عرض وإدارة سجلات النظام
  - **التنبيهات**: نظام التنبيهات الفورية
  - **مراقب الأداء**: مراقبة الأداء في الوقت الفعلي

### 2. واجهة سجلات الأخطاء

#### عرض حالة النظام
- حالة الواجهة الأمامية (Frontend)
- حالة الخلفية (Backend) 
- حالة قاعدة البيانات (Database)
- إحصائيات الأخطاء (إجمالي، حرجة، آخر خطأ)
- مؤشر أداء النظام

#### إدارة السجلات
- **فلترة السجلات** حسب المستوى والمصدر
- **عرض عادي** مع تفاصيل كاملة
- **وضع الطرفية** لعرض مشابه للـ terminal
- **تحديد متعدد** للسجلات
- **إرسال للدعم** للسجلات المحددة
- **تصدير السجلات** بصيغة JSON
- **حل المشاكل** مع إضافة ملاحظات

#### تشخيص النظام
- **تشخيص تلقائي** لحالة النظام
- **إصلاح تلقائي** للمشاكل الشائعة
- **عرض تفاصيل التشخيص** (المتصفح، الأداء، التخزين، الشبكة)

### 4. نظام التنبيهات الفورية

#### ميزات التنبيهات
- **تنبيهات فورية** للأخطاء الحرجة والتحذيرات
- **تصنيف التنبيهات** حسب النوع (حرج، خطأ، تحذير، معلومات)
- **إجراءات سريعة** مع كل تنبيه
- **تنبيهات مستمرة** للمشاكل الحرجة
- **فحص دوري** لحالة النظام كل 5 دقائق

#### أنواع التنبيهات
- **تنبيهات الشبكة**: مشاكل الاتصال بالخادم
- **تنبيهات قاعدة البيانات**: أخطاء قاعدة البيانات
- **تنبيهات المصادقة**: انتهاء صلاحية الجلسة
- **تنبيهات الأداء**: انخفاض أداء النظام
- **تنبيهات طارئة**: أخطاء حرجة متكررة

### 5. مراقب الأداء في الوقت الفعلي

#### مقاييس الأداء
- **استخدام المعالج**: قياس تقديري لاستخدام المعالج
- **استخدام الذاكرة**: مراقبة ذاكرة JavaScript
- **زمن استجابة الشبكة**: قياس زمن الاستجابة للخادم
- **أداء قاعدة البيانات**: زمن استجابة الاستعلامات
- **أداء الرسم**: زمن تحميل وعرض الصفحات
- **الأداء العام**: مؤشر شامل لحالة النظام

#### ميزات المراقبة
- **مراقبة مستمرة** كل 3 ثواني
- **تاريخ الأداء**: عرض آخر 20 قراءة
- **تنبيهات تلقائية** عند تجاوز العتبات
- **تصدير تقارير الأداء** بصيغة JSON
- **رسم بياني بسيط** لتاريخ الأداء

### 3. نظام تسجيل الأخطاء التلقائي

#### في الواجهة الأمامية
- **معالج الأخطاء العامة** للـ JavaScript
- **معالج أخطاء Promise** غير المعالجة
- **Error Boundary** لأخطاء React
- **تسجيل أخطاء API** تلقائياً
- **تسجيل إجراءات المستخدم** المهمة

#### في الخلفية
- **تسجيل أخطاء API** تلقائياً
- **تسجيل أخطاء قاعدة البيانات**
- **مراقبة حالة النظام**
- **تسجيل العمليات المهمة**

## الملفات المضافة/المحدثة

### الواجهة الأمامية
```
frontend/src/
├── components/
│   ├── SystemLogs.tsx          # مكون إدارة سجلات الأخطاء الرئيسي
│   ├── SystemAlerts.tsx        # مكون إدارة التنبيهات
│   ├── PerformanceMonitor.tsx  # مكون مراقبة الأداء
│   └── ErrorBoundary.tsx       # معالج أخطاء React
├── services/
│   ├── errorLogger.ts          # خدمة تسجيل الأخطاء
│   ├── alertService.ts         # خدمة إدارة التنبيهات
│   └── performanceMonitor.ts   # خدمة مراقبة الأداء
├── stores/
│   └── reportsStore.ts         # محدث لدعم سجلات النظام
├── pages/
│   └── Reports.tsx             # محدث لإضافة التبويبات الفرعية
├── index.css                   # محدث لإضافة أنماط الإشعارات
└── App.tsx                     # محدث لإضافة ErrorBoundary و Toaster
```

### الخلفية
```
backend/
├── routers/
│   └── system_logs.py          # API endpoints لسجلات النظام
├── scripts/
│   └── create_sample_system_logs.py  # إنشاء بيانات تجريبية
└── main.py                     # محدث لإضافة مسارات النظام
```

## API Endpoints

### سجلات النظام
- `GET /api/system/logs` - جلب جميع السجلات
- `GET /api/system/health` - جلب حالة النظام
- `POST /api/system/logs` - إضافة سجل جديد
- `DELETE /api/system/logs` - مسح جميع السجلات
- `PATCH /api/system/logs/{id}/resolve` - حل مشكلة سجل
- `POST /api/system/logs/send-support` - إرسال سجلات للدعم

## قاعدة البيانات

### جدول system_logs
```sql
CREATE TABLE system_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    level TEXT CHECK (level IN ('INFO', 'WARNING', 'ERROR', 'CRITICAL')),
    source TEXT CHECK (source IN ('FRONTEND', 'BACKEND', 'DATABASE', 'SYSTEM')),
    message TEXT NOT NULL,
    details TEXT,
    stack_trace TEXT,
    user_id INTEGER,
    session_id TEXT,
    resolved BOOLEAN DEFAULT FALSE,
    resolution_notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
);
```

## كيفية الاستخدام

### 1. إنشاء بيانات تجريبية
```bash
cd backend
python scripts/create_sample_system_logs.py
```

### 2. الوصول لسجلات الأخطاء
1. اذهب إلى صفحة التقارير
2. اختر تبويبة "النظام"
3. اختر تبويبة "سجلات الأخطاء"

### 3. تشخيص النظام
1. في صفحة سجلات الأخطاء
2. اضغط على زر "تشخيص النظام"
3. سيتم عرض نتائج التشخيص والإصلاحات المطبقة

### 4. إرسال تقرير للدعم
1. حدد السجلات المطلوبة
2. اضغط على "إرسال للدعم"
3. سيتم إنشاء ملف تقرير في مجلد `backend/support_reports/`

### 5. استخدام نظام التنبيهات
1. في صفحة سجلات الأخطاء
2. اختر تبويبة "التنبيهات"
3. يمكنك فلترة التنبيهات حسب النوع
4. اضغط على الإجراءات المتاحة مع كل تنبيه

### 6. مراقبة الأداء
1. في صفحة سجلات الأخطاء
2. اختر تبويبة "مراقب الأداء"
3. اضغط على "بدء" لبدء المراقبة
4. راقب المقاييس في الوقت الفعلي
5. اضغط على "تصدير" لحفظ تقرير الأداء

## الاستخدام المتقدم

### تخصيص التنبيهات

```typescript
import alertService from '../services/alertService';

// إضافة تنبيه مخصص
alertService.addAlert({
  type: 'warning',
  title: 'تحذير مخصص',
  message: 'رسالة التحذير',
  source: 'CUSTOM_MODULE',
  actions: [
    {
      label: 'إجراء',
      action: () => console.log('تم تنفيذ الإجراء'),
      style: 'primary'
    }
  ]
});

// تنبيهات سريعة للأخطاء الشائعة
alertService.createNetworkErrorAlert();
alertService.createDatabaseErrorAlert();
alertService.createAuthErrorAlert();
```

### مراقبة الأداء المخصصة

```typescript
import performanceMonitor from '../services/performanceMonitor';

// بدء المراقبة مع فترة مخصصة
performanceMonitor.startMonitoring(2000); // كل ثانيتين

// إضافة مستمع للأداء
const unsubscribe = performanceMonitor.addListener((performance) => {
  console.log('الأداء العام:', performance.overall.value);

  if (performance.overall.status === 'critical') {
    // اتخاذ إجراء عند انخفاض الأداء
    console.warn('أداء النظام منخفض!');
  }
});

// إيقاف المراقبة
performanceMonitor.stopMonitoring();
unsubscribe();
```

### تسجيل الأخطاء المخصص

```typescript
import errorLogger from '../services/errorLogger';

// تسجيل معلومات
errorLogger.logInfo('عملية مكتملة', { userId: 123, action: 'login' });

// تسجيل تحذير
errorLogger.logWarning('استخدام ذاكرة مرتفع', { memoryUsage: '85%' });

// تسجيل خطأ
errorLogger.logError('ERROR', 'FRONTEND', 'فشل في تحميل البيانات', {
  endpoint: '/api/data',
  error: 'Network timeout'
});

// تسجيل خطأ حرج
errorLogger.logCritical('خطأ حرج في النظام', {
  component: 'PaymentProcessor',
  error: 'Database connection lost'
});

// تسجيل خطأ API
errorLogger.logApiError(error, '/api/products');

// تسجيل إجراء مستخدم
errorLogger.logUserAction('product_purchase', { productId: 456, amount: 100 });

// تسجيل أداء
errorLogger.logPerformance('page_load_time', 1500, { page: 'dashboard' });
```

## الإصلاح التلقائي

يقوم النظام بإصلاح المشاكل التالية تلقائياً:

- **مشاكل التخزين المحلي**: مسح localStorage المعطل
- **مشاكل الذاكرة**: تشغيل garbage collection
- **مشاكل البيانات**: إعادة تحميل البيانات المهمة

## المراقبة والتنبيهات

### مستويات السجلات
- **INFO**: معلومات عامة
- **WARNING**: تحذيرات تحتاج انتباه
- **ERROR**: أخطاء تحتاج إصلاح
- **CRITICAL**: أخطاء حرجة تحتاج تدخل فوري

### حالة النظام
- **HEALTHY**: النظام يعمل بشكل طبيعي
- **WARNING**: يوجد مشاكل بسيطة
- **ERROR**: يوجد مشاكل تحتاج إصلاح

## الأمان والخصوصية

- **تشفير البيانات الحساسة** في السجلات
- **تحديد صلاحيات الوصول** للسجلات
- **حذف السجلات القديمة** تلقائياً
- **عدم تسجيل كلمات المرور** أو البيانات الحساسة

## الصيانة

### تنظيف السجلات القديمة
يُنصح بمسح السجلات القديمة دورياً لتوفير مساحة التخزين.

### مراقبة الأداء
مراقبة تأثير نظام التسجيل على أداء التطبيق وتعديل مستوى التسجيل حسب الحاجة.

### النسخ الاحتياطية
تضمين سجلات النظام في النسخ الاحتياطية الدورية.

## التكامل مع المكونات الأخرى

### إضافة التنبيهات في مكونات أخرى

```typescript
// في مكون المبيعات
import alertService from '../services/alertService';

const handleSaleError = (error: any) => {
  alertService.addAlert({
    type: 'error',
    title: 'خطأ في عملية البيع',
    message: 'فشل في حفظ عملية البيع',
    source: 'SALES_MODULE',
    actions: [
      {
        label: 'إعادة المحاولة',
        action: () => retrySale(),
        style: 'primary'
      }
    ]
  });
};
```

### إضافة مراقبة الأداء في الصفحات

```typescript
// في صفحة المنتجات
import performanceMonitor from '../services/performanceMonitor';

useEffect(() => {
  const startTime = performance.now();

  // تحميل البيانات
  loadProducts().then(() => {
    const loadTime = performance.now() - startTime;
    errorLogger.logPerformance('products_load_time', loadTime, {
      page: 'products',
      count: products.length
    });
  });
}, []);
```

### إضافة تسجيل الأخطاء في الخدمات

```typescript
// في خدمة API
import errorLogger from '../services/errorLogger';

const apiCall = async (endpoint: string) => {
  try {
    const response = await fetch(endpoint);
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    return response.json();
  } catch (error) {
    errorLogger.logApiError(error, endpoint);
    throw error;
  }
};
```

## الأمان والأداء

### تحسين الأداء
- **تجميع السجلات**: إرسال السجلات في مجموعات كل 30 ثانية
- **ضغط البيانات**: ضغط السجلات الكبيرة قبل الإرسال
- **تحديد الحجم**: حد أقصى 1000 سجل في الذاكرة
- **تنظيف تلقائي**: مسح السجلات القديمة تلقائياً

### الأمان
- **تشفير البيانات الحساسة** في السجلات
- **تحديد صلاحيات الوصول** للسجلات
- **عدم تسجيل كلمات المرور** أو البيانات الحساسة
- **تنظيف البيانات** قبل الإرسال للدعم

### الخصوصية
- **إخفاء المعلومات الشخصية** في السجلات
- **تشفير معرفات المستخدمين**
- **حذف البيانات الحساسة** من التقارير
- **موافقة المستخدم** قبل إرسال البيانات

## الدعم الفني

### استكشاف الأخطاء وإصلاحها

#### مشكلة: لا تظهر السجلات
1. تحقق من اتصال قاعدة البيانات
2. تأكد من تشغيل الخادم على المنفذ الصحيح
3. راجع سجلات المتصفح للأخطاء

#### مشكلة: التنبيهات لا تعمل
1. تحقق من إعدادات المتصفح للإشعارات
2. تأكد من تحميل خدمة التنبيهات
3. راجع وحدة التحكم للأخطاء

#### مشكلة: مراقب الأداء لا يعمل
1. تحقق من دعم المتصفح لـ Performance API
2. تأكد من الصلاحيات المطلوبة
3. راجع إعدادات الأمان في المتصفح

### الحصول على المساعدة
1. استخدم ميزة "تشخيص النظام"
2. أرسل تقرير الأخطاء للدعم
3. راجع سجلات النظام للتفاصيل
4. تواصل مع فريق التطوير مع تقرير مفصل

## التطوير المستقبلي

### ميزات مخططة
- **تنبيهات البريد الإلكتروني** للأخطاء الحرجة
- **لوحة تحكم مخصصة** لمراقبة النظام
- **تقارير دورية** تلقائية
- **تكامل مع أنظمة المراقبة الخارجية**
- **ذكاء اصطناعي** لتحليل الأخطاء والتنبؤ بالمشاكل

### تحسينات مقترحة
- **ضغط السجلات** لتوفير مساحة التخزين
- **فهرسة متقدمة** لتسريع البحث
- **تصدير متقدم** بصيغ متعددة
- **واجهة مطور** لإدارة السجلات

---

تم تطوير هذا النظام لتحسين موثوقية وقابلية صيانة تطبيق SmartPOS وتوفير أدوات متقدمة لمراقبة وإدارة النظام.
