# 📋 نظام خصائص المتغيرات - Variant Attributes System

## 🎯 نظرة عامة

نظام خصائص المتغيرات هو نظام شامل لإدارة خصائص المنتجات مثل الحجم، اللون، المادة، والوزن. يتيح هذا النظام للمستخدمين إنشاء وإدارة خصائص مخصصة مع قيمها المختلفة.

## 🏗️ الهيكل العام

### Backend Components

#### 1. النماذج (Models)
- **VariantAttribute**: نموذج الخصائص الرئيسية
- **VariantValue**: نموذج قيم الخصائص

#### 2. الخدمات (Services)
- **VariantAttributeService**: خدمة شاملة لإدارة الخصائص والقيم

#### 3. المسارات (Routes)
- **variant_attributes.py**: مسارات API لجميع العمليات

#### 4. المخططات (Schemas)
- **variant_attribute.py**: مخططات البيانات والتحقق

### Frontend Components

#### 1. الأنواع (Types)
- **variantAttribute.ts**: تعريفات TypeScript

#### 2. الخدمات (Services)
- **variantAttributeService.ts**: خدمة API للواجهة الأمامية

#### 3. المتاجر (Stores)
- **variantAttributeStore.ts**: إدارة الحالة باستخدام Zustand

#### 4. المكونات (Components)
- **VariantAttributesDataTable**: جدول البيانات الرئيسي
- **VariantAttributeModal**: نافذة إضافة/تعديل الخصائص
- **VariantAttributesTab**: تبويب في CatalogManagement

## 🔧 الميزات الرئيسية

### 1. إدارة الخصائص
- ✅ إنشاء خصائص جديدة
- ✅ تعديل الخصائص الموجودة
- ✅ حذف الخصائص (Soft Delete)
- ✅ إعادة ترتيب الخصائص
- ✅ تفعيل/إلغاء تفعيل الخصائص

### 2. إدارة قيم الخصائص
- ✅ إضافة قيم للخصائص
- ✅ تعديل القيم الموجودة
- ✅ حذف القيم
- ✅ إعادة ترتيب القيم
- ✅ دعم أكواد الألوان للخصائص اللونية

### 3. أنواع الخصائص المدعومة
- **text**: نص عادي
- **color**: ألوان مع أكواد hex
- **list**: قائمة من القيم المحددة مسبقاً
- **number**: قيم رقمية

### 4. الفلترة والبحث
- ✅ البحث في أسماء الخصائص
- ✅ فلترة حسب الحالة (نشط/غير نشط)
- ✅ فلترة حسب نوع الخاصية
- ✅ فلترة حسب وجود القيم

## 📊 قاعدة البيانات

### جدول variant_attributes
```sql
CREATE TABLE variant_attributes (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    name_ar VARCHAR(100) NOT NULL,
    description TEXT,
    attribute_type VARCHAR(50) DEFAULT 'text',
    is_required BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id)
);
```

### جدول variant_values
```sql
CREATE TABLE variant_values (
    id SERIAL PRIMARY KEY,
    attribute_id INTEGER NOT NULL REFERENCES variant_attributes(id) ON DELETE CASCADE,
    value VARCHAR(100) NOT NULL,
    value_ar VARCHAR(100) NOT NULL,
    color_code VARCHAR(7), -- للألوان
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

## 🔌 API Endpoints

### خصائص المتغيرات
- `GET /api/variant-attributes/` - جلب جميع الخصائص
- `GET /api/variant-attributes/{id}` - جلب خاصية محددة
- `POST /api/variant-attributes/` - إنشاء خاصية جديدة
- `PUT /api/variant-attributes/{id}` - تحديث خاصية
- `DELETE /api/variant-attributes/{id}` - حذف خاصية
- `PUT /api/variant-attributes/reorder` - إعادة ترتيب الخصائص

### قيم الخصائص
- `GET /api/variant-attributes/{id}/values` - جلب قيم خاصية
- `POST /api/variant-attributes/{id}/values` - إضافة قيمة جديدة
- `PUT /api/variant-attributes/values/{id}` - تحديث قيمة
- `DELETE /api/variant-attributes/values/{id}` - حذف قيمة
- `PUT /api/variant-attributes/values/reorder` - إعادة ترتيب القيم

## 🎨 واجهة المستخدم

### التبويب الجديد في CatalogManagement
تم إضافة تبويب "خصائص المتغيرات" إلى صفحة إدارة الكتالوج مع:
- أيقونة FiLayers من react-icons/fi
- تصميم متسق مع باقي التبويبات
- دعم كامل للغة العربية

### جدول البيانات
- عرض الخصائص مع معلوماتها الأساسية
- أزرار للتعديل والحذف
- مؤشرات الحالة (نشط/غير نشط)
- عداد قيم كل خاصية

### نافذة الإضافة/التعديل
- نموذج شامل لإدخال بيانات الخاصية
- إدارة القيم داخل نفس النافذة
- دعم أكواد الألوان للخصائص اللونية
- التحقق من صحة البيانات

## 🔒 الأمان والصلاحيات

- جميع العمليات تتطلب مصادقة المستخدم
- تسجيل المستخدم المنشئ والمحدث
- Soft delete للحفاظ على سلامة البيانات
- التحقق من صحة البيانات على مستوى الخادم والعميل

## 📈 البيانات الافتراضية

يتم إدراج البيانات الافتراضية التالية عند التثبيت:

### الخصائص الافتراضية:
1. **الحجم (Size)** - نوع: قائمة
   - قيم: XS, S, M, L, XL, XXL
2. **اللون (Color)** - نوع: لون
   - قيم: أحمر، أزرق، أخضر، أسود، أبيض
3. **المادة (Material)** - نوع: قائمة
   - قيم: قطن، جلد، صناعي، حرير
4. **الوزن (Weight)** - نوع: قائمة
   - قيم: خفيف، متوسط، ثقيل

## 🚀 التطوير المستقبلي

### المرحلة التالية:
1. ربط الخصائص بالمنتجات
2. إنشاء متغيرات المنتجات
3. إدارة المخزون لكل متغير
4. تقارير المبيعات حسب المتغيرات

### التحسينات المقترحة:
1. دعم الصور للقيم
2. خصائص ديناميكية (حسب الفئة)
3. قوالب خصائص جاهزة
4. استيراد/تصدير الخصائص

## 📝 ملاحظات التطوير

### مبادئ البرمجة المتبعة:
- ✅ البرمجة الكائنية (OOP)
- ✅ معالجة شاملة للأخطاء
- ✅ استخدام أيقونات معتمدة من react-icons/fi
- ✅ دعم كامل للغة العربية
- ✅ تصميم متجاوب
- ✅ أمان البيانات

### الاختبارات:
- ✅ اختبار إنشاء الجداول
- ✅ اختبار إدراج البيانات الافتراضية
- ✅ اختبار تشغيل الخادم
- ✅ اختبار API endpoints
- ✅ اختبار التكامل مع CatalogManagement

---

**تاريخ الإنشاء**: 2025-01-27  
**الإصدار**: 1.0.0  
**المطور**: AI Agent - SmartPOS System
