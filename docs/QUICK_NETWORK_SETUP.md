# 🚀 إعداد سريع للوصول من الشبكة المحلية

## المشكلة المحلولة ✅
تم حل مشكلة "request blocked" عند محاولة تسجيل الدخول من جهاز آخر في الشبكة.

## الحل السريع

### طريقة 1: التشغيل التلقائي (الأسهل)

#### Linux/macOS:
```bash
./start-network.sh
```

#### Windows:
```cmd
start-network.bat
```

### طريقة 2: التشغيل اليدوي

#### 1. تشغيل الخادم الخلفي
```bash
cd backend
python main.py
```
**يجب أن ترى**: `Uvicorn running on http://0.0.0.0:8002`

#### 2. تشغيل الواجهة الأمامية
```bash
cd frontend
npm run dev:network
```
**ابحث عن**: `🔗 Backend proxy target: http://[YOUR_IP]:8002`

### 3. اختبار الإعدادات (اختياري)
```bash
node network-test.js
```

## الوصول من الأجهزة الأخرى

### إذا كان عنوان IP للخادم هو `*************`:
- **الواجهة الأمامية**: `http://*************:5175`
- **الخادم الخلفي**: `http://*************:8002`

## التحقق من نجاح الإعداد

### في console المتصفح يجب أن ترى:
```
🌐 Current frontend host: *************
🔧 Environment - DEV: true, MODE: development, NODE_ENV: development
🔧 Force Dynamic IP: true
🔗 Using network backend: http://*************:8002
```

### في terminal الـ frontend يجب أن ترى:
```
🔧 Vite Environment - NODE_ENV: development, isDev: true
🔗 Backend proxy target: http://*************:8002
```

### إذا كنت ترى "Using production backend: http://localhost:8002":
1. أوقف الخادم الأمامي (Ctrl+C)
2. استخدم الأمر الجديد: `npm run dev:network`
3. أو استخدم السكريبت التلقائي: `./start-network.sh` أو `start-network.bat`

## إذا لم يعمل

### 1. تحقق من الجدار الناري
```bash
# Linux/Ubuntu
sudo ufw allow 8002
sudo ufw allow 5175

# Windows
# افتح Windows Defender Firewall وأضف المنافذ 8002 و 5175
```

### 2. تحقق من الاتصال
```bash
# من جهاز آخر في الشبكة
curl http://*************:8002/
curl http://*************:5175/
```

### 3. تحقق من console المتصفح
- افتح Developer Tools (F12)
- ابحث عن أخطاء CORS أو Network
- تأكد من أن الطلبات تذهب للعنوان الصحيح

## ملاحظات مهمة

⚠️ **هذه الإعدادات للتطوير فقط** - لا تستخدمها في الإنتاج بدون إعدادات أمان إضافية.

✅ **تم الاختبار على**: Windows, Linux, macOS مع شبكات محلية مختلفة.

📞 **للدعم**: راجع ملف `NETWORK_SETUP.md` للتفاصيل الكاملة.
