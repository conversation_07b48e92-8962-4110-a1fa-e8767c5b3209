# إصلاح مشكلة تنسيق التاريخ والوقت في مخططات ApexCharts

## 📋 وصف المشكلة

### المشكلة الأساسية
كانت هناك مشكلة في عرض مخططات ApexCharts بعد ربطها بنظام التنسيق الموحد للتاريخ والوقت. المشكلة تمثلت في:

```
Error: <path> attribute d: Expected number, "…6126680000002125C 20.85504840353…"
Error: parser Error
```

### السبب الجذري
المشكلة كانت في ملف `frontend/src/pages/Dashboard.tsx` في دالة `processChartData`:

1. **استخدام خاطئ لـ `formatDate`**: كانت الدالة تستخدم `formatDate` من `useDateTimeFormatters` لمعالجة البيانات المرسلة إلى ApexCharts
2. **تنسيق عربي/هجري في البيانات**: هذا أدى إلى إرسال تواريخ منسقة عربياً أو هجرياً (مثل "١٥ يوليو ٢٠٢٤") إلى المخطط
3. **فشل ApexCharts في المعالجة**: ApexCharts يتوقع تواريخ بصيغة ISO أو timestamp، وليس نصوص عربية منسقة

## 🔧 الحل المطبق

### 1. إصلاح دالة `processChartData`

#### قبل الإصلاح:
```typescript
// استخدام formatDate في معالجة البيانات (خطأ)
const formattedDate = await formatDate(dateObj);
if (dateMap.has(formattedDate)) {
  dateMap.set(formattedDate, item.amount);
}
```

#### بعد الإصلاح:
```typescript
// استخدام تنسيق ISO للبيانات (صحيح)
const isoDate = dateObj.toISOString().split('T')[0];
if (dateMap.has(isoDate)) {
  dateMap.set(isoDate, item.amount);
}
```

### 2. التغييرات المطبقة

#### أ. إصلاح معالجة الأسبوع والشهر:
```typescript
// إزالة await والاستخدام المتزامن
normalizedData.forEach((item) => {
  // ... معالجة البيانات
  const isoDate = dateObj.toISOString().split('T')[0];
  if (dateMap.has(isoDate)) {
    dateMap.set(isoDate, item.amount);
  }
});
```

#### ب. إصلاح معالجة السنة:
```typescript
// استخدام تنسيق YYYY-MM مباشرة
const year = dateObj.getFullYear();
const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
const yearMonth = `${year}-${month}`;
if (monthMap.has(yearMonth)) {
  monthMap.set(yearMonth, item.amount);
}
```

### 3. تحسين معالجة الأخطاء

#### إضافة تسجيل أخطاء محسن:
```typescript
} catch (error) {
  console.error('Error formatting chart label:', error);
  return value;
}
```

### 4. تنظيف الكود

#### إزالة الدوال غير المستخدمة:
- `formatYearMonth` - لم تعد مطلوبة
- `formatSaleDate` - لم تعد مستخدمة
- تنظيف الاستيرادات غير المستخدمة

## ✅ النتائج

### 1. إصلاح الأخطاء
- ✅ لا توجد أخطاء parser في ApexCharts
- ✅ المخططات تعرض بشكل صحيح لجميع الفترات
- ✅ البيانات تُرسل بصيغة صحيحة للمخطط

### 2. الحفاظ على التنسيق العربي
- ✅ العرض للمستخدم لا يزال بالتنسيق العربي/الهجري المطلوب
- ✅ التنسيق يحدث فقط في `formatter` وليس في البيانات
- ✅ جميع إعدادات التاريخ والوقت تعمل بشكل صحيح

### 3. اختبار جميع الفترات
- ✅ اليوم: يعرض الساعات بشكل صحيح
- ✅ الأسبوع: يعرض الأيام بشكل صحيح  
- ✅ الشهر: يعرض التواريخ بشكل صحيح
- ✅ السنة: يعرض الشهور بالأسماء العربية

## 🎯 المبدأ المطبق

### فصل منطق البيانات عن منطق العرض
```typescript
// ✅ الطريقة الصحيحة:
// 1. البيانات للمخطط: تنسيق ISO/timestamp
const chartData = processedData.map(item => ({
  date: item.date, // بصيغة ISO: "2024-07-15"
  amount: item.amount
}));

// 2. العرض للمستخدم: تنسيق عربي/هجري في formatter فقط
xaxis: {
  categories: chartData.map(item => item.date),
  labels: {
    formatter: (value: string) => {
      // هنا يحدث التنسيق العربي/الهجري
      return formatArabicDate(value);
    }
  }
}
```

## 📝 الدروس المستفادة

1. **فصل البيانات عن العرض**: البيانات المرسلة للمخططات يجب أن تبقى بصيغة قابلة للمعالجة
2. **التنسيق في العرض فقط**: التنسيق العربي/الهجري يجب أن يحدث في `formatter` وليس في البيانات
3. **اختبار جميع الحالات**: التأكد من اختبار جميع فترات العرض (يوم، أسبوع، شهر، سنة)
4. **معالجة الأخطاء**: إضافة تسجيل مفصل للأخطاء لتسهيل التشخيص

## 🔍 ملفات تم تعديلها

- `frontend/src/pages/Dashboard.tsx` - الإصلاح الرئيسي
- تم التحقق من `frontend/src/pages/Reports.tsx` - لا يحتاج إصلاح

---

**تاريخ الإصلاح**: 15 يوليو 2025  
**المطور**: نظام الذكاء الاصطناعي  
**الحالة**: ✅ مكتمل ومختبر
