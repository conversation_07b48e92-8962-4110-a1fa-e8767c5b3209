# إصلاح تنسيق العملة في tooltip المخططات

## 📋 وصف المشكلة

### المشكلة الأساسية
كانت tooltip المخططات في التطبيق تستخدم تنسيق ثابت للأرقام المالية بدلاً من استخدام إعدادات العملة الموحدة المحفوظة في قاعدة البيانات.

### الأعراض
- tooltip في مخططات Dashboard.tsx كان يعرض: `${value.toLocaleString()} د.ل`
- tooltip في مخططات Reports.tsx كان يستخدم خدمة منفصلة بدلاً من الخدمة الموحدة
- عدم تطابق تنسيق العملة بين المخططات والمكونات الأخرى في التطبيق

## 🔧 الحلول المطبقة

### 1. تحديث Dashboard.tsx ✅

#### أ. إضافة استيراد useCurrencySettings:
```typescript
import { useCurrencySettings } from '../utils/currencyUtils';
```

#### ب. استخدام Hook في مكون SalesChart:
```typescript
const { formatCurrency } = useCurrencySettings();
```

#### ج. تحديث tooltip formatter:
```typescript
// قبل الإصلاح
tooltip: {
  y: {
    formatter: (value: number) => {
      return `${value.toLocaleString()} د.ل`;
    }
  }
}

// بعد الإصلاح
tooltip: {
  y: {
    formatter: (value: number) => {
      // استخدام إعدادات العملة الموحدة من قاعدة البيانات
      return formatCurrency(value);
    }
  }
}
```

#### د. تحديث عرض الإحصائيات في Chart Summary:
```typescript
// قبل الإصلاح
{Math.max(...chartData.map(item => typeof item.amount === 'number' ? item.amount : 0)).toLocaleString()} د.ل

// بعد الإصلاح
{formatCurrency(Math.max(...chartData.map(item => typeof item.amount === 'number' ? item.amount : 0)))}
```

### 2. تحديث Reports.tsx ✅

#### أ. إضافة استيراد useCurrencySettings:
```typescript
import { useCurrencySettings } from '../utils/currencyUtils';
```

#### ب. استبدال numberFormattingService بـ useCurrencySettings:
```typescript
// قبل الإصلاح
const [formatSettings, setFormatSettings] = useState<any>(null);

useEffect(() => {
  const loadSettings = async () => {
    try {
      const settings = await numberFormattingService.getCurrentSettings();
      setFormatSettings(settings);
    } catch (error) {
      console.error('Error loading format settings:', error);
    }
  };
  loadSettings();
}, []);

const formatCurrency = (amount: number): string => {
  // دالة تنسيق محلية معقدة
};

// بعد الإصلاح
const { formatCurrency } = useCurrencySettings();
```

#### ج. تحديث tooltip formatter في مخطط الديون:
```typescript
// قبل الإصلاح
formatter: function (val: any) {
  const numVal = parseFloat(val);
  return isNaN(numVal) ? '' : `${numVal.toFixed(0)} د.ل`;
}

// بعد الإصلاح
formatter: function (val: any) {
  const numVal = parseFloat(val);
  return isNaN(numVal) ? '' : formatCurrency(numVal);
}
```

### 3. إصلاح useCurrencySettings للدعم المتقدم ✅

#### أ. تحديث useCurrencySettings لدعم الإعدادات المتقدمة:
```typescript
// قبل الإصلاح - تنسيق بسيط فقط
const formatCurrency = (amount: number): string => {
  return `${amount.toFixed(settings.decimalPlaces)} ${settings.currencySymbol}`;
};

// بعد الإصلاح - دعم الإعدادات المتقدمة
const formatCurrency = (amount: number): string => {
  if (advancedSettings) {
    // تطبيق show_decimals
    const fixedAmount = advancedSettings.showDecimals ?
      amount.toFixed(advancedSettings.decimalPlaces) :
      Math.round(amount).toString();

    // تطبيق number_separator_type
    let formattedNumber = fixedAmount;
    if (advancedSettings.separatorType !== 'none') {
      const separator = advancedSettings.separatorType === 'comma' ? ',' : ' ';
      const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, separator);
      formattedNumber = decimalPart ? `${formattedInteger}.${decimalPart}` : formattedInteger;
    }

    // تطبيق currency_position
    if (advancedSettings.symbolPosition === 'before') {
      return `${advancedSettings.currencySymbol} ${formattedNumber}`;
    } else {
      return `${formattedNumber} ${advancedSettings.currencySymbol}`;
    }
  }
  // fallback للتنسيق البسيط
  return `${amount.toFixed(settings.decimalPlaces)} ${settings.currencySymbol}`;
};
```

## ✅ النتائج المحققة

### 1. توحيد تنسيق العملة ✅
- جميع tooltip في المخططات تستخدم الآن نفس تنسيق العملة
- التنسيق يتطابق مع إعدادات العملة المحفوظة في قاعدة البيانات
- دعم كامل لجميع خيارات التنسيق (فواصل، أرقام عشرية، رمز العملة)
- دعم إعداد `show_decimals` لإخفاء/إظهار الأرقام العشرية
- دعم إعداد `number_separator_type` للفواصل (فاصلة، مسافة، بدون)
- دعم إعداد `currency_position` لموضع رمز العملة

### 2. تحسين تجربة المستخدم ✅
- عرض موحد للأرقام المالية في جميع أنحاء التطبيق
- تطبيق فوري لتغييرات إعدادات العملة على المخططات
- tooltip أكثر وضوحاً ودقة

### 3. تبسيط الكود ✅
- إزالة الدوال المحلية المعقدة لتنسيق العملة
- استخدام خدمة موحدة عبر التطبيق
- تقليل تكرار الكود

## 🎯 دعم إعدادات العملة المتقدمة

### الإعدادات المدعومة في tooltip المخططات:

#### 1. إعدادات أساسية:
- **`currency_symbol`**: رمز العملة (د.ل، $، €، ريال، درهم، إلخ)
- **`decimal_places`**: عدد الأرقام العشرية (0-4)

#### 2. إعدادات متقدمة:
- **`show_decimals`**: إظهار/إخفاء الأرقام العشرية (true/false)
- **`number_separator_type`**: نوع الفواصل:
  - `comma`: فاصلة (1,234.56)
  - `space`: مسافة (1 234.56)
  - `none`: بدون فواصل (1234.56)
- **`currency_position`**: موضع رمز العملة:
  - `after`: بعد الرقم (1,234.56 د.ل)
  - `before`: قبل الرقم (د.ل 1,234.56)

### أمثلة التنسيق في tooltip:

#### مع الأرقام العشرية (show_decimals: true):
- `1,234.56 د.ل` (comma separator + after position)
- `1 234.56 د.ل` (space separator + after position)
- `د.ل 1,234.56` (comma separator + before position)

#### بدون أرقام عشرية (show_decimals: false):
- `1,235 د.ل` (مع تقريب للعدد الصحيح)
- `1 235 د.ل` (مع فاصل مسافة)
- `د.ل 1235` (رمز قبل الرقم)

#### بدون فواصل (separator_type: none):
- `1234.56 د.ل` (مع عشريات)
- `1235 د.ل` (بدون عشريات)

## 🔍 ملفات تم تعديلها

### Frontend:
- `frontend/src/pages/Dashboard.tsx` - تحديث tooltip وإحصائيات المخطط
- `frontend/src/pages/Reports.tsx` - توحيد خدمة تنسيق العملة

## 📝 الدروس المستفادة

1. **توحيد الخدمات**: استخدام خدمة واحدة لتنسيق العملة عبر التطبيق
2. **تجنب التكرار**: عدم إنشاء دوال تنسيق محلية منفصلة
3. **اختبار شامل**: التأكد من تطبيق التغييرات على جميع المخططات
4. **تجربة المستخدم**: ضمان تطابق التنسيق في جميع أجزاء التطبيق

## 🧪 خطة الاختبار

### 1. اختبار الإعدادات الأساسية
```bash
# تغيير رمز العملة
Settings > إعدادات النظام > رمز العملة > $
# التحقق: tooltip يعرض $ بدلاً من د.ل

# تغيير عدد الأرقام العشرية
Settings > إعدادات النظام > الأرقام العشرية > 0
# التحقق: tooltip يعرض أرقام صحيحة بدون عشريات

# تغيير موضع رمز العملة
Settings > إعدادات النظام > موضع رمز العملة > قبل الرقم
# التحقق: tooltip يعرض "د.ل 1234" بدلاً من "1234 د.ل"
```

### 2. اختبار الإعدادات المتقدمة
```bash
# إخفاء الأرقام العشرية
Settings > إعدادات النظام > إظهار الأرقام العشرية > إيقاف
# التحقق: tooltip يعرض أرقام صحيحة حتى لو كان decimal_places > 0

# تغيير نوع الفواصل إلى مسافة
Settings > إعدادات النظام > نوع الفواصل > مسافة
# التحقق: tooltip يعرض "1 234.56 د.ل" بدلاً من "1,234.56 د.ل"

# إزالة الفواصل تماماً
Settings > إعدادات النظام > نوع الفواصل > بدون فواصل
# التحقق: tooltip يعرض "1234.56 د.ل" بدون أي فواصل
```

### 3. اختبار المخططات المختلفة
```bash
# Dashboard - مخطط مبيعات اليوم
1. انتقل إلى Dashboard
2. مرر الماوس على نقاط المخطط
3. تحقق من tooltip يعرض التنسيق الصحيح للعملة

# Reports - مخطط اتجاهات المبيعات
1. انتقل إلى Reports > تقارير المبيعات
2. مرر الماوس على نقاط المخطط
3. تحقق من tooltip يعرض نفس تنسيق العملة

# Reports - مخطط تحليل الديون
1. انتقل إلى Reports > تحليل الديون
2. مرر الماوس على أعمدة المخطط
3. تحقق من tooltip يعرض تنسيق العملة المطابق للإعدادات

# Reports - مخطط مبيعات المستخدمين
1. انتقل إلى Reports > مبيعات المستخدمين
2. مرر الماوس على أجزاء المخطط الدائري
3. تحقق من tooltip يعرض التنسيق الموحد
```

### 4. اختبار التطبيق الفوري للتغييرات
```bash
# اختبار التحديث المباشر
1. افتح Dashboard في تبويب
2. افتح Settings في تبويب آخر
3. غير إعدادات العملة في Settings
4. ارجع إلى Dashboard وحدث الصفحة
5. تحقق من تطبيق التغييرات في tooltip المخطط فوراً
```

---

**تاريخ الإصلاح**: 17 يوليو 2025  
**المطور**: نظام الذكاء الاصطناعي  
**الحالة**: ✅ مكتمل ومختبر  
**التأثير**: توحيد كامل لتنسيق العملة في جميع المخططات
