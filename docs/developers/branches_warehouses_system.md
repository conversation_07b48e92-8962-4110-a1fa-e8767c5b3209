# 📋 دليل المطورين - نظام إدارة الفروع والمستودعات

> **تاريخ الإنشاء:** 2025-01-11  
> **الإصدار:** 1.0  
> **المطور:** Augment Agent  

## 📖 نظرة عامة

نظام إدارة الفروع والمستودعات هو نظام متقدم يوفر إدارة شاملة للعلاقات Many-to-Many بين فروع الشركة ومستودعاتها، مع دعم التوزيع الذكي والإدارة المرنة للموارد.

## 🏗️ هيكل النظام

### 1. **النماذج (Models)**

#### **نموذج الفروع (Branch)**
```python
# المسار: backend/models/branch.py
class Branch(Base):
    __tablename__ = "branches"
    
    # الحقول الأساسية
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    code = Column(String(20), unique=True, nullable=False, index=True)
    address = Column(Text, nullable=True)
    phone = Column(String(20), nullable=True)
    manager_name = Column(String(100), nullable=True)
    email = Column(String(100), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    is_main = Column(Boolean, default=False, nullable=False)
    
    # معلومات إضافية
    city = Column(String(50), nullable=True)
    region = Column(String(50), nullable=True)
    postal_code = Column(String(20), nullable=True)
    
    # إعدادات الفرع
    max_daily_sales = Column(Integer, nullable=True)
    working_hours_start = Column(String(10), nullable=True)
    working_hours_end = Column(String(10), nullable=True)
    
    # تواريخ النظام
    created_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())
    updated_at = Column(DateTime(timezone=True), onupdate=tripoli_timestamp())
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    updated_by = Column(Integer, ForeignKey("users.id"), nullable=True)
```

#### **جدول الربط (branch_warehouses)**
```python
# المسار: backend/models/branch.py
branch_warehouses = Table(
    'branch_warehouses',
    Base.metadata,
    Column('branch_id', Integer, ForeignKey('branches.id'), primary_key=True),
    Column('warehouse_id', Integer, ForeignKey('warehouses.id'), primary_key=True),
    Column('created_at', DateTime(timezone=True), server_default=tripoli_timestamp()),
    Column('is_primary', Boolean, default=False),
    Column('priority', Integer, default=1)
)
```

### 2. **الخدمات (Services)**

#### **خدمة الفروع (BranchService)**
```python
# المسار: backend/services/branch_service.py

# إنشاء مثيل من الخدمة
branch_service = get_branch_service(db_session)

# العمليات المتاحة:
- create_branch(branch_data)          # إنشاء فرع جديد
- update_branch(branch_id, data)      # تحديث بيانات الفرع
- delete_branch(branch_id)            # حذف الفرع
- get_all_branches(include_inactive)  # جلب جميع الفروع
- get_branch_by_id(branch_id)         # جلب فرع بالمعرف
- get_branch_by_code(branch_code)     # جلب فرع بالكود
- set_main_branch(branch_id)          # تعيين الفرع الرئيسي
- toggle_branch_status(branch_id)     # تبديل حالة النشاط
- search_branches(search_term)        # البحث في الفروع
```

#### **خدمة العلاقات (BranchWarehouseService)**
```python
# المسار: backend/services/branch_warehouse_service.py

# إنشاء مثيل من الخدمة
service = get_branch_warehouse_service(db_session)

# العمليات المتاحة:
- link_branch_to_warehouse(branch_id, warehouse_id, is_primary, priority)
- unlink_branch_from_warehouse(branch_id, warehouse_id)
- set_primary_warehouse_for_branch(branch_id, warehouse_id)
- update_warehouse_priority_for_branch(branch_id, warehouse_id, priority)
- get_warehouses_for_branch(branch_id, include_inactive)
- get_branches_for_warehouse(warehouse_id, include_inactive)
- get_primary_warehouse_for_branch(branch_id)
- get_available_warehouses_for_branch(branch_id)
- get_available_branches_for_warehouse(warehouse_id)
```

### 3. **مخططات البيانات (Schemas)**

#### **مخططات الفروع**
```python
# المسار: backend/schemas/branch.py

# إنشاء فرع جديد
class BranchCreate(BranchBase):
    name: str
    code: str
    # ... باقي الحقول

# تحديث الفرع
class BranchUpdate(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    # ... باقي الحقول

# استجابة الفرع
class BranchResponse(BranchBase):
    id: int
    warehouses_count: Optional[int] = 0
    active_warehouses_count: Optional[int] = 0
    warehouses: Optional[List[WarehouseInBranch]] = []
    # ... باقي الحقول
```

#### **مخططات العلاقات**
```python
# ربط فرع بمستودع
class BranchWarehouseLinkCreate(BaseModel):
    branch_id: int
    warehouse_id: int
    is_primary: bool = False
    priority: int = Field(1, ge=1, le=100)

# تحديث الربط
class BranchWarehouseLinkUpdate(BaseModel):
    is_primary: Optional[bool] = None
    priority: Optional[int] = Field(None, ge=1, le=100)
```

### 4. **API Endpoints**

#### **endpoints الفروع**
```python
# المسار: backend/routers/branch.py

POST   /api/branches/                    # إنشاء فرع جديد
GET    /api/branches/                    # جلب جميع الفروع
GET    /api/branches/{branch_id}         # جلب فرع بالمعرف
GET    /api/branches/code/{branch_code}  # جلب فرع بالكود
PUT    /api/branches/{branch_id}         # تحديث الفرع
DELETE /api/branches/{branch_id}         # حذف الفرع
POST   /api/branches/{branch_id}/set-main        # تعيين فرع رئيسي
POST   /api/branches/{branch_id}/toggle-status  # تبديل حالة النشاط
GET    /api/branches/search/             # البحث في الفروع
```

#### **endpoints العلاقات**
```python
# المسار: backend/routers/branch_warehouse.py

POST   /api/branch-warehouses/link                           # ربط فرع بمستودع
DELETE /api/branch-warehouses/unlink                         # إلغاء الربط
POST   /api/branch-warehouses/set-primary                    # تعيين مستودع أساسي
PUT    /api/branch-warehouses/update-priority                # تحديث الأولوية
GET    /api/branch-warehouses/branch/{branch_id}/warehouses  # مستودعات الفرع
GET    /api/branch-warehouses/warehouse/{warehouse_id}/branches  # فروع المستودع
GET    /api/branch-warehouses/branch/{branch_id}/primary-warehouse  # المستودع الأساسي
GET    /api/branch-warehouses/branch/{branch_id}/available-warehouses  # المستودعات المتاحة
GET    /api/branch-warehouses/warehouse/{warehouse_id}/available-branches  # الفروع المتاحة
```

## 🔧 دليل الاستخدام للمطورين

### **1. إنشاء فرع جديد**
```python
from services.branch_service import get_branch_service
from database.session import SessionLocal

db = SessionLocal()
branch_service = get_branch_service(db)

branch_data = {
    "name": "فرع الزاوية",
    "code": "ZAW-001",
    "address": "شارع الجمهورية، الزاوية",
    "phone": "021-1234567",
    "manager_name": "أحمد محمد",
    "email": "<EMAIL>",
    "city": "الزاوية",
    "region": "الزاوية",
    "is_active": True,
    "working_hours_start": "08:00",
    "working_hours_end": "18:00",
    "created_by": user_id
}

result = branch_service.create_branch(branch_data)
if result['success']:
    print(f"تم إنشاء الفرع: {result['branch']['name']}")
else:
    print(f"خطأ: {result['error']}")

db.close()
```

### **2. ربط فرع بمستودع**
```python
from services.branch_warehouse_service import get_branch_warehouse_service

db = SessionLocal()
service = get_branch_warehouse_service(db)

# ربط الفرع بمستودع كمستودع أساسي
result = service.link_branch_to_warehouse(
    branch_id=1,
    warehouse_id=2,
    is_primary=True,
    priority=1
)

if result['success']:
    print("تم ربط الفرع بالمستودع بنجاح")
else:
    print(f"خطأ: {result['error']}")

db.close()
```

### **3. جلب مستودعات الفرع**
```python
# جلب جميع المستودعات المرتبطة بالفرع
result = service.get_warehouses_for_branch(branch_id=1)

if result['success']:
    warehouses = result['warehouses']
    for warehouse in warehouses:
        primary = "⭐" if warehouse['is_primary'] else ""
        print(f"{primary} {warehouse['name']} - أولوية: {warehouse['priority']}")
```

### **4. البحث في الفروع**
```python
# البحث في الفروع
result = branch_service.search_branches(
    search_term="طرابلس",
    include_inactive=False
)

if result['success']:
    branches = result['branches']
    print(f"تم العثور على {len(branches)} فرع")
    for branch in branches:
        print(f"- {branch['name']} ({branch['code']})")
```

## ⚠️ ملاحظات مهمة للمطورين

### **1. معالجة الأخطاء**
```python
try:
    result = branch_service.create_branch(branch_data)
    if not result['success']:
        # معالجة خطأ منطقي
        logger.error(f"فشل في إنشاء الفرع: {result['error']}")
        return {"error": result['error']}, 400
except Exception as e:
    # معالجة خطأ تقني
    logger.error(f"خطأ تقني: {e}")
    return {"error": "خطأ داخلي في الخادم"}, 500
```

### **2. التحقق من الصلاحيات**
```python
# تأكد من وجود صلاحيات المستخدم قبل العمليات الحساسة
if not user.has_permission('manage_branches'):
    raise HTTPException(
        status_code=403,
        detail="ليس لديك صلاحية لإدارة الفروع"
    )
```

### **3. استخدام المعاملات (Transactions)**
```python
try:
    # بداية المعاملة
    branch = branch_service.create_branch(branch_data)
    
    # ربط المستودعات
    for warehouse_id in warehouse_ids:
        service.link_branch_to_warehouse(
            branch['branch']['id'], 
            warehouse_id
        )
    
    db.commit()
    logger.info("تم إنشاء الفرع وربط المستودعات بنجاح")
    
except Exception as e:
    db.rollback()
    logger.error(f"فشل في العملية: {e}")
    raise
```

## 🔍 نصائح للتطوير

### **1. الفهرسة والأداء**
- استخدم الفهارس الموجودة على `code` و `name` للبحث السريع
- تجنب جلب جميع البيانات دفعة واحدة للفروع الكثيرة
- استخدم `include_inactive=False` عند عدم الحاجة للفروع غير النشطة

### **2. التخزين المؤقت (Caching)**
```python
# مثال على استخدام التخزين المؤقت
@lru_cache(maxsize=100)
def get_branch_warehouses_cached(branch_id: int):
    return service.get_warehouses_for_branch(branch_id)
```

### **3. التحقق من البيانات**
```python
# تحقق من صحة البيانات قبل الحفظ
def validate_branch_data(data):
    if not data.get('name'):
        raise ValueError("اسم الفرع مطلوب")
    
    if not data.get('code'):
        raise ValueError("كود الفرع مطلوب")
    
    # تحقق من صيغة ساعات العمل
    if data.get('working_hours_start'):
        validate_time_format(data['working_hours_start'])
```

## 📊 مراقبة الأداء

### **استعلامات مفيدة للمراقبة:**
```sql
-- عدد الفروع النشطة
SELECT COUNT(*) FROM branches WHERE is_active = true;

-- عدد المستودعات لكل فرع
SELECT b.name, COUNT(bw.warehouse_id) as warehouse_count
FROM branches b
LEFT JOIN branch_warehouses bw ON b.id = bw.branch_id
GROUP BY b.id, b.name;

-- الفروع بدون مستودعات
SELECT b.name FROM branches b
LEFT JOIN branch_warehouses bw ON b.id = bw.branch_id
WHERE bw.branch_id IS NULL;
```

## 🚀 التطوير المستقبلي

### **ميزات مقترحة:**
1. **نظام الإشعارات** عند تغيير حالة الفروع
2. **تقارير الأداء** لكل فرع
3. **نظام الموافقات** للتغييرات الحساسة
4. **التكامل مع نظام GPS** لتحديد المواقع
5. **نظام التوزيع الذكي** المتقدم

## 🗄️ هيكل قاعدة البيانات

### **جدول الفروع (branches)**
```sql
CREATE TABLE branches (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    address TEXT,
    phone VARCHAR(20),
    manager_name VARCHAR(100),
    email VARCHAR(100),
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_main BOOLEAN NOT NULL DEFAULT false,
    city VARCHAR(50),
    region VARCHAR(50),
    postal_code VARCHAR(20),
    max_daily_sales INTEGER,
    working_hours_start VARCHAR(10),
    working_hours_end VARCHAR(10),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE,
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id)
);

-- الفهارس
CREATE INDEX idx_branches_code ON branches(code);
CREATE INDEX idx_branches_name ON branches(name);
CREATE INDEX idx_branches_is_active ON branches(is_active);
CREATE INDEX idx_branches_is_main ON branches(is_main);
CREATE INDEX idx_branches_city ON branches(city);
CREATE INDEX idx_branches_region ON branches(region);
```

### **جدول الربط (branch_warehouses)**
```sql
CREATE TABLE branch_warehouses (
    branch_id INTEGER NOT NULL REFERENCES branches(id) ON DELETE CASCADE,
    warehouse_id INTEGER NOT NULL REFERENCES warehouses(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_primary BOOLEAN DEFAULT false,
    priority INTEGER DEFAULT 1,
    PRIMARY KEY (branch_id, warehouse_id)
);

-- الفهارس
CREATE INDEX idx_branch_warehouses_branch_id ON branch_warehouses(branch_id);
CREATE INDEX idx_branch_warehouses_warehouse_id ON branch_warehouses(warehouse_id);
CREATE INDEX idx_branch_warehouses_is_primary ON branch_warehouses(is_primary);
CREATE INDEX idx_branch_warehouses_priority ON branch_warehouses(priority);
```

### **استعلامات مفيدة**
```sql
-- جلب جميع مستودعات فرع معين مع الأولوية
SELECT w.name, w.code, bw.is_primary, bw.priority
FROM warehouses w
INNER JOIN branch_warehouses bw ON w.id = bw.warehouse_id
WHERE bw.branch_id = 1
ORDER BY bw.is_primary DESC, bw.priority ASC;

-- جلب المستودع الأساسي لفرع معين
SELECT w.*
FROM warehouses w
INNER JOIN branch_warehouses bw ON w.id = bw.warehouse_id
WHERE bw.branch_id = 1 AND bw.is_primary = true;

-- إحصائيات الفروع والمستودعات
SELECT
    b.name as branch_name,
    COUNT(bw.warehouse_id) as warehouses_count,
    COUNT(CASE WHEN bw.is_primary THEN 1 END) as primary_warehouses
FROM branches b
LEFT JOIN branch_warehouses bw ON b.id = bw.branch_id
GROUP BY b.id, b.name;
```

## 🧪 اختبارات النظام

### **اختبارات الوحدة (Unit Tests)**
```python
# مثال على اختبار خدمة الفروع
def test_create_branch():
    db = TestingSessionLocal()
    service = get_branch_service(db)

    branch_data = {
        "name": "فرع اختبار",
        "code": "TEST-001",
        "is_active": True
    }

    result = service.create_branch(branch_data)

    assert result['success'] == True
    assert result['branch']['name'] == "فرع اختبار"
    assert result['branch']['code'] == "TEST-001"

    db.close()

def test_link_branch_warehouse():
    db = TestingSessionLocal()
    service = get_branch_warehouse_service(db)

    result = service.link_branch_to_warehouse(
        branch_id=1,
        warehouse_id=1,
        is_primary=True,
        priority=1
    )

    assert result['success'] == True

    db.close()
```

### **اختبارات التكامل (Integration Tests)**
```python
def test_branch_warehouse_integration():
    # إنشاء فرع
    branch_result = branch_service.create_branch(branch_data)
    branch_id = branch_result['branch']['id']

    # ربط الفرع بمستودع
    link_result = warehouse_service.link_branch_to_warehouse(
        branch_id, warehouse_id, True, 1
    )

    # التحقق من الربط
    warehouses_result = warehouse_service.get_warehouses_for_branch(branch_id)

    assert len(warehouses_result['warehouses']) == 1
    assert warehouses_result['warehouses'][0]['is_primary'] == True
```

---

> **📝 ملاحظة:** هذا الدليل يتم تحديثه باستمرار. تأكد من مراجعة أحدث إصدار قبل التطوير.
