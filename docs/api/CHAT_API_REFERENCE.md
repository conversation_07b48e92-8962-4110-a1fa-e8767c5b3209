# 🔌 مرجع API نظام المحادثة الفورية

## 📋 نظرة عامة

يوفر نظام المحادثة الفورية مجموعة شاملة من API endpoints لإدارة الرسائل والمحادثات والمستخدمين. جميع endpoints محمية بمصادقة JWT.

## 🔐 المصادقة

جميع requests تتطلب JWT token في header:
```
Authorization: Bearer <your_jwt_token>
```

## 📨 إدارة الرسائل

### إرسال رسالة جديدة
```http
POST /api/chat/send
Content-Type: application/json
Authorization: Bearer <token>

{
  "receiver_id": 2,
  "content": "مرحبا، كيف حالك؟",
  "message_type": "text"
}
```

**Response:**
```json
{
  "id": 1,
  "sender_id": 1,
  "receiver_id": 2,
  "content": "مرحبا، كيف حالك؟",
  "message_type": "text",
  "status": "sent",
  "is_edited": false,
  "created_at": "2024-12-07T10:30:00Z",
  "delivered_at": null,
  "read_at": null,
  "sender_username": "admin",
  "sender_full_name": "المدير"
}
```

### جلب الرسائل مع مستخدم
```http
GET /api/chat/messages/{user_id}?page=1&limit=50&before_message_id=100
Authorization: Bearer <token>
```

**Parameters:**
- `user_id` (path): معرف المستخدم الآخر
- `page` (query): رقم الصفحة (افتراضي: 1)
- `limit` (query): عدد الرسائل (افتراضي: 50، أقصى: 100)
- `before_message_id` (query): جلب الرسائل قبل هذه الرسالة

**Response:**
```json
{
  "messages": [
    {
      "id": 1,
      "sender_id": 1,
      "receiver_id": 2,
      "content": "مرحبا",
      "message_type": "text",
      "status": "read",
      "is_edited": false,
      "created_at": "2024-12-07T10:30:00Z",
      "delivered_at": "2024-12-07T10:30:01Z",
      "read_at": "2024-12-07T10:30:05Z",
      "sender_username": "admin",
      "sender_full_name": "المدير"
    }
  ],
  "total_count": 25,
  "has_more": true,
  "page": 1,
  "limit": 50
}
```

### تحديد الرسائل كمقروءة
```http
POST /api/chat/mark-as-read
Content-Type: application/json
Authorization: Bearer <token>

{
  "other_user_id": 2,
  "message_ids": [1, 2, 3]  // اختياري
}
```

**Response:**
```json
{
  "success": true,
  "updated_count": 3,
  "message": "تم تحديد 3 رسالة كمقروءة"
}
```

### حذف رسالة
```http
DELETE /api/chat/messages/{message_id}
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "message": "تم حذف الرسالة بنجاح"
}
```

### تعديل رسالة
```http
PUT /api/chat/messages/{message_id}?new_content=المحتوى الجديد
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "message": "تم تعديل الرسالة بنجاح",
  "updated_message": {
    "id": 1,
    "content": "المحتوى الجديد",
    "is_edited": true,
    "edited_at": "2024-12-07T10:35:00Z"
  }
}
```

## 💬 إدارة المحادثات

### جلب قائمة المحادثات
```http
GET /api/chat/conversations
Authorization: Bearer <token>
```

**Response:**
```json
[
  {
    "user_id": 2,
    "username": "user2",
    "full_name": "المستخدم الثاني",
    "is_online": true,
    "last_seen": "2024-12-07T10:30:00Z",
    "last_message": {
      "id": 5,
      "sender_id": 2,
      "receiver_id": 1,
      "content": "شكراً لك",
      "message_type": "text",
      "status": "delivered",
      "created_at": "2024-12-07T10:25:00Z"
    },
    "unread_count": 2
  }
]
```

### عدد الرسائل غير المقروءة
```http
GET /api/chat/unread-count
Authorization: Bearer <token>
```

**Response:**
```json
{
  "unread_count": 5
}
```

## 🔍 البحث

### البحث في الرسائل
```http
GET /api/chat/search?query=مرحبا&limit=20
Authorization: Bearer <token>
```

**Parameters:**
- `query` (required): نص البحث
- `limit` (optional): عدد النتائج (افتراضي: 20، أقصى: 50)

**Response:**
```json
{
  "messages": [
    {
      "id": 1,
      "sender_id": 1,
      "receiver_id": 2,
      "content": "مرحبا، كيف حالك؟",
      "message_type": "text",
      "status": "read",
      "created_at": "2024-12-07T10:30:00Z",
      "sender_username": "admin",
      "sender_full_name": "المدير"
    }
  ],
  "query": "مرحبا",
  "count": 1
}
```

### البحث عن المستخدمين
```http
GET /api/chat/users/search?query=أحمد&limit=10
Authorization: Bearer <token>
```

**Parameters:**
- `query` (required): نص البحث (اسم المستخدم أو الاسم الكامل)
- `limit` (optional): عدد النتائج (افتراضي: 10، أقصى: 50)

**Response:**
```json
{
  "users": [
    {
      "user_id": 3,
      "username": "ahmed",
      "full_name": "أحمد محمد",
      "is_online": false,
      "last_seen": "2024-12-07T09:15:00Z"
    }
  ],
  "total_count": 1
}
```

## 👥 إدارة المستخدمين

### المستخدمون المتصلون
```http
GET /api/chat/users/online
Authorization: Bearer <token>
```

**Response:**
```json
[
  {
    "user_id": 2,
    "username": "user2",
    "full_name": "المستخدم الثاني",
    "is_online": true,
    "last_seen": "2024-12-07T10:30:00Z"
  }
]
```

### حالة نظام المحادثة
```http
GET /api/chat/status
Authorization: Bearer <token>
```

**Response:**
```json
{
  "user_id": 1,
  "username": "admin",
  "is_online": false,
  "total_online_users": 3,
  "online_users": [2, 3, 4]
}
```

## 🔌 WebSocket API

### الاتصال
```javascript
const ws = new WebSocket('ws://localhost:8002/ws/chat/{user_id}');
```

### رسائل WebSocket

#### رسالة الاتصال
```json
{
  "type": "connection_established",
  "user_id": 1,
  "username": "admin",
  "timestamp": "2024-12-07T10:30:00Z"
}
```

#### رسالة جديدة
```json
{
  "type": "new_message",
  "message": {
    "id": 1,
    "sender_id": 2,
    "receiver_id": 1,
    "content": "مرحبا",
    "message_type": "text",
    "status": "delivered",
    "created_at": "2024-12-07T10:30:00Z",
    "sender_username": "user2",
    "sender_full_name": "المستخدم الثاني"
  },
  "timestamp": "2024-12-07T10:30:00Z"
}
```

#### تغيير حالة المستخدم
```json
{
  "type": "user_status_change",
  "user_id": 2,
  "username": "user2",
  "full_name": "المستخدم الثاني",
  "is_online": true,
  "timestamp": "2024-12-07T10:30:00Z"
}
```

#### إشعار الكتابة
```json
{
  "type": "user_typing",
  "user_id": 2,
  "timestamp": "2024-12-07T10:30:00Z"
}
```

#### توقف الكتابة
```json
{
  "type": "user_stop_typing",
  "user_id": 2,
  "timestamp": "2024-12-07T10:30:00Z"
}
```

#### قراءة الرسائل
```json
{
  "type": "messages_read",
  "reader_id": 1,
  "count": 3,
  "timestamp": "2024-12-07T10:30:00Z"
}
```

#### حذف رسالة
```json
{
  "type": "message_deleted",
  "message_id": 1,
  "sender_id": 1,
  "timestamp": "2024-12-07T10:30:00Z"
}
```

#### تعديل رسالة
```json
{
  "type": "message_edited",
  "message": {
    "id": 1,
    "content": "المحتوى الجديد",
    "is_edited": true,
    "edited_at": "2024-12-07T10:35:00Z"
  },
  "timestamp": "2024-12-07T10:35:00Z"
}
```

### إرسال رسائل WebSocket

#### Heartbeat (Ping)
```json
{
  "type": "ping",
  "timestamp": "2024-12-07T10:30:00Z"
}
```

#### إشعار الكتابة
```json
{
  "type": "typing",
  "receiver_id": 2,
  "timestamp": "2024-12-07T10:30:00Z"
}
```

#### توقف الكتابة
```json
{
  "type": "stop_typing",
  "receiver_id": 2,
  "timestamp": "2024-12-07T10:30:00Z"
}
```

## ❌ رموز الأخطاء

### HTTP Status Codes
- `200`: نجح الطلب
- `400`: خطأ في البيانات المرسلة
- `401`: غير مصرح (token غير صحيح)
- `403`: ممنوع (لا توجد صلاحية)
- `404`: غير موجود
- `500`: خطأ في الخادم

### أمثلة على الأخطاء

#### خطأ في المصادقة
```json
{
  "detail": "Could not validate credentials"
}
```

#### مستخدم غير موجود
```json
{
  "detail": "المستخدم غير موجود"
}
```

#### رسالة لنفس المستخدم
```json
{
  "detail": "لا يمكن إرسال رسالة لنفسك"
}
```

#### رسالة غير موجودة
```json
{
  "detail": "الرسالة غير موجودة"
}
```

#### عدم وجود صلاحية
```json
{
  "detail": "غير مصرح لك بحذف هذه الرسالة"
}
```

## 📝 ملاحظات مهمة

### حدود الاستخدام
- **طول الرسالة**: أقصى 5000 حرف
- **عدد الرسائل في الصفحة**: أقصى 100 رسالة
- **عدد نتائج البحث**: أقصى 50 نتيجة

### أفضل الممارسات
1. **استخدم pagination** لجلب الرسائل
2. **أغلق WebSocket** عند عدم الحاجة
3. **تعامل مع أخطاء الشبكة** بشكل مناسب
4. **استخدم debouncing** لإشعارات الكتابة

### الأمان
- جميع endpoints محمية بـ JWT
- المستخدمون يمكنهم فقط رؤية رسائلهم
- تنظيف محتوى الرسائل من الأكواد الضارة

---

**آخر تحديث**: ديسمبر 2024  
**الإصدار**: 1.0.0
