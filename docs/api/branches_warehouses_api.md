# 🔌 API Documentation - نظام الفروع والمستودعات

> **تاريخ الإنشاء:** 2025-01-11  
> **الإصدار:** 1.0  
> **Base URL:** `http://localhost:8000`  

## 🔐 المصادقة

جميع endpoints تتطلب مصادقة عبر JWT Token:
```http
Authorization: Bearer <your_jwt_token>
```

## 🏢 API الفروع (Branches)

### **1. إنشاء فرع جديد**
```http
POST /api/branches/
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "فرع طرابلس الوسط",
  "code": "TRP-001",
  "address": "شارع الجمهورية، طرابلس",
  "phone": "021-1234567",
  "manager_name": "أحمد محمد",
  "email": "<EMAIL>",
  "is_active": true,
  "is_main": false,
  "city": "طرابلس",
  "region": "طرابلس",
  "postal_code": "12345",
  "max_daily_sales": 50000,
  "working_hours_start": "08:00",
  "working_hours_end": "18:00"
}
```

**Response (201):**
```json
{
  "success": true,
  "message": "تم إنشاء الفرع بنجاح",
  "branch": {
    "id": 1,
    "name": "فرع طرابلس الوسط",
    "code": "TRP-001",
    "address": "شارع الجمهورية، طرابلس",
    "phone": "021-1234567",
    "manager_name": "أحمد محمد",
    "email": "<EMAIL>",
    "is_active": true,
    "is_main": false,
    "city": "طرابلس",
    "region": "طرابلس",
    "postal_code": "12345",
    "max_daily_sales": 50000,
    "working_hours_start": "08:00",
    "working_hours_end": "18:00",
    "created_at": "2025-01-11T10:30:00Z",
    "updated_at": "2025-01-11T10:30:00Z"
  }
}
```

### **2. جلب جميع الفروع**
```http
GET /api/branches/?include_inactive=false
```

**Query Parameters:**
- `include_inactive` (boolean, optional): تضمين الفروع غير النشطة (default: false)

**Response (200):**
```json
{
  "success": true,
  "branches": [
    {
      "id": 1,
      "name": "فرع طرابلس الوسط",
      "code": "TRP-001",
      "address": "شارع الجمهورية، طرابلس",
      "phone": "021-1234567",
      "manager_name": "أحمد محمد",
      "email": "<EMAIL>",
      "is_active": true,
      "is_main": false,
      "city": "طرابلس",
      "region": "طرابلس",
      "warehouses_count": 2,
      "active_warehouses_count": 2,
      "created_at": "2025-01-11T10:30:00Z",
      "updated_at": "2025-01-11T10:30:00Z"
    }
  ],
  "total_count": 1
}
```

### **3. جلب فرع بالمعرف**
```http
GET /api/branches/{branch_id}
```

**Response (200):**
```json
{
  "success": true,
  "branch": {
    "id": 1,
    "name": "فرع طرابلس الوسط",
    "code": "TRP-001",
    // ... باقي البيانات
    "warehouses": [
      {
        "id": 1,
        "name": "مستودع الرئيسي",
        "code": "MAIN-001",
        "is_main": true,
        "is_active": true,
        "is_primary": true,
        "priority": 1,
        "capacity_limit": 1000.0,
        "current_capacity": 750.0,
        "capacity_percentage": 75.0,
        "link_created_at": "2025-01-11T10:30:00Z"
      }
    ]
  }
}
```

### **4. جلب فرع بالكود**
```http
GET /api/branches/code/{branch_code}
```

### **5. تحديث الفرع**
```http
PUT /api/branches/{branch_id}
Content-Type: application/json
```

**Request Body:** (جميع الحقول اختيارية)
```json
{
  "name": "فرع طرابلس الجديد",
  "phone": "021-7654321",
  "manager_name": "محمد أحمد"
}
```

### **6. حذف الفرع**
```http
DELETE /api/branches/{branch_id}
```

**Response (200):**
```json
{
  "success": true,
  "message": "تم حذف الفرع بنجاح"
}
```

### **7. تعيين فرع رئيسي**
```http
POST /api/branches/{branch_id}/set-main
```

### **8. تبديل حالة النشاط**
```http
POST /api/branches/{branch_id}/toggle-status
```

### **9. البحث في الفروع**
```http
GET /api/branches/search/?search_term=طرابلس&include_inactive=false
```

**Query Parameters:**
- `search_term` (string, required): مصطلح البحث
- `include_inactive` (boolean, optional): تضمين الفروع غير النشطة

## 🔗 API العلاقات (Branch-Warehouse Relations)

### **1. ربط فرع بمستودع**
```http
POST /api/branch-warehouses/link
Content-Type: application/json
```

**Request Body:**
```json
{
  "branch_id": 1,
  "warehouse_id": 2,
  "is_primary": true,
  "priority": 1
}
```

**Response (201):**
```json
{
  "success": true,
  "message": "تم ربط الفرع فرع طرابلس الوسط بالمستودع مستودع الظهرة",
  "link": {
    "branch_id": 1,
    "warehouse_id": 2,
    "is_primary": true,
    "priority": 1
  }
}
```

### **2. إلغاء ربط فرع من مستودع**
```http
DELETE /api/branch-warehouses/unlink?branch_id=1&warehouse_id=2
```

### **3. تعيين مستودع أساسي للفرع**
```http
POST /api/branch-warehouses/set-primary?branch_id=1&warehouse_id=2
```

### **4. تحديث أولوية مستودع للفرع**
```http
PUT /api/branch-warehouses/update-priority?branch_id=1&warehouse_id=2&priority=3
```

### **5. جلب مستودعات الفرع**
```http
GET /api/branch-warehouses/branch/{branch_id}/warehouses?include_inactive=false
```

**Response (200):**
```json
{
  "success": true,
  "warehouses": [
    {
      "id": 1,
      "name": "مستودع الرئيسي",
      "code": "MAIN-001",
      "address": "شارع الصناعة",
      "is_main": true,
      "is_active": true,
      "capacity_limit": 1000.0,
      "current_capacity": 750.0,
      "capacity_percentage": 75.0,
      "is_primary": true,
      "priority": 1,
      "link_created_at": "2025-01-11T10:30:00Z"
    }
  ],
  "total_count": 1
}
```

### **6. جلب فروع المستودع**
```http
GET /api/branch-warehouses/warehouse/{warehouse_id}/branches?include_inactive=false
```

### **7. جلب المستودع الأساسي للفرع**
```http
GET /api/branch-warehouses/branch/{branch_id}/primary-warehouse
```

### **8. جلب المستودعات المتاحة للفرع**
```http
GET /api/branch-warehouses/branch/{branch_id}/available-warehouses
```

### **9. جلب الفروع المتاحة للمستودع**
```http
GET /api/branch-warehouses/warehouse/{warehouse_id}/available-branches
```

## 📝 نماذج البيانات (Data Models)

### **Branch Model**
```json
{
  "id": "integer",
  "name": "string (max: 100)",
  "code": "string (max: 20, unique)",
  "address": "string (optional)",
  "phone": "string (max: 20, optional)",
  "manager_name": "string (max: 100, optional)",
  "email": "string (max: 100, optional)",
  "is_active": "boolean (default: true)",
  "is_main": "boolean (default: false)",
  "city": "string (max: 50, optional)",
  "region": "string (max: 50, optional)",
  "postal_code": "string (max: 20, optional)",
  "max_daily_sales": "integer (optional)",
  "working_hours_start": "string (format: HH:MM, optional)",
  "working_hours_end": "string (format: HH:MM, optional)",
  "created_at": "datetime",
  "updated_at": "datetime",
  "created_by": "integer (optional)",
  "updated_by": "integer (optional)"
}
```

### **Branch-Warehouse Link Model**
```json
{
  "branch_id": "integer",
  "warehouse_id": "integer",
  "is_primary": "boolean (default: false)",
  "priority": "integer (1-100, default: 1)",
  "created_at": "datetime"
}
```

## ⚠️ رموز الأخطاء (Error Codes)

### **400 Bad Request**
```json
{
  "success": false,
  "error": "يوجد فرع بنفس الكود: TRP-001"
}
```

### **404 Not Found**
```json
{
  "success": false,
  "error": "الفرع غير موجود"
}
```

### **403 Forbidden**
```json
{
  "detail": "ليس لديك صلاحية لإدارة الفروع"
}
```

### **422 Validation Error**
```json
{
  "detail": [
    {
      "loc": ["body", "name"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

## 🔍 أمثلة عملية

### **مثال 1: إنشاء فرع وربطه بمستودعين**
```bash
# 1. إنشاء الفرع
curl -X POST "http://localhost:8000/api/branches/" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "فرع الزاوية",
    "code": "ZAW-001",
    "address": "شارع الجمهورية، الزاوية",
    "city": "الزاوية",
    "is_active": true
  }'

# 2. ربط الفرع بالمستودع الأول (أساسي)
curl -X POST "http://localhost:8000/api/branch-warehouses/link" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "branch_id": 2,
    "warehouse_id": 1,
    "is_primary": true,
    "priority": 1
  }'

# 3. ربط الفرع بالمستودع الثاني (بديل)
curl -X POST "http://localhost:8000/api/branch-warehouses/link" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "branch_id": 2,
    "warehouse_id": 3,
    "is_primary": false,
    "priority": 2
  }'
```

### **مثال 2: البحث عن فروع في مدينة معينة**
```bash
curl -X GET "http://localhost:8000/api/branches/search/?search_term=طرابلس" \
  -H "Authorization: Bearer $TOKEN"
```

### **مثال 3: جلب جميع مستودعات فرع معين**
```bash
curl -X GET "http://localhost:8000/api/branch-warehouses/branch/1/warehouses" \
  -H "Authorization: Bearer $TOKEN"
```

## 📊 معدلات الاستخدام (Rate Limits)

- **الحد الأقصى**: 1000 طلب/ساعة لكل مستخدم
- **طلبات البحث**: 100 طلب/دقيقة
- **طلبات التحديث**: 50 طلب/دقيقة

## 🔄 إصدارات API

- **الإصدار الحالي**: v1
- **التوافق**: يتم الحفاظ على التوافق العكسي
- **التحديثات**: يتم الإعلان عن التغييرات مسبقاً

---

> **📝 ملاحظة:** هذا التوثيق يغطي الإصدار الحالي من API. للحصول على أحدث التحديثات، راجع `/docs` في الخادم.
