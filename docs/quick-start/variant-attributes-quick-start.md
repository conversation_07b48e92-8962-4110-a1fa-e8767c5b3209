# 🚀 دليل البدء السريع - نظام خصائص المتغيرات

## ⚡ البدء السريع

### 1. تشغيل النظام
```bash
# Backend
cd backend
source venv/bin/activate
uvicorn main:app --host 0.0.0.0 --port 8001 --reload

# Frontend
cd frontend
npm start
```

### 2. الوصول للنظام
1. افتح المتصفح على `http://localhost:3000`
2. سجل الدخول
3. اذهب إلى **إدارة الكتالوج**
4. انقر على تبويب **"خصائص المتغيرات"**

## 📁 هيكل الملفات

### Backend
```
backend/
├── models/
│   ├── variant_attribute.py     # نموذج الخصائص
│   └── variant_value.py         # نموذج القيم
├── services/
│   └── variant_attribute_service.py  # خدمة إدارة الخصائص
├── routers/
│   └── variant_attributes.py    # مسارات API
├── schemas/
│   └── variant_attribute.py     # مخططات البيانات
└── database/migrations/
    └── create_variant_attributes.sql  # Migration script
```

### Frontend
```
frontend/src/
├── types/
│   └── variantAttribute.ts      # تعريفات TypeScript
├── services/
│   └── variantAttributeService.ts  # خدمة API
├── stores/
│   └── variantAttributeStore.ts # إدارة الحالة
└── components/catalog/
    ├── VariantAttributesTab.tsx
    ├── VariantAttributesDataTable.tsx
    └── VariantAttributeModal.tsx
```

## 🔧 الاستخدام السريع

### إنشاء خاصية جديدة
```typescript
// استخدام الخدمة مباشرة
import { variantAttributeService } from '../services/variantAttributeService';

const createNewAttribute = async () => {
  try {
    const newAttribute = await variantAttributeService.createAttribute({
      name: 'Brand',
      name_ar: 'العلامة التجارية',
      attribute_type: 'list',
      values: [
        { value: 'Nike', value_ar: 'نايك', sort_order: 1 },
        { value: 'Adidas', value_ar: 'أديداس', sort_order: 2 }
      ]
    });
    console.log('تم إنشاء الخاصية:', newAttribute);
  } catch (error) {
    console.error('خطأ في إنشاء الخاصية:', error);
  }
};
```

### استخدام Store
```typescript
// استخدام Zustand store
import useVariantAttributeStore from '../stores/variantAttributeStore';

const MyComponent = () => {
  const { 
    attributes, 
    loading, 
    error, 
    fetchAttributes, 
    createAttribute 
  } = useVariantAttributeStore();

  useEffect(() => {
    fetchAttributes();
  }, []);

  const handleCreate = async (data) => {
    try {
      await createAttribute(data);
      // سيتم تحديث الحالة تلقائياً
    } catch (error) {
      console.error('خطأ:', error);
    }
  };

  if (loading) return <div>جاري التحميل...</div>;
  if (error) return <div>خطأ: {error}</div>;

  return (
    <div>
      {attributes.map(attr => (
        <div key={attr.id}>{attr.name_ar}</div>
      ))}
    </div>
  );
};
```

## 🔌 API Examples

### جلب جميع الخصائص
```bash
curl -X GET "http://localhost:8001/api/variant-attributes/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### إنشاء خاصية جديدة
```bash
curl -X POST "http://localhost:8001/api/variant-attributes/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "Size",
    "name_ar": "الحجم",
    "attribute_type": "list",
    "values": [
      {"value": "S", "value_ar": "صغير", "sort_order": 1},
      {"value": "M", "value_ar": "متوسط", "sort_order": 2},
      {"value": "L", "value_ar": "كبير", "sort_order": 3}
    ]
  }'
```

### إضافة قيمة لخاصية موجودة
```bash
curl -X POST "http://localhost:8001/api/variant-attributes/1/values" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "value": "XL",
    "value_ar": "كبير جداً",
    "sort_order": 4
  }'
```

## 🎨 أمثلة UI Components

### استخدام VariantAttributesDataTable
```typescript
import { VariantAttributesTab } from '../components/catalog';

const CatalogPage = () => {
  return (
    <div>
      <h1>إدارة الكتالوج</h1>
      <VariantAttributesTab />
    </div>
  );
};
```

### استخدام VariantAttributeModal
```typescript
import VariantAttributeModal from '../components/catalog/VariantAttributeModal';

const MyComponent = () => {
  const [modalOpen, setModalOpen] = useState(false);
  const [editAttribute, setEditAttribute] = useState(null);

  return (
    <>
      <button onClick={() => setModalOpen(true)}>
        إضافة خاصية جديدة
      </button>
      
      <VariantAttributeModal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        mode="create"
        attribute={editAttribute}
        onSuccess={(message) => {
          console.log(message);
          setModalOpen(false);
        }}
      />
    </>
  );
};
```

## 🔍 أنواع الخصائص

### 1. نص (text)
```typescript
{
  name: 'Description',
  name_ar: 'الوصف',
  attribute_type: 'text'
  // لا تحتاج قيم محددة مسبقاً
}
```

### 2. لون (color)
```typescript
{
  name: 'Color',
  name_ar: 'اللون',
  attribute_type: 'color',
  values: [
    { value: 'Red', value_ar: 'أحمر', color_code: '#FF0000' },
    { value: 'Blue', value_ar: 'أزرق', color_code: '#0000FF' }
  ]
}
```

### 3. قائمة (list)
```typescript
{
  name: 'Size',
  name_ar: 'الحجم',
  attribute_type: 'list',
  values: [
    { value: 'S', value_ar: 'صغير' },
    { value: 'M', value_ar: 'متوسط' },
    { value: 'L', value_ar: 'كبير' }
  ]
}
```

### 4. رقم (number)
```typescript
{
  name: 'Weight',
  name_ar: 'الوزن',
  attribute_type: 'number'
  // يمكن إضافة قيم محددة أو تركها فارغة
}
```

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في تشغيل الخادم
```bash
# تأكد من تفعيل البيئة الافتراضية
source venv/bin/activate

# تأكد من تثبيت المتطلبات
pip install -r requirements.txt

# تشغيل على منفذ مختلف إذا كان 8000 مستخدم
uvicorn main:app --host 0.0.0.0 --port 8001 --reload
```

#### 2. خطأ في قاعدة البيانات
```python
# تشغيل migration يدوياً
from database.session import engine
from models.variant_attribute import VariantAttribute
from models.variant_value import VariantValue
from database.base import Base

Base.metadata.create_all(bind=engine)
```

#### 3. خطأ في المصادقة
```typescript
// تأكد من وجود token صحيح
const token = localStorage.getItem('access_token');
if (!token) {
  // إعادة توجيه لصفحة تسجيل الدخول
  window.location.href = '/login';
}
```

#### 4. خطأ في استيراد المكونات
```typescript
// تأكد من المسارات الصحيحة
import { VariantAttributesTab } from '../components/catalog';
// أو
import VariantAttributesTab from '../components/catalog/VariantAttributesTab';
```

## 📊 البيانات الافتراضية

### تشغيل البيانات الافتراضية
```python
# في Python console
from database.session import engine, get_db
from sqlalchemy import text

# قراءة وتنفيذ migration
with open('database/migrations/create_variant_attributes.sql', 'r') as f:
    sql = f.read()

with engine.connect() as conn:
    for statement in sql.split(';'):
        if statement.strip():
            conn.execute(text(statement))
            conn.commit()
```

### التحقق من البيانات
```sql
-- التحقق من الخصائص
SELECT * FROM variant_attributes;

-- التحقق من القيم
SELECT va.name_ar, vv.value_ar, vv.color_code 
FROM variant_attributes va 
JOIN variant_values vv ON va.id = vv.attribute_id 
ORDER BY va.sort_order, vv.sort_order;
```

## 🔄 التحديثات المستقبلية

### إضافة خاصية جديدة
1. أضف النوع الجديد في `ATTRIBUTE_TYPES`
2. حدث validation في schemas
3. أضف معالجة في UI components
4. اختبر النوع الجديد

### ربط بالمنتجات
```typescript
// مثال على الربط المستقبلي
interface ProductVariant {
  id: number;
  product_id: number;
  attributes: {
    [attribute_id: number]: {
      value_id: number;
      custom_value?: string;
    }
  };
  sku: string;
  price: number;
  stock: number;
}
```

## 📞 الدعم

### للمساعدة
1. راجع [الدليل الشامل](../development/variant-attributes-complete-guide.md)
2. تحقق من [ذاكرة النظام](../../SYSTEM_MEMORY.md)
3. راجع [قواعد النظام](../../.augment/rules/smartpos_rules.md)

### تقرير الأخطاء
- تأكد من تضمين رسالة الخطأ كاملة
- أرفق معلومات البيئة (OS, Browser, Node version)
- وصف خطوات إعادة إنتاج المشكلة

---

**تاريخ الإنشاء**: 2025-01-27  
**الإصدار**: 1.0.0  
**آخر تحديث**: 2025-01-27
