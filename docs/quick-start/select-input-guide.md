# 📋 دليل استخدام مكونات القائمة المنسدلة

> **🎯 الهدف**: اختيار المكون المناسب لكل حالة استخدام

## 🔍 المكونات المتاحة

### 1. **SelectInput** - الاستخدام العادي
```typescript
import { SelectInput } from '../inputs';

<SelectInput
  label="الفئة"
  name="category"
  value={value}
  onChange={setValue}
  options={options}
/>
```

**متى تستخدمه:**
- ✅ في الصفحات العادية
- ✅ في الفلاتر والبحث
- ✅ خارج النوافذ المنبثقة
- ❌ داخل النوافذ المنبثقة

---

### 2. **ModalSelectInput** - للنوافذ المنبثقة البسيطة
```typescript
import { ModalSelectInput } from '../inputs';

<ModalSelectInput
  label="نوع الخاصية"
  name="attribute_type"
  value={value}
  onChange={setValue}
  options={options}
  required
  searchable={true}
/>
```

**متى تستخدمه:**
- ✅ في النوافذ المنبثقة الصغيرة
- ✅ عندما تكون القائمة قصيرة
- ✅ في النماذج البسيطة
- ❌ عندما تحتاج القائمة للظهور خارج النافذة

**المميزات:**
- 🚀 أداء سريع
- 🎨 تصميم مدمج
- 🔧 بساطة في الاستخدام

---

### 3. **SmartSelectInput** - للحالات المعقدة
```typescript
import { SmartSelectInput } from '../inputs';

<SmartSelectInput
  label="الوحدة الأساسية"
  name="base-unit"
  value={value}
  onChange={setValue}
  options={options}
  searchable={true}
  forcePortal={false} // اختياري: إجبار استخدام Portal
/>
```

**متى تستخدمه:**
- ✅ في النوافذ المنبثقة الكبيرة
- ✅ عندما تكون القائمة طويلة
- ✅ عندما تحتاج مرونة في العرض
- ✅ في الحالات المعقدة

**المميزات:**
- 🧠 **ذكي تلقائياً** - يحدد الطريقة الأمثل للعرض
- 🔄 **متكيف** - يتغير حسب الموضع والمساحة
- 🎯 **دقيق** - يحسب المواضع بدقة
- 🛡️ **آمن** - يتعامل مع جميع الحالات

---

## 🎯 دليل الاختيار السريع

### **للاستخدام العادي:**
```typescript
// في الصفحات العادية
<SelectInput ... />
```

### **للنوافذ المنبثقة البسيطة:**
```typescript
// نوافذ صغيرة، قوائم قصيرة
<ModalSelectInput ... />
```

### **للحالات المعقدة:**
```typescript
// نوافذ كبيرة، قوائم طويلة، حاجة للمرونة
<SmartSelectInput ... />
```

---

## 🔧 الخصائص المشتركة

جميع المكونات تدعم نفس الخصائص:

```typescript
interface CommonProps {
  label?: string;           // تسمية الحقل
  name: string;            // اسم الحقل
  value: string;           // القيمة المختارة
  onChange: (value: string) => void; // دالة التغيير
  options: Option[];       // قائمة الخيارات
  placeholder?: string;    // النص التوضيحي
  required?: boolean;      // هل الحقل مطلوب
  disabled?: boolean;      // هل الحقل معطل
  error?: string;          // رسالة خطأ
  success?: string;        // رسالة نجاح
  icon?: React.ReactNode;  // أيقونة
  searchable?: boolean;    // إمكانية البحث
  clearable?: boolean;     // إمكانية المسح
}
```

---

## 📝 أمثلة عملية

### **مثال 1: فلتر في صفحة عادية**
```typescript
<SelectInput
  label="حالة المنتج"
  name="status"
  value={filters.status}
  onChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
  options={[
    { value: 'all', label: 'جميع الحالات' },
    { value: 'active', label: 'نشط' },
    { value: 'inactive', label: 'غير نشط' }
  ]}
/>
```

### **مثال 2: نافذة إضافة عنصر بسيط**
```typescript
<ModalSelectInput
  label="نوع العنصر"
  name="type"
  value={formData.type}
  onChange={(value) => setFormData(prev => ({ ...prev, type: value }))}
  options={typeOptions}
  required
/>
```

### **مثال 3: نافذة معقدة مع قائمة طويلة**
```typescript
<SmartSelectInput
  label="اختر من قائمة طويلة"
  name="long-list"
  value={formData.selection}
  onChange={(value) => setFormData(prev => ({ ...prev, selection: value }))}
  options={longListOptions}
  searchable={true}
/>
```

---

## 🚨 نصائح مهمة

### ✅ **افعل:**
- استخدم المكون المناسب لكل حالة
- اختبر القائمة في أحجام نوافذ مختلفة
- استخدم البحث للقوائم الطويلة
- أضف رسائل خطأ واضحة

### ❌ **لا تفعل:**
- لا تستخدم SelectInput في النوافذ المنبثقة
- لا تستخدم SmartSelectInput للحالات البسيطة
- لا تنس إضافة required للحقول المطلوبة
- لا تجعل القوائم طويلة جداً بدون بحث

---

## 🧪 اختبار المكونات

### **قائمة التحقق:**
- [ ] القائمة تظهر في الموضع الصحيح
- [ ] الاختيار يعمل بشكل صحيح
- [ ] البحث يعمل (إذا كان مفعلاً)
- [ ] رسائل الخطأ تظهر بوضوح
- [ ] التصميم متسق مع باقي النظام
- [ ] يعمل في الوضع المظلم والمضيء
- [ ] يتكيف مع أحجام الشاشات المختلفة

---

**آخر تحديث**: 27 يوليو 2025  
**المطور**: AI Agent - SmartPOS System
