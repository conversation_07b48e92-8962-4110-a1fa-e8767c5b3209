# 🚀 دليل سريع - نظام إدارة الضرائب

> **للمطورين الجدد**: دليل سريع لفهم واستخدام نظام إدارة الضرائب الجديد

## 📋 نظرة عامة سريعة

نظام إدارة الضرائب المتقدم يسمح بإدارة شاملة للضرائب من خلال:
- **أنواع الضرائب** (Tax Types): تصنيف رئيسي للضرائب
- **القيم الضريبية** (Tax Rates): النسب والقيم المحددة لكل نوع
- **فئات متعددة**: معيارية، مخفضة، صفر، معفاة
- **طرق حساب متنوعة**: نسبة مئوية أو مبلغ ثابت

## 🎯 الوصول السريع

### الروابط:
- `/tax-types` - إدارة أنواع الضرائب

### من القائمة الجانبية:
انتقل إلى "إدارة الفهرس" ← "أنواع الضرائب"

## 🔧 للمطورين

### API Endpoints الرئيسية:

```javascript
// أنواع الضرائب
GET    /api/tax-types/                    // جلب جميع أنواع الضرائب
POST   /api/tax-types/                    // إنشاء نوع ضريبة جديد
PUT    /api/tax-types/{id}                // تحديث نوع ضريبة
DELETE /api/tax-types/{id}                // حذف نوع ضريبة
GET    /api/tax-types/statistics          // إحصائيات أنواع الضرائب

// القيم الضريبية
GET    /api/tax-rates/                    // جلب جميع القيم الضريبية
POST   /api/tax-rates/                    // إنشاء قيمة ضريبية جديدة
PUT    /api/tax-rates/{id}                // تحديث قيمة ضريبية
DELETE /api/tax-rates/{id}                // حذف قيمة ضريبية
POST   /api/tax-rates/calculate           // حساب الضريبة
```

### استخدام Stores:

```typescript
// استيراد المخازن
import useTaxTypeStore from '../stores/taxTypeStore';
import useTaxRateStore from '../stores/taxRateStore';

// في المكون
const { taxTypes, loading, fetchTaxTypes, createTaxType } = useTaxTypeStore();
const { taxRates, calculateTax } = useTaxRateStore();

// جلب البيانات
useEffect(() => {
  fetchTaxTypes();
}, []);

// إنشاء نوع ضريبة جديد
const newTaxType = await createTaxType({
  name: 'VAT',
  name_ar: 'ضريبة القيمة المضافة',
  tax_category: 'standard',
  calculation_method: 'percentage',
  is_active: true
});
```

### استخدام المكونات:

```typescript
// استيراد التبويب
import { TaxTypesTab } from '../components/catalog';

// في الصفحة
const CatalogPage = () => {
  return (
    <div>
      <TaxTypesTab />
    </div>
  );
};
```

## 🏗️ هيكل الملفات

```
backend/
├── models/
│   ├── tax_type.py              # نموذج أنواع الضرائب
│   └── tax_rate.py              # نموذج القيم الضريبية
├── services/tax/
│   ├── tax_type_service.py      # خدمة أنواع الضرائب
│   └── tax_rate_service.py      # خدمة القيم الضريبية
├── routers/tax/
│   ├── tax_types_router.py      # API أنواع الضرائب
│   └── tax_rates_router.py      # API القيم الضريبية
├── schemas/
│   └── tax.py                   # Schemas للتحقق من البيانات
└── migrations/
    └── create_tax_tables.py     # Migration جداول الضرائب

frontend/src/
├── components/catalog/
│   ├── TaxTypesTab.tsx          # تبويب أنواع الضرائب
│   └── TaxTypesDataTable.tsx    # جدول البيانات التفاعلي
├── stores/
│   ├── taxTypeStore.ts          # مخزن أنواع الضرائب
│   └── taxRateStore.ts          # مخزن القيم الضريبية
└── pages/
    └── CatalogManagement.tsx    # صفحة إدارة الفهرس (محدثة)
```

## 🚀 التثبيت السريع

### 1. تشغيل Migration:
```bash
cd backend
python run_tax_migration.py
```

### 2. إعادة تشغيل النظام:
```bash
# إيقاف النظام الحالي (Ctrl+C)
# ثم إعادة التشغيل
./start_system.sh
```

### 3. الوصول للنظام:
- افتح المتصفح على `http://localhost:5175`
- انتقل إلى "إدارة الفهرس"
- اختر تبويب "أنواع الضرائب"

## 📊 البيانات الافتراضية

سيتم إنشاء البيانات التالية تلقائياً:

### أنواع الضرائب:
1. **ضريبة القيمة المضافة** - معيارية (15%)
2. **ضريبة الخدمات** - معيارية (5%)
3. **ضريبة مخفضة** - مخفضة (5%)
4. **معدل صفر** - صفر (0%)
5. **معفى من الضريبة** - معفاة (0%)

## 🔍 اختبار سريع

### اختبار API:
```bash
# جلب أنواع الضرائب
curl -X GET "http://localhost:8002/api/tax-types/" \
  -H "Authorization: Bearer YOUR_TOKEN"

# إنشاء نوع ضريبة جديد
curl -X POST "http://localhost:8002/api/tax-types/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "Custom Tax",
    "name_ar": "ضريبة مخصصة",
    "tax_category": "standard",
    "calculation_method": "percentage",
    "is_active": true
  }'
```

### اختبار الواجهة:
1. انتقل إلى صفحة أنواع الضرائب
2. تأكد من عرض البيانات الافتراضية
3. جرب إضافة نوع ضريبة جديد
4. اختبر البحث والفلترة
5. جرب تعديل وحذف البيانات

## 🐛 مشاكل شائعة

### المشكلة: لا يظهر تبويب الضرائب
**الحل**: تأكد من إضافة المسار في `AppContent.tsx`

### المشكلة: خطأ في تحميل البيانات
**الحل**: تأكد من تشغيل migration وإعادة تشغيل الخادم

### المشكلة: خطأ في الصلاحيات
**الحل**: تأكد من تسجيل الدخول بحساب مدير

## 📚 موارد إضافية

- [التوثيق الشامل](../features/tax-management-system.md)
- [دليل API](../api/tax-endpoints.md)
- [أمثلة الاستخدام](../examples/tax-usage-examples.md)

## 🔄 التطوير والتخصيص

### إضافة فئة ضريبة جديدة:
1. حدث `tax_category` في النماذج والـ schemas
2. أضف الخيار الجديد في المكونات
3. حدث الفلاتر والتحقق من صحة البيانات

### إضافة طريقة حساب جديدة:
1. حدث `calculation_method` في النماذج
2. أضف منطق الحساب في `TaxRate.calculate_tax_amount()`
3. حدث الواجهة الأمامية

### تخصيص الواجهة:
- عدل `TaxTypesDataTable.tsx` لتخصيص العرض
- أضف فلاتر جديدة حسب الحاجة
- خصص الألوان والأيقونات

---

**دليل سريع - نظام إدارة الضرائب v1.0.0**
