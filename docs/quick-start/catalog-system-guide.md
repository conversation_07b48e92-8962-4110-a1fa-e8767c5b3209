# 🚀 دليل سريع - نظام إدارة الفهرس المتقدم (الإصدار 2.0.0)

> **للمطورين الجدد**: دليل سريع لفهم واستخدام نظام إدارة الفهرس المحدث مع جداول البيانات التفاعلية

## 📋 نظرة عامة سريعة

نظام إدارة الفهرس المتقدم يسمح بتنظيم المنتجات من خلال **جداول بيانات تفاعلية** مع:
- **الفئات** (Categories): تصنيف رئيسي مع نظام شجرة للفئات الفرعية
- **الفئات الفرعية** (Subcategories): تصنيف فرعي مع عرض هيكلي
- **العلامات التجارية** (Brands): الشركات المصنعة مع معلومات تفصيلية
- **الوحدات** (Units): وحدات القياس مع نظام تحويلات متقدم

## 🌟 المميزات الجديدة (الإصدار 2.0.0)

- ✅ **جداول بيانات تفاعلية** لجميع الأقسام
- ✅ **بحث فوري ومتقدم** في جميع الحقول
- ✅ **فلاتر ذكية متعددة** لكل قسم
- ✅ **نظام شجرة للفئات** مع توسيع/طي
- ✅ **تصميم موحد احترافي** عبر جميع المكونات
- ✅ **أداء محسن** مع استجابة سريعة

## 🎯 الوصول السريع

### الروابط:
- `/categories` - إدارة الفئات
- `/brands` - إدارة العلامات التجارية
- `/units` - إدارة الوحدات

### من القائمة الجانبية:
انقر على "الفئات" أو "العلامات التجارية" أو "الوحدات"

## 🔧 للمطورين

### API Endpoints الرئيسية:

```javascript
// الفئات
GET    /api/categories/                    // جلب جميع الفئات
POST   /api/categories/                    // إنشاء فئة جديدة
PUT    /api/categories/{id}                // تحديث فئة
DELETE /api/categories/{id}                // حذف فئة

// الفئات الفرعية
POST   /api/categories/{id}/subcategories  // إنشاء فئة فرعية
GET    /api/categories/{id}/subcategories  // جلب الفئات الفرعية

// العلامات التجارية
GET    /api/brands/                        // جلب جميع العلامات التجارية
POST   /api/brands/                        // إنشاء علامة تجارية
PUT    /api/brands/{id}                    // تحديث علامة تجارية
DELETE /api/brands/{id}                    // حذف علامة تجارية

// الوحدات
GET    /api/units/                         // جلب جميع الوحدات
POST   /api/units/                         // إنشاء وحدة جديدة
PUT    /api/units/{id}                     // تحديث وحدة
DELETE /api/units/{id}                     // حذف وحدة
```

### استخدام Stores في React:

```typescript
import useCategoryStore from '../stores/categoryStore';
import useBrandStore from '../stores/brandStore';
import useUnitStore from '../stores/unitStore';

// في المكون
const { categories, fetchCategories, createCategory } = useCategoryStore();
const { brands, fetchBrands, createBrand } = useBrandStore();
const { units, fetchUnits, createUnit } = useUnitStore();

// جلب البيانات
useEffect(() => {
  fetchCategories();
  fetchBrands();
  fetchUnits();
}, []);
```

### إنشاء فئة جديدة:

```typescript
const handleCreateCategory = async () => {
  try {
    await createCategory({
      name: "فئة جديدة",
      description: "وصف الفئة",
      is_active: true
    });
    // تم الإنشاء بنجاح
  } catch (error) {
    // معالجة الخطأ
  }
};
```

## 🗃️ هيكل قاعدة البيانات

### جدول الفئات (categories):
```sql
id, name, description, is_active, created_at, updated_at, created_by, updated_by
```

### جدول الفئات الفرعية (subcategories):
```sql
id, name, description, category_id, is_active, created_at, updated_at, created_by, updated_by
```

### جدول العلامات التجارية (brands):
```sql
id, name, description, logo_url, website, contact_info, is_active, created_at, updated_at, created_by, updated_by
```

### جدول الوحدات (units):
```sql
id, name, symbol, description, unit_type, base_unit_id, conversion_factor, is_active, created_at, updated_at, created_by, updated_by
```

## 🎨 مكونات UI المحدثة

### الملفات الرئيسية:
- `pages/CatalogManagement.tsx` - الصفحة الرئيسية
- `components/catalog/CategoriesTab.tsx` - تبويب الفئات (محدث)
- `components/catalog/BrandsTab.tsx` - تبويب العلامات التجارية (محدث)
- `components/catalog/UnitsTab.tsx` - تبويب الوحدات (محدث)

### **مكونات DataTable الجديدة:**
- `components/catalog/CategoriesDataTable.tsx` - **جدول الفئات المتقدم**
- `components/catalog/BrandsDataTable.tsx` - **جدول العلامات التجارية المتقدم**
- `components/catalog/UnitsDataTable.tsx` - **جدول الوحدات المتقدم**

### استخدام المكونات الجديدة:
```typescript
import CategoriesDataTable from '../components/catalog/CategoriesDataTable';
import BrandsDataTable from '../components/catalog/BrandsDataTable';
import UnitsDataTable from '../components/catalog/UnitsDataTable';

// استخدام مباشر
<CategoriesDataTable />
<BrandsDataTable />
<UnitsDataTable />

// أو من خلال التبويبات
<Route path="/categories" element={<CatalogManagement />} />
```

## 🔍 مميزات البحث والفلترة الجديدة

### **للفئات (CategoriesDataTable):**
```typescript
// فلاتر متاحة
const filters = {
  search: '',                    // البحث في الأسماء والأوصاف
  status: 'all|active|inactive', // حالة الفئة
  hasSubcategories: 'all|yes|no' // وجود فئات فرعية
};

// مميزات إضافية
- نظام شجرة للفئات الفرعية
- أزرار "توسيع الكل" و "طي الكل"
- عداد الفئات الفرعية
```

### **للعلامات التجارية (BrandsDataTable):**
```typescript
// فلاتر متاحة
const filters = {
  search: '',                    // البحث الشامل
  status: 'all|active|inactive', // حالة العلامة التجارية
  hasWebsite: 'all|yes|no'       // وجود موقع إلكتروني
};

// مميزات إضافية
- روابط تفاعلية للمواقع الإلكترونية
- عرض معلومات الاتصال مع أيقونات
```

### **للوحدات (UnitsDataTable):**
```typescript
// فلاتر متاحة
const filters = {
  search: '',                           // البحث الشامل
  status: 'all|active|inactive',        // حالة الوحدة
  unitType: 'all|weight|volume|length|area|count', // نوع الوحدة
  hasBaseUnit: 'all|yes|no'             // وجود وحدة أساسية
};

// مميزات إضافية
- أيقونات ملونة لأنواع الوحدات
- عرض الوحدات الأساسية ومعاملات التحويل
```

## 🚀 الاستخدام المتقدم للمكونات الجديدة

### **استخدام CategoriesDataTable:**
```typescript
import CategoriesDataTable from '../components/catalog/CategoriesDataTable';

const CategoriesPage = () => {
  return (
    <div className="p-6">
      <CategoriesDataTable className="custom-class" />
    </div>
  );
};

// المميزات المتاحة:
// - بحث فوري في الأسماء والأوصاف
// - فلترة حسب الحالة ووجود فئات فرعية
// - نظام شجرة مع توسيع/طي
// - إضافة/تعديل/حذف الفئات والفئات الفرعية
```

### **استخدام BrandsDataTable:**
```typescript
import BrandsDataTable from '../components/catalog/BrandsDataTable';

const BrandsPage = () => {
  return (
    <div className="p-6">
      <BrandsDataTable />
    </div>
  );
};

// المميزات المتاحة:
// - بحث شامل في جميع الحقول
// - فلترة حسب الحالة ووجود موقع إلكتروني
// - روابط تفاعلية للمواقع
// - عرض معلومات الاتصال
```

### **استخدام UnitsDataTable:**
```typescript
import UnitsDataTable from '../components/catalog/UnitsDataTable';

const UnitsPage = () => {
  return (
    <div className="p-6">
      <UnitsDataTable />
    </div>
  );
};

// المميزات المتاحة:
// - بحث متقدم في جميع الحقول
// - فلترة متعددة (الحالة، النوع، الوحدة الأساسية)
// - أيقونات ملونة للأنواع
// - إدارة الوحدات الأساسية والتحويلات
```

## 🔄 نظام التحويلات (الوحدات)

### مثال على إعداد تحويل:
```typescript
// إنشاء وحدة أساسية (كيلوغرام)
const kgUnit = await createUnit({
  name: "كيلوغرام",
  symbol: "kg",
  unit_type: "weight"
});

// إنشاء وحدة فرعية (غرام)
const gramUnit = await createUnit({
  name: "غرام",
  symbol: "g",
  unit_type: "weight",
  base_unit_id: kgUnit.id,
  conversion_factor: 0.001  // 1 غرام = 0.001 كيلوغرام
});
```

## ⚠️ نصائح مهمة

### 1. معالجة الأخطاء:
```typescript
try {
  await deleteCategory(id);
} catch (error) {
  if (error.message.includes("منتجات")) {
    alert("لا يمكن حذف الفئة لأنها تحتوي على منتجات");
  }
}
```

### 2. التحقق من البيانات:
```typescript
// تحقق من وجود اسم الفئة
if (!categoryName.trim()) {
  setError("اسم الفئة مطلوب");
  return;
}
```

### 3. التحديث الفوري:
```typescript
// بعد إنشاء فئة جديدة
await createCategory(newCategory);
await fetchCategories(); // تحديث القائمة
```

## 🐛 مشاكل شائعة وحلولها

### 1. خطأ "tripoli_timestamp":
```python
# ❌ خطأ
created_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())

# ✅ صحيح
created_at = Column(DateTime(timezone=True), server_default=func.now())
```

### 2. خطأ استيراد middleware:
```python
# ❌ خطأ
from middleware.auth import get_current_user

# ✅ صحيح
from utils.auth import get_current_user
```

### 3. خصائص مفقودة في المكونات:
```typescript
// ✅ تأكد من إضافة جميع الخصائص المطلوبة
<DeleteConfirmModal
  isOpen={deleteModal.isOpen}
  onClose={() => setDeleteModal({...})}
  onConfirm={confirmDelete}
  title="حذف الفئة"
  message="هل أنت متأكد؟"
  itemName={deleteModal.name}  // مطلوب
  isLoading={deleteModal.isLoading}  // مطلوب
/>
```

## 📚 مراجع إضافية

- [التوثيق الكامل](../development/catalog-management-system.md)
- [ذاكرة النظام](../../SYSTEM_MEMORY.md)
- [قواعد النظام](../../.augment/rules/smartpos_rules.md)

## 🆕 نصائح للإصدار الجديد (2.0.0)

### **1. استخدام المكونات الجديدة:**
```typescript
// ✅ استخدم مكونات DataTable الجديدة
import CategoriesDataTable from '../components/catalog/CategoriesDataTable';

// ❌ تجنب استخدام المكونات القديمة مباشرة
// import CategoriesTab from '../components/catalog/CategoriesTab';
```

### **2. الاستفادة من الفلاتر:**
```typescript
// الفلاتر تعمل تلقائياً - لا حاجة لإعداد إضافي
// فقط استخدم المكون وستحصل على جميع المميزات
<CategoriesDataTable />
```

### **3. التخصيص:**
```typescript
// يمكن إضافة classes مخصصة
<BrandsDataTable className="my-custom-styles" />
```

### **4. الأداء:**
- المكونات الجديدة محسنة للأداء
- البحث والفلترة تتم محلياً للاستجابة السريعة
- التحديثات فورية بعد العمليات

---

**آخر تحديث**: يوليو 2025 - الإصدار 2.0.0
**نصيحة**: ابدأ بمراجعة مكونات DataTable الجديدة في `components/catalog/` لفهم النمط المحدث قبل إضافة ميزات جديدة.

**للمطورين**: راجع [التوثيق الكامل](../development/catalog-management-system.md) للحصول على تفاصيل تقنية شاملة.
