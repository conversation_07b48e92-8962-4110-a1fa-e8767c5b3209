# 📦 نظام إدارة المستودعات - SmartPOS

## نظرة عامة

نظام إدارة المستودعات هو نظام شامل لإدارة المخزون عبر مستودعات متعددة، يوفر تتبع دقيق للمخزون، إدارة الحركات، وطلبات التحويل بين المستودعات.

## 🎯 الميزات الرئيسية

### 1. إدارة المستودعات
- إنشاء وإدارة مستودعات متعددة
- تعيين مستودع رئيسي
- إدارة السعة والحدود
- معلومات الاتصال والموقع

### 2. مخزون المستودعات
- تتبع المخزون لكل مستودع
- إدارة الحد الأدنى والأقصى للمخزون
- حجز المخزون للطلبات
- تنبيهات المخزون المنخفض

### 3. حركات المستودعات
- تسجيل جميع حركات الدخول والخروج
- تتبع التحويلات بين المستودعات
- تعديل المخزون مع الأسباب
- تقارير الحركات التفصيلية

### 4. طلبات التحويل
- إنشاء طلبات تحويل بين المستودعات
- نظام الموافقات المتدرج
- تتبع حالة الطلبات
- إدارة العناصر المحولة

## 🏗️ البنية التقنية

### Backend (Python/FastAPI)

#### النماذج (Models)
```
backend/models/warehouse.py
├── Warehouse - نموذج المستودعات
├── WarehouseInventory - نموذج مخزون المستودعات
├── WarehouseMovement - نموذج حركات المستودعات
├── TransferRequest - نموذج طلبات التحويل
└── TransferRequestItem - نموذج تفاصيل طلبات التحويل
```

#### الخدمات (Services)
```
backend/services/
├── warehouse_service.py - خدمة إدارة المستودعات
├── warehouse_inventory_service.py - خدمة مخزون المستودعات
├── warehouse_movement_service.py - خدمة حركات المستودعات
└── transfer_request_service.py - خدمة طلبات التحويل
```

#### API Endpoints
```
backend/routers/
├── warehouse.py - API المستودعات
├── warehouse_inventory.py - API مخزون المستودعات
├── warehouse_movement.py - API حركات المستودعات
└── transfer_request.py - API طلبات التحويل
```

#### Schemas
```
backend/schemas/warehouse.py
├── WarehouseCreate/Update/Response
├── WarehouseInventoryCreate/Update/Response
├── WarehouseMovementCreate/Response
└── TransferRequestCreate/Response
```

### Frontend (React/TypeScript)

#### الخدمات (Services)
```
frontend/src/services/
├── warehouseService.ts - خدمة المستودعات
├── warehouseInventoryService.ts - خدمة المخزون
├── warehouseMovementService.ts - خدمة الحركات
└── transferRequestService.ts - خدمة طلبات التحويل
```

#### إدارة الحالة (Stores)
```
frontend/src/stores/
├── warehouseStore.ts - حالة المستودعات
├── warehouseInventoryStore.ts - حالة المخزون
└── warehouseMovementStore.ts - حالة الحركات والتحويلات
```

#### المكونات (Components)
```
frontend/src/components/warehouse/
├── CreateWarehouseModal.tsx - نافذة إنشاء مستودع
├── WarehouseInventoryTable.tsx - جدول مخزون المستودع
└── ... (مكونات أخرى)
```

#### الصفحات (Pages)
```
frontend/src/pages/
└── WarehousesPage.tsx - صفحة إدارة المستودعات
```

## 🗄️ قاعدة البيانات

### جداول النظام

#### 1. warehouses - جدول المستودعات
```sql
- id (Primary Key)
- name (اسم المستودع)
- code (كود المستودع - فريد)
- address (العنوان)
- phone (الهاتف)
- manager_name (اسم المدير)
- email (البريد الإلكتروني)
- is_main (مستودع رئيسي)
- is_active (نشط)
- capacity_limit (الحد الأقصى للسعة)
- current_capacity (السعة الحالية)
- created_at, updated_at
```

#### 2. warehouse_inventory - جدول مخزون المستودعات
```sql
- id (Primary Key)
- warehouse_id (Foreign Key -> warehouses)
- product_id (Foreign Key -> products)
- quantity (الكمية)
- reserved_quantity (الكمية المحجوزة)
- min_stock_level (الحد الأدنى)
- max_stock_level (الحد الأقصى)
- location_code (كود الموقع)
- last_updated
```

#### 3. warehouse_movements - جدول حركات المستودعات
```sql
- id (Primary Key)
- movement_type (نوع الحركة: IN/OUT/TRANSFER/ADJUSTMENT)
- from_warehouse_id (مستودع المصدر)
- to_warehouse_id (مستودع الوجهة)
- product_id (Foreign Key -> products)
- quantity (الكمية)
- unit_cost (تكلفة الوحدة)
- total_cost (التكلفة الإجمالية)
- reference_type (نوع المرجع)
- reference_id (معرف المرجع)
- notes (ملاحظات)
- created_by (منشئ الحركة)
- created_at
```

#### 4. transfer_requests - جدول طلبات التحويل
```sql
- id (Primary Key)
- request_number (رقم الطلب - فريد)
- from_warehouse_id (مستودع المصدر)
- to_warehouse_id (مستودع الوجهة)
- status (الحالة: PENDING/APPROVED/IN_TRANSIT/COMPLETED/CANCELLED)
- requested_by (طالب التحويل)
- approved_by (موافق التحويل)
- notes (ملاحظات)
- requested_at, approved_at, completed_at
```

#### 5. transfer_request_items - جدول تفاصيل طلبات التحويل
```sql
- id (Primary Key)
- transfer_request_id (Foreign Key -> transfer_requests)
- product_id (Foreign Key -> products)
- requested_quantity (الكمية المطلوبة)
- approved_quantity (الكمية المعتمدة)
- transferred_quantity (الكمية المحولة)
- unit_cost (تكلفة الوحدة)
- notes (ملاحظات)
```

## 🔧 التثبيت والإعداد

### 1. تشغيل Migration
```bash
cd backend
source venv/bin/activate
python migrations/create_warehouse_tables.py
```

### 2. إعداد الأذونات
تأكد من أن المستخدمين لديهم الأذونات المناسبة لإدارة المستودعات.

### 3. إعداد المستودع الافتراضي
سيتم إنشاء مستودع رئيسي افتراضي تلقائياً عند تشغيل Migration.

## 📊 استخدام النظام

### 1. إدارة المستودعات

#### إنشاء مستودع جديد
1. انتقل إلى "إدارة المستودعات" > "المستودعات"
2. اضغط على "إضافة مستودع"
3. املأ البيانات المطلوبة
4. احفظ المستودع

#### تعيين مستودع رئيسي
1. في قائمة المستودعات
2. اضغط على "تعيين كرئيسي" للمستودع المطلوب

### 2. إدارة المخزون

#### عرض مخزون مستودع
1. انتقل إلى "مخزون المستودعات"
2. اختر المستودع المطلوب
3. استعرض قائمة المنتجات والكميات

#### تحديث مستويات المخزون
1. في جدول المخزون
2. اضغط على أيقونة التعديل للمنتج
3. حدث الكميات والحدود
4. احفظ التغييرات

### 3. حركات المستودعات

#### تسجيل حركة دخول
```json
{
  "movement_type": "IN",
  "to_warehouse_id": 1,
  "product_id": 123,
  "quantity": 100,
  "unit_cost": 10.50,
  "reference_type": "PURCHASE",
  "reference_id": 456,
  "notes": "شراء جديد"
}
```

#### تسجيل حركة خروج
```json
{
  "movement_type": "OUT",
  "from_warehouse_id": 1,
  "product_id": 123,
  "quantity": 50,
  "reference_type": "SALE",
  "reference_id": 789,
  "notes": "بيع للعميل"
}
```

### 4. طلبات التحويل

#### إنشاء طلب تحويل
1. انتقل إلى "طلبات التحويل"
2. اضغط على "طلب تحويل جديد"
3. اختر المستودع المصدر والوجهة
4. أضف المنتجات والكميات
5. أرسل الطلب

#### الموافقة على طلب التحويل
1. في قائمة الطلبات المعلقة
2. اضغط على "موافقة" للطلب المطلوب
3. راجع التفاصيل وأكد الموافقة

## 🔍 التقارير والإحصائيات

### 1. تقارير المستودعات
- ملخص المستودعات
- حالة السعة
- المنتجات قليلة المخزون

### 2. تقارير الحركات
- حركات المستودع خلال فترة
- تاريخ حركات المنتج
- ملخص الحركات حسب النوع

### 3. تقارير التحويلات
- طلبات التحويل المعلقة
- تاريخ التحويلات
- إحصائيات التحويلات

## ⚠️ ملاحظات مهمة

### الأمان
- جميع العمليات محمية بنظام المصادقة
- تسجيل جميع الحركات مع معرف المستخدم
- التحقق من الأذونات قبل كل عملية

### الأداء
- فهرسة محسنة لجميع الجداول
- استعلامات محسنة للبحث والفلترة
- تخزين مؤقت للبيانات المتكررة

### النسخ الاحتياطي
- تأكد من أخذ نسخ احتياطية منتظمة
- اختبار استعادة البيانات دورياً

## 🐛 استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في إنشاء المستودع
- تأكد من عدم تكرار الكود
- تحقق من صحة البيانات المدخلة

#### مشكلة في تحديث المخزون
- تأكد من وجود المنتج في المستودع
- تحقق من الكميات المتاحة

#### خطأ في طلب التحويل
- تأكد من توفر المخزون في المستودع المصدر
- تحقق من حالة المستودعات (نشطة)

## 📞 الدعم الفني

للحصول على المساعدة:
1. راجع هذا التوثيق أولاً
2. تحقق من ملفات السجل (logs)
3. تواصل مع فريق التطوير

---

**آخر تحديث:** ديسمبر 2024  
**الإصدار:** 1.0.0
