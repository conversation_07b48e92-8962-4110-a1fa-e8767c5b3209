# 📋 نظام إدارة الفروع والمستودعات - دليل شامل

> **تاريخ الإنشاء:** 2025-01-11  
> **الإصدار:** 1.0  
> **المطور:** Augment Agent  
> **الحالة:** مكتمل ✅

## 📖 نظرة عامة

نظام إدارة الفروع والمستودعات هو نظام متقدم يوفر إدارة شاملة للعلاقات Many-to-Many بين فروع الشركة ومستودعاتها، مع دعم التوزيع الذكي والإدارة المرنة للموارد.

### ✨ الميزات الرئيسية

- **إدارة الفروع**: إنشاء وتحديث وإدارة فروع الشركة
- **العلاقات المرنة**: ربط الفروع بالمستودعات بعلاقات Many-to-Many
- **التوزيع الذكي**: اختيار المستودع الأمثل بناءً على المسافة والتوفر
- **التحويلات المتقدمة**: تحويلات بين الفروع والمستودعات
- **التقارير المتقدمة**: تحليلات شاملة للأداء والكفاءة
- **سلامة البيانات**: أدوات فحص وضمان سلامة البيانات

## 🏗️ هيكل النظام

### 📁 الملفات والمجلدات

```
backend/
├── models/
│   ├── branch.py                    # نماذج الفروع وجدول الربط
│   └── warehouse.py                 # نماذج المستودعات (محدث)
├── services/
│   ├── branch_service.py            # خدمة إدارة الفروع
│   ├── branch_warehouse_service.py  # خدمة إدارة العلاقات
│   ├── warehouse_service.py         # خدمة المستودعات (محدث)
│   ├── smart_distribution_service.py # خدمة التوزيع الذكي
│   ├── transfer_request_service.py  # خدمة التحويلات (محدث)
│   └── advanced_reports_service.py  # خدمة التقارير المتقدمة
├── schemas/
│   ├── branch.py                    # مخططات البيانات للفروع
│   └── branch_warehouse.py          # مخططات العلاقات
├── routers/
│   ├── branch.py                    # API endpoints للفروع
│   └── branch_warehouse.py          # API endpoints للعلاقات
├── migrations/
│   └── branch_warehouse_system_migration.py # سكربت الترحيل
├── tests/
│   └── test_branch_warehouse_system.py      # اختبارات شاملة
└── utils/
    └── data_integrity_checker.py            # أداة فحص سلامة البيانات

docs/
├── developers/
│   └── branches_warehouses_system.md       # دليل المطورين
└── BRANCH_WAREHOUSE_SYSTEM_README.md       # هذا الملف
```

## 🚀 التثبيت والإعداد

### 1. متطلبات النظام

- Python 3.8+
- PostgreSQL 12+
- FastAPI
- SQLAlchemy
- Pydantic

### 2. تشغيل Migration

```bash
# الانتقال لمجلد المشروع
cd backend

# تشغيل migration
python migrations/branch_warehouse_system_migration.py
```

### 3. تشغيل الاختبارات

```bash
# تشغيل اختبارات النظام
python tests/test_branch_warehouse_system.py

# فحص سلامة البيانات
python utils/data_integrity_checker.py
```

## 📊 قاعدة البيانات

### جدول الفروع (branches)

```sql
CREATE TABLE branches (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    address TEXT,
    phone VARCHAR(20),
    manager_name VARCHAR(100),
    email VARCHAR(100),
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_main BOOLEAN NOT NULL DEFAULT false,
    city VARCHAR(50),
    region VARCHAR(50),
    postal_code VARCHAR(20),
    max_daily_sales INTEGER,
    working_hours_start VARCHAR(10),
    working_hours_end VARCHAR(10),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE,
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id)
);
```

### جدول الربط (branch_warehouses)

```sql
CREATE TABLE branch_warehouses (
    branch_id INTEGER NOT NULL REFERENCES branches(id) ON DELETE CASCADE,
    warehouse_id INTEGER NOT NULL REFERENCES warehouses(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_primary BOOLEAN DEFAULT false,
    priority INTEGER DEFAULT 1,
    PRIMARY KEY (branch_id, warehouse_id)
);
```

## 🔧 استخدام النظام

### إنشاء فرع جديد

```python
from services.branch_service import get_branch_service

# إنشاء فرع
branch_data = {
    "name": "فرع الزاوية",
    "code": "ZAW-001",
    "address": "شارع الجمهورية، الزاوية",
    "city": "الزاوية",
    "is_active": True
}

branch_service = get_branch_service(db)
result = branch_service.create_branch(branch_data)
```

### ربط فرع بمستودع

```python
from services.branch_warehouse_service import get_branch_warehouse_service

# ربط الفرع بمستودع
service = get_branch_warehouse_service(db)
result = service.link_branch_to_warehouse(
    branch_id=1,
    warehouse_id=2,
    is_primary=True,
    priority=1
)
```

### التوزيع الذكي

```python
from services.smart_distribution_service import get_smart_distribution_service

# العثور على المستودع الأمثل
smart_service = get_smart_distribution_service(db)
result = smart_service.find_optimal_warehouse_for_branch(
    branch_id=1,
    product_id=100,
    required_quantity=50.0
)
```

### إنشاء تقرير أداء

```python
from services.advanced_reports_service import get_advanced_reports_service

# تقرير أداء الفروع
reports_service = get_advanced_reports_service(db)
result = reports_service.generate_branch_performance_report()
```

## 🌐 API Endpoints

### الفروع

```
POST   /api/branches/                    # إنشاء فرع جديد
GET    /api/branches/                    # جلب جميع الفروع
GET    /api/branches/{branch_id}         # جلب فرع بالمعرف
PUT    /api/branches/{branch_id}         # تحديث الفرع
DELETE /api/branches/{branch_id}         # حذف الفرع
POST   /api/branches/{branch_id}/set-main        # تعيين فرع رئيسي
POST   /api/branches/{branch_id}/toggle-status  # تبديل حالة النشاط
```

### العلاقات

```
POST   /api/branch-warehouses/link                           # ربط فرع بمستودع
DELETE /api/branch-warehouses/unlink                         # إلغاء الربط
POST   /api/branch-warehouses/set-primary                    # تعيين مستودع أساسي
GET    /api/branch-warehouses/branch/{branch_id}/warehouses  # مستودعات الفرع
GET    /api/branch-warehouses/warehouse/{warehouse_id}/branches  # فروع المستودع
```

## 📈 التقارير والتحليلات

### 1. تقرير أداء الفروع

- إحصائيات التحويلات
- معدلات الإكمال
- القيم المالية
- نقاط الأداء

### 2. تقرير استخدام المستودعات

- معدلات الاستخدام
- إحصائيات المخزون
- كفاءة التوزيع

### 3. تقرير تحسين التوزيع

- تحليل كفاءة التوزيع
- توصيات التحسين
- نقاط التحسين

## 🧪 الاختبارات

### تشغيل الاختبارات الشاملة

```bash
python tests/test_branch_warehouse_system.py
```

### فحص سلامة البيانات

```bash
python utils/data_integrity_checker.py
```

### أنواع الاختبارات

- **اختبارات الخدمات الأساسية**: تحقق من تهيئة الخدمات
- **اختبارات العلاقات**: تحقق من ربط الفروع والمستودعات
- **اختبارات التوزيع الذكي**: تحقق من خوارزميات التوزيع
- **اختبارات التحويلات**: تحقق من عمليات التحويل
- **اختبارات التقارير**: تحقق من إنشاء التقارير
- **اختبارات سلامة البيانات**: تحقق من سلامة قاعدة البيانات

## 🔒 الأمان والصلاحيات

### التحقق من الصلاحيات

```python
# تأكد من وجود صلاحيات المستخدم
if not user.has_permission('manage_branches'):
    raise HTTPException(
        status_code=403,
        detail="ليس لديك صلاحية لإدارة الفروع"
    )
```

### معالجة الأخطاء

```python
try:
    result = branch_service.create_branch(branch_data)
    if not result['success']:
        logger.error(f"فشل في إنشاء الفرع: {result['error']}")
        return {"error": result['error']}, 400
except Exception as e:
    logger.error(f"خطأ تقني: {e}")
    return {"error": "خطأ داخلي في الخادم"}, 500
```

## 📝 أفضل الممارسات

### 1. استخدام المعاملات

```python
try:
    # بداية المعاملة
    branch = branch_service.create_branch(branch_data)
    
    # ربط المستودعات
    for warehouse_id in warehouse_ids:
        service.link_branch_to_warehouse(branch['branch']['id'], warehouse_id)
    
    db.commit()
    logger.info("تم إنشاء الفرع وربط المستودعات بنجاح")
    
except Exception as e:
    db.rollback()
    logger.error(f"فشل في العملية: {e}")
    raise
```

### 2. التخزين المؤقت

```python
from functools import lru_cache

@lru_cache(maxsize=100)
def get_branch_warehouses_cached(branch_id: int):
    return service.get_warehouses_for_branch(branch_id)
```

### 3. التحقق من البيانات

```python
def validate_branch_data(data):
    if not data.get('name'):
        raise ValueError("اسم الفرع مطلوب")
    
    if not data.get('code'):
        raise ValueError("كود الفرع مطلوب")
```

## 🚀 التطوير المستقبلي

### ميزات مقترحة

1. **نظام الإشعارات** عند تغيير حالة الفروع
2. **تقارير الأداء** لكل فرع
3. **نظام الموافقات** للتغييرات الحساسة
4. **التكامل مع نظام GPS** لتحديد المواقع
5. **نظام التوزيع الذكي** المتقدم

## 📞 الدعم والمساعدة

### للمطورين

- راجع `docs/developers/branches_warehouses_system.md` للتفاصيل التقنية
- استخدم أدوات الاختبار المتوفرة
- اتبع قواعد النظام في `.augment/rules/smartpos_rules.md`

### للمستخدمين

- راجع واجهة المستخدم للتفاعل مع النظام
- استخدم التقارير المتاحة لمراقبة الأداء
- تواصل مع فريق الدعم للمساعدة

---

> **📝 ملاحظة:** هذا النظام تم تطويره وفقاً لأفضل الممارسات في البرمجة الكائنية ومعالجة الأخطاء. تأكد من تشغيل الاختبارات قبل النشر في بيئة الإنتاج.
