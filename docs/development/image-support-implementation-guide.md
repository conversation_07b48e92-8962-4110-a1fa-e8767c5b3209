# 📸 دليل تطبيق دعم الصور في النظام

> **دليل شامل للمطورين لإضافة دعم الصور لأي كيان في النظام**

## 📋 نظرة عامة

هذا الدليل يوضح كيفية إضافة دعم الصور لأي كيان في النظام (مثل الفئات، العلامات التجارية، المنتجات، إلخ) باستخدام خدمة إدارة الصور المتقدمة الموجودة.

## 🏗️ الهيكل العام

### المكونات الأساسية:
1. **خدمة إدارة الصور** - `imageManagementService`
2. **مكون رفع الصور** - `ImageUploadComponent`
3. **قاعدة البيانات** - إضافة حقل `image_url` أو `logo_url`
4. **واجهة المستخدم** - دمج مكون الرفع في النماذج والجداول

## 🔧 خطوات التطبيق

### 1. إعداد قاعدة البيانات

#### إضافة حقل الصورة للنموذج:
```python
# backend/models/your_model.py
from sqlalchemy import Column, Integer, String, Text, Boolean

class YourModel(Base):
    __tablename__ = "your_table"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    image_url = Column(String(255), nullable=True)  # ← إضافة حقل الصورة
    is_active = Column(Boolean, default=True)
```

#### إنشاء Migration:
```python
# backend/migrations/add_image_url_to_your_table.py
import logging
from sqlalchemy import text
from database.session import engine

def add_image_url_field():
    """إضافة حقل image_url لجدول your_table"""
    try:
        with engine.connect() as conn:
            # التحقق من وجود العمود
            check_query = text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'your_table' 
                AND column_name = 'image_url'
            """)
            
            result = conn.execute(check_query)
            if result.fetchone():
                return True
            
            # إضافة العمود
            add_query = text("""
                ALTER TABLE your_table 
                ADD COLUMN image_url VARCHAR(255) NULL
            """)
            
            conn.execute(add_query)
            conn.commit()
            return True
            
    except Exception as e:
        print(f"خطأ في إضافة حقل image_url: {e}")
        return False
```

### 2. تحديث Schemas

```python
# backend/schemas/your_schema.py
from pydantic import BaseModel, Field
from typing import Optional

class YourModelBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    image_url: Optional[str] = Field(None, max_length=255)  # ← إضافة حقل الصورة
    is_active: bool = True

class YourModelUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    image_url: Optional[str] = Field(None, max_length=255)  # ← إضافة حقل الصورة
    is_active: Optional[bool] = None
```

### 3. تحديث Store في Frontend

```typescript
// frontend/src/stores/yourModelStore.ts
export interface YourModel {
  id: number;
  name: string;
  description: string | null;
  image_url: string | null;  // ← إضافة حقل الصورة
  is_active: boolean;
  created_at: string;
  updated_at: string | null;
}
```

### 4. تحديث مكون DataTable

#### إضافة الواردات:
```typescript
// في بداية الملف
import { ImageUploadComponent } from '../ImageUpload';
import { imageManagementService, ImageUploadResult } from '../../services/imageManagementService';
import { FiImage, FiUpload } from 'react-icons/fi';
```

#### إضافة حالة رفع الصور:
```typescript
// إضافة حقل image_url للنموذج
const [itemForm, setItemForm] = useState({
  name: '',
  description: '',
  image_url: '',  // ← إضافة حقل الصورة
  is_active: true
});

// حالة رفع الصور
const [imageUpload, setImageUpload] = useState({
  isUploading: false,
  uploadedImagePath: '',
  showUploadComponent: false
});
```

#### دوال معالجة الصور:
```typescript
// دوال معالجة رفع الصور
const handleImageUploadSuccess = (result: ImageUploadResult) => {
  console.log('✅ تم رفع الصورة بنجاح:', result);
  if (result.success && result.file_path) {
    setImageUpload(prev => ({
      ...prev,
      isUploading: false,
      uploadedImagePath: result.file_path || '',
      showUploadComponent: false
    }));
    setItemForm(prev => ({ ...prev, image_url: result.file_path || '' }));
  }
};

const handleImageUploadError = (error: string) => {
  console.error('❌ خطأ في رفع الصورة:', error);
  setImageUpload(prev => ({
    ...prev,
    isUploading: false,
    showUploadComponent: false
  }));
};

const handleRemoveImage = async () => {
  try {
    const currentImagePath = imageUpload.uploadedImagePath || itemForm.image_url;
    
    // حذف الصورة من المجلد
    if (currentImagePath) {
      const deleteResult = await imageManagementService.deleteImage(currentImagePath, true);
      if (deleteResult.success) {
        console.log('✅ تم حذف الصورة من المجلد بنجاح');
      }
    }
    
    // إعادة تعيين الحالة
    setImageUpload({
      isUploading: false,
      uploadedImagePath: '',
      showUploadComponent: false
    });
    setItemForm(prev => ({ ...prev, image_url: '' }));
    
  } catch (error) {
    console.error('❌ خطأ في حذف الصورة:', error);
  }
};
```

#### تحديث دوال إدارة العناصر:
```typescript
const handleCreateItem = () => {
  setItemForm({ name: '', description: '', image_url: '', is_active: true });
  setImageUpload({ isUploading: false, uploadedImagePath: '', showUploadComponent: false });
  setItemModal({ isOpen: true, mode: 'create', item: null });
};

const handleEditItem = (item: YourModel) => {
  setItemForm({
    name: item.name,
    description: item.description || '',
    image_url: item.image_url || '',
    is_active: item.is_active
  });
  setImageUpload({
    isUploading: false,
    uploadedImagePath: item.image_url || '',
    showUploadComponent: false
  });
  setItemModal({ isOpen: true, mode: 'edit', item });
};
```

### 5. إضافة عمود الصورة في الجدول

```tsx
{/* في رأس الجدول */}
<th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-16">
  الصورة
</th>

{/* في صفوف الجدول */}
<td className="px-6 py-4 whitespace-nowrap">
  <div className="flex items-center justify-center">
    {item.image_url ? (
      <div className="relative">
        <img
          src={imageManagementService.getImageUrl(item.image_url)}
          alt={`صورة ${item.name}`}
          className="w-10 h-10 rounded-lg object-cover border border-gray-200 dark:border-gray-600"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
            const fallback = target.parentElement?.nextElementSibling as HTMLElement;
            if (fallback) fallback.style.display = 'flex';
          }}
        />
        <div className="w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-700 items-center justify-center hidden">
          <FiImage className="w-5 h-5 text-gray-400" />
        </div>
      </div>
    ) : (
      <div className="w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
        <FiImage className="w-5 h-5 text-gray-400" />
      </div>
    )}
  </div>
</td>
```

### 6. إضافة قسم رفع الصور في النموذج

```tsx
{/* قسم رفع الصور */}
<div className="space-y-3">
  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
    صورة العنصر (اختياري)
  </label>
  
  {/* عرض الصورة الحالية */}
  {(imageUpload.uploadedImagePath || itemForm.image_url) && (
    <div className="flex items-center gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600">
      <img
        src={imageManagementService.getImageUrl(imageUpload.uploadedImagePath || itemForm.image_url)}
        alt="صورة العنصر"
        className="w-16 h-16 rounded-lg object-cover border border-gray-200 dark:border-gray-600"
      />
      <div className="flex-1">
        <p className="text-sm text-gray-600 dark:text-gray-400">الصورة الحالية</p>
        <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
          {imageUpload.uploadedImagePath || itemForm.image_url}
        </p>
      </div>
      <button
        type="button"
        onClick={handleRemoveImage}
        className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-200 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
      >
        <FiTrash className="w-4 h-4" />
      </button>
    </div>
  )}

  {/* مكون الرفع */}
  {imageUpload.showUploadComponent ? (
    <div className="border border-gray-200 dark:border-gray-600 rounded-xl p-4">
      <ImageUploadComponent
        folder="your_folder_name"  // ← اسم المجلد المخصص
        multiple={false}
        generateThumbnails={true}
        maxFiles={1}
        onUploadSuccess={handleImageUploadSuccess}
        onUploadError={handleImageUploadError}
        className="w-full"
      />
    </div>
  ) : (
    <button
      type="button"
      onClick={() => setImageUpload(prev => ({ ...prev, showUploadComponent: true }))}
      className="w-full flex items-center justify-center gap-2 p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl hover:border-primary-500 dark:hover:border-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-all duration-200 text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400"
    >
      <FiUpload className="w-5 h-5" />
      {imageUpload.uploadedImagePath || itemForm.image_url ? 'تغيير الصورة' : 'رفع صورة العنصر'}
    </button>
  )}
</div>
```

### 7. تحديث دوال الإرسال والحذف

```typescript
// دالة إرسال النموذج
const handleItemSubmit = async () => {
  try {
    const finalItemForm = {
      ...itemForm,
      image_url: imageUpload.uploadedImagePath || itemForm.image_url
    };

    if (itemModal.mode === 'create') {
      await createItem(finalItemForm);
      setSuccessModal({ isOpen: true, message: 'تم إنشاء العنصر بنجاح' });
    } else if (itemModal.item) {
      await updateItem(itemModal.item.id, finalItemForm);
      setSuccessModal({ isOpen: true, message: 'تم تحديث العنصر بنجاح' });
    }
    
    setItemModal({ isOpen: false, mode: 'create', item: null });
    setImageUpload({ isUploading: false, uploadedImagePath: '', showUploadComponent: false });
    await loadItems();
  } catch (error) {
    console.error('Error saving item:', error);
  }
};

// دالة حذف العنصر
const confirmDelete = async () => {
  try {
    setDeleteModal(prev => ({ ...prev, isLoading: true }));
    
    // العثور على العنصر المراد حذفه
    const itemToDelete = items.find(item => item.id === deleteModal.id);
    
    // حذف الصورة إذا كانت موجودة
    if (itemToDelete?.image_url) {
      try {
        const deleteResult = await imageManagementService.deleteImage(itemToDelete.image_url, true);
        if (deleteResult.success) {
          console.log('✅ تم حذف الصورة من المجلد بنجاح');
        }
      } catch (imageError) {
        console.warn('⚠️ خطأ في حذف الصورة:', imageError);
      }
    }
    
    await deleteItem(deleteModal.id);
    setSuccessModal({ isOpen: true, message: 'تم حذف العنصر بنجاح' });
    setDeleteModal({ isOpen: false, id: 0, name: '', isLoading: false });
    await loadItems();
  } catch (error) {
    console.error('Error deleting item:', error);
    setDeleteModal(prev => ({ ...prev, isLoading: false }));
  }
};
```

## 📁 إعداد المجلدات

### إنشاء مجلد جديد للصور:
```bash
# إنشاء المجلد
mkdir -p backend/static/uploads/your_folder_name

# إضافة ملف .gitkeep
echo "# مجلد صور العناصر الجديدة" > backend/static/uploads/your_folder_name/.gitkeep
```

### تحديث .gitignore:
```gitignore
# إضافة المجلد الجديد
backend/static/uploads/your_folder_name/
```

## 🎨 تصميم موحد

### شارات الحالة:
```tsx
{item.is_active ? (
  <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800">
    <FiEye className="w-3 h-3 ml-1" />
    نشط
  </span>
) : (
  <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 border border-gray-200 dark:border-gray-700">
    <FiEyeOff className="w-3 h-3 ml-1" />
    غير نشط
  </span>
)}
```

### أزرار الإجراءات:
```tsx
<div className="flex items-center justify-end gap-1">
  <button
    onClick={() => handleEditItem(item)}
    className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 transition-colors duration-150 p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 border border-transparent hover:border-blue-200 dark:hover:border-blue-700"
  >
    <FiEdit className="w-4 h-4" />
  </button>
  <button
    onClick={() => handleDeleteItem(item)}
    className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 transition-colors duration-150 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 border border-transparent hover:border-red-200 dark:hover:border-red-700"
  >
    <FiTrash className="w-4 h-4" />
  </button>
</div>
```

## ✅ قائمة التحقق

### Backend:
- [ ] إضافة حقل `image_url` للنموذج
- [ ] إنشاء وتشغيل migration
- [ ] تحديث schemas
- [ ] اختبار API endpoints

### Frontend:
- [ ] تحديث interface في store
- [ ] إضافة حالة رفع الصور
- [ ] إضافة دوال معالجة الصور
- [ ] إضافة عمود الصورة في الجدول
- [ ] إضافة قسم رفع الصور في النموذج
- [ ] تحديث دوال الإرسال والحذف
- [ ] تطبيق التصميم الموحد

### إعدادات:
- [ ] إنشاء مجلد الصور
- [ ] إضافة .gitkeep
- [ ] تحديث .gitignore
- [ ] تعيين الصلاحيات

### اختبار:
- [ ] رفع صورة جديدة
- [ ] تعديل صورة موجودة
- [ ] حذف صورة
- [ ] حذف عنصر مع صورة
- [ ] اختبار معالجة الأخطاء

## 🚀 نصائح للمطورين

1. **استخدم أسماء مجلدات واضحة** - مثل `products`, `users`, `categories`
2. **اتبع نفس النمط** - للحفاظ على الاتساق
3. **اختبر معالجة الأخطاء** - تأكد من عمل النظام حتى عند فشل رفع الصور
4. **استخدم التصميم الموحد** - للحفاظ على تجربة مستخدم متسقة
5. **وثق التغييرات** - أضف تعليقات واضحة في الكود

## � أمثلة عملية من الكود

### مثال كامل من العلامات التجارية:

#### النموذج (backend/models/brand.py):
```python
class Brand(Base):
    __tablename__ = "brands"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, unique=True)
    description = Column(Text, nullable=True)
    website = Column(String(255), nullable=True)
    contact_info = Column(String(255), nullable=True)
    logo_url = Column(String(255), nullable=True)  # ← حقل الصورة
    is_active = Column(Boolean, default=True)
```

#### Schema (backend/schemas/brand.py):
```python
class BrandBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    website: Optional[str] = Field(None, max_length=255)
    contact_info: Optional[str] = Field(None, max_length=255)
    logo_url: Optional[str] = Field(None, max_length=255)  # ← حقل الصورة
    is_active: bool = True
```

#### Store (frontend/src/stores/brandStore.ts):
```typescript
export interface Brand {
  id: number;
  name: string;
  description: string | null;
  website: string | null;
  contact_info: string | null;
  logo_url: string | null;  // ← حقل الصورة
  is_active: boolean;
  created_at: string;
  updated_at: string | null;
}
```

### مثال كامل من الفئات:

#### Migration (backend/migrations/add_category_image_url.py):
```python
def add_category_image_url():
    """إضافة حقل image_url لجدول الفئات"""
    try:
        with engine.connect() as conn:
            # التحقق من وجود العمود
            check_column_query = text("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'categories'
                AND column_name = 'image_url'
            """)

            result = conn.execute(check_column_query)
            if result.fetchone():
                return True

            # إضافة العمود
            add_column_query = text("""
                ALTER TABLE categories
                ADD COLUMN image_url VARCHAR(255) NULL
            """)

            conn.execute(add_column_query)
            conn.commit()
            return True

    except Exception as e:
        print(f"خطأ في إضافة حقل image_url: {e}")
        return False
```

## �📚 مراجع إضافية

- [خدمة إدارة الصور](../IMAGE_MANAGEMENT_SERVICE.md)
- [مكون رفع الصور](../components/ImageUploadComponent.md)
- [إعدادات Git للصور](../git-images-ignore.md)
- [دليل التصميم الموحد](../design/unified-design-system.md)
- [أمثلة عملية من الكود](./image-support-examples.md)
