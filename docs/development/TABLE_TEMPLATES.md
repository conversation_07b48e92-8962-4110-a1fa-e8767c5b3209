# 📋 قوالب الجداول - Table Templates

## 🎯 نظرة عامة

هذا الملف يحتوي على قوالب جاهزة للاستخدام لإنشاء جداول محسنة في التطبيق.

## 🏗️ القالب الأساسي

### 1. **قالب جدول بسيط**

```typescript
// BasicDataTable.tsx
import React, { useState, useEffect, useMemo } from 'react';
import { FiEdit, FiTrash2, FiEye } from 'react-icons/fi';
import TablePagination from './TablePagination';
import LoadingSpinner from '../LoadingSpinner';

interface DataItem {
  id: number;
  name: string;
  name_ar: string;
  status: boolean;
  created_at: string;
}

interface BasicDataTableProps {
  data: DataItem[];
  loading?: boolean;
  onEdit?: (item: DataItem) => void;
  onDelete?: (item: DataItem) => void;
  onView?: (item: DataItem) => void;
}

const BasicDataTable: React.FC<BasicDataTableProps> = ({
  data,
  loading = false,
  onEdit,
  onDelete,
  onView
}) => {
  // State
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [filters, setFilters] = useState({
    search: '',
    status: 'all'
  });

  // البيانات المفلترة
  const filteredData = useMemo(() => {
    return data.filter(item => {
      // فلتر البحث
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        const matchesSearch = 
          item.name.toLowerCase().includes(searchLower) ||
          item.name_ar.toLowerCase().includes(searchLower);
        if (!matchesSearch) return false;
      }

      // فلتر الحالة
      if (filters.status !== 'all') {
        const isActive = item.status;
        const shouldBeActive = filters.status === 'active';
        if (isActive !== shouldBeActive) return false;
      }

      return true;
    });
  }, [data, filters]);

  // حسابات pagination
  const totalItems = filteredData.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedData = filteredData.slice(startIndex, startIndex + itemsPerPage);

  // إعادة تعيين عند تغيير الفلاتر
  useEffect(() => {
    setCurrentPage(1);
  }, [filters]);

  // معالجات الأحداث
  const handlePageChange = (page: number) => setCurrentPage(page);
  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 overflow-hidden">
      
      {/* Header والفلاتر */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            البيانات
          </h2>
          
          <div className="flex items-center gap-4">
            {/* البحث */}
            <input
              type="text"
              placeholder="البحث..."
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              className="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
            
            {/* فلتر الحالة */}
            <select
              value={filters.status}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
              className="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">جميع الحالات</option>
              <option value="active">نشط</option>
              <option value="inactive">غير نشط</option>
            </select>
          </div>
        </div>
      </div>

      {/* محتوى الجدول */}
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <LoadingSpinner />
        </div>
      ) : totalItems === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-500 dark:text-gray-400">
            {filters.search || filters.status !== 'all' 
              ? 'لا توجد نتائج مطابقة للبحث' 
              : 'لا توجد بيانات للعرض'
            }
          </div>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  #
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  الاسم
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  تاريخ الإنشاء
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {paginatedData.map((item, index) => (
                <tr key={item.id} className="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-150">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                    <span className="font-medium">{startIndex + index + 1}</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {item.name_ar}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {item.name}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      item.status
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    }`}>
                      {item.status ? 'نشط' : 'غير نشط'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {new Date(item.created_at).toLocaleDateString('ar-SA')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center gap-2">
                      {onView && (
                        <button
                          onClick={() => onView(item)}
                          className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                        >
                          <FiEye className="h-4 w-4" />
                        </button>
                      )}
                      {onEdit && (
                        <button
                          onClick={() => onEdit(item)}
                          className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                        >
                          <FiEdit className="h-4 w-4" />
                        </button>
                      )}
                      {onDelete && (
                        <button
                          onClick={() => onDelete(item)}
                          className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                        >
                          <FiTrash2 className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      
      {/* Pagination */}
      {totalItems > 0 && (
        <TablePagination
          currentPage={currentPage}
          totalPages={totalPages}
          itemsPerPage={itemsPerPage}
          totalItems={totalItems}
          onPageChange={handlePageChange}
          onItemsPerPageChange={handleItemsPerPageChange}
          itemsPerPageOptions={[5, 10, 20, 50]}
        />
      )}
    </div>
  );
};

export default BasicDataTable;
```

## 🎨 قوالب متخصصة

### 2. **قالب جدول المنتجات**

```typescript
// ProductsDataTable.tsx
interface Product {
  id: number;
  name: string;
  name_ar: string;
  sku: string;
  price: number;
  stock: number;
  category: string;
  status: boolean;
  image?: string;
}

// إضافة فلاتر خاصة بالمنتجات
const [filters, setFilters] = useState({
  search: '',
  category: 'all',
  status: 'all',
  priceRange: 'all',
  stockStatus: 'all'
});

// منطق فلترة خاص بالمنتجات
const filteredProducts = useMemo(() => {
  return products.filter(product => {
    // فلتر البحث (الاسم، SKU)
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      const matchesSearch = 
        product.name.toLowerCase().includes(searchLower) ||
        product.name_ar.toLowerCase().includes(searchLower) ||
        product.sku.toLowerCase().includes(searchLower);
      if (!matchesSearch) return false;
    }

    // فلتر الفئة
    if (filters.category !== 'all' && product.category !== filters.category) {
      return false;
    }

    // فلتر حالة المخزون
    if (filters.stockStatus !== 'all') {
      const isInStock = product.stock > 0;
      const shouldBeInStock = filters.stockStatus === 'in_stock';
      if (isInStock !== shouldBeInStock) return false;
    }

    return true;
  });
}, [products, filters]);
```

### 3. **قالب جدول التقارير**

```typescript
// ReportsDataTable.tsx
interface Report {
  id: number;
  title: string;
  type: 'sales' | 'inventory' | 'financial';
  date_range: string;
  generated_at: string;
  file_url?: string;
}

// أعمدة خاصة بالتقارير
<th>نوع التقرير</th>
<th>الفترة الزمنية</th>
<th>تاريخ الإنشاء</th>
<th>تحميل</th>
```

## 📋 قائمة مراجعة سريعة

عند استخدام أي قالب:

### ✅ تخصيص البيانات:
- [ ] تعديل interface للبيانات
- [ ] تحديث منطق الفلترة
- [ ] تخصيص الأعمدة

### ✅ تخصيص الفلاتر:
- [ ] إضافة فلاتر مناسبة للبيانات
- [ ] تحديث state الفلاتر
- [ ] تطبيق منطق الفلترة

### ✅ تخصيص الإجراءات:
- [ ] تحديد الإجراءات المطلوبة
- [ ] إضافة معالجات الأحداث
- [ ] تخصيص الأيقونات والألوان

### ✅ اختبار الوظائف:
- [ ] اختبار التنقل بين الصفحات
- [ ] اختبار الفلاتر
- [ ] اختبار الإجراءات
- [ ] اختبار التجاوب

---

**تاريخ الإنشاء**: 2025-01-27  
**آخر تحديث**: 2025-01-27  
**الإصدار**: 1.0.0  
**المطور**: AI Agent - SmartPOS System
