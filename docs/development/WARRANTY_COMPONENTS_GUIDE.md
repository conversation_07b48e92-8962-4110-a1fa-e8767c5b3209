# 🛠️ دليل المطورين - مكونات نظام إدارة الضمانات

## 📋 نظرة عامة

هذا الدليل يوضح البنية التقنية والمكونات المستخدمة في نظام إدارة الضمانات، مع أمثلة عملية للتطوير والتخصيص.

## 🏗️ البنية العامة

### هيكل المجلدات
```
frontend/src/
├── types/warranty.ts                    # أنواع البيانات TypeScript
├── stores/warranty/                     # مخازن البيانات Zustand
│   ├── warrantyTypesStore.ts           # إدارة أنواع الضمانات
│   ├── productWarrantiesStore.ts       # إدارة ضمانات المنتجات
│   ├── warrantyClaimsStore.ts          # إدارة مطالبات الضمان
│   ├── warrantyReportsStore.ts         # إدارة التقارير
│   └── index.ts                        # تصدير المخازن
├── components/warranty/                 # مكونات واجهة المستخدم
│   ├── WarrantyTypesTab.tsx            # تبويب أنواع الضمانات
│   ├── ProductWarrantiesTab.tsx        # تبويب ضمانات المنتجات
│   ├── WarrantyClaimsTab.tsx           # تبويب مطالبات الضمان
│   ├── WarrantyReportsTab.tsx          # تبويب التقارير
│   └── index.ts                        # تصدير المكونات
└── pages/WarrantyManagement.tsx         # الصفحة الرئيسية
```

## 📝 أنواع البيانات (TypeScript Types)

### الواجهات الأساسية

#### WarrantyType - أنواع الضمانات
```typescript
interface WarrantyType {
  id: number;
  name: string;                    // الاسم بالإنجليزية
  name_ar: string;                 // الاسم بالعربية
  description?: string | null;     // الوصف
  duration_months: number;         // مدة الضمان بالأشهر
  coverage_type: 'full' | 'partial' | 'limited';  // نوع التغطية
  terms_conditions?: string | null; // الشروط والأحكام
  is_active: boolean;              // حالة التفعيل
  created_at: string;              // تاريخ الإنشاء
  updated_at?: string | null;      // تاريخ التحديث
  created_by: number;              // معرف المنشئ
  updated_by?: number | null;      // معرف المحدث
}
```

#### ProductWarranty - ضمانات المنتجات
```typescript
interface ProductWarranty {
  id: number;
  product_id: number;              // معرف المنتج
  product_name: string;            // اسم المنتج
  product_sku: string;             // رمز المنتج
  warranty_type_id: number;        // معرف نوع الضمان
  warranty_type_name: string;      // اسم نوع الضمان
  warranty_number: string;         // رقم الضمان الفريد
  purchase_date: string;           // تاريخ الشراء
  start_date: string;              // تاريخ بداية الضمان
  end_date: string;                // تاريخ انتهاء الضمان
  customer_id?: number | null;     // معرف العميل
  customer_name?: string | null;   // اسم العميل
  status: 'active' | 'expired' | 'voided' | 'claimed';  // الحالة
  notes?: string | null;           // ملاحظات
  // ... باقي الحقول
}
```

#### WarrantyClaim - مطالبات الضمان
```typescript
interface WarrantyClaim {
  id: number;
  warranty_id: number;             // معرف الضمان
  warranty_number: string;         // رقم الضمان
  product_name: string;            // اسم المنتج
  customer_name?: string | null;   // اسم العميل
  claim_number: string;            // رقم المطالبة
  claim_type: 'repair' | 'replacement' | 'refund';  // نوع المطالبة
  issue_description: string;       // وصف المشكلة
  claim_date: string;              // تاريخ المطالبة
  status: 'pending' | 'approved' | 'rejected' | 'in_progress' | 'completed';
  resolution?: string | null;      // القرار/الحل
  resolution_date?: string | null; // تاريخ القرار
  cost_amount?: number | null;     // التكلفة
  notes?: string | null;           // ملاحظات
  // ... باقي الحقول
}
```

### الثوابت والتسميات
```typescript
export const WARRANTY_STATUS_LABELS = {
  active: 'نشط',
  expired: 'منتهي',
  voided: 'ملغي',
  claimed: 'مطالب به'
} as const;

export const CLAIM_STATUS_LABELS = {
  pending: 'في الانتظار',
  approved: 'موافق عليه',
  rejected: 'مرفوض',
  in_progress: 'قيد التنفيذ',
  completed: 'مكتمل'
} as const;

export const COVERAGE_TYPE_LABELS = {
  full: 'شامل',
  partial: 'جزئي',
  limited: 'محدود'
} as const;
```

## 🗄️ إدارة الحالة (Zustand Stores)

### مثال: warrantyTypesStore.ts

#### البنية الأساسية
```typescript
interface WarrantyTypesState {
  // البيانات
  warrantyTypes: WarrantyType[];
  loading: boolean;
  error: string | null;
  selectedType: WarrantyType | null;

  // الإجراءات
  fetchWarrantyTypes: (filters?: WarrantyTypeFilters) => Promise<void>;
  createWarrantyType: (data: CreateWarrantyTypeData) => Promise<WarrantyType>;
  updateWarrantyType: (id: number, data: UpdateWarrantyTypeData) => Promise<WarrantyType>;
  deleteWarrantyType: (id: number) => Promise<void>;
  setSelectedType: (type: WarrantyType | null) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}
```

#### مثال على إجراء
```typescript
fetchWarrantyTypes: async (filters = {}) => {
  try {
    set({ loading: true, error: null });
    console.log('🔄 جلب أنواع الضمانات...');

    const queryParams = new URLSearchParams();
    if (filters.search) queryParams.append('search', filters.search);
    if (filters.status && filters.status !== 'all') {
      queryParams.append('active_only', (filters.status === 'active').toString());
    }

    const response = await api.get(`/api/warranty-types/?${queryParams.toString()}`);
    
    console.log('✅ تم جلب أنواع الضمانات:', response.data.length, 'نوع');
    set({ warrantyTypes: response.data, loading: false });
  } catch (error: any) {
    console.error('❌ خطأ في جلب أنواع الضمانات:', error);
    set({
      error: error.response?.data?.detail || 'فشل في جلب أنواع الضمانات',
      loading: false
    });
  }
}
```

### مميزات المخازن
- **معالجة الأخطاء:** شاملة مع رسائل واضحة
- **التسجيل:** جميع العمليات مسجلة في وحدة التحكم
- **إدارة التحميل:** مؤشرات تحميل لجميع العمليات
- **التحديث التلقائي:** تحديث الواجهة فور تغيير البيانات

## 🎨 مكونات واجهة المستخدم

### الصفحة الرئيسية: WarrantyManagement.tsx

#### البنية الأساسية
```typescript
const WarrantyManagement: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Stores
  const warrantyTypesStore = useWarrantyTypesStore();
  const productWarrantiesStore = useProductWarrantiesStore();
  const warrantyClaimsStore = useWarrantyClaimsStore();
  const warrantyReportsStore = useWarrantyReportsStore();

  // State
  const [activeTab, setActiveTab] = useState<'warranty-types' | 'product-warranties' | 'warranty-claims' | 'warranty-reports'>('warranty-types');

  // تحديد التبويب النشط حسب المسار
  useEffect(() => {
    const path = location.pathname;
    if (path === '/warranty-types') setActiveTab('warranty-types');
    else if (path === '/product-warranties') setActiveTab('product-warranties');
    // ... باقي التبويبات
  }, [location.pathname]);

  return (
    <div className="container mx-auto px-4 py-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      {/* Header */}
      {/* Tabs */}
      {/* Content */}
    </div>
  );
};
```

### مكونات التبويبات

#### مثال: WarrantyTypesTab.tsx
```typescript
const WarrantyTypesTab: React.FC = () => {
  const {
    warrantyTypes,
    loading,
    fetchWarrantyTypes,
    createWarrantyType,
    updateWarrantyType,
    deleteWarrantyType
  } = useWarrantyTypesStore();

  // State للفلاتر والنماذج
  const [filters, setFilters] = useState<WarrantyTypeFilters>({});
  const [typeModal, setTypeModal] = useState({ isOpen: false, mode: 'create', type: null });

  // تحميل البيانات عند التحميل
  useEffect(() => {
    fetchWarrantyTypes(filters);
  }, [fetchWarrantyTypes]);

  return (
    <div className="space-y-6">
      {/* Header مع زر الإضافة */}
      {/* Filters */}
      {/* Table */}
      {/* Modals */}
    </div>
  );
};
```

### الأنماط والتصميم

#### نظام الألوان
```typescript
const statusColors = {
  active: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
  expired: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
  voided: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
  claimed: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
};
```

#### الأيقونات المستخدمة
```typescript
import {
  FiShield,      // الأيقونة الرئيسية
  FiSettings,    // أنواع الضمانات
  FiPackage,     // ضمانات المنتجات
  FiFileText,    // مطالبات الضمان
  FiBarChart,    // التقارير
  FiPlus,        // إضافة
  FiEdit,        // تعديل
  FiTrash,       // حذف
  FiEye,         // عرض
  FiCheckCircle, // موافقة
  FiXCircle,     // رفض
  FiClock        // تمديد
} from 'react-icons/fi';
```

## 🔗 التكامل مع النظام

### المسارات (Routes)
```typescript
// في AppContent.tsx
const WarrantyManagement = React.lazy(() => import('../pages/WarrantyManagement'));

// المسارات
<Route path="/warranties" element={<WarrantyManagement />} />
<Route path="/warranty-types" element={<WarrantyManagement />} />
<Route path="/product-warranties" element={<WarrantyManagement />} />
<Route path="/warranty-claims" element={<WarrantyManagement />} />
<Route path="/warranty-reports" element={<WarrantyManagement />} />
```

### القائمة الجانبية
```typescript
// في sidebarStore.ts
{
  id: 'warranties',
  name: 'إدارة الضمانات',
  path: '/warranties',
  icon: 'FiShield',
  iconColor: 'text-blue-600 dark:text-blue-400',
  subItems: [
    { id: 'warranty-types', name: 'أنواع الضمانات', path: '/warranty-types' },
    { id: 'product-warranties', name: 'ضمانات المنتجات', path: '/product-warranties' },
    { id: 'warranty-claims', name: 'مطالبات الضمان', path: '/warranty-claims' },
    { id: 'warranty-reports', name: 'تقارير الضمانات', path: '/warranty-reports' }
  ]
}

// في Sidebar.tsx - إضافة الأيقونة
const iconMap = {
  // ... الأيقونات الأخرى
  FiShield
};
```

## 🧪 الاختبار والتطوير

### تشغيل النظام
```bash
# تشغيل الخادم الأمامي
cd frontend && npm run dev

# تشغيل الخادم الخلفي (إذا متوفر)
cd backend && npm run dev
```

### اختبار المكونات
```typescript
// مثال على اختبار مكون
import { render, screen } from '@testing-library/react';
import WarrantyTypesTab from '../WarrantyTypesTab';

test('renders warranty types tab', () => {
  render(<WarrantyTypesTab />);
  expect(screen.getByText('إدارة أنواع الضمانات')).toBeInTheDocument();
});
```

## 🔧 التخصيص والتطوير

### إضافة ميزة جديدة
1. **إضافة النوع في types/warranty.ts**
2. **تحديث المخزن المناسب**
3. **إضافة المكون في واجهة المستخدم**
4. **تحديث API endpoints**

### تخصيص التصميم
```typescript
// تخصيص الألوان
const customColors = {
  primary: 'bg-purple-600 hover:bg-purple-700',
  // ... ألوان أخرى
};

// تخصيص الأيقونات
import { FiCustomIcon } from 'react-icons/fi';
```

## 📊 API المطلوبة

### نقاط النهاية
```typescript
// أنواع الضمانات
GET    /api/warranty-types/           // جلب جميع الأنواع
POST   /api/warranty-types/           // إنشاء نوع جديد
PUT    /api/warranty-types/{id}       // تحديث نوع
DELETE /api/warranty-types/{id}       // حذف نوع

// ضمانات المنتجات
GET    /api/warranties/               // جلب جميع الضمانات
POST   /api/warranties/               // إنشاء ضمان جديد
POST   /api/warranties/{id}/extend    // تمديد ضمان
POST   /api/warranties/{id}/void      // إلغاء ضمان

// مطالبات الضمان
GET    /api/warranty-claims/          // جلب جميع المطالبات
POST   /api/warranty-claims/          // إنشاء مطالبة جديدة
PUT    /api/warranty-claims/{id}      // تحديث مطالبة

// التقارير
GET    /api/warranty-stats/           // إحصائيات الضمانات
GET    /api/warranties/expiring       // الضمانات المنتهية قريباً
GET    /api/warranty-claims/statistics // إحصائيات المطالبات
GET    /api/warranty-reports/export   // تصدير التقارير
```

---

**للمطورين:** هذا الدليل يوفر الأساس للتطوير والتخصيص  
**تاريخ الدليل:** يوليو 2025  
**الإصدار:** 1.0.0
