# 📋 أمثلة عملية لدعم الصور - من الكود الفعلي

> **أمثلة مفصلة من تطبيق دعم الصور في العلامات التجارية والفئات**

## 🏷️ مثال العلامات التجارية (Brands)

### 1. Frontend - دوال معالجة الصور

```typescript
// frontend/src/components/catalog/BrandsDataTable.tsx

// حالة رفع الصور
const [logoUpload, setLogoUpload] = useState({
  isUploading: false,
  uploadedImagePath: '',
  showUploadComponent: false
});

// دالة نجاح رفع الصورة
const handleLogoUploadSuccess = (result: ImageUploadResult) => {
  console.log('✅ تم رفع صورة الشعار بنجاح:', result);
  if (result.success && result.file_path) {
    setLogoUpload(prev => ({
      ...prev,
      isUploading: false,
      uploadedImagePath: result.file_path || '',
      showUploadComponent: false
    }));
    setBrandForm(prev => ({ ...prev, logo_url: result.file_path || '' }));
  }
};

// دالة حذف الصورة
const handleRemoveLogo = async () => {
  try {
    const currentImagePath = logoUpload.uploadedImagePath || brandForm.logo_url;
    
    // حذف الصورة من المجلد إذا كانت موجودة
    if (currentImagePath) {
      console.log('🗑️ حذف صورة الشعار من المجلد:', currentImagePath);
      const deleteResult = await imageManagementService.deleteImage(currentImagePath, true);
      
      if (deleteResult.success) {
        console.log('✅ تم حذف صورة الشعار من المجلد بنجاح');
      } else {
        console.warn('⚠️ فشل في حذف صورة الشعار من المجلد:', deleteResult.error);
      }
    }
    
    // إعادة تعيين الحالة
    setLogoUpload({
      isUploading: false,
      uploadedImagePath: '',
      showUploadComponent: false
    });
    setBrandForm(prev => ({ ...prev, logo_url: '' }));
    
  } catch (error) {
    console.error('❌ خطأ في حذف صورة الشعار:', error);
    // إعادة تعيين الحالة حتى لو فشل الحذف
    setLogoUpload({
      isUploading: false,
      uploadedImagePath: '',
      showUploadComponent: false
    });
    setBrandForm(prev => ({ ...prev, logo_url: '' }));
  }
};
```

### 2. عرض الصورة في الجدول

```tsx
{/* عمود الشعار في جدول العلامات التجارية */}
<td className="px-6 py-4 whitespace-nowrap">
  <div className="flex items-center justify-center">
    {brand.logo_url ? (
      <div className="relative">
        <img
          src={imageManagementService.getImageUrl(brand.logo_url)}
          alt={`شعار ${brand.name}`}
          className="w-10 h-10 rounded-lg object-cover border border-gray-200 dark:border-gray-600"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
            const fallback = target.parentElement?.nextElementSibling as HTMLElement;
            if (fallback) fallback.style.display = 'flex';
          }}
        />
        <div className="w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-700 items-center justify-center hidden">
          <FiImage className="w-5 h-5 text-gray-400" />
        </div>
      </div>
    ) : (
      <div className="w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
        <FiImage className="w-5 h-5 text-gray-400" />
      </div>
    )}
  </div>
</td>
```

### 3. قسم رفع الصور في النموذج

```tsx
{/* Logo Upload Section */}
<div className="space-y-3">
  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
    شعار العلامة التجارية (اختياري)
  </label>
  
  {/* Current Logo Display */}
  {(logoUpload.uploadedImagePath || brandForm.logo_url) && (
    <div className="flex items-center gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600">
      <img
        src={imageManagementService.getImageUrl(logoUpload.uploadedImagePath || brandForm.logo_url)}
        alt="شعار العلامة التجارية"
        className="w-16 h-16 rounded-lg object-cover border border-gray-200 dark:border-gray-600"
      />
      <div className="flex-1">
        <p className="text-sm text-gray-600 dark:text-gray-400">الشعار الحالي</p>
        <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
          {logoUpload.uploadedImagePath || brandForm.logo_url}
        </p>
      </div>
      <button
        type="button"
        onClick={handleRemoveLogo}
        className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-200 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
      >
        <FiTrash className="w-4 h-4" />
      </button>
    </div>
  )}

  {/* Upload Component */}
  {logoUpload.showUploadComponent ? (
    <div className="border border-gray-200 dark:border-gray-600 rounded-xl p-4">
      <ImageUploadComponent
        folder="brands"
        multiple={false}
        generateThumbnails={true}
        maxFiles={1}
        onUploadSuccess={handleLogoUploadSuccess}
        onUploadError={handleLogoUploadError}
        className="w-full"
      />
    </div>
  ) : (
    <button
      type="button"
      onClick={() => setLogoUpload(prev => ({ ...prev, showUploadComponent: true }))}
      className="w-full flex items-center justify-center gap-2 p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl hover:border-primary-500 dark:hover:border-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-all duration-200 text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400"
    >
      <FiUpload className="w-5 h-5" />
      {logoUpload.uploadedImagePath || brandForm.logo_url ? 'تغيير الشعار' : 'رفع شعار العلامة التجارية'}
    </button>
  )}
</div>
```

### 4. دالة حذف العلامة التجارية مع الصورة

```typescript
const confirmDelete = async () => {
  try {
    setDeleteModal(prev => ({ ...prev, isLoading: true }));
    
    // العثور على العلامة التجارية المراد حذفها
    const brandToDelete = brands.find(brand => brand.id === deleteModal.id);
    
    // حذف صورة الشعار إذا كانت موجودة
    if (brandToDelete?.logo_url) {
      console.log('🗑️ حذف صورة شعار العلامة التجارية من المجلد:', brandToDelete.logo_url);
      try {
        const deleteResult = await imageManagementService.deleteImage(brandToDelete.logo_url, true);
        if (deleteResult.success) {
          console.log('✅ تم حذف صورة الشعار من المجلد بنجاح');
        } else {
          console.warn('⚠️ فشل في حذف صورة الشعار من المجلد:', deleteResult.error);
        }
      } catch (imageError) {
        console.warn('⚠️ خطأ في حذف صورة الشعار:', imageError);
      }
    }
    
    // حذف العلامة التجارية من قاعدة البيانات
    await deleteBrand(deleteModal.id);
    setSuccessModal({ isOpen: true, message: 'تم حذف العلامة التجارية بنجاح' });
    setDeleteModal({ isOpen: false, id: 0, name: '', isLoading: false });
    await loadBrands();
  } catch (error) {
    console.error('Error deleting brand:', error);
    setDeleteModal(prev => ({ ...prev, isLoading: false }));
  }
};
```

## 📂 مثال الفئات (Categories)

### 1. Migration لإضافة حقل الصورة

```python
# backend/migrations/add_category_image_url.py

import logging
import sys
import os

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.dirname(current_dir)
sys.path.append(backend_dir)

from sqlalchemy import text
from database.session import engine

logger = logging.getLogger(__name__)

def add_category_image_url():
    """إضافة حقل image_url لجدول الفئات"""
    try:
        logger.info("🔄 بدء إضافة حقل image_url لجدول الفئات...")
        
        with engine.connect() as conn:
            # التحقق من وجود العمود أولاً
            check_column_query = text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'categories' 
                AND column_name = 'image_url'
            """)
            
            result = conn.execute(check_column_query)
            column_exists = result.fetchone()
            
            if column_exists:
                logger.info("✅ حقل image_url موجود بالفعل في جدول الفئات")
                return True
            
            # إضافة العمود الجديد
            add_column_query = text("""
                ALTER TABLE categories 
                ADD COLUMN image_url VARCHAR(255) NULL
            """)
            
            conn.execute(add_column_query)
            conn.commit()
            
            logger.info("✅ تم إضافة حقل image_url لجدول الفئات بنجاح")
            return True
                
    except Exception as e:
        logger.error(f"❌ خطأ في إضافة حقل image_url: {e}")
        return False

def main():
    """تشغيل migration إضافة حقل image_url للفئات"""
    try:
        logger.info("🚀 بدء migration إضافة حقل image_url للفئات...")
        
        success = add_category_image_url()
        
        if success:
            logger.info("🎉 تم إنجاز migration بنجاح!")
            print("✅ تم إضافة حقل image_url لجدول الفئات بنجاح")
        else:
            logger.error("❌ فشل في تنفيذ migration")
            print("❌ فشل في إضافة حقل image_url لجدول الفئات")
            
    except Exception as e:
        logger.error(f"❌ خطأ عام في migration: {e}")
        print(f"❌ خطأ في تنفيذ migration: {e}")

if __name__ == "__main__":
    # إعداد التسجيل
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    main()
```

### 2. تحديث النموذج والSchema

```python
# backend/models/category.py
class Category(Base):
    __tablename__ = "categories"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, unique=True)
    description = Column(Text, nullable=True)
    image_url = Column(String(255), nullable=True)  # For category image
    is_active = Column(Boolean, default=True)

# backend/schemas/category.py
class CategoryBase(BaseModel):
    """Base schema for category data."""
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    image_url: Optional[str] = Field(None, max_length=255)
    is_active: bool = True

class CategoryUpdate(BaseModel):
    """Schema for updating an existing category."""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    image_url: Optional[str] = Field(None, max_length=255)
    is_active: Optional[bool] = None
```

### 3. تحديث Store في Frontend

```typescript
// frontend/src/stores/categoryStore.ts
export interface Category {
  id: number;
  name: string;
  description: string | null;
  image_url: string | null;  // ← إضافة حقل الصورة
  is_active: boolean;
  created_at: string;
  updated_at: string | null;
  created_by: number;
  updated_by: number | null;
}
```

## 🎨 التصميم الموحد

### شارات الحالة الموحدة

```tsx
{/* للعلامات التجارية والفئات والوحدات */}
{item.is_active ? (
  <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800">
    <FiEye className="w-3 h-3 ml-1" />
    نشط
  </span>
) : (
  <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 border border-gray-200 dark:border-gray-700">
    <FiEyeOff className="w-3 h-3 ml-1" />
    غير نشط
  </span>
)}
```

### أزرار الإجراءات الموحدة

```tsx
{/* للعلامات التجارية والفئات والوحدات */}
<div className="flex items-center justify-end gap-1">
  <button
    onClick={() => handleEditItem(item)}
    className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 transition-colors duration-150 p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 border border-transparent hover:border-blue-200 dark:hover:border-blue-700"
    title="تعديل العنصر"
  >
    <FiEdit className="w-4 h-4" />
  </button>
  <button
    onClick={() => handleDeleteItem(item)}
    className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 transition-colors duration-150 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 border border-transparent hover:border-red-200 dark:hover:border-red-700"
    title="حذف العنصر"
  >
    <FiTrash className="w-4 h-4" />
  </button>
</div>
```

## 📁 هيكل المجلدات النهائي

```
backend/static/uploads/
├── .gitkeep
├── brands/
│   ├── .gitkeep
│   ├── logo_1.jpg
│   ├── logo_2.png
│   └── thumbnails/
│       ├── small_logo_1.jpg
│       ├── medium_logo_1.jpg
│       └── large_logo_1.jpg
├── categories/
│   ├── .gitkeep
│   ├── category_1.jpg
│   └── thumbnails/
│       ├── small_category_1.jpg
│       ├── medium_category_1.jpg
│       └── large_category_1.jpg
├── products/
│   └── .gitkeep
├── users/
│   └── .gitkeep
└── temp/
    └── .gitkeep
```

## ✅ نتائج التطبيق

### ما تم تحقيقه:
- ✅ **دعم كامل للصور** في العلامات التجارية والفئات
- ✅ **رفع صورة واحدة فقط** لكل عنصر
- ✅ **حذف الصور من المجلد** عند الحذف أو الإزالة
- ✅ **تصميم موحد** عبر جميع التبويبات
- ✅ **معالجة شاملة للأخطاء**
- ✅ **إعداد Git** لتجاهل الصور المرفوعة
- ✅ **توثيق شامل** للمطورين

### الفوائد:
- 🚀 **سهولة التطبيق** على كيانات جديدة
- 🎨 **تجربة مستخدم موحدة** عبر النظام
- 🔒 **أمان أفضل** مع حذف الصور غير المستخدمة
- 📱 **استجابة سريعة** مع الصور المصغرة
- 🛠️ **صيانة أسهل** مع الكود المنظم
