# 📚 دليل نظام خصائص المتغيرات الشامل

## 🎯 نظرة عامة

نظام خصائص المتغيرات هو نظام متكامل لإدارة خصائص المنتجات في SmartPOS. يتيح للمستخدمين إنشاء وإدارة خصائص مخصصة مثل الحجم، اللون، المادة، والوزن مع قيمها المختلفة.

## 🏗️ الهيكل التقني

### Backend Architecture

#### 1. النماذج (Models)
```python
# backend/models/variant_attribute.py
class VariantAttribute(Base):
    __tablename__ = "variant_attributes"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, unique=True, index=True)
    name_ar = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    attribute_type = Column(String(50), default='text', nullable=False)
    is_required = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    sort_order = Column(Integer, default=0)
    
    # Timestamps & User tracking
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(Integer, ForeignKey("users.id"))
    updated_by = Column(Integer, ForeignKey("users.id"))
    
    # Relationships
    values = relationship("VariantValue", back_populates="attribute", cascade="all, delete-orphan")

# backend/models/variant_value.py
class VariantValue(Base):
    __tablename__ = "variant_values"
    
    id = Column(Integer, primary_key=True, index=True)
    attribute_id = Column(Integer, ForeignKey("variant_attributes.id", ondelete="CASCADE"))
    value = Column(String(100), nullable=False, index=True)
    value_ar = Column(String(100), nullable=False, index=True)
    color_code = Column(String(7), nullable=True)  # للألوان - hex color code
    is_active = Column(Boolean, default=True)
    sort_order = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
```

#### 2. الخدمات (Services)
```python
# backend/services/variant_attribute_service.py
class VariantAttributeService:
    """
    خدمة شاملة لإدارة خصائص المتغيرات
    تطبق مبادئ البرمجة الكائنية مع معالجة شاملة للأخطاء
    """
    
    def __init__(self, db_session: Session):
        self.db = db_session
    
    # إدارة الخصائص
    async def get_all_attributes(self, include_inactive: bool = False) -> List[VariantAttribute]
    async def get_attribute_by_id(self, attribute_id: int) -> Optional[VariantAttribute]
    async def create_attribute(self, attribute_data: VariantAttributeCreate, user_id: int) -> VariantAttribute
    async def update_attribute(self, attribute_id: int, attribute_data: VariantAttributeUpdate, user_id: int) -> VariantAttribute
    async def delete_attribute(self, attribute_id: int) -> bool
    async def reorder_attributes(self, attribute_orders: List[AttributeOrderUpdate]) -> bool
    
    # إدارة قيم الخصائص
    async def get_attribute_values(self, attribute_id: int, include_inactive: bool = False) -> List[VariantValue]
    async def add_attribute_value(self, attribute_id: int, value_data: VariantValueCreate) -> VariantValue
    async def update_attribute_value(self, value_id: int, value_data: VariantValueUpdate) -> VariantValue
    async def delete_attribute_value(self, value_id: int) -> bool
    async def reorder_attribute_values(self, value_orders: List[ValueOrderUpdate]) -> bool
```

#### 3. مسارات API
```python
# backend/routers/variant_attributes.py
router = APIRouter(prefix="/api/variant-attributes", tags=["variant-attributes"])

# مسارات الخصائص
@router.get("/", response_model=List[VariantAttributeResponse])
@router.get("/{attribute_id}", response_model=VariantAttributeResponse)
@router.post("/", response_model=VariantAttributeResponse)
@router.put("/{attribute_id}", response_model=VariantAttributeResponse)
@router.delete("/{attribute_id}")
@router.put("/reorder")

# مسارات قيم الخصائص
@router.get("/{attribute_id}/values", response_model=List[VariantValueResponse])
@router.post("/{attribute_id}/values", response_model=VariantValueResponse)
@router.put("/values/{value_id}", response_model=VariantValueResponse)
@router.delete("/values/{value_id}")
@router.put("/values/reorder")
```

### Frontend Architecture

#### 1. الأنواع (Types)
```typescript
// frontend/src/types/variantAttribute.ts
export interface VariantAttribute {
  id: number;
  name: string;
  name_ar: string;
  description?: string | null;
  attribute_type: 'text' | 'color' | 'list' | 'number';
  is_required: boolean;
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at?: string | null;
  created_by: number;
  updated_by?: number | null;
  values: VariantValue[];
}

export interface VariantValue {
  id: number;
  attribute_id: number;
  value: string;
  value_ar: string;
  color_code?: string | null;
  is_active: boolean;
  sort_order: number;
  created_at: string;
}

export const ATTRIBUTE_TYPES = {
  text: { value: 'text', label: 'نص', label_en: 'Text' },
  color: { value: 'color', label: 'لون', label_en: 'Color' },
  list: { value: 'list', label: 'قائمة', label_en: 'List' },
  number: { value: 'number', label: 'رقم', label_en: 'Number' }
} as const;
```

#### 2. الخدمات (Services)
```typescript
// frontend/src/services/variantAttributeService.ts
export class VariantAttributeService {
  private static instance: VariantAttributeService;
  private baseUrl = '/api/variant-attributes';

  public static getInstance(): VariantAttributeService {
    if (!VariantAttributeService.instance) {
      VariantAttributeService.instance = new VariantAttributeService();
    }
    return VariantAttributeService.instance;
  }

  // إدارة الخصائص
  public async getAllAttributes(includeInactive: boolean = false): Promise<VariantAttribute[]>
  public async getAttributeById(attributeId: number): Promise<VariantAttribute>
  public async createAttribute(attributeData: CreateVariantAttributeData): Promise<VariantAttribute>
  public async updateAttribute(attributeId: number, attributeData: UpdateVariantAttributeData): Promise<VariantAttribute>
  public async deleteAttribute(attributeId: number): Promise<void>
  public async reorderAttributes(attributeOrders: AttributeOrderUpdate[]): Promise<void>

  // إدارة قيم الخصائص
  public async getAttributeValues(attributeId: number, includeInactive: boolean = false): Promise<VariantValue[]>
  public async addAttributeValue(attributeId: number, valueData: CreateVariantValueData): Promise<VariantValue>
  public async updateAttributeValue(valueId: number, valueData: UpdateVariantValueData): Promise<VariantValue>
  public async deleteAttributeValue(valueId: number): Promise<void>
  public async reorderAttributeValues(valueOrders: ValueOrderUpdate[]): Promise<void>
}
```

#### 3. إدارة الحالة (Store)
```typescript
// frontend/src/stores/variantAttributeStore.ts
interface VariantAttributeStore {
  // State
  attributes: VariantAttribute[];
  loading: boolean;
  error: string | null;

  // Actions - إدارة الخصائص
  fetchAttributes: (includeInactive?: boolean) => Promise<void>;
  createAttribute: (data: CreateVariantAttributeData) => Promise<VariantAttribute>;
  updateAttribute: (id: number, data: UpdateVariantAttributeData) => Promise<VariantAttribute>;
  deleteAttribute: (id: number) => Promise<void>;
  reorderAttributes: (orders: AttributeOrderUpdate[]) => Promise<void>;

  // Actions - إدارة قيم الخصائص
  addAttributeValue: (attributeId: number, value: CreateVariantValueData) => Promise<VariantValue>;
  updateAttributeValue: (valueId: number, value: UpdateVariantValueData) => Promise<VariantValue>;
  deleteAttributeValue: (valueId: number) => Promise<void>;
  reorderAttributeValues: (orders: ValueOrderUpdate[]) => Promise<void>;

  // Utility actions
  setError: (error: string | null) => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}
```

#### 4. المكونات (Components)
```typescript
// frontend/src/components/catalog/VariantAttributesDataTable.tsx
const VariantAttributesDataTable: React.FC = () => {
  // State management
  const { attributes, loading, error, fetchAttributes, deleteAttribute, clearError } = useVariantAttributeStore();
  
  // Filtering and search
  const [filters, setFilters] = useState<VariantAttributeFilters>({
    search: '',
    status: 'all',
    attributeType: 'all',
    hasValues: 'all'
  });

  // Modal states
  const [attributeModal, setAttributeModal] = useState({...});
  const [deleteModal, setDeleteModal] = useState({...});
  const [successModal, setSuccessModal] = useState({...});

  // Handlers
  const handleCreateAttribute = () => {...};
  const handleEditAttribute = (attribute: VariantAttribute) => {...};
  const handleDeleteAttribute = (attribute: VariantAttribute) => {...};
  const confirmDelete = async () => {...};
  const handleRefresh = () => {...};

  return (
    <div className="space-y-6">
      {/* Header with actions */}
      {/* Filters */}
      {/* Data table */}
      {/* Modals */}
    </div>
  );
};

// frontend/src/components/catalog/VariantAttributeModal.tsx
const VariantAttributeModal: React.FC<VariantAttributeModalProps> = ({
  isOpen, onClose, mode, attribute, onSuccess
}) => {
  // Form state
  const [formData, setFormData] = useState({...});
  const [values, setValues] = useState<CreateVariantValueData[]>([]);
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  // Validation
  const validateForm = () => {...};

  // Handlers
  const handleSubmit = async (e: React.FormEvent) => {...};
  const handleAddValue = () => {...};
  const handleRemoveValue = (index: number) => {...};
  const handleValueChange = (index: number, field: keyof CreateVariantValueData, value: any) => {...};

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={...} size="lg">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        {/* Values Section */}
        {/* Actions */}
      </form>
    </Modal>
  );
};
```

## 🔧 الميزات الرئيسية

### 1. إدارة الخصائص
- ✅ **إنشاء خصائص جديدة** مع قيمها
- ✅ **تعديل الخصائص الموجودة**
- ✅ **حذف الخصائص** (Soft Delete)
- ✅ **إعادة ترتيب الخصائص**
- ✅ **تفعيل/إلغاء تفعيل الخصائص**

### 2. إدارة قيم الخصائص
- ✅ **إضافة قيم للخصائص**
- ✅ **تعديل القيم الموجودة**
- ✅ **حذف القيم**
- ✅ **إعادة ترتيب القيم**
- ✅ **دعم أكواد الألوان** للخصائص اللونية

### 3. أنواع الخصائص المدعومة
- **text**: نص عادي
- **color**: ألوان مع أكواد hex
- **list**: قائمة من القيم المحددة مسبقاً
- **number**: قيم رقمية

### 4. الفلترة والبحث
- ✅ **البحث في أسماء الخصائص** (عربي وإنجليزي)
- ✅ **فلترة حسب الحالة** (نشط/غير نشط)
- ✅ **فلترة حسب نوع الخاصية**
- ✅ **فلترة حسب وجود القيم**

## 📊 قاعدة البيانات

### الجداول
```sql
-- جدول الخصائص الرئيسية
CREATE TABLE variant_attributes (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    name_ar VARCHAR(100) NOT NULL,
    description TEXT,
    attribute_type VARCHAR(50) DEFAULT 'text' NOT NULL,
    is_required BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id)
);

-- جدول قيم الخصائص
CREATE TABLE variant_values (
    id SERIAL PRIMARY KEY,
    attribute_id INTEGER NOT NULL REFERENCES variant_attributes(id) ON DELETE CASCADE,
    value VARCHAR(100) NOT NULL,
    value_ar VARCHAR(100) NOT NULL,
    color_code VARCHAR(7), -- للألوان - hex color code
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### الفهارس
```sql
-- فهارس الأداء
CREATE INDEX idx_variant_attributes_name ON variant_attributes(name);
CREATE INDEX idx_variant_attributes_name_ar ON variant_attributes(name_ar);
CREATE INDEX idx_variant_attributes_active ON variant_attributes(is_active);
CREATE INDEX idx_variant_attributes_sort ON variant_attributes(sort_order);

CREATE INDEX idx_variant_values_attribute_id ON variant_values(attribute_id);
CREATE INDEX idx_variant_values_value ON variant_values(value);
CREATE INDEX idx_variant_values_value_ar ON variant_values(value_ar);
CREATE INDEX idx_variant_values_active ON variant_values(is_active);
CREATE INDEX idx_variant_values_sort ON variant_values(sort_order);
```

## 🔌 API Endpoints

### خصائص المتغيرات
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/variant-attributes/` | جلب جميع الخصائص |
| GET | `/api/variant-attributes/{id}` | جلب خاصية محددة |
| POST | `/api/variant-attributes/` | إنشاء خاصية جديدة |
| PUT | `/api/variant-attributes/{id}` | تحديث خاصية |
| DELETE | `/api/variant-attributes/{id}` | حذف خاصية |
| PUT | `/api/variant-attributes/reorder` | إعادة ترتيب الخصائص |

### قيم الخصائص
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/variant-attributes/{id}/values` | جلب قيم خاصية |
| POST | `/api/variant-attributes/{id}/values` | إضافة قيمة جديدة |
| PUT | `/api/variant-attributes/values/{id}` | تحديث قيمة |
| DELETE | `/api/variant-attributes/values/{id}` | حذف قيمة |
| PUT | `/api/variant-attributes/values/reorder` | إعادة ترتيب القيم |

## 🎨 واجهة المستخدم

### التبويب في CatalogManagement
```typescript
// إضافة تبويب جديد في CatalogManagement.tsx
<button
  onClick={() => {
    setActiveTab('variant-attributes');
    navigate('/variant-attributes');
  }}
  className={`py-4 px-6 border-b-2 font-medium text-sm flex items-center transition-all duration-200 ease-in-out ${
    activeTab === 'variant-attributes'
      ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
      : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
  }`}
>
  <FiLayers className="ml-2" />
  خصائص المتغيرات
</button>
```

### جدول البيانات
- **عرض منظم** للخصائص مع معلوماتها الأساسية
- **أزرار العمليات** (تعديل، حذف)
- **مؤشرات الحالة** (نشط/غير نشط)
- **عداد قيم** كل خاصية
- **فلترة وبحث متقدم**

### نافذة الإضافة/التعديل
- **نموذج شامل** لإدخال بيانات الخاصية
- **إدارة القيم** داخل نفس النافذة
- **دعم أكواد الألوان** للخصائص اللونية
- **التحقق من صحة البيانات**

## 🔒 الأمان والصلاحيات

### المصادقة والتخويل
- جميع العمليات تتطلب **مصادقة المستخدم**
- تسجيل **المستخدم المنشئ والمحدث**
- **Soft delete** للحفاظ على سلامة البيانات

### التحقق من صحة البيانات
```python
# Backend validation
class VariantAttributeCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    name_ar: str = Field(..., min_length=1, max_length=100)
    attribute_type: str = Field(default='text', pattern=r'^(text|color|list|number)$')
    # ...

class VariantValueCreate(BaseModel):
    value: str = Field(..., min_length=1, max_length=100)
    value_ar: str = Field(..., min_length=1, max_length=100)
    color_code: Optional[str] = Field(None, max_length=7, pattern=r'^#[0-9A-Fa-f]{6}$')
    # ...
```

```typescript
// Frontend validation
const validateForm = () => {
  const newErrors: {[key: string]: string} = {};

  if (!formData.name.trim()) {
    newErrors.name = 'اسم الخاصية مطلوب';
  }

  if (!formData.name_ar.trim()) {
    newErrors.name_ar = 'الاسم العربي مطلوب';
  }

  if (formData.attribute_type === 'color' || formData.attribute_type === 'list') {
    if (values.length === 0) {
      newErrors.values = 'يجب إضافة قيمة واحدة على الأقل';
    }
  }

  // Validate values
  values.forEach((value, index) => {
    if (!value.value.trim()) {
      newErrors[`value_${index}`] = 'القيمة مطلوبة';
    }
    if (!value.value_ar.trim()) {
      newErrors[`value_ar_${index}`] = 'القيمة العربية مطلوبة';
    }
    if (formData.attribute_type === 'color' && !value.color_code) {
      newErrors[`color_${index}`] = 'كود اللون مطلوب';
    }
  });

  setErrors(newErrors);
  return Object.keys(newErrors).length === 0;
};
```

## 📈 البيانات الافتراضية

### الخصائص الافتراضية
```sql
INSERT INTO variant_attributes (name, name_ar, attribute_type, sort_order, created_by) VALUES
('Size', 'الحجم', 'list', 1, 1),
('Color', 'اللون', 'color', 2, 1),
('Material', 'المادة', 'list', 3, 1),
('Weight', 'الوزن', 'list', 4, 1),
('Style', 'النمط', 'list', 5, 1),
('Pattern', 'النقشة', 'list', 6, 1),
('Memory', 'الذاكرة', 'list', 7, 1),
('Storage', 'التخزين', 'list', 8, 1),
('Length', 'الطول', 'list', 9, 1),
('Capacity', 'السعة', 'list', 10, 1);
```

### القيم الافتراضية
```sql
-- قيم الحجم
INSERT INTO variant_values (attribute_id, value, value_ar, sort_order) VALUES
((SELECT id FROM variant_attributes WHERE name = 'Size'), 'XS', 'صغير جداً', 1),
((SELECT id FROM variant_attributes WHERE name = 'Size'), 'S', 'صغير', 2),
((SELECT id FROM variant_attributes WHERE name = 'Size'), 'M', 'متوسط', 3),
((SELECT id FROM variant_attributes WHERE name = 'Size'), 'L', 'كبير', 4),
((SELECT id FROM variant_attributes WHERE name = 'Size'), 'XL', 'كبير جداً', 5),
((SELECT id FROM variant_attributes WHERE name = 'Size'), 'XXL', 'كبير جداً جداً', 6);

-- قيم الألوان
INSERT INTO variant_values (attribute_id, value, value_ar, color_code, sort_order) VALUES
((SELECT id FROM variant_attributes WHERE name = 'Color'), 'Red', 'أحمر', '#FF0000', 1),
((SELECT id FROM variant_attributes WHERE name = 'Color'), 'Blue', 'أزرق', '#0000FF', 2),
((SELECT id FROM variant_attributes WHERE name = 'Color'), 'Green', 'أخضر', '#00FF00', 3),
((SELECT id FROM variant_attributes WHERE name = 'Color'), 'Black', 'أسود', '#000000', 4),
((SELECT id FROM variant_attributes WHERE name = 'Color'), 'White', 'أبيض', '#FFFFFF', 5);
```

## 🧪 الاختبار

### اختبار Backend
```bash
# تشغيل الخادم
cd backend
source venv/bin/activate
uvicorn main:app --host 0.0.0.0 --port 8001 --reload

# اختبار API endpoints
curl -X GET "http://localhost:8001/api/variant-attributes/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### اختبار Frontend
```bash
# تشغيل التطبيق
cd frontend
npm start

# الوصول للنظام
# http://localhost:3000/catalog-management
# انقر على تبويب "خصائص المتغيرات"
```

## 🚀 التطوير المستقبلي

### المرحلة التالية
1. **ربط الخصائص بالمنتجات**
2. **إنشاء متغيرات المنتجات**
3. **إدارة المخزون لكل متغير**
4. **تقارير المبيعات حسب المتغيرات**

### التحسينات المقترحة
1. **دعم الصور للقيم**
2. **خصائص ديناميكية (حسب الفئة)**
3. **قوالب خصائص جاهزة**
4. **استيراد/تصدير الخصائص**
5. **تحليلات استخدام الخصائص**

## 📝 ملاحظات التطوير

### مبادئ البرمجة المتبعة
- ✅ **البرمجة الكائنية (OOP)**
- ✅ **معالجة شاملة للأخطاء**
- ✅ **استخدام أيقونات معتمدة** من react-icons/fi
- ✅ **دعم كامل للغة العربية**
- ✅ **تصميم متجاوب**
- ✅ **أمان البيانات**
- ✅ **Singleton pattern** في الخدمات
- ✅ **State management** مع Zustand

### الأخطاء الشائعة وحلولها
1. **خطأ في استيراد المكونات**: تأكد من المسارات الصحيحة
2. **خطأ في أنواع البيانات**: استخدم TypeScript بشكل صحيح
3. **خطأ في API**: تحقق من المصادقة والصلاحيات
4. **خطأ في قاعدة البيانات**: تأكد من تشغيل migrations

---

**تاريخ الإنشاء**: 2025-01-27  
**الإصدار**: 1.0.0  
**المطور**: AI Agent - SmartPOS System  
**الحالة**: ✅ مكتمل ومختبر
