# فهرس مكونات النظام

## نظرة عامة

هذا المجلد يحتوي على توثيق جميع المكونات المطورة في النظام، بما في ذلك المكونات الجديدة والمحدثة.

## المكونات المتوفرة

### 1. نظام الأرقام المختصرة (Compact Numbers System)

#### الملفات:
- **[COMPACT_NUMBERS_DOCUMENTATION.md](./COMPACT_NUMBERS_DOCUMENTATION.md)** - التوثيق الشامل للنظام
- **[COMPACT_NUMBERS_DEVELOPER_GUIDE.md](./COMPACT_NUMBERS_DEVELOPER_GUIDE.md)** - دليل المطورين والمرجع السريع

#### المكونات المشمولة:
- `CompactNumberDisplay` - مكون عرض الأرقام المختصرة
- `CompactStatCard` - بطاقة إحصائية محسنة
- `GrowthRateCard` - بطاقة معدل النمو المخصصة
- `compactNumberService` - خدمة تنسيق الأرقام
- `useCompactNumber` - Hook للاستخدام المباشر

#### الميزات الرئيسية:
- ✅ اختصار الأرقام الكبيرة (K, M, B, T)
- ✅ دعم متعدد اللغات (عربي/إنجليزي)
- ✅ تنسيق العملات
- ✅ الأرقام الصحيحة بدون فاصلة عشرية
- ✅ نظام كاش ذكي
- ✅ معالجة شاملة للأخطاء
- ✅ دعم الوضع المظلم
- ✅ تصميم متجاوب

#### التطبيقات:
- لوحة التحكم (Dashboard)
- صفحة التقارير (Reports)
- البطاقات الإحصائية
- الرسوم البيانية

### 2. مكونات أخرى (قيد التطوير)

#### مكونات التاريخ والوقت:
- `FormattedDate` - تنسيق التواريخ
- `FormattedTime` - تنسيق الأوقات
- `FormattedDateTime` - تنسيق التاريخ والوقت

#### مكونات العملة:
- `FormattedCurrency` - تنسيق العملات
- `CurrencyInput` - إدخال العملات

#### مكونات الرسوم البيانية:
- `ArabicChart` - رسوم بيانية بدعم العربية
- `ChartTooltip` - تلميحات الرسوم البيانية

## إرشادات الاستخدام

### للمطورين الجدد:
1. ابدأ بقراءة التوثيق الشامل
2. راجع دليل المطورين للأمثلة السريعة
3. اختبر المكونات في بيئة التطوير
4. اتبع أفضل الممارسات المذكورة

### للمطورين المتقدمين:
1. راجع الكود المصدري للمكونات
2. اطلع على اختبارات الوحدة
3. ساهم في تحسين المكونات
4. أضف ميزات جديدة حسب الحاجة

## هيكل الملفات

```
docs/components/
├── README.md                           # هذا الملف
├── COMPACT_NUMBERS_DOCUMENTATION.md   # التوثيق الشامل للأرقام المختصرة
├── COMPACT_NUMBERS_DEVELOPER_GUIDE.md # دليل المطورين
└── [مكونات أخرى قيد الإضافة]
```

## معايير التوثيق

### لكل مكون جديد يجب توفير:
1. **توثيق شامل** - وصف كامل للمكون وميزاته
2. **دليل المطورين** - أمثلة سريعة ومرجع للخصائص
3. **أمثلة عملية** - حالات استخدام حقيقية
4. **اختبارات** - اختبارات وحدة وتكامل
5. **إرشادات الأداء** - نصائح للاستخدام الأمثل

### تنسيق الملفات:
- استخدام Markdown للتوثيق
- عناوين واضحة ومنظمة
- أمثلة كود مع تعليقات
- جداول للخصائص والمعاملات
- روابط للملفات ذات الصلة

## المساهمة

### إضافة مكون جديد:
1. أنشئ ملف توثيق شامل
2. أنشئ دليل مطورين
3. أضف أمثلة عملية
4. اختبر المكون بدقة
5. حدث هذا الفهرس

### تحديث مكون موجود:
1. حدث التوثيق الشامل
2. حدث دليل المطورين
3. أضف أمثلة جديدة إذا لزم الأمر
4. اختبر التغييرات
5. حدث رقم الإصدار

## الإصدارات

### الحالية:
- **نظام الأرقام المختصرة**: v1.0.0

### المخطط لها:
- **مكونات التاريخ والوقت**: v1.1.0
- **مكونات الرسوم البيانية المحسنة**: v1.2.0
- **نظام الإشعارات**: v1.3.0

## الدعم والمساعدة

### للحصول على المساعدة:
1. راجع التوثيق أولاً
2. ابحث في الأمثلة العملية
3. اطلع على قسم استكشاف الأخطاء
4. تواصل مع فريق التطوير

### الإبلاغ عن المشاكل:
1. وصف المشكلة بوضوح
2. أرفق أمثلة كود
3. اذكر البيئة والإصدار
4. اقترح حلول إن أمكن

---

**آخر تحديث**: 2025-01-17  
**المسؤول**: فريق التطوير  
**الحالة**: نشط ومحدث
