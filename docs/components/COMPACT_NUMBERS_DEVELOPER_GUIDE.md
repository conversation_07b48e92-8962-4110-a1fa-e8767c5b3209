# دليل المطورين - نظام الأرقام المختصرة

## مرجع سريع للمطورين

### الاستيراد والاستخدام الأساسي

```typescript
// استيراد المكونات
import { CompactNumberDisplay, CompactStatCard, GrowthRateCard } from '../components/CompactNumberDisplay';
import { useCompactNumber } from '../hooks/useCompactNumber';

// استيراد الخدمة مباشرة
import { compactNumberService } from '../services/compactNumberService';
```

### أمثلة سريعة

#### 1. عرض رقم مختصر بسيط
```tsx
<CompactNumberDisplay amount={1500000} />
// النتيجة: 1.5M
```

#### 2. عرض عملة مختصرة
```tsx
<CompactNumberDisplay 
  amount={2500000} 
  showCurrency={true} 
  unitType="english" 
/>
// النتيجة: 2.5M د.ل
```

#### 3. عرض رقم صحيح
```tsx
<CompactNumberDisplay 
  amount={1250} 
  isInteger={true}
  showFullNumber={false}
/>
// النتيجة: 1K (بدلاً من 1.3K)
```

#### 4. بطاقة إحصائية كاملة
```tsx
<CompactStatCard
  title="إجمالي المبيعات"
  amount={1234567}
  icon={<FaShoppingCart className="text-blue-500" />}
  showCurrency={true}
  isInteger={false}
  compactThreshold={1000}
  unitType="english"
  changeText="إجمالي عمليات البيع"
/>
```

#### 5. بطاقة معدل النمو
```tsx
<GrowthRateCard
  growthRate={25.5}
  currentTotal={1500000}
  previousTotal={1200000}
  hasData={true}
  isLoading={false}
  selectedPeriod="month"
/>
```

### استخدام Hook

```typescript
const MyComponent = () => {
  const { formatCompact, formatCompactCurrency } = useCompactNumber();
  
  const handleFormat = async () => {
    // تنسيق رقم عادي
    const result1 = await formatCompact(1500000, {
      unitType: 'english',
      compactThreshold: 1000,
      decimalPlaces: 1
    });
    
    // تنسيق عملة
    const result2 = await formatCompactCurrency(2500000, {
      unitType: 'english',
      compactThreshold: 1000
    });
    
    console.log(result1.compact); // "1.5M"
    console.log(result2.compact); // "2.5M د.ل"
  };
  
  return <button onClick={handleFormat}>تنسيق</button>;
};
```

### استخدام الخدمة مباشرة

```typescript
// الحصول على نسخة الخدمة
const service = compactNumberService.getInstance();

// تنسيق رقم
const result = await service.formatCompact(1500000, {
  unitType: 'english',
  compactThreshold: 1000,
  decimalPlaces: 1,
  showFullNumber: true
});

console.log(result.compact);    // "1.5M"
console.log(result.full);       // "1,500,000"
console.log(result.original);   // 1500000
```

## الخصائص والمعاملات

### CompactNumberDisplay Props

| الخاصية | النوع | الافتراضي | الوصف |
|---------|------|----------|-------|
| `amount` | `number` | مطلوب | المبلغ المراد عرضه |
| `showCurrency` | `boolean` | `false` | إظهار رمز العملة |
| `isInteger` | `boolean` | `false` | عرض كرقم صحيح |
| `compactThreshold` | `number` | `1000` | الحد الأدنى للاختصار |
| `unitType` | `'arabic' \| 'english'` | `'arabic'` | نوع الوحدات |
| `decimalPlaces` | `number` | `1` | عدد الأرقام العشرية |
| `size` | `'small' \| 'medium' \| 'large'` | `'medium'` | حجم العرض |
| `showFullNumber` | `boolean` | `true` | إظهار الرقم الكامل |
| `className` | `string` | `''` | فئة CSS إضافية |
| `loadingText` | `string` | `'...'` | نص التحميل |
| `errorText` | `string` | `'خطأ'` | نص الخطأ |
| `onError` | `(error: Error) => void` | - | دالة معالجة الأخطاء |

### CompactStatCard Props

| الخاصية | النوع | الافتراضي | الوصف |
|---------|------|----------|-------|
| `title` | `string` | مطلوب | عنوان البطاقة |
| `amount` | `number` | مطلوب | المبلغ |
| `icon` | `React.ReactNode` | - | أيقونة البطاقة |
| `showCurrency` | `boolean` | `false` | إظهار العملة |
| `isInteger` | `boolean` | `false` | رقم صحيح |
| `compactThreshold` | `number` | `1000` | حد الاختصار |
| `unitType` | `UnitType` | `'english'` | نوع الوحدات |
| `changeText` | `string` | - | نص التغيير |
| `className` | `string` | `''` | فئة CSS |

### GrowthRateCard Props

| الخاصية | النوع | الوصف |
|---------|------|-------|
| `growthRate` | `number` | معدل النمو بالنسبة المئوية |
| `currentTotal` | `number` | القيمة الحالية |
| `previousTotal` | `number` | القيمة السابقة |
| `hasData` | `boolean` | وجود بيانات للمقارنة |
| `isLoading` | `boolean` | حالة التحميل |
| `selectedPeriod` | `string` | الفترة المحددة |
| `className` | `string` | فئة CSS إضافية |

## إعدادات الخدمة

### CompactNumberSettings

```typescript
interface CompactNumberSettings {
  unitType: 'arabic' | 'english';     // نوع الوحدات
  compactThreshold: number;           // الحد الأدنى للاختصار
  decimalPlaces: number;              // عدد الأرقام العشرية
  showFullNumber: boolean;            // إظهار الرقم الكامل
}
```

### الوحدات المدعومة

#### الإنجليزية (english):
- `K` للآلاف (1,000)
- `M` للملايين (1,000,000)
- `B` للمليارات (1,000,000,000)
- `T` للتريليونات (1,000,000,000,000)

#### العربية (arabic):
- `أ` للآلاف
- `م` للملايين
- `ب` للمليارات
- `ت` للتريليونات

## أمثلة متقدمة

### 1. تخصيص كامل
```tsx
<CompactNumberDisplay
  amount={1234567.89}
  showCurrency={true}
  isInteger={false}
  compactThreshold={10000}
  unitType="english"
  decimalPlaces={2}
  size="large"
  showFullNumber={true}
  className="custom-number-display"
  loadingText="جاري التحميل..."
  errorText="حدث خطأ"
  onError={(error) => console.error('خطأ في التنسيق:', error)}
/>
```

### 2. استخدام متقدم للخدمة
```typescript
const service = compactNumberService.getInstance();

// تحديث الإعدادات العامة
service.updateSettings({
  unitType: 'english',
  compactThreshold: 1000,
  decimalPlaces: 1
});

// مسح الكاش
service.clearCache();

// تنسيق متعدد
const amounts = [1500, 15000, 150000, 1500000];
const results = await Promise.all(
  amounts.map(amount => service.formatCompact(amount))
);

results.forEach((result, index) => {
  console.log(`${amounts[index]} → ${result.compact}`);
});
```

### 3. معالجة الأخطاء
```tsx
const ErrorHandlingExample = () => {
  const [error, setError] = useState<string | null>(null);
  
  const handleError = (err: Error) => {
    setError(err.message);
    console.error('خطأ في تنسيق الرقم:', err);
  };
  
  return (
    <div>
      <CompactNumberDisplay
        amount={NaN} // قيمة خاطئة لاختبار معالجة الأخطاء
        onError={handleError}
        errorText="قيمة غير صحيحة"
      />
      {error && <p className="text-red-500">خطأ: {error}</p>}
    </div>
  );
};
```

## نصائح للأداء

### 1. استخدام الكاش
```typescript
// الخدمة تستخدم كاش تلقائي لمدة 10 دقائق
// لا حاجة لإعادة حساب نفس القيم
```

### 2. تجميع العمليات
```typescript
// بدلاً من استدعاءات متعددة
const result1 = await formatCompact(1000);
const result2 = await formatCompact(2000);
const result3 = await formatCompact(3000);

// استخدم Promise.all
const results = await Promise.all([
  formatCompact(1000),
  formatCompact(2000),
  formatCompact(3000)
]);
```

### 3. تجنب الاستدعاءات غير الضرورية
```tsx
// سيء - يعيد الحساب في كل render
const BadComponent = ({ amount }) => (
  <CompactNumberDisplay amount={amount} />
);

// جيد - استخدام useMemo للقيم المحسوبة
const GoodComponent = ({ amount }) => {
  const memoizedAmount = useMemo(() => amount, [amount]);
  
  return <CompactNumberDisplay amount={memoizedAmount} />;
};
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. الرقم لا يظهر مختصراً
```typescript
// تأكد من أن الرقم أكبر من compactThreshold
<CompactNumberDisplay 
  amount={500} 
  compactThreshold={1000} // 500 < 1000 لذا لن يختصر
/>

// الحل: تقليل compactThreshold أو زيادة amount
<CompactNumberDisplay 
  amount={500} 
  compactThreshold={100} // الآن سيظهر كـ 500
/>
```

#### 2. الأرقام الصحيحة تظهر بفاصلة عشرية
```typescript
// المشكلة
<CompactNumberDisplay amount={1250} />
// النتيجة: 1.3K

// الحل
<CompactNumberDisplay amount={1250} isInteger={true} />
// النتيجة: 1K
```

#### 3. رمز العملة لا يظهر
```typescript
// تأكد من تفعيل showCurrency
<CompactNumberDisplay 
  amount={1500000} 
  showCurrency={true} 
/>
```

---

**آخر تحديث**: 2025-01-17  
**الإصدار**: 1.0.0
