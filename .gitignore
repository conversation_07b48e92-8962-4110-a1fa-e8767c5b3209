# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Build outputs
dist/
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Backup files
*.backup
*.bak

# Temporary files
*.tmp
*.temp

# SSL certificates
*.pem
*.key
*.crt

# Test coverage
coverage/
.coverage
.nyc_output

# Cache
.cache/
.parcel-cache/

# Lock files (keep package-lock.json but ignore others)
yarn.lock
pnpm-lock.yaml

# Image uploads and static files
backend/static/uploads/
backend/static/images/
frontend/public/uploads/
frontend/public/images/

# Image Management Service uploads
backend/static/uploads/brands/
backend/static/uploads/categories/
backend/static/uploads/products/
backend/static/uploads/users/
backend/static/uploads/temp/

# Thumbnails
backend/static/uploads/*/thumbnails/
backend/static/uploads/*/thumb_*
backend/static/uploads/*/small_*
backend/static/uploads/*/medium_*
backend/static/uploads/*/large_*

# Image file types
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.webp
*.svg
*.ico
*.tiff
*.tif

# But keep placeholder images and icons in specific directories
!frontend/public/icons/
!frontend/public/logos/
!frontend/src/assets/
!docs/images/
!docs/screenshots/

# Keep sample/demo images
!**/sample.*
!**/demo.*
!**/placeholder.*
!**/default.*
