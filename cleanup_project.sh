#!/bin/bash

# 🧹 سكريبت تنظيف المشروع الشامل
# يقوم بإزالة الملفات غير الضرورية والمكررة

echo "🧹 بدء تنظيف المشروع..."

# إزالة ملفات Python المترجمة
echo "🗑️ إزالة ملفات Python المترجمة..."
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null
find . -name "*.pyc" -delete 2>/dev/null
find . -name "*.pyo" -delete 2>/dev/null
find . -name "*.pyd" -delete 2>/dev/null

# إزالة ملفات النسخ الاحتياطي
echo "🗑️ إزالة ملفات النسخ الاحتياطي..."
find . -name "*.backup" -delete 2>/dev/null
find . -name "*.bak" -delete 2>/dev/null
find . -name "*~" -delete 2>/dev/null

# إزالة ملفات مؤقتة
echo "🗑️ إزالة الملفات المؤقتة..."
find . -name "*.tmp" -delete 2>/dev/null
find . -name "*.temp" -delete 2>/dev/null
find . -name ".DS_Store" -delete 2>/dev/null
find . -name "Thumbs.db" -delete 2>/dev/null

# إزالة ملفات IDE
echo "🗑️ إزالة ملفات IDE..."
find . -name "*.swp" -delete 2>/dev/null
find . -name "*.swo" -delete 2>/dev/null

# إزالة ملفات السجلات القديمة
echo "🗑️ إزالة ملفات السجلات القديمة..."
find . -name "*.log" -mtime +7 -delete 2>/dev/null

# تنظيف مجلد frontend
if [ -d "frontend" ]; then
    echo "🧹 تنظيف مجلد Frontend..."
    cd frontend
    
    # إزالة node_modules إذا كان موجود وإعادة تثبيت
    if [ -d "node_modules" ]; then
        echo "🗑️ إزالة node_modules القديم..."
        rm -rf node_modules
    fi
    
    # إزالة package-lock.json إذا كان موجود
    if [ -f "package-lock.json" ]; then
        echo "🗑️ إزالة package-lock.json..."
        rm package-lock.json
    fi
    
    cd ..
fi

# تنظيف مجلد backend
if [ -d "backend" ]; then
    echo "🧹 تنظيف مجلد Backend..."
    cd backend
    
    # إزالة ملفات قاعدة البيانات المؤقتة
    find . -name "*.db-journal" -delete 2>/dev/null
    find . -name "*.db-wal" -delete 2>/dev/null
    find . -name "*.db-shm" -delete 2>/dev/null
    
    cd ..
fi

# إحصائيات التنظيف
echo ""
echo "📊 إحصائيات المشروع بعد التنظيف:"
echo "📁 عدد ملفات Python: $(find . -name "*.py" -not -path "./backend/venv/*" -not -path "./frontend/node_modules/*" | wc -l)"
echo "📁 عدد ملفات TypeScript: $(find . -name "*.ts" -not -path "./frontend/node_modules/*" | wc -l)"
echo "📁 عدد ملفات TSX: $(find . -name "*.tsx" -not -path "./frontend/node_modules/*" | wc -l)"
echo "📁 حجم المشروع (بدون node_modules/venv): $(du -sh --exclude=node_modules --exclude=venv --exclude=.git . | cut -f1)"

echo ""
echo "✅ تم تنظيف المشروع بنجاح!"
echo "💡 لإعادة تثبيت التبعيات:"
echo "   Backend: cd backend && pip install -r requirements.txt"
echo "   Frontend: cd frontend && npm install"
