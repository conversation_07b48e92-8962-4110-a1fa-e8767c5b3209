#!/bin/bash

# SmartPOS Network Startup Script
# سكريبت تشغيل SmartPOS للشبكة المحلية

echo "🚀 Starting SmartPOS for Network Access"
echo "======================================="

# Check if we're in the right directory
if [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    echo "❌ Error: Please run this script from the SmartPOS root directory"
    exit 1
fi

# Function to get local IP
get_local_ip() {
    # Try different methods to get local IP
    local ip=""
    
    # Method 1: Using hostname -I (Linux)
    if command -v hostname >/dev/null 2>&1; then
        ip=$(hostname -I | awk '{print $1}' 2>/dev/null)
    fi
    
    # Method 2: Using ifconfig (macOS/Linux)
    if [ -z "$ip" ] && command -v ifconfig >/dev/null 2>&1; then
        ip=$(ifconfig | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | head -1)
    fi
    
    # Method 3: Using ip command (Linux)
    if [ -z "$ip" ] && command -v ip >/dev/null 2>&1; then
        ip=$(ip route get 1 | awk '{print $7}' | head -1 2>/dev/null)
    fi
    
    # Fallback
    if [ -z "$ip" ]; then
        ip="localhost"
    fi
    
    echo "$ip"
}

# Get local IP
LOCAL_IP=$(get_local_ip)

echo "🌐 Detected Local IP: $LOCAL_IP"
echo ""

# Start backend
echo "🔧 Starting Backend Server..."
cd backend
python main.py &
BACKEND_PID=$!
cd ..

# Wait a moment for backend to start
sleep 3

# Start frontend
echo "🎨 Starting Frontend Server..."
cd frontend
NODE_ENV=development npm run dev:network &
FRONTEND_PID=$!
cd ..

echo ""
echo "✅ SmartPOS Started Successfully!"
echo "================================="
echo "🌐 Local Access:"
echo "   Frontend: http://localhost:5175"
echo "   Backend:  http://localhost:8002"
echo ""
echo "🌍 Network Access:"
echo "   Frontend: http://$LOCAL_IP:5175"
echo "   Backend:  http://$LOCAL_IP:8002"
echo ""
echo "📱 Access from other devices using: http://$LOCAL_IP:5175"
echo ""
echo "🛑 To stop the servers, press Ctrl+C"

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Stopping servers..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    echo "✅ Servers stopped"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup INT TERM

# Wait for user to stop
wait
