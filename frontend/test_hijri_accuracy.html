<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار دقة التحويل الهجري</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-row {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            margin: 5px 0;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .accurate {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .inaccurate {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .date-input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .stats {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار دقة التحويل الهجري</h1>
        
        <div>
            <label>اختبار تاريخ محدد:</label>
            <input type="date" id="testDate" class="date-input" value="2025-07-13">
            <button onclick="testSingleDate()">اختبار</button>
            <div id="singleResult"></div>
        </div>

        <div class="stats">
            <h3>إحصائيات الاختبار:</h3>
            <div id="stats"></div>
        </div>

        <div>
            <button onclick="runAccuracyTest()">اختبار شامل للدقة</button>
            <div id="testResults"></div>
        </div>
    </div>

    <script>
        // دالة التحويل الهجري المحسنة (نفس الدالة من المكون)
        function formatHijriDate(date, isShort = false, adjustment = 0) {
            try {
                const gregorianYear = date.getFullYear();
                const gregorianMonth = date.getMonth() + 1;
                const gregorianDay = date.getDate();
                
                // تحويل إلى Julian Day Number
                let a = Math.floor((14 - gregorianMonth) / 12);
                let y = gregorianYear - a;
                let m = gregorianMonth + 12 * a - 3;
                
                let jd = gregorianDay + Math.floor((153 * m + 2) / 5) + 365 * y + 
                         Math.floor(y / 4) - Math.floor(y / 100) + Math.floor(y / 400) + 1721119;
                
                // تحويل إلى التاريخ الهجري
                const hijriEpochJD = 1948085;
                const daysSinceHijriEpoch = jd - hijriEpochJD;
                
                let hijriYear = Math.floor(daysSinceHijriEpoch / 354.36667) + 1;
                let yearStart = Math.floor((hijriYear - 1) * 354.36667);
                
                if (daysSinceHijriEpoch < yearStart) {
                    hijriYear--;
                    yearStart = Math.floor((hijriYear - 1) * 354.36667);
                }
                
                const dayOfYear = daysSinceHijriEpoch - yearStart;
                
                const monthLengths = [30, 29, 30, 29, 30, 29, 30, 29, 30, 29, 30, 29];
                
                // السنوات الكبيسة الهجرية
                const leapYears = [2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29];
                if (leapYears.includes(hijriYear % 30)) {
                    monthLengths[11] = 30;
                }
                
                let hijriMonth = 1;
                let remainingDays = Math.floor(dayOfYear);
                
                for (let i = 0; i < 12; i++) {
                    if (remainingDays <= monthLengths[i]) {
                        hijriMonth = i + 1;
                        break;
                    }
                    remainingDays -= monthLengths[i];
                }
                
                let hijriDay = Math.max(1, remainingDays);
                
                // تطبيق التعديل
                hijriDay += adjustment;
                
                // معالجة تجاوز الحدود
                if (hijriDay <= 0) {
                    hijriMonth = hijriMonth === 1 ? 12 : hijriMonth - 1;
                    hijriDay += monthLengths[hijriMonth - 1];
                    if (hijriMonth === 12) hijriYear--;
                } else if (hijriDay > monthLengths[hijriMonth - 1]) {
                    hijriDay -= monthLengths[hijriMonth - 1];
                    hijriMonth = hijriMonth === 12 ? 1 : hijriMonth + 1;
                    if (hijriMonth === 1) hijriYear++;
                }
                
                const hijriMonthNames = [
                    'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
                    'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
                ];
                
                if (isShort) {
                    return `${hijriDay.toString().padStart(2, '0')}/${hijriMonth.toString().padStart(2, '0')}/${hijriYear}`;
                } else {
                    const monthName = hijriMonthNames[hijriMonth - 1];
                    return `${hijriDay} ${monthName} ${hijriYear}`;
                }
            } catch (error) {
                console.error('Error formatting Hijri date:', error);
                return 'خطأ في التحويل';
            }
        }

        // تواريخ مرجعية معروفة للاختبار
        const knownDates = [
            { gregorian: '2025-07-13', hijri: '17 محرم 1447', description: 'اليوم الحالي' },
            { gregorian: '2024-01-01', hijri: '19 جمادى الثانية 1445', description: 'رأس السنة الميلادية 2024' },
            { gregorian: '2024-07-07', hijri: '1 محرم 1446', description: 'رأس السنة الهجرية 1446' },
            { gregorian: '2023-04-21', hijri: '1 رمضان 1444', description: 'بداية رمضان 1444' },
            { gregorian: '2023-06-28', hijri: '10 ذو الحجة 1444', description: 'عيد الأضحى 1444' },
            { gregorian: '2022-05-02', hijri: '1 شوال 1443', description: 'عيد الفطر 1443' },
            { gregorian: '2020-01-01', hijri: '5 جمادى الأولى 1441', description: 'رأس السنة 2020' },
            { gregorian: '2000-01-01', hijri: '24 رمضان 1420', description: 'الألفية الجديدة' }
        ];

        function testSingleDate() {
            const dateInput = document.getElementById('testDate');
            const date = new Date(dateInput.value);
            const result = formatHijriDate(date, false, 0);
            
            document.getElementById('singleResult').innerHTML = `
                <div class="test-row">
                    <span><strong>التاريخ الميلادي:</strong> ${date.toLocaleDateString('ar-SA')}</span>
                    <span><strong>التاريخ الهجري:</strong> ${result}</span>
                </div>
            `;
        }

        function runAccuracyTest() {
            const results = [];
            let accurateCount = 0;
            
            knownDates.forEach(testCase => {
                const date = new Date(testCase.gregorian);
                const calculated = formatHijriDate(date, false, 0);
                const isAccurate = calculated === testCase.hijri;
                
                if (isAccurate) accurateCount++;
                
                results.push({
                    ...testCase,
                    calculated,
                    isAccurate
                });
            });
            
            // عرض الإحصائيات
            const accuracy = (accurateCount / knownDates.length * 100).toFixed(1);
            document.getElementById('stats').innerHTML = `
                <p><strong>إجمالي الاختبارات:</strong> ${knownDates.length}</p>
                <p><strong>النتائج الدقيقة:</strong> ${accurateCount}</p>
                <p><strong>النتائج غير الدقيقة:</strong> ${knownDates.length - accurateCount}</p>
                <p><strong>نسبة الدقة:</strong> ${accuracy}%</p>
            `;
            
            // عرض النتائج التفصيلية
            const resultsHtml = results.map(result => `
                <div class="test-row ${result.isAccurate ? 'accurate' : 'inaccurate'}">
                    <div>
                        <strong>${result.description}</strong><br>
                        <small>الميلادي: ${result.gregorian}</small>
                    </div>
                    <div>
                        <div>المتوقع: ${result.hijri}</div>
                        <div>المحسوب: ${result.calculated}</div>
                        <div>${result.isAccurate ? '✅ دقيق' : '❌ غير دقيق'}</div>
                    </div>
                </div>
            `).join('');
            
            document.getElementById('testResults').innerHTML = resultsHtml;
        }

        // تشغيل اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            testSingleDate();
            runAccuracyTest();
        };
    </script>
</body>
</html>
