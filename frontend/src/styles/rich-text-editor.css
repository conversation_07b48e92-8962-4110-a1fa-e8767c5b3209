/* محرر النصوص الغني - محسن للغة العربية */

/* الإعدادات الأساسية للمحرر */
[contenteditable] {
  direction: rtl;
  text-align: right;
  font-family: '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
  line-height: 1.6;
  word-wrap: break-word;
  overflow-wrap: break-word;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  caret-color: #3b82f6;
}

/* لون المؤشر في الوضع المظلم */
.dark [contenteditable] {
  caret-color: #60a5fa;
}

/* النص التوضيحي */
[contenteditable]:empty:before {
  content: attr(data-placeholder);
  color: #9CA3AF;
  font-style: italic;
  pointer-events: none;
  opacity: 0.7;
  direction: rtl;
  text-align: right;
}

[contenteditable]:focus:empty:before {
  opacity: 0.5;
}

.dark [contenteditable]:empty:before {
  color: #6B7280;
}

/* تنسيق الفقرات */
[contenteditable] p {
  margin: 0.5rem 0;
  direction: rtl;
  text-align: right;
  line-height: 1.6;
}

[contenteditable] p:first-child {
  margin-top: 0;
}

[contenteditable] p:last-child {
  margin-bottom: 0;
}

/* تنسيق القوائم */
[contenteditable] ul,
[contenteditable] ol {
  padding-right: 1.5rem;
  padding-left: 0;
  margin: 0.5rem 0;
  direction: rtl;
  text-align: right;
}

[contenteditable] li {
  direction: rtl;
  text-align: right;
  margin: 0.25rem 0;
  line-height: 1.5;
}

/* القوائم المتداخلة */
[contenteditable] ul ul,
[contenteditable] ol ol,
[contenteditable] ul ol,
[contenteditable] ol ul {
  margin-right: 1rem;
  margin-left: 0;
}

/* تنسيق النص */
[contenteditable] strong,
[contenteditable] b {
  font-weight: 700;
}

[contenteditable] em,
[contenteditable] i {
  font-style: italic;
}

[contenteditable] u {
  text-decoration: underline;
}

/* الخط الأفقي */
[contenteditable] hr {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 1rem 0;
  opacity: 0.6;
  width: 100%;
}

.dark [contenteditable] hr {
  border-top-color: #4b5563;
}

/* العناصر الكتلية */
[contenteditable] div,
[contenteditable] blockquote,
[contenteditable] h1,
[contenteditable] h2,
[contenteditable] h3,
[contenteditable] h4,
[contenteditable] h5,
[contenteditable] h6 {
  direction: rtl;
  text-align: right;
}

/* تحديد النص */
[contenteditable]::selection,
[contenteditable] *::selection {
  background-color: rgba(59, 130, 246, 0.2);
  color: inherit;
}

.dark [contenteditable]::selection,
.dark [contenteditable] *::selection {
  background-color: rgba(59, 130, 246, 0.4);
  color: inherit;
}

/* التركيز */
[contenteditable]:focus {
  outline: none;
}

/* ضمان السلوك الصحيح للاتجاه */
[contenteditable] * {
  direction: inherit;
}

/* التعامل مع المحتوى المختلط (أرقام، إنجليزي) */
[contenteditable] [dir="ltr"] {
  direction: ltr;
  text-align: left;
  display: inline;
}

/* فواصل الأسطر */
[contenteditable] br {
  line-height: 1.6;
}

/* الفقرات الفارغة */
[contenteditable] p:empty::before {
  content: '\200B'; /* مسافة بعرض صفر */
  direction: rtl;
}

/* التصميم المتجاوب */
@media (max-width: 640px) {
  [contenteditable] {
    font-size: 14px !important;
  }
}

/* أنماط الطباعة */
@media print {
  [contenteditable] {
    background: white !important;
    color: black !important;
    direction: rtl !important;
    text-align: right !important;
  }
}

/* تحسينات إضافية للعربية */
[contenteditable] {
  unicode-bidi: embed;
  text-align-last: right;
  word-spacing: 0.05em;
  letter-spacing: 0.01em;
}

/* تحسين عرض النص العربي */
[contenteditable]:focus {
  white-space: pre-wrap;
}

/* ضمان المحاذاة الصحيحة للأسطر الفارغة */
[contenteditable] p:empty {
  min-height: 1.2em;
}

/* تحسين عرض القوائم النقطية */
[contenteditable] ul {
  list-style-type: disc;
  list-style-position: inside;
}

[contenteditable] ol {
  list-style-type: decimal;
  list-style-position: inside;
}