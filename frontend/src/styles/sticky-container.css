/*
 * 🔧 أنماط مكون StickyContainer
 * يحل مشكلة عدم عمل خاصية sticky مع مكتبة react-custom-scrollbars
 */

/* تحسين مظهر المكون */
.sticky-content {
  transition: transform 0.15s cubic-bezier(0.4, 0, 0.2, 1), top 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 5;
}

/* تحسين الأداء */
.sticky-content {
  will-change: transform, position;
  backface-visibility: hidden;
  transform: translateZ(0);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* تخصيص شريط التمرير */
.sticky-content::-webkit-scrollbar {
  width: 4px;
}

.sticky-content::-webkit-scrollbar-track {
  background: transparent;
}

.sticky-content::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 4px;
}

.dark .sticky-content::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
}