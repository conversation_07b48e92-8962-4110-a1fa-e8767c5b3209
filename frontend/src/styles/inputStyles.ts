/**
 * أنماط موحدة لمكونات الإدخال في النظام
 * يضمن التناسق في الارتفاعات والتصميم عبر جميع مكونات الإدخال
 */

// الارتفاع الموحد لجميع مكونات الإدخال
export const INPUT_HEIGHT = 'h-10'; // 40px

// الأنماط الأساسية المشتركة
export const baseInputStyles = {
  // الارتفاع الموحد
  height: INPUT_HEIGHT,
  
  // التصميم الأساسي
  base: `
    ${INPUT_HEIGHT} rounded-xl border-2 px-4 transition-all duration-200 ease-in-out
    bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
    focus:outline-none
    placeholder:text-gray-400 dark:placeholder:text-gray-500
  `,
  
  // حالات الحدود
  borders: {
    default: 'border-gray-300/60 dark:border-gray-600/40',
    focus: 'border-primary-500 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20',
    error: 'border-red-500 focus:border-red-500 focus:ring-4 focus:ring-red-500/20',
    success: 'border-green-500 focus:border-green-500 focus:ring-4 focus:ring-green-500/20',
    disabled: 'border-gray-200 dark:border-gray-700'
  },
  
  // حالات الخلفية
  backgrounds: {
    default: 'bg-white dark:bg-gray-700',
    disabled: 'bg-gray-50 dark:bg-gray-800',
    focus: 'bg-white dark:bg-gray-700'
  },
  
  // حالات النص
  textColors: {
    default: 'text-gray-900 dark:text-gray-100',
    disabled: 'text-gray-500 dark:text-gray-400',
    placeholder: 'placeholder:text-gray-400 dark:placeholder:text-gray-500'
  }
};

// أنماط محددة لأنواع مختلفة من المدخلات
export const inputVariants = {
  // حقل النص العادي
  text: `
    w-full ${baseInputStyles.base}
  `,
  
  // حقل الأرقام
  number: `
    w-full ${baseInputStyles.base}
    font-mono text-left
  `,
  
  // حقل التحديد (Select)
  select: `
    w-full ${baseInputStyles.height} rounded-xl border-2 px-4 text-right transition-all duration-200 ease-in-out 
    flex items-center cursor-pointer
    ${baseInputStyles.backgrounds.default} ${baseInputStyles.textColors.default}
  `,
  
  // منطقة النص (TextArea)
  textarea: `
    w-full rounded-xl border-2 py-3 px-4 transition-all duration-200 ease-in-out
    ${baseInputStyles.backgrounds.default} ${baseInputStyles.textColors.default}
    ${baseInputStyles.textColors.placeholder}
  `,
  
  // حقل التاريخ
  date: `
    w-full ${baseInputStyles.base}
    cursor-pointer
  `,
  
  // حقل الوقت
  time: `
    w-full ${baseInputStyles.height} rounded-xl border-2 px-4 transition-all duration-200 ease-in-out
    ${baseInputStyles.backgrounds.default} ${baseInputStyles.textColors.default}
  `
};

// دالة مساعدة لبناء أنماط الإدخال
export const buildInputStyles = (
  variant: keyof typeof inputVariants = 'text',
  options: {
    hasIcon?: boolean;
    isFocused?: boolean;
    hasError?: boolean;
    hasSuccess?: boolean;
    isDisabled?: boolean;
    customClasses?: string;
  } = {}
) => {
  const {
    hasIcon = false,
    isFocused = false,
    hasError = false,
    hasSuccess = false,
    isDisabled = false,
    customClasses = ''
  } = options;

  let styles = inputVariants[variant];

  // إضافة padding للأيقونة
  if (hasIcon) {
    styles += ' pr-12';
  }

  // حالة التعطيل
  if (isDisabled) {
    styles += ` ${baseInputStyles.backgrounds.disabled} ${baseInputStyles.textColors.disabled} cursor-not-allowed ${baseInputStyles.borders.disabled}`;
  } else {
    // حالات الحدود
    if (hasError) {
      styles += ` ${baseInputStyles.borders.error}`;
    } else if (hasSuccess) {
      styles += ` ${baseInputStyles.borders.success}`;
    } else if (isFocused) {
      styles += ` ${baseInputStyles.borders.focus}`;
    } else {
      styles += ` ${baseInputStyles.borders.default}`;
    }
  }

  // إضافة أنماط مخصصة
  if (customClasses) {
    styles += ` ${customClasses}`;
  }

  return styles.trim();
};

// أنماط التسميات (Labels)
export const labelStyles = {
  base: 'block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2',
  required: 'text-red-500 mr-1'
};

// أنماط رسائل الخطأ والنجاح
export const messageStyles = {
  error: 'mt-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg text-red-700 dark:text-red-300 text-sm flex items-center',
  success: 'mt-2 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg text-green-700 dark:text-green-300 text-sm flex items-center',
  info: 'mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg text-blue-700 dark:text-blue-300 text-sm flex items-center'
};

// أنماط الأيقونات
export const iconStyles = {
  container: 'absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none z-10',
  base: 'transition-colors duration-200',
  states: {
    default: 'text-gray-400 dark:text-gray-500',
    focus: 'text-primary-500',
    error: 'text-red-500',
    success: 'text-green-500'
  }
};

// أنماط أزرار التحكم (للأرقام مثلاً)
export const controlButtonStyles = {
  base: 'absolute flex items-center justify-center w-6 h-6 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-150',
  increment: 'right-2 top-1',
  decrement: 'right-2 bottom-1'
};

// تصدير جميع الأنماط كمجموعة واحدة
export const inputStylesConfig = {
  INPUT_HEIGHT,
  baseInputStyles,
  inputVariants,
  buildInputStyles,
  labelStyles,
  messageStyles,
  iconStyles,
  controlButtonStyles
};

export default inputStylesConfig;
