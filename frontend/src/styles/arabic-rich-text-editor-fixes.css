/* إصلاحات إضافية لمحرر النصوص العربي */

/* إصلاح شريط الأدوات العام - تصميم مثل حاويات التطبيق */
.ql-toolbar {
  display: flex !important;
  flex-wrap: nowrap !important;
  align-items: center !important;
  justify-content: flex-start !important;
  gap: 6px !important;
  padding: 8px 12px !important;
  overflow-x: auto !important;
  overflow-y: hidden !important;
  min-height: 44px !important;
  background: #f9fafb !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 8px 8px 0 0 !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.ql-toolbar .ql-formats {
  display: flex !important;
  align-items: center !important;
  gap: 2px !important;
  margin: 0 !important;
  flex-shrink: 0 !important;
  padding: 3px !important;
  background: rgba(255, 255, 255, 0.9) !important;
  border-radius: 6px !important;
  border: 1px solid rgba(229, 231, 235, 0.8) !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

.ql-toolbar::-webkit-scrollbar {
  height: 4px !important;
}

.ql-toolbar::-webkit-scrollbar-track {
  background: #f1f5f9 !important;
  border-radius: 2px !important;
}

.ql-toolbar::-webkit-scrollbar-thumb {
  background: #cbd5e1 !important;
  border-radius: 2px !important;
}

.ql-toolbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8 !important;
}

/* إصلاح عناصر الاختيار في Quill */
.ql-toolbar .ql-picker {
  direction: rtl !important;
  position: relative !important;
}

.ql-toolbar .ql-picker-label {
  direction: rtl !important;
  text-align: right !important;
  padding: 6px 20px 6px 12px !important;
  background: white !important;
  border: 1px solid #d1d5db !important;
  border-radius: 8px !important;
  min-width: 80px !important;
  min-height: 32px !important;
  position: relative !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  transition: all 0.2s ease-in-out !important;
  color: #374151 !important;
  font-size: 14px !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

.ql-toolbar .ql-picker-label::before {
  position: absolute !important;
  left: 8px !important;
  right: auto !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  content: "▼" !important;
  font-size: 12px !important;
  color: #6b7280 !important;
  transition: color 0.2s ease-in-out !important;
}

.ql-toolbar .ql-picker-label:hover {
  background: #f9fafb !important;
  border-color: #9ca3af !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
}

.ql-toolbar .ql-picker-label:hover::before {
  color: #374151 !important;
}

.ql-toolbar .ql-picker-options {
  direction: rtl !important;
  text-align: right !important;
  right: 0 !important;
  left: auto !important;
  background: white !important;
  border: 1px solid #d1d5db !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  z-index: 1000 !important;
  overflow: hidden !important;
}

.ql-toolbar .ql-picker-item {
  direction: rtl !important;
  text-align: right !important;
  padding: 8px 16px !important;
  color: #374151 !important;
  font-size: 14px !important;
  transition: all 0.2s ease-in-out !important;
  border-bottom: 1px solid #f3f4f6 !important;
}

.ql-toolbar .ql-picker-item:last-child {
  border-bottom: none !important;
}

.ql-toolbar .ql-picker-item:hover {
  background: #f9fafb !important;
  color: #1f2937 !important;
}

.ql-toolbar .ql-picker-item.ql-selected {
  background: #eff6ff !important;
  color: #3b82f6 !important;
  font-weight: 500 !important;
}

/* إصلاح خاص لعنصر اختيار العناوين - متناسق مع باقي الأزرار */
.ql-toolbar .ql-header {
  min-width: 85px !important;
}

.ql-toolbar .ql-header .ql-picker-label {
  min-width: 85px !important;
  padding: 5px 18px 5px 8px !important;
  height: 28px !important;
}

.ql-toolbar .ql-header .ql-picker-options {
  min-width: 85px !important;
}

.ql-toolbar .ql-header .ql-picker-item {
  padding: 8px 12px !important;
  border-bottom: 1px solid #eee !important;
}

/* إصلاح الأزرار - متماشي مع تصميم التطبيق */
.ql-toolbar button {
  width: 32px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border: 1px solid #d1d5db !important;
  border-radius: 8px !important;
  background: white !important;
  margin: 0 2px !important;
  transition: all 0.2s ease-in-out !important;
  font-size: 14px !important;
  color: #374151 !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

.ql-toolbar button:hover {
  background: #f9fafb !important;
  border-color: #9ca3af !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
  transform: translateY(-1px) !important;
}

.ql-toolbar button.ql-active {
  background: #3b82f6 !important;
  border-color: #3b82f6 !important;
  color: white !important;
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3) !important;
}

.ql-toolbar button:focus {
  outline: none !important;
  ring: 2px !important;
  ring-color: rgba(59, 130, 246, 0.5) !important;
  ring-offset: 2px !important;
}

/* إصلاح أيقونات المحاذاة */
.ql-toolbar .ql-align .ql-picker-label::before {
  content: "⚏" !important;
  font-size: 14px !important;
}

/* إظهار أيقونات الأدوات بوضوح */
.ql-toolbar .ql-bold::before {
  content: "B" !important;
  font-weight: bold !important;
}

.ql-toolbar .ql-italic::before {
  content: "I" !important;
  font-style: italic !important;
}

.ql-toolbar .ql-underline::before {
  content: "U" !important;
  text-decoration: underline !important;
}

.ql-toolbar .ql-strike::before {
  content: "S" !important;
  text-decoration: line-through !important;
}

.ql-toolbar .ql-list[value="ordered"]::before {
  content: "1." !important;
}

.ql-toolbar .ql-list[value="bullet"]::before {
  content: "•" !important;
}

.ql-toolbar .ql-link::before {
  content: "🔗" !important;
}

.ql-toolbar .ql-image::before {
  content: "🖼" !important;
}

/* إخفاء أزرار غير مرغوب فيها */
.ql-direction,
.ql-align,
.ql-image,
.ql-video {
  display: none !important;
}

/* إصلاح القوائم - منع الاختفاء */
.ql-editor {
  overflow: visible !important;
  padding-right: 25px !important;
  padding-left: 15px !important;
}

.ql-container {
  overflow: visible !important;
}

.ql-editor ol,
.ql-editor ul {
  margin-right: 2em !important;
  padding-right: 0 !important;
  padding-left: 0 !important;
  overflow: visible !important;
  position: relative;
}

.ql-editor ol li,
.ql-editor ul li {
  list-style: none !important;
  position: relative;
  overflow: visible !important;
}

/* النقاط والأرقام */
.ql-editor ul li::before {
  content: "•" !important;
  position: absolute !important;
  right: -1.8em !important;
  top: 0 !important;
  color: #374151 !important;
  font-weight: bold !important;
  font-size: 1.2em !important;
  line-height: 1.4 !important;
  z-index: 1 !important;
}

.ql-editor ol {
  counter-reset: arabic-counter !important;
}

.ql-editor ol li {
  counter-increment: arabic-counter !important;
}

.ql-editor ol li::before {
  content: counter(arabic-counter) "." !important;
  position: absolute !important;
  right: -2.2em !important;
  top: 0 !important;
  color: #374151 !important;
  font-weight: bold !important;
  line-height: 1.4 !important;
  z-index: 1 !important;
  min-width: 1.5em !important;
  text-align: left !important;
}

/* الوضع المظلم */
.dark .ql-toolbar {
  background: #374151 !important;
  border-color: #4b5563 !important;
}

.dark .ql-toolbar {
  background: #374151 !important;
  border-color: #4b5563 !important;
}

.dark .ql-toolbar .ql-picker-label {
  background: #374151 !important;
  border-color: #4b5563 !important;
  color: #f9fafb !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3) !important;
}

.dark .ql-toolbar .ql-picker-label::before {
  color: #9ca3af !important;
}

.dark .ql-toolbar .ql-picker-label:hover {
  background: #4b5563 !important;
  border-color: #6b7280 !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.4) !important;
}

.dark .ql-toolbar .ql-picker-label:hover::before {
  color: #d1d5db !important;
}

.dark .ql-toolbar .ql-picker-options {
  background: #374151 !important;
  border-color: #4b5563 !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
  z-index: 10000 !important;
  position: absolute !important;
  top: 100% !important;
  bottom: auto !important;
}

.dark .ql-toolbar .ql-picker-item {
  color: #f9fafb !important;
  border-bottom-color: #4b5563 !important;
}

.dark .ql-toolbar .ql-picker-item:hover {
  background: #4b5563 !important;
  color: #ffffff !important;
}

.dark .ql-toolbar .ql-picker-item.ql-selected {
  background: #1e40af !important;
  color: #dbeafe !important;
}

.dark .ql-toolbar button {
  background: #374151 !important;
  border-color: #4b5563 !important;
  color: #f9fafb !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3) !important;
}

.dark .ql-toolbar button:hover {
  background: #4b5563 !important;
  border-color: #6b7280 !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.4) !important;
  transform: translateY(-1px) !important;
}

.dark .ql-toolbar button.ql-active {
  background: #3b82f6 !important;
  border-color: #3b82f6 !important;
  color: white !important;
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.4) !important;
}

.dark .ql-toolbar button:focus {
  ring-color: rgba(59, 130, 246, 0.6) !important;
}

.dark .ql-editor ul li::before,
.dark .ql-editor ol li::before {
  color: #d1d5db !important;
}

.dark .ql-container {
  border-color: #4b5563 !important;
}

.dark .ql-editor {
  background: #1f2937 !important;
  color: #f9fafb !important;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
  .ql-toolbar {
    padding: 6px !important;
    flex-wrap: nowrap !important;
    overflow-x: auto !important;
    gap: 2px !important;
  }

  .ql-toolbar .ql-formats {
    margin-left: 4px !important;
    flex-shrink: 0 !important;
  }

  .ql-toolbar button {
    width: 28px !important;
    height: 28px !important;
    margin: 0 1px !important;
    flex-shrink: 0 !important;
  }

  .ql-toolbar .ql-picker-label {
    min-width: 70px !important;
    max-width: 70px !important;
    padding: 4px 16px 4px 8px !important;
    font-size: 12px !important;
    flex-shrink: 0 !important;
    box-sizing: border-box !important;
  }

  .ql-editor {
    padding-right: 20px !important;
    padding-left: 12px !important;
    font-size: 16px !important; /* منع التكبير التلقائي في iOS */
  }

  .ql-editor ol,
  .ql-editor ul {
    margin-right: 1.5em !important;
  }

  .ql-editor ul li::before {
    right: -1.5em !important;
  }

  .ql-editor ol li::before {
    right: -1.8em !important;
  }
}

/* إصلاح مشكلة التداخل مع النص */
.ql-editor li {
  min-height: 1.4em;
  word-wrap: break-word;
}

/* القوائم المتداخلة */
.ql-editor ul ul,
.ql-editor ol ol,
.ql-editor ul ol,
.ql-editor ol ul {
  margin-right: 2em !important;
  margin-left: 0 !important;
}

.ql-editor ul ul li::before {
  content: "◦" !important;
  font-size: 1em !important;
}

.ql-editor ul ul ul li::before {
  content: "▪" !important;
  font-size: 0.8em !important;
}

/* إزالة الحدود الزرقاء عند التركيز - قاعدة عامة */
.ql-editor:focus {
  outline: none !important;
  box-shadow: none !important;
}

.ql-container:focus-within {
  outline: none !important;
  box-shadow: none !important;
}

/* تصميم الأزرار العام - مثالي بدون حركات */
.ql-toolbar button {
  width: 28px !important;
  height: 28px !important;
  border-radius: 5px !important;
  border: 1px solid #e5e7eb !important;
  background: #ffffff !important;
  color: #4b5563 !important;
  transition: background-color 0.15s ease, border-color 0.15s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
  font-size: 12px !important;
}

.ql-toolbar button:hover {
  background: #f3f4f6 !important;
  border-color: #d1d5db !important;
  color: #374151 !important;
}

.ql-toolbar button.ql-active {
  background: #3b82f6 !important;
  border-color: #3b82f6 !important;
  color: white !important;
  box-shadow: 0 1px 3px rgba(59, 130, 246, 0.4) !important;
}

/* تصميم القوائم المنسدلة العام */
.ql-toolbar .ql-picker {
  height: 28px !important;
  z-index: 10000 !important;
  position: relative !important;
}

.ql-toolbar .ql-picker-label {
  border: 1px solid #e5e7eb !important;
  border-radius: 5px !important;
  background: #ffffff !important;
  padding: 5px 18px 5px 8px !important;
  height: 28px !important;
  min-width: 85px !important;
  max-width: 85px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  transition: background-color 0.15s ease, border-color 0.15s ease !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
  font-size: 11px !important;
  font-weight: 400 !important;
  color: #4b5563 !important;
  box-sizing: border-box !important;
}

.ql-toolbar .ql-picker-label:hover {
  background: #f3f4f6 !important;
  border-color: #d1d5db !important;
}

/* إصلاح جميع القوائم المنسدلة لتظهر فوق المحرر */
.ql-toolbar .ql-picker-options {
  z-index: 9999 !important;
  position: absolute !important;
  background: #ffffff !important;
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05) !important;
  margin-top: 2px !important;
  top: 100% !important;
  bottom: auto !important;
}

/* إصلاح خاص لقائمة العناوين لضمان ظهورها فوق المحرر */
.ql-toolbar .ql-header .ql-picker-options {
  z-index: 10000 !important;
  position: absolute !important;
  top: 100% !important;
  bottom: auto !important;
  right: 0 !important;
  left: auto !important;
  transform: none !important;
}

/* ضمان أن الحاوي الرئيسي للمحرر لا يخفي القوائم */
.ql-container {
  overflow: visible !important;
  position: relative !important;
}

.ql-toolbar {
  position: relative !important;
  z-index: 100 !important;
  overflow: visible !important;
}

/* ضمان أن جميع عناصر الاختيار في شريط الأدوات لها نفس الحجم - تم دمجها في القاعدة الأساسية أعلاه */

/* إصلاح خاص لضمان عدم تداخل القوائم مع المحتوى */
.ql-editor {
  position: relative !important;
  z-index: 1 !important;
}

/* ضمان أن الحاويات الأب لا تخفي القوائم المنسدلة */
.ql-container,
.ql-container .ql-editor,
.quill-editor,
.rich-text-editor {
  overflow: visible !important;
}

/* إصلاح إضافي للقوائم المنسدلة في جميع المتصفحات */
.ql-toolbar .ql-picker.ql-expanded .ql-picker-options {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 10001 !important;
}