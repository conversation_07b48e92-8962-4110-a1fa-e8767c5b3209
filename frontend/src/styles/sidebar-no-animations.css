/*
 * 🚫 إزالة الحركات غير المرغوب فيها من القائمة الجانبية
 * يزيل جميع التأثيرات والحركات عند المرور على العناصر والأيقونات
 * يحافظ على السهم ثابت عند فتح القوائم الفرعية
 */

/* ========================================
   إزالة حركات العناصر عند التحويم
   ======================================== */

/* إزالة حركة translateX من عناصر القائمة */
.sidebar-item-hover:hover {
  transform: none !important;
  box-shadow: none !important;
  scale: none !important;
}

/* إزالة جميع التأثيرات من عناصر القائمة */
.sidebar-item-hover {
  transition: background-color 0.2s ease, color 0.2s ease !important;
  transform: none !important;
}

/* ========================================
   إزالة حركات الأيقونات
   ======================================== */

/* إزالة حركة scale من الأيقونات */
.animate-iconScale,
.sidebar-item-hover .animate-iconScale,
.sidebar-item-hover:hover .animate-iconScale {
  animation: none !important;
  transform: none !important;
  scale: none !important;
}

/* إزالة جميع التأثيرات من الأيقونات */
.sidebar-item-hover span:first-child {
  transition: color 0.2s ease !important;
  transform: none !important;
  animation: none !important;
}

/* ========================================
   إزالة حركات النصوص
   ======================================== */

/* إزالة حركة fadeIn من النصوص */
.animate-textFadeIn,
.sidebar-item-hover .animate-textFadeIn,
.sidebar-item-hover:hover .animate-textFadeIn {
  animation: none !important;
  transform: none !important;
  opacity: 1 !important;
}

/* إزالة جميع التأثيرات من النصوص */
.sidebar-item-hover span:not(:first-child) {
  transition: color 0.2s ease !important;
  transform: none !important;
  animation: none !important;
}

/* ========================================
   إعدادات السهم - السماح بالدوران فقط
   ======================================== */

/* السماح بدوران السهم عند فتح القوائم الفرعية */
.rotate-90 {
  transform: rotate(90deg) !important;
}

/* السماح بانتقال السهم */
aside svg.transition-transform,
aside .transition-transform {
  transition: transform 0.2s ease !important;
}

/* قاعدة محددة للسهم في القائمة الجانبية */
aside span svg.rotate-90,
aside svg.rotate-90 {
  transform: rotate(90deg) !important;
  transition: transform 0.2s ease !important;
}

/* قاعدة عامة للسهم */
svg.transition-transform {
  transition: transform 0.2s ease !important;
}

svg.rotate-90 {
  transform: rotate(90deg) !important;
}

/* منع أي حركات أخرى على السهم */
.chevron-rotate {
  transition: none !important;
  animation: none !important;
  scale: none !important;
}

.chevron-rotate.expanded {
  animation: none !important;
  scale: none !important;
}

/* ========================================
   إزالة حركات القوائم الفرعية
   ======================================== */

/* إزالة حركة slideDown من القوائم الفرعية */
.animate-slideDown,
.animate-submenuSlideDown {
  animation: none !important;
  transform: none !important;
  opacity: 1 !important;
}

/* إزالة جميع التأثيرات من القوائم الفرعية */
.sidebar-item-hover ul {
  transition: none !important;
  transform: none !important;
  animation: none !important;
}

/* ========================================
   إزالة حركات عناصر القوائم الفرعية
   ======================================== */

/* إزالة حركة translateX من عناصر القوائم الفرعية */
.submenu-item,
.submenu-item:hover {
  transform: none !important;
  background: none !important;
  transition: background-color 0.2s ease, color 0.2s ease !important;
}

/* ========================================
   إزالة حركات الشريط الجانبي نفسه
   ======================================== */

/* إزالة تأثيرات التوسع */
.sidebar-hover-expand,
.sidebar-expanded-shadow,
.sidebar-transition {
  box-shadow: none !important;
  transform: none !important;
}

/* الحفاظ على انتقال العرض فقط */
aside {
  transition: width 0.3s ease-in-out !important;
}

/* ========================================
   إزالة جميع الحركات العامة
   ======================================== */

/* إزالة جميع keyframes animations من القائمة الجانبية عدا السهم */
aside *:not(.rotate-90):not(.transition-transform),
aside *:before,
aside *:after {
  animation: none !important;
}

aside *:not(.rotate-90):not(.sidebar-tooltip-custom):not(.transition-transform) {
  transform: none !important;
}

/* استثناء: الحفاظ على انتقال الألوان فقط */
aside * {
  transition: background-color 0.2s ease, color 0.2s ease, opacity 0.2s ease !important;
}

/* ========================================
   إعدادات خاصة للوضع المصغر
   ======================================== */

/* منع أي حركات في الوضع المصغر */
.lg\:w-20 * {
  transform: none !important;
  animation: none !important;
  scale: none !important;
}

/* ========================================
   إعدادات التلميحات (tooltips)
   ======================================== */

/* الحفاظ على تلميحات الوضع المصغر بدون حركات مفرطة */
.sidebar-tooltip-custom {
  transition: opacity 0.2s ease !important;
  transform: translateY(-50%) !important;
}

.group:hover .sidebar-tooltip-custom {
  transform: translateY(-50%) !important;
}

/* ========================================
   إعدادات الحالة النشطة
   ======================================== */

/* الحفاظ على تمييز العنصر النشط بدون حركات */
.sidebar-item-hover.bg-primary-100,
.sidebar-item-hover.dark\:bg-primary-900\/30 {
  transform: none !important;
  animation: none !important;
}

/* ========================================
   إعدادات إضافية للاستقرار
   ======================================== */

/* منع أي تأثيرات scale أو rotate */
aside [class*="animate-"],
aside [class*="transition-"],
aside [class*="transform"] {
  animation: none !important;
  transform: none !important;
  scale: none !important;
  rotate: none !important;
}

/* الحفاظ على الانتقالات الأساسية فقط */
aside button,
aside a,
aside div[role="button"] {
  transition: background-color 0.2s ease, color 0.2s ease !important;
}

/* ========================================
   إعدادات خاصة للأيقونات
   ======================================== */

/* منع أي حركات على الأيقونات */
aside svg,
aside [class*="Fi"] {
  transition: color 0.2s ease !important;
  transform: none !important;
  animation: none !important;
}

/* ========================================
   إعدادات نهائية
   ======================================== */

/* تأكيد إزالة جميع الحركات */
aside * {
  will-change: auto !important;
}

/* منع أي تأثيرات CSS transforms عدا السهم والتلميحات */
aside *:not(.sidebar-tooltip-custom):not(.rotate-90):not(.transition-transform) {
  transform: none !important;
}

/* الحفاظ على cursor pointer للعناصر التفاعلية */
aside button,
aside a,
aside [role="button"],
aside .cursor-pointer {
  cursor: pointer !important;
}
