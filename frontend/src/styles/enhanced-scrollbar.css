/*
 * 🎨 شريط التمرير المحسن - Enhanced Scrollbar System
 * نظام شريط تمرير مخصص محسن بدون مكتبات خارجية
 * أداء أفضل وتوافق أكبر مع جميع المتصفحات
 */

/* ========================================
   إعدادات أساسية لشريط التمرير المحسن
   ======================================== */

.enhanced-scrollbar {
  /* إعدادات أساسية */
  scrollbar-width: thin;
  scroll-behavior: smooth;
  
  /* إزالة أسهم التمرير */
  -webkit-appearance: none;
}

/* شريط التمرير الأساسي */
.enhanced-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background: transparent;
}

/* مسار شريط التمرير */
.enhanced-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
  margin: 2px;
}

/* مقبض شريط التمرير */
.enhanced-scrollbar::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 4px;
  transition: all 0.2s ease-in-out;
  border: none;
}

/* إزالة أسهم التمرير تماماً */
.enhanced-scrollbar::-webkit-scrollbar-button {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

.enhanced-scrollbar::-webkit-scrollbar-corner {
  background: transparent;
  display: none;
}

/* ========================================
   أنماط العرض المختلفة
   ======================================== */

/* شريط التمرير يظهر عند التحويم فقط */
.enhanced-scrollbar.scrollbar-hover:hover::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.6);
}

.enhanced-scrollbar.scrollbar-hover:hover::-webkit-scrollbar-track {
  background: rgba(243, 244, 246, 0.3);
}

/* شريط التمرير يظهر دائماً */
.enhanced-scrollbar.scrollbar-always::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
}

.enhanced-scrollbar.scrollbar-always::-webkit-scrollbar-track {
  background: rgba(243, 244, 246, 0.2);
}

/* شريط التمرير تلقائي */
.enhanced-scrollbar.scrollbar-auto::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.4);
}

/* ========================================
   دعم الوضع المظلم
   ======================================== */

/* الوضع المظلم - عند التحويم */
.enhanced-scrollbar.dark.scrollbar-hover:hover::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.7);
}

.enhanced-scrollbar.dark.scrollbar-hover:hover::-webkit-scrollbar-track {
  background: rgba(31, 41, 55, 0.4);
}

/* الوضع المظلم - دائماً */
.enhanced-scrollbar.dark.scrollbar-always::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.6);
}

.enhanced-scrollbar.dark.scrollbar-always::-webkit-scrollbar-track {
  background: rgba(31, 41, 55, 0.3);
}

/* الوضع المظلم - تلقائي */
.enhanced-scrollbar.dark.scrollbar-auto::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
}

/* ========================================
   تأثيرات التفاعل
   ======================================== */

/* تأثير التحويم على المقبض */
.enhanced-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.8) !important;
  width: 10px;
}

.enhanced-scrollbar.dark::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.9) !important;
}

/* تأثير الضغط على المقبض */
.enhanced-scrollbar::-webkit-scrollbar-thumb:active {
  background: rgba(75, 85, 99, 0.9) !important;
}

.enhanced-scrollbar.dark::-webkit-scrollbar-thumb:active {
  background: rgba(209, 213, 219, 0.9) !important;
}

/* ========================================
   دعم Firefox
   ======================================== */

.enhanced-scrollbar {
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.enhanced-scrollbar.dark {
  scrollbar-color: rgba(156, 163, 175, 0.6) rgba(31, 41, 55, 0.3);
}

/* ========================================
   فئات مساعدة
   ======================================== */

/* شريط تمرير رفيع */
.enhanced-scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

/* شريط تمرير سميك */
.enhanced-scrollbar-thick::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

/* إخفاء شريط التمرير تماماً */
.enhanced-scrollbar-hidden {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.enhanced-scrollbar-hidden::-webkit-scrollbar {
  display: none;
}

/* ========================================
   تطبيق على التطبيق الكامل
   ======================================== */

/* تطبيق شريط التمرير المحسن على الجسم الرئيسي */
.app-enhanced-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.app-enhanced-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.app-enhanced-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.app-enhanced-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 4px;
  transition: all 0.2s ease-in-out;
}

.app-enhanced-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.7);
}

.app-enhanced-scrollbar::-webkit-scrollbar-button {
  display: none !important;
}

/* الوضع المظلم للتطبيق */
.dark .app-enhanced-scrollbar {
  scrollbar-color: rgba(156, 163, 175, 0.6) rgba(31, 41, 55, 0.3);
}

.dark .app-enhanced-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.6);
}

.dark .app-enhanced-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.8);
}

.dark .app-enhanced-scrollbar::-webkit-scrollbar-track {
  background: rgba(31, 41, 55, 0.2);
}
