/**
 * أنماط CSS لمنتقي الإيموجي ونظام الدردشة
 */

/* منتقي الإيموجي - حل جذري للموضع */
.emoji-picker {
  position: fixed;
  width: 350px;
  max-width: calc(100vw - 40px);
  height: 420px;
  max-height: calc(100vh - 100px);
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  /* سيتم تحديد الموضع بواسطة JavaScript */
}

.dark .emoji-picker {
  background: #374151;
  border-color: #4b5563;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* شريط البحث */
.emoji-search {
  padding: 12px;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.dark .emoji-search {
  border-bottom-color: #4b5563;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  color: #9ca3af;
  font-size: 14px;
  z-index: 1;
}

.dark .search-icon {
  color: #6b7280;
}

.search-input {
  width: 100%;
  padding: 8px 12px 8px 36px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background: #f9fafb;
  color: #374151;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  background: white;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dark .search-input {
  background: #4b5563;
  border-color: #6b7280;
  color: #f3f4f6;
}

.dark .search-input:focus {
  background: #374151;
  border-color: #60a5fa;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
}

/* فئات الإيموجي */
.emoji-categories {
  display: flex;
  padding: 8px 12px;
  gap: 4px;
  border-bottom: 1px solid #e5e7eb;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  flex-shrink: 0;
  background: #f9fafb;
}

.emoji-categories::-webkit-scrollbar {
  display: none;
}

.dark .emoji-categories {
  border-bottom-color: #4b5563;
  background: #374151;
}

.category-btn {
  flex-shrink: 0;
  padding: 8px;
  border: none;
  background: transparent;
  border-radius: 6px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.category-btn.active {
  background: #3b82f6;
  color: white;
}

.dark .category-btn {
  color: #9ca3af;
}

.dark .category-btn:hover {
  background: #4b5563;
  color: #f3f4f6;
}

.dark .category-btn.active {
  background: #60a5fa;
  color: white;
}

/* شبكة الإيموجي */
.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 4px;
  padding: 12px;
  overflow-y: auto;
  overflow-x: hidden;
  flex: 1;
  min-height: 0;
  max-height: 300px;
}

.emoji-btn {
  aspect-ratio: 1;
  border: none;
  background: transparent;
  border-radius: 6px;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  min-width: 0;
  min-height: 0;
  overflow: hidden;
}

.emoji-btn:hover {
  background: #f3f4f6;
  transform: scale(1.1);
}

.dark .emoji-btn:hover {
  background: #4b5563;
}

/* رسالة عدم وجود نتائج */
.no-results {
  padding: 24px;
  text-align: center;
  color: #6b7280;
}

.dark .no-results {
  color: #9ca3af;
}

.no-results p {
  margin: 0;
  font-size: 14px;
}

/* حاوي منتقي الإيموجي في MessageInput */
.emoji-picker-container {
  position: relative;
  display: inline-block;
}

/* تحسين عام للنافذة */
.emoji-picker * {
  box-sizing: border-box;
}



/* زر الإيموجي */
.action-btn.emoji-btn {
  color: #6b7280;
  transition: all 0.2s ease;
}

.action-btn.emoji-btn:hover {
  color: #3b82f6;
  background: #eff6ff;
}

.action-btn.emoji-btn.active {
  color: #3b82f6;
  background: #dbeafe;
}

.dark .action-btn.emoji-btn {
  color: #9ca3af;
}

.dark .action-btn.emoji-btn:hover {
  color: #60a5fa;
  background: rgba(59, 130, 246, 0.1);
}

.dark .action-btn.emoji-btn.active {
  color: #60a5fa;
  background: rgba(59, 130, 246, 0.2);
}

/* تحسين عرض الإيموجي في الرسائل */
.message-content {
  line-height: 1.4;
  word-wrap: break-word;
  overflow-wrap: break-word;
  display: block;
  width: 100%;
}

/* إزالة تعديلات message-text من هنا لتجنب التضارب */

/* تحسين الإيموجي في textarea */
.message-textarea {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
  line-height: 1.5;
  resize: none;
}

/* تحسين scrollbar لمنتقي الإيموجي */
.emoji-grid::-webkit-scrollbar {
  width: 6px;
}

.emoji-grid::-webkit-scrollbar-track {
  background: transparent;
}

.emoji-grid::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.emoji-grid::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

.dark .emoji-grid::-webkit-scrollbar-thumb {
  background: #4b5563;
}

.dark .emoji-grid::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* تحسين الاستجابة للأجهزة المحمولة */
@media (max-width: 640px) {
  .emoji-picker {
    width: 320px;
    height: 350px;
    max-height: calc(100vh - 120px);
    right: -50px;
  }



  .emoji-grid {
    grid-template-columns: repeat(7, 1fr);
    gap: 3px;
    padding: 10px;
  }

  .emoji-btn {
    font-size: 18px;
  }
}

/* تحسين للشاشات الصغيرة جداً */
@media (max-width: 480px) {
  .emoji-picker {
    width: 280px;
    height: 320px;
  }

  .emoji-grid {
    grid-template-columns: repeat(6, 1fr);
    gap: 2px;
    padding: 8px;
  }

  .emoji-btn {
    font-size: 16px;
  }
}

/* تحسين الأنيميشن */
.emoji-picker {
  animation: slideUpFade 0.15s ease-out;
  animation-fill-mode: both;
}

@keyframes slideUpFade {
  0% {
    opacity: 0;
    transform: translateY(8px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* تحسين التركيز */
.category-btn:focus,
.emoji-btn:focus,
.search-input:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.dark .category-btn:focus,
.dark .emoji-btn:focus,
.dark .search-input:focus {
  outline-color: #60a5fa;
}
