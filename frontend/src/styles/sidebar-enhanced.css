/**
 * تحسينات القائمة الجانبية - SmartPOS v2.0
 * تصميم أنيق بدون حدود مع خطوط متصلة جميلة
 * يركز على الجمالية والوضوح البصري
 */

/* ========================================
   متغيرات CSS للألوان والقياسات
   ======================================== */
:root {
  /* ألوان العناصر الرئيسية */
  --sidebar-primary-color: #3b82f6;
  --sidebar-primary-light: rgba(59, 130, 246, 0.06);
  --sidebar-primary-hover: rgba(59, 130, 246, 0.04);
  --sidebar-line-color: rgba(59, 130, 246, 0.3);
  --sidebar-line-active: rgba(59, 130, 246, 0.8);
  --sidebar-dot-color: #3b82f6;
  --sidebar-connecting-color: #d1d5db;

  /* قياسات الخطوط والمسافات */
  --sidebar-accent-line: 2px;
  --sidebar-connecting-line: 12px;
  --sidebar-dot-size: 4px;
  --sidebar-submenu-indent: 24px;
  --sidebar-vertical-line: 1px;
}

/* الوضع المظلم */
.dark {
  --sidebar-primary-color: #60a5fa;
  --sidebar-primary-light: rgba(96, 165, 250, 0.08);
  --sidebar-primary-hover: rgba(96, 165, 250, 0.05);
  --sidebar-line-color: rgba(96, 165, 250, 0.4);
  --sidebar-line-active: rgba(96, 165, 250, 0.9);
  --sidebar-dot-color: #60a5fa;
  --sidebar-connecting-color: #6b7280;
}

/* ========================================
   تحسينات العناصر الرئيسية - تصميم أنيق
   ======================================== */

/* العنصر الرئيسي العام */
.sidebar-main-item {
  position: relative;
  transition: all 0.25s ease-in-out;
  overflow: hidden;
}

/* خط التمييز الجانبي الرفيع */
.sidebar-main-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: var(--sidebar-accent-line);
  height: 0;
  background: var(--sidebar-line-color);
  transition: all 0.3s ease-in-out;
  border-radius: 0 2px 2px 0;
}

/* العنصر الرئيسي النشط */
.sidebar-main-item.active {
  background: linear-gradient(to left, transparent, var(--sidebar-primary-light));
  font-weight: 600;
}

.sidebar-main-item.active::before {
  height: 60%;
  background: var(--sidebar-line-active);
}

/* تأثير Hover للعناصر الرئيسية */
.sidebar-main-item:hover:not(.active) {
  background: var(--sidebar-primary-hover);
}

.sidebar-main-item:hover:not(.active)::before {
  height: 40%;
  background: var(--sidebar-line-color);
}

/* أيقونة العنصر الرئيسي */
.sidebar-main-icon {
  transition: all 0.25s ease-in-out;
  flex-shrink: 0;
}

/* نص العنصر الرئيسي - دائماً عريض */
.sidebar-main-text {
  font-weight: 600;
  transition: color 0.25s ease-in-out;
}

.sidebar-main-item.active .sidebar-main-text {
  font-weight: 700;
  color: var(--sidebar-primary-color);
}

/* ========================================
   نظام الخطوط المتصلة الجميلة للعناصر الفرعية
  ======================================== */

/* حاوي القائمة الفرعية مع إزاحة واضحة */
.sidebar-submenu-container {
  position: relative;
  margin-right: var(--sidebar-submenu-indent);
  margin-left: 24px;
  padding-top: 6px;
  padding-bottom: 4px;
  padding-right: 8px;
}

/* الخط العمودي الواضح والأنيق - متصل بالكامل */
.sidebar-submenu-container::before {
  content: '';
  position: absolute;
  right: -20px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom,
    transparent 0%,
    var(--sidebar-connecting-color) 8%,
    var(--sidebar-connecting-color) 92%,
    transparent 100%);
  opacity: 0.7;
  border-radius: 1px 0 0 1px;
}

/* العنصر الفرعي - بدون حركة وخط عادي */
.sidebar-submenu-item {
  position: relative;
  transition: none;
  padding-right: 20px;
  margin-bottom: 2px;
  font-weight: 400;
}

/* الخط الأفقي المتصل بالكامل مع الخط العمودي */
.sidebar-submenu-item::before {
  content: '';
  position: absolute;
  right: -20px;
  top: 50%;
  width: 14px;
  height: 2px;
  background: var(--sidebar-connecting-color);
  opacity: 0.7;
  transform: translateY(-50%);
  transition: none;
  border-radius: 0 1px 1px 0;
}

/* النقطة الجميلة في نهاية الخط المتصل - مع حركة */
.sidebar-submenu-item::after {
  content: '';
  position: absolute;
  right: -8px;
  top: 50%;
  width: 6px;
  height: 6px;
  background: var(--sidebar-dot-color);
  border-radius: 50%;
  transform: translateY(-50%);
  opacity: 0.8;
  transition: all 0.3s ease-in-out;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.15);
}

/* تأثير Hover - حركة النقطة فقط */
.sidebar-submenu-item:hover::after {
  opacity: 1;
  transform: translateY(-50%) scale(1.3);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.25);
}

/* العنصر الفرعي النشط - بدون حركة في العنصر */
.sidebar-submenu-item.active {
  font-weight: 400;
  color: var(--sidebar-primary-color);
}

.sidebar-submenu-item.active::before {
  background: var(--sidebar-primary-color);
  opacity: 1;
  width: 14px;
}

.sidebar-submenu-item.active::after {
  background: var(--sidebar-primary-color);
  opacity: 1;
  transform: translateY(-50%) scale(1.3);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.25);
}

/* ========================================
   تحسينات إضافية وأنيقة
   ======================================== */

/* تحسين السهم للقوائم الفرعية - أكثر وضوحاً */
.sidebar-chevron {
  transition: all 0.3s ease-in-out;
  opacity: 0.8;
  color: #6b7280;
}

.dark .sidebar-chevron {
  color: #9ca3af;
}

.sidebar-chevron.expanded {
  transform: rotate(90deg);
  opacity: 1;
  color: var(--sidebar-primary-color);
}

/* تأثير hover للسهم */
.sidebar-main-item:hover .sidebar-chevron:not(.expanded) {
  opacity: 1;
  color: #4b5563;
}

.dark .sidebar-main-item:hover .sidebar-chevron:not(.expanded) {
  color: #d1d5db;
}

/* تحسين المسافات */
.sidebar-item-spacing {
  margin-bottom: 3px;
}

.sidebar-submenu-spacing {
  margin-top: 2px;
  margin-bottom: 1px;
}

/* ========================================
   تحسينات للوضع المصغر
   ======================================== */

/* إخفاء الخطوط المتصلة في الوضع المصغر */
.sidebar-collapsed .sidebar-submenu-container::before,
.sidebar-collapsed .sidebar-submenu-item::before,
.sidebar-collapsed .sidebar-submenu-item::after {
  display: none;
}

/* تحسين العناصر الرئيسية في الوضع المصغر */
.sidebar-collapsed .sidebar-main-item {
  justify-content: center;
  padding-left: 0;
  padding-right: 0;
}

/* خط التمييز السفلي في الوضع المصغر */
.sidebar-collapsed .sidebar-main-item::before {
  left: 50%;
  top: auto;
  bottom: 0;
  transform: translateX(-50%);
  width: 0;
  height: var(--sidebar-accent-line);
  border-radius: 2px 2px 0 0;
}

.sidebar-collapsed .sidebar-main-item.active::before {
  width: 70%;
}

.sidebar-collapsed .sidebar-main-item:hover:not(.active)::before {
  width: 50%;
}

/* ========================================
   تحسينات الاستجابة
   ======================================== */

/* للشاشات الصغيرة */
@media (max-width: 768px) {
  .sidebar-submenu-container {
    margin-right: 16px;
    margin-left: 16px;
  }

  .sidebar-submenu-container::before {
    right: -14px;
  }

  .sidebar-submenu-item::before {
    width: 10px;
    right: -14px;
  }

  .sidebar-submenu-item::after {
    right: -6px;
    width: 5px;
    height: 5px;
  }
}

/* ========================================
   تحسينات الوصولية المحسنة
   ======================================== */

/* تحسين التركيز */
.sidebar-main-item:focus {
  outline: 2px solid var(--sidebar-primary-color);
  outline-offset: 2px;
  border-radius: 8px;
}

/* إزالة التركيز من العناصر الفرعية */
.sidebar-submenu-item:focus {
  outline: none !important;
  border: none !important;
}

/* تحسين التباين للوضع عالي التباين */
@media (prefers-contrast: high) {
  :root {
    --sidebar-connecting-color: #000000;
    --sidebar-primary-color: #0066cc;
    --sidebar-dot-color: #0066cc;
  }

  .dark {
    --sidebar-connecting-color: #ffffff;
    --sidebar-primary-color: #66b3ff;
    --sidebar-dot-color: #66b3ff;
  }
}

/* تحسين للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
  .sidebar-main-item,
  .sidebar-submenu-item,
  .sidebar-main-icon,
  .sidebar-chevron,
  .sidebar-submenu-item::after,
  .sidebar-main-item::before {
    transition: none !important;
    animation: none !important;
  }
}

/* إزالة الحركة من العناصر الفرعية عموماً */
.sidebar-submenu-item {
  transition: none !important;
  border: none !important;
  outline: none !important;
}

/* إزالة الحدود عند التركيز */
.sidebar-submenu-item:focus {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* الحفاظ على حركة النقاط فقط */
.sidebar-submenu-item::after {
  transition: all 0.3s ease-in-out !important;
}

/* إزالة جميع الحدود والخطوط من العناصر الفرعية */
.sidebar-submenu-item,
.sidebar-submenu-item:hover,
.sidebar-submenu-item:focus,
.sidebar-submenu-item:active,
.sidebar-submenu-item.active {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* ضمان الخط العادي للنص في العناصر الفرعية */
.sidebar-submenu-item span,
.sidebar-submenu-item.active span {
  font-weight: 400 !important;
}

/* ========================================
   تحسينات إضافية للجمالية
   ======================================== */

/* تأثير النبضة الخفيف للعناصر النشطة */
@keyframes gentle-pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

.sidebar-submenu-item.active::after {
  animation: gentle-pulse 2s ease-in-out infinite;
}

/* تحسين الانتقالات */
.sidebar-main-item,
.sidebar-submenu-item {
  will-change: transform, background-color;
}

/* تحسين الأداء */
.sidebar-submenu-container::before,
.sidebar-submenu-item::before,
.sidebar-submenu-item::after,
.sidebar-main-item::before {
  will-change: opacity, transform, width, height;
}
