/*
 * 🔧 إصلاح مشكلة sticky مع مكتبة react-custom-scrollbars
 * يحل مشكلة عدم عمل خاصية sticky مع مكتبة react-custom-scrollbars
 */

/* تطبيق خاصية sticky بشكل صحيح */
.sticky {
  position: -webkit-sticky;
  position: sticky;
  z-index: 10;
}

/* إصلاح مشكلة sticky في المتصفحات المختلفة */
@supports (position: sticky) or (position: -webkit-sticky) {
  .sticky {
    position: -webkit-sticky;
    position: sticky;
    z-index: 10;
  }
}

/* إصلاح مشكلة sticky مع react-custom-scrollbars */
.app-enhanced-scrollbar .sticky {
  position: -webkit-sticky;
  position: sticky;
  z-index: 10;
}

/* تطبيق خاصية sticky على العناصر المحددة */
.product-tools-sticky {
  position: -webkit-sticky;
  position: sticky;
  top: 24px;
  z-index: 10;
}

/* تأكيد عمل خاصية sticky في جميع الحالات */
html.js .sticky,
body.js .sticky {
  position: -webkit-sticky;
  position: sticky;
}