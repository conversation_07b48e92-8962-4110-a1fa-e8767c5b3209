/**
 * Hook لإدارة أنماط مكونات الإدخال الموحدة
 * يوفر دوال مساعدة لبناء أنماط متسقة عبر جميع مكونات الإدخال
 */

import { useMemo } from 'react';
import { inputStylesConfig } from '../styles/inputStyles';

interface UseInputStylesOptions {
  variant?: 'text' | 'number' | 'select' | 'textarea' | 'date' | 'time';
  hasIcon?: boolean;
  isFocused?: boolean;
  hasError?: boolean;
  hasSuccess?: boolean;
  isDisabled?: boolean;
  customClasses?: string;
}

export const useInputStyles = (options: UseInputStylesOptions = {}) => {
  const {
    variant = 'text',
    hasIcon = false,
    isFocused = false,
    hasError = false,
    hasSuccess = false,
    isDisabled = false,
    customClasses = ''
  } = options;

  // بناء أنماط الإدخال
  const inputStyles = useMemo(() => {
    return inputStylesConfig.buildInputStyles(variant, {
      hasIcon,
      isFocused,
      hasError,
      hasSuccess,
      isDisabled,
      customClasses
    });
  }, [variant, hasIcon, isFocused, hasError, hasSuccess, isDisabled, customClasses]);

  // أنماط الأيقونة
  const iconStyles = useMemo(() => {
    let styles = `${inputStylesConfig.iconStyles.container} ${inputStylesConfig.iconStyles.base}`;
    
    if (hasError) {
      styles += ` ${inputStylesConfig.iconStyles.states.error}`;
    } else if (hasSuccess) {
      styles += ` ${inputStylesConfig.iconStyles.states.success}`;
    } else if (isFocused) {
      styles += ` ${inputStylesConfig.iconStyles.states.focus}`;
    } else {
      styles += ` ${inputStylesConfig.iconStyles.states.default}`;
    }
    
    return styles;
  }, [hasError, hasSuccess, isFocused]);

  // أنماط التسمية
  const labelStyles = useMemo(() => {
    return inputStylesConfig.labelStyles.base;
  }, []);

  // أنماط رسائل الخطأ والنجاح
  const messageStyles = useMemo(() => {
    if (hasError) {
      return inputStylesConfig.messageStyles.error;
    } else if (hasSuccess) {
      return inputStylesConfig.messageStyles.success;
    }
    return '';
  }, [hasError, hasSuccess]);

  // دالة للحصول على أنماط الحدود فقط
  const getBorderStyles = useMemo(() => {
    if (isDisabled) {
      return inputStylesConfig.baseInputStyles.borders.disabled;
    } else if (hasError) {
      return inputStylesConfig.baseInputStyles.borders.error;
    } else if (hasSuccess) {
      return inputStylesConfig.baseInputStyles.borders.success;
    } else if (isFocused) {
      return inputStylesConfig.baseInputStyles.borders.focus;
    } else {
      return inputStylesConfig.baseInputStyles.borders.default;
    }
  }, [isDisabled, hasError, hasSuccess, isFocused]);

  // دالة للحصول على أنماط الخلفية فقط
  const getBackgroundStyles = useMemo(() => {
    if (isDisabled) {
      return inputStylesConfig.baseInputStyles.backgrounds.disabled;
    } else {
      return inputStylesConfig.baseInputStyles.backgrounds.default;
    }
  }, [isDisabled]);

  // دالة للحصول على أنماط النص فقط
  const getTextStyles = useMemo(() => {
    if (isDisabled) {
      return inputStylesConfig.baseInputStyles.textColors.disabled;
    } else {
      return inputStylesConfig.baseInputStyles.textColors.default;
    }
  }, [isDisabled]);

  return {
    // الأنماط الكاملة
    inputStyles,
    iconStyles,
    labelStyles,
    messageStyles,
    
    // الأنماط الجزئية
    borderStyles: getBorderStyles,
    backgroundStyles: getBackgroundStyles,
    textStyles: getTextStyles,
    
    // الثوابت
    INPUT_HEIGHT: inputStylesConfig.INPUT_HEIGHT,
    
    // دوال مساعدة
    buildCustomStyles: (additionalClasses: string) => `${inputStyles} ${additionalClasses}`,
    
    // حالات الإدخال
    states: {
      hasError,
      hasSuccess,
      isFocused,
      isDisabled,
      hasIcon
    }
  };
};

export default useInputStyles;
