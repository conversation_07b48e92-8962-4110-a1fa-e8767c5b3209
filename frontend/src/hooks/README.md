# Hooks Documentation

## useFullscreen Hook

Hook مخصص لإدارة وضع ملء الشاشة في التطبيق.

### الاستخدام

```typescript
import { useFullscreen } from '../hooks/useFullscreen';

function MyComponent() {
  const { isFullscreen, toggleFullscreen, enterFullscreen, exitFullscreen } = useFullscreen();

  return (
    <div>
      <p>حالة ملء الشاشة: {isFullscreen ? 'مفعل' : 'غير مفعل'}</p>
      <button onClick={toggleFullscreen}>
        {isFullscreen ? 'الخروج من ملء الشاشة' : 'ملء الشاشة'}
      </button>
    </div>
  );
}
```

### القيم المُرجعة

- `isFullscreen: boolean` - حالة ملء الشاشة الحالية
- `toggleFullscreen: () => Promise<void>` - دالة لتبديل وضع ملء الشاشة
- `enterFullscreen: () => Promise<void>` - دالة للدخول في وضع ملء الشاشة
- `exitFullscreen: () => Promise<void>` - دالة للخروج من وضع ملء الشاشة

### الميزات

- **تتبع تلقائي للحالة**: يتتبع تغييرات وضع ملء الشاشة تلقائياً
- **معالجة الأخطاء**: يتعامل مع الأخطاء بشكل آمن
- **دعم المتصفحات**: يستخدم Fullscreen API المعياري
- **تنظيف تلقائي**: ينظف event listeners عند إلغاء تحميل المكون

### متطلبات المتصفح

يتطلب دعم Fullscreen API في المتصفح. معظم المتصفحات الحديثة تدعم هذه الميزة.

### الأمان

- يتطلب تفاعل المستخدم لتفعيل ملء الشاشة
- لا يمكن تفعيل ملء الشاشة برمجياً بدون تفاعل المستخدم
