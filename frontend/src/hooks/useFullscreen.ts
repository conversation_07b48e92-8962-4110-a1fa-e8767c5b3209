import { useState, useEffect } from 'react';

/**
 * Hook مخصص لإدارة وضع ملء الشاشة
 * يوفر حالة isFullscreen ووظيفة toggleFullscreen
 */
export const useFullscreen = () => {
  const [isFullscreen, setIsFullscreen] = useState(false);

  useEffect(() => {
    // دالة للتحقق من تغيير حالة fullscreen
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    // إضافة مستمع للأحداث
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    
    // تنظيف المستمع عند إلغاء تحميل المكون
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  // دالة لتبديل وضع fullscreen
  const toggleFullscreen = async () => {
    try {
      if (!document.fullscreenElement) {
        // الدخول في وضع ملء الشاشة
        await document.documentElement.requestFullscreen();
      } else {
        // الخروج من وضع ملء الشاشة
        await document.exitFullscreen();
      }
    } catch (error) {
      console.error('Error toggling fullscreen:', error);
      // يمكن إضافة إشعار للمستخدم هنا في المستقبل
    }
  };

  // دالة للدخول في وضع ملء الشاشة
  const enterFullscreen = async () => {
    try {
      if (!document.fullscreenElement) {
        await document.documentElement.requestFullscreen();
      }
    } catch (error) {
      console.error('Error entering fullscreen:', error);
    }
  };

  // دالة للخروج من وضع ملء الشاشة
  const exitFullscreen = async () => {
    try {
      if (document.fullscreenElement) {
        await document.exitFullscreen();
      }
    } catch (error) {
      console.error('Error exiting fullscreen:', error);
    }
  };

  return {
    isFullscreen,
    toggleFullscreen,
    enterFullscreen,
    exitFullscreen
  };
};

export default useFullscreen;
