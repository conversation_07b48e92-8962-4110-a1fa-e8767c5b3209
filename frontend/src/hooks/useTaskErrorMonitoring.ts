import { useEffect, useRef, useState } from 'react';
import scheduledTaskErrorService from '../services/scheduledTaskErrorService';

interface ScheduledTask {
  id: number;
  name: string;
  task_type: string;
  status: 'active' | 'paused' | 'disabled';
  last_error?: string;
  failure_count: number;
  last_run?: string;
  cron_expression: string;
}

/**
 * Hook لمراقبة أخطاء المهام المجدولة وتسجيلها تلقائياً
 */
export const useTaskErrorMonitoring = (tasks: ScheduledTask[]) => {
  const previousTasksRef = useRef<Map<number, ScheduledTask>>(new Map());

  useEffect(() => {
    const previousTasks = previousTasksRef.current;
    const currentTasksMap = new Map(tasks.map(task => [task.id, task]));

    // فحص المهام للبحث عن أخطاء جديدة
    tasks.forEach(async (currentTask) => {
      const previousTask = previousTasks.get(currentTask.id);

      // إذا كانت هذه مهمة جديدة أو لم تكن لديها أخطاء سابقة
      if (!previousTask) {
        // تسجيل الخطأ إذا كان موجوداً
        if (currentTask.last_error) {
          await logTaskError(currentTask);
        }
        return;
      }

      // فحص إذا كان هناك خطأ جديد
      const hasNewError = currentTask.last_error &&
                         currentTask.last_error !== previousTask.last_error;

      // فحص إذا زاد عدد الأخطاء
      const errorCountIncreased = currentTask.failure_count > previousTask.failure_count;

      if (hasNewError || errorCountIncreased) {
        await logTaskError(currentTask);
      }

      // فحص إذا تم حل الخطأ (لم يعد هناك خطأ)
      if (previousTask.last_error && !currentTask.last_error) {
        await handleErrorResolution(currentTask);
      }
    });

    // تحديث المرجع للمهام السابقة
    previousTasksRef.current = currentTasksMap;
  }, [tasks]);

  /**
   * تسجيل خطأ المهمة في نظام السجلات
   */
  const logTaskError = async (task: ScheduledTask) => {
    try {
      if (!task.last_error) return;

      // تحديد شدة الخطأ
      const severity = scheduledTaskErrorService.determineSeverity(
        task.last_error,
        task.failure_count
      );

      // تحديد فئة الخطأ
      const category = scheduledTaskErrorService.categorizeError(task.last_error);

      // تسجيل الخطأ
      await scheduledTaskErrorService.logTaskError(
        task.id,
        task.name,
        task.last_error,
        {
          taskType: task.task_type,
          failureCount: task.failure_count,
          lastRun: task.last_run,
          cronExpression: task.cron_expression,
          autoLogged: true,
          timestamp: new Date().toISOString()
        },
        severity,
        category
      );

      console.log(`✅ تم تسجيل خطأ المهمة تلقائياً: ${task.name}`);
    } catch (error) {
      console.error('خطأ في تسجيل خطأ المهمة تلقائياً:', error);
    }
  };

  /**
   * معالجة حل الخطأ تلقائياً
   */
  const handleErrorResolution = async (task: ScheduledTask) => {
    try {
      // البحث عن أخطاء هذه المهمة غير المحلولة
      const taskErrors = scheduledTaskErrorService.getTaskErrorsById(task.id);
      const unresolvedErrors = taskErrors.filter(error => !error.resolved);

      // حل جميع الأخطاء غير المحلولة
      for (const error of unresolvedErrors) {
        await scheduledTaskErrorService.resolveTaskError(
          error.id,
          'تم حل المشكلة تلقائياً - لم تعد المهمة تظهر أخطاء'
        );
      }

      if (unresolvedErrors.length > 0) {
        console.log(`✅ تم حل ${unresolvedErrors.length} أخطاء تلقائياً للمهمة: ${task.name}`);
      }
    } catch (error) {
      console.error('خطأ في حل أخطاء المهمة تلقائياً:', error);
    }
  };

  return {
    logTaskError,
    handleErrorResolution
  };
};

/**
 * Hook لمراقبة حالة المهام وإرسال تنبيهات
 */
export const useTaskHealthMonitoring = (tasks: ScheduledTask[], options?: {
  enableConsoleWarnings?: boolean
}) => {
  const [lastWarningTime, setLastWarningTime] = useState<number>(0);
  const [dismissedAlerts, setDismissedAlerts] = useState<Set<string>>(new Set());
  const WARNING_INTERVAL = 5 * 60 * 1000; // 5 دقائق
  const enableWarnings = options?.enableConsoleWarnings ?? true;

  // حساب المهام الحرجة (فقط المهام النشطة مع أخطاء فعلية)
  const criticalTasks = tasks.filter(task =>
    task.status === 'active' &&
    task.failure_count >= 3 && // تقليل العتبة من 5 إلى 3
    task.last_error &&
    task.last_error.trim() !== '' &&
    !dismissedAlerts.has(`critical-${task.id}`)
  );

  // حساب المهام المتوقفة (المهام المتوقفة مؤقتاً + المهام التي لم تعمل لفترة طويلة)
  const pausedTasks = tasks.filter(task =>
    task.status === 'paused' && !dismissedAlerts.has(`paused-${task.id}`)
  );

  const longStoppedTasks = tasks.filter(task => {
    if (task.status !== 'active' || !task.last_run) return false;
    if (dismissedAlerts.has(`stale-${task.id}`)) return false;

    const lastRun = new Date(task.last_run);
    const now = new Date();
    const hoursSinceLastRun = (now.getTime() - lastRun.getTime()) / (1000 * 60 * 60);

    // إذا لم تعمل المهمة لأكثر من 72 ساعة (3 أيام)
    return hoursSinceLastRun > 72;
  });

  const staleTasks = [...pausedTasks, ...longStoppedTasks];

  useEffect(() => {
    const now = Date.now();

    // تجنب إظهار التحذيرات بشكل متكرر
    if (now - lastWarningTime < WARNING_INTERVAL) {
      return;
    }

    // إرسال تنبيهات للمهام الحرجة (فقط إذا كان هناك مهام حرجة)
    if (criticalTasks.length > 0 && enableWarnings) {
      console.warn(`⚠️ تحذير: ${criticalTasks.length} مهام في حالة حرجة`);

      criticalTasks.forEach(task => {
        console.warn(`🔴 مهمة حرجة: ${task.name} (${task.failure_count} أخطاء)`);
      });

      setLastWarningTime(now);
    }

    // إرسال تنبيهات للمهام المتوقفة (فقط إذا كان هناك مهام متوقفة)
    if (staleTasks.length > 0 && enableWarnings) {
      console.warn(`⏰ تحذير: ${staleTasks.length} مهام لم تعمل لفترة طويلة`);

      staleTasks.forEach(task => {
        const lastRunDate = new Date(task.last_run!);
        const hoursAgo = Math.floor((now - lastRunDate.getTime()) / (1000 * 60 * 60));
        console.warn(`⏰ مهمة متوقفة: ${task.name} (منذ ${hoursAgo} ساعة)`);
      });

      setLastWarningTime(now);
    }

  }, [tasks, lastWarningTime, enableWarnings, criticalTasks.length, staleTasks.length]);

  // وظيفة تنظيف التنبيهات
  const clearAlerts = () => {
    setDismissedAlerts(new Set());
  };

  // وظيفة إخفاء تنبيه محدد
  const dismissAlert = (type: 'critical' | 'stale', taskId: number) => {
    setDismissedAlerts(prev => new Set([...prev, `${type}-${taskId}`]));
  };

  return {
    criticalTasksCount: criticalTasks.length,
    staleTasksCount: staleTasks.length,
    criticalTasks,
    staleTasks,
    clearAlerts,
    dismissAlert
  };
};

export default useTaskErrorMonitoring;
