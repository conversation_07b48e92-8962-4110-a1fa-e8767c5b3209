import { useEffect, useRef } from 'react';
import { 
  autoFixFormAccessibility, 
  enhanceFormAccessibility, 
  auditFormAccessibility 
} from '../utils/accessibilityUtils';

/**
 * Hook to automatically enhance form accessibility
 */
export const useFormAccessibility = (options: {
  autoFix?: boolean;
  enhance?: boolean;
  audit?: boolean;
} = {}) => {
  const formRef = useRef<HTMLFormElement>(null);
  const { autoFix = true, enhance = true, audit = false } = options;

  useEffect(() => {
    if (formRef.current) {
      if (autoFix) {
        autoFixFormAccessibility(formRef.current);
      }
      
      if (enhance) {
        enhanceFormAccessibility(formRef.current);
      }
      
      if (audit) {
        auditFormAccessibility(formRef.current);
      }
    }
  }, [autoFix, enhance, audit]);

  return formRef;
};

/**
 * Hook to generate unique IDs for form elements
 */
export const useUniqueId = (prefix: string): string => {
  const idRef = useRef<string>();
  
  if (!idRef.current) {
    idRef.current = `${prefix}-${Math.random().toString(36).substring(2, 11)}`;
  }
  
  return idRef.current;
};

/**
 * Hook to manage ARIA attributes
 */
export const useAriaAttributes = (element: HTMLElement | null, attributes: Record<string, string>) => {
  useEffect(() => {
    if (element) {
      Object.entries(attributes).forEach(([key, value]) => {
        element.setAttribute(key, value);
      });
      
      return () => {
        Object.keys(attributes).forEach((key) => {
          element.removeAttribute(key);
        });
      };
    }
  }, [element, attributes]);
};
