/**
 * Hook للتحميل التدريجي للمحادثات
 * مشابه لنمط POS مع تحسينات خاصة بالمحادثات
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { chatApiService, Conversation } from '../services/chatApiService';

interface ConversationsPaginationState {
  conversations: Conversation[];
  loading: boolean;
  loadingMore: boolean;
  error: string | null;
  hasMore: boolean;
  currentPage: number;
  totalCount: number;
  totalPages: number;
}

interface UseConversationsPaginationOptions {
  initialLimit?: number;
  loadMoreLimit?: number;
  autoLoad?: boolean;
}

interface UseConversationsPaginationReturn extends ConversationsPaginationState {
  loadMoreConversations: () => void;
  refreshConversations: () => void;
  resetPagination: () => void;
}

export const useConversationsPagination = (
  options: UseConversationsPaginationOptions = {}
): UseConversationsPaginationReturn => {
  const {
    initialLimit = 20,
    loadMoreLimit = 20,
    autoLoad = true
  } = options;

  const [state, setState] = useState<ConversationsPaginationState>({
    conversations: [],
    loading: false,
    loadingMore: false,
    error: null,
    hasMore: true,
    currentPage: 1,
    totalCount: 0,
    totalPages: 1
  });

  // مراجع للتحكم في التحميل
  const isLoadingRef = useRef(false);
  const lastLoadTime = useRef(0);
  const minLoadInterval = 1000; // ثانية واحدة بين التحميلات

  /**
   * جلب المحادثات مع التحميل التدريجي
   */
  const fetchConversations = useCallback(async (
    page: number = 1,
    limit: number = initialLimit,
    isLoadMore: boolean = false
  ) => {
    // منع التحميل المتكرر
    const now = Date.now();
    if (now - lastLoadTime.current < minLoadInterval) {
      console.log('🔄 تم تجاهل طلب التحميل - محاولة حديثة جداً');
      return;
    }

    if (isLoadingRef.current) {
      console.log('🔄 تحميل قيد التقدم بالفعل');
      return;
    }

    try {
      isLoadingRef.current = true;
      lastLoadTime.current = now;

      setState(prev => ({
        ...prev,
        loading: !isLoadMore,
        loadingMore: isLoadMore,
        error: null
      }));

      console.log(`📞 جلب المحادثات - الصفحة: ${page}, الحد: ${limit}, تحميل إضافي: ${isLoadMore}`);

      const response = await chatApiService.getConversations(page, limit);

      setState(prev => {
        const newConversations = isLoadMore 
          ? [...prev.conversations, ...response.conversations]
          : response.conversations;

        return {
          ...prev,
          conversations: newConversations,
          loading: false,
          loadingMore: false,
          hasMore: response.has_more,
          currentPage: response.page,
          totalCount: response.total_count,
          totalPages: response.total_pages,
          error: null
        };
      });

      console.log(`✅ تم جلب ${response.conversations.length} محادثة`);

    } catch (error) {
      console.error('❌ خطأ في جلب المحادثات:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        loadingMore: false,
        error: error instanceof Error ? error.message : 'فشل في تحميل المحادثات'
      }));
    } finally {
      isLoadingRef.current = false;
    }
  }, [initialLimit]);

  /**
   * تحميل المزيد من المحادثات
   */
  const loadMoreConversations = useCallback(() => {
    if (state.hasMore && !state.loadingMore && !state.loading) {
      console.log('📥 تحميل المزيد من المحادثات...');
      fetchConversations(state.currentPage + 1, loadMoreLimit, true);
    } else {
      console.log('❌ لا يمكن تحميل المزيد:', {
        hasMore: state.hasMore,
        loadingMore: state.loadingMore,
        loading: state.loading
      });
    }
  }, [fetchConversations, state.hasMore, state.loadingMore, state.loading, state.currentPage, loadMoreLimit]);

  /**
   * تحديث المحادثات (إعادة تحميل من البداية)
   */
  const refreshConversations = useCallback(() => {
    console.log('🔄 تحديث المحادثات...');
    setState(prev => ({
      ...prev,
      currentPage: 1,
      hasMore: true
    }));
    fetchConversations(1, initialLimit, false);
  }, [fetchConversations, initialLimit]);

  /**
   * إعادة تعيين التحميل التدريجي
   */
  const resetPagination = useCallback(() => {
    console.log('🔄 إعادة تعيين التحميل التدريجي...');
    setState({
      conversations: [],
      loading: false,
      loadingMore: false,
      error: null,
      hasMore: true,
      currentPage: 1,
      totalCount: 0,
      totalPages: 1
    });
  }, []);

  /**
   * تحميل تلقائي عند التهيئة
   */
  useEffect(() => {
    if (autoLoad) {
      console.log('🚀 تحميل تلقائي للمحادثات...');
      fetchConversations(1, initialLimit, false);
    }
  }, [autoLoad, fetchConversations, initialLimit]);

  return {
    ...state,
    loadMoreConversations,
    refreshConversations,
    resetPagination
  };
};

export default useConversationsPagination;
