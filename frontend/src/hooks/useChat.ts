/**
 * Hook للمحادثة الفورية
 * يدير حالة المحادثة والاتصال والرسائل
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { chatWebSocketService } from '../services/chatWebSocketService';
import { chatApiService, ChatMessage, Conversation, UserOnlineStatus } from '../services/chatApiService';
import { chatNotificationService } from '../services/chatNotificationService';

interface UseChatOptions {
  userId: number;
  autoConnect?: boolean;
}

interface ChatState {
  isConnected: boolean;
  isConnecting: boolean;
  conversations: Conversation[];
  currentConversation: number | null;
  messages: { [conversationId: number]: ChatMessage[] };
  onlineUsers: UserOnlineStatus[];
  unreadCount: number;
  typingUsers: { [userId: number]: boolean };
  messagesHasMore: { [conversationId: number]: boolean };
  messagesLoading: { [conversationId: number]: boolean };
}

export const useChat = ({ userId, autoConnect = true }: UseChatOptions) => {
  const [state, setState] = useState<ChatState>({
    isConnected: false,
    isConnecting: false,
    conversations: [],
    currentConversation: null,
    messages: {},
    onlineUsers: [],
    unreadCount: 0,
    typingUsers: {},
    messagesHasMore: {},
    messagesLoading: {}
  });

  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  
  // مراجع للتنظيف
  const typingTimeouts = useRef<{ [userId: number]: NodeJS.Timeout }>({});
  const isInitialized = useRef(false);

  // الاتصال بـ WebSocket مع تحسينات
  const connect = useCallback(async () => {
    // منع الاتصالات المتكررة
    if (state.isConnecting) {
      console.log('🔄 الاتصال قيد التقدم بالفعل...');
      return;
    }

    if (state.isConnected) {
      console.log('✅ الاتصال موجود بالفعل');
      return;
    }

    setState(prev => ({ ...prev, isConnecting: true }));
    setError(null);

    try {
      await chatWebSocketService.connect(userId);
      setState(prev => ({ ...prev, isConnected: true, isConnecting: false }));
      console.log('✅ تم الاتصال بنجاح');
    } catch (err: any) {
      const errorMessage = err?.message || 'فشل في الاتصال بخدمة المحادثة';
      setError(errorMessage);
      setState(prev => ({ ...prev, isConnecting: false }));
      console.error('خطأ في الاتصال:', err);
    }
  }, [userId, state.isConnecting, state.isConnected]);

  // قطع الاتصال
  const disconnect = useCallback(() => {
    chatWebSocketService.disconnect();
    setState(prev => ({ ...prev, isConnected: false, isConnecting: false }));
  }, []);

  // جلب المحادثات
  const loadConversations = useCallback(async () => {
    setLoading(true);
    try {
      const conversations = await chatApiService.getAllConversations();
      setState(prev => ({ ...prev, conversations }));
    } catch (err) {
      setError('فشل في جلب المحادثات');
      console.error('خطأ في جلب المحادثات:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // جلب عدد الرسائل غير المقروءة
  const loadUnreadCount = useCallback(async () => {
    try {
      const response = await chatApiService.getUnreadCount();
      setState(prev => ({ ...prev, unreadCount: response.unread_count }));
    } catch (err) {
      console.error('خطأ في جلب عدد الرسائل غير المقروءة:', err);
    }
  }, []);

  // جلب الرسائل لمحادثة معينة - التحميل الأولي فقط
  const loadMessages = useCallback(async (
    otherUserId: number,
    page: number = 1,
    limit: number = 20  // حد أقصى صارم للتحميل الأولي
  ) => {
    // تحديث حالة التحميل للمحادثة المحددة
    setState(prev => ({
      ...prev,
      messagesLoading: {
        ...prev.messagesLoading,
        [otherUserId]: true
      }
    }));

    try {
      // التحميل الأولي - جلب آخر N رسالة فقط
      const response = await chatApiService.getMessages(otherUserId, page, limit, undefined, 'newer');
      setState(prev => {
        return {
          ...prev,
          messages: {
            ...prev.messages,
            [otherUserId]: response.messages  // استبدال كامل للرسائل
          },
          messagesHasMore: {
            ...prev.messagesHasMore,
            [otherUserId]: response.has_more
          },
          messagesLoading: {
            ...prev.messagesLoading,
            [otherUserId]: false
          }
        };
      });
      return response;
    } catch (err) {
      setState(prev => ({
        ...prev,
        messagesLoading: {
          ...prev.messagesLoading,
          [otherUserId]: false
        }
      }));
      setError('فشل في جلب الرسائل');
      console.error('خطأ في جلب الرسائل:', err);
      throw err;
    }
  }, []);

  // تحميل الرسائل الأقدم
  const loadOlderMessages = useCallback(async (otherUserId: number) => {
    const existingMessages = state.messages[otherUserId] || [];
    const hasMore = state.messagesHasMore[otherUserId] !== false;
    const isLoading = state.messagesLoading[otherUserId] || false;

    // التحقق من إمكانية التحميل
    if (existingMessages.length === 0 || !hasMore || isLoading) {
      return null;
    }

    // استخدام ID أقدم رسالة كنقطة مرجعية
    const oldestMessageId = existingMessages[0]?.id;
    if (!oldestMessageId) return null;

    // تحديث حالة التحميل
    setState(prev => ({
      ...prev,
      messagesLoading: {
        ...prev.messagesLoading,
        [otherUserId]: true
      }
    }));

    try {
      // تحميل الرسائل الأقدم
      const response = await chatApiService.getMessages(otherUserId, 1, 20, oldestMessageId, 'older');
      setState(prev => {
        const currentMessages = prev.messages[otherUserId] || [];
        return {
          ...prev,
          messages: {
            ...prev.messages,
            [otherUserId]: [...response.messages, ...currentMessages]  // إضافة في البداية
          },
          messagesHasMore: {
            ...prev.messagesHasMore,
            [otherUserId]: response.has_more
          },
          messagesLoading: {
            ...prev.messagesLoading,
            [otherUserId]: false
          }
        };
      });
      return response;
    } catch (err) {
      setState(prev => ({
        ...prev,
        messagesLoading: {
          ...prev.messagesLoading,
          [otherUserId]: false
        }
      }));
      console.error('خطأ في تحميل الرسائل الأقدم:', err);
      return null;
    }
  }, [state.messages, state.messagesHasMore, state.messagesLoading]);

  // الحصول على حالة التحميل لمحادثة معينة
  const getMessagesLoadingState = useCallback((otherUserId: number) => {
    return {
      loading: state.messagesLoading[otherUserId] || false,
      hasMore: state.messagesHasMore[otherUserId] !== false
    };
  }, [state.messagesLoading, state.messagesHasMore]);

  // إرسال رسالة
  const sendMessage = useCallback(async (receiverId: number, content: string) => {
    try {
      const message = await chatApiService.sendMessage({
        receiver_id: receiverId,
        content,
        message_type: 'text'
      });

      // إضافة الرسالة محلياً
      setState(prev => ({
        ...prev,
        messages: {
          ...prev.messages,
          [receiverId]: [...(prev.messages[receiverId] || []), message]
        }
      }));

      // تحديث قائمة المحادثات فوراً
      if ((window as any).updateConversationsList) {
        (window as any).updateConversationsList(message, receiverId);
      }

      // تحديث قائمة المحادثات من الخادم كنسخة احتياطية
      setTimeout(() => {
        loadConversations();
      }, 500);

      return message;
    } catch (err) {
      setError('فشل في إرسال الرسالة');
      console.error('خطأ في إرسال الرسالة:', err);
      throw err;
    }
  }, [loadConversations]);

  // تحديد الرسائل كمقروءة
  const markAsRead = useCallback(async (otherUserId: number) => {
    try {
      // تحديث فوري للحالة المحلية أولاً
      setState(prev => {
        // حساب عدد الرسائل غير المقروءة للمحادثة المحددة
        const conversation = prev.conversations.find(conv => conv.user_id === otherUserId);
        const unreadCountForConversation = conversation?.unread_count || 0;

        // حساب عدد الرسائل غير المقروءة من الرسائل المحلية أيضاً
        const messagesFromOtherUser = prev.messages[otherUserId] || [];
        const localUnreadCount = messagesFromOtherUser.filter(msg =>
          msg.sender_id === otherUserId && msg.status !== 'read'
        ).length;

        // استخدام أكبر عدد للتأكد من الدقة
        const actualUnreadCount = Math.max(unreadCountForConversation, localUnreadCount);

        console.log(`📖 تحديث فوري: تحديد ${actualUnreadCount} رسالة كمقروءة للمستخدم ${otherUserId}`);

        // إرسال إشعار فوري لتحديث جميع المكونات
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('unreadCountUpdated', {
            detail: { newCount: Math.max(0, prev.unreadCount - actualUnreadCount) }
          }));
        }, 50);

        return {
          ...prev,
          conversations: prev.conversations.map(conv =>
            conv.user_id === otherUserId ? { ...conv, unread_count: 0 } : conv
          ),
          messages: {
            ...prev.messages,
            [otherUserId]: (prev.messages[otherUserId] || []).map(msg =>
              msg.sender_id === otherUserId ? { ...msg, status: 'read' } : msg
            )
          },
          // تقليل عدد الرسائل غير المقروءة الإجمالي فوراً
          unreadCount: Math.max(0, prev.unreadCount - actualUnreadCount)
        };
      });

      // إرسال الطلب للخادم في الخلفية
      await chatApiService.markMessagesAsRead(otherUserId);

      // تحديث إضافي للعدد من الخادم للتأكد من الدقة
      setTimeout(() => {
        loadUnreadCount();
      }, 1000);

    } catch (err) {
      console.error('خطأ في تحديد الرسائل كمقروءة:', err);
      // في حالة الخطأ، إعادة تحميل العدد من الخادم
      setTimeout(() => {
        loadUnreadCount();
      }, 500);
    }
  }, [userId, loadUnreadCount]);

  // تحديد المحادثة الحالية
  const setCurrentConversation = useCallback((conversationId: number | null) => {
    setState(prev => ({ ...prev, currentConversation: conversationId }));
    
    // تحديد الرسائل كمقروءة عند فتح المحادثة
    if (conversationId) {
      markAsRead(conversationId);
    }
  }, [markAsRead]);

  // إرسال إشعار الكتابة
  const sendTypingNotification = useCallback((receiverId: number) => {
    if (state.isConnected) {
      chatWebSocketService.sendTypingNotification(receiverId);
    }
  }, [state.isConnected]);

  // إرسال إشعار توقف الكتابة
  const sendStopTypingNotification = useCallback((receiverId: number) => {
    if (state.isConnected) {
      chatWebSocketService.sendStopTypingNotification(receiverId);
    }
  }, [state.isConnected]);

  // معالجة أحداث WebSocket
  useEffect(() => {
    const handleNewMessage = (data: any) => {
      const message: ChatMessage = data.message;

      console.log('📨 رسالة جديدة في useChat:', {
        messageId: message.id,
        senderId: message.sender_id,
        currentUserId: userId,
        isIncoming: message.sender_id !== userId
      });

      // إرسال التنبيه للرسائل الواردة فقط
      if (message.sender_id !== userId) {
        console.log('🔔 إرسال رسالة لخدمة التنبيهات...');
        // تحديث نشاط المستخدم عند وصول رسالة جديدة
        chatNotificationService.refreshUserActivity();
        chatNotificationService.handleNewMessage(message);
      } else {
        console.log('📤 رسالة صادرة - لا حاجة لتنبيه');
      }

      setState(prev => {
        // تحديد المحادثة المناسبة (المرسل أو المستقبل)
        const conversationId = message.sender_id === userId ? message.receiver_id : message.sender_id;

        // تحديث المحادثات فوراً
        const existingConvIndex = prev.conversations.findIndex(conv => conv.user_id === conversationId);
        let updatedConversations = [...prev.conversations];

        if (existingConvIndex !== -1) {
          // تحديث المحادثة الموجودة
          updatedConversations[existingConvIndex] = {
            ...updatedConversations[existingConvIndex],
            last_message: message,
            unread_count: message.sender_id !== userId ?
              updatedConversations[existingConvIndex].unread_count + 1 :
              updatedConversations[existingConvIndex].unread_count
          };
        } else {
          // إنشاء محادثة جديدة
          const newConversation = {
            user_id: conversationId,
            username: message.sender_username || `user_${conversationId}`,
            full_name: message.sender_full_name || message.sender_username || `User ${conversationId}`,
            is_online: false,
            last_seen: undefined,
            last_message: message,
            unread_count: message.sender_id !== userId ? 1 : 0
          };

          updatedConversations = [newConversation, ...updatedConversations];

          // تحديث المحادثات من الخادم للحصول على البيانات الكاملة
          setTimeout(() => {
            loadConversations();
          }, 500);
        }

        return {
          ...prev,
          messages: {
            ...prev.messages,
            [conversationId]: [...(prev.messages[conversationId] || []), message]
          },
          conversations: updatedConversations,
          // تحديث عدد الرسائل غير المقروءة فوراً (فقط للرسائل الواردة)
          unreadCount: message.sender_id !== userId ? prev.unreadCount + 1 : prev.unreadCount
        };
      });
    };

    const handleUserStatusChange = (data: any) => {
      setState(prev => ({
        ...prev,
        conversations: prev.conversations.map(conv =>
          conv.user_id === data.user_id
            ? { ...conv, is_online: data.is_online }
            : conv
        ),
        // تحديث قائمة المستخدمين المتصلين أيضاً
        onlineUsers: prev.onlineUsers.map(user =>
          user.user_id === data.user_id
            ? { ...user, is_online: data.is_online }
            : user
        )
      }));

      // إرسال إشعار للمكونات الأخرى عن تغيير حالة المستخدم
      console.log(`🔄 تحديث حالة المستخدم ${data.user_id}: ${data.is_online ? 'متصل' : 'غير متصل'}`);
    };

    const handleUserTyping = (data: any) => {
      setState(prev => ({
        ...prev,
        typingUsers: { ...prev.typingUsers, [data.user_id]: true }
      }));

      // إزالة إشعار الكتابة بعد 3 ثوان
      if (typingTimeouts.current[data.user_id]) {
        clearTimeout(typingTimeouts.current[data.user_id]);
      }
      
      typingTimeouts.current[data.user_id] = setTimeout(() => {
        setState(prev => ({
          ...prev,
          typingUsers: { ...prev.typingUsers, [data.user_id]: false }
        }));
      }, 3000);
    };

    const handleUserStopTyping = (data: any) => {
      setState(prev => ({
        ...prev,
        typingUsers: { ...prev.typingUsers, [data.user_id]: false }
      }));

      if (typingTimeouts.current[data.user_id]) {
        clearTimeout(typingTimeouts.current[data.user_id]);
        delete typingTimeouts.current[data.user_id];
      }
    };

    const handleMessagesRead = (data: any) => {
      const readerId = data.reader_id;
      console.log(`📖 تم قراءة ${data.count} رسالة من قبل المستخدم ${readerId}`);

      setState(prev => {
        // تحديث حالة الرسائل المرسلة إلى هذا المستخدم لتصبح مقروءة
        const updatedMessages = { ...prev.messages };

        if (updatedMessages[readerId]) {
          updatedMessages[readerId] = updatedMessages[readerId].map(msg =>
            msg.sender_id === userId && msg.status !== 'read'
              ? { ...msg, status: 'read', read_at: data.timestamp }
              : msg
          );
        }

        // تحديث عدد الرسائل غير المقروءة في المحادثات
        const updatedConversations = prev.conversations.map(conv =>
          conv.user_id === readerId
            ? { ...conv, unread_count: 0 }
            : conv
        );

        // هذا الحدث يعني أن شخص آخر قرأ رسائلي، لا يؤثر على unreadCount الخاص بي
        // unreadCount يمثل الرسائل التي لم أقرأها أنا، وليس الرسائل التي لم يقرأها الآخرون

        return {
          ...prev,
          messages: updatedMessages,
          conversations: updatedConversations
        };
      });
    };

    const handleMessageEdited = (data: any) => {
      setState(prev => {
        const updatedMessages = { ...prev.messages };
        let editedMessage: ChatMessage | null = null;

        // البحث في جميع المحادثات عن الرسالة المعدلة
        Object.keys(updatedMessages).forEach(conversationId => {
          const conversationMessages = updatedMessages[parseInt(conversationId)];
          if (conversationMessages) {
            updatedMessages[parseInt(conversationId)] = conversationMessages.map(message => {
              if (message.id === data.message_id) {
                editedMessage = {
                  ...message,
                  content: data.new_content,
                  is_edited: true
                } as ChatMessage;
                return editedMessage;
              }
              return message;
            });
          }
        });

        // تحديث آخر رسالة في المحادثات إذا كانت هي المحررة
        const updatedConversations = prev.conversations.map(conv => {
          if (editedMessage && conv.last_message && conv.last_message.id === data.message_id) {
            return {
              ...conv,
              last_message: editedMessage
            };
          }
          return conv;
        });

        return {
          ...prev,
          messages: updatedMessages,
          conversations: updatedConversations
        };
      });
    };

    const handleMessageDeleted = (data: any) => {
      setState(prev => {
        const updatedMessages = { ...prev.messages };

        // البحث في جميع المحادثات عن الرسالة المحذوفة وإزالتها
        Object.keys(updatedMessages).forEach(conversationId => {
          const conversationMessages = updatedMessages[parseInt(conversationId)];
          if (conversationMessages) {
            updatedMessages[parseInt(conversationId)] = conversationMessages.filter(
              message => message.id !== data.message_id
            );
          }
        });

        // تحديث آخر رسالة في المحادثات إذا كانت هي المحذوفة
        const updatedConversations = prev.conversations.map(conv => {
          if (conv.last_message && conv.last_message.id === data.message_id) {
            // البحث عن آخر رسالة متبقية في هذه المحادثة
            const conversationMessages = updatedMessages[conv.user_id];
            const lastMessage = conversationMessages && conversationMessages.length > 0
              ? conversationMessages[conversationMessages.length - 1]
              : undefined;

            return {
              ...conv,
              last_message: lastMessage
            };
          }
          return conv;
        });

        return {
          ...prev,
          messages: updatedMessages,
          conversations: updatedConversations
        };
      });
    };

    const handleConnected = () => {
      console.log('🎉 تم تأسيس اتصال المحادثة');
      setState(prev => ({ ...prev, isConnected: true, isConnecting: false }));
      setError(null);
    };

    const handleDisconnected = (data: any) => {
      console.log('🔌 انقطع اتصال المحادثة:', data);
      setState(prev => ({ ...prev, isConnected: false, isConnecting: false }));

      // عرض رسالة خطأ فقط إذا لم يكن إغلاق طبيعي
      if (data?.code && data.code !== 1000) {
        setError('انقطع الاتصال مع الخادم');
      }
    };

    const handleError = (error: any) => {
      console.error('❌ خطأ في WebSocket:', error);
      setError('خطأ في الاتصال');
      setState(prev => ({ ...prev, isConnected: false, isConnecting: false }));
    };

    const handleConnectionStatusChanged = (data: { isConnected: boolean; isConnecting: boolean }) => {
      console.log('🔄 تغيير حالة الاتصال:', data);
      setState(prev => ({
        ...prev,
        isConnected: data.isConnected,
        isConnecting: data.isConnecting
      }));

      if (data.isConnected) {
        setError(null);
      } else if (!data.isConnecting) {
        setError('انقطع الاتصال مع الخادم');
      }
    };

    const handleMaxReconnectAttempts = () => {
      console.error('❌ تم استنفاد جميع محاولات إعادة الاتصال');
      setError('فشل في إعادة الاتصال - يرجى تحديث الصفحة');
      setState(prev => ({ ...prev, isConnected: false, isConnecting: false }));
    };

    // معالج خاص لتحديث العدد عندما أقرأ أنا الرسائل
    const handleMyMessagesRead = (_otherUserId: number, count: number) => {
      setState(prev => ({
        ...prev,
        unreadCount: Math.max(0, prev.unreadCount - count)
      }));
      console.log(`📖 تم تقليل عدد الرسائل غير المقروءة بـ ${count}`);
    };

    // إضافة المعالج للنافذة العامة للوصول إليه من markAsRead
    (window as any).updateUnreadCount = handleMyMessagesRead;

    // تسجيل المستمعين
    chatWebSocketService.on('new_message', handleNewMessage);
    chatWebSocketService.on('message_edited', handleMessageEdited);
    chatWebSocketService.on('message_deleted', handleMessageDeleted);
    chatWebSocketService.on('messages_read', handleMessagesRead);
    chatWebSocketService.on('user_status_change', handleUserStatusChange);
    chatWebSocketService.on('user_typing', handleUserTyping);
    chatWebSocketService.on('user_stop_typing', handleUserStopTyping);
    chatWebSocketService.on('connected', handleConnected);
    chatWebSocketService.on('disconnected', handleDisconnected);
    chatWebSocketService.on('error', handleError);
    chatWebSocketService.on('max_reconnect_attempts_reached', handleMaxReconnectAttempts);
    chatWebSocketService.on('connection_status_changed', handleConnectionStatusChanged);

    return () => {
      // إزالة المعالج من النافذة العامة
      if ((window as any).updateUnreadCount) {
        delete (window as any).updateUnreadCount;
      }

      // إزالة المستمعين
      chatWebSocketService.off('new_message', handleNewMessage);
      chatWebSocketService.off('message_edited', handleMessageEdited);
      chatWebSocketService.off('message_deleted', handleMessageDeleted);
      chatWebSocketService.off('messages_read', handleMessagesRead);
      chatWebSocketService.off('user_status_change', handleUserStatusChange);
      chatWebSocketService.off('user_typing', handleUserTyping);
      chatWebSocketService.off('user_stop_typing', handleUserStopTyping);
      chatWebSocketService.off('connected', handleConnected);
      chatWebSocketService.off('disconnected', handleDisconnected);
      chatWebSocketService.off('error', handleError);
      chatWebSocketService.off('max_reconnect_attempts_reached', handleMaxReconnectAttempts);
      chatWebSocketService.off('connection_status_changed', handleConnectionStatusChanged);

      // تنظيف timeouts
      Object.values(typingTimeouts.current).forEach(timeout => clearTimeout(timeout));
    };
  }, [loadConversations]);

  // التهيئة الأولية مع تحسينات
  useEffect(() => {
    if (!isInitialized.current && userId) {
      isInitialized.current = true;

      console.log('🚀 تهيئة نظام المحادثة للمستخدم:', userId);

      // تحميل البيانات أولاً
      loadConversations();
      loadUnreadCount();

      // ثم الاتصال إذا كان مطلوباً
      if (autoConnect) {
        // تأخير قصير للسماح بتحميل البيانات
        setTimeout(() => {
          connect();
        }, 500);
      }
    }
  }, [userId, autoConnect, connect, loadConversations, loadUnreadCount]);

  // تحديث دوري لعدد الرسائل غير المقروءة
  useEffect(() => {
    if (!userId) return;

    // تحديث فوري
    loadUnreadCount();

    // تحديث دوري كل 30 ثانية
    const interval = setInterval(loadUnreadCount, 30000);

    return () => clearInterval(interval);
  }, [userId, loadUnreadCount]);

  return {
    // الحالة
    ...state,
    error,
    loading,

    // الوظائف
    connect,
    disconnect,
    loadConversations,
    loadMessages,
    loadOlderMessages,
    getMessagesLoadingState,
    sendMessage,
    markAsRead,
    loadUnreadCount,
    setCurrentConversation,
    sendTypingNotification,
    sendStopTypingNotification,

    // معلومات إضافية
    connectionState: chatWebSocketService.getConnectionState()
  };
};
