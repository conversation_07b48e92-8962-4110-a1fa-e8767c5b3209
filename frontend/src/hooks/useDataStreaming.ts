import { useState, useEffect, useCallback, useRef } from 'react';
import { toast } from 'react-hot-toast';

export interface StreamingTask {
  id: string;
  task_type: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress_percentage: number;
  current_record: number;
  total_records: number;
  estimated_time_remaining?: number;
  error_message?: string;
  created_at: string;
  file_size?: number;
}

export interface StreamingParameters {
  start_date?: string;
  end_date?: string;
  format_type: 'json' | 'csv';
  compress: boolean;
  tables?: string[];
  category?: string;
  low_stock?: boolean;
  with_debts?: boolean;
  user_id?: number;
  chunk_size?: number;
}

export interface StreamingMetrics {
  total_exports: number;
  successful_exports: number;
  failed_exports: number;
  average_export_time: number;
  total_data_exported_mb: number;
  most_exported_table: string;
  peak_usage_hour: number;
}

interface UseDataStreamingOptions {
  autoMonitor?: boolean;
  monitorInterval?: number;
  maxRetries?: number;
}

export const useDataStreaming = (options: UseDataStreamingOptions = {}) => {
  const {
    autoMonitor = true,
    monitorInterval = 2000,
    maxRetries = 3
  } = options;

  const [tasks, setTasks] = useState<StreamingTask[]>([]);
  const [metrics, setMetrics] = useState<StreamingMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const monitoringRef = useRef<NodeJS.Timeout | null>(null);
  const retryCountRef = useRef<Map<string, number>>(new Map());

  // الحصول على التوكن
  const getAuthToken = useCallback(() => {
    return localStorage.getItem('token') || sessionStorage.getItem('token');
  }, []);

  // إنشاء headers للطلبات
  const getHeaders = useCallback(() => {
    const token = getAuthToken();
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };
  }, [getAuthToken]);

  // إنشاء مهمة تدفق جديدة
  const createTask = useCallback(async (
    taskType: string,
    parameters: StreamingParameters
  ): Promise<string | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/data-streaming/tasks/create', {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify({
          task_type: taskType,
          parameters
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      const newTask: StreamingTask = {
        id: result.task_id,
        task_type: taskType,
        status: 'pending',
        progress_percentage: 0,
        current_record: 0,
        total_records: 0,
        created_at: new Date().toISOString()
      };

      setTasks(prev => [newTask, ...prev]);
      toast.success('تم إنشاء مهمة التصدير بنجاح');

      return result.task_id;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف';
      setError(errorMessage);
      toast.error(`خطأ في إنشاء مهمة التصدير: ${errorMessage}`);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [getHeaders]);

  // مراقبة تقدم المهمة
  const monitorTask = useCallback(async (taskId: string): Promise<StreamingTask | null> => {
    try {
      const response = await fetch(`/api/data-streaming/tasks/${taskId}/progress`, {
        headers: getHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const progress = await response.json();

      setTasks(prev => prev.map(task =>
        task.id === taskId ? { ...task, ...progress } : task
      ));

      // إعادة تعيين عداد المحاولات عند النجاح
      retryCountRef.current.delete(taskId);

      // إشعارات للحالات المكتملة
      if (progress.status === 'completed') {
        toast.success('اكتمل التصدير بنجاح!');
      } else if (progress.status === 'failed') {
        toast.error(`فشل التصدير: ${progress.error_message || 'خطأ غير معروف'}`);
      }

      return progress;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'خطأ في مراقبة المهمة';

      // إدارة المحاولات المتكررة
      const currentRetries = retryCountRef.current.get(taskId) || 0;
      if (currentRetries < maxRetries) {
        retryCountRef.current.set(taskId, currentRetries + 1);
        console.warn(`فشل في مراقبة المهمة ${taskId}، المحاولة ${currentRetries + 1}/${maxRetries}`);
      } else {
        console.error(`فشل نهائي في مراقبة المهمة ${taskId}: ${errorMessage}`);
        setTasks(prev => prev.map(task =>
          task.id === taskId
            ? { ...task, status: 'failed' as const, error_message: 'فشل في المراقبة' }
            : task
        ));
      }

      return null;
    }
  }, [getHeaders, maxRetries]);

  // تحميل نتيجة المهمة
  const downloadTask = useCallback(async (taskId: string, filename?: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/data-streaming/tasks/${taskId}/download`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);

      // استخراج اسم الملف من headers أو استخدام الافتراضي
      const contentDisposition = response.headers.get('Content-Disposition');
      let downloadFilename = filename;

      if (!downloadFilename && contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (filenameMatch) {
          downloadFilename = filenameMatch[1].replace(/['"]/g, '');
        }
      }

      if (!downloadFilename) {
        downloadFilename = `export_${taskId}_${new Date().getTime()}`;
      }

      const a = document.createElement('a');
      a.href = url;
      a.download = downloadFilename;
      document.body.appendChild(a);
      a.click();

      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success('تم تحميل الملف بنجاح');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'خطأ في التحميل';
      toast.error(`خطأ في تحميل الملف: ${errorMessage}`);
      return false;
    }
  }, [getAuthToken]);

  // حذف مهمة
  const removeTask = useCallback((taskId: string) => {
    setTasks(prev => prev.filter(task => task.id !== taskId));
    retryCountRef.current.delete(taskId);
  }, []);

  // تنظيف المهام المكتملة
  const clearCompletedTasks = useCallback(() => {
    setTasks(prev => prev.filter(task =>
      task.status !== 'completed' && task.status !== 'failed'
    ));
  }, []);

  // الحصول على المقاييس
  const fetchMetrics = useCallback(async (): Promise<StreamingMetrics | null> => {
    try {
      const response = await fetch('/api/data-streaming/metrics', {
        headers: getHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const metricsData = await response.json();
      setMetrics(metricsData);
      return metricsData;
    } catch (error) {
      console.error('خطأ في جلب المقاييس:', error);
      return null;
    }
  }, [getHeaders]);

  // تنظيف الملفات القديمة
  const cleanupOldFiles = useCallback(async (maxAgeHours: number = 24): Promise<boolean> => {
    try {
      const response = await fetch(`/api/data-streaming/cleanup?max_age_hours=${maxAgeHours}`, {
        method: 'POST',
        headers: getHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      toast.success('تم تنظيف الملفات القديمة بنجاح');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'خطأ في التنظيف';
      toast.error(`خطأ في تنظيف الملفات: ${errorMessage}`);
      return false;
    }
  }, [getHeaders]);

  // تدفق مباشر للبيانات
  const streamData = useCallback(async (
    endpoint: string,
    parameters: Record<string, any> = {}
  ): Promise<ReadableStreamDefaultReader<Uint8Array> | null> => {
    try {
      const queryParams = new URLSearchParams();
      Object.entries(parameters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value));
        }
      });

      const url = `/api/data-streaming/${endpoint}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return response.body?.getReader() || null;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'خطأ في التدفق';
      toast.error(`خطأ في تدفق البيانات: ${errorMessage}`);
      return null;
    }
  }, [getAuthToken]);

  // تعطيل المراقبة التلقائية للمهام لتوفير موارد النظام
  useEffect(() => {
    if (!autoMonitor) return;

    // تعطيل المراقبة التلقائية لتوفير موارد النظام
    // const startMonitoring = () => {
    //   monitoringRef.current = setInterval(() => {
    //     const activeTasks = tasks.filter(task =>
    //       task.status === 'running' || task.status === 'pending'
    //     );

    //     activeTasks.forEach(task => {
    //       monitorTask(task.id);
    //     });
    //   }, monitorInterval);
    // };

    const stopMonitoring = () => {
      if (monitoringRef.current) {
        clearInterval(monitoringRef.current);
        monitoringRef.current = null;
      }
    };

    // تعطيل بدء المراقبة التلقائية
    // if (tasks.some(task => task.status === 'running' || task.status === 'pending')) {
    //   startMonitoring();
    // } else {
    //   stopMonitoring();
    // }

    return stopMonitoring;
  }, [tasks, autoMonitor, monitorInterval, monitorTask]);

  // تنظيف عند إلغاء التحميل
  useEffect(() => {
    return () => {
      if (monitoringRef.current) {
        clearInterval(monitoringRef.current);
      }
    };
  }, []);

  // إحصائيات مفيدة
  const taskStats = {
    total: tasks.length,
    pending: tasks.filter(t => t.status === 'pending').length,
    running: tasks.filter(t => t.status === 'running').length,
    completed: tasks.filter(t => t.status === 'completed').length,
    failed: tasks.filter(t => t.status === 'failed').length
  };

  return {
    // البيانات
    tasks,
    metrics,
    taskStats,
    isLoading,
    error,

    // العمليات
    createTask,
    monitorTask,
    downloadTask,
    removeTask,
    clearCompletedTasks,
    fetchMetrics,
    cleanupOldFiles,
    streamData,

    // مساعدات
    getActiveTasks: () => tasks.filter(t => t.status === 'running' || t.status === 'pending'),
    getCompletedTasks: () => tasks.filter(t => t.status === 'completed'),
    getFailedTasks: () => tasks.filter(t => t.status === 'failed')
  };
};
