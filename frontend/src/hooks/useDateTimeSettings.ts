/**
 * Hook مخصص لإدارة إعدادات التاريخ والوقت
 * يوفر واجهة سهلة للوصول إلى إعدادات التاريخ والوقت وتنسيق التواريخ
 * يطبق مبادئ البرمجة الكائنية والتصميم الموحد للنظام
 */

import { useState, useEffect, useCallback } from 'react';
import { 
  fetchDateTimeSettings, 
  formatDateTimeWithSettings, 
  DateTimeSettings,
  clearDateTimeSettingsCache 
} from '../services/dateTimeService';

interface UseDateTimeSettingsReturn {
  settings: DateTimeSettings | null;
  isLoading: boolean;
  error: string | null;
  formatDate: (date: Date | string) => Promise<string>;
  formatTime: (date: Date | string) => Promise<string>;
  formatDateTime: (date: Date | string) => Promise<string>;
  refreshSettings: () => Promise<void>;
}

/**
 * Hook لإدارة إعدادات التاريخ والوقت
 * @returns كائن يحتوي على الإعدادات ووظائف التنسيق
 */
export const useDateTimeSettings = (): UseDateTimeSettingsReturn => {
  const [settings, setSettings] = useState<DateTimeSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // تحميل الإعدادات
  const loadSettings = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const fetchedSettings = await fetchDateTimeSettings();
      setSettings(fetchedSettings);
    } catch (err) {
      console.error('Error loading datetime settings:', err);
      setError('فشل في تحميل إعدادات التاريخ والوقت');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // تحديث الإعدادات
  const refreshSettings = useCallback(async () => {
    clearDateTimeSettingsCache();
    await loadSettings();
  }, [loadSettings]);

  // تحميل الإعدادات عند تحميل المكون
  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  // وظائف التنسيق مع معالجة الأخطاء
  const formatDate = useCallback(async (date: Date | string): Promise<string> => {
    if (!date) return '';
    
    try {
      return await formatDateTimeWithSettings(date, 'date', settings || undefined);
    } catch (err) {
      console.error('Error formatting date:', err);
      return typeof date === 'string' ? date : date.toLocaleDateString('ar-LY');
    }
  }, [settings]);

  const formatTime = useCallback(async (date: Date | string): Promise<string> => {
    if (!date) return '';
    
    try {
      return await formatDateTimeWithSettings(date, 'time', settings || undefined);
    } catch (err) {
      console.error('Error formatting time:', err);
      return typeof date === 'string' ? date : date.toLocaleTimeString('ar-LY');
    }
  }, [settings]);

  const formatDateTime = useCallback(async (date: Date | string): Promise<string> => {
    if (!date) return '';
    
    try {
      return await formatDateTimeWithSettings(date, 'datetime', settings || undefined);
    } catch (err) {
      console.error('Error formatting datetime:', err);
      return typeof date === 'string' ? date : date.toLocaleString('ar-LY');
    }
  }, [settings]);

  return {
    settings,
    isLoading,
    error,
    formatDate,
    formatTime,
    formatDateTime,
    refreshSettings
  };
};

/**
 * Hook مبسط للحصول على إعدادات التاريخ والوقت فقط
 * @returns الإعدادات الحالية أو null
 */
export const useDateTimeSettingsOnly = (): DateTimeSettings | null => {
  const { settings } = useDateTimeSettings();
  return settings;
};

/**
 * Hook للحصول على وظائف التنسيق فقط
 * @returns وظائف التنسيق
 */
export const useDateTimeFormatters = () => {
  const { formatDate, formatTime, formatDateTime } = useDateTimeSettings();
  return { formatDate, formatTime, formatDateTime };
};

export default useDateTimeSettings;
