/**
 * Hook للنصائح الذكية - useSmartTips
 * يدير حالة النصائح الذكية ويوفر واجهة سهلة للمكونات
 * 
 * الميزات:
 * - إدارة حالة النصائح
 * - تحديث تلقائي للنصائح
 * - معالجة الإجراءات (إغلاق، تأجيل)
 * - تتبع تفضيلات المستخدم
 * - دعم الخوارزمية الذكية
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { smartTipsService, SmartTip, TipCategory } from '../services/smartTipsService';
import { useAuthStore } from '../stores/authStore';

interface UseSmartTipsOptions {
  autoRefresh?: boolean;
  refreshInterval?: number; // بالثواني
  categories?: TipCategory[];
  enabled?: boolean;
}

interface UseSmartTipsReturn {
  currentTip: SmartTip | null;
  isLoading: boolean;
  isEnabled: boolean;
  refreshTip: () => void;
  closeTip: () => void;
  snoozeTip: (minutes: number) => void;
  toggleTips: (enabled: boolean) => void;
  updateCategories: (categories: TipCategory[]) => void;
  tipHistory: { [tipId: string]: { count: number; lastShown: Date } };
}

export const useSmartTips = (options: UseSmartTipsOptions = {}): UseSmartTipsReturn => {
  const {
    autoRefresh = true,
    refreshInterval = 30,
    categories = ['welcome', 'productivity', 'business'],
    enabled = true
  } = options;

  const [currentTip, setCurrentTip] = useState<SmartTip | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isEnabled, setIsEnabled] = useState(enabled);
  const [tipHistory, setTipHistory] = useState<{ [tipId: string]: { count: number; lastShown: Date } }>({});
  
  const refreshIntervalRef = useRef<NodeJS.Timeout>();
  const snoozeTimeoutRef = useRef<NodeJS.Timeout>();
  const { user } = useAuthStore();

  /**
   * تحديث النصيحة الحالية
   */
  const refreshTip = useCallback(async () => {
    if (!isEnabled || !user) {
      setCurrentTip(null);
      return;
    }

    setIsLoading(true);

    try {
      // محاكاة تأخير قصير للتأثير البصري
      await new Promise(resolve => setTimeout(resolve, 300));

      const tip = smartTipsService.getSmartTip();
      setCurrentTip(tip);
      
      // تحديث تاريخ النصائح
      try {
        const savedSettings = localStorage.getItem('smartTipsSettings');
        if (savedSettings) {
          const parsed = JSON.parse(savedSettings);
          if (parsed.tipHistory) {
            setTipHistory({ ...parsed.tipHistory });
          }
        }
      } catch (error) {
        console.error('خطأ في تحديث تاريخ النصائح:', error);
      }
    } catch (error) {
      console.error('خطأ في تحديث النصيحة:', error);
      setCurrentTip(null);
    } finally {
      setIsLoading(false);
    }
  }, [isEnabled, user]);

  /**
   * إغلاق النصيحة الحالية
   */
  const closeTip = useCallback(() => {
    setCurrentTip(null);
    
    // تحديث النصيحة بعد فترة قصيرة
    setTimeout(() => {
      refreshTip();
    }, 5000);
  }, [refreshTip]);

  /**
   * تأجيل النصيحة لفترة محددة
   */
  const snoozeTip = useCallback((minutes: number) => {
    setCurrentTip(null);
    
    // مسح أي تأجيل سابق
    if (snoozeTimeoutRef.current) {
      clearTimeout(snoozeTimeoutRef.current);
    }
    
    // تعيين تأجيل جديد
    snoozeTimeoutRef.current = setTimeout(() => {
      refreshTip();
    }, minutes * 60 * 1000);
    
    console.log(`تم تأجيل النصائح لـ ${minutes} دقيقة`);
  }, [refreshTip]);

  /**
   * تفعيل/تعطيل النصائح
   */
  const toggleTips = useCallback((enabled: boolean) => {
    setIsEnabled(enabled);
    
    // تحديث إعدادات الخدمة
    smartTipsService.updateSettings({ enabled });
    
    if (!enabled) {
      setCurrentTip(null);
      // مسح التحديث التلقائي
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    } else {
      // إعادة تشغيل التحديث التلقائي
      refreshTip();
      if (autoRefresh) {
        refreshIntervalRef.current = setInterval(refreshTip, refreshInterval * 1000);
      }
    }
  }, [refreshTip, autoRefresh, refreshInterval]);

  /**
   * تحديث فئات النصائح المفضلة
   */
  const updateCategories = useCallback((newCategories: TipCategory[]) => {
    smartTipsService.updateSettings({ 
      preferredCategories: newCategories 
    });
    
    // تحديث النصيحة الحالية
    refreshTip();
  }, [refreshTip]);

  /**
   * تأثير التهيئة الأولية - مرة واحدة فقط
   */
  useEffect(() => {
    let mounted = true;

    const initializeTips = async () => {
      if (user && isEnabled && mounted) {
        // تحميل النصيحة الأولى
        await refreshTip();
      }
    };

    initializeTips();

    // تنظيف عند إلغاء التحميل
    return () => {
      mounted = false;
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
      if (snoozeTimeoutRef.current) {
        clearTimeout(snoozeTimeoutRef.current);
      }
    };
  }, [user?.id, isEnabled]); // فقط عند تغيير المستخدم أو التفعيل

  /**
   * تأثير التحديث التلقائي
   */
  useEffect(() => {
    if (user && isEnabled && autoRefresh) {
      refreshIntervalRef.current = setInterval(() => {
        refreshTip();
      }, refreshInterval * 1000);
    }

    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, [autoRefresh, refreshInterval, user?.id, isEnabled]);

  /**
   * تأثير تحديث الفئات - مرة واحدة فقط
   */
  useEffect(() => {
    if (categories.length > 0) {
      smartTipsService.updateSettings({
        preferredCategories: categories
      });
    }
  }, [categories.join(',')]); // فقط عند تغيير الفئات فعلياً

  // تم دمج مراقبة تغيير المستخدم في useEffect الرئيسي

  // تم إزالة مراقبة الوقت لتوفير الموارد - سيتم التحديث عبر الفترة الزمنية العادية

  return {
    currentTip,
    isLoading,
    isEnabled,
    refreshTip,
    closeTip,
    snoozeTip,
    toggleTips,
    updateCategories,
    tipHistory
  };
};

export default useSmartTips;
