/**
 * Hook لتمييز الأجهزة والتحقق من الجهاز الرئيسي
 */

import { useState, useEffect } from 'react';
import api from '../lib/axios';

export interface DeviceInfo {
  fingerprint: string;
  hostname: string;
  platform: string;
  system: string;
  machine: string;
  processor: string;
  node: string;
  timestamp: string;
  error?: string;
}

export interface ServerIdentity {
  server_fingerprint: string;
  server_hostname: string;
  server_platform: string;
  initialized_at: string;
  version: string;
}

export interface DeviceDetectionResult {
  isMainServer: boolean;
  isLoading: boolean;
  error?: string;
  clientIp?: string;
  currentDevice?: DeviceInfo;
  serverIdentity?: ServerIdentity;
  detectionMethod?: string;
  status?: string;
  message?: string;
}

/**
 * Hook لتمييز الأجهزة
 */
export const useDeviceDetection = () => {
  const [deviceInfo, setDeviceInfo] = useState<DeviceDetectionResult>({
    isMainServer: true, // افتراضي
    isLoading: true,
  });

  const checkDeviceInfo = async () => {
    try {
      setDeviceInfo(prev => ({ ...prev, isLoading: true, error: undefined }));

      const response = await api.get('/api/settings/device-info');
      const data = response.data;

      setDeviceInfo({
        isMainServer: data.is_main_server,
        isLoading: false,
        clientIp: data.client_ip,
        currentDevice: data.current_device,
        serverIdentity: data.server_identity,
        detectionMethod: data.detection_method,
        status: data.status,
        message: data.message,
        error: data.error,
      });

      return data.is_main_server;
    } catch (error: any) {
      console.error('Error checking device info:', error);

      setDeviceInfo({
        isMainServer: false, // افتراض أنه جهاز بعيد في حالة الخطأ
        isLoading: false,
        error: error.response?.data?.message || error.message || 'فشل في التحقق من معلومات الجهاز',
      });

      return false;
    }
  };

  useEffect(() => {
    checkDeviceInfo();
  }, []);

  return {
    ...deviceInfo,
    refresh: checkDeviceInfo,
  };
};

/**
 * Hook مبسط للتحقق من الجهاز الرئيسي فقط
 */
export const useIsMainServer = () => {
  const { isMainServer, isLoading, error } = useDeviceDetection();

  return {
    isMainServer,
    isLoading,
    error,
  };
};

export default useDeviceDetection;
