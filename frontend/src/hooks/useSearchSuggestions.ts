import { useState, useEffect, useCallback } from 'react';
import api from '../lib/axios';

interface SearchSuggestion {
  id: number;
  name: string;
  barcode?: string;
}

interface Product {
  id: number;
  name: string;
  barcode?: string;
}

export const useSearchSuggestions = () => {
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Cache for products to avoid repeated API calls
  const [cacheLoaded, setCacheLoaded] = useState(false);

  // Load products for suggestions (only names and barcodes)
  const loadProductsForSuggestions = useCallback(async () => {
    if (cacheLoaded) return;

    try {
      setLoading(true);
      setError(null);

      // Get products with minimal data for suggestions
      const response = await api.get('/api/products/', {
        params: {
          page: 1,
          limit: 500, // Get products for suggestions
          is_active: true
        }
      });

      // Handle both array and object responses
      const products = Array.isArray(response.data) ? response.data : (response.data.products || []);
      setCacheLoaded(true);

      // Generate suggestions from products (one suggestion per product)
      const newSuggestions: SearchSuggestion[] = products.map((product: Product) => ({
        id: product.id,
        name: product.name,
        barcode: product.barcode
      }));

      setSuggestions(newSuggestions);
    } catch (err: any) {
      console.error('Error loading products for suggestions:', err);
      setError('فشل في تحميل اقتراحات البحث');
    } finally {
      setLoading(false);
    }
  }, [cacheLoaded]);

  // Refresh suggestions (force reload)
  const refreshSuggestions = useCallback(async () => {
    setCacheLoaded(false);
    setSuggestions([]);
    await loadProductsForSuggestions();
  }, [loadProductsForSuggestions]);

  // Filter suggestions based on search term
  const getFilteredSuggestions = useCallback((searchTerm: string): SearchSuggestion[] => {
    if (!searchTerm.trim() || suggestions.length === 0) {
      return [];
    }

    const term = searchTerm.toLowerCase().trim();

    const filtered = suggestions.filter(suggestion => {
      const nameMatch = suggestion.name.toLowerCase().includes(term);
      const barcodeMatch = suggestion.barcode?.toLowerCase().includes(term);
      return nameMatch || barcodeMatch;
    }).slice(0, 10); // Limit to 10 suggestions

    return filtered;
  }, [suggestions]);

  // Load suggestions on mount
  useEffect(() => {
    loadProductsForSuggestions();
  }, [loadProductsForSuggestions]);

  return {
    suggestions,
    loading,
    error,
    refreshSuggestions,
    getFilteredSuggestions,
    cacheLoaded
  };
};

export default useSearchSuggestions;
