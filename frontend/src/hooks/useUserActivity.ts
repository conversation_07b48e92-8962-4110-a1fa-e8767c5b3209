/**
 * Hook لتتبع نشاط المستخدم وإرسال معلومات المستخدم الحالي للخادم
 */

import { useEffect, useRef } from 'react';
import { useAuthStore } from '../stores/authStore';
import api from '../lib/axios';

interface UserActivityOptions {
  /** تفعيل تتبع النشاط */
  enabled?: boolean;
  /** فترة إرسال التحديثات بالثواني */
  updateInterval?: number;
}

export const useUserActivity = (options: UserActivityOptions = {}) => {
  const { enabled = true, updateInterval = 30 } = options;
  const { user } = useAuthStore();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastUserRef = useRef<string | null>(null);
  const isDeviceApprovedRef = useRef<boolean>(true); // افتراض أن الجهاز معتمد في البداية

  const sendUserActivity = async (currentUser: string | null) => {
    try {
      // إرسال معلومات المستخدم الحالي في headers
      const headers: Record<string, string> = {};

      if (currentUser) {
        headers['X-Current-User'] = currentUser;
      }

      // إضافة معرف الجهاز من بصمة الجهاز
      try {
        const { unifiedDeviceFingerprint } = await import('../services/unifiedDeviceFingerprint');
        const deviceId = await unifiedDeviceFingerprint.getDeviceId();
        if (deviceId) {
          headers['X-Device-ID'] = deviceId;
        }
      } catch (error) {
        console.debug('لم يتم العثور على معرف الجهاز:', error);
      }

      // إرسال طلب لتحديث نشاط المستخدم
      await api.post('/api/settings/update-device-user',
        { current_user: currentUser },
        { headers }
      );

      console.log(`✅ تم إرسال نشاط المستخدم: ${currentUser || 'غير محدد'}`);

      // إذا نجح الطلب، فالجهاز معتمد
      isDeviceApprovedRef.current = true;

    } catch (error: any) {
      // إذا كان الخطأ 403 (غير معتمد)، لا نعرض رسالة خطأ لأنه أمر طبيعي
      if (error?.response?.status === 403) {
        isDeviceApprovedRef.current = false;
        console.log('ℹ️ الجهاز في انتظار الموافقة - تم إيقاف إرسال تحديثات المستخدم مؤقتاً');
        console.log('📋 سبب الرفض:', error?.response?.data?.detail || 'الجهاز غير معتمد');
        return;
      }
      // إذا كان الخطأ 400 (معرف الجهاز مفقود)
      if (error?.response?.status === 400) {
        console.warn('⚠️ معرف الجهاز مفقود - لا يمكن تتبع نشاط المستخدم');
        console.log('📋 تفاصيل الخطأ:', error?.response?.data?.detail);
        return;
      }
      console.debug('خطأ في إرسال نشاط المستخدم:', error);
    }
  };

  const startTracking = () => {
    if (!enabled) return;

    // إرسال فوري عند بدء التتبع
    const currentUser = user?.username || null;
    sendUserActivity(currentUser);
    lastUserRef.current = currentUser;

    // إعداد التحديث الدوري
    intervalRef.current = setInterval(() => {
      const currentUser = user?.username || null;

      // إرسال التحديث إذا تغير المستخدم أو إذا كان الجهاز غير معتمد (للتحقق من الموافقة)
      if (currentUser !== lastUserRef.current || !isDeviceApprovedRef.current) {
        sendUserActivity(currentUser);
        lastUserRef.current = currentUser;
      }
    }, updateInterval * 1000);
  };

  const stopTracking = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  useEffect(() => {
    if (enabled) {
      startTracking();
    } else {
      stopTracking();
    }

    return () => {
      stopTracking();
    };
  }, [enabled, user?.username, updateInterval]);

  // إرسال تحديث عند تغيير المستخدم (فقط للأجهزة المعتمدة)
  useEffect(() => {
    if (enabled && user?.username !== lastUserRef.current && isDeviceApprovedRef.current) {
      sendUserActivity(user?.username || null);
      lastUserRef.current = user?.username || null;
    }
  }, [user?.username, enabled]);

  return {
    sendUserActivity,
    startTracking,
    stopTracking,
    isTracking: intervalRef.current !== null
  };
};

/**
 * Hook مبسط لتتبع النشاط التلقائي
 */
export const useAutoUserActivity = () => {
  return useUserActivity({
    enabled: true,
    updateInterval: 60 // كل دقيقة لتقليل التحديثات المتكررة
  });
};
