/**
 * Hook لاستخدام مؤشرات المقارنة مع الأمس
 * يوفر واجهة سهلة لجلب وإدارة بيانات المقارنة
 * يتبع مبادئ React Hooks والتصميم الموحد للنظام
 */

import { useState, useEffect, useCallback } from 'react';
import { 
  comparisonIndicatorService, 
  DashboardComparison, 
  ComparisonData 
} from '../services/comparisonIndicatorService';

interface UseComparisonIndicatorsOptions {
  /** تفعيل التحديث التلقائي */
  autoRefresh?: boolean;
  /** فترة التحديث التلقائي بالثواني (افتراضي: 300 = 5 دقائق) */
  refreshInterval?: number;
  /** تفعيل جلب البيانات عند التحميل */
  fetchOnMount?: boolean;
}

interface UseComparisonIndicatorsReturn {
  /** بيانات المقارنة للوحة التحكم */
  comparison: DashboardComparison | null;
  /** حالة التحميل */
  isLoading: boolean;
  /** رسالة الخطأ */
  error: string | null;
  /** دالة إعادة جلب البيانات */
  refetch: () => Promise<void>;
  /** دالة مسح الكاش */
  clearCache: () => void;
  /** آخر وقت تحديث */
  lastUpdated: Date | null;
}

/**
 * Hook لاستخدام مؤشرات المقارنة
 */
export const useComparisonIndicators = (
  options: UseComparisonIndicatorsOptions = {}
): UseComparisonIndicatorsReturn => {
  const {
    autoRefresh = false,
    refreshInterval = 300, // 5 دقائق
    fetchOnMount = true
  } = options;

  const [comparison, setComparison] = useState<DashboardComparison | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  /**
   * جلب بيانات المقارنة
   */
  const fetchComparison = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('جلب بيانات مؤشرات المقارنة...');
      const data = await comparisonIndicatorService.getDashboardComparison();
      
      setComparison(data);
      setLastUpdated(new Date());
      console.log('تم جلب بيانات المقارنة بنجاح:', data);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'خطأ في جلب بيانات المقارنة';
      console.error('خطأ في جلب بيانات المقارنة:', err);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * إعادة جلب البيانات
   */
  const refetch = useCallback(async () => {
    await fetchComparison();
  }, [fetchComparison]);

  /**
   * مسح الكاش
   */
  const clearCache = useCallback(() => {
    comparisonIndicatorService.clearCache();
    console.log('تم مسح كاش مؤشرات المقارنة');
  }, []);

  // جلب البيانات عند التحميل
  useEffect(() => {
    if (fetchOnMount) {
      fetchComparison();
    }
  }, [fetchOnMount, fetchComparison]);

  // التحديث التلقائي
  useEffect(() => {
    if (!autoRefresh || refreshInterval <= 0) {
      return;
    }

    const interval = setInterval(() => {
      console.log('تحديث تلقائي لمؤشرات المقارنة');
      fetchComparison();
    }, refreshInterval * 1000);

    return () => {
      clearInterval(interval);
    };
  }, [autoRefresh, refreshInterval, fetchComparison]);

  return {
    comparison,
    isLoading,
    error,
    refetch,
    clearCache,
    lastUpdated
  };
};

/**
 * Hook مبسط للحصول على مقارنة واحدة فقط
 */
export const useComparisonIndicator = (
  type: 'todaySales' | 'todayDebts' | 'todayProfits',
  options: UseComparisonIndicatorsOptions = {}
): {
  comparison: ComparisonData | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
} => {
  const { comparison: fullComparison, isLoading, error, refetch } = useComparisonIndicators(options);

  const comparison = fullComparison ? fullComparison[type] : null;

  return {
    comparison,
    isLoading,
    error,
    refetch
  };
};

export default useComparisonIndicators;
