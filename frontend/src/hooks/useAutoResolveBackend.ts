import { useState, useCallback, useRef } from 'react';
import { 
  autoResolveBackendService, 
  AutoResolveProgress, 
  AutoResolveBackendResponse 
} from '../services/autoResolveBackendService';

export interface UseAutoResolveBackendReturn {
  // الحالات
  isProcessing: boolean;
  progress: AutoResolveProgress | null;
  result: AutoResolveBackendResponse | null;
  error: string | null;

  // الدوال
  startAutoResolve: (logIds: number[]) => Promise<void>;
  stopAutoResolve: () => void;
  reset: () => void;
}

export const useAutoResolveBackend = (): UseAutoResolveBackendReturn => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState<AutoResolveProgress | null>(null);
  const [result, setResult] = useState<AutoResolveBackendResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  // مرجع للتحكم في العملية
  const processingRef = useRef(false);

  // دالة بدء الحل التلقائي
  const startAutoResolve = useCallback(async (logIds: number[]) => {
    if (processingRef.current) {
      console.warn('عملية الحل التلقائي قيد التشغيل بالفعل');
      return;
    }

    try {
      // إعادة تعيين الحالات
      setError(null);
      setResult(null);
      setProgress(null);
      setIsProcessing(true);
      processingRef.current = true;

      // تعيين callback للتقدم
      autoResolveBackendService.setProgressCallback((progressData) => {
        setProgress(progressData);
      });

      // بدء العملية
      const processResult = await autoResolveBackendService.autoResolveSystemLogs(logIds);
      
      // حفظ النتيجة
      setResult(processResult);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'حدث خطأ غير متوقع';
      setError(errorMessage);
      console.error('خطأ في الحل التلقائي:', err);
    } finally {
      setIsProcessing(false);
      processingRef.current = false;
    }
  }, []);

  // دالة إيقاف العملية
  const stopAutoResolve = useCallback(() => {
    if (processingRef.current) {
      autoResolveBackendService.stop();
      setError('تم طلب إيقاف العملية - لكن المعالجة تتم في الخلفية');
      setIsProcessing(false);
      processingRef.current = false;
    }
  }, []);

  // دالة إعادة تعيين الحالات
  const reset = useCallback(() => {
    setIsProcessing(false);
    setProgress(null);
    setResult(null);
    setError(null);
    processingRef.current = false;
  }, []);

  return {
    isProcessing,
    progress,
    result,
    error,
    startAutoResolve,
    stopAutoResolve,
    reset
  };
};
