import { useState, useRef, useCallback, useEffect } from 'react';

export interface UseTooltipOptions {
  delay?: number;
  hideDelay?: number;
  disabled?: boolean;
  trigger?: 'hover' | 'click' | 'focus' | 'manual';
  onShow?: () => void;
  onHide?: () => void;
}

export interface UseTooltipReturn {
  isVisible: boolean;
  show: () => void;
  hide: () => void;
  toggle: () => void;
  triggerProps: {
    onMouseEnter?: () => void;
    onMouseLeave?: () => void;
    onClick?: () => void;
    onFocus?: () => void;
    onBlur?: () => void;
    ref: React.RefObject<HTMLElement>;
  };
  tooltipProps: {
    ref: React.RefObject<HTMLDivElement>;
  };
}

/**
 * Hook مخصص لإدارة حالة وسلوك التلميحات
 * يوفر تحكم كامل في عرض وإخفاء التلميحات مع دعم المؤقتات والأحداث
 */
export const useTooltip = (options: UseTooltipOptions = {}): UseTooltipReturn => {
  const {
    delay = 300,
    hideDelay = 100,
    disabled = false,
    trigger = 'hover',
    onShow,
    onHide
  } = options;

  const [isVisible, setIsVisible] = useState(false);
  const showTimeoutRef = useRef<NodeJS.Timeout>();
  const hideTimeoutRef = useRef<NodeJS.Timeout>();
  const triggerRef = useRef<HTMLElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);

  // تنظيف المؤقتات
  const clearTimeouts = useCallback(() => {
    if (showTimeoutRef.current) {
      clearTimeout(showTimeoutRef.current);
      showTimeoutRef.current = undefined;
    }
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
      hideTimeoutRef.current = undefined;
    }
  }, []);

  // إظهار التلميح
  const show = useCallback(() => {
    if (disabled) return;
    
    clearTimeouts();
    showTimeoutRef.current = setTimeout(() => {
      setIsVisible(true);
      onShow?.();
    }, delay);
  }, [disabled, delay, onShow, clearTimeouts]);

  // إخفاء التلميح
  const hide = useCallback(() => {
    clearTimeouts();
    hideTimeoutRef.current = setTimeout(() => {
      setIsVisible(false);
      onHide?.();
    }, hideDelay);
  }, [hideDelay, onHide, clearTimeouts]);

  // تبديل حالة التلميح
  const toggle = useCallback(() => {
    if (isVisible) {
      hide();
    } else {
      show();
    }
  }, [isVisible, show, hide]);

  // معالجات الأحداث
  const handleMouseEnter = useCallback(() => {
    if (trigger === 'hover') show();
  }, [trigger, show]);

  const handleMouseLeave = useCallback(() => {
    if (trigger === 'hover') hide();
  }, [trigger, hide]);

  const handleClick = useCallback(() => {
    if (trigger === 'click') toggle();
  }, [trigger, toggle]);

  const handleFocus = useCallback(() => {
    if (trigger === 'focus') show();
  }, [trigger, show]);

  const handleBlur = useCallback(() => {
    if (trigger === 'focus') hide();
  }, [trigger, hide]);

  // تنظيف المؤقتات عند إلغاء التحميل
  useEffect(() => {
    return () => clearTimeouts();
  }, [clearTimeouts]);

  // خصائص العنصر المحفز
  const triggerProps = {
    ref: triggerRef,
    ...(trigger === 'hover' && {
      onMouseEnter: handleMouseEnter,
      onMouseLeave: handleMouseLeave
    }),
    ...(trigger === 'click' && {
      onClick: handleClick
    }),
    ...(trigger === 'focus' && {
      onFocus: handleFocus,
      onBlur: handleBlur
    })
  };

  // خصائص التلميح
  const tooltipProps = {
    ref: tooltipRef
  };

  return {
    isVisible,
    show,
    hide,
    toggle,
    triggerProps,
    tooltipProps
  };
};

/**
 * Hook مبسط للتلميحات السريعة
 * يوفر واجهة مبسطة للاستخدام السريع
 */
export const useSimpleTooltip = (content: string, options: UseTooltipOptions = {}) => {
  const tooltip = useTooltip(options);
  
  return {
    ...tooltip,
    content,
    // خصائص مبسطة للعنصر المحفز
    bind: tooltip.triggerProps
  };
};

/**
 * Hook للتلميحات التفاعلية
 * يدعم التفاعل مع محتوى التلميح نفسه
 */
export const useInteractiveTooltip = (options: UseTooltipOptions = {}) => {
  const tooltip = useTooltip(options);
  const [isHovering, setIsHovering] = useState(false);

  // معالجات إضافية للتفاعل مع التلميح
  const handleTooltipMouseEnter = useCallback(() => {
    setIsHovering(true);
    tooltip.show();
  }, [tooltip]);

  const handleTooltipMouseLeave = useCallback(() => {
    setIsHovering(false);
    tooltip.hide();
  }, [tooltip]);

  return {
    ...tooltip,
    isHovering,
    tooltipProps: {
      ...tooltip.tooltipProps,
      onMouseEnter: handleTooltipMouseEnter,
      onMouseLeave: handleTooltipMouseLeave
    }
  };
};

export default useTooltip;
