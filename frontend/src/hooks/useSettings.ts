import { useState, useEffect } from 'react';
import apiClient from '../lib/axios';

interface Settings {
  [key: string]: string;
}

export const useSettings = () => {
  const [settings, setSettings] = useState<Settings>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSettings = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Use public settings endpoint that doesn't require authentication
      const response = await apiClient.get('/api/settings/public');
      setSettings(response.data || {});
    } catch (err) {
      console.error('Error fetching settings:', err);
      setError('فشل في تحميل الإعدادات');
      // Set default values in case of error
      setSettings({
        show_cashier_profits: 'true',
        currency_symbol: 'د.ل',
        decimal_places: '2',
        tax_rate: '0',
        tax_included: 'false'
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  const getSetting = (key: string, defaultValue: string = ''): string => {
    return settings[key] || defaultValue;
  };

  const getBooleanSetting = (key: string, defaultValue: boolean = false): boolean => {
    const value = settings[key];
    if (value === undefined || value === null) return defaultValue;
    return value === 'true';
  };

  const getNumberSetting = (key: string, defaultValue: number = 0): number => {
    const value = settings[key];
    if (value === undefined || value === null) return defaultValue;
    const parsed = parseFloat(value.toString());
    return isNaN(parsed) ? defaultValue : parsed;
  };

  return {
    settings,
    isLoading,
    error,
    getSetting,
    getBooleanSetting,
    getNumberSetting,
    refetch: fetchSettings
  };
};

export default useSettings;
