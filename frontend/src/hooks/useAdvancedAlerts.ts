import { useState, useEffect, useCallback } from 'react';
import { advancedAlertService, AdvancedSystemAlert, AlertStats, AlertFilter } from '../services/advancedAlertService';

export interface UseAdvancedAlertsOptions {
  filter?: AlertFilter;
  autoRefresh?: boolean;
  refreshInterval?: number; // بالميلي ثانية
}

export interface UseAdvancedAlertsReturn {
  alerts: AdvancedSystemAlert[];
  stats: AlertStats | null;
  isLoading: boolean;
  error: string | null;
  
  // دوال التحكم
  addAlert: (alert: Omit<AdvancedSystemAlert, 'id' | 'timestamp' | 'fingerprint'>) => string | null;
  removeAlert: (alertId: string) => boolean;
  clearAllAlerts: () => void;
  markAsRead: (alertId: string) => void;
  markAllAsRead: () => void;
  
  // دوال الكتم
  muteSource: (source: string, duration?: number) => void;
  unmuteSource: (source: string) => void;
  muteCategory: (category: string, duration?: number) => void;
  unmuteCategory: (category: string) => void;
  
  // دوال التنبيهات السريعة
  showSuccess: (title: string, message: string) => string | null;
  showError: (title: string, message: string, details?: any) => string | null;
  showWarning: (title: string, message: string) => string | null;
  showInfo: (title: string, message: string) => string | null;
  showCritical: (title: string, message: string, persistent?: boolean) => string | null;
  
  // دوال الفلترة
  getFilteredAlerts: (customFilter?: AlertFilter) => AdvancedSystemAlert[];
  refreshData: () => void;
}

export const useAdvancedAlerts = (options: UseAdvancedAlertsOptions = {}): UseAdvancedAlertsReturn => {
  const {
    filter,
    autoRefresh = false,
    refreshInterval = 30000 // 30 ثانية افتراضي
  } = options;

  const [alerts, setAlerts] = useState<AdvancedSystemAlert[]>([]);
  const [stats, setStats] = useState<AlertStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // تحديث البيانات
  const refreshData = useCallback(() => {
    try {
      setIsLoading(true);
      setError(null);
      
      const newAlerts = advancedAlertService.getAlerts(filter);
      const newStats = advancedAlertService.getStats();
      
      setAlerts(newAlerts);
      setStats(newStats);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'خطأ في تحميل التنبيهات');
    } finally {
      setIsLoading(false);
    }
  }, [filter]);

  // الاستماع للتغييرات
  useEffect(() => {
    const unsubscribe = advancedAlertService.addListener((newAlerts) => {
      try {
        const filteredAlerts = filter ? 
          newAlerts.filter(alert => {
            if (filter.types && !filter.types.includes(alert.type)) return false;
            if (filter.sources && !filter.sources.includes(alert.source)) return false;
            if (filter.categories && alert.category && !filter.categories.includes(alert.category)) return false;
            if (filter.minPriority && alert.priority < filter.minPriority) return false;
            if (filter.maxAge) {
              const cutoff = new Date(Date.now() - filter.maxAge * 60 * 1000);
              if (alert.timestamp < cutoff) return false;
            }
            if (filter.persistent !== undefined && alert.persistent !== filter.persistent) return false;
            return true;
          }) : newAlerts;

        setAlerts(filteredAlerts);
        setStats(advancedAlertService.getStats());
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'خطأ في معالجة التنبيهات');
      }
    });

    // تحميل البيانات الأولية
    refreshData();

    return unsubscribe;
  }, [filter, refreshData]);

  // التحديث التلقائي
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(refreshData, refreshInterval);
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, refreshData]);

  // دوال التحكم الأساسية
  const addAlert = useCallback((alert: Omit<AdvancedSystemAlert, 'id' | 'timestamp' | 'fingerprint'>) => {
    try {
      return advancedAlertService.addAlert(alert);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'خطأ في إضافة التنبيه');
      return null;
    }
  }, []);

  const removeAlert = useCallback((alertId: string) => {
    try {
      return advancedAlertService.removeAlert(alertId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'خطأ في حذف التنبيه');
      return false;
    }
  }, []);

  const clearAllAlerts = useCallback(() => {
    try {
      advancedAlertService.clearAllAlerts();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'خطأ في مسح التنبيهات');
    }
  }, []);

  const markAsRead = useCallback((alertId: string) => {
    try {
      advancedAlertService.markAsRead(alertId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'خطأ في تمييز التنبيه كمقروء');
    }
  }, []);

  const markAllAsRead = useCallback(() => {
    try {
      advancedAlertService.markAllAsRead();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'خطأ في تمييز جميع التنبيهات كمقروءة');
    }
  }, []);

  // دوال الكتم
  const muteSource = useCallback((source: string, duration?: number) => {
    try {
      advancedAlertService.muteSource(source, duration);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'خطأ في كتم المصدر');
    }
  }, []);

  const unmuteSource = useCallback((source: string) => {
    try {
      advancedAlertService.unmuteSource(source);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'خطأ في إلغاء كتم المصدر');
    }
  }, []);

  const muteCategory = useCallback((category: string, duration?: number) => {
    try {
      advancedAlertService.muteCategory(category, duration);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'خطأ في كتم الفئة');
    }
  }, []);

  const unmuteCategory = useCallback((category: string) => {
    try {
      advancedAlertService.unmuteCategory(category);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'خطأ في إلغاء كتم الفئة');
    }
  }, []);

  // دوال التنبيهات السريعة
  const showSuccess = useCallback((title: string, message: string) => {
    return addAlert({
      type: 'success',
      title,
      message,
      source: 'USER_ACTION',
      category: 'feedback',
      priority: 3,
      hideAfter: 3000
    });
  }, [addAlert]);

  const showError = useCallback((title: string, message: string, details?: any) => {
    return addAlert({
      type: 'error',
      title,
      message,
      source: 'APPLICATION',
      category: 'error',
      priority: 7,
      metadata: details ? { details } : undefined
    });
  }, [addAlert]);

  const showWarning = useCallback((title: string, message: string) => {
    return addAlert({
      type: 'warning',
      title,
      message,
      source: 'APPLICATION',
      category: 'warning',
      priority: 5
    });
  }, [addAlert]);

  const showInfo = useCallback((title: string, message: string) => {
    return addAlert({
      type: 'info',
      title,
      message,
      source: 'APPLICATION',
      category: 'info',
      priority: 3
    });
  }, [addAlert]);

  const showCritical = useCallback((title: string, message: string, persistent = true) => {
    return addAlert({
      type: 'critical',
      title,
      message,
      source: 'SYSTEM',
      category: 'critical',
      priority: 10,
      persistent,
      autoHide: !persistent
    });
  }, [addAlert]);

  // دالة الفلترة المخصصة
  const getFilteredAlerts = useCallback((customFilter?: AlertFilter) => {
    try {
      return advancedAlertService.getAlerts(customFilter || filter);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'خطأ في فلترة التنبيهات');
      return [];
    }
  }, [filter]);

  return {
    alerts,
    stats,
    isLoading,
    error,
    
    // دوال التحكم
    addAlert,
    removeAlert,
    clearAllAlerts,
    markAsRead,
    markAllAsRead,
    
    // دوال الكتم
    muteSource,
    unmuteSource,
    muteCategory,
    unmuteCategory,
    
    // دوال التنبيهات السريعة
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showCritical,
    
    // دوال الفلترة
    getFilteredAlerts,
    refreshData
  };
};

// Hook مبسط للتنبيهات السريعة
export const useQuickAlerts = () => {
  const { showSuccess, showError, showWarning, showInfo, showCritical } = useAdvancedAlerts();
  
  return {
    success: showSuccess,
    error: showError,
    warning: showWarning,
    info: showInfo,
    critical: showCritical
  };
};

// Hook للإحصائيات فقط
export const useAlertStats = () => {
  const { stats, isLoading, error, refreshData } = useAdvancedAlerts({
    autoRefresh: true,
    refreshInterval: 10000 // 10 ثواني
  });
  
  return {
    stats,
    isLoading,
    error,
    refresh: refreshData
  };
};

export default useAdvancedAlerts;
