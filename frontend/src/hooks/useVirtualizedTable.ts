/**
 * Hook محسن للجداول الافتراضية للبيانات الكبيرة
 * يدعم virtual scrolling وpagination محسن
 */

import { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { frontendCache, PERFORMANCE_CONFIG } from '../services/performanceOptimizer';

export interface VirtualizedTableConfig {
  itemHeight: number;
  containerHeight: number;
  overscan: number;
  pageSize: number;
  enableCache: boolean;
}

export interface VirtualizedTableState<T> {
  data: T[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  currentPage: number;
  totalPages: number;
  visibleItems: T[];
  startIndex: number;
  endIndex: number;
}

export interface VirtualizedTableActions {
  setPage: (page: number) => void;
  setPageSize: (size: number) => void;
  refresh: () => void;
  scrollToIndex: (index: number) => void;
  scrollToTop: () => void;
}

const DEFAULT_CONFIG: VirtualizedTableConfig = {
  itemHeight: 60,
  containerHeight: 400,
  overscan: 5,
  pageSize: PERFORMANCE_CONFIG.DEFAULT_PAGE_SIZE,
  enableCache: true,
};

/**
 * Hook للجداول الافتراضية المحسنة
 */
export function useVirtualizedTable<T>(
  fetchData: (page: number, pageSize: number) => Promise<{ data: T[]; totalCount: number }>,
  config: Partial<VirtualizedTableConfig> = {},
  dependencies: any[] = []
): [VirtualizedTableState<T>, VirtualizedTableActions] {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const containerRef = useRef<HTMLDivElement>(null);
  const [scrollTop] = useState(0);
  
  const [state, setState] = useState<VirtualizedTableState<T>>({
    data: [],
    loading: false,
    error: null,
    totalCount: 0,
    currentPage: 1,
    totalPages: 1,
    visibleItems: [],
    startIndex: 0,
    endIndex: 0,
  });

  // حساب العناصر المرئية
  const visibleRange = useMemo(() => {
    const { itemHeight, containerHeight, overscan } = finalConfig;
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const endIndex = Math.min(
      state.data.length - 1,
      startIndex + visibleCount + overscan * 2
    );
    
    return { startIndex, endIndex, visibleCount };
  }, [scrollTop, finalConfig, state.data.length]);

  // العناصر المرئية
  const visibleItems = useMemo(() => {
    return state.data.slice(visibleRange.startIndex, visibleRange.endIndex + 1);
  }, [state.data, visibleRange]);

  // مفتاح التخزين المؤقت
  const cacheKey = useMemo(() => {
    return `virtualized-table-${state.currentPage}-${finalConfig.pageSize}-${JSON.stringify(dependencies)}`;
  }, [state.currentPage, finalConfig.pageSize, dependencies]);

  // جلب البيانات
  const loadData = useCallback(async (page: number, pageSize: number) => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      // فحص التخزين المؤقت أولاً
      if (finalConfig.enableCache) {
        const cachedData = frontendCache.get(cacheKey);
        if (cachedData) {
          setState(prev => ({
            ...prev,
            ...cachedData,
            loading: false,
            visibleItems,
            startIndex: visibleRange.startIndex,
            endIndex: visibleRange.endIndex,
          }));
          return;
        }
      }
      
      // جلب البيانات من الخادم
      const result = await fetchData(page, pageSize);
      
      const newState = {
        data: result.data,
        totalCount: result.totalCount,
        currentPage: page,
        totalPages: Math.ceil(result.totalCount / pageSize),
        loading: false,
        error: null,
      };
      
      // حفظ في التخزين المؤقت
      if (finalConfig.enableCache) {
        frontendCache.set(cacheKey, newState);
      }
      
      setState(prev => ({
        ...prev,
        ...newState,
        visibleItems,
        startIndex: visibleRange.startIndex,
        endIndex: visibleRange.endIndex,
      }));
      
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'حدث خطأ في تحميل البيانات',
      }));
    }
  }, [fetchData, finalConfig.enableCache, cacheKey, visibleItems, visibleRange]);

  // تحديث العناصر المرئية عند تغيير النطاق
  useEffect(() => {
    setState(prev => ({
      ...prev,
      visibleItems,
      startIndex: visibleRange.startIndex,
      endIndex: visibleRange.endIndex,
    }));
  }, [visibleItems, visibleRange]);

  // تحميل البيانات عند تغيير الصفحة أو التبعيات
  useEffect(() => {
    loadData(state.currentPage, finalConfig.pageSize);
  }, [state.currentPage, finalConfig.pageSize, ...dependencies]);

  // معالج التمرير - مُعطل مؤقتاً
  // const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
  //   const target = event.target as HTMLDivElement;
  //   setScrollTop(target.scrollTop);
  // }, []);

  // الإجراءات
  const actions: VirtualizedTableActions = {
    setPage: useCallback((page: number) => {
      setState(prev => ({ ...prev, currentPage: page }));
    }, []),
    
    setPageSize: useCallback((size: number) => {
      setState(prev => ({ 
        ...prev, 
        currentPage: 1 // إعادة تعيين إلى الصفحة الأولى
      }));
      finalConfig.pageSize = size;
    }, []),
    
    refresh: useCallback(() => {
      // مسح التخزين المؤقت
      if (finalConfig.enableCache) {
        frontendCache.delete(cacheKey);
      }
      loadData(state.currentPage, finalConfig.pageSize);
    }, [loadData, state.currentPage, finalConfig.pageSize, cacheKey]),
    
    scrollToIndex: useCallback((index: number) => {
      if (containerRef.current) {
        const scrollTop = index * finalConfig.itemHeight;
        containerRef.current.scrollTop = scrollTop;
      }
    }, [finalConfig.itemHeight]),
    
    scrollToTop: useCallback(() => {
      if (containerRef.current) {
        containerRef.current.scrollTop = 0;
      }
    }, []),
  };

  // إرجاع الحالة والإجراءات
  return [
    {
      ...state,
      visibleItems,
      startIndex: visibleRange.startIndex,
      endIndex: visibleRange.endIndex,
    },
    actions
  ];
}

/**
 * Hook مبسط للجداول العادية مع pagination محسن
 */
export function usePaginatedTable<T>(
  fetchData: (page: number, pageSize: number) => Promise<{ data: T[]; totalCount: number }>,
  pageSize: number = PERFORMANCE_CONFIG.DEFAULT_PAGE_SIZE,
  dependencies: any[] = []
) {
  const [state, setState] = useState({
    data: [] as T[],
    loading: false,
    error: null as string | null,
    totalCount: 0,
    currentPage: 1,
    totalPages: 1,
  });

  const cacheKey = useMemo(() => {
    return `paginated-table-${state.currentPage}-${pageSize}-${JSON.stringify(dependencies)}`;
  }, [state.currentPage, pageSize, dependencies]);

  const loadData = useCallback(async (page: number) => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      // فحص التخزين المؤقت
      const cachedData = frontendCache.get(cacheKey);
      if (cachedData) {
        setState(prev => ({ ...prev, ...cachedData, loading: false }));
        return;
      }
      
      const result = await fetchData(page, pageSize);
      
      const newState = {
        data: result.data,
        totalCount: result.totalCount,
        currentPage: page,
        totalPages: Math.ceil(result.totalCount / pageSize),
        loading: false,
        error: null,
      };
      
      // حفظ في التخزين المؤقت
      frontendCache.set(cacheKey, newState);
      setState(prev => ({ ...prev, ...newState }));
      
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'حدث خطأ في تحميل البيانات',
      }));
    }
  }, [fetchData, pageSize, cacheKey]);

  useEffect(() => {
    loadData(state.currentPage);
  }, [state.currentPage, ...dependencies]);

  const actions = {
    setPage: (page: number) => setState(prev => ({ ...prev, currentPage: page })),
    refresh: () => {
      frontendCache.delete(cacheKey);
      loadData(state.currentPage);
    },
    nextPage: () => {
      if (state.currentPage < state.totalPages) {
        setState(prev => ({ ...prev, currentPage: prev.currentPage + 1 }));
      }
    },
    prevPage: () => {
      if (state.currentPage > 1) {
        setState(prev => ({ ...prev, currentPage: prev.currentPage - 1 }));
      }
    },
  };

  return [state, actions] as const;
}
