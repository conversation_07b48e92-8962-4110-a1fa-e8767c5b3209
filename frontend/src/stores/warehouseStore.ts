/**
 * Store إدارة المستودعات
 * يستخدم Zustand لإدارة حالة المستودعات
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { 
  warehouseService, 
  Warehouse, 
  WarehouseCreate, 
  WarehouseUpdate,
  WarehouseCapacityStatus,
  WarehouseSummary
} from '../services/warehouseService';

interface WarehouseState {
  // البيانات
  warehouses: Warehouse[];
  selectedWarehouse: Warehouse | null;
  warehouseSummary: WarehouseSummary | null;
  capacityStatus: Record<number, WarehouseCapacityStatus>;
  
  // حالات التحميل
  loading: boolean;
  creating: boolean;
  updating: boolean;
  deleting: boolean;
  
  // الأخطاء
  error: string | null;
  
  // الإجراءات
  fetchWarehouses: (includeInactive?: boolean) => Promise<void>;
  fetchWarehousesWithInventoryStatus: (includeInactive?: boolean) => Promise<void>;
  fetchWarehouseById: (id: number) => Promise<void>;
  createWarehouse: (data: WarehouseCreate) => Promise<boolean>;
  updateWarehouse: (id: number, data: WarehouseUpdate) => Promise<boolean>;
  deleteWarehouse: (id: number) => Promise<boolean>;
  setMainWarehouse: (id: number) => Promise<boolean>;
  fetchWarehouseCapacity: (id: number) => Promise<void>;
  fetchWarehouseSummary: () => Promise<void>;
  
  // إجراءات مساعدة
  setSelectedWarehouse: (warehouse: Warehouse | null) => void;
  clearError: () => void;
  reset: () => void;
}

export const useWarehouseStore = create<WarehouseState>()(
  devtools(
    (set, get) => ({
      // البيانات الأولية
      warehouses: [],
      selectedWarehouse: null,
      warehouseSummary: null,
      capacityStatus: {},
      
      // حالات التحميل الأولية
      loading: false,
      creating: false,
      updating: false,
      deleting: false,
      
      // الأخطاء الأولية
      error: null,
      
      // جلب جميع المستودعات
      fetchWarehouses: async (includeInactive = false) => {
        set({ loading: true, error: null });
        
        try {
          const response = await warehouseService.getAllWarehouses(includeInactive);
          
          if (response.success && response.warehouses) {
            set({ 
              warehouses: response.warehouses,
              loading: false 
            });
          } else {
            set({ 
              error: response.error || 'خطأ في جلب المستودعات',
              loading: false 
            });
          }
        } catch (error) {
          console.error('خطأ في جلب المستودعات:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            loading: false 
          });
        }
      },

      // جلب المستودعات مع معلومات المخزون
      fetchWarehousesWithInventoryStatus: async (includeInactive: boolean = false) => {
        set({ loading: true, error: null });

        try {
          const warehouses = await warehouseService.getWarehousesWithInventoryStatus(includeInactive);

          set({
            warehouses: warehouses,
            loading: false
          });
        } catch (error: any) {
          console.error('خطأ في جلب المستودعات مع معلومات المخزون:', error);
          set({
            error: error.message || 'خطأ في الاتصال بالخادم',
            loading: false
          });
        }
      },

      // جلب مستودع بالمعرف
      fetchWarehouseById: async (id: number) => {
        set({ loading: true, error: null });
        
        try {
          const response = await warehouseService.getWarehouseById(id);
          
          if (response.success && response.warehouse) {
            set({ 
              selectedWarehouse: response.warehouse,
              loading: false 
            });
          } else {
            set({ 
              error: response.error || 'خطأ في جلب المستودع',
              loading: false 
            });
          }
        } catch (error) {
          console.error('خطأ في جلب المستودع:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            loading: false 
          });
        }
      },
      
      // إنشاء مستودع جديد
      createWarehouse: async (data: WarehouseCreate) => {
        set({ creating: true, error: null });
        
        try {
          const response = await warehouseService.createWarehouse(data);
          
          if (response.success && response.warehouse) {
            const { warehouses } = get();
            set({ 
              warehouses: [...warehouses, response.warehouse],
              creating: false 
            });
            return true;
          } else {
            set({ 
              error: response.error || 'خطأ في إنشاء المستودع',
              creating: false 
            });
            return false;
          }
        } catch (error) {
          console.error('خطأ في إنشاء المستودع:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            creating: false 
          });
          return false;
        }
      },
      
      // تحديث المستودع
      updateWarehouse: async (id: number, data: WarehouseUpdate) => {
        set({ updating: true, error: null });
        
        try {
          const response = await warehouseService.updateWarehouse(id, data);
          
          if (response.success && response.warehouse) {
            const { warehouses, selectedWarehouse } = get();
            const updatedWarehouses = warehouses.map(w => 
              w.id === id ? response.warehouse! : w
            );
            
            set({ 
              warehouses: updatedWarehouses,
              selectedWarehouse: selectedWarehouse?.id === id ? response.warehouse : selectedWarehouse,
              updating: false 
            });
            return true;
          } else {
            set({ 
              error: response.error || 'خطأ في تحديث المستودع',
              updating: false 
            });
            return false;
          }
        } catch (error) {
          console.error('خطأ في تحديث المستودع:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            updating: false 
          });
          return false;
        }
      },
      
      // حذف المستودع
      deleteWarehouse: async (id: number) => {
        set({ deleting: true, error: null });
        
        try {
          const response = await warehouseService.deleteWarehouse(id);
          
          if (response.success) {
            const { warehouses, selectedWarehouse } = get();
            const filteredWarehouses = warehouses.filter(w => w.id !== id);
            
            set({ 
              warehouses: filteredWarehouses,
              selectedWarehouse: selectedWarehouse?.id === id ? null : selectedWarehouse,
              deleting: false 
            });
            return true;
          } else {
            set({ 
              error: response.error || 'خطأ في حذف المستودع',
              deleting: false 
            });
            return false;
          }
        } catch (error) {
          console.error('خطأ في حذف المستودع:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            deleting: false 
          });
          return false;
        }
      },
      
      // تعيين المستودع الرئيسي
      setMainWarehouse: async (id: number) => {
        set({ updating: true, error: null });
        
        try {
          const response = await warehouseService.setMainWarehouse(id);
          
          if (response.success) {
            const { warehouses } = get();
            const updatedWarehouses = warehouses.map(w => ({
              ...w,
              is_main: w.id === id
            }));
            
            set({ 
              warehouses: updatedWarehouses,
              updating: false 
            });
            return true;
          } else {
            set({ 
              error: response.error || 'خطأ في تعيين المستودع الرئيسي',
              updating: false 
            });
            return false;
          }
        } catch (error) {
          console.error('خطأ في تعيين المستودع الرئيسي:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            updating: false 
          });
          return false;
        }
      },
      
      // جلب حالة سعة المستودع
      fetchWarehouseCapacity: async (id: number) => {
        try {
          const response = await warehouseService.getWarehouseCapacity(id);
          
          if (response.success && response.capacity_status) {
            const { capacityStatus } = get();
            set({ 
              capacityStatus: {
                ...capacityStatus,
                [id]: response.capacity_status
              }
            });
          }
        } catch (error) {
          console.error('خطأ في جلب حالة السعة:', error);
        }
      },
      
      // جلب ملخص المستودعات
      fetchWarehouseSummary: async () => {
        try {
          const response = await warehouseService.getWarehousesSummary();
          
          if (response.success && response.summary) {
            set({ warehouseSummary: response.summary });
          }
        } catch (error) {
          console.error('خطأ في جلب ملخص المستودعات:', error);
        }
      },
      
      // تعيين المستودع المحدد
      setSelectedWarehouse: (warehouse: Warehouse | null) => {
        set({ selectedWarehouse: warehouse });
      },
      
      // مسح الأخطاء
      clearError: () => {
        set({ error: null });
      },
      
      // إعادة تعيين الحالة
      reset: () => {
        set({
          warehouses: [],
          selectedWarehouse: null,
          warehouseSummary: null,
          capacityStatus: {},
          loading: false,
          creating: false,
          updating: false,
          deleting: false,
          error: null
        });
      }
    }),
    {
      name: 'warehouse-store',
      partialize: (state: any) => ({
        warehouses: state.warehouses,
        selectedWarehouse: state.selectedWarehouse,
        warehouseSummary: state.warehouseSummary
      })
    }
  )
);

// Selectors مساعدة
export const warehouseSelectors = {
  // الحصول على المستودع الرئيسي
  getMainWarehouse: (state: WarehouseState) =>
    state.warehouses.find(w => w.is_main),

  // الحصول على المستودعات النشطة
  getActiveWarehouses: (state: WarehouseState) =>
    state.warehouses.filter(w => w.is_active),

  // الحصول على المستودعات غير النشطة
  getInactiveWarehouses: (state: WarehouseState) =>
    state.warehouses.filter(w => !w.is_active),

  // البحث في المستودعات
  searchWarehouses: (state: WarehouseState, query: string) =>
    state.warehouses.filter(w =>
      w.name.toLowerCase().includes(query.toLowerCase()) ||
      w.code.toLowerCase().includes(query.toLowerCase())
    ),

  // الحصول على المستودعات مرتبة حسب الاسم
  getWarehousesSorted: (state: WarehouseState) =>
    [...state.warehouses].sort((a, b) => {
      if (a.is_main && !b.is_main) return -1;
      if (!a.is_main && b.is_main) return 1;
      return a.name.localeCompare(b.name, 'ar');
    }),

  // التحقق من وجود مستودع بكود معين
  isCodeExists: (state: WarehouseState, code: string, excludeId?: number) =>
    state.warehouses.some(w =>
      w.code.toLowerCase() === code.toLowerCase() &&
      w.id !== excludeId
    ),

  // فلترة المستودعات حسب حالة المخزون
  filterWarehousesByInventory: (state: WarehouseState, hasInventoryFilter: 'all' | 'with_inventory' | 'without_inventory') => {
    if (hasInventoryFilter === 'all') return state.warehouses;

    return state.warehouses.filter(w => {
      const hasInventory = w.inventory_status?.has_inventory || false;
      return hasInventoryFilter === 'with_inventory' ? hasInventory : !hasInventory;
    });
  },

  // فلترة شاملة للمستودعات
  getFilteredWarehouses: (
    state: WarehouseState,
    searchQuery: string = '',
    showInactive: boolean = false,
    inventoryFilter: 'all' | 'with_inventory' | 'without_inventory' = 'all'
  ) => {
    let filtered = state.warehouses;

    // فلترة حسب الحالة النشطة
    if (!showInactive) {
      filtered = filtered.filter(w => w.is_active);
    }

    // فلترة حسب البحث
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(w =>
        w.name.toLowerCase().includes(query) ||
        w.code.toLowerCase().includes(query)
      );
    }

    // فلترة حسب حالة المخزون
    if (inventoryFilter !== 'all') {
      filtered = filtered.filter(w => {
        const hasInventory = w.inventory_status?.has_inventory || false;
        return inventoryFilter === 'with_inventory' ? hasInventory : !hasInventory;
      });
    }

    // ترتيب النتائج
    return filtered.sort((a, b) => {
      if (a.is_main && !b.is_main) return -1;
      if (!a.is_main && b.is_main) return 1;
      return a.name.localeCompare(b.name, 'ar');
    });
  }
};
