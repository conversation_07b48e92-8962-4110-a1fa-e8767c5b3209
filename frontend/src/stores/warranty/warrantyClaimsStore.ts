import { create } from 'zustand';
import api from '../../lib/axios';
import {
  WarrantyClaim,
  CreateClaimData,
  ProcessClaimData,
  ClaimFilters
} from '../../types/warranty';

interface WarrantyClaimsState {
  claims: WarrantyClaim[];
  loading: boolean;
  error: string | null;
  selectedClaim: WarrantyClaim | null;

  // Actions
  fetchClaims: (filters?: ClaimFilters) => Promise<void>;
  createClaim: (data: CreateClaimData) => Promise<WarrantyClaim>;
  processClaim: (id: number, data: ProcessClaimData) => Promise<WarrantyClaim>;
  approveClaim: (id: number, resolution: string, cost?: number) => Promise<WarrantyClaim>;
  rejectClaim: (id: number, reason: string) => Promise<WarrantyClaim>;
  setSelectedClaim: (claim: WarrantyClaim | null) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

const useWarrantyClaimsStore = create<WarrantyClaimsState>((set, get) => ({
  // Initial state
  claims: [],
  loading: false,
  error: null,
  selectedClaim: null,

  // ==================== إدارة مطالبات الضمان ====================

  fetchClaims: async (filters = {}) => {
    try {
      set({ loading: true, error: null });
      console.log('🔄 جلب مطالبات الضمان...');

      const queryParams = new URLSearchParams();
      
      if (filters.search) queryParams.append('search', filters.search);
      if (filters.status && filters.status !== 'all') {
        queryParams.append('status', filters.status);
      }
      if (filters.claim_type && filters.claim_type !== 'all') {
        queryParams.append('claim_type', filters.claim_type);
      }
      if (filters.date_range) {
        queryParams.append('start_date', filters.date_range.start);
        queryParams.append('end_date', filters.date_range.end);
      }

      const response = await api.get(`/api/warranty-claims/?${queryParams.toString()}`);
      
      console.log('✅ تم جلب مطالبات الضمان:', response.data.length, 'مطالبة');
      set({ claims: response.data, loading: false });
    } catch (error: any) {
      console.error('❌ خطأ في جلب مطالبات الضمان:', error);
      set({
        error: error.response?.data?.detail || 'فشل في جلب مطالبات الضمان',
        loading: false
      });
    }
  },

  createClaim: async (data: CreateClaimData) => {
    try {
      set({ loading: true, error: null });
      console.log('🔄 إنشاء مطالبة ضمان جديدة:', data);

      const response = await api.post('/api/warranty-claims/', data);
      const newClaim = response.data;

      console.log('✅ تم إنشاء مطالبة الضمان:', newClaim);

      // إضافة المطالبة الجديدة للقائمة
      set(state => ({
        claims: [newClaim, ...state.claims],
        loading: false
      }));

      return newClaim;
    } catch (error: any) {
      console.error('❌ خطأ في إنشاء مطالبة الضمان:', error);
      const errorMessage = error.response?.data?.detail || 'فشل في إنشاء مطالبة الضمان';
      set({ error: errorMessage, loading: false });
      throw error;
    }
  },

  processClaim: async (id: number, data: ProcessClaimData) => {
    try {
      set({ loading: true, error: null });
      console.log('🔄 معالجة مطالبة الضمان:', id, data);

      const response = await api.post(`/api/warranty-claims/${id}/process`, data);
      const updatedClaim = response.data;

      console.log('✅ تم معالجة مطالبة الضمان:', updatedClaim);

      // تحديث المطالبة في القائمة مع الاحتفاظ بالبيانات المرجعية
      set(state => ({
        claims: state.claims.map(claim => {
          if (claim.id === id) {
            // دمج البيانات المحدثة مع البيانات الأصلية للاحتفاظ بأسماء المنتج والعميل
            return {
              ...claim,
              ...updatedClaim,
              // الاحتفاظ بالبيانات المرجعية إذا لم تكن موجودة في الاستجابة
              product_name: updatedClaim.product_name || claim.product_name,
              customer_name: updatedClaim.customer_name || claim.customer_name,
              warranty_number: updatedClaim.warranty_number || claim.warranty_number
            };
          }
          return claim;
        }),
        selectedClaim: state.selectedClaim?.id === id ? {
          ...state.selectedClaim,
          ...updatedClaim,
          product_name: updatedClaim.product_name || state.selectedClaim.product_name,
          customer_name: updatedClaim.customer_name || state.selectedClaim.customer_name,
          warranty_number: updatedClaim.warranty_number || state.selectedClaim.warranty_number
        } : state.selectedClaim,
        loading: false
      }));

      return updatedClaim;
    } catch (error: any) {
      console.error('❌ خطأ في معالجة مطالبة الضمان:', error);
      const errorMessage = error.response?.data?.detail || 'فشل في معالجة مطالبة الضمان';
      set({ error: errorMessage, loading: false });
      throw error;
    }
  },

  approveClaim: async (id: number, resolution: string, cost?: number) => {
    try {
      const data: ProcessClaimData = {
        status: 'approved',
        resolution,
        actual_cost: cost
      };

      return await get().processClaim(id, data);
    } catch (error) {
      throw error;
    }
  },

  rejectClaim: async (id: number, reason: string) => {
    try {
      const data: ProcessClaimData = {
        status: 'rejected',
        resolution: reason
      };

      return await get().processClaim(id, data);
    } catch (error) {
      throw error;
    }
  },

  // ==================== إدارة الحالة ====================

  setSelectedClaim: (claim: WarrantyClaim | null) => {
    set({ selectedClaim: claim });
  },

  setError: (error: string | null) => {
    set({ error });
  },

  clearError: () => {
    set({ error: null });
  }
}));

export default useWarrantyClaimsStore;
