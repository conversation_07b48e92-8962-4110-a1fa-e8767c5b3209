import { create } from 'zustand';
import api from '../../lib/axios';
import {
  ProductWarranty,
  CreateWarrantyData,
  ExtendWarrantyData,
  VoidWarrantyData,
  WarrantyFilters
} from '../../types/warranty';

interface ProductWarrantiesState {
  warranties: ProductWarranty[];
  loading: boolean;
  error: string | null;
  selectedWarranty: ProductWarranty | null;
  filters: WarrantyFilters;
  showClaimedOnly: boolean;

  // Actions
  fetchWarranties: (filters?: WarrantyFilters) => Promise<void>;
  createWarranty: (data: CreateWarrantyData) => Promise<ProductWarranty>;
  extendWarranty: (id: number, data: ExtendWarrantyData) => Promise<ProductWarranty>;
  voidWarranty: (id: number, data: VoidWarrantyData) => Promise<ProductWarranty>;
  deleteWarranty: (id: number) => Promise<void>;
  setSelectedWarranty: (warranty: ProductWarranty | null) => void;
  setFilters: (filters: WarrantyFilters) => void;
  setShowClaimedOnly: (showClaimedOnly: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

const useProductWarrantiesStore = create<ProductWarrantiesState>((set) => ({
  // Initial state
  warranties: [],
  loading: false,
  error: null,
  selectedWarranty: null,
  filters: {},
  showClaimedOnly: false,

  // ==================== إدارة ضمانات المنتجات ====================

  fetchWarranties: async (filters = {}) => {
    try {
      set({ loading: true, error: null });
      console.log('🔄 جلب ضمانات المنتجات...');

      const queryParams = new URLSearchParams();
      
      if (filters.search) queryParams.append('search', filters.search);
      if (filters.status && filters.status !== 'all') {
        queryParams.append('status', filters.status);
      }
      if (filters.warranty_type_id) {
        queryParams.append('warranty_type_id', filters.warranty_type_id.toString());
      }
      if (filters.customer_id) {
        queryParams.append('customer_id', filters.customer_id.toString());
      }
      if (filters.date_range) {
        queryParams.append('start_date', filters.date_range.start);
        queryParams.append('end_date', filters.date_range.end);
      }

      const response = await api.get(`/api/warranties/?${queryParams.toString()}`);
      
      console.log('✅ تم جلب ضمانات المنتجات:', response.data.length, 'ضمان');
      set({ warranties: response.data, loading: false, filters });
    } catch (error: any) {
      console.error('❌ خطأ في جلب ضمانات المنتجات:', error);
      set({
        error: error.response?.data?.detail || 'فشل في جلب ضمانات المنتجات',
        loading: false
      });
    }
  },

  createWarranty: async (data: CreateWarrantyData) => {
    try {
      set({ loading: true, error: null });
      console.log('🔄 إنشاء ضمان جديد:', data);

      const response = await api.post('/api/warranties/', data);
      const newWarranty = response.data;

      console.log('✅ تم إنشاء الضمان:', newWarranty);

      // إضافة الضمان الجديد للقائمة
      set(state => ({
        warranties: [newWarranty, ...state.warranties],
        loading: false
      }));

      return newWarranty;
    } catch (error: any) {
      console.error('❌ خطأ في إنشاء الضمان:', error);
      const errorMessage = error.response?.data?.detail || 'فشل في إنشاء الضمان';
      set({ error: errorMessage, loading: false });
      throw error;
    }
  },

  extendWarranty: async (id: number, data: ExtendWarrantyData) => {
    try {
      set({ loading: true, error: null });
      console.log('🔄 تمديد الضمان:', id, data);

      const response = await api.post(`/api/warranties/${id}/extend`, data);
      const updatedWarranty = response.data;

      console.log('✅ تم تمديد الضمان:', updatedWarranty);

      // تحديث الضمان في القائمة
      set(state => ({
        warranties: state.warranties.map(warranty =>
          warranty.id === id ? updatedWarranty : warranty
        ),
        selectedWarranty: state.selectedWarranty?.id === id ? updatedWarranty : state.selectedWarranty,
        loading: false
      }));

      return updatedWarranty;
    } catch (error: any) {
      console.error('❌ خطأ في تمديد الضمان:', error);
      const errorMessage = error.response?.data?.detail || 'فشل في تمديد الضمان';
      set({ error: errorMessage, loading: false });
      throw error;
    }
  },

  voidWarranty: async (id: number, data: VoidWarrantyData) => {
    try {
      set({ loading: true, error: null });
      console.log('🔄 إلغاء الضمان:', id, data);

      // التحقق من صحة البيانات قبل الإرسال
      if (!data.reason || !data.reason.trim()) {
        throw new Error('سبب الإلغاء مطلوب');
      }

      if (data.reason.trim().length < 3) {
        throw new Error('سبب الإلغاء يجب أن يكون 3 أحرف على الأقل');
      }

      const response = await api.post(`/api/warranties/${id}/void`, data);

      // التحقق من نجاح العملية
      if (response.status !== 200 && response.status !== 201) {
        throw new Error(`فشل في إلغاء الضمان: ${response.status}`);
      }

      const responseData = response.data;
      console.log('✅ تم إلغاء الضمان:', responseData);

      // معالجة بسيطة للاستجابة (معطل مؤقتاً معلومات المطالبات)
      const updatedWarranty = responseData as ProductWarranty;

      // تحديث الضمان في القائمة
      set(state => ({
        warranties: state.warranties.map(warranty =>
          warranty.id === id ? updatedWarranty : warranty
        ),
        selectedWarranty: state.selectedWarranty?.id === id ? updatedWarranty : state.selectedWarranty,
        loading: false
      }));

      return updatedWarranty;
    } catch (error: any) {
      console.error('❌ خطأ في إلغاء الضمان:', error);

      let errorMessage = 'فشل في إلغاء الضمان';

      if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.message) {
        errorMessage = error.message;
      }

      set({ error: errorMessage, loading: false });
      throw error;
    }
  },

  deleteWarranty: async (id: number) => {
    try {
      set({ loading: true, error: null });
      console.log('🔄 حذف الضمان:', id);

      await api.delete(`/api/warranties/${id}`);

      console.log('✅ تم حذف الضمان:', id);

      // إزالة الضمان من القائمة
      set(state => ({
        warranties: state.warranties.filter(warranty => warranty.id !== id),
        selectedWarranty: state.selectedWarranty?.id === id ? null : state.selectedWarranty,
        loading: false
      }));

    } catch (error: any) {
      console.error('❌ خطأ في حذف الضمان:', error);
      const errorMessage = error.response?.data?.detail || 'فشل في حذف الضمان';
      set({ error: errorMessage, loading: false });
      throw error;
    }
  },

  // ==================== إدارة الحالة ====================

  setSelectedWarranty: (warranty: ProductWarranty | null) => {
    set({ selectedWarranty: warranty });
  },

  setFilters: (filters: WarrantyFilters) => {
    set({ filters });
  },

  setShowClaimedOnly: (showClaimedOnly: boolean) => {
    set({ showClaimedOnly });
  },

  setError: (error: string | null) => {
    set({ error });
  },

  clearError: () => {
    set({ error: null });
  }
}));

export default useProductWarrantiesStore;
