import { create } from 'zustand';
import api from '../../lib/axios';
import {
  WarrantyType,
  CreateWarrantyTypeData,
  UpdateWarrantyTypeData,
  WarrantyTypeFilters
} from '../../types/warranty';

interface WarrantyTypesState {
  warrantyTypes: WarrantyType[];
  loading: boolean;
  error: string | null;
  selectedType: WarrantyType | null;

  // Actions
  fetchWarrantyTypes: (filters?: WarrantyTypeFilters) => Promise<void>;
  createWarrantyType: (data: CreateWarrantyTypeData) => Promise<WarrantyType>;
  updateWarrantyType: (id: number, data: UpdateWarrantyTypeData) => Promise<WarrantyType>;
  deleteWarrantyType: (id: number) => Promise<void>;
  setSelectedType: (type: WarrantyType | null) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

const useWarrantyTypesStore = create<WarrantyTypesState>((set, get) => ({
  // Initial state
  warrantyTypes: [],
  loading: false,
  error: null,
  selectedType: null,

  // ==================== إدارة أنواع الضمانات ====================

  fetchWarrantyTypes: async (filters = {}) => {
    try {
      set({ loading: true, error: null });
      console.log('🔄 جلب أنواع الضمانات...');

      const queryParams = new URLSearchParams();
      
      if (filters.search) queryParams.append('search', filters.search);
      if (filters.status && filters.status !== 'all') {
        queryParams.append('active_only', (filters.status === 'active').toString());
      }
      if (filters.coverage_type && filters.coverage_type !== 'all') {
        queryParams.append('coverage_type', filters.coverage_type);
      }

      const response = await api.get(`/api/warranty-types/?${queryParams.toString()}`);
      
      console.log('✅ تم جلب أنواع الضمانات:', response.data.length, 'نوع');
      set({ warrantyTypes: response.data, loading: false });
    } catch (error: any) {
      console.error('❌ خطأ في جلب أنواع الضمانات:', error);
      set({
        error: error.response?.data?.detail || 'فشل في جلب أنواع الضمانات',
        loading: false
      });
    }
  },

  createWarrantyType: async (data: CreateWarrantyTypeData) => {
    try {
      set({ loading: true, error: null });
      console.log('🔄 إنشاء نوع ضمان جديد:', data);

      const response = await api.post('/api/warranty-types/', data);
      const newType = response.data;

      console.log('✅ تم إنشاء نوع الضمان:', newType);

      // إضافة النوع الجديد للقائمة
      set(state => ({
        warrantyTypes: [...state.warrantyTypes, newType],
        loading: false
      }));

      return newType;
    } catch (error: any) {
      console.error('❌ خطأ في إنشاء نوع الضمان:', error);
      const errorMessage = error.response?.data?.detail || 'فشل في إنشاء نوع الضمان';
      set({ error: errorMessage, loading: false });
      throw error;
    }
  },

  updateWarrantyType: async (id: number, data: UpdateWarrantyTypeData) => {
    try {
      set({ loading: true, error: null });
      console.log('🔄 تحديث نوع الضمان:', id, data);

      const response = await api.put(`/api/warranty-types/${id}`, data);
      const updatedType = response.data;

      console.log('✅ تم تحديث نوع الضمان:', updatedType);

      // تحديث النوع في القائمة
      set(state => ({
        warrantyTypes: state.warrantyTypes.map(type =>
          type.id === id ? updatedType : type
        ),
        selectedType: state.selectedType?.id === id ? updatedType : state.selectedType,
        loading: false
      }));

      return updatedType;
    } catch (error: any) {
      console.error('❌ خطأ في تحديث نوع الضمان:', error);
      const errorMessage = error.response?.data?.detail || 'فشل في تحديث نوع الضمان';
      set({ error: errorMessage, loading: false });
      throw error;
    }
  },

  deleteWarrantyType: async (id: number) => {
    try {
      set({ loading: true, error: null });
      console.log('🔄 حذف نوع الضمان:', id);

      await api.delete(`/api/warranty-types/${id}`);

      console.log('✅ تم حذف نوع الضمان:', id);

      // إزالة النوع من القائمة
      set(state => ({
        warrantyTypes: state.warrantyTypes.filter(type => type.id !== id),
        selectedType: state.selectedType?.id === id ? null : state.selectedType,
        loading: false
      }));
    } catch (error: any) {
      console.error('❌ خطأ في حذف نوع الضمان:', error);
      const errorMessage = error.response?.data?.detail || 'فشل في حذف نوع الضمان';
      set({ error: errorMessage, loading: false });
      throw error;
    }
  },

  // ==================== إدارة الحالة ====================

  setSelectedType: (type: WarrantyType | null) => {
    set({ selectedType: type });
  },

  setError: (error: string | null) => {
    set({ error });
  },

  clearError: () => {
    set({ error: null });
  }
}));

export default useWarrantyTypesStore;
