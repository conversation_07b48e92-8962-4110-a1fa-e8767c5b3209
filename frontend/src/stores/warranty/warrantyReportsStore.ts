import { create } from 'zustand';
import api from '../../lib/axios';
import {
  WarrantyStats,
  ClaimStatistics,
  ExpiringWarranty,
  DateRange,
  ReportType
} from '../../types/warranty';

interface WarrantyReportsState {
  stats: WarrantyStats | null;
  expiringWarranties: ExpiringWarranty[];
  claimStatistics: ClaimStatistics | null;
  loading: boolean;
  error: string | null;

  // Actions
  fetchWarrantyStats: (dateRange?: DateRange) => Promise<void>;
  fetchExpiringWarranties: (daysAhead: number) => Promise<void>;
  fetchClaimStatistics: (dateRange?: DateRange) => Promise<void>;
  exportWarrantyReport: (type: ReportType, dateRange?: DateRange) => Promise<void>;
  setError: (error: string | null) => void;
  clearError: () => void;
}

const useWarrantyReportsStore = create<WarrantyReportsState>((set, get) => ({
  // Initial state
  stats: null,
  expiringWarranties: [],
  claimStatistics: null,
  loading: false,
  error: null,

  // ==================== إدارة تقارير الضمانات ====================

  fetchWarrantyStats: async (dateRange?: DateRange) => {
    try {
      set({ loading: true, error: null });
      console.log('🔄 جلب إحصائيات الضمانات...');

      const queryParams = new URLSearchParams();
      if (dateRange) {
        queryParams.append('start_date', dateRange.start);
        queryParams.append('end_date', dateRange.end);
      }

      const response = await api.get(`/api/warranty-stats/?${queryParams.toString()}`);
      
      console.log('✅ تم جلب إحصائيات الضمانات:', response.data);
      set({ stats: response.data, loading: false });
    } catch (error: any) {
      console.error('❌ خطأ في جلب إحصائيات الضمانات:', error);
      set({
        error: error.response?.data?.detail || 'فشل في جلب إحصائيات الضمانات',
        loading: false
      });
    }
  },

  fetchExpiringWarranties: async (daysAhead: number = 30) => {
    try {
      set({ loading: true, error: null });
      console.log('🔄 جلب الضمانات المنتهية قريباً...');

      const response = await api.get(`/api/warranties/expiring?days_ahead=${daysAhead}`);
      
      console.log('✅ تم جلب الضمانات المنتهية قريباً:', response.data.length, 'ضمان');
      set({ expiringWarranties: response.data, loading: false });
    } catch (error: any) {
      console.error('❌ خطأ في جلب الضمانات المنتهية قريباً:', error);
      set({
        error: error.response?.data?.detail || 'فشل في جلب الضمانات المنتهية قريباً',
        loading: false
      });
    }
  },

  fetchClaimStatistics: async (dateRange?: DateRange) => {
    try {
      set({ loading: true, error: null });
      console.log('🔄 جلب إحصائيات المطالبات...');

      const queryParams = new URLSearchParams();
      if (dateRange) {
        queryParams.append('start_date', dateRange.start);
        queryParams.append('end_date', dateRange.end);
      }

      const response = await api.get(`/api/warranty-claims/statistics?${queryParams.toString()}`);
      
      console.log('✅ تم جلب إحصائيات المطالبات:', response.data);
      set({ claimStatistics: response.data, loading: false });
    } catch (error: any) {
      console.error('❌ خطأ في جلب إحصائيات المطالبات:', error);
      set({
        error: error.response?.data?.detail || 'فشل في جلب إحصائيات المطالبات',
        loading: false
      });
    }
  },

  exportWarrantyReport: async (type: ReportType, dateRange?: DateRange) => {
    try {
      set({ loading: true, error: null });
      console.log('🔄 تصدير تقرير الضمانات:', type);

      const queryParams = new URLSearchParams();
      queryParams.append('type', type);
      if (dateRange) {
        queryParams.append('start_date', dateRange.start);
        queryParams.append('end_date', dateRange.end);
      }

      const response = await api.get(`/api/warranty-reports/export?${queryParams.toString()}`, {
        responseType: 'blob'
      });

      // إنشاء رابط التحميل
      const blob = new Blob([response.data], { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // تحديد اسم الملف حسب نوع التقرير
      const reportNames = {
        warranties: 'تقرير_الضمانات',
        claims: 'تقرير_المطالبات',
        expiring: 'تقرير_الضمانات_المنتهية',
        statistics: 'تقرير_إحصائيات_الضمانات'
      };
      
      const fileName = `${reportNames[type]}_${new Date().toISOString().split('T')[0]}.xlsx`;
      link.download = fileName;
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      console.log('✅ تم تصدير التقرير:', fileName);
      set({ loading: false });
    } catch (error: any) {
      console.error('❌ خطأ في تصدير التقرير:', error);
      set({
        error: error.response?.data?.detail || 'فشل في تصدير التقرير',
        loading: false
      });
    }
  },

  // ==================== إدارة الحالة ====================

  setError: (error: string | null) => {
    set({ error });
  },

  clearError: () => {
    set({ error: null });
  }
}));

export default useWarrantyReportsStore;
