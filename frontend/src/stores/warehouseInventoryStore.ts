/**
 * Store مخزون المستودعات
 * يستخدم Zustand لإدارة حالة مخزون المستودعات
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { 
  warehouseInventoryService,
  WarehouseInventory,
  ProductInventory,
  InventoryFilters,
  InventoryUpdate,
  LowStockItem,
  StockAvailability
} from '../services/warehouseInventoryService';

interface WarehouseInventoryState {
  // البيانات
  warehouseInventory: Record<number, WarehouseInventory[]>; // warehouse_id -> inventory[]
  productInventory: Record<number, ProductInventory[]>; // product_id -> inventory[]
  lowStockItems: Record<number, LowStockItem[]>; // warehouse_id -> low stock items[]
  stockAvailability: Record<string, StockAvailability>; // "warehouse_id-product_id" -> availability
  
  // حالات التحميل
  loading: boolean;
  updating: boolean;
  checking: boolean;
  
  // الأخطاء
  error: string | null;
  
  // الإجراءات
  fetchProductInventory: (productId: number) => Promise<void>;
  fetchWarehouseInventory: (warehouseId: number, filters?: InventoryFilters) => Promise<void>;
  updateStockLevels: (warehouseId: number, productId: number, data: InventoryUpdate) => Promise<boolean>;
  checkStockAvailability: (warehouseId: number, productId: number, quantity: number) => Promise<boolean>;
  fetchLowStockItems: (warehouseId: number) => Promise<void>;
  reserveStock: (warehouseId: number, productId: number, quantity: number) => Promise<boolean>;
  releaseReservedStock: (warehouseId: number, productId: number, quantity: number) => Promise<boolean>;
  
  // إجراءات مساعدة
  clearError: () => void;
  clearWarehouseInventory: (warehouseId: number) => void;
  clearProductInventory: (productId: number) => void;
  reset: () => void;
}

export const useWarehouseInventoryStore = create<WarehouseInventoryState>()(
  devtools(
    (set, get) => ({
      // البيانات الأولية
      warehouseInventory: {},
      productInventory: {},
      lowStockItems: {},
      stockAvailability: {},
      
      // حالات التحميل الأولية
      loading: false,
      updating: false,
      checking: false,
      
      // الأخطاء الأولية
      error: null,
      
      // جلب مخزون المنتج في جميع المستودعات
      fetchProductInventory: async (productId: number) => {
        set({ loading: true, error: null });
        
        try {
          const response = await warehouseInventoryService.getProductInventory(productId);
          
          if (response.success && response.inventory) {
            const { productInventory } = get();
            set({ 
              productInventory: {
                ...productInventory,
                [productId]: response.inventory
              },
              loading: false 
            });
          } else {
            set({ 
              error: response.error || 'خطأ في جلب مخزون المنتج',
              loading: false 
            });
          }
        } catch (error) {
          console.error('خطأ في جلب مخزون المنتج:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            loading: false 
          });
        }
      },
      
      // جلب مخزون المستودع
      fetchWarehouseInventory: async (warehouseId: number, filters?: InventoryFilters) => {
        set({ loading: true, error: null });
        
        try {
          const response = await warehouseInventoryService.getWarehouseInventory(warehouseId, filters);
          
          if (response.success && response.inventory) {
            const { warehouseInventory } = get();
            set({ 
              warehouseInventory: {
                ...warehouseInventory,
                [warehouseId]: response.inventory
              },
              loading: false 
            });
          } else {
            set({ 
              error: response.error || 'خطأ في جلب مخزون المستودع',
              loading: false 
            });
          }
        } catch (error) {
          console.error('خطأ في جلب مخزون المستودع:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            loading: false 
          });
        }
      },
      
      // تحديث مستويات المخزون
      updateStockLevels: async (warehouseId: number, productId: number, data: InventoryUpdate) => {
        set({ updating: true, error: null });
        
        try {
          const response = await warehouseInventoryService.updateStockLevels(warehouseId, productId, data);
          
          if (response.success && response.inventory) {
            const { warehouseInventory } = get();
            const currentInventory = warehouseInventory[warehouseId] || [];
            
            // تحديث العنصر في المخزون
            const updatedInventory = currentInventory.map(item => 
              item.product_id === productId ? { ...item, ...response.inventory } : item
            );
            
            // إضافة العنصر إذا لم يكن موجوداً
            if (!currentInventory.find(item => item.product_id === productId)) {
              updatedInventory.push(response.inventory!);
            }
            
            set({ 
              warehouseInventory: {
                ...warehouseInventory,
                [warehouseId]: updatedInventory
              },
              updating: false 
            });
            return true;
          } else {
            set({ 
              error: response.error || 'خطأ في تحديث مستويات المخزون',
              updating: false 
            });
            return false;
          }
        } catch (error) {
          console.error('خطأ في تحديث مستويات المخزون:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            updating: false 
          });
          return false;
        }
      },
      
      // التحقق من توفر المخزون
      checkStockAvailability: async (warehouseId: number, productId: number, quantity: number) => {
        set({ checking: true, error: null });
        
        try {
          const response = await warehouseInventoryService.checkStockAvailability(warehouseId, productId, quantity);
          
          if (response.success) {
            const { stockAvailability } = get();
            const key = `${warehouseId}-${productId}`;
            
            set({ 
              stockAvailability: {
                ...stockAvailability,
                [key]: {
                  available: response.available || false,
                  current_quantity: response.current_quantity || 0,
                  reserved_quantity: response.reserved_quantity || 0,
                  available_quantity: response.available_quantity || 0,
                  requested_quantity: quantity,
                  shortage: (response as any).shortage || 0,
                  message: response.message || ''
                }
              },
              checking: false 
            });
            return response.available || false;
          } else {
            set({ 
              error: response.error || 'خطأ في التحقق من توفر المخزون',
              checking: false 
            });
            return false;
          }
        } catch (error) {
          console.error('خطأ في التحقق من توفر المخزون:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            checking: false 
          });
          return false;
        }
      },
      
      // جلب المنتجات قليلة المخزون
      fetchLowStockItems: async (warehouseId: number) => {
        set({ loading: true, error: null });
        
        try {
          const response = await warehouseInventoryService.getLowStockItems(warehouseId);
          
          if (response.success && response.low_stock_items) {
            const { lowStockItems } = get();
            set({ 
              lowStockItems: {
                ...lowStockItems,
                [warehouseId]: response.low_stock_items
              },
              loading: false 
            });
          } else {
            set({ 
              error: response.error || 'خطأ في جلب المنتجات قليلة المخزون',
              loading: false 
            });
          }
        } catch (error) {
          console.error('خطأ في جلب المنتجات قليلة المخزون:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            loading: false 
          });
        }
      },
      
      // حجز المخزون
      reserveStock: async (warehouseId: number, productId: number, quantity: number) => {
        set({ updating: true, error: null });
        
        try {
          const response = await warehouseInventoryService.reserveStock(warehouseId, productId, quantity);
          
          if (response.success) {
            // تحديث المخزون المحلي
            const { warehouseInventory } = get();
            const currentInventory = warehouseInventory[warehouseId] || [];
            
            const updatedInventory = currentInventory.map(item => 
              item.product_id === productId 
                ? { 
                    ...item, 
                    reserved_quantity: response.reserved_quantity || item.reserved_quantity,
                    available_quantity: response.available_quantity || (item.quantity - (response.reserved_quantity || item.reserved_quantity))
                  }
                : item
            );
            
            set({ 
              warehouseInventory: {
                ...warehouseInventory,
                [warehouseId]: updatedInventory
              },
              updating: false 
            });
            return true;
          } else {
            set({ 
              error: response.error || 'خطأ في حجز المخزون',
              updating: false 
            });
            return false;
          }
        } catch (error) {
          console.error('خطأ في حجز المخزون:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            updating: false 
          });
          return false;
        }
      },
      
      // إلغاء حجز المخزون
      releaseReservedStock: async (warehouseId: number, productId: number, quantity: number) => {
        set({ updating: true, error: null });
        
        try {
          const response = await warehouseInventoryService.releaseReservedStock(warehouseId, productId, quantity);
          
          if (response.success) {
            // تحديث المخزون المحلي
            const { warehouseInventory } = get();
            const currentInventory = warehouseInventory[warehouseId] || [];
            
            const updatedInventory = currentInventory.map(item => 
              item.product_id === productId 
                ? { 
                    ...item, 
                    reserved_quantity: response.reserved_quantity || item.reserved_quantity,
                    available_quantity: response.available_quantity || (item.quantity - (response.reserved_quantity || item.reserved_quantity))
                  }
                : item
            );
            
            set({ 
              warehouseInventory: {
                ...warehouseInventory,
                [warehouseId]: updatedInventory
              },
              updating: false 
            });
            return true;
          } else {
            set({ 
              error: response.error || 'خطأ في إلغاء حجز المخزون',
              updating: false 
            });
            return false;
          }
        } catch (error) {
          console.error('خطأ في إلغاء حجز المخزون:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            updating: false 
          });
          return false;
        }
      },
      
      // مسح الأخطاء
      clearError: () => {
        set({ error: null });
      },
      
      // مسح مخزون مستودع معين
      clearWarehouseInventory: (warehouseId: number) => {
        const { warehouseInventory, lowStockItems } = get();
        const newWarehouseInventory = { ...warehouseInventory };
        const newLowStockItems = { ...lowStockItems };
        
        delete newWarehouseInventory[warehouseId];
        delete newLowStockItems[warehouseId];
        
        set({ 
          warehouseInventory: newWarehouseInventory,
          lowStockItems: newLowStockItems
        });
      },
      
      // مسح مخزون منتج معين
      clearProductInventory: (productId: number) => {
        const { productInventory } = get();
        const newProductInventory = { ...productInventory };
        delete newProductInventory[productId];
        
        set({ productInventory: newProductInventory });
      },
      
      // إعادة تعيين الحالة
      reset: () => {
        set({
          warehouseInventory: {},
          productInventory: {},
          lowStockItems: {},
          stockAvailability: {},
          loading: false,
          updating: false,
          checking: false,
          error: null
        });
      }
    }),
    {
      name: 'warehouse-inventory-store'
    }
  )
);

// Selectors مساعدة
export const warehouseInventorySelectors = {
  // الحصول على مخزون مستودع معين
  getWarehouseInventory: (state: WarehouseInventoryState, warehouseId: number) => 
    state.warehouseInventory[warehouseId] || [],
  
  // الحصول على مخزون منتج معين
  getProductInventory: (state: WarehouseInventoryState, productId: number) => 
    state.productInventory[productId] || [],
  
  // الحصول على المنتجات قليلة المخزون
  getLowStockItems: (state: WarehouseInventoryState, warehouseId: number) => 
    state.lowStockItems[warehouseId] || [],
  
  // الحصول على توفر المخزون
  getStockAvailability: (state: WarehouseInventoryState, warehouseId: number, productId: number) => 
    state.stockAvailability[`${warehouseId}-${productId}`],
  
  // البحث في مخزون المستودع
  searchWarehouseInventory: (state: WarehouseInventoryState, warehouseId: number, query: string) => {
    const inventory = state.warehouseInventory[warehouseId] || [];
    return inventory.filter(item => 
      item.product_name?.toLowerCase().includes(query.toLowerCase()) ||
      item.product_barcode?.toLowerCase().includes(query.toLowerCase())
    );
  },
  
  // فلترة المخزون حسب الحالة
  filterInventoryByStatus: (state: WarehouseInventoryState, warehouseId: number, status: string) => {
    const inventory = state.warehouseInventory[warehouseId] || [];
    return inventory.filter(item => item.stock_status === status);
  },
  
  // حساب إجمالي قيمة المخزون
  getTotalInventoryValue: (state: WarehouseInventoryState, warehouseId: number) => {
    const inventory = state.warehouseInventory[warehouseId] || [];
    return inventory.reduce((total, item) => total + (item.item_value || 0), 0);
  }
};
