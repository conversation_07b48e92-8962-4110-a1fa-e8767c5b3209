import { create } from 'zustand';
import api from '../lib/axios';
import { dynamicPeriodsService, AvailablePeriod } from '../services/dynamicPeriodsService';

export interface ProductSalesAnalytics {
  id: number;
  name: string;
  category: string | null;
  total_sold: number;
  total_revenue: number;
  total_profit: number;
  profit_margin: number;
  last_sale_date: string | null;
  days_since_last_sale: number | null;
  average_daily_sales: number;
  stock_turnover_rate: number;
  current_stock: number;
  stock_value: number;
}

export interface BestSellingProduct {
  id: number;
  name: string;
  barcode?: string | null;
  category: string | null;
  total_sold: number;
  total_revenue: number;
  total_profit: number;
  profit_margin: number;
  sales_rank: number;
  percentage_of_total_sales: number;
}

export interface UnsoldProduct {
  id: number;
  name: string;
  barcode?: string | null;
  category: string | null;
  current_stock: number;
  stock_value: number;
  cost_value: number;
  days_in_stock: number;
  potential_loss: number;
  last_updated: string;
}

export interface ProductPerformance {
  id: number;
  name: string;
  barcode?: string | null;
  category: string | null;
  sales_trend: 'increasing' | 'decreasing' | 'stable' | 'no_sales';
  performance_score: number;
  total_sold: number;
  total_revenue: number;
  total_profit: number;
  stock_status: 'healthy' | 'low' | 'overstocked' | 'out_of_stock';
  recommendation: string;
}

export interface ExpectedLoss {
  product_id: number;
  product_name: string;
  product_barcode?: string | null;
  category: string | null;
  current_stock: number;
  days_without_sales: number;
  estimated_loss_amount: number;
  loss_category: 'high_risk' | 'medium_risk' | 'low_risk';
  recommendation: string;
}

export interface InventoryStatus {
  product_id: number;
  product_name: string;
  product_barcode?: string | null;
  category: string | null;
  current_stock: number;
  min_quantity: number;
  max_recommended: number;
  stock_status: string;
  days_of_supply: number;
  reorder_point: number;
  suggested_order_quantity: number;
}

export interface ProductAnalyticsSummary {
  total_products: number;
  active_products: number;
  products_with_sales: number;
  products_without_sales: number;
  total_stock_value: number;
  total_cost_value: number;
  average_profit_margin: number;
  top_performing_category: string | null;
  worst_performing_category: string | null;
  total_potential_losses: number;
}

export interface ProductAnalyticsResponse {
  summary: ProductAnalyticsSummary;
  best_selling: BestSellingProduct[];
  unsold_products: UnsoldProduct[];
  performance_analysis: ProductPerformance[];
  expected_losses: ExpectedLoss[];
  inventory_status: InventoryStatus[];
  period_days: number;
  generated_at: string;
}

interface ProductAnalyticsStore {
  // Data
  analytics: ProductAnalyticsResponse | null;
  bestSellingProducts: BestSellingProduct[];
  unsoldProducts: UnsoldProduct[];
  expectedLosses: ExpectedLoss[];
  inventoryStatus: InventoryStatus[];
  performanceAnalysis: ProductPerformance[];

  // Dynamic Periods
  availablePeriods: AvailablePeriod[];
  periodsLoading: boolean;
  periodsError: string | null;

  // UI State
  loading: boolean;
  initialLoading: boolean;
  loadingSteps: {
    summary: boolean;
    bestSelling: boolean;
    unsold: boolean;
    losses: boolean;
    inventory: boolean;
    performance: boolean;
  };
  error: string | null;
  selectedPeriod: number;
  selectedCategory: string | null;
  selectedRiskLevel: string | null;

  // Actions
  fetchAnalyticsSummary: (periodDays?: number) => Promise<void>;
  fetchAnalyticsSummaryOnly: (periodDays?: number) => Promise<void>;
  fetchBestSellingProducts: (periodDays?: number, limit?: number, category?: string) => Promise<void>;
  fetchUnsoldProducts: (periodDays?: number, category?: string, search?: string, minStock?: number) => Promise<void>;
  fetchExpectedLosses: (periodDays?: number, riskLevel?: string, search?: string) => Promise<void>;
  fetchInventoryStatus: (statusFilter?: string, category?: string, search?: string, periodDays?: number) => Promise<void>;
  fetchPerformanceAnalysis: (periodDays?: number, limit?: number, category?: string, search?: string) => Promise<void>;

  // Dynamic Periods Actions
  fetchAvailablePeriods: (forceRefresh?: boolean) => Promise<void>;
  refreshPeriodsCache: () => Promise<void>;

  // Filters
  setPeriod: (days: number) => void;
  setCategory: (category: string | null) => void;
  setRiskLevel: (level: string | null) => void;
  clearError: () => void;
  clearCache: () => void;
}

const useProductAnalyticsStore = create<ProductAnalyticsStore>((set) => ({
  // Initial state
  analytics: null,
  bestSellingProducts: [],
  unsoldProducts: [],
  expectedLosses: [],
  inventoryStatus: [],
  performanceAnalysis: [],

  // Dynamic Periods State
  availablePeriods: [],
  periodsLoading: false,
  periodsError: null,

  loading: false,
  initialLoading: false,
  loadingSteps: {
    summary: false,
    bestSelling: false,
    unsold: false,
    losses: false,
    inventory: false,
    performance: false
  },
  error: null,
  selectedPeriod: 30,
  selectedCategory: null,
  selectedRiskLevel: null,
  
  // Actions
  fetchAnalyticsSummary: async (periodDays = 30) => {
    try {
      set({ loading: true, error: null });

      console.log('Fetching analytics summary with optimized performance...');
      const startTime = performance.now();

      const response = await api.get('/api/product-analytics/summary', {
        params: { period_days: periodDays },
        timeout: 30000 // 30 second timeout
      });

      const endTime = performance.now();
      console.log(`Analytics summary loaded in ${(endTime - startTime).toFixed(2)}ms`);

      set({
        analytics: response.data,
        loading: false
      });
    } catch (error: any) {
      console.error('Error fetching analytics summary:', error);
      set({
        error: error.response?.data?.detail || 'خطأ في جلب تحليلات المنتجات',
        loading: false
      });
    }
  },

  // Fast summary-only fetch for initial load
  fetchAnalyticsSummaryOnly: async (periodDays = 30) => {
    try {
      set({
        initialLoading: true,
        error: null,
        loadingSteps: {
          summary: true,
          bestSelling: false,
          unsold: false,
          losses: false,
          inventory: false,
          performance: false
        }
      });

      console.log('Fetching analytics summary only for fast initial load...');
      const startTime = performance.now();

      const response = await api.get('/api/product-analytics/summary-only', {
        params: { period_days: periodDays },
        timeout: 15000 // 15 second timeout
      });

      const endTime = performance.now();
      console.log(`Analytics summary only loaded in ${(endTime - startTime).toFixed(2)}ms`);

      // Create a minimal analytics object with just the summary
      set({
        analytics: {
          summary: response.data,
          best_selling: [],
          unsold_products: [],
          performance_analysis: [],
          expected_losses: [],
          inventory_status: [],
          period_days: periodDays,
          generated_at: new Date().toISOString()
        },
        loadingSteps: {
          summary: false,
          bestSelling: false,
          unsold: false,
          losses: false,
          inventory: false,
          performance: false
        }
      });
    } catch (error: any) {
      console.error('Error fetching analytics summary only:', error);
      set({
        error: error.response?.data?.detail || 'خطأ في جلب ملخص التحليلات',
        initialLoading: false,
        loadingSteps: {
          summary: false,
          bestSelling: false,
          unsold: false,
          losses: false,
          inventory: false,
          performance: false
        }
      });
    }
  },
  
  fetchBestSellingProducts: async (periodDays = 30, limit = 20, category) => {
    try {
      set((state) => ({
        loadingSteps: { ...state.loadingSteps, bestSelling: true },
        error: null
      }));

      const params: any = { period_days: periodDays, limit };
      if (category) params.category = category;

      const response = await api.get('/api/product-analytics/best-selling', { params });

      set((state) => ({
        bestSellingProducts: response.data,
        loadingSteps: { ...state.loadingSteps, bestSelling: false }
      }));
    } catch (error: any) {
      set((state) => ({
        error: error.response?.data?.detail || 'خطأ في جلب المنتجات الأكثر مبيعاً',
        loadingSteps: { ...state.loadingSteps, bestSelling: false }
      }));
    }
  },
  
  fetchUnsoldProducts: async (periodDays = 30, category, search, minStock = 0) => {
    try {
      set((state) => ({
        loadingSteps: { ...state.loadingSteps, unsold: true },
        error: null
      }));

      const params: any = { period_days: periodDays, min_stock: minStock };
      if (category) params.category = category;
      if (search) params.search = search;

      const response = await api.get('/api/product-analytics/unsold', { params });

      set((state) => ({
        unsoldProducts: response.data,
        loadingSteps: { ...state.loadingSteps, unsold: false }
      }));
    } catch (error: any) {
      set((state) => ({
        error: error.response?.data?.detail || 'خطأ في جلب المنتجات غير المباعة',
        loadingSteps: { ...state.loadingSteps, unsold: false }
      }));
    }
  },
  
  fetchExpectedLosses: async (periodDays = 30, riskLevel, search) => {
    try {
      set((state) => ({
        loadingSteps: { ...state.loadingSteps, losses: true },
        error: null
      }));

      const params: any = { period_days: periodDays };
      if (riskLevel) params.risk_level = riskLevel;
      if (search) params.search = search;

      console.log(`Fetching expected losses with period: ${periodDays} days`);
      const response = await api.get('/api/product-analytics/expected-losses', { params });

      set((state) => ({
        expectedLosses: response.data,
        loadingSteps: { ...state.loadingSteps, losses: false }
      }));
    } catch (error: any) {
      set((state) => ({
        error: error.response?.data?.detail || 'خطأ في حساب الخسائر المتوقعة',
        loadingSteps: { ...state.loadingSteps, losses: false }
      }));
    }
  },
  
  fetchInventoryStatus: async (statusFilter, category, search, periodDays = 30) => {
    try {
      set((state) => ({
        loadingSteps: { ...state.loadingSteps, inventory: true },
        error: null
      }));

      const params: any = { period_days: periodDays };
      if (statusFilter) params.status_filter = statusFilter;
      if (category) params.category = category;
      if (search) params.search = search;

      console.log(`Fetching inventory status with period: ${periodDays} days`);
      const response = await api.get('/api/product-analytics/inventory-status', { params });

      set((state) => ({
        inventoryStatus: response.data,
        loadingSteps: { ...state.loadingSteps, inventory: false },
        initialLoading: false // Mark complete loading as done
      }));
    } catch (error: any) {
      set((state) => ({
        error: error.response?.data?.detail || 'خطأ في جلب حالة المخزون',
        loadingSteps: { ...state.loadingSteps, inventory: false },
        initialLoading: false
      }));
    }
  },

  fetchPerformanceAnalysis: async (periodDays = 30, limit, category, search) => {
    try {
      set((state) => ({
        loadingSteps: { ...state.loadingSteps, performance: true },
        error: null
      }));

      console.log('Fetching performance analysis for ALL products...');

      const params: any = { period_days: periodDays };
      if (limit !== null) params.limit = limit;
      if (category) params.category = category;
      if (search) params.search = search;

      const response = await api.get('/api/product-analytics/performance-analysis', { params });



      set((state) => ({
        performanceAnalysis: response.data,
        loadingSteps: { ...state.loadingSteps, performance: false }
      }));

      console.log(`Performance analysis loaded: ${response.data.length} products ${limit ? `(limited to ${limit})` : '(ALL PRODUCTS)'}`);
    } catch (error: any) {
      console.error('Error fetching performance analysis:', error);
      set((state) => ({
        error: error.response?.data?.detail || 'خطأ في جلب تحليل الأداء',
        loadingSteps: { ...state.loadingSteps, performance: false }
      }));
    }
  },

  // Filter actions
  setPeriod: (days: number) => {
    set({ selectedPeriod: days });
  },
  
  setCategory: (category: string | null) => {
    set({ selectedCategory: category });
  },
  
  setRiskLevel: (level: string | null) => {
    set({ selectedRiskLevel: level });
  },
  
  clearError: () => {
    set({ error: null });
  },

  // Dynamic Periods Actions
  fetchAvailablePeriods: async (forceRefresh = false) => {
    try {
      set({ periodsLoading: true, periodsError: null });

      const periods = await dynamicPeriodsService.getAvailablePeriods(forceRefresh);

      set({
        availablePeriods: periods,
        periodsLoading: false
      });

      console.log(`تم جلب ${periods.length} فترة متاحة للتحليل`);

    } catch (error: any) {
      console.error('خطأ في جلب الفترات المتاحة:', error);
      set({
        periodsError: 'خطأ في جلب الفترات المتاحة',
        periodsLoading: false
      });
    }
  },

  refreshPeriodsCache: async () => {
    try {
      dynamicPeriodsService.invalidateCache();
      await dynamicPeriodsService.getAvailablePeriods(true);
      console.log('تم تحديث كاش الفترات المتاحة');
    } catch (error) {
      console.error('خطأ في تحديث كاش الفترات:', error);
    }
  },

  clearCache: () => {
    set({
      analytics: null,
      bestSellingProducts: [],
      unsoldProducts: [],
      expectedLosses: [],
      inventoryStatus: [],
      performanceAnalysis: [],
      availablePeriods: [],
      error: null,
      periodsError: null,
      initialLoading: false,
      periodsLoading: false,
      loadingSteps: {
        summary: false,
        bestSelling: false,
        unsold: false,
        losses: false,
        inventory: false,
        performance: false
      }
    });
    // مسح كاش الفترات أيضاً
    dynamicPeriodsService.invalidateCache();
  }
}));

export default useProductAnalyticsStore;
