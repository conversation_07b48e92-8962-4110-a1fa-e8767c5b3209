import { create } from 'zustand';
import api from '../lib/axios';

export interface Category {
  id: number;
  name: string;
  description: string | null;
  image_url: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string | null;
  created_by: number;
  updated_by: number | null;
}

export interface Subcategory {
  id: number;
  name: string;
  description: string | null;
  category_id: number;
  is_active: boolean;
  created_at: string;
  updated_at: string | null;
  created_by: number;
  updated_by: number | null;
  category?: Category;
}

export interface CategoryWithSubcategories extends Category {
  subcategories: Subcategory[];
}

interface CategoryStore {
  categories: Category[];
  subcategories: Subcategory[];
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchCategories: () => Promise<void>;
  fetchCategoriesWithSubcategories: () => Promise<CategoryWithSubcategories[]>;
  fetchSubcategories: (categoryId: number) => Promise<void>;
  createCategory: (category: Omit<Category, 'id' | 'created_at' | 'updated_at' | 'created_by' | 'updated_by'>) => Promise<Category>;
  updateCategory: (id: number, category: Partial<Category>) => Promise<Category>;
  deleteCategory: (id: number) => Promise<void>;
  createSubcategory: (categoryId: number, subcategory: Omit<Subcategory, 'id' | 'category_id' | 'created_at' | 'updated_at' | 'created_by' | 'updated_by'>) => Promise<Subcategory>;
  updateSubcategory: (id: number, subcategory: Partial<Subcategory>) => Promise<Subcategory>;
  deleteSubcategory: (id: number) => Promise<void>;
  setError: (error: string | null) => void;
  clearError: () => void;
}

const useCategoryStore = create<CategoryStore>((set, get) => ({
  categories: [],
  subcategories: [],
  loading: false,
  error: null,

  fetchCategories: async () => {
    try {
      set({ loading: true, error: null });
      const response = await api.get('/api/categories/');
      set({ categories: response.data, loading: false });
    } catch (error: any) {
      console.error('Error fetching categories:', error);
      set({ 
        error: error.response?.data?.detail || 'فشل في جلب الفئات', 
        loading: false 
      });
    }
  },

  fetchCategoriesWithSubcategories: async () => {
    try {
      set({ loading: true, error: null });
      const response = await api.get('/api/categories/with-subcategories');
      set({ loading: false });
      return response.data;
    } catch (error: any) {
      console.error('Error fetching categories with subcategories:', error);
      set({ 
        error: error.response?.data?.detail || 'فشل في جلب الفئات والفئات الفرعية', 
        loading: false 
      });
      return [];
    }
  },

  fetchSubcategories: async (categoryId: number) => {
    try {
      set({ loading: true, error: null });
      const response = await api.get(`/api/categories/${categoryId}/subcategories`);
      set({ subcategories: response.data, loading: false });
    } catch (error: any) {
      console.error('Error fetching subcategories:', error);
      set({ 
        error: error.response?.data?.detail || 'فشل في جلب الفئات الفرعية', 
        loading: false 
      });
    }
  },

  createCategory: async (categoryData) => {
    try {
      set({ loading: true, error: null });
      const response = await api.post('/api/categories/', categoryData);
      const newCategory = response.data;
      
      set(state => ({
        categories: [...state.categories, newCategory],
        loading: false
      }));
      
      return newCategory;
    } catch (error: any) {
      console.error('Error creating category:', error);
      const errorMessage = error.response?.data?.detail || 'فشل في إنشاء الفئة';
      set({ error: errorMessage, loading: false });
      throw new Error(errorMessage);
    }
  },

  updateCategory: async (id, categoryData) => {
    try {
      set({ loading: true, error: null });
      const response = await api.put(`/api/categories/${id}`, categoryData);
      const updatedCategory = response.data;
      
      set(state => ({
        categories: state.categories.map(cat => 
          cat.id === id ? updatedCategory : cat
        ),
        loading: false
      }));
      
      return updatedCategory;
    } catch (error: any) {
      console.error('Error updating category:', error);
      const errorMessage = error.response?.data?.detail || 'فشل في تحديث الفئة';
      set({ error: errorMessage, loading: false });
      throw new Error(errorMessage);
    }
  },

  deleteCategory: async (id) => {
    try {
      set({ loading: true, error: null });
      await api.delete(`/api/categories/${id}`);
      
      set(state => ({
        categories: state.categories.filter(cat => cat.id !== id),
        loading: false
      }));
    } catch (error: any) {
      console.error('Error deleting category:', error);
      const errorMessage = error.response?.data?.detail || 'فشل في حذف الفئة';
      set({ error: errorMessage, loading: false });
      throw new Error(errorMessage);
    }
  },

  createSubcategory: async (categoryId, subcategoryData) => {
    try {
      set({ loading: true, error: null });
      const response = await api.post(`/api/categories/${categoryId}/subcategories`, subcategoryData);
      const newSubcategory = response.data;
      
      set(state => ({
        subcategories: [...state.subcategories, newSubcategory],
        loading: false
      }));
      
      return newSubcategory;
    } catch (error: any) {
      console.error('Error creating subcategory:', error);
      const errorMessage = error.response?.data?.detail || 'فشل في إنشاء الفئة الفرعية';
      set({ error: errorMessage, loading: false });
      throw new Error(errorMessage);
    }
  },

  updateSubcategory: async (id, subcategoryData) => {
    try {
      set({ loading: true, error: null });
      const response = await api.put(`/api/categories/subcategories/${id}`, subcategoryData);
      const updatedSubcategory = response.data;
      
      set(state => ({
        subcategories: state.subcategories.map(sub => 
          sub.id === id ? updatedSubcategory : sub
        ),
        loading: false
      }));
      
      return updatedSubcategory;
    } catch (error: any) {
      console.error('Error updating subcategory:', error);
      const errorMessage = error.response?.data?.detail || 'فشل في تحديث الفئة الفرعية';
      set({ error: errorMessage, loading: false });
      throw new Error(errorMessage);
    }
  },

  deleteSubcategory: async (id) => {
    try {
      set({ loading: true, error: null });
      await api.delete(`/api/categories/subcategories/${id}`);
      
      set(state => ({
        subcategories: state.subcategories.filter(sub => sub.id !== id),
        loading: false
      }));
    } catch (error: any) {
      console.error('Error deleting subcategory:', error);
      const errorMessage = error.response?.data?.detail || 'فشل في حذف الفئة الفرعية';
      set({ error: errorMessage, loading: false });
      throw new Error(errorMessage);
    }
  },

  setError: (error) => set({ error }),
  clearError: () => set({ error: null })
}));

export default useCategoryStore;
