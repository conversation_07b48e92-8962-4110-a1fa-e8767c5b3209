import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import api from '../lib/axios'

interface User {
  id: number
  username: string
  role: 'admin' | 'cashier'
  full_name: string
}

interface AuthState {
  token: string | null
  refreshToken: string | null
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  isInitialized: boolean
  login: (username: string, password: string) => Promise<void>
  logout: () => void
  initialize: () => Promise<void>
  refreshAccessToken: () => Promise<string>
  clearAuth: () => void
}

const STORAGE_KEY = 'auth-storage'

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      token: null,
      refreshToken: null,
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      isInitialized: false,

      initialize: async () => {
        const state = get();

        // If already initialized, don't run again
        if (state.isInitialized) {
          console.log('Auth store already initialized');
          return;
        }

        console.log('Initializing auth store...');

        try {
          // First check the current state (from persist middleware)
          const currentToken = state.token;
          console.log('Current token from state:', !!currentToken);

          if (currentToken) {
            console.log('Verifying existing token...');

            // Verify token by getting user info
            const userResponse = await api.get('/api/auth/me');
            console.log('Token verified, user:', userResponse.data);

            set({
              user: userResponse.data,
              isAuthenticated: true,
              isInitialized: true,
              error: null
            });
          } else {
            console.log('No token found in state');
            set({
              isInitialized: true,
              isAuthenticated: false,
              token: null,
              refreshToken: null,
              user: null,
              error: null
            });
          }
        } catch (error) {
          console.error('Token verification failed:', error);

          // Clear everything on error
          set({
            token: null,
            refreshToken: null,
            user: null,
            isAuthenticated: false,
            isInitialized: true,
            error: null
          });
        }
      },

      refreshAccessToken: async () => {
        const state = get();
        const refreshToken = state.refreshToken;

        if (!refreshToken) {
          throw new Error('No refresh token available');
        }

        try {
          // Use a direct request to avoid interceptors
          const response = await api.post('/api/auth/refresh', null, {
            headers: {
              'Authorization': `Bearer ${refreshToken}`,
              '_refreshing': 'true'  // Add this flag to prevent interceptor from adding access token
            }
          });

          if (!response.data || !response.data.access_token) {
            throw new Error('Invalid refresh response');
          }

          const { access_token, refresh_token } = response.data;

          // Update the tokens in the store
          set({
            token: access_token,
            refreshToken: refresh_token || refreshToken, // Use new refresh token if provided, otherwise keep the old one
            isAuthenticated: true,
            error: null
          });

          // Also update localStorage directly to ensure it's saved immediately
          try {
            const stored = localStorage.getItem(STORAGE_KEY);
            if (stored) {
              const storedData = JSON.parse(stored);
              storedData.state.token = access_token;
              storedData.state.refreshToken = refresh_token || refreshToken;
              localStorage.setItem(STORAGE_KEY, JSON.stringify(storedData));
            }
          } catch (storageError) {
            // Handle storage error silently
          }

          return access_token;
        } catch (error) {
          // If refresh fails, clear auth state
          set({
            token: null,
            refreshToken: null,
            user: null,
            isAuthenticated: false,
            error: 'انتهت الجلسة. يرجى تسجيل الدخول مرة أخرى.'
          });

          // Also clear localStorage
          localStorage.removeItem(STORAGE_KEY);

          throw error;
        }
      },

      login: async (username: string, password: string) => {
        set({ isLoading: true, error: null })
        try {
          console.log('Starting login process for user:', username);

          // Create form data for token endpoint
          const formData = new URLSearchParams()
          formData.append('username', username)
          formData.append('password', password)

          const response = await api.post('/api/auth/token',
            formData.toString(),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            }
          )

          const { access_token, refresh_token } = response.data
          console.log('Login successful, tokens received');

          // Set tokens and authenticated state immediately
          set({
            token: access_token,
            refreshToken: refresh_token,
            isAuthenticated: true,
            error: null,
          });

          console.log('Tokens set in state, fetching user info...');

          // Then get user info
          const userResponse = await api.get('/api/auth/me')
          console.log('User info retrieved:', userResponse.data);

          // Update user info and complete loading
          set({
            user: userResponse.data,
            isLoading: false,
            isInitialized: true,
          });

          console.log('Login process completed successfully');

          // تحديث معلومات الجهاز بعد تسجيل الدخول
          try {
            const { updateDeviceUser } = await import('../services/deviceUpdateService');
            await updateDeviceUser();
            console.log('✅ تم تحديث معلومات الجهاز بعد تسجيل الدخول');
          } catch (deviceError) {
            console.warn('⚠️ فشل في تحديث معلومات الجهاز:', deviceError);
          }
        } catch (error) {
          console.error('Login failed:', error);
          set({
            token: null,
            refreshToken: null,
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: 'اسم المستخدم أو كلمة المرور غير صحيحة',
            isInitialized: true,
          })
        }
      },

      logout: async () => {
        try {
          // ✅ استدعاء endpoint تسجيل الخروج الجديد لتسجيل الحدث
          const token = get().token;
          if (token) {
            try {
              await api.post('/api/auth/logout', {}, {
                headers: {
                  'Authorization': `Bearer ${token}`
                }
              });
              console.log('✅ تم تسجيل حدث تسجيل الخروج بنجاح');
            } catch (logoutError) {
              console.warn('⚠️ فشل في تسجيل حدث تسجيل الخروج:', logoutError);
            }
          }

        } catch (error) {
          console.warn('⚠️ خطأ في عملية تسجيل الخروج:', error);
        }

        // ✅ تنظيف حالة المصادقة أولاً
        set({
          token: null,
          refreshToken: null,
          user: null,
          isAuthenticated: false,
          error: null
        })
        localStorage.removeItem(STORAGE_KEY)
        localStorage.removeItem('token') // Clear old token storage too

        // ✅ تحديث معلومات الجهاز بعد مسح البيانات (مهم للترتيب الصحيح)
        try {
          const { updateDeviceUser } = await import('../services/deviceUpdateService');
          await updateDeviceUser();
          console.log('✅ تم تحديث معلومات الجهاز بعد تسجيل الخروج ومسح البيانات');

          // إرسال حدث إضافي للتأكد من التحديث
          setTimeout(() => {
            window.dispatchEvent(new CustomEvent('deviceUserUpdated', {
              detail: {
                success: true,
                timestamp: new Date().toISOString(),
                user: null,
                action: 'logout'
              }
            }));
          }, 100);

        } catch (deviceError) {
          console.warn('⚠️ فشل في تحديث معلومات الجهاز:', deviceError);
        }
      },

      clearAuth: () => {
        // Force clear all authentication data
        set({
          token: null,
          refreshToken: null,
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
          isInitialized: true
        })

        // Clear all possible storage keys
        localStorage.removeItem(STORAGE_KEY)
        localStorage.removeItem('token')
        localStorage.removeItem('refreshToken')
        localStorage.removeItem('user')

        console.log('All auth data cleared')
      },

      // Initialize the store
      initializeAuth: () => {
        console.log('Initializing auth store...');

        try {
          // Get stored auth data synchronously
          const storedAuth = localStorage.getItem(STORAGE_KEY);
          console.log('Stored auth data exists:', !!storedAuth);

          if (storedAuth) {
            const parsedAuth = JSON.parse(storedAuth);
            console.log('Parsed auth data:', {
              hasToken: !!parsedAuth.state?.token,
              hasRefreshToken: !!parsedAuth.state?.refreshToken,
              hasUser: !!parsedAuth.state?.user,
              isAuthenticated: parsedAuth.state?.isAuthenticated
            });

            if (parsedAuth.state?.token && parsedAuth.state?.user) {
              set({
                token: parsedAuth.state.token,
                refreshToken: parsedAuth.state.refreshToken,
                user: parsedAuth.state.user,
                isAuthenticated: true,
                isInitialized: true,
                isLoading: false,
                error: null
              });
              console.log('Auth restored from storage successfully');
              return;
            }
          }

          // No valid stored auth, set as initialized but not authenticated
          set({
            token: null,
            refreshToken: null,
            user: null,
            isAuthenticated: false,
            isInitialized: true,
            isLoading: false,
            error: null
          });
          console.log('No valid stored auth, initialized as unauthenticated');

        } catch (error) {
          console.error('Error initializing auth:', error);
          set({
            token: null,
            refreshToken: null,
            user: null,
            isAuthenticated: false,
            isInitialized: true,
            isLoading: false,
            error: null
          });
        }
      }
    }),
    {
      name: STORAGE_KEY,
      storage: createJSONStorage(() => localStorage)
    }
  )
)