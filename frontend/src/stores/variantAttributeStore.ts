import { create } from 'zustand';
import { variantAttributeService } from '../services/variantAttributeService';
import {
  VariantAttribute,
  VariantValue,
  CreateVariantAttributeData,
  UpdateVariantAttributeData,
  CreateVariantValueData,
  UpdateVariantValueData,
  AttributeOrderUpdate,
  ValueOrderUpdate,
  AttributeUsageInfo,
  DeleteAttributeResult
} from '../types/variantAttribute';

interface VariantAttributeStore {
  // State
  attributes: VariantAttribute[];
  loading: boolean;
  error: string | null;

  // Actions - إدارة الخصائص
  fetchAttributes: (includeInactive?: boolean) => Promise<void>;
  createAttribute: (data: CreateVariantAttributeData) => Promise<VariantAttribute>;
  updateAttribute: (id: number, data: UpdateVariantAttributeData) => Promise<VariantAttribute>;
  checkAttributeUsage: (id: number) => Promise<AttributeUsageInfo>;
  deleteAttribute: (id: number, forceDelete?: boolean) => Promise<DeleteAttributeResult>;
  reorderAttributes: (orders: AttributeOrderUpdate[]) => Promise<void>;

  // Actions - إدارة قيم الخصائص
  addAttributeValue: (attributeId: number, value: CreateVariantValueData) => Promise<VariantValue>;
  updateAttributeValue: (valueId: number, value: UpdateVariantValueData) => Promise<VariantValue>;
  deleteAttributeValue: (valueId: number) => Promise<void>;
  reorderAttributeValues: (orders: ValueOrderUpdate[]) => Promise<void>;

  // Utility actions
  setError: (error: string | null) => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

const useVariantAttributeStore = create<VariantAttributeStore>((set, get) => ({
  // Initial state
  attributes: [],
  loading: false,
  error: null,

  // ==================== إدارة الخصائص ====================

  fetchAttributes: async (includeInactive = true) => {
    try {
      console.log('🔄 بدء جلب خصائص المتغيرات...');
      set({ loading: true, error: null });
      const attributes = await variantAttributeService.getAllAttributes(includeInactive);
      console.log('✅ تم جلب خصائص المتغيرات:', attributes.length, 'خاصية');
      set({ attributes, loading: false });
    } catch (error: any) {
      console.error('❌ خطأ في جلب خصائص المتغيرات:', error);
      set({
        error: error.message || 'فشل في جلب خصائص المتغيرات',
        loading: false
      });
    }
  },

  createAttribute: async (data: CreateVariantAttributeData) => {
    try {
      set({ loading: true, error: null });
      const newAttribute = await variantAttributeService.createAttribute(data);

      // إعادة تحميل البيانات من الخادم للتأكد من التطابق
      await get().fetchAttributes();

      return newAttribute;
    } catch (error: any) {
      console.error('خطأ في إنشاء الخاصية:', error);
      set({
        error: error.message || 'فشل في إنشاء الخاصية',
        loading: false
      });
      throw error;
    }
  },

  updateAttribute: async (id: number, data: UpdateVariantAttributeData) => {
    try {
      set({ loading: true, error: null });
      const updatedAttribute = await variantAttributeService.updateAttribute(id, data);

      // إعادة تحميل البيانات من الخادم للتأكد من التطابق
      await get().fetchAttributes();

      return updatedAttribute;
    } catch (error: any) {
      console.error(`خطأ في تحديث الخاصية ${id}:`, error);
      set({
        error: error.message || 'فشل في تحديث الخاصية',
        loading: false
      });
      throw error;
    }
  },

  checkAttributeUsage: async (id: number) => {
    try {
      set({ loading: true, error: null });
      const usageInfo = await variantAttributeService.checkAttributeUsage(id);
      set({ loading: false });
      return usageInfo;
    } catch (error: any) {
      console.error(`خطأ في فحص استخدام الخاصية ${id}:`, error);
      set({
        error: error.message || 'فشل في فحص استخدام الخاصية',
        loading: false
      });
      throw error;
    }
  },

  deleteAttribute: async (id: number, forceDelete: boolean = false) => {
    try {
      set({ loading: true, error: null });
      const result = await variantAttributeService.deleteAttribute(id, forceDelete);

      // إعادة تحميل البيانات من الخادم للتأكد من التطابق
      await get().fetchAttributes();

      return result;
    } catch (error: any) {
      console.error(`خطأ في حذف الخاصية ${id}:`, error);
      set({
        error: error.message || 'فشل في حذف الخاصية',
        loading: false
      });
      throw error;
    }
  },

  reorderAttributes: async (orders: AttributeOrderUpdate[]) => {
    try {
      set({ loading: true, error: null });
      await variantAttributeService.reorderAttributes(orders);
      
      // تحديث ترتيب الخصائص محلياً
      set(state => {
        const updatedAttributes = state.attributes.map(attr => {
          const orderUpdate = orders.find(order => order.id === attr.id);
          return orderUpdate ? { ...attr, sort_order: orderUpdate.sort_order } : attr;
        });
        
        return {
          attributes: updatedAttributes.sort((a, b) => a.sort_order - b.sort_order),
          loading: false
        };
      });
    } catch (error: any) {
      console.error('خطأ في إعادة ترتيب الخصائص:', error);
      set({ 
        error: error.message || 'فشل في إعادة ترتيب الخصائص', 
        loading: false 
      });
      throw error;
    }
  },

  // ==================== إدارة قيم الخصائص ====================

  addAttributeValue: async (attributeId: number, value: CreateVariantValueData) => {
    try {
      set({ loading: true, error: null });
      const newValue = await variantAttributeService.addAttributeValue(attributeId, value);

      // إعادة تحميل البيانات من الخادم للتأكد من التطابق
      await get().fetchAttributes();

      return newValue;
    } catch (error: any) {
      console.error(`خطأ في إضافة قيمة للخاصية ${attributeId}:`, error);
      set({
        error: error.message || 'فشل في إضافة القيمة',
        loading: false
      });
      throw error;
    }
  },

  updateAttributeValue: async (valueId: number, value: UpdateVariantValueData) => {
    try {
      set({ loading: true, error: null });
      const updatedValue = await variantAttributeService.updateAttributeValue(valueId, value);

      // إعادة تحميل البيانات من الخادم للتأكد من التطابق
      await get().fetchAttributes();

      return updatedValue;
    } catch (error: any) {
      console.error(`خطأ في تحديث القيمة ${valueId}:`, error);
      set({
        error: error.message || 'فشل في تحديث القيمة',
        loading: false
      });
      throw error;
    }
  },

  deleteAttributeValue: async (valueId: number) => {
    try {
      set({ loading: true, error: null });
      await variantAttributeService.deleteAttributeValue(valueId);

      // إعادة تحميل البيانات من الخادم للتأكد من التطابق
      await get().fetchAttributes();
    } catch (error: any) {
      console.error(`خطأ في حذف القيمة ${valueId}:`, error);
      set({
        error: error.message || 'فشل في حذف القيمة',
        loading: false
      });
      throw error;
    }
  },

  reorderAttributeValues: async (orders: ValueOrderUpdate[]) => {
    try {
      set({ loading: true, error: null });
      await variantAttributeService.reorderAttributeValues(orders);
      
      // تحديث ترتيب القيم محلياً
      set(state => ({
        attributes: state.attributes.map(attr => ({
          ...attr,
          values: attr.values.map(val => {
            const orderUpdate = orders.find(order => order.id === val.id);
            return orderUpdate ? { ...val, sort_order: orderUpdate.sort_order } : val;
          }).sort((a, b) => a.sort_order - b.sort_order)
        })),
        loading: false
      }));
    } catch (error: any) {
      console.error('خطأ في إعادة ترتيب قيم الخصائص:', error);
      set({ 
        error: error.message || 'فشل في إعادة ترتيب قيم الخصائص', 
        loading: false 
      });
      throw error;
    }
  },

  // ==================== Utility actions ====================

  setError: (error: string | null) => {
    set({ error });
  },

  clearError: () => {
    set({ error: null });
  },

  setLoading: (loading: boolean) => {
    set({ loading });
  }
}));

export default useVariantAttributeStore;
