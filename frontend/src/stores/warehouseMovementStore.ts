/**
 * Store حركات المستودعات وطلبات التحويل
 * يستخدم Zustand لإدارة حالة الحركات والتحويلات
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { 
  warehouseMovementService,
  WarehouseMovement,
  MovementCreate,
  MovementFilters,
  StockAdjustment,
  MovementSummary
} from '../services/warehouseMovementService';
import {
  transferRequestService,
  TransferRequest,
  TransferRequestCreate,
  TransferRequestFilters,
  TransferApproval
} from '../services/transferRequestService';

interface WarehouseMovementState {
  // البيانات - الحركات
  warehouseMovements: Record<number, WarehouseMovement[]>; // warehouse_id -> movements[]
  productMovements: Record<number, WarehouseMovement[]>; // product_id -> movements[]
  movementSummary: Record<number, MovementSummary>; // warehouse_id -> summary
  
  // البيانات - طلبات التحويل
  transferRequests: TransferRequest[];
  pendingTransfers: TransferRequest[];
  selectedTransferRequest: TransferRequest | null;
  
  // حالات التحميل
  loading: boolean;
  creating: boolean;
  updating: boolean;
  processing: boolean;
  
  // الأخطاء
  error: string | null;
  
  // إجراءات الحركات
  recordMovement: (data: MovementCreate) => Promise<boolean>;
  fetchWarehouseMovements: (warehouseId: number, filters?: MovementFilters) => Promise<void>;
  fetchProductMovementHistory: (productId: number, filters?: Omit<MovementFilters, 'product_id'>) => Promise<void>;
  processStockAdjustment: (data: StockAdjustment) => Promise<boolean>;
  fetchMovementSummary: (warehouseId: number, dateFrom?: string, dateTo?: string) => Promise<void>;
  
  // إجراءات طلبات التحويل
  createTransferRequest: (data: TransferRequestCreate) => Promise<boolean>;
  fetchPendingTransfers: () => Promise<void>;
  fetchTransferHistory: (filters?: TransferRequestFilters) => Promise<void>;
  approveTransferRequest: (requestId: number, approvalData?: TransferApproval) => Promise<boolean>;
  processTransfer: (requestId: number) => Promise<boolean>;
  completeTransfer: (requestId: number) => Promise<boolean>;
  cancelTransferRequest: (requestId: number, reason: string) => Promise<boolean>;
  
  // إجراءات مساعدة
  setSelectedTransferRequest: (request: TransferRequest | null) => void;
  clearError: () => void;
  clearWarehouseMovements: (warehouseId: number) => void;
  clearProductMovements: (productId: number) => void;
  reset: () => void;
}

export const useWarehouseMovementStore = create<WarehouseMovementState>()(
  devtools(
    (set, get) => ({
      // البيانات الأولية
      warehouseMovements: {},
      productMovements: {},
      movementSummary: {},
      transferRequests: [],
      pendingTransfers: [],
      selectedTransferRequest: null,
      
      // حالات التحميل الأولية
      loading: false,
      creating: false,
      updating: false,
      processing: false,
      
      // الأخطاء الأولية
      error: null,
      
      // تسجيل حركة مستودع
      recordMovement: async (data: MovementCreate) => {
        set({ creating: true, error: null });
        
        try {
          const response = await warehouseMovementService.recordMovement(data);
          
          if (response.success) {
            // تحديث الحركات المحلية إذا كانت محملة
            const { warehouseMovements } = get();
            
            if (data.from_warehouse_id && warehouseMovements[data.from_warehouse_id]) {
              const movements = warehouseMovements[data.from_warehouse_id];
              set({
                warehouseMovements: {
                  ...warehouseMovements,
                  [data.from_warehouse_id]: [response.movement!, ...movements]
                }
              });
            }
            
            if (data.to_warehouse_id && warehouseMovements[data.to_warehouse_id]) {
              const movements = warehouseMovements[data.to_warehouse_id];
              set({
                warehouseMovements: {
                  ...warehouseMovements,
                  [data.to_warehouse_id]: [response.movement!, ...movements]
                }
              });
            }
            
            set({ creating: false });
            return true;
          } else {
            set({ 
              error: response.error || 'خطأ في تسجيل حركة المستودع',
              creating: false 
            });
            return false;
          }
        } catch (error) {
          console.error('خطأ في تسجيل حركة المستودع:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            creating: false 
          });
          return false;
        }
      },
      
      // جلب حركات المستودع
      fetchWarehouseMovements: async (warehouseId: number, filters?: MovementFilters) => {
        set({ loading: true, error: null });
        
        try {
          const response = await warehouseMovementService.getWarehouseMovements(warehouseId, filters);

          if (response.success && response.movements) {
            const { warehouseMovements } = get();
            set({
              warehouseMovements: {
                ...warehouseMovements,
                [warehouseId]: Array.isArray(response.movements)
                  ? response.movements.flat()
                  : []
              },
              loading: false 
            });
          } else {
            set({ 
              error: response.error || 'خطأ في جلب حركات المستودع',
              loading: false 
            });
          }
        } catch (error) {
          console.error('خطأ في جلب حركات المستودع:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            loading: false 
          });
        }
      },
      
      // جلب تاريخ حركات المنتج
      fetchProductMovementHistory: async (productId: number, filters?: Omit<MovementFilters, 'product_id'>) => {
        set({ loading: true, error: null });
        
        try {
          const response = await warehouseMovementService.getProductMovementHistory(productId, filters);

          if (response.success && response.movements) {
            const { productMovements } = get();
            set({
              productMovements: {
                ...productMovements,
                [productId]: Array.isArray(response.movements)
                  ? response.movements.flat()
                  : []
              },
              loading: false 
            });
          } else {
            set({ 
              error: response.error || 'خطأ في جلب تاريخ حركات المنتج',
              loading: false 
            });
          }
        } catch (error) {
          console.error('خطأ في جلب تاريخ حركات المنتج:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            loading: false 
          });
        }
      },
      
      // معالجة تعديل المخزون
      processStockAdjustment: async (data: StockAdjustment) => {
        set({ processing: true, error: null });
        
        try {
          const response = await warehouseMovementService.processStockAdjustment(data);
          
          if (response.success) {
            set({ processing: false });
            return true;
          } else {
            set({ 
              error: response.error || 'خطأ في تعديل المخزون',
              processing: false 
            });
            return false;
          }
        } catch (error) {
          console.error('خطأ في تعديل المخزون:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            processing: false 
          });
          return false;
        }
      },
      
      // جلب ملخص حركات المستودع
      fetchMovementSummary: async (warehouseId: number, dateFrom?: string, dateTo?: string) => {
        try {
          const response = await warehouseMovementService.getMovementSummary(warehouseId, dateFrom, dateTo);
          
          if (response.success && response.summary) {
            const { movementSummary } = get();
            set({ 
              movementSummary: {
                ...movementSummary,
                [warehouseId]: response.summary
              }
            });
          }
        } catch (error) {
          console.error('خطأ في جلب ملخص الحركات:', error);
        }
      },
      
      // إنشاء طلب تحويل جديد
      createTransferRequest: async (data: TransferRequestCreate) => {
        set({ creating: true, error: null });
        
        try {
          const response = await transferRequestService.createTransferRequest(data);
          
          if (response.success && response.transfer_request) {
            const { transferRequests, pendingTransfers } = get();
            const newRequest = response.transfer_request;
            
            set({ 
              transferRequests: [newRequest, ...transferRequests],
              pendingTransfers: [newRequest, ...pendingTransfers],
              creating: false 
            });
            return true;
          } else {
            set({ 
              error: response.error || 'خطأ في إنشاء طلب التحويل',
              creating: false 
            });
            return false;
          }
        } catch (error) {
          console.error('خطأ في إنشاء طلب التحويل:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            creating: false 
          });
          return false;
        }
      },
      
      // جلب طلبات التحويل المعلقة
      fetchPendingTransfers: async () => {
        set({ loading: true, error: null });
        
        try {
          const response = await transferRequestService.getPendingTransfers();
          
          if (response.success && response.pending_transfers) {
            set({
              pendingTransfers: Array.isArray(response.pending_transfers)
                ? response.pending_transfers.flat()
                : [],
              loading: false
            });
          } else {
            set({ 
              error: response.error || 'خطأ في جلب طلبات التحويل المعلقة',
              loading: false 
            });
          }
        } catch (error) {
          console.error('خطأ في جلب طلبات التحويل المعلقة:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            loading: false 
          });
        }
      },
      
      // جلب تاريخ طلبات التحويل
      fetchTransferHistory: async (filters?: TransferRequestFilters) => {
        set({ loading: true, error: null });
        
        try {
          const response = await transferRequestService.getTransferHistory(filters);
          
          if (response.success && response.transfers) {
            set({
              transferRequests: Array.isArray(response.transfers)
                ? response.transfers.flat()
                : [],
              loading: false
            });
          } else {
            set({ 
              error: response.error || 'خطأ في جلب تاريخ طلبات التحويل',
              loading: false 
            });
          }
        } catch (error) {
          console.error('خطأ في جلب تاريخ طلبات التحويل:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            loading: false 
          });
        }
      },
      
      // الموافقة على طلب التحويل
      approveTransferRequest: async (requestId: number, approvalData?: TransferApproval) => {
        set({ updating: true, error: null });
        
        try {
          const response = await transferRequestService.approveTransferRequest(requestId, approvalData);
          
          if (response.success) {
            // تحديث الطلبات المحلية
            const { transferRequests, pendingTransfers } = get();
            
            const updatedTransfers = transferRequests.map(t => 
              t.id === requestId ? { ...t, status: 'APPROVED' as const } : t
            );
            
            const updatedPending = pendingTransfers.filter(t => t.id !== requestId);
            
            set({ 
              transferRequests: updatedTransfers,
              pendingTransfers: updatedPending,
              updating: false 
            });
            return true;
          } else {
            set({ 
              error: response.error || 'خطأ في الموافقة على طلب التحويل',
              updating: false 
            });
            return false;
          }
        } catch (error) {
          console.error('خطأ في الموافقة على طلب التحويل:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            updating: false 
          });
          return false;
        }
      },
      
      // معالجة التحويل
      processTransfer: async (requestId: number) => {
        set({ processing: true, error: null });
        
        try {
          const response = await transferRequestService.processTransfer(requestId);
          
          if (response.success) {
            // تحديث الطلبات المحلية
            const { transferRequests } = get();
            const updatedTransfers = transferRequests.map(t => 
              t.id === requestId ? { ...t, status: 'IN_TRANSIT' as const } : t
            );
            
            set({ 
              transferRequests: updatedTransfers,
              processing: false 
            });
            return true;
          } else {
            set({ 
              error: response.error || 'خطأ في معالجة طلب التحويل',
              processing: false 
            });
            return false;
          }
        } catch (error) {
          console.error('خطأ في معالجة طلب التحويل:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            processing: false 
          });
          return false;
        }
      },
      
      // إكمال التحويل
      completeTransfer: async (requestId: number) => {
        set({ processing: true, error: null });
        
        try {
          const response = await transferRequestService.completeTransfer(requestId);
          
          if (response.success) {
            // تحديث الطلبات المحلية
            const { transferRequests } = get();
            const updatedTransfers = transferRequests.map(t => 
              t.id === requestId ? { ...t, status: 'COMPLETED' as const } : t
            );
            
            set({ 
              transferRequests: updatedTransfers,
              processing: false 
            });
            return true;
          } else {
            set({ 
              error: response.error || 'خطأ في إكمال طلب التحويل',
              processing: false 
            });
            return false;
          }
        } catch (error) {
          console.error('خطأ في إكمال طلب التحويل:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            processing: false 
          });
          return false;
        }
      },
      
      // إلغاء طلب التحويل
      cancelTransferRequest: async (requestId: number, reason: string) => {
        set({ updating: true, error: null });
        
        try {
          const response = await transferRequestService.cancelTransferRequest(requestId, reason);
          
          if (response.success) {
            // تحديث الطلبات المحلية
            const { transferRequests, pendingTransfers } = get();
            
            const updatedTransfers = transferRequests.map(t => 
              t.id === requestId ? { ...t, status: 'CANCELLED' as const } : t
            );
            
            const updatedPending = pendingTransfers.filter(t => t.id !== requestId);
            
            set({ 
              transferRequests: updatedTransfers,
              pendingTransfers: updatedPending,
              updating: false 
            });
            return true;
          } else {
            set({ 
              error: response.error || 'خطأ في إلغاء طلب التحويل',
              updating: false 
            });
            return false;
          }
        } catch (error) {
          console.error('خطأ في إلغاء طلب التحويل:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            updating: false 
          });
          return false;
        }
      },
      
      // تعيين طلب التحويل المحدد
      setSelectedTransferRequest: (request: TransferRequest | null) => {
        set({ selectedTransferRequest: request });
      },
      
      // مسح الأخطاء
      clearError: () => {
        set({ error: null });
      },
      
      // مسح حركات مستودع معين
      clearWarehouseMovements: (warehouseId: number) => {
        const { warehouseMovements, movementSummary } = get();
        const newWarehouseMovements = { ...warehouseMovements };
        const newMovementSummary = { ...movementSummary };
        
        delete newWarehouseMovements[warehouseId];
        delete newMovementSummary[warehouseId];
        
        set({ 
          warehouseMovements: newWarehouseMovements,
          movementSummary: newMovementSummary
        });
      },
      
      // مسح حركات منتج معين
      clearProductMovements: (productId: number) => {
        const { productMovements } = get();
        const newProductMovements = { ...productMovements };
        delete newProductMovements[productId];
        
        set({ productMovements: newProductMovements });
      },
      
      // إعادة تعيين الحالة
      reset: () => {
        set({
          warehouseMovements: {},
          productMovements: {},
          movementSummary: {},
          transferRequests: [],
          pendingTransfers: [],
          selectedTransferRequest: null,
          loading: false,
          creating: false,
          updating: false,
          processing: false,
          error: null
        });
      }
    }),
    {
      name: 'warehouse-movement-store'
    }
  )
);
