import { create } from 'zustand';
import apiClient from '../lib/axios';

// حماية عامة من الاستدعاءات المتكررة
let systemLogsRequestInProgress = false;
let systemHealthRequestInProgress = false;
let lastSystemLogsRequest = 0;
let lastSystemHealthRequest = 0;
const MIN_REQUEST_INTERVAL = 1000; // ثانية واحدة كحد أدنى بين الطلبات (للاختبار)

// متغير للتحكم في عرض مؤشر التحميل (لحل مشكلة التحميل المكرر)
let disableLoadingState = false;
import { getCurrentTripoliDateTime } from '../services/dateTimeService';
import { currentPeriodService } from '../services/currentPeriodService';
import { previousPeriodService } from '../services/previousPeriodService';

// تعريف أنواع البيانات
export interface SalesTrend {
  date: string;
  amount: number;
}

export interface ProductCategorySales {
  name: string;
  value: number;
  count?: number;
}

export interface ProductCategoriesResponse {
  categories: ProductCategorySales[];
  inactive_products: number;
}

export interface InventoryStatus {
  name: string;
  quantity: number;
  total_quantity?: number;
  value: number;
}

export interface TopProduct {
  id: number;
  name: string;
  quantity: number;
  total: number;
}

export interface RecentSale {
  id: number;
  total: number;
  createdAt: string;
  items: number;
  user?: {
    id: number;
    username: string;
    full_name: string;
  };
}

export interface SystemStats {
  totalUsers: number;
  activeUsers: number;
  lastLogin: string;
  systemUptime: string;
  lastBackup: string;
  databaseSize: string;
}

export interface ReportSummary {
  totalSales: number;
  totalItems: number;
  averageSale: number;
  totalRevenue: number;
  todaySales: number;
  productCount: number;
  lowStockCount: number;
}

// Debt Reports Interfaces
export interface DebtSummary {
  totalDebts: number;
  totalAmount: number;
  paidAmount: number;
  remainingAmount: number;
  collectionRate: number;
  averageDebtAge: number;
  overdueDebts: number;
  overdueAmount: number;
  averageDebtAmount: number;
  uniqueDebtors: number;
  maxDebt: number;
  minDebt: number;
}

export interface DebtAging {
  range: string;
  count: number;
  amount: number;
  percentage: number;
}

export interface TopDebtorCustomer {
  id: number;
  name: string;
  totalDebt: number;
  remainingDebt: number;
  debtCount: number;
  oldestDebtDate: string;
  riskLevel: 'low' | 'medium' | 'high';
}

export interface DebtTrend {
  date: string;
  newDebts: number;
  paidDebts: number;
  remainingDebts: number;
  newAmount: number;
  paidAmount: number;
  remainingAmount: number;
}

export interface CollectionEfficiency {
  period: string;
  targetCollection: number;
  actualCollection: number;
  efficiency: number;
  variance: number;
}

export interface SystemLog {
  id: number;
  timestamp: string;
  level: 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL';
  source: 'FRONTEND' | 'BACKEND' | 'DATABASE' | 'SYSTEM';
  message: string;
  details?: string;
  stack_trace?: string;
  user_id?: number;
  session_id?: string;
  resolved: boolean;
  resolution_notes?: string;
  sent_to_support?: boolean;
  support_sent_at?: string;
}

export interface SystemHealth {
  frontend_status: 'HEALTHY' | 'WARNING' | 'ERROR';
  backend_status: 'HEALTHY' | 'WARNING' | 'ERROR';
  database_status: 'HEALTHY' | 'WARNING' | 'ERROR';
  total_errors: number;
  critical_errors: number;
  regular_errors: number;
  warning_errors: number;
  resolved_errors: number;
  unresolved_errors: number;
  last_error_time?: string;
  system_performance: number; // 0-100
}

export type ReportPeriod = 'day' | 'week' | 'month' | 'year';
export type ReportType = 'sales' | 'products' | 'inventory' | 'customers' | 'system' | 'system-actions' | 'system-logs' | 'daily-users' | 'debts';

interface ReportsStore {
  // بيانات التقارير
  salesTrends: SalesTrend[];
  previousPeriodSales: SalesTrend[];
  previousPeriodTotal: number;
  productCategories: ProductCategorySales[];
  inactiveProducts: number;
  inventoryStatus: InventoryStatus[];
  reportSummary: ReportSummary;
  topProducts: TopProduct[];
  recentSales: RecentSale[];
  systemStats: SystemStats;

  // بيانات تقارير المديونية
  debtSummary: DebtSummary;
  debtAging: DebtAging[];
  topDebtors: TopDebtorCustomer[];
  debtTrends: DebtTrend[];
  collectionEfficiency: CollectionEfficiency[];

  // بيانات سجلات النظام
  systemLogs: SystemLog[];
  systemHealth: SystemHealth;

  // حالة التحميل والأخطاء
  isLoading: boolean;
  error: string | null;

  // الفلاتر والإعدادات
  selectedPeriod: ReportPeriod;
  selectedReportType: ReportType;

  // الوظائف
  fetchSalesTrends: (period: ReportPeriod) => Promise<void>;
  fetchPreviousPeriodTotal: (period: ReportPeriod) => Promise<void>;
  fetchProductCategories: () => Promise<void>;
  fetchInventoryStatus: () => Promise<void>;
  fetchSystemStats: () => Promise<void>;
  fetchDashboardStats: () => Promise<void>;

  // وظائف تقارير المديونية
  fetchDebtSummary: () => Promise<void>;
  fetchDebtAging: () => Promise<void>;
  fetchTopDebtors: () => Promise<void>;
  fetchDebtTrends: (period: ReportPeriod) => Promise<void>;
  fetchCollectionEfficiency: (period: ReportPeriod) => Promise<void>;

  // وظائف سجلات النظام
  fetchSystemLogs: (page?: number, limit?: number) => Promise<void>;
  fetchSystemHealth: () => Promise<void>;
  clearSystemLogs: (complete?: boolean) => Promise<void>;
  resolveSystemLog: (logId: number, notes?: string) => Promise<void>;
  sendLogsToSupport: (logs: SystemLog[]) => Promise<void>;
  autoFixSystemIssues: () => Promise<{ attempted: number; fixed: number; details: any[] }>;

  setReportType: (type: ReportType) => void;
  setPeriod: (period: ReportPeriod) => void;
  setLoadingStateControl: (disabled: boolean) => void;
}

// القيم الافتراضية
const initialSummary: ReportSummary = {
  totalSales: 0,
  totalItems: 0,
  averageSale: 0,
  totalRevenue: 0,
  todaySales: 0,
  productCount: 0,
  lowStockCount: 0
};

const initialSystemStats: SystemStats = {
  totalUsers: 0,
  activeUsers: 0,
  lastLogin: 'غير متاح',
  systemUptime: '0 أيام',
  lastBackup: 'لم يتم النسخ الاحتياطي بعد',
  databaseSize: '0 MB'
};

// القيم الافتراضية لتقارير المديونية
const initialDebtSummary: DebtSummary = {
  totalDebts: 0,
  totalAmount: 0,
  paidAmount: 0,
  remainingAmount: 0,
  collectionRate: 0,
  averageDebtAge: 0,
  overdueDebts: 0,
  overdueAmount: 0,
  averageDebtAmount: 0,
  uniqueDebtors: 0,
  maxDebt: 0,
  minDebt: 0
};

// القيم الافتراضية لسجلات النظام
const initialSystemHealth: SystemHealth = {
  frontend_status: 'HEALTHY',
  backend_status: 'HEALTHY',
  database_status: 'HEALTHY',
  total_errors: 0,
  critical_errors: 0,
  regular_errors: 0,
  warning_errors: 0,
  resolved_errors: 0,
  unresolved_errors: 0,
  system_performance: 100
};

// إنشاء متجر البيانات
export const useReportsStore = create<ReportsStore>((set, get) => ({
  // البيانات الافتراضية
  salesTrends: [],
  previousPeriodSales: [],
  previousPeriodTotal: 0,
  productCategories: [],
  inactiveProducts: 0,
  inventoryStatus: [],
  reportSummary: initialSummary,
  topProducts: [],
  recentSales: [],
  systemStats: initialSystemStats,

  // بيانات تقارير المديونية الافتراضية
  debtSummary: initialDebtSummary,
  debtAging: [],
  topDebtors: [],
  debtTrends: [],
  collectionEfficiency: [],

  // بيانات سجلات النظام الافتراضية
  systemLogs: [],
  systemHealth: initialSystemHealth,

  // حالة التحميل والأخطاء
  isLoading: false,
  error: null,

  // الفلاتر والإعدادات الافتراضية
  selectedPeriod: 'day',
  selectedReportType: 'sales',

  // وظيفة جلب بيانات اتجاهات المبيعات باستخدام الخدمات المنفصلة
  fetchSalesTrends: async (period: ReportPeriod) => {
    try {
      set({ isLoading: true, error: null });

      console.log(`جلب بيانات المبيعات للفترة: ${period} باستخدام الخدمات المنفصلة`);

      // جلب بيانات الفترة الحالية والسابقة بشكل متوازي باستخدام الخدمات المنفصلة
      const [currentData, previousData] = await Promise.all([
        currentPeriodService.getSalesByPeriod(period),
        previousPeriodService.getSalesByPeriod(period)
      ]);

      console.log('تم جلب البيانات من الخدمات المنفصلة بنجاح');
      console.log(`بيانات الفترة الحالية (${period}):`, currentData);
      console.log(`بيانات الفترة السابقة (${period}):`, previousData);

      // التحقق من صحة البيانات وتنسيقها
      const normalizedCurrentData = currentPeriodService.formatDataForChart(currentData);
      const normalizedPreviousData = previousPeriodService.formatDataForChart(previousData);

      console.log(`تم تنسيق ${normalizedCurrentData.length} عنصر للفترة الحالية`);
      console.log(`تم تنسيق ${normalizedPreviousData.length} عنصر للفترة السابقة`);

      // حساب ملخص التقرير
      const totalSales = normalizedCurrentData.length;
      const totalAmount = normalizedCurrentData.reduce((sum: number, item: SalesTrend) => sum + item.amount, 0);
      const averageSale = totalSales > 0 ? totalAmount / totalSales : 0;

      // الحصول على بيانات لوحة المعلومات الحالية
      const currentSummary = get().reportSummary;

      set({
        salesTrends: normalizedCurrentData,
        previousPeriodSales: normalizedPreviousData,
        isLoading: false,
        selectedPeriod: period,
        reportSummary: {
          ...currentSummary,
          totalSales,
          totalItems: totalSales,
          averageSale,
          totalRevenue: totalAmount
        }
      });

    } catch (error) {
      console.error('خطأ في جلب بيانات المبيعات باستخدام الخدمات المنفصلة:', error);

      // في حالة فشل جلب البيانات، استخدم الخدمات لإنشاء بيانات فارغة
      console.log('محاولة إنشاء بيانات فارغة باستخدام الخدمات...');

      try {
        // محاولة الحصول على بيانات فارغة من الخدمات
        const emptyCurrentData = await currentPeriodService.getSalesByPeriod(period).catch(() => []);
        const emptyPreviousData = await previousPeriodService.getSalesByPeriod(period).catch(() => []);

        // إذا فشلت الخدمات، أنشئ بيانات فارغة يدوياً
        let finalCurrentData = emptyCurrentData;
        let finalPreviousData = emptyPreviousData;

        if (emptyCurrentData.length === 0 || emptyPreviousData.length === 0) {
          console.log('إنشاء بيانات فارغة يدوياً للفترة:', period);

          if (period === 'day') {
            // 24 ساعة بقيم صفر
            finalCurrentData = Array.from({ length: 24 }, (_, hour) => ({
              date: `${hour.toString().padStart(2, '0')}:00`,
              amount: 0
            }));
            finalPreviousData = [...finalCurrentData];
          } else if (period === 'week') {
            // 7 أيام بقيم صفر
            finalCurrentData = Array.from({ length: 7 }, (_, day) => {
              const date = new Date();
              date.setDate(date.getDate() - (6 - day));
              return {
                date: date.toISOString().split('T')[0],
                amount: 0
              };
            });
            finalPreviousData = [...finalCurrentData];
          } else if (period === 'month') {
            // 30 يوم بقيم صفر
            finalCurrentData = Array.from({ length: 30 }, (_, day) => {
              const date = new Date();
              date.setDate(date.getDate() - (29 - day));
              return {
                date: date.toISOString().split('T')[0],
                amount: 0
              };
            });
            finalPreviousData = [...finalCurrentData];
          } else if (period === 'year') {
            // 12 شهر بقيم صفر
            finalCurrentData = Array.from({ length: 12 }, (_, month) => {
              const date = new Date();
              date.setMonth(date.getMonth() - (11 - month));
              return {
                date: `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`,
                amount: 0
              };
            });
            finalPreviousData = [...finalCurrentData];
          }
        }

        set({
          salesTrends: finalCurrentData,
          previousPeriodSales: finalPreviousData,
          isLoading: false,
          selectedPeriod: period,
          reportSummary: {
            totalSales: 0,
            totalItems: 0,
            averageSale: 0,
            totalRevenue: 0,
            todaySales: 0,
            productCount: 0,
            lowStockCount: 0
          },
          error: 'فشل في جلب البيانات من الخادم - تم عرض بيانات فارغة'
        });

      } catch (fallbackError) {
        console.error('خطأ في إنشاء البيانات الفارغة:', fallbackError);

        // في حالة فشل كل شيء، استخدم بيانات فارغة أساسية
        set({
          salesTrends: [],
          previousPeriodSales: [],
          isLoading: false,
          selectedPeriod: period,
          reportSummary: {
            totalSales: 0,
            totalItems: 0,
            averageSale: 0,
            totalRevenue: 0,
            todaySales: 0,
            productCount: 0,
            lowStockCount: 0
          },
          error: 'خطأ شديد في جلب البيانات'
        });
      }
    }
  },

  // وظيفة جلب إجمالي الفترة السابقة باستخدام الخدمة المنفصلة
  fetchPreviousPeriodTotal: async (period: ReportPeriod) => {
    try {
      console.log(`جلب إجمالي الفترة السابقة للفترة: ${period} باستخدام الخدمة المنفصلة`);

      // استخدام خدمة الفترة السابقة لجلب الإجمالي
      const previousTotal = await previousPeriodService.getPreviousPeriodTotal(period);

      console.log(`إجمالي الفترة السابقة للفترة ${period}:`, {
        previousTotal,
        period,
        timestamp: new Date().toISOString(),
        storeUpdate: 'updating previousPeriodTotal'
      });

      set({
        previousPeriodTotal: previousTotal
      });

    } catch (error) {
      console.error('خطأ في جلب إجمالي الفترة السابقة:', error);

      // في حالة الخطأ، استخدم قيمة 0
      set({
        previousPeriodTotal: 0
      });
    }
  },

  // وظيفة جلب بيانات فئات المنتجات
  fetchProductCategories: async () => {
    try {
      set({ isLoading: true, error: null });

      // استخدام نقطة نهاية API الجديدة لفئات المنتجات
      const response = await apiClient.get('/api/dashboard/product-categories');

      set({
        productCategories: response.data.categories || response.data,
        inactiveProducts: response.data.inactive_products || 0,
        isLoading: false
      });

    } catch (error) {
      console.error('Error fetching product categories:', error);

      // في حالة عدم وجود بيانات، استخدم بيانات فارغة
      set({
        productCategories: [],
        isLoading: false,
        error: 'فشل في جلب بيانات فئات المنتجات'
      });
    }
  },

  // وظيفة جلب بيانات حالة المخزون
  fetchInventoryStatus: async () => {
    try {
      set({ isLoading: true, error: null });

      // استخدام نقطة نهاية API الجديدة لحالة المخزون
      const response = await apiClient.get('/api/dashboard/inventory-status');

      console.log('Fetched inventory status:', response.data);

      set({
        inventoryStatus: response.data,
        isLoading: false
      });

    } catch (error) {
      console.error('Error fetching inventory status:', error);

      // في حالة عدم وجود بيانات، استخدم بيانات فارغة
      set({
        inventoryStatus: [],
        isLoading: false,
        error: 'فشل في جلب بيانات حالة المخزون'
      });
    }
  },

  // وظيفة جلب بيانات النظام
  fetchSystemStats: async () => {
    try {
      set({ isLoading: true, error: null });

      // استخدام نقطة نهاية API الجديدة لبيانات النظام
      const response = await apiClient.get('/api/dashboard/system-stats');

      set({
        systemStats: response.data,
        isLoading: false
      });

    } catch (error) {
      console.error('Error fetching system stats:', error);

      // في حالة عدم وجود بيانات، استخدم بيانات فارغة
      const mockData = {
        totalUsers: 0,
        activeUsers: 0,
        lastLogin: 'غير متاح',
        systemUptime: 'غير متاح',
        lastBackup: 'غير متاح',
        databaseSize: '0 MB'
      };

      set({
        systemStats: mockData,
        isLoading: false,
        error: null
      });
    }
  },

  // وظيفة جلب بيانات لوحة المعلومات
  fetchDashboardStats: async () => {
    try {
      if (!disableLoadingState) {
        set({ isLoading: true, error: null });
      }

      // استخدام نقطة نهاية API لبيانات لوحة المعلومات
      const response = await apiClient.get('/api/dashboard/stats');
      const data = response.data;

      // تحديث ملخص التقرير
      const currentSummary = get().reportSummary;

      set({
        reportSummary: {
          ...currentSummary,
          totalRevenue: data.totalRevenue || 0,
          todaySales: data.todaySales || 0,
          productCount: data.productCount || 0,
          lowStockCount: data.lowStockCount || 0
        },
        topProducts: data.topProducts || [],
        recentSales: data.recentSales || [],
        ...(disableLoadingState ? {} : { isLoading: false })
      });

    } catch (error) {
      console.error('Error fetching dashboard stats:', error);

      // في حالة عدم وجود نقطة نهاية API، استخدم بيانات وهمية
      const mockTopProducts = [
        { id: 1, name: 'هاتف ذكي', quantity: 15, total: 7500 },
        { id: 2, name: 'لابتوب', quantity: 8, total: 12000 },
        { id: 3, name: 'سماعات لاسلكية', quantity: 25, total: 2500 },
        { id: 4, name: 'شاحن متنقل', quantity: 30, total: 1800 },
        { id: 5, name: 'ساعة ذكية', quantity: 12, total: 3600 }
      ];

      const mockRecentSales = [
        { id: 101, total: 1250, createdAt: getCurrentTripoliDateTime().toISOString(), items: 3 },
        { id: 102, total: 850, createdAt: getCurrentTripoliDateTime().toISOString(), items: 2 },
        { id: 103, total: 2100, createdAt: getCurrentTripoliDateTime().toISOString(), items: 5 },
        { id: 104, total: 450, createdAt: getCurrentTripoliDateTime().toISOString(), items: 1 },
        { id: 105, total: 1800, createdAt: getCurrentTripoliDateTime().toISOString(), items: 4 }
      ];

      set({
        topProducts: mockTopProducts,
        recentSales: mockRecentSales,
        isLoading: false,
        error: null
      });
    }
  },

  // وظائف تقارير المديونية
  fetchDebtSummary: async () => {
    try {
      if (!disableLoadingState) {
        set({ isLoading: true, error: null });
      }

      const response = await apiClient.get('/api/debts/reports/summary');

      set({
        debtSummary: response.data,
        ...(disableLoadingState ? {} : { isLoading: false })
      });
    } catch (error) {
      console.error('Error fetching debt summary:', error);
      set({
        debtSummary: initialDebtSummary,
        ...(disableLoadingState ? {} : { isLoading: false }),
        error: 'فشل في جلب ملخص المديونية'
      });
    }
  },



  fetchDebtAging: async () => {
    try {
      if (!disableLoadingState) {
        set({ isLoading: true, error: null });
      }

      const response = await apiClient.get('/api/debts/reports/aging');

      set({
        debtAging: response.data,
        ...(disableLoadingState ? {} : { isLoading: false })
      });
    } catch (error) {
      console.error('Error fetching debt aging:', error);
      set({
        debtAging: [],
        ...(disableLoadingState ? {} : { isLoading: false }),
        error: 'فشل في جلب تقرير أعمار الديون'
      });
    }
  },

  fetchTopDebtors: async () => {
    try {
      if (!disableLoadingState) {
        set({ isLoading: true, error: null });
      }

      const response = await apiClient.get('/api/debts/reports/top-debtors');

      set({
        topDebtors: response.data,
        ...(disableLoadingState ? {} : { isLoading: false })
      });
    } catch (error) {
      console.error('Error fetching top debtors:', error);
      set({
        topDebtors: [],
        ...(disableLoadingState ? {} : { isLoading: false }),
        error: 'فشل في جلب أكبر المدينين'
      });
    }
  },

  fetchDebtTrends: async (period: ReportPeriod) => {
    try {
      if (!disableLoadingState) {
        set({ isLoading: true, error: null });
      }

      const response = await apiClient.get(`/api/debts/reports/trends?period=${period}`);

      set({
        debtTrends: response.data,
        ...(disableLoadingState ? {} : { isLoading: false })
      });
    } catch (error) {
      console.error('Error fetching debt trends:', error);
      set({
        debtTrends: [],
        ...(disableLoadingState ? {} : { isLoading: false }),
        error: 'فشل في جلب اتجاهات المديونية'
      });
    }
  },

  fetchCollectionEfficiency: async (period: ReportPeriod) => {
    try {
      if (!disableLoadingState) {
        set({ isLoading: true, error: null });
      }

      const response = await apiClient.get(`/api/debts/reports/collection-efficiency?period=${period}`);

      set({
        collectionEfficiency: response.data,
        ...(disableLoadingState ? {} : { isLoading: false })
      });
    } catch (error) {
      console.error('Error fetching collection efficiency:', error);
      set({
        collectionEfficiency: [],
        ...(disableLoadingState ? {} : { isLoading: false }),
        error: 'فشل في جلب كفاءة التحصيل'
      });
    }
  },

  // وظائف سجلات النظام
  fetchSystemLogs: async (page: number = 1, limit: number = 50) => {
    const now = Date.now();

    // فحص الحماية من الطلبات المتكررة
    if (systemLogsRequestInProgress) {
      console.log('SystemLogs request already in progress, skipping...');
      return;
    }

    // فحص الفترة الزمنية بين الطلبات
    if (now - lastSystemLogsRequest < MIN_REQUEST_INTERVAL) {
      console.log(`SystemLogs request too soon, waiting ${MIN_REQUEST_INTERVAL - (now - lastSystemLogsRequest)}ms`);
      return;
    }

    systemLogsRequestInProgress = true;
    lastSystemLogsRequest = now;

    try {
      set({ isLoading: true, error: null });

      const response = await apiClient.get('/api/system/logs', {
        params: {
          page,
          limit,
          // إزالة الفلاتر لإظهار جميع السجلات
        }
      });

      // إذا كانت الصفحة الأولى، استبدل السجلات، وإلا أضف إليها
      const currentLogs = get().systemLogs;
      const newLogs = response.data?.logs || response.data || [];

      set({
        systemLogs: page === 1 ? newLogs : [...currentLogs, ...newLogs],
        isLoading: false
      });
    } catch (error) {
      console.error('Error fetching system logs:', error);

      set({
        systemLogs: page === 1 ? [] : get().systemLogs,
        isLoading: false,
        error: 'فشل في جلب سجلات النظام'
      });
    } finally {
      systemLogsRequestInProgress = false;
    }
  },

  // إعادة تعيين الحماية وإجبار التحديث
  forceRefreshSystemLogs: async () => {
    systemLogsRequestInProgress = false;
    lastSystemLogsRequest = 0;
    return get().fetchSystemLogs();
  },

  fetchSystemHealth: async () => {
    const now = Date.now();

    // فحص الحماية من الطلبات المتكررة
    if (systemHealthRequestInProgress) {
      console.log('SystemHealth request already in progress, skipping...');
      return;
    }

    // فحص الفترة الزمنية بين الطلبات
    if (now - lastSystemHealthRequest < MIN_REQUEST_INTERVAL) {
      console.log(`SystemHealth request too soon, waiting ${MIN_REQUEST_INTERVAL - (now - lastSystemHealthRequest)}ms`);
      return;
    }

    systemHealthRequestInProgress = true;
    lastSystemHealthRequest = now;

    try {
      const response = await apiClient.get('/api/system/health');

      set({
        systemHealth: response.data || initialSystemHealth
      });
    } catch (error) {
      console.error('Error fetching system health:', error);

      set({
        systemHealth: initialSystemHealth,
        error: 'فشل في جلب حالة النظام'
      });
    } finally {
      systemHealthRequestInProgress = false;
    }
  },

  clearSystemLogs: async (complete: boolean = false) => {
    try {
      set({ isLoading: true, error: null });

      const params = complete ? { complete: 'true' } : {};
      await apiClient.delete('/api/system/logs', { params });

      set({
        systemLogs: [],
        isLoading: false
      });
    } catch (error) {
      console.error('Error clearing system logs:', error);
      // استخدام خدمة تسجيل الأخطاء
      const errorLogger = await import('../services/errorLogger');
      errorLogger.default.logApiError(error, '/api/system/logs');

      set({
        isLoading: false,
        error: 'فشل في مسح سجلات النظام'
      });
    }
  },

  resolveSystemLog: async (logId: number, notes?: string) => {
    try {
      set({ isLoading: true, error: null });

      await apiClient.patch(`/api/system/logs/${logId}/resolve`, {
        resolution_notes: notes
      });

      // تحديث السجل في القائمة
      const currentLogs = get().systemLogs;
      const updatedLogs = currentLogs.map(log =>
        log.id === logId
          ? { ...log, resolved: true, resolution_notes: notes }
          : log
      );

      set({
        systemLogs: updatedLogs,
        isLoading: false
      });

      // تحديث الإحصائيات فوراً بعد حل السجل
      await get().fetchSystemHealth();
    } catch (error) {
      console.error('Error resolving system log:', error);
      // استخدام خدمة تسجيل الأخطاء
      const errorLogger = await import('../services/errorLogger');
      errorLogger.default.logApiError(error, `/api/system/logs/${logId}/resolve`);

      set({
        isLoading: false,
        error: 'فشل في حل مشكلة السجل'
      });
    }
  },

  sendLogsToSupport: async (logs: SystemLog[]) => {
    try {
      set({ isLoading: true, error: null });

      await apiClient.post('/api/system/logs/send-support', {
        logs: logs.map(log => log.id)
      });

      set({
        isLoading: false
      });
    } catch (error) {
      console.error('Error sending logs to support:', error);
      // استخدام خدمة تسجيل الأخطاء
      const errorLogger = await import('../services/errorLogger');
      errorLogger.default.logApiError(error, '/api/system/logs/send-support');

      set({
        isLoading: false,
        error: 'فشل في إرسال السجلات للدعم'
      });
    }
  },

  autoFixSystemIssues: async () => {
    try {
      set({ isLoading: true, error: null });

      const response = await apiClient.post('/api/system/logs/auto-fix');

      // تحديث السجلات بعد الحل التلقائي
      await get().fetchSystemLogs();
      await get().fetchSystemHealth();

      set({
        isLoading: false
      });

      return response.data;
    } catch (error) {
      console.error('Error in auto-fix:', error);
      // استخدام خدمة تسجيل الأخطاء
      const errorLogger = await import('../services/errorLogger');
      errorLogger.default.logApiError(error, '/api/system/logs/auto-fix');

      set({
        isLoading: false,
        error: 'فشل في الحل التلقائي للمشاكل'
      });

      throw error;
    }
  },

  // وظائف تعيين نوع التقرير والفترة
  setReportType: (type: ReportType) => {
    console.log('🔄 [STORE] Setting report type without auto-fetch:', type);
    set({ selectedReportType: type });

    // تم إزالة التحميل التلقائي لحل مشكلة التحميل المكرر
    // البيانات ستُجلب يدوياً من المكون حسب الحاجة
  },

  // دالة للتحكم في عرض مؤشر التحميل (لحل مشكلة التحميل المكرر)
  setLoadingStateControl: (disabled: boolean) => {
    disableLoadingState = disabled;
  },

  setPeriod: (period: ReportPeriod) => {
    set({ selectedPeriod: period });
    get().fetchSalesTrends(period);
    get().fetchPreviousPeriodTotal(period);
  }
}));
