/**
 * Store إدارة العلاقات بين الفروع والمستودعات
 * يستخدم Zustand لإدارة حالة العلاقات
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { 
  branchWarehouseService,
  BranchWarehouseLinkCreate,
  WarehouseForBranch,
  BranchForWarehouse,
  AvailableWarehouse,
  AvailableBranch
} from '../services/branchWarehouseService';

interface BranchWarehouseState {
  // البيانات
  branchWarehouses: Record<number, WarehouseForBranch[]>; // branch_id -> warehouses[]
  warehouseBranches: Record<number, BranchForWarehouse[]>; // warehouse_id -> branches[]
  availableWarehouses: Record<number, AvailableWarehouse[]>; // branch_id -> available warehouses[]
  availableBranches: Record<number, AvailableBranch[]>; // warehouse_id -> available branches[]
  primaryWarehouses: Record<number, WarehouseForBranch | null>; // branch_id -> primary warehouse
  
  // حالات التحميل
  loading: boolean;
  linking: boolean;
  unlinking: boolean;
  updating: boolean;
  
  // الأخطاء
  error: string | null;
  
  // الإجراءات
  linkBranchToWarehouse: (linkData: BranchWarehouseLinkCreate) => Promise<boolean>;
  unlinkBranchFromWarehouse: (branchId: number, warehouseId: number) => Promise<boolean>;
  setPrimaryWarehouseForBranch: (branchId: number, warehouseId: number) => Promise<boolean>;
  updateWarehousePriorityForBranch: (branchId: number, warehouseId: number, priority: number) => Promise<boolean>;
  fetchWarehousesForBranch: (branchId: number, includeInactive?: boolean) => Promise<void>;
  fetchBranchesForWarehouse: (warehouseId: number, includeInactive?: boolean) => Promise<void>;
  fetchPrimaryWarehouseForBranch: (branchId: number) => Promise<void>;
  fetchAvailableWarehousesForBranch: (branchId: number) => Promise<void>;
  fetchAvailableBranchesForWarehouse: (warehouseId: number) => Promise<void>;
  
  // إجراءات مساعدة
  clearError: () => void;
  clearBranchWarehouses: (branchId: number) => void;
  clearWarehouseBranches: (warehouseId: number) => void;
  reset: () => void;
}

export const useBranchWarehouseStore = create<BranchWarehouseState>()(
  devtools(
    (set, get) => ({
      // البيانات الأولية
      branchWarehouses: {},
      warehouseBranches: {},
      availableWarehouses: {},
      availableBranches: {},
      primaryWarehouses: {},
      
      // حالات التحميل الأولية
      loading: false,
      linking: false,
      unlinking: false,
      updating: false,
      
      // الأخطاء الأولية
      error: null,
      
      // ربط فرع بمستودع
      linkBranchToWarehouse: async (linkData: BranchWarehouseLinkCreate) => {
        set({ linking: true, error: null });
        
        try {
          const response = await branchWarehouseService.linkBranchToWarehouse(linkData);
          
          if (response.success) {
            // تحديث البيانات المحلية
            const { branchWarehouses, availableWarehouses } = get();
            
            // إزالة المستودع من القائمة المتاحة
            if (availableWarehouses[linkData.branch_id]) {
              const updatedAvailable = availableWarehouses[linkData.branch_id].filter(
                w => w.id !== linkData.warehouse_id
              );
              set({
                availableWarehouses: {
                  ...availableWarehouses,
                  [linkData.branch_id]: updatedAvailable
                }
              });
            }
            
            // إعادة جلب مستودعات الفرع
            await get().fetchWarehousesForBranch(linkData.branch_id);
            
            set({ linking: false });
            return true;
          } else {
            set({ 
              error: response.error || 'خطأ في ربط الفرع بالمستودع',
              linking: false 
            });
            return false;
          }
        } catch (error) {
          console.error('خطأ في ربط الفرع بالمستودع:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            linking: false 
          });
          return false;
        }
      },
      
      // إلغاء ربط فرع من مستودع
      unlinkBranchFromWarehouse: async (branchId: number, warehouseId: number) => {
        set({ unlinking: true, error: null });
        
        try {
          const response = await branchWarehouseService.unlinkBranchFromWarehouse(branchId, warehouseId);
          
          if (response.success) {
            // تحديث البيانات المحلية
            const { branchWarehouses } = get();
            
            if (branchWarehouses[branchId]) {
              const updatedWarehouses = branchWarehouses[branchId].filter(
                w => w.id !== warehouseId
              );
              set({
                branchWarehouses: {
                  ...branchWarehouses,
                  [branchId]: updatedWarehouses
                }
              });
            }
            
            // إعادة جلب المستودعات المتاحة
            await get().fetchAvailableWarehousesForBranch(branchId);
            
            set({ unlinking: false });
            return true;
          } else {
            set({ 
              error: response.error || 'خطأ في إلغاء ربط الفرع من المستودع',
              unlinking: false 
            });
            return false;
          }
        } catch (error) {
          console.error('خطأ في إلغاء ربط الفرع من المستودع:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            unlinking: false 
          });
          return false;
        }
      },
      
      // تعيين مستودع أساسي للفرع
      setPrimaryWarehouseForBranch: async (branchId: number, warehouseId: number) => {
        set({ updating: true, error: null });
        
        try {
          const response = await branchWarehouseService.setPrimaryWarehouseForBranch(branchId, warehouseId);
          
          if (response.success) {
            // تحديث البيانات المحلية
            const { branchWarehouses } = get();
            
            if (branchWarehouses[branchId]) {
              const updatedWarehouses = branchWarehouses[branchId].map(w => ({
                ...w,
                is_primary: w.id === warehouseId
              }));
              
              set({
                branchWarehouses: {
                  ...branchWarehouses,
                  [branchId]: updatedWarehouses
                }
              });
            }
            
            // إعادة جلب المستودع الأساسي
            await get().fetchPrimaryWarehouseForBranch(branchId);
            
            set({ updating: false });
            return true;
          } else {
            set({ 
              error: response.error || 'خطأ في تعيين المستودع الأساسي',
              updating: false 
            });
            return false;
          }
        } catch (error) {
          console.error('خطأ في تعيين المستودع الأساسي:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            updating: false 
          });
          return false;
        }
      },
      
      // تحديث أولوية مستودع للفرع
      updateWarehousePriorityForBranch: async (branchId: number, warehouseId: number, priority: number) => {
        set({ updating: true, error: null });
        
        try {
          const response = await branchWarehouseService.updateWarehousePriorityForBranch(
            branchId, 
            warehouseId, 
            priority
          );
          
          if (response.success) {
            // تحديث البيانات المحلية
            const { branchWarehouses } = get();
            
            if (branchWarehouses[branchId]) {
              const updatedWarehouses = branchWarehouses[branchId].map(w => 
                w.id === warehouseId ? { ...w, priority } : w
              );
              
              set({
                branchWarehouses: {
                  ...branchWarehouses,
                  [branchId]: updatedWarehouses
                }
              });
            }
            
            set({ updating: false });
            return true;
          } else {
            set({ 
              error: response.error || 'خطأ في تحديث أولوية المستودع',
              updating: false 
            });
            return false;
          }
        } catch (error) {
          console.error('خطأ في تحديث أولوية المستودع:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            updating: false 
          });
          return false;
        }
      },
      
      // جلب مستودعات الفرع
      fetchWarehousesForBranch: async (branchId: number, includeInactive = false) => {
        set({ loading: true, error: null });
        
        try {
          const response = await branchWarehouseService.getWarehousesForBranch(branchId, includeInactive);
          
          if (response.success && response.warehouses) {
            const { branchWarehouses } = get();
            set({ 
              branchWarehouses: {
                ...branchWarehouses,
                [branchId]: response.warehouses
              },
              loading: false 
            });
          } else {
            set({ 
              error: response.error || 'خطأ في جلب مستودعات الفرع',
              loading: false 
            });
          }
        } catch (error) {
          console.error('خطأ في جلب مستودعات الفرع:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            loading: false 
          });
        }
      },
      
      // جلب فروع المستودع
      fetchBranchesForWarehouse: async (warehouseId: number, includeInactive = false) => {
        set({ loading: true, error: null });
        
        try {
          const response = await branchWarehouseService.getBranchesForWarehouse(warehouseId, includeInactive);
          
          if (response.success && response.branches) {
            const { warehouseBranches } = get();
            set({ 
              warehouseBranches: {
                ...warehouseBranches,
                [warehouseId]: response.branches
              },
              loading: false 
            });
          } else {
            set({ 
              error: response.error || 'خطأ في جلب فروع المستودع',
              loading: false 
            });
          }
        } catch (error) {
          console.error('خطأ في جلب فروع المستودع:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            loading: false 
          });
        }
      },
      
      // جلب المستودع الأساسي للفرع
      fetchPrimaryWarehouseForBranch: async (branchId: number) => {
        set({ loading: true, error: null });
        
        try {
          const response = await branchWarehouseService.getPrimaryWarehouseForBranch(branchId);
          
          if (response.success) {
            const { primaryWarehouses } = get();
            set({ 
              primaryWarehouses: {
                ...primaryWarehouses,
                [branchId]: response.warehouse || null
              },
              loading: false 
            });
          } else {
            set({ 
              error: response.error || 'خطأ في جلب المستودع الأساسي',
              loading: false 
            });
          }
        } catch (error) {
          console.error('خطأ في جلب المستودع الأساسي:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            loading: false 
          });
        }
      },
      
      // جلب المستودعات المتاحة للفرع
      fetchAvailableWarehousesForBranch: async (branchId: number) => {
        set({ loading: true, error: null });
        
        try {
          const response = await branchWarehouseService.getAvailableWarehousesForBranch(branchId);
          
          if (response.success && response.available_warehouses) {
            const { availableWarehouses } = get();
            set({ 
              availableWarehouses: {
                ...availableWarehouses,
                [branchId]: response.available_warehouses
              },
              loading: false 
            });
          } else {
            set({ 
              error: response.error || 'خطأ في جلب المستودعات المتاحة',
              loading: false 
            });
          }
        } catch (error) {
          console.error('خطأ في جلب المستودعات المتاحة:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            loading: false 
          });
        }
      },
      
      // جلب الفروع المتاحة للمستودع
      fetchAvailableBranchesForWarehouse: async (warehouseId: number) => {
        set({ loading: true, error: null });
        
        try {
          const response = await branchWarehouseService.getAvailableBranchesForWarehouse(warehouseId);
          
          if (response.success && response.available_branches) {
            const { availableBranches } = get();
            set({ 
              availableBranches: {
                ...availableBranches,
                [warehouseId]: response.available_branches
              },
              loading: false 
            });
          } else {
            set({ 
              error: response.error || 'خطأ في جلب الفروع المتاحة',
              loading: false 
            });
          }
        } catch (error) {
          console.error('خطأ في جلب الفروع المتاحة:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            loading: false 
          });
        }
      },
      
      // مسح الخطأ
      clearError: () => set({ error: null }),
      
      // مسح مستودعات الفرع
      clearBranchWarehouses: (branchId: number) => {
        const { branchWarehouses } = get();
        const updated = { ...branchWarehouses };
        delete updated[branchId];
        set({ branchWarehouses: updated });
      },
      
      // مسح فروع المستودع
      clearWarehouseBranches: (warehouseId: number) => {
        const { warehouseBranches } = get();
        const updated = { ...warehouseBranches };
        delete updated[warehouseId];
        set({ warehouseBranches: updated });
      },
      
      // إعادة تعيين الحالة
      reset: () => set({
        branchWarehouses: {},
        warehouseBranches: {},
        availableWarehouses: {},
        availableBranches: {},
        primaryWarehouses: {},
        loading: false,
        linking: false,
        unlinking: false,
        updating: false,
        error: null
      })
    }),
    {
      name: 'branch-warehouse-store'
    }
  )
);

export default useBranchWarehouseStore;
