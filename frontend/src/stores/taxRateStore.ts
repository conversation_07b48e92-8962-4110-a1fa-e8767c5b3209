import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import apiClient from '../lib/axios';

export interface TaxRate {
  id: number;
  tax_type_id: number;
  name: string;
  rate_value: number;
  description?: string;
  effective_from?: string;
  effective_to?: string;
  is_default: boolean;
  is_active: boolean;
  applies_to: 'all' | 'products' | 'services';
  min_amount?: number;
  max_amount?: number;
  tax_code?: string;
  sort_order: number;
  created_at?: string;
  updated_at?: string;
  created_by?: number;
  updated_by?: number;
  tax_type_name?: string;
  display_rate?: string;
  applies_to_display_name?: string;
}

export interface TaxRateCreateData {
  tax_type_id: number;
  name: string;
  rate_value: number;
  description?: string;
  effective_from?: string;
  effective_to?: string;
  is_default: boolean;
  is_active: boolean;
  applies_to: 'all' | 'products' | 'services';
  min_amount?: number;
  max_amount?: number;
  tax_code?: string;
  sort_order: number;
}

export interface TaxRateUpdateData {
  name?: string;
  rate_value?: number;
  description?: string;
  effective_from?: string;
  effective_to?: string;
  is_default?: boolean;
  is_active?: boolean;
  applies_to?: 'all' | 'products' | 'services';
  min_amount?: number;
  max_amount?: number;
  tax_code?: string;
  sort_order?: number;
}

export interface TaxCalculationRequest {
  tax_rate_id: number;
  base_amount: number;
}

export interface TaxCalculationResult {
  base_amount: number;
  tax_amount: number;
  total_amount: number;
  tax_rate: TaxRate;
}

interface TaxRateStore {
  taxRates: TaxRate[];
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchTaxRates: (filters?: any) => Promise<void>;
  createTaxRate: (data: TaxRateCreateData) => Promise<TaxRate>;
  updateTaxRate: (id: number, data: TaxRateUpdateData) => Promise<TaxRate>;
  deleteTaxRate: (id: number) => Promise<void>;
  getTaxRateById: (id: number) => TaxRate | undefined;
  calculateTax: (request: TaxCalculationRequest) => Promise<TaxCalculationResult>;
  clearError: () => void;
}

const useTaxRateStore = create<TaxRateStore>()(
  devtools(
    (set, get) => ({
      taxRates: [],
      loading: false,
      error: null,

      fetchTaxRates: async (filters = {}) => {
        set({ loading: true, error: null });
        try {
          const params = new URLSearchParams();
          Object.entries(filters).forEach(([key, value]) => {
            if (value !== undefined && value !== null && value !== '') {
              params.append(key, String(value));
            }
          });

          const response = await apiClient.get(`/api/tax-rates/?${params.toString()}`);
          set({ taxRates: response.data, loading: false });
        } catch (error: any) {
          console.error('خطأ في جلب قيم الضرائب:', error);
          set({ 
            error: error.response?.data?.detail || 'حدث خطأ في جلب قيم الضرائب',
            loading: false 
          });
        }
      },

      createTaxRate: async (data: TaxRateCreateData) => {
        set({ loading: true, error: null });
        try {
          const response = await apiClient.post('/api/tax-rates/', data);
          const newTaxRate = response.data;
          
          set(state => ({
            taxRates: [...state.taxRates, newTaxRate],
            loading: false
          }));
          
          return newTaxRate;
        } catch (error: any) {
          console.error('خطأ في إنشاء القيمة الضريبية:', error);
          const errorMessage = error.response?.data?.detail || 'حدث خطأ في إنشاء القيمة الضريبية';
          set({ error: errorMessage, loading: false });
          throw new Error(errorMessage);
        }
      },

      updateTaxRate: async (id: number, data: TaxRateUpdateData) => {
        set({ loading: true, error: null });
        try {
          const response = await apiClient.put(`/api/tax-rates/${id}`, data);
          const updatedTaxRate = response.data;
          
          set(state => ({
            taxRates: state.taxRates.map(taxRate => 
              taxRate.id === id ? updatedTaxRate : taxRate
            ),
            loading: false
          }));
          
          return updatedTaxRate;
        } catch (error: any) {
          console.error('خطأ في تحديث القيمة الضريبية:', error);
          const errorMessage = error.response?.data?.detail || 'حدث خطأ في تحديث القيمة الضريبية';
          set({ error: errorMessage, loading: false });
          throw new Error(errorMessage);
        }
      },

      deleteTaxRate: async (id: number) => {
        set({ loading: true, error: null });
        try {
          await apiClient.delete(`/api/tax-rates/${id}`);
          
          set(state => ({
            taxRates: state.taxRates.filter(taxRate => taxRate.id !== id),
            loading: false
          }));
        } catch (error: any) {
          console.error('خطأ في حذف القيمة الضريبية:', error);
          const errorMessage = error.response?.data?.detail || 'حدث خطأ في حذف القيمة الضريبية';
          set({ error: errorMessage, loading: false });
          throw new Error(errorMessage);
        }
      },

      getTaxRateById: (id: number) => {
        return get().taxRates.find(taxRate => taxRate.id === id);
      },

      calculateTax: async (request: TaxCalculationRequest) => {
        set({ loading: true, error: null });
        try {
          const response = await apiClient.post('/api/tax-rates/calculate', request);
          set({ loading: false });
          return response.data;
        } catch (error: any) {
          console.error('خطأ في حساب الضريبة:', error);
          const errorMessage = error.response?.data?.detail || 'حدث خطأ في حساب الضريبة';
          set({ error: errorMessage, loading: false });
          throw new Error(errorMessage);
        }
      },

      clearError: () => {
        set({ error: null });
      }
    }),
    {
      name: 'tax-rate-store',
    }
  )
);

export default useTaxRateStore;
