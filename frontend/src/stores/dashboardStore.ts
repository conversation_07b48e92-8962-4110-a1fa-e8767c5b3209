import { create } from 'zustand'
import api from '../lib/axios'

export interface Product {
  id: number;
  name: string;
  barcode: string | null;
  description: string | null;
  price: number;
  cost_price: number;
  quantity: number;
  min_quantity: number;
  category: string | null;
  unit: string;
  is_active: boolean;
  created_at: string;
  updated_at: string | null;
}

interface DashboardStats {
  totalSales: number
  totalRevenue: number
  todaySales: number
  todayProfits: number
  totalDebts: number
  unpaidDebts: number
  lowStockCount: number
  recentSales: Array<{
    id: number
    total: number
    createdAt: string
    items: number
    payment_method: string
    amount_paid: number
    total_amount: number
    payment_status: string
  }>
  topProducts: Array<{
    id: number
    name: string
    quantity: number
    total: number
  }>
}

interface StockAlert {
  id: number;
  name: string;
  currentStock: number;
  minRequired: number;
  unit: string;
  category: string | null;
}

interface SalesTrend {
  date: string;
  amount: number;
}

interface DashboardStore {
  stats: DashboardStats
  lowStockProducts: StockAlert[]
  salesTrends: SalesTrend[]
  isLoading: boolean
  isStockLoading: boolean
  isTrendsLoading: boolean
  error: string | null
  refreshInterval: number | null
  autoRefresh: boolean
  selectedDateRange: 'today' | 'week' | 'month' | 'year'
  fetchStats: () => Promise<void>
  fetchLowStockProducts: () => Promise<void>
  fetchSalesTrends: (period: 'day' | 'week' | 'month' | 'year') => Promise<void>
  updateStockLevel: (productId: number, newQuantity: number) => Promise<void>
  toggleAutoRefresh: () => void
  setRefreshInterval: (interval: number | null) => void
  setDateRange: (range: 'today' | 'week' | 'month' | 'year') => void
}

const initialStats: DashboardStats = {
  totalSales: 0,
  totalRevenue: 0,
  todaySales: 0,
  todayProfits: 0,
  totalDebts: 0,
  unpaidDebts: 0,
  lowStockCount: 0,
  recentSales: [],
  topProducts: []
}

export const useDashboardStore = create<DashboardStore>((set, get) => ({
  stats: initialStats,
  lowStockProducts: [],
  salesTrends: [],
  isLoading: false,
  isStockLoading: false,
  isTrendsLoading: false,
  error: null,
  refreshInterval: null,
  autoRefresh: false,
  selectedDateRange: 'today',

  fetchStats: async () => {
    try {
      set({ isLoading: true, error: null })
      const response = await api.get('/api/dashboard/stats')
      set({ stats: response.data, isLoading: false })
    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch dashboard stats',
        isLoading: false
      })
    }
  },

  fetchLowStockProducts: async () => {
    try {
      set({ isStockLoading: true, error: null })
      console.log('Fetching zero stock products...')
      const response = await api.get('/api/products/', {
        params: {
          zero_stock: true,
          limit: 10
        }
      })

      // Transform the product data into stock alerts
      console.log('Low stock products response:', response.data)

      if (Array.isArray(response.data)) {
        const stockAlerts: StockAlert[] = response.data.map((product: Product) => ({
          id: product.id,
          name: product.name,
          currentStock: product.quantity,
          minRequired: product.min_quantity,
          unit: product.unit,
          category: product.category
        }))

        console.log('Transformed stock alerts:', stockAlerts)
        set({ lowStockProducts: stockAlerts, isStockLoading: false })
      } else {
        console.error('Response data is not an array:', response.data)
        set({ lowStockProducts: [], isStockLoading: false })
      }
    } catch (error) {
      console.error('Error fetching low stock products:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch low stock products',
        isStockLoading: false
      })
    }
  },

  fetchSalesTrends: async (period) => {
    try {
      set({ isTrendsLoading: true, error: null })
      console.log(`Fetching sales trends for period: ${period}`)
      const response = await api.get(`/api/dashboard/sales-trends/${period}`)

      // Normalize the data to ensure all items have a valid amount
      const normalizedData = response.data.map((item: any) => {
        // Ensure we have a valid date
        if (!item.date) {
          console.warn('Item missing date:', item)
          return null
        }

        // Ensure amount is a valid number
        let amount = 0

        // Check if amount exists and is a number
        if (typeof item.amount === 'number') {
          amount = item.amount
        }
        // If amount doesn't exist but total does, use total
        else if (typeof item.total === 'number') {
          amount = item.total
          console.log(`Using 'total' instead of 'amount' for ${item.date}: ${amount}`)
        }
        // Otherwise, default to 0
        else {
          console.warn(`Invalid amount for ${item.date}, defaulting to 0`)
        }

        return {
          date: item.date,
          amount: amount
        }
      }).filter(Boolean) // Remove any null items

      console.log(`Normalized sales trends data:`, normalizedData)
      set({ salesTrends: normalizedData, isTrendsLoading: false })
    } catch (error) {
      console.error('Error fetching sales trends:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch sales trends',
        isTrendsLoading: false
      })
    }
  },

  updateStockLevel: async (productId, newQuantity) => {
    try {
      set({ isStockLoading: true, error: null })
      const response = await api.put(`/api/products/${productId}/stock`, { quantity: newQuantity })

      // Log the response to verify the update was successful
      console.log('Stock update response:', response.data)

      // Refresh stock data after update
      await get().fetchLowStockProducts()
      await get().fetchStats()

      // Set loading to false after successful update
      set({ isStockLoading: false })

      // Return the updated product
      return response.data

    } catch (error) {
      console.error('Error updating stock level:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to update stock level',
        isStockLoading: false
      })
      throw error
    }
  },

  toggleAutoRefresh: () => {
    const { refreshInterval } = get()

    // Clear any existing interval
    if (refreshInterval !== null) {
      window.clearInterval(refreshInterval)
    }

    // تعطيل التحديث التلقائي لتوفير موارد النظام
    // المستخدم يمكنه تحديث البيانات يدوياً عند الحاجة
    console.log('التحديث التلقائي معطل لتوفير موارد النظام');
    set({ autoRefresh: false, refreshInterval: null });

    // if (!autoRefresh) {
    //   // Set up auto-refresh every minute (60000ms)
    //   const intervalId = window.setInterval(() => {
    //     get().fetchStats()
    //     get().fetchLowStockProducts()

    //     const { selectedDateRange } = get()
    //     let period: 'day' | 'week' | 'month' | 'year'

    //     switch (selectedDateRange) {
    //       case 'today':
    //         period = 'day'
    //         break
    //       case 'month':
    //         period = 'month'
    //         break
    //       case 'year':
    //         period = 'year'
    //         break
    //       default:
    //         period = 'week'
    //     }

    //     get().fetchSalesTrends(period)
    //   }, 60000) as unknown as number

    //   set({ autoRefresh: true, refreshInterval: intervalId })
    // } else {
    //   set({ autoRefresh: false, refreshInterval: null })
    // }
  },

  setRefreshInterval: (interval) => {
    const { refreshInterval } = get()

    // Clear any existing interval
    if (refreshInterval !== null) {
      window.clearInterval(refreshInterval)
    }

    if (interval === null) {
      set({ refreshInterval: null })
      return
    }

    // Set up new interval
    const intervalId = window.setInterval(() => {
      get().fetchStats()
      get().fetchLowStockProducts()

      const { selectedDateRange } = get()
      let period: 'day' | 'week' | 'month' | 'year'

      switch (selectedDateRange) {
        case 'today':
          period = 'day'
          break
        case 'month':
          period = 'month'
          break
        case 'year':
          period = 'year'
          break
        default:
          period = 'week'
      }

      get().fetchSalesTrends(period)
    }, interval) as unknown as number

    set({ refreshInterval: intervalId, autoRefresh: true })
  },

  setDateRange: (range) => {
    set({ selectedDateRange: range })

    let period: 'day' | 'week' | 'month' | 'year'

    switch (range) {
      case 'today':
        period = 'day'
        break
      case 'month':
        period = 'month'
        break
      case 'year':
        period = 'year'
        break
      default:
        period = 'week'
    }

    get().fetchSalesTrends(period)
  }
}))