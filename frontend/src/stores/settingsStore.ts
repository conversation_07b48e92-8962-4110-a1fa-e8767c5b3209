import { create } from 'zustand';
import api from '../lib/axios';

export interface Setting {
  id: number;
  key: string;
  value: string;
  description?: string;
  created_at: string;
  updated_at?: string;
}

interface SettingsState {
  settings: Setting[];
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchSettings: () => Promise<Setting[]>;
  getSetting: (key: string) => Setting | undefined;
  updateSetting: (key: string, value: string) => Promise<Setting>;
  createSetting: (key: string, value: string, description?: string) => Promise<Setting>;
  deleteSetting: (key: string) => Promise<void>;
}

const useSettingsStore = create<SettingsState>((set, get) => ({
  settings: [],
  loading: false,
  error: null,
  
  fetchSettings: async () => {
    try {
      set({ loading: true, error: null });
      console.log('Fetching settings from API');
      
      const response = await api.get<Setting[]>('/api/settings');
      console.log('Settings fetched:', response.data);
      
      set({ settings: response.data, loading: false });
      return response.data;
    } catch (error) {
      console.error('Error fetching settings:', error);
      set({ 
        error: error instanceof Error ? error.message : 'فشل في جلب الإعدادات',
        loading: false 
      });
      return [];
    }
  },
  
  getSetting: (key: string) => {
    return get().settings.find(setting => setting.key === key);
  },
  
  updateSetting: async (key: string, value: string) => {
    try {
      set({ loading: true, error: null });
      console.log(`Updating setting ${key} with value ${value}`);
      
      const response = await api.put<Setting>(`/api/settings/${key}`, { value });
      console.log('Setting updated:', response.data);
      
      // Update the setting in the store
      set(state => ({
        settings: state.settings.map(setting => 
          setting.key === key ? { ...setting, value } : setting
        ),
        loading: false
      }));
      
      return response.data;
    } catch (error) {
      console.error(`Error updating setting ${key}:`, error);
      set({ 
        error: error instanceof Error ? error.message : 'فشل في تحديث الإعداد',
        loading: false 
      });
      throw error;
    }
  },
  
  createSetting: async (key: string, value: string, description?: string) => {
    try {
      set({ loading: true, error: null });
      console.log(`Creating setting ${key} with value ${value}`);
      
      const response = await api.post<Setting>('/api/settings', { 
        key, 
        value,
        description 
      });
      console.log('Setting created:', response.data);
      
      // Add the new setting to the store
      set(state => ({
        settings: [...state.settings, response.data],
        loading: false
      }));
      
      return response.data;
    } catch (error) {
      console.error(`Error creating setting ${key}:`, error);
      set({ 
        error: error instanceof Error ? error.message : 'فشل في إنشاء الإعداد',
        loading: false 
      });
      throw error;
    }
  },
  
  deleteSetting: async (key: string) => {
    try {
      set({ loading: true, error: null });
      console.log(`Deleting setting ${key}`);
      
      await api.delete(`/api/settings/${key}`);
      console.log(`Setting ${key} deleted`);
      
      // Remove the setting from the store
      set(state => ({
        settings: state.settings.filter(setting => setting.key !== key),
        loading: false
      }));
    } catch (error) {
      console.error(`Error deleting setting ${key}:`, error);
      set({ 
        error: error instanceof Error ? error.message : 'فشل في حذف الإعداد',
        loading: false 
      });
      throw error;
    }
  }
}));

export default useSettingsStore;
