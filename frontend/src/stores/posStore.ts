import { create } from 'zustand';
import api from '../lib/axios';


interface Product {
  id: number;
  name: string;
  price: number;
  quantity: number;
  barcode: string;
  image_url?: string;
}

interface CartItem extends Product {
  cartQuantity: number;
}

interface POSStore {
  products: Product[];
  cart: CartItem[];
  isLoading: boolean;
  error: string | null;
  fetchProducts: () => Promise<void>;
  addToCart: (product: Product) => void;
  removeFromCart: (productId: number) => void;
  updateCartItemQuantity: (productId: number, quantity: number) => void;
  clearCart: () => void;
  getTotalAmount: () => number;
  getTaxAmount: () => number;
  getFinalAmount: () => number;
  processSale: (paymentMethod: string) => Promise<void>;
}

export const usePOSStore = create<POSStore>((set, get) => ({
  products: [],
  cart: [],
  isLoading: false,
  error: null,

  fetchProducts: async () => {
    set({ isLoading: true, error: null });
    try {
      const response = await api.get('/api/products');
      set({ products: response.data, isLoading: false });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch products',
        isLoading: false
      });

    }
  },

  addToCart: (product) => {
    const cart = get().cart;
    const existingItem = cart.find(item => item.id === product.id);

    if (existingItem) {
      set({
        cart: cart.map(item =>
          item.id === product.id
            ? { ...item, cartQuantity: item.cartQuantity + 1 }
            : item
        ),
      });
    } else {
      set({
        cart: [...cart, { ...product, cartQuantity: 1 }],
      });
    }
  },

  removeFromCart: (productId) => {
    set({
      cart: get().cart.filter(item => item.id !== productId),
    });
  },

  updateCartItemQuantity: (productId, quantity) => {
    if (quantity <= 0) {
      get().removeFromCart(productId);
      return;
    }

    set({
      cart: get().cart.map(item =>
        item.id === productId
          ? { ...item, cartQuantity: quantity }
          : item
      ),
    });
  },

  clearCart: () => {
    set({ cart: [] });
  },

  getTotalAmount: () => {
    return get().cart.reduce(
      (total, item) => total + item.price * item.cartQuantity,
      0
    );
  },

  getTaxAmount: () => {
    return get().getTotalAmount() * 0.15; // 15% VAT
  },

  getFinalAmount: () => {
    return get().getTotalAmount() + get().getTaxAmount();
  },

  processSale: async (paymentMethod) => {
    set({ isLoading: true, error: null });
    try {
      const sale = {
        items: get().cart.map(item => ({
          product_id: item.id,
          quantity: item.cartQuantity,
          price: item.price,
          total: item.price * item.cartQuantity,
        })),
        payment_method: paymentMethod,
        total_amount: get().getTotalAmount(),
        tax_amount: get().getTaxAmount(),
        final_amount: get().getFinalAmount(),
      };

      await api.post('/api/sales', sale);
      get().clearCart();
      set({ isLoading: false });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to process sale',
        isLoading: false
      });

    }
  },
}));