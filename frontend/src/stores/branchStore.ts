/**
 * Store إدارة الفروع
 * يستخدم Zustand لإدارة حالة الفروع
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { 
  branchService,
  Branch,
  BranchCreate,
  BranchUpdate,
  BranchSearchParams,
  BranchStatistics
} from '../services/branchService';

interface BranchState {
  // البيانات
  branches: Branch[];
  currentBranch: Branch | null;
  statistics: BranchStatistics | null;
  
  // حالات التحميل
  loading: boolean;
  creating: boolean;
  updating: boolean;
  deleting: boolean;
  
  // الأخطاء
  error: string | null;
  
  // الإجراءات
  fetchBranches: (includeInactive?: boolean) => Promise<void>;
  fetchBranchById: (branchId: number) => Promise<void>;
  fetchBranchByCode: (branchCode: string) => Promise<void>;
  fetchBranchByUuid: (branchUuid: string) => Promise<void>;
  createBranch: (branchData: BranchCreate) => Promise<boolean>;
  updateBranch: (branchId: number, branchData: BranchUpdate) => Promise<boolean>;
  deleteBranch: (branchId: number) => Promise<boolean>;
  setMainBranch: (branchId: number) => Promise<boolean>;
  toggleBranchStatus: (branchId: number) => Promise<boolean>;
  searchBranches: (searchParams: BranchSearchParams) => Promise<void>;
  fetchBranchStatistics: () => Promise<void>;
  
  // إجراءات مساعدة
  clearError: () => void;
  clearCurrentBranch: () => void;
  reset: () => void;
}

export const useBranchStore = create<BranchState>()(
  devtools(
    (set, get) => ({
      // البيانات الأولية
      branches: [],
      currentBranch: null,
      statistics: null,
      
      // حالات التحميل الأولية
      loading: false,
      creating: false,
      updating: false,
      deleting: false,
      
      // الأخطاء الأولية
      error: null,
      
      // جلب جميع الفروع
      fetchBranches: async (includeInactive = false) => {
        set({ loading: true, error: null });
        
        try {
          const response = await branchService.getAllBranches(includeInactive);
          
          if (response.success && response.branches) {
            set({ 
              branches: response.branches,
              loading: false 
            });
          } else {
            set({ 
              error: response.error || 'خطأ في جلب الفروع',
              loading: false 
            });
          }
        } catch (error) {
          console.error('خطأ في جلب الفروع:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            loading: false 
          });
        }
      },
      
      // جلب فرع بالمعرف
      fetchBranchById: async (branchId: number) => {
        set({ loading: true, error: null });
        
        try {
          const response = await branchService.getBranchById(branchId);
          
          if (response.success && response.branch) {
            set({ 
              currentBranch: response.branch,
              loading: false 
            });
          } else {
            set({ 
              error: response.error || 'خطأ في جلب الفرع',
              loading: false 
            });
          }
        } catch (error) {
          console.error('خطأ في جلب الفرع:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            loading: false 
          });
        }
      },
      
      // جلب فرع بالكود
      fetchBranchByCode: async (branchCode: string) => {
        set({ loading: true, error: null });
        
        try {
          const response = await branchService.getBranchByCode(branchCode);
          
          if (response.success && response.branch) {
            set({ 
              currentBranch: response.branch,
              loading: false 
            });
          } else {
            set({ 
              error: response.error || 'خطأ في جلب الفرع',
              loading: false 
            });
          }
        } catch (error) {
          console.error('خطأ في جلب الفرع:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            loading: false 
          });
        }
      },
      
      // إنشاء فرع جديد
      createBranch: async (branchData: BranchCreate) => {
        set({ creating: true, error: null });
        
        try {
          const response = await branchService.createBranch(branchData);
          
          if (response.success && response.branch) {
            const { branches } = get();
            set({ 
              branches: [...branches, response.branch],
              creating: false 
            });
            return true;
          } else {
            set({ 
              error: response.error || 'خطأ في إنشاء الفرع',
              creating: false 
            });
            return false;
          }
        } catch (error) {
          console.error('خطأ في إنشاء الفرع:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            creating: false 
          });
          return false;
        }
      },

      // جلب فرع بـ UUID
      fetchBranchByUuid: async (branchUuid: string) => {
        set({ loading: true, error: null });

        try {
          const response = await branchService.getBranchByUuid(branchUuid);

          if (response.success && response.branch) {
            set({
              currentBranch: response.branch,
              loading: false
            });
          } else {
            set({
              error: response.error || 'خطأ في جلب الفرع',
              loading: false
            });
          }
        } catch (error) {
          console.error('خطأ في جلب الفرع بـ UUID:', error);
          set({
            error: 'خطأ في الاتصال بالخادم',
            loading: false
          });
        }
      },

      // تحديث فرع
      updateBranch: async (branchId: number, branchData: BranchUpdate) => {
        set({ updating: true, error: null });
        
        try {
          const response = await branchService.updateBranch(branchId, branchData);
          
          if (response.success && response.branch) {
            const { branches } = get();
            const updatedBranches = branches.map(branch => 
              branch.id === branchId ? response.branch! : branch
            );
            
            set({ 
              branches: updatedBranches,
              currentBranch: response.branch,
              updating: false 
            });
            return true;
          } else {
            set({ 
              error: response.error || 'خطأ في تحديث الفرع',
              updating: false 
            });
            return false;
          }
        } catch (error) {
          console.error('خطأ في تحديث الفرع:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            updating: false 
          });
          return false;
        }
      },
      
      // حذف فرع
      deleteBranch: async (branchId: number) => {
        set({ deleting: true, error: null });
        
        try {
          const response = await branchService.deleteBranch(branchId);
          
          if (response.success) {
            const { branches } = get();
            const filteredBranches = branches.filter(branch => branch.id !== branchId);
            
            set({ 
              branches: filteredBranches,
              deleting: false 
            });
            return true;
          } else {
            set({ 
              error: response.error || 'خطأ في حذف الفرع',
              deleting: false 
            });
            return false;
          }
        } catch (error) {
          console.error('خطأ في حذف الفرع:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            deleting: false 
          });
          return false;
        }
      },
      
      // تعيين فرع رئيسي
      setMainBranch: async (branchId: number) => {
        set({ updating: true, error: null });
        
        try {
          const response = await branchService.setMainBranch(branchId);
          
          if (response.success) {
            // تحديث جميع الفروع - إزالة is_main من الفروع الأخرى
            const { branches } = get();
            const updatedBranches = branches.map(branch => ({
              ...branch,
              is_main: branch.id === branchId
            }));
            
            set({ 
              branches: updatedBranches,
              updating: false 
            });
            return true;
          } else {
            set({ 
              error: response.error || 'خطأ في تعيين الفرع الرئيسي',
              updating: false 
            });
            return false;
          }
        } catch (error) {
          console.error('خطأ في تعيين الفرع الرئيسي:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            updating: false 
          });
          return false;
        }
      },
      
      // تبديل حالة نشاط الفرع
      toggleBranchStatus: async (branchId: number) => {
        set({ updating: true, error: null });
        
        try {
          const response = await branchService.toggleBranchStatus(branchId);
          
          if (response.success) {
            const { branches } = get();
            const updatedBranches = branches.map(branch => 
              branch.id === branchId 
                ? { ...branch, is_active: !branch.is_active }
                : branch
            );
            
            set({ 
              branches: updatedBranches,
              updating: false 
            });
            return true;
          } else {
            set({ 
              error: response.error || 'خطأ في تبديل حالة الفرع',
              updating: false 
            });
            return false;
          }
        } catch (error) {
          console.error('خطأ في تبديل حالة الفرع:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            updating: false 
          });
          return false;
        }
      },
      
      // البحث في الفروع
      searchBranches: async (searchParams: BranchSearchParams) => {
        set({ loading: true, error: null });
        
        try {
          const response = await branchService.searchBranches(searchParams);
          
          if (response.success && response.branches) {
            set({ 
              branches: response.branches,
              loading: false 
            });
          } else {
            set({ 
              error: response.error || 'خطأ في البحث في الفروع',
              loading: false 
            });
          }
        } catch (error) {
          console.error('خطأ في البحث في الفروع:', error);
          set({ 
            error: 'خطأ في الاتصال بالخادم',
            loading: false 
          });
        }
      },
      
      // جلب إحصائيات الفروع
      fetchBranchStatistics: async () => {
        set({ loading: true, error: null });

        try {
          const response = await branchService.getBranchStatistics();

          if (response.success && response.statistics) {
            set({
              statistics: response.statistics,
              loading: false
            });
          } else {
            // تحويل رسالة الخطأ إلى نص
            let errorMessage = 'خطأ في جلب إحصائيات الفروع';
            if (response.error) {
              if (typeof response.error === 'string') {
                errorMessage = response.error;
              } else if (typeof response.error === 'object') {
                errorMessage = JSON.stringify(response.error);
              }
            }
            set({
              error: errorMessage,
              loading: false
            });
          }
        } catch (error: any) {
          console.error('خطأ في جلب إحصائيات الفروع:', error);

          // تحويل رسالة الخطأ إلى نص
          let errorMessage = 'خطأ في الاتصال بالخادم';
          if (error?.response?.data?.detail) {
            if (Array.isArray(error.response.data.detail)) {
              errorMessage = error.response.data.detail.map((d: any) => d.msg || d).join(', ');
            } else if (typeof error.response.data.detail === 'string') {
              errorMessage = error.response.data.detail;
            }
          } else if (error?.message) {
            errorMessage = error.message;
          }

          set({
            error: errorMessage,
            loading: false
          });
        }
      },
      
      // مسح الخطأ
      clearError: () => set({ error: null }),
      
      // مسح الفرع الحالي
      clearCurrentBranch: () => set({ currentBranch: null }),
      
      // إعادة تعيين الحالة
      reset: () => set({
        branches: [],
        currentBranch: null,
        statistics: null,
        loading: false,
        creating: false,
        updating: false,
        deleting: false,
        error: null
      })
    }),
    {
      name: 'branch-store'
    }
  )
);

export default useBranchStore;
