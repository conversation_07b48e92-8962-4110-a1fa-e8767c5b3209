import { create } from 'zustand';
import api from '../lib/axios';

export interface Unit {
  id: number;
  name: string;
  symbol: string | null;
  description: string | null;
  unit_type: string | null;
  base_unit_id: number | null;
  conversion_factor: number | null;
  is_active: boolean;
  created_at: string;
  updated_at: string | null;
  created_by: number;
  updated_by: number | null;
  base_unit?: Unit;
}

interface UnitStore {
  units: Unit[];
  unitTypes: string[];
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchUnits: (params?: { search?: string; unit_type?: string; active_only?: boolean }) => Promise<void>;
  fetchUnitTypes: () => Promise<void>;
  getUnitNames: () => Promise<string[]>;
  createUnit: (unit: Omit<Unit, 'id' | 'created_at' | 'updated_at' | 'created_by' | 'updated_by' | 'base_unit'>) => Promise<Unit>;
  updateUnit: (id: number, unit: Partial<Unit>) => Promise<Unit>;
  deleteUnit: (id: number) => Promise<void>;
  setError: (error: string | null) => void;
  clearError: () => void;
}

const useUnitStore = create<UnitStore>((set, get) => ({
  units: [],
  unitTypes: [],
  loading: false,
  error: null,

  fetchUnits: async (params = {}) => {
    try {
      set({ loading: true, error: null });
      const queryParams = new URLSearchParams();
      
      if (params.search) queryParams.append('search', params.search);
      if (params.unit_type) queryParams.append('unit_type', params.unit_type);
      if (params.active_only !== undefined) queryParams.append('active_only', params.active_only.toString());
      
      const response = await api.get(`/api/units/?${queryParams.toString()}`);
      set({ units: response.data, loading: false });
    } catch (error: any) {
      console.error('Error fetching units:', error);
      set({ 
        error: error.response?.data?.detail || 'فشل في جلب الوحدات', 
        loading: false 
      });
    }
  },

  fetchUnitTypes: async () => {
    try {
      const response = await api.get('/api/units/types');
      set({ unitTypes: response.data });
    } catch (error: any) {
      console.error('Error fetching unit types:', error);
      set({ error: error.response?.data?.detail || 'فشل في جلب أنواع الوحدات' });
    }
  },

  getUnitNames: async () => {
    try {
      const response = await api.get('/api/units/list/names');
      return response.data;
    } catch (error: any) {
      console.error('Error fetching unit names:', error);
      set({ error: error.response?.data?.detail || 'فشل في جلب أسماء الوحدات' });
      return [];
    }
  },

  createUnit: async (unitData) => {
    try {
      set({ loading: true, error: null });
      const response = await api.post('/api/units/', unitData);
      const newUnit = response.data;
      
      set(state => ({
        units: [...state.units, newUnit],
        loading: false
      }));
      
      return newUnit;
    } catch (error: any) {
      console.error('Error creating unit:', error);
      const errorMessage = error.response?.data?.detail || 'فشل في إنشاء الوحدة';
      set({ error: errorMessage, loading: false });
      throw new Error(errorMessage);
    }
  },

  updateUnit: async (id, unitData) => {
    try {
      set({ loading: true, error: null });
      const response = await api.put(`/api/units/${id}`, unitData);
      const updatedUnit = response.data;
      
      set(state => ({
        units: state.units.map(unit => 
          unit.id === id ? updatedUnit : unit
        ),
        loading: false
      }));
      
      return updatedUnit;
    } catch (error: any) {
      console.error('Error updating unit:', error);
      const errorMessage = error.response?.data?.detail || 'فشل في تحديث الوحدة';
      set({ error: errorMessage, loading: false });
      throw new Error(errorMessage);
    }
  },

  deleteUnit: async (id) => {
    try {
      set({ loading: true, error: null });
      await api.delete(`/api/units/${id}`);
      
      set(state => ({
        units: state.units.filter(unit => unit.id !== id),
        loading: false
      }));
    } catch (error: any) {
      console.error('Error deleting unit:', error);
      const errorMessage = error.response?.data?.detail || 'فشل في حذف الوحدة';
      set({ error: errorMessage, loading: false });
      throw new Error(errorMessage);
    }
  },

  setError: (error) => set({ error }),
  clearError: () => set({ error: null })
}));

export default useUnitStore;
