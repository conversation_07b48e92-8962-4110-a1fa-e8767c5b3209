import { create } from 'zustand';
import api from '../lib/axios';

export interface Brand {
  id: number;
  name: string;
  description: string | null;
  logo_url: string | null;
  website: string | null;
  contact_info: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string | null;
  created_by: number;
  updated_by: number | null;
}

interface BrandStore {
  brands: Brand[];
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchBrands: (params?: { search?: string; active_only?: boolean }) => Promise<void>;
  getBrandNames: () => Promise<string[]>;
  createBrand: (brand: Omit<Brand, 'id' | 'created_at' | 'updated_at' | 'created_by' | 'updated_by'>) => Promise<Brand>;
  updateBrand: (id: number, brand: Partial<Brand>) => Promise<Brand>;
  deleteBrand: (id: number) => Promise<void>;
  setError: (error: string | null) => void;
  clearError: () => void;
}

const useBrandStore = create<BrandStore>((set, get) => ({
  brands: [],
  loading: false,
  error: null,

  fetchBrands: async (params = {}) => {
    try {
      set({ loading: true, error: null });
      const queryParams = new URLSearchParams();
      
      if (params.search) queryParams.append('search', params.search);
      if (params.active_only !== undefined) queryParams.append('active_only', params.active_only.toString());
      
      const response = await api.get(`/api/brands/?${queryParams.toString()}`);
      set({ brands: response.data, loading: false });
    } catch (error: any) {
      console.error('Error fetching brands:', error);
      set({ 
        error: error.response?.data?.detail || 'فشل في جلب العلامات التجارية', 
        loading: false 
      });
    }
  },

  getBrandNames: async () => {
    try {
      const response = await api.get('/api/brands/list/names');
      return response.data;
    } catch (error: any) {
      console.error('Error fetching brand names:', error);
      set({ error: error.response?.data?.detail || 'فشل في جلب أسماء العلامات التجارية' });
      return [];
    }
  },

  createBrand: async (brandData) => {
    try {
      set({ loading: true, error: null });
      const response = await api.post('/api/brands/', brandData);
      const newBrand = response.data;
      
      set(state => ({
        brands: [...state.brands, newBrand],
        loading: false
      }));
      
      return newBrand;
    } catch (error: any) {
      console.error('Error creating brand:', error);
      const errorMessage = error.response?.data?.detail || 'فشل في إنشاء العلامة التجارية';
      set({ error: errorMessage, loading: false });
      throw new Error(errorMessage);
    }
  },

  updateBrand: async (id, brandData) => {
    try {
      set({ loading: true, error: null });
      const response = await api.put(`/api/brands/${id}`, brandData);
      const updatedBrand = response.data;
      
      set(state => ({
        brands: state.brands.map(brand => 
          brand.id === id ? updatedBrand : brand
        ),
        loading: false
      }));
      
      return updatedBrand;
    } catch (error: any) {
      console.error('Error updating brand:', error);
      const errorMessage = error.response?.data?.detail || 'فشل في تحديث العلامة التجارية';
      set({ error: errorMessage, loading: false });
      throw new Error(errorMessage);
    }
  },

  deleteBrand: async (id) => {
    try {
      set({ loading: true, error: null });
      await api.delete(`/api/brands/${id}`);
      
      set(state => ({
        brands: state.brands.filter(brand => brand.id !== id),
        loading: false
      }));
    } catch (error: any) {
      console.error('Error deleting brand:', error);
      const errorMessage = error.response?.data?.detail || 'فشل في حذف العلامة التجارية';
      set({ error: errorMessage, loading: false });
      throw new Error(errorMessage);
    }
  },

  setError: (error) => set({ error }),
  clearError: () => set({ error: null })
}));

export default useBrandStore;
