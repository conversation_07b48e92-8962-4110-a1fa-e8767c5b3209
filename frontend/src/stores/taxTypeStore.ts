import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import apiClient from '../lib/axios';

export interface TaxType {
  id: number;
  name: string;
  name_ar: string;
  description?: string;
  tax_category: 'standard' | 'reduced' | 'zero' | 'exempt';
  calculation_method: 'percentage' | 'fixed';
  is_compound: boolean;
  is_active: boolean;
  sort_order: number;
  created_at?: string;
  updated_at?: string;
  created_by?: number;
  updated_by?: number;
  tax_rates_count?: number;
  active_rates_count?: number;
  category_display_name?: string;
  calculation_method_display_name?: string;
}

export interface TaxTypeCreateData {
  name: string;
  name_ar: string;
  description?: string;
  tax_category: 'standard' | 'reduced' | 'zero' | 'exempt';
  calculation_method: 'percentage' | 'fixed';
  is_compound: boolean;
  is_active: boolean;
  sort_order: number;
}

export interface TaxTypeUpdateData {
  name?: string;
  name_ar?: string;
  description?: string;
  tax_category?: 'standard' | 'reduced' | 'zero' | 'exempt';
  calculation_method?: 'percentage' | 'fixed';
  is_compound?: boolean;
  is_active?: boolean;
  sort_order?: number;
}

interface TaxTypeStore {
  taxTypes: TaxType[];
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchTaxTypes: () => Promise<void>;
  createTaxType: (data: TaxTypeCreateData) => Promise<TaxType>;
  updateTaxType: (id: number, data: TaxTypeUpdateData) => Promise<TaxType>;
  deleteTaxType: (id: number) => Promise<void>;
  getTaxTypeById: (id: number) => TaxType | undefined;
  clearError: () => void;
}

const useTaxTypeStore = create<TaxTypeStore>()(
  devtools(
    (set, get) => ({
      taxTypes: [],
      loading: false,
      error: null,

      fetchTaxTypes: async () => {
        set({ loading: true, error: null });
        try {
          const response = await apiClient.get('/api/tax-types/');
          set({ taxTypes: response.data, loading: false });
        } catch (error: any) {
          console.error('خطأ في جلب أنواع الضرائب:', error);
          set({ 
            error: error.response?.data?.detail || 'حدث خطأ في جلب أنواع الضرائب',
            loading: false 
          });
        }
      },

      createTaxType: async (data: TaxTypeCreateData) => {
        set({ loading: true, error: null });
        try {
          const response = await apiClient.post('/api/tax-types/', data);
          const newTaxType = response.data;
          
          set(state => ({
            taxTypes: [...state.taxTypes, newTaxType],
            loading: false
          }));
          
          return newTaxType;
        } catch (error: any) {
          console.error('خطأ في إنشاء نوع الضريبة:', error);
          const errorMessage = error.response?.data?.detail || 'حدث خطأ في إنشاء نوع الضريبة';
          set({ error: errorMessage, loading: false });
          throw new Error(errorMessage);
        }
      },

      updateTaxType: async (id: number, data: TaxTypeUpdateData) => {
        set({ loading: true, error: null });
        try {
          const response = await apiClient.put(`/api/tax-types/${id}`, data);
          const updatedTaxType = response.data;
          
          set(state => ({
            taxTypes: state.taxTypes.map(taxType => 
              taxType.id === id ? updatedTaxType : taxType
            ),
            loading: false
          }));
          
          return updatedTaxType;
        } catch (error: any) {
          console.error('خطأ في تحديث نوع الضريبة:', error);
          const errorMessage = error.response?.data?.detail || 'حدث خطأ في تحديث نوع الضريبة';
          set({ error: errorMessage, loading: false });
          throw new Error(errorMessage);
        }
      },

      deleteTaxType: async (id: number) => {
        set({ loading: true, error: null });
        try {
          await apiClient.delete(`/api/tax-types/${id}`);
          
          set(state => ({
            taxTypes: state.taxTypes.filter(taxType => taxType.id !== id),
            loading: false
          }));
        } catch (error: any) {
          console.error('خطأ في حذف نوع الضريبة:', error);
          const errorMessage = error.response?.data?.detail || 'حدث خطأ في حذف نوع الضريبة';
          set({ error: errorMessage, loading: false });
          throw new Error(errorMessage);
        }
      },

      getTaxTypeById: (id: number) => {
        return get().taxTypes.find(taxType => taxType.id === id);
      },

      clearError: () => {
        set({ error: null });
      }
    }),
    {
      name: 'tax-type-store',
    }
  )
);

export default useTaxTypeStore;
