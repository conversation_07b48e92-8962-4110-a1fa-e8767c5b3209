# اختبار رفع صور الفئات الرئيسية

## الميزات المضافة

### 1. إضافة حقل image_url لنموذج Category
- تم إضافة حقل `image_url` إلى جدول `categories` في قاعدة البيانات
- تم تحديث schemas الخاصة بالفئات لدعم الحقل الجديد
- تم تشغيل migration بنجاح لإضافة العمود

### 2. رفع صورة واحدة فقط للفئة
- تم تعديل مكون `ImageUploadComponent` لدعم رفع صورة واحدة فقط عندما `multiple=false`
- يتم استبدال الصورة الحالية عند اختيار صورة جديدة
- النصوص تتغير من "الصور" إلى "الصورة" عندما `multiple=false`

### 3. عرض صورة الفئة في الجدول
- تم إضافة عمود "الصورة" في جدول الفئات
- يعرض صورة مصغرة 10x10 بكسل
- يعرض أيقونة افتراضية إذا لم تكن هناك صورة
- معالجة أخطاء تحميل الصور

### 4. إدارة صورة الفئة في النموذج
- قسم مخصص لرفع صورة الفئة في نموذج إنشاء/تعديل الفئة
- عرض الصورة الحالية مع إمكانية حذفها
- زر لتغيير الصورة أو رفع صورة جديدة
- حفظ مسار الصورة في حقل `image_url`

## خطوات الاختبار

### اختبار إنشاء فئة جديدة مع صورة:
1. انتقل إلى صفحة إدارة الكتالوج
2. اختر تبويب "الفئات"
3. انقر على "إضافة فئة جديدة"
4. املأ البيانات الأساسية (اسم الفئة، الوصف)
5. في قسم "صورة الفئة"، انقر على "رفع صورة الفئة"
6. اختر صورة واحدة فقط
7. تأكد من ظهور معاينة الصورة
8. انقر على "رفع الصورة"
9. احفظ الفئة
10. تحقق من ظهور الصورة في الجدول

### اختبار تعديل صورة فئة موجودة:
1. انقر على زر التعديل لفئة موجودة
2. في قسم صورة الفئة، انقر على "تغيير صورة الفئة"
3. اختر صورة جديدة
4. احفظ التغييرات
5. تحقق من تحديث الصورة في الجدول

### اختبار حذف صورة الفئة:
1. افتح نموذج تعديل فئة لها صورة
2. انقر على أيقونة الحذف بجانب الصورة الحالية
3. احفظ التغييرات
4. تحقق من اختفاء الصورة من الجدول

## الملفات المعدلة

### Backend:
1. `backend/models/category.py`
   - إضافة حقل `image_url` لنموذج Category

2. `backend/schemas/category.py`
   - إضافة `image_url` إلى CategoryBase و CategoryUpdate

3. `backend/migrations/add_category_image_url.py`
   - Migration لإضافة العمود الجديد

### Frontend:
1. `frontend/src/stores/categoryStore.ts`
   - إضافة `image_url` إلى واجهة Category

2. `frontend/src/components/catalog/CategoriesDataTable.tsx`
   - إضافة عمود الصورة في الجدول
   - إضافة قسم رفع الصور في النموذج
   - إضافة دوال معالجة رفع وحذف الصور

## التحقق من الأخطاء

- ✅ لا توجد أخطاء TypeScript في الملفات المعدلة
- ✅ تم تطبيق مبادئ البرمجة الكائنية
- ✅ استخدام خدمة إدارة الصور الموجودة
- ✅ دعم رفع صورة واحدة فقط للفئة
- ✅ عرض الصور في الجدول والنموذج
- ✅ حفظ مسار الصورة في قاعدة البيانات
- ✅ تم تشغيل migration بنجاح

## ملاحظات

- يتم حفظ الصور في مجلد `static/uploads/categories/`
- يتم إنشاء صور مصغرة تلقائياً
- مسار الصورة يُحفظ في حقل `image_url` في قاعدة البيانات
- الخدمة تدعم جميع صيغ الصور المعتادة (JPG, PNG, GIF, WebP, BMP)
- الميزة تطبق فقط على الفئات الرئيسية وليس الفرعية

## مقارنة مع العلامات التجارية

تم تطبيق نفس النهج المستخدم في العلامات التجارية:
- ✅ نفس مكون رفع الصور
- ✅ نفس طريقة عرض الصور في الجدول
- ✅ نفس واجهة إدارة الصور في النموذج
- ✅ نفس معالجة الأخطاء والحالات الاستثنائية
