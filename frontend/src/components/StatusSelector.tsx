import React from 'react';
import { FaPlay, FaPause, FaStop, FaInfoCircle } from 'react-icons/fa';

interface StatusOption {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  bgColor: string;
}

interface StatusSelectorProps {
  value: string;
  onChange: (status: string) => void;
  className?: string;
  disabled?: boolean;
  layout?: 'horizontal' | 'vertical';
}

const StatusSelector: React.FC<StatusSelectorProps> = ({
  value,
  onChange,
  className = '',
  disabled = false,
  layout = 'horizontal'
}) => {
  const statusOptions: StatusOption[] = [
    {
      id: 'active',
      name: 'نشط',
      description: 'المهمة تعمل وفقاً للجدولة المحددة',
      icon: <FaPlay className="text-green-600" />,
      color: 'text-green-700 dark:text-green-300',
      bgColor: 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
    },
    {
      id: 'paused',
      name: 'متوقف مؤقتاً',
      description: 'المهمة متوقفة مؤقتاً ويمكن تفعيلها لاحقاً',
      icon: <FaPause className="text-yellow-600" />,
      color: 'text-yellow-700 dark:text-yellow-300',
      bgColor: 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'
    },
    {
      id: 'disabled',
      name: 'معطل',
      description: 'المهمة معطلة ولن تعمل حتى يتم تفعيلها',
      icon: <FaStop className="text-red-600" />,
      color: 'text-red-700 dark:text-red-300',
      bgColor: 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
    }
  ];

  const selectedStatus = statusOptions.find(status => status.id === value);

  const getLayoutClasses = () => {
    return layout === 'horizontal' 
      ? 'grid grid-cols-1 md:grid-cols-3 gap-3'
      : 'space-y-3';
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className={getLayoutClasses()}>
        {statusOptions.map((status) => (
          <button
            key={status.id}
            type="button"
            onClick={() => !disabled && onChange(status.id)}
            disabled={disabled}
            className={`
              p-4 rounded-lg border-2 text-right transition-all duration-200 hover:shadow-md
              ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
              ${value === status.id
                ? `border-primary-500 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 shadow-md`
                : `border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300`
              }
            `}
          >
            <div className="flex items-start">
              <div className="flex-shrink-0 ml-3">
                {status.icon}
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-semibold text-base">{status.name}</h3>
                  {value === status.id && (
                    <div className="w-3 h-3 bg-primary-500 rounded-full"></div>
                  )}
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {status.description}
                </p>
              </div>
            </div>
          </button>
        ))}
      </div>

      {/* معلومات إضافية للحالة المحددة */}
      {selectedStatus && (
        <div className={`p-4 rounded-lg border ${selectedStatus.bgColor}`}>
          <h4 className={`font-medium mb-2 flex items-center ${selectedStatus.color}`}>
            <FaInfoCircle className="ml-2" />
            معلومات الحالة: {selectedStatus.name}
          </h4>
          
          <div className={`text-sm space-y-1 ${selectedStatus.color}`}>
            {selectedStatus.id === 'active' && (
              <>
                <p>• المهمة ستعمل تلقائياً وفقاً للجدولة المحددة</p>
                <p>• سيتم تسجيل جميع عمليات التشغيل والأخطاء</p>
                <p>• يمكن إيقافها مؤقتاً أو تعطيلها في أي وقت</p>
              </>
            )}
            
            {selectedStatus.id === 'paused' && (
              <>
                <p>• المهمة متوقفة مؤقتاً ولن تعمل</p>
                <p>• يمكن تفعيلها مرة أخرى بسهولة</p>
                <p>• الجدولة محفوظة ولن تتأثر</p>
              </>
            )}
            
            {selectedStatus.id === 'disabled' && (
              <>
                <p>• المهمة معطلة بالكامل</p>
                <p>• لن تعمل حتى يتم تفعيلها يدوياً</p>
                <p>• مناسب للمهام التي لا تحتاجها حالياً</p>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default StatusSelector;
