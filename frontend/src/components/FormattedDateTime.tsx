/**
 * مكونات لعرض التاريخ والوقت مع الإعدادات الموحدة
 * تستخدم إعدادات التاريخ والوقت المخزنة في قاعدة البيانات
 * تطبق مبادئ البرمجة الكائنية والتصميم الموحد للنظام
 */

import React, { useState, useEffect } from 'react';
import { FaClock, FaCalendarAlt } from 'react-icons/fa';
import { useDateTimeFormatters } from '../hooks/useDateTimeSettings';

interface FormattedDateTimeProps {
  date: string | Date;
  showTime?: boolean;
  showIcon?: boolean;
  className?: string;
  fallback?: string;
}

interface FormattedDateProps {
  date: string | Date;
  className?: string;
  fallback?: string;
}

interface FormattedTimeProps {
  date: string | Date;
  className?: string;
  fallback?: string;
}

interface LastUpdateTimeProps {
  updateTime: Date;
  className?: string;
}

/**
 * مكون لعرض التاريخ والوقت مع الإعدادات الموحدة
 */
export const FormattedDateTime: React.FC<FormattedDateTimeProps> = ({ 
  date, 
  showTime = false, 
  showIcon = false,
  className = '',
  fallback = 'غير محدد'
}) => {
  const [formattedDate, setFormattedDate] = useState<string>('');
  const [formattedTime, setFormattedTime] = useState<string>('');
  const { formatDate, formatTime } = useDateTimeFormatters();

  useEffect(() => {
    const formatDates = async () => {
      if (!date) {
        setFormattedDate(fallback);
        setFormattedTime(fallback);
        return;
      }

      try {
        const dateFormatted = await formatDate(date);
        setFormattedDate(dateFormatted);
        
        if (showTime) {
          const timeFormatted = await formatTime(date);
          setFormattedTime(timeFormatted);
        }
      } catch (error) {
        console.error('Error formatting date:', error);
        setFormattedDate(fallback);
        setFormattedTime(fallback);
      }
    };

    formatDates();
  }, [date, showTime, formatDate, formatTime, fallback]);

  if (showTime) {
    return (
      <div className={`flex flex-col ${className}`}>
        <div className="flex items-center">
          {showIcon && <FaCalendarAlt className="ml-1 text-xs" />}
          <span>{formattedDate}</span>
        </div>
        <div className="flex items-center mt-0.5">
          <FaClock className="ml-1 text-xs text-secondary-500 dark:text-secondary-400" />
          <span className="text-xs text-secondary-500 dark:text-secondary-400">
            {formattedTime}
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex items-center ${className}`}>
      {showIcon && <FaCalendarAlt className="ml-1 text-xs" />}
      <span>{formattedDate}</span>
    </div>
  );
};

/**
 * مكون لعرض التاريخ فقط مع الإعدادات الموحدة
 */
export const FormattedDate: React.FC<FormattedDateProps> = ({ 
  date, 
  className = '',
  fallback = 'غير محدد'
}) => {
  const [formattedDate, setFormattedDate] = useState<string>('');
  const { formatDate } = useDateTimeFormatters();

  useEffect(() => {
    const formatDateAsync = async () => {
      if (!date) {
        setFormattedDate(fallback);
        return;
      }

      try {
        const formatted = await formatDate(date);
        setFormattedDate(formatted);
      } catch (error) {
        console.error('Error formatting date:', error);
        setFormattedDate(fallback);
      }
    };

    formatDateAsync();
  }, [date, formatDate, fallback]);

  return <span className={className}>{formattedDate}</span>;
};

/**
 * مكون لعرض الوقت فقط مع الإعدادات الموحدة
 */
export const FormattedTime: React.FC<FormattedTimeProps> = ({ 
  date, 
  className = '',
  fallback = 'غير محدد'
}) => {
  const [formattedTime, setFormattedTime] = useState<string>('');
  const { formatTime } = useDateTimeFormatters();

  useEffect(() => {
    const formatTimeAsync = async () => {
      if (!date) {
        setFormattedTime(fallback);
        return;
      }

      try {
        const formatted = await formatTime(date);
        setFormattedTime(formatted);
      } catch (error) {
        console.error('Error formatting time:', error);
        setFormattedTime(fallback);
      }
    };

    formatTimeAsync();
  }, [date, formatTime, fallback]);

  return <span className={className}>{formattedTime}</span>;
};

/**
 * مكون لعرض آخر تحديث مع الإعدادات الموحدة
 */
export const LastUpdateTime: React.FC<LastUpdateTimeProps> = ({ 
  updateTime, 
  className = ''
}) => {
  const [formattedTime, setFormattedTime] = useState<string>('');
  const { formatDateTime } = useDateTimeFormatters();

  useEffect(() => {
    const formatTime = async () => {
      try {
        const formatted = await formatDateTime(updateTime);
        setFormattedTime(formatted);
      } catch (error) {
        console.error('Error formatting update time:', error);
        setFormattedTime(updateTime.toLocaleString('ar-LY'));
      }
    };

    formatTime();
  }, [updateTime, formatDateTime]);

  return <span className={className}>آخر تحديث: {formattedTime}</span>;
};

/**
 * Hook للحصول على التاريخ الحالي منسق
 */
export const useCurrentFormattedDateTime = () => {
  const { formatDateTime } = useDateTimeFormatters();

  const getCurrentFormattedDateTime = async (): Promise<string> => {
    try {
      return await formatDateTime(new Date());
    } catch (error) {
      console.error('Error formatting current date:', error);
      return new Date().toLocaleString('ar-LY');
    }
  };

  return { getCurrentFormattedDateTime };
};

export default FormattedDateTime;
