/**
 * مكون مؤشر المقارنة الصغير
 * يعرض مؤشر صغير مع أيقونة صعود أو هبوط ونسبة التغيير
 * يتبع التصميم الموحد للنظام ويدعم الوضع المظلم
 */

import React from 'react';
import { FiTrendingUp, FiTrendingDown, FiMinus } from 'react-icons/fi';
import { ComparisonData } from '../services/comparisonIndicatorService';

interface ComparisonIndicatorProps {
  /** بيانات المقارنة */
  comparison: ComparisonData;
  /** نص المقارنة (مثل "مقارنة بمبيعات أمس") */
  comparisonText: string;
  /** فئة CSS إضافية */
  className?: string;
  /** إظهار النسبة المئوية (افتراضي: true) */
  showPercentage?: boolean;
  /** إظهار القيمة المطلقة للفرق (افتراضي: false) */
  showDifference?: boolean;
  /** حجم المؤشر */
  size?: 'small' | 'medium' | 'large';
}

const ComparisonIndicator: React.FC<ComparisonIndicatorProps> = ({
  comparison,
  comparisonText,
  className = '',
  showPercentage = true,
  showDifference = false,
  size = 'small'
}) => {
  // تحديد الأيقونة بناءً على الاتجاه مع دائرة ملونة صغيرة
  const getTrendIcon = () => {
    const iconSize = size === 'small' ? 'w-2.5 h-2.5' : size === 'medium' ? 'w-3 h-3' : 'w-3.5 h-3.5';
    const circleSize = size === 'small' ? 'w-5 h-5' : size === 'medium' ? 'w-6 h-6' : 'w-7 h-7';

    switch (comparison.trend) {
      case 'up':
        return (
          <div className={`${circleSize} rounded-xl bg-green-100 dark:bg-green-900/30 flex items-center justify-center`}>
            <FiTrendingUp className={`${iconSize} text-green-600 dark:text-green-400`} />
          </div>
        );
      case 'down':
        return (
          <div className={`${circleSize} rounded-xl bg-red-100 dark:bg-red-900/30 flex items-center justify-center`}>
            <FiTrendingDown className={`${iconSize} text-red-600 dark:text-red-400`} />
          </div>
        );
      case 'neutral':
      default:
        return (
          <div className={`${circleSize} rounded-xl bg-gray-100 dark:bg-gray-700 flex items-center justify-center`}>
            <FiMinus className={`${iconSize} text-gray-600 dark:text-gray-400`} />
          </div>
        );
    }
  };

  // تحديد لون النص بناءً على الاتجاه
  const getTrendColor = () => {
    switch (comparison.trend) {
      case 'up':
        return 'text-green-600 dark:text-green-400';
      case 'down':
        return 'text-red-600 dark:text-red-400';
      case 'neutral':
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  // تنسيق النسبة المئوية بدون فاصلة عشرية وبدون علامات + أو -
  const formatPercentage = (percentage: number): string => {
    return `${Math.abs(percentage).toFixed(0)}%`;
  };

  // التحقق من وجود بيانات للمقارنة
  const hasData = comparison.previous !== 0 || comparison.current !== 0;

  // رمز عدم وجود بيانات
  const getNoDataSymbol = (): string => {
    return "-";
  };

  // تنسيق الفرق
  const formatDifference = (difference: number): string => {
    const sign = difference > 0 ? '+' : '';
    return `${sign}${difference.toLocaleString('ar-LY', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    })}`;
  };

  // تحديد حجم النص - موحد مع باقي البطاقات
  const getTextSize = () => {
    // استخدام نفس حجم النص في جميع البطاقات
    return 'text-sm';
  };

  // إظهار المؤشر دائماً حتى لو لم تكن هناك بيانات
  // في حالة عدم وجود بيانات، سنعرض مؤشر محايد مع رسالة مناسبة

  return (
    <div className={`flex items-center justify-between ${className}`}>
      {/* نص المقارنة */}
      <span className={`${getTextSize()} text-gray-500 dark:text-gray-400 font-medium`}>
        {comparisonText}
      </span>

      {/* المؤشر والنسبة */}
      <div className={`flex items-center gap-1.5`}>
        {getTrendIcon()}

        {/* النسبة المئوية أو الفرق أو رسالة عدم وجود بيانات */}
        <span className={`${getTextSize()} font-semibold ${getTrendColor()}`}>
          {hasData ? (
            <>
              {showPercentage && formatPercentage(comparison.percentageChange)}
              {showDifference && !showPercentage && formatDifference(comparison.difference)}
              {showDifference && showPercentage && ` (${formatDifference(comparison.difference)})`}
            </>
          ) : (
            <span className="text-gray-500 dark:text-gray-400 font-normal">
              {getNoDataSymbol()}
            </span>
          )}
        </span>
      </div>
    </div>
  );
};

export default ComparisonIndicator;
