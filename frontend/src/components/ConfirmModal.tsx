import React from 'react';
import { FaExclamationTriangle, FaDatabase, FaSync, FaTrash } from 'react-icons/fa';
import { Ban, AlertCircle, X, CheckCircle } from './ui/icons';
import Modal from './Modal';

interface ConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  description?: string;
  type: 'backup' | 'update' | 'clear-cache' | 'danger' | 'block';
  isLoading?: boolean;
  confirmText?: string;
  cancelText?: string;
}

const ConfirmModal: React.FC<ConfirmModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  description,
  type,
  isLoading = false,
  confirmText,
  cancelText
}) => {
  const getIcon = () => {
    switch (type) {
      case 'backup':
        return <FaDatabase className="h-8 w-8 text-primary-600 dark:text-primary-400" />;
      case 'update':
        return <FaSync className="h-8 w-8 text-blue-600 dark:text-blue-400" />;
      case 'clear-cache':
        return <FaTrash className="h-8 w-8 text-orange-600 dark:text-orange-400" />;
      case 'block':
        return <Ban className="h-8 w-8 text-red-600 dark:text-red-400" size={32} />;
      case 'danger':
        return <AlertCircle className="h-8 w-8 text-red-600 dark:text-red-400" size={32} />;
      default:
        return <FaExclamationTriangle className="h-8 w-8 text-gray-600 dark:text-gray-400" />;
    }
  };

  const getIconBg = () => {
    switch (type) {
      case 'backup':
        return 'bg-primary-100 dark:bg-primary-900/30';
      case 'update':
        return 'bg-blue-100 dark:bg-blue-900/30';
      case 'clear-cache':
        return 'bg-orange-100 dark:bg-orange-900/30';
      case 'block':
        return 'bg-red-100 dark:bg-red-900/30';
      case 'danger':
        return 'bg-red-100 dark:bg-red-900/30';
      default:
        return 'bg-gray-100 dark:bg-gray-900/30';
    }
  };

  const getButtonClass = () => {
    switch (type) {
      case 'backup':
        return 'btn-primary';
      case 'update':
        return 'btn-info';
      case 'clear-cache':
        return 'btn-warning';
      case 'block':
        return 'bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors disabled:opacity-50';
      case 'danger':
        return 'bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors disabled:opacity-50';
      default:
        return 'btn-secondary';
    }
  };

  const getButtonText = () => {
    if (confirmText) {
      return isLoading ? 'جاري التنفيذ...' : confirmText;
    }

    switch (type) {
      case 'backup':
        return isLoading ? 'جاري الإنشاء...' : 'إنشاء النسخة';
      case 'update':
        return isLoading ? 'جاري التحديث...' : 'تحديث النظام';
      case 'clear-cache':
        return isLoading ? 'جاري المسح...' : 'مسح التخزين';
      case 'block':
        return isLoading ? 'جاري الحظر...' : 'حظر الجهاز';
      case 'danger':
        return isLoading ? 'جاري التنفيذ...' : 'تأكيد';
      default:
        return isLoading ? 'جاري التنفيذ...' : 'تأكيد';
    }
  };

  const getConfirmButtonIcon = () => {
    switch (type) {
      case 'backup':
        return <FaDatabase className="h-4 w-4" />;
      case 'update':
        return <FaSync className="h-4 w-4" />;
      case 'clear-cache':
        return <FaTrash className="h-4 w-4" />;
      case 'block':
        return <Ban className="h-4 w-4" size={16} />;
      case 'danger':
        return <AlertCircle className="h-4 w-4" size={16} />;
      default:
        return <CheckCircle className="h-4 w-4" size={16} />;
    }
  };

  const getCancelButtonIcon = () => {
    return <X className="h-4 w-4" size={16} />;
  };

  const getWarningInfo = () => {
    switch (type) {
      case 'backup':
        return {
          title: 'سيتم تنفيذ العمليات التالية:',
          items: [
            '• إنشاء نسخة احتياطية من قاعدة البيانات الحالية',
            '• حفظ النسخة في مجلد النسخ الاحتياطية',
            '• تحديث إحصائيات النظام',
            '• عرض تفاصيل النسخة الجديدة'
          ],
          note: 'هذه العملية آمنة ولا تؤثر على البيانات الحالية'
        };
      case 'update':
        return {
          title: 'سيتم تنفيذ العمليات التالية:',
          items: [
            '• فحص التحديثات المتاحة',
            '• تحديث ملفات النظام',
            '• إعادة تشغيل الخدمات المطلوبة',
            '• التحقق من سلامة النظام'
          ],
          note: 'قد يستغرق التحديث بضع دقائق'
        };
      case 'clear-cache':
        return {
          title: 'سيتم تنفيذ العمليات التالية:',
          items: [
            '• مسح جميع بيانات التخزين المحلي',
            '• مسح بيانات الجلسة المؤقتة',
            '• إعادة تحميل الصفحة تلقائياً',
            '• إعادة تسجيل الدخول مطلوبة'
          ],
          note: '⚠️ ستحتاج لإعادة تسجيل الدخول بعد هذه العملية'
        };
      case 'block':
        return {
          title: 'سيتم تنفيذ العمليات التالية:',
          items: [
            '• منع الجهاز من الوصول إلى النظام نهائياً',
            '• حذف الجهاز من قائمة الأجهزة المعتمدة',
            '• إضافة الجهاز إلى قائمة الأجهزة المحظورة',
            '• تسجيل عملية الحظر في سجل النشاط',
            '• إشعار فوري لجميع المديرين'
          ],
          note: '🔒 يمكن إلغاء الحظر لاحقاً من إعدادات أمان الأجهزة'
        };
      default:
        return null;
    }
  };

  const warningInfo = getWarningInfo();

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={title} size="md" zIndex="highest">
      <div className="text-center">
        {/* Icon */}
        <div className={`mx-auto flex h-16 w-16 items-center justify-center rounded-full ${getIconBg()} mb-6`}>
          {getIcon()}
        </div>

        {/* Message */}
        <div className="mb-6">
          <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
            {message}
          </p>
          {description && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mb-4">
              {description}
            </p>
          )}
        </div>

        {/* Warning Details */}
        {warningInfo && (
          <div className="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4 mb-6">
            <h5 className="text-sm font-semibold text-gray-800 dark:text-gray-200 mb-2">
              {warningInfo.title}
            </h5>
            <ul className="text-xs text-gray-700 dark:text-gray-300 space-y-1 text-right mb-3">
              {warningInfo.items.map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
            <p className="text-xs font-medium text-gray-600 dark:text-gray-400">
              {warningInfo.note}
            </p>
          </div>
        )}

        {/* Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 sm:gap-3 justify-center">
          <button
            onClick={onClose}
            disabled={isLoading}
            className="btn-secondary flex items-center justify-center min-w-[120px]"
          >
            {getCancelButtonIcon()}
            <span className="mr-2">{cancelText || 'إلغاء'}</span>
          </button>
          <button
            onClick={onConfirm}
            disabled={isLoading}
            className={`${getButtonClass()} flex items-center justify-center min-w-[140px]`}
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                <span>{getButtonText()}</span>
              </>
            ) : (
              <>
                {getConfirmButtonIcon()}
                <span className="mr-2">{getButtonText()}</span>
              </>
            )}
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default ConfirmModal;
