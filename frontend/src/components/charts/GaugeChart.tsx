/**
 * مكون الرسم البياني الدائري (Gauge Chart)
 * يعرض النسب المئوية والمؤشرات بطريقة بصرية جذابة
 * يدعم الوضع المظلم والتصميم الموحد للنظام
 */

import React from 'react';
import ReactApexChart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';

interface GaugeChartProps {
  /** القيمة المراد عرضها (0-100) */
  value: number;
  /** العنوان */
  title: string;
  /** النص الفرعي */
  subtitle?: string;
  /** اللون الأساسي */
  color?: string;
  /** الحد الأدنى للقيمة */
  min?: number;
  /** الحد الأقصى للقيمة */
  max?: number;
  /** الارتفاع */
  height?: number;
  /** العرض */
  width?: string;
  /** إظهار التسميات */
  showLabels?: boolean;
  /** تفعيل الرسوم المتحركة */
  enableAnimation?: boolean;
  /** فئة CSS إضافية */
  className?: string;
  /** نوع المؤشر */
  variant?: 'success' | 'warning' | 'danger' | 'info' | 'primary';
  /** إظهار النسبة المئوية */
  showPercentage?: boolean;
}

const GaugeChart: React.FC<GaugeChartProps> = ({
  value,
  title,
  subtitle,
  color,
  min = 0,
  max = 100,
  height = 200,
  width = '100%',
  showLabels = true,
  enableAnimation = true,
  className = '',

  showPercentage = true
}) => {
  // تحديد لون الخلفية حسب القيمة
  const getBackgroundColor = (value: number): string => {
    if (value >= 80) return '#10b981'; // أخضر
    if (value >= 60) return '#f59e0b'; // أصفر
    if (value >= 40) return '#f97316'; // برتقالي
    return '#ef4444'; // أحمر
  };

  // استخدام اللون التلقائي إذا لم يتم تحديد لون
  const chartColor = color || getBackgroundColor(value);

  // إعدادات الرسم البياني
  const options: ApexOptions = {
    chart: {
      type: 'radialBar',
      fontFamily: 'Almarai, sans-serif',
      animations: {
        enabled: enableAnimation,
        speed: 800,
        animateGradually: {
          enabled: true,
          delay: 150
        },
        dynamicAnimation: {
          enabled: true,
          speed: 350
        }
      },
      sparkline: {
        enabled: false
      }
    },
    plotOptions: {
      radialBar: {
        startAngle: -135,
        endAngle: 135,
        hollow: {
          margin: 0,
          size: '70%',
          background: 'transparent',
          image: undefined,
          position: 'front',
          dropShadow: {
            enabled: true,
            top: 3,
            left: 0,
            blur: 4,
            opacity: 0.24
          }
        },
        track: {
          background: '#e5e7eb',
          strokeWidth: '67%',
          margin: 0,
          dropShadow: {
            enabled: true,
            top: -3,
            left: 0,
            blur: 4,
            opacity: 0.35
          }
        },
        dataLabels: {
          show: showLabels,
          name: {
            offsetY: -10,
            show: true,
            color: '#374151',
            fontSize: '14px',
            fontWeight: '600'
          },
          value: {
            formatter: function(val: number) {
              return showPercentage ? `${Math.round(val)}%` : Math.round(val).toString();
            },
            color: '#111827',
            fontSize: '24px',
            fontWeight: 'bold',
            show: true,
            offsetY: 16
          }
        }
      }
    },
    fill: {
      type: 'gradient',
      gradient: {
        shade: 'dark',
        type: 'horizontal',
        shadeIntensity: 0.5,
        gradientToColors: [chartColor],
        inverseColors: true,
        opacityFrom: 1,
        opacityTo: 1,
        stops: [0, 100]
      }
    },
    stroke: {
      lineCap: 'round'
    },
    labels: [title],
    colors: [chartColor],
    responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: height * 0.8
          },
          plotOptions: {
            radialBar: {
              dataLabels: {
                name: {
                  fontSize: '12px'
                },
                value: {
                  fontSize: '20px'
                }
              }
            }
          }
        }
      }
    ]
  };

  // البيانات
  const series = [Math.min(max, Math.max(min, value))];

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
      {/* العنوان */}
      <div className="text-center mb-4">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100">
          {title}
        </h3>
        {subtitle && (
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {subtitle}
          </p>
        )}
      </div>

      {/* الرسم البياني */}
      <div className="flex justify-center">
        <ReactApexChart
          type="radialBar"
          height={height}
          width={width}
          options={options}
          series={series}
        />
      </div>

      {/* معلومات إضافية */}
      <div className="text-center mt-4">
        <div className="flex justify-center items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
          <span>الحد الأدنى: {min}</span>
          <span>الحد الأقصى: {max}</span>
        </div>
      </div>
    </div>
  );
};

export default GaugeChart;
