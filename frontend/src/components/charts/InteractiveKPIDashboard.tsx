/**
 * لوحة مؤشرات الأداء التفاعلية
 * تجمع بين الرسوم البيانية والأرقام والمؤشرات البصرية
 * تدعم الوضع المظلم والتصميم الموحد للنظام
 */

import React, { useState } from 'react';
import {
  FiTrendingUp,
  FiTrendingDown,
  FiMinus,
  FiInfo,
  FiTarget,
  FiActivity,
  FiPercent,
  FiDollarSign,
  FiShield
} from 'react-icons/fi';
import GaugeChart from './GaugeChart';
import ProgressRing from './ProgressRing';
import FormattedCurrency from '../FormattedCurrency';

interface KPIData {
  id: string;
  title: string;
  value: number;
  target?: number;
  unit?: string;
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: number;
  description?: string;
  color?: string;
  variant?: 'success' | 'warning' | 'danger' | 'info' | 'primary';
  icon?: React.ReactNode;
  isCurrency?: boolean;
  isPercentage?: boolean;
}

interface InteractiveKPIDashboardProps {
  /** بيانات مؤشرات الأداء */
  kpis: KPIData[];
  /** العنوان الرئيسي */
  title?: string;
  /** النمط المفضل للعرض */
  displayMode?: 'cards' | 'gauges' | 'rings' | 'mixed';
  /** تفعيل الرسوم المتحركة */
  enableAnimation?: boolean;
  /** فئة CSS إضافية */
  className?: string;
  /** إظهار التفاصيل عند النقر */
  showDetails?: boolean;
  /** دالة معاودة عند النقر على مؤشر */
  onKPIClick?: (kpi: KPIData) => void;
}

const InteractiveKPIDashboard: React.FC<InteractiveKPIDashboardProps> = ({
  kpis,
  title = 'مؤشرات الأداء الرئيسية',
  displayMode = 'mixed',
  enableAnimation = true,
  className = '',
  showDetails = true,
  onKPIClick
}) => {
  const [selectedKPI, setSelectedKPI] = useState<string | null>(null);

  // دالة للحصول على أيقونة الاتجاه
  const getTrendIcon = (trend?: 'up' | 'down' | 'neutral') => {
    switch (trend) {
      case 'up':
        return <FiTrendingUp className="w-4 h-4 text-green-600 dark:text-green-400" />;
      case 'down':
        return <FiTrendingDown className="w-4 h-4 text-red-600 dark:text-red-400" />;
      case 'neutral':
      default:
        return <FiMinus className="w-4 h-4 text-gray-600 dark:text-gray-400" />;
    }
  };

  // دالة للحصول على لون الاتجاه
  const getTrendColor = (trend?: 'up' | 'down' | 'neutral') => {
    switch (trend) {
      case 'up':
        return 'text-green-600 dark:text-green-400';
      case 'down':
        return 'text-red-600 dark:text-red-400';
      case 'neutral':
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  // دالة لتنسيق القيمة
  const formatValue = (kpi: KPIData) => {
    if (kpi.isCurrency) {
      return <FormattedCurrency amount={kpi.value} className="text-2xl font-bold" />;
    }
    if (kpi.isPercentage) {
      return `${kpi.value.toFixed(1)}%`;
    }
    return `${kpi.value.toLocaleString()}${kpi.unit || ''}`;
  };

  // دالة للتعامل مع النقر على مؤشر
  const handleKPIClick = (kpi: KPIData) => {
    setSelectedKPI(selectedKPI === kpi.id ? null : kpi.id);
    if (onKPIClick) {
      onKPIClick(kpi);
    }
  };

  // عرض مؤشر كبطاقة
  const renderKPICard = (kpi: KPIData) => (
    <div
      key={kpi.id}
      onClick={() => handleKPIClick(kpi)}
      className={`bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 p-6 cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 ${
        selectedKPI === kpi.id ? 'ring-2 ring-primary-500 ring-opacity-50' : ''
      }`}
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          {kpi.icon && (
            <div className="p-2 bg-gray-100 dark:bg-gray-700 rounded-lg ml-3">
              {kpi.icon}
            </div>
          )}
          <div>
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">
              {kpi.title}
            </h3>
            <div className="text-2xl font-bold text-gray-900 dark:text-gray-100 mt-1">
              {formatValue(kpi)}
            </div>
          </div>
        </div>
        
        {kpi.trend && (
          <div className="flex items-center">
            {getTrendIcon(kpi.trend)}
            {kpi.trendValue && (
              <span className={`text-sm font-medium mr-1 ${getTrendColor(kpi.trend)}`}>
                {kpi.trendValue > 0 ? '+' : ''}{kpi.trendValue.toFixed(1)}%
              </span>
            )}
          </div>
        )}
      </div>

      {kpi.target && (
        <div className="mb-3">
          <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
            <span>التقدم نحو الهدف</span>
            <span>{((kpi.value / kpi.target) * 100).toFixed(1)}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="bg-primary-600 h-2 rounded-full transition-all duration-500"
              style={{ width: `${Math.min(100, (kpi.value / kpi.target) * 100)}%` }}
            />
          </div>
        </div>
      )}

      {showDetails && selectedKPI === kpi.id && kpi.description && (
        <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {kpi.description}
          </p>
        </div>
      )}
    </div>
  );

  // عرض مؤشر كـ Gauge
  const renderKPIGauge = (kpi: KPIData) => (
    <div key={kpi.id} onClick={() => handleKPIClick(kpi)}>
      <GaugeChart
        value={kpi.isPercentage ? kpi.value : (kpi.target ? (kpi.value / kpi.target) * 100 : kpi.value)}
        title={kpi.title}
        subtitle={kpi.description}
        variant={kpi.variant}
        color={kpi.color}
        enableAnimation={enableAnimation}
        className="cursor-pointer hover:scale-105 transition-transform duration-200"
      />
    </div>
  );

  // عرض مؤشر كـ Progress Ring
  const renderKPIRing = (kpi: KPIData) => (
    <div
      key={kpi.id}
      onClick={() => handleKPIClick(kpi)}
      className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 p-6 cursor-pointer hover:scale-105 transition-transform duration-200"
    >
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-4">
          {kpi.title}
        </h3>
        <ProgressRing
          value={kpi.isPercentage ? kpi.value : (kpi.target ? (kpi.value / kpi.target) * 100 : kpi.value)}
          variant={kpi.variant}
          color={kpi.color}
          enableAnimation={enableAnimation}
          icon={kpi.icon}
          subtitle={kpi.description}
        />
        <div className="mt-4">
          <div className="text-xl font-bold text-gray-900 dark:text-gray-100">
            {formatValue(kpi)}
          </div>
          {kpi.trend && kpi.trendValue && (
            <div className="flex items-center justify-center mt-2">
              {getTrendIcon(kpi.trend)}
              <span className={`text-sm font-medium mr-1 ${getTrendColor(kpi.trend)}`}>
                {kpi.trendValue > 0 ? '+' : ''}{kpi.trendValue.toFixed(1)}%
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className={`space-y-6 ${className}`}>
      {/* العنوان */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
          <FiTarget className="ml-2 text-primary-600 dark:text-primary-400" />
          {title}
        </h2>
        <div className="text-sm text-gray-600 dark:text-gray-400">
          {kpis.length} مؤشر
        </div>
      </div>

      {/* المؤشرات */}
      <div className={`grid gap-6 ${
        displayMode === 'cards' || displayMode === 'mixed'
          ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
          : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
      }`}>
        {kpis.map((kpi, index) => {
          if (displayMode === 'cards') {
            return renderKPICard(kpi);
          } else if (displayMode === 'gauges') {
            return renderKPIGauge(kpi);
          } else if (displayMode === 'rings') {
            return renderKPIRing(kpi);
          } else {
            // Mixed mode - alternate between different types
            const type = index % 3;
            if (type === 0) return renderKPICard(kpi);
            if (type === 1) return renderKPIGauge(kpi);
            return renderKPIRing(kpi);
          }
        })}
      </div>
    </div>
  );
};

export default InteractiveKPIDashboard;
