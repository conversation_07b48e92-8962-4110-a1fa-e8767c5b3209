/**
 * مكون حلقة التقدم (Progress Ring)
 * يعرض النسب المئوية بشكل دائري متحرك
 * يدعم الوضع المظلم والتصميم الموحد للنظام
 */

import React, { useState, useEffect } from 'react';

interface ProgressRingProps {
  /** القيمة المراد عرضها (0-100) */
  value: number;
  /** الحجم بالبكسل */
  size?: number;
  /** سماكة الحلقة */
  strokeWidth?: number;
  /** اللون الأساسي */
  color?: string;
  /** لون الخلفية */
  backgroundColor?: string;
  /** إظهار النسبة المئوية في المنتصف */
  showPercentage?: boolean;
  /** النص المخصص في المنتصف */
  centerText?: string;
  /** تفعيل الرسوم المتحركة */
  enableAnimation?: boolean;
  /** مدة الرسم المتحرك بالميلي ثانية */
  animationDuration?: number;
  /** فئة CSS إضافية */
  className?: string;
  /** نوع المؤشر */
  variant?: 'success' | 'warning' | 'danger' | 'info' | 'primary';
  /** إظهار الظل */
  showShadow?: boolean;
  /** النص الفرعي */
  subtitle?: string;
  /** الأيقونة في المنتصف */
  icon?: React.ReactNode;
}

const ProgressRing: React.FC<ProgressRingProps> = ({
  value,
  size = 120,
  strokeWidth = 8,
  color,
  backgroundColor = '#e5e7eb',
  showPercentage = true,
  centerText,
  enableAnimation = true,
  animationDuration = 1000,
  className = '',
  variant = 'primary',
  showShadow = true,
  subtitle,
  icon
}) => {
  const [animatedValue, setAnimatedValue] = useState(0);

  // تحديد الألوان حسب النوع - ألوان هادئة متناسقة مع التطبيق
  const getColorByVariant = (variant: string): string => {
    switch (variant) {
      case 'success':
        return '#059669'; // emerald-600 - أخضر هادئ
      case 'warning':
        return '#d97706'; // amber-600 - أصفر هادئ
      case 'danger':
        return '#dc2626'; // rose-600 - أحمر هادئ
      case 'info':
        return '#0284c7'; // sky-600 - أزرق هادئ
      case 'primary':
      default:
        return '#2563eb'; // blue-600 - أزرق أساسي هادئ
    }
  };

  // تحديد اللون النهائي
  const finalColor = color || getColorByVariant(variant);

  // حساب المعاملات
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (animatedValue / 100) * circumference;

  // تأثير الرسم المتحرك
  useEffect(() => {
    if (enableAnimation) {
      const startTime = Date.now();
      const startValue = animatedValue;
      const targetValue = Math.min(100, Math.max(0, value));

      const animate = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / animationDuration, 1);
        
        // استخدام easing function للحصول على حركة سلسة
        const easeOutCubic = 1 - Math.pow(1 - progress, 3);
        const currentValue = startValue + (targetValue - startValue) * easeOutCubic;
        
        setAnimatedValue(currentValue);

        if (progress < 1) {
          requestAnimationFrame(animate);
        }
      };

      requestAnimationFrame(animate);
    } else {
      setAnimatedValue(value);
    }
  }, [value, enableAnimation, animationDuration]);

  // تحديد لون الخلفية في الوضع المظلم
  const darkBackgroundColor = '#374151';

  return (
    <div className={`flex flex-col items-center ${className}`}>
      {/* الحلقة */}
      <div className="relative">
        <svg
          width={size}
          height={size}
          className={`transform -rotate-90 ${showShadow ? 'drop-shadow-lg' : ''}`}
        >
          {/* دائرة الخلفية */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={backgroundColor}
            strokeWidth={strokeWidth}
            fill="transparent"
            className="dark:stroke-gray-600"
          />
          
          {/* دائرة التقدم */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={finalColor}
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className="transition-all duration-300 ease-out"
            style={{
              filter: showShadow ? `drop-shadow(0 0 6px ${finalColor}40)` : 'none'
            }}
          />
        </svg>

        {/* المحتوى في المنتصف */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          {icon && (
            <div className="mb-1 text-gray-600 dark:text-gray-300">
              {icon}
            </div>
          )}
          
          {centerText ? (
            <span className="text-lg font-bold text-gray-800 dark:text-gray-100">
              {centerText}
            </span>
          ) : showPercentage ? (
            <span className="text-lg font-bold text-gray-800 dark:text-gray-100">
              {Math.round(animatedValue)}%
            </span>
          ) : null}
        </div>
      </div>

      {/* النص الفرعي */}
      {subtitle && (
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-2 text-center">
          {subtitle}
        </p>
      )}
    </div>
  );
};

export default ProgressRing;
