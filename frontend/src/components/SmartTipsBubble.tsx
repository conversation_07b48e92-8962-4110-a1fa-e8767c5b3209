/**
 * مكون فقاعة النصائح الذكية - SmartTipsBubble
 * يعرض النصائح التفاعلية بشكل جميل كأنها تأتي من الرجل في صورة الترحيب
 * 
 * الميزات:
 * - تصميم فقاعة محادثة جميلة
 * - تأثيرات بصرية متقدمة
 * - دعم الوضع المظلم والمضيء
 * - أيقونات من react-icons/fi
 * - تأثيرات الكتابة المتحركة
 * - إمكانية إغلاق وتأجيل النصائح
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  FiX,
  FiSun,
  FiMoon,
  FiZap,
  FiSearch,
  FiBarChart,
  FiPackage,
  FiTrendingUp,
  FiUsers,
  FiMessageCircle,
  FiShield
} from 'react-icons/fi';
import { FaLightbulb } from 'react-icons/fa';
import { SmartTip } from '../services/smartTipsService';

// خريطة الأيقونات
const iconMap = {
  FiSun,
  FiMoon,
  FiZap,
  FiSearch,
  FiBarChart,
  FiPackage,
  FiTrendingUp,
  FiUsers,
  FaLightbulb,
  FiMessageCircle,
  FiShield
};

interface SmartTipsBubbleProps {
  tip: SmartTip | null;
  onClose?: () => void;
  onSnooze?: (minutes: number) => void;
  className?: string;
  showAnimation?: boolean;
}

export const SmartTipsBubble: React.FC<SmartTipsBubbleProps> = ({
  tip,
  onClose,
  className = '',
  showAnimation = true
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [displayedText, setDisplayedText] = useState('');
  const bubbleRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  // تأثير الظهور والاختفاء
  useEffect(() => {
    if (tip) {
      setIsVisible(true);
      if (showAnimation) {
        startTypingAnimation();
      } else {
        // تنظيف النص من أي قيم undefined
        const cleanMessage = String(tip.message || '').replace(/undefined/g, '').trim();
        setDisplayedText(cleanMessage);
      }
    } else {
      setIsVisible(false);
      setDisplayedText('');
    }

    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [tip, showAnimation]);

  /**
   * بدء تأثير الكتابة المتحركة
   */
  const startTypingAnimation = () => {
    if (!tip || !tip.message) return;

    setIsTyping(true);
    setDisplayedText('');

    // طريقة أكثر أمانًا للتعامل مع النص وتجنب "undefined"
    const message = (typeof tip.message === 'string' ? tip.message : '').trim();
    let currentIndex = 0;

    const typeNextCharacter = () => {
      if (currentIndex < message.length) {
        // استخدام substring لضمان عدم إضافة محتوى غير مرغوب فيه
        setDisplayedText(message.substring(0, currentIndex + 1));
        currentIndex++;
        
        const delay = message[currentIndex - 1] === ' ' ? 15 : 25;
        typingTimeoutRef.current = setTimeout(typeNextCharacter, delay);
      } else {
        setIsTyping(false);
      }
    };

    // بدء الكتابة بعد تأخير قصير
    typingTimeoutRef.current = setTimeout(typeNextCharacter, 200);
  };

  /**
   * معالجة إغلاق النصيحة
   */
  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => {
      onClose?.();
    }, 300);
  };



  /**
   * الحصول على الأيقونة المناسبة
   */
  const getTipIcon = () => {
    try {
      if (!tip?.icon) return <FaLightbulb className="w-4 h-4" />;

      const IconComponent = iconMap[tip.icon as keyof typeof iconMap];
      return IconComponent ? <IconComponent className="w-4 h-4" /> : <FaLightbulb className="w-4 h-4" />;
    } catch (error) {
      console.error('خطأ في تحميل الأيقونة:', error);
      return <FaLightbulb className="w-4 h-4" />;
    }
  };

  /**
   * الحصول على لون النصيحة حسب الفئة
   */
  const getTipColor = () => {
    if (!tip) return 'primary';
    
    switch (tip.category) {
      case 'welcome': return 'primary';
      case 'productivity': return 'success';
      case 'features': return 'info';
      case 'shortcuts': return 'warning';
      case 'motivation': return 'success';
      case 'business': return 'primary';
      default: return 'primary';
    }
  };

  /**
   * الحصول على أنماط الموضع
   */
  const getPositionStyles = () => {
    // تصميم أفقي يتمدد حسب النص
    return 'relative z-50 w-max min-w-fit max-w-lg';
  };

  if (!tip || !isVisible) {
    return null;
  }

  const tipColor = getTipColor();

  return (
    <div
      ref={bubbleRef}
      className={`
        ${getPositionStyles()}
        ${className}
        transform transition-all duration-500 ease-out animate-gentle-bounce
        ${isVisible ? 'translate-y-0 opacity-100 scale-100' : 'translate-y-4 opacity-0 scale-95'}
      `}
    >
      {/* فقاعة النصيحة البسيطة والأفقية */}
      <div className="relative">
        {/* الفقاعة الأفقية البسيطة */}
        <div className={`
          relative bg-white dark:bg-gray-800 rounded-lg shadow-md border
          ${tipColor === 'primary' ? 'border-primary-200 dark:border-primary-700' : ''}
          ${tipColor === 'success' ? 'border-success-200 dark:border-success-700' : ''}
          ${tipColor === 'info' ? 'border-info-200 dark:border-info-700' : ''}
          ${tipColor === 'warning' ? 'border-warning-200 dark:border-warning-700' : ''}
          px-2 py-1.5 flex items-center gap-2 w-max min-w-fit max-w-xs
          hover:shadow-lg transition-all duration-300
        `}>

          {/* الأيقونة فقط */}
          <div className={`
            flex-shrink-0 flex items-center justify-center
            ${tipColor === 'primary' ? 'text-primary-600 dark:text-primary-400' : ''}
            ${tipColor === 'success' ? 'text-success-600 dark:text-success-400' : ''}
            ${tipColor === 'info' ? 'text-info-600 dark:text-info-400' : ''}
            ${tipColor === 'warning' ? 'text-warning-600 dark:text-warning-400' : ''}
          `}>
            {getTipIcon()}
          </div>

          {/* النص المصغر */}
          <div className="flex-1 min-w-0">
            <p className="text-gray-800 dark:text-gray-200 text-xs font-medium whitespace-nowrap">
              {displayedText}
              {isTyping && (
                <span className="inline-block w-0.5 h-3 bg-primary-500 ml-1 animate-pulse" />
              )}
            </p>
          </div>

          {/* زر الإغلاق المصغر */}
          <button
            onClick={handleClose}
            className="p-0.5 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex-shrink-0 ml-1"
            aria-label="إغلاق النصيحة"
          >
            <FiX className="w-3 h-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" />
          </button>

        </div>

        {/* سهم يشير إلى الصورة من الجهة اليسرى */}
        <div
          className={`
            absolute w-3 h-3 transform
            bg-white dark:bg-gray-800 border-t border-l
            ${tipColor === 'primary' ? 'border-primary-200 dark:border-primary-700' : ''}
            ${tipColor === 'success' ? 'border-success-200 dark:border-success-700' : ''}
            ${tipColor === 'info' ? 'border-info-200 dark:border-info-700' : ''}
            ${tipColor === 'warning' ? 'border-warning-200 dark:border-warning-700' : ''}
          `}
          style={{
            left: '-6px',
            top: '9px',
            transform: 'rotate(-45deg)'
          }}
        />
      </div>
    </div>
  );
};

export default SmartTipsBubble;
