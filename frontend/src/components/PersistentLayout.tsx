import React, { useEffect, useMemo, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { useSidebarStore } from '../stores/sidebarStore';
import { useAutoUserActivity } from '../hooks/useUserActivity';
import { useAuthStore } from '../stores/authStore';
import { layoutCacheService } from '../services/layoutCacheService';
import Sidebar from './Sidebar';
import Topbar from './Topbar';
import ScrollToTopButton from './ScrollToTopButton';
import ChatNotificationManager from './Chat/ChatNotificationManager';

interface PersistentLayoutProps {
  children: React.ReactNode;
}

/**
 * Layout مستمر لا يعاد تحميله عند التنقل
 * يحتوي على القائمة الجانبية والشريط العلوي الثابتين
 * مع منطقة محتوى ديناميكية
 */
const PersistentLayout: React.FC<PersistentLayoutProps> = ({ children }) => {
  const location = useLocation();
  const { user } = useAuthStore();
  const {
    isOpen,
    setActiveMenuItem,
    setActiveSubMenuItem,
    menuItems,
    activeMenuItem,
    activeSubMenuItem,
    expandedMenus
  } = useSidebarStore();

  // مرجع لتتبع التحميل الأولي
  const initialLoadDone = useRef(false);

  // تفعيل تتبع نشاط المستخدم التلقائي
  useAutoUserActivity();

  // استرجاع حالة Layout المحفوظة عند التحميل الأولي
  useEffect(() => {
    if (initialLoadDone.current) return;

    const cachedState = layoutCacheService.getLayoutState();
    if (cachedState) {
      console.log('🔄 [PersistentLayout] استرجاع حالة Layout من الكاش');

      // استرجاع حالة القائمة الجانبية
      if (cachedState.sidebarState.activeMenuItem) {
        setActiveMenuItem(cachedState.sidebarState.activeMenuItem);
      }
      if (cachedState.sidebarState.activeSubMenuItem) {
        setActiveSubMenuItem(cachedState.sidebarState.activeSubMenuItem);
      }
    }

    initialLoadDone.current = true;
  }, [setActiveMenuItem, setActiveSubMenuItem]);

  // حفظ حالة Layout في الكاش عند التغيير
  useEffect(() => {
    if (!initialLoadDone.current) return;

    const sidebarState = {
      isOpen,
      activeMenuItem,
      activeSubMenuItem,
      expandedMenus
    };

    const topbarState = {
      searchQuery: '',
      showUserMenu: false,
      showQuickActions: false
    };

    layoutCacheService.cacheLayoutState(sidebarState, topbarState);
  }, [isOpen, activeMenuItem, activeSubMenuItem, expandedMenus]);

  // تحديث القائمة النشطة بناءً على المسار الحالي
  // استخدام useMemo لتحسين الأداء
  const activeMenuInfo = useMemo(() => {
    const currentPath = location.pathname;
    
    // البحث عن القائمة النشطة
    for (const item of menuItems) {
      // فحص المسار الرئيسي
      if (item.path === currentPath) {
        return { mainItem: item.id, subItem: null };
      }
      
      // فحص القوائم الفرعية
      if (item.subItems) {
        for (const subItem of item.subItems) {
          if (subItem.path === currentPath) {
            return { mainItem: item.id, subItem: subItem.id };
          }
        }
      }
      
      // فحص المسارات المتشابهة (مثل /products/123)
      if (currentPath.startsWith(item.path) && item.path !== '/') {
        return { mainItem: item.id, subItem: null };
      }
    }
    
    // افتراضي: لوحة التحكم
    return { mainItem: 'dashboard', subItem: null };
  }, [location.pathname, menuItems]);

  // تحديث القائمة النشطة عند تغيير المسار
  useEffect(() => {
    if (activeMenuInfo.mainItem) {
      setActiveMenuItem(activeMenuInfo.mainItem);
    }
    if (activeMenuInfo.subItem) {
      setActiveSubMenuItem(activeMenuInfo.subItem);
    }
  }, [activeMenuInfo, setActiveMenuItem, setActiveSubMenuItem]);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      {/* الشريط العلوي - ثابت */}
      <Topbar />
      
      {/* المحتوى الرئيسي مع الشريط الجانبي */}
      <div className="flex flex-1 relative">
        {/* الشريط الجانبي - ثابت */}
        <Sidebar />
        
        {/* منطقة المحتوى - ديناميكية */}
        <main
          className={`
            flex-1 transition-all duration-300 ease-in-out
            ${isOpen ? 'lg:mr-64' : 'lg:mr-16'}
            min-h-0 pt-0
          `}
        >
          {/* حاوي المحتوى مع التمرير */}
          <div className="h-full overflow-auto">
            <div className="p-4 sm:p-6 lg:p-6">
              {children}
            </div>
          </div>
        </main>
      </div>

      {/* مكونات إضافية ثابتة */}
      {user?.id && (
        <ChatNotificationManager
          onOpenChat={(senderId) => {
            console.log('🔔 PersistentLayout: طلب فتح محادثة مع المستخدم:', senderId);
            const event = new CustomEvent('openChatWithUser', {
              detail: { userId: senderId }
            });
            window.dispatchEvent(event);
          }}
        />
      )}

      <ScrollToTopButton />
    </div>
  );
};

export default PersistentLayout;
