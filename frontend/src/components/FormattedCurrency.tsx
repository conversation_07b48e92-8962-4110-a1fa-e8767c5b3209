/**
 * مكون تنسيق الأرقام المالية الموحد
 * يستخدم خدمة تنسيق الأرقام الجديدة لعرض الأرقام المالية بالتنسيق المحدد في الإعدادات
 */

import React, { useState, useEffect } from 'react';
import { formatCurrencyAdvanced, formatNumberAdvanced } from '../utils/currencyUtils';

interface FormattedCurrencyProps {
  /** المبلغ المراد تنسيقه */
  amount: number;
  /** إظهار رمز العملة أم لا (افتراضي: true) */
  showCurrency?: boolean;
  /** فئة CSS إضافية */
  className?: string;
  /** نص بديل في حالة التحميل */
  loadingText?: string;
  /** نص بديل في حالة الخطأ */
  errorText?: string;
  /** دالة callback عند حدوث خطأ */
  onError?: (error: Error) => void;
  /** تحديد عدد الأرقام العشرية (يتجاوز إعدادات النظام) */
  decimalPlaces?: number;
  /** إجبار إعادة التحميل */
  forceRefresh?: boolean;
}

const FormattedCurrency: React.FC<FormattedCurrencyProps> = ({
  amount,
  showCurrency = true,
  className = '',
  loadingText = '...',
  errorText = 'خطأ',
  onError,
  decimalPlaces,
  forceRefresh = false
}) => {
  const [formattedValue, setFormattedValue] = useState<string>(loadingText);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    let isMounted = true;

    const formatAmount = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // التحقق من صحة المبلغ
        if (typeof amount !== 'number' || isNaN(amount)) {
          throw new Error('Invalid amount provided');
        }

        let formatted: string;

        if (showCurrency) {
          formatted = await formatCurrencyAdvanced(amount);
        } else {
          formatted = await formatNumberAdvanced(amount);
        }

        // تطبيق عدد الأرقام العشرية المخصص إذا تم تحديده
        if (decimalPlaces !== undefined && decimalPlaces >= 0) {
          const parts = formatted.split(' ');
          if (showCurrency && parts.length >= 2) {
            // إذا كان هناك رمز عملة، نحتاج لتعديل الجزء الرقمي فقط
            const numberPart = parts[0];
            const currencyPart = parts.slice(1).join(' ');
            const cleanAmount = parseFloat(numberPart.replace(/[,\s]/g, ''));

            // إظهار الأرقام العشرية فقط إذا كانت موجودة أو مطلوبة
            let fixedNumber: string;
            if (cleanAmount % 1 === 0 && decimalPlaces === 0) {
              // رقم صحيح ولا نريد عشريات
              fixedNumber = cleanAmount.toString();
            } else if (cleanAmount % 1 === 0 && decimalPlaces > 0) {
              // رقم صحيح لكن نريد إظهار عشريات
              fixedNumber = cleanAmount.toFixed(decimalPlaces);
            } else {
              // رقم عشري
              fixedNumber = cleanAmount.toFixed(decimalPlaces);
            }

            // إعادة تطبيق الفواصل
            const formattedNumber = fixedNumber.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            formatted = `${formattedNumber} ${currencyPart}`;
          } else {
            // بدون رمز عملة
            const cleanAmount = parseFloat(formatted.replace(/[,\s]/g, ''));
            let fixedNumber: string;
            if (cleanAmount % 1 === 0 && decimalPlaces === 0) {
              fixedNumber = cleanAmount.toString();
            } else if (cleanAmount % 1 === 0 && decimalPlaces > 0) {
              fixedNumber = cleanAmount.toFixed(decimalPlaces);
            } else {
              fixedNumber = cleanAmount.toFixed(decimalPlaces);
            }
            formatted = fixedNumber.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
          }
        }

        if (isMounted) {
          setFormattedValue(formatted);
          setIsLoading(false);
        }
      } catch (err) {
        const error = err instanceof Error ? err : new Error('Unknown formatting error');
        
        if (isMounted) {
          setError(error);
          setFormattedValue(errorText);
          setIsLoading(false);
          
          if (onError) {
            onError(error);
          }
        }
      }
    };

    formatAmount();

    return () => {
      isMounted = false;
    };
  }, [amount, showCurrency, decimalPlaces, forceRefresh, errorText, onError]);

  // إذا كان هناك خطأ، عرض النص البديل
  if (error) {
    return (
      <span 
        className={`text-red-500 ${className}`}
        title={`خطأ في التنسيق: ${error.message}`}
      >
        {errorText}
      </span>
    );
  }

  // إذا كان قيد التحميل، عرض نص التحميل
  if (isLoading) {
    return (
      <span className={`text-gray-400 animate-pulse ${className}`}>
        {loadingText}
      </span>
    );
  }

  return (
    <span 
      className={className}
      title={`المبلغ: ${amount}`}
    >
      {formattedValue}
    </span>
  );
};

export default FormattedCurrency;

/**
 * Hook لاستخدام تنسيق العملة بشكل مباشر
 */
export const useFormattedCurrency = () => {
  const formatCurrency = async (amount: number): Promise<string> => {
    try {
      return await formatCurrencyAdvanced(amount);
    } catch (error) {
      console.error('Error formatting currency:', error);
      // استخدام الإعدادات الأساسية كـ fallback
      try {
        const settings = await import('../utils/currencyUtils').then(m => m.fetchCurrencySettings());
        return `${amount.toFixed(settings.decimalPlaces)} ${settings.currencySymbol}`;
      } catch {
        return `${amount.toFixed(2)} د.ل`; // fallback نهائي
      }
    }
  };

  const formatNumber = async (amount: number): Promise<string> => {
    try {
      return await formatNumberAdvanced(amount);
    } catch (error) {
      console.error('Error formatting number:', error);
      // استخدام الإعدادات الأساسية كـ fallback
      try {
        const settings = await import('../utils/currencyUtils').then(m => m.fetchCurrencySettings());
        return amount.toFixed(settings.decimalPlaces);
      } catch {
        return amount.toFixed(2); // fallback نهائي
      }
    }
  };

  return { formatCurrency, formatNumber };
};

/**
 * مكون مبسط لعرض الأرقام فقط (بدون عملة)
 */
export const FormattedNumber: React.FC<Omit<FormattedCurrencyProps, 'showCurrency'>> = (props) => {
  return <FormattedCurrency {...props} showCurrency={false} />;
};

/**
 * مكون لعرض النسب المئوية
 */
interface FormattedPercentageProps {
  value: number;
  className?: string;
  decimalPlaces?: number;
}

export const FormattedPercentage: React.FC<FormattedPercentageProps> = ({
  value,
  className = '',
  decimalPlaces = 1
}) => {
  const formattedValue = value.toFixed(decimalPlaces);
  
  return (
    <span className={className}>
      {formattedValue}%
    </span>
  );
};

/**
 * مكون لعرض التغيير في القيم (مع الألوان)
 */
interface FormattedChangeProps {
  current: number;
  previous: number;
  showCurrency?: boolean;
  className?: string;
  showPercentage?: boolean;
}

export const FormattedChange: React.FC<FormattedChangeProps> = ({
  current,
  previous,
  showCurrency = true,
  className = '',
  showPercentage = false
}) => {
  const change = current - previous;
  const percentageChange = previous !== 0 ? ((change / previous) * 100) : 0;
  
  const isPositive = change > 0;
  const isNegative = change < 0;
  
  const colorClass = isPositive 
    ? 'text-green-600 dark:text-green-400' 
    : isNegative 
    ? 'text-red-600 dark:text-red-400' 
    : 'text-gray-600 dark:text-gray-400';

  const icon = isPositive ? '↗' : isNegative ? '↘' : '→';

  return (
    <span className={`${colorClass} ${className}`}>
      {icon} 
      <FormattedCurrency 
        amount={Math.abs(change)} 
        showCurrency={showCurrency}
        className="mr-1"
      />
      {showPercentage && (
        <span className="mr-1">
          (<FormattedPercentage value={Math.abs(percentageChange)} />)
        </span>
      )}
    </span>
  );
};
