import React, { useState, useEffect } from 'react';
import { <PERSON>aEn<PERSON>ope, Fa<PERSON>ser, FaS<PERSON>, Fa<PERSON><PERSON><PERSON>, FaCheckCircle, FaExclamationTriangle } from 'react-icons/fa';
import Modal from './Modal';
import api from '../lib/axios';

interface SupportEmailModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface StoreInfo {
  store_name: string;
  store_address: string;
  store_phone: string;
  store_email: string;
}

interface EmailFormData {
  subject: string;
  message: string;
  senderName: string;
  senderEmail: string;
}

const SupportEmailModal: React.FC<SupportEmailModalProps> = ({ isOpen, onClose }) => {
  const [formData, setFormData] = useState<EmailFormData>({
    subject: '',
    message: '',
    senderName: '',
    senderEmail: ''
  });
  const [storeInfo, setStoreInfo] = useState<StoreInfo>({
    store_name: 'SmartPOS Libya',
    store_address: 'طرابلس، ليبيا',
    store_phone: '+218-91-1234567',
    store_email: '<EMAIL>'
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingStoreInfo, setIsLoadingStoreInfo] = useState(false);
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [statusMessage, setStatusMessage] = useState('');
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // مراقبة حالة الاتصال
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // جلب معلومات المؤسسة عند فتح النافذة
  useEffect(() => {
    if (isOpen) {
      fetchStoreInfo();
      // إعادة تعيين النموذج
      setFormData({
        subject: '',
        message: '',
        senderName: '',
        senderEmail: ''
      });
      setStatus('idle');
      setStatusMessage('');
    }
  }, [isOpen]);

  const fetchStoreInfo = async () => {
    setIsLoadingStoreInfo(true);
    try {
      const response = await api.get('/api/settings/public');
      const settings = response.data;
      
      setStoreInfo({
        store_name: settings.store_name || 'SmartPOS Libya',
        store_address: settings.store_address || 'طرابلس، ليبيا',
        store_phone: settings.store_phone || '+218-91-1234567',
        store_email: settings.store_email || '<EMAIL>'
      });
    } catch (error) {
      console.error('Error fetching store info:', error);
      // استخدام القيم الافتراضية في حالة الخطأ
    } finally {
      setIsLoadingStoreInfo(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // التحقق من حالة الاتصال
    if (!isOnline) {
      setStatus('error');
      setStatusMessage('🌐 لا يوجد اتصال بالإنترنت. يرجى التحقق من اتصالك والمحاولة مرة أخرى.');
      return;
    }

    // التحقق من صحة البيانات
    if (!formData.subject.trim() || !formData.message.trim() || !formData.senderName.trim() || !formData.senderEmail.trim()) {
      setStatus('error');
      setStatusMessage('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    // التحقق من صحة البريد الإلكتروني
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.senderEmail)) {
      setStatus('error');
      setStatusMessage('يرجى إدخال بريد إلكتروني صحيح');
      return;
    }

    setIsLoading(true);
    setStatus('idle');
    
    try {
      const response = await api.post('/api/support/send-email', {
        ...formData,
        storeInfo
      });

      if (response.data.success) {
        setStatus('success');
        setStatusMessage('تم إرسال رسالتك بنجاح! سنرد عليك في أقرب وقت ممكن.');
        
        // إغلاق النافذة بعد 3 ثوانٍ
        setTimeout(() => {
          onClose();
        }, 3000);
      } else {
        setStatus('error');
        setStatusMessage(response.data.message || 'فشل في إرسال الرسالة');
      }
    } catch (error: any) {
      console.error('Error sending support email:', error);
      setStatus('error');

      // تحديد نوع الخطأ وعرض رسالة مناسبة
      let errorMessage = 'فشل في إرسال الرسالة. يرجى المحاولة مرة أخرى.';

      if (error.code === 'NETWORK_ERROR' || error.message === 'Network Error') {
        errorMessage = '❌ فشل في الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.';
      } else if (error.response?.status === 0 || !error.response) {
        errorMessage = '🌐 لا يمكن الوصول إلى الخادم. يرجى التحقق من اتصال الإنترنت وإعدادات الشبكة.';
      } else if (error.response?.status >= 500) {
        errorMessage = '⚠️ خطأ في الخادم. يرجى المحاولة مرة أخرى بعد قليل أو التواصل مع الدعم مباشرة.';
      } else if (error.response?.status === 400) {
        errorMessage = error.response?.data?.detail || 'بيانات غير صحيحة. يرجى التحقق من المعلومات المدخلة.';
      } else if (error.response?.status === 403) {
        errorMessage = 'غير مصرح لك بهذا الإجراء. يرجى تسجيل الدخول والمحاولة مرة أخرى.';
      } else {
        errorMessage = error.response?.data?.detail ||
                     error.response?.data?.message ||
                     '❌ حدث خطأ غير متوقع. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.';
      }

      setStatusMessage(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'success':
        return <FaCheckCircle className="text-green-600 dark:text-green-400" />;
      case 'error':
        return <FaExclamationTriangle className="text-red-600 dark:text-red-400" />;
      default:
        return null;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'success':
        return 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-700 dark:text-green-400';
      case 'error':
        return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-700 dark:text-red-400';
      default:
        return '';
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="إرسال رسالة للدعم الفني"
      size="lg"
    >
      <div className="space-y-6">
        {/* تنبيه حالة الاتصال */}
        {!isOnline && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div className="flex items-center gap-2">
              <FaExclamationTriangle className="text-red-600 dark:text-red-400" />
              <div>
                <h4 className="font-medium text-red-800 dark:text-red-200">لا يوجد اتصال بالإنترنت</h4>
                <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                  يرجى التحقق من اتصالك بالإنترنت قبل إرسال الرسالة
                </p>
              </div>
            </div>
          </div>
        )}

        {/* معلومات المؤسسة */}
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-700">
          <div className="flex items-center gap-2 mb-3">
            <FaStore className="text-blue-600 dark:text-blue-400" />
            <h3 className="font-semibold text-blue-800 dark:text-blue-200">معلومات المؤسسة</h3>
            {isLoadingStoreInfo && (
              <FaSpinner className="animate-spin text-blue-600 dark:text-blue-400" />
            )}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
            <div>
              <span className="font-medium text-blue-700 dark:text-blue-300">اسم المؤسسة: </span>
              <span className="text-blue-600 dark:text-blue-400">{storeInfo.store_name}</span>
            </div>
            <div>
              <span className="font-medium text-blue-700 dark:text-blue-300">الهاتف: </span>
              <span className="text-blue-600 dark:text-blue-400">{storeInfo.store_phone}</span>
            </div>
            <div className="md:col-span-2">
              <span className="font-medium text-blue-700 dark:text-blue-300">العنوان: </span>
              <span className="text-blue-600 dark:text-blue-400">{storeInfo.store_address}</span>
            </div>
          </div>
          <p className="text-xs text-blue-600 dark:text-blue-400 mt-2">
            ستُضمن هذه المعلومات تلقائياً في رسالتك لمساعدة فريق الدعم في تحديد متجرك
          </p>
        </div>

        {/* نموذج الرسالة */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* معلومات المرسل */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="senderName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <FaUser className="inline ml-1" />
                اسمك الكامل *
              </label>
              <input
                type="text"
                id="senderName"
                name="senderName"
                value={formData.senderName}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                placeholder="أدخل اسمك الكامل"
                required
              />
            </div>
            <div>
              <label htmlFor="senderEmail" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <FaEnvelope className="inline ml-1" />
                بريدك الإلكتروني *
              </label>
              <input
                type="email"
                id="senderEmail"
                name="senderEmail"
                value={formData.senderEmail}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                placeholder="<EMAIL>"
                required
              />
            </div>
          </div>

          {/* موضوع الرسالة */}
          <div>
            <label htmlFor="subject" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              موضوع الرسالة *
            </label>
            <input
              type="text"
              id="subject"
              name="subject"
              value={formData.subject}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              placeholder="مثال: مشكلة في طباعة الفواتير"
              required
            />
          </div>

          {/* محتوى الرسالة */}
          <div>
            <label htmlFor="message" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              محتوى الرسالة *
            </label>
            <textarea
              id="message"
              name="message"
              value={formData.message}
              onChange={handleInputChange}
              rows={6}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 resize-none"
              placeholder="اشرح مشكلتك أو استفسارك بالتفصيل..."
              required
            />
          </div>

          {/* رسالة الحالة */}
          {status !== 'idle' && statusMessage && (
            <div className={`p-4 rounded-lg border ${getStatusColor()}`}>
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 mt-0.5">
                  {getStatusIcon()}
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium">{statusMessage}</p>
                  {status === 'error' && (
                    <div className="mt-2 text-xs opacity-90">
                      <p className="mb-1">💡 نصائح لحل المشكلة:</p>
                      <ul className="list-disc list-inside space-y-1">
                        <li>تأكد من اتصالك بالإنترنت</li>
                        <li>تحقق من صحة البريد الإلكتروني المدخل</li>
                        <li>تأكد من ملء جميع الحقول المطلوبة</li>
                        <li>إذا استمرت المشكلة، تواصل مع الدعم مباشرة على: <EMAIL></li>
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* أزرار التحكم */}
          <div className="flex flex-col sm:flex-row gap-3 justify-end pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={onClose}
              disabled={isLoading}
              className="btn-secondary flex items-center justify-center min-w-[120px]"
            >
              إلغاء
            </button>
            <button
              type="submit"
              disabled={isLoading || status === 'success' || !isOnline}
              className={`flex items-center justify-center min-w-[120px] ${
                !isOnline
                  ? 'bg-gray-400 hover:bg-gray-400 cursor-not-allowed text-white px-4 py-2 rounded-lg font-medium'
                  : 'btn-primary'
              }`}
              title={!isOnline ? 'لا يوجد اتصال بالإنترنت' : ''}
            >
              {isLoading ? (
                <>
                  <FaSpinner className="animate-spin ml-2" />
                  جاري الإرسال...
                </>
              ) : !isOnline ? (
                <>
                  <FaExclamationTriangle className="ml-2" />
                  لا يوجد اتصال
                </>
              ) : (
                <>
                  <FaEnvelope className="ml-2" />
                  إرسال الرسالة
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </Modal>
  );
};

export default SupportEmailModal;
