import React, { useState, useEffect } from 'react';
import { FaCloud, FaSync, FaTrash, FaDownload, FaCalendarAlt, FaHdd, FaExclamationTriangle, FaDatabase, FaInfoCircle } from 'react-icons/fa';
import apiClient from '../lib/axios';
import { useAuthStore } from '../stores/authStore';
import DeleteConfirmModal from './DeleteConfirmModal';
import AllGoogleDriveBackupsModal from './AllGoogleDriveBackupsModal';
import SimpleConfirmModal from './SimpleConfirmModal';
import SimpleSuccessModal from './SimpleSuccessModal';
import { FormattedDateTime } from './FormattedDateTime';

interface GoogleDriveFile {
  id: string;
  name: string;
  size: string;
  createdTime: string;
  modifiedTime: string;
  webViewLink?: string;
  webContentLink?: string;
}

interface GoogleDriveBackupsProps {
  onError?: (error: string) => void;
  onSuccess?: (message: string) => void;
}

const GoogleDriveBackups: React.FC<GoogleDriveBackupsProps> = ({ onError, onSuccess }) => {
  const { user, isAuthenticated: userAuthenticated } = useAuthStore();
  const [files, setFiles] = useState<GoogleDriveFile[]>([]);
  const [loading, setLoading] = useState(false);
  const [isConfigured, setIsConfigured] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasPermission, setHasPermission] = useState(false);
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const [deleteModal, setDeleteModal] = useState({
    isOpen: false,
    fileId: '',
    fileName: '',
    isLoading: false
  });
  const [allFilesModal, setAllFilesModal] = useState(false);
  const [downloadingFiles, setDownloadingFiles] = useState<{[key: string]: number}>({});
  const [errorModal, setErrorModal] = useState({ isOpen: false, title: '', message: '' });
  const [successModal, setSuccessModal] = useState({ isOpen: false, title: '', message: '' });

  // التحقق من الصلاحيات
  useEffect(() => {
    const checkPermissions = () => {
      if (!userAuthenticated) {
        setHasPermission(false);
        setError('يرجى تسجيل الدخول أولاً للوصول إلى ملفات Google Drive.');
        return;
      }

      if (!user || user.role !== 'admin') {
        setHasPermission(false);
        setError('ليس لديك صلاحية للوصول إلى ملفات Google Drive. هذه الميزة متاحة للمديرين فقط.');
        return;
      }

      setHasPermission(true);
      setError(null);
    };

    checkPermissions();
  }, [user, userAuthenticated]);

  // التحقق من حالة Google Drive
  const checkGoogleDriveStatus = async () => {
    if (!hasPermission) {
      return;
    }

    try {
      const response = await apiClient.get('/api/google-drive/status');
      const data = response.data;

      setIsConfigured(data.configured || false);
      // التحقق من المصادقة بناءً على وجود user_email وعدم وجود خطأ
      const authenticated = data.configured && data.user_email && !data.error;
      setIsAuthenticated(authenticated);
      setUserEmail(data.user_email || null);

      if (!data.available) {
        setError('مكتبات Google APIs غير متوفرة في النظام.');
      } else if (!data.configured) {
        setError('خدمة Google Drive غير مكونة. يرجى إعداد الخدمة من الإعدادات أولاً.');
      } else if (data.error) {
        setError(data.error);
      } else if (!authenticated) {
        setError('لم يتم تسجيل الدخول بحساب Google. يرجى تسجيل الدخول من الإعدادات.');
      } else {
        setError(null);
      }
    } catch (error: any) {
      console.error('Error checking Google Drive status:', error);
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        setError('ليس لديك صلاحية للوصول إلى إعدادات Google Drive. يرجى تسجيل الدخول كمدير.');
        setHasPermission(false);
      } else {
        setError('فشل في التحقق من حالة Google Drive');
      }
      setIsConfigured(false);
      setIsAuthenticated(false);
    }
  };

  // جلب قائمة الملفات من Google Drive
  const fetchGoogleDriveFiles = async () => {
    if (!hasPermission || !isConfigured || !isAuthenticated) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await apiClient.get('/api/google-drive/files');
      const data = response.data;

      if (data.success) {
        setFiles(data.files || []);
        if (data.files?.length === 0 && data.message) {
          setError(data.message);
        }
      } else {
        setError(data.error || 'فشل في جلب قائمة الملفات');
        setFiles([]);
      }
    } catch (error) {
      console.error('Error fetching Google Drive files:', error);
      setError('فشل في جلب قائمة الملفات من Google Drive');
      setFiles([]);
      onError?.('فشل في جلب قائمة الملفات من Google Drive');
    } finally {
      setLoading(false);
    }
  };

  // فتح نافذة تأكيد الحذف
  const openDeleteModal = (fileId: string, fileName: string) => {
    setDeleteModal({
      isOpen: true,
      fileId,
      fileName,
      isLoading: false
    });
  };

  // دالة تحميل الملف مع شريط التقدم
  const handleDownload = async (fileId: string, fileName: string) => {
    let progressInterval: NodeJS.Timeout | null = null;

    try {
      setDownloadingFiles(prev => ({ ...prev, [fileId]: 0 }));

      // بدء التحميل مع تتبع التقدم الفعلي
      progressInterval = setInterval(() => {
        setDownloadingFiles(prev => {
          const currentProgress = prev[fileId] || 0;
          if (currentProgress < 90) {
            return { ...prev, [fileId]: currentProgress + 10 };
          }
          return prev;
        });
      }, 200);

      // محاولة التحميل باستخدام API الخاص بنا
      try {
        const response = await apiClient.get(`/api/google-drive/download/${fileId}`, {
          responseType: 'blob',
          onDownloadProgress: (progressEvent) => {
            if (progressEvent.total) {
              const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              setDownloadingFiles(prev => ({ ...prev, [fileId]: percentCompleted }));
            }
          }
        });

        // إيقاف المؤشر المؤقت وتعيين 100%
        if (progressInterval) {
          clearInterval(progressInterval);
          progressInterval = null;
        }
        setDownloadingFiles(prev => ({ ...prev, [fileId]: 100 }));
        await new Promise(resolve => setTimeout(resolve, 300));

        // التحقق من أن الاستجابة تحتوي على بيانات
        if (!response.data || response.data.size === 0) {
          throw new Error('الملف فارغ أو تالف');
        }

        // إنشاء blob وتحميل الملف
        const blob = new Blob([response.data], {
          type: 'application/octet-stream'
        });

        // التحقق من حجم الـ blob
        if (blob.size === 0) {
          throw new Error('الملف فارغ أو تالف');
        }

        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        link.style.display = 'none';

        // إضافة الرابط إلى الصفحة وتفعيله
        document.body.appendChild(link);

        // محاولة التحميل
        try {
          link.click();
        } catch (clickError) {
          console.error('خطأ في تفعيل التحميل:', clickError);
          // محاولة بديلة
          const event = new MouseEvent('click', {
            view: window,
            bubbles: true,
            cancelable: true
          });
          link.dispatchEvent(event);
        }

        // إخفاء تحذيرات HTTPS في وحدة التحكم
        const originalConsoleWarn = console.warn;
        console.warn = (...args) => {
          if (args[0] && typeof args[0] === 'string' && args[0].includes('insecure connection')) {
            return; // تجاهل تحذيرات HTTPS
          }
          originalConsoleWarn.apply(console, args);
        };

        // استعادة console.warn بعد فترة قصيرة
        setTimeout(() => {
          console.warn = originalConsoleWarn;
        }, 2000);

        // تنظيف العناصر
        document.body.removeChild(link);

        // تنظيف الـ URL بعد فترة قصيرة
        setTimeout(() => {
          window.URL.revokeObjectURL(url);
        }, 1000);

        // إزالة حالة التحميل
        setDownloadingFiles(prev => {
          const newState = { ...prev };
          delete newState[fileId];
          return newState;
        });

        // عرض رسالة نجاح
        setTimeout(() => {
          setSuccessModal({
            isOpen: true,
            title: 'تم التحميل بنجاح',
            message: `تم تحميل الملف "${fileName}" بنجاح إلى جهازك`
          });
        }, 500);

      } catch (apiError) {
        console.error('API download failed:', apiError);
        if (progressInterval) {
          clearInterval(progressInterval);
          progressInterval = null;
        }

        // إذا فشل API، جرب webContentLink
        const file = files.find(f => f.id === fileId);
        if (file?.webContentLink) {
          setDownloadingFiles(prev => ({ ...prev, [fileId]: 100 }));
          await new Promise(resolve => setTimeout(resolve, 200));

          // فتح رابط التحميل في نافذة جديدة
          window.open(file.webContentLink, '_blank');

          // إزالة حالة التحميل
          setDownloadingFiles(prev => {
            const newState = { ...prev };
            delete newState[fileId];
            return newState;
          });

          // عرض رسالة نجاح
          setSuccessModal({
            isOpen: true,
            title: 'تم بدء التحميل',
            message: `تم فتح رابط تحميل الملف "${fileName}" في نافذة جديدة`
          });
        } else {
          throw apiError;
        }
      }

    } catch (error) {
      console.error('Error downloading file:', error);

      // إيقاف المؤشر المؤقت في حالة الخطأ
      if (progressInterval) {
        clearInterval(progressInterval);
        progressInterval = null;
      }

      // إزالة حالة التحميل في حالة الخطأ
      setDownloadingFiles(prev => {
        const newState = { ...prev };
        delete newState[fileId];
        return newState;
      });

      // عرض رسالة خطأ في مودال
      setErrorModal({
        isOpen: true,
        title: 'فشل في التحميل',
        message: `فشل في تحميل الملف "${fileName}". يرجى المحاولة مرة أخرى أو التحقق من الاتصال بالإنترنت.`
      });
    }
  };

  // تأكيد حذف الملف
  const confirmDeleteFile = async () => {
    setDeleteModal(prev => ({ ...prev, isLoading: true }));

    try {
      const response = await apiClient.delete(`/api/google-drive/files/${deleteModal.fileId}`);
      const data = response.data;

      if (data.success) {
        // إغلاق نافذة الحذف
        setDeleteModal({ isOpen: false, fileId: '', fileName: '', isLoading: false });

        onSuccess?.(`تم حذف الملف "${deleteModal.fileName}" بنجاح من Google Drive`);
        fetchGoogleDriveFiles(); // تحديث القائمة
      } else {
        onError?.(data.error || 'فشل في حذف الملف');
        setDeleteModal(prev => ({ ...prev, isLoading: false }));
      }
    } catch (error: any) {
      console.error('Error deleting file:', error);

      // تحسين رسائل الخطأ
      let errorMessage = 'فشل في حذف الملف من Google Drive';

      if (error.response) {
        // خطأ من الخادم
        const status = error.response.status;
        const detail = error.response.data?.detail || error.response.data?.message;

        console.error('Server error details:', {
          status,
          detail,
          data: error.response.data
        });

        if (status === 404) {
          errorMessage = 'الملف غير موجود أو تم حذفه مسبقاً';
        } else if (status === 401) {
          errorMessage = 'انتهت صلاحية التفويض. يرجى تسجيل الدخول مرة أخرى';
        } else if (status === 403) {
          errorMessage = 'ليس لديك صلاحية لحذف هذا الملف';
        } else if (detail) {
          errorMessage = detail;
        }
      } else if (error.request) {
        // خطأ في الشبكة
        console.error('Network error:', error.request);
        errorMessage = 'خطأ في الاتصال بالخادم. تحقق من الاتصال بالإنترنت';
      } else {
        // خطأ آخر
        console.error('Other error:', error.message);
        errorMessage = error.message || errorMessage;
      }

      onError?.(errorMessage);
      setDeleteModal(prev => ({ ...prev, isLoading: false }));
    }
  };

  // تحديث البيانات
  const handleRefresh = async () => {
    if (!hasPermission) {
      return;
    }
    await checkGoogleDriveStatus();
    if (hasPermission && isConfigured && isAuthenticated) {
      await fetchGoogleDriveFiles();
    }
  };

  // تحميل البيانات عند تحميل المكون
  useEffect(() => {
    if (hasPermission) {
      checkGoogleDriveStatus();
    }
  }, [hasPermission]);

  // جلب الملفات عند تغيير حالة المصادقة
  useEffect(() => {
    if (hasPermission && isConfigured && isAuthenticated) {
      fetchGoogleDriveFiles();
    }
  }, [hasPermission, isConfigured, isAuthenticated]);

  // تنسيق التاريخ باستخدام المكون الموحد
  const formatDate = (dateString: string) => {
    return <FormattedDateTime date={dateString} showTime={true} fallback={dateString} />;
  };

  // تحويل حجم الملف إلى وحدات مفهومة
  const formatFileSize = (sizeInBytes: string | number) => {
    try {
      const bytes = typeof sizeInBytes === 'string' ? parseInt(sizeInBytes) : sizeInBytes;

      if (isNaN(bytes) || bytes === 0) {
        return 'غير محدد';
      }

      const units = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت', 'تيرابايت'];
      const unitIndex = Math.floor(Math.log(bytes) / Math.log(1024));
      const size = bytes / Math.pow(1024, unitIndex);

      // تنسيق الرقم حسب الحجم
      let formattedSize;
      if (unitIndex === 0) {
        // بايت - عدد صحيح
        formattedSize = bytes.toString();
      } else if (size >= 100) {
        // أكبر من 100 - عدد صحيح
        formattedSize = Math.round(size).toString();
      } else if (size >= 10) {
        // بين 10-100 - رقم عشري واحد
        formattedSize = size.toFixed(1);
      } else {
        // أقل من 10 - رقمين عشريين
        formattedSize = size.toFixed(2);
      }

      return `${formattedSize} ${units[unitIndex] || 'بايت'}`;
    } catch {
      return 'غير محدد';
    }
  };

  // عرض رسالة الخطأ أو عدم التكوين أو عدم وجود صلاحيات
  if (!hasPermission || !isConfigured || !isAuthenticated) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="text-center">
          <div className="bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 p-4 rounded-full mb-4 inline-block">
            <FaExclamationTriangle className="text-3xl" />
          </div>
          <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-2">
            {!hasPermission ? 'ليس لديك صلاحية' :
             !isConfigured ? 'خدمة Google Drive غير مكونة' : 'لم يتم تسجيل الدخول'}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {error || (!hasPermission
              ? 'هذه الميزة متاحة للمديرين فقط' :
              !isConfigured
              ? 'يرجى إعداد خدمة Google Drive من الإعدادات أولاً'
              : 'يرجى تسجيل الدخول بحساب Google من الإعدادات')}
          </p>
          {hasPermission && (
            <button
              onClick={handleRefresh}
              className="btn-primary flex items-center mx-auto"
              disabled={loading}
            >
              <FaSync className={`ml-2 ${loading ? 'animate-spin' : ''}`} />
              <span>إعادة التحقق</span>
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      {/* Header */}
      <div className="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <div className="bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 p-2 rounded-lg ml-3">
            <FaCloud className="text-xl" />
          </div>
          <div>
            <h3 className="font-bold text-lg text-gray-900 dark:text-gray-100">نسخ Google Drive</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              النسخ الاحتياطية المحفوظة في Google Drive
              {userEmail && (
                <span className="block text-xs text-blue-600 dark:text-blue-400 mt-0.5">
                  الحساب: {userEmail}
                </span>
              )}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2 space-x-reverse">
          <button
            onClick={() => setAllFilesModal(true)}
            className="btn-outline-sm flex items-center"
          >
            <FaDatabase className="ml-2" />
            <span>عرض الكل</span>
          </button>
          <button
            onClick={handleRefresh}
            className="btn-outline-sm flex items-center"
            disabled={loading}
          >
            <FaSync className={`ml-2 ${loading ? 'animate-spin' : ''}`} />
            <span>تحديث</span>
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {loading && files.length === 0 ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">جاري تحميل الملفات...</p>
          </div>
        ) : error && files.length === 0 ? (
          <div className="text-center py-8">
            <div className="bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 p-4 rounded-full mb-4 inline-block">
              <FaInfoCircle className="text-3xl" />
            </div>
            <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">لا توجد نسخ احتياطية</h4>
            <p className="text-gray-600 dark:text-gray-400">{error}</p>
          </div>
        ) : files.length === 0 ? (
          <div className="text-center py-8">
            <div className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 p-4 rounded-full mb-4 inline-block">
              <FaCloud className="text-3xl" />
            </div>
            <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">لا توجد ملفات</h4>
            <p className="text-gray-600 dark:text-gray-400">لا توجد نسخ احتياطية في Google Drive</p>
          </div>
        ) : (
          <div className="space-y-3">
            {files.slice(0, 4).map((file, index) => (
              <div
                key={file.id}
                className="bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 rounded-lg p-3 hover:shadow-md transition-shadow duration-200"
              >
                {/* Desktop Layout */}
                <div className="hidden sm:flex items-center justify-between">
                  {/* File Info */}
                  <div className="flex-1">
                    <div className="flex items-center mb-1">
                      <FaCloud className="text-blue-600 dark:text-blue-400 ml-2 flex-shrink-0" />
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-0.5">
                          <h5 className="font-medium text-gray-900 dark:text-gray-100 break-all text-sm">
                            {file.name}
                          </h5>
                          {index === 0 && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 flex-shrink-0">
                              أحدث
                            </span>
                          )}
                        </div>
                        <div className="flex items-center space-x-3 space-x-reverse mt-0.5">
                          <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                            <FaCalendarAlt className="ml-1 text-xs" />
                            <span>{formatDate(file.createdTime)}</span>
                          </div>
                          <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                            <FaHdd className="ml-1 text-xs" />
                            <span>{formatFileSize(file.size)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <button
                      onClick={() => handleDownload(file.id, file.name)}
                      className="btn-outline-sm flex items-center"
                      title="تحميل الملف"
                      disabled={downloadingFiles[file.id] !== undefined || loading}
                    >
                      {downloadingFiles[file.id] !== undefined ? (
                        <div className="flex items-center">
                          <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent ml-1"></div>
                          <span className="text-xs">{downloadingFiles[file.id]}%</span>
                        </div>
                      ) : (
                        <FaDownload className="text-sm" />
                      )}
                    </button>
                    <button
                      onClick={() => openDeleteModal(file.id, file.name)}
                      className="btn-danger-sm flex items-center"
                      title="حذف الملف"
                      disabled={loading || downloadingFiles[file.id] !== undefined}
                    >
                      <FaTrash className="text-sm" />
                    </button>
                  </div>
                </div>

                {/* Mobile Layout */}
                <div className="sm:hidden">
                  <div className="flex items-start mb-2">
                    <FaCloud className="text-blue-600 dark:text-blue-400 ml-2 mt-0.5 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h5 className="font-medium text-gray-900 dark:text-gray-100 break-all text-sm">
                          {file.name}
                        </h5>
                        {index === 0 && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 flex-shrink-0">
                            أحدث
                          </span>
                        )}
                      </div>
                      <div className="space-y-0.5">
                        <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                          <FaCalendarAlt className="ml-1 text-xs flex-shrink-0" />
                          <span className="truncate">{formatDate(file.createdTime)}</span>
                        </div>
                        <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                          <FaHdd className="ml-1 text-xs flex-shrink-0" />
                          <span>{formatFileSize(file.size)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-end space-x-2 space-x-reverse">
                    <button
                      onClick={() => handleDownload(file.id, file.name)}
                      className="btn-outline-sm flex items-center"
                      title="تحميل الملف"
                      disabled={downloadingFiles[file.id] !== undefined || loading}
                    >
                      {downloadingFiles[file.id] !== undefined ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent ml-1"></div>
                          <span>{downloadingFiles[file.id]}%</span>
                        </>
                      ) : (
                        <>
                          <FaDownload className="ml-1 text-sm" />
                          <span>تحميل</span>
                        </>
                      )}
                    </button>
                    <button
                      onClick={() => openDeleteModal(file.id, file.name)}
                      className="btn-danger-sm flex items-center"
                      title="حذف الملف"
                      disabled={loading || downloadingFiles[file.id] !== undefined}
                    >
                      <FaTrash className="ml-1 text-sm" />
                      <span>حذف</span>
                    </button>
                  </div>

                  {/* شريط التقدم */}
                  {downloadingFiles[file.id] !== undefined && (
                    <div className="mt-3 px-1">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                          جاري التحميل...
                        </span>
                        <span className="text-xs text-gray-600 dark:text-gray-400">
                          {downloadingFiles[file.id]}%
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                          style={{ width: `${downloadingFiles[file.id]}%` }}
                        ></div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}

            {/* رسالة عرض المزيد */}
            {files.length > 4 && (
              <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 mt-4 rounded-lg border border-gray-200 dark:border-gray-600 text-center">
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  يتم عرض 4 ملفات من أصل {files.length} ملف.{' '}
                  <button
                    onClick={() => setAllFilesModal(true)}
                    className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium"
                  >
                    عرض جميع الملفات
                  </button>
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* نافذة تأكيد الحذف */}
      <DeleteConfirmModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, fileId: '', fileName: '', isLoading: false })}
        onConfirm={confirmDeleteFile}
        title="تأكيد حذف الملف"
        message="هل أنت متأكد من حذف هذا الملف من Google Drive؟"
        itemName={deleteModal.fileName}
        isLoading={deleteModal.isLoading}
      />

      {/* نافذة عرض جميع ملفات Google Drive */}
      <AllGoogleDriveBackupsModal
        isOpen={allFilesModal}
        onClose={() => setAllFilesModal(false)}
        onDelete={(fileId, fileName) => {
          openDeleteModal(fileId, fileName);
          setAllFilesModal(false); // إغلاق النافذة عند فتح نافذة التأكيد
        }}
      />

      {/* نافذة رسالة الخطأ */}
      <SimpleConfirmModal
        isOpen={errorModal.isOpen}
        onClose={() => setErrorModal({ isOpen: false, title: '', message: '' })}
        onConfirm={() => setErrorModal({ isOpen: false, title: '', message: '' })}
        title={errorModal.title}
        message={errorModal.message}
        confirmText="حسناً"
      />

      {/* نافذة رسالة النجاح */}
      <SimpleSuccessModal
        isOpen={successModal.isOpen}
        onClose={() => setSuccessModal({ isOpen: false, title: '', message: '' })}
        message={successModal.message}
        autoClose={true}
        autoCloseDelay={3000}
      />
    </div>
  );
};

export default GoogleDriveBackups;
