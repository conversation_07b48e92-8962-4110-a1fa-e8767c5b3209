import React, { useState } from 'react';
import ArabicRichTextEditorWithButtons from './inputs/ArabicRichTextEditorWithButtons';

const TestEnhancedArabicEditor: React.FC = () => {
  const [content, setContent] = useState(`
    <h1>اختبار محرر النصوص العربي المحسن</h1>
    <p>هذا نص تجريبي لاختبار المحرر المحسن للغة العربية.</p>
    
    <h2>اختبار القوائم النقطية:</h2>
    <ul>
      <li>العنصر الأول في القائمة النقطية</li>
      <li>العنصر الثاني مع نص أطول لاختبار التفاف النص والتأكد من عدم تداخل النقاط مع المحتوى</li>
      <li>العنصر الثالث
        <ul>
          <li>عنصر فرعي أول</li>
          <li>عنصر فرعي ثاني</li>
        </ul>
      </li>
    </ul>

    <h3>اختبار القوائم الرقمية:</h3>
    <ol>
      <li>الخطوة الأولى في العملية</li>
      <li>الخطوة الثانية مع شرح مفصل لاختبار عدم اختفاء الأرقام</li>
      <li>الخطوة الثالثة
        <ol>
          <li>خطوة فرعية أولى</li>
          <li>خطوة فرعية ثانية</li>
        </ol>
      </li>
    </ol>

    <p><strong>نص عريض</strong> و <em>نص مائل</em> و <u>نص مسطر</u></p>
  `);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            اختبار محرر النصوص العربي المحسن
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            هذا المحرر تم تحسينه لحل مشاكل:
          </p>
          <ul className="list-disc list-inside text-gray-600 dark:text-gray-400 mb-6 space-y-2">
            <li>✅ إصلاح تداخل الأسهم مع النص في عنصر اختيار حجم الخط</li>
            <li>✅ إصلاح اختفاء النقاط والأرقام في القوائم</li>
            <li>✅ تحسين دعم اتجاه RTL لجميع العناصر</li>
            <li>✅ تحسين التوافق مع الوضع المظلم</li>
            <li>✅ إظهار جميع الأيقونات بوضوح</li>
            <li>✅ تحسين تخطيط شريط الأدوات</li>
            <li>✅ تصميم الأزرار متماشي مع تصميم التطبيق</li>
            <li>✅ تأثيرات hover وانتقالات سلسة</li>
            <li>✅ ظلال وحدود محسنة</li>
            <li>✅ ترتيب الأزرار في صف واحد</li>
            <li>✅ إخفاء أزرار المحاذاة والصور</li>
            <li>✅ شريط تمرير أفقي للأجهزة الصغيرة</li>
          </ul>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            المحرر المحسن:
          </h2>
          
          <ArabicRichTextEditorWithButtons
            value={content}
            onChange={setContent}
            placeholder="اكتب النص هنا... جرب استخدام العناوين والقوائم النقطية والرقمية"
            minHeight="400px"
          />

          <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              تعليمات الاختبار:
            </h3>
            <ol className="list-decimal list-inside text-gray-600 dark:text-gray-400 space-y-1">
              <li>جرب استخدام أزرار زيادة وتقليل حجم العنوان (A+ و A-)</li>
              <li>أنشئ قائمة نقطية وتأكد من ظهور النقاط</li>
              <li>أنشئ قائمة رقمية وتأكد من ظهور الأرقام</li>
              <li>جرب القوائم المتداخلة</li>
              <li>اختبر في الوضع المظلم والفاتح</li>
            </ol>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mt-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            معاينة المحتوى:
          </h2>
          <div 
            className="prose prose-lg max-w-none dark:prose-invert"
            style={{ direction: 'rtl', textAlign: 'right' }}
            dangerouslySetInnerHTML={{ __html: content }}
          />
        </div>
      </div>
    </div>
  );
};

export default TestEnhancedArabicEditor;