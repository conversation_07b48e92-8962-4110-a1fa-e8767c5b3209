/**
 * مكون إعدادات أمان الأجهزة
 */

import React, { useState, useEffect } from 'react';
import {
  FaUserShield,
  FaExclamationTriangle,
  FaCheck,
  FaTimes
} from 'react-icons/fa';
import api from '../lib/axios';

interface SecuritySettings {
  require_approval: boolean;
  auto_block_suspicious: boolean;
  max_devices_per_ip: number;
  block_unknown_devices: boolean;
  notification_enabled: boolean;
}

const DeviceSecuritySettings: React.FC = () => {
  const [settings, setSettings] = useState<SecuritySettings>({
    require_approval: false,
    auto_block_suspicious: false,
    max_devices_per_ip: 5,
    block_unknown_devices: false,
    notification_enabled: true
  });
  const [isLoading, setIsLoading] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'success' | 'error'>('idle');

  // تحميل الإعدادات
  useEffect(() => {
    loadSecuritySettings();
  }, []);

  const loadSecuritySettings = async () => {
    try {
      const response = await api.get('/api/device-security/settings');
      setSettings(response.data);
    } catch (error) {
      console.error('خطأ في تحميل إعدادات الأمان:', error);
    }
  };

  const updateSetting = async (key: string, value: string) => {
    try {
      setIsLoading(true);
      setSaveStatus('saving');
      
      await api.post('/api/device-security/settings', { key, value });
      await loadSecuritySettings();
      
      setSaveStatus('success');
      setTimeout(() => setSaveStatus('idle'), 2000);
    } catch (error) {
      console.error('خطأ في تحديث الإعداد:', error);
      setSaveStatus('error');
      setTimeout(() => setSaveStatus('idle'), 3000);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* العنوان */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="bg-red-100 dark:bg-red-900/30 p-3 rounded-xl">
              <FaUserShield className="text-2xl text-red-600 dark:text-red-400" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                إعدادات أمان الأجهزة
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                إدارة وحماية الأجهزة المتصلة بالنظام
              </p>
            </div>
          </div>
          
          {/* مؤشر حالة الحفظ */}
          {saveStatus !== 'idle' && (
            <div className="flex items-center gap-2">
              {saveStatus === 'saving' && (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600"></div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">جاري الحفظ...</span>
                </>
              )}
              {saveStatus === 'success' && (
                <>
                  <FaCheck className="text-green-600 dark:text-green-400" />
                  <span className="text-sm text-green-600 dark:text-green-400">تم الحفظ بنجاح</span>
                </>
              )}
              {saveStatus === 'error' && (
                <>
                  <FaTimes className="text-red-600 dark:text-red-400" />
                  <span className="text-sm text-red-600 dark:text-red-400">فشل في الحفظ</span>
                </>
              )}
            </div>
          )}
        </div>
      </div>

      {/* الإعدادات */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* تطلب موافقة للأجهزة الجديدة */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-gray-100">
                    تطلب موافقة للأجهزة الجديدة
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    يتطلب موافقة المدير قبل السماح للأجهزة الجديدة بالوصول
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.require_approval}
                    onChange={(e) => updateSetting('require_approval', e.target.checked.toString())}
                    className="sr-only peer"
                    disabled={isLoading}
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                </label>
              </div>
            </div>

            {/* حظر الأجهزة غير المعروفة */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-gray-100">
                    حظر الأجهزة غير المعروفة
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    حظر تلقائي للأجهزة التي لا تحتوي على معلومات كافية
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.block_unknown_devices}
                    onChange={(e) => updateSetting('block_unknown_devices', e.target.checked.toString())}
                    className="sr-only peer"
                    disabled={isLoading}
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                </label>
              </div>
            </div>

            {/* تفعيل الإشعارات */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-gray-100">
                    تفعيل الإشعارات
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    إرسال إشعارات عند محاولة وصول أجهزة جديدة
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.notification_enabled}
                    onChange={(e) => updateSetting('notification_enabled', e.target.checked.toString())}
                    className="sr-only peer"
                    disabled={isLoading}
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                </label>
              </div>
            </div>

            {/* الحد الأقصى للأجهزة لكل IP */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <div>
                <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                  الحد الأقصى للأجهزة لكل IP
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  عدد الأجهزة المسموح بها من نفس عنوان IP
                </p>
                <input
                  type="number"
                  min="1"
                  max="20"
                  value={settings.max_devices_per_ip}
                  onChange={(e) => updateSetting('max_devices_per_ip', e.target.value)}
                  className="w-20 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  disabled={isLoading}
                />
              </div>
            </div>
          </div>

          {/* تحذير مهم */}
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <FaExclamationTriangle className="text-yellow-600 dark:text-yellow-400 mt-0.5" />
              <div>
                <h4 className="font-medium text-yellow-800 dark:text-yellow-200">
                  تحذير مهم
                </h4>
                <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                  تفعيل "تطلب موافقة للأجهزة الجديدة" سيمنع جميع الأجهزة الجديدة من الوصول حتى تتم الموافقة عليها من قبل المدير. 
                  تأكد من أن لديك وصول دائم من جهاز واحد على الأقل قبل تفعيل هذا الخيار.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeviceSecuritySettings;
