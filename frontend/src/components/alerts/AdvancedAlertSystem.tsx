import React, { useState, useEffect, useRef } from 'react';
import {
  FaTimes,
  FaExclamationTriangle,
  FaInfoCircle,
  FaCheckCircle,
  FaBell,
  FaEyeSlash,
  FaExpand,
  FaCompress
} from 'react-icons/fa';
import { advancedAlertService, AdvancedSystemAlert } from '../../services/advancedAlertService';

interface AdvancedAlertSystemProps {
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'center';
  maxVisible?: number;
  autoHide?: boolean;
  showCounter?: boolean;
  enableSound?: boolean;
  enableGrouping?: boolean;
}

const AdvancedAlertSystem: React.FC<AdvancedAlertSystemProps> = ({
  position = 'top-right',
  maxVisible = 5,
  showCounter = true,
  enableSound = true
}) => {
  const [alerts, setAlerts] = useState<AdvancedSystemAlert[]>([]);
  const [visibleAlerts, setVisibleAlerts] = useState<AdvancedSystemAlert[]>([]);
  const [isExpanded, setIsExpanded] = useState(false);
  const [filter, setFilter] = useState<'all' | 'critical' | 'error' | 'warning' | 'info' | 'success'>('all');
  const [isMinimized, setIsMinimized] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);

  // تحديث التنبيهات
  useEffect(() => {
    const unsubscribe = advancedAlertService.addListener((newAlerts) => {
      setAlerts(newAlerts);

      // تشغيل صوت للتنبيهات الجديدة
      if (enableSound && newAlerts.length > alerts.length) {
        playNotificationSound();
      }
    });

    // تحميل التنبيهات الحالية
    setAlerts(advancedAlertService.getAlerts());

    return unsubscribe;
  }, [alerts.length, enableSound]);

  // فلترة وعرض التنبيهات
  useEffect(() => {
    let filtered = alerts;
    
    // تطبيق الفلتر
    if (filter !== 'all') {
      filtered = alerts.filter(alert => alert.type === filter);
    }

    // ترتيب حسب الأولوية والوقت
    filtered.sort((a, b) => {
      const priorityOrder = { critical: 5, error: 4, warning: 3, info: 2, success: 1 };
      const aPriority = priorityOrder[a.type] || 0;
      const bPriority = priorityOrder[b.type] || 0;
      
      if (aPriority !== bPriority) {
        return bPriority - aPriority;
      }
      
      return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
    });

    // تحديد التنبيهات المرئية
    if (isExpanded) {
      setVisibleAlerts(filtered);
    } else {
      setVisibleAlerts(filtered.slice(0, maxVisible));
    }
  }, [alerts, filter, isExpanded, maxVisible]);

  // تشغيل صوت التنبيه
  const playNotificationSound = () => {
    if (audioRef.current) {
      audioRef.current.play().catch(() => {
        // تجاهل أخطاء تشغيل الصوت
      });
    }
  };

  // إزالة تنبيه
  const removeAlert = (alertId: string) => {
    advancedAlertService.removeAlert(alertId);
  };

  // مسح جميع التنبيهات
  const clearAllAlerts = () => {
    advancedAlertService.clearAllAlerts();
  };

  // الحصول على أيقونة التنبيه
  const getAlertIcon = (type: AdvancedSystemAlert['type']) => {
    switch (type) {
      case 'critical':
        return <FaExclamationTriangle className="text-red-500" />;
      case 'error':
        return <FaExclamationTriangle className="text-red-400" />;
      case 'warning':
        return <FaExclamationTriangle className="text-yellow-500" />;
      case 'info':
        return <FaInfoCircle className="text-blue-500" />;
      case 'success':
        return <FaCheckCircle className="text-green-500" />;
      default:
        return <FaInfoCircle className="text-gray-500" />;
    }
  };

  // الحصول على ألوان التنبيه
  const getAlertColors = (type: AdvancedSystemAlert['type']) => {
    switch (type) {
      case 'critical':
        return {
          bg: 'bg-red-50 dark:bg-red-900/20',
          border: 'border-red-200 dark:border-red-700',
          text: 'text-red-800 dark:text-red-200'
        };
      case 'error':
        return {
          bg: 'bg-red-50 dark:bg-red-900/20',
          border: 'border-red-200 dark:border-red-700',
          text: 'text-red-700 dark:text-red-300'
        };
      case 'warning':
        return {
          bg: 'bg-yellow-50 dark:bg-yellow-900/20',
          border: 'border-yellow-200 dark:border-yellow-700',
          text: 'text-yellow-800 dark:text-yellow-200'
        };
      case 'info':
        return {
          bg: 'bg-blue-50 dark:bg-blue-900/20',
          border: 'border-blue-200 dark:border-blue-700',
          text: 'text-blue-800 dark:text-blue-200'
        };
      case 'success':
        return {
          bg: 'bg-green-50 dark:bg-green-900/20',
          border: 'border-green-200 dark:border-green-700',
          text: 'text-green-800 dark:text-green-200'
        };
      default:
        return {
          bg: 'bg-gray-50 dark:bg-gray-900/20',
          border: 'border-gray-200 dark:border-gray-700',
          text: 'text-gray-800 dark:text-gray-200'
        };
    }
  };

  // تحديد موقع العرض
  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4';
      case 'top-right':
        return 'top-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'bottom-right':
        return 'bottom-4 right-4';
      case 'center':
        return 'top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2';
      default:
        return 'top-4 right-4';
    }
  };

  // تنسيق الوقت
  const formatTime = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `منذ ${days} يوم`;
    if (hours > 0) return `منذ ${hours} ساعة`;
    if (minutes > 0) return `منذ ${minutes} دقيقة`;
    return 'الآن';
  };

  if (isMinimized) {
    return (
      <div className={`fixed ${getPositionClasses()} z-50`}>
        <button
          onClick={() => setIsMinimized(false)}
          className="bg-primary-600 hover:bg-primary-700 text-white p-3 rounded-full shadow-lg relative transition-all duration-200 hover:scale-105"
        >
          <FaBell className="w-5 h-5" />
          {showCounter && alerts.length > 0 && (
            <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center">
              {alerts.length > 99 ? '99+' : alerts.length}
            </span>
          )}
        </button>
      </div>
    );
  }

  return (
    <>
      {/* صوت التنبيه */}
      <audio ref={audioRef} preload="auto">
        <source src="/sounds/notification.mp3" type="audio/mpeg" />
        <source src="/sounds/notification.wav" type="audio/wav" />
      </audio>

      <div className={`fixed ${getPositionClasses()} z-50 max-w-md w-full`}>
        {/* شريط التحكم */}
        <div className="mb-2 flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-2 shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2">
            <FaBell className="text-primary-600 w-4 h-4" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              التنبيهات ({alerts.length})
            </span>
          </div>
          
          <div className="flex items-center gap-1">
            {/* فلتر التنبيهات */}
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value as any)}
              className="text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700"
            >
              <option value="all">الكل</option>
              <option value="critical">حرجة</option>
              <option value="error">أخطاء</option>
              <option value="warning">تحذيرات</option>
              <option value="info">معلومات</option>
              <option value="success">نجاح</option>
            </select>

            {/* توسيع/تصغير */}
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
            >
              {isExpanded ? <FaCompress className="w-3 h-3" /> : <FaExpand className="w-3 h-3" />}
            </button>

            {/* إخفاء */}
            <button
              onClick={() => setIsMinimized(true)}
              className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
            >
              <FaEyeSlash className="w-3 h-3" />
            </button>

            {/* مسح الكل */}
            {alerts.length > 0 && (
              <button
                onClick={clearAllAlerts}
                className="p-1 hover:bg-red-100 dark:hover:bg-red-900/20 rounded text-red-600"
              >
                <FaTimes className="w-3 h-3" />
              </button>
            )}
          </div>
        </div>

        {/* قائمة التنبيهات */}
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {visibleAlerts.map((alert) => {
            const colors = getAlertColors(alert.type);

            return (
              <div
                key={alert.id}
                className={`${colors.bg} ${colors.border} border rounded-lg p-3 shadow-sm relative group transition-all duration-300 hover:shadow-md`}
              >
                  {/* زر الإغلاق */}
                  <button
                    onClick={() => removeAlert(alert.id)}
                    className="absolute top-2 right-2 opacity-70 hover:opacity-100 transition-all duration-200 p-1.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-full hover:scale-110"
                    title="إغلاق التنبيه"
                  >
                    <FaTimes className="w-3 h-3 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200" />
                  </button>

                  {/* محتوى التنبيه */}
                  <div className="flex items-start gap-3 pr-8">
                    <div className="flex-shrink-0 mt-0.5">
                      {getAlertIcon(alert.type)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <h4 className={`font-medium text-sm ${colors.text} mb-1`}>
                        {alert.title}
                      </h4>
                      <p className={`text-xs ${colors.text} opacity-90 mb-2`}>
                        {alert.message}
                      </p>
                      
                      <div className="flex items-center justify-between">
                        <span className={`text-xs ${colors.text} opacity-70`}>
                          {alert.source} • {formatTime(alert.timestamp)}
                        </span>
                        
                        {alert.actions && alert.actions.length > 0 && (
                          <div className="flex gap-1">
                            {alert.actions.slice(0, 2).map((action, index) => (
                              <button
                                key={index}
                                onClick={action.action}
                                className={`text-xs px-2 py-1 rounded ${
                                  action.style === 'primary'
                                    ? 'bg-primary-600 text-white hover:bg-primary-700'
                                    : action.style === 'danger'
                                    ? 'bg-red-600 text-white hover:bg-red-700'
                                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-600 dark:text-gray-200'
                                }`}
                              >
                                {action.label}
                              </button>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
        </div>

        {/* عرض المزيد */}
        {!isExpanded && alerts.length > maxVisible && (
          <button
            onClick={() => setIsExpanded(true)}
            className="w-full mt-2 text-center text-sm text-primary-600 hover:text-primary-700 py-2 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 transition-colors duration-200"
          >
            عرض {alerts.length - maxVisible} تنبيهات أخرى
          </button>
        )}

        {/* رسالة فارغة */}
        {alerts.length === 0 && (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <FaCheckCircle className="w-8 h-8 mx-auto mb-2 text-green-500" />
            <p className="text-sm">لا توجد تنبيهات</p>
          </div>
        )}
      </div>
    </>
  );
};

export default AdvancedAlertSystem;
