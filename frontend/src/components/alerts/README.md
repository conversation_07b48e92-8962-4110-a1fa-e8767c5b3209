# نظام التنبيهات المتقدم - Advanced Alert System

نظام تنبيهات ذكي ومتقدم يوفر تجربة مستخدم احترافية مع منع التكرار وإدارة متقدمة للتنبيهات.

## 🚀 الميزات الرئيسية

### ✨ منع التكرار الذكي
- **Deduplication**: منع التنبيهات المكررة خلال 5 دقائق
- **Rate Limiting**: حد أقصى 10 تنبيهات/دقيقة لكل مصدر
- **Fingerprinting**: تحديد فريد لكل تنبيه

### 🎯 أنواع التنبيهات
- **Success**: تنبيهات النجاح (3 ثواني)
- **Info**: تنبيهات المعلومات (5 ثواني)
- **Warning**: تنبيهات التحذير (7 ثواني)
- **Error**: تنبيهات الأخطاء (10 ثواني)
- **Critical**: تنبيهات حرجة (دائمة)

### 🔧 إدارة متقدمة
- **فلترة ذكية**: حسب النوع، المصدر، الفئة، والوقت
- **كتم مؤقت**: كتم مصادر أو فئات معينة
- **إحصائيات شاملة**: تتبع مفصل لجميع التنبيهات
- **تصدير البيانات**: تصدير التنبيهات للمراجعة

### 🎨 واجهة احترافية
- **تصميم متجاوب**: يعمل على جميع الأحجام
- **الوضع المظلم**: دعم كامل للوضع المظلم
- **انيميشن سلس**: تأثيرات بصرية احترافية
- **إمكانية الوصول**: دعم قارئات الشاشة

## 📦 التثبيت والاستخدام

### 1. الاستيراد الأساسي

```tsx
import { 
  AdvancedAlertSystem, 
  useQuickAlerts,
  advancedAlertService 
} from './components/alerts';
```

### 2. استخدام Hook السريع

```tsx
function MyComponent() {
  const alerts = useQuickAlerts();

  const handleSuccess = () => {
    alerts.success('تم الحفظ', 'تم حفظ البيانات بنجاح');
  };

  const handleError = () => {
    alerts.error('خطأ', 'فشل في الحفظ', { errorCode: 'DB_ERROR' });
  };

  return (
    <div>
      <button onClick={handleSuccess}>نجاح</button>
      <button onClick={handleError}>خطأ</button>
    </div>
  );
}
```

### 3. إضافة النظام للتطبيق

```tsx
function App() {
  return (
    <div>
      {/* محتوى التطبيق */}
      <MyComponent />
      
      {/* نظام التنبيهات */}
      <AdvancedAlertSystem
        position="top-right"
        maxVisible={5}
        showCounter={true}
        enableSound={true}
      />
    </div>
  );
}
```

## 🛠️ API المتقدم

### إضافة تنبيه مخصص

```tsx
advancedAlertService.addAlert({
  type: 'warning',
  title: 'تحذير مخزون',
  message: 'المنتج وصل للحد الأدنى',
  source: 'INVENTORY',
  category: 'stock',
  priority: 7,
  persistent: false,
  hideAfter: 5000,
  actions: [
    {
      id: 'view-product',
      label: 'عرض المنتج',
      action: () => navigateToProduct(),
      style: 'primary'
    }
  ],
  metadata: {
    productId: '123',
    currentStock: 5,
    minStock: 10
  }
});
```

### فلترة التنبيهات

```tsx
const { alerts } = useAdvancedAlerts({
  filter: {
    types: ['critical', 'error'],
    sources: ['DATABASE', 'NETWORK'],
    maxAge: 60, // آخر ساعة
    minPriority: 5
  },
  autoRefresh: true,
  refreshInterval: 30000
});
```

### كتم التنبيهات

```tsx
// كتم مصدر لمدة ساعة
advancedAlertService.muteSource('NETWORK', 60 * 60 * 1000);

// كتم فئة دائماً
advancedAlertService.muteCategory('debug');

// إلغاء الكتم
advancedAlertService.unmuteSource('NETWORK');
```

## 📊 لوحة الإدارة

```tsx
import { AlertManagementPanel } from './components/alerts';

function AdminPage() {
  return (
    <div>
      <h1>إدارة النظام</h1>
      <AlertManagementPanel />
    </div>
  );
}
```

## 🔍 التشخيص وحل المشاكل

### استخدام أدوات التشخيص

```javascript
// في وحدة التحكم
AlertDiagnostics.runFullDiagnostics().then(result => {
  console.log('نتائج التشخيص:', result);
});

// إصلاح سريع
AlertDiagnostics.quickFix();

// حل تنبيه محدد
AlertDiagnostics.resolveSpecificAlert('alert_id_here');
```

### سكريبت الإصلاح السريع

```javascript
// تحميل سكريبت الإصلاح
const script = document.createElement('script');
script.src = '/fix-alert.js';
document.head.appendChild(script);
```

## ⚙️ الإعدادات المتقدمة

### تخصيص الألوان والأنماط

```tsx
const customColors = {
  critical: 'bg-red-600 text-white',
  error: 'bg-red-500 text-white',
  warning: 'bg-yellow-500 text-white',
  info: 'bg-blue-500 text-white',
  success: 'bg-green-500 text-white'
};
```

### تخصيص مدة العرض

```tsx
const customDurations = {
  critical: 0, // دائم
  error: 10000, // 10 ثواني
  warning: 7000, // 7 ثواني
  info: 5000, // 5 ثواني
  success: 3000 // 3 ثواني
};
```

## 🧪 الاختبار

### اختبار التنبيهات

```tsx
import { AlertSystemDemo } from './components/alerts';

// صفحة اختبار شاملة
function TestPage() {
  return <AlertSystemDemo />;
}
```

## 📈 الأداء والتحسين

### نصائح الأداء
- استخدم `useQuickAlerts` للعمليات البسيطة
- فعل `autoRefresh` فقط عند الحاجة
- استخدم الفلاتر لتقليل عدد التنبيهات المعروضة
- نظف التنبيهات القديمة دورياً

### مراقبة الأداء

```tsx
const { stats } = useAlertStats();

console.log('إحصائيات الأداء:', {
  totalAlerts: stats?.total,
  criticalAlerts: stats?.critical,
  recentAlerts: stats?.recent
});
```

## 🔒 الأمان

- جميع البيانات محفوظة محلياً
- لا يتم إرسال معلومات حساسة
- تشفير إعدادات المستخدم
- تنظيف تلقائي للبيانات القديمة

## 🆘 الدعم وحل المشاكل

### مشاكل شائعة

1. **التنبيهات لا تظهر**
   ```javascript
   // تحقق من الخدمة
   console.log(window.advancedAlertService);
   ```

2. **تنبيهات مكررة**
   ```javascript
   // تنظيف شامل
   advancedAlertService.performFullCleanup();
   ```

3. **أداء بطيء**
   ```javascript
   // تقليل معدل التحديث
   const { alerts } = useAdvancedAlerts({
     autoRefresh: false
   });
   ```

### الحصول على المساعدة

- راجع وحدة التحكم للأخطاء
- استخدم أدوات التشخيص المدمجة
- فحص سجلات النظام
- تشغيل الإصلاح التلقائي

---

## 📝 ملاحظات التطوير

- تم تطوير النظام ليكون متوافق مع React 18+
- يدعم TypeScript بالكامل
- متوافق مع جميع المتصفحات الحديثة
- يتبع معايير الوصولية WCAG 2.1

**تم تطوير هذا النظام لحل مشكلة التنبيهات المكررة وتوفير تجربة مستخدم احترافية وسريعة.**
