# دليل البدء السريع - نظام التنبيهات المتقدم

## 🚀 البدء السريع

### 1. حل المشكلة الحالية فوراً

```javascript
// في وحدة التحكم (F12)
const script = document.createElement('script');
script.src = '/fix-alert.js';
document.head.appendChild(script);
```

### 2. اختبار النظام الجديد

```javascript
// تحميل اختبار سريع
const testScript = document.createElement('script');
testScript.src = '/quick-alert-test.js';
document.head.appendChild(testScript);
```

## 📦 الاستخدام في الكود

### استيراد سريع

```tsx
import { useQuickAlerts } from './components/alerts';

function MyComponent() {
  const alerts = useQuickAlerts();
  
  const handleSave = () => {
    alerts.success('تم الحفظ', 'تم حفظ البيانات بنجاح');
  };
  
  return <button onClick={handleSave}>حفظ</button>;
}
```

### إضافة النظام للتطبيق

```tsx
import { AdvancedAlertSystem } from './components/alerts';

function App() {
  return (
    <div>
      {/* محتوى التطبيق */}
      
      {/* نظام التنبيهات - أضف في نهاية App */}
      <AdvancedAlertSystem />
    </div>
  );
}
```

## 🎯 أمثلة سريعة

### تنبيهات أساسية

```tsx
const alerts = useQuickAlerts();

// نجاح
alerts.success('تم الحفظ', 'تم حفظ البيانات بنجاح');

// خطأ
alerts.error('فشل الحفظ', 'حدث خطأ أثناء الحفظ');

// تحذير
alerts.warning('تحذير', 'المخزون منخفض');

// معلومات
alerts.info('معلومة', 'يمكنك استخدام Ctrl+S للحفظ');

// حرج
alerts.critical('خطأ حرج', 'مشكلة في قاعدة البيانات');
```

### تنبيه متقدم

```tsx
import { advancedAlertService } from './components/alerts';

advancedAlertService.addAlert({
  type: 'warning',
  title: 'مخزون منخفض',
  message: 'المنتج "قلم أزرق" وصل للحد الأدنى (5 قطع)',
  source: 'INVENTORY',
  category: 'stock',
  priority: 7,
  actions: [
    {
      id: 'view-product',
      label: 'عرض المنتج',
      action: () => navigateToProduct('123'),
      style: 'primary'
    },
    {
      id: 'order-more',
      label: 'طلب المزيد',
      action: () => createPurchaseOrder('123'),
      style: 'secondary'
    }
  ],
  metadata: {
    productId: '123',
    currentStock: 5,
    minStock: 10
  }
});
```

## 🔧 اختبار من وحدة التحكم

```javascript
// اختبار سريع
quickTest.success('تم الحفظ', 'البيانات محفوظة');
quickTest.error('خطأ', 'فشل في العملية');
quickTest.warning('تحذير', 'انتبه لهذا');

// عرض الإحصائيات
quickTest.stats();

// عرض التنبيهات
quickTest.list();

// مسح الكل
quickTest.clear();
```

## 🎨 لوحة الإدارة

```tsx
import { AlertManagementPanel } from './components/alerts';

function AdminPage() {
  return (
    <div>
      <h1>إدارة النظام</h1>
      <AlertManagementPanel />
    </div>
  );
}
```

## 🔍 التشخيص

```javascript
// في وحدة التحكم
AlertDiagnostics.runFullDiagnostics().then(result => {
  console.log('نتائج التشخيص:', result);
});

// إصلاح سريع
AlertDiagnostics.quickFix();
```

## ⚙️ الإعدادات

### كتم التنبيهات

```javascript
// كتم مصدر لمدة ساعة
advancedAlertService.muteSource('NETWORK', 60 * 60 * 1000);

// كتم فئة دائماً
advancedAlertService.muteCategory('debug');

// إلغاء الكتم
advancedAlertService.unmuteSource('NETWORK');
```

### فلترة التنبيهات

```tsx
const { alerts } = useAdvancedAlerts({
  filter: {
    types: ['critical', 'error'],
    sources: ['DATABASE', 'NETWORK'],
    maxAge: 60 // آخر ساعة
  }
});
```

## 🚨 حل المشاكل

### التنبيهات لا تظهر

```javascript
// تحقق من الخدمة
console.log(window.advancedAlertService);

// تنظيف شامل
advancedAlertService.clearAllAlerts();
```

### تنبيهات مكررة

```javascript
// تشغيل التنظيف
advancedAlertService.performFullCleanup();
```

### أداء بطيء

```tsx
// تقليل التحديث التلقائي
const { alerts } = useAdvancedAlerts({
  autoRefresh: false
});
```

## 📱 الاستجابة للأحداث

```tsx
useEffect(() => {
  const unsubscribe = advancedAlertService.addListener((alerts) => {
    console.log('تحديث التنبيهات:', alerts.length);
  });
  
  return unsubscribe;
}, []);
```

## 🎯 أفضل الممارسات

1. **استخدم الأنواع المناسبة**
   - `success` للعمليات الناجحة
   - `error` للأخطاء القابلة للإصلاح
   - `critical` للمشاكل الحرجة فقط

2. **أضف معلومات مفيدة**
   - استخدم `actions` للإجراءات السريعة
   - أضف `metadata` للتفاصيل الإضافية

3. **تجنب الإزعاج**
   - لا تفرط في التنبيهات
   - استخدم الكتم للمصادر المزعجة

4. **اختبر دائماً**
   - استخدم أدوات الاختبار المدمجة
   - راقب الإحصائيات بانتظام

---

## 🆘 الدعم السريع

**مشكلة عاجلة؟**
```javascript
// تشغيل الإصلاح الطارئ
const script = document.createElement('script');
script.src = '/fix-alert.js';
document.head.appendChild(script);
```

**تحتاج مساعدة؟**
- راجع وحدة التحكم للأخطاء
- استخدم `AlertDiagnostics.runFullDiagnostics()`
- شغل `quickTest.stats()` لرؤية الحالة

**كل شيء يعمل؟**
```javascript
quickTest.success('ممتاز!', 'النظام يعمل بشكل مثالي');
```
