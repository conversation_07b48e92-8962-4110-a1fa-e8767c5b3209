import React from 'react';
import {
  FaPlay,
  FaExclamationTriangle,
  FaInfoCircle,
  FaCheckCircle,
  FaBug,
  FaNetworkWired
} from 'react-icons/fa';
import { useQuickAlerts } from '../../hooks/useAdvancedAlerts';
import AdvancedAlertSystem from './AdvancedAlertSystem';
import AlertManagementPanel from './AlertManagementPanel';

const AlertSystemDemo: React.FC = () => {
  const alerts = useQuickAlerts();

  // أمثلة على التنبيهات المختلفة
  const demoAlerts = [
    {
      title: 'تنبيه نجاح',
      description: 'تم حفظ البيانات بنجاح',
      action: () => alerts.success('تم الحفظ', 'تم حفظ البيانات بنجاح في قاعدة البيانات'),
      icon: <FaCheckCircle className="text-green-500" />,
      color: 'bg-green-50 border-green-200 hover:bg-green-100'
    },
    {
      title: 'تنبيه معلومات',
      description: 'معلومات مفيدة للمستخدم',
      action: () => alerts.info('معلومة مهمة', 'يمكنك استخدام اختصارات لوحة المفاتيح لتسريع العمل'),
      icon: <FaInfoCircle className="text-blue-500" />,
      color: 'bg-blue-50 border-blue-200 hover:bg-blue-100'
    },
    {
      title: 'تنبيه تحذير',
      description: 'تحذير من مشكلة محتملة',
      action: () => alerts.warning('تحذير مخزون', 'المنتج "قلم أزرق" وصل إلى الحد الأدنى للمخزون (5 قطع متبقية)'),
      icon: <FaExclamationTriangle className="text-yellow-500" />,
      color: 'bg-yellow-50 border-yellow-200 hover:bg-yellow-100'
    },
    {
      title: 'تنبيه خطأ',
      description: 'خطأ في العملية',
      action: () => alerts.error('خطأ في الحفظ', 'فشل في حفظ البيانات. يرجى المحاولة مرة أخرى', { 
        errorCode: 'DB_CONNECTION_FAILED',
        timestamp: new Date().toISOString()
      }),
      icon: <FaBug className="text-red-500" />,
      color: 'bg-red-50 border-red-200 hover:bg-red-100'
    },
    {
      title: 'تنبيه حرج',
      description: 'مشكلة حرجة تحتاج انتباه فوري',
      action: () => alerts.critical('خطأ حرج في النظام', 'فقدان الاتصال بقاعدة البيانات. النظام قد لا يعمل بشكل صحيح', true),
      icon: <FaExclamationTriangle className="text-red-600" />,
      color: 'bg-red-50 border-red-200 hover:bg-red-100'
    },
    {
      title: 'خطأ شبكة',
      description: 'مشكلة في الاتصال بالشبكة',
      action: () => {
        // محاكاة خطأ شبكة
        const networkError = new Error('Network timeout');
        alerts.error('خطأ في الشبكة', 'فشل في الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت', {
          error: networkError.message,
          url: 'https://api.example.com/data',
          method: 'GET',
          timeout: 5000
        });
      },
      icon: <FaNetworkWired className="text-orange-500" />,
      color: 'bg-orange-50 border-orange-200 hover:bg-orange-100'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* العنوان */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            نظام التنبيهات المتقدم
          </h1>
          <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            نظام تنبيهات ذكي ومتقدم يمنع التكرار ويعرض التنبيهات بطريقة احترافية وسريعة.
            يدعم أنواع مختلفة من التنبيهات مع إمكانيات متقدمة للفلترة والإدارة.
          </p>
        </div>

        {/* أزرار التجربة */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-6 flex items-center gap-2">
            <FaPlay className="text-primary-600" />
            تجربة التنبيهات
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {demoAlerts.map((demo, index) => (
              <button
                key={index}
                onClick={demo.action}
                className={`p-4 rounded-lg border-2 text-right transition-all duration-200 hover:scale-105 active:scale-95 ${demo.color}`}
              >
                <div className="flex items-center gap-3 mb-2">
                  {demo.icon}
                  <h3 className="font-medium text-gray-900 dark:text-gray-100">
                    {demo.title}
                  </h3>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {demo.description}
                </p>
              </button>
            ))}
          </div>

          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
            <h3 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
              ميزات النظام المتقدم:
            </h3>
            <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
              <li>• منع التنبيهات المكررة تلقائياً</li>
              <li>• تحكم في معدل التنبيهات لمنع الإزعاج</li>
              <li>• تجميع التنبيهات المتشابهة</li>
              <li>• إخفاء تلقائي حسب نوع التنبيه</li>
              <li>• فلترة متقدمة حسب النوع والمصدر والوقت</li>
              <li>• إحصائيات شاملة ومفصلة</li>
              <li>• إمكانية كتم مصادر أو فئات معينة</li>
              <li>• تصدير التنبيهات للمراجعة</li>
            </ul>
          </div>
        </div>

        {/* لوحة إدارة التنبيهات */}
        <AlertManagementPanel />

        {/* معلومات تقنية */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-6">
            المعلومات التقنية
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-3">
                أنواع التنبيهات المدعومة:
              </h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <li className="flex items-center gap-2">
                  <FaCheckCircle className="text-green-500" />
                  <span><strong>success:</strong> تنبيهات النجاح (3 ثواني)</span>
                </li>
                <li className="flex items-center gap-2">
                  <FaInfoCircle className="text-blue-500" />
                  <span><strong>info:</strong> تنبيهات المعلومات (5 ثواني)</span>
                </li>
                <li className="flex items-center gap-2">
                  <FaExclamationTriangle className="text-yellow-500" />
                  <span><strong>warning:</strong> تنبيهات التحذير (7 ثواني)</span>
                </li>
                <li className="flex items-center gap-2">
                  <FaExclamationTriangle className="text-red-500" />
                  <span><strong>error:</strong> تنبيهات الأخطاء (10 ثواني)</span>
                </li>
                <li className="flex items-center gap-2">
                  <FaExclamationTriangle className="text-red-600" />
                  <span><strong>critical:</strong> تنبيهات حرجة (دائمة)</span>
                </li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-3">
                الميزات المتقدمة:
              </h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <li>• <strong>Rate Limiting:</strong> حد أقصى 10 تنبيهات/دقيقة لكل مصدر</li>
                <li>• <strong>Deduplication:</strong> منع التكرار خلال 5 دقائق</li>
                <li>• <strong>Priority System:</strong> ترتيب حسب الأولوية (1-10)</li>
                <li>• <strong>Auto Cleanup:</strong> تنظيف تلقائي للتنبيهات القديمة</li>
                <li>• <strong>Persistent Storage:</strong> حفظ الإعدادات محلياً</li>
                <li>• <strong>Real-time Updates:</strong> تحديث فوري للواجهة</li>
                <li>• <strong>Accessibility:</strong> دعم قارئات الشاشة</li>
                <li>• <strong>Dark Mode:</strong> دعم الوضع المظلم</li>
              </ul>
            </div>
          </div>

          <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-900/50 rounded-lg">
            <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
              استخدام النظام في الكود:
            </h3>
            <pre className="text-sm text-gray-600 dark:text-gray-400 overflow-x-auto">
{`// استخدام Hook السريع
const alerts = useQuickAlerts();

// تنبيه نجاح
alerts.success('تم الحفظ', 'تم حفظ البيانات بنجاح');

// تنبيه خطأ مع تفاصيل
alerts.error('خطأ', 'فشل في الحفظ', { errorCode: 'DB_ERROR' });

// تنبيه حرج دائم
alerts.critical('خطأ حرج', 'مشكلة في النظام', true);`}
            </pre>
          </div>
        </div>
      </div>

      {/* نظام التنبيهات المتقدم */}
      <AdvancedAlertSystem
        position="top-right"
        maxVisible={5}
        autoHide={true}
        showCounter={true}
        enableSound={true}
        enableGrouping={true}
      />
    </div>
  );
};

export default AlertSystemDemo;
