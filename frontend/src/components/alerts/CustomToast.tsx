import React from 'react';
import { toast } from 'react-hot-toast';
import { 
  FaTimes, 
  FaCheckCircle, 
  FaInfoCircle, 
  FaExclamationTriangle, 
  FaExclamationCircle 
} from 'react-icons/fa';

export interface CustomToastProps {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info' | 'critical';
  title: string;
  message: string;
  onClose?: () => void;
}

const CustomToast: React.FC<CustomToastProps> = ({ 
  id, 
  type, 
  title, 
  message, 
  onClose 
}) => {
  const handleClose = () => {
    toast.dismiss(id);
    onClose?.();
  };

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <FaCheckCircle className="w-5 h-5 text-green-600" />;
      case 'info':
        return <FaInfoCircle className="w-5 h-5 text-blue-600" />;
      case 'warning':
        return <FaExclamationTriangle className="w-5 h-5 text-yellow-600" />;
      case 'error':
        return <FaExclamationCircle className="w-5 h-5 text-red-600" />;
      case 'critical':
        return <FaExclamationTriangle className="w-5 h-5 text-red-700" />;
      default:
        return <FaInfoCircle className="w-5 h-5 text-gray-600" />;
    }
  };

  const getColors = () => {
    switch (type) {
      case 'success':
        return {
          bg: 'bg-green-50 dark:bg-green-900/20',
          border: 'border-green-200 dark:border-green-700',
          text: 'text-green-800 dark:text-green-200'
        };
      case 'info':
        return {
          bg: 'bg-blue-50 dark:bg-blue-900/20',
          border: 'border-blue-200 dark:border-blue-700',
          text: 'text-blue-800 dark:text-blue-200'
        };
      case 'warning':
        return {
          bg: 'bg-yellow-50 dark:bg-yellow-900/20',
          border: 'border-yellow-200 dark:border-yellow-700',
          text: 'text-yellow-800 dark:text-yellow-200'
        };
      case 'error':
        return {
          bg: 'bg-red-50 dark:bg-red-900/20',
          border: 'border-red-200 dark:border-red-700',
          text: 'text-red-800 dark:text-red-200'
        };
      case 'critical':
        return {
          bg: 'bg-red-100 dark:bg-red-800/30',
          border: 'border-red-300 dark:border-red-600',
          text: 'text-red-900 dark:text-red-100'
        };
      default:
        return {
          bg: 'bg-gray-50 dark:bg-gray-900/20',
          border: 'border-gray-200 dark:border-gray-700',
          text: 'text-gray-800 dark:text-gray-200'
        };
    }
  };

  const colors = getColors();

  return (
    <div 
      className={`
        ${colors.bg} ${colors.border} ${colors.text}
        border rounded-lg p-4 shadow-lg relative
        max-w-md w-full
        animate-in slide-in-from-right-full duration-300
      `}
      style={{ direction: 'rtl' }}
    >
      {/* زر الإغلاق */}
      <button
        onClick={handleClose}
        className="absolute top-2 right-2 opacity-70 hover:opacity-100 transition-all duration-200 p-1.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-full hover:scale-110"
        title="إغلاق التنبيه"
      >
        <FaTimes className="w-3 h-3 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200" />
      </button>

      {/* محتوى التنبيه */}
      <div className="flex items-start gap-3 pr-8">
        <div className="flex-shrink-0 mt-0.5">
          {getIcon()}
        </div>
        
        <div className="flex-1 min-w-0">
          <h4 className={`font-medium text-sm ${colors.text} mb-1`}>
            {title}
          </h4>
          <p className={`text-xs ${colors.text} opacity-90`}>
            {message}
          </p>
        </div>
      </div>
    </div>
  );
};

export default CustomToast;

// دالة مساعدة لإنشاء تنبيه مخصص مع زر إغلاق
export const showCustomToast = (
  type: CustomToastProps['type'],
  title: string,
  message: string,
  options?: {
    duration?: number;
    onClose?: () => void;
  }
) => {
  const toastId = toast.custom(
    (t) => (
      <CustomToast
        id={t.id}
        type={type}
        title={title}
        message={message}
        onClose={options?.onClose}
      />
    ),
    {
      duration: options?.duration || (type === 'critical' ? Infinity : 
                type === 'error' ? 10000 :
                type === 'warning' ? 7000 :
                type === 'info' ? 5000 : 3000),
      position: 'top-right'
    }
  );

  return toastId;
};

// دوال سريعة للأنواع المختلفة
export const customToast = {
  success: (title: string, message: string, options?: { duration?: number; onClose?: () => void }) =>
    showCustomToast('success', title, message, options),
    
  info: (title: string, message: string, options?: { duration?: number; onClose?: () => void }) =>
    showCustomToast('info', title, message, options),
    
  warning: (title: string, message: string, options?: { duration?: number; onClose?: () => void }) =>
    showCustomToast('warning', title, message, options),
    
  error: (title: string, message: string, options?: { duration?: number; onClose?: () => void }) =>
    showCustomToast('error', title, message, options),
    
  critical: (title: string, message: string, options?: { duration?: number; onClose?: () => void }) =>
    showCustomToast('critical', title, message, options)
};
