import React, { useState, useEffect } from 'react';
import {
  Fa<PERSON>ell,
  <PERSON>a<PERSON><PERSON><PERSON>,
  FaChartBar,
  FaDownload,
  FaRedo,
  FaExclamationTriangle,
  FaInfoCircle,
  FaCheckCircle,
  FaTimes
} from 'react-icons/fa';
import { advancedAlertService, AdvancedSystemAlert, AlertStats } from '../../services/advancedAlertService';

const AlertManagementPanel: React.FC = () => {
  const [alerts, setAlerts] = useState<AdvancedSystemAlert[]>([]);
  const [stats, setStats] = useState<AlertStats | null>(null);
  const [selectedAlerts, setSelectedAlerts] = useState<Set<string>>(new Set());
  const [filter, setFilter] = useState({
    type: 'all' as 'all' | 'critical' | 'error' | 'warning' | 'info' | 'success',
    source: 'all',
    timeRange: '24h' as '1h' | '24h' | '7d' | '30d' | 'all'
  });
  const [showStats, setShowStats] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  // تحديث البيانات
  useEffect(() => {
    const unsubscribe = advancedAlertService.addListener((newAlerts) => {
      setAlerts(newAlerts);
      setStats(advancedAlertService.getStats());
    });

    // تحميل البيانات الأولية
    refreshData();

    return unsubscribe;
  }, []);

  // تحديث البيانات
  const refreshData = () => {
    setIsLoading(true);
    setAlerts(advancedAlertService.getAlerts());
    setStats(advancedAlertService.getStats());
    setIsLoading(false);
  };

  // فلترة التنبيهات
  const filteredAlerts = alerts.filter(alert => {
    // فلتر النوع
    if (filter.type !== 'all' && alert.type !== filter.type) {
      return false;
    }

    // فلتر المصدر
    if (filter.source !== 'all' && alert.source !== filter.source) {
      return false;
    }

    // فلتر الوقت
    if (filter.timeRange !== 'all') {
      const now = new Date();
      let cutoff: Date;
      
      switch (filter.timeRange) {
        case '1h':
          cutoff = new Date(now.getTime() - 60 * 60 * 1000);
          break;
        case '24h':
          cutoff = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case '7d':
          cutoff = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          cutoff = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        default:
          cutoff = new Date(0);
      }
      
      if (alert.timestamp < cutoff) {
        return false;
      }
    }

    return true;
  });

  // تحديد/إلغاء تحديد تنبيه
  const toggleSelectAlert = (alertId: string) => {
    const newSelected = new Set(selectedAlerts);
    if (newSelected.has(alertId)) {
      newSelected.delete(alertId);
    } else {
      newSelected.add(alertId);
    }
    setSelectedAlerts(newSelected);
  };

  // تحديد/إلغاء تحديد الكل
  const toggleSelectAll = () => {
    if (selectedAlerts.size === filteredAlerts.length) {
      setSelectedAlerts(new Set());
    } else {
      setSelectedAlerts(new Set(filteredAlerts.map(alert => alert.id)));
    }
  };

  // حذف التنبيهات المحددة
  const deleteSelectedAlerts = () => {
    selectedAlerts.forEach(alertId => {
      advancedAlertService.removeAlert(alertId);
    });
    setSelectedAlerts(new Set());
  };

  // تمييز كمقروء
  const markSelectedAsRead = () => {
    selectedAlerts.forEach(alertId => {
      advancedAlertService.markAsRead(alertId);
    });
    setSelectedAlerts(new Set());
  };

  // تصدير التنبيهات
  const exportAlerts = () => {
    const data = {
      exportDate: new Date().toISOString(),
      stats,
      alerts: filteredAlerts
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `alerts-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // الحصول على أيقونة النوع
  const getTypeIcon = (type: AdvancedSystemAlert['type']) => {
    switch (type) {
      case 'critical':
        return <FaExclamationTriangle className="text-red-500" />;
      case 'error':
        return <FaExclamationTriangle className="text-red-400" />;
      case 'warning':
        return <FaExclamationTriangle className="text-yellow-500" />;
      case 'info':
        return <FaInfoCircle className="text-blue-500" />;
      case 'success':
        return <FaCheckCircle className="text-green-500" />;
      default:
        return <FaInfoCircle className="text-gray-500" />;
    }
  };

  // تنسيق الوقت
  const formatTime = (timestamp: Date) => {
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(timestamp);
  };

  // الحصول على مصادر فريدة
  const uniqueSources = Array.from(new Set(alerts.map(alert => alert.source)));

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      {/* رأس اللوحة */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <FaBell className="text-primary-600 w-6 h-6" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              إدارة التنبيهات
            </h2>
            {stats && (
              <span className="bg-primary-100 dark:bg-primary-900/20 text-primary-800 dark:text-primary-200 px-2 py-1 rounded-full text-sm">
                {stats.total} تنبيه
              </span>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowStats(!showStats)}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
              title="إظهار/إخفاء الإحصائيات"
            >
              <FaChartBar className="w-4 h-4" />
            </button>
            
            <button
              onClick={exportAlerts}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
              title="تصدير التنبيهات"
            >
              <FaDownload className="w-4 h-4" />
            </button>
            
            <button
              onClick={refreshData}
              className={`p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg ${isLoading ? 'animate-spin' : ''}`}
              title="تحديث"
            >
              <FaRedo className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* الإحصائيات */}
      {showStats && stats && (
        <div className="p-6 bg-gray-50 dark:bg-gray-900/50 border-b border-gray-200 dark:border-gray-700 transition-all duration-300">

          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{stats.critical}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">حرجة</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-500">{stats.byType.error || 0}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">أخطاء</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-500">{stats.byType.warning || 0}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">تحذيرات</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-500">{stats.byType.info || 0}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">معلومات</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-500">{stats.byType.success || 0}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">نجاح</div>
            </div>
          </div>
        </div>
      )}

      {/* أدوات التحكم والفلاتر */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex flex-wrap items-center gap-4">
          {/* فلتر النوع */}
          <div className="flex items-center gap-2">
            <FaFilter className="w-4 h-4 text-gray-500" />
            <select
              value={filter.type}
              onChange={(e) => setFilter({ ...filter, type: e.target.value as any })}
              className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700"
            >
              <option value="all">جميع الأنواع</option>
              <option value="critical">حرجة</option>
              <option value="error">أخطاء</option>
              <option value="warning">تحذيرات</option>
              <option value="info">معلومات</option>
              <option value="success">نجاح</option>
            </select>
          </div>

          {/* فلتر المصدر */}
          <select
            value={filter.source}
            onChange={(e) => setFilter({ ...filter, source: e.target.value })}
            className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700"
          >
            <option value="all">جميع المصادر</option>
            {uniqueSources.map(source => (
              <option key={source} value={source}>{source}</option>
            ))}
          </select>

          {/* فلتر الوقت */}
          <select
            value={filter.timeRange}
            onChange={(e) => setFilter({ ...filter, timeRange: e.target.value as any })}
            className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700"
          >
            <option value="1h">آخر ساعة</option>
            <option value="24h">آخر 24 ساعة</option>
            <option value="7d">آخر أسبوع</option>
            <option value="30d">آخر شهر</option>
            <option value="all">جميع الأوقات</option>
          </select>

          {/* أدوات التحكم الجماعي */}
          {selectedAlerts.size > 0 && (
            <div className="flex items-center gap-2 ml-auto">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {selectedAlerts.size} محدد
              </span>
              
              <button
                onClick={markSelectedAsRead}
                className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm"
              >
                تمييز كمقروء
              </button>
              
              <button
                onClick={deleteSelectedAlerts}
                className="px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 text-sm"
              >
                حذف
              </button>
            </div>
          )}
        </div>
      </div>

      {/* قائمة التنبيهات */}
      <div className="max-h-96 overflow-y-auto">
        {filteredAlerts.length === 0 ? (
          <div className="p-8 text-center text-gray-500 dark:text-gray-400">
            <FaCheckCircle className="w-12 h-12 mx-auto mb-4 text-green-500" />
            <p>لا توجد تنبيهات تطابق الفلاتر المحددة</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {/* رأس الجدول */}
            <div className="p-4 bg-gray-50 dark:bg-gray-900/50 flex items-center gap-4 text-sm font-medium text-gray-700 dark:text-gray-300">
              <input
                type="checkbox"
                checked={selectedAlerts.size === filteredAlerts.length && filteredAlerts.length > 0}
                onChange={toggleSelectAll}
                className="rounded"
              />
              <div className="w-8">النوع</div>
              <div className="flex-1">العنوان والرسالة</div>
              <div className="w-24">المصدر</div>
              <div className="w-32">الوقت</div>
              <div className="w-16">إجراءات</div>
            </div>

            {/* صفوف التنبيهات */}
            {filteredAlerts.map((alert) => (
              <div
                key={alert.id}
                className="p-4 hover:bg-gray-50 dark:hover:bg-gray-900/50 flex items-center gap-4 transition-colors duration-200"
              >
                <input
                  type="checkbox"
                  checked={selectedAlerts.has(alert.id)}
                  onChange={() => toggleSelectAlert(alert.id)}
                  className="rounded"
                />
                
                <div className="w-8 flex justify-center">
                  {getTypeIcon(alert.type)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 truncate">
                    {alert.title}
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                    {alert.message}
                  </p>
                </div>
                
                <div className="w-24 text-sm text-gray-600 dark:text-gray-400 truncate">
                  {alert.source}
                </div>
                
                <div className="w-32 text-sm text-gray-600 dark:text-gray-400">
                  {formatTime(alert.timestamp)}
                </div>
                
                <div className="w-16 flex justify-center">
                  <button
                    onClick={() => advancedAlertService.removeAlert(alert.id)}
                    className="p-1 hover:bg-red-100 dark:hover:bg-red-900/20 rounded text-red-600"
                    title="حذف"
                  >
                    <FaTimes className="w-3 h-3" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default AlertManagementPanel;
