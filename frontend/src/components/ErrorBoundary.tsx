import { Component, ErrorInfo, ReactNode } from 'react';
import { FaExclamationTriangle, FaSync, FaBug, FaPaperPlane } from 'react-icons/fa';
import errorLogger from '../services/errorLogger';
import api from '../lib/axios';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  isSendingReport: boolean;
  reportSent: boolean;
  showModal: boolean;
  modalType: 'success' | 'error' | null;
  modalMessage: string;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      isSendingReport: false,
      reportSent: false,
      showModal: false,
      modalType: null,
      modalMessage: ''
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // تحديث الحالة لإظهار واجهة الخطأ
    return {
      hasError: true,
      error,
      errorInfo: null,
      isSendingReport: false,
      reportSent: false,
      showModal: false,
      modalType: null,
      modalMessage: ''
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // تسجيل الخطأ
    console.error('React Error Boundary caught an error:', error, errorInfo);
    
    // تسجيل الخطأ في نظام السجلات
    errorLogger.logCritical('React Component Error', {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorBoundary: true
    });

    this.setState({
      error,
      errorInfo
    });
  }

  handleReload = () => {
    // إعادة تحميل الصفحة
    window.location.reload();
  };

  handleReset = () => {
    // إعادة تعيين حالة الخطأ
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      isSendingReport: false,
      reportSent: false,
      showModal: false,
      modalType: null,
      modalMessage: ''
    });
  };

  closeModal = () => {
    console.log('🔒 إغلاق النافذة المنبثقة...');
    this.setState({
      showModal: false,
      modalType: null,
      modalMessage: ''
    });
    console.log('✅ تم إغلاق النافذة المنبثقة');
  };

  handleSendReport = async () => {
    if (this.state.isSendingReport || this.state.reportSent) {
      console.log('🚫 إرسال التقرير متوقف - الحالة:', {
        isSendingReport: this.state.isSendingReport,
        reportSent: this.state.reportSent
      });
      return;
    }

    console.log('🚀 بدء إرسال تقرير الخطأ...');
    this.setState({ isSendingReport: true });

    try {
      // إنشاء سجل خطأ مفصل للإرسال
      const errorReport = {
        timestamp: new Date().toISOString(),
        error_type: 'React Error Boundary',
        message: this.state.error?.message || 'خطأ غير محدد في واجهة المستخدم',
        stack: this.state.error?.stack || '',
        component_stack: this.state.errorInfo?.componentStack || '',
        user_agent: navigator.userAgent,
        url: window.location.href,
        level: 'CRITICAL',
        source: 'FRONTEND',
        error_boundary: true
      };

      // تسجيل الخطأ في النظام أولاً لإنشاء سجل في قاعدة البيانات
      errorLogger.logCritical('Error Boundary - خطأ حرج في واجهة المستخدم', errorReport);

      // انتظار قصير للتأكد من حفظ السجل
      await new Promise(resolve => setTimeout(resolve, 1000));

      // محاولة جلب آخر الأخطاء الحرجة لإرسالها
      let logIds: number[] = [];
      try {
        const recentLogs = await api.get('/api/system/logs?level=CRITICAL&limit=5');
        logIds = recentLogs.data.logs ? recentLogs.data.logs.map((log: any) => log.id) : [];
        console.log('📊 تم جلب السجلات الحرجة:', logIds);
      } catch (logError) {
        console.warn('⚠️ فشل في جلب السجلات الحرجة، سيتم إرسال السجل الحالي فقط:', logError);
        // في حالة فشل جلب السجلات، سنرسل السجل الحالي فقط
        logIds = [];
      }

      // إذا لم نجد سجلات، سنقوم بإنشاء سجل مؤقت للإرسال
      if (logIds.length === 0) {
        console.log('📝 لم يتم العثور على سجلات، سيتم إنشاء سجل مؤقت للإرسال');
        // إنشاء سجل مؤقت للإرسال
        const tempLog = {
          id: Date.now(), // معرف مؤقت
          level: 'CRITICAL',
          source: 'FRONTEND',
          message: errorReport.message,
          details: JSON.stringify({
            stack: errorReport.stack,
            component_stack: errorReport.component_stack,
            url: errorReport.url,
            user_agent: errorReport.user_agent,
            timestamp: errorReport.timestamp,
            error_boundary: true
          }),
          timestamp: errorReport.timestamp,
          resolved: false,
          user_id: null,
          session_id: null
        };

        // إرسال السجل المؤقت مباشرة
        const response = await api.post('/api/system/logs/send-support', {
          logs: [], // قائمة فارغة من معرفات السجلات
          temp_logs: [tempLog], // السجل المؤقت
          priority: 'critical',
          auto_send: true,
          error_details: {
            type: 'Error Boundary',
            message: errorReport.message,
            stack: errorReport.stack,
            component_stack: errorReport.component_stack,
            url: errorReport.url,
            user_agent: errorReport.user_agent,
            timestamp: errorReport.timestamp
          }
        });

        console.log('📧 تم إرسال السجل المؤقت:', response.data);

        // معالجة الاستجابة للسجل المؤقت
        if (response.data && response.data.success) {
          console.log('✅ نجح إرسال السجل المؤقت، تحديث الحالة...');

          this.setState({
            reportSent: true,
            isSendingReport: false
          });

          // عرض رسالة نجاح مفصلة للسجل المؤقت
          const successMessage = `🎉 تم إرسال تقرير الخطأ بنجاح!

📧 تم الإرسال إلى: ${response.data.support_email || '<EMAIL>'}
📤 من: ${response.data.sender_email || '<EMAIL>'}
📊 عدد السجلات المرسلة: ${response.data.logs_count || 1}

⏰ سيتم الرد خلال 24 ساعة لمعالجة المشكلة
🔧 تم إرفاق تفاصيل الخطأ وبيانات النظام
📝 تم إرسال السجل مباشرة بدون حفظ في قاعدة البيانات`;

          console.log('🎉 عرض نافذة النجاح...');
          this.setState({
            showModal: true,
            modalType: 'success',
            modalMessage: successMessage
          });

          console.log('✅ تم تحديث الحالة بنجاح، النافذة يجب أن تظهر الآن');
          return; // إنهاء الدالة هنا
        } else {
          console.error('❌ فشل في إرسال السجل المؤقت:', response.data);
          throw new Error(response.data?.message || 'فشل في إرسال السجل المؤقت - استجابة غير صحيحة من الخادم');
        }
      }

      // إرسال التقرير عبر البريد الإلكتروني مع بيانات المتجر
      const response = await api.post('/api/system/logs/send-support', {
        logs: logIds,
        priority: 'critical',
        auto_send: true,
        error_details: {
          type: 'Error Boundary',
          message: errorReport.message,
          stack: errorReport.stack,
          component_stack: errorReport.component_stack,
          url: errorReport.url,
          user_agent: errorReport.user_agent,
          timestamp: errorReport.timestamp
        }
      });

      if (response.data && response.data.success) {
        console.log('✅ نجح إرسال السجلات العادية، تحديث الحالة...');

        this.setState({
          reportSent: true,
          isSendingReport: false
        });

        // عرض رسالة نجاح مفصلة
        const successMessage = `🎉 تم إرسال تقرير الخطأ بنجاح!

📧 تم الإرسال إلى: ${response.data.support_email || '<EMAIL>'}
📤 من: ${response.data.sender_email || '<EMAIL>'}
📊 عدد السجلات المرسلة: ${response.data.logs_count || logIds.length}

⏰ سيتم الرد خلال 24 ساعة لمعالجة المشكلة
🔧 تم إرفاق تفاصيل الخطأ وبيانات النظام`;

        console.log('🎉 عرض نافذة النجاح للسجلات العادية...');
        this.setState({
          showModal: true,
          modalType: 'success',
          modalMessage: successMessage
        });

        console.log('✅ تم تحديث الحالة بنجاح للسجلات العادية');
      } else {
        console.error('❌ فشل في إرسال السجلات العادية:', response.data);
        throw new Error(response.data?.message || 'فشل في إرسال التقرير - استجابة غير صحيحة من الخادم');
      }
    } catch (error) {
      console.error('❌ فشل في إرسال تقرير الخطأ:', error);
      console.log('🔄 إعادة تعيين حالة الإرسال...');
      this.setState({ isSendingReport: false });

      // تحديد نوع الخطأ لعرض رسالة مناسبة
      let errorMessage = 'فشل في إرسال تقرير الخطأ';
      let errorDetails = '';

      if (error && typeof error === 'object') {
        const err = error as any;

        // طباعة تفاصيل الخطأ للتشخيص
        console.error('Error details:', {
          message: err.message,
          response: err.response?.data,
          status: err.response?.status,
          code: err.code
        });

        if (err.code === 'NETWORK_ERROR' || err.message?.includes('Network Error')) {
          errorMessage = 'خطأ في الشبكة';
          errorDetails = 'تعذر الاتصال بالخادم\nيرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى';
        } else if (err.response?.status === 408 || err.code === 'ECONNABORTED') {
          errorMessage = 'انتهت مهلة الاتصال';
          errorDetails = 'يرجى المحاولة مرة أخرى أو التحقق من سرعة الإنترنت';
        } else if (err.response?.status >= 500) {
          errorMessage = 'خطأ في الخادم';
          errorDetails = 'يرجى المحاولة مرة أخرى لاحقاً أو التواصل مع الدعم مباشرة على:\<EMAIL>';
        } else if (err.response?.status === 400) {
          errorMessage = 'خطأ في البيانات المرسلة';
          errorDetails = err.response?.data?.detail || 'البيانات المرسلة غير صحيحة';
        } else if (err.response?.status === 401 || err.response?.status === 403) {
          errorMessage = 'خطأ في الصلاحيات';
          errorDetails = 'يرجى تسجيل الدخول مرة أخرى والمحاولة';
        } else {
          errorMessage = 'خطأ غير متوقع';
          errorDetails = err.response?.data?.detail || err.message || 'حدث خطأ غير معروف';
        }
      }

      const fullErrorMessage = `❌ ${errorMessage}

${errorDetails}

💡 يمكنك أيضاً التواصل مع الدعم مباشرة:
📧 <EMAIL>
📱 أو من خلال مركز المساعدة في التطبيق`;

      console.log('❌ عرض نافذة الخطأ...');
      this.setState({
        showModal: true,
        modalType: 'error',
        modalMessage: fullErrorMessage
      });

      console.log('❌ تم تحديث الحالة لعرض نافذة الخطأ');
    }
  };

  render() {
    if (this.state.hasError) {
      // يمكن عرض واجهة خطأ مخصصة
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
          <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/30 mb-4">
                <FaExclamationTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
              
              <h1 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                حدث خطأ غير متوقع
              </h1>
              
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-6">
                نعتذر، حدث خطأ في التطبيق. يمكنك المحاولة مرة أخرى أو إعادة تحميل الصفحة.
              </p>

              {/* تفاصيل الخطأ للمطورين */}
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="text-left mb-6 p-3 bg-gray-100 dark:bg-gray-700 rounded text-xs">
                  <summary className="cursor-pointer font-medium text-gray-700 dark:text-gray-300 mb-2">
                    تفاصيل الخطأ (للمطورين)
                  </summary>
                  <div className="space-y-2">
                    <div>
                      <strong>الرسالة:</strong>
                      <pre className="mt-1 text-red-600 dark:text-red-400 whitespace-pre-wrap">
                        {this.state.error.message}
                      </pre>
                    </div>
                    {this.state.error.stack && (
                      <div>
                        <strong>Stack Trace:</strong>
                        <pre className="mt-1 text-gray-600 dark:text-gray-400 whitespace-pre-wrap text-xs overflow-x-auto">
                          {this.state.error.stack}
                        </pre>
                      </div>
                    )}
                    {this.state.errorInfo?.componentStack && (
                      <div>
                        <strong>Component Stack:</strong>
                        <pre className="mt-1 text-gray-600 dark:text-gray-400 whitespace-pre-wrap text-xs overflow-x-auto">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </div>
                </details>
              )}

              <div className="flex flex-col sm:flex-row gap-3">
                <button
                  onClick={this.handleReset}
                  className="flex-1 inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  <FaSync className="ml-2 h-4 w-4" />
                  المحاولة مرة أخرى
                </button>
                
                <button
                  onClick={this.handleReload}
                  className="flex-1 inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  إعادة تحميل الصفحة
                </button>
              </div>

              <button
                onClick={this.handleSendReport}
                disabled={this.state.isSendingReport || this.state.reportSent}
                className={`mt-3 w-full inline-flex items-center justify-center px-4 py-2 border text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 ${
                  this.state.reportSent
                    ? 'border-green-300 dark:border-green-600 text-green-700 dark:text-green-300 bg-green-50 dark:bg-green-900/30 cursor-not-allowed'
                    : this.state.isSendingReport
                    ? 'border-blue-300 dark:border-blue-600 text-blue-700 dark:text-blue-300 bg-blue-50 dark:bg-blue-900/30 cursor-not-allowed'
                    : 'border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                }`}
              >
                {this.state.reportSent ? (
                  <>
                    <FaPaperPlane className="ml-2 h-4 w-4 text-green-600" />
                    تم إرسال التقرير بنجاح ✅
                  </>
                ) : this.state.isSendingReport ? (
                  <>
                    <div className="ml-2 h-4 w-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                    جاري إرسال التقرير...
                  </>
                ) : (
                  <>
                    <FaBug className="ml-2 h-4 w-4" />
                    إرسال تقرير للدعم الفني
                  </>
                )}
              </button>

              {/* رسالة توضيحية */}
              <div className="mt-3 text-xs text-gray-500 dark:text-gray-400 text-center">
                {this.state.reportSent ? (
                  <p className="text-green-600 dark:text-green-400">
                    📧 تم إرسال التقرير إلى فريق الدعم الفني<br />
                    سيتم التواصل معك خلال 24 ساعة
                  </p>
                ) : (
                  <p>
                    سيتم إرسال تفاصيل الخطأ إلى فريق الدعم الفني<br />
                    لمساعدتك في حل المشكلة بأسرع وقت ممكن
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* نافذة النتائج */}
          {this.state.showModal && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6">
                <div className="text-center">
                  <div className={`mx-auto flex items-center justify-center h-12 w-12 rounded-full mb-4 ${
                    this.state.modalType === 'success'
                      ? 'bg-green-100 dark:bg-green-900/30'
                      : 'bg-red-100 dark:bg-red-900/30'
                  }`}>
                    {this.state.modalType === 'success' ? (
                      <FaPaperPlane className="h-6 w-6 text-green-600 dark:text-green-400" />
                    ) : (
                      <FaExclamationTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
                    )}
                  </div>

                  <h3 className={`text-lg font-medium mb-4 ${
                    this.state.modalType === 'success'
                      ? 'text-green-900 dark:text-green-100'
                      : 'text-red-900 dark:text-red-100'
                  }`}>
                    {this.state.modalType === 'success' ? 'تم الإرسال بنجاح' : 'فشل في الإرسال'}
                  </h3>

                  <div className="text-sm text-gray-600 dark:text-gray-300 mb-6 whitespace-pre-line text-right">
                    {this.state.modalMessage}
                  </div>

                  <button
                    onClick={this.closeModal}
                    className={`w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                      this.state.modalType === 'success'
                        ? 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
                        : 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
                    }`}
                  >
                    إغلاق
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
