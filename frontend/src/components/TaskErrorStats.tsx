import React, { useState, useEffect } from 'react';
import {
  FaExclamation<PERSON>riangle,
  FaExclamationCircle,
  FaInfoCircle,
  FaChartBar,
  FaTasks,
  FaCheckCircle,
  FaTimesCircle
} from 'react-icons/fa';
import scheduledTaskErrorService, { type TaskErrorStats } from '../services/scheduledTaskErrorService';

interface TaskErrorStatsProps {
  className?: string;
}

const TaskErrorStats: React.FC<TaskErrorStatsProps> = ({ className = '' }) => {
  const [stats, setStats] = useState<TaskErrorStats | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    // تحميل الإحصائيات الأولية
    const currentStats = scheduledTaskErrorService.getErrorStats();
    setStats(currentStats);

    // الاشتراك في التحديثات
    const unsubscribe = scheduledTaskErrorService.addErrorListener(() => {
      const updatedStats = scheduledTaskErrorService.getErrorStats();
      setStats(updatedStats);
    });

    return unsubscribe;
  }, []);

  if (!stats || stats.totalErrors === 0) {
    return null;
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'NETWORK':
        return <FaExclamationCircle className="text-blue-500" />;
      case 'CONFIGURATION':
        return <FaExclamationTriangle className="text-purple-500" />;
      case 'SYSTEM':
        return <FaTimesCircle className="text-gray-500" />;
      case 'EXECUTION':
        return <FaExclamationTriangle className="text-orange-500" />;
      default:
        return <FaInfoCircle className="text-gray-500" />;
    }
  };

  const getCategoryName = (category: string) => {
    switch (category) {
      case 'NETWORK':
        return 'شبكة';
      case 'CONFIGURATION':
        return 'إعدادات';
      case 'SYSTEM':
        return 'نظام';
      case 'EXECUTION':
        return 'تنفيذ';
      default:
        return category;
    }
  };

  const getStatusColor = () => {
    if (stats.criticalErrors > 0) {
      return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200';
    }
    if (stats.unresolvedErrors > 0) {
      return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200';
    }
    return 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-200';
  };

  const getStatusIcon = () => {
    if (stats.criticalErrors > 0) {
      return <FaExclamationTriangle className="text-red-600" />;
    }
    if (stats.unresolvedErrors > 0) {
      return <FaExclamationCircle className="text-yellow-600" />;
    }
    return <FaCheckCircle className="text-green-600" />;
  };

  const getStatusMessage = () => {
    if (stats.criticalErrors > 0) {
      return `${stats.criticalErrors} أخطاء حرجة تحتاج انتباه فوري`;
    }
    if (stats.unresolvedErrors > 0) {
      return `${stats.unresolvedErrors} أخطاء غير محلولة`;
    }
    return 'جميع الأخطاء محلولة';
  };

  return (
    <div className={`border rounded-lg p-4 ${getStatusColor()} ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          {getStatusIcon()}
          <div className="mr-2">
            <h4 className="font-medium text-sm">
              إحصائيات أخطاء المهام
            </h4>
            <p className="text-xs">
              {getStatusMessage()}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2 space-x-reverse">
          <div className="text-xs text-center">
            <div className="font-medium">{stats.totalErrors}</div>
            <div>إجمالي</div>
          </div>

          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-1 hover:bg-black/10 dark:hover:bg-white/10 rounded transition-colors"
            title={isExpanded ? 'إخفاء التفاصيل' : 'عرض التفاصيل'}
          >
            <FaChartBar className="text-sm" />
          </button>
        </div>
      </div>

      {isExpanded && (
        <div className="mt-4 space-y-3">
          {/* إحصائيات سريعة */}
          <div className="grid grid-cols-3 gap-3 text-xs">
            <div className="text-center p-2 bg-white dark:bg-gray-800 rounded">
              <div className="font-medium text-lg">{stats.totalErrors}</div>
              <div>إجمالي الأخطاء</div>
            </div>
            <div className="text-center p-2 bg-white dark:bg-gray-800 rounded">
              <div className="font-medium text-lg text-red-600">{stats.unresolvedErrors}</div>
              <div>غير محلولة</div>
            </div>
            <div className="text-center p-2 bg-white dark:bg-gray-800 rounded">
              <div className="font-medium text-lg text-red-700">{stats.criticalErrors}</div>
              <div>حرجة</div>
            </div>
          </div>

          {/* الأخطاء حسب الفئة */}
          {Object.keys(stats.errorsByCategory).length > 0 && (
            <div>
              <h5 className="font-medium text-xs mb-2 flex items-center">
                <FaChartBar className="ml-1" />
                الأخطاء حسب الفئة
              </h5>
              <div className="space-y-1">
                {Object.entries(stats.errorsByCategory).map(([category, count]) => (
                  <div key={category} className="flex items-center justify-between text-xs">
                    <div className="flex items-center">
                      {getCategoryIcon(category)}
                      <span className="mr-1">{getCategoryName(category)}</span>
                    </div>
                    <span className="font-medium">{count}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* الأخطاء حسب المهمة */}
          {Object.keys(stats.errorsByTask).length > 0 && (
            <div>
              <h5 className="font-medium text-xs mb-2 flex items-center">
                <FaTasks className="ml-1" />
                الأخطاء حسب المهمة
              </h5>
              <div className="space-y-1 max-h-24 overflow-y-auto">
                {Object.entries(stats.errorsByTask)
                  .sort(([,a], [,b]) => b - a)
                  .slice(0, 5)
                  .map(([taskName, count]) => (
                  <div key={taskName} className="flex items-center justify-between text-xs">
                    <span className="truncate flex-1" title={taskName}>
                      {taskName}
                    </span>
                    <span className="font-medium mr-2">{count}</span>
                  </div>
                ))}
                {Object.keys(stats.errorsByTask).length > 5 && (
                  <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
                    +{Object.keys(stats.errorsByTask).length - 5} مهام أخرى
                  </div>
                )}
              </div>
            </div>
          )}

          {/* إجراءات سريعة */}
          <div className="flex justify-end space-x-2 space-x-reverse pt-2 border-t border-current/20">
            <button
              onClick={() => {
                // فتح صفحة التقارير في تبويب سجلات الأخطاء
                window.open('/reports?tab=system&subtab=system-logs&logtab=logs&focus=true', '_blank');
              }}
              className="text-xs px-2 py-1 bg-white dark:bg-gray-800 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              عرض السجلات
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskErrorStats;
