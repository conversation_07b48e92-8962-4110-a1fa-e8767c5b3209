import React, { useState, useEffect, useRef } from 'react';
import { FaChevronDown, FaCheck } from 'react-icons/fa';

interface Option {
  value: string;
  label: string;
}

interface FilterSelectProps {
  options: Option[];
  value: string;
  onChange: (value: string) => void;
  name: string;
  placeholder?: string;
  className?: string;
  label?: string;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const FilterSelect: React.FC<FilterSelectProps> = ({
  options,
  value,
  onChange,
  name,
  placeholder = 'اختر...',
  className = '',
  label,
  disabled = false,
  size = 'sm'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedLabel, setSelectedLabel] = useState('');
  const selectRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Update selected label when value changes
  useEffect(() => {
    const option = options.find(opt => opt.value === value);
    setSelectedLabel(option ? option.label : placeholder);
  }, [value, options, placeholder]);

  // Handle option selection
  const handleSelect = (optionValue: string) => {
    onChange(optionValue);
    setIsOpen(false);
  };

  // Get size classes
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          button: 'h-8 px-3 pr-8 text-xs',
          icon: 'h-3 w-3',
          dropdown: 'text-xs'
        };
      case 'md':
        return {
          button: 'h-10 px-3 pr-10 text-sm',
          icon: 'h-4 w-4',
          dropdown: 'text-sm'
        };
      case 'lg':
        return {
          button: 'h-12 px-4 pr-12 text-base',
          icon: 'h-5 w-5',
          dropdown: 'text-base'
        };
      default:
        return {
          button: 'h-8 px-3 pr-8 text-xs',
          icon: 'h-3 w-3',
          dropdown: 'text-xs'
        };
    }
  };

  const sizeClasses = getSizeClasses();

  return (
    <div className={`relative ${className}`} ref={selectRef}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          {label}
        </label>
      )}
      <div className="relative">
        <button
          type="button"
          onClick={() => !disabled && setIsOpen(!isOpen)}
          className={`
            w-full border border-gray-300 dark:border-gray-600 rounded-lg 
            bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 
            focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 
            text-right transition-all duration-200 appearance-none
            ${sizeClasses.button}
            ${disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer hover:border-gray-400 dark:hover:border-gray-500'}
            ${isOpen ? 'ring-2 ring-primary-500 border-primary-500' : ''}
          `}
          disabled={disabled}
        >
          <span className={`block truncate ${!value ? 'text-gray-500 dark:text-gray-400' : ''}`}>
            {selectedLabel}
          </span>
          <span className="absolute inset-y-0 left-0 flex items-center pl-2 pointer-events-none">
            <FaChevronDown 
              className={`
                ${sizeClasses.icon} text-gray-400 dark:text-gray-500 
                transition-transform duration-200 
                ${isOpen ? 'transform rotate-180' : ''}
              `} 
            />
          </span>
        </button>

        {/* Hidden input for form submission */}
        <input type="hidden" name={name} value={value} />
      </div>

      {isOpen && (
        <div className="absolute z-50 mt-1 w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 max-h-60 overflow-auto custom-scrollbar">
          <ul className="py-1">
            {options.map((option) => (
              <li
                key={option.value}
                onClick={() => handleSelect(option.value)}
                className={`
                  px-3 py-2 cursor-pointer transition-colors duration-150
                  hover:bg-gray-100 dark:hover:bg-gray-700 
                  flex items-center justify-between
                  ${sizeClasses.dropdown}
                  ${option.value === value 
                    ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-300' 
                    : 'text-gray-900 dark:text-gray-100'
                  }
                `}
              >
                <span className="block truncate">{option.label}</span>
                {option.value === value && (
                  <span className="text-primary-600 dark:text-primary-400">
                    <FaCheck className={sizeClasses.icon} />
                  </span>
                )}
              </li>
            ))}
            {options.length === 0 && (
              <li className={`px-3 py-2 text-gray-500 dark:text-gray-400 ${sizeClasses.dropdown}`}>
                لا توجد خيارات
              </li>
            )}
          </ul>
        </div>
      )}
    </div>
  );
};

export default FilterSelect;
