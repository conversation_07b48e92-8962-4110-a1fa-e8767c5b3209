# 📊 ComparisonIndicator Component

مكون مؤشر المقارنة الصغير لعرض مقارنات البيانات مع الفترات السابقة.

## 🎯 الاستخدام الأساسي

```tsx
import ComparisonIndicator from './ComparisonIndicator';
import { ComparisonData } from '../services/comparisonIndicatorService';

const salesComparison: ComparisonData = {
  current: 15750,
  previous: 12800,
  difference: 2950,
  percentageChange: 23.5,
  trend: 'up',
  isPositive: true
};

<ComparisonIndicator
  comparison={salesComparison}
  comparisonText="مقارنة بمبيعات أمس"
/>
```

## 📋 الخصائص (Props)

| الخاصية | النوع | الافتراضي | الوصف |
|---------|------|----------|-------|
| `comparison` | `ComparisonData` | مطلوب | بيانات المقارنة |
| `comparisonText` | `string` | مطلوب | نص المقارنة |
| `className` | `string` | `''` | فئة CSS إضافية |
| `showPercentage` | `boolean` | `true` | إظهار النسبة المئوية |
| `showDifference` | `boolean` | `false` | إظهار القيمة المطلقة للفرق |
| `size` | `'small' \| 'medium' \| 'large'` | `'small'` | حجم المؤشر |

## 🎨 الأحجام المتاحة

### Small (افتراضي) - للبطاقات
```tsx
<ComparisonIndicator
  comparison={data}
  comparisonText="مقارنة بالأمس"
  size="small"
/>
// دائرة: w-4 h-4، أيقونة: w-2.5 h-2.5
```

### Medium - للأقسام
```tsx
<ComparisonIndicator
  comparison={data}
  comparisonText="مقارنة بالأمس"
  size="medium"
/>
// دائرة: w-5 h-5، أيقونة: w-3 h-3
```

### Large - للعناوين
```tsx
<ComparisonIndicator
  comparison={data}
  comparisonText="مقارنة بالأمس"
  size="large"
/>
// دائرة: w-6 h-6، أيقونة: w-3.5 h-3.5
```

## 🎯 أنواع الاتجاهات

### صعود (Up)
- **الأيقونة**: `FiTrendingUp` في دائرة خضراء
- **اللون**: أخضر (`text-green-600`)
- **المثال**: `24%` (بدون علامة + وبدون فاصلة عشرية)

### هبوط (Down)
- **الأيقونة**: `FiTrendingDown` في دائرة حمراء
- **اللون**: أحمر (`text-red-600`)
- **المثال**: `12%` (بدون علامة - وبدون فاصلة عشرية)

### ثبات (Neutral)
- **الأيقونة**: `FiMinus` في دائرة رمادية
- **اللون**: رمادي (`text-gray-600`)
- **المثال**: `0%`

## 📊 أمثلة متقدمة

### مع إظهار الفرق
```tsx
<ComparisonIndicator
  comparison={salesComparison}
  comparisonText="مقارنة بمبيعات أمس"
  showPercentage={true}
  showDifference={true}
/>
// النتيجة: 24% (+2,950) مقارنة بمبيعات أمس
```

### بدون نسبة مئوية
```tsx
<ComparisonIndicator
  comparison={salesComparison}
  comparisonText="مقارنة بمبيعات أمس"
  showPercentage={false}
  showDifference={true}
/>
// النتيجة: 2,950 مقارنة بمبيعات أمس
```

### حجم كبير
```tsx
<ComparisonIndicator
  comparison={salesComparison}
  comparisonText="مقارنة بمبيعات أمس"
  size="large"
  className="my-4"
/>
```

## 🎨 التخصيص

### ألوان مخصصة
```tsx
<ComparisonIndicator
  comparison={salesComparison}
  comparisonText="مقارنة بمبيعات أمس"
  className="text-blue-600 dark:text-blue-400"
/>
```

### تباعد مخصص
```tsx
<ComparisonIndicator
  comparison={salesComparison}
  comparisonText="مقارنة بمبيعات أمس"
  className="px-4 py-2 bg-gray-50 dark:bg-gray-800 rounded-lg"
/>
```

## 🔧 التكامل مع البطاقات

### في CompactStatCard
```tsx
<CompactStatCard
  title="مبيعات اليوم"
  amount={15750}
  icon={<FaReceipt />}
  showCurrency={true}
  comparison={salesComparison}
  comparisonText="مقارنة بمبيعات أمس"
/>
```

### في بطاقة مخصصة
```tsx
<div className="bg-white dark:bg-gray-800 p-4 rounded-lg">
  <h3 className="text-lg font-semibold mb-2">مبيعات اليوم</h3>
  <p className="text-3xl font-bold">15,750 د.ل</p>
  
  <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
    <ComparisonIndicator
      comparison={salesComparison}
      comparisonText="مقارنة بمبيعات أمس"
    />
  </div>
</div>
```

## 🌙 دعم الوضع المظلم

المكون يدعم الوضع المظلم تلقائياً:

```css
/* الألوان تتكيف تلقائياً */
.text-green-600.dark:text-green-400  /* صعود */
.text-red-600.dark:text-red-400      /* هبوط */
.text-gray-600.dark:text-gray-400    /* ثبات */
```

## 🛡️ معالجة الحالات الخاصة

### بيانات فارغة
```tsx
// إذا كانت البيانات فارغة، لا يظهر المكون شيئاً
const emptyComparison = {
  current: 0,
  previous: 0,
  difference: 0,
  percentageChange: 0,
  trend: 'neutral',
  isPositive: true
};

<ComparisonIndicator
  comparison={emptyComparison}
  comparisonText="مقارنة بالأمس"
/>
// النتيجة: لا يظهر شيء
```

### قيم كبيرة
```tsx
const largeComparison = {
  current: 1500000,
  previous: 1200000,
  difference: 300000,
  percentageChange: 25.0,
  trend: 'up',
  isPositive: true
};

<ComparisonIndicator
  comparison={largeComparison}
  comparisonText="مقارنة بالشهر الماضي"
  showDifference={true}
/>
// النتيجة: +25.0% (+300,000) مقارنة بالشهر الماضي
```

## 📱 الاستجابة (Responsive)

المكون مُحسن للعمل على جميع أحجام الشاشات:

```tsx
<ComparisonIndicator
  comparison={salesComparison}
  comparisonText="مقارنة بالأمس"
  className="text-xs sm:text-sm md:text-base"
/>
```

## 🔍 نصائح الاستخدام

1. **استخدم نصوص واضحة**: "مقارنة بمبيعات أمس" أفضل من "مقارنة"
2. **اختر الحجم المناسب**: `small` للبطاقات، `medium` للأقسام، `large` للعناوين
3. **فعّل showDifference للقيم المهمة**: مفيد للمبالغ الكبيرة
4. **استخدم className للتخصيص**: بدلاً من تعديل المكون مباشرة

## 🐛 استكشاف الأخطاء

### المكون لا يظهر
- تأكد من أن `comparison.current` أو `comparison.previous` ليس صفر
- تحقق من صحة بيانات `ComparisonData`

### الألوان لا تظهر
- تأكد من تضمين Tailwind CSS
- تحقق من دعم الوضع المظلم في التطبيق

### الأيقونات مفقودة
- تأكد من تثبيت `react-icons`
- تحقق من استيراد `react-icons/fi`

---

**آخر تحديث**: أغسطس 2025  
**الإصدار**: 1.0.0  
**التوافق**: React 18+, TypeScript 4.5+
