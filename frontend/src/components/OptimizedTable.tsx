/**
 * مكون جدول محسن للبيانات الكبيرة
 * يدعم virtual scrolling وpagination وsearch محسن
 */

import React, { memo, useMemo, useCallback, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaSyncAlt } from 'react-icons/fa';
import { usePaginatedTable } from '../hooks/useVirtualizedTable';
import { createDebouncedSearch } from '../services/performanceOptimizer';

export interface TableColumn<T> {
  key: keyof T | string;
  title: string;
  width?: string;
  render?: (value: any, record: T, index: number) => React.ReactNode;
  sortable?: boolean;
  filterable?: boolean;
  searchable?: boolean;
}

export interface OptimizedTableProps<T> {
  columns: TableColumn<T>[];
  fetchData: (page: number, pageSize: number, search?: string, filters?: any) => Promise<{ data: T[]; totalCount: number }>;
  pageSize?: number;
  searchable?: boolean;
  filterable?: boolean;
  refreshable?: boolean;
  className?: string;
  rowKey?: keyof T | ((record: T) => string | number);
  onRowClick?: (record: T, index: number) => void;
  loading?: boolean;
  emptyText?: string;
  dependencies?: any[];
}

/**
 * مكون صف الجدول المحسن
 */
const TableRow = memo(function TableRowComponent<T>({
  record,
  columns,
  index,
  onClick,
  rowKey
}: {
  record: T;
  columns: TableColumn<T>[];
  index: number;
  onClick?: (record: T, index: number) => void;
  rowKey?: keyof T | ((record: T) => string | number);
}) {
  const handleClick = useCallback(() => {
    onClick?.(record, index);
  }, [onClick, record, index]);

  const key = useMemo(() => {
    if (typeof rowKey === 'function') {
      return rowKey(record);
    }
    return rowKey ? String(record[rowKey]) : index;
  }, [record, rowKey, index]);

  return (
    <tr
      key={key}
      onClick={handleClick}
      className={`
        border-b border-gray-200 dark:border-gray-700 
        hover:bg-gray-50 dark:hover:bg-gray-800 
        transition-colors duration-150
        ${onClick ? 'cursor-pointer' : ''}
      `}
    >
      {columns.map((column, colIndex) => {
        const value = typeof column.key === 'string' && column.key.includes('.')
          ? column.key.split('.').reduce((obj: any, key) => obj?.[key], record)
          : record[column.key as keyof T];

        return (
          <td
            key={colIndex}
            className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100"
            style={{ width: column.width }}
          >
            {column.render ? column.render(value, record, index) : String(value || '')}
          </td>
        );
      })}
    </tr>
  );
});

/**
 * مكون الجدول المحسن الرئيسي
 */
export function OptimizedTable<T>({
  columns,
  fetchData,
  pageSize = 50,
  searchable = true,
  filterable = false,
  refreshable = true,
  className = '',
  rowKey,
  onRowClick,
  loading: externalLoading = false,
  emptyText = 'لا توجد بيانات',
  dependencies = [],
}: OptimizedTableProps<T>) {
  const searchRef = useRef<HTMLInputElement>(null);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [filters] = React.useState<any>({});

  // دالة جلب البيانات مع البحث والفلترة
  const fetchDataWithParams = useCallback(
    (page: number, size: number) => {
      return fetchData(page, size, searchQuery, filters);
    },
    [fetchData, searchQuery, filters]
  );

  // استخدام hook الجدول المحسن
  const [tableState, tableActions] = usePaginatedTable(
    fetchDataWithParams,
    pageSize,
    [searchQuery, filters, ...dependencies]
  );

  // دوال البحث والفلترة المحسنة
  const debouncedSearch = useMemo(
    () => createDebouncedSearch((query: string) => {
      setSearchQuery(query);
      tableActions.setPage(1); // العودة للصفحة الأولى عند البحث
    }),
    [tableActions]
  );

  // const debouncedFilter = useMemo(
  //   () => createDebouncedFilter((newFilters: any) => {
  //     setFilters(newFilters);
  //     tableActions.setPage(1); // العودة للصفحة الأولى عند الفلترة
  //   }),
  //   [tableActions]
  // );

  // معالج البحث
  const handleSearch = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    debouncedSearch(e.target.value);
  }, [debouncedSearch]);

  // معالج التحديث
  const handleRefresh = useCallback(() => {
    tableActions.refresh();
  }, [tableActions]);

  // معالج تغيير الصفحة
  const handlePageChange = useCallback((page: number) => {
    tableActions.setPage(page);
  }, [tableActions]);

  const isLoading = tableState.loading || externalLoading;

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm ${className}`}>
      {/* شريط الأدوات */}
      {(searchable || filterable || refreshable) && (
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            {/* البحث */}
            {searchable && (
              <div className="relative flex-1 max-w-md">
                <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm" />
                <input
                  ref={searchRef}
                  type="text"
                  placeholder="البحث..."
                  onChange={handleSearch}
                  className="
                    w-full pl-10 pr-4 py-2 
                    border border-gray-300 dark:border-gray-600 
                    rounded-lg 
                    bg-white dark:bg-gray-700 
                    text-gray-900 dark:text-gray-100
                    placeholder-gray-500 dark:placeholder-gray-400
                    focus:ring-2 focus:ring-primary-500 focus:border-transparent
                    transition-colors duration-200
                  "
                />
              </div>
            )}

            {/* أزرار الإجراءات */}
            <div className="flex gap-2">
              {filterable && (
                <button
                  type="button"
                  className="
                    px-3 py-2 
                    border border-gray-300 dark:border-gray-600 
                    rounded-lg 
                    bg-white dark:bg-gray-700 
                    text-gray-700 dark:text-gray-300
                    hover:bg-gray-50 dark:hover:bg-gray-600
                    transition-colors duration-200
                    flex items-center gap-2
                  "
                >
                  <FaFilter className="text-sm" />
                  <span>فلترة</span>
                </button>
              )}

              {refreshable && (
                <button
                  type="button"
                  onClick={handleRefresh}
                  disabled={isLoading}
                  className="
                    px-3 py-2 
                    border border-gray-300 dark:border-gray-600 
                    rounded-lg 
                    bg-white dark:bg-gray-700 
                    text-gray-700 dark:text-gray-300
                    hover:bg-gray-50 dark:hover:bg-gray-600
                    disabled:opacity-50 disabled:cursor-not-allowed
                    transition-colors duration-200
                    flex items-center gap-2
                  "
                >
                  <FaSyncAlt className={`text-sm ${isLoading ? 'animate-spin' : ''}`} />
                  <span>تحديث</span>
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* الجدول */}
      <div className="overflow-x-auto">
        <table className="w-full">
          {/* رأس الجدول */}
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              {columns.map((column, index) => (
                <th
                  key={index}
                  className="
                    px-4 py-3 
                    text-right text-xs font-medium 
                    text-gray-500 dark:text-gray-300 
                    uppercase tracking-wider
                  "
                  style={{ width: column.width }}
                >
                  {column.title}
                </th>
              ))}
            </tr>
          </thead>

          {/* جسم الجدول */}
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {isLoading ? (
              <tr>
                <td colSpan={columns.length} className="px-4 py-8 text-center">
                  <div className="flex items-center justify-center gap-2 text-gray-500 dark:text-gray-400">
                    <FaSpinner className="animate-spin" />
                    <span>جاري التحميل...</span>
                  </div>
                </td>
              </tr>
            ) : tableState.data.length === 0 ? (
              <tr>
                <td colSpan={columns.length} className="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
                  {emptyText}
                </td>
              </tr>
            ) : (
              tableState.data.map((record, index) => {
                const key = typeof rowKey === 'function'
                  ? rowKey(record)
                  : rowKey
                    ? String(record[rowKey])
                    : index;

                return (
                  <TableRow
                    key={key}
                    record={record}
                    columns={columns as TableColumn<any>[]}
                    index={index}
                    onClick={onRowClick as any}
                    rowKey={rowKey as any}
                  />
                );
              })
            )}
          </tbody>
        </table>
      </div>

      {/* pagination */}
      {tableState.totalPages > 1 && (
        <div className="px-4 py-3 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700 dark:text-gray-300">
              عرض {((tableState.currentPage - 1) * pageSize) + 1} إلى{' '}
              {Math.min(tableState.currentPage * pageSize, tableState.totalCount)} من{' '}
              {tableState.totalCount} نتيجة
            </div>

            <div className="flex gap-1">
              <button
                onClick={() => handlePageChange(tableState.currentPage - 1)}
                disabled={tableState.currentPage === 1}
                className="
                  px-3 py-1 
                  border border-gray-300 dark:border-gray-600 
                  rounded 
                  bg-white dark:bg-gray-700 
                  text-gray-700 dark:text-gray-300
                  hover:bg-gray-50 dark:hover:bg-gray-600
                  disabled:opacity-50 disabled:cursor-not-allowed
                  transition-colors duration-200
                "
              >
                السابق
              </button>

              {/* أرقام الصفحات */}
              {Array.from({ length: Math.min(5, tableState.totalPages) }, (_, i) => {
                const page = i + 1;
                return (
                  <button
                    key={page}
                    onClick={() => handlePageChange(page)}
                    className={`
                      px-3 py-1 
                      border border-gray-300 dark:border-gray-600 
                      rounded 
                      transition-colors duration-200
                      ${page === tableState.currentPage
                        ? 'bg-primary-500 text-white border-primary-500'
                        : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
                      }
                    `}
                  >
                    {page}
                  </button>
                );
              })}

              <button
                onClick={() => handlePageChange(tableState.currentPage + 1)}
                disabled={tableState.currentPage === tableState.totalPages}
                className="
                  px-3 py-1 
                  border border-gray-300 dark:border-gray-600 
                  rounded 
                  bg-white dark:bg-gray-700 
                  text-gray-700 dark:text-gray-300
                  hover:bg-gray-50 dark:hover:bg-gray-600
                  disabled:opacity-50 disabled:cursor-not-allowed
                  transition-colors duration-200
                "
              >
                التالي
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default memo(OptimizedTable) as typeof OptimizedTable;
