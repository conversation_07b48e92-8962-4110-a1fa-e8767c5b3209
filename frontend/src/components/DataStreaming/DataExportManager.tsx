import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Progress } from '../ui/progress';
import { Badge } from '../ui/badge';
import {
  Download,
  FileText,
  Database,
  Users,
  ShoppingCart,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from '../ui/icons';
import toast from 'react-hot-toast';

interface ExportTask {
  id: string;
  task_type: string;
  status: string;
  progress_percentage: number;
  current_record: number;
  total_records: number;
  estimated_time_remaining?: number;
  error_message?: string;
  created_at: string;
}

interface ExportParameters {
  start_date?: string;
  end_date?: string;
  format_type: 'json' | 'csv';
  compress: boolean;
  tables?: string[];
  category?: string;
  low_stock?: boolean;
  with_debts?: boolean;
}

const DataExportManager: React.FC = () => {
  const [tasks, setTasks] = useState<ExportTask[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [exportParams, setExportParams] = useState<ExportParameters>({
    format_type: 'json',
    compress: true
  });

  // مراقبة المهام النشطة
  useEffect(() => {
    const interval = setInterval(() => {
      tasks.forEach(task => {
        if (task.status === 'running' || task.status === 'pending') {
          monitorTask(task.id);
        }
      });
    }, 2000);

    return () => clearInterval(interval);
  }, [tasks]);

  const createExportTask = async (taskType: string, parameters: ExportParameters) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/data-streaming/tasks/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          task_type: taskType,
          parameters
        })
      });

      if (!response.ok) {
        throw new Error('فشل في إنشاء مهمة التصدير');
      }

      const result = await response.json();
      
      const newTask: ExportTask = {
        id: result.task_id,
        task_type: taskType,
        status: 'pending',
        progress_percentage: 0,
        current_record: 0,
        total_records: 0,
        created_at: new Date().toISOString()
      };

      setTasks(prev => [newTask, ...prev]);
      toast.success('تم إنشاء مهمة التصدير بنجاح');
      
      return result.task_id;
    } catch (error) {
      toast.error('خطأ في إنشاء مهمة التصدير');
      console.error('Export task creation error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const monitorTask = async (taskId: string) => {
    try {
      const response = await fetch(`/api/data-streaming/tasks/${taskId}/progress`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) return;

      const progress = await response.json();
      
      setTasks(prev => prev.map(task => 
        task.id === taskId ? { ...task, ...progress } : task
      ));

      if (progress.status === 'completed') {
        toast.success('اكتمل التصدير بنجاح!');
      } else if (progress.status === 'failed') {
        toast.error(`فشل التصدير: ${progress.error_message}`);
      }
    } catch (error) {
      console.error('Task monitoring error:', error);
    }
  };

  const downloadTaskResult = async (taskId: string) => {
    try {
      const response = await fetch(`/api/data-streaming/tasks/${taskId}/download`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('فشل في تحميل الملف');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `export_${taskId}.${exportParams.format_type}${exportParams.compress ? '.gz' : ''}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success('تم تحميل الملف بنجاح');
    } catch (error) {
      toast.error('خطأ في تحميل الملف');
      console.error('Download error:', error);
    }
  };

  const handleQuickExport = async (type: string) => {
    let taskType = '';
    let parameters = { ...exportParams };

    switch (type) {
      case 'sales':
        taskType = 'sales_export';
        break;
      case 'products':
        taskType = 'products_export';
        break;
      case 'customers':
        taskType = 'customers_export';
        break;
      case 'bulk':
        taskType = 'bulk_export';
        parameters.tables = ['sales', 'products', 'customers'];
        break;
    }

    await createExportTask(taskType, parameters);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'running':
        return <Clock className="h-4 w-4 text-blue-500 animate-spin" />;
      default:
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      completed: 'default',
      failed: 'destructive',
      running: 'secondary',
      pending: 'outline'
    };

    const labels: Record<string, string> = {
      completed: 'مكتمل',
      failed: 'فاشل',
      running: 'قيد التنفيذ',
      pending: 'في الانتظار'
    };

    return (
      <Badge variant={variants[status] || 'outline'}>
        {labels[status] || status}
      </Badge>
    );
  };

  const formatTimeRemaining = (seconds?: number) => {
    if (!seconds) return 'غير محدد';
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
      return `${minutes}د ${remainingSeconds}ث`;
    }
    return `${remainingSeconds}ث`;
  };

  return (
    <div className="space-y-6">
      {/* أدوات التصدير السريع */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            تصدير البيانات
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <Button
              onClick={() => handleQuickExport('sales')}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              <ShoppingCart className="h-4 w-4" />
              تصدير المبيعات
            </Button>
            
            <Button
              onClick={() => handleQuickExport('products')}
              disabled={isLoading}
              variant="outline"
              className="flex items-center gap-2"
            >
              <FileText className="h-4 w-4" />
              تصدير المنتجات
            </Button>
            
            <Button
              onClick={() => handleQuickExport('customers')}
              disabled={isLoading}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Users className="h-4 w-4" />
              تصدير العملاء
            </Button>
            
            <Button
              onClick={() => handleQuickExport('bulk')}
              disabled={isLoading}
              variant="secondary"
              className="flex items-center gap-2"
            >
              <Database className="h-4 w-4" />
              تصدير شامل
            </Button>
          </div>

          {/* إعدادات التصدير */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
            <div>
              <label className="block text-sm font-medium mb-2">نوع التنسيق</label>
              <select
                value={exportParams.format_type}
                onChange={(e) => setExportParams(prev => ({ 
                  ...prev, 
                  format_type: e.target.value as 'json' | 'csv' 
                }))}
                className="w-full p-2 border rounded-md"
              >
                <option value="json">JSON</option>
                <option value="csv">CSV</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">تاريخ البداية</label>
              <input
                type="date"
                value={exportParams.start_date || ''}
                onChange={(e) => setExportParams(prev => ({ 
                  ...prev, 
                  start_date: e.target.value 
                }))}
                className="w-full p-2 border rounded-md"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">تاريخ النهاية</label>
              <input
                type="date"
                value={exportParams.end_date || ''}
                onChange={(e) => setExportParams(prev => ({ 
                  ...prev, 
                  end_date: e.target.value 
                }))}
                className="w-full p-2 border rounded-md"
              />
            </div>
          </div>

          <div className="mt-4">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={exportParams.compress}
                onChange={(e) => setExportParams(prev => ({ 
                  ...prev, 
                  compress: e.target.checked 
                }))}
              />
              <span className="text-sm">ضغط الملفات (GZIP)</span>
            </label>
          </div>
        </CardContent>
      </Card>

      {/* قائمة المهام */}
      <Card>
        <CardHeader>
          <CardTitle>مهام التصدير</CardTitle>
        </CardHeader>
        <CardContent>
          {tasks.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              لا توجد مهام تصدير حالياً
            </div>
          ) : (
            <div className="space-y-4">
              {tasks.map((task) => (
                <div key={task.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(task.status)}
                      <span className="font-medium">
                        {task.task_type.replace('_export', '').replace('_', ' ')}
                      </span>
                      {getStatusBadge(task.status)}
                    </div>
                    
                    {task.status === 'completed' && (
                      <Button
                        size="sm"
                        onClick={() => downloadTaskResult(task.id)}
                        className="flex items-center gap-1"
                      >
                        <Download className="h-3 w-3" />
                        تحميل
                      </Button>
                    )}
                  </div>
                  
                  {task.status === 'running' && (
                    <div className="space-y-2">
                      <Progress value={task.progress_percentage} className="w-full" />
                      <div className="flex justify-between text-sm text-gray-600">
                        <span>
                          {task.current_record.toLocaleString()} / {task.total_records.toLocaleString()}
                        </span>
                        <span>
                          الوقت المتبقي: {formatTimeRemaining(task.estimated_time_remaining)}
                        </span>
                      </div>
                    </div>
                  )}
                  
                  {task.status === 'failed' && task.error_message && (
                    <div className="text-red-600 text-sm mt-2">
                      خطأ: {task.error_message}
                    </div>
                  )}
                  
                  <div className="text-xs text-gray-500 mt-2">
                    تم الإنشاء: {new Date(task.created_at).toLocaleString('ar-SA')}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default DataExportManager;
