import React, { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import {
  TrendingUp,
  Activity,
  Database,
  Clock,
  CheckCircle,
  XCircle,
  RefreshCw,
  Trash2
} from '../ui/icons';
import toast from 'react-hot-toast';

// مكونات الرسوم البيانية البديلة
const ResponsiveContainer: React.FC<{ width: string; height: number; children: React.ReactNode }> = ({ children }) => (
  <div style={{ width: '100%', height: '300px' }}>{children}</div>
);

const PieChart: React.FC<{ children: React.ReactNode }> = () => (
  <div className="flex items-center justify-center h-full">
    <div className="text-gray-500">رسم بياني دائري (يتطلب مكتبة recharts)</div>
  </div>
);

const Pie: React.FC<any> = () => null;
const Cell: React.FC<any> = () => null;
const Tooltip: React.FC<any> = () => null;
const LineChart: React.FC<{ data: any; children: React.ReactNode }> = () => (
  <div className="flex items-center justify-center h-full">
    <div className="text-gray-500">رسم بياني خطي (يتطلب مكتبة recharts)</div>
  </div>
);
const Line: React.FC<any> = () => null;
const CartesianGrid: React.FC<any> = () => null;
const XAxis: React.FC<any> = () => null;
const YAxis: React.FC<any> = () => null;
const Bar: React.FC<any> = () => null;

// Hook بديل مبسط
const useDataStreaming = () => ({
  tasks: [] as any[],
  metrics: {
    total_exports: 0,
    successful_exports: 0,
    failed_exports: 0,
    average_export_time: 0,
    total_data_exported_mb: 0,
    most_exported_table: 'sales',
    peak_usage_hour: 14
  },
  taskStats: { total: 0, pending: 0, running: 0, completed: 0, failed: 0 },
  fetchMetrics: async () => {},
  cleanupOldFiles: async (_: number) => {},
  clearCompletedTasks: () => {}
});

const StreamingMetrics: React.FC = () => {
  const {
    tasks,
    metrics,
    taskStats,
    fetchMetrics,
    cleanupOldFiles,
    clearCompletedTasks
  } = useDataStreaming();

  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isCleaning, setIsCleaning] = useState(false);

  // جلب المقاييس عند التحميل
  useEffect(() => {
    fetchMetrics();
  }, [fetchMetrics]);

  // تحديث المقاييس
  const handleRefreshMetrics = async () => {
    setIsRefreshing(true);
    try {
      await fetchMetrics();
      toast.success('تم تحديث المقاييس بنجاح');
    } catch (error) {
      toast.error('خطأ في تحديث المقاييس');
    } finally {
      setIsRefreshing(false);
    }
  };

  // تنظيف الملفات القديمة
  const handleCleanup = async () => {
    setIsCleaning(true);
    try {
      await cleanupOldFiles(24);
      await fetchMetrics(); // تحديث المقاييس بعد التنظيف
    } finally {
      setIsCleaning(false);
    }
  };

  // بيانات الرسم البياني للمهام
  const taskChartData = [
    { name: 'مكتملة', value: taskStats.completed, color: '#10b981' },
    { name: 'قيد التنفيذ', value: taskStats.running, color: '#3b82f6' },
    { name: 'في الانتظار', value: taskStats.pending, color: '#f59e0b' },
    { name: 'فاشلة', value: taskStats.failed, color: '#ef4444' }
  ];

  // بيانات الأداء اليومي (مثال)
  const performanceData = [
    { day: 'الأحد', exports: 12, success_rate: 95 },
    { day: 'الاثنين', exports: 18, success_rate: 98 },
    { day: 'الثلاثاء', exports: 15, success_rate: 92 },
    { day: 'الأربعاء', exports: 22, success_rate: 96 },
    { day: 'الخميس', exports: 19, success_rate: 94 },
    { day: 'الجمعة', exports: 8, success_rate: 100 },
    { day: 'السبت', exports: 5, success_rate: 100 }
  ];

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}س ${minutes}د`;
    } else if (minutes > 0) {
      return `${minutes}د ${secs}ث`;
    } else {
      return `${secs}ث`;
    }
  };

  return (
    <div className="space-y-6">
      {/* شريط الأدوات */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">مقاييس تدفق البيانات</h2>
        <div className="flex gap-2">
          <Button
            onClick={handleRefreshMetrics}
            disabled={isRefreshing}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            تحديث
          </Button>
          <Button
            onClick={clearCompletedTasks}
            variant="outline"
            size="sm"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            مسح المكتملة
          </Button>
          <Button
            onClick={handleCleanup}
            disabled={isCleaning}
            variant="outline"
            size="sm"
          >
            <Database className={`h-4 w-4 mr-2 ${isCleaning ? 'animate-pulse' : ''}`} />
            تنظيف الملفات
          </Button>
        </div>
      </div>

      {/* بطاقات الإحصائيات الرئيسية */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي المهام</p>
                <p className="text-2xl font-bold">{taskStats.total}</p>
              </div>
              <Activity className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">مهام مكتملة</p>
                <p className="text-2xl font-bold text-green-600">{taskStats.completed}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">مهام نشطة</p>
                <p className="text-2xl font-bold text-blue-600">{taskStats.running + taskStats.pending}</p>
              </div>
              <Clock className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">مهام فاشلة</p>
                <p className="text-2xl font-bold text-red-600">{taskStats.failed}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* مقاييس النظام */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-2">
                <p className="text-sm font-medium text-gray-600">معدل النجاح</p>
                <TrendingUp className="h-4 w-4 text-green-500" />
              </div>
              <p className="text-2xl font-bold">
                {metrics.total_exports > 0 
                  ? Math.round((metrics.successful_exports / metrics.total_exports) * 100)
                  : 0}%
              </p>
              <p className="text-xs text-gray-500">
                {metrics.successful_exports} من {metrics.total_exports}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-2">
                <p className="text-sm font-medium text-gray-600">متوسط وقت التصدير</p>
                <Clock className="h-4 w-4 text-blue-500" />
              </div>
              <p className="text-2xl font-bold">
                {formatDuration(metrics.average_export_time)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-2">
                <p className="text-sm font-medium text-gray-600">إجمالي البيانات المصدرة</p>
                <Database className="h-4 w-4 text-purple-500" />
              </div>
              <p className="text-2xl font-bold">
                {formatFileSize(metrics.total_data_exported_mb * 1024 * 1024)}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* الرسوم البيانية */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* توزيع المهام */}
        <Card>
          <CardHeader>
            <CardTitle>توزيع المهام</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={taskChartData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }: any) => `${name}: ${value}`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {taskChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* الأداء اليومي */}
        <Card>
          <CardHeader>
            <CardTitle>الأداء الأسبوعي</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={performanceData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip />
                <Bar yAxisId="left" dataKey="exports" fill="#3b82f6" name="عدد التصديرات" />
                <Line 
                  yAxisId="right" 
                  type="monotone" 
                  dataKey="success_rate" 
                  stroke="#10b981" 
                  strokeWidth={2}
                  name="معدل النجاح (%)"
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* معلومات إضافية */}
      {metrics && (
        <Card>
          <CardHeader>
            <CardTitle>معلومات النظام</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-sm font-medium">الجدول الأكثر تصديراً</span>
                <Badge variant="secondary">{metrics.most_exported_table}</Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-sm font-medium">ساعة الذروة</span>
                <Badge variant="outline">{metrics.peak_usage_hour}:00</Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-sm font-medium">المهام الفاشلة</span>
                <Badge variant="destructive">{metrics.failed_exports}</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* قائمة المهام الحديثة */}
      <Card>
        <CardHeader>
          <CardTitle>المهام الحديثة</CardTitle>
        </CardHeader>
        <CardContent>
          {tasks.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              لا توجد مهام حالياً
            </div>
          ) : (
            <div className="space-y-2">
              {tasks.slice(0, 5).map((task) => (
                <div key={task.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className={`w-2 h-2 rounded-full ${
                      task.status === 'completed' ? 'bg-green-500' :
                      task.status === 'running' ? 'bg-blue-500' :
                      task.status === 'failed' ? 'bg-red-500' : 'bg-yellow-500'
                    }`} />
                    <span className="font-medium">{task.task_type}</span>
                    <Badge variant="outline" className="text-xs">
                      {task.status === 'completed' ? 'مكتمل' :
                       task.status === 'running' ? 'قيد التنفيذ' :
                       task.status === 'failed' ? 'فاشل' : 'في الانتظار'}
                    </Badge>
                  </div>
                  
                  <div className="text-sm text-gray-500">
                    {new Date(task.created_at).toLocaleString('ar-SA')}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default StreamingMetrics;
