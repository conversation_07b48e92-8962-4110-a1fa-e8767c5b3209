import React, { useState, useEffect } from 'react';
import {
  FaDesktop,
  FaMobile,
  FaTabletAlt,
  FaLaptop,
  FaCheckCircle,
  FaTimesCircle,
  FaClock,
  FaShieldAlt,
  FaNetworkWired,
  FaGlobe,
  FaHome
} from 'react-icons/fa';

interface DeviceInfo {
  device_id: string;
  hostname: string;
  system: string;
  platform: string;
  browser: string;
  browser_version?: string;
  os_version?: string;
  device_type: string;
  client_ip: string;
  is_main_server: boolean;
  is_local_access: boolean;
  status: 'online' | 'offline' | 'recently_active' | 'pending_approval';
  last_access: string;
  current_user?: string;
  screen_resolution?: string;
  timezone?: string;
  language?: string;
  is_mobile?: boolean;
  is_tablet?: boolean;
}

interface DevicesSummary {
  total_devices: number;
  main_server_count: number;
  local_devices_count: number;
  remote_devices_count: number;
  online_devices: number;
  recently_active_devices: number;
  offline_devices: number;
  devices: DeviceInfo[];
}

const EnhancedDeviceManager: React.FC = () => {
  const [devicesSummary, setDevicesSummary] = useState<DevicesSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDevices();
    // تم تعطيل التحديث التلقائي لتجنب التضارب مع useConnectedDevices
    // المستخدم يمكنه تحديث البيانات يدوياً عند الحاجة
    // const interval = setInterval(fetchDevices, 30000); // تحديث كل 30 ثانية
    // return () => clearInterval(interval);
  }, []);

  const fetchDevices = async () => {
    try {
      const response = await fetch('/api/settings/connected-devices');
      if (response.ok) {
        const data = await response.json();
        setDevicesSummary(data);
        setError(null);
      } else {
        setError('فشل في جلب بيانات الأجهزة');
      }
    } catch (err) {
      setError('خطأ في الاتصال بالخادم');
    } finally {
      setLoading(false);
    }
  };

  const getDeviceIcon = (device: DeviceInfo) => {
    if (device.is_main_server) {
      return <FaDesktop className="text-blue-600" />;
    }

    if (device.is_mobile) {
      return <FaMobile className="text-green-600" />;
    }

    if (device.is_tablet) {
      return <FaTabletAlt className="text-purple-600" />;
    }

    if (device.device_type === 'desktop') {
      return <FaLaptop className="text-gray-600" />;
    }

    return <FaNetworkWired className="text-gray-500" />;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <FaCheckCircle className="text-green-500" />;
      case 'recently_active':
        return <FaClock className="text-yellow-500" />;
      case 'pending_approval':
        return <FaClock className="text-orange-500" />;
      case 'offline':
      default:
        return <FaTimesCircle className="text-red-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'online':
        return 'متصل';
      case 'recently_active':
        return 'نشط مؤخراً';
      case 'pending_approval':
        return 'في انتظار الموافقة';
      case 'offline':
      default:
        return 'غير متصل';
    }
  };

  const getConnectionTypeIcon = (device: DeviceInfo) => {
    if (device.is_main_server) {
      return <FaShieldAlt className="text-blue-500" title="الخادم الرئيسي" />;
    }
    if (device.is_local_access) {
      return <FaHome className="text-green-500" title="اتصال محلي" />;
    }
    return <FaGlobe className="text-orange-500" title="اتصال بعيد" />;
  };

  const formatLastAccess = (lastAccess: string) => {
    try {
      const date = new Date(lastAccess);
      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const diffMins = Math.floor(diffMs / 60000);

      if (diffMins < 1) return 'الآن';
      if (diffMins < 60) return `منذ ${diffMins} دقيقة`;

      const diffHours = Math.floor(diffMins / 60);
      if (diffHours < 24) return `منذ ${diffHours} ساعة`;

      const diffDays = Math.floor(diffHours / 24);
      return `منذ ${diffDays} يوم`;
    } catch {
      return 'غير معروف';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="mr-3">جاري تحميل الأجهزة...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center">
          <FaTimesCircle className="text-red-500 ml-2" />
          <span className="text-red-700">{error}</span>
        </div>
        <button
          onClick={fetchDevices}
          className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          إعادة المحاولة
        </button>
      </div>
    );
  }

  if (!devicesSummary) {
    return (
      <div className="text-center p-8 text-gray-500">
        لا توجد بيانات أجهزة متاحة
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* إحصائيات الأجهزة */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
          <FaDesktop className="text-2xl text-blue-600 mx-auto mb-2" />
          <div className="text-2xl font-bold text-blue-800">{devicesSummary.total_devices}</div>
          <div className="text-sm text-blue-600">إجمالي الأجهزة</div>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
          <FaCheckCircle className="text-2xl text-green-600 mx-auto mb-2" />
          <div className="text-2xl font-bold text-green-800">{devicesSummary.online_devices}</div>
          <div className="text-sm text-green-600">متصل الآن</div>
        </div>

        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 text-center">
          <FaGlobe className="text-2xl text-orange-600 mx-auto mb-2" />
          <div className="text-2xl font-bold text-orange-800">{devicesSummary.remote_devices_count}</div>
          <div className="text-sm text-orange-600">أجهزة بعيدة</div>
        </div>

        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
          <FaHome className="text-2xl text-purple-600 mx-auto mb-2" />
          <div className="text-2xl font-bold text-purple-800">{devicesSummary.local_devices_count}</div>
          <div className="text-sm text-purple-600">أجهزة محلية</div>
        </div>
      </div>

      {/* قائمة الأجهزة */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-xl font-bold text-gray-900">الأجهزة المتصلة</h3>
          <p className="text-gray-600">عرض تفصيلي لجميع الأجهزة المتصلة بالنظام</p>
        </div>

        <div className="divide-y divide-gray-200">
          {devicesSummary.devices.map((device) => (
            <div key={device.device_id} className="p-6 hover:bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="text-2xl">
                    {getDeviceIcon(device)}
                  </div>

                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-semibold text-gray-900">{device.hostname}</h4>
                      {getConnectionTypeIcon(device)}
                    </div>

                    <div className="text-sm text-gray-600 space-y-1">
                      <div className="flex items-center gap-4">
                        <span><strong>النظام:</strong> {device.system} {device.os_version}</span>
                        <span><strong>المتصفح:</strong> {device.browser} {device.browser_version}</span>
                      </div>

                      <div className="flex items-center gap-4">
                        <span><strong>IP:</strong> {device.client_ip}</span>
                        <span><strong>آخر وصول:</strong> {formatLastAccess(device.last_access)}</span>
                      </div>

                      {device.current_user && (
                        <div><strong>المستخدم:</strong> {device.current_user}</div>
                      )}

                      {device.screen_resolution && (
                        <div><strong>دقة الشاشة:</strong> {device.screen_resolution}</div>
                      )}

                      {device.timezone && (
                        <div><strong>المنطقة الزمنية:</strong> {device.timezone}</div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  {getStatusIcon(device.status)}
                  <span className="text-sm font-medium">
                    {getStatusText(device.status)}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default EnhancedDeviceManager;
