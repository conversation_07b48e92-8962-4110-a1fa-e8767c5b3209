/**
 * مكون اختيار الوقت التفاعلي مع دعم العربية
 * يوفر واجهة تفاعلية لاختيار الوقت مع عرض صباحاً/مساءً بدلاً من AM/PM
 * يطبق مبادئ البرمجة الكائنية والتصميم الموحد للنظام
 */

import React, { useState, useRef, useEffect } from 'react';
import { FiClock, FiChevronUp, FiChevronDown } from 'react-icons/fi';

interface TimePickerInputProps {
  value: string; // HH:MM format (24-hour)
  onChange: (value: string) => void;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
}

const TimePickerInput: React.FC<TimePickerInputProps> = ({
  value,
  onChange,
  className = '',
  placeholder = 'اختر الوقت',
  disabled = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedHour, setSelectedHour] = useState(12);
  const [selectedMinute, setSelectedMinute] = useState(0);
  const [selectedPeriod, setSelectedPeriod] = useState<'صباحاً' | 'مساءً'>('صباحاً');
  const dropdownRef = useRef<HTMLDivElement>(null);

  // تحويل القيمة من تنسيق 24 ساعة إلى 12 ساعة
  useEffect(() => {
    if (value) {
      const [hourStr, minuteStr] = value.split(':');
      const hour24 = parseInt(hourStr);
      const minute = parseInt(minuteStr);

      if (hour24 === 0) {
        setSelectedHour(12);
        setSelectedPeriod('صباحاً');
      } else if (hour24 < 12) {
        setSelectedHour(hour24);
        setSelectedPeriod('صباحاً');
      } else if (hour24 === 12) {
        setSelectedHour(12);
        setSelectedPeriod('مساءً');
      } else {
        setSelectedHour(hour24 - 12);
        setSelectedPeriod('مساءً');
      }

      setSelectedMinute(minute);
    }
  }, [value]);

  // إغلاق القائمة عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // تحويل الوقت إلى تنسيق 24 ساعة
  const convertTo24Hour = (hour: number, minute: number, period: 'صباحاً' | 'مساءً'): string => {
    let hour24 = hour;

    if (period === 'صباحاً' && hour === 12) {
      hour24 = 0;
    } else if (period === 'مساءً' && hour !== 12) {
      hour24 = hour + 12;
    }

    return `${hour24.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
  };

  // تطبيق التغيير
  const applyChange = (hour: number, minute: number, period: 'صباحاً' | 'مساءً') => {
    const time24 = convertTo24Hour(hour, minute, period);
    onChange(time24);
  };

  // تغيير الساعة
  const changeHour = (delta: number) => {
    let newHour = selectedHour + delta;
    if (newHour > 12) newHour = 1;
    if (newHour < 1) newHour = 12;
    
    setSelectedHour(newHour);
    applyChange(newHour, selectedMinute, selectedPeriod);
  };

  // تغيير الدقيقة
  const changeMinute = (delta: number) => {
    let newMinute = selectedMinute + delta;
    if (newMinute >= 60) newMinute = 0;
    if (newMinute < 0) newMinute = 59;
    
    setSelectedMinute(newMinute);
    applyChange(selectedHour, newMinute, selectedPeriod);
  };

  // تغيير الفترة (صباحاً/مساءً)
  const togglePeriod = () => {
    const newPeriod = selectedPeriod === 'صباحاً' ? 'مساءً' : 'صباحاً';
    setSelectedPeriod(newPeriod);
    applyChange(selectedHour, selectedMinute, newPeriod);
  };

  // تنسيق العرض
  const formatDisplayTime = (): string => {
    if (!value) return placeholder;
    return `${selectedHour}:${selectedMinute.toString().padStart(2, '0')} ${selectedPeriod}`;
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* حقل الإدخال */}
      <div
        onClick={() => !disabled && setIsOpen(!isOpen)}
        className={`
          bg-white dark:bg-gray-700 px-4 py-2 rounded-xl border-2 border-gray-300 dark:border-gray-600
          h-10 flex items-center justify-between cursor-pointer transition-all duration-200
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-gray-400 dark:hover:border-gray-500'}
          ${isOpen ? 'border-primary-500 ring-4 ring-primary-500/20' : ''}
        `}
      >
        <div className="flex items-center gap-3">
          <FiClock className="text-gray-500 dark:text-gray-400" />
          <span className="text-gray-900 dark:text-gray-100 font-medium">
            {formatDisplayTime()}
          </span>
        </div>
        <FiChevronDown 
          className={`text-gray-500 dark:text-gray-400 transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`} 
        />
      </div>

      {/* قائمة اختيار الوقت */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-700 border-2 border-gray-300 dark:border-gray-600 rounded-xl shadow-lg z-50">
          <div className="p-4">
            <div className="flex items-center justify-center gap-4">
              {/* اختيار الساعة */}
              <div className="flex flex-col items-center">
                <button
                  type="button"
                  onClick={() => changeHour(1)}
                  className="p-1 text-gray-500 hover:text-primary-500 transition-colors"
                >
                  <FiChevronUp />
                </button>
                <div className="w-10 h-10 flex items-center justify-center bg-gray-100 dark:bg-gray-600 rounded-lg font-bold text-lg">
                  {selectedHour}
                </div>
                <button
                  type="button"
                  onClick={() => changeHour(-1)}
                  className="p-1 text-gray-500 hover:text-primary-500 transition-colors"
                >
                  <FiChevronDown />
                </button>
              </div>

              {/* فاصل */}
              <div className="text-2xl font-bold text-gray-500">:</div>

              {/* اختيار الدقيقة */}
              <div className="flex flex-col items-center">
                <button
                  type="button"
                  onClick={() => changeMinute(5)}
                  className="p-1 text-gray-500 hover:text-primary-500 transition-colors"
                >
                  <FiChevronUp />
                </button>
                <div className="w-10 h-10 flex items-center justify-center bg-gray-100 dark:bg-gray-600 rounded-lg font-bold text-lg">
                  {selectedMinute.toString().padStart(2, '0')}
                </div>
                <button
                  type="button"
                  onClick={() => changeMinute(-5)}
                  className="p-1 text-gray-500 hover:text-primary-500 transition-colors"
                >
                  <FiChevronDown />
                </button>
              </div>

              {/* اختيار الفترة */}
              <div className="flex flex-col items-center">
                <button
                  type="button"
                  onClick={togglePeriod}
                  className={`
                    px-3 py-2 rounded-lg font-medium text-sm transition-all duration-200
                    ${selectedPeriod === 'صباحاً' 
                      ? 'bg-yellow-100 text-yellow-700 border-2 border-yellow-300' 
                      : 'bg-blue-100 text-blue-700 border-2 border-blue-300'
                    }
                    hover:scale-105 hover:shadow-md
                  `}
                >
                  {selectedPeriod}
                </button>
              </div>
            </div>

            {/* أزرار سريعة */}
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
              <div className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
                أوقات سريعة:
              </div>
              <div className="flex flex-wrap gap-2">
                {[
                  { label: '9:00 صباحاً', value: '09:00' },
                  { label: '12:00 ظهراً', value: '12:00' },
                  { label: '2:00 مساءً', value: '14:00' },
                  { label: '5:00 مساءً', value: '17:00' },
                  { label: '8:00 مساءً', value: '20:00' }
                ].map((preset) => (
                  <button
                    key={preset.value}
                    type="button"
                    onClick={() => {
                      onChange(preset.value);
                      setIsOpen(false);
                    }}
                    className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors"
                  >
                    {preset.label}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TimePickerInput;
