/**
 * مثال تطبيقي لمكون مؤشر المقارنة
 * يُظهر جميع الحالات والاستخدامات المختلفة للمكون
 */

import React from 'react';
import ComparisonIndicator from './ComparisonIndicator';
import { ComparisonData } from '../services/comparisonIndicatorService';
import { FaReceipt, FaMoneyBillWave, FaCoins } from 'react-icons/fa';

const ComparisonIndicatorExample: React.FC = () => {
  // بيانات تجريبية للمقارنات
  const salesUpComparison: ComparisonData = {
    current: 15750,
    previous: 12800,
    difference: 2950,
    percentageChange: 23.7, // سيظهر كـ 24%
    trend: 'up',
    isPositive: true
  };

  const debtsDownComparison: ComparisonData = {
    current: 2340,
    previous: 2680,
    difference: -340,
    percentageChange: -12.3, // سيظهر كـ 12%
    trend: 'down',
    isPositive: false
  };

  const profitsNeutralComparison: ComparisonData = {
    current: 8920,
    previous: 8920,
    difference: 0,
    percentageChange: 0.2, // سيظهر كـ 0%
    trend: 'neutral',
    isPositive: true
  };

  const largeNumberComparison: ComparisonData = {
    current: 1500000,
    previous: 1200000,
    difference: 300000,
    percentageChange: 25.0,
    trend: 'up',
    isPositive: true
  };

  return (
    <div className="p-8 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-8">
          أمثلة مؤشر المقارنة
        </h1>

        {/* الأحجام المختلفة */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-6">
            الأحجام المختلفة
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* حجم صغير */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg">
              <h3 className="text-lg font-semibold mb-4">حجم صغير (Small)</h3>
              <ComparisonIndicator
                comparison={salesUpComparison}
                comparisonText="مقارنة بمبيعات أمس"
                size="small"
              />
            </div>

            {/* حجم متوسط */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg">
              <h3 className="text-lg font-semibold mb-4">حجم متوسط (Medium)</h3>
              <ComparisonIndicator
                comparison={salesUpComparison}
                comparisonText="مقارنة بمبيعات أمس"
                size="medium"
              />
            </div>

            {/* حجم كبير */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg">
              <h3 className="text-lg font-semibold mb-4">حجم كبير (Large)</h3>
              <ComparisonIndicator
                comparison={salesUpComparison}
                comparisonText="مقارنة بمبيعات أمس"
                size="large"
              />
            </div>
          </div>
        </section>

        {/* الاتجاهات المختلفة */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-6">
            الاتجاهات المختلفة
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* صعود */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg">
              <h3 className="text-lg font-semibold mb-4 text-green-600">صعود (Up)</h3>
              <ComparisonIndicator
                comparison={salesUpComparison}
                comparisonText="مقارنة بمبيعات أمس"
                size="medium"
              />
            </div>

            {/* هبوط */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg">
              <h3 className="text-lg font-semibold mb-4 text-red-600">هبوط (Down)</h3>
              <ComparisonIndicator
                comparison={debtsDownComparison}
                comparisonText="مقارنة بديون أمس"
                size="medium"
              />
            </div>

            {/* ثبات */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg">
              <h3 className="text-lg font-semibold mb-4 text-gray-600">ثبات (Neutral)</h3>
              <ComparisonIndicator
                comparison={profitsNeutralComparison}
                comparisonText="مقارنة بأرباح أمس"
                size="medium"
              />
            </div>
          </div>
        </section>

        {/* خيارات العرض */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-6">
            خيارات العرض
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* مع النسبة المئوية فقط */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg">
              <h3 className="text-lg font-semibold mb-4">النسبة المئوية فقط</h3>
              <ComparisonIndicator
                comparison={salesUpComparison}
                comparisonText="مقارنة بمبيعات أمس"
                showPercentage={true}
                showDifference={false}
              />
            </div>

            {/* مع النسبة والفرق */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg">
              <h3 className="text-lg font-semibold mb-4">النسبة والفرق معاً</h3>
              <ComparisonIndicator
                comparison={salesUpComparison}
                comparisonText="مقارنة بمبيعات أمس"
                showPercentage={true}
                showDifference={true}
              />
            </div>
          </div>
        </section>

        {/* بطاقات إحصائية كاملة */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-6">
            بطاقات إحصائية مع المؤشرات
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* بطاقة مبيعات */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                    مبيعات اليوم
                  </h3>
                  <p className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                    15,750 د.ل
                  </p>
                </div>
                <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-full">
                  <FaReceipt className="text-green-600 dark:text-green-400" />
                </div>
              </div>
              
              <div className="border-t border-gray-200 dark:border-gray-700 pt-3">
                <ComparisonIndicator
                  comparison={salesUpComparison}
                  comparisonText="مقارنة بمبيعات أمس"
                  size="small"
                />
              </div>
            </div>

            {/* بطاقة ديون */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                    ديون اليوم
                  </h3>
                  <p className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                    2,340 د.ل
                  </p>
                </div>
                <div className="p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-full">
                  <FaMoneyBillWave className="text-yellow-600 dark:text-yellow-400" />
                </div>
              </div>
              
              <div className="border-t border-gray-200 dark:border-gray-700 pt-3">
                <ComparisonIndicator
                  comparison={debtsDownComparison}
                  comparisonText="مقارنة بديون أمس"
                  size="small"
                />
              </div>
            </div>

            {/* بطاقة أرباح */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                    أرباح اليوم
                  </h3>
                  <p className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                    8,920 د.ل
                  </p>
                </div>
                <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                  <FaCoins className="text-blue-600 dark:text-blue-400" />
                </div>
              </div>
              
              <div className="border-t border-gray-200 dark:border-gray-700 pt-3">
                <ComparisonIndicator
                  comparison={profitsNeutralComparison}
                  comparisonText="مقارنة بأرباح أمس"
                  size="small"
                />
              </div>
            </div>
          </div>
        </section>

        {/* أرقام كبيرة */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-6">
            التعامل مع الأرقام الكبيرة
          </h2>
          
          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg max-w-md">
            <h3 className="text-lg font-semibold mb-4">مبيعات الشهر</h3>
            <p className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              1,500,000 د.ل
            </p>
            
            <ComparisonIndicator
              comparison={largeNumberComparison}
              comparisonText="مقارنة بالشهر الماضي"
              showPercentage={true}
              showDifference={true}
              size="medium"
            />
          </div>
        </section>

        {/* ملاحظة للمطورين */}
        <section className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-xl">
          <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-3">
            ملاحظة للمطورين
          </h3>
          <p className="text-blue-700 dark:text-blue-300">
            هذا المكون يدعم جميع الأحجام والاتجاهات ويتكامل بسهولة مع بطاقات الإحصائيات الموجودة.
            تم تحسين الدوائر لتكون أصغر حجماً والنص موحد بحجم text-sm مع باقي البطاقات.
            النسب المئوية تظهر بدون فاصلة عشرية وبدون علامات (+/-).
          </p>
        </section>
      </div>
    </div>
  );
};

export default ComparisonIndicatorExample;
