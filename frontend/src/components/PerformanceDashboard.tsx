/**
 * لوحة مراقبة الأداء - تعرض إحصائيات الأداء والصحة
 */

import React, { useState, useEffect } from 'react';
// خدمة الاتصال متوفرة عبر unifiedConnectionService
import { appStateManager } from '../services/appStateManager';
import api from '../lib/axios';

interface PerformanceData {
  system: {
    cpu_percent: number;
    memory_percent: number;
    disk_percent: number;
  };
  requests: {
    total_requests: number;
    active_requests: number;
    slow_requests: number;
    failed_requests: number;
    average_response_time: number;
  };
  database: {
    status: string;
    connection: string;
    needs_optimization: boolean;
  };
}

const PerformanceDashboard: React.FC = () => {
  const [performanceData, setPerformanceData] = useState<PerformanceData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [autoRefresh, setAutoRefresh] = useState(true);

  const fetchPerformanceData = async () => {
    if (isLoading) return;

    setIsLoading(true);
    try {
      // جلب إحصائيات الأداء
      const [performanceResponse, databaseResponse] = await Promise.all([
        api.get('/api/system/performance'),
        api.get('/api/system/database/health')
      ]);

      const perfData = performanceResponse.data.data;
      const dbData = databaseResponse.data.data;

      setPerformanceData({
        system: perfData.system_resources || {
          cpu_percent: 0,
          memory_percent: 0,
          disk_percent: 0
        },
        requests: {
          total_requests: perfData.total_requests || 0,
          active_requests: perfData.active_requests || 0,
          slow_requests: perfData.slow_requests || 0,
          failed_requests: perfData.failed_requests || 0,
          average_response_time: perfData.average_response_time || 0
        },
        database: {
          status: dbData.status || 'unknown',
          connection: dbData.connection || 'unknown',
          needs_optimization: dbData.needs_optimization || false
        }
      });

      setLastUpdate(new Date());
    } catch (error) {
      console.error('Failed to fetch performance data:', error);
      appStateManager.addError('فشل في جلب بيانات الأداء');
    } finally {
      setIsLoading(false);
    }
  };

  const optimizeDatabase = async () => {
    setIsLoading(true);
    try {
      await api.post('/api/system/database/optimize');
      appStateManager.addWarning('تم بدء تحسين قاعدة البيانات');
      // إعادة جلب البيانات بعد التحسين
      setTimeout(fetchPerformanceData, 5000);
    } catch (error) {
      console.error('Failed to optimize database:', error);
      appStateManager.addError('فشل في تحسين قاعدة البيانات');
    } finally {
      setIsLoading(false);
    }
  };

  const resetPerformanceStats = async () => {
    setIsLoading(true);
    try {
      await api.post('/api/system/performance/reset');
      appStateManager.addWarning('تم إعادة تعيين إحصائيات الأداء');
      fetchPerformanceData();
    } catch (error) {
      console.error('Failed to reset performance stats:', error);
      appStateManager.addError('فشل في إعادة تعيين الإحصائيات');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchPerformanceData();

    let interval: NodeJS.Timeout | null = null;
    if (autoRefresh) {
      interval = setInterval(fetchPerformanceData, 30000); // كل 30 ثانية
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh]);

  const getStatusColor = (value: number, thresholds: { warning: number; critical: number }) => {
    if (value >= thresholds.critical) return 'text-red-600';
    if (value >= thresholds.warning) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getStatusBg = (value: number, thresholds: { warning: number; critical: number }) => {
    if (value >= thresholds.critical) return 'bg-red-100';
    if (value >= thresholds.warning) return 'bg-yellow-100';
    return 'bg-green-100';
  };

  if (!performanceData) {
    return (
      <div className="p-6 bg-white rounded-lg shadow">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-3 bg-gray-200 rounded"></div>
            <div className="h-3 bg-gray-200 rounded w-5/6"></div>
            <div className="h-3 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-800">مراقبة الأداء</h2>
        <div className="flex items-center gap-3">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="mr-2"
            />
            <span className="text-sm text-gray-600">تحديث تلقائي</span>
          </label>
          <button
            onClick={fetchPerformanceData}
            disabled={isLoading}
            className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {isLoading ? 'جاري التحديث...' : 'تحديث'}
          </button>
        </div>
      </div>

      {/* System Resources */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className={`p-4 rounded-lg ${getStatusBg(performanceData.system.cpu_percent, { warning: 70, critical: 90 })}`}>
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">المعالج</span>
            <span className={`text-lg font-bold ${getStatusColor(performanceData.system.cpu_percent, { warning: 70, critical: 90 })}`}>
              {performanceData.system.cpu_percent.toFixed(1)}%
            </span>
          </div>
          <div className="mt-2 bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full ${
                performanceData.system.cpu_percent >= 90 ? 'bg-red-500' :
                performanceData.system.cpu_percent >= 70 ? 'bg-yellow-500' : 'bg-green-500'
              }`}
              style={{ width: `${Math.min(performanceData.system.cpu_percent, 100)}%` }}
            ></div>
          </div>
        </div>

        <div className={`p-4 rounded-lg ${getStatusBg(performanceData.system.memory_percent, { warning: 80, critical: 95 })}`}>
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">الذاكرة</span>
            <span className={`text-lg font-bold ${getStatusColor(performanceData.system.memory_percent, { warning: 80, critical: 95 })}`}>
              {performanceData.system.memory_percent.toFixed(1)}%
            </span>
          </div>
          <div className="mt-2 bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full ${
                performanceData.system.memory_percent >= 95 ? 'bg-red-500' :
                performanceData.system.memory_percent >= 80 ? 'bg-yellow-500' : 'bg-green-500'
              }`}
              style={{ width: `${Math.min(performanceData.system.memory_percent, 100)}%` }}
            ></div>
          </div>
        </div>

        <div className={`p-4 rounded-lg ${getStatusBg(performanceData.system.disk_percent, { warning: 85, critical: 95 })}`}>
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">القرص الصلب</span>
            <span className={`text-lg font-bold ${getStatusColor(performanceData.system.disk_percent, { warning: 85, critical: 95 })}`}>
              {performanceData.system.disk_percent.toFixed(1)}%
            </span>
          </div>
          <div className="mt-2 bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full ${
                performanceData.system.disk_percent >= 95 ? 'bg-red-500' :
                performanceData.system.disk_percent >= 85 ? 'bg-yellow-500' : 'bg-green-500'
              }`}
              style={{ width: `${Math.min(performanceData.system.disk_percent, 100)}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Request Statistics */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">إحصائيات الطلبات</h3>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{performanceData.requests.total_requests}</div>
            <div className="text-sm text-gray-600">إجمالي الطلبات</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{performanceData.requests.active_requests}</div>
            <div className="text-sm text-gray-600">طلبات نشطة</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">{performanceData.requests.slow_requests}</div>
            <div className="text-sm text-gray-600">طلبات بطيئة</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{performanceData.requests.failed_requests}</div>
            <div className="text-sm text-gray-600">طلبات فاشلة</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {performanceData.requests.average_response_time.toFixed(0)}ms
            </div>
            <div className="text-sm text-gray-600">متوسط الاستجابة</div>
          </div>
        </div>
      </div>

      {/* Database Status */}
      <div className="bg-white p-6 rounded-lg shadow">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-800">حالة قاعدة البيانات</h3>
          <div className="flex gap-2">
            {performanceData.database.needs_optimization && (
              <button
                onClick={optimizeDatabase}
                disabled={isLoading}
                className="px-3 py-1 text-sm bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50"
              >
                تحسين قاعدة البيانات
              </button>
            )}
            <button
              onClick={resetPerformanceStats}
              disabled={isLoading}
              className="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600 disabled:opacity-50"
            >
              إعادة تعيين الإحصائيات
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center">
            <span className="text-sm text-gray-600 mr-2">الحالة:</span>
            <span className={`px-2 py-1 rounded text-xs ${
              performanceData.database.status === 'healthy' ? 'bg-green-100 text-green-800' :
              performanceData.database.status === 'warning' ? 'bg-yellow-100 text-yellow-800' :
              'bg-red-100 text-red-800'
            }`}>
              {performanceData.database.status === 'healthy' ? 'سليمة' :
               performanceData.database.status === 'warning' ? 'تحذير' : 'خطأ'}
            </span>
          </div>

          <div className="flex items-center">
            <span className="text-sm text-gray-600 mr-2">الاتصال:</span>
            <span className={`px-2 py-1 rounded text-xs ${
              performanceData.database.connection === 'ok' ? 'bg-green-100 text-green-800' :
              'bg-red-100 text-red-800'
            }`}>
              {performanceData.database.connection === 'ok' ? 'متصل' : 'منقطع'}
            </span>
          </div>

          <div className="flex items-center">
            <span className="text-sm text-gray-600 mr-2">يحتاج تحسين:</span>
            <span className={`px-2 py-1 rounded text-xs ${
              performanceData.database.needs_optimization ? 'bg-orange-100 text-orange-800' :
              'bg-green-100 text-green-800'
            }`}>
              {performanceData.database.needs_optimization ? 'نعم' : 'لا'}
            </span>
          </div>
        </div>
      </div>

      {/* Last Update */}
      <div className="text-sm text-gray-500 text-center">
        آخر تحديث: {lastUpdate.toLocaleString('ar-SA')}
      </div>
    </div>
  );
};

export default PerformanceDashboard;
