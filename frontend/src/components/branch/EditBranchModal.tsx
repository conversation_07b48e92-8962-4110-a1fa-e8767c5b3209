/**
 * نافذة تعديل الفرع
 * تتيح للمستخدم تعديل بيانات الفرع الموجود
 */

import React, { useState, useEffect } from 'react';
import { FiSave, FiMapPin, FiUser, FiClock } from 'react-icons/fi';
import Modal from '../Modal';
import { TextInput, NumberInput } from '../inputs';
import ToggleSwitch from '../ToggleSwitch';
import { useBranchStore } from '../../stores/branchStore';
import { Branch, BranchUpdate } from '../../services/branchService';

interface EditBranchModalProps {
  isOpen: boolean;
  onClose: () => void;
  branch: Branch | null;
  onSuccess?: () => void;
}

const EditBranchModal: React.FC<EditBranchModalProps> = ({
  isOpen,
  onClose,
  branch,
  onSuccess
}) => {
  const { updateBranch, updating, error } = useBranchStore();

  // Form state
  const [formData, setFormData] = useState<BranchUpdate>({});
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Initialize form data when branch changes
  useEffect(() => {
    if (branch) {
      setFormData({
        name: branch.name,
        code: branch.code,
        address: branch.address || '',
        phone: branch.phone || '',
        manager_name: branch.manager_name || '',
        email: branch.email || '',
        is_main: branch.is_main,
        is_active: branch.is_active,
        city: branch.city || '',
        region: branch.region || '',
        postal_code: branch.postal_code || '',
        max_daily_sales: branch.max_daily_sales,
        working_hours_start: branch.working_hours_start || '',
        working_hours_end: branch.working_hours_end || ''
      });
      setFormErrors({});
    }
  }, [branch]);

  // Handle input change
  const handleInputChange = (field: keyof BranchUpdate, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field
    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name?.trim()) {
      errors.name = 'اسم الفرع مطلوب';
    }

    if (!formData.code?.trim()) {
      errors.code = 'كود الفرع مطلوب';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'البريد الإلكتروني غير صحيح';
    }

    if (formData.working_hours_start && !/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(formData.working_hours_start)) {
      errors.working_hours_start = 'صيغة الوقت غير صحيحة (HH:MM)';
    }

    if (formData.working_hours_end && !/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(formData.working_hours_end)) {
      errors.working_hours_end = 'صيغة الوقت غير صحيحة (HH:MM)';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle submit
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!branch || !validateForm()) {
      return;
    }

    try {
      const success = await updateBranch(branch.id, formData);
      
      if (success && onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('خطأ في تحديث الفرع:', error);
    }
  };

  // Handle close
  const handleClose = () => {
    setFormData({});
    setFormErrors({});
    onClose();
  };

  if (!branch) {
    return null;
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={`تعديل الفرع: ${branch.name}`}
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Error Display */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4">
            <div className="text-red-600 dark:text-red-400 text-sm">
              {error}
            </div>
          </div>
        )}

        {/* Basic Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center">
            <FiMapPin className="ml-2 text-primary-600 dark:text-primary-400" />
            المعلومات الأساسية
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TextInput
              name="name"
              label="اسم الفرع *"
              value={formData.name || ''}
              onChange={(value) => handleInputChange('name', value)}
              error={formErrors.name}
              placeholder="أدخل اسم الفرع"
            />

            <TextInput
              name="code"
              label="كود الفرع *"
              value={formData.code || ''}
              onChange={(value) => handleInputChange('code', value)}
              error={formErrors.code}
              placeholder="أدخل كود الفرع"
            />
          </div>

          <TextInput
            name="address"
            label="العنوان"
            value={formData.address || ''}
            onChange={(value) => handleInputChange('address', value)}
            placeholder="أدخل عنوان الفرع"
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TextInput
              name="city"
              label="المدينة"
              value={formData.city || ''}
              onChange={(value) => handleInputChange('city', value)}
              placeholder="أدخل المدينة"
            />

            <TextInput
              name="region"
              label="المنطقة"
              value={formData.region || ''}
              onChange={(value) => handleInputChange('region', value)}
              placeholder="أدخل المنطقة"
            />
          </div>

          <TextInput
            name="postal_code"
            label="الرمز البريدي"
            value={formData.postal_code || ''}
            onChange={(value) => handleInputChange('postal_code', value)}
            placeholder="أدخل الرمز البريدي"
          />
        </div>

        {/* Manager Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center">
            <FiUser className="ml-2 text-primary-600 dark:text-primary-400" />
            معلومات المدير
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TextInput
              name="manager_name"
              label="اسم المدير"
              value={formData.manager_name || ''}
              onChange={(value) => handleInputChange('manager_name', value)}
              placeholder="أدخل اسم مدير الفرع"
            />

            <TextInput
              name="phone"
              label="رقم الهاتف"
              value={formData.phone || ''}
              onChange={(value) => handleInputChange('phone', value)}
              placeholder="أدخل رقم الهاتف"
            />
          </div>

          <TextInput
            name="email"
            label="البريد الإلكتروني"
            type="email"
            value={formData.email || ''}
            onChange={(value) => handleInputChange('email', value)}
            error={formErrors.email}
            placeholder="أدخل البريد الإلكتروني"
          />
        </div>

        {/* Working Hours */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center">
            <FiClock className="ml-2 text-primary-600 dark:text-primary-400" />
            ساعات العمل
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TextInput
              name="working_hours_start"
              label="بداية العمل"
              value={formData.working_hours_start || ''}
              onChange={(value) => handleInputChange('working_hours_start', value)}
              error={formErrors.working_hours_start}
              placeholder="08:00"
            />

            <TextInput
              name="working_hours_end"
              label="نهاية العمل"
              value={formData.working_hours_end || ''}
              onChange={(value) => handleInputChange('working_hours_end', value)}
              error={formErrors.working_hours_end}
              placeholder="18:00"
            />
          </div>

          <NumberInput
            name="max_daily_sales"
            label="الحد الأقصى للمبيعات اليومية"
            value={formData.max_daily_sales?.toString() || ''}
            onChange={(value) => handleInputChange('max_daily_sales', value ? parseInt(value) : undefined)}
            placeholder="أدخل الحد الأقصى للمبيعات"
            min={0}
          />
        </div>

        {/* Settings */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            الإعدادات
          </h3>

          <div className="space-y-4">
            <div className="bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-10 flex items-center">
              <ToggleSwitch
                id="is_active"
                checked={formData.is_active || false}
                onChange={(checked) => handleInputChange('is_active', checked)}
                label="فرع نشط"
                className="w-full"
              />
            </div>

            <div className="bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-10 flex items-center">
              <ToggleSwitch
                id="is_main"
                checked={formData.is_main || false}
                onChange={(checked) => handleInputChange('is_main', checked)}
                label="فرع رئيسي"
                className="w-full"
              />
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3 space-x-reverse pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            onClick={handleClose}
            className="bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium min-w-[140px]"
          >
            إلغاء
          </button>
          
          <button
            type="submit"
            disabled={updating}
            className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl min-w-[140px] disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {updating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                جاري التحديث...
              </>
            ) : (
              <>
                <FiSave className="ml-2" />
                حفظ التغييرات
              </>
            )}
          </button>
        </div>
      </form>
    </Modal>
  );
};

export default EditBranchModal;
