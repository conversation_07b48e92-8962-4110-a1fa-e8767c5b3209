/**
 * جدول مستودعات الفرع
 * يعرض قائمة المستودعات المرتبطة بالفرع مع إمكانيات الإدارة
 */

import React, { useState } from 'react';
import {
  FiPackage,
  FiStar,
  FiEdit,
  FiLink,
  FiCheckCircle,
  FiXCircle
} from 'react-icons/fi';
import { useBranchWarehouseStore } from '../../stores/branchWarehouseStore';
import { WarehouseForBranch } from '../../services/branchWarehouseService';
import { FormattedDate } from '../FormattedDateTime';

interface BranchWarehouseTableProps {
  branchId: number;
  warehouses: WarehouseForBranch[];
  loading: boolean;
  onUpdate: () => void;
}

const BranchWarehouseTable: React.FC<BranchWarehouseTableProps> = ({
  branchId,
  warehouses,
  loading,
  onUpdate
}) => {
  const {
    unlinkBranchFromWarehouse,
    setPrimaryWarehouseForBranch,
    updateWarehousePriorityForBranch,
    unlinking,
    updating
  } = useBranchWarehouseStore();

  const [editingPriority, setEditingPriority] = useState<number | null>(null);
  const [newPriority, setNewPriority] = useState<number>(1);

  // Handle unlink warehouse
  const handleUnlinkWarehouse = async (warehouse: WarehouseForBranch) => {
    if (window.confirm(`هل أنت متأكد من إلغاء ربط المستودع "${warehouse.name}" من الفرع؟`)) {
      const success = await unlinkBranchFromWarehouse(branchId, warehouse.id);
      if (success) {
        onUpdate();
      }
    }
  };

  // Handle set primary warehouse
  const handleSetPrimaryWarehouse = async (warehouse: WarehouseForBranch) => {
    if (warehouse.is_primary) {
      return; // Already primary
    }

    if (window.confirm(`هل أنت متأكد من تعيين "${warehouse.name}" كمستودع أساسي للفرع؟`)) {
      const success = await setPrimaryWarehouseForBranch(branchId, warehouse.id);
      if (success) {
        onUpdate();
      }
    }
  };

  // Handle priority edit
  const handleEditPriority = (warehouse: WarehouseForBranch) => {
    setEditingPriority(warehouse.id);
    setNewPriority(warehouse.priority);
  };

  // Handle priority save
  const handleSavePriority = async (warehouseId: number) => {
    if (newPriority < 1 || newPriority > 100) {
      alert('الأولوية يجب أن تكون بين 1 و 100');
      return;
    }

    const success = await updateWarehousePriorityForBranch(branchId, warehouseId, newPriority);
    if (success) {
      setEditingPriority(null);
      onUpdate();
    }
  };

  // Handle priority cancel
  const handleCancelPriority = () => {
    setEditingPriority(null);
    setNewPriority(1);
  };

  // Sort warehouses by priority and primary status
  const sortedWarehouses = [...warehouses].sort((a, b) => {
    if (a.is_primary && !b.is_primary) return -1;
    if (!a.is_primary && b.is_primary) return 1;
    return a.priority - b.priority;
  });

  if (loading) {
    return (
      <div className="p-8 text-center">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <p className="mt-2 text-gray-600 dark:text-gray-400">جاري تحميل المستودعات...</p>
      </div>
    );
  }

  if (warehouses.length === 0) {
    return (
      <div className="p-8 text-center">
        <FiPackage className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          لا توجد مستودعات مرتبطة
        </h3>
        <p className="text-gray-500 dark:text-gray-400">
          لم يتم ربط أي مستودع بهذا الفرع بعد
        </p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead className="bg-gray-50 dark:bg-gray-700">
          <tr>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              المستودع
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              الحالة
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              الأولوية
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              تاريخ الربط
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              الإجراءات
            </th>
          </tr>
        </thead>
        <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          {sortedWarehouses.map((warehouse) => (
            <tr key={warehouse.id} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className={`h-10 w-10 rounded-full flex items-center justify-center ${
                      warehouse.is_primary 
                        ? 'bg-yellow-100 dark:bg-yellow-900/20' 
                        : 'bg-gray-100 dark:bg-gray-700'
                    }`}>
                      {warehouse.is_primary ? (
                        <FiStar className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                      ) : (
                        <FiPackage className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                      )}
                    </div>
                  </div>
                  <div className="mr-4">
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {warehouse.name}
                      {warehouse.is_primary && (
                        <span className="mr-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400">
                          أساسي
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {warehouse.code}
                    </div>
                    {warehouse.address && (
                      <div className="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">
                        {warehouse.address}
                      </div>
                    )}
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  warehouse.is_active
                    ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400'
                    : 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400'
                }`}>
                  {warehouse.is_active ? (
                    <FiCheckCircle className="ml-1 h-3 w-3" />
                  ) : (
                    <FiXCircle className="ml-1 h-3 w-3" />
                  )}
                  {warehouse.is_active ? 'نشط' : 'غير نشط'}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                {editingPriority === warehouse.id ? (
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <input
                      type="number"
                      min="1"
                      max="100"
                      value={newPriority}
                      onChange={(e) => setNewPriority(parseInt(e.target.value) || 1)}
                      className="w-16 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    />
                    <button
                      onClick={() => handleSavePriority(warehouse.id)}
                      disabled={updating}
                      className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300"
                      title="حفظ"
                    >
                      <FiCheckCircle className="h-4 w-4" />
                    </button>
                    <button
                      onClick={handleCancelPriority}
                      className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
                      title="إلغاء"
                    >
                      <FiXCircle className="h-4 w-4" />
                    </button>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {warehouse.priority}
                    </span>
                    <button
                      onClick={() => handleEditPriority(warehouse)}
                      className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                      title="تعديل الأولوية"
                    >
                      <FiEdit className="h-4 w-4" />
                    </button>
                  </div>
                )}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                {warehouse.created_at ? <FormattedDate date={warehouse.created_at} /> : 'غير محدد'}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div className="flex items-center space-x-2 space-x-reverse">
                  {!warehouse.is_primary && (
                    <button
                      onClick={() => handleSetPrimaryWarehouse(warehouse)}
                      disabled={updating}
                      className="text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-300"
                      title="تعيين كمستودع أساسي"
                    >
                      <FiStar className="h-4 w-4" />
                    </button>
                  )}
                  <button
                    onClick={() => handleUnlinkWarehouse(warehouse)}
                    disabled={unlinking}
                    className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
                    title="إلغاء الربط"
                  >
                    <FiLink className="h-4 w-4" />
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default BranchWarehouseTable;
