/**
 * تبويب فروع البيع والمستودعات
 * يعرض العلاقات بين فروع البيع والمستودعات مع إمكانيات الإدارة
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  FiPlus,
  FiRefreshCw,
  FiLink,
  FiMapPin,
  FiPackage,
  FiStar
} from 'react-icons/fi';

// Import stores
import { useBranchStore } from '../../stores/branchStore';
import { useBranchWarehouseStore } from '../../stores/branchWarehouseStore';
import { useWarehouseStore } from '../../stores/warehouseStore';

// Import components
import SearchInput from '../SearchInput';

// Temporary modal component
const LinkBranchWarehouseModal: React.FC<any> = () => null;

interface BranchWarehousesTabProps {
  className?: string;
}

const BranchWarehousesTab: React.FC<BranchWarehousesTabProps> = ({ className = '' }) => {
  const {
    branches,
    loading: branchesLoading,
    fetchBranches
  } = useBranchStore();

  const {
    branchWarehouses,
    loading: relationLoading,
    error,
    fetchWarehousesForBranch,
    clearError
  } = useBranchWarehouseStore();

  const {
    loading: warehousesLoading,
    fetchWarehouses
  } = useWarehouseStore();

  // State
  const [selectedBranchId, setSelectedBranchId] = useState<number | null>(null);
  const [showLinkModal, setShowLinkModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Refs
  const initialLoadDone = useRef(false);

  // Initial load
  useEffect(() => {
    if (initialLoadDone.current) return;

    const loadData = async () => {
      try {
        await Promise.all([
          fetchBranches(false),
          fetchWarehouses(false)
        ]);
        initialLoadDone.current = true;
      } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
      }
    };

    loadData();
  }, []);

  // Load warehouses when branch is selected
  useEffect(() => {
    if (selectedBranchId) {
      fetchWarehousesForBranch(selectedBranchId, false);
    }
  }, [selectedBranchId]);

  // Handle refresh
  const handleRefresh = async () => {
    try {
      await Promise.all([
        fetchBranches(false),
        fetchWarehouses(false)
      ]);

      if (selectedBranchId) {
        await fetchWarehousesForBranch(selectedBranchId, false);
      }
    } catch (error) {
      console.error('خطأ في تحديث البيانات:', error);
    }
  };

  // Handle link success
  const handleLinkSuccess = () => {
    setShowLinkModal(false);
    if (selectedBranchId) {
      fetchWarehousesForBranch(selectedBranchId, false);
    }
  };

  // Get warehouses for selected branch
  const branchWarehousesList = selectedBranchId 
    ? branchWarehouses[selectedBranchId] || []
    : [];

  const loading = branchesLoading || warehousesLoading || relationLoading;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Error Display */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4">
          <div className="flex items-center justify-between">
            <div className="text-red-600 dark:text-red-400 text-sm">
              {error}
            </div>
            <button
              onClick={clearError}
              className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
            >
              ✕
            </button>
          </div>
        </div>
      )}

      {/* Header مع اختيار فرع البيع ومستودعاته - مدموج */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700">
        {/* Header Section */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h2 className="text-xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                <FiLink className="ml-2 text-primary-600 dark:text-primary-400" />
                إدارة فروع البيع والمستودعات
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                إدارة العلاقات بين فروع البيع والمستودعات
              </p>
            </div>
            
            <div className="flex items-center gap-3">
              <button
                onClick={handleRefresh}
                disabled={loading}
                className="bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium min-w-[100px] disabled:opacity-50"
              >
                <FiRefreshCw className={`ml-2 ${loading ? 'animate-spin' : ''}`} />
                تحديث
              </button>
              
              <button
                onClick={() => setShowLinkModal(true)}
                disabled={!selectedBranchId}
                className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl min-w-[140px] disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FiPlus className="ml-2" />
                ربط مستودع
              </button>
            </div>
          </div>
        </div>

        {/* Branch Selection Section - تصميم مدمج وأنيق */}
        <div className="p-6 bg-primary-50 dark:bg-primary-900/20">
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-primary-800 dark:text-primary-200 flex items-center">
              <FiMapPin className="ml-2 text-primary-600 dark:text-primary-400" />
              اختيار فرع البيع
            </h3>

            {/* Compact Branch Pills */}
            <div className="flex flex-wrap gap-2">
              {branches.map((branch) => {
                const isSelected = selectedBranchId === branch.id;
                return (
                  <button
                    key={branch.id}
                    onClick={() => setSelectedBranchId(branch.id)}
                    className={`inline-flex items-center px-3 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                      isSelected
                        ? 'bg-primary-500 text-white shadow-md'
                        : 'bg-white dark:bg-gray-600 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-500 shadow-sm border border-gray-200 dark:border-gray-500'
                    }`}
                  >
                    {/* Branch Icon */}
                    {branch.is_main ? (
                      <FiStar className="w-4 h-4 ml-1.5" />
                    ) : (
                      <FiMapPin className="w-4 h-4 ml-1.5" />
                    )}

                    {/* Branch Name */}
                    <span className="truncate max-w-[120px]">{branch.name}</span>

                    {/* Main Badge */}
                    {branch.is_main && (
                      <span className={`mr-1.5 px-1.5 py-0.5 rounded-full text-xs ${
                        isSelected
                          ? 'bg-yellow-400 text-yellow-900'
                          : 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300'
                      }`}>
                        رئيسي
                      </span>
                    )}
                  </button>
                );
              })}

            {branches.length === 0 && (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                <FiMapPin className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>لا توجد فروع بيع متاحة</p>
              </div>
            )}
          </div>
          </div>
        </div>

        {/* Warehouses Table Section - مدموج */}
        {selectedBranchId && (
          <>
            <div className="border-t border-gray-200 dark:border-gray-700"></div>
            <div className="p-6">
              <div className="mb-4">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center">
                      <FiPackage className="ml-2 text-primary-600 dark:text-primary-400" />
                      مستودعات فرع البيع ({branchWarehousesList.length})
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      قائمة المستودعات المرتبطة بفرع البيع المحدد
                    </p>
                  </div>
                  
                  {/* Search input for warehouses */}
                  {branchWarehouses[selectedBranchId!]?.length > 0 && (
                    <div className="w-full sm:w-80">
                      <SearchInput
                        value={searchTerm}
                        onChange={setSearchTerm}
                        placeholder="البحث في المستودعات..."
                        className="w-full"
                      />
                    </div>
                  )}
                </div>
              </div>
              
              {/* Warehouses Cards */}
              {relationLoading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="bg-gray-100 dark:bg-gray-700 rounded-xl p-4 animate-pulse">
                      <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-3/4"></div>
                    </div>
                  ))}
                </div>
              ) : branchWarehousesList.length > 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {branchWarehousesList.map((warehouse) => (
                    <div
                      key={warehouse.id}
                      className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4 border border-gray-200 dark:border-gray-600 hover:shadow-md transition-all duration-200 hover:border-primary-300 dark:hover:border-primary-600"
                    >
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <div className="h-10 w-10 bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 rounded-lg flex items-center justify-center flex-shrink-0">
                          <FiPackage className="h-5 w-5" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h5 className="font-medium text-gray-900 dark:text-gray-100 truncate">
                            {warehouse.name}
                          </h5>
                          <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                            {warehouse.code}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  <FiPackage className="h-12 w-12 mx-auto mb-3 opacity-50" />
                  <p className="text-lg font-medium mb-1">لا توجد مستودعات مرتبطة</p>
                  <p className="text-sm">قم بربط مستودعات بهذا الفرع لعرضها هنا</p>
                </div>
              )}
            </div>
          </>
        )}

        {/* Empty State - مدموج */}
        {!selectedBranchId && (
          <>
            <div className="border-t border-gray-200 dark:border-gray-700"></div>
            <div className="p-12 text-center">
              <FiMapPin className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                اختر فرع بيع لعرض مستودعاته
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                قم بتحديد فرع بيع من القائمة أعلاه لعرض وإدارة المستودعات المرتبطة به
              </p>
            </div>
          </>
        )}
      </div>

      {/* Link Modal */}
      <LinkBranchWarehouseModal
        isOpen={showLinkModal}
        onClose={() => setShowLinkModal(false)}
        branchId={selectedBranchId}
        onSuccess={handleLinkSuccess}
      />
    </div>
  );
};

export default BranchWarehousesTab;
