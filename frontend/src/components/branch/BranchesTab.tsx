/**
 * تبويب فروع البيع
 * يعرض قائمة فروع البيع مع إمكانيات الإدارة الكاملة
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  FiPlus,
  FiEdit,
  FiTrash,
  FiUser,
  FiStar,
  FiMapPin,
  FiGitBranch,
  FiPhone,
  FiEye,
  FiToggleLeft,
  FiToggleRight
} from 'react-icons/fi';
import { useBranchStore } from '../../stores/branchStore';
import { Branch } from '../../services/branchService';
import { SelectInput } from '../inputs';
import { FormattedDate } from '../FormattedDateTime';
import { TopbarTooltip } from '../ui';
import TablePagination from '../catalog/TablePagination';
import SearchInput from '../SearchInput';
import CreateBranchModal from './CreateBranchModal';
import EditBranchModal from './EditBranchModal';
import BranchDetailsModal from './BranchDetailsModal';

interface BranchesTabProps {
  className?: string;
}

const BranchesTab: React.FC<BranchesTabProps> = ({ className = '' }) => {
  const {
    branches,
    loading,
    error,
    fetchBranches,
    deleteBranch,
    setMainBranch,
    toggleBranchStatus,
    clearError
  } = useBranchStore();

  // State
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('active');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedBranch, setSelectedBranch] = useState<Branch | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Refs
  const initialLoadDone = useRef(false);

  // Load branches on mount
  useEffect(() => {
    if (initialLoadDone.current) return;

    const loadBranches = async () => {
      try {
        await fetchBranches(statusFilter === 'all');
        initialLoadDone.current = true;
      } catch (error) {
        console.error('خطأ في تحميل فروع البيع:', error);
      }
    };

    loadBranches();
  }, []);

  // Filter branches based on search and status
  const allFilteredBranches = React.useMemo(() => {
    let filtered = branches.filter(branch => {
      const matchesSearch = !searchTerm ||
        branch.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (branch.code && branch.code.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (branch.city && branch.city.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (branch.region && branch.region.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesStatus = statusFilter === 'all' ||
        (statusFilter === 'active' && branch.is_active) ||
        (statusFilter === 'inactive' && !branch.is_active);

      return matchesSearch && matchesStatus;
    });

    // Sort branches
    return filtered.sort((a, b) => {
      // Main branch first
      if (a.is_main && !b.is_main) return -1;
      if (!a.is_main && b.is_main) return 1;
      // Then by name
      return a.name.localeCompare(b.name, 'ar');
    });
  }, [branches, searchTerm, statusFilter]);

  // Pagination calculations
  const totalItems = allFilteredBranches.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const filteredBranches = allFilteredBranches.slice(startIndex, endIndex);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, statusFilter]);

  // Handle status filter change
  const handleStatusFilterChange = async (newStatus: 'all' | 'active' | 'inactive') => {
    setStatusFilter(newStatus);
    await fetchBranches(newStatus === 'all');
  };

  // Handle delete branch
  const handleDeleteBranch = async (branch: Branch) => {
    if (branch.is_main) {
      alert('لا يمكن حذف فرع البيع الرئيسي');
      return;
    }

    if (window.confirm(`هل أنت متأكد من حذف فرع البيع "${branch.name}"؟`)) {
      const success = await deleteBranch(branch.id);
      if (success) {
        console.log('تم حذف فرع البيع بنجاح');
      }
    }
  };

  // Handle set main branch
  const handleSetMainBranch = async (branch: Branch) => {
    if (window.confirm(`هل أنت متأكد من تعيين "${branch.name}" كفرع بيع رئيسي؟`)) {
      const success = await setMainBranch(branch.id);
      if (success) {
        console.log('تم تعيين فرع البيع الرئيسي بنجاح');
      }
    }
  };

  // Handle toggle status
  const handleToggleStatus = async (branch: Branch) => {
    if (branch.is_main && branch.is_active) {
      alert('لا يمكن إلغاء تفعيل فرع البيع الرئيسي');
      return;
    }

    const action = branch.is_active ? 'إلغاء تفعيل' : 'تفعيل';
    if (window.confirm(`هل أنت متأكد من ${action} فرع البيع "${branch.name}"؟`)) {
      const success = await toggleBranchStatus(branch.id);
      if (success) {
        console.log(`تم ${action} فرع البيع بنجاح`);
      }
    }
  };

  // Handle edit branch
  const handleEditBranch = (branch: Branch) => {
    setSelectedBranch(branch);
    setShowEditModal(true);
  };

  // Handle view details
  const handleViewDetails = (branch: Branch) => {
    setSelectedBranch(branch);
    setShowDetailsModal(true);
  };

  // Handle modal close
  const handleModalClose = () => {
    setSelectedBranch(null);
    setShowCreateModal(false);
    setShowEditModal(false);
    setShowDetailsModal(false);
  };

  // Handle modal success
  const handleModalSuccess = () => {
    handleModalClose();
    fetchBranches(statusFilter === 'all');
  };

  return (
    <div className={`${className}`}>
      {/* Data Table with Header and Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 overflow-hidden">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center p-6 gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <div className="min-w-0 flex-1">
                <h2 className="text-lg sm:text-xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FiGitBranch className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                  <span className="truncate">إدارة فروع البيع</span>
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  عرض وإدارة جميع فروع البيع في النظام
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3 flex-shrink-0">
              <button
                onClick={() => setShowCreateModal(true)}
                className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl min-w-[140px]"
              >
                <FiPlus className="ml-2" />
                إضافة فرع بيع
              </button>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="mx-6 mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4">
              <div className="flex items-center justify-between">
                <div className="text-red-600 dark:text-red-400 text-sm">
                  {error}
                </div>
                <button
                  onClick={clearError}
                  className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
                >
                  ✕
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Filters - Updated with unified components */}
        <div className="bg-gray-50 dark:bg-gray-700/50 border-b border-gray-200 dark:border-gray-600 p-4">
          <div className="flex gap-4">
            {/* البحث - يأخذ المساحة المتبقية مع المكون الموحد */}
            <div className="flex-1">
              <SearchInput
                value={searchTerm}
                onChange={setSearchTerm}
                placeholder="البحث في فروع البيع..."
                className="w-full"
              />
            </div>

            {/* فلتر الحالة - عرض ثابت */}
            <div className="w-48 flex-shrink-0">
              <SelectInput
                name="statusFilter"
                label=""
                value={statusFilter}
                onChange={(value) => handleStatusFilterChange(value as 'all' | 'active' | 'inactive')}
                options={[
                  { value: 'active', label: 'النشطة فقط' },
                  { value: 'inactive', label: 'غير النشطة فقط' },
                  { value: 'all', label: 'جميع الفروع' }
                ]}
                placeholder="فلترة حسب الحالة"
                className="h-10"
              />
            </div>
          </div>
        </div>

        {/* Branches List */}
        {loading ? (
          <div className="p-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            <p className="mt-2 text-gray-600 dark:text-gray-400">جاري تحميل فروع البيع...</p>
          </div>
        ) : filteredBranches.length === 0 ? (
          <div className="p-8 text-center text-gray-500 dark:text-gray-400">
            لا توجد فروع متاحة
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    فرع البيع
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    المدير
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الموقع
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    تاريخ الإنشاء
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-28">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredBranches.map((branch) => (
                  <tr key={branch.id} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <div className={`h-10 w-10 rounded-full flex items-center justify-center ${
                            branch.is_main 
                              ? 'bg-yellow-100 dark:bg-yellow-900/20' 
                              : 'bg-gray-100 dark:bg-gray-700'
                          }`}>
                            {branch.is_main ? (
                              <FiStar className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                            ) : (
                              <FiMapPin className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                            )}
                          </div>
                        </div>
                        <div className="mr-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {branch.name}
                            {branch.is_main && (
                              <span className="mr-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400">
                                رئيسي
                              </span>
                            )}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            <span className="font-medium text-gray-600 dark:text-gray-300">كود الفرع: </span>
                            <span className="font-mono">{branch.code || 'لا يوجد كود'}</span>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-900 dark:text-gray-100">
                        <FiUser className="ml-2 h-4 w-4 text-gray-400" />
                        {branch.manager_name || 'غير محدد'}
                      </div>
                      {branch.phone && (
                        <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                          <FiPhone className="ml-2 h-4 w-4" />
                          {branch.phone}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-gray-100">
                        {branch.city && branch.region ? `${branch.city}, ${branch.region}` : branch.city || branch.region || 'غير محدد'}
                      </div>
                      {branch.address && (
                        <div className="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">
                          {branch.address}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${
                        branch.is_active
                          ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800'
                          : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 border-gray-200 dark:border-gray-700'
                      }`}>
                        {branch.is_active ? 'نشط' : 'غير نشط'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {branch.created_at ? <FormattedDate date={branch.created_at} /> : 'غير محدد'}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm font-medium w-28">
                      <div className="flex items-center justify-end gap-1">
                        <TopbarTooltip text="عرض التفاصيل" position="top">
                          <button
                            onClick={() => handleViewDetails(branch)}
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors duration-150 p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 border border-transparent hover:border-blue-200 dark:hover:border-blue-700"
                          >
                            <FiEye className="w-4 h-4" />
                          </button>
                        </TopbarTooltip>
                        <TopbarTooltip text="تعديل الفرع" position="top">
                          <button
                            onClick={() => handleEditBranch(branch)}
                            className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200 transition-colors duration-150 p-2 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20 border border-transparent hover:border-green-200 dark:hover:border-green-700"
                          >
                            <FiEdit className="w-4 h-4" />
                          </button>
                        </TopbarTooltip>
                        {!branch.is_main && (
                          <TopbarTooltip text="تعيين كفرع رئيسي" position="top">
                            <button
                              onClick={() => handleSetMainBranch(branch)}
                              className="text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-300 transition-colors duration-150 p-2 rounded-lg hover:bg-yellow-50 dark:hover:bg-yellow-900/20 border border-transparent hover:border-yellow-200 dark:hover:border-yellow-700"
                            >
                              <FiStar className="w-4 h-4" />
                            </button>
                          </TopbarTooltip>
                        )}
                        <TopbarTooltip text={branch.is_active ? 'إلغاء التفعيل' : 'تفعيل'} position="top">
                          <button
                            onClick={() => handleToggleStatus(branch)}
                            className={`${
                              branch.is_active
                                ? 'text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300'
                                : 'text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300'
                            } transition-colors duration-150 p-2 rounded-lg border border-transparent hover:bg-gray-50 dark:hover:bg-gray-900/20 hover:border-gray-200 dark:hover:border-gray-700`}
                          >
                            {branch.is_active ? (
                              <FiToggleRight className="w-4 h-4" />
                            ) : (
                              <FiToggleLeft className="w-4 h-4" />
                            )}
                          </button>
                        </TopbarTooltip>
                        {!branch.is_main && (
                          <TopbarTooltip text="حذف الفرع" position="top">
                            <button
                              onClick={() => handleDeleteBranch(branch)}
                              className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 transition-colors duration-150 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 border border-transparent hover:border-red-200 dark:hover:border-red-700"
                            >
                              <FiTrash className="w-4 h-4" />
                            </button>
                          </TopbarTooltip>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalItems > 0 && (
            <TablePagination
              currentPage={currentPage}
              totalPages={totalPages}
              itemsPerPage={itemsPerPage}
              totalItems={totalItems}
              onPageChange={setCurrentPage}
              onItemsPerPageChange={setItemsPerPage}
              itemsPerPageOptions={[10, 20, 50]}
            />
          )}
          </>
        )}
      </div>

      {/* Modals */}
      <CreateBranchModal
        isOpen={showCreateModal}
        onClose={handleModalClose}
        onSuccess={handleModalSuccess}
      />

      <EditBranchModal
        isOpen={showEditModal}
        onClose={handleModalClose}
        branch={selectedBranch}
        onSuccess={handleModalSuccess}
      />

      <BranchDetailsModal
        isOpen={showDetailsModal}
        onClose={handleModalClose}
        branch={selectedBranch}
      />
    </div>
  );
};

export default BranchesTab;
