/**
 * نافذة تفاصيل الفرع
 * تعرض جميع معلومات الفرع بشكل مفصل باستخدام التصميم الموحد للنظام
 */

import React from 'react';
import { 
  FiMapPin, 
  FiUser, 
  FiMail, 
  FiPhone, 
  FiClock, 
  FiStar,
  FiCheckCircle,
  FiXCircle,
  FiCalendar,
  FiActivity,
  FiGitBranch,
  FiDollarSign,
  FiPackage,
  FiHome
} from 'react-icons/fi';
import Modal from '../Modal';
import { Branch } from '../../services/branchService';
import { FormattedDateTime } from '../FormattedDateTime';

interface BranchDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  branch: Branch | null;
}

const BranchDetailsModal: React.FC<BranchDetailsModalProps> = ({
  isOpen,
  onClose,
  branch
}) => {
  if (!branch) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`تفاصيل الفرع: ${branch.name}`}
      size="lg"
    >
      <div className="space-y-6">
        {/* Header with Status */}
        <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-xl">
          <div className="flex items-center gap-3">
            <div className={`p-3 rounded-lg ${
              branch.is_main
                ? 'bg-warning-100 dark:bg-warning-900/30 text-warning-600 dark:text-warning-400'
                : branch.is_active
                  ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400'
            }`}>
              {branch.is_main ? (
                <FiStar className="w-6 h-6" />
              ) : (
                <FiGitBranch className="w-6 h-6" />
              )}
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">
                {branch.name}
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 font-mono">
                {branch.code || 'لا يوجد كود'}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <span className={`inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium ${
              branch.is_active
                ? 'bg-success-100 text-success-700 dark:bg-success-900/30 dark:text-success-400'
                : 'bg-danger-100 text-danger-700 dark:bg-danger-900/30 dark:text-danger-400'
            }`}>
              <div className={`w-2 h-2 rounded-full ml-2 ${
                branch.is_active ? 'bg-success-500' : 'bg-danger-500'
              }`}></div>
              {branch.is_active ? 'نشط' : 'غير نشط'}
            </span>

            {branch.is_main && (
              <span className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-warning-100 text-warning-700 dark:bg-warning-900/30 dark:text-warning-400">
                <FiStar className="w-3 h-3 ml-1" />
                رئيسي
              </span>
            )}
          </div>
        </div>

        {/* Location Information */}
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
            معلومات الموقع
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Address */}
            <div className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="p-2 bg-primary-100 dark:bg-primary-900/30 rounded-lg">
                <FiMapPin className="w-4 h-4 text-primary-600 dark:text-primary-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">العنوان</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {branch.address || 'لم يتم تحديد العنوان'}
                </p>
              </div>
            </div>

            {/* City */}
            <div className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="p-2 bg-secondary-100 dark:bg-secondary-900/30 rounded-lg">
                <FiHome className="w-4 h-4 text-secondary-600 dark:text-secondary-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">المدينة</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {branch.city || 'لم يتم تحديد المدينة'}
                </p>
              </div>
            </div>

            {/* Region */}
            <div className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="p-2 bg-info-100 dark:bg-info-900/30 rounded-lg">
                <FiMapPin className="w-4 h-4 text-info-600 dark:text-info-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">المنطقة</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {branch.region || 'لم يتم تحديد المنطقة'}
                </p>
              </div>
            </div>

            {/* Postal Code */}
            <div className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="p-2 bg-warning-100 dark:bg-warning-900/30 rounded-lg">
                <FiMail className="w-4 h-4 text-warning-600 dark:text-warning-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">الرمز البريدي</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {branch.postal_code || 'لم يتم تحديد الرمز البريدي'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Manager Information */}
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
            معلومات المدير
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Manager Name */}
            <div className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="p-2 bg-secondary-100 dark:bg-secondary-900/30 rounded-lg">
                <FiUser className="w-4 h-4 text-secondary-600 dark:text-secondary-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">اسم المدير</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {branch.manager_name || 'لم يتم تحديد المدير'}
                </p>
              </div>
            </div>

            {/* Phone */}
            <div className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="p-2 bg-success-100 dark:bg-success-900/30 rounded-lg">
                <FiPhone className="w-4 h-4 text-success-600 dark:text-success-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">رقم الهاتف</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {branch.phone || 'لم يتم تحديد الهاتف'}
                </p>
              </div>
            </div>

            {/* Email */}
            <div className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg md:col-span-2">
              <div className="p-2 bg-info-100 dark:bg-info-900/30 rounded-lg">
                <FiMail className="w-4 h-4 text-info-600 dark:text-info-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">البريد الإلكتروني</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {branch.email || 'لم يتم تحديد البريد الإلكتروني'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Working Hours and Settings */}
        {(branch.working_hours_start || branch.working_hours_end || branch.max_daily_sales) && (
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
              ساعات العمل والإعدادات
            </h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Working Hours Start */}
              {branch.working_hours_start && (
                <div className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <div className="p-2 bg-success-100 dark:bg-success-900/30 rounded-lg">
                    <FiClock className="w-4 h-4 text-success-600 dark:text-success-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-700 dark:text-gray-300">بداية العمل</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {branch.working_hours_start}
                    </p>
                  </div>
                </div>
              )}

              {/* Working Hours End */}
              {branch.working_hours_end && (
                <div className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <div className="p-2 bg-danger-100 dark:bg-danger-900/30 rounded-lg">
                    <FiClock className="w-4 h-4 text-danger-600 dark:text-danger-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-700 dark:text-gray-300">نهاية العمل</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {branch.working_hours_end}
                    </p>
                  </div>
                </div>
              )}

              {/* Max Daily Sales */}
              {branch.max_daily_sales && (
                <div className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg md:col-span-2">
                  <div className="p-2 bg-warning-100 dark:bg-warning-900/30 rounded-lg">
                    <FiDollarSign className="w-4 h-4 text-warning-600 dark:text-warning-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-700 dark:text-gray-300">الحد الأقصى للمبيعات اليومية</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {branch.max_daily_sales.toLocaleString()} د.ل
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Warehouse Information */}
        {(branch.warehouses_count !== undefined || branch.active_warehouses_count !== undefined) && (
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
              معلومات المستودعات
            </h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Total Warehouses */}
              {branch.warehouses_count !== undefined && (
                <div className="flex items-start gap-3 p-4 bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 rounded-lg border border-primary-200 dark:border-primary-700">
                  <div className="p-2 bg-primary-500 rounded-lg">
                    <FiPackage className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-primary-700 dark:text-primary-300">إجمالي المستودعات</p>
                    <p className="text-2xl font-bold text-primary-900 dark:text-primary-100">
                      {branch.warehouses_count}
                    </p>
                  </div>
                </div>
              )}

              {/* Active Warehouses */}
              {branch.active_warehouses_count !== undefined && (
                <div className="flex items-start gap-3 p-4 bg-gradient-to-r from-success-50 to-success-100 dark:from-success-900/30 dark:to-success-800/30 rounded-lg border border-success-200 dark:border-success-700">
                  <div className="p-2 bg-success-500 rounded-lg">
                    <FiCheckCircle className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-success-700 dark:text-success-300">المستودعات النشطة</p>
                    <p className="text-2xl font-bold text-success-900 dark:text-success-100">
                      {branch.active_warehouses_count}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* System Information */}
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
            معلومات النظام
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* UUID */}
            <div className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg md:col-span-2">
              <div className="p-2 bg-info-100 dark:bg-info-900/30 rounded-lg">
                <FiActivity className="w-4 h-4 text-info-600 dark:text-info-400" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">المعرف الفريد (UUID)</p>
                <p className="text-sm text-gray-600 dark:text-gray-400 font-mono break-all">
                  {branch.uuid}
                </p>
              </div>
            </div>

            {/* Created At */}
            {branch.created_at && (
              <div className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <div className="p-2 bg-success-100 dark:bg-success-900/30 rounded-lg">
                  <FiCalendar className="w-4 h-4 text-success-600 dark:text-success-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300">تاريخ الإنشاء</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    <FormattedDateTime
                      date={branch.created_at}
                      showTime={true}
                      fallback="غير محدد"
                    />
                  </p>
                </div>
              </div>
            )}

            {/* Updated At */}
            {branch.updated_at && (
              <div className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <div className="p-2 bg-warning-100 dark:bg-warning-900/30 rounded-lg">
                  <FiActivity className="w-4 h-4 text-warning-600 dark:text-warning-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300">آخر تحديث</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    <FormattedDateTime
                      date={branch.updated_at}
                      showTime={true}
                      fallback="غير محدد"
                    />
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Close Button */}
        <div className="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-6 py-3 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 rounded-xl transition-colors duration-200 font-medium"
          >
            إغلاق
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default BranchDetailsModal;
