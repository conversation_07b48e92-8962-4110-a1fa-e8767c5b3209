/**
 * نافذة ربط فرع بمستودع
 * تتيح للمستخدم ربط فرع بمستودع مع تحديد الأولوية والحالة الأساسية
 */

import React, { useState, useEffect } from 'react';
import { FiSave, FiLink, FiPackage, FiStar } from 'react-icons/fi';
import Modal from '../Modal';
import { SelectInput, NumberInput } from '../inputs';
import ToggleSwitch from '../ToggleSwitch';
import { useBranchWarehouseStore } from '../../stores/branchWarehouseStore';
import { BranchWarehouseLinkCreate } from '../../services/branchWarehouseService';

interface LinkBranchWarehouseModalProps {
  isOpen: boolean;
  onClose: () => void;
  branchId: number | null;
  onSuccess?: () => void;
}

const LinkBranchWarehouseModal: React.FC<LinkBranchWarehouseModalProps> = ({
  isOpen,
  onClose,
  branchId,
  onSuccess
}) => {
  const {
    linkBranchToWarehouse,
    fetchAvailableWarehousesForBranch,
    availableWarehouses,
    linking,
    error
  } = useBranchWarehouseStore();

  // const { warehouses } = useWarehouseStore(); // Not used currently

  // Form state
  const [formData, setFormData] = useState<BranchWarehouseLinkCreate>({
    branch_id: 0,
    warehouse_id: 0,
    is_primary: false,
    priority: 1
  });

  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Load available warehouses when modal opens
  useEffect(() => {
    if (isOpen && branchId) {
      setFormData(prev => ({
        ...prev,
        branch_id: branchId
      }));
      fetchAvailableWarehousesForBranch(branchId);
    }
  }, [isOpen, branchId]);

  // Get available warehouses for the branch
  const availableWarehousesList = branchId ? availableWarehouses[branchId] || [] : [];

  // Handle input change
  const handleInputChange = (field: keyof BranchWarehouseLinkCreate, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field
    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.warehouse_id) {
      errors.warehouse_id = 'يجب اختيار مستودع';
    }

    if (formData.priority && (formData.priority < 1 || formData.priority > 100)) {
      errors.priority = 'الأولوية يجب أن تكون بين 1 و 100';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle submit
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      const success = await linkBranchToWarehouse(formData);
      
      if (success) {
        // Reset form
        setFormData({
          branch_id: branchId || 0,
          warehouse_id: 0,
          is_primary: false,
          priority: 1
        });
        setFormErrors({});
        
        if (onSuccess) {
          onSuccess();
        }
      }
    } catch (error) {
      console.error('خطأ في ربط الفرع بالمستودع:', error);
    }
  };

  // Handle close
  const handleClose = () => {
    setFormData({
      branch_id: branchId || 0,
      warehouse_id: 0,
      is_primary: false,
      priority: 1
    });
    setFormErrors({});
    onClose();
  };

  if (!branchId) {
    return null;
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="ربط فرع بمستودع"
      size="md"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Error Display */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4">
            <div className="text-red-600 dark:text-red-400 text-sm">
              {error}
            </div>
          </div>
        )}

        {/* Information */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4">
          <div className="flex items-center">
            <FiLink className="h-5 w-5 text-blue-600 dark:text-blue-400 ml-2" />
            <div className="text-blue-600 dark:text-blue-400 text-sm">
              سيتم ربط المستودع المحدد بالفرع الحالي
            </div>
          </div>
        </div>

        {/* Warehouse Selection */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center">
            <FiPackage className="ml-2 text-primary-600 dark:text-primary-400" />
            اختيار المستودع
          </h3>

          <SelectInput
            name="warehouse_id"
            label="المستودع *"
            value={formData.warehouse_id.toString()}
            onChange={(value) => handleInputChange('warehouse_id', parseInt(value) || 0)}
            error={formErrors.warehouse_id}
            options={[
              { value: '0', label: 'اختر مستودعاً...' },
              ...availableWarehousesList.map(warehouse => ({
                value: warehouse.id.toString(),
                label: `${warehouse.name} (${warehouse.code})`
              }))
            ]}
            placeholder="اختر المستودع المراد ربطه"
          />

          {availableWarehousesList.length === 0 && (
            <div className="text-sm text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
              لا توجد مستودعات متاحة للربط بهذا الفرع
            </div>
          )}
        </div>

        {/* Link Settings */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            إعدادات الربط
          </h3>

          <NumberInput
            name="priority"
            label="الأولوية"
            value={(formData.priority || 1).toString()}
            onChange={(value) => handleInputChange('priority', value ? parseInt(value) : 1)}
            error={formErrors.priority}
            placeholder="أدخل أولوية المستودع (1-100)"
            min={1}
            max={100}
          />
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            1 = أعلى أولوية، 100 = أقل أولوية
          </p>

          <div className="bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-10 flex items-center">
            <ToggleSwitch
              id="is_primary"
              checked={formData.is_primary || false}
              onChange={(checked) => handleInputChange('is_primary', checked)}
              label="مستودع أساسي للفرع"
              className="w-full"
            />
          </div>

          {formData.is_primary && (
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl p-4">
              <div className="flex items-center">
                <FiStar className="h-5 w-5 text-yellow-600 dark:text-yellow-400 ml-2" />
                <div className="text-yellow-600 dark:text-yellow-400 text-sm">
                  سيتم تعيين هذا المستودع كمستودع أساسي للفرع. سيتم إلغاء تعيين أي مستودع أساسي آخر.
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3 space-x-reverse pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            onClick={handleClose}
            className="bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium min-w-[140px]"
          >
            إلغاء
          </button>
          
          <button
            type="submit"
            disabled={linking || availableWarehousesList.length === 0}
            className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl min-w-[140px] disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {linking ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                جاري الربط...
              </>
            ) : (
              <>
                <FiSave className="ml-2" />
                ربط المستودع
              </>
            )}
          </button>
        </div>
      </form>
    </Modal>
  );
};

export default LinkBranchWarehouseModal;
