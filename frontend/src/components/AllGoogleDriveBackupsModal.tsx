import React, { useState, useEffect } from 'react';
import { FaCloud, FaDownload, FaTrash, FaSync, FaCalendarAlt, FaHdd } from 'react-icons/fa';
import Modal from './Modal';
import SimpleConfirmModal from './SimpleConfirmModal';
import SimpleSuccessModal from './SimpleSuccessModal';
import apiClient from '../lib/axios';

interface GoogleDriveFile {
  id: string;
  name: string;
  size: string;
  createdTime: string;
  webContentLink?: string;
}

interface AllGoogleDriveBackupsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onDelete?: (fileId: string, fileName: string) => void;
}

const AllGoogleDriveBackupsModal: React.FC<AllGoogleDriveBackupsModalProps> = ({
  isOpen,
  onClose,
  onDelete
}) => {
  const [files, setFiles] = useState<GoogleDriveFile[]>([]);
  const [loading, setLoading] = useState(false);
  const [downloadingFiles, setDownloadingFiles] = useState<{[key: string]: number}>({});
  const [errorModal, setErrorModal] = useState({ isOpen: false, title: '', message: '' });
  const [successModal, setSuccessModal] = useState({ isOpen: false, title: '', message: '' });

  // تحويل حجم الملف إلى وحدات مفهومة
  const formatFileSize = (sizeInBytes: string | number) => {
    try {
      const bytes = typeof sizeInBytes === 'string' ? parseInt(sizeInBytes) : sizeInBytes;

      if (isNaN(bytes) || bytes === 0) {
        return 'غير محدد';
      }

      const units = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت', 'تيرابايت'];
      const unitIndex = Math.floor(Math.log(bytes) / Math.log(1024));
      const size = bytes / Math.pow(1024, unitIndex);

      // تنسيق الرقم حسب الحجم
      let formattedSize;
      if (unitIndex === 0) {
        // بايت - عدد صحيح
        formattedSize = bytes.toString();
      } else if (size >= 100) {
        // أكبر من 100 - عدد صحيح
        formattedSize = Math.round(size).toString();
      } else if (size >= 10) {
        // بين 10-100 - رقم عشري واحد
        formattedSize = size.toFixed(1);
      } else {
        // أقل من 10 - رقمين عشريين
        formattedSize = size.toFixed(2);
      }

      return `${formattedSize} ${units[unitIndex] || 'بايت'}`;
    } catch {
      return 'غير محدد';
    }
  };

  // تنسيق التاريخ
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString('ar-LY', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return dateString;
    }
  };

  // جلب قائمة ملفات Google Drive
  const fetchGoogleDriveFiles = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get('/api/google-drive/files');
      const data = response.data;

      if (data.success) {
        setFiles(data.files || []);
      } else {
        console.error('Error fetching Google Drive files:', data);
        setFiles([]);
      }
    } catch (error) {
      console.error('Error fetching Google Drive files:', error);
      setFiles([]);
    } finally {
      setLoading(false);
    }
  };

  // جلب البيانات عند فتح النافذة
  useEffect(() => {
    if (isOpen) {
      fetchGoogleDriveFiles();
    }
  }, [isOpen]);



  const handleDelete = async (fileId: string, fileName: string) => {
    if (onDelete) {
      onDelete(fileId, fileName);
      // تحديث القائمة بعد الحذف
      setTimeout(() => {
        fetchGoogleDriveFiles();
      }, 1000);
    }
  };

  // دالة تحميل الملف مع شريط التقدم
  const handleDownload = async (fileId: string, fileName: string) => {
    try {
      setDownloadingFiles(prev => ({ ...prev, [fileId]: 0 }));

      // محاكاة التحميل مع شريط التقدم
      for (let progress = 0; progress <= 60; progress += 20) {
        setDownloadingFiles(prev => ({ ...prev, [fileId]: progress }));
        await new Promise(resolve => setTimeout(resolve, 80));
      }

      // محاولة التحميل باستخدام API الخاص بنا
      try {
        setDownloadingFiles(prev => ({ ...prev, [fileId]: 80 }));

        const response = await apiClient.get(`/api/google-drive/download/${fileId}`, {
          responseType: 'blob'
        });

        setDownloadingFiles(prev => ({ ...prev, [fileId]: 100 }));
        await new Promise(resolve => setTimeout(resolve, 200));

        // التحقق من أن الاستجابة تحتوي على بيانات
        if (!response.data || response.data.size === 0) {
          throw new Error('الملف فارغ أو تالف');
        }

        // إنشاء blob وتحميل الملف
        const blob = new Blob([response.data], {
          type: 'application/octet-stream'
        });

        // التحقق من حجم الـ blob
        if (blob.size === 0) {
          throw new Error('الملف فارغ أو تالف');
        }

        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        link.style.display = 'none';

        // إضافة الرابط إلى الصفحة وتفعيله
        document.body.appendChild(link);

        // محاولة التحميل
        try {
          link.click();
        } catch (clickError) {
          console.error('خطأ في تفعيل التحميل:', clickError);
          // محاولة بديلة
          const event = new MouseEvent('click', {
            view: window,
            bubbles: true,
            cancelable: true
          });
          link.dispatchEvent(event);
        }

        // إخفاء تحذيرات HTTPS في وحدة التحكم
        const originalConsoleWarn = console.warn;
        console.warn = (...args) => {
          if (args[0] && typeof args[0] === 'string' && args[0].includes('insecure connection')) {
            return; // تجاهل تحذيرات HTTPS
          }
          originalConsoleWarn.apply(console, args);
        };

        // استعادة console.warn بعد فترة قصيرة
        setTimeout(() => {
          console.warn = originalConsoleWarn;
        }, 2000);

        // تنظيف العناصر
        document.body.removeChild(link);

        // تنظيف الـ URL بعد فترة قصيرة
        setTimeout(() => {
          window.URL.revokeObjectURL(url);
        }, 1000);

        // إزالة حالة التحميل
        setDownloadingFiles(prev => {
          const newState = { ...prev };
          delete newState[fileId];
          return newState;
        });

        // عرض رسالة نجاح
        setTimeout(() => {
          setSuccessModal({
            isOpen: true,
            title: 'تم التحميل بنجاح',
            message: `تم تحميل الملف "${fileName}" بنجاح إلى جهازك`
          });
        }, 500);

      } catch (apiError) {
        console.error('API download failed:', apiError);

        // إذا فشل API، جرب webContentLink
        const file = files.find(f => f.id === fileId);
        if (file?.webContentLink) {
          setDownloadingFiles(prev => ({ ...prev, [fileId]: 100 }));
          await new Promise(resolve => setTimeout(resolve, 200));

          // فتح رابط التحميل في نافذة جديدة
          window.open(file.webContentLink, '_blank');

          // إزالة حالة التحميل
          setDownloadingFiles(prev => {
            const newState = { ...prev };
            delete newState[fileId];
            return newState;
          });

          // عرض رسالة نجاح
          setSuccessModal({
            isOpen: true,
            title: 'تم بدء التحميل',
            message: `تم فتح رابط تحميل الملف "${fileName}" في نافذة جديدة`
          });
        } else {
          throw apiError;
        }
      }

    } catch (error) {
      console.error('Error downloading file:', error);

      // إزالة حالة التحميل في حالة الخطأ
      setDownloadingFiles(prev => {
        const newState = { ...prev };
        delete newState[fileId];
        return newState;
      });

      // عرض رسالة خطأ في مودال
      setErrorModal({
        isOpen: true,
        title: 'فشل في التحميل',
        message: `فشل في تحميل الملف "${fileName}". يرجى المحاولة مرة أخرى أو التحقق من الاتصال بالإنترنت.`
      });
    }
  };

  // حساب الحجم الإجمالي
  const totalSize = files.reduce((total, file) => {
    const bytes = parseInt(file.size) || 0;
    return total + bytes;
  }, 0);

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="جميع نسخ Google Drive" size="lg">
      <div>
        {/* Header with refresh button */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 gap-3">
          <div className="flex items-center">
            <FaCloud className="text-blue-600 dark:text-blue-400 ml-3 flex-shrink-0" />
            <div className="min-w-0">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                إدارة نسخ Google Drive
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                عرض وإدارة جميع النسخ الاحتياطية في Google Drive
              </p>
            </div>
          </div>
          <button
            onClick={fetchGoogleDriveFiles}
            className="btn-outline-sm flex items-center justify-center sm:justify-start"
            disabled={loading}
          >
            <FaSync className={`ml-2 ${loading ? 'animate-spin' : ''}`} />
            <span>تحديث</span>
          </button>
        </div>

        {/* Content */}
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-300">جاري تحميل ملفات Google Drive...</p>
          </div>
        ) : files.length === 0 ? (
          <div className="text-center py-12">
            <FaCloud className="mx-auto h-16 w-16 text-gray-400 dark:text-gray-500 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              لا توجد ملفات
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              لا توجد نسخ احتياطية في Google Drive.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Stats */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div className="flex items-center justify-center sm:justify-start">
                  <div className="bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 p-2 rounded-lg ml-3">
                    <FaCloud className="text-lg" />
                  </div>
                  <div className="text-center sm:text-right">
                    <div className="text-lg font-bold text-gray-900 dark:text-gray-100">{files.length}</div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">إجمالي الملفات</div>
                  </div>
                </div>
                <div className="flex items-center justify-center sm:justify-start">
                  <div className="bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 p-2 rounded-lg ml-3">
                    <FaHdd className="text-lg" />
                  </div>
                  <div className="text-center sm:text-right">
                    <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
                      {formatFileSize(totalSize)}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">الحجم الإجمالي</div>
                  </div>
                </div>
                <div className="flex items-center justify-center sm:justify-start">
                  <div className="bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 p-2 rounded-lg ml-3">
                    <FaCalendarAlt className="text-lg" />
                  </div>
                  <div className="text-center sm:text-right">
                    <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
                      {files.length > 0 ? formatDate(files[0].createdTime).split(' ')[0] : '-'}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">آخر نسخة</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Files List */}
            <div className="space-y-3">
              {files.map((file, index) => (
                <div
                  key={file.id}
                  className="bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 rounded-lg p-3 hover:shadow-md transition-shadow duration-200"
                >
                  {/* Desktop Layout */}
                  <div className="hidden sm:flex items-center justify-between">
                    {/* File Info */}
                    <div className="flex-1">
                      <div className="flex items-center mb-1">
                        <FaCloud className="text-blue-600 dark:text-blue-400 ml-2 flex-shrink-0" />
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-0.5">
                            <h5 className="font-medium text-gray-900 dark:text-gray-100 break-all text-sm">
                              {file.name}
                            </h5>
                            {index === 0 && (
                              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 flex-shrink-0">
                                أحدث
                              </span>
                            )}
                          </div>
                          <div className="flex items-center space-x-3 space-x-reverse">
                            <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                              <FaCalendarAlt className="ml-1 text-xs" />
                              <span>{formatDate(file.createdTime)}</span>
                            </div>
                            <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                              <FaHdd className="ml-1 text-xs" />
                              <span>{formatFileSize(file.size)}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <button
                        onClick={() => handleDownload(file.id, file.name)}
                        className="btn-outline-sm flex items-center relative"
                        title="تحميل الملف"
                        disabled={downloadingFiles[file.id] !== undefined}
                      >
                        {downloadingFiles[file.id] !== undefined ? (
                          <div className="flex items-center">
                            <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent ml-1"></div>
                            <span className="text-xs">{downloadingFiles[file.id]}%</span>
                          </div>
                        ) : (
                          <FaDownload className="text-sm" />
                        )}
                      </button>
                      <button
                        onClick={() => handleDelete(file.id, file.name)}
                        className="btn-danger-sm flex items-center"
                        title="حذف الملف"
                        disabled={downloadingFiles[file.id] !== undefined}
                      >
                        <FaTrash className="text-sm" />
                      </button>
                    </div>
                  </div>

                  {/* Mobile Layout */}
                  <div className="sm:hidden">
                    <div className="flex items-start mb-2">
                      <FaCloud className="text-blue-600 dark:text-blue-400 ml-2 mt-0.5 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h5 className="font-medium text-gray-900 dark:text-gray-100 break-all text-sm">
                            {file.name}
                          </h5>
                          {index === 0 && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 flex-shrink-0">
                              أحدث
                            </span>
                          )}
                        </div>
                        <div className="space-y-0.5">
                          <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                            <FaCalendarAlt className="ml-1 text-xs flex-shrink-0" />
                            <span className="truncate">{formatDate(file.createdTime)}</span>
                          </div>
                          <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                            <FaHdd className="ml-1 text-xs flex-shrink-0" />
                            <span>{formatFileSize(file.size)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex justify-end space-x-2 space-x-reverse">
                      <button
                        onClick={() => handleDownload(file.id, file.name)}
                        className="btn-outline-sm flex items-center"
                        title="تحميل الملف"
                        disabled={downloadingFiles[file.id] !== undefined}
                      >
                        {downloadingFiles[file.id] !== undefined ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent ml-1"></div>
                            <span>{downloadingFiles[file.id]}%</span>
                          </>
                        ) : (
                          <>
                            <FaDownload className="ml-1 text-sm" />
                            <span>تحميل</span>
                          </>
                        )}
                      </button>
                      <button
                        onClick={() => handleDelete(file.id, file.name)}
                        className="btn-danger-sm flex items-center"
                        title="حذف الملف"
                        disabled={downloadingFiles[file.id] !== undefined}
                      >
                        <FaTrash className="ml-1 text-sm" />
                        <span>حذف</span>
                      </button>
                    </div>
                  </div>

                  {/* شريط التقدم */}
                  {downloadingFiles[file.id] !== undefined && (
                    <div className="mt-3 px-1">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                          جاري التحميل...
                        </span>
                        <span className="text-xs text-gray-600 dark:text-gray-400">
                          {downloadingFiles[file.id]}%
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                          style={{ width: `${downloadingFiles[file.id]}%` }}
                        ></div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Info about Google Drive */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3 mt-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <FaCloud className="h-5 w-5 text-blue-400" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-blue-800 dark:text-blue-300">
                    نسخ Google Drive
                  </h3>
                  <p className="mt-1 text-sm text-blue-700 dark:text-blue-400">
                    هذه النسخ محفوظة في Google Drive الخاص بك. يمكنك تحميلها أو حذفها حسب الحاجة.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="flex justify-end mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="btn-secondary"
          >
            إغلاق
          </button>
        </div>
      </div>

      {/* نافذة رسالة الخطأ */}
      <SimpleConfirmModal
        isOpen={errorModal.isOpen}
        onClose={() => setErrorModal({ isOpen: false, title: '', message: '' })}
        onConfirm={() => setErrorModal({ isOpen: false, title: '', message: '' })}
        title={errorModal.title}
        message={errorModal.message}
        confirmText="حسناً"
      />

      {/* نافذة رسالة النجاح */}
      <SimpleSuccessModal
        isOpen={successModal.isOpen}
        onClose={() => setSuccessModal({ isOpen: false, title: '', message: '' })}
        message={successModal.message}
        autoClose={true}
        autoCloseDelay={3000}
      />
    </Modal>
  );
};

export default AllGoogleDriveBackupsModal;
