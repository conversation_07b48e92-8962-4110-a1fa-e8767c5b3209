import React from 'react';
import { Fa<PERSON><PERSON>ck<PERSON>ircle, FaEx<PERSON><PERSON><PERSON><PERSON>, Fa<PERSON>older, FaHdd, Fa<PERSON><PERSON><PERSON>, FaTimes } from 'react-icons/fa';
import Modal from './Modal';

interface BackupPathTestModalProps {
  isOpen: boolean;
  onClose: () => void;
  testResult: {
    success: boolean;
    backup_path: string;
    absolute_path: string;
    exists: boolean;
    is_writable: boolean;
    message: string;
  } | null;
}

const BackupPathTestModal: React.FC<BackupPathTestModalProps> = ({
  isOpen,
  onClose,
  testResult
}) => {
  if (!testResult) return null;

  const { success, backup_path, absolute_path, exists, is_writable, message } = testResult;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="🧪 نتيجة اختبار مسار النسخ الاحتياطية"
      size="lg"
    >
      <div className="space-y-4">
        {/* حالة النتيجة */}
        <div className={`flex items-center p-4 rounded-lg border-2 ${
          success
            ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
            : 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20'
        }`}>
          {success ? (
            <FaCheckCircle className="text-green-600 dark:text-green-400 text-2xl ml-3 flex-shrink-0" />
          ) : (
            <FaExclamationTriangle className="text-red-600 dark:text-red-400 text-2xl ml-3 flex-shrink-0" />
          )}
          <div>
            <h3 className={`font-semibold text-lg ${
              success
                ? 'text-green-800 dark:text-green-200'
                : 'text-red-800 dark:text-red-200'
            }`}>
              {success ? 'المسار صالح ومتاح' : 'مشكلة في المسار'}
            </h3>
            <p className={`text-sm mt-1 ${
              success
                ? 'text-green-700 dark:text-green-300'
                : 'text-red-700 dark:text-red-300'
            }`}>
              {message}
            </p>
          </div>
        </div>

        {/* تفاصيل المسار */}
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
            <FaFolder className="ml-2 text-primary-600" />
            تفاصيل المسار:
          </h4>

          <div className="space-y-3">
            {/* المسار المدخل */}
            <div className="flex items-start">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400 min-w-[100px]">
                المسار المدخل:
              </span>
              <span className="text-sm font-mono bg-white dark:bg-gray-700 px-2 py-1 rounded border text-gray-900 dark:text-gray-100 break-all flex-1">
                {backup_path}
              </span>
            </div>

            {/* المسار المطلق */}
            <div className="flex items-start">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400 min-w-[100px]">
                المسار المطلق:
              </span>
              <span className="text-sm font-mono bg-white dark:bg-gray-700 px-2 py-1 rounded border text-gray-900 dark:text-gray-100 break-all flex-1">
                {absolute_path}
              </span>
            </div>
          </div>
        </div>

        {/* حالة المجلد */}
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
            <FaHdd className="ml-2 text-primary-600" />
            حالة المجلد:
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {/* وجود المجلد */}
            <div className="flex items-center p-3 rounded-lg border border-gray-200 dark:border-gray-600">
              {exists ? (
                <FaCheck className="text-green-600 dark:text-green-400 ml-2" />
              ) : (
                <FaTimes className="text-red-600 dark:text-red-400 ml-2" />
              )}
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  وجود المجلد
                </p>
                <p className={`text-xs ${
                  exists
                    ? 'text-green-600 dark:text-green-400'
                    : 'text-red-600 dark:text-red-400'
                }`}>
                  {exists ? 'المجلد موجود' : 'المجلد غير موجود'}
                </p>
              </div>
            </div>

            {/* إمكانية الكتابة */}
            <div className="flex items-center p-3 rounded-lg border border-gray-200 dark:border-gray-600">
              {is_writable ? (
                <FaCheck className="text-green-600 dark:text-green-400 ml-2" />
              ) : (
                <FaTimes className="text-red-600 dark:text-red-400 ml-2" />
              )}
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  إمكانية الكتابة
                </p>
                <p className={`text-xs ${
                  is_writable
                    ? 'text-green-600 dark:text-green-400'
                    : 'text-red-600 dark:text-red-400'
                }`}>
                  {is_writable ? 'قابل للكتابة' : 'غير قابل للكتابة'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* نصائح */}
        {!success && (
          <div className="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
            <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
              💡 نصائح لحل المشكلة:
            </h4>
            <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
              {!exists && (
                <>
                  <li>• تأكد من صحة مسار المجلد المدخل</li>
                  <li>• تأكد من وجود المجلد على القرص الصلب</li>
                </>
              )}
              {!is_writable && (
                <>
                  <li>• تأكد من أن لديك صلاحيات الكتابة في هذا المجلد</li>
                  <li>• جرب تشغيل التطبيق كمدير (Administrator)</li>
                </>
              )}
              <li>• استخدم مسار مطلق مثل: C:\Backups أو D:\MyBackups</li>
              <li>• تجنب المجلدات المحمية مثل Program Files</li>
              <li>• تأكد من وجود مساحة كافية على القرص</li>
            </ul>
          </div>
        )}

        {/* معلومات إضافية للنجاح */}
        {success && (
          <div className="bg-green-50 dark:bg-green-900/30 p-4 rounded-lg border border-green-200 dark:border-green-800">
            <h4 className="font-semibold text-green-800 dark:text-green-200 mb-2">
              ✅ المسار جاهز للاستخدام:
            </h4>
            <ul className="text-sm text-green-700 dark:text-green-300 space-y-1">
              <li>• سيتم حفظ النسخ الاحتياطية في هذا المجلد</li>
              <li>• يمكنك الآن إنشاء نسخ احتياطية بأمان</li>
              <li>• تأكد من عمل نسخ احتياطية دورية لحماية بياناتك</li>
            </ul>
          </div>
        )}

        {/* زر الإغلاق */}
        <div className="flex justify-center pt-4">
          <button
            onClick={onClose}
            className="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors flex items-center"
          >
            <FaCheck className="ml-2" />
            موافق
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default BackupPathTestModal;
