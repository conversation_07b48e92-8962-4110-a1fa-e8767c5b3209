import React from 'react';
import { Scrollbars } from 'react-custom-scrollbars-2';
import { useTheme } from '../contexts/ThemeContext';

interface CustomScrollbarProps {
  children: React.ReactNode;
  style?: React.CSSProperties;
  className?: string;
  autoHide?: boolean;
  autoHideTimeout?: number;
  autoHideDuration?: number;
  thumbMinSize?: number;
  universal?: boolean;
}

/**
 * مكون شريط التمرير المخصص المتوافق مع الوضع المظلم
 * يحل مشكلة شريط التمرير الرئيسي للمتصفح في الوضع المظلم
 * محسن للاستخدام في المكونات الفرعية
 */
const CustomScrollbar: React.FC<CustomScrollbarProps> = ({
  children,
  style = { height: '100%' },
  className = '',
  autoHide = true,
  autoHideTimeout = 800,
  autoHideDuration = 150,
  thumbMinSize = 25,
  universal = true,
}) => {
  const { currentTheme } = useTheme();
  const isDark = currentTheme === 'dark';

  // تخصيص مظهر مقبض التمرير العمودي
  const renderThumbVertical = ({ style, ...props }: any) => {
    const thumbStyle = {
      ...style,
      backgroundColor: isDark 
        ? 'rgba(75, 85, 99, 0.6)' // gray-600 للوضع المظلم
        : 'rgba(156, 163, 175, 0.6)', // gray-400 للوضع الفاتح
      borderRadius: '6px',
      transition: 'all 0.2s ease-in-out',
      cursor: 'pointer',
    };

    return (
      <div
        {...props}
        style={thumbStyle}
        className="hover:opacity-80 active:opacity-90"
      />
    );
  };

  // تخصيص مظهر مقبض التمرير الأفقي
  const renderThumbHorizontal = ({ style, ...props }: any) => {
    const thumbStyle = {
      ...style,
      backgroundColor: isDark 
        ? 'rgba(75, 85, 99, 0.6)' // gray-600 للوضع المظلم
        : 'rgba(156, 163, 175, 0.6)', // gray-400 للوضع الفاتح
      borderRadius: '6px',
      transition: 'all 0.2s ease-in-out',
      cursor: 'pointer',
    };

    return (
      <div
        {...props}
        style={thumbStyle}
        className="hover:opacity-80 active:opacity-90"
      />
    );
  };

  // تخصيص مظهر مسار التمرير العمودي
  const renderTrackVertical = ({ style, ...props }: any) => {
    const trackStyle = {
      ...style,
      backgroundColor: 'transparent',
      borderRadius: '6px',
      right: '2px',
      bottom: '2px',
      top: '2px',
      width: '8px',
    };

    return <div {...props} style={trackStyle} />;
  };

  // تخصيص مظهر مسار التمرير الأفقي
  const renderTrackHorizontal = ({ style, ...props }: any) => {
    const trackStyle = {
      ...style,
      backgroundColor: 'transparent',
      borderRadius: '6px',
      left: '2px',
      right: '2px',
      bottom: '2px',
      height: '8px',
    };

    return <div {...props} style={trackStyle} />;
  };

  // تخصيص منطقة العرض
  const renderView = ({ style, ...props }: any) => {
    const viewStyle = {
      ...style,
      // إزالة الحشو الافتراضي
      paddingRight: 0,
      paddingBottom: 0,
    };

    return <div {...props} style={viewStyle} />;
  };

  return (
    <div className={className}>
      <Scrollbars
        style={style}
        renderView={renderView}
        renderThumbVertical={renderThumbVertical}
        renderThumbHorizontal={renderThumbHorizontal}
        renderTrackVertical={renderTrackVertical}
        renderTrackHorizontal={renderTrackHorizontal}
        autoHide={autoHide}
        autoHideTimeout={autoHideTimeout}
        autoHideDuration={autoHideDuration}
        thumbMinSize={thumbMinSize}
        universal={universal}
        hideTracksWhenNotNeeded={true}
      >
        {children}
      </Scrollbars>
    </div>
  );
};

export default CustomScrollbar;
