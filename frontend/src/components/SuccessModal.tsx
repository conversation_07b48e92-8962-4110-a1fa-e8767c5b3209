import React from 'react';
import { FaCheckCircle, FaDatabase, FaCalendarAlt, FaHdd, FaFileArchive, FaCloud, FaExclamationTriangle } from 'react-icons/fa';
import Modal from './Modal';

interface SuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  message: string;
  details?: {
    backup_name?: string;
    size?: string;
    restored_at?: string;
    backup_before_restore?: string;
    created_at?: string;
    google_drive_status?: string;
    google_drive_message?: string;
    google_drive_file?: string;
  };
  autoClose?: boolean;
  autoCloseDelay?: number;
}

const SuccessModal: React.FC<SuccessModalProps> = ({
  isOpen,
  onClose,
  title,
  message,
  details,
  autoClose = false,
  autoCloseDelay = 3000
}) => {
  React.useEffect(() => {
    if (isOpen && autoClose) {
      const timer = setTimeout(() => {
        onClose();
      }, autoCloseDelay);
      return () => clearTimeout(timer);
    }
  }, [isOpen, autoClose, autoCloseDelay, onClose]);

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={title} size="sm" zIndex="highest">
      <div className="text-center py-2">
        {/* Success Icon */}
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/30 mb-4">
          <FaCheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
        </div>

        {/* Success Message */}
        <div className="mb-4">
          <p className="text-base font-medium text-gray-900 dark:text-gray-100">
            {message}
          </p>
        </div>

        {/* Details Card */}
        {details && (
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6 text-right">
            <h5 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-3">
              تفاصيل العملية:
            </h5>
            <div className="grid grid-cols-1 gap-3">
              {details.backup_name && (
                <div className="flex items-center">
                  <FaDatabase className="text-primary-600 dark:text-primary-400 ml-3 flex-shrink-0" />
                  <div className="flex-1">
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {details.restored_at ? 'النسخة المستعادة' : 'اسم الملف'}
                    </p>
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100 break-all">
                      {details.backup_name}
                    </p>
                  </div>
                </div>
              )}

              {details.size && (
                <div className="flex items-center">
                  <FaHdd className="text-success-600 dark:text-success-400 ml-3 flex-shrink-0" />
                  <div className="flex-1">
                    <p className="text-xs text-gray-500 dark:text-gray-400">حجم الملف</p>
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {details.size}
                    </p>
                  </div>
                </div>
              )}

              {(details.restored_at || details.created_at) && (
                <div className="flex items-center">
                  <FaCalendarAlt className="text-warning-600 dark:text-warning-400 ml-3 flex-shrink-0" />
                  <div className="flex-1">
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {details.restored_at ? 'تاريخ النسخة' : 'تاريخ الإنشاء'}
                    </p>
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {details.restored_at || details.created_at}
                    </p>
                  </div>
                </div>
              )}

              {details.backup_before_restore && (
                <div className="flex items-center">
                  <FaFileArchive className="text-info-600 dark:text-info-400 ml-3 flex-shrink-0" />
                  <div className="flex-1">
                    <p className="text-xs text-gray-500 dark:text-gray-400">نسخة احتياطية من البيانات السابقة</p>
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100 break-all">
                      {details.backup_before_restore}
                    </p>
                  </div>
                </div>
              )}

              {details.google_drive_status && (
                <div className="flex items-center">
                  {details.google_drive_status === 'نجح' ? (
                    <FaCloud className="text-blue-600 dark:text-blue-400 ml-3 flex-shrink-0" />
                  ) : (
                    <FaExclamationTriangle className="text-yellow-600 dark:text-yellow-400 ml-3 flex-shrink-0" />
                  )}
                  <div className="flex-1">
                    <p className="text-xs text-gray-500 dark:text-gray-400">Google Drive</p>
                    <p className={`text-sm font-medium ${
                      details.google_drive_status === 'نجح'
                        ? 'text-blue-600 dark:text-blue-400'
                        : 'text-yellow-600 dark:text-yellow-400'
                    }`}>
                      {details.google_drive_message}
                    </p>
                    {details.google_drive_file && details.google_drive_status === 'نجح' && (
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 break-all">
                        الملف: {details.google_drive_file}
                      </p>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Auto-close notice */}
        {autoClose && (
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3 mb-6">
            <p className="text-xs text-blue-700 dark:text-blue-400">
              {details?.backup_before_restore
                ? 'سيتم إعادة تحميل الصفحة تلقائياً لتطبيق التغييرات...'
                : 'ستُغلق هذه النافذة تلقائياً خلال ثوانٍ قليلة...'
              }
            </p>
          </div>
        )}

        {/* Close Button */}
        <button
          onClick={onClose}
          className="btn-primary flex items-center justify-center min-w-[100px] mx-auto text-sm"
        >
          <FaCheckCircle className="ml-2" />
          <span>موافق</span>
        </button>
      </div>
    </Modal>
  );
};

export default SuccessModal;
