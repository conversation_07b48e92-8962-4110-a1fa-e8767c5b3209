/**
 * مكون الخريطة التفاعلية باستخدام Leaflet و OpenStreetMap
 * يوفر إمكانية البحث عن العناوين وتحديد المواقع على الخريطة
 * يطبق مبادئ البرمجة الكائنية والتصميم الموحد للنظام
 */

import React, { useState, useRef, useEffect } from 'react';
import { MapContainer, TileLayer, Marker, Popup, useMapEvents } from 'react-leaflet';
import L from 'leaflet';
import { FiSearch, FiMapPin, FiNavigation, FiLoader, FiX } from 'react-icons/fi';
import { openStreetMapService, LocationCoordinates, SearchResult } from '../services/openStreetMapService';
import './InteractiveMap.css';

// إصلاح أيقونات Leaflet الافتراضية
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface InteractiveMapProps {
  onLocationSelect: (coordinates: LocationCoordinates, address: string) => void;
  initialLocation?: LocationCoordinates;
  currentCoordinates?: LocationCoordinates;
  className?: string;
}

interface MapClickHandlerProps {
  onMapClick: (coordinates: LocationCoordinates) => void;
}

// مكون للتعامل مع النقر على الخريطة
const MapClickHandler: React.FC<MapClickHandlerProps> = ({ onMapClick }) => {
  useMapEvents({
    click: (e) => {
      onMapClick({
        lat: e.latlng.lat,
        lng: e.latlng.lng
      });
    }
  });
  return null;
};

const InteractiveMap: React.FC<InteractiveMapProps> = ({
  onLocationSelect,
  initialLocation,
  currentCoordinates,
  className = ''
}) => {


  // استخدام الإحداثيات الحالية إذا كانت متوفرة، وإلا استخدام الموقع الأولي
  const defaultLocation = currentCoordinates || initialLocation || { lat: 32.8872, lng: 13.1913 };

  const [selectedLocation, setSelectedLocation] = useState<LocationCoordinates | null>(
    currentCoordinates || initialLocation || null
  );
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isLoadingAddress, setIsLoadingAddress] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [mapCenter, setMapCenter] = useState<LocationCoordinates>(defaultLocation);
  const [mapZoom, setMapZoom] = useState(currentCoordinates ? 16 : 13); // زوم أكبر إذا كانت هناك إحداثيات محفوظة
  const searchTimeoutRef = useRef<NodeJS.Timeout>();
  const initialLoadRef = useRef(false); // لتتبع التحميل الأولي

  // إعادة تعيين التحميل الأولي عند تغيير الإحداثيات الحالية
  useEffect(() => {
    initialLoadRef.current = false;
  }, [currentCoordinates]);

  // تحميل العنوان المحفوظ عند وجود إحداثيات (مرة واحدة فقط عند التحميل الأولي)
  useEffect(() => {

    const loadSavedAddress = async () => {
      if (currentCoordinates && !initialLoadRef.current) { // فقط في التحميل الأولي
        console.log('🔄 تحميل العنوان المحفوظ للإحداثيات:', currentCoordinates);
        setIsLoadingAddress(true);

        try {
          // الحصول على العنوان من الإحداثيات المحفوظة
          const result = await openStreetMapService.reverseGeocode(
            currentCoordinates.lat,
            currentCoordinates.lng
          );

          if (result && result.address) {
            const arabicAddress = openStreetMapService.formatAddressForStorage(result);
            console.log('✅ تم تحميل العنوان المحفوظ:', arabicAddress);
            setSearchQuery(arabicAddress);

            // تحديث الموقع المحدد والخريطة فقط إذا لم يكن محدداً مسبقاً
            if (!selectedLocation) {
              setSelectedLocation(currentCoordinates);
              setMapCenter(currentCoordinates);
              setMapZoom(16);
            }
          } else {
            // في حالة عدم وجود نتيجة، استخدم الإحداثيات
            const fallbackAddress = `${currentCoordinates.lat.toFixed(6)}, ${currentCoordinates.lng.toFixed(6)}`;
            console.log('⚠️ استخدام الإحداثيات كعنوان احتياطي:', fallbackAddress);
            setSearchQuery(fallbackAddress);

            // تحديث الموقع المحدد والخريطة فقط إذا لم يكن محدداً مسبقاً
            if (!selectedLocation) {
              setSelectedLocation(currentCoordinates);
              setMapCenter(currentCoordinates);
              setMapZoom(16);
            }
          }
        } catch (error) {
          console.error('❌ خطأ في تحميل العنوان المحفوظ:', error);
          // استخدام الإحداثيات كعنوان احتياطي
          const fallbackAddress = `${currentCoordinates.lat.toFixed(6)}, ${currentCoordinates.lng.toFixed(6)}`;
          setSearchQuery(fallbackAddress);

          // تحديث الموقع المحدد والخريطة فقط إذا لم يكن محدداً مسبقاً
          if (!selectedLocation) {
            setSelectedLocation(currentCoordinates);
            setMapCenter(currentCoordinates);
            setMapZoom(16);
          }
        } finally {
          setIsLoadingAddress(false);
          initialLoadRef.current = true; // تم التحميل الأولي
        }
      }
    };

    loadSavedAddress();
  }, [currentCoordinates]); // إزالة onLocationSelect من dependencies لتجنب infinite loop

  // البحث عن العناوين
  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      setShowResults(false);
      return;
    }

    setIsSearching(true);
    try {
      const results = await openStreetMapService.searchAddresses(query, 5);
      setSearchResults(results);
      setShowResults(true);
    } catch (error) {
      console.error('خطأ في البحث:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // تأخير البحث لتحسين الأداء
  const handleSearchInputChange = (value: string) => {
    setSearchQuery(value);
    
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      handleSearch(value);
    }, 500);
  };

  // اختيار نتيجة البحث
  const handleSearchResultSelect = async (result: SearchResult) => {
    const coordinates: LocationCoordinates = {
      lat: parseFloat(result.lat),
      lng: parseFloat(result.lon)
    };

    setSelectedLocation(coordinates);
    setMapCenter(coordinates);
    setMapZoom(16);
    setShowResults(false);

    // استخدام العنوان العربي المحسن
    const arabicAddress = openStreetMapService.formatAddressForStorage(result);
    setSearchQuery(arabicAddress);
    onLocationSelect(coordinates, arabicAddress);
  };

  // التعامل مع النقر على الخريطة
  const handleMapClick = async (coordinates: LocationCoordinates) => {
    setSelectedLocation(coordinates);
    setIsLoadingAddress(true);

    try {
      // الحصول على العنوان العربي المحسن من الخدمة
      const result = await openStreetMapService.reverseGeocode(coordinates.lat, coordinates.lng);

      if (result && result.address) {
        const arabicAddress = openStreetMapService.formatAddressForStorage(result);
        setSearchQuery(arabicAddress);
        onLocationSelect(coordinates, arabicAddress);
      } else {
        // في حالة عدم وجود نتيجة، استخدم الإحداثيات
        const fallbackAddress = `${coordinates.lat.toFixed(6)}, ${coordinates.lng.toFixed(6)}`;
        setSearchQuery(fallbackAddress);
        onLocationSelect(coordinates, fallbackAddress);
      }
    } catch (error) {
      console.error('خطأ في الحصول على العنوان:', error);
      const fallbackAddress = `${coordinates.lat.toFixed(6)}, ${coordinates.lng.toFixed(6)}`;
      setSearchQuery(fallbackAddress);
      onLocationSelect(coordinates, fallbackAddress);
    } finally {
      setIsLoadingAddress(false);
    }
  };

  // الحصول على الموقع الحالي
  const handleGetCurrentLocation = async () => {
    setIsLoadingAddress(true);

    try {
      console.log('🔍 بدء تحديد الموقع الحالي...');

      // فحص دعم المتصفح لتحديد الموقع
      if (!navigator.geolocation) {
        throw new Error('خدمة تحديد الموقع غير مدعومة في هذا المتصفح');
      }

      console.log('✅ خدمة تحديد الموقع مدعومة');

      // فحص الأذونات أولاً
      if (navigator.permissions) {
        try {
          const permission = await navigator.permissions.query({ name: 'geolocation' });
          console.log('🔐 حالة إذن الموقع:', permission.state);

          if (permission.state === 'denied') {
            throw new Error('تم رفض إذن الوصول للموقع. يرجى السماح بالوصول للموقع في إعدادات المتصفح.');
          }
        } catch (permError) {
          console.log('⚠️ لا يمكن فحص الأذونات:', permError);
        }
      }

      const coordinates = await openStreetMapService.getCurrentLocation();
      console.log('📍 تم الحصول على الإحداثيات:', coordinates);

      setSelectedLocation(coordinates);
      setMapCenter(coordinates);
      setMapZoom(16);

      // الحصول على العنوان العربي المحسن من الخدمة
      console.log('🔍 جاري الحصول على العنوان...');
      const result = await openStreetMapService.reverseGeocode(coordinates.lat, coordinates.lng);

      if (result && result.address) {
        const arabicAddress = openStreetMapService.formatAddressForStorage(result);
        console.log('✅ تم الحصول على العنوان:', arabicAddress);
        setSearchQuery(arabicAddress);
        onLocationSelect(coordinates, arabicAddress);
      } else {
        const fallbackAddress = `${coordinates.lat.toFixed(6)}, ${coordinates.lng.toFixed(6)}`;
        console.log('⚠️ استخدام الإحداثيات كعنوان احتياطي:', fallbackAddress);
        setSearchQuery(fallbackAddress);
        onLocationSelect(coordinates, fallbackAddress);
      }

      console.log('🎉 تم تحديد الموقع بنجاح');

    } catch (error) {
      console.error('❌ خطأ في تحديد الموقع الحالي:', error);

      // عرض رسالة خطأ محسنة للمستخدم
      const errorMessage = error instanceof Error ? error.message : 'فشل في تحديد الموقع الحالي';

      // إضافة نصائح للمستخدم
      let helpMessage = '';
      if (errorMessage.includes('رفض الإذن') || errorMessage.includes('PERMISSION_DENIED')) {
        helpMessage = '\n\nنصائح:\n• تأكد من السماح بالوصول للموقع في المتصفح\n• تحقق من إعدادات الخصوصية في المتصفح\n• جرب إعادة تحميل الصفحة';
      } else if (errorMessage.includes('غير متاحة') || errorMessage.includes('POSITION_UNAVAILABLE')) {
        helpMessage = '\n\nنصائح:\n• تأكد من تفعيل خدمات الموقع في الجهاز\n• تحقق من اتصال الإنترنت\n• جرب في مكان مفتوح';
      } else if (errorMessage.includes('انتهت المهلة') || errorMessage.includes('TIMEOUT')) {
        helpMessage = '\n\nنصائح:\n• جرب مرة أخرى\n• تأكد من قوة إشارة GPS\n• انتقل إلى مكان مفتوح';
      }

      alert(`خطأ في تحديد الموقع: ${errorMessage}${helpMessage}`);

    } finally {
      setIsLoadingAddress(false);
    }
  };

  // مسح البحث
  const clearSearch = () => {
    setSearchQuery('');
    setSearchResults([]);
    setShowResults(false);
  };

  return (
    <div className={`relative ${className}`}>
      {/* شريط البحث مع z-index عالي */}
      <div className="relative mb-4 z-50">
        <div className="relative">
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            {isSearching ? (
              <FiLoader className="h-5 w-5 text-gray-400 animate-spin" />
            ) : (
              <FiSearch className="h-5 w-5 text-gray-400" />
            )}
          </div>
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => handleSearchInputChange(e.target.value)}
            placeholder="ابحث عن عنوان أو مكان..."
            className="w-full pl-10 pr-12 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-4 focus:ring-primary-500/20 focus:border-primary-500 transition-all duration-200"
          />
          {searchQuery && (
            <button
              onClick={clearSearch}
              className="absolute inset-y-0 left-0 pl-3 flex items-center"
            >
              <FiX className="h-5 w-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" />
            </button>
          )}
        </div>

        {/* نتائج البحث مع z-index أعلى */}
        {showResults && searchResults.length > 0 && (
          <div className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-700 border-2 border-gray-300 dark:border-gray-600 rounded-xl shadow-xl max-h-60 overflow-y-auto" style={{ zIndex: 9999 }}>
            {searchResults.map((result, index) => (
              <button
                key={result.place_id}
                onClick={() => handleSearchResultSelect(result)}
                className={`w-full text-right p-3 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors ${
                  index !== searchResults.length - 1 ? 'border-b border-gray-200 dark:border-gray-600' : ''
                }`}
              >
                <div className="flex items-start gap-3">
                  <FiMapPin className="h-5 w-5 text-primary-500 mt-0.5 flex-shrink-0" />
                  <div className="text-sm">
                    <div className="font-medium text-gray-900 dark:text-gray-100">
                      {result.display_name}
                    </div>
                    <div className="text-gray-500 dark:text-gray-400 text-xs mt-1">
                      {result.type} • {result.class}
                    </div>
                  </div>
                </div>
              </button>
            ))}
          </div>
        )}
      </div>

      {/* أزرار التحكم */}
      <div className="flex gap-2 mb-4">
        <button
          onClick={handleGetCurrentLocation}
          disabled={isLoadingAddress}
          className="flex items-center gap-2 px-4 py-2 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-md hover:shadow-lg"
          title="انقر للحصول على موقعك الحالي (يتطلب إذن الوصول للموقع)"
        >
          {isLoadingAddress ? (
            <>
              <FiLoader className="h-4 w-4 animate-spin" />
              <span className="text-sm">جاري تحديد الموقع...</span>
            </>
          ) : (
            <>
              <FiNavigation className="h-4 w-4" />
              <span className="text-sm">موقعي الحالي</span>
            </>
          )}
        </button>

        {/* رسالة توضيحية للموقع الحالي */}
        <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
          <span>💡 يتطلب إذن الوصول للموقع</span>
        </div>
      </div>

      {/* الخريطة */}
      <div className="h-96 rounded-xl overflow-hidden border-2 border-gray-300 dark:border-gray-600 relative z-10">
        <MapContainer
          center={[mapCenter.lat, mapCenter.lng]}
          zoom={mapZoom}
          style={{ height: '100%', width: '100%' }}
          key={`${mapCenter.lat}-${mapCenter.lng}-${mapZoom}`}
        >
          <TileLayer
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />
          
          <MapClickHandler onMapClick={handleMapClick} />
          
          {selectedLocation && (
            <Marker position={[selectedLocation.lat, selectedLocation.lng]}>
              <Popup>
                <div className="text-center">
                  <div className="font-medium text-gray-900">الموقع المحدد</div>
                  <div className="text-sm text-gray-600 mt-1">
                    {selectedLocation.lat.toFixed(6)}, {selectedLocation.lng.toFixed(6)}
                  </div>
                </div>
              </Popup>
            </Marker>
          )}
        </MapContainer>
      </div>

      {/* معلومات الموقع المحدد */}
      {selectedLocation && (
        <div className="mt-4 p-4 bg-green-50 dark:bg-green-900/20 rounded-xl border border-green-200 dark:border-green-800">
          <div className="flex items-start gap-3">
            <FiMapPin className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <div className="font-medium text-green-900 dark:text-green-100 mb-2">
                الموقع المحدد
              </div>

              {isLoadingAddress ? (
                <div className="flex items-center gap-2 text-sm text-green-600 dark:text-green-400">
                  <FiLoader className="h-4 w-4 animate-spin" />
                  <span>جاري الحصول على تفاصيل العنوان الدقيقة...</span>
                </div>
              ) : (
                <>
                  <div className="text-sm text-green-700 dark:text-green-300 mb-2 leading-relaxed">
                    {searchQuery || 'لم يتم تحديد عنوان'}
                  </div>
                  <div className="text-xs text-green-600 dark:text-green-400 space-y-1">
                    <div>خط العرض: {selectedLocation.lat.toFixed(6)}</div>
                    <div>خط الطول: {selectedLocation.lng.toFixed(6)}</div>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      )}


    </div>
  );
};

export default InteractiveMap;
