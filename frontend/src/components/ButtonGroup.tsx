import React from 'react';

interface ButtonGroupProps {
  children: React.ReactNode;
  label?: string;
  className?: string;
  orientation?: 'horizontal' | 'vertical';
  size?: 'sm' | 'md' | 'lg';
  spacing?: 'tight' | 'normal' | 'loose';
}

const ButtonGroup: React.FC<ButtonGroupProps> = ({
  children,
  label,
  className = '',
  orientation = 'horizontal',
  size = 'md',
  spacing = 'normal'
}) => {
  const getSpacingClass = () => {
    const spacingMap = {
      tight: 'gap-1',
      normal: 'gap-2',
      loose: 'gap-3'
    };
    return spacingMap[spacing];
  };

  const getOrientationClass = () => {
    return orientation === 'horizontal' ? 'flex flex-wrap items-end' : 'flex flex-col';
  };

  const getSizeClass = () => {
    const sizeMap = {
      sm: 'text-xs',
      md: 'text-sm',
      lg: 'text-base'
    };
    return sizeMap[size];
  };

  return (
    <div className={`${className}`}>
      {label && orientation === 'horizontal' ? (
        <div className="flex items-end gap-2">
          <div className={`font-medium text-gray-600 dark:text-gray-400 self-end pb-1 ${getSizeClass()}`}>
            {label}
          </div>
          <div className={`${getOrientationClass()} ${getSpacingClass()}`}>
            {children}
          </div>
        </div>
      ) : (
        <>
          {label && (
            <div className={`font-medium text-gray-600 dark:text-gray-400 mb-2 ${getSizeClass()}`}>
              {label}
            </div>
          )}
          <div className={`${getOrientationClass()} ${getSpacingClass()}`}>
            {children}
          </div>
        </>
      )}
    </div>
  );
};

export default ButtonGroup;
