/**
 * مكون عرض الأرقام المختصرة
 * يعرض الأرقام الكبيرة بتنسيق مختصر مع إمكانية عرض الرقم الكامل
 * يتبع التصميم الموحد للنظام ويدعم الوضع المظلم
 */

import React, { useState, useEffect } from 'react';
import CountUp from 'react-countup';
import { compactNumberService, type CompactNumberResult, type UnitType } from '../services/compactNumberService';
import { FaArrowUp, FaArrowDown, FaChartLine } from 'react-icons/fa';
import UnitIcon, { calculateCompactValue } from './UnitIcon';
import ComparisonIndicator from './ComparisonIndicator';
import { ComparisonData } from '../services/comparisonIndicatorService';

interface CompactNumberDisplayProps {
  /** المبلغ المراد عرضه */
  amount: number;
  /** إظهار رمز العملة أم لا (افتراضي: false) */
  showCurrency?: boolean;
  /** فئة CSS إضافية */
  className?: string;
  /** الحد الأدنى لبدء الاختصار (افتراضي: 1000) */
  compactThreshold?: number;
  /** إظهار الرقم الكامل تحت المختصر (افتراضي: true) */
  showFullNumber?: boolean;
  /** نوع الوحدات (افتراضي: 'arabic') */
  unitType?: UnitType;
  /** عدد الأرقام العشرية للرقم المختصر (افتراضي: 1) */
  decimalPlaces?: number;
  /** حجم العرض */
  size?: 'small' | 'medium' | 'large';
  /** نص بديل في حالة التحميل */
  loadingText?: string;
  /** نص بديل في حالة الخطأ */
  errorText?: string;
  /** دالة callback عند حدوث خطأ */
  onError?: (error: Error) => void;
  /** إجبار إعادة التحميل */
  forceRefresh?: boolean;
}

const CompactNumberDisplay: React.FC<CompactNumberDisplayProps> = ({
  amount,
  showCurrency = false,
  className = '',
  compactThreshold = 1000,
  showFullNumber = true,
  unitType = 'arabic',
  decimalPlaces = 1,
  size = 'medium',
  loadingText = '...',
  errorText = 'خطأ',
  onError,
  forceRefresh = false
}) => {
  const [result, setResult] = useState<CompactNumberResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    let isMounted = true;

    const formatAmount = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // التحقق من صحة المبلغ
        if (typeof amount !== 'number' || isNaN(amount)) {
          throw new Error('Invalid amount provided');
        }

        const options = {
          compactThreshold,
          unitType,
          decimalPlaces,
          showFullNumber
        };

        let formattedResult: CompactNumberResult;

        if (showCurrency) {
          formattedResult = await compactNumberService.formatCompactCurrency(amount, options);
        } else {
          formattedResult = await compactNumberService.formatCompact(amount, options);
        }

        if (isMounted) {
          setResult(formattedResult);
          setIsLoading(false);
        }

      } catch (err) {
        const error = err instanceof Error ? err : new Error('Unknown formatting error');
        
        if (isMounted) {
          setError(error);
          setResult(null);
          setIsLoading(false);
          
          if (onError) {
            onError(error);
          }
        }
      }
    };

    formatAmount();

    return () => {
      isMounted = false;
    };
  }, [amount, showCurrency, compactThreshold, unitType, decimalPlaces, showFullNumber, forceRefresh]);

  // تحديد أحجام النص حسب الحجم المطلوب
  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return {
          compact: 'text-lg font-bold',
          full: 'text-xs'
        };
      case 'large':
        return {
          compact: 'text-4xl font-bold',
          full: 'text-sm'
        };
      default: // medium
        return {
          compact: 'text-3xl font-bold',
          full: 'text-xs'
        };
    }
  };

  const sizeClasses = getSizeClasses();

  // إذا كان هناك خطأ، عرض النص البديل
  if (error) {
    return (
      <div className={`text-red-500 ${className}`}>
        <div className={`${sizeClasses.compact} text-red-500`} title={`خطأ في التنسيق: ${error.message}`}>
          {errorText}
        </div>
        {showFullNumber && (
          <div className={`${sizeClasses.full} text-red-400 mt-1`}>
            خطأ في التحميل
          </div>
        )}
      </div>
    );
  }

  // إذا كان قيد التحميل، عرض نص التحميل
  if (isLoading || !result) {
    return (
      <div className={`text-gray-400 animate-pulse ${className}`}>
        <div className={`${sizeClasses.compact} text-gray-400`}>
          {loadingText}
        </div>
        {showFullNumber && (
          <div className={`${sizeClasses.full} text-gray-300 mt-1`}>
            جاري التحميل...
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`${className}`} title={`القيمة الأصلية: ${result.originalValue}`}>
      {/* الرقم المختصر */}
      <div className={`${sizeClasses.compact} text-secondary-900 dark:text-secondary-100`}>
        {result.compact}
      </div>

      {/* الرقم الكامل أو نقاط بديلة */}
      {showFullNumber && (
        <div className={`${sizeClasses.full} text-secondary-500 dark:text-secondary-400 mt-1`}>
          {result.isCompacted ? result.full : '• • •'}
        </div>
      )}
    </div>
  );
};

export default CompactNumberDisplay;



/**
 * Hook لاستخدام تنسيق الأرقام المختصرة بشكل مباشر
 */
export const useCompactNumber = () => {
  const formatCompact = async (
    amount: number,
    options?: {
      compactThreshold?: number;
      unitType?: UnitType;
      decimalPlaces?: number;
      showFullNumber?: boolean;
    }
  ): Promise<CompactNumberResult> => {
    try {
      return await compactNumberService.formatCompact(amount, options);
    } catch (error) {
      console.error('Error formatting compact number:', error);
      return {
        compact: amount.toString(),
        full: amount.toString(),
        isCompacted: false,
        originalValue: amount
      };
    }
  };

  const formatCompactCurrency = async (
    amount: number,
    options?: {
      compactThreshold?: number;
      unitType?: UnitType;
      decimalPlaces?: number;
      showFullNumber?: boolean;
    }
  ): Promise<CompactNumberResult> => {
    try {
      return await compactNumberService.formatCompactCurrency(amount, options);
    } catch (error) {
      console.error('Error formatting compact currency:', error);
      return {
        compact: `${amount} د.ل`,
        full: `${amount} د.ل`,
        isCompacted: false,
        originalValue: amount
      };
    }
  };

  return { formatCompact, formatCompactCurrency };
};

/**
 * مكون مبسط لعرض الأرقام المختصرة بدون عملة
 */
export const CompactNumber: React.FC<Omit<CompactNumberDisplayProps, 'showCurrency'>> = (props) => {
  return <CompactNumberDisplay {...props} showCurrency={false} />;
};

/**
 * مكون مبسط لعرض العملة المختصرة
 */
export const CompactCurrency: React.FC<Omit<CompactNumberDisplayProps, 'showCurrency'>> = (props) => {
  return <CompactNumberDisplay {...props} showCurrency={true} />;
};

/**
 * مكون للبطاقات الإحصائية مع أرقام مختصرة
 */
interface CompactStatCardProps {
  title: string;
  amount: number;
  icon?: React.ReactNode;
  showCurrency?: boolean;
  className?: string;
  compactThreshold?: number;
  changeText?: string;
  /** تفعيل الرسوم المتحركة (افتراضي: false للتوافق مع النظام الحالي) */
  enableAnimation?: boolean;
  /** مدة الرسم المتحرك بالثواني (افتراضي: 2) */
  animationDuration?: number;
  /** تأخير البدء بالثواني (افتراضي: 0) */
  animationDelay?: number;
  /** القيمة الابتدائية للرسم المتحرك (افتراضي: 0) */
  startValue?: number;
  /** بيانات المقارنة مع الفترة السابقة */
  comparison?: ComparisonData;
  /** نص المقارنة (مثل "مقارنة بمبيعات أمس") */
  comparisonText?: string;
}

/**
 * دالة مساعدة لتنسيق الأرقام مع دعم الأرقام العشرية
 * تستخدم النقطة كفاصلة عشرية والفاصلة لفصل الآلاف
 */
const formatNumber = (value: number, showCurrency: boolean = false): string => {
  const hasDecimals = value % 1 !== 0;

  // تنسيق الرقم مع استخدام النقطة كفاصلة عشرية
  let formattedNumber: string;
  if (hasDecimals) {
    // تنسيق الرقم مع منازل عشرية
    formattedNumber = value.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  } else {
    // تنسيق الرقم الصحيح
    formattedNumber = Math.round(value).toLocaleString('en-US');
  }

  return `${formattedNumber}${showCurrency ? ' د.ل' : ''}`;
};

/**
 * مكون بطاقة إحصائية احترافي مع رموز ملونة وأرقام متحركة
 * يعرض رمز ملون واحد فقط مع الرقم المختصر
 */
export const CompactStatCard: React.FC<CompactStatCardProps> = React.memo(({
  title,
  amount,
  icon,
  showCurrency = false,
  className = '',
  compactThreshold = 1000,
  changeText,
  enableAnimation = false,
  animationDuration = 2,
  animationDelay = 0,
  startValue = 0,
  comparison,
  comparisonText
}) => {
  // حساب الرمز الملون والقيمة المختصرة
  const { compactValue, unit } = calculateCompactValue(amount);
  const isCompacted = amount >= compactThreshold && unit;

  return (
    <div className={`touch-card stats-card ${className}`}>
      <div className="flex justify-between items-start mb-2">
        <div className="flex-1">
          <p className="text-secondary-500 dark:text-secondary-400 text-sm font-medium mb-1">
            {title}
          </p>

          {/* عرض الرقم مع الرمز الملون */}
          <div className="flex items-center gap-2">
            {/* الرمز الملون - يظهر فقط للأرقام المختصرة */}
            {isCompacted && (
              <UnitIcon unit={unit!} size="medium" />
            )}

            {/* الرقم */}
            <div className="text-3xl font-bold text-secondary-900 dark:text-secondary-100">
              {enableAnimation ? (
                <CountUp
                  start={startValue}
                  end={isCompacted ? compactValue : amount}
                  duration={animationDuration}
                  delay={animationDelay}
                  decimals={isCompacted ? 1 : (amount % 1 !== 0 ? 2 : 0)}
                  preserveValue
                  suffix={showCurrency ? ' د.ل' : ''}
                  separator=","
                  decimal="."
                />
              ) : (
                <>
                  {isCompacted
                    ? `${compactValue.toFixed(1)}${showCurrency ? ' د.ل' : ''}`
                    : formatNumber(amount, showCurrency)
                  }
                </>
              )}
            </div>
          </div>

          {/* الرقم الكامل أو النقاط - يظهر دائماً للحفاظ على التوازن البصري */}
          <div className="text-xs text-secondary-500 dark:text-secondary-400 mt-1">
            {isCompacted
              ? formatNumber(amount, showCurrency)
              : '• • •'
            }
          </div>
        </div>

        {/* أيقونة البطاقة */}
        {icon && (
          <div className="p-4 rounded-xl bg-primary-50 dark:bg-primary-900/30 text-xl transition-all duration-300 hover:scale-105">
            {icon}
          </div>
        )}
      </div>

      {/* نص التغيير أو مؤشر المقارنة */}
      {(changeText || (comparison && comparisonText)) && (
        <>
          <div className="stats-card-divider"></div>
          {comparison && comparisonText ? (
            <ComparisonIndicator
              comparison={comparison}
              comparisonText={comparisonText}
              size="small"
              className="text-sm"
            />
          ) : (
            <div className="flex items-center text-sm text-secondary-500 dark:text-secondary-400">
              <span>{changeText}</span>
            </div>
          )}
        </>
      )}
    </div>
  );
});

CompactStatCard.displayName = 'CompactStatCard';

/**
 * مكون خاص لبطاقة معدل النمو مع أرقام مختصرة
 */
interface GrowthRateCardProps {
  growthRate: number;
  currentTotal: number;
  previousTotal: number;
  hasData: boolean;
  isLoading: boolean;
  selectedPeriod: string;
  className?: string;
}

export const GrowthRateCard: React.FC<GrowthRateCardProps> = ({
  growthRate,
  currentTotal,
  previousTotal,
  hasData,
  isLoading,
  selectedPeriod,
  className = ''
}) => {
  // تنسيق الأرقام بدون اختصار - عرض كامل
  const formatCurrency = (amount: number): string => {
    try {
      return `${amount.toLocaleString('ar-LY', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })} د.ل`;
    } catch (error) {
      return `${amount.toFixed(2)} د.ل`;
    }
  };

  return (
    <div className={`touch-card stats-card ${className}`}>
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <p className="text-secondary-500 dark:text-secondary-400 text-sm font-medium mb-1">
            معدل النمو
          </p>

          {/* معدل النمو الرئيسي */}
          <div className="flex items-center gap-2 mb-2">
            <p className={`text-2xl font-bold leading-tight ${growthRate >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
              {growthRate >= 0 ? '+' : ''}{growthRate.toFixed(1)}%
            </p>
            <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium ${growthRate >= 0 ? 'bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400' : 'bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400'}`}>
              {growthRate >= 0 ? (
                <FaArrowUp className="text-xs mr-1" />
              ) : (
                <FaArrowDown className="text-xs mr-1" />
              )}
              <span>{growthRate >= 0 ? 'نمو' : 'انخفاض'}</span>
            </div>
          </div>

          {isLoading ? (
            <p className="text-xs text-gray-500 dark:text-gray-400">
              جاري تحميل البيانات...
            </p>
          ) : hasData ? (
            <>
              <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                {selectedPeriod === 'day' ? 'مقارنة مع اليوم السابق' :
                 selectedPeriod === 'week' ? 'مقارنة مع الأسبوع السابق' :
                 selectedPeriod === 'month' ? 'مقارنة مع الشهر السابق' :
                 'مقارنة مع السنة السابقة'}
              </p>

              {/* القيم الحالية والسابقة */}
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-green-500 dark:bg-green-600 flex-shrink-0"></div>
                  <span className="text-gray-600 dark:text-gray-300 text-xs font-medium">الحالية:</span>
                  <span className="text-gray-900 dark:text-gray-100 text-xs font-bold">
                    {formatCurrency(currentTotal)}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gray-400 dark:bg-gray-500 flex-shrink-0"></div>
                  <span className="text-gray-600 dark:text-gray-300 text-xs font-medium">السابقة:</span>
                  <span className="text-gray-900 dark:text-gray-100 text-xs font-bold">
                    {formatCurrency(previousTotal)}
                  </span>
                </div>
              </div>
            </>
          ) : (
            <p className="text-xs text-gray-500 dark:text-gray-400">
              لا توجد بيانات للفترة السابقة للمقارنة
            </p>
          )}
        </div>

        {/* الأيقونة بنفس تصميم باقي البطاقات */}
        <div className="p-4 rounded-xl bg-primary-50 dark:bg-primary-900/30 text-xl">
          <FaChartLine className={`${growthRate >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`} />
        </div>
      </div>
    </div>
  );
};
