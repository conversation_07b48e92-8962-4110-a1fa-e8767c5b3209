import React from 'react';
import { FiCheck, FiRotateCcw } from 'react-icons/fi';

interface FilterActionButtonsProps {
  onApplyFilters: () => void;
  onResetFilters: () => void;
  className?: string;
  applyText?: string;
  resetText?: string;
  isLoading?: boolean;
}

/**
 * مكون موحد لأزرار تطبيق وإعادة تعيين الفلاتر
 * مصمم ليتطابق مع ارتفاع مكونات الإدخال الموحد (h-10)
 */
const FilterActionButtons: React.FC<FilterActionButtonsProps> = ({
  onApplyFilters,
  onResetFilters,
  className = '',
  applyText = 'تطبيق الفلاتر',
  resetText = 'إعادة تعيين',
  isLoading = false
}) => {
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <button
        type="button"
        onClick={onApplyFilters}
        disabled={isLoading}
        className="flex-1 h-10 bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white px-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 disabled:border-primary-400 flex items-center justify-center gap-2 text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 min-w-0"
      >
        <span className="truncate">{applyText}</span>
        <FiCheck className="w-4 h-4 flex-shrink-0" />
      </button>
      <button
        type="button"
        onClick={onResetFilters}
        disabled={isLoading}
        className="flex-1 h-10 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 disabled:bg-gray-200 dark:disabled:bg-gray-700 text-gray-700 dark:text-gray-300 disabled:text-gray-500 dark:disabled:text-gray-500 px-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 disabled:border-gray-300 dark:disabled:border-gray-600 flex items-center justify-center gap-2 text-sm font-medium focus:outline-none focus:ring-4 focus:ring-gray-500/20 min-w-0"
      >
        <span className="truncate">{resetText}</span>
        <FiRotateCcw className="w-4 h-4 flex-shrink-0" />
      </button>
    </div>
  );
};

export default FilterActionButtons;