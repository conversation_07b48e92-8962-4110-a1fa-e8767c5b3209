import React, { useState } from 'react';
import RichTextEditor from './RichTextEditor';

/**
 * مكون اختبار محرر النصوص الغني
 * يستخدم لاختبار دعم اللغة العربية والتأكد من عمل جميع الميزات
 */
const RichTextEditorTest: React.FC = () => {
  const [content, setContent] = useState<string>('');

  const handleContentChange = (value: string) => {
    setContent(value);
    console.log('محتوى المحرر:', value);
  };

  const sampleArabicText = `
    <h1>عنوان كبير باللغة العربية</h1>
    <h2>عنوان متوسط</h2>
    <p>هذا نص تجريبي باللغة العربية لاختبار محرر النصوص الغني.</p>
    <p><strong>نص عريض</strong> و <em>نص مائل</em> و <u>نص مسطر</u></p>
    <ul>
      <li>عنصر قائمة نقطية أول</li>
      <li>عنصر قائمة نقطية ثاني</li>
      <li>عنصر قائمة نقطية ثالث</li>
    </ul>
    <ol>
      <li>عنصر قائمة رقمية أول</li>
      <li>عنصر قائمة رقمية ثاني</li>
      <li>عنصر قائمة رقمية ثالث</li>
    </ol>
    <p>يمكنك أيضاً إضافة <a href="https://example.com">روابط</a> في النص.</p>
  `;

  const loadSampleText = () => {
    setContent(sampleArabicText);
  };

  const clearContent = () => {
    setContent('');
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-100 mb-2">
          اختبار محرر النصوص الغني
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          اختبار دعم اللغة العربية وجميع ميزات التنسيق
        </p>
      </div>

      <div className="mb-4 flex gap-3">
        <button
          onClick={loadSampleText}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          تحميل نص تجريبي
        </button>
        <button
          onClick={clearContent}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
        >
          مسح المحتوى
        </button>
      </div>

      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          محرر النصوص الغني
        </label>
        <RichTextEditor
          value={content}
          onChange={handleContentChange}
          placeholder="اكتب النص هنا... جرب استخدام الأزرار في شريط الأدوات لتنسيق النص"
        />
      </div>

      <div className="border-t pt-4">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">
          المحتوى المُنتج (HTML):
        </h3>
        <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
          <pre className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap break-all">
            {content || 'لا يوجد محتوى بعد...'}
          </pre>
        </div>
      </div>

      <div className="mt-4 border-t pt-4">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">
          معاينة المحتوى:
        </h3>
        <div 
          className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg prose prose-sm max-w-none"
          style={{ direction: 'rtl', textAlign: 'right' }}
          dangerouslySetInnerHTML={{ __html: content || '<p class="text-gray-500">لا يوجد محتوى للمعاينة...</p>' }}
        />
      </div>

      <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
          ميزات المحرر المدعومة:
        </h4>
        <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
          <li>✅ دعم كامل للغة العربية (RTL)</li>
          <li>✅ تنسيق النص (عريض، مائل، تسطير)</li>
          <li>✅ العناوين بأحجام مختلفة</li>
          <li>✅ القوائم النقطية والرقمية</li>
          <li>✅ المحاذاة والمسافات البادئة</li>
          <li>✅ الروابط والصور والفيديو</li>
          <li>✅ تلميحات الأزرار باللغة العربية</li>
          <li>✅ دعم الوضع المظلم</li>
        </ul>
      </div>
    </div>
  );
};

export default RichTextEditorTest;
