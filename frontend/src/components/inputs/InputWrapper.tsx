/**
 * مكون مُغلف موحد لجميع مكونات الإدخال
 * يضمن التناسق في التصميم والارتفاع عبر جميع مكونات الإدخال
 */

import React from 'react';
import { FaExclamationTriangle, FaCheckCircle } from 'react-icons/fa';
import { inputStylesConfig } from '../../styles/inputStyles';

interface InputWrapperProps {
  label?: string;
  name: string;
  required?: boolean;
  error?: string;
  success?: string;
  className?: string;
  children: React.ReactNode;
}

const InputWrapper: React.FC<InputWrapperProps> = ({
  label,
  name,
  required = false,
  error,
  success,
  className = '',
  children
}) => {
  const showError = error;
  const showSuccess = success && !error;

  return (
    <div className={className}>
      {/* Label */}
      {label && (
        <label
          htmlFor={name}
          className={inputStylesConfig.labelStyles.base}
        >
          {label}
          {required && <span className={inputStylesConfig.labelStyles.required}>*</span>}
        </label>
      )}

      {/* Input Content */}
      {children}

      {/* Error Message */}
      {showError && (
        <div className={inputStylesConfig.messageStyles.error}>
          <FaExclamationTriangle className="ml-2 flex-shrink-0 text-red-500" />
          {error}
        </div>
      )}

      {/* Success Message */}
      {showSuccess && (
        <div className={inputStylesConfig.messageStyles.success}>
          <FaCheckCircle className="ml-2 flex-shrink-0 text-green-500" />
          {success}
        </div>
      )}
    </div>
  );
};

export default InputWrapper;
