import React, { useEffect, useRef, useState } from 'react';
import ReactQuill, { Quill } from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import '../../styles/arabic-rich-text-editor-fixes.css';

// تخصيص Quill للعربية
const DirectionAttribute = Quill.import('attributors/attribute/direction');
const AlignStyle = Quill.import('attributors/style/align');
const BackgroundStyle = Quill.import('attributors/style/background');
const ColorStyle = Quill.import('attributors/style/color');
const FontStyle = Quill.import('attributors/style/font');
const SizeStyle = Quill.import('attributors/style/size');

Quill.register(DirectionAttribute, true);
Quill.register(AlignStyle, true);
Quill.register(BackgroundStyle, true);
Quill.register(ColorStyle, true);
Quill.register(FontStyle, true);
Quill.register(SizeStyle, true);

// أنماط CSS محسنة للعربية
const arabicEditorStyles = `
  .arabic-quill-editor {
    direction: rtl;
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
  }

  .arabic-quill-editor .ql-toolbar {
    direction: rtl !important;
    border-top-right-radius: 0.5rem;
    border-top-left-radius: 0.5rem;
    border: 1px solid #e5e7eb;
    border-bottom: none;
    background: #f9fafb;
    padding: 8px;
  }

  .arabic-quill-editor .ql-toolbar .ql-formats {
    margin-left: 15px;
    margin-right: 0;
  }

  .arabic-quill-editor .ql-toolbar .ql-formats:first-child {
    margin-left: 0;
  }

  /* إصلاح شريط الأدوات للعربية - تصميم مثل حاويات التطبيق */
  .arabic-quill-editor .ql-toolbar {
    display: flex !important;
    flex-wrap: nowrap !important;
    align-items: center !important;
    justify-content: flex-start !important;
    gap: 6px !important;
    padding: 8px 12px !important;
    overflow-x: auto !important;
    overflow-y: hidden !important;
    min-height: 44px !important;
    background: #f9fafb !important;
    border: 1px solid #e5e7eb !important;
    border-bottom: 1px solid #d1d5db !important;
    border-top-right-radius: 8px !important;
    border-top-left-radius: 8px !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  }

  .arabic-quill-editor .ql-toolbar .ql-formats {
    display: flex !important;
    align-items: center !important;
    gap: 2px !important;
    margin: 0 !important;
    flex-shrink: 0 !important;
    padding: 3px !important;
    background: rgba(255, 255, 255, 0.9) !important;
    border-radius: 6px !important;
    border: 1px solid rgba(229, 231, 235, 0.8) !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
  }

  .arabic-quill-editor .ql-toolbar::-webkit-scrollbar {
    height: 4px !important;
  }

  .arabic-quill-editor .ql-toolbar::-webkit-scrollbar-track {
    background: #f1f5f9 !important;
    border-radius: 2px !important;
  }

  .arabic-quill-editor .ql-toolbar::-webkit-scrollbar-thumb {
    background: #cbd5e1 !important;
    border-radius: 2px !important;
  }

  .arabic-quill-editor .ql-toolbar::-webkit-scrollbar-thumb:hover {
    background: #94a3b8 !important;
  }

  /* إصلاح عنصر اختيار حجم العنوان للعربية - تصميم مصغر */
  .arabic-quill-editor .ql-toolbar .ql-header {
    direction: rtl !important;
    min-width: 110px !important;
    height: 30px !important;
  }

  .arabic-quill-editor .ql-toolbar .ql-header .ql-picker {
    direction: rtl !important;
    width: 100% !important;
    height: 100% !important;
  }

  .arabic-quill-editor .ql-toolbar .ql-header .ql-picker-label {
    direction: rtl !important;
    text-align: right !important;
    padding: 6px 22px 6px 10px !important;
    position: relative !important;
    background: #ffffff !important;
    border: 1px solid #d1d5db !important;
    border-radius: 6px !important;
    height: 30px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    transition: all 0.2s ease !important;
    color: #374151 !important;
    font-size: 12px !important;
    font-weight: 400 !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
  }

  .arabic-quill-editor .ql-toolbar .ql-header .ql-picker-label::before {
    position: absolute !important;
    left: 8px !important;
    right: auto !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    content: "▼" !important;
    font-size: 9px !important;
    color: #6b7280 !important;
    transition: all 0.2s ease !important;
  }

  .arabic-quill-editor .ql-toolbar .ql-header .ql-picker-label:hover {
    background: #f9fafb !important;
    border-color: #9ca3af !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }

  .arabic-quill-editor .ql-toolbar .ql-header .ql-picker-label:hover::before {
    color: #374151 !important;
  }

  .arabic-quill-editor .ql-toolbar .ql-header .ql-picker-options {
    direction: rtl !important;
    text-align: right !important;
    right: 0 !important;
    left: auto !important;
    min-width: 110px !important;
    background: #ffffff !important;
    border: 1px solid #d1d5db !important;
    border-radius: 8px !important;
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05) !important;
    z-index: 9999 !important;
    overflow: hidden !important;
    margin-top: 2px !important;
    position: absolute !important;
  }

  .arabic-quill-editor .ql-toolbar .ql-header .ql-picker-item {
    direction: rtl !important;
    text-align: right !important;
    padding: 8px 12px !important;
    border-bottom: 1px solid #f3f4f6 !important;
    color: #374151 !important;
    font-size: 12px !important;
    font-weight: 400 !important;
    transition: all 0.2s ease !important;
  }

  .arabic-quill-editor .ql-toolbar .ql-header .ql-picker-item:last-child {
    border-bottom: none !important;
  }

  .arabic-quill-editor .ql-toolbar .ql-header .ql-picker-item:hover {
    background: #f9fafb !important;
    color: #1f2937 !important;
  }

  .arabic-quill-editor .ql-toolbar .ql-header .ql-picker-item.ql-selected {
    background: #eff6ff !important;
    color: #3b82f6 !important;
    font-weight: 500 !important;
  }

  /* إصلاح جميع عناصر الاختيار في شريط الأدوات - تصميم مصغر */
  .arabic-quill-editor .ql-toolbar .ql-picker {
    direction: rtl !important;
    position: relative !important;
    height: 28px !important;
    z-index: 10000 !important;
  }

  .arabic-quill-editor .ql-toolbar .ql-picker-label {
    direction: rtl !important;
    text-align: right !important;
    padding: 5px 18px 5px 8px !important;
    background: #ffffff !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 5px !important;
    min-width: 75px !important;
    height: 28px !important;
    position: relative !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    transition: all 0.2s ease-in-out !important;
    color: #374151 !important;
    font-size: 14px !important;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
  }

  .arabic-quill-editor .ql-toolbar .ql-picker-label::before {
    position: absolute !important;
    left: 8px !important;
    right: auto !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    content: "▼" !important;
    font-size: 12px !important;
    color: #6b7280 !important;
    transition: color 0.2s ease-in-out !important;
  }

  .arabic-quill-editor .ql-toolbar .ql-picker-label:hover {
    background: #f9fafb !important;
    border-color: #9ca3af !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
  }

  .arabic-quill-editor .ql-toolbar .ql-picker-label:hover::before {
    color: #374151 !important;
  }

  .arabic-quill-editor .ql-toolbar .ql-picker-options {
    direction: rtl !important;
    text-align: right !important;
    right: 0 !important;
    left: auto !important;
    background: white !important;
    border: 1px solid #d1d5db !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    z-index: 1000 !important;
    overflow: hidden !important;
  }

  .arabic-quill-editor .ql-toolbar .ql-picker-item {
    direction: rtl !important;
    text-align: right !important;
    padding: 8px 16px !important;
    color: #374151 !important;
    font-size: 14px !important;
    transition: all 0.2s ease-in-out !important;
    border-bottom: 1px solid #f3f4f6 !important;
  }

  .arabic-quill-editor .ql-toolbar .ql-picker-item:last-child {
    border-bottom: none !important;
  }

  .arabic-quill-editor .ql-toolbar .ql-picker-item:hover {
    background: #f9fafb !important;
    color: #1f2937 !important;
  }

  .arabic-quill-editor .ql-toolbar .ql-picker-item.ql-selected {
    background: #eff6ff !important;
    color: #3b82f6 !important;
    font-weight: 500 !important;
  }

  /* إصلاح الأزرار في شريط الأدوات - تصميم مثالي بدون حركات */
  .arabic-quill-editor .ql-toolbar button {
    width: 28px !important;
    height: 28px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 5px !important;
    background: #ffffff !important;
    margin: 0 !important;
    transition: background-color 0.15s ease, border-color 0.15s ease !important;
    font-size: 12px !important;
    color: #4b5563 !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
  }

  .arabic-quill-editor .ql-toolbar button:hover {
    background: #f3f4f6 !important;
    border-color: #d1d5db !important;
    color: #374151 !important;
  }

  .arabic-quill-editor .ql-toolbar button.ql-active {
    background: #3b82f6 !important;
    border-color: #3b82f6 !important;
    color: white !important;
    box-shadow: 0 1px 3px rgba(59, 130, 246, 0.4) !important;
  }

  .arabic-quill-editor .ql-toolbar button:focus {
    outline: none !important;
    ring: 2px !important;
    ring-color: rgba(59, 130, 246, 0.5) !important;
    ring-offset: 2px !important;
  }

  /* إصلاح أيقونات المحاذاة */
  .arabic-quill-editor .ql-toolbar .ql-align .ql-picker-label::before {
    content: "⚏" !important;
  }

  .arabic-quill-editor .ql-toolbar .ql-align .ql-picker-item[data-value=""]::before {
    content: "⬅" !important;
  }

  .arabic-quill-editor .ql-toolbar .ql-align .ql-picker-item[data-value="center"]::before {
    content: "⬌" !important;
  }

  .arabic-quill-editor .ql-toolbar .ql-align .ql-picker-item[data-value="right"]::before {
    content: "➡" !important;
  }

  .arabic-quill-editor .ql-toolbar .ql-align .ql-picker-item[data-value="justify"]::before {
    content: "⬍" !important;
  }

  /* إصلاح جميع القوائم المنسدلة لتظهر فوق المحرر */
  .arabic-quill-editor .ql-toolbar .ql-picker-options {
    z-index: 9999 !important;
    position: absolute !important;
    background: #ffffff !important;
    border: 1px solid #d1d5db !important;
    border-radius: 6px !important;
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05) !important;
    margin-top: 2px !important;
  }

  .arabic-quill-editor .ql-container {
    direction: rtl !important;
    border-bottom-right-radius: 8px !important;
    border-bottom-left-radius: 8px !important;
    border: 1px solid #e5e7eb !important;
    border-top: none !important;
    overflow: visible !important; /* إصلاح مشكلة اختفاء القوائم */
    background: #ffffff !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  }

  .arabic-quill-editor .ql-editor {
    direction: rtl !important;
    text-align: right !important;
    min-height: 180px;
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
    line-height: 1.6;
    font-size: 14px;
    padding: 12px 25px 12px 15px; /* زيادة المساحة اليمنى للقوائم */
    unicode-bidi: embed;
    overflow: visible !important; /* إصلاح مشكلة اختفاء القوائم */
  }

  .arabic-quill-editor .ql-editor.ql-blank::before {
    direction: rtl !important;
    text-align: right !important;
    font-style: italic;
    color: #9ca3af;
    left: auto !important;
    right: 25px !important; /* تعديل موضع النص التوضيحي */
  }

  /* إصلاح القوائم للعربية - حل مشكلة الاختفاء */
  .arabic-quill-editor .ql-editor ol,
  .arabic-quill-editor .ql-editor ul {
    direction: rtl !important;
    text-align: right !important;
    padding-right: 0 !important;
    padding-left: 0 !important;
    margin: 0.5em 0;
    margin-right: 2em !important; /* إضافة مساحة للنقاط والأرقام */
    position: relative;
    overflow: visible !important;
  }

  .arabic-quill-editor .ql-editor ol li,
  .arabic-quill-editor .ql-editor ul li {
    direction: rtl !important;
    text-align: right !important;
    padding-right: 0 !important;
    padding-left: 0 !important;
    margin: 0.25em 0;
    position: relative;
    list-style: none !important; /* إزالة النقاط الافتراضية */
    overflow: visible !important;
  }

  /* القوائم النقطية - تحسين الموضع */
  .arabic-quill-editor .ql-editor ul li::before {
    content: "•";
    position: absolute;
    right: -1.8em;
    top: 0;
    color: #374151;
    font-weight: bold;
    font-size: 1.2em;
    line-height: 1.4;
    z-index: 1;
  }

  /* القوائم الرقمية - تحسين الموضع */
  .arabic-quill-editor .ql-editor ol {
    counter-reset: arabic-counter;
  }

  .arabic-quill-editor .ql-editor ol li {
    counter-increment: arabic-counter;
  }

  .arabic-quill-editor .ql-editor ol li::before {
    content: counter(arabic-counter) ".";
    position: absolute;
    right: -2.2em;
    top: 0;
    color: #374151;
    font-weight: bold;
    line-height: 1.4;
    z-index: 1;
    min-width: 1.5em;
    text-align: left;
  }

  /* القوائم المتداخلة */
  .arabic-quill-editor .ql-editor ul ul,
  .arabic-quill-editor .ql-editor ol ol,
  .arabic-quill-editor .ql-editor ul ol,
  .arabic-quill-editor .ql-editor ol ul {
    margin-right: 2em !important;
    margin-left: 0 !important;
    margin-top: 0.25em;
    margin-bottom: 0.25em;
  }

  .arabic-quill-editor .ql-editor ul ul li::before {
    content: "◦";
    font-size: 1em;
  }

  .arabic-quill-editor .ql-editor ul ul ul li::before {
    content: "▪";
    font-size: 0.8em;
  }

  /* تحسين المسافات للقوائم المتداخلة */
  .arabic-quill-editor .ql-editor li li {
    margin-right: 0 !important;
  }

  /* ضمان عدم تداخل النقاط مع النص */
  .arabic-quill-editor .ql-editor li {
    min-height: 1.4em;
    word-wrap: break-word;
  }

  /* العناوين */
  .arabic-quill-editor .ql-editor h1,
  .arabic-quill-editor .ql-editor h2,
  .arabic-quill-editor .ql-editor h3,
  .arabic-quill-editor .ql-editor h4,
  .arabic-quill-editor .ql-editor h5,
  .arabic-quill-editor .ql-editor h6 {
    direction: rtl !important;
    text-align: right !important;
    font-weight: bold;
    margin: 0.5em 0;
  }

  /* الفقرات */
  .arabic-quill-editor .ql-editor p {
    direction: rtl !important;
    text-align: right !important;
    margin: 0.5em 0;
  }

  /* الروابط */
  .arabic-quill-editor .ql-editor a {
    direction: rtl !important;
    color: #3b82f6;
    text-decoration: underline;
  }

  /* الوضع المظلم */
  .dark .arabic-quill-editor .ql-toolbar {
    background: #374151 !important;
    border-color: #4b5563 !important;
  }

  .dark .arabic-quill-editor .ql-container {
    border-color: #4b5563 !important;
  }

  .dark .arabic-quill-editor .ql-editor {
    background: #1f2937 !important;
    color: #f9fafb !important;
  }

  .dark .arabic-quill-editor .ql-editor.ql-blank::before {
    color: #6b7280 !important;
  }

  .dark .arabic-quill-editor .ql-editor ul li::before,
  .dark .arabic-quill-editor .ql-editor ol li::before {
    color: #d1d5db !important;
  }

  /* إصلاح عناصر الاختيار في الوضع المظلم */
  .dark .arabic-quill-editor .ql-toolbar .ql-picker-label {
    background: #374151 !important;
    border-color: #4b5563 !important;
    color: #f9fafb !important;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3) !important;
  }

  .dark .arabic-quill-editor .ql-toolbar .ql-picker-label::before {
    color: #9ca3af !important;
  }

  .dark .arabic-quill-editor .ql-toolbar .ql-picker-label:hover {
    background: #4b5563 !important;
    border-color: #6b7280 !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.4) !important;
  }

  .dark .arabic-quill-editor .ql-toolbar .ql-picker-label:hover::before {
    color: #d1d5db !important;
  }

  .dark .arabic-quill-editor .ql-toolbar .ql-picker-options {
    background: #374151 !important;
    border-color: #4b5563 !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
  }

  .dark .arabic-quill-editor .ql-toolbar .ql-picker-item {
    color: #f9fafb !important;
    border-bottom-color: #4b5563 !important;
  }

  .dark .arabic-quill-editor .ql-toolbar .ql-picker-item:hover {
    background: #4b5563 !important;
    color: #ffffff !important;
  }

  .dark .arabic-quill-editor .ql-toolbar .ql-picker-item.ql-selected {
    background: #1e40af !important;
    color: #dbeafe !important;
  }

  .dark .arabic-quill-editor .ql-toolbar button {
    background: #374151 !important;
    border-color: #4b5563 !important;
    color: #f9fafb !important;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3) !important;
  }

  .dark .arabic-quill-editor .ql-toolbar button:hover {
    background: #4b5563 !important;
    border-color: #6b7280 !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.4) !important;
    transform: translateY(-1px) !important;
  }

  .dark .arabic-quill-editor .ql-toolbar button.ql-active {
    background: #3b82f6 !important;
    border-color: #3b82f6 !important;
    color: white !important;
    box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.4) !important;
  }

  .dark .arabic-quill-editor .ql-toolbar button:focus {
    ring-color: rgba(59, 130, 246, 0.6) !important;
  }

  /* إخفاء أزرار غير مرغوب فيها */
  .arabic-quill-editor .ql-direction,
  .arabic-quill-editor .ql-align,
  .arabic-quill-editor .ql-image,
  .arabic-quill-editor .ql-video {
    display: none !important;
  }

  /* تحسين التركيز - إزالة الحدود الزرقاء */
  .arabic-quill-editor .ql-editor:focus {
    outline: none !important;
    box-shadow: none !important;
  }

  /* تحسينات للأجهزة المحمولة */
  @media (max-width: 768px) {
    .arabic-quill-editor .ql-toolbar {
      padding: 6px !important;
      flex-wrap: nowrap !important;
      overflow-x: auto !important;
      gap: 2px !important;
    }

    .arabic-quill-editor .ql-toolbar .ql-formats {
      margin-left: 4px !important;
      flex-shrink: 0 !important;
    }

    .arabic-quill-editor .ql-toolbar button {
      width: 28px !important;
      height: 28px !important;
      margin: 0 1px !important;
      flex-shrink: 0 !important;
    }

    .arabic-quill-editor .ql-toolbar .ql-picker-label {
      min-width: 70px !important;
      padding: 4px 16px 4px 8px !important;
      font-size: 12px !important;
      flex-shrink: 0 !important;
    }

    .arabic-quill-editor .ql-editor {
      padding: 10px 20px 10px 12px;
      font-size: 16px; /* منع التكبير التلقائي في iOS */
    }

    .arabic-quill-editor .ql-editor ol,
    .arabic-quill-editor .ql-editor ul {
      margin-right: 1.5em !important;
    }

    .arabic-quill-editor .ql-editor ul li::before {
      right: -1.5em;
    }

    .arabic-quill-editor .ql-editor ol li::before {
      right: -1.8em;
    }
  }

  /* تحسين الأداء */
  .arabic-quill-editor .ql-editor {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* إصلاح مشكلة التمرير في Safari */
  .arabic-quill-editor .ql-container {
    -webkit-overflow-scrolling: touch;
  }
`;

// التلميحات العربية
const ARABIC_TOOLTIPS: { [key: string]: string } = {
  'ql-bold': 'عريض (Ctrl+B)',
  'ql-italic': 'مائل (Ctrl+I)',
  'ql-underline': 'تسطير (Ctrl+U)',
  'ql-strike': 'يتوسطه خط',
  'ql-list[value="ordered"]': 'قائمة رقمية',
  'ql-list[value="bullet"]': 'قائمة نقطية',
  'ql-script[value="sub"]': 'نص منخفض',
  'ql-script[value="super"]': 'نص مرتفع',
  'ql-indent[value="-1"]': 'تقليل المسافة البادئة',
  'ql-indent[value="+1"]': 'زيادة المسافة البادئة',
  'ql-align': 'محاذاة النص',
  'ql-link': 'إدراج رابط',
  'ql-image': 'إدراج صورة',
  'ql-video': 'إدراج فيديو',
  'ql-clean': 'إزالة التنسيق',
  'ql-header[value="1"]': 'عنوان كبير',
  'ql-header[value="2"]': 'عنوان متوسط',
  'ql-header[value="3"]': 'عنوان صغير',
};

interface ArabicRichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  height?: number;
}

const ArabicRichTextEditor: React.FC<ArabicRichTextEditorProps> = ({
  value,
  onChange,
  placeholder = "اكتب النص هنا...",
  height = 180
}) => {
  const editorRef = useRef<ReactQuill>(null);
  const [isReady, setIsReady] = useState(false);

  // تطبيق التحسينات العربية
  const applyArabicEnhancements = () => {
    if (editorRef.current && isReady) {
      try {
        const editor = editorRef.current.getEditor();
        const toolbar = editor.getModule('toolbar').container;

        // تطبيق التلميحات العربية
        Object.keys(ARABIC_TOOLTIPS).forEach(key => {
          const button = toolbar.querySelector(`.${key}`);
          if (button) {
            button.setAttribute('title', ARABIC_TOOLTIPS[key]);
            button.setAttribute('aria-label', ARABIC_TOOLTIPS[key]);
          }
        });

        // إصلاح عناصر الاختيار في شريط الأدوات
        const pickers = toolbar.querySelectorAll('.ql-picker');
        pickers.forEach((picker: Element) => {
          (picker as HTMLElement).style.direction = 'rtl';
          
          const label = picker.querySelector('.ql-picker-label');
          if (label) {
            (label as HTMLElement).style.direction = 'rtl';
            (label as HTMLElement).style.textAlign = 'right';
            (label as HTMLElement).style.padding = '6px 20px 6px 8px';
            (label as HTMLElement).style.minWidth = '80px';
            (label as HTMLElement).style.position = 'relative';
            (label as HTMLElement).style.display = 'flex';
            (label as HTMLElement).style.alignItems = 'center';
            (label as HTMLElement).style.justifyContent = 'space-between';
          }

          const options = picker.querySelector('.ql-picker-options');
          if (options) {
            (options as HTMLElement).style.direction = 'rtl';
            (options as HTMLElement).style.textAlign = 'right';
            (options as HTMLElement).style.right = '0';
            (options as HTMLElement).style.left = 'auto';
            (options as HTMLElement).style.zIndex = '1000';
          }

          const items = picker.querySelectorAll('.ql-picker-item');
          items.forEach((item: Element) => {
            (item as HTMLElement).style.direction = 'rtl';
            (item as HTMLElement).style.textAlign = 'right';
            (item as HTMLElement).style.padding = '6px 12px';
          });
        });

        // إصلاح خاص لعنصر اختيار العناوين
        const headerPicker = toolbar.querySelector('.ql-header');
        if (headerPicker) {
          (headerPicker as HTMLElement).style.minWidth = '120px';
          
          const headerLabel = headerPicker.querySelector('.ql-picker-label');
          if (headerLabel) {
            (headerLabel as HTMLElement).style.minWidth = '120px';
            (headerLabel as HTMLElement).style.padding = '6px 24px 6px 8px';
          }

          const headerOptions = headerPicker.querySelector('.ql-picker-options');
          if (headerOptions) {
            (headerOptions as HTMLElement).style.minWidth = '120px';
          }
        }

        // إصلاح الأزرار
        const buttons = toolbar.querySelectorAll('button');
        buttons.forEach((button: Element) => {
          (button as HTMLElement).style.width = '32px';
          (button as HTMLElement).style.height = '32px';
          (button as HTMLElement).style.display = 'flex';
          (button as HTMLElement).style.alignItems = 'center';
          (button as HTMLElement).style.justifyContent = 'center';
          (button as HTMLElement).style.margin = '0 2px';
        });

        // فرض اتجاه RTL
        const editorElement = editor.root;
        editorElement.setAttribute('dir', 'rtl');
        editorElement.style.direction = 'rtl';
        editorElement.style.textAlign = 'right';

        // معالج الأحداث لضمان RTL
        editor.on('text-change', () => {
          setTimeout(() => {
            const root = editor.root;
            root.setAttribute('dir', 'rtl');
            
            // إصلاح القوائم
            const lists = root.querySelectorAll('ol, ul');
            lists.forEach((list: Element) => {
              (list as HTMLElement).style.direction = 'rtl';
              (list as HTMLElement).style.textAlign = 'right';
              (list as HTMLElement).style.marginRight = '2em';
              (list as HTMLElement).style.paddingLeft = '0';
              (list as HTMLElement).style.paddingRight = '0';
            });

            // إصلاح عناصر القوائم
            const listItems = root.querySelectorAll('ol li, ul li');
            listItems.forEach((li: Element) => {
              (li as HTMLElement).style.direction = 'rtl';
              (li as HTMLElement).style.textAlign = 'right';
              (li as HTMLElement).style.listStyle = 'none';
              (li as HTMLElement).style.position = 'relative';
            });

            // إصلاح الفقرات
            const paragraphs = root.querySelectorAll('p');
            paragraphs.forEach((p: Element) => {
              (p as HTMLElement).style.direction = 'rtl';
              (p as HTMLElement).style.textAlign = 'right';
            });
          }, 10);
        });

      } catch (error) {
        console.error('خطأ في تطبيق التحسينات العربية:', error);
      }
    }
  };

  useEffect(() => {
    if (editorRef.current) {
      setIsReady(true);
      const timer = setTimeout(applyArabicEnhancements, 100);
      return () => clearTimeout(timer);
    }
  }, [isReady]);

  useEffect(() => {
    if (isReady) {
      const timer = setTimeout(applyArabicEnhancements, 50);
      return () => clearTimeout(timer);
    }
  }, [value, isReady]);

  // إعدادات المحرر
  const modules = {
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'script': 'sub'}, { 'script': 'super' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      ['link'],
      ['clean']
    ],
    clipboard: {
      matchVisual: false
    }
  };

  const formats = [
    'header', 'bold', 'italic', 'underline', 'strike',
    'list', 'bullet', 'indent', 'link', 'image',
    'align', 'script'
  ];

  return (
    <div className="arabic-quill-editor" dir="rtl">
      <style>{arabicEditorStyles}</style>
      <ReactQuill
        ref={editorRef}
        theme="snow"
        value={value}
        onChange={onChange}
        modules={modules}
        formats={formats}
        placeholder={placeholder}
        style={{ 
          direction: 'rtl',
          minHeight: `${height}px`
        }}
      />
    </div>
  );
};

export default ArabicRichTextEditor;
