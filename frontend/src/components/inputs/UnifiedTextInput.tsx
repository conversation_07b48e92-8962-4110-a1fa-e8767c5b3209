/**
 * مكون إدخال نص موحد يستخدم الأنماط الجديدة
 * مثال على كيفية استخدام النظام الموحد للأنماط
 */

import React, { useState, forwardRef } from 'react';
import { FaExclamationTriangle, FaCheckCircle } from 'react-icons/fa';
import { useInputStyles } from '../../hooks/useInputStyles';

interface UnifiedTextInputProps {
  label?: string;
  name: string;
  id?: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  success?: string;
  icon?: React.ReactNode;
  type?: 'text' | 'email' | 'password' | 'url' | 'tel';
  dir?: 'rtl' | 'ltr';
  className?: string;
  maxLength?: number;
  minLength?: number;
  pattern?: string;
  autoComplete?: string;
  autoFocus?: boolean;
}

const UnifiedTextInput = forwardRef<HTMLInputElement, UnifiedTextInputProps>(({
  label,
  name,
  id,
  value,
  onChange,
  placeholder = '',
  required = false,
  disabled = false,
  error,
  success,
  icon,
  type = 'text',
  dir = 'rtl',
  className = '',
  maxLength,
  minLength,
  pattern,
  autoComplete,
  autoFocus = false
}, ref) => {
  const [isFocused, setIsFocused] = useState(false);
  const [hasInteracted, setHasInteracted] = useState(false);

  // استخدام Hook الأنماط الموحدة
  const {
    inputStyles,
    iconStyles,
    labelStyles,
    messageStyles,
    states
  } = useInputStyles({
    variant: 'text',
    hasIcon: !!icon,
    isFocused,
    hasError: !!(error && hasInteracted),
    hasSuccess: !!(success && hasInteracted && !error),
    isDisabled: disabled
  });

  // Generate unique ID if not provided
  const inputId = id || `${name}-${Math.random().toString(36).substring(2, 11)}`;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
    if (!hasInteracted) {
      setHasInteracted(true);
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
    setHasInteracted(true);
  };

  const showError = error && hasInteracted;
  const showSuccess = success && hasInteracted && !error;

  return (
    <div className={className}>
      {/* Label */}
      {label && (
        <label htmlFor={inputId} className={labelStyles}>
          {label}
          {required && <span className="text-red-500 mr-1">*</span>}
        </label>
      )}

      {/* Input Container */}
      <div className="relative">
        {/* Icon */}
        {icon && (
          <div className={iconStyles}>
            {icon}
          </div>
        )}

        {/* Input Field */}
        <input
          ref={ref}
          id={inputId}
          type={type}
          name={name}
          value={value || ''}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          dir={dir}
          maxLength={maxLength}
          minLength={minLength}
          pattern={pattern}
          autoComplete={autoComplete}
          autoFocus={autoFocus}
          className={inputStyles}
        />

        {/* Status Icon */}
        {(showError || showSuccess) && (
          <div className="absolute inset-y-0 left-0 flex items-center pl-3">
            {showError ? (
              <FaExclamationTriangle className="h-5 w-5 text-red-500" />
            ) : (
              <FaCheckCircle className="h-5 w-5 text-green-500" />
            )}
          </div>
        )}
      </div>

      {/* Error Message */}
      {showError && (
        <div className={messageStyles}>
          <FaExclamationTriangle className="ml-2 flex-shrink-0 text-red-500" />
          {error}
        </div>
      )}

      {/* Success Message */}
      {showSuccess && (
        <div className="mt-2 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <p className="text-green-700 dark:text-green-300 text-sm flex items-center">
            <FaCheckCircle className="ml-2 flex-shrink-0 text-green-500" />
            {success}
          </p>
        </div>
      )}
    </div>
  );
});

UnifiedTextInput.displayName = 'UnifiedTextInput';

export default UnifiedTextInput;
