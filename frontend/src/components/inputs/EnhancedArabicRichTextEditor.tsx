import React, { useEffect, useRef, useState, useCallback } from 'react';
import ReactQuill, { Quill } from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import '../../styles/arabic-rich-text-editor-fixes.css';

// تخصيص Quill للعربية
const DirectionAttribute = Quill.import('attributors/attribute/direction');
const AlignStyle = Quill.import('attributors/style/align');
const BackgroundStyle = Quill.import('attributors/style/background');
const ColorStyle = Quill.import('attributors/style/color');
const FontStyle = Quill.import('attributors/style/font');
const SizeStyle = Quill.import('attributors/style/size');

Quill.register(DirectionAttribute, true);
Quill.register(AlignStyle, true);
Quill.register(BackgroundStyle, true);
Quill.register(ColorStyle, true);
Quill.register(FontStyle, true);
Quill.register(SizeStyle, true);

interface EnhancedArabicRichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  minHeight?: string;
  showToolbar?: boolean;
}

// أنماط CSS محسنة للعربية مع شريط أدوات مدمج
const enhancedArabicEditorStyles = `
  .enhanced-arabic-quill-editor {
    direction: rtl;
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
  }

  /* شريط الأدوات المدمج */
  .enhanced-arabic-quill-editor .integrated-toolbar {
    direction: rtl !important;
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
    padding: 8px 12px;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: flex-start;
    gap: 6px;
    overflow-x: auto;
    overflow-y: hidden;
    min-height: 44px;
    position: relative;
    z-index: 10;
  }

  .enhanced-arabic-quill-editor .integrated-toolbar .toolbar-group {
    display: flex;
    align-items: center;
    gap: 2px;
    margin: 0;
    flex-shrink: 0;
    padding: 3px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 6px;
    border: 1px solid rgba(229, 231, 235, 0.8);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  .enhanced-arabic-quill-editor .integrated-toolbar .toolbar-button {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e5e7eb;
    border-radius: 5px;
    background: #ffffff;
    margin: 0;
    transition: all 0.15s ease;
    font-size: 12px;
    color: #4b5563;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    user-select: none;
  }

  .enhanced-arabic-quill-editor .integrated-toolbar .toolbar-button:hover {
    background: #f3f4f6;
    border-color: #d1d5db;
    color: #374151;
    transform: translateY(-1px);
  }

  .enhanced-arabic-quill-editor .integrated-toolbar .toolbar-button.active {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.4);
  }

  .enhanced-arabic-quill-editor .integrated-toolbar .toolbar-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  .enhanced-arabic-quill-editor .integrated-toolbar .toolbar-button:disabled:hover {
    background: #ffffff;
    border-color: #e5e7eb;
    color: #4b5563;
    transform: none;
  }

  .enhanced-arabic-quill-editor .integrated-toolbar .header-size-display {
    font-size: 11px;
    color: #6b7280;
    margin: 0 4px;
    min-width: 60px;
    text-align: center;
    background: #f9fafb;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #e5e7eb;
  }

  /* إخفاء شريط الأدوات الافتراضي */
  .enhanced-arabic-quill-editor .ql-toolbar {
    display: none !important;
  }

  .enhanced-arabic-quill-editor .ql-container {
    direction: rtl !important;
    border: none !important;
    overflow: visible !important;
    background: #ffffff !important;
  }

  .enhanced-arabic-quill-editor .ql-editor {
    direction: rtl !important;
    text-align: right !important;
    min-height: 180px;
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
    line-height: 1.6;
    font-size: 14px;
    padding: 12px 25px 12px 15px;
    unicode-bidi: embed;
    overflow: visible !important;
  }

  .enhanced-arabic-quill-editor .ql-editor.ql-blank::before {
    direction: rtl !important;
    text-align: right !important;
    font-style: italic;
    color: #9ca3af;
    left: auto !important;
    right: 25px !important;
  }

  /* إصلاح القوائم للعربية */
  .enhanced-arabic-quill-editor .ql-editor ol,
  .enhanced-arabic-quill-editor .ql-editor ul {
    direction: rtl !important;
    text-align: right !important;
    padding-right: 0 !important;
    padding-left: 0 !important;
    margin: 0.5em 0;
    margin-right: 2em !important;
    position: relative;
    overflow: visible !important;
  }

  .enhanced-arabic-quill-editor .ql-editor ol li,
  .enhanced-arabic-quill-editor .ql-editor ul li {
    direction: rtl !important;
    text-align: right !important;
    padding-right: 0 !important;
    padding-left: 0 !important;
    margin: 0.25em 0;
    list-style: none !important;
    position: relative;
    overflow: visible !important;
  }

  /* النقاط والأرقام */
  .enhanced-arabic-quill-editor .ql-editor ul li::before {
    content: "•" !important;
    position: absolute !important;
    right: -1.8em !important;
    top: 0 !important;
    color: #374151 !important;
    font-weight: bold !important;
    font-size: 1.2em !important;
    line-height: 1.4 !important;
    z-index: 1 !important;
  }

  .enhanced-arabic-quill-editor .ql-editor ol {
    counter-reset: arabic-counter !important;
  }

  .enhanced-arabic-quill-editor .ql-editor ol li {
    counter-increment: arabic-counter !important;
  }

  .enhanced-arabic-quill-editor .ql-editor ol li::before {
    content: counter(arabic-counter) "." !important;
    position: absolute !important;
    right: -2.2em !important;
    top: 0 !important;
    color: #374151 !important;
    font-weight: bold !important;
    line-height: 1.4 !important;
    z-index: 1 !important;
    min-width: 1.5em !important;
    text-align: left !important;
  }

  /* العناوين */
  .enhanced-arabic-quill-editor .ql-editor h1,
  .enhanced-arabic-quill-editor .ql-editor h2,
  .enhanced-arabic-quill-editor .ql-editor h3,
  .enhanced-arabic-quill-editor .ql-editor h4,
  .enhanced-arabic-quill-editor .ql-editor h5,
  .enhanced-arabic-quill-editor .ql-editor h6 {
    direction: rtl !important;
    text-align: right !important;
    font-weight: bold;
    margin: 0.5em 0;
  }

  /* الفقرات */
  .enhanced-arabic-quill-editor .ql-editor p {
    direction: rtl !important;
    text-align: right !important;
    margin: 0.5em 0;
  }

  /* الروابط */
  .enhanced-arabic-quill-editor .ql-editor a {
    direction: rtl !important;
    color: #3b82f6;
    text-decoration: underline;
  }

  /* الوضع المظلم */
  .dark .enhanced-arabic-quill-editor {
    border-color: #4b5563;
  }

  .dark .enhanced-arabic-quill-editor .integrated-toolbar {
    background: #374151 !important;
    border-bottom-color: #4b5563 !important;
  }

  .dark .enhanced-arabic-quill-editor .integrated-toolbar .toolbar-group {
    background: rgba(55, 65, 81, 0.9) !important;
    border-color: rgba(75, 85, 99, 0.8) !important;
  }

  .dark .enhanced-arabic-quill-editor .integrated-toolbar .toolbar-button {
    background: #374151 !important;
    border-color: #4b5563 !important;
    color: #f9fafb !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  }

  .dark .enhanced-arabic-quill-editor .integrated-toolbar .toolbar-button:hover {
    background: #4b5563 !important;
    border-color: #6b7280 !important;
    color: #ffffff !important;
  }

  .dark .enhanced-arabic-quill-editor .integrated-toolbar .toolbar-button.active {
    background: #3b82f6 !important;
    border-color: #3b82f6 !important;
    color: white !important;
  }

  .dark .enhanced-arabic-quill-editor .integrated-toolbar .header-size-display {
    background: #4b5563 !important;
    border-color: #6b7280 !important;
    color: #d1d5db !important;
  }

  .dark .enhanced-arabic-quill-editor .ql-container {
    background: #1f2937 !important;
  }

  .dark .enhanced-arabic-quill-editor .ql-editor {
    background: #1f2937 !important;
    color: #f9fafb !important;
  }

  .dark .enhanced-arabic-quill-editor .ql-editor.ql-blank::before {
    color: #6b7280 !important;
  }

  .dark .enhanced-arabic-quill-editor .ql-editor ul li::before,
  .dark .enhanced-arabic-quill-editor .ql-editor ol li::before {
    color: #d1d5db !important;
  }

  /* التصميم المتجاوب */
  @media (max-width: 768px) {
    .enhanced-arabic-quill-editor .integrated-toolbar {
      padding: 6px !important;
      gap: 2px !important;
    }

    .enhanced-arabic-quill-editor .integrated-toolbar .toolbar-button {
      width: 26px !important;
      height: 26px !important;
      font-size: 11px !important;
    }

    .enhanced-arabic-quill-editor .integrated-toolbar .header-size-display {
      min-width: 50px !important;
      font-size: 10px !important;
      padding: 3px 6px !important;
    }

    .enhanced-arabic-quill-editor .ql-editor {
      padding: 10px 20px 10px 12px !important;
      font-size: 16px !important; /* منع التكبير التلقائي في iOS */
    }
  }
`;

const EnhancedArabicRichTextEditor: React.FC<EnhancedArabicRichTextEditorProps> = ({
  value,
  onChange,
  placeholder = 'اكتب النص هنا...',
  className = '',
  disabled = false,
  minHeight = '180px',
  showToolbar = true
}) => {
  const editorRef = useRef<ReactQuill>(null);
  const [isReady, setIsReady] = useState(false);
  const [currentHeaderLevel, setCurrentHeaderLevel] = useState<number>(0);
  const [activeFormats, setActiveFormats] = useState<{[key: string]: boolean}>({});

  // إعدادات المحرر
  const modules = {
    toolbar: false, // إخفاء شريط الأدوات الافتراضي
    clipboard: {
      matchVisual: false
    }
  };

  const formats = [
    'header', 'bold', 'italic', 'underline', 'strike',
    'list', 'bullet', 'indent', 'link', 'clean',
    'align', 'script'
  ];

  // تطبيق تحسينات العربية
  const applyArabicEnhancements = useCallback(() => {
    if (!editorRef.current) return;

    const editor = editorRef.current.getEditor();
    const container = editorRef.current.getEditingArea();
    
    if (container) {
      container.setAttribute('dir', 'rtl');
      const editorElement = container.querySelector('.ql-editor');
      if (editorElement) {
        (editorElement as HTMLElement).style.direction = 'rtl';
        (editorElement as HTMLElement).style.textAlign = 'right';
        (editorElement as HTMLElement).style.minHeight = minHeight;
      }
    }

    updateCurrentHeaderLevel();
  }, [minHeight]);

  // تحديث مستوى العنوان الحالي وحالة الأزرار النشطة
  const updateCurrentHeaderLevel = useCallback(() => {
    if (!editorRef.current) return;

    const editor = editorRef.current.getEditor();
    const range = editor.getSelection();
    
    if (range) {
      const format = editor.getFormat(range);
      const headerLevel = format.header || 0;
      setCurrentHeaderLevel(headerLevel);
      
      setActiveFormats({
        bold: !!format.bold,
        italic: !!format.italic,
        underline: !!format.underline,
        strike: !!format.strike,
        list: !!format.list,
        indent: (format.indent || 0) > 0
      });
    }
  }, []);

  // زيادة حجم العنوان
  const increaseHeaderSize = useCallback(() => {
    if (!editorRef.current || disabled) return;

    const editor = editorRef.current.getEditor();
    const range = editor.getSelection();
    
    if (range) {
      let newLevel = currentHeaderLevel;
      if (newLevel === 0) {
        newLevel = 3;
      } else if (newLevel === 3) {
        newLevel = 2;
      } else if (newLevel === 2) {
        newLevel = 1;
      }
      
      editor.format('header', newLevel === 0 ? false : newLevel);
      setCurrentHeaderLevel(newLevel);
    }
  }, [currentHeaderLevel, disabled]);

  // تقليل حجم العنوان
  const decreaseHeaderSize = useCallback(() => {
    if (!editorRef.current || disabled) return;

    const editor = editorRef.current.getEditor();
    const range = editor.getSelection();
    
    if (range) {
      let newLevel = currentHeaderLevel;
      if (newLevel === 1) {
        newLevel = 2;
      } else if (newLevel === 2) {
        newLevel = 3;
      } else if (newLevel === 3) {
        newLevel = 0;
      }
      
      editor.format('header', newLevel === 0 ? false : newLevel);
      setCurrentHeaderLevel(newLevel);
    }
  }, [currentHeaderLevel, disabled]);

  // تطبيق تنسيق
  const applyFormat = useCallback((format: string, value?: any) => {
    if (!editorRef.current || disabled) return;

    const editor = editorRef.current.getEditor();
    const range = editor.getSelection();
    
    if (!range) {
      const length = editor.getLength();
      editor.setSelection(length - 1, 0);
      return;
    }

    try {
      if (format === 'clean') {
        editor.removeFormat(range.index, range.length);
      } else if (format === 'link') {
        if (value) {
          editor.format('link', value);
        } else {
          editor.format('link', false);
        }
      } else if (format === 'indent') {
        const currentFormat = editor.getFormat(range);
        const currentIndent = currentFormat.indent || 0;
        
        if (value === '+1') {
          editor.format('indent', currentIndent + 1);
        } else if (value === '-1' && currentIndent > 0) {
          editor.format('indent', currentIndent - 1);
        }
      } else {
        const currentFormat = editor.getFormat(range);
        const isActive = currentFormat[format];
        
        if (format === 'bold' || format === 'italic' || format === 'underline' || format === 'strike') {
          editor.format(format, !isActive);
        } else {
          editor.format(format, value);
        }
      }
      
      if (format === 'header') {
        setTimeout(updateCurrentHeaderLevel, 10);
      }
      
      editor.focus();
    } catch (error) {
      console.error('خطأ في تطبيق التنسيق:', error);
    }
  }, [disabled, updateCurrentHeaderLevel]);

  // الحصول على نص مستوى العنوان
  const getHeaderLevelText = (level: number): string => {
    switch (level) {
      case 1: return 'عنوان كبير';
      case 2: return 'عنوان متوسط';
      case 3: return 'عنوان صغير';
      default: return 'نص عادي';
    }
  };

  // معالج تغيير النص
  const handleChange = useCallback((content: string) => {
    onChange(content);
    setTimeout(updateCurrentHeaderLevel, 10);
  }, [onChange, updateCurrentHeaderLevel]);

  useEffect(() => {
    setIsReady(true);
  }, []);

  useEffect(() => {
    if (isReady) {
      const timer = setTimeout(applyArabicEnhancements, 50);
      return () => clearTimeout(timer);
    }
  }, [value, isReady, applyArabicEnhancements]);

  // مراقبة تغييرات التحديد
  useEffect(() => {
    if (!editorRef.current || !isReady) return;

    const editor = editorRef.current.getEditor();
    
    const handleSelectionChange = () => {
      updateCurrentHeaderLevel();
    };

    const handleTextChange = () => {
      setTimeout(updateCurrentHeaderLevel, 10);
    };

    editor.on('selection-change', handleSelectionChange);
    editor.on('text-change', handleTextChange);

    return () => {
      editor.off('selection-change', handleSelectionChange);
      editor.off('text-change', handleTextChange);
    };
  }, [isReady, updateCurrentHeaderLevel]);

  // دعم اختصارات لوحة المفاتيح
  useEffect(() => {
    if (!editorRef.current || !isReady) return;

    const handleKeyDown = (e: Event) => {
      const keyboardEvent = e as KeyboardEvent;
      if (keyboardEvent.ctrlKey || keyboardEvent.metaKey) {
        switch (keyboardEvent.key.toLowerCase()) {
          case 'b':
            keyboardEvent.preventDefault();
            applyFormat('bold');
            break;
          case 'i':
            keyboardEvent.preventDefault();
            applyFormat('italic');
            break;
          case 'u':
            keyboardEvent.preventDefault();
            applyFormat('underline');
            break;
        }
      }
    };

    const editorElement = editorRef.current.getEditingArea();
    if (editorElement) {
      editorElement.addEventListener('keydown', handleKeyDown);
      return () => {
        editorElement.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [isReady, applyFormat]);

  return (
    <div className={`enhanced-arabic-quill-editor ${className}`} dir="rtl">
      <style>{enhancedArabicEditorStyles}</style>
      
      {/* شريط الأدوات المدمج */}
      {showToolbar && (
        <div className="integrated-toolbar">
          {/* مجموعة أزرار العناوين */}
          <div className="toolbar-group">
            <button
              type="button"
              className="toolbar-button"
              onClick={increaseHeaderSize}
              disabled={disabled || currentHeaderLevel === 1}
              title="زيادة حجم العنوان"
            >
              A+
            </button>
            <div className="header-size-display">
              {getHeaderLevelText(currentHeaderLevel)}
            </div>
            <button
              type="button"
              className="toolbar-button"
              onClick={decreaseHeaderSize}
              disabled={disabled || currentHeaderLevel === 0}
              title="تقليل حجم العنوان"
            >
              A-
            </button>
          </div>

          {/* مجموعة تنسيق النص */}
          <div className="toolbar-group">
            <button
              type="button"
              className={`toolbar-button ${activeFormats.bold ? 'active' : ''}`}
              onClick={() => applyFormat('bold')}
              disabled={disabled}
              title="عريض (Ctrl+B)"
            >
              <strong>B</strong>
            </button>
            <button
              type="button"
              className={`toolbar-button ${activeFormats.italic ? 'active' : ''}`}
              onClick={() => applyFormat('italic')}
              disabled={disabled}
              title="مائل (Ctrl+I)"
            >
              <em>I</em>
            </button>
            <button
              type="button"
              className={`toolbar-button ${activeFormats.underline ? 'active' : ''}`}
              onClick={() => applyFormat('underline')}
              disabled={disabled}
              title="تحته خط (Ctrl+U)"
            >
              <u>U</u>
            </button>
            <button
              type="button"
              className={`toolbar-button ${activeFormats.strike ? 'active' : ''}`}
              onClick={() => applyFormat('strike')}
              disabled={disabled}
              title="يتوسطه خط"
            >
              <s>S</s>
            </button>
          </div>

          {/* مجموعة القوائم */}
          <div className="toolbar-group">
            <button
              type="button"
              className="toolbar-button"
              onClick={() => applyFormat('list', 'ordered')}
              disabled={disabled}
              title="قائمة مرقمة"
            >
              1.
            </button>
            <button
              type="button"
              className="toolbar-button"
              onClick={() => applyFormat('list', 'bullet')}
              disabled={disabled}
              title="قائمة نقطية"
            >
              •
            </button>
          </div>

          {/* مجموعة المسافات البادئة */}
          <div className="toolbar-group">
            <button
              type="button"
              className={`toolbar-button ${activeFormats.indent ? 'active' : ''}`}
              onClick={() => applyFormat('indent', '-1')}
              disabled={disabled || !activeFormats.indent}
              title="تقليل المسافة البادئة"
            >
              ⇤
            </button>
            <button
              type="button"
              className="toolbar-button"
              onClick={() => applyFormat('indent', '+1')}
              disabled={disabled}
              title="زيادة المسافة البادئة"
            >
              ⇥
            </button>
          </div>

          {/* مجموعة الروابط والتنظيف */}
          <div className="toolbar-group">
            <button
              type="button"
              className="toolbar-button"
              onClick={() => {
                const url = prompt('أدخل الرابط:');
                if (url) {
                  applyFormat('link', url);
                }
              }}
              disabled={disabled}
              title="إضافة رابط"
            >
              ⛓
            </button>
            <button
              type="button"
              className="toolbar-button"
              onClick={() => applyFormat('clean')}
              disabled={disabled}
              title="إزالة التنسيق"
            >
              ✖
            </button>
          </div>
        </div>
      )}

      {/* محرر النصوص */}
      <ReactQuill
        ref={editorRef}
        theme="snow"
        value={value}
        onChange={handleChange}
        modules={modules}
        formats={formats}
        placeholder={placeholder}
        readOnly={disabled}
      />
    </div>
  );
};

export default EnhancedArabicRichTextEditor;