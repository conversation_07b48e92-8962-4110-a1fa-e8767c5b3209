# محرر النصوص العربي مع أزرار العناوين

## نظرة عامة

هذا المكون هو نسخة محسنة من محرر النصوص العربي الذي يستبدل القائمة المنسدلة لاختيار حجم العناوين بأزرار بسيطة لزيادة وتقليل الحجم.

## المميزات الجديدة

### 1. أزرار العناوين البديلة
- **زر A+**: زيادة حجم العنوان
- **زر A-**: تقليل حجم العنوان
- **مؤشر الحالة**: يعرض مستوى العنوان الحالي

### 2. مستويات العناوين
- **نص عادي**: النص الافتراضي
- **عنوان صغير**: H3
- **عنوان متوسط**: H2
- **عنوان كبير**: H1

### 3. سلوك الأزرار
- الأزرار تتفاعل مع موضع المؤشر الحالي
- تعطيل الأزرار عند الوصول للحد الأقصى/الأدنى
- تحديث فوري لمؤشر الحالة

## كيفية الاستخدام

```tsx
import ArabicRichTextEditorWithButtons from '../components/inputs/ArabicRichTextEditorWithButtons';

const MyComponent = () => {
  const [content, setContent] = useState('');

  return (
    <ArabicRichTextEditorWithButtons
      value={content}
      onChange={setContent}
      placeholder="اكتب النص هنا..."
      disabled={false}
      minHeight="200px"
    />
  );
};
```

## الخصائص (Props)

| الخاصية | النوع | الافتراضي | الوصف |
|---------|------|----------|-------|
| `value` | `string` | - | محتوى المحرر |
| `onChange` | `(value: string) => void` | - | دالة التعامل مع تغيير المحتوى |
| `placeholder` | `string` | `'اكتب وصف المنتج هنا...'` | النص التوضيحي |
| `className` | `string` | `''` | فئات CSS إضافية |
| `disabled` | `boolean` | `false` | تعطيل المحرر |
| `minHeight` | `string` | `'180px'` | الحد الأدنى لارتفاع المحرر |

## التصميم والأنماط

### الوضع العادي
- خلفية فاتحة مع حدود رمادية
- أزرار بيضاء مع تأثيرات hover
- نص رمادي داكن

### الوضع المظلم
- خلفية داكنة مع حدود رمادية داكنة
- أزرار داكنة مع نص فاتح
- دعم كامل للوضع المظلم

### التصميم المتجاوب
- تقليل حجم الأزرار في الشاشات الصغيرة
- تحسين المساحات والخطوط للأجهزة المحمولة
- منع التكبير التلقائي في iOS

## الفروق عن المحرر الأصلي

| الميزة | المحرر الأصلي | المحرر الجديد |
|-------|--------------|---------------|
| اختيار العناوين | قائمة منسدلة | أزرار زيادة/تقليل |
| سهولة الاستخدام | متوسطة | عالية |
| مشاكل القائمة | تظهر تحت المحرر | لا توجد قوائم |
| التناسق | حجم مختلف | حجم موحد |
| السرعة | بطيئة | سريعة |

## مثال كامل

```tsx
import React, { useState } from 'react';
import ArabicRichTextEditorWithButtons from '../components/inputs/ArabicRichTextEditorWithButtons';

const ProductForm = () => {
  const [description, setDescription] = useState('');

  const handleSave = () => {
    console.log('Product description:', description);
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <h2 className="text-2xl font-bold mb-4">إضافة منتج جديد</h2>
      
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          وصف المنتج
        </label>
        <ArabicRichTextEditorWithButtons
          value={description}
          onChange={setDescription}
          placeholder="اكتب وصف المنتج هنا..."
          minHeight="250px"
        />
      </div>
      
      <button
        onClick={handleSave}
        className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
      >
        حفظ المنتج
      </button>
    </div>
  );
};
```

## اختبار المكون

يمكنك اختبار المكون باستخدام الصفحة المخصصة:

```bash
# انتقل إلى الصفحة التالية في المتصفح
/test-arabic-rich-text-editor-with-buttons
```

## الملفات المرتبطة

- `ArabicRichTextEditorWithButtons.tsx` - المكون الرئيسي
- `TestArabicRichTextEditorWithButtons.tsx` - صفحة الاختبار
- `arabic-rich-text-editor-fixes.css` - الأنماط المشتركة

## التحديثات المستقبلية

- [ ] إضافة اختصارات لوحة المفاتيح
- [ ] دعم المزيد من أحجام الخطوط
- [ ] إضافة أزرار للألوان
- [ ] تحسين الأداء للنصوص الطويلة