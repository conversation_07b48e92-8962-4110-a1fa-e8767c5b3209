import React, { useEffect, useRef } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

// Scoped and refined CSS for the editor - محسن للغة العربية مع تصميم متوافق مع النظام
const editorStyles = `
  .smartpos-quill-editor {
    direction: rtl;
    font-family: '<PERSON>rai', 'Segoe UI', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
  }

  .smartpos-quill-editor .ql-toolbar {
    direction: rtl !important;
    border-top-right-radius: 0.75rem;
    border-top-left-radius: 0.75rem;
    border: 2px solid #e5e7eb;
    border-bottom: none;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: 12px 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  /* تحسين أزرار شريط الأدوات - تصميم مثل حاويات التطبيق */
  .smartpos-quill-editor .ql-toolbar {
    display: flex !important;
    flex-wrap: nowrap !important;
    align-items: center !important;
    justify-content: flex-start !important;
    gap: 6px !important;
    padding: 8px 12px !important;
    overflow-x: auto !important;
    overflow-y: hidden !important;
    min-height: 44px !important;
    background: #f9fafb !important;
    border: 1px solid #e5e7eb !important;
    border-bottom: 1px solid #d1d5db !important;
    border-top-right-radius: 8px !important;
    border-top-left-radius: 8px !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  }

  .smartpos-quill-editor .ql-toolbar .ql-formats {
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    gap: 2px !important;
    flex-shrink: 0 !important;
    padding: 3px !important;
    background: rgba(255, 255, 255, 0.9) !important;
    border-radius: 6px !important;
    border: 1px solid rgba(229, 231, 235, 0.8) !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
  }

  /* تصميم الأزرار بما يتماشى مع النظام - تصميم مثالي بدون حركات */
  .smartpos-quill-editor .ql-toolbar button {
    width: 28px !important;
    height: 28px !important;
    border-radius: 5px !important;
    border: 1px solid #e5e7eb !important;
    background: #ffffff !important;
    color: #4b5563 !important;
    transition: background-color 0.15s ease, border-color 0.15s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
    font-size: 12px !important;
  }

  .smartpos-quill-editor .ql-toolbar button:hover {
    background: #f3f4f6 !important;
    border-color: #d1d5db !important;
    color: #374151 !important;
  }

  .smartpos-quill-editor .ql-toolbar button.ql-active {
    background: #3b82f6 !important;
    border-color: #3b82f6 !important;
    color: white !important;
    box-shadow: 0 1px 3px rgba(59, 130, 246, 0.4) !important;
  }

  /* تحسين القوائم المنسدلة - تصميم مصغر هادئ */
  .smartpos-quill-editor .ql-toolbar .ql-picker {
    color: #4b5563 !important;
    height: 28px !important;
    z-index: 10000 !important;
    position: relative !important;
  }

  .smartpos-quill-editor .ql-toolbar .ql-picker-label {
    border: 1px solid #e5e7eb !important;
    border-radius: 5px !important;
    background: #ffffff !important;
    padding: 5px 18px 5px 8px !important;
    height: 28px !important;
    min-width: 75px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    transition: background-color 0.15s ease, border-color 0.15s ease !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
    direction: rtl !important;
    text-align: right !important;
    font-size: 11px !important;
    font-weight: 400 !important;
    position: relative !important;
  }

  .smartpos-quill-editor .ql-toolbar .ql-picker-label::after {
    content: "▼" !important;
    position: absolute !important;
    left: 6px !important;
    font-size: 8px !important;
    color: #6b7280 !important;
    transition: color 0.15s ease !important;
  }

  .smartpos-quill-editor .ql-toolbar .ql-picker-label:hover {
    background: #f3f4f6 !important;
    border-color: #d1d5db !important;
  }

  .smartpos-quill-editor .ql-toolbar .ql-picker-label:hover::after {
    color: #374151 !important;
  }

  /* إصلاح جميع القوائم المنسدلة لتظهر فوق المحرر */
  .smartpos-quill-editor .ql-toolbar .ql-picker-options {
    z-index: 9999 !important;
    position: absolute !important;
    background: #ffffff !important;
    border: 1px solid #d1d5db !important;
    border-radius: 6px !important;
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05) !important;
    margin-top: 2px !important;
  }

  /* تخصيص قائمة العناوين بالعربية */
  .smartpos-quill-editor .ql-header .ql-picker-options {
    direction: rtl !important;
    text-align: right !important;
  }

  .smartpos-quill-editor .ql-header .ql-picker-item[data-value="1"]::before {
    content: "عنوان كبير" !important;
  }

  .smartpos-quill-editor .ql-header .ql-picker-item[data-value="2"]::before {
    content: "عنوان متوسط" !important;
  }

  .smartpos-quill-editor .ql-header .ql-picker-item[data-value="3"]::before {
    content: "عنوان صغير" !important;
  }

  .smartpos-quill-editor .ql-header .ql-picker-item:not([data-value])::before {
    content: "نص عادي" !important;
  }

  .smartpos-quill-editor .ql-header .ql-picker-label::before {
    content: "حجم النص" !important;
  }

  .smartpos-quill-editor .ql-header .ql-picker-label[data-value="1"]::before {
    content: "عنوان كبير" !important;
  }

  .smartpos-quill-editor .ql-header .ql-picker-label[data-value="2"]::before {
    content: "عنوان متوسط" !important;
  }

  .smartpos-quill-editor .ql-header .ql-picker-label[data-value="3"]::before {
    content: "عنوان صغير" !important;
  }

  .smartpos-quill-editor .ql-container {
    border-bottom-right-radius: 0.75rem;
    border-bottom-left-radius: 0.75rem;
    border: 2px solid #e5e7eb;
    border-top: none;
    direction: rtl;
    background: #ffffff;
  }

  .smartpos-quill-editor .ql-editor {
    direction: rtl !important;
    text-align: right !important;
    min-height: 200px;
    font-family: 'Almarai', 'Segoe UI', Tahoma, Arial, sans-serif;
    line-height: 1.7;
    font-size: 15px;
    padding: 20px 24px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    unicode-bidi: embed;
    text-align-last: right;
    color: #1f2937;
  }

  .smartpos-quill-editor .ql-editor.ql-blank::before {
    direction: rtl !important;
    text-align: right !important;
    font-style: italic;
    color: #9ca3af;
    left: auto !important;
    right: 24px !important;
    font-family: 'Almarai', 'Segoe UI', Tahoma, Arial, sans-serif;
  }

  /* إصلاح القوائم النقطية والرقمية - حل بسيط */
  .smartpos-quill-editor .ql-editor ul,
  .smartpos-quill-editor .ql-editor ol {
    direction: rtl !important;
    text-align: right !important;
    padding-right: 0 !important;
    padding-left: 1.5em !important;
    margin: 0.5em 0 !important;
  }

  .smartpos-quill-editor .ql-editor ul li,
  .smartpos-quill-editor .ql-editor ol li {
    direction: rtl !important;
    text-align: right !important;
    padding-right: 0 !important;
    margin: 0.2em 0 !important;
    line-height: 1.6 !important;
  }

  /* القوائم النقطية */
  .smartpos-quill-editor .ql-editor ul {
    list-style-type: disc !important;
    list-style-position: outside !important;
  }

  .smartpos-quill-editor .ql-editor ul li {
    list-style-type: disc !important;
    display: list-item !important;
  }

  /* القوائم الرقمية */
  .smartpos-quill-editor .ql-editor ol {
    list-style-type: decimal !important;
    list-style-position: outside !important;
  }

  .smartpos-quill-editor .ql-editor ol li {
    list-style-type: decimal !important;
    display: list-item !important;
  }

  /* تحسين عرض العناوين */
  .smartpos-quill-editor .ql-editor h1,
  .smartpos-quill-editor .ql-editor h2,
  .smartpos-quill-editor .ql-editor h3,
  .smartpos-quill-editor .ql-editor h4,
  .smartpos-quill-editor .ql-editor h5,
  .smartpos-quill-editor .ql-editor h6 {
    direction: rtl !important;
    text-align: right !important;
    font-weight: bold;
  }

  /* تحسين عرض الفقرات */
  .smartpos-quill-editor .ql-editor p {
    direction: rtl !important;
    text-align: right !important;
    margin: 0.5em 0;
  }

  /* تحسين عرض الروابط */
  .smartpos-quill-editor .ql-editor a {
    direction: rtl !important;
    text-decoration: underline;
    color: #3b82f6;
  }

  /* إخفاء زر RTL الافتراضي */
  .smartpos-quill-editor .ql-direction[value="rtl"] {
    display: none;
  }

  /* تحسين أزرار المحاذاة */
  .smartpos-quill-editor .ql-align .ql-picker-options {
    direction: rtl;
  }

  /* تحسين القوائم المنسدلة */
  .smartpos-quill-editor .ql-picker-options {
    direction: rtl;
    text-align: right;
  }

  /* تحسين الوضع المظلم */
  .dark .smartpos-quill-editor .ql-toolbar {
    background: linear-gradient(135deg, #374151 0%, #1f2937 100%) !important;
    border-color: #4b5563 !important;
  }

  .dark .smartpos-quill-editor .ql-toolbar button {
    background: #4b5563 !important;
    border-color: #6b7280 !important;
    color: #e5e7eb !important;
  }

  .dark .smartpos-quill-editor .ql-toolbar button:hover {
    background: #6b7280 !important;
    border-color: #9ca3af !important;
    color: #f3f4f6 !important;
  }

  .dark .smartpos-quill-editor .ql-toolbar button.ql-active {
    background: #3b82f6 !important;
    border-color: #2563eb !important;
    color: #ffffff !important;
  }

  .dark .smartpos-quill-editor .ql-toolbar .ql-picker-label {
    background: #4b5563 !important;
    border-color: #6b7280 !important;
    color: #e5e7eb !important;
  }

  .dark .smartpos-quill-editor .ql-toolbar .ql-picker-label:hover {
    background: #6b7280 !important;
    border-color: #9ca3af !important;
  }

  .dark .smartpos-quill-editor .ql-container {
    border-color: #4b5563 !important;
    background: #1f2937 !important;
  }

  .dark .smartpos-quill-editor .ql-editor {
    background: #1f2937 !important;
    color: #f9fafb !important;
  }

  .dark .smartpos-quill-editor .ql-editor.ql-blank::before {
    color: #6b7280 !important;
  }

  .dark .smartpos-quill-editor .ql-editor ul li::before,
  .dark .smartpos-quill-editor .ql-editor ol li::before {
    color: #60a5fa !important;
  }

  .dark .smartpos-quill-editor .ql-editor a {
    color: #60a5fa !important;
  }

  /* تحسين التمرير والتفاعل - إزالة الحدود الزرقاء */
  .smartpos-quill-editor .ql-editor:focus {
    outline: none !important;
    box-shadow: none !important;
  }

  /* إصلاح مشاكل التداخل */
  .smartpos-quill-editor .ql-editor * {
    direction: inherit;
  }
`;

const ARABIC_TOOLTIPS: { [key: string]: string } = {
  'ql-bold': 'عريض (Ctrl+B)',
  'ql-italic': 'مائل (Ctrl+I)',
  'ql-underline': 'تسطير (Ctrl+U)',
  'ql-strike': 'يتوسطه خط',
  'ql-list[value="ordered"]': 'قائمة رقمية',
  'ql-list[value="bullet"]': 'قائمة نقطية',
  'ql-script[value="sub"]': 'نص منخفض',
  'ql-script[value="super"]': 'نص مرتفع',
  'ql-indent[value="-1"]': 'تقليل المسافة البادئة',
  'ql-indent[value="+1"]': 'زيادة المسافة البادئة',
  'ql-direction[value="rtl"]': 'اتجاه النص من اليمين لليسار',
  'ql-align': 'محاذاة النص',
  'ql-align[value="center"]': 'محاذاة وسط',
  'ql-align[value="right"]': 'محاذاة يمين',
  'ql-align[value="left"]': 'محاذاة يسار',
  'ql-align[value="justify"]': 'محاذاة مبررة',
  'ql-link': 'إدراج رابط',
  'ql-image': 'إدراج صورة',
  'ql-video': 'إدراج فيديو',
  'ql-clean': 'إزالة التنسيق',
  'ql-header[value="1"]': 'عنوان كبير',
  'ql-header[value="2"]': 'عنوان متوسط',
  'ql-header[value="3"]': 'عنوان صغير',
  'ql-header[value="false"]': 'نص عادي',
};

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({ value, onChange, placeholder }) => {
  const editorRef = useRef<ReactQuill>(null);

  // تطبيق التحسينات العربية على المحرر
  const applyArabicEnhancements = () => {
    if (editorRef.current) {
      try {
        const editor = editorRef.current.getEditor();
        const toolbar = editor.getModule('toolbar').container;

        // تطبيق التلميحات العربية على الأزرار
        Object.keys(ARABIC_TOOLTIPS).forEach(key => {
          const button = toolbar.querySelector(`.${key}`);
          if (button) {
            button.setAttribute('title', ARABIC_TOOLTIPS[key]);
            button.setAttribute('aria-label', ARABIC_TOOLTIPS[key]);
          }
        });

        // تخصيص النصوص العربية لقائمة العناوين
        const headerPicker = toolbar.querySelector('.ql-header .ql-picker-label');
        if (headerPicker) {
          headerPicker.setAttribute('title', 'حجم النص');
        }

        // تطبيق التلميحات على القوائم المنسدلة
        const headerSelect = toolbar.querySelector('.ql-header .ql-picker-label');
        if (headerSelect) {
          headerSelect.setAttribute('title', 'حجم العنوان');
        }

        const alignSelect = toolbar.querySelector('.ql-align .ql-picker-label');
        if (alignSelect) {
          alignSelect.setAttribute('title', 'محاذاة النص');
        }

        // فرض اتجاه RTL على المحرر
        const editorElement = editor.root;
        editorElement.setAttribute('dir', 'rtl');
        editorElement.style.direction = 'rtl';
        editorElement.style.textAlign = 'right';

        // إعداد معالج بسيط لضمان RTL
        editor.on('text-change', () => {
          const editorRoot = editor.root;
          editorRoot.setAttribute('dir', 'rtl');
          editorRoot.style.direction = 'rtl';
          editorRoot.style.textAlign = 'right';
        });

      } catch (error) {
        console.error('خطأ في تطبيق التحسينات العربية:', error);
      }
    }
  };

  useEffect(() => {
    // تطبيق التحسينات العربية بعد تحميل المحرر
    const timer = setTimeout(applyArabicEnhancements, 100);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // إعادة تطبيق التحسينات عند تغيير القيمة
    const timer = setTimeout(applyArabicEnhancements, 50);
    return () => clearTimeout(timer);
  }, [value]);

  // إعدادات المحرر محسنة للغة العربية مع تحسينات إضافية
  const modules = {
    toolbar: {
        container: [
            [{ 'header': [1, 2, 3, false] }],
            ['bold', 'italic', 'underline', 'strike'],
            [{ 'list': 'ordered'}, { 'list': 'bullet' }],
            [{ 'script': 'sub'}, { 'script': 'super' }],
            [{ 'indent': '-1'}, { 'indent': '+1' }],
            [{ 'align': ['', 'center', 'right', 'justify'] }],
            ['link', 'image'],
            ['clean']
        ],

    },
    clipboard: {
      // الحفاظ على اتجاه النص عند النسخ واللصق
      matchVisual: false
    },
    keyboard: {
      bindings: {
        // معالجة بسيطة للقوائم
        'list autofill': {
          key: ' ',
          shiftKey: null,
          handler: function(_range: any, _context: any) {
            return true;
          }
        }
      }
    }
  };

  // تنسيقات النص المدعومة
  const formats = [
    'header', 'font', 'size',
    'bold', 'italic', 'underline', 'strike', 'blockquote',
    'list', 'bullet', 'indent',
    'link', 'image', 'video',
    'align', 'direction',
    'script'
  ];

  return (
    <div className="smartpos-quill-editor" dir="rtl">
      <style>{editorStyles}</style>
      <ReactQuill
        ref={editorRef}
        theme="snow"
        value={value}
        onChange={onChange}
        modules={modules}
        formats={formats}
        placeholder={placeholder}
        preserveWhitespace={true}
        bounds=".smartpos-quill-editor"
        readOnly={false}
        style={{ direction: 'rtl' }}
      />
    </div>
  );
};

export default RichTextEditor;