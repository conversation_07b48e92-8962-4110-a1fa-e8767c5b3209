/**
 * زر المحادثة العائم
 * يظهر في الزاوية السفلية ويفتح نافذة المحادثة
 */

import React, { useState, useEffect } from 'react';
import ChatWindow from './ChatWindow';
import { chatApiService } from '../../services/chatApiService';
import { chatNotificationService } from '../../services/chatNotificationService';
import { useAuthStore } from '../../stores/authStore';
import { FaComments, FaTimes } from 'react-icons/fa';
import './ChatButton.css';

const ChatButton: React.FC = () => {
  const { user } = useAuthStore();
  const [isOpen, setIsOpen] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [targetUserId, setTargetUserId] = useState<number | null>(null);

  // جلب عدد الرسائل غير المقروءة دورياً
  useEffect(() => {
    if (!user?.id) return;

    const fetchUnreadCount = async () => {
      try {
        const response = await chatApiService.getUnreadCount();
        setUnreadCount(response.unread_count);
      } catch (error) {
        console.error('خطأ في جلب عدد الرسائل غير المقروءة:', error);
      }
    };

    // جلب فوري
    fetchUnreadCount();

    // جلب دوري كل 30 ثانية
    const interval = setInterval(fetchUnreadCount, 30000);

    return () => clearInterval(interval);
  }, [user?.id]);

  // الاستماع لأحداث فتح المحادثة من مكونات أخرى
  useEffect(() => {
    const handleOpenChatWithUser = (event: CustomEvent<{ userId: number }>) => {
      console.log('🔔 ChatButton: استلام حدث فتح محادثة مع المستخدم:', event.detail.userId);
      setTargetUserId(event.detail.userId);
      setIsOpen(true);
      setUnreadCount(0);

      // إشعار خدمة التنبيهات بفتح النافذة
      chatNotificationService.setChatWindowState(true, event.detail.userId);
      console.log('✅ ChatButton: تم فتح النافذة مع المستخدم:', event.detail.userId);
    };

    console.log('🎧 ChatButton: بدء الاستماع لأحداث openChatWithUser');
    window.addEventListener('openChatWithUser', handleOpenChatWithUser as EventListener);

    return () => {
      console.log('🔇 ChatButton: إيقاف الاستماع لأحداث openChatWithUser');
      window.removeEventListener('openChatWithUser', handleOpenChatWithUser as EventListener);
    };
  }, []);

  const handleToggleChat = () => {
    if (isLoading) return;

    setIsLoading(true);
    const newIsOpen = !isOpen;
    setIsOpen(newIsOpen);

    // إعادة تعيين عداد الرسائل عند فتح المحادثة
    if (newIsOpen) {
      setUnreadCount(0);
      // إشعار خدمة التنبيهات بفتح النافذة
      chatNotificationService.setChatWindowState(true, null);
    } else {
      // إشعار خدمة التنبيهات بإغلاق النافذة
      chatNotificationService.setChatWindowState(false, null);
    }

    setTimeout(() => setIsLoading(false), 300);
  };

  const handleCloseChat = () => {
    setIsOpen(false);
    setTargetUserId(null);

    // إشعار خدمة التنبيهات بإغلاق النافذة
    chatNotificationService.setChatWindowState(false, null);
    console.log('🔔 ChatButton: تم إشعار خدمة التنبيهات بإغلاق النافذة');
  };

  // عدم عرض الزر إذا لم يكن المستخدم مسجل دخول
  if (!user?.id) {
    return null;
  }

  return (
    <>
      {/* زر المحادثة العائم */}
      <div className={`chat-button-container ${isOpen ? 'chat-open' : ''}`}>
        <div className="chat-button-wrapper">
          <button
            className={`chat-button ${isLoading ? 'loading' : ''}`}
            onClick={handleToggleChat}
            title={isOpen ? 'إغلاق المحادثة' : 'فتح المحادثة'}
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="chat-button-spinner">
                <div className="spinner small"></div>
              </div>
            ) : isOpen ? (
              <FaTimes className="chat-button-icon close" />
            ) : (
              <FaComments className="chat-button-icon" />
            )}
          </button>

          {/* شارة الرسائل غير المقروءة - خارج الزر */}
          {!isOpen && unreadCount > 0 && (
            <div className="chat-button-badge">
              {unreadCount > 99 ? '99+' : unreadCount}
            </div>
          )}
        </div>

        {/* نص مساعد */}
        {!isOpen && (
          <div className="chat-button-tooltip">
            المحادثة الفورية
            {unreadCount > 0 && (
              <div className="tooltip-unread">
                {unreadCount} رسالة جديدة
              </div>
            )}
          </div>
        )}
      </div>

      {/* نافذة المحادثة */}
      <ChatWindow
        isOpen={isOpen}
        onClose={handleCloseChat}
        initialUserId={targetUserId}
      />
    </>
  );
};

export default ChatButton;
