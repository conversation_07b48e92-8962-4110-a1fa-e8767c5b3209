/* تصميم قائمة المستخدمين المتصلين - متوافق مع تصميم النظام */

.online-users-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--color-card-bg);
  border-radius: 0;
  overflow: hidden;
}

/* رأس القائمة */
.online-users-header {
  padding: 20px;
  background: var(--color-card-bg);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
  color: var(--color-text-primary);
}

.header-icon {
  font-size: 20px;
  color: #0284c7;
}

.header-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.users-count {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--color-text-secondary);
  font-size: 14px;
  font-weight: 500;
}

.online-indicator {
  color: #10b981;
  font-size: 12px;
  animation: pulse 2s infinite;
}

/* شريط البحث */
.search-container {
  padding: 15px 20px;
  background: var(--color-bg-secondary);
  border-bottom: 1px solid var(--color-border);
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  right: 15px;
  color: var(--color-text-secondary);
  font-size: 14px;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 12px 45px 12px 15px;
  border: 1px solid var(--color-input-border);
  border-radius: 0.5rem;
  background: var(--color-input-bg);
  color: var(--color-text-primary);
  font-size: 14px;
  outline: none;
  transition: all 0.2s ease;
}

.search-input::placeholder {
  color: var(--color-text-secondary);
}

.search-input:focus {
  border-color: #0284c7;
  box-shadow: 0 0 0 3px rgba(2, 132, 199, 0.1);
}



/* حاوية المستخدمين */
.users-container {
  flex: 1;
  overflow-y: auto;
  padding: 10px 0;
}

/* عنصر المستخدم */
.user-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
  position: relative;
  border-bottom: 1px solid var(--color-border);
}

.user-item:hover {
  background: var(--color-bg-secondary);
  border-left-color: #0284c7;
}

.user-item.selected {
  background: rgba(2, 132, 199, 0.1);
  border-left-color: #0284c7;
}

/* صورة المستخدم */
.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #0284c7;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
  position: relative;
  transition: all 0.2s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
}

.user-avatar.online {
  background: #10b981;
}

.user-avatar.offline {
  background: var(--color-text-secondary);
}

.avatar-text {
  color: white;
  font-weight: 600;
  font-size: 13px;
}

.status-indicator {
  position: absolute;
  bottom: 0px;
  right: 0px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--color-card-bg);
}

.status-indicator.online {
  background: #10b981;
  color: white;
  animation: pulse 2s infinite;
}

.status-indicator.offline {
  background: var(--color-text-secondary);
  color: white;
}

.status-indicator svg {
  font-size: 5px;
}

/* معلومات المستخدم */
.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  color: var(--color-text-primary);
  font-weight: 600;
  font-size: 13px;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.user-status {
  margin: 2px 0 0 0;
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 3px;
  color: var(--color-text-secondary);
}

.status-offline {
  color: var(--color-text-secondary);
  display: flex;
  align-items: center;
  gap: 3px;
}

.status-icon {
  font-size: 9px;
}



/* حالات التحميل والخطأ */
.online-users-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--color-text-secondary);
  text-align: center;
}

.loading-spinner {
  margin-bottom: 15px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-border);
  border-top: 4px solid #0284c7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.error-message {
  padding: 20px;
  text-align: center;
  color: var(--color-text-primary);
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 0.5rem;
  margin: 15px 20px;
}

.retry-button {
  margin-top: 10px;
  padding: 8px 16px;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  background: var(--color-card-bg);
  color: var(--color-text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background: #0284c7;
  color: white;
  border-color: #0284c7;
}

/* حالة فارغة */
.empty-state {
  padding: 40px 20px;
  text-align: center;
  color: var(--color-text-primary);
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 15px;
  color: var(--color-text-secondary);
}

.empty-state h4 {
  margin: 0 0 10px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.empty-state p {
  margin: 0;
  color: var(--color-text-secondary);
  font-size: 14px;
}

/* إخفاء رسالة الحالة الفارغة في الوضع المظلم */
.dark .empty-message {
  display: none;
}



/* الرسوم المتحركة */
@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.7; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* التجاوب */
@media (max-width: 768px) {
  .online-users-header {
    padding: 15px;
  }

  .header-title h3 {
    font-size: 16px;
  }

  .user-item {
    padding: 6px 12px;
  }

  .user-avatar {
    width: 32px;
    height: 32px;
    margin-left: 8px;
  }

  .avatar-text {
    font-size: 12px;
  }

  .user-name {
    font-size: 12px;
  }

  .user-status {
    font-size: 10px;
  }
}

/* معلومات إضافية */
.users-info {
  padding: 15px 20px;
  background: var(--color-bg-secondary);
  border-top: 1px solid var(--color-border);
  text-align: center;
}

.users-info small {
  color: var(--color-text-secondary);
  font-size: 12px;
}
