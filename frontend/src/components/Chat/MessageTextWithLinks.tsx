/**
 * مكون لعرض النص مع الروابط والإيموجي
 */

import React from 'react';
import { parseTextWithLinks, TextPart } from '../../utils/linkUtils';
import LinkPreview from './LinkPreview';

interface MessageTextWithLinksProps {
  text: string;
  className?: string;
  showPreview?: boolean;
}

const MessageTextWithLinks: React.FC<MessageTextWithLinksProps> = ({
  text,
  className = '',
  showPreview = true
}) => {
  // تحليل النص لاستخراج الروابط
  const textParts = parseTextWithLinks(text);

  // استخراج الروابط للمعاينة
  const links = textParts.filter(part => part.type === 'link' && part.url);

  // دالة لتحسين عرض الإيموجي في النصوص
  const renderTextWithEmojis = (text: string) => {
    // استخدام regex محسن للإيموجي
    const emojiRegex = /(\p{Emoji})/gu;

    // تقسيم النص والحفاظ على الإيموجي في مكانها
    const parts = text.split(emojiRegex);

    return parts.map((part, index) => {
      // فحص إذا كان الجزء إيموجي
      if (emojiRegex.test(part)) {
        return (
          <span
            key={index}
            style={{
              fontSize: '1.2em',
              display: 'inline',
              verticalAlign: 'middle',
              margin: '0 1px',
              lineHeight: 'normal'
            }}
          >
            {part}
          </span>
        );
      }
      // إرجاع النص العادي
      return part;
    });
  };

  const renderTextPart = (part: TextPart, index: number) => {
    if (part.type === 'link' && part.url) {
      // إخفاء الرابط من النص - لا نعرض شيء هنا
      // المعاينة ستظهر في قسم منفصل
      return null;
    }

    // عرض النص العادي مع الإيموجي
    return (
      <span key={index}>
        {renderTextWithEmojis(part.content)}
      </span>
    );
  };

  // فحص إذا كان هناك نص غير الروابط
  const hasNonLinkText = textParts.some(part =>
    part.type === 'text' && part.content.trim().length > 0
  );

  return (
    <div className={`message-text-with-links ${className}`}>
      {/* النص مع الروابط - يظهر فقط إذا كان هناك نص غير الروابط */}
      {hasNonLinkText && (
        <div className="message-text-content">
          {textParts.map(renderTextPart)}
        </div>
      )}

      {/* معاينة الروابط */}
      {showPreview && links.length > 0 && (
        <div className="link-previews">
          {links.slice(0, 3).map((link, index) => (
            <LinkPreview
              key={index}
              url={link.url!}
              className="message-link-preview"
            />
          ))}
          {links.length > 3 && (
            <div className="more-links-indicator">
              +{links.length - 3} روابط أخرى
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MessageTextWithLinks;
