/**
 * زر المحادثة في القائمة العلوية
 * يظهر في القائمة العلوية بجانب زر الإشعارات مع عدد الرسائل غير المقروءة
 */

import React, { useState, useEffect } from 'react';
import ChatWindow from './ChatWindow';
import { useChat } from '../../hooks/useChat';
import { useAuthStore } from '../../stores/authStore';
import { TopbarTooltip } from '../ui';
import { FiMessageCircle } from 'react-icons/fi';

interface ChatHeaderButtonProps {
  className?: string;
}

const ChatHeaderButton: React.FC<ChatHeaderButtonProps> = ({ className = '' }) => {
  const { user } = useAuthStore();
  const [isOpen, setIsOpen] = useState(false);
  const [targetUserId, setTargetUserId] = useState<number | null>(null);
  
  // استخدام useChat hook للحصول على عدد الرسائل غير المقروءة
  const {
    unreadCount,
    isConnected,
    connect,
    disconnect,
    loadUnreadCount
  } = useChat({
    userId: user?.id ? parseInt(user.id.toString()) : 0,
    autoConnect: true
  });

  // إدارة الاتصال عند تغيير حالة النافذة
  useEffect(() => {
    if (isOpen && user?.id && !isConnected) {
      connect();
    }
  }, [isOpen, user?.id, isConnected, connect]);

  // الاستماع لتحديثات العدد الفورية
  useEffect(() => {
    const handleUnreadCountUpdate = () => {
      loadUnreadCount();
    };

    window.addEventListener('unreadCountUpdated', handleUnreadCountUpdate);

    return () => {
      window.removeEventListener('unreadCountUpdated', handleUnreadCountUpdate);
    };
  }, [loadUnreadCount]);

  const handleToggleChat = () => {
    const newIsOpen = !isOpen;
    setIsOpen(newIsOpen);

    // تحديث عدد الرسائل غير المقروءة عند فتح النافذة
    if (newIsOpen) {
      setTimeout(() => {
        loadUnreadCount();
      }, 100);

      // إشعار خدمة التنبيهات بفتح النافذة
      import('../../services/chatNotificationService').then(({ chatNotificationService }) => {
        chatNotificationService.setChatWindowState(true, null);
      });
    } else {
      // إشعار خدمة التنبيهات بإغلاق النافذة
      import('../../services/chatNotificationService').then(({ chatNotificationService }) => {
        chatNotificationService.setChatWindowState(false, null);
      });
    }
  };

  const handleCloseChat = () => {
    setIsOpen(false);
    setTargetUserId(null);

    // تحديث عدد الرسائل غير المقروءة عند إغلاق النافذة
    setTimeout(() => {
      loadUnreadCount();
    }, 100);

    // إشعار خدمة التنبيهات بإغلاق النافذة
    import('../../services/chatNotificationService').then(({ chatNotificationService }) => {
      chatNotificationService.setChatWindowState(false, null);
    });
  };

  // دالة لفتح المحادثة مع مستخدم معين
  const openChatWithUser = (userId: number) => {
    setTargetUserId(userId);
    setIsOpen(true);

    // تحديث عدد الرسائل غير المقروءة عند فتح النافذة
    setTimeout(() => {
      loadUnreadCount();
    }, 100);

    // إشعار خدمة التنبيهات بفتح النافذة
    import('../../services/chatNotificationService').then(({ chatNotificationService }) => {
      chatNotificationService.setChatWindowState(true, userId);
    });
  };

  // الاستماع لأحداث فتح المحادثة من مكونات أخرى
  useEffect(() => {
    const handleOpenChatWithUser = (event: CustomEvent<{ userId: number }>) => {
      console.log('🔔 ChatHeaderButton: استلام حدث فتح محادثة مع المستخدم:', event.detail.userId);
      openChatWithUser(event.detail.userId);
    };

    console.log('🎧 ChatHeaderButton: بدء الاستماع لأحداث openChatWithUser');
    window.addEventListener('openChatWithUser', handleOpenChatWithUser as EventListener);

    return () => {
      console.log('🔇 ChatHeaderButton: إيقاف الاستماع لأحداث openChatWithUser');
      window.removeEventListener('openChatWithUser', handleOpenChatWithUser as EventListener);
    };
  }, []);

  // عدم عرض الزر إذا لم يكن المستخدم مسجل دخول
  if (!user?.id) {
    return null;
  }

  return (
    <>
      {/* زر المحادثة في القائمة العلوية */}
      <div className={`relative ${className}`}>
        <TopbarTooltip
          text={unreadCount > 0 ? `${unreadCount} رسالة غير مقروءة` : 'المحادثة الفورية'}
          position="bottom"
          variant={unreadCount > 0 ? 'primary' : 'default'}
        >
          <button
            onClick={handleToggleChat}
            className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-500 hover:bg-gray-200 dark:hover:bg-gray-600 hover:text-primary-600 dark:hover:text-primary-400 transition-all duration-200 relative"
            aria-label="المحادثة الفورية"
          >
            <FiMessageCircle className="w-4 h-4" />

            {/* شارة عدد الرسائل غير المقروءة */}
            {unreadCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
                {unreadCount > 99 ? '99+' : unreadCount}
              </span>
            )}
          </button>
        </TopbarTooltip>
      </div>

      {/* نافذة المحادثة */}
      {isOpen && (
        <ChatWindow
          isOpen={isOpen}
          onClose={handleCloseChat}
          initialUserId={targetUserId}
        />
      )}
    </>
  );
};

export default ChatHeaderButton;
