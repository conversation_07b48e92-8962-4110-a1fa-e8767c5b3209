/**
 * مكون عرض جميع المستخدمين
 * يعرض جميع المستخدمين النشطين في النظام مع بطاقات محسنة
 */

import React, { useState, useEffect } from 'react';
import {
  FaCircle,
  FaSearch,
  FaUserFriends,
  FaSpinner
} from 'react-icons/fa';
import { chatApiService, UserOnlineStatus } from '../../services/chatApiService';
import './AllUsersList.css';

interface AllUsersListProps {
  onUserSelect: (userId: number, userData?: UserOnlineStatus) => void;
  selectedUserId?: number | null;
  className?: string;
}

const AllUsersList: React.FC<AllUsersListProps> = ({
  onUserSelect,
  selectedUserId,
  className = ''
}) => {
  const [allUsers, setAllUsers] = useState<UserOnlineStatus[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  // جلب جميع المستخدمين
  const loadAllUsers = async () => {
    try {
      setLoading(true);
      const users = await chatApiService.getAllUsers();
      setAllUsers(users);
    } catch (err) {
      setError('فشل في جلب المستخدمين');
      console.error('خطأ في جلب جميع المستخدمين:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAllUsers();
  }, []);

  // الاستماع لتغييرات حالة المستخدم وتحديث القائمة فوراً
  useEffect(() => {
    const handleUserStatusUpdate = (data: any) => {
      setAllUsers(prev => prev.map(user =>
        user.user_id === data.user_id
          ? { ...user, is_online: data.is_online, last_seen: data.is_online ? undefined : new Date().toISOString() }
          : user
      ));
    };

    // استيراد خدمة WebSocket والاستماع للأحداث
    import('../../services/chatWebSocketService').then(({ chatWebSocketService }) => {
      chatWebSocketService.on('user_status_change', handleUserStatusUpdate);
    });

    // تنظيف المستمع عند إلغاء التحميل
    return () => {
      import('../../services/chatWebSocketService').then(({ chatWebSocketService }) => {
        chatWebSocketService.off('user_status_change', handleUserStatusUpdate);
      });
    };
  }, []);



  // الحصول على الأحرف الأولى من الاسم
  const getInitials = (name: string) => {
    if (!name) return '👤';
    const words = name.trim().split(' ');
    if (words.length === 1) {
      return words[0].charAt(0).toUpperCase();
    }
    return (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
  };

  // دالة لتوليد لون فريد لكل مستخدم بناءً على معرفه
  const getUserAvatarColor = (userId: number) => {
    const colors = [
      '#0284c7', // أزرق
      '#059669', // أخضر
      '#7c3aed', // بنفسجي
      '#dc2626', // أحمر
      '#ea580c', // برتقالي
      '#0891b2', // سماوي
      '#65a30d', // أخضر فاتح
      '#c2410c', // برتقالي داكن
      '#9333ea', // بنفسجي فاتح
      '#be123c', // وردي داكن
      '#0d9488', // تركوازي
      '#4338ca', // أزرق داكن
    ];

    return colors[userId % colors.length];
  };

  // تصفية المستخدمين حسب البحث
  const filteredUsers = allUsers.filter(user => {
    if (!searchQuery.trim()) return true;
    const query = searchQuery.toLowerCase();
    return (
      user.username?.toLowerCase().includes(query) ||
      user.full_name?.toLowerCase().includes(query)
    );
  });

  const handleUserClick = (user: UserOnlineStatus) => {
    onUserSelect(user.user_id, user);
  };

  return (
    <div className={`all-users-list ${className}`}>
      {/* رأس القائمة */}
      <div className="all-users-header">
        <div className="header-title">
          <FaUserFriends className="header-icon" />
          <h3>جميع المستخدمين</h3>
        </div>
        
        <div className="users-count">
          <FaUserFriends className="count-icon" />
          <span>{allUsers.length} مستخدم</span>
        </div>
      </div>

      {/* شريط البحث */}
      <div className="search-container">
        <div className="search-input-wrapper">
          <FaSearch className="search-icon" />
          <input
            type="text"
            placeholder="ابحث عن مستخدم..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="search-input"
          />
        </div>
      </div>

      {/* حاوية المستخدمين */}
      <div className="users-container custom-scrollbar-auto">
        {loading && (
          <div className="loading-state">
            <FaSpinner className="spinner" />
            <p>جاري تحميل المستخدمين...</p>
          </div>
        )}

        {error && (
          <div className="error-message">
            <p>{error}</p>
            <button onClick={loadAllUsers} className="retry-button">
              إعادة المحاولة
            </button>
          </div>
        )}

        {filteredUsers.length === 0 && !loading && !error && (
          <div className="empty-state">
            <div className="empty-icon">
              {searchQuery ? <FaSearch /> : <FaUserFriends />}
            </div>
            <h4>
              {searchQuery 
                ? 'لا توجد نتائج' 
                : 'لا توجد مستخدمون'
              }
            </h4>
            <p className="empty-message">
              {searchQuery 
                ? 'جرب البحث بكلمات مختلفة' 
                : 'لا يوجد مستخدمون مسجلون في النظام'
              }
            </p>
          </div>
        )}

        {filteredUsers.map((user) => (
          <div
            key={user.user_id}
            className={`user-item ${selectedUserId === user.user_id ? 'selected' : ''}`}
            onClick={() => handleUserClick(user)}
          >
            {/* صورة المستخدم */}
            <div
              className={`user-avatar ${user.is_online ? 'online' : 'offline'}`}
              style={{
                backgroundColor: getUserAvatarColor(user.user_id),
                background: getUserAvatarColor(user.user_id)
              }}
            >
              <span className="avatar-text">
                {getInitials(user.full_name || user.username)}
              </span>
              <div className={`status-indicator ${user.is_online ? 'online' : 'offline'}`}>
                <FaCircle />
              </div>
            </div>

            {/* معلومات المستخدم */}
            <div className="user-info">
              <div className="user-name-container">
                <h4 className="user-name">
                  {user.full_name || user.username}
                </h4>
                <span className="user-username">
                  @{user.username}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* معلومات إضافية */}
      <div className="users-info">
        <small>
          يمكنك البحث بالاسم أو اسم المستخدم
        </small>
      </div>
    </div>
  );
};

export default AllUsersList;
