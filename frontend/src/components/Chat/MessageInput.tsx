/**
 * مكون إدخال الرسائل
 * يتيح للمستخدم كتابة وإرسال الرسائل
 */

import React, { useState, useRef, useEffect } from 'react';
import { FaPaperclip, FaImage, FaPaperPlane, FaExclamationTriangle, FaSmile } from 'react-icons/fa';
import { Zap } from '../ui/icons';
import EmojiPicker from './EmojiPicker';

interface MessageInputProps {
  onSendMessage: (content: string) => void;
  onStartTyping?: () => void;
  onStopTyping?: () => void;
  disabled?: boolean;
  placeholder?: string;
  maxLength?: number;
  isConnected?: boolean;
  isConnecting?: boolean;
}

const MessageInput: React.FC<MessageInputProps> = ({
  onSendMessage,
  onStartTyping,
  onStopTyping,
  disabled = false,
  placeholder = 'اكتب رسالة...',
  maxLength = 5000,
  isConnected = true,
  isConnecting = false
}) => {
  const [message, setMessage] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const emojiButtonRef = useRef<HTMLButtonElement>(null);
  const isTypingRef = useRef<boolean>(false);

  // تعديل ارتفاع textarea تلقائياً مع منع التوسع غير المرغوب
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      // إعادة تعيين الارتفاع أولاً
      textarea.style.height = 'auto';

      // حساب الارتفاع المطلوب
      const scrollHeight = textarea.scrollHeight;
      const lineHeight = 20; // ارتفاع السطر الواحد
      const minHeight = lineHeight + 12; // ارتفاع سطر واحد + padding
      const maxHeight = 120; // الحد الأقصى

      // تحديد الارتفاع النهائي
      let newHeight = Math.max(minHeight, Math.min(scrollHeight, maxHeight));

      // تطبيق الارتفاع الجديد
      textarea.style.height = newHeight + 'px';
    }
  }, [message]);

  // إدارة إشعارات الكتابة - طريقة بسيطة ومنطقية
  useEffect(() => {
    // إزالة timeout السابق
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    if (message.trim()) {
      // إرسال إشعار الكتابة إذا لم نكن نكتب بالفعل
      if (!isTypingRef.current) {
        isTypingRef.current = true;
        onStartTyping?.();
      }

      // تعيين timeout لإيقاف الكتابة بعد 2 ثانية من عدم الكتابة
      typingTimeoutRef.current = setTimeout(() => {
        isTypingRef.current = false;
        onStopTyping?.();
      }, 2000);
    } else {
      // إيقاف الكتابة فوراً إذا كان الحقل فارغ
      if (isTypingRef.current) {
        isTypingRef.current = false;
        onStopTyping?.();
      }
    }

    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [message, onStartTyping, onStopTyping]);

  // تنظيف عند إلغاء التحميل
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      if (isTypingRef.current) {
        onStopTyping?.();
      }
    };
  }, [onStopTyping]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    sendMessage();
  };

  const sendMessage = () => {
    const trimmedMessage = message.trim();
    if (trimmedMessage && !disabled) {
      onSendMessage(trimmedMessage);
      setMessage('');

      // إيقاف الكتابة فوراً عند الإرسال
      if (isTypingRef.current) {
        isTypingRef.current = false;
        onStopTyping?.();
      }

      // التركيز على textarea بعد الإرسال
      setTimeout(() => {
        textareaRef.current?.focus();
      }, 0);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    if (value.length <= maxLength) {
      setMessage(value);
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    // يمكن إضافة منطق خاص للصق الصور أو الملفات هنا
    const pastedText = e.clipboardData.getData('text');
    if (message.length + pastedText.length > maxLength) {
      e.preventDefault();
      const remainingLength = maxLength - message.length;
      if (remainingLength > 0) {
        setMessage(message + pastedText.substring(0, remainingLength));
      }
    }
  };

  // معالجة اختيار الإيموجي
  const handleEmojiSelect = (emoji: string) => {
    const textarea = textareaRef.current;
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newMessage = message.substring(0, start) + emoji + message.substring(end);

      if (newMessage.length <= maxLength) {
        setMessage(newMessage);

        // إعادة تعيين موضع المؤشر بعد الإيموجي
        setTimeout(() => {
          const newCursorPosition = start + emoji.length;
          textarea.setSelectionRange(newCursorPosition, newCursorPosition);
          textarea.focus();
        }, 0);
      }
    }
    setShowEmojiPicker(false);
  };

  // تبديل عرض منتقي الإيموجي
  const toggleEmojiPicker = () => {
    setShowEmojiPicker(!showEmojiPicker);
  };

  const isMessageEmpty = !message.trim();
  const characterCount = message.length;
  const isNearLimit = characterCount > maxLength * 0.8;

  // تحديد حالة زر الإرسال
  const getSendButtonState = () => {
    if (isConnecting) return 'connecting';
    if (!isConnected) return 'disconnected';
    if (disabled) return 'disabled';
    if (isMessageEmpty) return 'empty';
    return 'ready';
  };

  const sendButtonState = getSendButtonState();

  // تحديد أيقونة زر الإرسال
  const getSendButtonIcon = () => {
    switch (sendButtonState) {
      case 'connecting':
        return <Zap className="connecting-icon" size={16} />;
      case 'disconnected':
        return <FaExclamationTriangle />;
      case 'disabled':
        return <Zap className="disabled-icon" size={16} />;
      case 'ready':
        return <FaPaperPlane />;
      default:
        return <FaPaperPlane />;
    }
  };

  // تحديد نص زر الإرسال
  const getSendButtonTitle = () => {
    switch (sendButtonState) {
      case 'connecting':
        return 'جاري الاتصال...';
      case 'disconnected':
        return 'انقطع الاتصال - يرجى المحاولة لاحقاً';
      case 'disabled':
        return 'غير متاح';
      case 'empty':
        return 'إرسال';
      case 'ready':
        return 'إرسال (Enter)';
      default:
        return 'إرسال';
    }
  };

  return (
    <div className="message-input-container">
      <form onSubmit={handleSubmit} className="message-input-form">
        <div className="input-wrapper">
          {/* منطقة النص */}
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            onPaste={handlePaste}
            placeholder={placeholder}
            disabled={disabled}
            className={`message-textarea ${disabled ? 'disabled' : ''}`}
            rows={1}
            maxLength={maxLength}
          />

          {/* أزرار إضافية */}
          <div className="input-actions">
            {/* زر الإيموجي */}
            <div className="emoji-picker-container">
              <button
                ref={emojiButtonRef}
                type="button"
                className={`action-btn emoji-btn ${showEmojiPicker ? 'active' : ''}`}
                disabled={disabled}
                onClick={toggleEmojiPicker}
                title="إضافة إيموجي"
              >
                <FaSmile />
              </button>

              {/* منتقي الإيموجي */}
              <EmojiPicker
                isOpen={showEmojiPicker}
                onEmojiSelect={handleEmojiSelect}
                onClose={() => setShowEmojiPicker(false)}
                triggerRef={emojiButtonRef}
              />
            </div>

            {/* زر الملفات (مستقبلاً) */}
            <button
              type="button"
              className="action-btn file-btn"
              disabled={disabled}
              title="إرفاق ملف"
            >
              <FaPaperclip />
            </button>

            {/* زر الصور (مستقبلاً) */}
            <button
              type="button"
              className="action-btn image-btn"
              disabled={disabled}
              title="إرفاق صورة"
            >
              <FaImage />
            </button>
          </div>

          {/* زر الإرسال */}
          <button
            type="submit"
            className={`send-btn ${sendButtonState}`}
            disabled={sendButtonState !== 'ready'}
            title={getSendButtonTitle()}
          >
            {getSendButtonIcon()}
          </button>
        </div>

        {/* معلومات إضافية */}
        <div className="input-info">
          {/* عداد الأحرف */}
          {isNearLimit && (
            <span className={`character-count ${characterCount >= maxLength ? 'limit-reached' : ''}`}>
              {characterCount}/{maxLength}
            </span>
          )}
        </div>
      </form>
    </div>
  );
};

export default MessageInput;
