import React, { useState, useEffect } from 'react';
import ChatMessageAlert from './ChatMessageAlert';
import { chatNotificationService } from '../../services/chatNotificationService';

interface ChatMessage {
  id: number;
  sender_id: number;
  receiver_id: number;
  content: string;
  message_type: string;
  status: string;
  created_at: string;
  sender_username?: string;
  sender_full_name?: string;
}

interface ChatNotificationManagerProps {
  onOpenChat: (senderId: number) => void;
  maxNotifications?: number;
}

const ChatNotificationManager: React.FC<ChatNotificationManagerProps> = ({
  onOpenChat,
  maxNotifications = 1
}) => {
  const [notifications, setNotifications] = useState<ChatMessage[]>([]);

  useEffect(() => {
    console.log('🔔 تهيئة ChatNotificationManager...');

    // الاستماع للتنبيهات الجديدة
    const unsubscribe = chatNotificationService.addListener((message: ChatMessage) => {
      console.log('🔔 تم استلام تنبيه جديد في Manager:', {
        messageId: message.id,
        senderId: message.sender_id,
        content: message.content.substring(0, 30) + '...'
      });

      setNotifications(prev => {
        // تجنب التكرار
        if (prev.some(n => n.id === message.id)) {
          console.log('⚠️ تنبيه مكرر - تم تجاهله');
          return prev;
        }

        // إذا كان هناك تنبيه موجود، استبدله بالجديد
        if (prev.length > 0) {
          console.log('🔄 استبدال التنبيه الحالي بالجديد');
          // إزالة التنبيه القديم من الخدمة
          prev.forEach(notification => {
            chatNotificationService.dismissNotification(notification.id);
          });
          // عرض التنبيه الجديد فقط
          return [message];
        }

        // إضافة التنبيه الجديد
        console.log('✅ تم إضافة تنبيه جديد');
        return [message];
      });
    });

    // تحميل التنبيهات النشطة الحالية
    const activeNotifications = chatNotificationService.getActiveNotifications();
    console.log('📋 التنبيهات النشطة الحالية:', activeNotifications.length);
    setNotifications(activeNotifications);

    return unsubscribe;
  }, [maxNotifications]);

  const handleReply = (senderId: number) => {
    console.log('📞 فتح المحادثة مع المستخدم:', senderId);

    // فتح المحادثة مع المرسل
    onOpenChat(senderId);

    // إزالة جميع التنبيهات (لأننا نعرض تنبيه واحد فقط)
    setNotifications(prev => {
      // إزالة من الخدمة أيضاً
      prev.forEach(notification => {
        chatNotificationService.dismissNotification(notification.id);
      });

      console.log('🗑️ تم إزالة جميع التنبيهات بعد فتح المحادثة');
      return [];
    });
  };

  const handleDismiss = (messageId: number) => {
    console.log('❌ إغلاق التنبيه:', messageId);

    // إزالة التنبيه من الحالة المحلية
    setNotifications(prev => prev.filter(n => n.id !== messageId));

    // إزالة من الخدمة
    chatNotificationService.dismissNotification(messageId);
  };

  return (
    <>
      {notifications.map((notification) => (
        <ChatMessageAlert
          key={notification.id}
          message={notification}
          onReply={handleReply}
          onDismiss={() => handleDismiss(notification.id)}
          autoHide={false}
          duration={8000}
        />
      ))}
    </>
  );
};

export default ChatNotificationManager;
