/**
 * مكون البحث عن المستخدمين المحسن
 * يتيح البحث عن مستخدمين جدد لبدء محادثة معهم مع تصميم عصري
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  FaSearch,
  FaTimes,
  FaCircle,
  FaClock,
  FaComments,
  FaUserFriends,
  FaSpinner
} from 'react-icons/fa';
import { chatApiService, UserOnlineStatus } from '../../services/chatApiService';
import './UserSearch.css';

interface UserSearchProps {
  onUserSelect: (userId: number) => void;
  onClose: () => void;
}

const UserSearch: React.FC<UserSearchProps> = ({ onUserSelect, onClose }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<UserOnlineStatus[]>([]);
  const [onlineUsers, setOnlineUsers] = useState<UserOnlineStatus[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // التركيز على حقل البحث عند التحميل
  useEffect(() => {
    searchInputRef.current?.focus();
  }, []);

  // جلب المستخدمين المتصلين عند التحميل
  useEffect(() => {
    loadOnlineUsers();
  }, []);

  // البحث مع تأخير
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    if (searchQuery.trim().length >= 2) {
      searchTimeoutRef.current = setTimeout(() => {
        performSearch(searchQuery.trim());
      }, 300);
    } else {
      setSearchResults([]);
      setError(null);
    }

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [searchQuery]);

  const loadOnlineUsers = async () => {
    try {
      const users = await chatApiService.getOnlineUsers();
      setOnlineUsers(users);
    } catch (err) {
      console.error('خطأ في جلب المستخدمين المتصلين:', err);
    }
  };

  const performSearch = async (query: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await chatApiService.searchUsers(query, 20);
      setSearchResults(response.users);
    } catch (err) {
      setError('فشل في البحث عن المستخدمين');
      console.error('خطأ في البحث:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleUserClick = (user: UserOnlineStatus) => {
    onUserSelect(user.user_id);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  const formatLastSeen = (lastSeen: string) => {
    try {
      const date = new Date(lastSeen);
      const now = new Date();
      const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

      if (diffInHours < 1) {
        return 'منذ قليل';
      } else if (diffInHours < 24) {
        return `منذ ${diffInHours} ساعة`;
      } else {
        const diffInDays = Math.floor(diffInHours / 24);
        return `منذ ${diffInDays} يوم`;
      }
    } catch {
      return 'غير معروف';
    }
  };

  const renderUserItem = (user: UserOnlineStatus) => (
    <div
      key={user.user_id}
      className="user-search-item"
      onClick={() => handleUserClick(user)}
    >
      <div className={`user-search-avatar ${user.is_online ? 'online' : 'offline'}`}>
        <span className="avatar-text">
          {user.full_name?.charAt(0) || user.username?.charAt(0) || '👤'}
        </span>
        <div className={`status-indicator ${user.is_online ? 'online' : 'offline'}`}>
          <FaCircle />
        </div>
      </div>

      <div className="user-search-info">
        <h4 className="user-search-name">
          {user.full_name || user.username}
        </h4>
        <p className="user-search-status">
          {user.is_online ? (
            <span className="status-online">
              <FaCircle className="status-dot" />
              متصل الآن
            </span>
          ) : (
            <span className="status-offline">
              <FaClock className="status-icon" />
              آخر ظهور {user.last_seen ? formatLastSeen(user.last_seen) : 'غير معروف'}
            </span>
          )}
        </p>
        <p className="user-search-username">
          @{user.username}
        </p>
      </div>

      <div className="user-search-actions">
        <button
          className="start-chat-button"
          title="بدء محادثة"
          onClick={(e) => {
            e.stopPropagation();
            handleUserClick(user);
          }}
        >
          <FaComments />
        </button>
      </div>
    </div>
  );

  return (
    <div className="user-search">
      {/* رأس البحث */}
      <div className="search-header">
        <div className="header-title">
          <FaUserFriends className="header-icon" />
          <h4>البحث عن مستخدمين</h4>
        </div>
        <button className="close-search-btn" onClick={onClose} title="إغلاق">
          <FaTimes />
        </button>
      </div>

      {/* حقل البحث */}
      <div className="search-input-container">
        <div className="search-input-wrapper">
          <FaSearch className="search-icon" />
          <input
            ref={searchInputRef}
            type="text"
            value={searchQuery}
            onChange={handleSearchChange}
            onKeyDown={handleKeyDown}
            placeholder="ابحث بالاسم أو اسم المستخدم..."
            className="search-input"
          />
          {loading && (
            <div className="search-loading">
              <FaSpinner className="spinner" />
            </div>
          )}
        </div>
      </div>

      {/* النتائج */}
      <div className="search-results custom-scrollbar-auto">
        {error && (
          <div className="error-message">
            ❌ {error}
          </div>
        )}

        {searchQuery.trim().length >= 2 ? (
          // نتائج البحث
          <>
            {searchResults.length > 0 ? (
              <>
                <div className="results-header">
                  <h5>نتائج البحث ({searchResults.length})</h5>
                </div>
                <div className="users-list">
                  {searchResults.map(user => renderUserItem(user))}
                </div>
              </>
            ) : !loading && (
              <div className="no-results">
                <div className="no-results-icon">🔍</div>
                <p>لا توجد نتائج للبحث "{searchQuery}"</p>
                <small>جرب البحث بكلمات مختلفة</small>
              </div>
            )}
          </>
        ) : (
          // المستخدمون المتصلون
          <>
            {onlineUsers.length > 0 ? (
              <>
                <div className="results-header">
                  <h5>🟢 المستخدمون المتصلون ({onlineUsers.length})</h5>
                </div>
                <div className="users-list">
                  {onlineUsers.map(user => renderUserItem(user))}
                </div>
              </>
            ) : (
              <div className="no-online-users">
                <div className="no-users-icon">👥</div>
                <p>لا يوجد مستخدمون متصلون حالياً</p>
                <small>ابحث عن مستخدم لبدء محادثة</small>
              </div>
            )}
          </>
        )}
      </div>

      {/* نصائح */}
      <div className="search-tips">
        <small>
          💡 نصيحة: اكتب على الأقل حرفين للبحث
        </small>
      </div>
    </div>
  );
};

export default UserSearch;
