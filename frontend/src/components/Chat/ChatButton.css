/* تصميم زر المحادثة العائم - متوافق مع تصميم النظام */

.chat-button-container {
  position: fixed;
  bottom: 90px; /* فوق زر العودة للأعلى مباشرة مع مسافة مناسبة */
  right: 24px; /* نفس موقع زر العودة للأعلى تماماً */
  z-index: 1001; /* أعلى من زر العودة إلى الأعلى */
  display: flex;
  flex-direction: column;
  align-items: center; /* محاذاة في المنتصف */
  gap: 10px;
}

.chat-button {
  width: 48px; /* نفس أبعاد زر العودة للأعلى */
  height: 48px; /* نفس أبعاد زر العودة للأعلى */
  border-radius: 50%;
  background: #10b981; /* لون أخضر مختلف عن زر التمرير */
  border: 1px solid rgba(16, 185, 129, 0.2);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem; /* تقليل حجم الأيقونة قليلاً */
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.chat-button:hover {
  background: #059669; /* لون أخضر داكن عند التحويم */
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.chat-button:active {
  transform: translateY(0);
}

.chat-button.loading {
  cursor: not-allowed;
  opacity: 0.8;
}

.chat-button-icon {
  transition: all 0.2s ease;
}

.chat-button-icon.close {
  font-size: 1.2rem;
  font-weight: bold;
}

.chat-button-spinner {
  animation: spin 1s linear infinite;
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* حاوي الزر مع الشارة */
.chat-button-wrapper {
  position: relative;
  display: inline-block;
}

/* شارة عدد الرسائل غير المقروءة - فوق الزر */
.chat-button-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: bold;
  border: 3px solid white;
  animation: pulse 2s infinite;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* نص مساعد */
.chat-button-tooltip {
  background: var(--color-card-bg);
  color: var(--color-text-primary);
  padding: 8px 12px;
  border-radius: 0.5rem;
  font-size: 0.85rem;
  white-space: nowrap;
  opacity: 0;
  transform: translateX(-10px); /* تغيير الاتجاه للموقع الجديد */
  transition: all 0.2s ease;
  pointer-events: none;
  text-align: center;
  border: 1px solid var(--color-border);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.chat-button-container:hover .chat-button-tooltip {
  opacity: 1;
  transform: translateX(0);
}

.tooltip-unread {
  font-size: 0.75rem;
  color: #f59e0b;
  margin-top: 2px;
  font-weight: bold;
}

/* حالة المحادثة المفتوحة */
.chat-button-container.chat-open .chat-button {
  background: #ef4444;
  border-color: rgba(239, 68, 68, 0.2);
}

.chat-button-container.chat-open .chat-button:hover {
  background: #dc2626;
}

/* تأثيرات إضافية */
.chat-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.chat-button:hover::before {
  opacity: 1;
}

/* تجاوب الشاشات الصغيرة */
@media (max-width: 768px) {
  .chat-button-container {
    bottom: 86px; /* فوق زر العودة للأعلى في الشاشات الصغيرة */
    right: 20px; /* نفس موقع زر العودة للأعلى */
  }

  .chat-button {
    width: 48px; /* نفس أبعاد زر العودة للأعلى */
    height: 48px;
    font-size: 1.1rem;
  }

  .chat-button-badge {
    width: 24px;
    height: 24px;
    font-size: 0.65rem;
    top: -6px;
    right: -6px;
    border-width: 2px;
  }

  .chat-button-tooltip {
    display: none;
  }
}

@media (max-width: 480px) {
  .chat-button-container {
    bottom: 81px; /* فوق زر العودة للأعلى في الشاشات الصغيرة جداً */
    right: 15px; /* نفس موقع زر العودة للأعلى */
  }

  .chat-button {
    width: 48px; /* نفس أبعاد زر العودة للأعلى */
    height: 48px;
    font-size: 1rem;
  }
}

/* تأثير الظهور */
.chat-button-container {
  animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(100px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* تأثير النقر */
.chat-button:active {
  animation: buttonClick 0.2s ease;
}

@keyframes buttonClick {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}
