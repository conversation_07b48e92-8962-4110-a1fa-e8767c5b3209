/* تصميم قائمة جميع المستخدمين - متوافق مع تصميم النظام */

.all-users-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--color-card-bg);
  border-radius: 0;
  overflow: hidden;
}

/* رأس القائمة */
.all-users-header {
  padding: 20px;
  background: var(--color-card-bg);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
  color: var(--color-text-primary);
}

.header-icon {
  font-size: 20px;
  color: #0284c7;
}

.header-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.users-count {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: rgba(2, 132, 199, 0.1);
  border-radius: 0.5rem;
  color: #0284c7;
  font-size: 13px;
  font-weight: 500;
}

.count-icon {
  font-size: 12px;
}

/* شريط البحث */
.search-container {
  padding: 15px 20px;
  background: var(--color-card-bg);
  border-bottom: 1px solid var(--color-border);
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  right: 12px;
  color: var(--color-text-secondary);
  font-size: 14px;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 10px 40px 10px 12px;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  background: var(--color-input-bg);
  color: var(--color-text-primary);
  font-size: 14px;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #0284c7;
  box-shadow: 0 0 0 3px rgba(2, 132, 199, 0.1);
}

.search-input::placeholder {
  color: var(--color-text-secondary);
}

/* حاوية المستخدمين */
.users-container {
  flex: 1;
  overflow-y: auto;
  padding: 10px 0;
}

/* حالة التحميل */
.loading-state {
  padding: 40px 20px;
  text-align: center;
  color: var(--color-text-secondary);
}

.spinner {
  font-size: 2rem;
  margin-bottom: 15px;
  animation: spin 1s linear infinite;
  color: #0284c7;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* رسالة خطأ */
.error-message {
  padding: 20px;
  text-align: center;
  color: #ef4444;
}

.retry-button {
  margin-top: 10px;
  padding: 8px 16px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background: #dc2626;
}

/* حالة فارغة */
.empty-state {
  padding: 40px 20px;
  text-align: center;
  color: var(--color-text-primary);
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 15px;
  color: var(--color-text-secondary);
}

.empty-state h4 {
  margin: 0 0 10px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.empty-state p {
  margin: 0;
  color: var(--color-text-secondary);
  font-size: 14px;
}

/* إخفاء رسالة الحالة الفارغة في الوضع المظلم */
.dark .empty-message {
  display: none;
}

/* عنصر المستخدم */
.user-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 4px solid transparent;
  position: relative;
  border-bottom: 1px solid var(--color-border);
}

.user-item:hover {
  background: var(--color-bg-secondary);
  border-left-color: #0284c7;
}

.user-item.selected {
  background: rgba(2, 132, 199, 0.1);
  border-left-color: #0284c7;
}

/* صورة المستخدم */
.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #0284c7;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 12px;
  position: relative;
  transition: all 0.2s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
}

.user-avatar.online {
  background: #10b981;
}

.user-avatar.offline {
  background: #6b7280;
}

.avatar-text {
  color: white;
  font-weight: 600;
  font-size: 14px;
}

/* مؤشر الحالة */
.status-indicator {
  position: absolute;
  bottom: 1px;
  left: 1px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--color-card-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--color-card-bg);
}

.status-indicator.online {
  color: #10b981;
}

.status-indicator.offline {
  color: #6b7280;
}

.status-indicator svg {
  font-size: 6px;
}

/* معلومات المستخدم */
.user-info {
  flex: 1;
  min-width: 0;
}

.user-name-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.user-name {
  color: var(--color-text-primary);
  font-weight: 600;
  font-size: 14px;
  margin: 0;
  line-height: 1.2;
  flex: 1;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-left: 8px;
}

.user-username {
  margin: 0;
  font-size: 11px;
  color: var(--color-text-secondary);
  font-style: italic;
  white-space: nowrap;
  flex-shrink: 0;
}





/* معلومات إضافية */
.users-info {
  padding: 15px 20px;
  background: var(--color-bg-secondary);
  border-top: 1px solid var(--color-border);
  text-align: center;
}

.users-info small {
  color: var(--color-text-secondary);
  font-size: 12px;
}

/* تحسينات للوضع المظلم */
.dark .user-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.dark .user-item.selected {
  background: rgba(2, 132, 199, 0.2);
}

/* تأثيرات الحركة */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .all-users-header {
    padding: 15px;
  }
  
  .search-container {
    padding: 10px 15px;
  }
  
  .user-item {
    padding: 10px 12px;
  }

  .user-avatar {
    width: 36px;
    height: 36px;
    margin-left: 10px;
  }

  .avatar-text {
    font-size: 13px;
  }

  .user-name {
    font-size: 13px;
    margin-left: 6px;
  }

  .user-username {
    font-size: 10px;
  }
}
