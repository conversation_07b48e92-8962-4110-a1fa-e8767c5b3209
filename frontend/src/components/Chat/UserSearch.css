/* تصميم مكون البحث عن المستخدمين - متوافق مع تصميم النظام */

.user-search {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--color-card-bg);
  border-radius: 0;
  overflow: hidden;
}

/* رأس البحث */
.search-header {
  padding: 20px;
  background: var(--color-card-bg);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
  color: var(--color-text-primary);
}

.header-icon {
  font-size: 20px;
  color: #0284c7;
}

.header-title h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.close-search-btn {
  width: 36px;
  height: 36px;
  border: 1px solid var(--color-border);
  border-radius: 50%;
  background: var(--color-card-bg);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-search-btn:hover {
  background: var(--color-bg-secondary);
  color: var(--color-text-primary);
  border-color: var(--color-input-border);
}

/* حقل البحث */
.search-input-container {
  padding: 15px 20px;
  background: var(--color-bg-secondary);
  border-bottom: 1px solid var(--color-border);
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  right: 15px;
  color: var(--color-text-secondary);
  font-size: 14px;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 12px 45px 12px 15px;
  border: 1px solid var(--color-input-border);
  border-radius: 0.5rem;
  background: var(--color-input-bg);
  color: var(--color-text-primary);
  font-size: 14px;
  outline: none;
  transition: all 0.2s ease;
}

.search-input::placeholder {
  color: var(--color-text-secondary);
}

.search-input:focus {
  border-color: #0284c7;
  box-shadow: 0 0 0 3px rgba(2, 132, 199, 0.1);
}

.search-loading {
  position: absolute;
  left: 15px;
  color: var(--color-text-secondary);
}

.spinner {
  animation: spin 1s linear infinite;
}

/* النتائج */
.search-results {
  flex: 1;
  overflow-y: auto;
  padding: 10px 0;
}

.results-header {
  padding: 10px 20px;
  background: var(--color-bg-secondary);
  border-bottom: 1px solid var(--color-border);
}

.results-header h5 {
  margin: 0;
  color: var(--color-text-primary);
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* عنصر المستخدم في البحث */
.user-search-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 4px solid transparent;
  position: relative;
  border-bottom: 1px solid var(--color-border);
}

.user-search-item:hover {
  background: var(--color-bg-secondary);
  border-left-color: #0284c7;
  border-left-color: rgba(255, 255, 255, 0.5);
}

.user-search-item.selected {
  background: rgba(255, 255, 255, 0.2);
  border-left-color: #ffd700;
  transform: translateX(5px);
}

/* صورة المستخدم في البحث */
.user-search-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b6b, #feca57);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 15px;
  position: relative;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.user-search-avatar:hover {
  transform: scale(1.05);
}

.user-search-avatar.online {
  background: linear-gradient(135deg, #4CAF50, #45a049);
}

.user-search-avatar.offline {
  background: linear-gradient(135deg, #9e9e9e, #757575);
}

.avatar-text {
  color: white;
  font-weight: bold;
  font-size: 16px;
}

.status-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid white;
}

.status-indicator.online {
  background: #4CAF50;
  color: white;
  animation: pulse 2s infinite;
}

.status-indicator.offline {
  background: #9e9e9e;
  color: white;
}

.status-indicator svg {
  font-size: 8px;
}

/* معلومات المستخدم في البحث */
.user-search-info {
  flex: 1;
  min-width: 0;
}

.user-search-name {
  color: white;
  font-weight: 600;
  font-size: 16px;
  margin: 0 0 5px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-search-status {
  margin: 0 0 3px 0;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.user-search-username {
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
}

.status-online {
  color: #4CAF50;
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-offline {
  color: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  font-size: 8px;
  animation: pulse 2s infinite;
}

.status-icon {
  font-size: 12px;
}

/* أزرار الإجراءات في البحث */
.user-search-actions {
  display: flex;
  gap: 8px;
}

.start-chat-button {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.start-chat-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* حالات التحميل والخطأ */
.error-message {
  padding: 20px;
  text-align: center;
  color: white;
}

.retry-button {
  margin-top: 10px;
  padding: 8px 16px;
  border: none;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* حالة فارغة */
.no-results,
.no-online-users {
  padding: 40px 20px;
  text-align: center;
  color: white;
}

.no-results-icon,
.no-users-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.no-results h4,
.no-online-users h4 {
  margin: 0 0 10px 0;
  font-size: 18px;
  font-weight: 600;
}

.no-results p,
.no-online-users p {
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

/* نصائح البحث */
.search-tips {
  padding: 10px 20px;
  background: rgba(255, 255, 255, 0.05);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.search-tips small {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

/* الرسوم المتحركة */
@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.7; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* التجاوب */
@media (max-width: 768px) {
  .search-header {
    padding: 15px;
  }
  
  .header-title h4 {
    font-size: 16px;
  }
  
  .user-search-item {
    padding: 12px 15px;
  }
  
  .user-search-avatar {
    width: 42px;
    height: 42px;
    margin-left: 12px;
  }
  
  .avatar-text {
    font-size: 14px;
  }
  
  .user-search-name {
    font-size: 15px;
  }
  
  .user-search-status {
    font-size: 12px;
  }
}
