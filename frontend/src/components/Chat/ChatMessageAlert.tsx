import React, { useState, useEffect } from 'react';
import { FaTimes, FaReply, FaUser } from 'react-icons/fa';

interface ChatMessage {
  id: number;
  sender_id: number;
  receiver_id: number;
  content: string;
  message_type: string;
  status: string;
  created_at: string;
  sender_username?: string;
  sender_full_name?: string;
}

interface ChatMessageAlertProps {
  message: ChatMessage;
  onReply: (senderId: number) => void;
  onDismiss: () => void;
  autoHide?: boolean;
  duration?: number;
}

// دالة لتوليد لون فريد للمستخدم
const getUserAvatarColor = (userId: number): string => {
  const colors = [
    '#0284c7', '#059669', '#7c3aed', '#dc2626', '#ea580c',
    '#0891b2', '#16a34a', '#9333ea', '#e11d48', '#f59e0b',
    '#0e7490', '#15803d', '#7c2d12', '#be185d', '#a16207'
  ];
  return colors[userId % colors.length];
};

// دالة لاقتطاع النص
const truncateText = (text: string, maxLength: number = 60): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

// دالة لتنسيق الوقت
const formatTime = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) return 'الآن';
  if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;

  // للتواريخ الأقدم من 24 ساعة، نعرض التاريخ بتنسيق بسيط
  return date.toLocaleDateString('ar-SA', {
    day: 'numeric',
    month: 'short',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const ChatMessageAlert: React.FC<ChatMessageAlertProps> = ({
  message,
  onReply,
  onDismiss,
  autoHide = false,
  duration = 8000
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isAnimating, setIsAnimating] = useState(false);

  // إخفاء تلقائي
  useEffect(() => {
    if (autoHide) {
      const timer = setTimeout(() => {
        handleDismiss();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [autoHide, duration]);

  const handleDismiss = () => {
    setIsAnimating(true);
    setTimeout(() => {
      setIsVisible(false);
      onDismiss();
    }, 300);
  };

  const handleReply = () => {
    onReply(message.sender_id);
    handleDismiss();
  };

  if (!isVisible) return null;

  const senderName = message.sender_full_name || message.sender_username || 'مستخدم';
  const avatarColor = getUserAvatarColor(message.sender_id);

  return (
    <>
      {/* Overlay */}
      <div
        className={`
          fixed inset-0 bg-black bg-opacity-50 z-40
          transition-opacity duration-300
          ${isAnimating ? 'opacity-0' : 'opacity-100'}
        `}
        onClick={handleDismiss}
      />

      {/* التنبيه */}
      <div
        className={`
          fixed top-1/2 left-1/2 z-50 max-w-sm w-full
          bg-white dark:bg-gray-800
          border border-gray-200 dark:border-gray-700
          rounded-lg shadow-xl
          transform transition-all duration-300 ease-in-out
          ${isAnimating ? 'scale-95 opacity-0' : 'scale-100 opacity-100'}
          -translate-x-1/2 -translate-y-1/2
        `}
        style={{ direction: 'rtl' }}
      >
      {/* هيدر التنبيه */}
      <div className="flex items-center justify-between p-3 border-b border-gray-100 dark:border-gray-700">
        <div className="flex items-center gap-2">
          <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
            <FaReply className="text-white text-xs" />
          </div>
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            رسالة جديدة
          </span>
        </div>
        
        <button
          onClick={handleDismiss}
          className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          title="إغلاق التنبيه"
        >
          <FaTimes className="w-3 h-3 text-gray-500 dark:text-gray-400" />
        </button>
      </div>

      {/* محتوى التنبيه */}
      <div className="p-4">
        {/* معلومات المرسل */}
        <div className="flex items-center gap-3 mb-3">
          <div
            className="w-10 h-10 rounded-full flex items-center justify-center text-white font-semibold text-sm"
            style={{ backgroundColor: avatarColor }}
          >
            {senderName.charAt(0).toUpperCase()}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <h4 className="font-medium text-gray-900 dark:text-gray-100 truncate">
                {senderName}
              </h4>
              <span className="text-xs text-gray-500 dark:text-gray-400 flex-shrink-0 mr-2">
                {formatTime(message.created_at)}
              </span>
            </div>
          </div>
        </div>

        {/* محتوى الرسالة */}
        <div className="mb-4">
          <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
            {truncateText(message.content)}
          </p>
        </div>

        {/* أزرار الإجراءات */}
        <div className="flex gap-2">
          <button
            onClick={handleReply}
            className="flex-1 bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium py-2 px-3 rounded-md transition-colors flex items-center justify-center gap-2"
          >
            <FaReply className="text-xs" />
            عرض الرسالة
          </button>
          
          <button
            onClick={handleDismiss}
            className="px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
          >
            إغلاق
          </button>
        </div>
      </div>

      {/* شريط التقدم للإخفاء التلقائي - يظهر فقط عند التفعيل */}
      {autoHide && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-100 dark:bg-gray-700 rounded-b-lg overflow-hidden">
          <div
            className="h-full bg-blue-500 transition-all ease-linear chat-progress-bar"
            style={{
              width: '100%',
              animationDuration: `${duration}ms`
            }}
          />
        </div>
      )}
      </div>
    </>
  );
};

export default ChatMessageAlert;
