/**
 * مكون قائمة المستخدمين المتصلين
 * يعرض المستخدمين المتاحين مع حالة الاتصال بتصميم عصري
 */

import React, { useState, useEffect } from 'react';
import {
  FaCircle,
  FaSearch,
  FaClock,
  FaUserFriends
} from 'react-icons/fa';
import { chatApiService, UserOnlineStatus } from '../../services/chatApiService';
import { formatDistanceToNow } from 'date-fns';
import { ar } from 'date-fns/locale';
import './OnlineUsersList.css';

interface OnlineUsersListProps {
  onUserSelect: (userId: number, userData?: UserOnlineStatus) => void;
  selectedUserId?: number | null;
  className?: string;
}

const OnlineUsersList: React.FC<OnlineUsersListProps> = ({
  onUserSelect,
  selectedUserId,
  className = ''
}) => {
  const [onlineUsers, setOnlineUsers] = useState<UserOnlineStatus[]>([]);
  const [allUsers, setAllUsers] = useState<UserOnlineStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  // جلب المستخدمين المتصلين
  const loadOnlineUsers = async () => {
    try {
      setLoading(true);
      const users = await chatApiService.getOnlineUsers();
      setOnlineUsers(users);
    } catch (err) {
      setError('فشل في جلب المستخدمين المتصلين');
      console.error('خطأ في جلب المستخدمين المتصلين:', err);
    } finally {
      setLoading(false);
    }
  };

  // البحث عن المستخدمين
  const searchUsers = async (query: string) => {
    if (!query.trim()) {
      setAllUsers([]);
      return;
    }

    try {
      const response = await chatApiService.searchUsers(query, 20);
      setAllUsers(response.users);
    } catch (err) {
      console.error('خطأ في البحث عن المستخدمين:', err);
    }
  };

  // تحميل المستخدمين عند التحميل الأولي
  useEffect(() => {
    loadOnlineUsers();

    // تحديث كل 30 ثانية
    const interval = setInterval(loadOnlineUsers, 30000);
    return () => clearInterval(interval);
  }, []);

  // الاستماع لتغييرات حالة المستخدم وتحديث القائمة فوراً
  useEffect(() => {
    const handleUserStatusUpdate = (data: any) => {
      setOnlineUsers(prev => prev.map(user =>
        user.user_id === data.user_id
          ? { ...user, is_online: data.is_online, last_seen: data.is_online ? undefined : new Date().toISOString() }
          : user
      ));

      setAllUsers(prev => prev.map(user =>
        user.user_id === data.user_id
          ? { ...user, is_online: data.is_online, last_seen: data.is_online ? undefined : new Date().toISOString() }
          : user
      ));
    };

    // استيراد خدمة WebSocket والاستماع للأحداث
    import('../../services/chatWebSocketService').then(({ chatWebSocketService }) => {
      chatWebSocketService.on('user_status_change', handleUserStatusUpdate);
    });

    // تنظيف المستمع عند إلغاء التحميل
    return () => {
      import('../../services/chatWebSocketService').then(({ chatWebSocketService }) => {
        chatWebSocketService.off('user_status_change', handleUserStatusUpdate);
      });
    };
  }, []);

  // البحث مع تأخير
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchQuery.trim()) {
        searchUsers(searchQuery);
      } else {
        setAllUsers([]);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  // تنسيق وقت آخر ظهور
  const formatLastSeen = (lastSeen: string) => {
    try {
      const date = new Date(lastSeen);
      return formatDistanceToNow(date, { 
        addSuffix: true, 
        locale: ar 
      });
    } catch {
      return 'غير معروف';
    }
  };

  // الحصول على الأحرف الأولى من الاسم
  const getInitials = (name: string) => {
    if (!name) return '👤';
    const words = name.trim().split(' ');
    if (words.length === 1) {
      return words[0].charAt(0).toUpperCase();
    }
    return (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
  };

  // دالة لتوليد لون فريد لكل مستخدم بناءً على معرفه
  const getUserAvatarColor = (userId: number) => {
    const colors = [
      '#0284c7', // أزرق
      '#059669', // أخضر
      '#7c3aed', // بنفسجي
      '#dc2626', // أحمر
      '#ea580c', // برتقالي
      '#0891b2', // سماوي
      '#65a30d', // أخضر فاتح
      '#c2410c', // برتقالي داكن
      '#9333ea', // بنفسجي فاتح
      '#be123c', // وردي داكن
      '#0d9488', // تركوازي
      '#4338ca', // أزرق داكن
    ];

    return colors[userId % colors.length];
  };

  // تصفية المستخدمين حسب البحث
  const getFilteredUsers = () => {
    const usersToShow = searchQuery.trim() ? allUsers : onlineUsers;
    return usersToShow;
  };

  const filteredUsers = getFilteredUsers();

  const handleUserClick = (user: UserOnlineStatus) => {
    onUserSelect(user.user_id, user);
  };

  if (loading && onlineUsers.length === 0) {
    return (
      <div className={`online-users-list ${className}`}>
        <div className="online-users-loading">
          <div className="loading-spinner">
            <div className="spinner"></div>
          </div>
          <p>جاري تحميل المستخدمين...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`online-users-list ${className}`}>
      {/* رأس القائمة */}
      <div className="online-users-header">
        <div className="header-title">
          <FaUserFriends className="header-icon" />
          <h3>المستخدمون المتاحون</h3>
        </div>
        
        <div className="users-count">
          <FaCircle className="online-indicator" />
          <span>{onlineUsers.length} متصل</span>
        </div>
      </div>

      {/* شريط البحث */}
      <div className="search-container">
        <div className="search-input-wrapper">
          <FaSearch className="search-icon" />
          <input
            type="text"
            placeholder="ابحث عن مستخدم..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="search-input"
          />
        </div>
      </div>



      {/* قائمة المستخدمين */}
      <div className="users-container custom-scrollbar-auto">
        {error && (
          <div className="error-message">
            <p>{error}</p>
            <button onClick={loadOnlineUsers} className="retry-button">
              إعادة المحاولة
            </button>
          </div>
        )}

        {filteredUsers.length === 0 && !loading && !error && (
          <div className="empty-state">
            <div className="empty-icon">
              {searchQuery ? <FaSearch /> : <FaUserFriends />}
            </div>
            <h4>
              {searchQuery
                ? 'لا توجد نتائج'
                : 'لا يوجد مستخدمون متصلون'
              }
            </h4>
            <p className="empty-message">
              {searchQuery
                ? 'جرب البحث بكلمات مختلفة'
                : 'سيظهر المستخدمون هنا عند اتصالهم'
              }
            </p>
          </div>
        )}

        {filteredUsers.map((user) => (
          <div
            key={user.user_id}
            className={`user-item ${selectedUserId === user.user_id ? 'selected' : ''}`}
            onClick={() => handleUserClick(user)}
          >
            {/* صورة المستخدم */}
            <div
              className={`user-avatar ${user.is_online ? 'online' : 'offline'}`}
              style={{
                backgroundColor: getUserAvatarColor(user.user_id),
                background: getUserAvatarColor(user.user_id)
              }}
            >
              <span className="avatar-text">
                {getInitials(user.full_name || user.username)}
              </span>
              <div className={`status-indicator ${user.is_online ? 'online' : 'offline'}`}>
                <FaCircle />
              </div>
            </div>

            {/* معلومات المستخدم */}
            <div className="user-info">
              <h4 className="user-name">
                {user.full_name || user.username}
              </h4>
              {!user.is_online && (
                <p className="user-status">
                  <FaClock className="status-icon" />
                  آخر ظهور {user.last_seen ? formatLastSeen(user.last_seen) : 'غير معروف'}
                </p>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* معلومات إضافية */}
      <div className="users-info">
        <small>
         يمكنك البحث بالاسم أو اسم المستخدم
        </small>
      </div>
    </div>
  );
};

export default OnlineUsersList;
