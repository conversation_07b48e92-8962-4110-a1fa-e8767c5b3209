/**
 * نافذة المحادثة الرئيسية
 * تعرض قائمة المحادثات والرسائل
 */

import React, { useState, useEffect, useMemo } from 'react';
import { useAuthStore } from '../../stores/authStore';
import { useChat } from '../../hooks/useChat';
import { useConversationsPagination } from '../../hooks/useConversationsPagination';
import { chatApiService } from '../../services/chatApiService';
import { chatNotificationService } from '../../services/chatNotificationService';
import ConversationsList from './ConversationsList.tsx';
import MessagesList from './MessagesList.tsx';
import MessageInput from './MessageInput.tsx';
import OnlineUsersList from './OnlineUsersList.tsx';
import AllUsersList from './AllUsersList.tsx';
import DeleteConversationModal from './DeleteConversationModal';
import {
  FaUsers,
  FaTimes,
  FaComments,
  FaUserFriends,
  FaCircle,
  FaClock,
  FaExclamationTriangle
} from 'react-icons/fa';
import { MessageCircle, Zap } from '../../components/ui/icons';
import { formatDistanceToNow } from 'date-fns';
import { ar } from 'date-fns/locale';
import './ChatWindow.css';
import '../../styles/scrollbar.css';

interface ChatWindowProps {
  isOpen: boolean;
  onClose: () => void;
  initialUserId?: number | null;
}

const ChatWindow: React.FC<ChatWindowProps> = ({ isOpen, onClose, initialUserId }) => {
  const { user } = useAuthStore();
  const [sidebarView, setSidebarView] = useState<'conversations' | 'online' | 'all'>('conversations');
  const [selectedUserData, setSelectedUserData] = useState<any>(null);
  const [deleteConversationModal, setDeleteConversationModal] = useState<{
    isOpen: boolean;
    userId: number | null;
    userName: string;
    isLoading: boolean;
  }>({
    isOpen: false,
    userId: null,
    userName: '',
    isLoading: false
  });

  const {
    isConnected,
    isConnecting,
    conversations,
    currentConversation,
    messages,
    typingUsers,
    loading,
    connect,
    disconnect,
    loadConversations,
    loadMessages,
    loadOlderMessages,
    getMessagesLoadingState,
    sendMessage,
    setCurrentConversation,
    sendTypingNotification,
    sendStopTypingNotification,
    markAsRead,
    loadUnreadCount
  } = useChat({
    userId: user?.id || 0,
    autoConnect: isOpen
  });

  // التحميل التدريجي للمحادثات
  const {
    conversations: paginatedConversations,
    loading: conversationsLoading,
    loadingMore: conversationsLoadingMore,
    hasMore: conversationsHasMore,
    loadMoreConversations,
    refreshConversations
  } = useConversationsPagination({
    initialLimit: 20,
    loadMoreLimit: 20,
    autoLoad: isOpen && !!user?.id
  });

  // إدارة الاتصال عند فتح/إغلاق النافذة
  useEffect(() => {
    if (isOpen && user?.id && !isConnected && !isConnecting) {
      connect();
    } else if (!isOpen && isConnected) {
      disconnect();
    }
  }, [isOpen, user?.id, isConnected, isConnecting, connect, disconnect]);

  // تحديث selectedUserData عند تغيير المحادثات
  useEffect(() => {
    if (currentConversation && conversations.length > 0) {
      const updatedUser = conversations.find(conv => conv.user_id === currentConversation);
      if (updatedUser) {
        // تحديث البيانات المحفوظة بالبيانات الجديدة من المحادثات
        setSelectedUserData((prev: any) => {
          if (!prev || prev.user_id !== currentConversation) {
            // إنشاء بيانات جديدة إذا لم تكن موجودة
            return {
              user_id: updatedUser.user_id,
              username: updatedUser.username,
              full_name: updatedUser.full_name,
              is_online: updatedUser.is_online,
              last_seen: updatedUser.last_seen,
              unread_count: updatedUser.unread_count || 0
            };
          } else {
            // تحديث البيانات الموجودة
            return {
              ...prev,
              is_online: updatedUser.is_online,
              last_seen: updatedUser.last_seen,
              full_name: updatedUser.full_name || prev.full_name,
              username: updatedUser.username || prev.username,
              unread_count: updatedUser.unread_count || prev.unread_count || 0
            };
          }
        });
      }
    }
  }, [conversations, currentConversation]);

  // تحديث selectedUserData عند تغيير المحادثة المختارة
  useEffect(() => {
    if (currentConversation) {
      // البحث عن بيانات المستخدم في المحادثات
      const userFromConversations = conversations.find(conv => conv.user_id === currentConversation);
      if (userFromConversations) {
        setSelectedUserData({
          user_id: userFromConversations.user_id,
          username: userFromConversations.username,
          full_name: userFromConversations.full_name,
          is_online: userFromConversations.is_online,
          last_seen: userFromConversations.last_seen,
          unread_count: userFromConversations.unread_count || 0
        });
      }
    }
  }, [currentConversation, conversations]);

  // الاستماع لتغييرات حالة المستخدم وتحديث selectedUserData
  useEffect(() => {
    if (!isOpen) return;

    const handleUserStatusUpdate = (data: any) => {
      // تحديث selectedUserData إذا كان المستخدم الحالي
      if (selectedUserData && selectedUserData.user_id === data.user_id) {
        setSelectedUserData((prev: any) => prev ? {
          ...prev,
          is_online: data.is_online,
          last_seen: data.is_online ? undefined : new Date().toISOString()
        } : null);
      }

      // إجبار إعادة تحديث المكون لضمان انعكاس التغييرات في الهيدر
      if (currentConversation === data.user_id) {
        // تحديث فوري للبيانات المحلية
        setSelectedUserData((prev: any) => {
          if (prev && prev.user_id === data.user_id) {
            return {
              ...prev,
              is_online: data.is_online,
              last_seen: data.is_online ? undefined : new Date().toISOString()
            };
          }
          return prev;
        });
      }
    };

    // استيراد خدمة WebSocket والاستماع للأحداث
    import('../../services/chatWebSocketService').then(({ chatWebSocketService }) => {
      chatWebSocketService.on('user_status_change', handleUserStatusUpdate);
    });

    // تنظيف المستمع عند إلغاء التحميل
    return () => {
      import('../../services/chatWebSocketService').then(({ chatWebSocketService }) => {
        chatWebSocketService.off('user_status_change', handleUserStatusUpdate);
      });
    };
  }, [selectedUserData, isOpen]);

  // جلب المحادثات عند فتح النافذة وتحديث العدد
  useEffect(() => {
    if (isOpen && user?.id) {
      loadConversations();

      // تحديث عدد الرسائل غير المقروءة فوراً عند فتح النافذة
      setTimeout(() => {
        loadUnreadCount();
      }, 100);
    }
  }, [isOpen, user?.id, loadConversations, loadUnreadCount]);

  // تتبع حالة نافذة المحادثة لخدمة التنبيهات
  useEffect(() => {
    chatNotificationService.setChatWindowState(isOpen, currentConversation);

    // تنظيف الحالة عند إغلاق النافذة
    return () => {
      if (!isOpen) {
        chatNotificationService.setChatWindowState(false, null);
      }
    };
  }, [isOpen, currentConversation]);

  // فتح المحادثة مع المستخدم المحدد عند تمرير initialUserId
  useEffect(() => {
    if (isOpen && initialUserId && initialUserId !== currentConversation) {
      handleUserSelect(initialUserId);
    }
  }, [isOpen, initialUserId, currentConversation]);

  // جلب الرسائل عند تحديد محادثة وتحديد الرسائل كمقروءة
  useEffect(() => {
    if (currentConversation && user?.id) {
      // التحميل الأولي مع حد أقصى 20 رسالة
      loadMessages(currentConversation, 1, 20);

      // تحديد الرسائل كمقروءة فوراً عند فتح المحادثة
      markAsRead(currentConversation);

      // تحديث إضافي للعدد الإجمالي
      setTimeout(() => {
        loadUnreadCount();
      }, 200);
    }
  }, [currentConversation, user?.id, loadMessages, markAsRead, loadUnreadCount]);

  const handleConversationSelect = (userId: number) => {
    setCurrentConversation(userId);
    setSidebarView('conversations');

    // تحديد الرسائل كمقروءة فوراً عند اختيار المحادثة
    markAsRead(userId);
  };

  const handleUserSelect = (userId: number, userData?: any) => {
    setCurrentConversation(userId);
    setSidebarView('conversations');

    // حفظ بيانات المستخدم إذا تم تمريرها
    if (userData) {
      setSelectedUserData(userData);
    }

    // تحديث المحادثات لضمان ظهور المحادثة الجديدة إذا لم تكن موجودة
    setTimeout(() => {
      loadConversations();
      refreshConversations();
    }, 100);

    // تحديد الرسائل كمقروءة فوراً عند اختيار المستخدم
    markAsRead(userId);
  };

  const handleShowOnlineUsers = () => {
    setSidebarView('online');
  };

  const handleShowAllUsers = () => {
    setSidebarView('all');
  };

  // دالة لتوليد لون فريد لكل مستخدم بناءً على معرفه
  const getUserAvatarColor = (userId: number) => {
    const colors = [
      '#0284c7', // أزرق
      '#059669', // أخضر
      '#7c3aed', // بنفسجي
      '#dc2626', // أحمر
      '#ea580c', // برتقالي
      '#0891b2', // سماوي
      '#65a30d', // أخضر فاتح
      '#c2410c', // برتقالي داكن
      '#9333ea', // بنفسجي فاتح
      '#be123c', // وردي داكن
      '#0d9488', // تركوازي
      '#4338ca', // أزرق داكن
    ];

    return colors[userId % colors.length];
  };

  const handleDeleteConversation = (userId: number, userName: string) => {
    setDeleteConversationModal({
      isOpen: true,
      userId,
      userName,
      isLoading: false
    });
  };

  const confirmDeleteConversation = async () => {
    if (!deleteConversationModal.userId) return;

    setDeleteConversationModal(prev => ({ ...prev, isLoading: true }));

    try {
      await chatApiService.deleteConversation(deleteConversationModal.userId);

      // إعادة تحميل المحادثات
      await loadConversations();
      refreshConversations();

      // إذا كانت المحادثة المحذوفة هي المحادثة الحالية، قم بإلغاء تحديدها
      if (currentConversation === deleteConversationModal.userId) {
        setCurrentConversation(null);
      }

      // إغلاق المودال
      setDeleteConversationModal({
        isOpen: false,
        userId: null,
        userName: '',
        isLoading: false
      });

    } catch (error) {
      console.error('خطأ في حذف المحادثة:', error);
      setDeleteConversationModal(prev => ({ ...prev, isLoading: false }));
    }
  };

  const closeDeleteConversationModal = () => {
    setDeleteConversationModal({
      isOpen: false,
      userId: null,
      userName: '',
      isLoading: false
    });
  };

  const handleDeleteMessage = async (messageId: number) => {
    try {
      await chatApiService.deleteMessage(messageId);

      // إعادة تحميل الرسائل
      if (currentConversation) {
        await loadMessages(currentConversation);
      }

      // إعادة تحميل المحادثات لتحديث آخر رسالة
      await loadConversations();
      refreshConversations();

    } catch (error) {
      console.error('خطأ في حذف الرسالة:', error);
      alert('حدث خطأ أثناء حذف الرسالة. يرجى المحاولة مرة أخرى.');
    }
  };

  const handleEditMessage = async (messageId: number, newContent: string) => {
    try {
      await chatApiService.editMessage(messageId, newContent);

      // إعادة تحميل الرسائل
      if (currentConversation) {
        await loadMessages(currentConversation);
      }

      // إعادة تحميل المحادثات لتحديث آخر رسالة إذا لزم الأمر
      await loadConversations();
      refreshConversations();

    } catch (error) {
      console.error('خطأ في تعديل الرسالة:', error);
      alert('حدث خطأ أثناء تعديل الرسالة. يرجى المحاولة مرة أخرى.');
    }
  };

  const handleShowConversations = () => {
    setSidebarView('conversations');
  };

  const handleSendMessage = async (content: string) => {
    if (currentConversation && content.trim()) {
      try {
        await sendMessage(currentConversation, content.trim());

        // تحديث إضافي للمحادثات لضمان ظهور المحادثة الجديدة فوراً
        setTimeout(() => {
          loadConversations();
          refreshConversations();
        }, 200);
      } catch (error) {
        console.error('فشل في إرسال الرسالة:', error);
      }
    }
  };

  const handleStartTyping = () => {
    if (currentConversation) {
      sendTypingNotification(currentConversation);
    }
  };

  const handleStopTyping = () => {
    if (currentConversation) {
      sendStopTypingNotification(currentConversation);
    }
  };

  const handleLoadOlderMessages = async () => {
    if (currentConversation) {
      try {
        await loadOlderMessages(currentConversation);
      } catch (error) {
        console.error('فشل في تحميل الرسائل الأقدم:', error);
      }
    }
  };

  const getCurrentConversationUser = () => {
    if (!currentConversation) return null;

    // البحث في المحادثات الأصلية أولاً (أحدث البيانات)
    const conversationUser = conversations.find(conv => conv.user_id === currentConversation);
    if (conversationUser) {
      // دمج مع البيانات المحفوظة إذا كانت متوفرة
      if (selectedUserData && selectedUserData.user_id === currentConversation) {
        return {
          ...conversationUser,
          // استخدام البيانات المحدثة من selectedUserData إذا كانت أحدث
          is_online: selectedUserData.is_online !== undefined ? selectedUserData.is_online : conversationUser.is_online,
          last_seen: selectedUserData.last_seen !== undefined ? selectedUserData.last_seen : conversationUser.last_seen
        };
      }
      return conversationUser;
    }

    // البحث في المحادثات المقسمة كبديل
    const paginatedUser = paginatedConversations.find(conv => conv.user_id === currentConversation);
    if (paginatedUser) {
      // دمج مع البيانات المحفوظة إذا كانت متوفرة
      if (selectedUserData && selectedUserData.user_id === currentConversation) {
        return {
          ...paginatedUser,
          is_online: selectedUserData.is_online !== undefined ? selectedUserData.is_online : paginatedUser.is_online,
          last_seen: selectedUserData.last_seen !== undefined ? selectedUserData.last_seen : paginatedUser.last_seen
        };
      }
      return paginatedUser;
    }

    // إذا لم توجد في المحادثات، استخدم البيانات المحفوظة
    if (selectedUserData && selectedUserData.user_id === currentConversation) {
      return {
        user_id: selectedUserData.user_id,
        username: selectedUserData.username,
        full_name: selectedUserData.full_name,
        is_online: selectedUserData.is_online,
        last_seen: selectedUserData.last_seen,
        unread_count: 0
      };
    }

    return null;
  };

  const currentMessages = currentConversation ? messages[currentConversation] || [] : [];

  // استخدام useMemo لضمان إعادة حساب currentUser عند تغيير البيانات
  const currentUser = useMemo(() => {
    return getCurrentConversationUser();
  }, [currentConversation, conversations, paginatedConversations, selectedUserData]);

  const isTyping = currentConversation ? typingUsers[currentConversation] : false;

  if (!isOpen) return null;

  return (
    <div className="chat-window-overlay">
      <div className="chat-window">
        {/* رأس النافذة */}
        <div className={`chat-header ${!isConnected ? 'disconnected' : isConnecting ? 'connecting' : ''}`}>
          <div className="chat-header-content">
            <h3 className="chat-title">
              <MessageCircle className="chat-title-icon" size={20} />
              {!isConnected ? (
                <>
                  <FaExclamationTriangle className="connection-warning-icon" />
                  انقطع الاتصال
                </>
              ) : isConnecting ? (
                <>
                  <Zap className="connecting-icon" size={16} />
                  جاري الاتصال...
                </>
              ) : (
                'المحادثة الفورية'
              )}
            </h3>
          </div>
          <div className="chat-header-actions">
            <button
              className={`nav-btn ${sidebarView === 'conversations' ? 'active' : ''}`}
              onClick={handleShowConversations}
              title="المحادثات"
            >
              <FaComments />
            </button>
            <button
              className={`nav-btn ${sidebarView === 'online' ? 'active' : ''}`}
              onClick={handleShowOnlineUsers}
              title="المستخدمون المتاحون"
            >
              <FaUserFriends />
            </button>
            <button
              className={`nav-btn ${sidebarView === 'all' ? 'active' : ''}`}
              onClick={handleShowAllUsers}
              title="عرض كل المستخدمين"
            >
              <FaUsers />
            </button>
            <button className="close-btn" onClick={onClose} title="إغلاق">
              <FaTimes />
            </button>
          </div>
        </div>

        {/* محتوى النافذة */}
        <div className="chat-content">
          {/* الشريط الجانبي */}
          <div className="chat-sidebar">
            {sidebarView === 'online' && (
              <OnlineUsersList
                onUserSelect={handleUserSelect}
                selectedUserId={currentConversation}
              />
            )}
            {sidebarView === 'all' && (
              <AllUsersList
                onUserSelect={handleUserSelect}
                selectedUserId={currentConversation}
              />
            )}
            {sidebarView === 'conversations' && (
              <ConversationsList
                conversations={paginatedConversations}
                selectedConversation={currentConversation}
                onConversationSelect={handleConversationSelect}
                onDeleteConversation={handleDeleteConversation}
                loading={conversationsLoading}
                loadingMore={conversationsLoadingMore}
                hasMore={conversationsHasMore}
                onLoadMore={loadMoreConversations}
              />
            )}
          </div>

          {/* منطقة المحادثة */}
          <div className="chat-main">
            {currentConversation ? (
              <>
                {/* رأس المحادثة */}
                <div className="conversation-header">
                  <div className="user-info">
                    <div
                      className="user-avatar"
                      key={`avatar-${currentUser?.user_id || 'default'}`}
                      style={{
                        backgroundColor: currentUser?.user_id ? getUserAvatarColor(currentUser.user_id) : '#0284c7',
                        background: currentUser?.user_id ? getUserAvatarColor(currentUser.user_id) : '#0284c7'
                      }}
                    >
                      <span className="avatar-text">
                        {currentUser?.full_name?.charAt(0) || currentUser?.username?.charAt(0) || 'U'}
                      </span>
                      <div className={`status-indicator ${currentUser?.is_online ? 'online' : 'offline'}`}>
                        <FaCircle />
                      </div>
                    </div>
                    <div className="user-details">
                      <div className="user-name-section">
                        <h4 className="user-full-name">
                          {currentUser?.full_name || currentUser?.username}
                          {isTyping && (
                            <span className="typing-indicator-header">
                              يكتب الآن
                            </span>
                          )}
                        </h4>
                        <span className="user-username">
                          @{currentUser?.username}
                        </span>
                      </div>
                      <div className="user-status-section">
                        {currentUser?.is_online ? (
                          <span className="user-status online">
                            <FaCircle className="status-icon" />
                            متصل الآن
                          </span>
                        ) : (
                          <span className="user-status offline">
                            <FaClock className="status-icon" />
                            آخر ظهور {currentUser?.last_seen ?
                              formatDistanceToNow(new Date(currentUser.last_seen), {
                                addSuffix: true,
                                locale: ar
                              }) : 'غير معروف'
                            }
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* قائمة الرسائل */}
                <MessagesList
                  messages={currentMessages}
                  currentUserId={user?.id ? parseInt(user.id.toString()) : 0}
                  loading={currentConversation ? getMessagesLoadingState(currentConversation).loading : false}
                  hasMore={currentConversation ? getMessagesLoadingState(currentConversation).hasMore : false}
                  onLoadMore={handleLoadOlderMessages}
                  onDeleteMessage={handleDeleteMessage}
                  onEditMessage={handleEditMessage}
                  conversations={conversations}
                  onMarkAsRead={markAsRead}
                  otherUserId={currentConversation || undefined}
                />

                {/* إدخال الرسالة */}
                <MessageInput
                  onSendMessage={handleSendMessage}
                  onStartTyping={handleStartTyping}
                  onStopTyping={handleStopTyping}
                  disabled={!isConnected}
                  isConnected={isConnected}
                  isConnecting={isConnecting}
                  placeholder={
                    isConnected
                      ? `اكتب رسالة إلى ${currentUser?.full_name || currentUser?.username}...`
                      : 'غير متصل...'
                  }
                />
              </>
            ) : (
              <div className="no-conversation">
                <div className="no-conversation-content">
                  <div className="no-conversation-icon">
                    <FaComments />
                  </div>
                  <h3>اختر محادثة للبدء</h3>
                  <p>
                    اختر محادثة من القائمة أو ابحث عن مستخدم جديد
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* مودال تأكيد حذف المحادثة */}
      <DeleteConversationModal
        isOpen={deleteConversationModal.isOpen}
        onClose={closeDeleteConversationModal}
        onConfirm={confirmDeleteConversation}
        userName={deleteConversationModal.userName}
        isLoading={deleteConversationModal.isLoading}
      />
    </div>
  );
};

export default ChatWindow;
