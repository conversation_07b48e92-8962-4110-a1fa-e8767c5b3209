/**
 * مكون قائمة المحادثات
 * يعرض جميع المحادثات مع آخر رسالة وعدد الرسائل غير المقروءة
 */

import React, { useState, useEffect, useRef } from 'react';
import { Conversation } from '../../services/chatApiService';
import { formatDistanceToNow } from 'date-fns';
import { ar } from 'date-fns/locale';
import { FaComments, FaImage, FaPaperclip, FaUser, FaSpinner, FaTrash } from 'react-icons/fa';

interface ConversationsListProps {
  conversations: Conversation[];
  selectedConversation: number | null;
  onConversationSelect: (userId: number) => void;
  onDeleteConversation?: (userId: number, userName: string) => void;
  loading: boolean;
  loadingMore?: boolean;
  hasMore?: boolean;
  onLoadMore?: () => void;
}

const ConversationsList: React.FC<ConversationsListProps> = ({
  conversations,
  selectedConversation,
  onConversationSelect,
  onDeleteConversation,
  loading,
  loadingMore = false,
  hasMore = false,
  onLoadMore
}) => {
  const [hoveredConversation, setHoveredConversation] = useState<number | null>(null);
  const [updatedConversations, setUpdatedConversations] = useState<Conversation[]>(conversations);
  const conversationsContainerRef = useRef<HTMLDivElement>(null);

  // تحديث المحادثات المحلية عند تغيير props
  useEffect(() => {
    setUpdatedConversations(conversations);
  }, [conversations]);

  // دمج التحديثات المحلية مع المحادثات من props
  const getDisplayConversations = () => {
    // إنشاء خريطة للتحديثات المحلية
    const localUpdatesMap = new Map();
    updatedConversations.forEach(conv => {
      localUpdatesMap.set(conv.user_id, conv);
    });

    // دمج المحادثات من props مع التحديثات المحلية
    const mergedConversations = conversations.map(conv => {
      const localUpdate = localUpdatesMap.get(conv.user_id);
      if (localUpdate) {
        // استخدام التحديث المحلي إذا كان أحدث
        return {
          ...conv,
          ...localUpdate,
          // الحفاظ على البيانات الأساسية من props
          user_id: conv.user_id,
          username: conv.username,
          full_name: conv.full_name
        };
      }
      return conv;
    });

    // إضافة أي محادثات جديدة من التحديثات المحلية
    updatedConversations.forEach(localConv => {
      const existsInProps = conversations.some(conv => conv.user_id === localConv.user_id);
      if (!existsInProps) {
        mergedConversations.push(localConv);
      }
    });

    return mergedConversations;
  };

  const displayConversations = getDisplayConversations();

  // الاستماع لتغييرات حالة المستخدم وأحداث المحادثة وتحديث المحادثات فوراً
  useEffect(() => {
    const handleUserStatusUpdate = (data: any) => {
      setUpdatedConversations(prev => prev.map(conv =>
        conv.user_id === data.user_id
          ? { ...conv, is_online: data.is_online }
          : conv
      ));
    };

    const handleNewMessage = (data: any) => {
      const message = data.message || data;
      const senderId = message.sender_id;
      const receiverId = message.receiver_id;
      const currentUserId = getCurrentUserId();

      setUpdatedConversations(prev => {
        // تحديد المحادثة المناسبة (المرسل أو المستقبل)
        const conversationUserId = senderId === currentUserId ? receiverId : senderId;

        // البحث عن المحادثة الموجودة
        const existingConvIndex = prev.findIndex(conv => conv.user_id === conversationUserId);

        // إنشاء كائن رسالة جديد
        const newMessage = {
          id: message.id,
          sender_id: senderId,
          receiver_id: receiverId,
          content: message.content,
          message_type: message.message_type || 'text',
          status: message.status || 'sent',
          is_edited: false,
          created_at: message.created_at || message.timestamp,
          sender_username: message.sender_username,
          sender_full_name: message.sender_full_name
        };

        if (existingConvIndex !== -1) {
          // تحديث المحادثة الموجودة
          const updatedConversations = [...prev];
          updatedConversations[existingConvIndex] = {
            ...updatedConversations[existingConvIndex],
            last_message: newMessage,
            unread_count: senderId !== currentUserId ?
              updatedConversations[existingConvIndex].unread_count + 1 :
              updatedConversations[existingConvIndex].unread_count
          };
          return updatedConversations;
        } else {
          // إنشاء محادثة جديدة إذا لم تكن موجودة
          const newConversation = {
            user_id: conversationUserId,
            username: message.sender_username || `user_${conversationUserId}`,
            full_name: message.sender_full_name || message.sender_username || `User ${conversationUserId}`,
            is_online: false,
            last_seen: undefined,
            last_message: newMessage,
            unread_count: senderId !== currentUserId ? 1 : 0
          };

          return [newConversation, ...prev];
        }
      });
    };

    // دالة مساعدة للحصول على معرف المستخدم الحالي
    const getCurrentUserId = () => {
      // يمكن الحصول على معرف المستخدم من السياق أو localStorage
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      return user.id ? parseInt(user.id.toString()) : 0;
    };

    const handleMessagesRead = (data: any) => {
      setUpdatedConversations(prev => prev.map(conv =>
        conv.user_id === data.reader_id
          ? { ...conv, unread_count: 0 }
          : conv
      ));
    };

    const handleMessageEdited = (data: any) => {
      // تحديث آخر رسالة إذا تم تحرير الرسالة الأخيرة
      setUpdatedConversations(prev => prev.map(conv => {
        if ((conv.user_id === data.sender_id || conv.user_id === data.receiver_id) &&
            conv.last_message && conv.last_message.id === data.id) {
          const editedMessage = {
            ...conv.last_message,
            content: data.content,
            is_edited: true
          };

          return {
            ...conv,
            last_message: editedMessage
          };
        }
        return conv;
      }));
    };

    const handleMessageDeleted = (data: any) => {
      // تحديث آخر رسالة إذا تم حذف الرسالة الأخيرة
      setUpdatedConversations(prev => prev.map(conv => {
        if (conv.user_id === data.sender_id || conv.user_id === data.receiver_id) {
          if (data.was_last_message && conv.last_message) {
            // إنشاء رسالة محذوفة
            const deletedMessage = {
              ...conv.last_message,
              content: 'تم حذف الرسالة',
              created_at: data.timestamp
            };

            return {
              ...conv,
              last_message: deletedMessage
            };
          }
        }
        return conv;
      }));
    };

    // معالج خاص للرسائل المرسلة محلياً (من useChat)
    const handleLocalMessageSent = (message: any, receiverId: number) => {
      setUpdatedConversations(prev => {
        const existingConvIndex = prev.findIndex(conv => conv.user_id === receiverId);

        if (existingConvIndex !== -1) {
          // تحديث المحادثة الموجودة
          const updatedConversations = [...prev];
          updatedConversations[existingConvIndex] = {
            ...updatedConversations[existingConvIndex],
            last_message: message,
            // لا نزيد عدد الرسائل غير المقروءة للرسائل المرسلة
          };
          return updatedConversations;
        } else {
          // إنشاء محادثة جديدة للمستخدم المرسل إليه
          const newConversation = {
            user_id: receiverId,
            username: `user_${receiverId}`,
            full_name: `User ${receiverId}`,
            is_online: false,
            last_seen: undefined,
            last_message: message,
            unread_count: 0
          };

          return [newConversation, ...prev];
        }
      });
    };

    // إضافة المعالج للنافذة العامة للوصول إليه من useChat
    (window as any).updateConversationsList = handleLocalMessageSent;

    // استيراد خدمة WebSocket والاستماع للأحداث
    import('../../services/chatWebSocketService').then(({ chatWebSocketService }) => {
      chatWebSocketService.on('user_status_change', handleUserStatusUpdate);
      chatWebSocketService.on('new_message', handleNewMessage);
      chatWebSocketService.on('message_edited', handleMessageEdited);
      chatWebSocketService.on('messages_read', handleMessagesRead);
      chatWebSocketService.on('message_deleted', handleMessageDeleted);
    });

    // تنظيف المستمعين عند إلغاء التحميل
    return () => {
      // إزالة المعالج من النافذة العامة
      if ((window as any).updateConversationsList) {
        delete (window as any).updateConversationsList;
      }

      import('../../services/chatWebSocketService').then(({ chatWebSocketService }) => {
        chatWebSocketService.off('user_status_change', handleUserStatusUpdate);
        chatWebSocketService.off('new_message', handleNewMessage);
        chatWebSocketService.off('message_edited', handleMessageEdited);
        chatWebSocketService.off('messages_read', handleMessagesRead);
        chatWebSocketService.off('message_deleted', handleMessageDeleted);
      });
    };
  }, []);

  // مراقبة التمرير للتحميل التدريجي
  useEffect(() => {
    const container = conversationsContainerRef.current;
    if (!container || !onLoadMore) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

      // تحميل المزيد عند الاقتراب من النهاية (50px من الأسفل)
      if (distanceFromBottom <= 50 && hasMore && !loadingMore && !loading) {
        onLoadMore();
      }
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [hasMore, loadingMore, loading, onLoadMore]);

  const handleDeleteConversation = (e: React.MouseEvent, userId: number, userName: string) => {
    e.stopPropagation(); // منع تحديد المحادثة عند النقر على زر الحذف
    if (onDeleteConversation) {
      onDeleteConversation(userId, userName);
    }
  };

  const formatMessageTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, { 
        addSuffix: true, 
        locale: ar 
      });
    } catch {
      return '';
    }
  };

  const truncateMessage = (content: string, maxLength: number = 50) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  // دالة لتوليد لون فريد لكل مستخدم بناءً على معرفه
  const getUserAvatarColor = (userId: number) => {
    const colors = [
      '#0284c7', // أزرق
      '#059669', // أخضر
      '#7c3aed', // بنفسجي
      '#dc2626', // أحمر
      '#ea580c', // برتقالي
      '#0891b2', // سماوي
      '#65a30d', // أخضر فاتح
      '#c2410c', // برتقالي داكن
      '#9333ea', // بنفسجي فاتح
      '#be123c', // وردي داكن
      '#0d9488', // تركوازي
      '#4338ca', // أزرق داكن
    ];

    return colors[userId % colors.length];
  };

  const getMessagePreview = (conversation: Conversation) => {
    if (!conversation.last_message) {
      return 'لا توجد رسائل';
    }

    const { content, message_type, sender_username } = conversation.last_message;

    if (message_type === 'image') {
      return (
        <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          <FaImage style={{ fontSize: '0.8rem' }} />
          صورة
        </span>
      );
    } else if (message_type === 'file') {
      return (
        <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          <FaPaperclip style={{ fontSize: '0.8rem' }} />
          ملف
        </span>
      );
    }

    const prefix = sender_username ? `${sender_username}: ` : '';
    return prefix + truncateMessage(content);
  };

  if (loading && displayConversations.length === 0) {
    return (
      <div className="conversations-list loading">
        <div className="loading-spinner">
          <FaSpinner className="spinner" />
          <p>جاري تحميل المحادثات...</p>
        </div>
      </div>
    );
  }

  if (displayConversations.length === 0) {
    return (
      <div className="conversations-list empty">
        <div className="empty-state">
          <div className="empty-icon">
            <FaComments />
          </div>
          <h4>لا توجد محادثات</h4>
          <p>ابدأ محادثة جديدة بالبحث عن مستخدم</p>
        </div>
      </div>
    );
  }

  return (
    <div className="conversations-list">
      <div className="conversations-header">
        <h4>المحادثات ({displayConversations.length})</h4>
      </div>

      <div
        className="conversations-container custom-scrollbar-auto"
        ref={conversationsContainerRef}
      >
        {displayConversations.map((conversation) => (
          <div
            key={conversation.user_id}
            className={`conversation-item ${
              selectedConversation === conversation.user_id ? 'selected' : ''
            }`}
            onClick={() => {
              // تحديث العدد فوراً عند النقر على المحادثة
              if (conversation.unread_count > 0) {
                setUpdatedConversations(prev => prev.map(conv =>
                  conv.user_id === conversation.user_id ? { ...conv, unread_count: 0 } : conv
                ));
              }
              onConversationSelect(conversation.user_id);
            }}
            onMouseEnter={() => setHoveredConversation(conversation.user_id)}
            onMouseLeave={() => setHoveredConversation(null)}
          >
            {/* صورة المستخدم */}
            <div className="conversation-avatar">
              <div
                className={`avatar ${conversation.is_online ? 'online' : 'offline'}`}
                style={{
                  backgroundColor: getUserAvatarColor(conversation.user_id),
                  background: getUserAvatarColor(conversation.user_id)
                }}
              >
                {conversation.full_name?.charAt(0) || conversation.username?.charAt(0) || <FaUser />}
                {conversation.is_online && <div className="online-indicator"></div>}
              </div>
            </div>

            {/* معلومات المحادثة */}
            <div className="conversation-content">
              <div className="conversation-header-info">
                <h5 className="conversation-name">
                  {conversation.full_name || conversation.username}
                </h5>
                <div className="conversation-time-status">
                  <span className="conversation-time">
                    {conversation.last_message && formatMessageTime(conversation.last_message.created_at)}
                  </span>
                  {/* عدد الرسائل غير المقروءة في الأعلى */}
                  {conversation.unread_count > 0 && (
                    <div className="unread-badge">
                      {conversation.unread_count > 99 ? '99+' : conversation.unread_count}
                    </div>
                  )}
                </div>
              </div>

              <div className="conversation-preview">
                <p className="last-message">
                  {getMessagePreview(conversation)}
                </p>

                {/* حالة الرسالة في الأسفل */}
                {conversation.last_message && (
                  <div className="message-status">
                    {conversation.last_message.status === 'sent' && (
                      <span className="status-sent">✓</span>
                    )}
                    {conversation.last_message.status === 'delivered' && (
                      <span className="status-delivered">✓✓</span>
                    )}
                    {conversation.last_message.status === 'read' && (
                      <span className="status-read">✓✓</span>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* زر حذف المحادثة */}
            {hoveredConversation === conversation.user_id && onDeleteConversation && (
              <div className="conversation-actions">
                <button
                  className="delete-conversation-btn"
                  onClick={(e) => handleDeleteConversation(
                    e,
                    conversation.user_id,
                    conversation.full_name || conversation.username
                  )}
                  title="حذف المحادثة"
                >
                  <FaTrash />
                </button>
              </div>
            )}
          </div>
        ))}

        {/* مؤشر التحميل الإضافي */}
        {loadingMore && (
          <div className="loading-more-indicator">
            <FaSpinner className="spinner" />
            <span>جاري تحميل المزيد من المحادثات...</span>
          </div>
        )}

        {/* رسالة عدم وجود المزيد */}
        {!hasMore && conversations.length > 0 && (
          <div className="no-more-conversations">
            <span>لا توجد محادثات أخرى</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default ConversationsList;
