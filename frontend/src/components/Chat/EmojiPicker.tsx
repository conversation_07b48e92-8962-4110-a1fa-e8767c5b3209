/**
 * منتقي الإيموجي للدردشة - إصدار محسن
 * يعرض قائمة بأيقونات السمايل مقسمة بفئات مع إمكانية البحث
 */

import React, { useState, useRef, useEffect } from 'react';
import { FaSearch, FaSmile, FaHeart, FaHandPaper, FaLeaf, FaUtensils, FaCar, FaFutbol, FaFlag } from 'react-icons/fa';

interface EmojiPickerProps {
  onEmojiSelect: (emoji: string) => void;
  onClose: () => void;
  isOpen: boolean;
  triggerRef?: React.RefObject<HTMLElement>;
}

// فئات الإيموجي مع أيقوناتها - محسنة
const emojiCategories = [
  {
    id: 'smileys',
    name: 'الوجوه والمشاعر',
    icon: FaSmile,
    emojis: [
      '😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂', '🙂', '🙃',
      '😉', '😊', '😇', '🥰', '😍', '🤩', '😘', '😗', '😚', '😙',
      '😋', '😛', '😜', '🤪', '😝', '🤑', '🤗', '🤭', '🤫', '🤔',
      '🤐', '🤨', '😐', '😑', '😶', '😏', '😒', '🙄', '😬', '🤥',
      '😔', '😪', '🤤', '😴', '😷', '🤒', '🤕', '🤢', '🤮', '🤧',
      '🥵', '🥶', '🥴', '😵', '🤯', '🤠', '🥳', '😎', '🤓', '🧐'
    ]
  },
  {
    id: 'hearts',
    name: 'القلوب والحب',
    icon: FaHeart,
    emojis: [
      '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
      '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '♥️',
      '💋', '💌', '💐', '🌹', '🌷', '🌺', '🌸', '🌼', '🌻', '💒'
    ]
  },
  {
    id: 'gestures',
    name: 'الإيماءات والأيدي',
    icon: FaHandPaper,
    emojis: [
      '👋', '🤚', '🖐️', '✋', '🖖', '👌', '🤌', '🤏', '✌️', '🤞',
      '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇', '☝️', '👍',
      '👎', '👊', '✊', '🤛', '🤜', '👏', '🙌', '👐', '🤲', '🤝',
      '🙏', '✍️', '💅', '🤳', '💪', '🦾', '🦿', '🦵', '🦶', '👂'
    ]
  },
  {
    id: 'nature',
    name: 'الطبيعة والحيوانات',
    icon: FaLeaf,
    emojis: [
      '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯',
      '🦁', '🐮', '🐷', '🐽', '🐸', '🐵', '🙈', '🙉', '🙊', '🐒',
      '🐔', '🐧', '🐦', '🐤', '🐣', '🐥', '🦆', '🦅', '🦉', '🦇',
      '🐺', '🐗', '🐴', '🦄', '🐝', '🐛', '🦋', '🐌', '🐞', '🐜'
    ]
  },
  {
    id: 'food',
    name: 'الطعام والشراب',
    icon: FaUtensils,
    emojis: [
      '🍎', '🍐', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🫐', '🍈',
      '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦',
      '🥬', '🥒', '🌶️', '🫑', '🌽', '🥕', '🫒', '🧄', '🧅', '🥔',
      '🍠', '🥐', '🥖', '🍞', '🥨', '🥯', '🧀', '🥚', '🍳', '🧈'
    ]
  },
  {
    id: 'travel',
    name: 'السفر والمواصلات',
    icon: FaCar,
    emojis: [
      '🚗', '🚕', '🚙', '🚌', '🚎', '🏎️', '🚓', '🚑', '🚒', '🚐',
      '🛻', '🚚', '🚛', '🚜', '🏍️', '🛵', '🚲', '🛴', '🛹', '🛼',
      '🚁', '🛸', '✈️', '🛩️', '🛫', '🛬', '🪂', '💺', '🚀', '🛰️',
      '🚢', '⛵', '🚤', '🛥️', '🛳️', '⛴️', '🚂', '🚃', '🚄', '🚅'
    ]
  },
  {
    id: 'sports',
    name: 'الرياضة والألعاب',
    icon: FaFutbol,
    emojis: [
      '⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱',
      '🪀', '🏓', '🏸', '🏒', '🏑', '🥍', '🏏', '🪃', '🥅', '⛳',
      '🪁', '🏹', '🎣', '🤿', '🥊', '🥋', '🎽', '🛹', '🛷', '⛸️',
      '🥌', '🎿', '⛷️', '🏂', '🪂', '🏋️', '🤸', '🤺', '⛹️', '🤾'
    ]
  },
  {
    id: 'symbols',
    name: 'الرموز والعلامات',
    icon: FaFlag,
    emojis: [
      '❤️', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔', '❣️',
      '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '♥️', '💯',
      '⭐', '🌟', '✨', '💫', '⚡', '🔥', '💥', '💢', '💨', '💦',
      '💤', '🕳️', '💣', '💬', '👁️‍🗨️', '🗨️', '🗯️', '💭', '💡', '🔔',
      '🔕', '📢', '📣', '📯', '🎵', '🎶', '🎼', '🎹', '🥁', '🎷'
    ]
  }
];

const EmojiPicker: React.FC<EmojiPickerProps> = ({ onEmojiSelect, onClose, isOpen, triggerRef }) => {
  const [activeCategory, setActiveCategory] = useState('smileys');
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredEmojis, setFilteredEmojis] = useState<string[]>([]);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const pickerRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // حساب موضع النافذة
  useEffect(() => {
    if (isOpen && triggerRef?.current) {
      const triggerElement = triggerRef.current;
      const rect = triggerElement.getBoundingClientRect();

      // حساب الموضع المناسب
      const windowWidth = window.innerWidth;
      const pickerWidth = 350;
      const pickerHeight = 420;

      // حساب الموضع بحيث تظهر النافذة في منتصف الأيقونة
      let top = rect.top - pickerHeight - 10; // فوق الزر
      let left = rect.left + (rect.width / 2) - (pickerWidth / 2); // وسط الأيقونة

      // التأكد من عدم خروج النافذة من حدود الشاشة
      if (top < 10) {
        top = rect.bottom + 10; // تحت الزر إذا لم تكن هناك مساحة فوق
      }

      if (left < 10) {
        left = 10;
      } else if (left + pickerWidth > windowWidth - 10) {
        left = windowWidth - pickerWidth - 10;
      }

      setPosition({ top, left });
    }
  }, [isOpen, triggerRef]);

  // إغلاق المنتقي عند النقر خارجه
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (pickerRef.current && !pickerRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);

      // التركيز على حقل البحث عند فتح المنتقي
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // تصفية الإيموجي حسب البحث
  useEffect(() => {
    if (searchTerm.trim()) {
      const allEmojis = emojiCategories.flatMap(category => category.emojis);
      setFilteredEmojis(allEmojis);
    } else {
      setFilteredEmojis([]);
    }
  }, [searchTerm]);

  const handleEmojiClick = (emoji: string) => {
    onEmojiSelect(emoji);
    onClose();
  };

  const getCurrentEmojis = () => {
    if (searchTerm.trim()) {
      return filteredEmojis;
    }
    const category = emojiCategories.find(cat => cat.id === activeCategory);
    return category ? category.emojis : [];
  };

  if (!isOpen) return null;

  return (
    <div
      className="emoji-picker"
      ref={pickerRef}
      style={{
        top: `${position.top}px`,
        left: `${position.left}px`
      }}
    >
      {/* شريط البحث */}
      <div className="emoji-search">
        <div className="search-input-wrapper">
          <FaSearch className="search-icon" />
          <input
            ref={searchInputRef}
            type="text"
            placeholder="ابحث عن إيموجي..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
      </div>

      {/* فئات الإيموجي */}
      {!searchTerm.trim() && (
        <div className="emoji-categories">
          {emojiCategories.map((category) => {
            const IconComponent = category.icon;
            return (
              <button
                key={category.id}
                className={`category-btn ${activeCategory === category.id ? 'active' : ''}`}
                onClick={() => setActiveCategory(category.id)}
                title={category.name}
              >
                <IconComponent />
              </button>
            );
          })}
        </div>
      )}

      {/* قائمة الإيموجي */}
      <div className="emoji-grid">
        {getCurrentEmojis().map((emoji, index) => (
          <button
            key={`${emoji}-${index}`}
            className="emoji-btn"
            onClick={() => handleEmojiClick(emoji)}
            title={emoji}
          >
            {emoji}
          </button>
        ))}
      </div>

      {/* رسالة عدم وجود نتائج */}
      {searchTerm.trim() && filteredEmojis.length === 0 && (
        <div className="no-results">
          <p>لا توجد إيموجي تطابق البحث</p>
        </div>
      )}
    </div>
  );
};

export default EmojiPicker;
