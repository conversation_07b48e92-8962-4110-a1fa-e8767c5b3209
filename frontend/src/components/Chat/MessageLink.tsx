/**
 * مكون لعرض الروابط في الرسائل
 */

import React from 'react';
import { FaExternalLinkAlt, FaGlobe } from 'react-icons/fa';
import { extractDomain, shortenUrl } from '../../utils/linkUtils';

interface MessageLinkProps {
  url: string;
  displayText: string;
  className?: string;
}

const MessageLink: React.FC<MessageLinkProps> = ({ url, displayText, className = '' }) => {
  const domain = extractDomain(url);
  const shortUrl = shortenUrl(displayText, 40);

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // فتح الرابط في نافذة جديدة
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  return (
    <span className={`message-link ${className}`}>
      <a
        href={url}
        onClick={handleClick}
        className="link-content"
        title={`فتح ${domain}`}
      >
        <FaGlobe className="link-icon" />
        <span className="link-text">{shortUrl}</span>
        <FaExternalLinkAlt className="external-icon" />
      </a>
    </span>
  );
};

export default MessageLink;
