/**
 * مكون معاينة الرابط التلقائية
 */

import React, { useState } from 'react';
import { FaExternalLinkAlt, FaImage, FaSpinner, FaExclamationTriangle, FaPlay, FaYoutube } from 'react-icons/fa';
import { useLinkPreview } from '../../services/linkPreviewService';

interface LinkPreviewProps {
  url: string;
  className?: string;
}

const LinkPreview: React.FC<LinkPreviewProps> = ({ url, className = '' }) => {
  const preview = useLinkPreview(url);
  const [imageError, setImageError] = useState(false);
  const [showVideoEmbed, setShowVideoEmbed] = useState(false);

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // إذا كان فيديو يوتيوب، عرض الفيديو المدمج
    if (preview.type === 'video' && preview.embedUrl && !showVideoEmbed) {
      setShowVideoEmbed(true);
      return;
    }

    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const handleImageError = () => {
    setImageError(true);
  };

  if (preview.isLoading) {
    return (
      <div className={`link-preview loading ${className}`}>
        <div className="preview-content">
          <div className="preview-loading">
            <FaSpinner className="loading-spinner" />
            <span>جاري تحميل المعاينة...</span>
          </div>
        </div>
      </div>
    );
  }

  if (preview.error && !preview.title) {
    return (
      <div className={`link-preview error ${className}`} onClick={handleClick}>
        <div className="preview-content">
          <div className="preview-error">
            <FaExclamationTriangle className="error-icon" />
            <div className="error-text">
              <div className="error-title">فشل في تحميل المعاينة</div>
              <div className="error-url">{preview.domain}</div>
            </div>
            <FaExternalLinkAlt className="external-icon" />
          </div>
        </div>
      </div>
    );
  }

  // دالة لعرض محتوى الصورة/الفيديو
  const renderMediaContent = () => {
    if (preview.type === 'video' && preview.videoId) {
      // إذا كان المستخدم اختار عرض الفيديو المدمج
      if (showVideoEmbed && preview.embedUrl) {
        return (
          <div className="preview-video-embed">
            <iframe
              src={preview.embedUrl}
              title={preview.title || 'فيديو'}
              style={{ border: 'none' }}
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
              width="100%"
              height="315"
            />
          </div>
        );
      }

      // عرض صورة مصغرة للفيديو مع زر التشغيل
      return (
        <div className="preview-video">
          <div className="video-thumbnail">
            <img
              src={preview.image}
              alt={preview.title || preview.domain}
              onError={handleImageError}
              loading="lazy"
            />
            <div className="video-overlay">
              <div className="play-button">
                <FaPlay />
              </div>
              <div className="video-platform">
                <FaYoutube />
              </div>
            </div>
          </div>
        </div>
      );
    }

    // عرض الصور لجميع أنواع المحتوى (ما عدا الفيديوهات التي لها معالجة خاصة)
    if (preview.image && !imageError && preview.type !== 'video') {
      return (
        <div className="preview-image">
          <img
            src={preview.image}
            alt={preview.title || preview.domain}
            onError={handleImageError}
            loading="lazy"
          />
          {preview.type === 'image' && (
            <div className="image-overlay">
              <FaImage />
            </div>
          )}
        </div>
      );
    }

    return null;
  };

  return (
    <div className={`link-preview ${preview.type || 'website'} ${showVideoEmbed ? 'embedded' : ''} ${className}`} onClick={handleClick}>
      <div className="preview-content">
        {/* محتوى الوسائط */}
        {renderMediaContent()}

        {/* محتوى المعاينة */}
        <div className="preview-info">
          <div className="preview-header">
            {preview.favicon && (
              <img
                src={preview.favicon}
                alt=""
                className="site-favicon"
                onError={(e) => {
                  (e.target as HTMLImageElement).style.display = 'none';
                }}
              />
            )}
            <span className="site-name">{preview.siteName || preview.domain}</span>
            <FaExternalLinkAlt className="external-icon" />
          </div>

          {preview.title && (
            <h4 className="preview-title">{preview.title}</h4>
          )}

          {preview.description && (
            <p className="preview-description">{preview.description}</p>
          )}

          <div className="preview-url">{preview.domain}</div>
        </div>
      </div>
    </div>
  );
};

export default LinkPreview;
