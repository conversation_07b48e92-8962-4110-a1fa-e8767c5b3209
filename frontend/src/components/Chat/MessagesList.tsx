/**
 * مكون قائمة الرسائل
 * يعرض الرسائل في المحادثة مع إمكانية التمرير والتحميل
 */

import React, { useEffect, useRef, useState } from 'react';
import { ChatMessage, Conversation } from '../../services/chatApiService';
import { format, isToday, isYesterday } from 'date-fns';
import { ar } from 'date-fns/locale';
import { FaEdit, FaTrash, FaEllipsisV, FaTimes, FaCheck, FaComments, FaChevronDown } from 'react-icons/fa';
import DeleteMessageModal from './DeleteMessageModal';
import MessageTextWithLinks from './MessageTextWithLinks';

interface MessagesListProps {
  messages: ChatMessage[];
  currentUserId: number;
  loading: boolean;
  onLoadMore?: () => void;
  hasMore?: boolean;
  onDeleteMessage?: (messageId: number) => void;
  onEditMessage?: (messageId: number, newContent: string) => void;
  conversations?: Conversation[]; // لجلب معلومات المستخدمين
  onMarkAsRead?: (otherUserId: number) => void; // لتحديد الرسائل كمقروءة
  otherUserId?: number; // معرف المستخدم الآخر في المحادثة
}

const MessagesList: React.FC<MessagesListProps> = ({
  messages,
  currentUserId,
  loading,
  onLoadMore,
  hasMore = false,
  onDeleteMessage,
  onEditMessage,
  conversations = [],
  onMarkAsRead,
  otherUserId
}) => {
  const [editingMessageId, setEditingMessageId] = useState<number | null>(null);
  const [editingContent, setEditingContent] = useState<string>('');
  const [contextMenuMessageId, setContextMenuMessageId] = useState<number | null>(null);
  const [localMessages, setLocalMessages] = useState<ChatMessage[]>(messages);
  const [deleteModal, setDeleteModal] = useState<{
    isOpen: boolean;
    messageId: number | null;
    messageContent: string;
    isLoading: boolean;
  }>({
    isOpen: false,
    messageId: null,
    messageContent: '',
    isLoading: false
  });
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // دالة للحصول على معلومات المستخدم
  const getUserInfo = (userId: number) => {
    return conversations.find(conv => conv.user_id === userId);
  };

  // دالة لتحديد ما إذا كان يجب إظهار معلومات المستخدم
  const shouldShowUserInfo = (message: ChatMessage, index: number) => {
    if (message.sender_id === currentUserId) return false; // لا نعرض معلومات للرسائل الخاصة

    const previousMessage = index > 0 ? localMessages[index - 1] : undefined;

    // إظهار معلومات المستخدم إذا:
    // 1. هذه أول رسالة
    // 2. الرسالة السابقة من مستخدم مختلف
    // 3. مر أكثر من 5 دقائق على الرسالة السابقة
    if (!previousMessage ||
        previousMessage.sender_id !== message.sender_id ||
        previousMessage.sender_id === currentUserId) {
      return true;
    }

    // فحص الوقت
    const currentTime = new Date(message.created_at).getTime();
    const previousTime = new Date(previousMessage.created_at).getTime();
    const timeDiff = currentTime - previousTime;
    const fiveMinutes = 5 * 60 * 1000; // 5 دقائق بالميلي ثانية

    return timeDiff > fiveMinutes;
  };

  // دالة لتوليد لون فريد لكل مستخدم بناءً على معرفه
  const getUserAvatarColor = (userId: number) => {
    const colors = [
      '#0284c7', // أزرق
      '#059669', // أخضر
      '#7c3aed', // بنفسجي
      '#dc2626', // أحمر
      '#ea580c', // برتقالي
      '#0891b2', // سماوي
      '#65a30d', // أخضر فاتح
      '#c2410c', // برتقالي داكن
      '#9333ea', // بنفسجي فاتح
      '#be123c', // وردي داكن
      '#0d9488', // تركوازي
      '#4338ca', // أزرق داكن
    ];

    return colors[userId % colors.length];
  };

  // دوال إدارة تعديل الرسائل
  const handleStartEdit = (message: ChatMessage) => {
    setEditingMessageId(message.id);
    setEditingContent(message.content);
    setContextMenuMessageId(null);
  };

  const handleCancelEdit = () => {
    setEditingMessageId(null);
    setEditingContent('');
  };

  const handleSaveEdit = () => {
    if (editingMessageId && editingContent.trim() && onEditMessage) {
      onEditMessage(editingMessageId, editingContent.trim());
      setEditingMessageId(null);
      setEditingContent('');
    }
  };

  const handleDeleteMessage = (messageId: number, messageContent: string) => {
    setDeleteModal({
      isOpen: true,
      messageId,
      messageContent,
      isLoading: false
    });
    setContextMenuMessageId(null);
  };

  const confirmDeleteMessage = async () => {
    if (!deleteModal.messageId || !onDeleteMessage) return;

    setDeleteModal(prev => ({ ...prev, isLoading: true }));

    try {
      onDeleteMessage?.(deleteModal.messageId);
      setDeleteModal({
        isOpen: false,
        messageId: null,
        messageContent: '',
        isLoading: false
      });
    } catch (error) {
      console.error('خطأ في حذف الرسالة:', error);
      setDeleteModal(prev => ({ ...prev, isLoading: false }));
    }
  };

  const closeDeleteModal = () => {
    setDeleteModal({
      isOpen: false,
      messageId: null,
      messageContent: '',
      isLoading: false
    });
  };

  const handleContextMenu = (e: React.MouseEvent, messageId: number) => {
    e.preventDefault();
    e.stopPropagation();

    // إغلاق أي قائمة مفتوحة أولاً، ثم فتح القائمة الجديدة
    if (contextMenuMessageId === messageId) {
      setContextMenuMessageId(null);
    } else {
      setContextMenuMessageId(messageId);

      // تحسين موضع القائمة بطريقة ذكية - محسن للقرب من الزر
      setTimeout(() => {
        const messageElement = (e.target as Element).closest('.message');
        const container = messagesContainerRef.current;

        if (messageElement && container) {
          const messageRect = messageElement.getBoundingClientRect();
          const containerRect = container.getBoundingClientRect();

          // تنظيف الـ classes السابقة
          messageElement.classList.remove('near-left-edge', 'near-right-edge', 'near-top', 'near-bottom');

          // فحص المساحة المتاحة - قيم محسنة للقائمة الأصغر
          const spaceLeft = messageRect.left - containerRect.left;
          const spaceRight = containerRect.right - messageRect.right;
          const spaceTop = messageRect.top - containerRect.top;
          const spaceBottom = containerRect.bottom - messageRect.bottom;

          // تحديد الموضع الأمثل - قيم أصغر للقائمة المضغوطة
          if (spaceLeft < 120) messageElement.classList.add('near-left-edge'); // قللنا من 180 إلى 120
          if (spaceRight < 120) messageElement.classList.add('near-right-edge'); // قللنا من 180 إلى 120
          if (spaceTop < 80) messageElement.classList.add('near-top'); // قللنا من 120 إلى 80
          if (spaceBottom < 80) messageElement.classList.add('near-bottom'); // قللنا من 120 إلى 80
        }
      }, 0);
    }
  };

  // إغلاق قائمة الخيارات
  const handleCloseContextMenu = () => {
    // تنظيف جميع الـ classes
    const messages = document.querySelectorAll('.message');
    messages.forEach(msg => {
      msg.classList.remove('near-left-edge', 'near-right-edge', 'near-top', 'near-bottom');
    });

    setContextMenuMessageId(null);
  };

  // إغلاق قائمة الخيارات عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (contextMenuMessageId !== null) {
        const target = event.target as Element;
        if (!target.closest('.message-context-menu') && !target.closest('.message-menu-btn')) {
          setContextMenuMessageId(null);
        }
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [contextMenuMessageId]);

  // التمرير إلى أسفل عند التحميل الأولي فقط
  useEffect(() => {
    if (localMessages.length > 0 && localMessages.length <= 20) {
      // التمرير فقط للتحميل الأولي
      scrollToBottom(false); // بدون animation للتحميل الأولي
    }
  }, [localMessages.length]);

  // مراقبة التمرير لتحميل المزيد من الرسائل
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const { scrollTop } = container;

      // تحميل المزيد من الرسائل عند الوصول للأعلى تماماً
      if (scrollTop <= 50 && hasMore && onLoadMore && !loading) {
        // حفظ الموضع الحالي قبل التحميل
        const currentScrollHeight = container.scrollHeight;
        const currentScrollTop = scrollTop;

        // استدعاء onLoadMore
        onLoadMore();

        // الحفاظ على الموضع بعد تحميل الرسائل الجديدة
        setTimeout(() => {
          if (container) {
            const newScrollHeight = container.scrollHeight;
            const heightDifference = newScrollHeight - currentScrollHeight;
            if (heightDifference > 0) {
              // تعديل موضع التمرير للحفاظ على الرسالة المرئية
              container.scrollTop = currentScrollTop + heightDifference;
            }
          }
        }, 150);
      }
    };

    container.addEventListener('scroll', handleScroll, { passive: true });
    return () => container.removeEventListener('scroll', handleScroll);
  }, [hasMore, onLoadMore, loading]);

  // تحديث الرسائل المحلية عند تغيير props
  useEffect(() => {
    setLocalMessages(messages);

    // فحص موضع التمرير عند تحديث الرسائل
    setTimeout(() => {
      const container = messagesContainerRef.current;
      if (container) {
        const { scrollTop, scrollHeight, clientHeight } = container;
        const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

        if (distanceFromBottom > 80) {
          setShowScrollToBottom(true);
        } else if (distanceFromBottom <= 30) {
          setShowScrollToBottom(false);
        }
      }
    }, 100);
  }, [messages]);

  // تحديد الرسائل كمقروءة عند عرض الرسائل
  useEffect(() => {
    if (messages.length > 0 && otherUserId && onMarkAsRead) {
      // تحديد الرسائل كمقروءة بعد تأخير قصير للتأكد من عرض الرسائل
      const timer = setTimeout(() => {
        onMarkAsRead(otherUserId);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [messages.length, otherUserId, onMarkAsRead]);

  // الاستماع لتغييرات حالة قراءة الرسائل
  useEffect(() => {
    const handleMessagesRead = (data: any) => {
      const readerId = data.reader_id;

      setLocalMessages(prev => prev.map(msg =>
        msg.receiver_id === readerId && msg.sender_id === currentUserId && msg.status !== 'read'
          ? { ...msg, status: 'read', read_at: data.timestamp }
          : msg
      ));
    };

    // استيراد خدمة WebSocket والاستماع للأحداث
    import('../../services/chatWebSocketService').then(({ chatWebSocketService }) => {
      chatWebSocketService.on('messages_read', handleMessagesRead);
    });

    // تنظيف المستمع عند إلغاء التحميل
    return () => {
      import('../../services/chatWebSocketService').then(({ chatWebSocketService }) => {
        chatWebSocketService.off('messages_read', handleMessagesRead);
      });
    };
  }, [currentUserId]);

  const scrollToBottom = (smooth: boolean = true) => {
    messagesEndRef.current?.scrollIntoView({ behavior: smooth ? 'smooth' : 'auto' });
  };

  // متابعة آخر رسالة لتحديد ما إذا كانت جديدة
  const lastMessageRef = useRef<number | null>(null);
  const userScrolledUpRef = useRef(false);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);

  // التمرير التلقائي للأسفل عند استقبال رسائل جديدة فقط
  useEffect(() => {
    if (messages.length > 0) {
      const container = messagesContainerRef.current;
      if (!container) return;

      const lastMessage = messages[messages.length - 1];
      const isNewMessage = lastMessageRef.current !== lastMessage?.id;

      if (isNewMessage) {
        lastMessageRef.current = lastMessage?.id || null;

        const { scrollTop, scrollHeight, clientHeight } = container;
        const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;

        // التمرير التلقائي في الحالات التالية:
        // 1. المستخدم بالقرب من الأسفل
        // 2. الرسالة الجديدة من المستخدم الحالي (رسالة مرسلة)
        const isOwnMessage = lastMessage?.sender_id === currentUserId;

        if ((isNearBottom && !userScrolledUpRef.current) || isOwnMessage) {
          setTimeout(() => {
            scrollToBottom(true);
            // إعادة تعيين حالة التمرير للأعلى عند إرسال رسالة جديدة
            if (isOwnMessage) {
              userScrolledUpRef.current = false;
              setShowScrollToBottom(false);
            }
          }, 50);
        }
      }
    }
  }, [messages, currentUserId]);

  // مراقبة تمرير المستخدم لتحديد ما إذا كان قد تمرر للأعلى يدوياً
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const handleUserScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

      // إظهار زر التمرير للأسفل عند البعد عن الأسفل بأكثر من 80px (قللنا الحد)
      if (distanceFromBottom > 80) {
        userScrolledUpRef.current = true;
        setShowScrollToBottom(true);
      } else if (distanceFromBottom <= 30) { // زيادة الحد للإخفاء لتجنب الوميض
        userScrolledUpRef.current = false;
        setShowScrollToBottom(false);
      }

      // يمكن إضافة تسجيل للتشخيص هنا إذا لزم الأمر
    };

    // إضافة throttling لتحسين الأداء
    let scrollTimeout: NodeJS.Timeout;
    const throttledScroll = () => {
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(handleUserScroll, 50); // تأخير 50ms
    };

    container.addEventListener('scroll', throttledScroll, { passive: true });

    // فحص أولي للموضع
    handleUserScroll();

    return () => {
      container.removeEventListener('scroll', throttledScroll);
      clearTimeout(scrollTimeout);
    };
  }, []);

  const formatMessageTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      
      if (isToday(date)) {
        return format(date, 'h:mm a', { locale: ar });
      } else if (isYesterday(date)) {
        return `أمس ${format(date, 'h:mm a', { locale: ar })}`;
      } else {
        // تنسيق مختصر للتاريخ مع الوقت: "07 يوليو 2025 3:35 ص"
        return format(date, 'dd MMMM yyyy h:mm a', { locale: ar });
      }
    } catch {
      return '';
    }
  };

  const formatDateSeparator = (dateString: string) => {
    try {
      const date = new Date(dateString);
      
      if (isToday(date)) {
        return 'اليوم';
      } else if (isYesterday(date)) {
        return 'أمس';
      } else {
        return format(date, 'EEEE، dd MMMM yyyy', { locale: ar });
      }
    } catch {
      return '';
    }
  };

  const shouldShowDateSeparator = (currentMessage: ChatMessage, previousMessage?: ChatMessage) => {
    if (!previousMessage) return true;
    
    const currentDate = new Date(currentMessage.created_at).toDateString();
    const previousDate = new Date(previousMessage.created_at).toDateString();
    
    return currentDate !== previousDate;
  };

  const getMessageStatus = (message: ChatMessage) => {
    if (message.sender_id !== currentUserId) return null;

    switch (message.status) {
      case 'sent':
        return <span className="message-status sent">✓</span>;
      case 'delivered':
        return <span className="message-status delivered">✓✓</span>;
      case 'read':
        return <span className="message-status read">✓✓</span>;
      default:
        return null;
    }
  };



  const renderMessage = (message: ChatMessage, index: number) => {
    const isOwnMessage = message.sender_id === currentUserId;
    const previousMessage = index > 0 ? localMessages[index - 1] : undefined;
    const showDateSeparator = shouldShowDateSeparator(message, previousMessage);
    const showUserInfo = shouldShowUserInfo(message, index);
    const userInfo = !isOwnMessage ? getUserInfo(message.sender_id) : null;

    return (
      <React.Fragment key={message.id}>
        {/* فاصل التاريخ */}
        {showDateSeparator && (
          <div className="date-separator">
            <span>{formatDateSeparator(message.created_at)}</span>
          </div>
        )}

        {/* معلومات المستخدم للرسائل من الآخرين */}
        {!isOwnMessage && showUserInfo && userInfo && (
          <div className="message-user-info">
            <div
              className="message-user-avatar"
              style={{
                backgroundColor: getUserAvatarColor(message.sender_id),
                background: getUserAvatarColor(message.sender_id)
              }}
            >
              <span className="avatar-text">
                {userInfo.full_name ? userInfo.full_name.charAt(0).toUpperCase() : userInfo.username.charAt(0).toUpperCase()}
              </span>
            </div>
            <span className="message-user-name">
              {userInfo.full_name || userInfo.username}
            </span>
          </div>
        )}

        {/* الرسالة */}
        <div
          className={`message ${isOwnMessage ? 'own' : 'other'} ${!isOwnMessage && showUserInfo ? 'with-user-info' : 'without-user-info'}`}
          onContextMenu={isOwnMessage ? (e) => handleContextMenu(e, message.id) : undefined}
        >
          <div className="message-content">
            <div className="message-bubble">
              {editingMessageId === message.id ? (
                // واجهة التعديل
                <div className="message-edit-form">
                  <textarea
                    value={editingContent}
                    onChange={(e) => setEditingContent(e.target.value)}
                    className="edit-textarea"
                    rows={3}
                    maxLength={5000}
                    autoFocus
                  />
                  <div className="edit-actions">
                    <button
                      className="edit-save-btn"
                      onClick={handleSaveEdit}
                      disabled={!editingContent.trim()}
                    >
                      <FaCheck /> حفظ
                    </button>
                    <button
                      className="edit-cancel-btn"
                      onClick={handleCancelEdit}
                    >
                      <FaTimes /> إلغاء
                    </button>
                  </div>
                </div>
              ) : (
                // عرض الرسالة العادي
                <>
                  {message.message_type === 'text' && (
                    <MessageTextWithLinks
                      text={message.content}
                      className="message-text message-content"
                    />
                  )}
                  {message.message_type === 'image' && (
                    <div className="message-image">
                      <img src={message.content} alt="صورة" />
                    </div>
                  )}
                  {message.message_type === 'file' && (
                    <div className="message-file">
                      <span>📎 {message.content}</span>
                    </div>
                  )}

                  {message.is_edited && (
                    <span className="edited-indicator">معدلة</span>
                  )}
                </>
              )}
            </div>

            {editingMessageId !== message.id && (
              <div className="message-meta">
                <span className="message-time">
                  {formatMessageTime(message.created_at)}
                </span>
                {getMessageStatus(message)}
              </div>
            )}

            {/* قائمة السياق للرسائل الخاصة */}
            {isOwnMessage && contextMenuMessageId === message.id && editingMessageId !== message.id && (
              <div className="message-context-menu">
                <button
                  className="context-menu-item edit"
                  onClick={() => handleStartEdit(message)}
                >
                  <FaEdit /> تعديل
                </button>
                <button
                  className="context-menu-item delete"
                  onClick={() => handleDeleteMessage(message.id, message.content)}
                >
                  <FaTrash /> حذف
                </button>
                <button
                  className="context-menu-item cancel"
                  onClick={handleCloseContextMenu}
                >
                  <FaTimes /> إلغاء
                </button>
              </div>
            )}

            {/* زر القائمة للرسائل الخاصة - مرئي دائماً */}
            {isOwnMessage && editingMessageId !== message.id && (
              <button
                className={`message-menu-btn ${contextMenuMessageId === message.id ? 'active' : ''}`}
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  handleContextMenu(e, message.id);
                }}
                title="خيارات الرسالة"
              >
                <FaEllipsisV />
              </button>
            )}
          </div>
        </div>
      </React.Fragment>
    );
  };



  if (loading && localMessages.length === 0) {
    return (
      <div className="messages-list loading">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>جاري تحميل الرسائل...</p>
        </div>
      </div>
    );
  }

  if (localMessages.length === 0) {
    return (
      <div className="messages-list empty">
        <div className="empty-state">
          <div className="empty-icon">
            <FaComments />
          </div>
          <h4>لا توجد رسائل</h4>
          <p>ابدأ المحادثة بإرسال رسالة</p>
        </div>
      </div>
    );
  }

  return (
    <div className="messages-list">
      <div
        className="messages-container custom-scrollbar-auto"
        ref={messagesContainerRef}
      >
        {/* مؤشر التحميل في الأعلى */}
        {loading && hasMore && (
          <div className="load-more-indicator">
            <div className="spinner small"></div>
            <span>جاري تحميل الرسائل الأقدم...</span>
          </div>
        )}

        {/* رسالة عدم وجود رسائل أقدم */}
        {!hasMore && localMessages.length >= 20 && (
          <div className="no-more-messages">
            <span>بداية المحادثة</span>
          </div>
        )}

        {/* رسالة التحميل الأولي */}
        {localMessages.length === 0 && !loading && (
          <div className="no-more-messages">
            <span>💬 ابدأ محادثة جديدة</span>
          </div>
        )}

        {/* الرسائل */}
        {localMessages.map((message, index) => renderMessage(message, index))}

        {/* مرجع نهاية الرسائل */}
        <div ref={messagesEndRef} />
      </div>

      {/* زر التمرير للأسفل - محسن */}
      <button
        className={`scroll-to-bottom-btn ${showScrollToBottom ? 'show' : 'hide'}`}
        onClick={() => scrollToBottom(true)}
        title="التمرير للأسفل"
        style={{
          display: 'flex', // دائماً موجود في DOM
          opacity: showScrollToBottom ? 0.9 : 0,
          visibility: showScrollToBottom ? 'visible' : 'hidden',
          pointerEvents: showScrollToBottom ? 'auto' : 'none'
        }}
      >
        <FaChevronDown />
      </button>

      {/* مودال تأكيد حذف الرسالة */}
      <DeleteMessageModal
        isOpen={deleteModal.isOpen}
        onClose={closeDeleteModal}
        onConfirm={confirmDeleteMessage}
        messageContent={deleteModal.messageContent}
        isLoading={deleteModal.isLoading}
      />
    </div>
  );
};

export default MessagesList;
