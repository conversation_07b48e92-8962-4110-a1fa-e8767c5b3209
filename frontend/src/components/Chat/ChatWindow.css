/* تصميم نافذة المحادثة الفورية - متوافق مع تصميم النظام */

/* الرسوم المتحركة */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.chat-window-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
  animation: fadeIn 0.3s ease-out;
}

.chat-window {
  background: var(--color-card-bg);
  border-radius: 1rem;
  box-shadow: var(--color-card-shadow);
  width: 100%;
  max-width: 1200px;
  height: 85vh;
  max-height: 700px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideUp 0.4s ease-out;
  border: 1px solid var(--color-border);
}

/* رأس النافذة */
.chat-header {
  background: var(--color-header-bg);
  color: var(--color-text-primary);
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--color-border);
}

.chat-header-content h3 {
  margin: 0;
  font-size: 1.2rem;
  color: var(--color-text-primary);
}

.chat-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chat-title-icon {
  color: var(--color-primary);
  flex-shrink: 0;
}

.chat-status {
  font-size: 0.9rem;
  margin-top: 5px;
  color: var(--color-text-secondary);
}

.status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
}

.status.connecting {
  background: rgba(251, 191, 36, 0.1);
  color: #f59e0b;
}

.status.connected {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.status.disconnected {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.status.error {
  background: rgba(239, 68, 68, 0.15);
  color: #ef4444;
}

.chat-header-actions {
  display: flex;
  gap: 8px;
}

.search-toggle-btn,
.close-btn {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  padding: 8px 12px;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
}

.search-toggle-btn:hover,
.close-btn:hover {
  background: var(--color-input-bg);
  color: var(--color-text-primary);
  border-color: var(--color-input-border);
}

/* محتوى النافذة */
.chat-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* الشريط الجانبي */
.chat-sidebar {
  width: 350px;
  background: var(--color-bg-secondary);
  display: flex;
  flex-direction: column;
  border-left: 1px solid var(--color-border);
}

/* منطقة المحادثة الرئيسية */
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--color-card-bg);
  overflow: visible; /* للسماح بظهور الأزرار خارج الحدود */
  position: relative;
}

/* رأس المحادثة - محسن مثل فيسبوك */
.conversation-header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-card-bg);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  /* لون احتياطي إذا لم يتم تطبيق اللون من JavaScript */
  background: #0284c7;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.95rem;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.9);
}

.user-avatar .avatar-text {
  z-index: 1;
}

.user-avatar .status-indicator {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  border: 2px solid var(--color-card-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
}

.user-avatar .status-indicator.online {
  background: #10b981;
  color: #10b981;
}

.user-avatar .status-indicator.offline {
  background: #6b7280;
  color: #6b7280;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name-section {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.typing-indicator-header {
  font-size: 0.75rem;
  color: var(--color-primary);
  font-weight: 500;
  margin-right: 8px;
  padding: 2px 8px;
  background: var(--color-primary-light);
  border-radius: 12px;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* دعم الوضع المظلم لمؤشر الكتابة */
[data-theme="dark"] .typing-indicator-header {
  background: rgba(var(--color-primary-rgb), 0.2);
  color: var(--color-primary-light);
}

.user-full-name {
  margin: 0;
  font-size: 0.95rem;
  color: var(--color-text-primary);
  font-weight: 600;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.3;
}

.user-username {
  font-size: 0.8rem;
  color: var(--color-text-secondary);
  font-weight: 400;
  flex-shrink: 0;
}

.user-status-section {
  display: flex;
  align-items: center;
}

.user-status {
  font-size: 0.85rem;
  color: var(--color-text-secondary);
  display: flex;
  align-items: center;
  gap: 4px;
}

.user-status.online {
  color: #10b981;
}

.user-status.offline {
  color: var(--color-text-secondary);
}

.user-status .status-icon {
  font-size: 0.7rem;
  flex-shrink: 0;
}

/* حالة عدم وجود محادثة */
.no-conversation {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-bg-secondary);
}

.no-conversation-content {
  text-align: center;
  color: var(--color-text-secondary);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.no-conversation-icon {
  font-size: 4rem;  /* يمكنك تعديل الحجم حسب الحاجة */
  margin-bottom: 20px;
  color: var(--color-text-secondary);
}

.no-conversation-content h3 {
  margin: 0 0 10px 0;
  color: var(--color-text-primary);
}

.no-conversation-content p {
  margin: 0 0 20px 0;
  max-width: 300px;
  color: var(--color-text-secondary);
}

.start-chat-btn {
  background: #0284c7;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.2s;
}

.start-chat-btn:hover {
  background: #0369a1;
  transform: translateY(-1px);
}

/* قائمة المحادثات */
.conversations-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.conversations-header {
  padding: 15px 20px;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-card-bg);
}

.conversations-header h4 {
  margin: 0;
  color: var(--color-text-primary);
  font-size: 1rem;
}

.conversations-container {
  flex: 1;
  overflow-y: auto;
}

.conversation-item {
  display: flex;
  align-items: center;
  padding: 8px 20px; /* ارتفاع أقل */
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid var(--color-border);
  position: relative;
  min-height: 56px; /* ارتفاع أدنى أقل */
}

.conversation-item:hover {
  background: var(--color-bg-secondary);
}

.conversation-item.selected {
  background: rgba(2, 132, 199, 0.08);
  border-right: 4px solid #0284c7;
  box-shadow: inset 0 0 0 1px rgba(2, 132, 199, 0.1);
}

.conversation-item.selected:hover {
  background: rgba(2, 132, 199, 0.12);
}

/* زر حذف المحادثة */
.conversation-actions {
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  gap: 4px;
}

.delete-conversation-btn {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 0.375rem;
  padding: 6px 8px;
  cursor: pointer;
  font-size: 0.75rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 28px;
  height: 28px;
}

.delete-conversation-btn:hover {
  background: #dc2626;
  transform: scale(1.05);
}

.delete-conversation-btn:active {
  transform: scale(0.95);
}

.conversation-avatar {
  margin-left: 12px;
}

.avatar {
  width: 42px; /* حجم أصغر */
  height: 42px;
  border-radius: 50%;
  background: #0284c7; /* سيتم تجاوزه بالألوان المتنوعة */
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.9rem; /* حجم نص أصغر */
  position: relative;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06); /* ظل أخف */
  transition: transform 0.2s ease;
}

/* إزالة تأثير تكبير الأيقونة عند المرور */

.avatar.offline {
  opacity: 0.85;
  filter: grayscale(10%);
}

.avatar.online {
  opacity: 1;
  filter: none;
}

.online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 14px;
  height: 14px;
  background: #10b981;
  border: 3px solid white;
  border-radius: 50%;
  box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.3);
  animation: pulse-online 2s infinite;
}

@keyframes pulse-online {
  0%, 100% {
    box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.3);
  }
  50% {
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
  }
}

.conversation-content {
  flex: 1;
  min-width: 0;
}

.conversation-header-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 4px; /* مسافة معتدلة */
}

.conversation-name {
  margin: 0;
  font-size: 1rem; /* حجم نص معتدل */
  color: var(--color-text-primary);
  font-weight: 600;
  line-height: 1.3;
}

.conversation-time-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.conversation-time {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  white-space: nowrap;
}

.conversation-preview {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: 1px;
}

.last-message {
  margin: 0;
  font-size: 0.85rem;
  color: var(--color-text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  line-height: 1.4;
  max-width: calc(100% - 60px); /* ترك مساحة للشارة */
}

.unread-badge {
  background: #ef4444;
  color: white;
  border-radius: 12px;
  padding: 2px 6px;
  font-size: 0.7rem;
  font-weight: 600;
  min-width: 18px;
  text-align: center;
  box-shadow: 0 2px 6px rgba(239, 68, 68, 0.3);
  flex-shrink: 0;
}

.message-status {
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 20px;
}

.status-sent {
  color: var(--color-text-secondary);
  font-weight: 500;
}

.status-delivered {
  color: #059669; /* أخضر للتسليم */
  font-weight: 500;
}

.status-read {
  color: #0284c7; /* أزرق للقراءة */
  font-weight: 600;
  text-shadow: 0 0 2px rgba(2, 132, 199, 0.3);
}

/* قائمة الرسائل */
.messages-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: visible; /* للسماح بظهور الأزرار خارج الحدود */
  padding: 5px 10px; /* مساحة موحدة على الجانبين للأزرار */
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.date-separator {
  text-align: center;
  margin: 20px 0;
}

.date-separator span {
  background: var(--color-bg-secondary);
  padding: 5px 15px;
  border-radius: 15px;
  font-size: 0.8rem;
  color: var(--color-text-secondary);
}

/* معلومات المستخدم للرسائل */
.message-user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  margin-right: 8px;
  margin-left: 45px; /* مساحة للأزرار */
}

.message-user-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  /* لون احتياطي إذا لم يتم تطبيق اللون من JavaScript */
  background: #0284c7;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.7rem;
  flex-shrink: 0;
}

.message-user-name {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--color-text-secondary);
  line-height: 1;
}

.message {
  display: flex;
  position: relative; /* مهم للموضع النسبي للأزرار */
  padding: 0 45px; /* مساحة موحدة على الجانبين للأزرار */
}

/* تباعد مختلف حسب وجود معلومات المستخدم */
.message.with-user-info {
  margin-bottom: 12px; /* تباعد عادي */
}

.message.without-user-info {
  margin-bottom: 4px; /* تباعد أقل للرسائل المتتالية */
}

.message.own {
  justify-content: flex-end;
}

.message.other {
  justify-content: flex-start;
}

.message-content {
  max-width: 75%; /* عرض متوازن للرسائل مع مساحة متساوية على الجانبين */
  min-width: 100px; /* حد أدنى للعرض */
  display: flex;
  flex-direction: column;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  overflow: hidden; /* يمنع تجاوز المحتوى */
}

.message.own .message-content {
  align-items: flex-end;
}

.message.other .message-content {
  align-items: flex-start;
}

.message-bubble {
  padding: 8px 12px;
  border-radius: 16px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  position: relative;
  border: none; /* إزالة الحدود مثل فيسبوك */
  box-sizing: border-box;
  max-width: 100%;
  min-width: 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

.message.own .message-bubble {
  background: linear-gradient(135deg, #0084ff, #0066cc);
  color: white;
  border-bottom-right-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 132, 255, 0.2);
  transition: all 0.15s ease;
}

.dark .message.own .message-bubble {
  background: linear-gradient(135deg, #1e40af, #1d4ed8);
  color: white;
  box-shadow: 0 1px 2px rgba(30, 64, 175, 0.3);
}

.message.own .message-bubble:hover {
  background: linear-gradient(135deg, #0066cc, #0052a3);
  box-shadow: 0 2px 8px rgba(0, 132, 255, 0.3);
  transform: translateY(-1px);
}

.dark .message.own .message-bubble:hover {
  background: linear-gradient(135deg, #1d4ed8, #2563eb);
  box-shadow: 0 2px 8px rgba(30, 64, 175, 0.4);
  transform: translateY(-1px);
}

.message.other .message-bubble {
  background: #f0f2f5;
  color: #1c1e21;
  border-bottom-left-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.15s ease;
}

.dark .message.other .message-bubble {
  background: #3a3b3c;
  color: #e4e6ea;
}

.message.other .message-bubble:hover {
  background: #e4e6ea;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
}

.dark .message.other .message-bubble:hover {
  background: #4e4f50;
}

.message-text {
  margin: 0;
  line-height: 1.4;
  white-space: pre-line; /* يحافظ على الأسطر الجديدة المقصودة فقط */
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word; /* يكسر الكلمات الطويلة */
  hyphens: auto;
  text-align: right; /* محاذاة النص للعربية */
  max-width: 100%; /* يضمن عدم تجاوز حدود الفقاعة */
  overflow: hidden; /* يمنع تجاوز النص */
  display: block; /* يضمن احترام max-width */
}

/* قاعدة خاصة للنصوص الطويلة جداً */
.message-text * {
  word-break: break-word !important;
  overflow-wrap: break-word !important;
  max-width: 100% !important;
}

/* قاعدة لكسر النصوص الطويلة بدون مسافات */
.message-text {
  word-break: break-all; /* كسر قوي للنصوص الطويلة */
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-top: 6px; /* مساحة موحدة ومحسنة */
  font-size: 0.75rem;
  color: var(--color-text-secondary);
}

.message.own .message-meta {
  justify-content: flex-end;
}

.message-status {
  font-size: 0.8rem;
}

.message-status.sent {
  color: var(--color-text-secondary);
}

.message-status.delivered {
  color: var(--color-text-secondary);
}

.message-status.read {
  color: #0284c7;
}

.edited-indicator {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
  margin-top: 4px;
}

.message.other .edited-indicator {
  color: var(--color-text-secondary);
}

/* مؤشر الكتابة */
.typing {
  background: var(--color-bg-secondary) !important;
  padding: 12px 16px !important;
  border: 1px solid var(--color-border) !important;
}

.typing-indicator {
  display: flex;
  gap: 4px;
  margin-bottom: 5px;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--color-text-secondary);
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.typing-text {
  margin: 0;
  font-size: 0.85rem;
  color: var(--color-text-secondary);
  font-style: italic;
}

/* تنسيق الروابط في الرسائل */
.message-link {
  position: relative;
  display: inline-block;
  margin: 0 2px;
}

.link-content {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 3px 8px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  color: #1d4ed8;
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  max-width: 300px;
  overflow: hidden;
  font-weight: 500;
}

/* تنسيق مختلف للروابط في الرسائل الخاصة */
.message.own .link-content {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  color: rgba(255, 255, 255, 0.9);
}

.message.own .link-content:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.6);
}

.dark .link-content {
  background: rgba(96, 165, 250, 0.15);
  border-color: rgba(96, 165, 250, 0.4);
  color: #60a5fa;
}

.dark .message.own .link-content {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.9);
}

.dark .message.own .link-content:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
}

.link-content:hover {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .link-content:hover {
  background: rgba(96, 165, 250, 0.25);
  border-color: rgba(96, 165, 250, 0.6);
}

.link-icon {
  font-size: 0.8rem;
  opacity: 0.7;
}

.link-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 250px;
}

.external-icon {
  font-size: 0.7rem;
  opacity: 0.6;
}

/* تم إزالة tooltip للروابط لأنه زائد عن الحاجة */

/* تم إزالة أنماط tooltip للروابط */

@keyframes fadeInTooltip {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(5px);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* تنسيق النص مع الروابط */
.message-text-with-links {
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  line-height: 1.4;
}

.message-text-content {
  margin-bottom: 8px;
}

/* معاينة الروابط */
.link-previews {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* عندما لا يوجد نص غير الروابط، قلل المسافة */
.message-text-with-links:not(:has(.message-text-content)) .link-previews {
  margin-top: 0;
}

/* تحسين عرض الرسائل التي تحتوي على روابط فقط */
.message-text-with-links:not(:has(.message-text-content)) {
  min-height: auto;
}

.link-preview {
  border: 1px solid var(--color-border);
  border-radius: 12px;
  overflow: hidden;
  background: var(--color-bg-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  max-width: 400px;
}

.link-preview:hover {
  border-color: var(--color-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.dark .link-preview {
  background: var(--color-bg-tertiary);
  border-color: var(--color-border-secondary);
}

.dark .link-preview:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.preview-content {
  display: flex;
  flex-direction: column;
}

.preview-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
  background: var(--color-bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* أنماط الفيديو */
.preview-video {
  width: 100%;
  height: 200px;
  overflow: hidden;
  background: var(--color-bg-tertiary);
  position: relative;
  cursor: pointer;
}

.video-thumbnail {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.link-preview:hover .video-overlay {
  background: rgba(0, 0, 0, 0.5);
}

.play-button {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #dc2626;
  font-size: 20px;
  transition: all 0.3s ease;
  margin-left: 4px; /* تعديل للأيقونة */
}

.link-preview:hover .play-button {
  background: white;
  transform: scale(1.1);
}

.video-platform {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(255, 0, 0, 0.9);
  color: white;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* أنماط الصور */
.image-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 4px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.preview-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
}

.link-preview:hover .preview-image img,
.link-preview:hover .video-thumbnail img {
  transform: scale(1.05);
}

/* تحسين عرض أنواع المحتوى المختلفة */
.link-preview.image {
  max-width: 500px;
}

.link-preview.image .preview-image {
  height: auto;
  min-height: 200px;
  max-height: 400px;
}

.link-preview.video .preview-video {
  height: 225px; /* نسبة 16:9 تقريباً */
}

/* أنماط الفيديو المدمج */
.preview-video-embed {
  width: 100%;
  height: 315px;
  background: #000;
  border-radius: 8px 8px 0 0;
  overflow: hidden;
}

.preview-video-embed iframe {
  width: 100%;
  height: 100%;
  border-radius: 8px 8px 0 0;
}

/* تحسين عرض الفيديو المدمج */
.link-preview.video.embedded {
  max-width: 560px;
}

.link-preview.video.embedded .preview-video-embed {
  height: 315px; /* نسبة 16:9 لليوتيوب */
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
}

.preview-info {
  padding: 12px;
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 6px;
}

.site-favicon {
  width: 16px;
  height: 16px;
  border-radius: 2px;
}

.site-name {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  flex: 1;
}

.external-icon {
  font-size: 0.7rem;
  color: var(--color-text-secondary);
  opacity: 0.6;
}

.preview-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0 0 4px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.preview-description {
  font-size: 0.8rem;
  color: var(--color-text-secondary);
  margin: 0 0 6px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.preview-url {
  font-size: 0.75rem;
  color: var(--color-text-tertiary);
  text-decoration: none;
}

/* حالات التحميل والخطأ */
.link-preview.loading {
  cursor: default;
}

.link-preview.loading:hover {
  transform: none;
  box-shadow: none;
  border-color: var(--color-border);
}

.preview-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px;
  color: var(--color-text-secondary);
  font-size: 0.85rem;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.link-preview.error {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
}

.preview-error {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
}

.error-icon {
  color: #ef4444;
  font-size: 0.9rem;
}

.error-text {
  flex: 1;
}

.error-title {
  font-size: 0.85rem;
  color: var(--color-text-primary);
  font-weight: 500;
}

.error-url {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
}

.more-links-indicator {
  padding: 8px 12px;
  background: var(--color-bg-tertiary);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  font-size: 0.8rem;
  color: var(--color-text-secondary);
  text-align: center;
}

.inline-link {
  vertical-align: baseline;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
  .link-content {
    max-width: 250px;
    font-size: 0.85rem;
    padding: 2px 6px;
  }

  .link-text {
    max-width: 200px;
  }

  /* تم إزالة أنماط tooltip للأجهزة المحمولة */
}

/* تم نقل تعريف زر التمرير للأسفل إلى نهاية الملف لتجنب التكرار */

/* إدخال الرسائل */
.message-input-container {
  border-top: 1px solid var(--color-border);
  background: var(--color-card-bg);
  padding: 12px 16px;
}

.message-input-form {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  background: #f0f2f5;
  border-radius: 20px;
  padding: 8px 12px;
  border: none;
  transition: all 0.15s ease;
  min-height: 40px;
}

.dark .input-wrapper {
  background: #374151;
  border: 1px solid #4b5563;
}

.input-wrapper:focus-within {
  background: #e4e6ea;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.dark .input-wrapper:focus-within {
  background: #4b5563;
  border-color: #6b7280;
  box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.2);
}

.message-textarea {
  flex: 1;
  border: none;
  background: transparent;
  resize: none;
  outline: none;
  font-family: inherit;
  font-size: 0.9rem;
  line-height: 1.3;
  min-height: 20px;
  max-height: 100px;
  padding: 6px 8px;
  color: var(--color-text-primary);
  min-width: 0;
  overflow-y: auto;
  overflow-x: hidden;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.message-textarea::placeholder {
  color: var(--color-text-secondary);
}

.message-textarea.disabled {
  color: var(--color-text-secondary);
  cursor: not-allowed;
}

.input-actions {
  display: flex;
  gap: 5px;
  align-items: center;
  flex-shrink: 0;
}

.action-btn {
  background: none;
  border: none;
  font-size: 1.1rem;
  cursor: pointer;
  padding: 6px;
  border-radius: 50%;
  transition: all 0.15s ease;
  color: #65676b;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dark .action-btn {
  color: #b0b3b8;
}

.action-btn:hover:not(:disabled) {
  background: rgba(0, 0, 0, 0.05);
  color: #0084ff;
}

.dark .action-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.1);
  color: #0084ff;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.send-btn {
  background: #0084ff;
  border: none;
  font-size: 1rem;
  cursor: pointer;
  width: 32px;
  height: 32px;
  min-width: 32px;
  min-height: 32px;
  max-width: 32px;
  max-height: 32px;
  border-radius: 50%;
  transition: all 0.15s ease;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  flex-shrink: 0;
}

.dark .send-btn {
  background: #1e40af;
  box-shadow: 0 1px 3px rgba(30, 64, 175, 0.3);
}

.send-btn.active {
  background: #0084ff;
}

.dark .send-btn.active {
  background: #1e40af;
}

.send-btn.active:hover {
  background: #0066cc;
  transform: scale(1.1);
}

.dark .send-btn.active:hover {
  background: #1d4ed8;
  transform: scale(1.1);
  box-shadow: 0 2px 6px rgba(30, 64, 175, 0.4);
}

.send-btn.disabled {
  background: #bcc0c4;
  cursor: not-allowed;
}

.dark .send-btn.disabled {
  background: #65676b;
}

.send-btn.disabled:hover {
  transform: none;
}

.send-btn.connecting {
  background: #f59e0b;
}

.send-btn.disconnected {
  background: #ef4444;
}

.send-btn.empty {
  background: #bcc0c4;
  cursor: default;
}

.dark .send-btn.empty {
  background: #65676b;
}

.send-btn.empty:hover {
  transform: none;
}

.input-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
  color: var(--color-text-secondary);
  margin-top: 5px;
}

.character-count {
  color: #f59e0b;
}

.character-count.limit-reached {
  color: #ef4444;
  font-weight: bold;
}

.input-hint {
  font-style: italic;
}

/* البحث عن المستخدمين */
.user-search {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-header {
  padding: 15px 20px;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-card-bg);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-header h4 {
  margin: 0;
  color: var(--color-text-primary);
  font-size: 1rem;
}

.close-search-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: var(--color-text-secondary);
  padding: 4px;
  border-radius: 0.25rem;
  transition: all 0.2s;
}

.close-search-btn:hover {
  background: var(--color-bg-secondary);
  color: var(--color-text-primary);
}

.search-input-container {
  padding: 15px 20px;
  border-bottom: 1px solid var(--color-border);
  position: relative;
}

.search-input {
  width: 100%;
  padding: 12px 40px 12px 15px;
  border: 1px solid var(--color-input-border);
  border-radius: 0.5rem;
  font-size: 1rem;
  outline: none;
  transition: all 0.2s;
  background: var(--color-input-bg);
  color: var(--color-text-primary);
}

.search-input::placeholder {
  color: var(--color-text-secondary);
}

.search-input:focus {
  border-color: #0284c7;
  box-shadow: 0 0 0 3px rgba(2, 132, 199, 0.1);
}

.search-loading {
  position: absolute;
  right: 35px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1rem;
  color: var(--color-text-secondary);
}

.search-results {
  flex: 1;
  overflow-y: auto;
}

.results-header {
  padding: 15px 20px 10px;
  border-bottom: 1px solid var(--color-border);
}

.results-header h5 {
  margin: 0;
  color: var(--color-text-primary);
  font-size: 0.9rem;
}

.users-list {
  padding: 0;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: background 0.2s;
  border-bottom: 1px solid var(--color-border);
}

.user-item:hover {
  background: var(--color-bg-secondary);
}

.user-item .user-avatar {
  margin-left: 12px;
  /* لون احتياطي إذا لم يتم تطبيق اللون من JavaScript */
  background: #0284c7 !important;
}

.user-item .user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  margin: 0 0 2px 0;
  font-size: 1rem;
  color: var(--color-text-primary);
  font-weight: 600;
}

.user-username {
  margin: 0 0 4px 0;
  font-size: 0.85rem;
  color: var(--color-text-secondary);
}

.user-item .user-status {
  font-size: 0.8rem;
}

.last-seen {
  display: block;
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  margin-top: 2px;
}

.user-actions {
  margin-right: 10px;
}

.start-chat-btn {
  background: #0284c7;
  color: white;
  border: none;
  padding: 6px 10px;
  border-radius: 0.375rem;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.start-chat-btn:hover {
  background: #0369a1;
}

/* حالات فارغة */
.loading,
.empty {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-secondary);
  text-align: center;
}

.loading-spinner,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-border);
  border-top: 4px solid #0284c7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner.small {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-icon,
.no-results-icon,
.no-users-icon {
  font-size: 3rem;
  color: var(--color-text-secondary);
}

.error-message {
  padding: 15px 20px;
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border-radius: 0.5rem;
  margin: 15px 20px;
  text-align: center;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.no-results,
.no-online-users {
  padding: 40px 20px;
  text-align: center;
  color: var(--color-text-secondary);
}

.search-tips {
  padding: 10px 20px;
  border-top: 1px solid var(--color-border);
  background: var(--color-bg-secondary);
  text-align: center;
  color: var(--color-text-secondary);
}

/* تجاوب الشاشات الصغيرة */
@media (max-width: 768px) {
  .chat-window {
    width: 100%;
    height: 100vh;
    max-height: none;
    border-radius: 0;
  }

  .chat-sidebar {
    width: 100%;
    border-left: none;
    border-bottom: 1px solid var(--color-border);
  }

  .chat-content {
    flex-direction: column;
  }

  .chat-main {
    display: none;
  }

  .chat-main.active {
    display: flex;
  }

  .message-content {
    max-width: 85%;
  }
}

/* أزرار التنقل */
.nav-btn {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  padding: 8px 12px;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.nav-btn:hover {
  background: var(--color-input-bg);
  color: var(--color-text-primary);
  border-color: var(--color-input-border);
}

.nav-btn.active {
  background: #0284c7;
  color: white;
  border-color: #0284c7;
}

.close-btn {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  padding: 8px 12px;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.close-btn:hover {
  background: var(--color-input-bg);
  color: var(--color-text-primary);
  border-color: var(--color-input-border);
}

.chat-header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* حالات اتصال الهيدر */
.chat-header.disconnected {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border-bottom-color: #fca5a5;
}

.dark .chat-header.disconnected {
  background: linear-gradient(135deg, #450a0a 0%, #7f1d1d 100%);
  border-bottom-color: #dc2626;
}

.chat-header.connecting {
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
  border-bottom-color: #fbbf24;
}

.dark .chat-header.connecting {
  background: linear-gradient(135deg, #451a03 0%, #78350f 100%);
  border-bottom-color: #f59e0b;
}

.connection-warning-icon {
  color: #ef4444;
  margin-left: 8px;
  font-size: 0.9rem;
}

.chat-header .connecting-icon {
  margin-left: 8px;
  color: #f59e0b;
  animation: pulse 1.5s ease-in-out infinite;
}

/* تصميم تعديل وحذف الرسائل */
.message {
  position: relative;
  margin-bottom: 8px; /* مسافة بين الرسائل */
  overflow: visible; /* يسمح بظهور الأزرار خارج الحدود */
}

.message-content {
  position: relative; /* مهم للموضع النسبي للأزرار */
  overflow: visible; /* يسمح بظهور الأزرار خارج الحدود */
}

.message-menu-btn {
  position: absolute;
  top: 8px; /* موضع أعلى الرسالة */
  transform: none;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%; /* دائري أنيق */
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0; /* مخفي افتراضياً */
  visibility: hidden; /* مخفي تماماً */
  transition: all 0.2s ease;
  font-size: 0.8rem;
  color: #64748b;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 200;
  backdrop-filter: blur(10px);
}

/* النمط الداكن */
.dark .message-menu-btn {
  background: rgba(51, 65, 85, 0.95);
  border-color: rgba(71, 85, 105, 0.3);
  color: #94a3b8;
  backdrop-filter: blur(10px);
}

/* إظهار الزر عند التحويم على الرسالة */
.message:hover .message-menu-btn {
  opacity: 1;
  visibility: visible; /* إظهار الزر */
  transform: scale(1.05); /* تكبير خفيف عند الظهور */
}

/* موضع ذكي للأزرار حسب نوع الرسالة */
.message.own .message-menu-btn {
  left: -35px; /* على يسار الرسائل الخاصة */
}

.message.other .message-menu-btn {
  right: -35px; /* على يمين رسائل الآخرين */
}

/* تأكد من ظهور الزر عند التحويم - قاعدة إضافية */
.message.own:hover .message-menu-btn,
.message.other:hover .message-menu-btn {
  opacity: 1 !important;
  visibility: visible !important;
}

/* إزالة القاعدة التجريبية */

/* تأثير خاص عند فتح القائمة */
.message-menu-btn.active {
  opacity: 1 !important;
  visibility: visible !important;
  background: #3b82f6; /* أزرق نشط */
  color: white;
  border-color: #2563eb;
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.3);
}

.dark .message-menu-btn.active {
  background: #60a5fa;
  border-color: #3b82f6;
  box-shadow: 0 2px 6px rgba(96, 165, 250, 0.3);
}

.message-menu-btn:hover {
  background: #e2e8f0; /* خلفية رمادية فاتحة */
  color: #475569; /* لون أغمق */
  border-color: #cbd5e1;
}

.dark .message-menu-btn:hover {
  background: #475569;
  color: #e2e8f0;
  border-color: #64748b;
}

/* تأثير عند النقر - تغيير لون فقط */
.message-menu-btn:active {
  background: #cbd5e1;
  color: #334155;
}

.dark .message-menu-btn:active {
  background: #64748b;
  color: #f1f5f9;
}

.message-context-menu {
  position: absolute;
  top: 50%; /* في منتصف الزر */
  transform: translateY(-50%); /* توسيط عمودي */
  margin-top: 0; /* إزالة المسافة الإضافية */
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 6px; /* زوايا أصغر مثل فيسبوك */
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12); /* ظل أنعم */
  z-index: 1000;
  min-width: 100px; /* أصغر من السابق */
  width: auto;
  overflow: visible;
  animation: contextMenuSlideIn 0.12s ease-out; /* أسرع قليلاً */
  display: flex;
  flex-direction: column;
  padding: 2px; /* padding أصغر */
}

/* النمط الداكن للقائمة */
.dark .message-context-menu {
  background: #242526;
  border: 1px solid #3e4042;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
}

/* موضع مثل فيسبوك - مباشرة بجانب الزر */
.message.own .message-context-menu {
  right: 32px; /* بجانب الزر مباشرة (عرض الزر + مسافة صغيرة) */
  left: auto;
}

.message.other .message-context-menu {
  left: 32px; /* بجانب الزر مباشرة */
  right: auto;
}

/* مواضع ذكية للقائمة حسب المساحة المتاحة - محسنة لتكون أقرب */

/* عندما تكون قريبة من الأعلى - اعرض القائمة للأسفل */
.message.near-top .message-context-menu {
  top: 100% !important;
  bottom: auto !important;
  transform: translateY(4px) !important; /* مسافة صغيرة من الزر */
}

/* عندما تكون قريبة من الأسفل - اعرض القائمة للأعلى */
.message.near-bottom .message-context-menu {
  top: auto !important;
  bottom: 100% !important;
  transform: translateY(-4px) !important; /* مسافة صغيرة من الزر */
}

/* عندما تكون قريبة من اليسار - للرسائل الخاصة */
.message.own.near-left-edge .message-context-menu {
  left: 32px !important;
  right: auto !important;
}

/* عندما تكون قريبة من اليمين - لرسائل الآخرين */
.message.other.near-right-edge .message-context-menu {
  right: 32px !important;
  left: auto !important;
}

/* النمط الداكن */
.dark .message-context-menu {
  background: rgba(31, 41, 55, 0.98);
  border-color: rgba(59, 130, 246, 0.2);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.5),
    0 8px 16px rgba(0, 0, 0, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(20px);
}

/* إزالة القواعد القديمة - سيتم استخدام الموضع الجديد */

/* Animation محسن مثل فيسبوك - أسرع وأنعم */
@keyframes contextMenuSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-50%) scale(0.95); /* تأثير تكبير خفيف */
  }
  100% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
}

.context-menu-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 6px; /* مسافة أصغر */
  width: 100%;
  padding: 6px 10px; /* padding أصغر للحصول على حجم مضغوط */
  border: none;
  background: transparent;
  color: #1c1e21;
  cursor: pointer;
  font-size: 13px; /* حجم خط أصغر */
  transition: background-color 0.1s ease;
  font-weight: 400; /* وزن عادي */
  border-radius: 4px; /* زوايا أصغر */
  margin: 0; /* بدون مسافة */
  position: relative;
  white-space: nowrap;
  text-align: right; /* محاذاة النص للعربية */
  min-height: 28px; /* ارتفاع ثابت أصغر */
}

/* النمط الداكن */
.dark .context-menu-item {
  color: #d1d5db;
}

.context-menu-item:hover {
  background: #f2f3f5; /* لون رمادي فاتح مثل فيسبوك */
}

.dark .context-menu-item:hover {
  background: #3a3b3c; /* لون رمادي داكن للوضع الليلي */
}

.context-menu-item.edit {
  color: #059669;
}

.context-menu-item.edit:hover {
  background: #f2f3f5; /* نفس لون hover العادي */
}

.dark .context-menu-item.edit {
  color: #10b981;
}

.dark .context-menu-item.edit:hover {
  background: #3a3b3c; /* نفس لون hover الداكن */
}

.context-menu-item.delete {
  color: #dc2626; /* لون أحمر دائم */
}

.context-menu-item.delete:hover {
  background: #f2f3f5; /* نفس لون hover العادي */
}

.dark .context-menu-item.delete {
  color: #f87171;
}

.dark .context-menu-item.delete:hover {
  background: #3a3b3c; /* نفس لون hover الداكن */
}

.context-menu-item.cancel {
  color: #6b7280;
}

.context-menu-item.cancel:hover {
  background: linear-gradient(135deg, rgba(107, 114, 128, 0.12), rgba(107, 114, 128, 0.06));
  color: #4b5563;
  border-color: rgba(107, 114, 128, 0.2);
  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.15);
  transform: translateX(-3px) scale(1.02);
}

.dark .context-menu-item.cancel {
  color: #9ca3af;
}

.dark .context-menu-item.cancel:hover {
  background: linear-gradient(135deg, rgba(156, 163, 175, 0.18), rgba(156, 163, 175, 0.1));
  color: #d1d5db;
  border-color: rgba(156, 163, 175, 0.3);
}

/* تأثير عند النقر */
.context-menu-item:active {
  transform: translateX(-1px) scale(0.96);
  transition: all 0.1s ease;
}

/* تأثير ripple عند النقر */
.context-menu-item::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
  pointer-events: none;
  z-index: -1;
}

.context-menu-item:active::before {
  width: 100%;
  height: 100%;
}

/* فواصل بصرية محسنة بين العناصر */
.context-menu-item.edit::after,
.context-menu-item.delete::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 18px;
  right: 18px;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
  opacity: 0.6;
}

.dark .context-menu-item.edit::after,
.dark .context-menu-item.delete::after {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
}

/* فواصل بين الأزرار - قبل الحذف وقبل الإلغاء */
.context-menu-item.delete::before,
.context-menu-item.cancel::before {
  content: '';
  position: absolute;
  left: -2px; /* على اليسار قبل الزر */
  top: 25%;
  bottom: 25%;
  width: 1px;
  background: rgba(0, 0, 0, 0.15);
}

.dark .context-menu-item.delete::before,
.dark .context-menu-item.cancel::before {
  background: rgba(255, 255, 255, 0.2);
}

/* انيميشن ظهور القائمة من اليمين */
@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(8px);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

/* انيميشن ظهور القائمة من اليسار */
@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

/* تصميم نموذج التعديل */
.message-edit-form {
  width: 100%;
}

.edit-textarea {
  width: 100%;
  min-height: 60px;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  background: var(--color-input-bg);
  color: var(--color-text-primary);
  font-family: inherit;
  font-size: 0.9rem;
  resize: vertical;
  outline: none;
  transition: border-color 0.2s ease;
}

.edit-textarea:focus {
  border-color: #0284c7;
  box-shadow: 0 0 0 2px rgba(2, 132, 199, 0.1);
}

.edit-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  justify-content: flex-end;
}

.edit-save-btn,
.edit-cancel-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.edit-save-btn {
  background: #22c55e;
  color: white;
}

.edit-save-btn:hover:not(:disabled) {
  background: #16a34a;
}

.edit-save-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.edit-cancel-btn {
  background: #6b7280;
  color: white;
}

.edit-cancel-btn:hover {
  background: #4b5563;
}

/* تحسينات زر الإرسال وحالة الاتصال */
.send-btn.connecting {
  background: #f59e0b;
  color: white;
  cursor: not-allowed;
}

.send-btn.connecting:hover {
  background: #f59e0b;
}

.send-btn.disconnected {
  background: #ef4444;
  color: white;
  cursor: not-allowed;
}

.send-btn.disconnected:hover {
  background: #ef4444;
}

.send-btn.empty {
  background: #e5e7eb;
  color: #9ca3af;
  cursor: pointer;
  width: 40px;
  height: 40px;
  min-width: 40px;
  min-height: 40px;
  border: 1px solid #d1d5db;
}

.send-btn.empty:hover {
  background: #f3f4f6;
  color: #6b7280;
}

.send-btn.ready {
  background: #0284c7;
  color: white;
  cursor: pointer;
  width: 40px;
  height: 40px;
  min-width: 40px;
  min-height: 40px;
}

.send-btn.ready:hover {
  background: #0369a1;
  transform: scale(1.05);
}

/* التأكد من أن الزر يبقى دائرياً في جميع الحالات */
.send-btn,
.send-btn.empty,
.send-btn.ready,
.send-btn.connecting,
.send-btn.disconnected,
.send-btn.disabled {
  aspect-ratio: 1;
  border-radius: 50% !important;
  overflow: hidden;
  box-sizing: border-box;
  white-space: nowrap;
  text-align: center;
  vertical-align: middle;
  line-height: 1;
}

/* إزالة أي تأثيرات خارجية قد تؤثر على شكل الزر */
.send-btn * {
  pointer-events: none;
}

/* رسائل حالة الاتصال */
.connection-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.8rem;
  padding: 6px 12px;
  border-radius: 0.375rem;
  margin-bottom: 4px;
}

.connection-status.disconnected {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.connection-status.connecting {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.connection-status .status-icon {
  font-size: 0.7rem;
}

.connection-status .connecting-icon {
  animation: pulse 1.5s ease-in-out infinite;
}

.send-btn .connecting-icon {
  animation: pulse 1.5s ease-in-out infinite;
}

.send-btn .disabled-icon {
  opacity: 0.6;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* دعم الوضع المظلم لرسائل الاتصال */
.dark .connection-status.disconnected {
  background: rgba(239, 68, 68, 0.15);
  color: #fca5a5;
  border-color: rgba(239, 68, 68, 0.3);
}

.dark .connection-status.connecting {
  background: rgba(245, 158, 11, 0.15);
  color: #fbbf24;
  border-color: rgba(245, 158, 11, 0.3);
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* مؤشر التحميل الإضافي للمحادثات */
.loading-more-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px;
  color: var(--text-muted);
  font-size: 14px;
  gap: 8px;
  border-top: 1px solid var(--border-color);
  background: var(--color-bg-secondary);
}

.loading-more-indicator .spinner {
  animation: spin 1s linear infinite;
  color: var(--color-primary);
}

/* رسالة عدم وجود المزيد من المحادثات */
.no-more-conversations {
  text-align: center;
  padding: 15px;
  color: var(--text-muted);
  font-size: 12px;
  border-top: 1px solid var(--border-color);
  margin-top: 10px;
  background: var(--color-bg-secondary);
  font-style: italic;
}

/* رسالة عدم وجود محادثات */
.no-conversations {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-muted);
  font-size: 14px;
}

/* مؤشر التحميل المحسن للرسائل */
.load-more-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  color: var(--color-text-secondary);
  font-size: 14px;
  background: var(--color-bg-secondary);
  border-radius: 8px;
  margin: 8px 12px;
  border: 1px solid var(--color-border);
  animation: fadeIn 0.3s ease-in-out;
}

/* رسالة عدم وجود رسائل أقدم */
.no-more-messages {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  color: var(--color-text-secondary);
  font-size: 13px;
  text-align: center;
  opacity: 0.7;
  margin: 8px 12px;
}

/* زر التمرير للأسفل - محسن ومحدث */
.scroll-to-bottom-btn {
  position: absolute;
  bottom: 80px; /* رفع الموضع قليلاً فوق خانة الكتابة */
  right: 20px;
  width: 40px; /* حجم أكبر للوضوح */
  height: 40px;
  border-radius: 50%;
  background: var(--color-primary, #0284c7); /* لون أساسي واضح */
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px; /* أيقونة أكبر */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  z-index: 15; /* z-index أعلى لضمان الظهور */
  opacity: 0.9;
  /* ضمان الظهور دائماً */
  visibility: visible !important;
  pointer-events: auto !important;
}

.scroll-to-bottom-btn:hover {
  background: var(--color-primary-dark, #0369a1);
  color: white;
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  opacity: 1;
  border-color: rgba(255, 255, 255, 0.3);
}

/* تحسين للوضع المظلم */
[data-theme="dark"] .scroll-to-bottom-btn {
  background: var(--color-primary, #0284c7);
  border-color: rgba(255, 255, 255, 0.1);
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .scroll-to-bottom-btn:hover {
  background: var(--color-primary-dark, #0369a1);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

/* ضمان ظهور الزر في جميع الحالات */
.scroll-to-bottom-btn.show {
  opacity: 0.9 !important;
  visibility: visible !important;
  pointer-events: auto !important;
  display: flex !important;
  transform: translateY(0) scale(1) !important;
}

.scroll-to-bottom-btn.hide {
  opacity: 0 !important;
  visibility: hidden !important;
  pointer-events: none !important;
  transform: translateY(10px) scale(0.8) !important;
}

/* انتقال سلس بين الحالات */
.scroll-to-bottom-btn {
  transition: all 0.3s ease !important;
}

/* تحسين الموضع النسبي لحاوي الرسائل */
.messages-container {
  position: relative;
}

/* ضمان عدم تداخل الزر مع عناصر أخرى */
.scroll-to-bottom-btn {
  /* إضافة قواعد إضافية للتأكد من الظهور */
  transform: translateZ(0); /* تفعيل تسريع الأجهزة */
  will-change: transform, opacity; /* تحسين الأداء */
  /* ضمان الظهور فوق جميع العناصر */
  z-index: 1000 !important;
  position: fixed !important; /* تغيير من absolute إلى fixed */
}

/* تحسين موضع الزر للتأكد من ظهوره في المكان الصحيح */
.messages-container .scroll-to-bottom-btn {
  position: absolute !important; /* إعادة تعيين للموضع النسبي داخل الحاوي */
}

/* تنسيقات تنبيهات الرسائل الجديدة - في الوسط */
.chat-message-alert {
  animation: slideInFromCenter 0.3s ease-out;
  /* ضمان عرض تنبيه واحد فقط */
  z-index: 9999;
}

@keyframes slideInFromCenter {
  from {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

.chat-message-alert.dismissing {
  animation: slideOutToCenter 0.3s ease-in forwards;
}

@keyframes slideOutToCenter {
  from {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  to {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0;
  }
}

/* تحسين تنبيهات الرسائل للوضع المظلم */
.dark .chat-message-alert {
  background: var(--color-bg-secondary);
  border-color: var(--color-border);
  color: var(--color-text-primary);
}

.dark .chat-message-alert .text-gray-700 {
  color: var(--color-text-primary) !important;
}

.dark .chat-message-alert .text-gray-500 {
  color: var(--color-text-secondary) !important;
}

/* شريط التقدم لتنبيهات الرسائل */
.chat-progress-bar {
  animation: shrink linear forwards;
}

@keyframes shrink {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* تحسين مؤشر التحميل الصغير */
.spinner.small {
  width: 16px;
  height: 16px;
  border-width: 2px;
}

/* Animation للمؤشرات */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
