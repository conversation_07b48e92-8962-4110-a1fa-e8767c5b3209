import React from 'react';
import { FaMoneyBillWave, FaHandHoldingUsd, FaClock } from 'react-icons/fa';
import FormattedCurrency from './FormattedCurrency';

interface PaymentOptionsProps {
  paymentType: 'full' | 'partial' | 'credit';
  onPaymentTypeChange: (type: 'full' | 'partial' | 'credit') => void;
  totalAmount: number;
  hasCustomer: boolean;
}

const PaymentOptions: React.FC<PaymentOptionsProps> = ({
  paymentType,
  onPaymentTypeChange,
  totalAmount,
  hasCustomer
}) => {

  const options = [
    {
      id: 'full',
      label: 'دفع كامل',
      description: (
        <span>
          دفع المبلغ كاملاً (<FormattedCurrency amount={totalAmount} />)
        </span>
      ),
      icon: <FaMoneyBillWave />,
      color: 'green',
      available: true
    },
    {
      id: 'partial',
      label: 'دفع جزئي',
      description: 'دفع جزء من المبلغ والباقي دين',
      icon: <FaHandHoldingUsd />,
      color: 'orange',
      available: hasCustomer
    },
    {
      id: 'credit',
      label: 'بيع آجل',
      description: 'تسجيل المبلغ كاملاً كدين',
      icon: <FaClock />,
      color: 'red',
      available: hasCustomer
    }
  ];

  return (
    <div className="space-y-3">
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
        نوع الدفع
      </label>

      <div className="grid grid-cols-1 gap-3">
        {options.map((option) => (
          <button
            key={option.id}
            type="button"
            onClick={() => {
              if (option.available) {
                console.log('🔄 Payment type changed to:', option.id);
                onPaymentTypeChange(option.id as any);
              }
            }}
            disabled={!option.available}
            className={`
              relative p-4 rounded-xl border-2 text-right transition-all duration-200
              ${paymentType === option.id
                ? `border-${option.color}-500 bg-${option.color}-50 dark:bg-${option.color}-900/20`
                : option.available
                  ? 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 bg-white dark:bg-gray-800'
                  : 'border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50 opacity-50 cursor-not-allowed'
              }
            `}
          >
            <div className="flex items-center">
              <div className={`
                flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center
                ${paymentType === option.id
                  ? `bg-${option.color}-100 dark:bg-${option.color}-900/40 text-${option.color}-600 dark:text-${option.color}-400`
                  : option.available
                    ? 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500'
                }
              `}>
                {option.icon}
              </div>

              <div className="mr-4 flex-1">
                <div className={`
                  font-medium text-base
                  ${paymentType === option.id
                    ? `text-${option.color}-700 dark:text-${option.color}-300`
                    : option.available
                      ? 'text-gray-900 dark:text-gray-100'
                      : 'text-gray-500 dark:text-gray-400'
                  }
                `}>
                  {option.label}
                </div>
                <div className={`
                  text-sm mt-1
                  ${paymentType === option.id
                    ? `text-${option.color}-600 dark:text-${option.color}-400`
                    : option.available
                      ? 'text-gray-600 dark:text-gray-400'
                      : 'text-gray-400 dark:text-gray-500'
                  }
                `}>
                  {option.description}
                </div>

                {!option.available && (
                  <div className="text-xs text-red-500 dark:text-red-400 mt-1">
                    يتطلب اختيار عميل
                  </div>
                )}
              </div>

              {paymentType === option.id && (
                <div className={`
                  flex-shrink-0 w-5 h-5 rounded-full border-2 border-${option.color}-500
                  bg-${option.color}-500 flex items-center justify-center
                `}>
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
              )}
            </div>
          </button>
        ))}
      </div>

      {!hasCustomer && (
        <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-xl p-3">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="mr-3">
              <p className="text-sm text-amber-800 dark:text-amber-200">
                لاستخدام الدفع الجزئي أو البيع الآجل، يجب اختيار عميل أولاً
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentOptions;
