import React from 'react';
import { FaDatabase, FaBroom, FaCog, FaInfoCircle, FaCloud } from 'react-icons/fa';

interface TaskType {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  features: string[];
  recommended?: boolean;
}

interface TaskTypeSelectorProps {
  value: string;
  onChange: (taskType: string) => void;
  className?: string;
  disabled?: boolean;
  backupPath?: string;
}

const TaskTypeSelector: React.FC<TaskTypeSelectorProps> = ({
  value,
  onChange,
  className = '',
  disabled = false,
  backupPath
}) => {
  const taskTypes: TaskType[] = [
    {
      id: 'database_backup',
      name: 'نسخ احتياطي لقاعدة البيانات',
      description: 'إنشاء نسخة احتياطية كاملة من قاعدة البيانات',
      icon: <FaDatabase className="text-blue-500" />,
      features: [
        'نسخ احتياطي كامل للبيانات',
        'ضغط تلقائي للملفات',
        'تسمية تلقائية بالتاريخ والوقت',
        'حفظ في المسار المحدد'
      ],
      recommended: true
    },
    {
      id: 'cleanup_old_backups',
      name: 'تنظيف النسخ الاحتياطية القديمة',
      description: 'حذف النسخ الاحتياطية القديمة للحفاظ على مساحة التخزين',
      icon: <FaBroom className="text-orange-500" />,
      features: [
        'حذف النسخ الأقدم من 30 يوم',
        'الحفاظ على النسخ الحديثة',
        'تحرير مساحة التخزين',
        'تشغيل آمن ومحمي'
      ]
    },
    {
      id: 'google_drive_backup',
      name: 'نسخ احتياطي إلى Google Drive',
      description: 'رفع النسخ الاحتياطية تلقائياً إلى Google Drive',
      icon: <FaCloud className="text-green-500" />,
      features: [
        'رفع تلقائي إلى Google Drive',
        'نسخ احتياطي آمن في السحابة',
        'وصول من أي مكان',
        'حماية من فقدان البيانات المحلية'
      ]
    },
    {
      id: 'google_drive_cleanup',
      name: 'تنظيف Google Drive',
      description: 'حذف النسخ الاحتياطية القديمة من Google Drive',
      icon: <FaCloud className="text-red-500" />,
      features: [
        'حذف النسخ القديمة من Google Drive',
        'الحفاظ على مساحة التخزين السحابية',
        'الاحتفاظ بعدد محدد من النسخ',
        'تنظيف تلقائي ومجدول'
      ]
    },
    {
      id: 'system_maintenance',
      name: 'صيانة النظام',
      description: 'مهام صيانة عامة للنظام',
      icon: <FaCog className="text-gray-500" />,
      features: [
        'تنظيف الملفات المؤقتة',
        'تحسين الأداء',
        'فحص سلامة البيانات',
        'تحديث الإحصائيات'
      ]
    }
  ];

  const selectedTaskType = taskTypes.find(type => type.id === value);

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="grid grid-cols-1 gap-4">
        {taskTypes.map((taskType) => (
          <button
            key={taskType.id}
            type="button"
            onClick={() => !disabled && onChange(taskType.id)}
            disabled={disabled}
            className={`
              p-4 rounded-lg border-2 text-right transition-all duration-200 hover:shadow-md
              ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
              ${value === taskType.id
                ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 shadow-md'
                : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300'
              }
            `}
          >
            <div className="flex items-start">
              <div className="flex-shrink-0 ml-3">
                {taskType.icon}
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <h3 className="font-semibold text-base">{taskType.name}</h3>
                    {taskType.recommended && (
                      <span className="mr-2 px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-xs font-medium">
                        مُوصى به
                      </span>
                    )}
                  </div>
                  {value === taskType.id && (
                    <div className="w-3 h-3 bg-primary-500 rounded-full"></div>
                  )}
                </div>

                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  {taskType.description}
                </p>

                <div className="space-y-1">
                  {taskType.features.map((feature, index) => (
                    <div key={index} className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                      <span className="w-1.5 h-1.5 bg-current rounded-full ml-2 flex-shrink-0"></span>
                      {feature}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </button>
        ))}
      </div>

      {/* معلومات إضافية للنوع المحدد */}
      {selectedTaskType && backupPath && (
        <div className="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
          <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center">
            <FaInfoCircle className="ml-2" />
            معلومات إضافية
          </h4>

          {selectedTaskType.id === 'database_backup' && (
            <div className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
              <p>• سيتم حفظ النسخة الاحتياطية في: <code className="bg-blue-100 dark:bg-blue-800 px-1 rounded">{backupPath}</code></p>
              <p>• تنسيق الملف: <code>backup_YYYY-MM-DD_HH-MM-SS.sql.gz</code></p>
              <p>• الحجم المتوقع: يعتمد على حجم البيانات</p>
            </div>
          )}

          {selectedTaskType.id === 'cleanup_old_backups' && (
            <div className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
              <p>• سيتم البحث في: <code className="bg-blue-100 dark:bg-blue-800 px-1 rounded">{backupPath}</code></p>
              <p>• الاحتفاظ بآخر 30 نسخة احتياطية</p>
              <p>• حذف النسخ الأقدم تلقائياً</p>
            </div>
          )}

          {selectedTaskType.id === 'google_drive_backup' && (
            <div className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
              <p>• إنشاء نسخة احتياطية محلية أولاً</p>
              <p>• رفع النسخة إلى Google Drive</p>
              <p>• حذف النسخة المحلية (اختياري)</p>
              <p>• يتطلب تكوين Google Drive مسبقاً</p>
            </div>
          )}

          {selectedTaskType.id === 'google_drive_cleanup' && (
            <div className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
              <p>• البحث في مجلد النسخ الاحتياطية في Google Drive</p>
              <p>• الاحتفاظ بآخر 10 نسخ احتياطية</p>
              <p>• حذف النسخ الأقدم تلقائياً</p>
              <p>• يتطلب تكوين Google Drive مسبقاً</p>
            </div>
          )}

          {selectedTaskType.id === 'system_maintenance' && (
            <div className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
              <p>• تنظيف شامل للنظام</p>
              <p>• تحسين الأداء العام</p>
              <p>• فحص سلامة البيانات</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TaskTypeSelector;
