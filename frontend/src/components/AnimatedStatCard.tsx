/**
 * مكون البطاقة الإحصائية المتحركة
 * يدمج الأرقام المتحركة مع تصميم البطاقات الإحصائية
 * يتبع التصميم الموحد للنظام ويدعم الوضع المظلم
 */

import React, { useState, useEffect } from 'react';
import { AnimatedCompactNumber, AnimatedCompactCurrency } from './AnimatedNumber';
import { type UnitType } from '../services/compactNumberService';

// واجهة خصائص البطاقة الإحصائية المتحركة
interface AnimatedStatCardProps {
  /** عنوان البطاقة */
  title: string;
  /** المبلغ المراد عرضه */
  amount: number;
  /** القيمة الابتدائية للرسم المتحرك (افتراضي: 0) */
  startValue?: number;
  /** الأيقونة */
  icon?: React.ReactNode;
  /** إظهار رمز العملة أم لا (افتراضي: false) */
  showCurrency?: boolean;
  /** فئة CSS إضافية */
  className?: string;
  /** الحد الأدنى لبدء الاختصار (افتراضي: 1000) */
  compactThreshold?: number;
  /** نوع الوحدات (افتراضي: 'english') */
  unitType?: UnitType;
  /** نص التغيير أو الوصف */
  changeText?: string;
  /** مدة الرسم المتحرك بالثواني (افتراضي: 2.5) */
  animationDuration?: number;
  /** تأخير البدء بالثواني (افتراضي: 0) */
  animationDelay?: number;
  /** تفعيل الرسم المتحرك (افتراضي: true) */
  enableAnimation?: boolean;
  /** تفعيل الرسم المتحرك عند الظهور في الشاشة (افتراضي: true) */
  animateOnView?: boolean;
  /** دالة callback عند انتهاء الرسم المتحرك */
  onAnimationEnd?: () => void;
}

const AnimatedStatCard: React.FC<AnimatedStatCardProps> = ({
  title,
  amount,
  startValue = 0,
  icon,
  showCurrency = false,
  className = '',
  compactThreshold = 1000,
  unitType = 'english',
  changeText,
  animationDuration = 2.5,
  animationDelay = 0,
  enableAnimation = true,
  animateOnView = true,
  onAnimationEnd
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [cardRef, setCardRef] = useState<HTMLDivElement | null>(null);

  // مراقبة ظهور البطاقة في الشاشة
  useEffect(() => {
    if (!animateOnView || !cardRef) {
      setIsVisible(true);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    );

    observer.observe(cardRef);

    return () => {
      observer.disconnect();
    };
  }, [cardRef, animateOnView]);

  // معالجة انتهاء الرسم المتحرك
  const handleAnimationEnd = () => {
    if (onAnimationEnd) {
      onAnimationEnd();
    }
  };

  return (
    <div 
      ref={setCardRef}
      className={`touch-card stats-card transition-all duration-300 hover:shadow-lg ${className}`}
    >
      <div className="flex justify-between items-start mb-2">
        <div className="flex-1">
          <p className="text-secondary-500 dark:text-secondary-400 text-sm font-medium mb-1">
            {title}
          </p>
          
          {/* الرقم المتحرك */}
          {showCurrency ? (
            <AnimatedCompactCurrency
              end={amount}
              start={startValue}
              duration={animationDuration}
              delay={isVisible ? animationDelay : 0}
              compactThreshold={compactThreshold}
              unitType={unitType}
              size="medium"
              enableAnimation={enableAnimation && isVisible}
              onEnd={handleAnimationEnd}
            />
          ) : (
            <AnimatedCompactNumber
              end={amount}
              start={startValue}
              duration={animationDuration}
              delay={isVisible ? animationDelay : 0}
              compactThreshold={compactThreshold}
              unitType={unitType}
              size="medium"
              enableAnimation={enableAnimation && isVisible}
              onEnd={handleAnimationEnd}
            />
          )}
        </div>
        
        {/* الأيقونة مع تأثير متحرك */}
        {icon && (
          <div className={`
            p-4 rounded-full bg-primary-50 dark:bg-primary-900/30 text-xl
            transition-all duration-300 hover:scale-110 hover:rotate-3
            ${isVisible ? 'animate-fadeIn' : 'opacity-0'}
          `}>
            {icon}
          </div>
        )}
      </div>
      
      {/* نص التغيير أو الوصف */}
      {changeText && (
        <>
          <div className={`
            stats-card-divider transition-all duration-500 delay-200
            ${isVisible ? 'opacity-100' : 'opacity-0'}
          `}></div>
          <div className={`
            flex items-center text-sm text-secondary-500 dark:text-secondary-400
            transition-all duration-500 delay-300
            ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2'}
          `}>
            <span>{changeText}</span>
          </div>
        </>
      )}
    </div>
  );
};

export default AnimatedStatCard;

/**
 * مكون البطاقة الإحصائية المتحركة مع معدل النمو
 */
interface AnimatedGrowthCardProps {
  /** عنوان البطاقة */
  title: string;
  /** القيمة الحالية */
  currentValue: number;
  /** القيمة السابقة */
  previousValue: number;
  /** إظهار رمز العملة أم لا (افتراضي: true) */
  showCurrency?: boolean;
  /** فئة CSS إضافية */
  className?: string;
  /** مدة الرسم المتحرك بالثواني (افتراضي: 3) */
  animationDuration?: number;
  /** تأخير البدء بالثواني (افتراضي: 0.5) */
  animationDelay?: number;
  /** تفعيل الرسم المتحرك (افتراضي: true) */
  enableAnimation?: boolean;
}

export const AnimatedGrowthCard: React.FC<AnimatedGrowthCardProps> = ({
  title,
  currentValue,
  previousValue,
  showCurrency = true,
  className = '',
  animationDuration = 3,
  animationDelay = 0.5,
  enableAnimation = true
}) => {
  // حساب معدل النمو
  const growthRate = previousValue > 0 
    ? ((currentValue - previousValue) / previousValue) * 100 
    : 0;

  const isPositiveGrowth = growthRate >= 0;

  return (
    <div className={`touch-card stats-card ${className}`}>
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <p className="text-secondary-500 dark:text-secondary-400 text-sm font-medium mb-1">
            {title}
          </p>
          
          {/* القيمة الحالية المتحركة */}
          {showCurrency ? (
            <AnimatedCompactCurrency
              end={currentValue}
              duration={animationDuration}
              delay={animationDelay}
              enableAnimation={enableAnimation}
              size="medium"
            />
          ) : (
            <AnimatedCompactNumber
              end={currentValue}
              duration={animationDuration}
              delay={animationDelay}
              enableAnimation={enableAnimation}
              size="medium"
            />
          )}
          
          {/* معدل النمو المتحرك */}
          <div className={`
            flex items-center gap-2 mt-2
            transition-all duration-500 delay-1000
            ${enableAnimation ? 'animate-fadeInUp' : ''}
          `}>
            <span className={`
              text-sm font-bold
              ${isPositiveGrowth ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}
            `}>
              {isPositiveGrowth ? '+' : ''}{growthRate.toFixed(1)}%
            </span>
            <span className="text-xs text-secondary-500 dark:text-secondary-400">
              مقارنة بالفترة السابقة
            </span>
          </div>
        </div>
        
        {/* أيقونة النمو */}
        <div className={`
          p-4 rounded-full text-xl transition-all duration-300
          ${isPositiveGrowth 
            ? 'bg-green-50 dark:bg-green-900/30 text-green-600 dark:text-green-400' 
            : 'bg-red-50 dark:bg-red-900/30 text-red-600 dark:text-red-400'
          }
          ${enableAnimation ? 'animate-scaleIn' : ''}
        `}>
          {isPositiveGrowth ? '📈' : '📉'}
        </div>
      </div>
    </div>
  );
};

/**
 * Hook لإدارة مجموعة من البطاقات المتحركة
 */
export const useAnimatedCards = (cardCount: number, staggerDelay: number = 0.2) => {
  const [animationStates, setAnimationStates] = useState<boolean[]>(
    new Array(cardCount).fill(false)
  );

  const startStaggeredAnimation = () => {
    animationStates.forEach((_, index) => {
      setTimeout(() => {
        setAnimationStates(prev => {
          const newStates = [...prev];
          newStates[index] = true;
          return newStates;
        });
      }, index * staggerDelay * 1000);
    });
  };

  const resetAnimations = () => {
    setAnimationStates(new Array(cardCount).fill(false));
  };

  return {
    animationStates,
    startStaggeredAnimation,
    resetAnimations
  };
};
