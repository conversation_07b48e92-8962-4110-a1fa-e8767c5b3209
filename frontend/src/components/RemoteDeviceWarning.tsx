/**
 * مكون تحذيري للأجهزة البعيدة
 */

import React from 'react';
import { FaExclamationTriangle, FaServer, FaNetworkWired, FaInfoCircle } from 'react-icons/fa';
import { useDeviceDetection } from '../hooks/useDeviceDetection';

interface RemoteDeviceWarningProps {
  /** رسالة مخصصة للتحذير */
  customMessage?: string;
  /** إظهار معلومات تفصيلية عن الجهاز */
  showDetails?: boolean;
  /** نوع التحذير */
  variant?: 'warning' | 'info' | 'error';
  /** إخفاء التحذير إذا كان الجهاز رئيسي */
  hideIfMainServer?: boolean;
  /** فئات CSS إضافية */
  className?: string;
}

const RemoteDeviceWarning: React.FC<RemoteDeviceWarningProps> = ({
  customMessage,
  showDetails = false,
  variant = 'warning',
  hideIfMainServer = true,
  className = '',
}) => {
  const { isMainServer, isLoading, currentDevice, serverIdentity, message, error } = useDeviceDetection();

  // إخفاء التحذير إذا كان الجهاز رئيسي والخيار مفعل
  if (hideIfMainServer && isMainServer) {
    return null;
  }

  // إخفاء التحذير أثناء التحميل
  if (isLoading) {
    return null;
  }

  // تحديد ألوان التحذير حسب النوع
  const getVariantClasses = () => {
    switch (variant) {
      case 'error':
        return {
          container: 'bg-red-50 dark:bg-red-900/30 border-red-200 dark:border-red-800',
          text: 'text-red-700 dark:text-red-300',
          icon: 'text-red-600 dark:text-red-400',
        };
      case 'info':
        return {
          container: 'bg-blue-50 dark:bg-blue-900/30 border-blue-200 dark:border-blue-800',
          text: 'text-blue-700 dark:text-blue-300',
          icon: 'text-blue-600 dark:text-blue-400',
        };
      default: // warning
        return {
          container: 'bg-yellow-50 dark:bg-yellow-900/30 border-yellow-200 dark:border-yellow-800',
          text: 'text-yellow-700 dark:text-yellow-300',
          icon: 'text-yellow-600 dark:text-yellow-400',
        };
    }
  };

  const variantClasses = getVariantClasses();

  // تحديد الأيقونة حسب النوع
  const getIcon = () => {
    switch (variant) {
      case 'error':
        return <FaExclamationTriangle className={`ml-2 mt-0.5 flex-shrink-0 ${variantClasses.icon}`} />;
      case 'info':
        return <FaInfoCircle className={`ml-2 mt-0.5 flex-shrink-0 ${variantClasses.icon}`} />;
      default:
        return <FaNetworkWired className={`ml-2 mt-0.5 flex-shrink-0 ${variantClasses.icon}`} />;
    }
  };

  return (
    <div className={`border px-4 py-3 rounded-md ${variantClasses.container} ${className}`}>
      <div className="flex items-start">
        {getIcon()}
        <div className={variantClasses.text}>
          <div className="font-medium">
            {isMainServer ? (
              <span className="flex items-center">
                <FaServer className="ml-1" />
                الجهاز الرئيسي
              </span>
            ) : (
              <span className="flex items-center">
                <FaNetworkWired className="ml-1" />
                جهاز بعيد
              </span>
            )}
          </div>
          <div className="text-sm mt-1">
            {customMessage || message || (
              isMainServer 
                ? "أنت تستخدم التطبيق على الخادم الرئيسي - جميع الميزات متاحة"
                : "أنت تستخدم التطبيق من جهاز بعيد - بعض الميزات قد تكون محدودة"
            )}
          </div>
          
          {error && (
            <div className="text-xs mt-2 opacity-75">
              خطأ في التمييز: {error}
            </div>
          )}
          
          {showDetails && (currentDevice || serverIdentity) && (
            <div className="mt-3 text-xs space-y-2">
              {currentDevice && (
                <div className="bg-white/50 dark:bg-black/20 p-2 rounded border">
                  <div className="font-medium mb-1">معلومات الجهاز الحالي:</div>
                  <div>الاسم: {currentDevice.hostname}</div>
                  <div>النظام: {currentDevice.system}</div>
                  <div>المنصة: {currentDevice.platform}</div>
                  {currentDevice.fingerprint && (
                    <div className="font-mono text-xs opacity-75">
                      البصمة: {currentDevice.fingerprint.substring(0, 16)}...
                    </div>
                  )}
                </div>
              )}
              
              {serverIdentity && (
                <div className="bg-white/50 dark:bg-black/20 p-2 rounded border">
                  <div className="font-medium mb-1">معلومات الخادم الرئيسي:</div>
                  <div>الاسم: {serverIdentity.server_hostname}</div>
                  <div>المنصة: {serverIdentity.server_platform}</div>
                  {serverIdentity.server_fingerprint && (
                    <div className="font-mono text-xs opacity-75">
                      البصمة: {serverIdentity.server_fingerprint.substring(0, 16)}...
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RemoteDeviceWarning;
