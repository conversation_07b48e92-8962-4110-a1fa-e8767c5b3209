/**
 * مكون عرض نشاط الأجهزة البعيدة في الوقت الفعلي
 */

import React, { useState, useEffect } from 'react';
import {
  FaDesktop,
  FaLaptop,
  FaMobile,
  FaTabletAlt,
  FaCircle,
  FaUser,
  FaClock,
  FaEye,
  FaSync,
  FaChartLine
} from 'react-icons/fa';
import api from '../lib/axios';
import { formatDateTime } from '../services/dateTimeService';

interface RemoteDeviceActivity {
  device_id: string;
  client_ip: string;
  hostname: string;
  current_user: string | null;
  last_activity: string;
  status: 'online' | 'recently_active' | 'offline';
  device_type: string;
  system: string;
  browser: string;
  session_start: string;
  total_session_time: number;
  page_views: number;
  last_page: string;
  user_agent: string;
}

interface ActivitySummary {
  total_devices: number;
  online_devices: number;
  recently_active_devices: number;
  offline_devices: number;
  active_users_count: number;
  active_users: string[];
  activities: RemoteDeviceActivity[];
}

interface RemoteDeviceActivityProps {
  showSummaryOnly?: boolean;
  className?: string;
}

const RemoteDeviceActivity: React.FC<RemoteDeviceActivityProps> = ({
  showSummaryOnly = false,
  className = '',
}) => {
  const [summary, setSummary] = useState<ActivitySummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchActivitySummary = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await api.get('/api/remote-activity/summary');
      const data = response.data;

      if (data.success) {
        setSummary(data.data);
        setLastUpdated(new Date());
      } else {
        throw new Error(data.message || 'فشل في جلب بيانات نشاط الأجهزة البعيدة');
      }
    } catch (error: any) {
      setError(error.response?.data?.detail || error.message || 'فشل في جلب بيانات نشاط الأجهزة البعيدة');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchActivitySummary();
    
    // تحديث كل 30 ثانية
    const interval = setInterval(fetchActivitySummary, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType.toLowerCase()) {
      case 'mobile':
        return <FaMobile className="text-blue-500" />;
      case 'tablet':
        return <FaTabletAlt className="text-green-500" />;
      case 'laptop':
        return <FaLaptop className="text-purple-500" />;
      case 'desktop':
        return <FaDesktop className="text-gray-600" />;
      default:
        return <FaDesktop className="text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'text-green-500';
      case 'recently_active':
        return 'text-yellow-500';
      case 'offline':
        return 'text-gray-500';
      default:
        return 'text-gray-400';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'online':
        return 'متصل';
      case 'recently_active':
        return 'نشط مؤخراً';
      case 'offline':
        return 'غير متصل';
      default:
        return 'غير معروف';
    }
  };

  const formatSessionTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}س ${minutes}د`;
    }
    return `${minutes}د`;
  };

  if (isLoading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 ${className}`}>
        <div className="text-center">
          <div className="text-red-500 mb-2">خطأ في تحميل البيانات</div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mb-4">{error}</div>
          <button
            onClick={fetchActivitySummary}
            className="btn-primary-sm"
          >
            إعادة المحاولة
          </button>
        </div>
      </div>
    );
  }

  if (!summary) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 ${className}`}>
        <div className="text-center text-gray-500 dark:text-gray-400">
          لا توجد بيانات نشاط للأجهزة البعيدة
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <FaChartLine className="text-blue-500 text-xl" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              نشاط الأجهزة البعيدة
            </h3>
          </div>
          
          <div className="flex items-center gap-3">
            {lastUpdated && (
              <div className="text-xs text-gray-500 dark:text-gray-400">
                آخر تحديث: {formatDateTime(lastUpdated)}
              </div>
            )}
            
            <button
              onClick={fetchActivitySummary}
              className="btn-secondary-sm flex items-center gap-2"
              disabled={isLoading}
            >
              <FaSync className={isLoading ? 'animate-spin' : ''} />
              <span>تحديث</span>
            </button>
          </div>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="p-6">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {summary.total_devices}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">إجمالي الأجهزة</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-green-500">
              {summary.online_devices}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">متصل</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-500">
              {summary.recently_active_devices}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">نشط مؤخراً</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-500">
              {summary.active_users_count}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">مستخدمين نشطين</div>
          </div>
        </div>

        {/* Active Users */}
        {summary.active_users.length > 0 && (
          <div className="mb-6">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
              المستخدمين النشطين:
            </h4>
            <div className="flex flex-wrap gap-2">
              {summary.active_users.map((user, index) => (
                <span
                  key={index}
                  className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full"
                >
                  <FaUser className="text-xs" />
                  {user}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Device List */}
        {!showSummaryOnly && summary.activities.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">
              تفاصيل الأجهزة ({summary.activities.length}):
            </h4>
            
            <div className="space-y-3">
              {summary.activities.map((activity) => (
                <div
                  key={activity.device_id}
                  className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    {getDeviceIcon(activity.device_type)}
                    
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">
                        {activity.hostname}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        {activity.client_ip} • {activity.system}
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className={`flex items-center gap-1 text-sm font-medium ${getStatusColor(activity.status)}`}>
                      <FaCircle className="text-xs" />
                      {getStatusText(activity.status)}
                    </div>
                    
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {activity.current_user && (
                        <div className="flex items-center gap-1">
                          <FaUser className="text-xs" />
                          {activity.current_user}
                        </div>
                      )}
                      
                      <div className="flex items-center gap-1 mt-1">
                        <FaClock className="text-xs" />
                        {formatSessionTime(activity.total_session_time)}
                      </div>
                      
                      <div className="flex items-center gap-1 mt-1">
                        <FaEye className="text-xs" />
                        {activity.page_views} صفحة
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RemoteDeviceActivity;
