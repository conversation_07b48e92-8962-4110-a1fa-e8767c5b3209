import React from 'react';
import { FaCheckCircle } from 'react-icons/fa';
import Modal from './Modal';

interface SimpleSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  message: string;
  autoClose?: boolean;
  autoCloseDelay?: number;
}

const SimpleSuccessModal: React.FC<SimpleSuccessModalProps> = ({
  isOpen,
  onClose,
  message,
  autoClose = true,
  autoCloseDelay = 2000
}) => {
  React.useEffect(() => {
    if (isOpen && autoClose) {
      const timer = setTimeout(() => {
        onClose();
      }, autoCloseDelay);
      return () => clearTimeout(timer);
    }
  }, [isOpen, autoClose, autoCloseDelay, onClose]);

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="نجح" size="sm">
      <div className="text-center">
        {/* Success Icon */}
        <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/30 mb-6">
          <FaCheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
        </div>

        {/* Success Message */}
        <div className="mb-6">
          <p className="text-sm text-gray-600 dark:text-gray-300">
            {message}
          </p>
        </div>

        {/* Auto-close notice */}
        {autoClose && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3 mb-6">
            <p className="text-xs text-green-700 dark:text-green-400">
              ستُغلق هذه النافذة تلقائياً خلال ثوانٍ قليلة...
            </p>
          </div>
        )}

        {/* Close Button */}
        <button
          onClick={onClose}
          className="btn-primary flex items-center justify-center min-w-[120px] mx-auto"
        >
          <span>موافق</span>
        </button>
      </div>
    </Modal>
  );
};

export default SimpleSuccessModal;
