import React from 'react';
import { FaClock, FaCalendarAlt } from 'react-icons/fa';

interface FrequencyOption {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  cronPattern: string;
  examples: string[];
}

interface FrequencySelectorProps {
  value: string;
  onChange: (frequency: string) => void;
  className?: string;
  showExamples?: boolean;
}

const FrequencySelector: React.FC<FrequencySelectorProps> = ({
  value,
  onChange,
  className = '',
  showExamples = true
}) => {
  const frequencies: FrequencyOption[] = [
    {
      id: 'daily',
      name: 'يومياً',
      description: 'تشغيل يومي في وقت محدد',
      icon: <FaClock className="text-blue-500" />,
      cronPattern: '0 {hour} * * *',
      examples: ['كل يوم في الساعة 2:00 صباحاً', 'مناسب للنسخ الاحتياطية اليومية']
    },
    {
      id: 'weekly',
      name: 'أسبوعياً',
      description: 'تشغيل أسبوعي في يوم ووقت محددين',
      icon: <FaCalendarAlt className="text-green-500" />,
      cronPattern: '0 {hour} * * {days}',
      examples: ['كل أسبوع في أيام محددة', 'يمكن اختيار عدة أيام']
    },
    {
      id: 'monthly',
      name: 'شهرياً',
      description: 'تشغيل شهري في اليوم الأول',
      icon: <FaCalendarAlt className="text-purple-500" />,
      cronPattern: '0 {hour} 1 * *',
      examples: ['كل شهر في اليوم الأول', 'مناسب للتقارير الشهرية']
    },
    {
      id: 'workdays',
      name: 'أيام العمل',
      description: 'من الاثنين إلى الجمعة',
      icon: <FaClock className="text-orange-500" />,
      cronPattern: '0 {hour} * * 1-5',
      examples: ['من الاثنين إلى الجمعة', 'مناسب للمهام التجارية']
    },
    {
      id: 'custom',
      name: 'مخصص',
      description: 'إعداد مخصص متقدم',
      icon: <FaClock className="text-gray-500" />,
      cronPattern: '{minute} {hour} {day} {month} {dayOfWeek}',
      examples: ['تحكم كامل في جميع المعاملات', 'للمستخدمين المتقدمين']
    }
  ];

  const selectedFrequency = frequencies.find(f => f.id === value);

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {frequencies.map((frequency) => (
          <button
            key={frequency.id}
            type="button"
            onClick={() => onChange(frequency.id)}
            className={`
              p-4 rounded-lg border-2 text-right transition-all duration-200 hover:shadow-md
              ${value === frequency.id
                ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 shadow-md'
                : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300'
              }
            `}
          >
            <div className="flex items-start">
              <div className="flex-shrink-0 ml-3">
                {frequency.icon}
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-semibold text-base">{frequency.name}</h3>
                  {value === frequency.id && (
                    <div className="w-3 h-3 bg-primary-500 rounded-full"></div>
                  )}
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  {frequency.description}
                </p>
                <div className="text-xs text-gray-500 dark:text-gray-500">
                  نمط Cron: <code className="bg-gray-100 dark:bg-gray-700 px-1 rounded">
                    {frequency.cronPattern}
                  </code>
                </div>
              </div>
            </div>
          </button>
        ))}
      </div>

      {showExamples && selectedFrequency && (
        <div className="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
          <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center">
            <FaClock className="ml-2" />
            أمثلة على "{selectedFrequency.name}"
          </h4>
          <ul className="space-y-1">
            {selectedFrequency.examples.map((example, index) => (
              <li key={index} className="text-sm text-blue-700 dark:text-blue-300 flex items-center">
                <span className="w-2 h-2 bg-blue-400 rounded-full ml-2 flex-shrink-0"></span>
                {example}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default FrequencySelector;
