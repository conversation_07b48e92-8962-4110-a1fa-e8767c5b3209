import { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { FiShield, FiSearch, FiX } from 'react-icons/fi';
import api from '../lib/axios';

interface WarrantyType {
  id: number;
  name: string;
  name_ar: string;
  description?: string;
  duration_months: number;
  coverage_type: string;
  is_active: boolean;
}

interface WarrantyTypeSelectorProps {
  selectedWarrantyType: WarrantyType | null;
  onWarrantyTypeSelect: (warrantyType: WarrantyType | null) => void;
  className?: string;
}

export interface WarrantyTypeSelectorRef {
  refreshWarrantyTypes: () => void;
}

const WarrantyTypeSelector = forwardRef<WarrantyTypeSelectorRef, WarrantyTypeSelectorProps>(({
  selectedWarrantyType,
  onWarrantyTypeSelect,
  className = ''
}, ref) => {
  const [warrantyTypes, setWarrantyTypes] = useState<WarrantyType[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchWarrantyTypes();
  }, []);



  // Expose refresh function to parent component
  useImperativeHandle(ref, () => ({
    refreshWarrantyTypes: fetchWarrantyTypes
  }));

  const fetchWarrantyTypes = async (search = '') => {
    try {
      setLoading(true);

      const response = await api.get('/api/warranty-types', {
        params: {
          search: search || searchTerm,
          status: 'active'
        }
      });

      const newWarrantyTypes = response.data || [];
      console.log(`WarrantyTypeSelector: Fetched ${newWarrantyTypes.length} warranty types`);

      setWarrantyTypes(newWarrantyTypes);
    } catch (error) {
      console.error('Error fetching warranty types:', error);
    } finally {
      setLoading(false);
    }
  };

  // Handle search with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchWarrantyTypes(searchTerm);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  // Function to highlight search term in text
  const highlightSearchTerm = (text: string, searchTerm: string) => {
    if (!searchTerm) return text;

    const regex = new RegExp(`(${searchTerm})`, 'gi');
    const parts = text.split(regex);

    return parts.map((part, index) =>
      regex.test(part) ? (
        <span key={index} className="bg-yellow-200 dark:bg-yellow-800 text-yellow-900 dark:text-yellow-100 px-1 rounded">
          {part}
        </span>
      ) : part
    );
  };

  const handleWarrantyTypeSelect = (warrantyType: WarrantyType) => {
    onWarrantyTypeSelect(warrantyType);
    setIsDropdownOpen(false);
    setSearchTerm('');
  };

  const handleClearSelection = () => {
    onWarrantyTypeSelect(null);
    setSearchTerm('');
  };

  const getCoverageTypeLabel = (coverageType: string) => {
    const labels: { [key: string]: string } = {
      'full': 'شامل',
      'partial': 'جزئي',
      'limited': 'محدود'
    };
    return labels[coverageType] || coverageType;
  };

  return (
    <div className={`relative ${className}`}>
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        نوع الضمان *
      </label>

      {selectedWarrantyType ? (
        // Selected warranty type display
        <div className="flex items-center justify-between h-10 px-4 bg-primary-50 dark:bg-primary-900/20 border-2 border-primary-200 dark:border-primary-700 rounded-xl">
          <div className="flex items-center flex-1 min-w-0">
            <div className="flex-shrink-0 w-6 h-6 ml-1 mr-2 flex items-center justify-center">
              <div className="flex w-6 h-6 bg-primary-100 dark:bg-primary-900/30 rounded-full items-center justify-center">
                <FiShield className="text-primary-600 dark:text-primary-400 text-xs" />
              </div>
            </div>
            <div className="flex-1 min-w-0 flex items-center">
              <span className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                {selectedWarrantyType.name_ar}
              </span>
              <span className="text-xs text-blue-600 dark:text-blue-400 mr-2 flex-shrink-0">
                - {selectedWarrantyType.duration_months} شهر
              </span>
              <span className="text-xs text-green-600 dark:text-green-400 mr-2 flex-shrink-0">
                - {getCoverageTypeLabel(selectedWarrantyType.coverage_type)}
              </span>
            </div>
          </div>
          <button
            onClick={handleClearSelection}
            className="flex-shrink-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1"
            title="إلغاء الاختيار"
          >
            <FiX className="h-4 w-4" />
          </button>
        </div>
      ) : (
        // Warranty type selection interface
        <div>
          <div className="relative">
            <input
              type="text"
              placeholder="ابحث عن نوع ضمان..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setIsDropdownOpen(true);
              }}
              onFocus={() => setIsDropdownOpen(true)}
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              onKeyDown={(e) => {
                if (e.key === 'Escape') {
                  setSearchTerm('');
                  setIsDropdownOpen(false);
                } else if (e.key === 'Enter' && warrantyTypes.length > 0) {
                  handleWarrantyTypeSelect(warrantyTypes[0]);
                }
              }}
              className="w-full h-10 px-4 pr-10 pl-4 border-2 border-gray-300/60 dark:border-gray-600/40 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-4 focus:ring-primary-500/20 focus:border-primary-500 transition-all duration-200 ease-in-out"
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center">
              <FiSearch className="text-gray-400 dark:text-gray-500 h-4 w-4" />
            </div>
            {searchTerm && (
              <button
                onClick={() => {
                  setSearchTerm('');
                  setIsDropdownOpen(false);
                }}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <FiX className="h-4 w-4" />
              </button>
            )}
          </div>

          {/* Dropdown */}
          {isDropdownOpen && (
            <div className="absolute z-50 w-full mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-xl overflow-hidden">
              {/* Search results header */}
              {searchTerm && (
                <div className="px-4 py-2 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      نتائج البحث عن: "{searchTerm}"
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-500">
                      {warrantyTypes.length} نتيجة
                    </span>
                  </div>
                </div>
              )}
              
              {loading ? (
                <div className="p-6 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-3"></div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {searchTerm ? 'جاري البحث...' : 'جاري تحميل أنواع الضمانات...'}
                  </div>
                </div>
              ) : (
                <div className="max-h-60 overflow-y-auto custom-scrollbar-thin">
                  {warrantyTypes.length === 0 ? (
                    <div className="p-6 text-center">
                      <div className="text-gray-400 dark:text-gray-500 mb-2">
                        <FiSearch className="h-8 w-8 mx-auto mb-2" />
                      </div>
                      <div className="text-gray-500 dark:text-gray-400 font-medium">
                        {searchTerm ? 'لا توجد نتائج للبحث' : 'لا توجد أنواع ضمانات'}
                      </div>
                    </div>
                  ) : (
                    warrantyTypes.map((warrantyType) => (
                      <button
                        key={warrantyType.id}
                        onClick={() => handleWarrantyTypeSelect(warrantyType)}
                        className="w-full px-4 py-3 text-right hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-200 dark:border-gray-700 last:border-b-0"
                      >
                        <div className="flex items-center w-full">
                          <div className="flex-shrink-0 w-8 h-8 ml-1 mr-3">
                            <div className="flex w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-full items-center justify-center">
                              <FiShield className="text-gray-600 dark:text-gray-400 text-sm" />
                            </div>
                          </div>
                          <div className="flex-1">
                            <div className="text-sm font-medium text-gray-900 dark:text-gray-100 text-right">
                              {highlightSearchTerm(warrantyType.name_ar, searchTerm)}
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-xs text-blue-600 dark:text-blue-400">
                                {warrantyType.duration_months} شهر
                              </span>
                              <span className="text-xs text-green-600 dark:text-green-400">
                                {getCoverageTypeLabel(warrantyType.coverage_type)}
                              </span>
                            </div>
                            {warrantyType.description && (
                              <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 text-right">
                                {warrantyType.description}
                              </div>
                            )}
                          </div>
                        </div>
                      </button>
                    ))
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Backdrop to close dropdown */}
      {isDropdownOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsDropdownOpen(false)}
        />
      )}
    </div>
  );
});

WarrantyTypeSelector.displayName = 'WarrantyTypeSelector';

export default WarrantyTypeSelector;
