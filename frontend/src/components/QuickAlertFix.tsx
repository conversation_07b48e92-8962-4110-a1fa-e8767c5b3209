import React, { useState } from 'react';
import { Fa<PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaExclamationTriangle } from 'react-icons/fa';
import alertService from '../services/alertService';
import { advancedAlertService } from '../services/advancedAlertService';

interface FixResult {
  success: boolean;
  removedAlerts: number;
  beforeStats: any;
  afterStats: any;
  error?: string;
}

const QuickAlertFix: React.FC = () => {
  const [isFixing, setIsFixing] = useState(false);
  const [fixResult, setFixResult] = useState<FixResult | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  const performQuickFix = async () => {
    setIsFixing(true);
    setFixResult(null);

    try {
      // إحصائيات قبل الإصلاح
      const beforeStats = alertService.getAlertStatistics();

      // إزالة التنبيهات المشكوك فيها
      const alerts = alertService.getAlerts();
      const problematicAlerts = alerts.filter(alert =>
        alert.source === 'ALERT_SYSTEM' ||
        alert.source === 'CRITICAL_ERRORS' ||
        (alert.type === 'critical' && alert.title.includes('أخطاء حرجة')) ||
        (alert.type === 'critical' && alert.title.includes('تحذير'))
      );

      problematicAlerts.forEach(alert => {
        alertService.removeAlert(alert.id);
      });

      // إعادة تعيين العداد
      if (alertService.resetCriticalErrorCount) {
        alertService.resetCriticalErrorCount();
      }

      // تنظيف النظام المتقدم
      if (advancedAlertService && advancedAlertService.clearAllAlerts) {
        advancedAlertService.clearAllAlerts();
      }

      // تنظيف شامل
      if (alertService.performFullCleanup) {
        alertService.performFullCleanup();
      }

      // إحصائيات بعد الإصلاح
      const afterStats = alertService.getAlertStatistics();

      setFixResult({
        success: true,
        removedAlerts: problematicAlerts.length,
        beforeStats,
        afterStats
      });

      // إنشاء تنبيه نجاح
      alertService.addAlert({
        type: 'info',
        title: '✅ تم إصلاح النظام',
        message: `تم حل مشكلة التنبيهات المكررة. تم إزالة ${problematicAlerts.length} تنبيه مشكوك فيه.`,
        source: 'SYSTEM_REPAIR'
      });

    } catch (error) {
      setFixResult({
        success: false,
        removedAlerts: 0,
        beforeStats: {},
        afterStats: {},
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      });

      // محاولة إصلاح طارئ
      try {
        alertService.clearAllAlerts();
      } catch (emergencyError) {
        console.error('فشل الحل الطارئ:', emergencyError);
      }
    } finally {
      setIsFixing(false);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
      <div className="flex items-center gap-3 mb-4">
        <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
          <FaTools className="text-blue-600 dark:text-blue-400" />
        </div>
        <div>
          <h3 className="font-semibold text-gray-900 dark:text-gray-100">
            إصلاح سريع للتنبيهات
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            حل مشكلة التنبيهات المكررة والحرجة
          </p>
        </div>
      </div>

      <div className="space-y-3">
        <button
          onClick={performQuickFix}
          disabled={isFixing}
          className={`w-full flex items-center justify-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
            isFixing
              ? 'bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {isFixing ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-gray-400 border-t-transparent"></div>
              جاري الإصلاح...
            </>
          ) : (
            <>
              <FaTools />
              تشغيل الإصلاح السريع
            </>
          )}
        </button>

        {fixResult && (
          <div className={`p-3 rounded-lg border ${
            fixResult.success
              ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700'
              : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-700'
          }`}>
            <div className="flex items-center gap-2 mb-2">
              {fixResult.success ? (
                <FaCheck className="text-green-600 dark:text-green-400" />
              ) : (
                <FaExclamationTriangle className="text-red-600 dark:text-red-400" />
              )}
              <span className={`font-medium ${
                fixResult.success
                  ? 'text-green-800 dark:text-green-200'
                  : 'text-red-800 dark:text-red-200'
              }`}>
                {fixResult.success ? 'تم الإصلاح بنجاح!' : 'فشل الإصلاح'}
              </span>
            </div>

            {fixResult.success ? (
              <div className="text-sm text-green-700 dark:text-green-300">
                <p>تم إزالة {fixResult.removedAlerts} تنبيه مشكوك فيه</p>
                <p>التنبيهات المتبقية: {fixResult.afterStats.totalAlerts}</p>
              </div>
            ) : (
              <div className="text-sm text-red-700 dark:text-red-300">
                <p>خطأ: {fixResult.error}</p>
                <p>تم تطبيق حل طارئ بمسح جميع التنبيهات</p>
              </div>
            )}

            <button
              onClick={() => setShowDetails(!showDetails)}
              className="mt-2 text-xs text-blue-600 dark:text-blue-400 hover:underline"
            >
              {showDetails ? 'إخفاء التفاصيل' : 'عرض التفاصيل'}
            </button>

            {showDetails && (
              <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-700 rounded text-xs">
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <strong>قبل الإصلاح:</strong>
                    <pre className="text-xs">{JSON.stringify(fixResult.beforeStats, null, 2)}</pre>
                  </div>
                  <div>
                    <strong>بعد الإصلاح:</strong>
                    <pre className="text-xs">{JSON.stringify(fixResult.afterStats, null, 2)}</pre>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
          <p><strong>ما يفعله الإصلاح:</strong></p>
          <ul className="list-disc list-inside space-y-1 mr-4">
            <li>إزالة التنبيهات المكررة من النظام</li>
            <li>إعادة تعيين عداد الأخطاء الحرجة</li>
            <li>تنظيف التنبيهات القديمة والمعطوبة</li>
            <li>تنظيف النظام المتقدم</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default QuickAlertFix;
