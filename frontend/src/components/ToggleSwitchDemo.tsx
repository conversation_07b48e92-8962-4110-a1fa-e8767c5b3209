import React, { useState } from 'react';
import ToggleSwitch from './ToggleSwitch';

const ToggleSwitchDemo: React.FC = () => {
  const [smallToggle, setSmallToggle] = useState(false);
  const [mediumToggle, setMediumToggle] = useState(true);
  const [largeToggle, setLargeToggle] = useState(false);
  const [notifications, setNotifications] = useState(true);
  const [darkMode, setDarkMode] = useState(false);
  const [autoSave, setAutoSave] = useState(true);

  return (
    <div className="p-8 max-w-2xl mx-auto space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          مكون مفتاح التبديل الجديد
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          تصميم حديث يشبه مفاتيح iOS و Android
        </p>
      </div>

      {/* أمثلة على الأحجام المختلفة */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          الأحجام المختلفة
        </h2>
        <div className="space-y-4">
          <ToggleSwitch
            id="small-demo"
            checked={smallToggle}
            onChange={setSmallToggle}
            label="حجم صغير (افتراضي) - 40px × 20px"
            size="small"
          />
          <ToggleSwitch
            id="medium-demo"
            checked={mediumToggle}
            onChange={setMediumToggle}
            label="حجم متوسط - 48px × 24px"
            size="medium"
          />
          <ToggleSwitch
            id="large-demo"
            checked={largeToggle}
            onChange={setLargeToggle}
            label="حجم كبير - 56px × 28px"
            size="large"
          />
        </div>
      </div>

      {/* أمثلة عملية */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          أمثلة عملية
        </h2>
        <div className="space-y-4">
          <ToggleSwitch
            id="notifications"
            checked={notifications}
            onChange={setNotifications}
            label="تفعيل الإشعارات"
            size="small"
          />
          <ToggleSwitch
            id="dark-mode"
            checked={darkMode}
            onChange={setDarkMode}
            label="الوضع المظلم"
            size="medium"
          />
          <ToggleSwitch
            id="auto-save"
            checked={autoSave}
            onChange={setAutoSave}
            label="الحفظ التلقائي (إعداد مهم)"
            size="large"
          />
        </div>
      </div>

      {/* معلومات الحالة */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          حالة المفاتيح
        </h2>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium">حجم صغير:</span>
            <span className={`ml-2 ${smallToggle ? 'text-green-600' : 'text-red-600'}`}>
              {smallToggle ? 'مفعل' : 'غير مفعل'}
            </span>
          </div>
          <div>
            <span className="font-medium">حجم متوسط:</span>
            <span className={`ml-2 ${mediumToggle ? 'text-green-600' : 'text-red-600'}`}>
              {mediumToggle ? 'مفعل' : 'غير مفعل'}
            </span>
          </div>
          <div>
            <span className="font-medium">حجم كبير:</span>
            <span className={`ml-2 ${largeToggle ? 'text-green-600' : 'text-red-600'}`}>
              {largeToggle ? 'مفعل' : 'غير مفعل'}
            </span>
          </div>
          <div>
            <span className="font-medium">الإشعارات:</span>
            <span className={`ml-2 ${notifications ? 'text-green-600' : 'text-red-600'}`}>
              {notifications ? 'مفعلة' : 'غير مفعلة'}
            </span>
          </div>
          <div>
            <span className="font-medium">الوضع المظلم:</span>
            <span className={`ml-2 ${darkMode ? 'text-green-600' : 'text-red-600'}`}>
              {darkMode ? 'مفعل' : 'غير مفعل'}
            </span>
          </div>
          <div>
            <span className="font-medium">الحفظ التلقائي:</span>
            <span className={`ml-2 ${autoSave ? 'text-green-600' : 'text-red-600'}`}>
              {autoSave ? 'مفعل' : 'غير مفعل'}
            </span>
          </div>
        </div>
      </div>

      {/* معلومات تقنية */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          المزايا الجديدة
        </h2>
        <ul className="space-y-2 text-sm text-gray-700 dark:text-gray-300">
          <li className="flex items-center">
            <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
            حجم أصغر ومعقول (40px × 20px افتراضي)
          </li>
          <li className="flex items-center">
            <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
            مظهر حديث يشبه مفاتيح iOS/Android
          </li>
          <li className="flex items-center">
            <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
            ثلاثة أحجام قابلة للتخصيص
          </li>
          <li className="flex items-center">
            <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
            حركة سريعة وسلسة (200ms)
          </li>
          <li className="flex items-center">
            <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
            ألوان بسيطة وأنيقة
          </li>
          <li className="flex items-center">
            <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
            دعم محسن للوضع المظلم
          </li>
        </ul>
      </div>
    </div>
  );
};

export default ToggleSwitchDemo;