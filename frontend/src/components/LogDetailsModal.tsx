import React, { useState } from 'react';
import {
  FaExclamationTriangle,
  FaInfoCircle,
  FaTimesCircle,
  FaBug,
  FaServer,
  FaDatabase,
  FaDesktop,
  FaCheck,
  FaTimes,
  FaTerminal,
  FaCopy,
  FaCalendarAlt,
  FaClock,
  FaCode,
  FaPaperPlane,
  FaCheckCircle
} from 'react-icons/fa';
import Modal from './Modal';
import { FormattedDate, FormattedTime } from './FormattedDateTime';

interface LogDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  log: any;
  onResolve?: (logId: number) => void;
  onDelete?: (logId: number) => void;
  onSendToSupport?: (log: any) => void;
}

const LogDetailsModal: React.FC<LogDetailsModalProps> = ({
  isOpen,
  onClose,
  log,
  onResolve,
  onDelete,
  onSendToSupport
}) => {
  // حالة النسخ
  const [copyStates, setCopyStates] = useState<{[key: string]: boolean}>({});

  if (!log) return null;

  // الحصول على أيقونة المستوى
  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'CRITICAL':
        return <FaTimesCircle className="text-red-600" />;
      case 'ERROR':
        return <FaExclamationTriangle className="text-red-500" />;
      case 'WARNING':
        return <FaExclamationTriangle className="text-yellow-500" />;
      case 'INFO':
        return <FaInfoCircle className="text-blue-500" />;
      default:
        return <FaBug className="text-gray-500" />;
    }
  };

  // الحصول على أيقونة المصدر
  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'FRONTEND':
        return <FaDesktop className="text-blue-600" />;
      case 'BACKEND':
        return <FaServer className="text-green-600" />;
      case 'DATABASE':
        return <FaDatabase className="text-purple-600" />;
      case 'SYSTEM':
        return <FaTerminal className="text-gray-600" />;
      default:
        return <FaBug className="text-gray-500" />;
    }
  };

  // نسخ النص إلى الحافظة مع إشعار
  const copyToClipboard = async (text: string, key: string) => {
    try {
      await navigator.clipboard.writeText(text);

      // إظهار إشعار النجاح
      setCopyStates(prev => ({ ...prev, [key]: true }));

      // إخفاء الإشعار بعد 2 ثانية
      setTimeout(() => {
        setCopyStates(prev => ({ ...prev, [key]: false }));
      }, 2000);
    } catch (error) {
      console.error('فشل في نسخ النص:', error);

      // fallback للمتصفحات القديمة
      try {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        // إظهار إشعار النجاح
        setCopyStates(prev => ({ ...prev, [key]: true }));
        setTimeout(() => {
          setCopyStates(prev => ({ ...prev, [key]: false }));
        }, 2000);
      } catch (fallbackError) {
        console.error('فشل في النسخ باستخدام fallback:', fallbackError);
      }
    }
  };

  // تنسيق التاريخ والوقت باستخدام المكونات الموحدة
  const getFormattedDateTime = (timestamp: string) => {
    return {
      dateComponent: <FormattedDate date={timestamp} />,
      timeComponent: <FormattedTime date={timestamp} />
    };
  };

  const dateTime = getFormattedDateTime(log.timestamp);

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="تفاصيل السجل"
      size="xl"
    >
      <div className="space-y-6">
        {/* Header مع معلومات أساسية */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center gap-3">
              {getLevelIcon(log.level)}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  سجل {log.level}
                </h3>
                <div className="flex items-center gap-2 mt-1">
                  {getSourceIcon(log.source)}
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    المصدر: {log.source}
                  </span>
                </div>
              </div>
            </div>
            
            {/* حالة السجل */}
            <div className="text-right">
              {log.resolved ? (
                <span className="inline-flex items-center gap-1 px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 text-sm rounded-full">
                  <FaCheck className="w-4 h-4" />
                  محلول
                </span>
              ) : log.sent_to_support ? (
                // السجلات المرسلة للدعم
                <span className="inline-flex items-center gap-1 px-3 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 text-sm rounded-full">
                  <FaPaperPlane className="w-4 h-4" />
                  تم إرساله للدعم
                </span>
              ) : (
                // تصنيف الحالة حسب نوع السجل
                log.level === 'INFO' ? (
                  <span className="inline-flex items-center gap-1 px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-sm rounded-full">
                    <FaInfoCircle className="w-4 h-4" />
                    معلومة
                  </span>
                ) : log.level === 'CRITICAL' ? (
                  <span className="inline-flex items-center gap-1 px-3 py-1 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 text-sm rounded-full">
                    <FaPaperPlane className="w-4 h-4" />
                    يتطلب دعم فني
                  </span>
                ) : (
                  <span className="inline-flex items-center gap-1 px-3 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 text-sm rounded-full">
                    <FaExclamationTriangle className="w-4 h-4" />
                    قيد المعالجة
                  </span>
                )
              )}
            </div>
          </div>

          {/* معلومات التوقيت */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
              <FaCalendarAlt className="text-blue-500" />
              <span>{dateTime.dateComponent}</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
              <FaClock className="text-green-500" />
              <span>{dateTime.timeComponent}</span>
            </div>
          </div>
        </div>

        {/* الرسالة الرئيسية */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-md font-semibold text-gray-900 dark:text-gray-100">
              الرسالة
            </h4>
            <button
              onClick={() => copyToClipboard(log.message, 'message')}
              className={`transition-all duration-200 ${
                copyStates.message
                  ? 'text-green-600 dark:text-green-400'
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
              title={copyStates.message ? 'تم النسخ!' : 'نسخ الرسالة'}
            >
              {copyStates.message ? (
                <FaCheckCircle className="w-4 h-4" />
              ) : (
                <FaCopy className="w-4 h-4" />
              )}
            </button>
          </div>
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4 relative">
            <p className="text-gray-900 dark:text-gray-100 leading-relaxed">
              {log.message}
            </p>
            {copyStates.message && (
              <div className="absolute top-2 left-2 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 px-2 py-1 rounded text-xs flex items-center gap-1 animate-pulse">
                <FaCheckCircle className="w-3 h-3" />
                تم النسخ!
              </div>
            )}
          </div>
        </div>

        {/* التفاصيل التقنية */}
        {log.details && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="text-md font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                <FaCode className="text-purple-500" />
                التفاصيل التقنية
              </h4>
              <button
                onClick={() => copyToClipboard(log.details, 'details')}
                className={`transition-all duration-200 ${
                  copyStates.details
                    ? 'text-green-600 dark:text-green-400'
                    : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
                title={copyStates.details ? 'تم النسخ!' : 'نسخ التفاصيل'}
              >
                {copyStates.details ? (
                  <FaCheckCircle className="w-4 h-4" />
                ) : (
                  <FaCopy className="w-4 h-4" />
                )}
              </button>
            </div>
            <div className="bg-gray-900 dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4 overflow-x-auto relative">
              <pre className="text-sm text-green-400 font-mono whitespace-pre-wrap">
                {log.details}
              </pre>
              {copyStates.details && (
                <div className="absolute top-2 left-2 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 px-2 py-1 rounded text-xs flex items-center gap-1 animate-pulse">
                  <FaCheckCircle className="w-3 h-3" />
                  تم النسخ!
                </div>
              )}
            </div>
          </div>
        )}

        {/* ملاحظات الحل */}
        {log.resolution_notes && (
          <div className="space-y-3">
            <h4 className="text-md font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
              <FaCheck className="text-green-500" />
              ملاحظات الحل
            </h4>
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
              <p className="text-green-800 dark:text-green-300 leading-relaxed">
                {log.resolution_notes}
              </p>
            </div>
          </div>
        )}

        {/* معلومات إضافية */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <h4 className="text-md font-semibold text-gray-900 dark:text-gray-100 mb-3">
            معلومات إضافية
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600 dark:text-gray-400">رقم السجل:</span>
              <span className="text-gray-900 dark:text-gray-100 font-mono mr-2">#{log.id}</span>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">المستوى:</span>
              <span className="text-gray-900 dark:text-gray-100 mr-2">{log.level}</span>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">المصدر:</span>
              <span className="text-gray-900 dark:text-gray-100 mr-2">{log.source}</span>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">الطابع الزمني:</span>
              <span className="text-gray-900 dark:text-gray-100 font-mono mr-2 text-xs">
                {log.timestamp}
              </span>
            </div>
            {log.sent_to_support && (
              <div>
                <span className="text-gray-600 dark:text-gray-400">حالة الدعم:</span>
                <span className="text-purple-600 dark:text-purple-400 mr-2">تم الإرسال للدعم</span>
              </div>
            )}
            {log.support_sent_at && (
              <div>
                <span className="text-gray-600 dark:text-gray-400">تاريخ إرسال الدعم:</span>
                <span className="text-gray-900 dark:text-gray-100 font-mono mr-2 text-xs">
                  {new Date(log.support_sent_at).toLocaleString('ar-LY')}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* أزرار الإجراءات */}
        <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
          {/* إجراءات حسب حالة ونوع السجل */}
          {!log.resolved && !log.sent_to_support && (
            <>
              {log.level === 'INFO' && onDelete && (
                // سجلات المعلومات - يمكن حذفها
                <button
                  onClick={() => {
                    onDelete(log.id);
                    onClose();
                  }}
                  className="btn-danger flex items-center justify-center gap-2"
                >
                  <FaTimes />
                  <span>حذف السجل</span>
                </button>
              )}

              {log.level === 'CRITICAL' && onSendToSupport && (
                // الأخطاء الحرجة - يجب إرسالها للدعم
                <button
                  onClick={() => {
                    onSendToSupport(log);
                    onClose();
                  }}
                  className="btn-warning flex items-center justify-center gap-2"
                >
                  <FaPaperPlane />
                  <span>إرسال للدعم الفني</span>
                </button>
              )}

              {(log.level === 'ERROR' || log.level === 'WARNING') && onResolve && (
                // الأخطاء العادية والتحذيرات - يمكن حلها
                <button
                  onClick={() => {
                    onResolve(log.id);
                    onClose();
                  }}
                  className="btn-primary flex items-center justify-center gap-2"
                >
                  <FaCheck />
                  <span>حل المشكلة</span>
                </button>
              )}
            </>
          )}

          {/* رسالة للسجلات المرسلة للدعم */}
          {log.sent_to_support && !log.resolved && (
            <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-3">
              <div className="flex items-center gap-2 text-purple-800 dark:text-purple-300">
                <FaPaperPlane className="w-4 h-4" />
                <span className="text-sm font-medium">تم إرسال هذا السجل للدعم الفني</span>
              </div>
              <p className="text-xs text-purple-600 dark:text-purple-400 mt-1">
                في انتظار رد فريق الدعم الفني خلال 24 ساعة
              </p>
              {log.support_sent_at && (
                <p className="text-xs text-purple-500 dark:text-purple-500 mt-1">
                  تاريخ الإرسال: {new Date(log.support_sent_at).toLocaleString('ar-LY')}
                </p>
              )}
            </div>
          )}

          {/* رسالة للسجلات المحلولة */}
          {log.resolved && (
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
              <div className="flex items-center gap-2 text-green-800 dark:text-green-300">
                <FaCheck className="w-4 h-4" />
                <span className="text-sm font-medium">تم حل هذه المشكلة بنجاح</span>
              </div>
              <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                لا توجد إجراءات إضافية مطلوبة
              </p>
            </div>
          )}

          {/* أزرار عامة */}
          <button
            onClick={() => copyToClipboard(JSON.stringify(log, null, 2), 'fullData')}
            className={`btn-secondary flex items-center justify-center gap-2 transition-all duration-200 ${
              copyStates.fullData ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' : ''
            }`}
          >
            {copyStates.fullData ? (
              <FaCheckCircle />
            ) : (
              <FaCopy />
            )}
            <span>{copyStates.fullData ? 'تم النسخ!' : 'نسخ كامل البيانات'}</span>
          </button>

          <button
            onClick={onClose}
            className="btn-outline flex items-center justify-center gap-2"
          >
            إغلاق
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default LogDetailsModal;
