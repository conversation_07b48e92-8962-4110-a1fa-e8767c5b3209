import React from 'react';
import { FaExclamationTriangle, FaFolder, FaInfoCircle, FaDesktop } from 'react-icons/fa';
import Modal from './Modal';

interface FolderSelectionErrorModalProps {
  isOpen: boolean;
  onClose: () => void;
  error: string;
  autoClose?: boolean;
  autoCloseDelay?: number;
}

const FolderSelectionErrorModal: React.FC<FolderSelectionErrorModalProps> = ({
  isOpen,
  onClose,
  error,
  autoClose = false,
  autoCloseDelay = 5000
}) => {
  React.useEffect(() => {
    if (isOpen && autoClose) {
      const timer = setTimeout(() => {
        onClose();
      }, autoCloseDelay);
      return () => clearTimeout(timer);
    }
  }, [isOpen, autoClose, autoCloseDelay, onClose]);

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="فشل في اختيار المجلد" size="md" zIndex="highest">
      <div className="text-center py-2">
        {/* Error Icon */}
        <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/30 mb-6">
          <FaExclamationTriangle className="h-8 w-8 text-red-600 dark:text-red-400" />
        </div>

        {/* Error Message */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            ❌ فشل في اختيار المجلد
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            حدث خطأ أثناء محاولة اختيار مجلد النسخ الاحتياطية
          </p>
        </div>

        {/* Error Details Card */}
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6 text-right">
          <h5 className="text-sm font-semibold text-red-800 dark:text-red-200 mb-3 flex items-center justify-center">
            <FaExclamationTriangle className="ml-2" />
            تفاصيل الخطأ:
          </h5>
          
          <div className="bg-white dark:bg-red-900/30 rounded border p-3">
            <p className="text-sm font-mono text-red-700 dark:text-red-300 break-words">
              {error}
            </p>
          </div>
        </div>

        {/* Solutions Card */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6 text-right">
          <h5 className="text-sm font-semibold text-blue-800 dark:text-blue-200 mb-3 flex items-center justify-center">
            <FaInfoCircle className="ml-2" />
            💡 حلول مقترحة:
          </h5>
          
          <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-2">
            <li className="flex items-start">
              <span className="ml-2">•</span>
              <span>تأكد من أن النظام يدعم نوافذ اختيار المجلدات</span>
            </li>
            <li className="flex items-start">
              <span className="ml-2">•</span>
              <span>جرب استخدام زر "نافذة مخصصة" كبديل</span>
            </li>
            <li className="flex items-start">
              <span className="ml-2">•</span>
              <span>يمكنك إدخال المسار يدوياً في الحقل</span>
            </li>
            <li className="flex items-start">
              <span className="ml-2">•</span>
              <span>تأكد من تشغيل التطبيق بصلاحيات كافية</span>
            </li>
          </ul>
        </div>

        {/* Alternative Options */}
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6 text-right">
          <h5 className="text-sm font-semibold text-green-800 dark:text-green-200 mb-3 flex items-center justify-center">
            <FaFolder className="ml-2" />
            🔄 خيارات بديلة:
          </h5>
          
          <div className="space-y-2 text-xs text-green-700 dark:text-green-300">
            <div className="flex items-center justify-center">
              <FaDesktop className="ml-2 text-blue-600" />
              <span>استخدم زر "نافذة مخصصة" للاختيار</span>
            </div>
            <div className="flex items-center justify-center">
              <FaFolder className="ml-2 text-primary-600" />
              <span>أو أدخل المسار يدوياً: مثل C:\Backups</span>
            </div>
          </div>
        </div>

        {/* Auto-close notice */}
        {autoClose && (
          <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-3 mb-6">
            <p className="text-xs text-gray-600 dark:text-gray-400">
              ستُغلق هذه النافذة تلقائياً خلال ثوانٍ قليلة...
            </p>
          </div>
        )}

        {/* Close Button */}
        <button
          onClick={onClose}
          className="btn-danger flex items-center justify-center min-w-[120px] mx-auto text-sm"
        >
          <FaExclamationTriangle className="ml-2" />
          <span>موافق</span>
        </button>
      </div>
    </Modal>
  );
};

export default FolderSelectionErrorModal;
