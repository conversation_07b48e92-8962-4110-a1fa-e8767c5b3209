import React, { useState, useEffect } from 'react';
import { FaChevronUp } from 'react-icons/fa';

interface ScrollToTopButtonProps {
  /** عتبة التمرير لإظهار الزر (بالبكسل) */
  threshold?: number;
  /** مدة الانتقال السلس للعودة للأعلى (بالميلي ثانية) */
  duration?: number;
  /** فئة CSS إضافية */
  className?: string;
}

const ScrollToTopButton: React.FC<ScrollToTopButtonProps> = ({
  threshold = 300,
  duration = 800,
  className = ''
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);

  // مراقبة التمرير لإظهار/إخفاء الزر
  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > threshold) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    // إضافة مستمع التمرير مع throttling لتحسين الأداء
    let timeoutId: NodeJS.Timeout;
    const handleScroll = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(toggleVisibility, 10);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    
    // فحص الموضع الحالي عند التحميل
    toggleVisibility();

    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
    };
  }, [threshold]);

  // دالة العودة للأعلى مع انتقال سلس
  const scrollToTop = () => {
    if (isScrolling) return; // منع النقرات المتعددة

    setIsScrolling(true);
    
    const startPosition = window.pageYOffset;
    const startTime = performance.now();

    const animateScroll = (currentTime: number) => {
      const timeElapsed = currentTime - startTime;
      const progress = Math.min(timeElapsed / duration, 1);
      
      // استخدام easing function للحصول على انتقال سلس
      const easeInOutCubic = (t: number): number => {
        return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
      };
      
      const easedProgress = easeInOutCubic(progress);
      const currentPosition = startPosition * (1 - easedProgress);
      
      window.scrollTo(0, currentPosition);
      
      if (progress < 1) {
        requestAnimationFrame(animateScroll);
      } else {
        setIsScrolling(false);
      }
    };

    requestAnimationFrame(animateScroll);
  };

  // عدم عرض الزر إذا لم يكن مرئياً
  if (!isVisible) {
    return null;
  }

  return (
    <button
      onClick={scrollToTop}
      disabled={isScrolling}
      className={`
        fixed bottom-6 right-6 z-50
        w-12 h-12 
        bg-primary-600 hover:bg-primary-700 
        dark:bg-primary-500 dark:hover:bg-primary-600
        text-white 
        rounded-full 
        shadow-lg hover:shadow-xl
        transition-all duration-300 ease-in-out
        flex items-center justify-center
        group
        ${isScrolling ? 'opacity-70 cursor-not-allowed' : 'opacity-90 hover:opacity-100'}
        ${isVisible ? 'animate-fadeInUp' : 'animate-fadeOutDown'}
        ${className}
      `}
      aria-label="العودة للأعلى"
      title="العودة للأعلى"
    >
      <FaChevronUp 
        className={`
          text-lg transition-transform duration-200 
          ${isScrolling ? 'animate-pulse' : 'group-hover:scale-110'}
        `} 
      />
      
      {/* تأثير الموجة عند النقر */}
      <div className="absolute inset-0 rounded-full bg-white opacity-0 group-active:opacity-20 transition-opacity duration-150" />
    </button>
  );
};

export default ScrollToTopButton;
