import React, { useState, useEffect } from 'react';
import {
  FaExclamationTriangle,
  FaExclamationCircle,
  FaTimes,
  FaCheck,
  FaInfoCircle,
  FaTools,
  FaNetworkWired,
  FaCog,
  FaPlay,
  FaChevronDown,
  FaChevronUp
} from 'react-icons/fa';
import scheduledTaskErrorService, { TaskError } from '../services/scheduledTaskErrorService';

interface TaskErrorDisplayProps {
  taskId: number;
  taskName: string;
  className?: string;
}

const TaskErrorDisplay: React.FC<TaskErrorDisplayProps> = ({
  taskId,
  taskName: _taskName,
  className = ''
}) => {
  const [errors, setErrors] = useState<TaskError[]>([]);
  const [expandedErrors, setExpandedErrors] = useState<Set<string>>(new Set());
  const [isResolving, setIsResolving] = useState<Set<string>>(new Set());

  useEffect(() => {
    // تحميل الأخطاء الأولية
    const taskErrors = scheduledTaskErrorService.getTaskErrorsById(taskId);
    setErrors(taskErrors);

    // الاشتراك في التحديثات
    const unsubscribe = scheduledTaskErrorService.addErrorListener((allErrors) => {
      const taskErrors = allErrors.filter(error => error.taskId === taskId);
      setErrors(taskErrors);
    });

    return unsubscribe;
  }, [taskId]);

  // فلترة الأخطاء غير المحلولة
  const unresolvedErrors = errors.filter(error => !error.resolved);

  if (unresolvedErrors.length === 0) {
    return null;
  }

  const getSeverityIcon = (severity: TaskError['severity']) => {
    switch (severity) {
      case 'CRITICAL':
        return <FaExclamationTriangle className="text-red-600" />;
      case 'ERROR':
        return <FaExclamationCircle className="text-orange-600" />;
      case 'WARNING':
        return <FaInfoCircle className="text-yellow-600" />;
      default:
        return <FaInfoCircle className="text-blue-600" />;
    }
  };

  const getCategoryIcon = (category: TaskError['category']) => {
    switch (category) {
      case 'NETWORK':
        return <FaNetworkWired className="text-blue-500" />;
      case 'CONFIGURATION':
        return <FaCog className="text-purple-500" />;
      case 'SYSTEM':
        return <FaTools className="text-gray-500" />;
      case 'EXECUTION':
        return <FaPlay className="text-green-500" />;
      default:
        return <FaInfoCircle className="text-gray-500" />;
    }
  };

  const getSeverityColor = (severity: TaskError['severity']) => {
    switch (severity) {
      case 'CRITICAL':
        return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200';
      case 'ERROR':
        return 'bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800 text-orange-800 dark:text-orange-200';
      case 'WARNING':
        return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200';
      default:
        return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200';
    }
  };

  const toggleErrorExpansion = (errorId: string) => {
    const newExpanded = new Set(expandedErrors);
    if (newExpanded.has(errorId)) {
      newExpanded.delete(errorId);
    } else {
      newExpanded.add(errorId);
    }
    setExpandedErrors(newExpanded);
  };

  const handleResolveError = async (errorId: string) => {
    try {
      setIsResolving(prev => new Set(prev).add(errorId));
      await scheduledTaskErrorService.resolveTaskError(errorId, 'تم حل المشكلة يدوياً');
    } catch (error) {
      console.error('خطأ في حل المشكلة:', error);
    } finally {
      setIsResolving(prev => {
        const newSet = new Set(prev);
        newSet.delete(errorId);
        return newSet;
      });
    }
  };

  const handleDeleteError = async (errorId: string) => {
    try {
      await scheduledTaskErrorService.deleteTaskError(errorId);
    } catch (error) {
      console.error('خطأ في حذف المشكلة:', error);
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('ar-LY', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className={`space-y-2 ${className}`}>
      {unresolvedErrors.map((error) => (
        <div
          key={error.id}
          className={`border rounded-lg p-3 ${getSeverityColor(error.severity)}`}
        >
          <div className="flex items-start justify-between">
            <div className="flex items-start flex-1">
              <div className="flex items-center ml-2">
                {getSeverityIcon(error.severity)}
                <span className="mr-1 text-xs font-medium">
                  {error.severity === 'CRITICAL' ? 'حرج' :
                   error.severity === 'ERROR' ? 'خطأ' : 'تحذير'}
                </span>
              </div>

              <div className="flex-1">
                <div className="flex items-center mb-1">
                  {getCategoryIcon(error.category)}
                  <span className="mr-1 text-xs text-gray-600 dark:text-gray-400">
                    {error.category === 'NETWORK' ? 'شبكة' :
                     error.category === 'CONFIGURATION' ? 'إعدادات' :
                     error.category === 'SYSTEM' ? 'نظام' : 'تنفيذ'}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400 mr-2">
                    {formatTimestamp(error.timestamp)}
                  </span>
                </div>

                <p className="text-sm font-medium mb-1">
                  {error.errorMessage}
                </p>

                {error.errorDetails && (
                  <button
                    onClick={() => toggleErrorExpansion(error.id)}
                    className="text-xs text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 flex items-center"
                  >
                    {expandedErrors.has(error.id) ? (
                      <>
                        <FaChevronUp className="ml-1" />
                        إخفاء التفاصيل
                      </>
                    ) : (
                      <>
                        <FaChevronDown className="ml-1" />
                        عرض التفاصيل
                      </>
                    )}
                  </button>
                )}

                {expandedErrors.has(error.id) && error.errorDetails && (
                  <div className="mt-2 p-2 bg-white dark:bg-gray-800 rounded border text-xs">
                    <pre className="whitespace-pre-wrap text-gray-700 dark:text-gray-300">
                      {error.errorDetails}
                    </pre>
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-1 space-x-reverse">
              <button
                onClick={() => handleResolveError(error.id)}
                disabled={isResolving.has(error.id)}
                className="p-1 text-green-600 hover:bg-green-100 dark:hover:bg-green-900/30 rounded transition-colors disabled:opacity-50"
                title="حل المشكلة"
              >
                {isResolving.has(error.id) ? (
                  <div className="w-3 h-3 border border-green-600 border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  <FaCheck className="text-xs" />
                )}
              </button>

              <button
                onClick={() => handleDeleteError(error.id)}
                className="p-1 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/30 rounded transition-colors"
                title="حذف"
              >
                <FaTimes className="text-xs" />
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default TaskErrorDisplay;
