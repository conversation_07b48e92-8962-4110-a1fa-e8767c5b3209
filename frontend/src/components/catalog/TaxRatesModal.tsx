import React, { useState, useEffect } from 'react';
import {
  FiPlus,
  FiEdit,
  FiTrash,
  FiX,
  FiPercent,
  FiDollarSign,
  FiEye,
  FiEyeOff,
  FiCalendar
} from 'react-icons/fi';
import { TaxType } from '../../stores/taxTypeStore';
import useTaxRateStore, { TaxRate } from '../../stores/taxRateStore';
import { TextInput, SelectInput, NumberInput } from '../inputs';
import TextArea from '../inputs/TextArea';
import DatePicker from '../DatePicker';
import ToggleSwitch from '../ToggleSwitch';
import DeleteConfirmModal from '../DeleteConfirmModal';
import SuccessModal from '../SuccessModal';

interface TaxRatesModalProps {
  isOpen: boolean;
  onClose: () => void;
  taxType: TaxType | null;
}

interface TaxRateForm {
  name: string;
  rate_value: string;
  description: string;
  effective_from: string;
  effective_to: string;
  is_default: boolean;
  is_active: boolean;
  applies_to: 'all' | 'products' | 'services';
  min_amount: string;
  max_amount: string;
  tax_code: string;
  sort_order: number;
}

const TaxRatesModal: React.FC<TaxRatesModalProps> = ({ isOpen, onClose, taxType }) => {
  const {
    taxRates,
    loading,
    fetchTaxRates,
    createTaxRate,
    updateTaxRate,
    deleteTaxRate
  } = useTaxRateStore();

  // State
  const [rateModal, setRateModal] = useState({
    isOpen: false,
    mode: 'create' as 'create' | 'edit',
    rate: null as TaxRate | null
  });

  const [deleteModal, setDeleteModal] = useState({
    isOpen: false,
    id: 0,
    name: '',
    isLoading: false
  });

  const [successModal, setSuccessModal] = useState({
    isOpen: false,
    message: ''
  });

  const [rateForm, setRateForm] = useState<TaxRateForm>({
    name: '',
    rate_value: '',
    description: '',
    effective_from: '',
    effective_to: '',
    is_default: false,
    is_active: true,
    applies_to: 'all',
    min_amount: '',
    max_amount: '',
    tax_code: '',
    sort_order: 0
  });

  // Load tax rates when modal opens
  useEffect(() => {
    if (isOpen && taxType) {
      fetchTaxRates({ tax_type_id: taxType.id });
    }
  }, [isOpen, taxType]);

  // Filter rates for current tax type
  const currentTaxRates = taxRates.filter(rate => rate.tax_type_id === taxType?.id);

  const handleCreateRate = () => {
    setRateForm({
      name: '',
      rate_value: '',
      description: '',
      effective_from: '',
      effective_to: '',
      is_default: false,
      is_active: true,
      applies_to: 'all',
      min_amount: '',
      max_amount: '',
      tax_code: '',
      sort_order: 0
    });
    setRateModal({ isOpen: true, mode: 'create', rate: null });
  };

  const handleEditRate = (rate: TaxRate) => {
    setRateForm({
      name: rate.name,
      rate_value: rate.rate_value.toString(),
      description: rate.description || '',
      effective_from: rate.effective_from || '',
      effective_to: rate.effective_to || '',
      is_default: rate.is_default,
      is_active: rate.is_active,
      applies_to: rate.applies_to,
      min_amount: rate.min_amount?.toString() || '',
      max_amount: rate.max_amount?.toString() || '',
      tax_code: rate.tax_code || '',
      sort_order: rate.sort_order
    });
    setRateModal({ isOpen: true, mode: 'edit', rate });
  };

  const handleDeleteRate = (rate: TaxRate) => {
    setDeleteModal({
      isOpen: true,
      id: rate.id,
      name: rate.name,
      isLoading: false
    });
  };

  const handleSaveRate = async () => {
    try {
      if (!taxType) return;

      const formData = {
        tax_type_id: taxType.id,
        name: rateForm.name,
        rate_value: parseFloat(rateForm.rate_value),
        description: rateForm.description || undefined,
        effective_from: rateForm.effective_from || undefined,
        effective_to: rateForm.effective_to || undefined,
        is_default: rateForm.is_default,
        is_active: rateForm.is_active,
        applies_to: rateForm.applies_to,
        min_amount: rateForm.min_amount ? parseFloat(rateForm.min_amount) : undefined,
        max_amount: rateForm.max_amount ? parseFloat(rateForm.max_amount) : undefined,
        tax_code: rateForm.tax_code || undefined,
        sort_order: rateForm.sort_order
      };

      if (rateModal.mode === 'create') {
        await createTaxRate(formData);
        setSuccessModal({
          isOpen: true,
          message: 'تم إنشاء القيمة الضريبية بنجاح'
        });
      } else if (rateModal.rate) {
        await updateTaxRate(rateModal.rate.id, formData);
        setSuccessModal({
          isOpen: true,
          message: 'تم تحديث القيمة الضريبية بنجاح'
        });
      }
      
      setRateModal({ isOpen: false, mode: 'create', rate: null });
      fetchTaxRates({ tax_type_id: taxType.id });
    } catch (error) {
      console.error('خطأ في حفظ القيمة الضريبية:', error);
    }
  };

  const handleConfirmDelete = async () => {
    setDeleteModal(prev => ({ ...prev, isLoading: true }));
    try {
      await deleteTaxRate(deleteModal.id);
      setDeleteModal({ isOpen: false, id: 0, name: '', isLoading: false });
      setSuccessModal({
        isOpen: true,
        message: 'تم حذف القيمة الضريبية بنجاح'
      });
      if (taxType) {
        fetchTaxRates({ tax_type_id: taxType.id });
      }
    } catch (error) {
      console.error('خطأ في حذف القيمة الضريبية:', error);
      setDeleteModal(prev => ({ ...prev, isLoading: false }));
    }
  };

  const appliesOptions = [
    { value: 'all', label: 'جميع العناصر' },
    { value: 'products', label: 'المنتجات فقط' },
    { value: 'services', label: 'الخدمات فقط' }
  ];

  if (!isOpen || !taxType) return null;

  return (
    <>
      {/* Main Modal */}
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-3">
              {taxType.calculation_method === 'percentage' ? (
                <FiPercent className="w-6 h-6 text-primary-600 dark:text-primary-400" />
              ) : (
                <FiDollarSign className="w-6 h-6 text-primary-600 dark:text-primary-400" />
              )}
              <div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100">
                  إدارة القيم الضريبية
                </h2>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {taxType.name_ar} ({taxType.name})
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={handleCreateRate}
                className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center gap-2 text-sm font-medium"
              >
                <FiPlus className="w-4 h-4" />
                إضافة قيمة ضريبية
              </button>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <FiX className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                <span className="mr-3 text-gray-600 dark:text-gray-400">جاري التحميل...</span>
              </div>
            ) : currentTaxRates.length === 0 ? (
              <div className="text-center py-12">
                <FiPercent className="mx-auto text-6xl text-gray-300 dark:text-gray-600 mb-4" />
                <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  لا توجد قيم ضريبية
                </h3>
                <p className="text-gray-500 dark:text-gray-400 mb-6">
                  لم يتم إنشاء أي قيم ضريبية لهذا النوع بعد
                </p>
                <button
                  onClick={handleCreateRate}
                  className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center gap-2 text-sm font-medium mx-auto"
                >
                  <FiPlus className="w-4 h-4" />
                  إضافة قيمة ضريبية جديدة
                </button>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        اسم القيمة
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        القيمة
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        ينطبق على
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        الحالة
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        الإجراءات
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {currentTaxRates.map((rate) => (
                      <tr key={rate.id} className="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {rate.name}
                              {rate.is_default && (
                                <span className="mr-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                                  افتراضي
                                </span>
                              )}
                            </div>
                            {rate.description && (
                              <div className="text-sm text-gray-500 dark:text-gray-400">
                                {rate.description}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {taxType.calculation_method === 'percentage' 
                              ? `${rate.rate_value}%` 
                              : `${rate.rate_value} د.ل`
                            }
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400">
                            {rate.applies_to === 'all' ? 'جميع العناصر' :
                             rate.applies_to === 'products' ? 'المنتجات فقط' : 'الخدمات فقط'}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          {rate.is_active ? (
                            <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800">
                              <FiEye className="w-3 h-3 ml-1" />
                              نشط
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 border border-gray-200 dark:border-gray-700">
                              <FiEyeOff className="w-3 h-3 ml-1" />
                              غير نشط
                            </span>
                          )}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center gap-1">
                            <button
                              onClick={() => handleEditRate(rate)}
                              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 transition-colors duration-150 p-1 rounded hover:bg-blue-50 dark:hover:bg-blue-900/20"
                              title="تعديل القيمة الضريبية"
                            >
                              <FiEdit className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleDeleteRate(rate)}
                              className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 transition-colors duration-150 p-1 rounded hover:bg-red-50 dark:hover:bg-red-900/20"
                              title="حذف القيمة الضريبية"
                            >
                              <FiTrash className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Rate Form Modal */}
      {rateModal.isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-3xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100">
                {rateModal.mode === 'create' ? 'إضافة قيمة ضريبية جديدة' : 'تعديل القيمة الضريبية'}
              </h3>
              <button
                onClick={() => setRateModal({ isOpen: false, mode: 'create', rate: null })}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <FiX className="w-5 h-5" />
              </button>
            </div>

            <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextInput
                    label="اسم القيمة الضريبية"
                    name="name"
                    value={rateForm.name}
                    onChange={(value) => setRateForm(prev => ({ ...prev, name: value }))}
                    placeholder="أدخل اسم القيمة الضريبية"
                    required
                  />

                  <NumberInput
                    label={`القيمة (${taxType.calculation_method === 'percentage' ? '%' : 'د.ل'})`}
                    name="rate_value"
                    step="0.01"
                    value={rateForm.rate_value}
                    onChange={(value) => setRateForm(prev => ({ ...prev, rate_value: value }))}
                    placeholder="أدخل القيمة الضريبية"
                    required
                  />
                </div>

                <TextArea
                  label="الوصف"
                  name="description"
                  value={rateForm.description}
                  onChange={(value: string) => setRateForm(prev => ({ ...prev, description: value }))}
                  placeholder="أدخل وصف القيمة الضريبية (اختياري)"
                  rows={3}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <DatePicker
                    label="تاريخ بداية السريان"
                    name="effective_from"
                    value={rateForm.effective_from}
                    onChange={(value) => setRateForm(prev => ({ ...prev, effective_from: value }))}
                  />

                  <DatePicker
                    label="تاريخ نهاية السريان"
                    name="effective_to"
                    value={rateForm.effective_to}
                    onChange={(value) => setRateForm(prev => ({ ...prev, effective_to: value }))}
                  />
                </div>

                <SelectInput
                  label="ينطبق على"
                  name="applies_to"
                  value={rateForm.applies_to}
                  onChange={(value) => setRateForm(prev => ({ ...prev, applies_to: value as any }))}
                  options={appliesOptions}
                  required
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <NumberInput
                    label="الحد الأدنى للمبلغ (د.ل)"
                    name="min_amount"
                    step="0.01"
                    value={rateForm.min_amount}
                    onChange={(value) => setRateForm(prev => ({ ...prev, min_amount: value }))}
                    placeholder="اختياري"
                  />

                  <NumberInput
                    label="الحد الأقصى للمبلغ (د.ل)"
                    name="max_amount"
                    step="0.01"
                    value={rateForm.max_amount}
                    onChange={(value) => setRateForm(prev => ({ ...prev, max_amount: value }))}
                    placeholder="اختياري"
                  />
                </div>

                <TextInput
                  label="رمز الضريبة الحكومي"
                  name="tax_code"
                  value={rateForm.tax_code}
                  onChange={(value) => setRateForm(prev => ({ ...prev, tax_code: value }))}
                  placeholder="أدخل رمز الضريبة (اختياري)"
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-10 flex items-center">
                    <ToggleSwitch
                      id="is_default"
                      checked={rateForm.is_default}
                      onChange={(checked) => setRateForm(prev => ({ ...prev, is_default: checked }))}
                      label="القيمة الافتراضية"
                      className="w-full"
                    />
                  </div>

                  <div className="bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-10 flex items-center">
                    <ToggleSwitch
                      id="is_active"
                      checked={rateForm.is_active}
                      onChange={(checked) => setRateForm(prev => ({ ...prev, is_active: checked }))}
                      label="نشط"
                      className="w-full"
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setRateModal({ isOpen: false, mode: 'create', rate: null })}
                    className="bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium min-w-[140px]"
                  >
                    إلغاء
                  </button>
                  <button
                    type="button"
                    onClick={handleSaveRate}
                    className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium shadow-lg hover:shadow-xl min-w-[140px]"
                  >
                    {rateModal.mode === 'create' ? 'إضافة' : 'حفظ التغييرات'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, id: 0, name: '', isLoading: false })}
        onConfirm={handleConfirmDelete}
        title="حذف القيمة الضريبية"
        message={`هل أنت متأكد من حذف القيمة الضريبية؟`}
        itemName={deleteModal.name}
        isLoading={deleteModal.isLoading}
      />

      {/* Success Modal */}
      <SuccessModal
        isOpen={successModal.isOpen}
        onClose={() => setSuccessModal({ isOpen: false, message: '' })}
        title="تم بنجاح"
        message={successModal.message}
      />
    </>
  );
};

export default TaxRatesModal;
