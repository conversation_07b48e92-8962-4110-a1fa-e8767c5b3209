import React, { useState } from 'react';
import { FiAlertTriangle, FiTrash2, FiX, FiInfo } from 'react-icons/fi';
import { AttributeUsageInfo } from '../../types/variantAttribute';

interface AttributeDeleteConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (forceDelete?: boolean) => void;
  attributeName: string;
  isUsed: boolean;
  usageInfo: AttributeUsageInfo | null;
}

const AttributeDeleteConfirmModal: React.FC<AttributeDeleteConfirmModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  attributeName,
  isUsed,
  usageInfo
}) => {
  const [showForceDelete, setShowForceDelete] = useState(false);

  if (!isOpen) return null;

  const handleConfirm = () => {
    onConfirm(showForceDelete);
  };

  const handleForceDelete = () => {
    onConfirm(true);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-md w-full mx-4 transform transition-all">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <div className={`p-2 rounded-lg ${isUsed ? 'bg-red-100 dark:bg-red-900/20' : 'bg-orange-100 dark:bg-orange-900/20'}`}>
              {isUsed ? (
                <FiAlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400" />
              ) : (
                <FiTrash2 className="w-5 h-5 text-orange-600 dark:text-orange-400" />
              )}
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {isUsed ? 'تحذير: الخاصية مستخدمة' : 'تأكيد الحذف'}
            </h3>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <FiX className="w-5 h-5 text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {isUsed ? (
            <div className="space-y-4">
              <div className="bg-red-50 dark:bg-red-900/10 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <div className="flex items-start space-x-3 rtl:space-x-reverse">
                  <FiAlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-red-800 dark:text-red-200 mb-2">
                      لا يمكن حذف الخاصية "{attributeName}"
                    </h4>
                    <p className="text-sm text-red-700 dark:text-red-300">
                      هذه الخاصية مستخدمة حالياً في {usageInfo?.products_count || 0} منتج.
                      حذفها قد يؤثر على بيانات المنتجات.
                    </p>
                  </div>
                </div>
              </div>

              {usageInfo && (
                <div className="bg-blue-50 dark:bg-blue-900/10 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <div className="flex items-start space-x-3 rtl:space-x-reverse">
                    <FiInfo className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                    <div className="text-sm">
                      <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-1">
                        تفاصيل الاستخدام:
                      </h5>
                      <ul className="text-blue-700 dark:text-blue-300 space-y-1">
                        <li>• عدد القيم: {usageInfo.values_count}</li>
                        <li>• عدد المنتجات: {usageInfo.products_count}</li>
                      </ul>
                    </div>
                  </div>
                </div>
              )}

              {!showForceDelete && (
                <div className="text-center">
                  <button
                    onClick={() => setShowForceDelete(true)}
                    className="text-sm text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 underline"
                  >
                    إجبار الحذف (غير مستحسن)
                  </button>
                </div>
              )}

              {showForceDelete && (
                <div className="bg-yellow-50 dark:bg-yellow-900/10 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                  <p className="text-sm text-yellow-800 dark:text-yellow-200">
                    <strong>تحذير:</strong> إجبار الحذف سيؤدي إلى تعطيل الخاصية وجميع قيمها.
                    هذا قد يؤثر على عرض المنتجات المرتبطة بها.
                  </p>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center">
              <p className="text-gray-700 dark:text-gray-300 mb-4">
                هل أنت متأكد من حذف الخاصية "{attributeName}"؟
              </p>
              {usageInfo && usageInfo.values_count > 0 && (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  سيتم حذف {usageInfo.values_count} قيمة مرتبطة بهذه الخاصية.
                </p>
              )}
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3 rtl:space-x-reverse p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
          >
            إلغاء
          </button>
          
          {isUsed ? (
            showForceDelete && (
              <button
                onClick={handleForceDelete}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors flex items-center space-x-2 rtl:space-x-reverse"
              >
                <FiTrash2 className="w-4 h-4" />
                <span>إجبار الحذف</span>
              </button>
            )
          ) : (
            <button
              onClick={handleConfirm}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors flex items-center space-x-2 rtl:space-x-reverse"
            >
              <FiTrash2 className="w-4 h-4" />
              <span>حذف</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default AttributeDeleteConfirmModal;
