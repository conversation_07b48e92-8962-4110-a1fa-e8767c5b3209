import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  FiPlus,
  FiEdit,
  FiTrash,
  FiTag,
  FiEye,
  FiEyeOff,
  FiChevronDown,
  FiChevronRight,
  FiSearch,
  FiFilter,
  FiRefreshCw,
  FiImage,
  FiUpload
} from 'react-icons/fi';
import useCategoryStore, { Category, Subcategory, CategoryWithSubcategories } from '../../stores/categoryStore';
import Modal from '../Modal';
import DeleteConfirmModal from '../DeleteConfirmModal';
import SuccessModal from '../SuccessModal';
import { TextInput, TextArea, SelectInput } from '../inputs';
import ToggleSwitch from '../ToggleSwitch';
import { TopbarTooltip } from '../ui';
import { ImageUploadComponent } from '../ImageUpload';
import { imageManagementService, ImageUploadResult } from '../../services/imageManagementService';
import TablePagination from './TablePagination';
import SearchInput from '../SearchInput';

interface CategoriesDataTableProps {
  className?: string;
}

interface ExpandedCategories {
  [key: number]: boolean;
}

interface TableFilters {
  search: string;
  status: 'all' | 'active' | 'inactive';
  hasSubcategories: 'all' | 'yes' | 'no';
}

const CategoriesDataTable: React.FC<CategoriesDataTableProps> = ({ className = '' }) => {
  const {
    loading,
    fetchCategoriesWithSubcategories,
    createCategory,
    updateCategory,
    deleteCategory,
    createSubcategory,
    updateSubcategory,
    deleteSubcategory
  } = useCategoryStore();

  // State
  const [categoriesWithSubs, setCategoriesWithSubs] = useState<CategoryWithSubcategories[]>([]);
  const [expandedCategories, setExpandedCategories] = useState<ExpandedCategories>({});
  const [filters, setFilters] = useState<TableFilters>({
    search: '',
    status: 'all',
    hasSubcategories: 'all'
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Modal states
  const [categoryModal, setCategoryModal] = useState({
    isOpen: false,
    mode: 'create' as 'create' | 'edit',
    category: null as Category | null
  });

  const [subcategoryModal, setSubcategoryModal] = useState({
    isOpen: false,
    mode: 'create' as 'create' | 'edit',
    categoryId: 0,
    subcategory: null as Subcategory | null
  });

  const [deleteModal, setDeleteModal] = useState({
    isOpen: false,
    type: 'category' as 'category' | 'subcategory',
    id: 0,
    name: '',
    isLoading: false
  });

  const [successModal, setSuccessModal] = useState({
    isOpen: false,
    message: ''
  });

  // Form states
  const [categoryForm, setCategoryForm] = useState({
    name: '',
    description: '',
    image_url: '',
    is_active: true
  });

  const [subcategoryForm, setSubcategoryForm] = useState({
    name: '',
    description: '',
    is_active: true
  });

  // Image upload state for categories
  const [categoryImageUpload, setCategoryImageUpload] = useState({
    isUploading: false,
    uploadedImagePath: '',
    showUploadComponent: false
  });

  // Load data
  useEffect(() => {
    loadCategoriesWithSubcategories();
  }, []);

  const loadCategoriesWithSubcategories = async () => {
    try {
      const data = await fetchCategoriesWithSubcategories();
      setCategoriesWithSubs(data);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  // Filter and search logic
  const filteredCategories = useMemo(() => {
    return categoriesWithSubs.filter(category => {
      // Search filter
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        const matchesCategory = category.name.toLowerCase().includes(searchLower) ||
                               (category.description && category.description.toLowerCase().includes(searchLower));
        const matchesSubcategory = category.subcategories.some(sub =>
          sub.name.toLowerCase().includes(searchLower) ||
          (sub.description && sub.description.toLowerCase().includes(searchLower))
        );
        if (!matchesCategory && !matchesSubcategory) return false;
      }

      // Status filter
      if (filters.status !== 'all') {
        if (filters.status === 'active' && !category.is_active) return false;
        if (filters.status === 'inactive' && category.is_active) return false;
      }

      // Subcategories filter
      if (filters.hasSubcategories !== 'all') {
        if (filters.hasSubcategories === 'yes' && category.subcategories.length === 0) return false;
        if (filters.hasSubcategories === 'no' && category.subcategories.length > 0) return false;
      }

      return true;
    });
  }, [categoriesWithSubs, filters]);

  // Pagination calculations
  const totalItems = filteredCategories.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedCategories = filteredCategories.slice(startIndex, endIndex);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [filters]);

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Toggle category expansion
  const toggleCategoryExpansion = useCallback((categoryId: number) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  }, []);

  // Expand all categories
  const expandAllCategories = useCallback(() => {
    const newExpanded: ExpandedCategories = {};
    paginatedCategories.forEach(category => {
      if (category.subcategories.length > 0) {
        newExpanded[category.id] = true;
      }
    });
    setExpandedCategories(newExpanded);
  }, [paginatedCategories]);

  // Collapse all categories
  const collapseAllCategories = useCallback(() => {
    setExpandedCategories({});
  }, []);

  // Image upload handlers for categories
  const handleCategoryImageUploadSuccess = (result: ImageUploadResult) => {
    console.log('✅ تم رفع صورة الفئة بنجاح:', result);
    if (result.success && result.file_path) {
      setCategoryImageUpload(prev => ({
        ...prev,
        isUploading: false,
        uploadedImagePath: result.file_path || '',
        showUploadComponent: false
      }));
      setCategoryForm(prev => ({ ...prev, image_url: result.file_path || '' }));
    }
  };

  const handleCategoryImageUploadError = (error: string) => {
    console.error('❌ خطأ في رفع صورة الفئة:', error);
    setCategoryImageUpload(prev => ({
      ...prev,
      isUploading: false,
      showUploadComponent: false
    }));
  };

  const handleRemoveCategoryImage = async () => {
    try {
      const currentImagePath = categoryImageUpload.uploadedImagePath || categoryForm.image_url;

      // حذف الصورة من المجلد إذا كانت موجودة
      if (currentImagePath) {
        console.log('🗑️ حذف صورة الفئة من المجلد:', currentImagePath);
        const deleteResult = await imageManagementService.deleteImage(currentImagePath, true);

        if (deleteResult.success) {
          console.log('✅ تم حذف صورة الفئة من المجلد بنجاح');
        } else {
          console.warn('⚠️ فشل في حذف صورة الفئة من المجلد:', deleteResult.error);
        }
      }

      // إعادة تعيين الحالة
      setCategoryImageUpload({
        isUploading: false,
        uploadedImagePath: '',
        showUploadComponent: false
      });
      setCategoryForm(prev => ({ ...prev, image_url: '' }));

    } catch (error) {
      console.error('❌ خطأ في حذف صورة الفئة:', error);
      // إعادة تعيين الحالة حتى لو فشل الحذف
      setCategoryImageUpload({
        isUploading: false,
        uploadedImagePath: '',
        showUploadComponent: false
      });
      setCategoryForm(prev => ({ ...prev, image_url: '' }));
    }
  };

  // Category handlers
  const handleCreateCategory = () => {
    setCategoryForm({ name: '', description: '', image_url: '', is_active: true });
    setCategoryImageUpload({ isUploading: false, uploadedImagePath: '', showUploadComponent: false });
    setCategoryModal({ isOpen: true, mode: 'create', category: null });
  };

  const handleEditCategory = (category: Category) => {
    setCategoryForm({
      name: category.name,
      description: category.description || '',
      image_url: category.image_url || '',
      is_active: category.is_active
    });
    setCategoryImageUpload({
      isUploading: false,
      uploadedImagePath: category.image_url || '',
      showUploadComponent: false
    });
    setCategoryModal({ isOpen: true, mode: 'edit', category });
  };

  const handleDeleteCategory = (category: Category) => {
    setDeleteModal({
      isOpen: true,
      type: 'category',
      id: category.id,
      name: category.name,
      isLoading: false
    });
  };

  // Subcategory handlers
  const handleCreateSubcategory = (categoryId: number) => {
    setSubcategoryForm({ name: '', description: '', is_active: true });
    setSubcategoryModal({ isOpen: true, mode: 'create', categoryId, subcategory: null });
  };

  const handleEditSubcategory = (subcategory: Subcategory) => {
    setSubcategoryForm({
      name: subcategory.name,
      description: subcategory.description || '',
      is_active: subcategory.is_active
    });
    setSubcategoryModal({ isOpen: true, mode: 'edit', categoryId: subcategory.category_id, subcategory });
  };

  const handleDeleteSubcategory = (subcategory: Subcategory) => {
    setDeleteModal({
      isOpen: true,
      type: 'subcategory',
      id: subcategory.id,
      name: subcategory.name,
      isLoading: false
    });
  };

  // Form submission handlers
  const handleCategorySubmit = async () => {
    try {
      // التأكد من أن مسار الصورة محدث في النموذج
      const finalCategoryForm = {
        ...categoryForm,
        image_url: categoryImageUpload.uploadedImagePath || categoryForm.image_url
      };

      if (categoryModal.mode === 'create') {
        await createCategory(finalCategoryForm);
        setSuccessModal({ isOpen: true, message: 'تم إنشاء الفئة بنجاح' });
      } else if (categoryModal.category) {
        await updateCategory(categoryModal.category.id, finalCategoryForm);
        setSuccessModal({ isOpen: true, message: 'تم تحديث الفئة بنجاح' });
      }

      // إعادة تعيين الحالات
      setCategoryModal({ isOpen: false, mode: 'create', category: null });
      setCategoryImageUpload({ isUploading: false, uploadedImagePath: '', showUploadComponent: false });
      await loadCategoriesWithSubcategories();
    } catch (error) {
      console.error('Error saving category:', error);
    }
  };

  const handleSubcategorySubmit = async () => {
    try {
      if (subcategoryModal.mode === 'create') {
        await createSubcategory(subcategoryModal.categoryId, subcategoryForm);
        setSuccessModal({ isOpen: true, message: 'تم إنشاء الفئة الفرعية بنجاح' });
      } else if (subcategoryModal.subcategory) {
        await updateSubcategory(subcategoryModal.subcategory.id, subcategoryForm);
        setSuccessModal({ isOpen: true, message: 'تم تحديث الفئة الفرعية بنجاح' });
      }
      setSubcategoryModal({ isOpen: false, mode: 'create', categoryId: 0, subcategory: null });
      await loadCategoriesWithSubcategories();
    } catch (error) {
      console.error('Error saving subcategory:', error);
    }
  };

  const confirmDelete = async () => {
    try {
      setDeleteModal(prev => ({ ...prev, isLoading: true }));

      if (deleteModal.type === 'category') {
        // العثور على الفئة المراد حذفها
        const categoryToDelete = categoriesWithSubs.find((cat: CategoryWithSubcategories) => cat.id === deleteModal.id);

        // حذف صورة الفئة إذا كانت موجودة
        if (categoryToDelete?.image_url) {
          console.log('🗑️ حذف صورة الفئة من المجلد:', categoryToDelete.image_url);
          try {
            const deleteResult = await imageManagementService.deleteImage(categoryToDelete.image_url, true);
            if (deleteResult.success) {
              console.log('✅ تم حذف صورة الفئة من المجلد بنجاح');
            } else {
              console.warn('⚠️ فشل في حذف صورة الفئة من المجلد:', deleteResult.error);
            }
          } catch (imageError) {
            console.warn('⚠️ خطأ في حذف صورة الفئة:', imageError);
          }
        }

        await deleteCategory(deleteModal.id);
        setSuccessModal({ isOpen: true, message: 'تم حذف الفئة بنجاح' });
      } else {
        await deleteSubcategory(deleteModal.id);
        setSuccessModal({ isOpen: true, message: 'تم حذف الفئة الفرعية بنجاح' });
      }

      setDeleteModal({ isOpen: false, type: 'category', id: 0, name: '', isLoading: false });
      await loadCategoriesWithSubcategories();
    } catch (error) {
      console.error('Error deleting:', error);
      setDeleteModal(prev => ({ ...prev, isLoading: false }));
    }
  };

  // Clear filters
  const clearFilters = () => {
    setFilters({
      search: '',
      status: 'all',
      hasSubcategories: 'all'
    });
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Actions */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                <FiTag className="ml-3 text-primary-600 dark:text-primary-400" />
                إدارة الفئات والفئات الفرعية
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                تنظيم المنتجات في فئات وفئات فرعية
              </p>
            </div>
            
            <div className="flex flex-wrap items-center gap-3">
              <button
                onClick={expandAllCategories}
                className="px-4 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
              >
                توسيع الكل
              </button>
              <button
                onClick={collapseAllCategories}
                className="px-4 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
              >
                طي الكل
              </button>
              <button
                onClick={handleCreateCategory}
                className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl"
              >
                <FiPlus className="ml-2" />
                إضافة فئة جديدة
              </button>
            </div>
          </div>
        </div>

        {/* Filters and Search - Updated with unified components */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-end">
            {/* Search - Using unified SearchInput */}
            <SearchInput
            label="البحث"
                value={filters.search}
              onChange={(value) => setFilters(prev => ({ ...prev, search: value }))}
              placeholder="البحث في الفئات..."
              className="flex-1 min-w-[300px]"
              disabled={loading}
            />

            {/* Status Filter */}
            <div className="flex-1 min-w-[180px]">
              <SelectInput
                label="الحالة"
                name="status-filter"
                value={filters.status}
                onChange={(value) => setFilters(prev => ({ ...prev, status: value as any }))}
                options={[
                  { value: 'all', label: 'جميع الحالات' },
                  { value: 'active', label: 'نشط' },
                  { value: 'inactive', label: 'غير نشط' }
                ]}
                placeholder="اختر الحالة..."
              />
            </div>

            {/* Subcategories Filter */}
            <div className="flex-1 min-w-[200px]">
              <SelectInput
                label="الفئات الفرعية"
                name="subcategories-filter"
                value={filters.hasSubcategories}
                onChange={(value) => setFilters(prev => ({ ...prev, hasSubcategories: value as any }))}
                options={[
                  { value: 'all', label: 'جميع الفئات' },
                  { value: 'yes', label: 'لها فئات فرعية' },
                  { value: 'no', label: 'بدون فئات فرعية' }
                ]}
                placeholder="اختر نوع الفئة..."
              />
            </div>

            {/* Clear Filters Button - Single button only */}
            <div className="flex-shrink-0">
              <button
                onClick={clearFilters}
                disabled={loading}
                className="h-10 px-6 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-500 transition-all duration-200 ease-in-out flex items-center justify-center font-medium border-2 border-transparent hover:border-gray-300 dark:hover:border-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FiFilter className="ml-2" />
                مسح الفلاتر
              </button>
            </div>
          </div>
        </div>
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-600 dark:text-gray-400 mt-2 mr-3">جاري التحميل...</p>
          </div>
        ) : totalItems === 0 ? (
          <div className="text-center py-12">
            <FiTag className="mx-auto text-6xl text-gray-300 dark:text-gray-600 mb-4" />
            <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
              {filters.search || filters.status !== 'all' || filters.hasSubcategories !== 'all'
                ? 'لا توجد نتائج مطابقة للفلاتر'
                : 'لا توجد فئات'}
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              {filters.search || filters.status !== 'all' || filters.hasSubcategories !== 'all'
                ? 'جرب تغيير الفلاتر أو البحث'
                : 'ابدأ بإضافة فئة جديدة لتنظيم منتجاتك'}
            </p>
            {(!filters.search && filters.status === 'all' && filters.hasSubcategories === 'all') && (
              <button
                onClick={handleCreateCategory}
                className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl mx-auto"
              >
                <FiPlus className="ml-2" />
                إضافة فئة جديدة
              </button>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto" style={{ overflow: 'visible' }}>
            <table className="w-full" style={{ overflow: 'visible' }}>
              {/* Table Header */}
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-12">
                    #
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-16">
                    الصورة
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    اسم الفئة
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الوصف
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-32">
                    الفئات الفرعية
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-24">
                    الحالة
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-32">
                    الإجراءات
                  </th>
                </tr>
              </thead>

              {/* Table Body */}
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {paginatedCategories.map((category, index) => (
                  <React.Fragment key={category.id}>
                    {/* Category Row */}
                    <tr className="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-150">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        <div className="flex items-center">
                          <span className="font-medium">{startIndex + index + 1}</span>
                          {category.subcategories.length > 0 && (
                            <button
                              onClick={() => toggleCategoryExpansion(category.id)}
                              className="mr-2 p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-150"
                            >
                              {expandedCategories[category.id] ? (
                                <FiChevronDown className="w-4 h-4 text-gray-500" />
                              ) : (
                                <FiChevronRight className="w-4 h-4 text-gray-500" />
                              )}
                            </button>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center justify-center">
                          {category.image_url ? (
                            <div className="relative">
                              <img
                                src={imageManagementService.getImageUrl(category.image_url)}
                                alt={`صورة ${category.name}`}
                                className="w-10 h-10 rounded-lg object-cover border border-gray-200 dark:border-gray-600"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.style.display = 'none';
                                  const fallback = target.parentElement?.nextElementSibling as HTMLElement;
                                  if (fallback) fallback.style.display = 'flex';
                                }}
                              />
                              <div className="w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-700 items-center justify-center hidden">
                                <FiImage className="w-5 h-5 text-gray-400" />
                              </div>
                            </div>
                          ) : (
                            <div className="w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                              <FiImage className="w-5 h-5 text-gray-400" />
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <FiTag className="ml-3 text-primary-600 dark:text-primary-400 w-5 h-5" />
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {category.name}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-600 dark:text-gray-400">
                        <div className="max-w-xs truncate">
                          {category.description || '-'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        <div className="flex items-center">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-200">
                            {category.subcategories.length} فئة فرعية
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {category.is_active ? (
                            <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800">
                              <FiEye className="w-3 h-3 ml-1" />
                              نشط
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 border border-gray-200 dark:border-gray-700">
                              <FiEyeOff className="w-3 h-3 ml-1" />
                              غير نشط
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center justify-end gap-1">
                          <TopbarTooltip text="إضافة فئة فرعية" position="top">
                            <button
                              onClick={() => handleCreateSubcategory(category.id)}
                              className="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-200 transition-colors duration-150 p-2 rounded-lg hover:bg-primary-50 dark:hover:bg-primary-900/20 border border-transparent hover:border-primary-200 dark:hover:border-primary-700"
                            >
                              <FiPlus className="w-4 h-4" />
                            </button>
                          </TopbarTooltip>
                          <TopbarTooltip text="تعديل الفئة" position="top">
                            <button
                              onClick={() => handleEditCategory(category)}
                              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 transition-colors duration-150 p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 border border-transparent hover:border-blue-200 dark:hover:border-blue-700"
                            >
                              <FiEdit className="w-4 h-4" />
                            </button>
                          </TopbarTooltip>
                          <TopbarTooltip text="حذف الفئة" position="top">
                            <button
                              onClick={() => handleDeleteCategory(category)}
                              className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 transition-colors duration-150 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 border border-transparent hover:border-red-200 dark:hover:border-red-700"
                            >
                              <FiTrash className="w-4 h-4" />
                            </button>
                          </TopbarTooltip>
                        </div>
                      </td>
                    </tr>

                    {/* Subcategories - Clean Tree Structure */}
                    {expandedCategories[category.id] && category.subcategories.map((subcategory, subIndex) => (
                      <tr key={`sub-${subcategory.id}`} className="bg-gradient-to-r from-blue-50/50 to-transparent dark:from-blue-950/20 dark:to-transparent border-r-2 border-blue-200 dark:border-blue-700 hover:from-blue-100/50 dark:hover:from-blue-900/30 transition-all duration-200">
                        <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                          <div className="flex items-center">
                            {/* Tree branch indicator */}
                            <div className="flex items-center mr-2">
                              <div className="w-4 h-px bg-blue-300 dark:bg-blue-600"></div>
                              <div className="w-1 h-1 bg-blue-400 dark:bg-blue-500 rounded-full"></div>
                            </div>
                            <span className="text-xs font-semibold text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/50 px-2 py-1 rounded-md">
                              {startIndex + index + 1}.{subIndex + 1}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-3 whitespace-nowrap">
                          <div className="flex items-center justify-center">
                            {/* خلية فارغة للصورة */}
                            <div className="w-10 h-10 flex items-center justify-center">
                              <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                                <div className="w-2 h-2 bg-blue-400 dark:bg-blue-500 rounded-full"></div>
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-3 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="w-6 h-px bg-blue-200 dark:bg-blue-700 mr-2"></div>
                            <FiTag className="ml-2 text-blue-500 dark:text-blue-400 w-4 h-4" />
                            <div className="mr-2">
                              <div className="text-sm font-medium text-gray-800 dark:text-gray-200">
                                {subcategory.name}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-3 text-sm text-gray-500 dark:text-gray-400">
                          <div className="max-w-xs truncate italic">
                            {subcategory.description || 'لا يوجد وصف'}
                          </div>
                        </td>
                        <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-400 dark:text-gray-500">
                          <span className="text-xs">-</span>
                        </td>
                        <td className="px-6 py-3 whitespace-nowrap">
                          <div className="flex items-center">
                            {subcategory.is_active ? (
                              <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800">
                                <FiEye className="w-3 h-3 ml-1" />
                                نشط
                              </span>
                            ) : (
                              <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 border border-gray-200 dark:border-gray-700">
                                <FiEyeOff className="w-3 h-3 ml-1" />
                                غير نشط
                              </span>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-3 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center justify-end gap-1">
                            <TopbarTooltip text="تعديل الفئة الفرعية" position="top">
                              <button
                                onClick={() => handleEditSubcategory(subcategory)}
                                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 transition-colors duration-150 p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 border border-transparent hover:border-blue-200 dark:hover:border-blue-700"
                              >
                                <FiEdit className="w-4 h-4" />
                              </button>
                            </TopbarTooltip>
                            <TopbarTooltip text="حذف الفئة الفرعية" position="top">
                              <button
                                onClick={() => handleDeleteSubcategory(subcategory)}
                                className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 transition-colors duration-150 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 border border-transparent hover:border-red-200 dark:hover:border-red-700"
                              >
                                <FiTrash className="w-4 h-4" />
                              </button>
                            </TopbarTooltip>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {totalItems > 0 && (
          <TablePagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={totalItems}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
            itemsPerPageOptions={[5, 10, 20, 50]}
          />
        )}
      </div>

      {/* Category Modal */}
      <Modal
        isOpen={categoryModal.isOpen}
        onClose={() => {
          setCategoryModal({ isOpen: false, mode: 'create', category: null });
          setCategoryImageUpload({ isUploading: false, uploadedImagePath: '', showUploadComponent: false });
        }}
        title={categoryModal.mode === 'create' ? 'إضافة فئة جديدة' : 'تعديل الفئة'}
      >
        <div className="space-y-4">
          <TextInput
            label="اسم الفئة"
            name="category-name"
            value={categoryForm.name}
            onChange={(value) => setCategoryForm(prev => ({ ...prev, name: value }))}
            placeholder="أدخل اسم الفئة"
            required
          />

          <TextArea
            label="الوصف"
            name="category-description"
            value={categoryForm.description}
            onChange={(value) => setCategoryForm(prev => ({ ...prev, description: value }))}
            placeholder="أدخل وصف الفئة (اختياري)"
            rows={3}
          />

          {/* Category Image Upload Section */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              صورة الفئة (اختياري)
            </label>

            {/* Current Image Display */}
            {(categoryImageUpload.uploadedImagePath || categoryForm.image_url) && (
              <div className="flex items-center gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600">
                <img
                  src={imageManagementService.getImageUrl(categoryImageUpload.uploadedImagePath || categoryForm.image_url)}
                  alt="صورة الفئة"
                  className="w-16 h-16 rounded-lg object-cover border border-gray-200 dark:border-gray-600"
                />
                <div className="flex-1">
                  <p className="text-sm text-gray-600 dark:text-gray-400">الصورة الحالية</p>
                  <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                    {categoryImageUpload.uploadedImagePath || categoryForm.image_url}
                  </p>
                </div>
                <button
                  type="button"
                  onClick={handleRemoveCategoryImage}
                  className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-200 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                >
                  <FiTrash className="w-4 h-4" />
                </button>
              </div>
            )}

            {/* Upload Component */}
            {categoryImageUpload.showUploadComponent ? (
              <div className="border border-gray-200 dark:border-gray-600 rounded-xl p-4">
                <ImageUploadComponent
                  folder="categories"
                  multiple={false}
                  generateThumbnails={true}
                  maxFiles={1}
                  onUploadSuccess={handleCategoryImageUploadSuccess}
                  onUploadError={handleCategoryImageUploadError}
                  className="w-full"
                />
              </div>
            ) : (
              <button
                type="button"
                onClick={() => setCategoryImageUpload(prev => ({ ...prev, showUploadComponent: true }))}
                className="w-full flex items-center justify-center gap-2 p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl hover:border-primary-500 dark:hover:border-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-all duration-200 text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400"
              >
                <FiUpload className="w-5 h-5" />
                {categoryImageUpload.uploadedImagePath || categoryForm.image_url ? 'تغيير صورة الفئة' : 'رفع صورة الفئة'}
              </button>
            )}
          </div>

          <div className="bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-12 flex items-center">
            <ToggleSwitch
              id="category-active"
              checked={categoryForm.is_active}
              onChange={(checked) => setCategoryForm(prev => ({ ...prev, is_active: checked }))}
              label="فئة نشطة"
              className="w-full"
            />
          </div>

          <div className="flex gap-3 pt-4">
            <button
              onClick={handleCategorySubmit}
              className="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl"
            >
              {categoryModal.mode === 'create' ? 'إضافة الفئة' : 'حفظ التغييرات'}
            </button>
            <button
              onClick={() => {
                setCategoryModal({ isOpen: false, mode: 'create', category: null });
                setCategoryImageUpload({ isUploading: false, uploadedImagePath: '', showUploadComponent: false });
              }}
              className="flex-1 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium"
            >
              إلغاء
            </button>
          </div>
        </div>
      </Modal>

      {/* Subcategory Modal */}
      <Modal
        isOpen={subcategoryModal.isOpen}
        onClose={() => setSubcategoryModal({ isOpen: false, mode: 'create', categoryId: 0, subcategory: null })}
        title={subcategoryModal.mode === 'create' ? 'إضافة فئة فرعية جديدة' : 'تعديل الفئة الفرعية'}
      >
        <div className="space-y-4">
          <TextInput
            label="اسم الفئة الفرعية"
            name="subcategory-name"
            value={subcategoryForm.name}
            onChange={(value) => setSubcategoryForm(prev => ({ ...prev, name: value }))}
            placeholder="أدخل اسم الفئة الفرعية"
            required
          />

          <TextArea
            label="الوصف"
            name="subcategory-description"
            value={subcategoryForm.description}
            onChange={(value) => setSubcategoryForm(prev => ({ ...prev, description: value }))}
            placeholder="أدخل وصف الفئة الفرعية (اختياري)"
            rows={3}
          />

          <div className="bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-12 flex items-center">
            <ToggleSwitch
              id="subcategory-active"
              checked={subcategoryForm.is_active}
              onChange={(checked) => setSubcategoryForm(prev => ({ ...prev, is_active: checked }))}
              label="فئة فرعية نشطة"
              className="w-full"
            />
          </div>

          <div className="flex gap-3 pt-4">
            <button
              onClick={handleSubcategorySubmit}
              className="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl"
            >
              {subcategoryModal.mode === 'create' ? 'إضافة الفئة الفرعية' : 'حفظ التغييرات'}
            </button>
            <button
              onClick={() => setSubcategoryModal({ isOpen: false, mode: 'create', categoryId: 0, subcategory: null })}
              className="flex-1 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium"
            >
              إلغاء
            </button>
          </div>
        </div>
      </Modal>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, type: 'category', id: 0, name: '', isLoading: false })}
        onConfirm={confirmDelete}
        title={deleteModal.type === 'category' ? 'حذف الفئة' : 'حذف الفئة الفرعية'}
        message={`هل أنت متأكد من حذف ${deleteModal.type === 'category' ? 'الفئة' : 'الفئة الفرعية'} "${deleteModal.name}"؟`}
        itemName={deleteModal.name}
        isLoading={deleteModal.isLoading}
      />

      {/* Success Modal */}
      <SuccessModal
        isOpen={successModal.isOpen}
        onClose={() => setSuccessModal({ isOpen: false, message: '' })}
        title="نجح العملية"
        message={successModal.message}
      />
    </div>
  );
};

export default CategoriesDataTable;
