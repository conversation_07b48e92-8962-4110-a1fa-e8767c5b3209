import React, { useState, useEffect } from 'react';
import { FiX, FiPlus, FiTrash2, FiEdit3 } from 'react-icons/fi';
import Modal from '../Modal';
import ToggleSwitch from '../ToggleSwitch';
import { TextInput, TextArea, ModalSelectInput } from '../inputs';
import useVariantAttributeStore from '../../stores/variantAttributeStore';
import {
  VariantAttribute,
  CreateVariantAttributeData,
  UpdateVariantAttributeData,
  CreateVariantValueData,
  UpdateVariantValueData,
  ATTRIBUTE_TYPES
} from '../../types/variantAttribute';

interface VariantAttributeModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: 'create' | 'edit';
  attribute?: VariantAttribute | null;
  onSuccess: (message: string) => void;
}

const VariantAttributeModal: React.FC<VariantAttributeModalProps> = ({
  isOpen,
  onClose,
  mode,
  attribute,
  onSuccess
}) => {
  const {
    createAttribute,
    updateAttribute,
    addAttributeValue,
    updateAttributeValue,
    loading
  } = useVariantAttributeStore();

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    name_ar: '',
    description: '',
    attribute_type: 'text' as 'text' | 'color' | 'list' | 'number',
    is_required: false,
    is_active: true,
    sort_order: 0
  });

  const [values, setValues] = useState<CreateVariantValueData[]>([]);
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  // Initialize form data when modal opens
  useEffect(() => {
    if (isOpen) {
      if (mode === 'edit' && attribute) {
        setFormData({
          name: attribute.name,
          name_ar: attribute.name_ar,
          description: attribute.description || '',
          attribute_type: attribute.attribute_type,
          is_required: attribute.is_required,
          is_active: attribute.is_active,
          sort_order: attribute.sort_order
        });
        setValues(attribute.values.map(val => ({
          value: val.value,
          value_ar: val.value_ar,
          color_code: val.color_code,
          is_active: val.is_active,
          sort_order: val.sort_order
        })));
      } else {
        setFormData({
          name: '',
          name_ar: '',
          description: '',
          attribute_type: 'text',
          is_required: false,
          is_active: true,
          sort_order: 0
        });
        setValues([]);
      }
      setErrors({});
    }
  }, [isOpen, mode, attribute]);

  // Validation
  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!formData.name.trim()) {
      newErrors.name = 'اسم الخاصية مطلوب';
    }

    if (!formData.name_ar.trim()) {
      newErrors.name_ar = 'الاسم العربي مطلوب';
    }

    if (formData.attribute_type === 'color' || formData.attribute_type === 'list') {
      if (values.length === 0) {
        newErrors.values = 'يجب إضافة قيمة واحدة على الأقل';
      }
    }

    // Validate values
    values.forEach((value, index) => {
      if (!value.value.trim()) {
        newErrors[`value_${index}`] = 'القيمة مطلوبة';
      }
      if (!value.value_ar.trim()) {
        newErrors[`value_ar_${index}`] = 'القيمة العربية مطلوبة';
      }
      if (formData.attribute_type === 'color' && !value.color_code) {
        newErrors[`color_${index}`] = 'كود اللون مطلوب';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handlers
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      if (mode === 'create') {
        const createData: CreateVariantAttributeData = {
          ...formData,
          values: values.length > 0 ? values : undefined
        };
        await createAttribute(createData);
        onSuccess('تم إنشاء الخاصية بنجاح');
      } else if (mode === 'edit' && attribute) {
        // تحديث بيانات الخاصية الأساسية
        const updateData: UpdateVariantAttributeData = {
          name: formData.name,
          name_ar: formData.name_ar,
          description: formData.description || null,
          attribute_type: formData.attribute_type,
          is_required: formData.is_required,
          is_active: formData.is_active,
          sort_order: formData.sort_order
        };
        await updateAttribute(attribute.id, updateData);

        // تحديث القيم إذا كانت موجودة
        if (shouldShowValues && values.length > 0) {
          await handleUpdateValues();
        }

        onSuccess('تم تحديث الخاصية وقيمها بنجاح');
      }
    } catch (error) {
      console.error('خطأ في حفظ الخاصية:', error);
    }
  };

  const handleAddValue = () => {
    setValues(prev => [...prev, {
      value: '',
      value_ar: '',
      color_code: formData.attribute_type === 'color' ? '#000000' : undefined,
      is_active: true,
      sort_order: prev.length
    }]);
  };

  const handleRemoveValue = (index: number) => {
    setValues(prev => prev.filter((_, i) => i !== index));
  };

  const handleValueChange = (index: number, field: keyof CreateVariantValueData, value: any) => {
    setValues(prev => prev.map((val, i) =>
      i === index ? { ...val, [field]: value } : val
    ));
  };

  const handleUpdateValues = async () => {
    if (!attribute) return;

    try {
      const originalValues = attribute.values;

      // إنشاء خريطة للقيم الأصلية بناءً على الفهرس
      const originalValuesMap = new Map(originalValues.map((val, index) => [index, val]));

      // معالجة كل قيمة في القائمة الحالية
      for (let i = 0; i < values.length; i++) {
        const currentValue = values[i];
        const originalValue = originalValuesMap.get(i);

        if (originalValue) {
          // تحقق من وجود تغييرات قبل التحديث
          const hasChanges = (
            originalValue.value !== currentValue.value ||
            originalValue.value_ar !== currentValue.value_ar ||
            originalValue.color_code !== currentValue.color_code ||
            originalValue.is_active !== currentValue.is_active ||
            originalValue.sort_order !== currentValue.sort_order
          );

          if (hasChanges) {
            const updateData: UpdateVariantValueData = {
              value: currentValue.value,
              value_ar: currentValue.value_ar,
              color_code: currentValue.color_code,
              is_active: currentValue.is_active ?? true,
              sort_order: currentValue.sort_order ?? i
            };
            await updateAttributeValue(originalValue.id, updateData);
          }
        } else {
          // إضافة قيمة جديدة
          const newValueData: CreateVariantValueData = {
            value: currentValue.value,
            value_ar: currentValue.value_ar,
            color_code: currentValue.color_code,
            is_active: currentValue.is_active ?? true,
            sort_order: currentValue.sort_order ?? i
          };
          await addAttributeValue(attribute.id, newValueData);
        }
      }
    } catch (error) {
      console.error('خطأ في تحديث القيم:', error);
      throw error;
    }
  };

  const shouldShowValues = formData.attribute_type === 'color' || formData.attribute_type === 'list';

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === 'create' ? 'إضافة خاصية جديدة' : 'تعديل الخاصية'}
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information Section */}
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4">
          <h3 className="text-base font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
            <FiEdit3 className="ml-2 text-primary-600 dark:text-primary-400" />
            المعلومات الأساسية
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TextInput
              label="اسم الخاصية (إنجليزي)"
              name="name"
              value={formData.name}
              onChange={(value: string) => setFormData(prev => ({ ...prev, name: value }))}
              placeholder="Size, Color, Material..."
              required
              error={errors.name}
            />

            <TextInput
              label="اسم الخاصية (عربي)"
              name="name_ar"
              value={formData.name_ar}
              onChange={(value: string) => setFormData(prev => ({ ...prev, name_ar: value }))}
              placeholder="الحجم، اللون، المادة..."
              required
              error={errors.name_ar}
            />
          </div>

          <div className="mt-4">
            <TextArea
              label="الوصف"
              name="description"
              value={formData.description}
              onChange={(value: string) => setFormData(prev => ({ ...prev, description: value }))}
              placeholder="وصف اختياري للخاصية..."
              rows={2}
            />
          </div>
        </div>

        {/* Configuration Section */}
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4">
          <h3 className="text-base font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
            <FiEdit3 className="ml-2 text-primary-600 dark:text-primary-400" />
            إعدادات الخاصية
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ModalSelectInput
              label="نوع الخاصية"
              name="attribute_type"
              value={formData.attribute_type}
              onChange={(value: string) => setFormData(prev => ({ ...prev, attribute_type: value as 'text' | 'color' | 'list' | 'number' }))}
              options={Object.values(ATTRIBUTE_TYPES).map(type => ({
                value: type.value,
                label: type.label
              }))}
              required
            />

            <div className="space-y-3">
              <div className="grid grid-cols-1 gap-3">
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 border border-gray-200 dark:border-gray-600">
                  <ToggleSwitch
                    id="is_required"
                    label="خاصية مطلوبة"
                    checked={formData.is_required}
                    onChange={(checked: boolean) => setFormData(prev => ({ ...prev, is_required: checked }))}
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    هل هذه الخاصية مطلوبة عند إضافة منتج؟
                  </p>
                </div>

                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 border border-gray-200 dark:border-gray-600">
                  <ToggleSwitch
                    id="is_active"
                    label="نشطة"
                    checked={formData.is_active}
                    onChange={(checked: boolean) => setFormData(prev => ({ ...prev, is_active: checked }))}
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    هل هذه الخاصية متاحة للاستخدام؟
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Values Section */}
        {shouldShowValues && (
          <div className="bg-gray-50 dark:bg-gray-800/50 rounded-xl p-4 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-base font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                <FiEdit3 className="ml-2 text-primary-600 dark:text-primary-400" />
                قيم الخاصية
              </h3>
              <button
                type="button"
                onClick={handleAddValue}
                className="bg-primary-600 hover:bg-primary-700 text-white px-3 py-1.5 rounded-lg transition-colors flex items-center text-sm font-medium shadow-sm"
              >
                <FiPlus className="ml-1 w-3 h-3" />
                إضافة قيمة
              </button>
            </div>

            {errors.values && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-2">
                <p className="text-red-600 dark:text-red-400 text-sm">
                  {errors.values}
                </p>
              </div>
            )}

            <div className="space-y-2 max-h-64 overflow-y-auto pr-1">
              {values.map((value, index) => (
                <div key={index} className="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-3 shadow-sm">
                  <div className="space-y-3">
                    {/* القيم الأساسية */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <div>
                        <label htmlFor={`value_en_${index}`} className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                          القيمة (إنجليزي) *
                        </label>
                        <input
                          type="text"
                          id={`value_en_${index}`}
                          name={`value_en_${index}`}
                          value={value.value}
                          onChange={(e) => handleValueChange(index, 'value', e.target.value)}
                          placeholder="Red, Large..."
                          className={`w-full px-3 py-2 text-sm border rounded-lg transition-colors ${
                            errors[`value_${index}`]
                              ? 'border-red-500 focus:border-red-500 focus:ring-1 focus:ring-red-500'
                              : 'border-gray-300 dark:border-gray-600 focus:border-primary-500 focus:ring-1 focus:ring-primary-500'
                          } bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none`}
                        />
                        {errors[`value_${index}`] && (
                          <p className="text-red-600 dark:text-red-400 text-xs mt-1">
                            {errors[`value_${index}`]}
                          </p>
                        )}
                      </div>

                      <div>
                        <label htmlFor={`value_ar_${index}`} className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                          القيمة (عربي) *
                        </label>
                        <input
                          type="text"
                          id={`value_ar_${index}`}
                          name={`value_ar_${index}`}
                          value={value.value_ar}
                          onChange={(e) => handleValueChange(index, 'value_ar', e.target.value)}
                          placeholder="أحمر، كبير..."
                          className={`w-full px-3 py-2 text-sm border rounded-lg transition-colors ${
                            errors[`value_ar_${index}`]
                              ? 'border-red-500 focus:border-red-500 focus:ring-1 focus:ring-red-500'
                              : 'border-gray-300 dark:border-gray-600 focus:border-primary-500 focus:ring-1 focus:ring-primary-500'
                          } bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none`}
                        />
                        {errors[`value_ar_${index}`] && (
                          <p className="text-red-600 dark:text-red-400 text-xs mt-1">
                            {errors[`value_ar_${index}`]}
                          </p>
                        )}
                      </div>
                    </div>

                    {/* اللون والإجراءات */}
                    <div className="flex items-center justify-between">
                      {formData.attribute_type === 'color' && (
                        <div className="flex-1 mr-3">
                          <label htmlFor={`color_code_${index}`} className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                            كود اللون *
                          </label>
                          <div className="flex items-center gap-2">
                            <input
                              type="color"
                              id={`color_picker_${index}`}
                              name={`color_picker_${index}`}
                              value={value.color_code || '#000000'}
                              onChange={(e) => handleValueChange(index, 'color_code', e.target.value)}
                              className="w-10 h-8 rounded border border-gray-300 dark:border-gray-600 cursor-pointer"
                            />
                            <input
                              type="text"
                              id={`color_code_${index}`}
                              name={`color_code_${index}`}
                              value={value.color_code || '#000000'}
                              onChange={(e) => handleValueChange(index, 'color_code', e.target.value)}
                              placeholder="#000000"
                              className="flex-1 px-2 py-1.5 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:border-primary-500"
                            />
                          </div>
                          {errors[`color_${index}`] && (
                            <p className="text-red-600 dark:text-red-400 text-xs mt-1">
                              {errors[`color_${index}`]}
                            </p>
                          )}
                        </div>
                      )}

                      <button
                        type="button"
                        onClick={() => handleRemoveValue(index)}
                        className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-200 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors flex-shrink-0"
                        title="حذف القيمة"
                      >
                        <FiTrash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}

              {values.length === 0 && (
                <div className="text-center py-6 text-gray-500 dark:text-gray-400">
                  <FiEdit3 className="mx-auto w-8 h-8 mb-2 opacity-50" />
                  <p className="text-sm">لم يتم إضافة أي قيم بعد</p>
                  <p className="text-xs mt-1">انقر على "إضافة قيمة" لبدء إضافة القيم</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-3 pt-4">
          <button
            type="submit"
            disabled={loading}
            className="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                جاري الحفظ...
              </>
            ) : (
              <>
                {mode === 'create' ? <FiPlus className="ml-2 w-4 h-4" /> : <FiEdit3 className="ml-2 w-4 h-4" />}
                {mode === 'create' ? 'إضافة الخاصية' : 'حفظ التغييرات'}
              </>
            )}
          </button>
          <button
            type="button"
            onClick={onClose}
            disabled={loading}
            className="flex-1 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium"
          >
            <FiX className="ml-2 w-4 h-4" />
            إلغاء
          </button>
        </div>
      </form>
    </Modal>
  );
};

export default VariantAttributeModal;
