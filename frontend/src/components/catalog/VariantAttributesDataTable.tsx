import React, { useState, useEffect, useMemo } from 'react';
import {
  FiPlus,
  FiEdit,
  FiTrash2,
  FiTag,
  FiSearch,
  FiRefreshCw,
  FiLayers,
  FiEye,
  FiEyeOff,
  FiFilter
} from 'react-icons/fi';
import useVariantAttributeStore from '../../stores/variantAttributeStore';
import { VariantAttribute, VariantAttributeFilters, ATTRIBUTE_TYPES } from '../../types/variantAttribute';

import SuccessModal from '../SuccessModal';
import { SelectInput } from '../inputs';
import { TopbarTooltip } from '../ui';
import VariantAttributeModal from './VariantAttributeModal';
import AttributeDeleteConfirmModal from './AttributeDeleteConfirmModal';
import TablePagination from './TablePagination';
import SearchInput from '../SearchInput';

interface VariantAttributesDataTableProps {
  className?: string;
}

const VariantAttributesDataTable: React.FC<VariantAttributesDataTableProps> = ({ className = '' }) => {
  const {
    attributes,
    loading,
    error,
    fetchAttributes,
    checkAttributeUsage,
    deleteAttribute,
    clearError
  } = useVariantAttributeStore();

  // State
  const [filters, setFilters] = useState<VariantAttributeFilters>({
    search: '',
    status: 'all',
    attributeType: 'all',
    hasValues: 'all'
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Modal states
  const [attributeModal, setAttributeModal] = useState({
    isOpen: false,
    mode: 'create' as 'create' | 'edit',
    attribute: null as VariantAttribute | null
  });

  const [deleteModal, setDeleteModal] = useState({
    isOpen: false,
    attributeId: null as number | null,
    attributeName: '',
    isUsed: false,
    usageInfo: null as any
  });

  const [successModal, setSuccessModal] = useState({
    isOpen: false,
    message: ''
  });

  // Load data on component mount
  useEffect(() => {
    fetchAttributes();
  }, []);

  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      clearError();
    };
  }, []);

  // Filter attributes based on current filters
  const filteredAttributes = useMemo(() => {
    return attributes.filter(attribute => {
      // Search filter
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        const matchesSearch = 
          attribute.name.toLowerCase().includes(searchLower) ||
          attribute.name_ar.toLowerCase().includes(searchLower) ||
          (attribute.description && attribute.description.toLowerCase().includes(searchLower));
        
        if (!matchesSearch) return false;
      }

      // Status filter
      if (filters.status !== 'all') {
        const isActive = filters.status === 'active';
        if (attribute.is_active !== isActive) return false;
      }

      // Attribute type filter
      if (filters.attributeType !== 'all') {
        if (attribute.attribute_type !== filters.attributeType) return false;
      }

      // Has values filter
      if (filters.hasValues !== 'all') {
        const hasValues = attribute.values && attribute.values.length > 0;
        const shouldHaveValues = filters.hasValues === 'yes';
        if (hasValues !== shouldHaveValues) return false;
      }

      return true;
    });
  }, [attributes, filters]);

  // Pagination calculations
  const totalItems = filteredAttributes.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedAttributes = filteredAttributes.slice(startIndex, endIndex);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [filters]);

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Handlers
  const handleCreateAttribute = () => {
    setAttributeModal({
      isOpen: true,
      mode: 'create',
      attribute: null
    });
  };

  const handleEditAttribute = (attribute: VariantAttribute) => {
    setAttributeModal({
      isOpen: true,
      mode: 'edit',
      attribute
    });
  };

  const handleDeleteAttribute = async (attribute: VariantAttribute) => {
    try {
      // فحص استخدام الخاصية أولاً
      const usageInfo = await checkAttributeUsage(attribute.id);

      if (usageInfo.is_used) {
        // إظهار تحذير إذا كانت الخاصية مستخدمة
        setDeleteModal({
          isOpen: true,
          attributeId: attribute.id,
          attributeName: attribute.name_ar,
          isUsed: true,
          usageInfo: usageInfo
        });
      } else {
        // إظهار تأكيد عادي إذا لم تكن مستخدمة
        setDeleteModal({
          isOpen: true,
          attributeId: attribute.id,
          attributeName: attribute.name_ar,
          isUsed: false,
          usageInfo: usageInfo
        });
      }
    } catch (error) {
      console.error('خطأ في فحص استخدام الخاصية:', error);
      // في حالة فشل الفحص، إظهار تأكيد عادي
      setDeleteModal({
        isOpen: true,
        attributeId: attribute.id,
        attributeName: attribute.name_ar,
        isUsed: false,
        usageInfo: null
      });
    }
  };

  const confirmDelete = async (forceDelete: boolean = false) => {
    if (deleteModal.attributeId) {
      try {
        const result = await deleteAttribute(deleteModal.attributeId, forceDelete);
        setDeleteModal({ isOpen: false, attributeId: null, attributeName: '', isUsed: false, usageInfo: null });
        setSuccessModal({
          isOpen: true,
          message: result.message || 'تم حذف الخاصية بنجاح'
        });
      } catch (error: any) {
        console.error('خطأ في حذف الخاصية:', error);
        setSuccessModal({
          isOpen: true,
          message: error.message || 'فشل في حذف الخاصية'
        });
      }
    }
  };

  const handleRefresh = () => {
    fetchAttributes();
  };

  const handleToggleStatus = async (attribute: VariantAttribute) => {
    try {
      // سيتم تنفيذ هذا في VariantAttributeModal
      handleEditAttribute(attribute);
    } catch (error) {
      console.error('خطأ في تغيير حالة الخاصية:', error);
    }
  };

  const getAttributeTypeLabel = (type: string) => {
    return ATTRIBUTE_TYPES[type as keyof typeof ATTRIBUTE_TYPES]?.label || type;
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      search: '',
      status: 'all',
      attributeType: 'all',
      hasValues: 'all'
    });
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Error Display */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4">
          <div className="flex items-center">
            <div className="text-red-600 dark:text-red-400 text-sm">
              {error}
            </div>
          </div>
        </div>
      )}

      {/* Data Table with Header and Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 overflow-hidden">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center p-6 gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <div className="min-w-0 flex-1">
                <h2 className="text-lg sm:text-xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FiLayers className="ml-2 sm:ml-3 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                  <span className="truncate">خصائص المتغيرات</span>
                </h2>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1">
                  إدارة خصائص المنتجات مثل الحجم واللون والمادة
                </p>
              </div>
            </div>

            <div className="flex flex-wrap items-center gap-3">
              <button
                onClick={handleCreateAttribute}
                className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl"
              >
                <FiPlus className="ml-2" />
                إضافة خاصية جديدة
              </button>
            </div>
          </div>
        </div>
        {/* Filters and Search - Updated with unified components */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-end">
            {/* Search - Using unified SearchInput */}
            <SearchInput
              label="البحث"
              value={filters.search}
              onChange={(value) => setFilters(prev => ({ ...prev, search: value }))}
              placeholder="البحث في الخصائص..."
              className="flex-1 min-w-[300px]"
              disabled={loading}
            />

            {/* Status Filter */}
            <div className="flex-1 min-w-[180px]">
              <SelectInput
                label="الحالة"
                name="status-filter"
                value={filters.status}
                onChange={(value) => setFilters(prev => ({ ...prev, status: value as any }))}
                options={[
                  { value: 'all', label: 'جميع الحالات' },
                  { value: 'active', label: 'نشط' },
                  { value: 'inactive', label: 'غير نشط' }
                ]}
                placeholder="اختر الحالة..."
              />
            </div>

            {/* Attribute Type Filter */}
            <div className="flex-1 min-w-[180px]">
              <SelectInput
                label="نوع الخاصية"
                name="type-filter"
                value={filters.attributeType}
                onChange={(value) => setFilters(prev => ({ ...prev, attributeType: value as any }))}
                options={[
                  { value: 'all', label: 'جميع الأنواع' },
                  { value: 'text', label: 'نص' },
                  { value: 'color', label: 'لون' },
                  { value: 'list', label: 'قائمة' },
                  { value: 'number', label: 'رقم' }
                ]}
                placeholder="اختر النوع..."
              />
            </div>

            {/* Clear Filters Button - Single button only */}
            <div className="flex-shrink-0">
              <button
                onClick={clearFilters}
                disabled={loading}
                className="h-10 px-6 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-500 transition-all duration-200 ease-in-out flex items-center justify-center font-medium border-2 border-transparent hover:border-gray-300 dark:hover:border-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FiFilter className="ml-2" />
                مسح الفلاتر
              </button>
            </div>
          </div>
        </div>
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            <span className="mr-3 text-gray-600 dark:text-gray-400">جاري التحميل...</span>
          </div>
        ) : totalItems === 0 ? (
          <div className="text-center py-12">
            <FiTag className="mx-auto text-6xl text-gray-300 dark:text-gray-600 mb-4" />
            <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
              لا توجد خصائص
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              {filters.search || filters.status !== 'all' || filters.attributeType !== 'all' || filters.hasValues !== 'all'
                ? 'جرب تغيير الفلاتر أو البحث'
                : 'ابدأ بإضافة خاصية جديدة لتنظيم منتجاتك'}
            </p>
            {(!filters.search && filters.status === 'all' && filters.attributeType === 'all' && filters.hasValues === 'all') && (
              <button
                onClick={handleCreateAttribute}
                className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl mx-auto"
              >
                <FiPlus className="ml-2" />
                إضافة خاصية جديدة
              </button>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-12">
                    #
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    اسم الخاصية
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الوصف
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-24">
                    النوع
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-24">
                    عدد القيم
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-24">
                    الحالة
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-32">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {paginatedAttributes.map((attribute, index) => (
                  <tr key={attribute.id} className="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-150">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                      <span className="font-medium">{startIndex + index + 1}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <FiTag className="ml-3 text-primary-600 dark:text-primary-400 w-5 h-5" />
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {attribute.name_ar}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {attribute.name}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-600 dark:text-gray-400">
                      <div className="max-w-xs truncate">
                        {attribute.description || '-'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-200">
                        {getAttributeTypeLabel(attribute.attribute_type)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                      <div className="flex items-center">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200">
                          {attribute.values?.length || 0} قيمة
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {attribute.is_active ? (
                          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800">
                            <FiEye className="w-3 h-3 ml-1" />
                            نشط
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 border border-gray-200 dark:border-gray-700">
                            <FiEyeOff className="w-3 h-3 ml-1" />
                            غير نشط
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center justify-end gap-1">
                        <TopbarTooltip text="تعديل الخاصية" position="top">
                          <button
                            onClick={() => handleEditAttribute(attribute)}
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 transition-colors duration-150 p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 border border-transparent hover:border-blue-200 dark:hover:border-blue-700"
                          >
                            <FiEdit className="w-4 h-4" />
                          </button>
                        </TopbarTooltip>
                        <TopbarTooltip text="حذف الخاصية" position="top">
                          <button
                            onClick={() => handleDeleteAttribute(attribute)}
                            className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 transition-colors duration-150 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 border border-transparent hover:border-red-200 dark:hover:border-red-700"
                          >
                            <FiTrash2 className="w-4 h-4" />
                          </button>
                        </TopbarTooltip>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {totalItems > 0 && (
          <TablePagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={totalItems}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
            itemsPerPageOptions={[5, 10, 20, 50]}
          />
        )}
      </div>

      {/* Modals */}
      <VariantAttributeModal
        isOpen={attributeModal.isOpen}
        onClose={() => setAttributeModal({ isOpen: false, mode: 'create', attribute: null })}
        mode={attributeModal.mode}
        attribute={attributeModal.attribute}
        onSuccess={(message: string) => {
          setAttributeModal({ isOpen: false, mode: 'create', attribute: null });
          setSuccessModal({ isOpen: true, message });
          fetchAttributes();
        }}
      />

      <AttributeDeleteConfirmModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, attributeId: null, attributeName: '', isUsed: false, usageInfo: null })}
        onConfirm={confirmDelete}
        attributeName={deleteModal.attributeName}
        isUsed={deleteModal.isUsed}
        usageInfo={deleteModal.usageInfo}
      />

      <SuccessModal
        isOpen={successModal.isOpen}
        onClose={() => setSuccessModal({ isOpen: false, message: '' })}
        title="نجح العملية"
        message={successModal.message}
      />
    </div>
  );
};

export default VariantAttributesDataTable;
