import React, { useState, useEffect, useMemo } from 'react';
import {
  FiPlus,
  FiEdit,
  FiTrash,
  FiTag,
  FiEye,
  FiEyeOff,
  FiSearch,
  FiFilter,
  FiRefreshCw,
  FiGlobe,
  FiPhone,
  FiImage,
  FiUpload
} from 'react-icons/fi';
import useBrandStore, { Brand } from '../../stores/brandStore';
import Modal from '../Modal';
import DeleteConfirmModal from '../DeleteConfirmModal';
import SuccessModal from '../SuccessModal';
import { TextInput, TextArea, SelectInput } from '../inputs';
import ToggleSwitch from '../ToggleSwitch';
import { ImageUploadComponent } from '../ImageUpload';
import { imageManagementService, ImageUploadResult } from '../../services/imageManagementService';
import TablePagination from './TablePagination';
import SearchInput from '../SearchInput';

interface BrandsDataTableProps {
  className?: string;
}

interface TableFilters {
  search: string;
  status: 'all' | 'active' | 'inactive';
  hasWebsite: 'all' | 'yes' | 'no';
}

const BrandsDataTable: React.FC<BrandsDataTableProps> = ({ className = '' }) => {
  const {
    brands,
    loading,
    fetchBrands,
    createBrand,
    updateBrand,
    deleteBrand
  } = useBrandStore();

  // State
  const [filters, setFilters] = useState<TableFilters>({
    search: '',
    status: 'all',
    hasWebsite: 'all'
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Modal states
  const [brandModal, setBrandModal] = useState({
    isOpen: false,
    mode: 'create' as 'create' | 'edit',
    brand: null as Brand | null
  });

  const [deleteModal, setDeleteModal] = useState({
    isOpen: false,
    id: 0,
    name: '',
    isLoading: false
  });

  const [successModal, setSuccessModal] = useState({
    isOpen: false,
    message: ''
  });

  // Form state
  const [brandForm, setBrandForm] = useState({
    name: '',
    description: '',
    website: '',
    contact_info: '',
    logo_url: '',
    is_active: true
  });

  // Image upload state
  const [logoUpload, setLogoUpload] = useState({
    isUploading: false,
    uploadedImagePath: '',
    showUploadComponent: false
  });

  // Load data
  useEffect(() => {
    loadBrands();
  }, []);

  const loadBrands = async () => {
    try {
      await fetchBrands({ active_only: false }); // جلب جميع العلامات التجارية (النشطة والمعطلة)
    } catch (error) {
      console.error('Error loading brands:', error);
    }
  };

  // Filter and search logic
  const filteredBrands = useMemo(() => {
    return brands.filter(brand => {
      // Search filter
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        const matchesBrand = brand.name.toLowerCase().includes(searchLower) ||
                            (brand.description && brand.description.toLowerCase().includes(searchLower)) ||
                            (brand.website && brand.website.toLowerCase().includes(searchLower)) ||
                            (brand.contact_info && brand.contact_info.toLowerCase().includes(searchLower));
        if (!matchesBrand) return false;
      }

      // Status filter
      if (filters.status !== 'all') {
        if (filters.status === 'active' && !brand.is_active) return false;
        if (filters.status === 'inactive' && brand.is_active) return false;
      }

      // Website filter
      if (filters.hasWebsite !== 'all') {
        if (filters.hasWebsite === 'yes' && !brand.website) return false;
        if (filters.hasWebsite === 'no' && brand.website) return false;
      }

      return true;
    });
  }, [brands, filters]);

  // Pagination calculations
  const totalItems = filteredBrands.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedBrands = filteredBrands.slice(startIndex, endIndex);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [filters]);

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Image upload handlers
  const handleLogoUploadSuccess = (result: ImageUploadResult) => {
    console.log('✅ تم رفع صورة الشعار بنجاح:', result);
    if (result.success && result.file_path) {
      setLogoUpload(prev => ({
        ...prev,
        isUploading: false,
        uploadedImagePath: result.file_path || '',
        showUploadComponent: false
      }));
      setBrandForm(prev => ({ ...prev, logo_url: result.file_path || '' }));
    }
  };

  const handleLogoUploadError = (error: string) => {
    console.error('❌ خطأ في رفع صورة الشعار:', error);
    setLogoUpload(prev => ({
      ...prev,
      isUploading: false,
      showUploadComponent: false
    }));
  };

  const handleRemoveLogo = async () => {
    try {
      const currentImagePath = logoUpload.uploadedImagePath || brandForm.logo_url;

      // حذف الصورة من المجلد إذا كانت موجودة
      if (currentImagePath) {
        console.log('🗑️ حذف صورة الشعار من المجلد:', currentImagePath);
        const deleteResult = await imageManagementService.deleteImage(currentImagePath, true);

        if (deleteResult.success) {
          console.log('✅ تم حذف صورة الشعار من المجلد بنجاح');
        } else {
          console.warn('⚠️ فشل في حذف صورة الشعار من المجلد:', deleteResult.error);
        }
      }

      // إعادة تعيين الحالة
      setLogoUpload({
        isUploading: false,
        uploadedImagePath: '',
        showUploadComponent: false
      });
      setBrandForm(prev => ({ ...prev, logo_url: '' }));

    } catch (error) {
      console.error('❌ خطأ في حذف صورة الشعار:', error);
      // إعادة تعيين الحالة حتى لو فشل الحذف
      setLogoUpload({
        isUploading: false,
        uploadedImagePath: '',
        showUploadComponent: false
      });
      setBrandForm(prev => ({ ...prev, logo_url: '' }));
    }
  };

  // Brand handlers
  const handleCreateBrand = () => {
    setBrandForm({ name: '', description: '', website: '', contact_info: '', logo_url: '', is_active: true });
    setLogoUpload({ isUploading: false, uploadedImagePath: '', showUploadComponent: false });
    setBrandModal({ isOpen: true, mode: 'create', brand: null });
  };

  const handleEditBrand = (brand: Brand) => {
    setBrandForm({
      name: brand.name,
      description: brand.description || '',
      website: brand.website || '',
      contact_info: brand.contact_info || '',
      logo_url: brand.logo_url || '',
      is_active: brand.is_active
    });
    setLogoUpload({
      isUploading: false,
      uploadedImagePath: brand.logo_url || '',
      showUploadComponent: false
    });
    setBrandModal({ isOpen: true, mode: 'edit', brand });
  };

  const handleDeleteBrand = (brand: Brand) => {
    setDeleteModal({
      isOpen: true,
      id: brand.id,
      name: brand.name,
      isLoading: false
    });
  };

  // Form submission handlers
  const handleBrandSubmit = async () => {
    try {
      // التأكد من أن مسار الصورة محدث في النموذج
      const finalBrandForm = {
        ...brandForm,
        logo_url: logoUpload.uploadedImagePath || brandForm.logo_url
      };

      if (brandModal.mode === 'create') {
        await createBrand(finalBrandForm);
        setSuccessModal({ isOpen: true, message: 'تم إنشاء العلامة التجارية بنجاح' });
      } else if (brandModal.brand) {
        await updateBrand(brandModal.brand.id, finalBrandForm);
        setSuccessModal({ isOpen: true, message: 'تم تحديث العلامة التجارية بنجاح' });
      }

      // إعادة تعيين الحالات
      setBrandModal({ isOpen: false, mode: 'create', brand: null });
      setLogoUpload({ isUploading: false, uploadedImagePath: '', showUploadComponent: false });
      await loadBrands();
    } catch (error) {
      console.error('Error saving brand:', error);
    }
  };

  const confirmDelete = async () => {
    try {
      setDeleteModal(prev => ({ ...prev, isLoading: true }));

      // العثور على العلامة التجارية المراد حذفها
      const brandToDelete = brands.find(brand => brand.id === deleteModal.id);

      // حذف صورة الشعار إذا كانت موجودة
      if (brandToDelete?.logo_url) {
        console.log('🗑️ حذف صورة شعار العلامة التجارية من المجلد:', brandToDelete.logo_url);
        try {
          const deleteResult = await imageManagementService.deleteImage(brandToDelete.logo_url, true);
          if (deleteResult.success) {
            console.log('✅ تم حذف صورة الشعار من المجلد بنجاح');
          } else {
            console.warn('⚠️ فشل في حذف صورة الشعار من المجلد:', deleteResult.error);
          }
        } catch (imageError) {
          console.warn('⚠️ خطأ في حذف صورة الشعار:', imageError);
        }
      }

      // حذف العلامة التجارية من قاعدة البيانات
      await deleteBrand(deleteModal.id);
      setSuccessModal({ isOpen: true, message: 'تم حذف العلامة التجارية بنجاح' });
      setDeleteModal({ isOpen: false, id: 0, name: '', isLoading: false });
      await loadBrands();
    } catch (error) {
      console.error('Error deleting brand:', error);
      setDeleteModal(prev => ({ ...prev, isLoading: false }));
    }
  };

  // Clear filters
  const clearFilters = () => {
    setFilters({
      search: '',
      status: 'all',
      hasWebsite: 'all'
    });
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Data Table with Header and Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 overflow-hidden">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center p-6 gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <div className="min-w-0 flex-1">
                <h2 className="text-lg sm:text-xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FiTag className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                  <span className="truncate">إدارة العلامات التجارية</span>
                </h2>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1">
                  إدارة العلامات التجارية للمنتجات
                </p>
              </div>
            </div>

            <div className="flex flex-wrap items-center gap-3">
              <button
                onClick={handleCreateBrand}
                className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl"
              >
                <FiPlus className="ml-2" />
                إضافة علامة تجارية جديدة
              </button>
            </div>
          </div>
        </div>

        {/* Filters and Search - Updated with unified components */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-end">
            {/* Search - Using unified SearchInput */}
            <SearchInput
              label="البحث"
              value={filters.search}
              onChange={(value) => setFilters(prev => ({ ...prev, search: value }))}
              placeholder="البحث في العلامات التجارية..."
              className="flex-1 min-w-[300px]"
              disabled={loading}
            />

            {/* Status Filter */}
            <div className="flex-1 min-w-[200px]">
              <SelectInput
                label="الحالة"
                name="status-filter"
                value={filters.status}
                onChange={(value) => setFilters(prev => ({ ...prev, status: value as any }))}
                options={[
                  { value: 'all', label: 'جميع الحالات' },
                  { value: 'active', label: 'نشط' },
                  { value: 'inactive', label: 'غير نشط' }
                ]}
                placeholder="اختر الحالة..."
              />
            </div>

            {/* Website Filter */}
            <div className="flex-1 min-w-[200px]">
              <SelectInput
                label="الموقع الإلكتروني"
                name="website-filter"
                value={filters.hasWebsite}
                onChange={(value) => setFilters(prev => ({ ...prev, hasWebsite: value as any }))}
                options={[
                  { value: 'all', label: 'جميع العلامات' },
                  { value: 'yes', label: 'لها موقع إلكتروني' },
                  { value: 'no', label: 'بدون موقع إلكتروني' }
                ]}
                placeholder="اختر نوع الموقع..."
              />
            </div>

            {/* Clear Filters Button - Single button only */}
            <div className="flex-shrink-0">
              <button
                onClick={clearFilters}
                disabled={loading}
                className="h-10 px-6 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-500 transition-all duration-200 ease-in-out flex items-center justify-center font-medium border-2 border-transparent hover:border-gray-300 dark:hover:border-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FiFilter className="ml-2" />
                مسح الفلاتر
              </button>
            </div>
          </div>
        </div>
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-600 dark:text-gray-400 mt-2 mr-3">جاري التحميل...</p>
          </div>
        ) : totalItems === 0 ? (
          <div className="text-center py-12">
            <FiTag className="mx-auto text-6xl text-gray-300 dark:text-gray-600 mb-4" />
            <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
              {filters.search || filters.status !== 'all' || filters.hasWebsite !== 'all'
                ? 'لا توجد نتائج مطابقة للفلاتر'
                : 'لا توجد علامات تجارية'}
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              {filters.search || filters.status !== 'all' || filters.hasWebsite !== 'all'
                ? 'جرب تغيير الفلاتر أو البحث'
                : 'ابدأ بإضافة علامة تجارية جديدة'}
            </p>
            {(!filters.search && filters.status === 'all' && filters.hasWebsite === 'all') && (
              <button
                onClick={handleCreateBrand}
                className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl mx-auto"
              >
                <FiPlus className="ml-2" />
                إضافة علامة تجارية جديدة
              </button>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              {/* Table Header */}
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-12">
                    #
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-16">
                    الشعار
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    اسم العلامة التجارية
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الوصف
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الموقع الإلكتروني
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    معلومات الاتصال
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-24">
                    الحالة
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-32">
                    الإجراءات
                  </th>
                </tr>
              </thead>

              {/* Table Body */}
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {paginatedBrands.map((brand, index) => (
                  <tr key={brand.id} className="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-150">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                      <span className="font-medium">{startIndex + index + 1}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center justify-center">
                        {brand.logo_url ? (
                          <div className="relative">
                            <img
                              src={imageManagementService.getImageUrl(brand.logo_url)}
                              alt={`شعار ${brand.name}`}
                              className="w-10 h-10 rounded-lg object-cover border border-gray-200 dark:border-gray-600"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                const fallback = target.parentElement?.nextElementSibling as HTMLElement;
                                if (fallback) fallback.style.display = 'flex';
                              }}
                            />
                            <div className="w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-700 items-center justify-center hidden">
                              <FiImage className="w-5 h-5 text-gray-400" />
                            </div>
                          </div>
                        ) : (
                          <div className="w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                            <FiImage className="w-5 h-5 text-gray-400" />
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <FiTag className="ml-3 text-primary-600 dark:text-primary-400 w-5 h-5" />
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {brand.name}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-600 dark:text-gray-400">
                      <div className="max-w-xs truncate">
                        {brand.description || '-'}
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-600 dark:text-gray-400">
                      {brand.website ? (
                        <div className="flex items-center">
                          <FiGlobe className="ml-2 w-4 h-4 text-blue-500" />
                          <a
                            href={brand.website.startsWith('http') ? brand.website : `https://${brand.website}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 transition-colors duration-150 truncate max-w-xs"
                          >
                            {brand.website}
                          </a>
                        </div>
                      ) : (
                        '-'
                      )}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-600 dark:text-gray-400">
                      {brand.contact_info ? (
                        <div className="flex items-center">
                          <FiPhone className="ml-2 w-4 h-4 text-green-500" />
                          <span className="truncate max-w-xs">{brand.contact_info}</span>
                        </div>
                      ) : (
                        '-'
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {brand.is_active ? (
                          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800">
                            <FiEye className="w-3 h-3 ml-1" />
                            نشط
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 border border-gray-200 dark:border-gray-700">
                            <FiEyeOff className="w-3 h-3 ml-1" />
                            غير نشط
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center justify-end gap-1">
                        <button
                          onClick={() => handleEditBrand(brand)}
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 transition-colors duration-150 p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 border border-transparent hover:border-blue-200 dark:hover:border-blue-700"
                          title="تعديل العلامة التجارية"
                        >
                          <FiEdit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteBrand(brand)}
                          className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 transition-colors duration-150 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 border border-transparent hover:border-red-200 dark:hover:border-red-700"
                          title="حذف العلامة التجارية"
                        >
                          <FiTrash className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {totalItems > 0 && (
          <TablePagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={totalItems}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
            itemsPerPageOptions={[5, 10, 20, 50]}
          />
        )}
      </div>

      {/* Brand Modal */}
      <Modal
        isOpen={brandModal.isOpen}
        onClose={() => {
          setBrandModal({ isOpen: false, mode: 'create', brand: null });
          setLogoUpload({ isUploading: false, uploadedImagePath: '', showUploadComponent: false });
        }}
        title={brandModal.mode === 'create' ? 'إضافة علامة تجارية جديدة' : 'تعديل العلامة التجارية'}
      >
        <div className="space-y-4">
          <TextInput
            label="اسم العلامة التجارية"
            name="brand-name"
            value={brandForm.name}
            onChange={(value) => setBrandForm(prev => ({ ...prev, name: value }))}
            placeholder="أدخل اسم العلامة التجارية"
            required
          />

          <TextArea
            label="الوصف"
            name="brand-description"
            value={brandForm.description}
            onChange={(value) => setBrandForm(prev => ({ ...prev, description: value }))}
            placeholder="أدخل وصف العلامة التجارية (اختياري)"
            rows={3}
          />

          <TextInput
            label="الموقع الإلكتروني"
            name="brand-website"
            value={brandForm.website}
            onChange={(value) => setBrandForm(prev => ({ ...prev, website: value }))}
            placeholder="أدخل الموقع الإلكتروني (اختياري)"
          />

          <TextInput
            label="معلومات الاتصال"
            name="brand-contact"
            value={brandForm.contact_info}
            onChange={(value) => setBrandForm(prev => ({ ...prev, contact_info: value }))}
            placeholder="أدخل معلومات الاتصال (اختياري)"
          />

          {/* Logo Upload Section */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              شعار العلامة التجارية (اختياري)
            </label>

            {/* Current Logo Display */}
            {(logoUpload.uploadedImagePath || brandForm.logo_url) && (
              <div className="flex items-center gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600">
                <img
                  src={imageManagementService.getImageUrl(logoUpload.uploadedImagePath || brandForm.logo_url)}
                  alt="شعار العلامة التجارية"
                  className="w-16 h-16 rounded-lg object-cover border border-gray-200 dark:border-gray-600"
                />
                <div className="flex-1">
                  <p className="text-sm text-gray-600 dark:text-gray-400">الشعار الحالي</p>
                  <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                    {logoUpload.uploadedImagePath || brandForm.logo_url}
                  </p>
                </div>
                <button
                  type="button"
                  onClick={handleRemoveLogo}
                  className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-200 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                >
                  <FiTrash className="w-4 h-4" />
                </button>
              </div>
            )}

            {/* Upload Component */}
            {logoUpload.showUploadComponent ? (
              <div className="border border-gray-200 dark:border-gray-600 rounded-xl p-4">
                <ImageUploadComponent
                  folder="brands"
                  multiple={false}
                  generateThumbnails={true}
                  maxFiles={1}
                  onUploadSuccess={handleLogoUploadSuccess}
                  onUploadError={handleLogoUploadError}
                  className="w-full"
                />
              </div>
            ) : (
              <button
                type="button"
                onClick={() => setLogoUpload(prev => ({ ...prev, showUploadComponent: true }))}
                className="w-full flex items-center justify-center gap-2 p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl hover:border-primary-500 dark:hover:border-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-all duration-200 text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400"
              >
                <FiUpload className="w-5 h-5" />
                {logoUpload.uploadedImagePath || brandForm.logo_url ? 'تغيير الشعار' : 'رفع شعار العلامة التجارية'}
              </button>
            )}
          </div>

          <div className="bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-12 flex items-center">
            <ToggleSwitch
              id="brand-active"
              checked={brandForm.is_active}
              onChange={(checked) => setBrandForm(prev => ({ ...prev, is_active: checked }))}
              label="علامة تجارية نشطة"
              className="w-full"
            />
          </div>

          <div className="flex gap-3 pt-4">
            <button
              onClick={handleBrandSubmit}
              className="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl"
            >
              {brandModal.mode === 'create' ? 'إضافة العلامة التجارية' : 'حفظ التغييرات'}
            </button>
            <button
              onClick={() => {
                setBrandModal({ isOpen: false, mode: 'create', brand: null });
                setLogoUpload({ isUploading: false, uploadedImagePath: '', showUploadComponent: false });
              }}
              className="flex-1 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium"
            >
              إلغاء
            </button>
          </div>
        </div>
      </Modal>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, id: 0, name: '', isLoading: false })}
        onConfirm={confirmDelete}
        title="حذف العلامة التجارية"
        message={`هل أنت متأكد من حذف العلامة التجارية "${deleteModal.name}"؟`}
        itemName={deleteModal.name}
        isLoading={deleteModal.isLoading}
      />

      {/* Success Modal */}
      <SuccessModal
        isOpen={successModal.isOpen}
        onClose={() => setSuccessModal({ isOpen: false, message: '' })}
        title="نجح العملية"
        message={successModal.message}
      />
    </div>
  );
};

export default BrandsDataTable;
