import React, { useState, useEffect, useMemo } from 'react';
import {
  FiPlus,
  FiEdit,
  FiTrash,
  FiPercent,
  FiEye,
  FiEyeOff,
  FiSearch,
  FiRefreshCw,
  FiDollarSign,
  FiLayers,
  FiChevronDown,
  FiChevronRight,
  FiFilter
} from 'react-icons/fi';
import useTaxTypeStore, { TaxType } from '../../stores/taxTypeStore';
import useTaxRateStore, { TaxRate } from '../../stores/taxRateStore';
import Modal from '../Modal';
import DeleteConfirmModal from '../DeleteConfirmModal';
import SuccessModal from '../SuccessModal';
import { TextInput, SelectInput, NumberInput } from '../inputs';
import TextArea from '../inputs/TextArea';
import DatePicker from '../DatePicker';
import ToggleSwitch from '../ToggleSwitch';
import TablePagination from './TablePagination';
import { TopbarTooltip } from '../ui';
import SearchInput from '../SearchInput';

interface TaxTypesDataTableProps {
  className?: string;
}

interface TableFilters {
  search: string;
  status: 'all' | 'active' | 'inactive';
  tax_category: 'all' | 'standard' | 'reduced' | 'zero' | 'exempt';
}

interface ExpandedTaxTypes {
  [key: number]: boolean;
}

interface TaxRateForm {
  name: string;
  rate_value: string;
  description: string;
  effective_from: string;
  effective_to: string;
  is_default: boolean;
  is_active: boolean;
  applies_to: 'all' | 'products' | 'services';
  min_amount: string;
  max_amount: string;
  tax_code: string;
  sort_order: number;
}

const TaxTypesDataTable: React.FC<TaxTypesDataTableProps> = ({ className = '' }) => {
  const {
    taxTypes,
    loading,
    fetchTaxTypes,
    createTaxType,
    updateTaxType,
    deleteTaxType
  } = useTaxTypeStore();

  const {
    taxRates,
    fetchTaxRates,
    createTaxRate,
    updateTaxRate,
    deleteTaxRate
  } = useTaxRateStore();

  // State
  const [filters, setFilters] = useState<TableFilters>({
    search: '',
    status: 'all',
    tax_category: 'all'
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Expanded tax types state
  const [expandedTaxTypes, setExpandedTaxTypes] = useState<ExpandedTaxTypes>({});

  // Modal states
  const [taxTypeModal, setTaxTypeModal] = useState({
    isOpen: false,
    mode: 'create' as 'create' | 'edit',
    taxType: null as TaxType | null
  });

  const [deleteModal, setDeleteModal] = useState({
    isOpen: false,
    id: 0,
    name: '',
    isLoading: false
  });

  const [successModal, setSuccessModal] = useState({
    isOpen: false,
    message: ''
  });

  // Tax Rate Modal states
  const [taxRateModal, setTaxRateModal] = useState({
    isOpen: false,
    mode: 'create' as 'create' | 'edit',
    taxTypeId: 0,
    rate: null as TaxRate | null
  });

  const [deleteTaxRateModal, setDeleteTaxRateModal] = useState({
    isOpen: false,
    id: 0,
    name: '',
    isLoading: false
  });

  // Form state
  const [taxTypeForm, setTaxTypeForm] = useState({
    name: '',
    name_ar: '',
    description: '',
    tax_category: 'standard',
    calculation_method: 'percentage',
    is_compound: false,
    is_active: true,
    sort_order: 0
  });

  const [taxRateForm, setTaxRateForm] = useState<TaxRateForm>({
    name: '',
    rate_value: '',
    description: '',
    effective_from: '',
    effective_to: '',
    is_default: false,
    is_active: true,
    applies_to: 'all',
    min_amount: '',
    max_amount: '',
    tax_code: '',
    sort_order: 0
  });

  // Load data
  useEffect(() => {
    loadTaxTypes();
    loadTaxRates();
  }, []);

  const loadTaxTypes = async () => {
    try {
      await fetchTaxTypes();
    } catch (error) {
      console.error('خطأ في تحميل أنواع الضرائب:', error);
    }
  };

  const loadTaxRates = async () => {
    try {
      await fetchTaxRates();
    } catch (error) {
      console.error('خطأ في تحميل قيم الضرائب:', error);
    }
  };

  // Toggle tax type expansion
  const toggleTaxTypeExpansion = (taxTypeId: number) => {
    setExpandedTaxTypes(prev => ({
      ...prev,
      [taxTypeId]: !prev[taxTypeId]
    }));
  };

  // Get tax rates for a specific tax type
  const getTaxRatesForType = (taxTypeId: number) => {
    return taxRates.filter(rate => rate.tax_type_id === taxTypeId);
  };

  // Filter and paginate data
  const filteredTaxTypes = useMemo(() => {
    return taxTypes.filter(taxType => {
      const matchesSearch = !filters.search || 
        taxType.name_ar.toLowerCase().includes(filters.search.toLowerCase()) ||
        taxType.name.toLowerCase().includes(filters.search.toLowerCase()) ||
        (taxType.description && taxType.description.toLowerCase().includes(filters.search.toLowerCase()));

      const matchesStatus = filters.status === 'all' || 
        (filters.status === 'active' && taxType.is_active) ||
        (filters.status === 'inactive' && !taxType.is_active);

      const matchesCategory = filters.tax_category === 'all' ||
        taxType.tax_category === filters.tax_category;

      return matchesSearch && matchesStatus && matchesCategory;
    });
  }, [taxTypes, filters]);

  const paginatedTaxTypes = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredTaxTypes.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredTaxTypes, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(filteredTaxTypes.length / itemsPerPage);
  const totalItems = filteredTaxTypes.length;

  // Expand all tax types that have tax rates
  const expandAllTaxTypes = () => {
    const newExpanded: ExpandedTaxTypes = {};
    filteredTaxTypes.forEach(taxType => {
      const taxRatesForType = getTaxRatesForType(taxType.id);
      if (taxRatesForType.length > 0) {
        newExpanded[taxType.id] = true;
      }
    });
    setExpandedTaxTypes(newExpanded);
  };

  // Collapse all tax types
  const collapseAllTaxTypes = () => {
    setExpandedTaxTypes({});
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (items: number) => {
    setItemsPerPage(items);
    setCurrentPage(1);
  };

  // Handlers
  const handleCreateTaxType = () => {
    setTaxTypeForm({
      name: '',
      name_ar: '',
      description: '',
      tax_category: 'standard',
      calculation_method: 'percentage',
      is_compound: false,
      is_active: true,
      sort_order: 0
    });
    setTaxTypeModal({ isOpen: true, mode: 'create', taxType: null });
  };

  const handleEditTaxType = (taxType: TaxType) => {
    setTaxTypeForm({
      name: taxType.name,
      name_ar: taxType.name_ar,
      description: taxType.description || '',
      tax_category: taxType.tax_category,
      calculation_method: taxType.calculation_method,
      is_compound: taxType.is_compound,
      is_active: taxType.is_active,
      sort_order: taxType.sort_order
    });
    setTaxTypeModal({ isOpen: true, mode: 'edit', taxType });
  };

  const handleDeleteTaxType = (taxType: TaxType) => {
    setDeleteModal({
      isOpen: true,
      id: taxType.id,
      name: taxType.name_ar,
      isLoading: false
    });
  };

  // Tax Rate handlers
  const handleCreateTaxRate = (taxTypeId: number) => {
    setTaxRateForm({
      name: '',
      rate_value: '',
      description: '',
      effective_from: '',
      effective_to: '',
      is_default: false,
      is_active: true,
      applies_to: 'all',
      min_amount: '',
      max_amount: '',
      tax_code: '',
      sort_order: 0
    });
    setTaxRateModal({ isOpen: true, mode: 'create', taxTypeId, rate: null });
  };

  const handleEditTaxRate = (rate: TaxRate) => {
    setTaxRateForm({
      name: rate.name,
      rate_value: rate.rate_value.toString(),
      description: rate.description || '',
      effective_from: rate.effective_from || '',
      effective_to: rate.effective_to || '',
      is_default: rate.is_default,
      is_active: rate.is_active,
      applies_to: rate.applies_to,
      min_amount: rate.min_amount?.toString() || '',
      max_amount: rate.max_amount?.toString() || '',
      tax_code: rate.tax_code || '',
      sort_order: rate.sort_order
    });
    setTaxRateModal({ isOpen: true, mode: 'edit', taxTypeId: rate.tax_type_id, rate });
  };

  const handleDeleteTaxRate = (rate: TaxRate) => {
    setDeleteTaxRateModal({
      isOpen: true,
      id: rate.id,
      name: rate.name,
      isLoading: false
    });
  };

  const handleTaxRateSubmit = async () => {
    try {
      const formData = {
        tax_type_id: taxRateModal.taxTypeId,
        name: taxRateForm.name,
        rate_value: parseFloat(taxRateForm.rate_value),
        description: taxRateForm.description || undefined,
        effective_from: taxRateForm.effective_from || undefined,
        effective_to: taxRateForm.effective_to || undefined,
        is_default: taxRateForm.is_default,
        is_active: taxRateForm.is_active,
        applies_to: taxRateForm.applies_to,
        min_amount: taxRateForm.min_amount ? parseFloat(taxRateForm.min_amount) : undefined,
        max_amount: taxRateForm.max_amount ? parseFloat(taxRateForm.max_amount) : undefined,
        tax_code: taxRateForm.tax_code || undefined,
        sort_order: taxRateForm.sort_order
      };

      if (taxRateModal.mode === 'create') {
        await createTaxRate(formData);
        setSuccessModal({
          isOpen: true,
          message: 'تم إضافة القيمة الضريبية بنجاح'
        });
      } else if (taxRateModal.rate) {
        await updateTaxRate(taxRateModal.rate.id, formData);
        setSuccessModal({
          isOpen: true,
          message: 'تم تحديث القيمة الضريبية بنجاح'
        });
      }

      setTaxRateModal({ isOpen: false, mode: 'create', taxTypeId: 0, rate: null });
      await loadTaxRates();
    } catch (error) {
      console.error('خطأ في حفظ القيمة الضريبية:', error);
    }
  };

  const confirmDeleteTaxRate = async () => {
    try {
      setDeleteTaxRateModal(prev => ({ ...prev, isLoading: true }));
      await deleteTaxRate(deleteTaxRateModal.id);
      setDeleteTaxRateModal({ isOpen: false, id: 0, name: '', isLoading: false });
      setSuccessModal({
        isOpen: true,
        message: 'تم حذف القيمة الضريبية بنجاح'
      });
      await loadTaxRates();
    } catch (error) {
      console.error('خطأ في حذف القيمة الضريبية:', error);
      setDeleteTaxRateModal(prev => ({ ...prev, isLoading: false }));
    }
  };

  const handleSaveTaxType = async () => {
    try {
      // تحويل نوع البيانات إلى النوع المطلوب
      const formData = {
        ...taxTypeForm,
        tax_category: taxTypeForm.tax_category as 'standard' | 'reduced' | 'zero' | 'exempt',
        calculation_method: taxTypeForm.calculation_method as 'percentage' | 'fixed'
      };

      if (taxTypeModal.mode === 'create') {
        await createTaxType(formData);
        setSuccessModal({
          isOpen: true,
          message: 'تم إنشاء نوع الضريبة بنجاح'
        });
      } else if (taxTypeModal.taxType) {
        await updateTaxType(taxTypeModal.taxType.id, formData);
        setSuccessModal({
          isOpen: true,
          message: 'تم تحديث نوع الضريبة بنجاح'
        });
      }
      setTaxTypeModal({ isOpen: false, mode: 'create', taxType: null });
    } catch (error) {
      console.error('خطأ في حفظ نوع الضريبة:', error);
    }
  };

  const handleConfirmDelete = async () => {
    setDeleteModal(prev => ({ ...prev, isLoading: true }));
    try {
      await deleteTaxType(deleteModal.id);
      setDeleteModal({ isOpen: false, id: 0, name: '', isLoading: false });
      setSuccessModal({
        isOpen: true,
        message: 'تم حذف نوع الضريبة بنجاح'
      });
    } catch (error) {
      console.error('خطأ في حذف نوع الضريبة:', error);
      setDeleteModal(prev => ({ ...prev, isLoading: false }));
    }
  };

  const handleRefresh = () => {
    loadTaxTypes();
  };

  const handleClearFilters = () => {
    setFilters({
      search: '',
      status: 'all',
      tax_category: 'all'
    });
    setCurrentPage(1);
  };

  // Options for select inputs
  const statusOptions = [
    { value: 'all', label: 'جميع الحالات' },
    { value: 'active', label: 'نشط' },
    { value: 'inactive', label: 'غير نشط' }
  ];

  const categoryOptions = [
    { value: 'all', label: 'جميع الفئات' },
    { value: 'standard', label: 'معيارية' },
    { value: 'reduced', label: 'مخفضة' },
    { value: 'zero', label: 'صفر' },
    { value: 'exempt', label: 'معفاة' }
  ];



  const taxCategoryFormOptions = [
    { value: 'standard', label: 'معيارية' },
    { value: 'reduced', label: 'مخفضة' },
    { value: 'zero', label: 'صفر' },
    { value: 'exempt', label: 'معفاة' }
  ];

  const calculationMethodFormOptions = [
    { value: 'percentage', label: 'نسبة مئوية' },
    { value: 'fixed', label: 'مبلغ ثابت' }
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Data Table with Header and Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 overflow-hidden">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center p-6 gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <div className="min-w-0 flex-1">
                <h2 className="text-lg sm:text-xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FiPercent className="ml-2 sm:ml-3 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                  <span className="truncate">أنواع الضرائب</span>
                </h2>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1">
                  إدارة أنواع الضرائب والقيم الضريبية المختلفة
                </p>
              </div>
            </div>

            <div className="flex flex-wrap items-center gap-3">
              <button
                onClick={expandAllTaxTypes}
                className="px-4 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
              >
                توسيع الكل
              </button>
              <button
                onClick={collapseAllTaxTypes}
                className="px-4 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
              >
                طي الكل
              </button>
              <button
                onClick={handleCreateTaxType}
                className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl"
              >
                <FiPlus className="ml-2" />
                إضافة نوع ضريبة جديد
              </button>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-end">
            {/* Search - Using unified SearchInput */}
            <SearchInput
              label="البحث"
              value={filters.search}
              onChange={(value) => setFilters(prev => ({ ...prev, search: value }))}
              placeholder="البحث في أنواع الضرائب..."
              className="flex-1 min-w-[300px]"
              disabled={loading}
            />

            {/* Status Filter */}
            <div className="flex-1 min-w-[180px]">
              <SelectInput
                label="الحالة"
                name="status-filter"
                value={filters.status}
                onChange={(value) => setFilters(prev => ({ ...prev, status: value as any }))}
                options={statusOptions}
                placeholder="اختر الحالة..."
              />
            </div>

            {/* Category Filter */}
            <div className="flex-1 min-w-[200px]">
              <SelectInput
                label="فئة الضريبة"
                name="category-filter"
                value={filters.tax_category}
                onChange={(value) => setFilters(prev => ({ ...prev, tax_category: value as any }))}
                options={categoryOptions}
                placeholder="اختر الفئة..."
              />
            </div>

            {/* Clear Filters Button - Single button only */}
            <div className="flex-shrink-0">
              <button
                onClick={handleClearFilters}
                disabled={loading}
                className="h-10 px-6 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-500 transition-all duration-200 ease-in-out flex items-center justify-center font-medium border-2 border-transparent hover:border-gray-300 dark:hover:border-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FiFilter className="ml-2" />
                مسح الفلاتر
              </button>
            </div>
          </div>
        </div>
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            <span className="mr-3 text-gray-600 dark:text-gray-400">جاري التحميل...</span>
          </div>
        ) : filteredTaxTypes.length === 0 ? (
          <div className="text-center py-12">
            <FiPercent className="mx-auto text-6xl text-gray-300 dark:text-gray-600 mb-4" />
            <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
              لا توجد أنواع ضرائب
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              {filters.search || filters.status !== 'all' || filters.tax_category !== 'all'
                ? 'لا توجد أنواع ضرائب تطابق معايير البحث'
                : 'لم يتم إنشاء أي أنواع ضرائب بعد'
              }
            </p>
            {(!filters.search && filters.status === 'all' && filters.tax_category === 'all') && (
              <button
                onClick={handleCreateTaxType}
                className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center gap-2 text-sm font-medium"
              >
                <FiPlus className="w-4 h-4" />
                إضافة نوع ضريبة جديد
              </button>
            )}
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      #
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      نوع الضريبة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      الوصف
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      الفئة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      طريقة الحساب
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      القيم الضريبية
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      الحالة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {paginatedTaxTypes.map((taxType, index) => {
                    const startIndex = (currentPage - 1) * itemsPerPage;
                    const taxRatesForType = getTaxRatesForType(taxType.id);
                    return (
                      <React.Fragment key={taxType.id}>
                        {/* Tax Type Row */}
                        <tr className="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-150">
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            <div className="flex items-center">
                              <span className="font-medium">{startIndex + index + 1}</span>
                              {taxRatesForType.length > 0 && (
                                <button
                                  onClick={() => toggleTaxTypeExpansion(taxType.id)}
                                  className="mr-2 p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-150"
                                >
                                  {expandedTaxTypes[taxType.id] ? (
                                    <FiChevronDown className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                                  ) : (
                                    <FiChevronRight className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                                  )}
                                </button>
                              )}
                            </div>
                          </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {taxType.calculation_method === 'percentage' ? (
                              <FiPercent className="ml-3 text-primary-600 dark:text-primary-400 w-5 h-5" />
                            ) : (
                              <FiDollarSign className="ml-3 text-primary-600 dark:text-primary-400 w-5 h-5" />
                            )}
                            <div>
                              <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                {taxType.name_ar}
                              </div>
                              <div className="text-sm text-gray-500 dark:text-gray-400">
                                {taxType.name}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-600 dark:text-gray-400">
                          <div className="max-w-xs truncate">
                            {taxType.description || '-'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            taxType.tax_category === 'standard' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' :
                            taxType.tax_category === 'reduced' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                            taxType.tax_category === 'zero' ? 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400' :
                            'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                          }`}>
                            {taxType.tax_category === 'standard' ? 'معيارية' :
                             taxType.tax_category === 'reduced' ? 'مخفضة' :
                             taxType.tax_category === 'zero' ? 'صفر' : 'معفاة'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            taxType.calculation_method === 'percentage'
                              ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400'
                              : 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400'
                          }`}>
                            {taxType.calculation_method === 'percentage' ? 'نسبة مئوية' : 'مبلغ ثابت'}
                          </span>
                          {taxType.is_compound && (
                            <div className="mt-1">
                              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
                                <FiLayers className="w-3 h-3 mr-1" />
                                مركبة
                              </span>
                            </div>
                          )}
                        </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            <div className="flex items-center">
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-200">
                                {taxRatesForType.length} قيمة ضريبية
                              </span>
                            </div>
                          </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {taxType.is_active ? (
                              <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800">
                                <FiEye className="w-3 h-3 ml-1" />
                                نشط
                              </span>
                            ) : (
                              <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 border border-gray-200 dark:border-gray-700">
                                <FiEyeOff className="w-3 h-3 ml-1" />
                                غير نشط
                              </span>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center justify-end gap-1">
                            <TopbarTooltip text="إضافة قيمة ضريبية" position="top">
                              <button
                                onClick={() => handleCreateTaxRate(taxType.id)}
                                className="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-200 transition-colors duration-150 p-2 rounded-lg hover:bg-primary-50 dark:hover:bg-primary-900/20 border border-transparent hover:border-primary-200 dark:hover:border-primary-700"
                              >
                                <FiPlus className="w-4 h-4" />
                              </button>
                            </TopbarTooltip>
                            <button
                              onClick={() => handleEditTaxType(taxType)}
                              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 transition-colors duration-150 p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 border border-transparent hover:border-blue-200 dark:hover:border-blue-700"
                              title="تعديل نوع الضريبة"
                            >
                              <FiEdit className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleDeleteTaxType(taxType)}
                              className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 transition-colors duration-150 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 border border-transparent hover:border-red-200 dark:hover:border-red-700"
                              title="حذف نوع الضريبة"
                            >
                              <FiTrash className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>

                      {/* Tax Rates - Nested Table Structure */}
                      {expandedTaxTypes[taxType.id] && taxRatesForType.map((rate, rateIndex) => (
                        <tr key={`rate-${rate.id}`} className="bg-gradient-to-r from-blue-50/50 to-transparent dark:from-blue-950/20 dark:to-transparent border-r-2 border-blue-200 dark:border-blue-700 hover:from-blue-100/50 dark:hover:from-blue-900/30 transition-all duration-200">
                          <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                            <div className="flex items-center">
                              {/* Tree branch indicator */}
                              <div className="flex items-center mr-2">
                                <div className="w-4 h-px bg-blue-300 dark:bg-blue-600"></div>
                                <div className="w-1 h-1 bg-blue-400 dark:bg-blue-500 rounded-full"></div>
                              </div>
                              <span className="text-xs font-semibold text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/50 px-2 py-1 rounded-md">
                                {startIndex + index + 1}.{rateIndex + 1}
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-3 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="w-6 h-px bg-blue-200 dark:bg-blue-700 mr-2"></div>
                              <FiPercent className="ml-2 text-blue-500 dark:text-blue-400 w-4 h-4" />
                              <div className="mr-2">
                                <div className="text-sm font-medium text-gray-800 dark:text-gray-200">
                                  {rate.name}
                                </div>
                                <div className="text-xs text-gray-500 dark:text-gray-400">
                                  {rate.rate_value}{taxType.calculation_method === 'percentage' ? '%' : ' د.ل'} {rate.tax_code && `(${rate.tax_code})`}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-3 text-sm text-gray-500 dark:text-gray-400">
                            <div className="max-w-xs truncate italic">
                              {rate.description || 'لا يوجد وصف'}
                            </div>
                          </td>
                          <td className="px-6 py-3 whitespace-nowrap text-sm">
                            <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                              rate.applies_to === 'all' ? 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400' :
                              rate.applies_to === 'products' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                              'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                            }`}>
                              {rate.applies_to === 'all' ? 'الكل' :
                               rate.applies_to === 'products' ? 'المنتجات' : 'الخدمات'}
                            </span>
                          </td>
                          <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-400 dark:text-gray-500">
                            <span className="text-xs">-</span>
                          </td>
                          <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-400 dark:text-gray-500">
                            <span className="text-xs">-</span>
                          </td>
                          <td className="px-6 py-3 whitespace-nowrap">
                            <div className="flex items-center">
                              {rate.is_active ? (
                                <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800">
                                  <FiEye className="w-3 h-3 ml-1" />
                                  نشط
                                </span>
                              ) : (
                                <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 border border-gray-200 dark:border-gray-700">
                                  <FiEyeOff className="w-3 h-3 ml-1" />
                                  غير نشط
                                </span>
                              )}
                            </div>
                          </td>
                          <td className="px-6 py-3 whitespace-nowrap text-sm font-medium">
                            <div className="flex items-center justify-end gap-1">
                              <TopbarTooltip text="تعديل القيمة الضريبية" position="top">
                                <button
                                  onClick={() => handleEditTaxRate(rate)}
                                  className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 transition-colors duration-150 p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 border border-transparent hover:border-blue-200 dark:hover:border-blue-700"
                                >
                                  <FiEdit className="w-4 h-4" />
                                </button>
                              </TopbarTooltip>
                              <TopbarTooltip text="حذف القيمة الضريبية" position="top">
                                <button
                                  onClick={() => handleDeleteTaxRate(rate)}
                                  className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 transition-colors duration-150 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 border border-transparent hover:border-red-200 dark:hover:border-red-700"
                                >
                                  <FiTrash className="w-4 h-4" />
                                </button>
                              </TopbarTooltip>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </React.Fragment>
                    );
                  })}
                </tbody>
              </table>
            </div>

        {/* Pagination */}
        {totalItems > 0 && (
          <TablePagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={totalItems}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
            itemsPerPageOptions={[5, 10, 20, 50]}
          />
        )}
          </>
        )}
      </div>

      {/* Tax Type Modal */}
      <Modal
        isOpen={taxTypeModal.isOpen}
        onClose={() => setTaxTypeModal({ isOpen: false, mode: 'create', taxType: null })}
        title={taxTypeModal.mode === 'create' ? 'إضافة نوع ضريبة جديد' : 'تعديل نوع الضريبة'}
      >
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TextInput
              label="الاسم بالعربية"
              name="name_ar"
              value={taxTypeForm.name_ar}
              onChange={(value) => setTaxTypeForm(prev => ({ ...prev, name_ar: value }))}
              placeholder="أدخل اسم نوع الضريبة بالعربية"
              required
            />

            <TextInput
              label="الاسم بالإنجليزية"
              name="name"
              value={taxTypeForm.name}
              onChange={(value) => setTaxTypeForm(prev => ({ ...prev, name: value }))}
              placeholder="أدخل اسم نوع الضريبة بالإنجليزية"
              required
            />
          </div>

          <TextArea
            label="الوصف"
            name="description"
            value={taxTypeForm.description}
            onChange={(value) => setTaxTypeForm(prev => ({ ...prev, description: value }))}
            placeholder="أدخل وصف نوع الضريبة (اختياري)"
            rows={3}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <SelectInput
              label="فئة الضريبة"
              name="tax_category"
              value={taxTypeForm.tax_category}
              onChange={(value) => setTaxTypeForm(prev => ({ ...prev, tax_category: value }))}
              options={taxCategoryFormOptions}
              required
            />

            <SelectInput
              label="طريقة الحساب"
              name="calculation_method"
              value={taxTypeForm.calculation_method}
              onChange={(value) => setTaxTypeForm(prev => ({ ...prev, calculation_method: value }))}
              options={calculationMethodFormOptions}
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-10 flex items-center">
              <ToggleSwitch
                id="is_compound"
                checked={taxTypeForm.is_compound}
                onChange={(checked) => setTaxTypeForm(prev => ({ ...prev, is_compound: checked }))}
                label="ضريبة مركبة"
                className="w-full"
              />
            </div>

            <div className="bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-10 flex items-center">
              <ToggleSwitch
                id="is_active"
                checked={taxTypeForm.is_active}
                onChange={(checked) => setTaxTypeForm(prev => ({ ...prev, is_active: checked }))}
                label="نشط"
                className="w-full"
              />
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={() => setTaxTypeModal({ isOpen: false, mode: 'create', taxType: null })}
              className="bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium min-w-[140px]"
            >
              إلغاء
            </button>
            <button
              type="button"
              onClick={handleSaveTaxType}
              className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium shadow-lg hover:shadow-xl min-w-[140px]"
            >
              {taxTypeModal.mode === 'create' ? 'إضافة' : 'حفظ التغييرات'}
            </button>
          </div>
        </div>
      </Modal>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, id: 0, name: '', isLoading: false })}
        onConfirm={handleConfirmDelete}
        title="حذف نوع الضريبة"
        message={`هل أنت متأكد من حذف نوع الضريبة؟`}
        itemName={deleteModal.name}
        isLoading={deleteModal.isLoading}
      />

      {/* Success Modal */}
      <SuccessModal
        isOpen={successModal.isOpen}
        onClose={() => setSuccessModal({ isOpen: false, message: '' })}
        title="تم بنجاح"
        message={successModal.message}
      />

      {/* Tax Rate Modal */}
      <Modal
        isOpen={taxRateModal.isOpen}
        onClose={() => setTaxRateModal({ isOpen: false, mode: 'create', taxTypeId: 0, rate: null })}
        title={taxRateModal.mode === 'create' ? 'إضافة قيمة ضريبية جديدة' : 'تعديل القيمة الضريبية'}
        size="lg"
      >
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TextInput
              label="اسم القيمة الضريبية"
              name="name"
              value={taxRateForm.name}
              onChange={(value) => setTaxRateForm(prev => ({ ...prev, name: value }))}
              placeholder="أدخل اسم القيمة الضريبية"
              required
            />

            <NumberInput
              label="القيمة الضريبية"
              name="rate_value"
              step="0.01"
              value={taxRateForm.rate_value}
              onChange={(value) => setTaxRateForm(prev => ({ ...prev, rate_value: value }))}
              placeholder="أدخل القيمة الضريبية"
              required
            />
          </div>

          <TextArea
            label="الوصف"
            name="description"
            value={taxRateForm.description}
            onChange={(value) => setTaxRateForm(prev => ({ ...prev, description: value }))}
            placeholder="أدخل وصف القيمة الضريبية (اختياري)"
            rows={3}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <DatePicker
              label="تاريخ بداية السريان"
              name="effective_from"
              value={taxRateForm.effective_from}
              onChange={(value) => setTaxRateForm(prev => ({ ...prev, effective_from: value }))}
            />

            <DatePicker
              label="تاريخ نهاية السريان"
              name="effective_to"
              value={taxRateForm.effective_to}
              onChange={(value: string) => setTaxRateForm(prev => ({ ...prev, effective_to: value }))}
            />
          </div>

          <SelectInput
            label="ينطبق على"
            name="applies_to"
            value={taxRateForm.applies_to}
            onChange={(value) => setTaxRateForm(prev => ({ ...prev, applies_to: value as 'all' | 'products' | 'services' }))}
            options={[
              { value: 'all', label: 'جميع العناصر' },
              { value: 'products', label: 'المنتجات فقط' },
              { value: 'services', label: 'الخدمات فقط' }
            ]}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <NumberInput
              label="الحد الأدنى للمبلغ"
              name="min_amount"
              step="0.01"
              value={taxRateForm.min_amount}
              onChange={(value) => setTaxRateForm(prev => ({ ...prev, min_amount: value }))}
              placeholder="الحد الأدنى (اختياري)"
            />

            <NumberInput
              label="الحد الأقصى للمبلغ"
              name="max_amount"
              step="0.01"
              value={taxRateForm.max_amount}
              onChange={(value) => setTaxRateForm(prev => ({ ...prev, max_amount: value }))}
              placeholder="الحد الأقصى (اختياري)"
            />
          </div>

          <TextInput
            label="رمز الضريبة"
            name="tax_code"
            value={taxRateForm.tax_code}
            onChange={(value) => setTaxRateForm(prev => ({ ...prev, tax_code: value }))}
            placeholder="رمز الضريبة الحكومي (اختياري)"
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-12 flex items-center">
              <ToggleSwitch
                id="is_default"
                checked={taxRateForm.is_default}
                onChange={(checked) => setTaxRateForm(prev => ({ ...prev, is_default: checked }))}
                label="قيمة افتراضية"
                className="w-full"
              />
            </div>

            <div className="bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-12 flex items-center">
              <ToggleSwitch
                id="is_active"
                checked={taxRateForm.is_active}
                onChange={(checked) => setTaxRateForm(prev => ({ ...prev, is_active: checked }))}
                label="قيمة نشطة"
                className="w-full"
              />
            </div>
          </div>

          <div className="flex gap-3 pt-4">
            <button
              onClick={handleTaxRateSubmit}
              className="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl"
            >
              {taxRateModal.mode === 'create' ? 'إضافة القيمة الضريبية' : 'حفظ التغييرات'}
            </button>
            <button
              onClick={() => setTaxRateModal({ isOpen: false, mode: 'create', taxTypeId: 0, rate: null })}
              className="flex-1 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium"
            >
              إلغاء
            </button>
          </div>
        </div>
      </Modal>

      {/* Delete Tax Rate Modal */}
      <DeleteConfirmModal
        isOpen={deleteTaxRateModal.isOpen}
        onClose={() => setDeleteTaxRateModal({ isOpen: false, id: 0, name: '', isLoading: false })}
        onConfirm={confirmDeleteTaxRate}
        title="حذف القيمة الضريبية"
        message={`هل أنت متأكد من حذف القيمة الضريبية "${deleteTaxRateModal.name}"؟`}
        itemName={deleteTaxRateModal.name}
        isLoading={deleteTaxRateModal.isLoading}
      />
    </div>
  );
};

export default TaxTypesDataTable;
