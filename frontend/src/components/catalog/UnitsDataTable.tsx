import React, { useState, useEffect, useMemo } from 'react';
import {
  FiPlus,
  FiEdit,
  FiTrash,
  FiPackage,
  FiEye,
  FiEyeOff,
  FiSearch,
  FiFilter,
  FiRefreshCw
} from 'react-icons/fi';
import useUnitStore, { Unit } from '../../stores/unitStore';
import Modal from '../Modal';
import DeleteConfirmModal from '../DeleteConfirmModal';
import SuccessModal from '../SuccessModal';
import { TextInput, TextArea, SelectInput, ModalSelectInput, SmartSelectInput } from '../inputs';
import ToggleSwitch from '../ToggleSwitch';
import TablePagination from './TablePagination';
import SearchInput from '../SearchInput';

interface UnitsDataTableProps {
  className?: string;
}

interface TableFilters {
  search: string;
  status: 'all' | 'active' | 'inactive';
  unitType: 'all' | 'weight' | 'volume' | 'length' | 'area' | 'count';
  hasBaseUnit: 'all' | 'yes' | 'no';
}

const UnitsDataTable: React.FC<UnitsDataTableProps> = ({ className = '' }) => {
  const {
    units,
    loading,
    fetchUnits,
    createUnit,
    updateUnit,
    deleteUnit
  } = useUnitStore();

  // State
  const [filters, setFilters] = useState<TableFilters>({
    search: '',
    status: 'all',
    unitType: 'all',
    hasBaseUnit: 'all'
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Modal states
  const [unitModal, setUnitModal] = useState({
    isOpen: false,
    mode: 'create' as 'create' | 'edit',
    unit: null as Unit | null
  });

  const [deleteModal, setDeleteModal] = useState({
    isOpen: false,
    id: 0,
    name: '',
    isLoading: false
  });

  const [successModal, setSuccessModal] = useState({
    isOpen: false,
    message: ''
  });

  // Form state
  const [unitForm, setUnitForm] = useState({
    name: '',
    symbol: '',
    description: '',
    unit_type: '',
    base_unit_id: null as number | null,
    conversion_factor: null as number | null,
    is_active: true
  });

  // Load data
  useEffect(() => {
    loadUnits();
  }, []);

  const loadUnits = async () => {
    try {
      await fetchUnits({ active_only: false }); // جلب جميع الوحدات (النشطة والمعطلة)
    } catch (error) {
      console.error('Error loading units:', error);
    }
  };

  // Get unit type icon
  const getUnitTypeIcon = (unitType: string) => {
    switch (unitType) {
      case 'weight':
        return <FiPackage className="w-5 h-5 text-orange-500" />;
      case 'volume':
        return <FiPackage className="w-5 h-5 text-blue-500" />;
      case 'length':
        return <FiPackage className="w-5 h-5 text-green-500" />;
      case 'area':
        return <FiPackage className="w-5 h-5 text-purple-500" />;
      case 'count':
        return <FiPackage className="w-5 h-5 text-gray-500" />;
      default:
        return <FiPackage className="w-5 h-5 text-gray-400" />;
    }
  };

  // Filter and search logic
  const filteredUnits = useMemo(() => {
    return units.filter(unit => {
      // Search filter
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        const matchesUnit = unit.name.toLowerCase().includes(searchLower) ||
                           (unit.symbol && unit.symbol.toLowerCase().includes(searchLower)) ||
                           (unit.description && unit.description.toLowerCase().includes(searchLower)) ||
                           (unit.unit_type && unit.unit_type.toLowerCase().includes(searchLower));
        if (!matchesUnit) return false;
      }

      // Status filter
      if (filters.status !== 'all') {
        if (filters.status === 'active' && !unit.is_active) return false;
        if (filters.status === 'inactive' && unit.is_active) return false;
      }

      // Unit type filter
      if (filters.unitType !== 'all') {
        if (unit.unit_type !== filters.unitType) return false;
      }

      // Base unit filter
      if (filters.hasBaseUnit !== 'all') {
        if (filters.hasBaseUnit === 'yes' && !unit.base_unit_id) return false;
        if (filters.hasBaseUnit === 'no' && unit.base_unit_id) return false;
      }

      return true;
    });
  }, [units, filters]);

  // Pagination calculations
  const totalItems = filteredUnits.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedUnits = filteredUnits.slice(startIndex, endIndex);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [filters]);

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Unit handlers
  const handleCreateUnit = () => {
    setUnitForm({ 
      name: '', 
      symbol: '', 
      description: '', 
      unit_type: '', 
      base_unit_id: null, 
      conversion_factor: null, 
      is_active: true 
    });
    setUnitModal({ isOpen: true, mode: 'create', unit: null });
  };

  const handleEditUnit = (unit: Unit) => {
    setUnitForm({
      name: unit.name,
      symbol: unit.symbol || '',
      description: unit.description || '',
      unit_type: unit.unit_type || '',
      base_unit_id: unit.base_unit_id || null,
      conversion_factor: unit.conversion_factor || null,
      is_active: unit.is_active
    });
    setUnitModal({ isOpen: true, mode: 'edit', unit });
  };

  const handleDeleteUnit = (unit: Unit) => {
    setDeleteModal({
      isOpen: true,
      id: unit.id,
      name: unit.name,
      isLoading: false
    });
  };

  // Form submission handlers
  const handleUnitSubmit = async () => {
    try {
      if (unitModal.mode === 'create') {
        await createUnit(unitForm);
        setSuccessModal({ isOpen: true, message: 'تم إنشاء الوحدة بنجاح' });
      } else if (unitModal.unit) {
        await updateUnit(unitModal.unit.id, unitForm);
        setSuccessModal({ isOpen: true, message: 'تم تحديث الوحدة بنجاح' });
      }
      setUnitModal({ isOpen: false, mode: 'create', unit: null });
      await loadUnits();
    } catch (error) {
      console.error('Error saving unit:', error);
    }
  };

  const confirmDelete = async () => {
    try {
      setDeleteModal(prev => ({ ...prev, isLoading: true }));
      await deleteUnit(deleteModal.id);
      setSuccessModal({ isOpen: true, message: 'تم حذف الوحدة بنجاح' });
      setDeleteModal({ isOpen: false, id: 0, name: '', isLoading: false });
      await loadUnits();
    } catch (error) {
      console.error('Error deleting unit:', error);
      setDeleteModal(prev => ({ ...prev, isLoading: false }));
    }
  };

  // Clear filters
  const clearFilters = () => {
    setFilters({
      search: '',
      status: 'all',
      unitType: 'all',
      hasBaseUnit: 'all'
    });
  };

  // Get base units for dropdown
  const baseUnits = units.filter(unit => !unit.base_unit_id);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Data Table with Header and Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 overflow-hidden">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center p-6 gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <div className="min-w-0 flex-1">
                <h2 className="text-lg sm:text-xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FiPackage className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                  <span className="truncate">إدارة الوحدات</span>
                </h2>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1">
                  إدارة وحدات القياس للمنتجات
                </p>
              </div>
            </div>

            <div className="flex flex-wrap items-center gap-3">
              <button
                onClick={handleCreateUnit}
                className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl"
              >
                <FiPlus className="ml-2" />
                إضافة وحدة جديدة
              </button>
            </div>
          </div>
        </div>

        {/* Filters and Search - Updated with unified components */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-end">
            {/* Search - Using unified SearchInput */}
            <SearchInput
              label="البحث"
              value={filters.search}
              onChange={(value) => setFilters(prev => ({ ...prev, search: value }))}
              placeholder="البحث في الوحدات..."
              className="flex-1 min-w-[300px]"
              disabled={loading}
            />

            {/* Status Filter */}
            <div className="flex-1 min-w-[180px]">
              <SelectInput
                label="الحالة"
                name="status-filter"
                value={filters.status}
                onChange={(value) => setFilters(prev => ({ ...prev, status: value as any }))}
                options={[
                  { value: 'all', label: 'جميع الحالات' },
                  { value: 'active', label: 'نشط' },
                  { value: 'inactive', label: 'غير نشط' }
                ]}
                placeholder="اختر الحالة..."
              />
            </div>

            {/* Unit Type Filter */}
            <div className="flex-1 min-w-[180px]">
              <SelectInput
                label="نوع الوحدة"
                name="unit-type-filter"
                value={filters.unitType}
                onChange={(value) => setFilters(prev => ({ ...prev, unitType: value as any }))}
                options={[
                  { value: 'all', label: 'جميع الأنواع' },
                  { value: 'weight', label: 'وزن' },
                  { value: 'volume', label: 'حجم' },
                  { value: 'length', label: 'طول' },
                  { value: 'area', label: 'مساحة' },
                  { value: 'count', label: 'عدد' }
                ]}
                placeholder="اختر نوع الوحدة..."
              />
            </div>

            {/* Base Unit Filter */}
            <div className="flex-1 min-w-[180px]">
              <SelectInput
                label="الوحدة الأساسية"
                name="base-unit-filter"
                value={filters.hasBaseUnit}
                onChange={(value) => setFilters(prev => ({ ...prev, hasBaseUnit: value as any }))}
                options={[
                  { value: 'all', label: 'جميع الوحدات' },
                  { value: 'yes', label: 'لها وحدة أساسية' },
                  { value: 'no', label: 'وحدة أساسية' }
                ]}
                placeholder="اختر نوع الوحدة الأساسية..."
              />
            </div>

            {/* Clear Filters Button - Single button only */}
            <div className="flex-shrink-0">
              <button
                onClick={clearFilters}
                disabled={loading}
                className="h-10 px-6 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-500 transition-all duration-200 ease-in-out flex items-center justify-center font-medium border-2 border-transparent hover:border-gray-300 dark:hover:border-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FiFilter className="ml-2" />
                مسح الفلاتر
              </button>
            </div>
          </div>
        </div>
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-600 dark:text-gray-400 mt-2 mr-3">جاري التحميل...</p>
          </div>
        ) : totalItems === 0 ? (
          <div className="text-center py-12">
            <FiPackage className="mx-auto text-6xl text-gray-300 dark:text-gray-600 mb-4" />
            <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
              {filters.search || filters.status !== 'all' || filters.unitType !== 'all' || filters.hasBaseUnit !== 'all'
                ? 'لا توجد نتائج مطابقة للفلاتر'
                : 'لا توجد وحدات'}
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              {filters.search || filters.status !== 'all' || filters.unitType !== 'all' || filters.hasBaseUnit !== 'all'
                ? 'جرب تغيير الفلاتر أو البحث'
                : 'ابدأ بإضافة وحدة جديدة'}
            </p>
            {(!filters.search && filters.status === 'all' && filters.unitType === 'all' && filters.hasBaseUnit === 'all') && (
              <button
                onClick={handleCreateUnit}
                className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl mx-auto"
              >
                <FiPlus className="ml-2" />
                إضافة وحدة جديدة
              </button>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              {/* Table Header */}
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-12">
                    #
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    اسم الوحدة
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الرمز
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    النوع
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الوحدة الأساسية
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    معامل التحويل
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-24">
                    الحالة
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-32">
                    الإجراءات
                  </th>
                </tr>
              </thead>

              {/* Table Body */}
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {paginatedUnits.map((unit, index) => (
                  <tr key={unit.id} className="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-150">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                      <span className="font-medium">{startIndex + index + 1}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 ml-3">
                          {getUnitTypeIcon(unit.unit_type || '')}
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {unit.name}
                          </div>
                          {unit.description && (
                            <div className="text-xs text-gray-500 dark:text-gray-400 truncate max-w-xs">
                              {unit.description}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                      {unit.symbol ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                          {unit.symbol}
                        </span>
                      ) : (
                        '-'
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                      {unit.unit_type ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200">
                          {unit.unit_type === 'weight' && 'وزن'}
                          {unit.unit_type === 'volume' && 'حجم'}
                          {unit.unit_type === 'length' && 'طول'}
                          {unit.unit_type === 'area' && 'مساحة'}
                          {unit.unit_type === 'count' && 'عدد'}
                        </span>
                      ) : (
                        '-'
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                      {unit.base_unit ? unit.base_unit.name : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                      {unit.conversion_factor || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {unit.is_active ? (
                          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800">
                            <FiEye className="w-3 h-3 ml-1" />
                            نشط
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 border border-gray-200 dark:border-gray-700">
                            <FiEyeOff className="w-3 h-3 ml-1" />
                            غير نشط
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center justify-end gap-1">
                        <button
                          onClick={() => handleEditUnit(unit)}
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 transition-colors duration-150 p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 border border-transparent hover:border-blue-200 dark:hover:border-blue-700"
                          title="تعديل الوحدة"
                        >
                          <FiEdit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteUnit(unit)}
                          className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 transition-colors duration-150 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 border border-transparent hover:border-red-200 dark:hover:border-red-700"
                          title="حذف الوحدة"
                        >
                          <FiTrash className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {totalItems > 0 && (
          <TablePagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={totalItems}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
            itemsPerPageOptions={[5, 10, 20, 50]}
          />
        )}
      </div>

      {/* Unit Modal */}
      <Modal
        isOpen={unitModal.isOpen}
        onClose={() => setUnitModal({ isOpen: false, mode: 'create', unit: null })}
        title={unitModal.mode === 'create' ? 'إضافة وحدة جديدة' : 'تعديل الوحدة'}
      >
        <div className="space-y-4">
          <TextInput
            label="اسم الوحدة"
            name="unit-name"
            value={unitForm.name}
            onChange={(value) => setUnitForm(prev => ({ ...prev, name: value }))}
            placeholder="أدخل اسم الوحدة"
            required
          />

          <TextInput
            label="الرمز"
            name="unit-symbol"
            value={unitForm.symbol}
            onChange={(value) => setUnitForm(prev => ({ ...prev, symbol: value }))}
            placeholder="أدخل رمز الوحدة (اختياري)"
          />

          <TextArea
            label="الوصف"
            name="unit-description"
            value={unitForm.description}
            onChange={(value) => setUnitForm(prev => ({ ...prev, description: value }))}
            placeholder="أدخل وصف الوحدة (اختياري)"
            rows={3}
          />

          <ModalSelectInput
            label="نوع الوحدة"
            name="unit-type"
            value={unitForm.unit_type}
            onChange={(value) => setUnitForm(prev => ({ ...prev, unit_type: value }))}
            options={[
              { value: '', label: 'اختر نوع الوحدة' },
              { value: 'weight', label: 'وزن' },
              { value: 'volume', label: 'حجم' },
              { value: 'length', label: 'طول' },
              { value: 'area', label: 'مساحة' },
              { value: 'count', label: 'عدد' }
            ]}
          />

          <SmartSelectInput
            label="الوحدة الأساسية"
            name="base-unit"
            value={unitForm.base_unit_id?.toString() || ''}
            onChange={(value) => setUnitForm(prev => ({ ...prev, base_unit_id: value ? parseInt(value) : null }))}
            options={[
              { value: '', label: 'لا توجد وحدة أساسية' },
              ...baseUnits.map(unit => ({ value: unit.id.toString(), label: unit.name }))
            ]}
            searchable={true}
          />

          {unitForm.base_unit_id && (
            <TextInput
              label="معامل التحويل"
              name="conversion-factor"
              value={unitForm.conversion_factor?.toString() || ''}
              onChange={(value) => setUnitForm(prev => ({ ...prev, conversion_factor: value ? parseFloat(value) : null }))}
              placeholder="أدخل معامل التحويل"
            />
          )}

          <div className="bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-10 flex items-center">
            <ToggleSwitch
              id="unit-active"
              checked={unitForm.is_active}
              onChange={(checked) => setUnitForm(prev => ({ ...prev, is_active: checked }))}
              label="وحدة نشطة"
              className="w-full"
            />
          </div>

          <div className="flex gap-3 pt-4">
            <button
              onClick={handleUnitSubmit}
              className="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl"
            >
              {unitModal.mode === 'create' ? 'إضافة الوحدة' : 'حفظ التغييرات'}
            </button>
            <button
              onClick={() => setUnitModal({ isOpen: false, mode: 'create', unit: null })}
              className="flex-1 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium"
            >
              إلغاء
            </button>
          </div>
        </div>
      </Modal>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, id: 0, name: '', isLoading: false })}
        onConfirm={confirmDelete}
        title="حذف الوحدة"
        message={`هل أنت متأكد من حذف الوحدة "${deleteModal.name}"؟`}
        itemName={deleteModal.name}
        isLoading={deleteModal.isLoading}
      />

      {/* Success Modal */}
      <SuccessModal
        isOpen={successModal.isOpen}
        onClose={() => setSuccessModal({ isOpen: false, message: '' })}
        title="نجح العملية"
        message={successModal.message}
      />
    </div>
  );
};

export default UnitsDataTable;
