import React, { useState } from 'react';
import { SimpleTooltip, Tooltip } from './ui';
import { FiInfo, FiCheck, FiAlert, FiX, FiStar, FiHeart } from 'react-icons/fi';

/**
 * مكون عرض توضيحي للتلميحات
 * يعرض جميع أنواع وأحجام ومواضع التلميحات المتاحة
 */
const TooltipDemo: React.FC = () => {
  const [selectedVariant, setSelectedVariant] = useState<'default' | 'light' | 'primary' | 'success' | 'warning' | 'error'>('default');
  const [selectedSize, setSelectedSize] = useState<'sm' | 'md' | 'lg'>('md');
  const [selectedPosition, setSelectedPosition] = useState<'top' | 'bottom' | 'left' | 'right'>('top');

  const variants = [
    { key: 'default', name: 'افتراضي', color: 'bg-gray-600' },
    { key: 'light', name: 'فاتح', color: 'bg-white border border-gray-300' },
    { key: 'primary', name: 'أساسي', color: 'bg-blue-600' },
    { key: 'success', name: 'نجاح', color: 'bg-green-600' },
    { key: 'warning', name: 'تحذير', color: 'bg-yellow-600' },
    { key: 'error', name: 'خطأ', color: 'bg-red-600' }
  ] as const;

  const sizes = [
    { key: 'sm', name: 'صغير' },
    { key: 'md', name: 'متوسط' },
    { key: 'lg', name: 'كبير' }
  ] as const;

  const positions = [
    { key: 'top', name: 'أعلى' },
    { key: 'bottom', name: 'أسفل' },
    { key: 'left', name: 'يسار' },
    { key: 'right', name: 'يمين' }
  ] as const;

  return (
    <div className="p-8 space-y-8 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          عرض توضيحي للتلميحات
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mb-8">
          اختبار جميع أنواع وأحجام ومواضع التلميحات في النظام
        </p>

        {/* أدوات التحكم */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm mb-8">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
            أدوات التحكم
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* اختيار النوع */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                نوع التلميح
              </label>
              <div className="grid grid-cols-2 gap-2">
                {variants.map(variant => (
                  <button
                    key={variant.key}
                    onClick={() => setSelectedVariant(variant.key)}
                    className={`
                      p-2 rounded-lg text-sm font-medium transition-all duration-200
                      ${selectedVariant === variant.key
                        ? 'ring-2 ring-primary-500 bg-primary-50 dark:bg-primary-900/20'
                        : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                      }
                    `}
                  >
                    <div className={`w-4 h-4 rounded mx-auto mb-1 ${variant.color}`}></div>
                    <span className="text-gray-900 dark:text-gray-100">{variant.name}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* اختيار الحجم */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                حجم التلميح
              </label>
              <div className="space-y-2">
                {sizes.map(size => (
                  <button
                    key={size.key}
                    onClick={() => setSelectedSize(size.key)}
                    className={`
                      w-full p-2 rounded-lg text-sm font-medium transition-all duration-200
                      ${selectedSize === size.key
                        ? 'bg-primary-600 text-white'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-600'
                      }
                    `}
                  >
                    {size.name}
                  </button>
                ))}
              </div>
            </div>

            {/* اختيار الموضع */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                موضع التلميح
              </label>
              <div className="grid grid-cols-2 gap-2">
                {positions.map(position => (
                  <button
                    key={position.key}
                    onClick={() => setSelectedPosition(position.key)}
                    className={`
                      p-2 rounded-lg text-sm font-medium transition-all duration-200
                      ${selectedPosition === position.key
                        ? 'bg-primary-600 text-white'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-600'
                      }
                    `}
                  >
                    {position.name}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* منطقة الاختبار */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-12 shadow-sm">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-8 text-center">
            منطقة الاختبار
          </h2>
          
          <div className="flex items-center justify-center min-h-[200px]">
            <SimpleTooltip
              text={`تلميح ${variants.find(v => v.key === selectedVariant)?.name} - ${sizes.find(s => s.key === selectedSize)?.name}`}
              position={selectedPosition}
              variant={selectedVariant}
              size={selectedSize}
              delay={300}
            >
              <button className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors font-medium">
                مرر الماوس هنا لرؤية التلميح
              </button>
            </SimpleTooltip>
          </div>
        </div>

        {/* أمثلة متنوعة */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-6">
            أمثلة متنوعة
          </h2>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {/* تلميحات الأيقونات */}
            <div className="text-center">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                تلميحات الأيقونات
              </h3>
              <div className="flex justify-center gap-3">
                <SimpleTooltip text="معلومات" variant="primary">
                  <FiInfo className="w-6 h-6 text-blue-600 cursor-pointer hover:text-blue-700" />
                </SimpleTooltip>
                <SimpleTooltip text="تم بنجاح" variant="success">
                  <FiCheck className="w-6 h-6 text-green-600 cursor-pointer hover:text-green-700" />
                </SimpleTooltip>
                <SimpleTooltip text="تحذير" variant="warning">
                  <FiAlert className="w-6 h-6 text-yellow-600 cursor-pointer hover:text-yellow-700" />
                </SimpleTooltip>
                <SimpleTooltip text="خطأ" variant="error">
                  <FiX className="w-6 h-6 text-red-600 cursor-pointer hover:text-red-700" />
                </SimpleTooltip>
              </div>
            </div>

            {/* تلميحات تفاعلية */}
            <div className="text-center">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                تلميحات تفاعلية
              </h3>
              <Tooltip
                content={
                  <div className="p-2">
                    <p className="font-semibold mb-2">تلميح تفاعلي</p>
                    <p className="text-sm">يمكنك التفاعل مع هذا المحتوى</p>
                    <button className="mt-2 px-3 py-1 bg-primary-600 text-white rounded text-xs hover:bg-primary-700">
                      انقر هنا
                    </button>
                  </div>
                }
                interactive={true}
                variant="light"
                position="bottom"
              >
                <FiStar className="w-6 h-6 text-yellow-500 cursor-pointer hover:text-yellow-600 mx-auto" />
              </Tooltip>
            </div>

            {/* أحجام مختلفة */}
            <div className="text-center">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                أحجام مختلفة
              </h3>
              <div className="space-y-2">
                <SimpleTooltip text="صغير" size="sm" variant="default">
                  <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded mx-auto cursor-pointer"></div>
                </SimpleTooltip>
                <SimpleTooltip text="متوسط" size="md" variant="default">
                  <div className="w-10 h-10 bg-gray-400 dark:bg-gray-500 rounded mx-auto cursor-pointer"></div>
                </SimpleTooltip>
                <SimpleTooltip text="كبير" size="lg" variant="default">
                  <div className="w-12 h-12 bg-gray-500 dark:bg-gray-400 rounded mx-auto cursor-pointer"></div>
                </SimpleTooltip>
              </div>
            </div>

            {/* مواضع مختلفة */}
            <div className="text-center">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                مواضع مختلفة
              </h3>
              <div className="grid grid-cols-3 gap-2 max-w-24 mx-auto">
                <div></div>
                <SimpleTooltip text="أعلى" position="top">
                  <div className="w-6 h-6 bg-blue-500 rounded cursor-pointer"></div>
                </SimpleTooltip>
                <div></div>
                
                <SimpleTooltip text="يسار" position="left">
                  <div className="w-6 h-6 bg-blue-500 rounded cursor-pointer"></div>
                </SimpleTooltip>
                <FiHeart className="w-6 h-6 text-red-500 mx-auto" />
                <SimpleTooltip text="يمين" position="right">
                  <div className="w-6 h-6 bg-blue-500 rounded cursor-pointer"></div>
                </SimpleTooltip>
                
                <div></div>
                <SimpleTooltip text="أسفل" position="bottom">
                  <div className="w-6 h-6 bg-blue-500 rounded cursor-pointer"></div>
                </SimpleTooltip>
                <div></div>
              </div>
            </div>
          </div>
        </div>

        {/* ملاحظات الاختبار */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-3">
            ملاحظات الاختبار
          </h2>
          <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-2">
            <li>• تأكد من ظهور التلميحات في جميع المواضع بشكل صحيح</li>
            <li>• اختبر التلميحات في الوضع المظلم والمضيء</li>
            <li>• تحقق من الانيميشن والتأثيرات البصرية</li>
            <li>• اختبر التلميحات التفاعلية والقابلة للنقر</li>
            <li>• تأكد من عمل التلميحات على الأجهزة اللمسية</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default TooltipDemo;
