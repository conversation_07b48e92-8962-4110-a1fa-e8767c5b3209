import React, { useState, useEffect } from 'react';
import {
  FaPlus,
  FaEdit,
  FaTrash,
  FaPlay,
  FaPause,
  FaClock,
  FaExclamationTriangle,
  FaSpinner,
  FaCalendarAlt,
  FaDatabase,
  FaBroom,
  FaCloud,
  FaCheck,
  FaCircle
} from 'react-icons/fa';
import api from '../lib/axios';
import Modal from './Modal';
import CronBuilder from './CronBuilder';
// import SchedulingDemo from './SchedulingDemo'; // تم حذف المكون
// import EnhancedFormDemo from './EnhancedFormDemo'; // تم حذف المكون
import TaskTypeSelector from './TaskTypeSelector';
import StatusSelector from './StatusSelector';
import TaskErrorDisplay from './TaskErrorDisplay';
import TaskErrorStats from './TaskErrorStats';


import ActionBar from './ActionBar';
import { TextInput, TextArea } from './inputs';
import { formatDateTime } from '../services/dateTimeService';
import scheduledTaskErrorService from '../services/scheduledTaskErrorService';
import { useTaskErrorMonitoring, useTaskHealthMonitoring } from '../hooks/useTaskErrorMonitoring';

interface ScheduledTask {
  id: number;
  name: string;
  description?: string;
  task_type: 'database_backup' | 'cleanup_old_backups' | 'google_drive_backup' | 'google_drive_cleanup' | 'system_maintenance';
  cron_expression: string;
  status: 'active' | 'paused' | 'disabled';
  last_run?: string;
  next_run?: string;
  run_count: number;
  failure_count: number;
  last_error?: string;
  is_system_task: boolean;
  created_at: string;
}

interface ScheduledTasksManagerProps {
  backupPath: string;
}

const ScheduledTasksManager: React.FC<ScheduledTasksManagerProps> = ({ backupPath }) => {
  // استخدام backupPath في معاملات المهام
  const getTaskParams = (taskType: string) => {
    switch (taskType) {
      case 'database_backup':
        return { backup_path: backupPath };
      case 'cleanup_old_backups':
        return { keep_count: 30, backup_path: backupPath };
      case 'google_drive_backup':
        return { delete_local_after_upload: false };
      case 'google_drive_cleanup':
        return { keep_count: 10 };
      default:
        return {};
    }
  };
  const [tasks, setTasks] = useState<ScheduledTask[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingTask, setEditingTask] = useState<ScheduledTask | null>(null);
  const [error, setError] = useState<string>('');
  const [schedulerStatus, setSchedulerStatus] = useState<any>(null);
  const [runningTasks, setRunningTasks] = useState<Set<number>>(new Set());
  const [successMessage, setSuccessMessage] = useState<string>('');
  const [showRunningModal, setShowRunningModal] = useState(false);
  const [runningTaskName, setRunningTaskName] = useState<string>('');

  // مراقبة أخطاء المهام تلقائياً
  useTaskErrorMonitoring(tasks);

  // مراقبة صحة المهام
  const {
    criticalTasksCount,
    staleTasksCount,
    clearAlerts
  } = useTaskHealthMonitoring(tasks, {
    enableConsoleWarnings: false // يمكنك تغيير هذا إلى true لإظهار التحذيرات
  });

  // بيانات النموذج
  const [formData, setFormData] = useState<{
    name: string;
    description: string;
    task_type: 'database_backup' | 'cleanup_old_backups' | 'google_drive_backup' | 'google_drive_cleanup' | 'system_maintenance';
    cron_expression: string;
    status: 'active' | 'paused' | 'disabled';
  }>({
    name: '',
    description: '',
    task_type: 'database_backup',
    cron_expression: '0 2 * * *',
    status: 'active'
  });

  useEffect(() => {
    fetchTasks();
    fetchSchedulerStatus();
  }, []);

  const fetchSchedulerStatus = async () => {
    try {
      const response = await api.get('/api/scheduled-tasks/status');
      setSchedulerStatus(response.data);
    } catch (error) {
      console.error('Error fetching scheduler status:', error);
    }
  };

  const fetchTasks = async () => {
    try {
      setLoading(true);
      setError(''); // مسح الأخطاء السابقة
      const response = await api.get('/api/scheduled-tasks');
      setTasks(response.data);
    } catch (error: any) {
      console.error('Error fetching tasks:', error);
      let errorMessage = 'فشل في تحميل المهام المجدولة';

      if (error.response?.status === 401) {
        errorMessage = 'يجب تسجيل الدخول للوصول إلى المهام المجدولة';
      } else if (error.response?.status === 403) {
        errorMessage = 'ليس لديك صلاحية للوصول إلى المهام المجدولة';
      } else if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.message) {
        errorMessage = `خطأ في الاتصال: ${error.message}`;
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTask = async () => {
    try {
      const taskData = {
        ...formData,
        task_params: getTaskParams(formData.task_type)
      };

      await api.post('/api/scheduled-tasks', taskData);
      await fetchTasks();
      setShowCreateModal(false);
      resetForm();
    } catch (error: any) {
      console.error('Error creating task:', error);
      setError(error.response?.data?.detail || 'فشل في إنشاء المهمة');
    }
  };

  const handleUpdateTask = async () => {
    if (!editingTask) return;

    try {
      const taskData = {
        ...formData,
        task_params: getTaskParams(formData.task_type)
      };

      await api.put(`/api/scheduled-tasks/${editingTask.id}`, taskData);
      await fetchTasks();
      setShowEditModal(false);
      setEditingTask(null);
      resetForm();
    } catch (error: any) {
      console.error('Error updating task:', error);
      setError(error.response?.data?.detail || 'فشل في تحديث المهمة');
    }
  };

  const handleDeleteTask = async (taskId: number) => {
    if (!window.confirm('هل أنت متأكد من حذف هذه المهمة؟')) return;

    try {
      await api.delete(`/api/scheduled-tasks/${taskId}`);
      await fetchTasks();
    } catch (error: any) {
      console.error('Error deleting task:', error);
      setError(error.response?.data?.detail || 'فشل في حذف المهمة');
    }
  };

  const handleToggleTask = async (taskId: number) => {
    try {
      // تحديث الحالة المحلية فور<|im_start|> لتحسين تجربة المستخدم
      setTasks(prevTasks =>
        prevTasks.map(task =>
          task.id === taskId
            ? {
                ...task,
                status: task.status === 'active' ? 'paused' : 'active'
              }
            : task
        )
      );

      await api.post(`/api/scheduled-tasks/${taskId}/toggle`);

      // تحديث البيانات من الخادم للتأكد من التزامن
      await fetchTasks();

      // تحديث حالة المجدول
      await fetchSchedulerStatus();
    } catch (error: any) {
      console.error('Error toggling task:', error);
      setError(error.response?.data?.detail || 'فشل في تغيير حالة المهمة');

      // إعادة تحميل البيانات في حالة الخطأ
      await fetchTasks();
    }
  };

  const handleRunNow = async (taskId: number) => {
    try {
      // العثور على اسم المهمة
      const task = tasks.find(t => t.id === taskId);
      const taskName = task?.name || `المهمة ${taskId}`;

      // إضافة المهمة إلى قائمة المهام قيد التشغيل
      setRunningTasks(prev => new Set(prev).add(taskId));
      setRunningTaskName(taskName);
      setShowRunningModal(true);
      setError('');
      setSuccessMessage('');

      const response = await api.post(`/api/scheduled-tasks/${taskId}/run-now`);

      // إخفاء modal التحميل
      setShowRunningModal(false);

      // عرض رسالة النجاح
      setSuccessMessage(response.data.message || `تم تشغيل "${taskName}" بنجاح`);

      // إخفاء رسالة النجاح تلقائياً بعد 5 ثوان
      setTimeout(() => {
        setSuccessMessage('');
      }, 5000);

      // تحديث قائمة المهام
      await fetchTasks();
    } catch (error: any) {
      console.error('Error running task:', error);
      setShowRunningModal(false);
      setError(error.response?.data?.detail || 'فشل في تشغيل المهمة');
    } finally {
      // إزالة المهمة من قائمة المهام قيد التشغيل
      setRunningTasks(prev => {
        const newSet = new Set(prev);
        newSet.delete(taskId);
        return newSet;
      });
    }
  };

  // تسجيل خطأ المهمة في نظام السجلات
  const handleLogTaskError = async (task: ScheduledTask) => {
    try {
      if (!task.last_error) return;

      // تحديد شدة الخطأ بناءً على عدد الأخطاء
      const severity = scheduledTaskErrorService.determineSeverity(
        task.last_error,
        task.failure_count
      );

      // تحديد فئة الخطأ
      const category = scheduledTaskErrorService.categorizeError(task.last_error);

      // تسجيل الخطأ
      await scheduledTaskErrorService.logTaskError(
        task.id,
        task.name,
        task.last_error,
        {
          taskType: task.task_type,
          failureCount: task.failure_count,
          lastRun: task.last_run,
          cronExpression: task.cron_expression
        },
        severity,
        category
      );

      // إعادة تحميل المهام لإزالة الخطأ القديم من العرض
      await fetchTasks();
    } catch (error) {
      console.error('خطأ في تسجيل خطأ المهمة:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      task_type: 'database_backup' as const,
      cron_expression: '0 2 * * *',
      status: 'active' as const
    });
  };

  const openEditModal = (task: ScheduledTask) => {
    setEditingTask(task);
    setFormData({
      name: task.name,
      description: task.description || '',
      task_type: task.task_type,
      cron_expression: task.cron_expression,
      status: task.status
    });
    setShowEditModal(true);
  };

  const getTaskTypeIcon = (type: string) => {
    switch (type) {
      case 'database_backup':
        return <FaDatabase className="text-blue-500" />;
      case 'cleanup_old_backups':
        return <FaBroom className="text-orange-500" />;
      case 'google_drive_backup':
        return <FaCloud className="text-green-500" />;
      case 'google_drive_cleanup':
        return <FaCloud className="text-red-500" />;
      default:
        return <FaClock className="text-gray-500" />;
    }
  };

  const getTaskTypeName = (type: string) => {
    switch (type) {
      case 'database_backup':
        return 'نسخ احتياطي';
      case 'cleanup_old_backups':
        return 'تنظيف النسخ القديمة';
      case 'google_drive_backup':
        return 'نسخ احتياطي إلى Google Drive';
      case 'google_drive_cleanup':
        return 'تنظيف Google Drive';
      case 'system_maintenance':
        return 'صيانة النظام';
      default:
        return type;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400';
      case 'paused':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'disabled':
        return 'text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'نشط';
      case 'paused':
        return 'متوقف';
      case 'disabled':
        return 'معطل';
      default:
        return status;
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'لم يتم التشغيل بعد';
    return formatDateTime(dateString, 'datetime');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <FaSpinner className="animate-spin text-2xl text-primary-600" />
        <span className="mr-2">جاري تحميل المهام...</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <ActionBar
        title="المهام المجدولة"
        subtitle={
          <div className="space-y-1">
            <div className="text-sm">
              مسار النسخ الاحتياطية: <code className="bg-gray-100 dark:bg-gray-700 px-1 rounded text-xs">{backupPath}</code>
            </div>
            {schedulerStatus && (
              <div className="text-xs flex items-center flex-wrap gap-2">
                <div className="flex items-center">
                  <span className="text-gray-600 dark:text-gray-400">حالة المجدول:</span>
                  <span className={`mr-1 flex items-center ${schedulerStatus.scheduler_running ? 'text-green-600' : 'text-red-600'}`}>
                    <FaCircle className="text-xs ml-1" />
                    {schedulerStatus.scheduler_running ? 'يعمل' : 'متوقف'}
                  </span>
                  {schedulerStatus.job_count > 0 && (
                    <span className="mr-2 text-gray-600 dark:text-gray-400">
                      ({schedulerStatus.job_count} مهمة نشطة)
                    </span>
                  )}
                </div>

                {/* التنبيهات */}
                {(criticalTasksCount > 0 || staleTasksCount > 0) && (
                  <div className="flex items-center gap-2">
                    {criticalTasksCount > 0 && (
                      <span className="flex items-center text-red-600 bg-red-50 dark:bg-red-900/20 px-2 py-1 rounded-md">
                        <FaExclamationTriangle className="text-xs ml-1" />
                        {criticalTasksCount} مهام حرجة
                      </span>
                    )}
                    {staleTasksCount > 0 && (
                      <span className="flex items-center text-amber-600 bg-amber-50 dark:bg-amber-900/20 px-2 py-1 rounded-md">
                        <FaPause className="text-xs ml-1" />
                        {staleTasksCount} مهام متوقفة
                      </span>
                    )}
                    <button
                      onClick={clearAlerts}
                      className="flex items-center text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                      title="تنظيف التنبيهات"
                    >
                      <FaBroom className="text-xs" />
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        }
        primaryAction={
          <button
            onClick={() => setShowCreateModal(true)}
            className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-all duration-200 flex items-center text-sm font-medium shadow-sm hover:shadow-md h-9"
          >
            <FaPlus className="ml-2" />
            إضافة مهمة
          </button>
        }
        secondaryActions={
          <div className="text-sm text-gray-500">
            {/* تم إزالة العروض التجريبية مؤقتاً */}
          </div>
        }
        layout="split"
        className="mb-4"
      />

      {/* Success Message */}
      {successMessage && (
        <div className="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-300 px-4 py-3 rounded-md">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <FaCheck className="ml-2" />
              <span>{successMessage}</span>
            </div>
            <button
              onClick={() => setSuccessMessage('')}
              className="text-green-500 hover:text-green-700 text-lg"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-4 py-3 rounded-md">
          <div className="flex items-center justify-between">
            <span>{error}</span>
            <div className="flex items-center space-x-2 space-x-reverse">
              <button
                onClick={fetchTasks}
                className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-200 text-sm underline"
              >
                إعادة المحاولة
              </button>
              <button
                onClick={() => setError('')}
                className="text-red-500 hover:text-red-700 text-lg"
              >
                ×
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Scheduler Status Warning */}
      {schedulerStatus && !schedulerStatus.scheduler_available && (
        <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 text-yellow-700 dark:text-yellow-300 px-4 py-3 rounded-md">
          <div className="flex items-center">
            <FaExclamationTriangle className="ml-2" />
            <span>تحذير: خدمة جدولة المهام غير متاحة. قد لا تعمل المهام المجدولة بشكل صحيح.</span>
          </div>
        </div>
      )}

      {/* Task Error Statistics */}
      <TaskErrorStats className="mb-4" />

      {/* Tasks List */}
      <div className="space-y-3">
        {tasks.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <FaCalendarAlt className="mx-auto text-4xl mb-4 opacity-50" />
            <p>لا توجد مهام مجدولة</p>
            <p className="text-sm">انقر على "إضافة مهمة" لإنشاء مهمة جديدة</p>
          </div>
        ) : (
          tasks.map((task) => (
            <div
              key={task.id}
              className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center mb-2">
                    {getTaskTypeIcon(task.task_type)}
                    <h4 className="font-medium text-gray-900 dark:text-gray-100 mr-2">
                      {task.name}
                    </h4>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                      {getStatusText(task.status)}
                    </span>
                    {task.is_system_task && (
                      <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-xs font-medium mr-2">
                        مهمة النظام
                      </span>
                    )}
                  </div>

                  {task.description && (
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      {task.description}
                    </p>
                  )}

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 dark:text-gray-400">
                    <div>
                      <span className="font-medium">النوع:</span> {getTaskTypeName(task.task_type)}
                    </div>
                    <div>
                      <span className="font-medium">الجدولة:</span>
                      <code className="bg-gray-100 dark:bg-gray-700 px-1 rounded text-xs mr-1">
                        {task.cron_expression}
                      </code>
                    </div>
                    <div>
                      <span className="font-medium">التشغيل التالي:</span> {formatDate(task.next_run)}
                    </div>
                    <div>
                      <span className="font-medium">آخر تشغيل:</span> {formatDate(task.last_run)}
                    </div>
                    <div>
                      <span className="font-medium">عدد التشغيلات:</span> {task.run_count}
                    </div>
                    <div>
                      <span className="font-medium">الأخطاء:</span>
                      <span className={task.failure_count > 0 ? 'text-red-600 dark:text-red-400' : ''}>
                        {task.failure_count}
                      </span>
                    </div>
                  </div>

                  {/* عرض الأخطاء المحسن */}
                  <TaskErrorDisplay
                    taskId={task.id}
                    taskName={task.name}
                    className="mt-3"
                  />

                  {/* عرض آخر خطأ إذا لم توجد أخطاء في النظام الجديد */}
                  {task.last_error && (
                    <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded text-sm text-red-700 dark:text-red-300">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <FaExclamationTriangle className="inline ml-1" />
                          آخر خطأ: {task.last_error}
                        </div>
                        <button
                          onClick={() => handleLogTaskError(task)}
                          className="text-xs text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-200 underline"
                          title="تسجيل في نظام الأخطاء"
                        >
                          تسجيل
                        </button>
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex items-center space-x-2 space-x-reverse">
                  <button
                    onClick={() => handleRunNow(task.id)}
                    disabled={runningTasks.has(task.id)}
                    className={`p-2 rounded-md transition-colors ${
                      runningTasks.has(task.id)
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/30'
                    }`}
                    title={runningTasks.has(task.id) ? 'جاري التشغيل...' : 'تشغيل الآن'}
                  >
                    {runningTasks.has(task.id) ? (
                      <FaSpinner className="text-sm animate-spin" />
                    ) : (
                      <FaPlay className="text-sm" />
                    )}
                  </button>

                  <button
                    onClick={() => handleToggleTask(task.id)}
                    className={`p-2 rounded-md transition-colors ${
                      task.status === 'active'
                        ? 'text-yellow-600 hover:bg-yellow-50 dark:hover:bg-yellow-900/30'
                        : 'text-green-600 hover:bg-green-50 dark:hover:bg-green-900/30'
                    }`}
                    title={task.status === 'active' ? 'إيقاف' : 'تفعيل'}
                  >
                    {task.status === 'active' ? <FaPause className="text-sm" /> : <FaPlay className="text-sm" />}
                  </button>

                  <button
                    onClick={() => openEditModal(task)}
                    className="p-2 text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-md transition-colors"
                    title="تعديل"
                  >
                    <FaEdit className="text-sm" />
                  </button>

                  {!task.is_system_task && (
                    <button
                      onClick={() => handleDeleteTask(task.id)}
                      className="p-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/30 rounded-md transition-colors"
                      title="حذف"
                    >
                      <FaTrash className="text-sm" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Create Task Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => {
          setShowCreateModal(false);
          resetForm();
        }}
        title="إضافة مهمة مجدولة جديدة"
        size="lg"
      >
        <div className="space-y-6">
          {/* اسم المهمة */}
          <TextInput
            name="taskName"
            label="اسم المهمة"
            value={formData.name}
            onChange={(value) => setFormData({ ...formData, name: value })}
            placeholder="أدخل اسم المهمة..."
            required={true}
            icon={<FaCalendarAlt className="text-primary-500" />}
          />

          {/* الوصف */}
          <TextArea
            name="taskDescription"
            label="الوصف (اختياري)"
            value={formData.description}
            onChange={(value) => setFormData({ ...formData, description: value })}
            placeholder="وصف المهمة..."
            rows={3}
          />

          {/* نوع المهمة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              نوع المهمة
            </label>
            <TaskTypeSelector
              value={formData.task_type}
              onChange={(value) => setFormData({ ...formData, task_type: value as any })}
              backupPath={backupPath}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              جدولة التشغيل
            </label>
            <CronBuilder
              value={formData.cron_expression}
              onChange={(cron) => setFormData({ ...formData, cron_expression: cron })}
            />
          </div>

          <div className="flex justify-end space-x-3 space-x-reverse pt-4">
            <button
              onClick={() => {
                setShowCreateModal(false);
                resetForm();
              }}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-md hover:bg-gray-50 dark:hover:bg-gray-500 transition-colors"
            >
              إلغاء
            </button>
            <button
              onClick={handleCreateTask}
              disabled={!formData.name.trim()}
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              إنشاء المهمة
            </button>
          </div>
        </div>
      </Modal>

      {/* Edit Task Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={() => {
          setShowEditModal(false);
          setEditingTask(null);
          resetForm();
        }}
        title="تعديل المهمة المجدولة"
        size="lg"
      >
        <div className="space-y-6">
          {/* اسم المهمة */}
          <TextInput
            name="editTaskName"
            label="اسم المهمة"
            value={formData.name}
            onChange={(value) => setFormData({ ...formData, name: value })}
            placeholder="أدخل اسم المهمة..."
            required={true}
            disabled={editingTask?.is_system_task}
            icon={<FaCalendarAlt className="text-primary-500" />}
          />

          {/* الوصف */}
          <TextArea
            name="editTaskDescription"
            label="الوصف (اختياري)"
            value={formData.description}
            onChange={(value) => setFormData({ ...formData, description: value })}
            placeholder="وصف المهمة..."
            rows={3}
            disabled={editingTask?.is_system_task}
          />

          {/* نوع المهمة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              نوع المهمة
            </label>
            <TaskTypeSelector
              value={formData.task_type}
              onChange={(value) => setFormData({ ...formData, task_type: value as any })}
              backupPath={backupPath}
              disabled={editingTask?.is_system_task}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              جدولة التشغيل
            </label>
            <CronBuilder
              value={formData.cron_expression}
              onChange={(cron) => setFormData({ ...formData, cron_expression: cron })}
            />
          </div>

          {/* حالة المهمة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              حالة المهمة
            </label>
            <StatusSelector
              value={formData.status}
              onChange={(value) => setFormData({ ...formData, status: value as any })}
              layout="horizontal"
            />
          </div>

          <div className="flex justify-end space-x-3 space-x-reverse pt-4">
            <button
              onClick={() => {
                setShowEditModal(false);
                setEditingTask(null);
                resetForm();
              }}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-md hover:bg-gray-50 dark:hover:bg-gray-500 transition-colors"
            >
              إلغاء
            </button>
            <button
              onClick={handleUpdateTask}
              disabled={!formData.name.trim()}
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              حفظ التغييرات
            </button>
          </div>
        </div>
      </Modal>

      {/* Running Task Modal */}
      <Modal
        isOpen={showRunningModal}
        onClose={() => {}} // منع الإغلاق أثناء التشغيل
        title="تشغيل المهمة"
        size="sm"
      >
        <div className="text-center py-6">
          <div className="flex justify-center mb-4">
            <FaSpinner className="animate-spin text-4xl text-primary-600" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            جاري تشغيل المهمة...
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            {runningTaskName}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-500">
            يرجى الانتظار حتى اكتمال التشغيل
          </p>
        </div>
      </Modal>
    </div>
  );
};

export default ScheduledTasksManager;
