import React, { useState, useEffect, useRef } from 'react';
import { 
  FaCalendarAlt, 
  FaChevronDown, 
  FaCheck, 
  Fa<PERSON><PERSON>ner,
  FaCalendarDay,
  FaCalendarWeek,
  FaCalendar,
  FaChartLine,
  FaExclamationTriangle
} from 'react-icons/fa';
import { dynamicPeriodsService, PeriodOption } from '../../services/dynamicPeriodsService';

interface DynamicPeriodSelectorProps {
  value: number;
  onChange: (periodDays: number) => void;
  className?: string;
  disabled?: boolean;
  label?: string;
}

const DynamicPeriodSelector: React.FC<DynamicPeriodSelectorProps> = ({
  value,
  onChange,
  className = '',
  disabled = false,
  label = 'فترة التحليل'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [options, setOptions] = useState<PeriodOption[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);

  const selectRef = useRef<HTMLDivElement>(null);

  // جلب الفترات المتاحة عند تحميل المكون
  useEffect(() => {
    loadAvailablePeriods();
  }, []);

  // إغلاق القائمة عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const loadAvailablePeriods = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('بدء جلب الفترات المتاحة...');
      const periodOptions = await dynamicPeriodsService.getPeriodOptions();
      console.log('تم جلب خيارات الفترات:', periodOptions);

      setOptions(periodOptions);

      // إذا لم تكن هناك فترات متاحة، عرض رسالة
      if (periodOptions.length === 0) {
        console.log('لا توجد فترات متاحة للتحليل');
        setError('لا توجد فترات متاحة للتحليل');
      } else {
        console.log(`تم تحميل ${periodOptions.length} فترة متاحة`);
      }

    } catch (err) {
      console.error('خطأ في جلب الفترات المتاحة:', err);
      setError('خطأ في جلب الفترات المتاحة');
    } finally {
      setLoading(false);
    }
  };

  const getIconForPeriod = (periodDays: number) => {
    switch (periodDays) {
      case 7:
        return <FaCalendarDay className="w-4 h-4 text-primary-600 dark:text-primary-400" />;
      case 30:
        return <FaCalendarWeek className="w-4 h-4 text-success-600 dark:text-success-400" />;
      case 60:
        return <FaCalendarAlt className="w-4 h-4 text-warning-600 dark:text-warning-400" />;
      case 90:
        return <FaCalendar className="w-4 h-4 text-secondary-600 dark:text-secondary-400" />;
      case 180:
        return <FaChartLine className="w-4 h-4 text-purple-600 dark:text-purple-400" />;
      case 365:
        return <FaCalendar className="w-4 h-4 text-indigo-600 dark:text-indigo-400" />;
      default:
        return <FaCalendarAlt className="w-4 h-4 text-gray-600 dark:text-gray-400" />;
    }
  };

  const selectedOption = options.find(option => option.value === value.toString());

  const handleToggle = () => {
    if (!disabled && !loading) {
      setIsOpen(!isOpen);
      setHighlightedIndex(-1);
    }
  };

  const handleSelect = (optionValue: string) => {
    const periodDays = parseInt(optionValue);
    onChange(periodDays);
    setIsOpen(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        setIsOpen(true);
      }
      return;
    }

    switch (e.key) {
      case 'Escape':
        setIsOpen(false);
        break;
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedIndex(prev =>
          prev < options.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex(prev =>
          prev > 0 ? prev - 1 : options.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedIndex >= 0 && options[highlightedIndex]) {
          handleSelect(options[highlightedIndex].value);
        }
        break;
    }
  };

  const handleRefresh = async () => {
    await loadAvailablePeriods();
  };

  return (
    <div className={`relative ${className}`} ref={selectRef}>
      {/* Label */}
      {label && label.trim() !== '' && (
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {label}
        </label>
      )}

      {/* Select Container */}
      <div className="relative">
        {/* Icon */}
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none z-10">
          <FaCalendarAlt className="text-primary-600 dark:text-primary-400" />
        </div>

        {/* Select Button */}
        <button
          type="button"
          onClick={handleToggle}
          onKeyDown={handleKeyDown}
          disabled={disabled || loading}
          className={`
            w-full rounded-xl border-2 h-12 px-4 pr-12 pl-10 text-right transition-all duration-200 ease-in-out flex items-center
            ${disabled || loading
              ? 'bg-gray-50 dark:bg-gray-800 text-gray-500 dark:text-gray-400 cursor-not-allowed border-gray-200 dark:border-gray-700'
              : 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 cursor-pointer'
            }
            ${isOpen
              ? 'border-primary-500 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20'
              : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
            }
            focus:outline-none
          `}
        >
          {loading ? (
            <span className="flex items-center justify-center">
              <FaSpinner className="animate-spin ml-2" />
              جاري التحميل...
            </span>
          ) : error ? (
            <span className="text-red-500 flex items-center">
              <FaExclamationTriangle className="ml-2" />
              {error}
            </span>
          ) : selectedOption ? (
            <div className="flex items-center justify-between w-full">
              <span className="font-medium">{selectedOption.label}</span>
              <span className="text-xs text-gray-500 dark:text-gray-400 mr-2">
                {selectedOption.details}
              </span>
            </div>
          ) : (
            <span className="text-gray-400 dark:text-gray-500">اختر فترة التحليل...</span>
          )}
        </button>

        {/* Dropdown Arrow */}
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <FaChevronDown className={`h-4 w-4 text-gray-400 dark:text-gray-500 transition-transform duration-200 ${
            isOpen ? 'transform rotate-180' : ''
          }`} />
        </div>
      </div>

      {/* Dropdown */}
      {isOpen && !loading && !error && (
        <div className="absolute z-50 mt-1 w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          {/* Header */}
          <div className="p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-750">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                الفترات المتاحة للتحليل
              </span>
              <button
                onClick={handleRefresh}
                className="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300"
              >
                تحديث
              </button>
            </div>
          </div>

          {/* Options */}
          <div className="max-h-48 overflow-auto custom-scrollbar">
            {options.length === 0 ? (
              <div className="px-3 py-4 text-center text-gray-500 dark:text-gray-400">
                <FaExclamationTriangle className="mx-auto mb-2 text-2xl" />
                <p>لا توجد فترات متاحة للتحليل</p>
                <p className="text-xs mt-1">تأكد من وجود مبيعات في النظام</p>
              </div>
            ) : (
              options.map((option, index) => (
                <button
                  key={option.value}
                  type="button"
                  onClick={() => !option.disabled && handleSelect(option.value)}
                  disabled={option.disabled}
                  className={`w-full px-4 py-2.5 text-right transition-colors ${
                    option.disabled
                      ? 'text-gray-400 dark:text-gray-600 cursor-not-allowed bg-gray-50 dark:bg-gray-800'
                      : option.salesCount === 0 && option.productsCount === 0
                        ? 'text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 opacity-75'
                        : index === highlightedIndex
                          ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-300'
                          : option.value === value.toString()
                            ? 'bg-primary-100 dark:bg-primary-900/50 text-primary-700 dark:text-primary-200'
                            : 'text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      {getIconForPeriod(parseInt(option.value))}
                      <span className="font-medium mr-3">{option.label}</span>
                    </div>

                    <div className="flex items-center gap-4">
                      <span className={`text-xs whitespace-nowrap ${
                        option.salesCount === 0 && option.productsCount === 0
                          ? 'text-gray-400 dark:text-gray-500'
                          : 'text-gray-500 dark:text-gray-400'
                      }`}>
                        {option.details}
                      </span>

                      {option.totalAmount > 0 && (
                        <span className="text-xs text-green-600 dark:text-green-400 font-medium whitespace-nowrap">
                          {dynamicPeriodsService.formatAmount(option.totalAmount)}
                        </span>
                      )}

                      {option.salesCount === 0 && option.productsCount === 0 && (
                        <span className="text-xs text-orange-500 dark:text-orange-400 whitespace-nowrap">
                          لا توجد بيانات
                        </span>
                      )}

                      {option.value === value.toString() && (
                        <FaCheck className="h-4 w-4 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                      )}
                    </div>
                  </div>
                </button>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default DynamicPeriodSelector;
