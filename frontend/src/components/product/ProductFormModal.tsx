/**
 * نموذج إضافة/تعديل المنتج
 * يركز على البيانات الأساسية للمنتج (منفصل عن إدارة المخزون)
 * يتبع النمط الموحد للتصميم
 */

import React, { useState, useEffect } from "react";
import { FaSave, FaSpinner } from "react-icons/fa";
import { FiPackage, FiDollarSign, FiImage, FiSettings } from "react-icons/fi";

// Import components
import Modal from "../Modal";
import ToggleSwitch from "../ToggleSwitch";

// Import sections
import {
  BasicInfoSection,
  PricingInventorySection,
  ImageSection,
  AdditionalFieldsSection,
} from "./ProductForm";

// Import stores
import useProductStore from "../../stores/productStore";

interface ProductFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  product?: any;
  title?: string;
}

interface ProductFormData {
  // Basic Info
  name: string;
  slug: string;
  category_id: number | null;
  subcategory_id: number | null;
  brand_id: number | null;
  unit_id: number | null;
  description: string;

  // Pricing & Tax (المخزون منفصل في نظام المستودعات)
  product_type: "simple" | "variable";
  price: number;
  cost_price: number;
  tax_type: "inclusive" | "exclusive";
  tax_id: number | null;
  discount_type: "percentage" | "fixed";
  discount_value: number;

  // Images
  images: File[];
  image_urls: string[];

  // Additional Fields
  warranty_type: string;
  manufacturer: string;
  manufactured_date: string;
  expiry_date: string;

  // Status
  is_active: boolean;
}

const ProductFormModal: React.FC<ProductFormModalProps> = ({
  isOpen,
  onClose,
  product,
  title,
}) => {
  // Store hooks
  const {
    createProductAdvanced,
    updateProductAdvanced,
    generateSlug,
  } = useProductStore();

  // Form state
  const [formData, setFormData] = useState<ProductFormData>({
    name: "",
    slug: "",
    category_id: null,
    subcategory_id: null,
    brand_id: null,
    unit_id: null,
    description: "",
    product_type: "simple",
    price: 0,
    cost_price: 0,
    tax_type: "inclusive",
    tax_id: null,
    discount_type: "percentage",
    discount_value: 0,
    images: [],
    image_urls: [],
    warranty_type: "",
    manufacturer: "",
    manufactured_date: "",
    expiry_date: "",
    is_active: true,
  });

  // UI state
  const [activeSection, setActiveSection] = useState("basic");
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form data when product changes
  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name || "",
        slug: product.slug || "",
        category_id: product.category_id || null,
        subcategory_id: product.subcategory_id || null,
        brand_id: product.brand_id || null,
        unit_id: product.unit_id || null,
        description: product.description || "",
        product_type: product.product_type || "simple",
        price: product.price || 0,
        cost_price: product.cost_price || 0,
        tax_type: product.tax_type || "inclusive",
        tax_id: product.tax_id || null,
        discount_type: product.discount_type || "percentage",
        discount_value: product.discount_value || 0,
        images: [],
        image_urls: product.image_urls || [],
        warranty_type: product.warranty_type || "",
        manufacturer: product.manufacturer || "",
        manufactured_date: product.manufactured_date || "",
        expiry_date: product.expiry_date || "",
        is_active: product.is_active !== undefined ? product.is_active : true,
      });
    } else {
      // Reset form for new product
      setFormData({
        name: "",
        slug: "",
        category_id: null,
        subcategory_id: null,
        brand_id: null,
        unit_id: null,
        description: "",
        product_type: "simple",
        price: 0,
        cost_price: 0,
        tax_type: "inclusive",
        tax_id: null,
        discount_type: "percentage",
        discount_value: 0,
        images: [],
        image_urls: [],
        warranty_type: "",
        manufacturer: "",
        manufactured_date: "",
        expiry_date: "",
        is_active: true,
      });
    }
  }, [product, isOpen]);



  // Generate slug from name using the service
  const generateSlugFromName = async (name: string) => {
    try {
      const result = await generateSlug(name, 100, false);
      return result.slug;
    } catch (error) {
      console.error("خطأ في توليد الرابط:", error);
      // Fallback to simple generation
      return name
        .toLowerCase()
        .replace(/[^a-z0-9\u0600-\u06FF\s-]/g, "")
        .replace(/\s+/g, "-")
        .replace(/-+/g, "-")
        .trim();
    }
  };

  // Update form data
  const updateFormData = (field: string, value: any) => {
    setFormData((prev) => {
      const newData = { ...prev, [field]: value };

      // Auto-generate slug when name changes
      if (field === "name" && value) {
        generateSlugFromName(value).then((slug) => {
          setFormData((current) => ({ ...current, slug }));
        });
      }

      return newData;
    });

    // Clear error for this field
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required fields
    if (!formData.name.trim()) {
      newErrors.name = "اسم المنتج مطلوب";
    }
    if (formData.price <= 0) {
      newErrors.price = "السعر يجب أن يكون أكبر من صفر";
    }
    if (formData.cost_price < 0) {
      newErrors.cost_price = "سعر التكلفة لا يمكن أن يكون سالب";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle submit
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      const productData = {
        ...formData,
        // Convert string numbers to actual numbers
        price: Number(formData.price),
        cost_price: Number(formData.cost_price),
        discount_value: Number(formData.discount_value),
      };

      if (product) {
        await updateProductAdvanced(product.id, productData);
      } else {
        await createProductAdvanced(productData);
      }

      onClose();
    } catch (error) {
      console.error("خطأ في حفظ المنتج:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Sections configuration
  const sections = [
    {
      id: "basic",
      name: "البيانات الأساسية",
      icon: FiPackage,
      component: BasicInfoSection,
    },
    {
      id: "pricing",
      name: "التسعير والمخزون",
      icon: FiDollarSign,
      component: PricingInventorySection,
    },
    {
      id: "images",
      name: "الصور",
      icon: FiImage,
      component: ImageSection,
    },
    {
      id: "additional",
      name: "حقول إضافية",
      icon: FiSettings,
      component: AdditionalFieldsSection,
    },
  ];

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title || (product ? "تعديل المنتج" : "إضافة منتج جديد")}
      size="xl"
    >
      <div className="flex flex-col h-full max-h-[90vh]">
        {/* Header with sections tabs */}
        <div className="flex-shrink-0 border-b border-gray-200 dark:border-gray-700">
          <div className="flex overflow-x-auto">
            {sections.map((section) => {
              const IconComponent = section.icon;
              return (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`flex items-center gap-2 px-4 py-3 text-sm font-medium border-b-2 transition-all duration-200 whitespace-nowrap ${
                    activeSection === section.id
                      ? "border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20"
                      : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"
                  }`}
                >
                  <IconComponent className="w-4 h-4" />
                  {section.name}
                </button>
              );
            })}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {sections.map((section) => {
            const SectionComponent = section.component;
            return (
              <div
                key={section.id}
                className={activeSection === section.id ? "block" : "hidden"}
              >
                <SectionComponent
                  formData={formData}
                  updateFormData={updateFormData}
                  errors={errors}
                />
              </div>
            );
          })}
        </div>

        {/* Footer */}
        <div className="flex-shrink-0 border-t border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-10 flex items-center">
                <ToggleSwitch
                  id="is_active"
                  checked={formData.is_active}
                  onChange={(checked) => updateFormData("is_active", checked)}
                  label="منتج نشط"
                  className="w-full"
                />
              </div>
            </div>

            <div className="flex items-center gap-3">
              <button
                onClick={onClose}
                disabled={isSubmitting}
                className="px-6 py-3 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-xl transition-all duration-200 disabled:opacity-50"
              >
                إلغاء
              </button>
              <button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-xl flex items-center gap-2 transition-all duration-200 disabled:opacity-50 shadow-lg hover:shadow-xl"
              >
                {isSubmitting ? (
                  <FaSpinner className="w-4 h-4 animate-spin" />
                ) : (
                  <FaSave className="w-4 h-4" />
                )}
                {product ? "تحديث" : "حفظ"}
              </button>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default ProductFormModal;
