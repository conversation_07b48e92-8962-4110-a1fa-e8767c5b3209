import React from 'react';
import { FiDollarSign, FiPercent, FiTrendingUp } from 'react-icons/fi';

interface ProductPricingCardProps {
  price: number;
  costPrice: number;
  taxAmount?: number;
  discountAmount?: number;
  showTaxCalculation?: boolean;
  currency?: string;
}

const ProductPricingCard: React.FC<ProductPricingCardProps> = ({
  price,
  costPrice,
  taxAmount = 0,
  discountAmount = 0,
  showTaxCalculation = false,
  currency = 'د.ل'
}) => {
  // Calculate profit margin
  const profitMargin = costPrice > 0 
    ? (((price - costPrice) / costPrice) * 100).toFixed(1)
    : '0.0';

  // Calculate final price
  const netPrice = price - discountAmount;
  const finalPrice = showTaxCalculation ? netPrice + taxAmount : netPrice;

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {/* Profit Margin */}
      <div className="bg-green-50 dark:bg-green-900/20 rounded-xl p-4 border border-green-200 dark:border-green-800">
        <div className="flex items-center gap-2 mb-2">
          <FiPercent className="w-4 h-4 text-green-600 dark:text-green-400" />
          <span className="text-sm font-medium text-green-700 dark:text-green-300">
            هامش الربح
          </span>
        </div>
        <p className="text-2xl font-bold text-green-600 dark:text-green-400">
          {profitMargin}%
        </p>
      </div>

      {/* Net Price (after discount) */}
      {discountAmount > 0 && (
        <div className="bg-orange-50 dark:bg-orange-900/20 rounded-xl p-4 border border-orange-200 dark:border-orange-800">
          <div className="flex items-center gap-2 mb-2">
            <FiDollarSign className="w-4 h-4 text-orange-600 dark:text-orange-400" />
            <span className="text-sm font-medium text-orange-700 dark:text-orange-300">
              السعر بعد الخصم
            </span>
          </div>
          <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">
            {netPrice.toFixed(2)} {currency}
          </p>
        </div>
      )}

      {/* Final Price */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-4 border border-blue-200 dark:border-blue-800">
        <div className="flex items-center gap-2 mb-2">
          <FiTrendingUp className="w-4 h-4 text-blue-600 dark:text-blue-400" />
          <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
            {showTaxCalculation && taxAmount > 0 ? 'السعر النهائي مع الضريبة' : 'السعر النهائي'}
          </span>
        </div>
        <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
          {finalPrice.toFixed(2)} {currency}
        </p>
      </div>
    </div>
  );
};

export default ProductPricingCard;