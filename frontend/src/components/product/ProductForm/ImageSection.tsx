/**
 * قسم الصور للمنتج
 * يحتوي على رفع وإدارة صور المنتج مع دعم السحب والإفلات
 */

import React, { useState, useRef, useCallback } from 'react';
import { FaUpload, FaTrash, FaEye, FaImage, FaSpinner } from 'react-icons/fa';
import { FiImage, FiUpload, FiX } from 'react-icons/fi';

// Import image management service
import { imageManagementService } from '../../../services/imageManagementService';

interface ImageSectionProps {
  formData: any;
  updateFormData: (field: string, value: any) => void;
  errors: Record<string, string>;
}

interface ImageFile {
  id: string;
  file?: File;
  url: string;
  name: string;
  size?: number;
  isUploading?: boolean;
  uploadProgress?: number;
}

const ImageSection: React.FC<ImageSectionProps> = ({
  formData,
  updateFormData,
  errors
}) => {
  // Local state
  const [images, setImages] = useState<ImageFile[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | null>(null);

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Initialize images from form data
  React.useEffect(() => {
    if (formData.image_urls && formData.image_urls.length > 0) {
      const existingImages: ImageFile[] = formData.image_urls.map((url: string, index: number) => ({
        id: `existing-${index}`,
        url,
        name: `صورة ${index + 1}`,
        isUploading: false
      }));
      setImages(existingImages);
    }
  }, [formData.image_urls]);

  // Handle file selection
  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files) return;

    const newImages: ImageFile[] = [];
    
    Array.from(files).forEach((file) => {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        console.error('نوع الملف غير مدعوم:', file.name);
        return;
      }

      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        console.error('حجم الملف كبير جداً:', file.name);
        return;
      }

      const imageId = `new-${Date.now()}-${Math.random()}`;
      const imageUrl = URL.createObjectURL(file);

      newImages.push({
        id: imageId,
        file,
        url: imageUrl,
        name: file.name,
        size: file.size,
        isUploading: false
      });
    });

    setImages(prev => [...prev, ...newImages]);
    updateFormData('images', [...formData.images, ...newImages.map(img => img.file).filter(Boolean)]);
  }, [formData.images, updateFormData]);

  // Handle drag events
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  // Handle drop
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files);
    }
  }, [handleFileSelect]);

  // Handle file input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e.target.files);
  };

  // Remove image
  const removeImage = (imageId: string) => {
    setImages(prev => {
      const updatedImages = prev.filter(img => img.id !== imageId);
      
      // Update form data
      const newFiles = updatedImages
        .map(img => img.file)
        .filter(Boolean) as File[];
      
      const newUrls = updatedImages
        .filter(img => !img.file)
        .map(img => img.url);
      
      updateFormData('images', newFiles);
      updateFormData('image_urls', newUrls);
      
      return updatedImages;
    });
  };

  // Upload image to server
  const uploadImage = async (imageId: string) => {
    const image = images.find(img => img.id === imageId);
    if (!image || !image.file) return;

    setImages(prev => prev.map(img => 
      img.id === imageId 
        ? { ...img, isUploading: true, uploadProgress: 0 }
        : img
    ));

    try {
      const result = await imageManagementService.uploadImage(
        image.file,
        'products',
        true
      );

      if (result.success) {
        setImages(prev => prev.map(img => 
          img.id === imageId 
            ? { 
                ...img, 
                url: `${window.location.origin}/static/${result.file_path}`,
                isUploading: false,
                uploadProgress: 100
              }
            : img
        ));
      } else {
        console.error('فشل في رفع الصورة:', result.error);
        setImages(prev => prev.map(img => 
          img.id === imageId 
            ? { ...img, isUploading: false, uploadProgress: 0 }
            : img
        ));
      }
    } catch (error) {
      console.error('خطأ في رفع الصورة:', error);
      setImages(prev => prev.map(img => 
        img.id === imageId 
          ? { ...img, isUploading: false, uploadProgress: 0 }
          : img
      ));
    }
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">

      {/* Upload Area */}
      <div
        className={`relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 ${
          dragActive
            ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={handleInputChange}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
        />
        
        <div className="space-y-4">
          <div className="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
            <FiUpload className="w-8 h-8 text-gray-400" />
          </div>
          
          <div>
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              اسحب الصور هنا أو انقر للاختيار
            </h4>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              يدعم: JPG, PNG, GIF, WebP (حد أقصى 10 ميجابايت لكل صورة)
            </p>
          </div>
          
          <button
            type="button"
            onClick={() => fileInputRef.current?.click()}
            className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl min-w-[140px] gap-2 mx-auto"
          >
            <FaUpload className="w-4 h-4" />
            اختيار الصور
          </button>
        </div>
      </div>

      {/* Images Grid */}
      {images.length > 0 && (
        <div className="space-y-4">
          <h4 className="text-md font-medium text-gray-900 dark:text-white">
            الصور المرفوعة ({images.length})
          </h4>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {images.map((image) => (
              <div
                key={image.id}
                className="relative group bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden shadow-sm hover:shadow-md transition-all duration-200"
              >
                {/* Image */}
                <div className="aspect-square relative">
                  <img
                    src={image.url}
                    alt={image.name}
                    className="w-full h-full object-cover"
                  />
                  
                  {/* Upload Progress */}
                  {image.isUploading && (
                    <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                      <div className="text-white text-center">
                        <FaSpinner className="w-6 h-6 animate-spin mx-auto mb-2" />
                        <p className="text-sm">جاري الرفع...</p>
                        {image.uploadProgress && (
                          <div className="w-16 bg-gray-200 rounded-full h-1 mt-2">
                            <div
                              className="bg-primary-600 h-1 rounded-full transition-all duration-300"
                              style={{ width: `${image.uploadProgress}%` }}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                  
                  {/* Actions Overlay */}
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center gap-2">
                    <button
                      type="button"
                      onClick={() => setPreviewImage(image.url)}
                      className="p-2 bg-white/20 hover:bg-white/30 rounded-lg text-white transition-all duration-200"
                      title="معاينة"
                    >
                      <FaEye className="w-4 h-4" />
                    </button>
                    
                    {image.file && !image.isUploading && (
                      <button
                        type="button"
                        onClick={() => uploadImage(image.id)}
                        className="p-2 bg-primary-600/80 hover:bg-primary-600 rounded-lg text-white transition-all duration-200"
                        title="رفع إلى الخادم"
                      >
                        <FaUpload className="w-4 h-4" />
                      </button>
                    )}
                    
                    <button
                      type="button"
                      onClick={() => removeImage(image.id)}
                      className="p-2 bg-red-600/80 hover:bg-red-600 rounded-lg text-white transition-all duration-200"
                      title="حذف"
                    >
                      <FaTrash className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                
                {/* Image Info */}
                <div className="p-3">
                  <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {image.name}
                  </p>
                  {image.size && (
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {formatFileSize(image.size)}
                    </p>
                  )}
                  {image.file && !image.isUploading && (
                    <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                      لم يتم رفعها بعد
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Preview Modal */}
      {previewImage && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-4xl max-h-full">
            <button
              onClick={() => setPreviewImage(null)}
              className="absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors"
            >
              <FiX className="w-8 h-8" />
            </button>
            <img
              src={previewImage}
              alt="معاينة الصورة"
              className="max-w-full max-h-full object-contain rounded-lg"
            />
          </div>
        </div>
      )}

      {/* Error Display */}
      {errors.images && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4">
          <p className="text-red-600 dark:text-red-400 text-sm">
            {errors.images}
          </p>
        </div>
      )}

      {/* Tips */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4">
        <h5 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
          نصائح لرفع الصور:
        </h5>
        <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
          <li>• استخدم صور عالية الجودة لأفضل عرض</li>
          <li>• الصورة الأولى ستكون الصورة الرئيسية للمنتج</li>
          <li>• يمكنك رفع عدة صور لنفس المنتج</li>
          <li>• سيتم إنشاء صور مصغرة تلقائياً</li>
        </ul>
      </div>
    </div>
  );
};

export default ImageSection;
