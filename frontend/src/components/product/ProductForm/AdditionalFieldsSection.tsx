/**
 * قسم الحقول الإضافية للمنتج
 * يحتوي على الضمان، اسم المصنع، تاريخ التصنيع، وتاريخ الانتهاء
 */

import React, { useState, useEffect } from 'react';
import { FaCalendarAlt, FaIndustry, FaShieldAlt, FaClock } from 'react-icons/fa';
import { FiSettings, FiCalendar, FiShield } from 'react-icons/fi';

// Import components
import { TextInput, ModalSelectInput } from '../../inputs';
import DatePicker from '../../DatePicker';

interface AdditionalFieldsSectionProps {
  formData: any;
  updateFormData: (field: string, value: any) => void;
  errors: Record<string, string>;
}

const AdditionalFieldsSection: React.FC<AdditionalFieldsSectionProps> = ({
  formData,
  updateFormData,
  errors
}) => {
  // Warranty type options
  const warrantyTypeOptions = [
    { value: '', label: 'بدون ضمان' },
    { value: 'replacement', label: 'ضمان استبدال' },
    { value: 'repair', label: 'ضمان إصلاح' },
    { value: 'onsite', label: 'ضمان في الموقع' },
    { value: 'manufacturer', label: 'ضمان الشركة المصنعة' },
    { value: 'extended', label: 'ضمان ممتد' },
    { value: 'limited', label: 'ضمان محدود' }
  ];

  // Calculate days until expiry
  const calculateDaysUntilExpiry = () => {
    if (!formData.expiry_date) return null;
    
    const expiryDate = new Date(formData.expiry_date);
    const today = new Date();
    const diffTime = expiryDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
  };

  // Calculate product age
  const calculateProductAge = () => {
    if (!formData.manufactured_date) return null;
    
    const manufacturedDate = new Date(formData.manufactured_date);
    const today = new Date();
    const diffTime = today.getTime() - manufacturedDate.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 30) {
      return `${diffDays} يوم`;
    } else if (diffDays < 365) {
      const months = Math.floor(diffDays / 30);
      return `${months} شهر`;
    } else {
      const years = Math.floor(diffDays / 365);
      const remainingMonths = Math.floor((diffDays % 365) / 30);
      return remainingMonths > 0 ? `${years} سنة و ${remainingMonths} شهر` : `${years} سنة`;
    }
  };

  const daysUntilExpiry = calculateDaysUntilExpiry();
  const productAge = calculateProductAge();

  return (
    <div className="space-y-6">

      {/* Warranty Section - Updated with unified design */}
      <div className="bg-success-50 dark:bg-success-900/20 rounded-xl p-6 border-2 border-success-200 dark:border-success-800">
        <div className="flex items-center gap-3 mb-4">
          <FiShield className="w-5 h-5 text-green-600 dark:text-green-400" />
          <h4 className="text-md font-medium text-gray-900 dark:text-white">
            معلومات الضمان
          </h4>
        </div>

        <ModalSelectInput
          label="نوع الضمان"
          name="warranty_type"
          value={formData.warranty_type}
          onChange={(value) => updateFormData('warranty_type', value)}
          options={warrantyTypeOptions}
          placeholder="اختر نوع الضمان..."
          error={errors.warranty_type}
        />

        {formData.warranty_type && (
          <div className="mt-4 p-4 bg-green-100 dark:bg-green-800/30 rounded-lg">
            <div className="flex items-center gap-2 text-green-700 dark:text-green-300">
              <FaShieldAlt className="w-4 h-4" />
              <span className="text-sm font-medium">
                {warrantyTypeOptions.find(opt => opt.value === formData.warranty_type)?.label}
              </span>
            </div>
            <p className="text-xs text-green-600 dark:text-green-400 mt-1">
              سيتم عرض معلومات الضمان للعملاء عند الشراء
            </p>
          </div>
        )}
      </div>

      {/* Manufacturer Section */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
        <div className="flex items-center gap-3 mb-4">
          <FaIndustry className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          <h4 className="text-md font-medium text-gray-900 dark:text-white">
            معلومات المصنع
          </h4>
        </div>

        <TextInput
          label="اسم المصنع"
          name="manufacturer"
          value={formData.manufacturer}
          onChange={(value) => updateFormData('manufacturer', value)}
          placeholder="أدخل اسم الشركة المصنعة..."
          error={errors.manufacturer}
          maxLength={100}
        />

        {formData.manufacturer && (
          <div className="mt-4 p-4 bg-blue-100 dark:bg-blue-800/30 rounded-lg">
            <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
              <FaIndustry className="w-4 h-4" />
              <span className="text-sm font-medium">
                مُصنع بواسطة: {formData.manufacturer}
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Dates Section */}
      <div className="bg-purple-50 dark:bg-purple-900/20 rounded-xl p-6 border border-purple-200 dark:border-purple-800">
        <div className="flex items-center gap-3 mb-4">
          <FiCalendar className="w-5 h-5 text-purple-600 dark:text-purple-400" />
          <h4 className="text-md font-medium text-gray-900 dark:text-white">
            التواريخ المهمة
          </h4>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Manufacturing Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              تاريخ التصنيع
            </label>
            <DatePicker
              value={formData.manufactured_date}
              onChange={(value) => updateFormData('manufactured_date', value)}
              placeholder="اختر تاريخ التصنيع..."
              name="manufactured_date"
            />
            {errors.manufactured_date && (
              <p className="text-red-600 dark:text-red-400 text-xs mt-1">
                {errors.manufactured_date}
              </p>
            )}
            
            {productAge && (
              <div className="mt-2 p-2 bg-purple-100 dark:bg-purple-800/30 rounded-lg">
                <div className="flex items-center gap-2 text-purple-700 dark:text-purple-300">
                  <FaClock className="w-3 h-3" />
                  <span className="text-xs">
                    عمر المنتج: {productAge}
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Expiry Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              تاريخ انتهاء الصلاحية
            </label>
            <DatePicker
              value={formData.expiry_date}
              onChange={(value) => updateFormData('expiry_date', value)}
              placeholder="اختر تاريخ انتهاء الصلاحية..."
              name="expiry_date"
            />
            {errors.expiry_date && (
              <p className="text-red-600 dark:text-red-400 text-xs mt-1">
                {errors.expiry_date}
              </p>
            )}
            
            {daysUntilExpiry !== null && (
              <div className={`mt-2 p-2 rounded-lg ${
                daysUntilExpiry < 0 
                  ? 'bg-red-100 dark:bg-red-800/30' 
                  : daysUntilExpiry < 30 
                    ? 'bg-yellow-100 dark:bg-yellow-800/30' 
                    : 'bg-green-100 dark:bg-green-800/30'
              }`}>
                <div className={`flex items-center gap-2 ${
                  daysUntilExpiry < 0 
                    ? 'text-red-700 dark:text-red-300' 
                    : daysUntilExpiry < 30 
                      ? 'text-yellow-700 dark:text-yellow-300' 
                      : 'text-green-700 dark:text-green-300'
                }`}>
                  <FaCalendarAlt className="w-3 h-3" />
                  <span className="text-xs">
                    {daysUntilExpiry < 0 
                      ? `منتهي الصلاحية منذ ${Math.abs(daysUntilExpiry)} يوم`
                      : daysUntilExpiry === 0 
                        ? 'ينتهي اليوم'
                        : `${daysUntilExpiry} يوم حتى انتهاء الصلاحية`
                    }
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Date Validation Warning */}
        {formData.manufactured_date && formData.expiry_date && 
         new Date(formData.manufactured_date) >= new Date(formData.expiry_date) && (
          <div className="mt-4 p-4 bg-red-100 dark:bg-red-800/30 rounded-lg border border-red-200 dark:border-red-700">
            <div className="flex items-center gap-2 text-red-700 dark:text-red-300">
              <FaCalendarAlt className="w-4 h-4" />
              <span className="text-sm font-medium">
                تحذير: تاريخ انتهاء الصلاحية يجب أن يكون بعد تاريخ التصنيع
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Summary Card */}
      {(formData.warranty_type || formData.manufacturer || formData.manufactured_date || formData.expiry_date) && (
        <div className="bg-gray-50 dark:bg-gray-800/50 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
          <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
            ملخص المعلومات الإضافية
          </h4>
          
          <div className="space-y-3">
            {formData.warranty_type && (
              <div className="flex items-center gap-3">
                <FaShieldAlt className="w-4 h-4 text-green-600 dark:text-green-400" />
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  الضمان: {warrantyTypeOptions.find(opt => opt.value === formData.warranty_type)?.label}
                </span>
              </div>
            )}
            
            {formData.manufacturer && (
              <div className="flex items-center gap-3">
                <FaIndustry className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  المصنع: {formData.manufacturer}
                </span>
              </div>
            )}
            
            {formData.manufactured_date && (
              <div className="flex items-center gap-3">
                <FaCalendarAlt className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  تاريخ التصنيع: {new Date(formData.manufactured_date).toLocaleDateString('ar-SA')}
                  {productAge && ` (${productAge})`}
                </span>
              </div>
            )}
            
            {formData.expiry_date && (
              <div className="flex items-center gap-3">
                <FaClock className={`w-4 h-4 ${
                  daysUntilExpiry !== null && daysUntilExpiry < 30 
                    ? 'text-yellow-600 dark:text-yellow-400' 
                    : 'text-gray-600 dark:text-gray-400'
                }`} />
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  تاريخ الانتهاء: {new Date(formData.expiry_date).toLocaleDateString('ar-SA')}
                  {daysUntilExpiry !== null && (
                    <span className={`mr-2 ${
                      daysUntilExpiry < 0 
                        ? 'text-red-600 dark:text-red-400' 
                        : daysUntilExpiry < 30 
                          ? 'text-yellow-600 dark:text-yellow-400' 
                          : 'text-green-600 dark:text-green-400'
                    }`}>
                      ({daysUntilExpiry < 0 
                        ? `منتهي منذ ${Math.abs(daysUntilExpiry)} يوم`
                        : daysUntilExpiry === 0 
                          ? 'ينتهي اليوم'
                          : `${daysUntilExpiry} يوم متبقي`
                      })
                    </span>
                  )}
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Tips */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4">
        <h5 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
          نصائح للحقول الإضافية:
        </h5>
        <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
          <li>• معلومات الضمان تساعد في بناء ثقة العملاء</li>
          <li>• اسم المصنع مفيد للبحث والفلترة</li>
          <li>• تواريخ التصنيع والانتهاء مهمة للمنتجات القابلة للتلف</li>
          <li>• سيتم تنبيهك عند اقتراب انتهاء صلاحية المنتجات</li>
        </ul>
      </div>
    </div>
  );
};

export default AdditionalFieldsSection;
