import React, { useState, useEffect } from 'react';
import { FiP<PERSON>age, FiLayers, FiTrash, FiTag, FiPlus } from 'react-icons/fi';
import { FaBarcode } from 'react-icons/fa';

// Import stores and types
import useVariantAttributeStore from '../../stores/variantAttributeStore';
import { NumberInput, ModalSelectInput, BarcodeInput } from '../inputs';

interface ProductVariant {
  id: string;
  combination: Record<number, number>; // attributeId -> valueId
  combinationText: string;
  price: number;
  costPrice: number;
  sku?: string;
  barcode?: string;
  isActive: boolean;
}

interface ProductVariantsSectionProps {
  formData: any;
  updateFormData: (field: string, value: any) => void;
  errors: Record<string, string>;
}

const ProductVariantsSection: React.FC<ProductVariantsSectionProps> = ({
  formData,
  updateFormData,
  errors
}) => {
  // Store
  const { attributes, loading, fetchAttributes } = useVariantAttributeStore();

  // Local state
  const [selectedAttributes, setSelectedAttributes] = useState<number[]>([]);
  const [selectedValues, setSelectedValues] = useState<Record<number, number[]>>({}); // attributeId -> valueIds[]
  const [productVariants, setProductVariants] = useState<ProductVariant[]>([]);

  // Load attributes on mount
  useEffect(() => {
    fetchAttributes(false); // Only active attributes
  }, [fetchAttributes]);

  // Generate combinations when attributes or selected values change
  useEffect(() => {
    if (selectedAttributes.length > 0) {
      generateVariantCombinations();
    } else {
      setProductVariants([]);
    }
  }, [selectedAttributes, selectedValues, attributes]);

  // Update form data when variants or attributes change
  useEffect(() => {
    updateFormData('product_variants', productVariants);
    updateFormData('selected_variant_attributes', selectedAttributes);
  }, [productVariants, selectedAttributes, updateFormData]);

  // Generate combinations based on selected values only
  const generateVariantCombinations = () => {
    const selectedAttributeObjects = attributes.filter(attr =>
      selectedAttributes.includes(attr.id) && attr.is_active
    );

    if (selectedAttributeObjects.length === 0) {
      setProductVariants([]);
      return;
    }

    // Check if all attributes have selected values
    const hasAllValues = selectedAttributeObjects.every(attr =>
      selectedValues[attr.id] && selectedValues[attr.id].length > 0
    );

    if (!hasAllValues) {
      setProductVariants([]);
      return;
    }

    const combinations: ProductVariant[] = [];

    const generateCombinations = (
      attrIndex: number,
      currentCombination: Record<number, number>,
      currentText: string[]
    ) => {
      if (attrIndex >= selectedAttributeObjects.length) {
        // Create variant for this combination
        const variantId = `variant_${Date.now()}_${Object.values(currentCombination).join('_')}`;
        const combinationTextStr = currentText.join(' - ');

        combinations.push({
          id: variantId,
          combination: { ...currentCombination },
          combinationText: combinationTextStr,
          price: formData.price || 0,
          costPrice: formData.cost_price || 0,
          isActive: true
        });
        return;
      }

      const currentAttribute = selectedAttributeObjects[attrIndex];
      const selectedValueIds = selectedValues[currentAttribute.id] || [];
      const selectedValueObjects = currentAttribute.values.filter(v =>
        selectedValueIds.includes(v.id) && v.is_active
      );

      selectedValueObjects.forEach(value => {
        const newCombination = {
          ...currentCombination,
          [currentAttribute.id]: value.id
        };
        const newText = [
          ...currentText,
          `${currentAttribute.name_ar || currentAttribute.name}: ${value.value_ar || value.value}`
        ];

        generateCombinations(attrIndex + 1, newCombination, newText);
      });
    };

    generateCombinations(0, {}, []);
    setProductVariants(combinations);
  };

  // Update variant data
  const updateVariant = (variantId: string, field: string, value: any) => {
    setProductVariants(prev => prev.map(variant => 
      variant.id === variantId 
        ? { ...variant, [field]: value }
        : variant
    ));
  };

  // Remove attribute
  const removeAttribute = (attributeId: number) => {
    setSelectedAttributes(prev => prev.filter(id => id !== attributeId));
    setSelectedValues(prev => {
      const newValues = { ...prev };
      delete newValues[attributeId];
      return newValues;
    });
  };

  // Add attribute
  const addAttribute = (attributeId: number) => {
    if (!selectedAttributes.includes(attributeId)) {
      setSelectedAttributes(prev => [...prev, attributeId]);
      setSelectedValues(prev => ({ ...prev, [attributeId]: [] }));
    }
  };

  // Toggle value selection
  const toggleValue = (attributeId: number, valueId: number) => {
    setSelectedValues(prev => {
      const currentValues = prev[attributeId] || [];
      const isSelected = currentValues.includes(valueId);

      return {
        ...prev,
        [attributeId]: isSelected
          ? currentValues.filter(id => id !== valueId)
          : [...currentValues, valueId]
      };
    });
  };

  // Available attributes (not selected)
  const availableAttributes = attributes.filter(attr => 
    attr.is_active && 
    !selectedAttributes.includes(attr.id) &&
    attr.values.some(v => v.is_active)
  );

  // Selected attribute objects
  const selectedAttributeObjects = attributes.filter(attr => 
    selectedAttributes.includes(attr.id)
  );

  return (
    <div className="space-y-4">
      {/* Info Message */}
      <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3 border border-purple-200 dark:border-purple-800">
        <p className="text-xs text-purple-600 dark:text-purple-400">
          اختر خصائص المنتج لإنشاء متغيرات مختلفة مع أسعار منفصلة لكل متغير
        </p>
      </div>

      {/* Combined Attributes and Variants Container */}
      <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
        {/* Attribute Selection Header */}
        <div className="flex items-center justify-between mb-3">
          <h5 className="text-sm font-medium text-gray-900 dark:text-white flex items-center gap-2">
            <FiTag className="w-4 h-4 text-purple-600 dark:text-purple-400" />
            خصائص ومتغيرات المنتج
          </h5>
          {loading && (
            <div className="flex items-center gap-2 text-xs text-gray-500">
              <div className="w-3 h-3 border-2 border-purple-600 border-t-transparent rounded-full animate-spin"></div>
              جاري التحميل...
            </div>
          )}
        </div>

        {/* Add Attribute Dropdown */}
        {availableAttributes.length > 0 && (
          <div className="mb-3">
            <ModalSelectInput
              label="إضافة خاصية"
              name="add_attribute"
              value=""
              onChange={(value) => {
                if (value) {
                  addAttribute(parseInt(value));
                }
              }}
              options={availableAttributes.map(attr => ({
                value: attr.id.toString(),
                label: `${attr.name_ar} (${attr.values.filter(v => v.is_active).length} قيم)`
              }))}
              placeholder="اختر خاصية لإضافتها..."
              searchable
            />
          </div>
        )}

        {/* Selected Attributes */}
        {selectedAttributeObjects.length > 0 && (
          <div className="space-y-3">
            {selectedAttributeObjects.map(attribute => {
              const selectedValueIds = selectedValues[attribute.id] || [];
              const activeValues = attribute.values.filter(v => v.is_active);

              return (
                <div key={attribute.id} className="bg-white dark:bg-gray-700 rounded-lg p-3 border border-gray-200 dark:border-gray-600">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {attribute.name_ar || attribute.name}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        ({selectedValueIds.length}/{activeValues.length} مختار)
                      </span>
                    </div>
                    <button
                      type="button"
                      onClick={() => removeAttribute(attribute.id)}
                      className="p-1 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-all duration-200"
                      title="إزالة الخاصية"
                    >
                      <FiTrash className="w-3 h-3" />
                    </button>
                  </div>

                  {/* Available Values as Clickable Buttons */}
                  <div className="space-y-2">
                    <div className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                      اختر القيم المطلوبة:
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {activeValues.map(value => {
                        const isSelected = selectedValueIds.includes(value.id);
                        return (
                          <button
                            key={value.id}
                            type="button"
                            onClick={() => toggleValue(attribute.id, value.id)}
                            className={`inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-medium border transition-all duration-200 ${
                              isSelected
                                ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200 border-purple-300 dark:border-purple-600 shadow-sm'
                                : 'bg-gray-50 dark:bg-gray-600 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-500 hover:bg-gray-100 dark:hover:bg-gray-500'
                            }`}
                          >
                            {value.color_code && (
                              <span
                                className="w-3 h-3 rounded-full mr-2 border border-gray-300"
                                style={{ backgroundColor: value.color_code }}
                              />
                            )}
                            <span className="font-medium">
                              {value.value_ar || value.value}
                            </span>
                            {value.value_ar && value.value && value.value_ar !== value.value && (
                              <span className="text-gray-500 dark:text-gray-400 ml-1">
                                ({value.value})
                              </span>
                            )}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {selectedAttributes.length === 0 && !loading && (
          <div className="text-center py-4 text-gray-500 dark:text-gray-400">
            <FiLayers className="w-6 h-6 mx-auto mb-2 opacity-50" />
            <p className="text-sm">لم يتم اختيار أي خصائص بعد</p>
            <p className="text-xs">اختر خصائص لإنشاء متغيرات المنتج</p>
          </div>
        )}

        {/* Message when attributes selected but no values chosen */}
        {selectedAttributes.length > 0 && productVariants.length === 0 && (
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 border border-blue-200 dark:border-blue-800 mt-3">
            <div className="flex items-center gap-2 mb-1">
              <FiPackage className="w-4 h-4 text-blue-600 dark:text-blue-400" />
              <span className="text-sm font-medium text-blue-800 dark:text-blue-300">
                اختر قيم الخصائص
              </span>
            </div>
            <p className="text-xs text-blue-600 dark:text-blue-400">
              تم اختيار الخصائص، الآن اختر القيم المطلوبة لكل خاصية لإنشاء متغيرات المنتج
            </p>
          </div>
        )}

        {/* Generated Variants - Inside the same container */}
        {productVariants.length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
            <div className="flex items-center justify-between mb-3">
              <h5 className="text-sm font-medium text-gray-900 dark:text-white flex items-center gap-2">
                <FiPackage className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                متغيرات المنتج ({productVariants.length})
              </h5>
            </div>

          <div className="space-y-3">
            {productVariants.map((variant, index) => (
              <div
                key={variant.id}
                className="bg-white dark:bg-gray-700 rounded-lg p-3 border border-gray-200 dark:border-gray-600"
              >
                {/* Header Row with Profit Margin */}
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h6 className="text-sm font-medium text-gray-900 dark:text-white">
                      متغير {index + 1}
                    </h6>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {variant.combinationText}
                    </p>
                  </div>
                  <div className="flex items-center">
                    {/* Profit Margin */}
                    <div className="bg-green-50 dark:bg-green-900/20 rounded-full px-3 py-1 border border-green-200 dark:border-green-800">
                      <span className="text-xs font-medium text-green-600 dark:text-green-400">
                        هامش الربح: {variant.costPrice > 0
                          ? (((variant.price - variant.costPrice) / variant.costPrice) * 100).toFixed(1)
                          : '0.0'
                        }%
                      </span>
                    </div>
                  </div>
                </div>

                {/* All Fields in One Row with Responsive Handling */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 overflow-hidden">
                  {/* Price Field */}
                  <div className="min-w-0">
                    <NumberInput
                      label="السعر"
                      name={`variant_price_${variant.id}`}
                      value={variant.price.toString()}
                      onChange={(value) => updateVariant(variant.id, 'price', parseFloat(value) || 0)}
                      placeholder="0.00"
                      min={0}
                      step="0.01"
                      currency="د.ل"
                    />
                  </div>

                  {/* Cost Price Field */}
                  <div className="min-w-0">
                    <NumberInput
                      label="سعر التكلفة"
                      name={`variant_cost_${variant.id}`}
                      value={variant.costPrice.toString()}
                      onChange={(value) => updateVariant(variant.id, 'costPrice', parseFloat(value) || 0)}
                      placeholder="0.00"
                      min={0}
                      step="0.01"
                      currency="د.ل"
                    />
                  </div>

                  {/* Barcode Field */}
                  <div className="min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 truncate pr-1">
                        باركود العنصر
                      </label>
                      <button
                        type="button"
                        onClick={() => updateVariant(variant.id, 'barcode', `VAR-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`)}
                        className="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors duration-200 cursor-pointer flex items-center gap-1 flex-shrink-0"
                      >
                        <FaBarcode className="w-3 h-3" />
                        <span className="hidden sm:inline">توليد</span>
                      </button>
                    </div>
                    <div className="min-w-0">
                      <BarcodeInput
                        name={`variant_barcode_${variant.id}`}
                        value={variant.barcode || ''}
                        onChange={(value) => updateVariant(variant.id, 'barcode', value)}
                        placeholder="123456789012"
                        showGenerateButton={false}
                      />
                    </div>
                  </div>

                  {/* SKU Field */}
                  <div className="min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 truncate pr-1">
                        الباركود الداخلي (SKU)
                      </label>
                      <button
                        type="button"
                        onClick={() => updateVariant(variant.id, 'sku', `SKU-${Date.now()}-${Math.random().toString(36).substring(2, 5)}`)}
                        className="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors duration-200 cursor-pointer flex items-center gap-1 flex-shrink-0"
                      >
                        <FaBarcode className="w-3 h-3" />
                        <span className="hidden sm:inline">توليد</span>
                      </button>
                    </div>
                    <div className="min-w-0">
                      <BarcodeInput
                        name={`variant_sku_${variant.id}`}
                        value={variant.sku || ''}
                        onChange={(value) => updateVariant(variant.id, 'sku', value)}
                        placeholder="SKU123456"
                        showGenerateButton={false}
                      />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Summary */}
          <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
            <div className="grid grid-cols-3 gap-2 text-center">
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded p-2 border border-blue-200 dark:border-blue-800">
                <div className="text-xs text-blue-600 dark:text-blue-400 mb-1">إجمالي</div>
                <div className="text-sm font-bold text-blue-800 dark:text-blue-300">
                  {productVariants.length}
                </div>
              </div>
              <div className="bg-green-50 dark:bg-green-900/20 rounded p-2 border border-green-200 dark:border-green-800">
                <div className="text-xs text-green-600 dark:text-green-400 mb-1">نشط</div>
                <div className="text-sm font-bold text-green-800 dark:text-green-300">
                  {productVariants.filter(v => v.isActive).length}
                </div>
              </div>
              <div className="bg-purple-50 dark:bg-purple-900/20 rounded p-2 border border-purple-200 dark:border-purple-800">
                <div className="text-xs text-purple-600 dark:text-purple-400 mb-1">متوسط السعر</div>
                <div className="text-sm font-bold text-purple-800 dark:text-purple-300">
                  {productVariants.length > 0
                    ? (productVariants.reduce((sum, v) => sum + v.price, 0) / productVariants.length).toFixed(0)
                    : '0'
                  }
                </div>
              </div>
            </div>
          </div>
          </div>
        )}
      </div>

      {/* No attributes available warning */}
      {!loading && attributes.length === 0 && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-xl p-6 border border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center gap-3 mb-2">
            <FiLayers className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
            <h4 className="text-md font-medium text-yellow-800 dark:text-yellow-300">
              لا توجد خصائص متغيرات متاحة
            </h4>
          </div>
          <p className="text-sm text-yellow-700 dark:text-yellow-400 mb-3">
            لإنشاء منتج متعدد الخيارات، تحتاج أولاً إلى إنشاء خصائص المتغيرات مثل الألوان والأحجام.
          </p>
          <button
            type="button"
            onClick={() => window.open('/variant-attributes', '_blank')}
            className="inline-flex items-center gap-2 px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg font-medium text-sm transition-all duration-200"
          >
            <FiPlus className="w-4 h-4" />
            إضافة خصائص المتغيرات
          </button>
        </div>
      )}
    </div>
  );
};

export default ProductVariantsSection;