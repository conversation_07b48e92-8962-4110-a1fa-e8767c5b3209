import React from 'react';
import { FaCheckCircle, FaFolder, FaHdd, FaDesktop } from 'react-icons/fa';
import Modal from './Modal';

interface FolderSelectionSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  folderPath: string;
  absolutePath?: string;
  autoClose?: boolean;
  autoCloseDelay?: number;
}

const FolderSelectionSuccessModal: React.FC<FolderSelectionSuccessModalProps> = ({
  isOpen,
  onClose,
  folderPath,
  absolutePath,
  autoClose = true,
  autoCloseDelay = 3000
}) => {
  React.useEffect(() => {
    if (isOpen && autoClose) {
      const timer = setTimeout(() => {
        onClose();
      }, autoCloseDelay);
      return () => clearTimeout(timer);
    }
  }, [isOpen, autoClose, autoCloseDelay, onClose]);

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="تم اختيار المجلد بنجاح" size="md" zIndex="highest">
      <div className="text-center py-2">
        {/* Success Icon */}
        <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/30 mb-6">
          <FaCheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
        </div>

        {/* Success Message */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            🎉 تم اختيار المجلد بنجاح!
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            تم تحديد مسار النسخ الاحتياطية الجديد وحفظه في الإعدادات
          </p>
        </div>

        {/* Folder Details Card */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6 text-right">
          <h5 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center justify-center">
            <FaFolder className="ml-2 text-primary-600" />
            تفاصيل المجلد المختار:
          </h5>
          
          <div className="space-y-4">
            {/* المسار المختار */}
            <div className="flex items-start">
              <FaDesktop className="text-blue-600 dark:text-blue-400 ml-3 mt-1 flex-shrink-0" />
              <div className="flex-1">
                <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                  المسار المختار:
                </p>
                <p className="text-sm font-mono bg-white dark:bg-gray-600 px-3 py-2 rounded border text-gray-900 dark:text-gray-100 break-all">
                  {folderPath}
                </p>
              </div>
            </div>

            {/* المسار المطلق (إذا كان مختلفاً) */}
            {absolutePath && absolutePath !== folderPath && (
              <div className="flex items-start">
                <FaHdd className="text-green-600 dark:text-green-400 ml-3 mt-1 flex-shrink-0" />
                <div className="flex-1">
                  <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                    المسار المطلق:
                  </p>
                  <p className="text-sm font-mono bg-white dark:bg-gray-600 px-3 py-2 rounded border text-gray-900 dark:text-gray-100 break-all">
                    {absolutePath}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Success Features */}
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6">
          <h5 className="text-sm font-semibold text-green-800 dark:text-green-200 mb-3">
            ✅ المزايا المحققة:
          </h5>
          <ul className="text-xs text-green-700 dark:text-green-300 space-y-1 text-right">
            <li>• تم الحصول على المسار الفعلي من النظام</li>
            <li>• تم التأكد من وجود المجلد وإمكانية الكتابة فيه</li>
            <li>• سيتم حفظ النسخ الاحتياطية في هذا المجلد</li>
            <li>• يمكنك الآن اختبار المسار للتأكد من صحته</li>
          </ul>
        </div>

        {/* Auto-close notice */}
        {autoClose && (
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3 mb-6">
            <p className="text-xs text-blue-700 dark:text-blue-400">
              ستُغلق هذه النافذة تلقائياً خلال ثوانٍ قليلة...
            </p>
          </div>
        )}

        {/* Close Button */}
        <button
          onClick={onClose}
          className="btn-primary flex items-center justify-center min-w-[120px] mx-auto text-sm"
        >
          <FaCheckCircle className="ml-2" />
          <span>ممتاز</span>
        </button>
      </div>
    </Modal>
  );
};

export default FolderSelectionSuccessModal;
