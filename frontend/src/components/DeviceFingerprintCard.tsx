import React, { useEffect, useState } from 'react';
import { FaShieldAlt, FaFingerprint, FaCheckCircle, FaExclamationTriangle, FaTimesCircle, FaEye, FaChartLine } from 'react-icons/fa';

interface FingerprintData {
  fingerprint_id: string;
  hardware_fingerprint: string;
  storage_fingerprint: string;
  system_fingerprint: string;
  stability: {
    stability_score: number;
    stability_level: string;
    stability_color: string;
    is_stable: boolean;
    recommendation: string;
  };
  exists_in_database: boolean;
  is_active: boolean;
  analytics?: {
    access_stats: {
      total_events: number;
      access_count: number;
      unique_ips: number;
      unique_user_agents: number;
    };
    security_flags: {
      multiple_ips: boolean;
      multiple_browsers: boolean;
      frequent_access: boolean;
    };
  };
}

interface DeviceFingerprintCardProps {
  deviceId: string;
  isMainServer?: boolean;
  compact?: boolean;
}

const DeviceFingerprintCard: React.FC<DeviceFingerprintCardProps> = ({
  deviceId,
  isMainServer = false,
  compact = false
}) => {
  const [fingerprintData, setFingerprintData] = useState<FingerprintData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFingerprintData = async () => {
      try {
        setLoading(true);
        setError(null);

        // تعطيل جلب بيانات البصمة لتوفير موارد النظام
        // إنشاء بيانات وهمية لعرض المكون بدون طلبات شبكة
        setFingerprintData({
          fingerprint_id: isMainServer ? 'main_server' : 'device_disabled',
          hardware_fingerprint: isMainServer ? 'server_hardware' : 'disabled_hardware',
          storage_fingerprint: isMainServer ? 'server_storage' : 'disabled_storage',
          system_fingerprint: isMainServer ? 'Server_System' : 'Device_System',
          stability: {
            stability_score: isMainServer ? 100 : 85,
            stability_level: isMainServer ? 'خادم رئيسي' : 'معطل لتوفير الموارد',
            stability_color: isMainServer ? 'blue' : 'gray',
            is_stable: true,
            recommendation: isMainServer ? 'الخادم الرئيسي - ثابت دائماً' : 'تم تعطيل جلب البصمة لتوفير موارد النظام'
          },
          exists_in_database: true,
          is_active: true
        });
        setLoading(false);
        return;

        // الكود الأصلي معطل لتوفير موارد النظام
        // if (isMainServer) {
        //   setFingerprintData({
        //     fingerprint_id: 'main_server',
        //     hardware_fingerprint: 'server_hardware',
        //     storage_fingerprint: 'server_storage',
        //     system_fingerprint: 'Server_System',
        //     stability: {
        //       stability_score: 100,
        //       stability_level: 'خادم رئيسي',
        //       stability_color: 'blue',
        //       is_stable: true,
        //       recommendation: 'الخادم الرئيسي - ثابت دائماً'
        //     },
        //     exists_in_database: true,
        //     is_active: true
        //   });
        //   setLoading(false);
        //   return;
        // }

        // الكود الأصلي لجلب بيانات البصمة معطل لتوفير موارد النظام
        // جلب بيانات البصمة للأجهزة البعيدة
        // أولاً محاولة جلب التحليلات المفصلة
        // try {
        //   const analyticsResponse = await fetch(`/api/device/fingerprint-analytics/${deviceId}`, {
        //     method: 'GET',
        //     headers: {
        //       'Content-Type': 'application/json',
        //     }
        //   });

        //   if (analyticsResponse.ok) {
        //     const analyticsData = await analyticsResponse.json();
        //     if (analyticsData.success && analyticsData.analytics) {
        //       // تحويل بيانات التحليلات إلى تنسيق FingerprintData
        //       setFingerprintData({
        //         fingerprint_id: analyticsData.analytics.fingerprint_id,
        //         hardware_fingerprint: 'device_hardware',
        //         storage_fingerprint: 'device_storage',
        //         system_fingerprint: 'Device_System',
        //         stability: analyticsData.analytics.stability,
        //         exists_in_database: true,
        //         is_active: analyticsData.analytics.is_active,
        //         analytics: analyticsData.analytics
        //       });
        //       return;
        //     }
        //   }
        // } catch (error) {
        //   console.warn('فشل في جلب تحليلات البصمة:', error);
        // }

        // // إذا فشل جلب التحليلات، محاولة جلب البيانات الأساسية
        // const response = await fetch('/api/device/fingerprint-validation', {
        //   method: 'GET',
        //   headers: {
        //     'Content-Type': 'application/json',
        //   }
        // });

        // if (response.ok) {
        //   const data = await response.json();
        //   if (data.success && data.validation) {
        //     setFingerprintData(data.validation);
        //   } else {
        //     setError('لا توجد بيانات بصمة');
        //   }
        // } else {
        //   setError('فشل في جلب بيانات البصمة');
        // }
      } catch (err) {
        setError('خطأ في الشبكة');
        console.error('خطأ في جلب بيانات البصمة:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchFingerprintData();
  }, [deviceId, isMainServer]);

  const getStabilityIcon = (level: string) => {
    if (isMainServer) return <FaShieldAlt className="text-blue-600" />;

    switch (level) {
      case 'ممتاز':
        return <FaCheckCircle className="text-green-600" />;
      case 'جيد':
        return <FaCheckCircle className="text-blue-600" />;
      case 'متوسط':
        return <FaExclamationTriangle className="text-orange-600" />;
      default:
        return <FaTimesCircle className="text-red-600" />;
    }
  };

  const getStabilityColor = (color: string) => {
    const colorMap: Record<string, string> = {
      'green': 'bg-green-100 text-green-800 border-green-200',
      'blue': 'bg-blue-100 text-blue-800 border-blue-200',
      'orange': 'bg-orange-100 text-orange-800 border-orange-200',
      'red': 'bg-red-100 text-red-800 border-red-200'
    };
    return colorMap[color] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  if (loading) {
    return (
      <div className={`${compact ? 'p-2' : 'p-3'} bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600`}>
        <div className="flex items-center space-x-2 space-x-reverse">
          <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
          <span className="text-xs text-gray-600 dark:text-gray-400">جاري تحليل البصمة...</span>
        </div>
      </div>
    );
  }

  if (error || !fingerprintData) {
    return (
      <div className={`${compact ? 'p-2' : 'p-3'} bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800`}>
        <div className="flex items-center space-x-2 space-x-reverse">
          <FaTimesCircle className="text-red-600 text-sm" />
          <span className="text-xs text-red-700 dark:text-red-300">
            {error || 'لا توجد بيانات بصمة'}
          </span>
        </div>
      </div>
    );
  }

  const { stability, exists_in_database, analytics } = fingerprintData;

  if (compact) {
    return (
      <div className="p-2 bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 space-x-reverse">
            {getStabilityIcon(stability.stability_level)}
            <div>
              <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStabilityColor(stability.stability_color)}`}>
                {stability.stability_level}
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-1 space-x-reverse text-xs text-gray-600 dark:text-gray-400">
            <FaFingerprint />
            <span>{stability.stability_score}/100</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-3 bg-gradient-to-br from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
      {/* رأس البطاقة */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2 space-x-reverse">
          <FaFingerprint className="text-purple-600 dark:text-purple-400" />
          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">بصمة الجهاز</span>
        </div>
        <div className="flex items-center space-x-1 space-x-reverse">
          {exists_in_database ? (
            <FaCheckCircle className="text-green-600 text-xs" title="مسجل في النظام" />
          ) : (
            <FaTimesCircle className="text-red-600 text-xs" title="غير مسجل" />
          )}
        </div>
      </div>

      {/* مستوى الثبات */}
      <div className="mb-3">
        <div className="flex items-center justify-between mb-1">
          <span className="text-xs text-gray-600 dark:text-gray-400">مستوى الثبات</span>
          <span className="text-xs font-medium text-gray-900 dark:text-gray-100">
            {stability.stability_score}/100
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${
              stability.stability_score >= 90 ? 'bg-green-500' :
              stability.stability_score >= 70 ? 'bg-blue-500' :
              stability.stability_score >= 50 ? 'bg-orange-500' : 'bg-red-500'
            }`}
            style={{ width: `${stability.stability_score}%` }}
          ></div>
        </div>
        <div className="mt-1">
          <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStabilityColor(stability.stability_color)}`}>
            {getStabilityIcon(stability.stability_level)}
            <span className="mr-1">{stability.stability_level}</span>
          </div>
        </div>
      </div>

      {/* معلومات البصمة */}
      <div className="space-y-2 text-xs">
        <div className="flex justify-between">
          <span className="text-gray-600 dark:text-gray-400">معرف البصمة:</span>
          <span className="font-mono text-gray-900 dark:text-gray-100">
            {fingerprintData.fingerprint_id.slice(0, 8)}...
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600 dark:text-gray-400">بصمة الأجهزة:</span>
          <span className="font-mono text-gray-900 dark:text-gray-100">
            {fingerprintData.hardware_fingerprint.slice(0, 6)}...
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600 dark:text-gray-400">بصمة التخزين:</span>
          <span className="font-mono text-gray-900 dark:text-gray-100">
            {fingerprintData.storage_fingerprint.slice(0, 6)}...
          </span>
        </div>
      </div>

      {/* إحصائيات الوصول */}
      {analytics && (
        <div className="mt-3 pt-3 border-t border-purple-200 dark:border-purple-700">
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex items-center space-x-1 space-x-reverse">
              <FaEye className="text-blue-600" />
              <span className="text-gray-600 dark:text-gray-400">الوصول:</span>
              <span className="font-medium text-gray-900 dark:text-gray-100">
                {analytics.access_stats.access_count}
              </span>
            </div>
            <div className="flex items-center space-x-1 space-x-reverse">
              <FaChartLine className="text-green-600" />
              <span className="text-gray-600 dark:text-gray-400">الأحداث:</span>
              <span className="font-medium text-gray-900 dark:text-gray-100">
                {analytics.access_stats.total_events}
              </span>
            </div>
          </div>

          {/* تحذيرات أمنية */}
          {(analytics.security_flags.multiple_ips || analytics.security_flags.multiple_browsers || analytics.security_flags.frequent_access) && (
            <div className="mt-2 p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded border border-yellow-200 dark:border-yellow-800">
              <div className="flex items-center space-x-1 space-x-reverse">
                <FaExclamationTriangle className="text-yellow-600 text-xs" />
                <span className="text-xs text-yellow-700 dark:text-yellow-300 font-medium">
                  تحذيرات أمنية
                </span>
              </div>
              <div className="mt-1 text-xs text-yellow-600 dark:text-yellow-400">
                {analytics.security_flags.multiple_ips && <div>• عدة عناوين IP</div>}
                {analytics.security_flags.multiple_browsers && <div>• متصفحات متعددة</div>}
                {analytics.security_flags.frequent_access && <div>• وصول متكرر</div>}
              </div>
            </div>
          )}
        </div>
      )}

      {/* التوصية */}
      <div className="mt-2 text-xs text-gray-600 dark:text-gray-400 italic">
        {stability.recommendation}
      </div>
    </div>
  );
};

export default DeviceFingerprintCard;
