import React from 'react';
import { FaExclamationTriangle } from 'react-icons/fa';
import Modal from './Modal';

interface SimpleErrorModalProps {
  isOpen: boolean;
  onClose: () => void;
  message: string;
  autoClose?: boolean;
  autoCloseDelay?: number;
}

const SimpleErrorModal: React.FC<SimpleErrorModalProps> = ({
  isOpen,
  onClose,
  message,
  autoClose = false,
  autoCloseDelay = 3000
}) => {
  React.useEffect(() => {
    if (isOpen && autoClose) {
      const timer = setTimeout(() => {
        onClose();
      }, autoCloseDelay);
      return () => clearTimeout(timer);
    }
  }, [isOpen, autoClose, autoCloseDelay, onClose]);

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="خطأ" size="sm">
      <div className="text-center">
        {/* Error Icon */}
        <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/30 mb-6">
          <FaExclamationTriangle className="h-8 w-8 text-red-600 dark:text-red-400" />
        </div>

        {/* Error Message */}
        <div className="mb-6">
          <p className="text-sm text-gray-600 dark:text-gray-300">
            {message}
          </p>
        </div>

        {/* Auto-close notice */}
        {autoClose && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3 mb-6">
            <p className="text-xs text-red-700 dark:text-red-400">
              ستُغلق هذه النافذة تلقائياً خلال ثوانٍ قليلة...
            </p>
          </div>
        )}

        {/* Close Button */}
        <button
          onClick={onClose}
          className="btn-danger flex items-center justify-center min-w-[120px] mx-auto"
        >
          <span>موافق</span>
        </button>
      </div>
    </Modal>
  );
};

export default SimpleErrorModal;
