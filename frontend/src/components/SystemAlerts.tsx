import React, { useState, useEffect } from 'react';
import {
  FaExclamationTriangle,
  FaInfoCircle,
  FaTimesCircle,
  FaTimes,
  FaBellSlash,
  FaCheck,
  FaExternalLinkAlt,
  FaServer,
  FaDatabase,
  FaDesktop,
  FaNetworkWired,
  FaShieldAlt,
  FaTools,
  FaRoute,
  FaBoxes,
  FaShoppingCart,
  FaUsers
} from 'react-icons/fa';
import { FiBell } from 'react-icons/fi';
import { TopbarTooltip } from './ui';
import alertService, { SystemAlert } from '../services/alertService';
import { useNavigate } from 'react-router-dom';

interface SystemAlertsProps {
  className?: string;
  showInSidebar?: boolean;
  showInHeader?: boolean;
}

const SystemAlerts: React.FC<SystemAlertsProps> = ({
  className = '',
  showInSidebar = false,
  showInHeader = false
}) => {
  const navigate = useNavigate();
  const [alerts, setAlerts] = useState<SystemAlert[]>([]);
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    // الحصول على التنبيهات الحالية
    setAlerts(alertService.getAlerts());

    // الاستماع للتنبيهات الجديدة
    const unsubscribe = alertService.addListener((newAlerts) => {
      setAlerts(newAlerts);
    });

    return unsubscribe;
  }, []);

  // الحصول على أيقونة التنبيه
  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'critical':
        return <FaExclamationTriangle className="text-red-600 dark:text-red-400" />;
      case 'error':
        return <FaTimesCircle className="text-orange-600 dark:text-orange-400" />;
      case 'warning':
        return <FaExclamationTriangle className="text-yellow-600 dark:text-yellow-400" />;
      case 'info':
      default:
        return <FaInfoCircle className="text-blue-600 dark:text-blue-400" />;
    }
  };

  // الحصول على أيقونة المصدر
  const getSourceIcon = (source: string) => {
    switch (source.toUpperCase()) {
      case 'DATABASE':
        return <FaDatabase className="text-purple-600 dark:text-purple-400 text-xs" />;
      case 'BACKEND':
      case 'SERVER':
        return <FaServer className="text-blue-600 dark:text-blue-400 text-xs" />;
      case 'FRONTEND':
      case 'CLIENT':
        return <FaDesktop className="text-green-600 dark:text-green-400 text-xs" />;
      case 'NETWORK':
        return <FaNetworkWired className="text-orange-600 dark:text-orange-400 text-xs" />;
      case 'AUTH':
      case 'SECURITY':
        return <FaShieldAlt className="text-red-600 dark:text-red-400 text-xs" />;
      case 'SYSTEM':
        return <FaTools className="text-gray-600 dark:text-gray-400 text-xs" />;
      case 'INVENTORY':
        return <FaBoxes className="text-indigo-600 dark:text-indigo-400 text-xs" />;
      case 'SALES':
        return <FaShoppingCart className="text-emerald-600 dark:text-emerald-400 text-xs" />;
      case 'CUSTOMER':
        return <FaUsers className="text-cyan-600 dark:text-cyan-400 text-xs" />;
      default:
        return <FaInfoCircle className="text-gray-500 dark:text-gray-400 text-xs" />;
    }
  };

  // الحصول على مسار التوجيه حسب نوع المشكلة
  const getNavigationPath = (alert: SystemAlert): string | null => {
    const source = alert.source.toUpperCase();
    const message = alert.message.toLowerCase();

    // توجيه حسب المصدر والرسالة
    if (source === 'DATABASE' || message.includes('database')) {
      return '/reports?tab=system&subtab=logs';
    }
    if (source === 'NETWORK' || message.includes('network') || message.includes('connection')) {
      return '/reports?tab=system&subtab=monitor';
    }
    if (source === 'AUTH' || message.includes('auth') || message.includes('login')) {
      return '/settings?tab=users';
    }
    if (source === 'SYSTEM' || message.includes('performance') || message.includes('memory')) {
      return '/reports?tab=system&subtab=monitor';
    }
    if (source === 'INVENTORY' || message.includes('inventory') || message.includes('stock')) {
      return '/inventory';
    }
    if (source === 'SALES' || message.includes('sales') || message.includes('transaction')) {
      return '/sales';
    }
    if (source === 'CUSTOMER' || message.includes('customer')) {
      return '/customers';
    }

    return '/reports?tab=system&subtab=logs';
  };

  // التعامل مع التوجيه للمشكلة
  const handleNavigateToIssue = (alert: SystemAlert) => {
    const path = getNavigationPath(alert);
    if (path) {
      navigate(path);
      setIsExpanded(false);
    }
  };

  // إزالة التنبيه
  const handleDismissAlert = (alertId: string) => {
    alertService.removeAlert(alertId);
  };

  // تنسيق الوقت النسبي
  const getRelativeTime = (timestamp: Date): string => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `منذ ${days} يوم`;
    if (hours > 0) return `منذ ${hours} ساعة`;
    if (minutes > 0) return `منذ ${minutes} دقيقة`;
    return 'الآن';
  };

  // عدد التنبيهات حسب النوع
  const alertCounts = {
    critical: alerts.filter(a => a.type === 'critical').length,
    error: alerts.filter(a => a.type === 'error').length,
    warning: alerts.filter(a => a.type === 'warning').length,
    info: alerts.filter(a => a.type === 'info').length,
    total: alerts.length
  };

  // عرض مخصص للشريط العلوي
  if (showInHeader) {
    return (
      <>
        <div className={`relative ${className}`}>
          <TopbarTooltip
            text={alertCounts.total > 0 ? `${alertCounts.total} تنبيه جديد` : 'لا توجد تنبيهات'}
            position="bottom"
            variant={alertCounts.total > 0 ? 'warning' : 'default'}
          >
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-500 hover:bg-gray-200 dark:hover:bg-gray-600 hover:text-primary-600 dark:hover:text-primary-400 transition-all duration-200 relative"
              aria-label="التنبيهات"
            >
              <FiBell className="w-4 h-4" />
              {alertCounts.total > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {alertCounts.total > 9 ? '9+' : alertCounts.total}
                </span>
              )}
            </button>
          </TopbarTooltip>
        </div>

        {/* الشريط الجانبي العصري للتنبيهات */}
        {isExpanded && (
          <>
            {/* خلفية شفافة */}
            <div
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[10002] transition-opacity duration-300"
              onClick={() => setIsExpanded(false)}
            />

            {/* الشريط الجانبي */}
            <div className="fixed top-0 right-0 h-full w-96 bg-white dark:bg-gray-800 shadow-2xl z-[10003] transform transition-transform duration-300 ease-out flex flex-col">
              {/* رأس الشريط الجانبي */}
              <div className="flex-shrink-0 flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-gray-800 dark:to-gray-700">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary-100 dark:bg-primary-900/30 rounded-full">
                    <FiBell className="text-primary-600 dark:text-primary-400" />
                  </div>
                  <div>
                    <h2 className="font-bold text-lg text-gray-900 dark:text-gray-100">تنبيهات النظام</h2>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {alertCounts.total > 0 ? `${alertCounts.total} تنبيه` : 'لا توجد تنبيهات'}
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => setIsExpanded(false)}
                  className="p-2 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-full transition-colors"
                >
                  <FaTimes className="text-gray-600 dark:text-gray-400" />
                </button>
              </div>

              {/* محتوى الشريط الجانبي */}
              <div className="flex-1 overflow-hidden flex flex-col min-h-0">
                {/* إحصائيات سريعة */}
                <div className="flex-shrink-0 p-4 bg-gray-50/80 dark:bg-gray-700/30 backdrop-blur-sm">
                  <div className="grid grid-cols-2 gap-3">
                    {alertCounts.critical > 0 && (
                      <div className="bg-red-100 dark:bg-red-900/30 p-3 rounded-lg text-center border border-red-200 dark:border-red-800/50">
                        <div className="text-red-600 dark:text-red-400 font-bold text-lg">{alertCounts.critical}</div>
                        <div className="text-red-700 dark:text-red-300 text-xs">حرجة</div>
                      </div>
                    )}
                    {alertCounts.error > 0 && (
                      <div className="bg-orange-100 dark:bg-orange-900/30 p-3 rounded-lg text-center border border-orange-200 dark:border-orange-800/50">
                        <div className="text-orange-600 dark:text-orange-400 font-bold text-lg">{alertCounts.error}</div>
                        <div className="text-orange-700 dark:text-orange-300 text-xs">أخطاء</div>
                      </div>
                    )}
                    {alertCounts.warning > 0 && (
                      <div className="bg-yellow-100 dark:bg-yellow-900/30 p-3 rounded-lg text-center border border-yellow-200 dark:border-yellow-800/50">
                        <div className="text-yellow-600 dark:text-yellow-400 font-bold text-lg">{alertCounts.warning}</div>
                        <div className="text-yellow-700 dark:text-yellow-300 text-xs">تحذيرات</div>
                      </div>
                    )}
                    {alertCounts.info > 0 && (
                      <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg text-center border border-blue-200 dark:border-blue-800/50">
                        <div className="text-blue-600 dark:text-blue-400 font-bold text-lg">{alertCounts.info}</div>
                        <div className="text-blue-700 dark:text-blue-300 text-xs">معلومات</div>
                      </div>
                    )}
                  </div>
                </div>

                {/* منطقة المحتوى القابلة للتمرير */}
                <div className="flex-1 flex flex-col min-h-0">
                  {/* عنوان وزر عرض الكل */}
                  {alerts.length > 0 && (
                    <div className="flex-shrink-0 px-4 py-3 border-b border-gray-200 dark:border-gray-600 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
                      <div className="flex items-center justify-between">
                        <h3 className="font-semibold text-sm text-gray-700 dark:text-gray-300">آخر التنبيهات</h3>
                        <button
                          onClick={() => {
                            navigate('/reports?tab=system&subtab=system-logs&logtab=alerts&focus=true');
                            setIsExpanded(false);
                          }}
                          className="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 flex items-center gap-1 font-medium transition-colors"
                        >
                          <span>عرض الكل</span>
                          <FaExternalLinkAlt className="w-3 h-3" />
                        </button>
                      </div>
                    </div>
                  )}

                  {/* قائمة التنبيهات */}
                  <div
                    className="flex-1 overflow-y-auto custom-scrollbar-auto bg-white dark:bg-gray-800"
                  >
                    <div className="p-3 pb-6">
                      {alerts.length === 0 ? (
                        <div className="text-center py-8">
                          <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 rounded-full flex items-center justify-center shadow-inner">
                            <FiBell className="text-gray-400 dark:text-gray-500 text-xl" />
                          </div>
                          <h3 className="font-medium text-gray-700 dark:text-gray-300 mb-1">لا توجد تنبيهات</h3>
                          <p className="text-gray-500 dark:text-gray-400 text-xs">النظام يعمل بشكل طبيعي</p>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          {alerts.slice(0, 10).map((alert, index) => (
                            <div
                              key={index}
                              className={`group relative overflow-hidden rounded-lg border transition-all duration-200 hover:shadow-md ${
                                alert.type === 'critical'
                                  ? 'bg-gradient-to-r from-red-50 to-red-100/30 dark:from-red-900/20 dark:to-red-800/10 border-red-200 dark:border-red-700/50' :
                                alert.type === 'error'
                                  ? 'bg-gradient-to-r from-orange-50 to-orange-100/30 dark:from-orange-900/20 dark:to-orange-800/10 border-orange-200 dark:border-orange-700/50' :
                                alert.type === 'warning'
                                  ? 'bg-gradient-to-r from-yellow-50 to-yellow-100/30 dark:from-yellow-900/20 dark:to-yellow-800/10 border-yellow-200 dark:border-yellow-700/50' :
                                  'bg-gradient-to-r from-blue-50 to-blue-100/30 dark:from-blue-900/20 dark:to-blue-800/10 border-blue-200 dark:border-blue-700/50'
                              }`}
                            >
                              {/* شريط جانبي ملون */}
                              <div className={`absolute right-0 top-0 bottom-0 w-0.5 ${
                                alert.type === 'critical' ? 'bg-red-500' :
                                alert.type === 'error' ? 'bg-orange-500' :
                                alert.type === 'warning' ? 'bg-yellow-500' :
                                'bg-blue-500'
                              }`} />

                              <div className="p-3">
                                <div className="flex items-start gap-2">
                                  {/* أيقونة التنبيه مصغرة */}
                                  <div className={`flex-shrink-0 w-7 h-7 rounded-full flex items-center justify-center text-xs ${
                                    alert.type === 'critical' ? 'bg-red-100 dark:bg-red-800/30' :
                                    alert.type === 'error' ? 'bg-orange-100 dark:bg-orange-800/30' :
                                    alert.type === 'warning' ? 'bg-yellow-100 dark:bg-yellow-800/30' :
                                    'bg-blue-100 dark:bg-blue-800/30'
                                  }`}>
                                    {getAlertIcon(alert.type)}
                                  </div>

                                  <div className="flex-1 min-w-0">
                                    {/* العنوان */}
                                    <div className="flex items-start justify-between mb-1">
                                      <h4 className="font-medium text-sm text-gray-900 dark:text-gray-100 leading-tight truncate">
                                        {alert.title}
                                      </h4>

                                      {/* زر الإغلاق */}
                                      <button
                                        onClick={() => {
                                          const newAlerts = alerts.filter((_, i) => i !== index);
                                          setAlerts(newAlerts);
                                        }}
                                        className="flex-shrink-0 w-6 h-6 rounded-full bg-white/80 dark:bg-gray-800/80 hover:bg-white dark:hover:bg-gray-700 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 flex items-center justify-center transition-all duration-200 opacity-0 group-hover:opacity-100 mr-1"
                                        title="إزالة التنبيه"
                                      >
                                        <FaTimes className="w-2.5 h-2.5" />
                                      </button>
                                    </div>

                                    {/* الرسالة */}
                                    <p className="text-xs text-gray-600 dark:text-gray-400 mb-2 line-clamp-2 leading-relaxed">
                                      {alert.message}
                                    </p>

                                    {/* معلومات إضافية */}
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center gap-2">
                                        <span className={`inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium ${
                                          alert.type === 'critical' ? 'bg-red-100 text-red-700 dark:bg-red-800/20 dark:text-red-300' :
                                          alert.type === 'error' ? 'bg-orange-100 text-orange-700 dark:bg-orange-800/20 dark:text-orange-300' :
                                          alert.type === 'warning' ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-800/20 dark:text-yellow-300' :
                                          'bg-blue-100 text-blue-700 dark:bg-blue-800/20 dark:text-blue-300'
                                        }`}>
                                          {alert.type === 'critical' ? 'حرج' :
                                           alert.type === 'error' ? 'خطأ' :
                                           alert.type === 'warning' ? 'تحذير' : 'معلومات'}
                                        </span>
                                        <span className="text-xs text-gray-500 dark:text-gray-400">
                                          {getRelativeTime(alert.timestamp)}
                                        </span>
                                      </div>

                                      {/* زر الإجراء */}
                                      {getNavigationPath(alert) && (
                                        <button
                                          onClick={() => handleNavigateToIssue(alert)}
                                          className={`inline-flex items-center gap-1 px-2 py-1 rounded text-xs font-medium transition-all duration-200 opacity-0 group-hover:opacity-100 ${
                                            alert.type === 'critical' ? 'bg-red-600 hover:bg-red-700 text-white' :
                                            alert.type === 'error' ? 'bg-orange-600 hover:bg-orange-700 text-white' :
                                            alert.type === 'warning' ? 'bg-yellow-600 hover:bg-yellow-700 text-white' :
                                            'bg-blue-600 hover:bg-blue-700 text-white'
                                          }`}
                                          title="الانتقال إلى مكان المشكلة"
                                        >
                                          <FaExternalLinkAlt className="w-2.5 h-2.5" />
                                          <span>حل</span>
                                        </button>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* زر الانتقال لسجلات الأخطاء - ثابت في الأسفل */}
                <div className="flex-shrink-0 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
                  <button
                    onClick={() => {
                      navigate('/reports?tab=system&subtab=system-logs&logtab=logs&focus=true');
                      setIsExpanded(false);
                    }}
                    className="w-full bg-primary-600 hover:bg-primary-700 text-white py-2.5 px-4 rounded-lg text-sm font-medium flex items-center justify-center gap-2 transition-all duration-200 hover:shadow-md"
                  >
                    <FaRoute />
                    <span>عرض سجلات الأخطاء</span>
                  </button>
                </div>
              </div>
            </div>
          </>
        )}
      </>
    );
  }

  if (showInSidebar) {
    return (
      <div className={`${className} bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm`}>
        {/* عرض مصغر في الشريط الجانبي */}
        <div className="p-3">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-sm flex items-center gap-2">
              <FiBell className="text-gray-500" />
              التنبيهات
              {alertCounts.total > 0 && (
                <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                  {alertCounts.total}
                </span>
              )}
            </h3>
          </div>

          {/* آخر 5 تنبيهات مصغرة */}
          {alerts.length > 0 ? (
            <div className="border-t border-gray-200 dark:border-gray-700 pt-3">
              <div
                className="space-y-2 max-h-80 overflow-y-auto custom-scrollbar-auto pr-1"
                style={{
                  minHeight: '200px'
                }}
              >
                {alerts.slice(0, 5).map((alert) => (
                  <div
                    key={alert.id}
                    className={`group relative overflow-hidden rounded-lg border transition-all duration-200 hover:shadow-md hover:scale-[1.02] ${
                      alert.type === 'critical'
                        ? 'bg-gradient-to-r from-red-50 to-red-100/30 dark:from-red-900/20 dark:to-red-800/10 border-red-200 dark:border-red-700/50' :
                      alert.type === 'error'
                        ? 'bg-gradient-to-r from-orange-50 to-orange-100/30 dark:from-orange-900/20 dark:to-orange-800/10 border-orange-200 dark:border-orange-700/50' :
                      alert.type === 'warning'
                        ? 'bg-gradient-to-r from-yellow-50 to-yellow-100/30 dark:from-yellow-900/20 dark:to-yellow-800/10 border-yellow-200 dark:border-yellow-700/50' :
                        'bg-gradient-to-r from-blue-50 to-blue-100/30 dark:from-blue-900/20 dark:to-blue-800/10 border-blue-200 dark:border-blue-700/50'
                    }`}
                  >
                    {/* شريط جانبي ملون */}
                    <div className={`absolute right-0 top-0 bottom-0 w-0.5 ${
                      alert.type === 'critical' ? 'bg-red-500' :
                      alert.type === 'error' ? 'bg-orange-500' :
                      alert.type === 'warning' ? 'bg-yellow-500' :
                      'bg-blue-500'
                    }`} />

                    <div className="p-2">
                      <div className="flex items-start gap-2">
                        {/* أيقونة مصغرة */}
                        <div className={`flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center text-xs ${
                          alert.type === 'critical' ? 'bg-red-100 dark:bg-red-800/30' :
                          alert.type === 'error' ? 'bg-orange-100 dark:bg-orange-800/30' :
                          alert.type === 'warning' ? 'bg-yellow-100 dark:bg-yellow-800/30' :
                          'bg-blue-100 dark:bg-blue-800/30'
                        }`}>
                          {getAlertIcon(alert.type)}
                        </div>

                        <div className="flex-1 min-w-0">
                          {/* العنوان */}
                          <div className="flex items-start justify-between mb-1">
                            <h4 className="font-medium text-xs text-gray-900 dark:text-gray-100 leading-tight truncate">
                              {alert.title}
                            </h4>

                            {/* زر الإغلاق */}
                            <button
                              onClick={() => handleDismissAlert(alert.id)}
                              className="flex-shrink-0 w-4 h-4 rounded-full bg-white/80 dark:bg-gray-800/80 hover:bg-white dark:hover:bg-gray-700 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 flex items-center justify-center transition-all duration-200 opacity-0 group-hover:opacity-100 mr-1"
                              title="إزالة التنبيه"
                            >
                              <FaTimes className="w-2 h-2" />
                            </button>
                          </div>

                          {/* الرسالة */}
                          <p className="text-xs text-gray-600 dark:text-gray-400 mb-1 line-clamp-1 leading-relaxed">
                            {alert.message}
                          </p>

                          {/* معلومات إضافية */}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-1">
                              <span className={`inline-flex items-center px-1 py-0.5 rounded text-xs font-medium ${
                                alert.type === 'critical' ? 'bg-red-100 text-red-700 dark:bg-red-800/20 dark:text-red-300' :
                                alert.type === 'error' ? 'bg-orange-100 text-orange-700 dark:bg-orange-800/20 dark:text-orange-300' :
                                alert.type === 'warning' ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-800/20 dark:text-yellow-300' :
                                'bg-blue-100 text-blue-700 dark:bg-blue-800/20 dark:text-blue-300'
                              }`}>
                                {alert.type === 'critical' ? 'حرج' :
                                 alert.type === 'error' ? 'خطأ' :
                                 alert.type === 'warning' ? 'تحذير' : 'معلومات'}
                              </span>
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                {getRelativeTime(alert.timestamp)}
                              </span>
                            </div>

                            {/* زر الإجراء */}
                            {getNavigationPath(alert) && (
                              <button
                                onClick={() => handleNavigateToIssue(alert)}
                                className={`inline-flex items-center gap-1 px-1.5 py-0.5 rounded text-xs font-medium transition-all duration-200 opacity-0 group-hover:opacity-100 ${
                                  alert.type === 'critical' ? 'bg-red-600 hover:bg-red-700 text-white' :
                                  alert.type === 'error' ? 'bg-orange-600 hover:bg-orange-700 text-white' :
                                  alert.type === 'warning' ? 'bg-yellow-600 hover:bg-yellow-700 text-white' :
                                  'bg-blue-600 hover:bg-blue-700 text-white'
                                }`}
                                title="الانتقال إلى مكان المشكلة"
                              >
                                <FaExternalLinkAlt className="w-2 h-2" />
                                <span>حل</span>
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-100 to-green-200 dark:from-green-800/30 dark:to-green-700/20 rounded-full flex items-center justify-center shadow-inner">
                <FaCheck className="text-green-600 dark:text-green-400 text-xl" />
              </div>
              <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-1">لا توجد تنبيهات</h4>
              <p className="text-xs text-gray-500 dark:text-gray-400">النظام يعمل بشكل طبيعي</p>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      {/* العرض الكامل */}
      <div className="touch-card">
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <h2 className="font-bold text-lg flex items-center gap-2">
              <FiBell className="text-gray-600 dark:text-gray-300" />
              تنبيهات النظام
              {alertCounts.total > 0 && (
                <span className="bg-red-500 text-white text-sm rounded-full px-2 py-1 min-w-[24px] text-center">
                  {alertCounts.total}
                </span>
              )}
            </h2>
          </div>
        </div>

        <div className="p-6">
          {/* قائمة التنبيهات */}
          {alerts.length === 0 ? (
            <div className="text-center py-16">
              <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 rounded-full flex items-center justify-center shadow-inner">
                <FaBellSlash className="text-gray-400 dark:text-gray-500 text-3xl" />
              </div>
              <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-3">لا توجد تنبيهات</h3>
              <p className="text-gray-500 dark:text-gray-400">النظام يعمل بشكل طبيعي ولا توجد مشاكل تحتاج انتباه</p>
            </div>
          ) : (
            <div
              className="space-y-4 max-h-[70vh] overflow-y-auto custom-scrollbar-auto"
              style={{
                minHeight: '400px'
              }}
            >
              {alerts.map((alert) => {
                const navigationPath = getNavigationPath(alert);

                return (
                  <div
                    key={alert.id}
                    className={`group relative overflow-hidden rounded-xl border transition-all duration-300 hover:shadow-xl hover:scale-[1.01] ${
                      alert.type === 'critical'
                        ? 'bg-gradient-to-r from-red-50 to-red-100/50 dark:from-red-900/30 dark:to-red-800/20 border-red-200 dark:border-red-700 shadow-red-100/50 dark:shadow-red-900/20' :
                      alert.type === 'error'
                        ? 'bg-gradient-to-r from-orange-50 to-orange-100/50 dark:from-orange-900/30 dark:to-orange-800/20 border-orange-200 dark:border-orange-700 shadow-orange-100/50 dark:shadow-orange-900/20' :
                      alert.type === 'warning'
                        ? 'bg-gradient-to-r from-yellow-50 to-yellow-100/50 dark:from-yellow-900/30 dark:to-yellow-800/20 border-yellow-200 dark:border-yellow-700 shadow-yellow-100/50 dark:shadow-yellow-900/20' :
                        'bg-gradient-to-r from-blue-50 to-blue-100/50 dark:from-blue-900/30 dark:to-blue-800/20 border-blue-200 dark:border-blue-700 shadow-blue-100/50 dark:shadow-blue-900/20'
                    }`}
                  >
                    {/* شريط جانبي ملون */}
                    <div className={`absolute right-0 top-0 bottom-0 w-1.5 ${
                      alert.type === 'critical' ? 'bg-gradient-to-b from-red-500 to-red-600' :
                      alert.type === 'error' ? 'bg-gradient-to-b from-orange-500 to-orange-600' :
                      alert.type === 'warning' ? 'bg-gradient-to-b from-yellow-500 to-yellow-600' :
                      'bg-gradient-to-b from-blue-500 to-blue-600'
                    }`} />

                    <div className="p-5">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-4 flex-1">
                          {/* أيقونات التنبيه والمصدر */}
                          <div className="flex flex-col items-center gap-2">
                            <div className={`w-12 h-12 rounded-full flex items-center justify-center shadow-sm ${
                              alert.type === 'critical' ? 'bg-red-100 dark:bg-red-800/50' :
                              alert.type === 'error' ? 'bg-orange-100 dark:bg-orange-800/50' :
                              alert.type === 'warning' ? 'bg-yellow-100 dark:bg-yellow-800/50' :
                              'bg-blue-100 dark:bg-blue-800/50'
                            }`}>
                              {getAlertIcon(alert.type)}
                            </div>
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                              alert.type === 'critical' ? 'bg-red-50 dark:bg-red-900/30' :
                              alert.type === 'error' ? 'bg-orange-50 dark:bg-orange-900/30' :
                              alert.type === 'warning' ? 'bg-yellow-50 dark:bg-yellow-900/30' :
                              'bg-blue-50 dark:bg-blue-900/30'
                            }`}>
                              {getSourceIcon(alert.source)}
                            </div>
                          </div>

                          <div className="flex-1">
                            {/* رأس التنبيه */}
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex-1">
                                <h3 className="font-bold text-lg text-gray-900 dark:text-gray-100 mb-2 leading-tight">
                                  {alert.title}
                                </h3>
                                <div className="flex items-center gap-3 mb-3">
                                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                                    alert.type === 'critical' ? 'bg-red-100 text-red-800 dark:bg-red-800/30 dark:text-red-200' :
                                    alert.type === 'error' ? 'bg-orange-100 text-orange-800 dark:bg-orange-800/30 dark:text-orange-200' :
                                    alert.type === 'warning' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800/30 dark:text-yellow-200' :
                                    'bg-blue-100 text-blue-800 dark:bg-blue-800/30 dark:text-blue-200'
                                  }`}>
                                    {alert.type === 'critical' ? 'حرج' :
                                     alert.type === 'error' ? 'خطأ' :
                                     alert.type === 'warning' ? 'تحذير' : 'معلومات'}
                                  </span>
                                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300">
                                    <span className="mr-1">{alert.source}</span>
                                  </span>
                                </div>
                              </div>
                            </div>

                            {/* محتوى التنبيه */}
                            <p className="text-base text-gray-700 dark:text-gray-300 mb-4 leading-relaxed">
                              {alert.message}
                            </p>

                            {/* معلومات إضافية وأزرار */}
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                                <span className="flex items-center gap-2">
                                  <div className="w-2 h-2 rounded-full bg-current opacity-60" />
                                  {getRelativeTime(alert.timestamp)}
                                </span>
                                <span className="flex items-center gap-2">
                                  <div className="w-2 h-2 rounded-full bg-current opacity-60" />
                                  {alert.timestamp.toLocaleString('ar-LY', {
                                    year: 'numeric',
                                    month: 'short',
                                    day: 'numeric',
                                    hour: '2-digit',
                                    minute: '2-digit'
                                  })}
                                </span>
                              </div>

                              {/* أزرار الإجراءات */}
                              <div className="flex gap-3">
                                {navigationPath && (
                                  <button
                                    onClick={() => handleNavigateToIssue(alert)}
                                    className={`inline-flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-md ${
                                      alert.type === 'critical' ? 'bg-red-600 hover:bg-red-700 text-white' :
                                      alert.type === 'error' ? 'bg-orange-600 hover:bg-orange-700 text-white' :
                                      alert.type === 'warning' ? 'bg-yellow-600 hover:bg-yellow-700 text-white' :
                                      'bg-blue-600 hover:bg-blue-700 text-white'
                                    }`}
                                    title="الانتقال إلى مكان المشكلة"
                                  >
                                    <FaExternalLinkAlt className="w-4 h-4" />
                                    <span>حل المشكلة</span>
                                  </button>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* زر الإغلاق */}
                        <button
                          onClick={() => handleDismissAlert(alert.id)}
                          className="flex-shrink-0 w-10 h-10 rounded-full bg-white/80 dark:bg-gray-800/80 hover:bg-white dark:hover:bg-gray-700 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md opacity-0 group-hover:opacity-100"
                          title="إزالة التنبيه"
                        >
                          <FaTimes className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SystemAlerts;
