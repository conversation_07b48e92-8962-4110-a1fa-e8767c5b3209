import React, { useState } from 'react';
import { Fa<PERSON>lock, FaSun, FaMoon } from 'react-icons/fa';

interface TimeSelectorProps {
  value: string;
  onChange: (time: string) => void;
  className?: string;
  label?: string;
  showPresets?: boolean;
  format24?: boolean;
}

const TimeSelector: React.FC<TimeSelectorProps> = ({
  value,
  onChange,
  className = '',
  label,
  showPresets = true,
  format24 = true
}) => {
  const [showCustom, setShowCustom] = useState(false);

  // أوقات محددة مسبقاً شائعة
  const timePresets = [
    { value: '00:00', label: '12:00 ص', icon: <FaMoon className="text-blue-500" /> },
    { value: '02:00', label: '2:00 ص', icon: <FaMoon className="text-blue-600" /> },
    { value: '06:00', label: '6:00 ص', icon: <FaSun className="text-yellow-500" /> },
    { value: '08:00', label: '8:00 ص', icon: <FaSun className="text-yellow-600" /> },
    { value: '12:00', label: '12:00 ظ', icon: <FaSun className="text-orange-500" /> },
    { value: '18:00', label: '6:00 م', icon: <FaSun className="text-orange-600" /> },
    { value: '20:00', label: '8:00 م', icon: <FaMoon className="text-purple-500" /> },
    { value: '22:00', label: '10:00 م', icon: <FaMoon className="text-purple-600" /> }
  ];

  const formatTimeDisplay = (time: string) => {
    if (!format24) {
      const [hours, minutes] = time.split(':');
      const hour = parseInt(hours);
      const ampm = hour >= 12 ? 'م' : 'ص';
      const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
      return `${displayHour}:${minutes} ${ampm}`;
    }
    return time;
  };

  const isPresetSelected = timePresets.some(preset => preset.value === value);

  return (
    <div className={`space-y-3 ${className}`}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          <FaClock className="inline ml-2" />
          {label}
        </label>
      )}

      {showPresets && (
        <div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-3">
            {timePresets.map((preset) => (
              <button
                key={preset.value}
                type="button"
                onClick={() => {
                  onChange(preset.value);
                  setShowCustom(false);
                }}
                className={`
                  p-3 rounded-lg border-2 text-center transition-all duration-200
                  ${value === preset.value
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300'
                  }
                `}
              >
                <div className="flex flex-col items-center">
                  {preset.icon}
                  <span className="text-xs font-medium mt-1">
                    {formatTimeDisplay(preset.value)}
                  </span>
                </div>
              </button>
            ))}
          </div>

          <div className="flex items-center justify-center">
            <button
              type="button"
              onClick={() => setShowCustom(!showCustom)}
              className={`
                text-sm px-4 py-2 rounded-md transition-colors
                ${showCustom || !isPresetSelected
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                }
              `}
            >
              {showCustom ? 'إخفاء الوقت المخصص' : 'وقت مخصص'}
            </button>
          </div>
        </div>
      )}

      {(showCustom || !showPresets || !isPresetSelected) && (
        <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-4 space-x-reverse">
            <input
              type="time"
              value={value}
              onChange={(e) => onChange(e.target.value)}
              className="h-10 px-4 border-2 border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-4 focus:ring-primary-500/20 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-all duration-200 ease-in-out"
            />
            <span className="text-sm text-gray-600 dark:text-gray-400">
              (بتوقيت طرابلس)
            </span>
          </div>
          
          {value && (
            <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
              الوقت المحدد: <span className="font-medium">{formatTimeDisplay(value)}</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TimeSelector;
