import React, { useEffect, useRef } from 'react';
import { Scrollbars } from 'react-custom-scrollbars-2';
import { useTheme } from '../contexts/ThemeContext';

interface AppScrollbarProps {
  children: React.ReactNode;
}

/**
 * مكون شريط التمرير الرئيسي للتطبيق
 * يحل مشكلة شريط التمرير الرئيسي للمتصفح في الوضع المظلم
 * يطبق على كامل التطبيق بدلاً من شريط التمرير الافتراضي
 */
const AppScrollbar: React.FC<AppScrollbarProps> = ({ children }) => {
  const { currentTheme } = useTheme();
  const isDark = currentTheme === 'dark';
  const scrollbarsRef = useRef<Scrollbars>(null);

  // تطبيق إعدادات إضافية عند تغيير الوضع
  useEffect(() => {
    // إخفاء شريط التمرير الافتراضي للمتصفح بطريقة محسنة
    const originalBodyOverflow = document.body.style.overflow;
    const originalHtmlOverflow = document.documentElement.style.overflow;

    document.body.style.overflow = 'hidden';
    document.documentElement.style.overflow = 'hidden';

    // إضافة فئة CSS لضمان إخفاء شريط التمرير الافتراضي
    document.body.classList.add('custom-scrollbar-app');

    return () => {
      // استعادة الإعدادات الأصلية
      document.body.style.overflow = originalBodyOverflow;
      document.documentElement.style.overflow = originalHtmlOverflow;
      document.body.classList.remove('custom-scrollbar-app');
    };
  }, [currentTheme]);

  // تخصيص مظهر مقبض التمرير العمودي
  const renderThumbVertical = ({ style, ...props }: any) => {
    const thumbStyle = {
      ...style,
      backgroundColor: isDark
        ? 'rgba(156, 163, 175, 0.8)' // gray-400 للوضع المظلم - أكثر وضوحاً
        : 'rgba(107, 114, 128, 0.8)', // gray-500 للوضع الفاتح - أكثر وضوحاً
      borderRadius: '6px',
      transition: 'all 0.2s ease-in-out',
      cursor: 'pointer',
      width: '8px',
      right: '2px',
      border: isDark ? '1px solid rgba(75, 85, 99, 0.3)' : '1px solid rgba(156, 163, 175, 0.3)',
    };

    return (
      <div
        {...props}
        style={thumbStyle}
        className="hover:opacity-100 active:opacity-100"
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = isDark
            ? 'rgba(156, 163, 175, 1)'
            : 'rgba(107, 114, 128, 1)';
          e.currentTarget.style.width = '10px';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = isDark
            ? 'rgba(156, 163, 175, 0.8)'
            : 'rgba(107, 114, 128, 0.8)';
          e.currentTarget.style.width = '8px';
        }}
      />
    );
  };

  // تخصيص مظهر مقبض التمرير الأفقي
  const renderThumbHorizontal = ({ style, ...props }: any) => {
    const thumbStyle = {
      ...style,
      backgroundColor: isDark 
        ? 'rgba(75, 85, 99, 0.7)' // gray-600 للوضع المظلم
        : 'rgba(156, 163, 175, 0.7)', // gray-400 للوضع الفاتح
      borderRadius: '8px',
      transition: 'all 0.3s ease-in-out',
      cursor: 'pointer',
      height: '10px',
      bottom: '2px',
    };

    return (
      <div
        {...props}
        style={thumbStyle}
        className="hover:opacity-90 active:opacity-100"
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = isDark 
            ? 'rgba(107, 114, 128, 0.8)' 
            : 'rgba(107, 114, 128, 0.8)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = isDark 
            ? 'rgba(75, 85, 99, 0.7)' 
            : 'rgba(156, 163, 175, 0.7)';
        }}
      />
    );
  };

  // تخصيص مظهر مسار التمرير العمودي
  const renderTrackVertical = ({ style, ...props }: any) => {
    const trackStyle = {
      ...style,
      backgroundColor: isDark
        ? 'rgba(31, 41, 55, 0.3)' // gray-800 للوضع المظلم
        : 'rgba(243, 244, 246, 0.5)', // gray-100 للوضع الفاتح
      borderRadius: '6px',
      right: '0px',
      bottom: '0px',
      top: '0px',
      width: '12px',
      border: isDark
        ? '1px solid rgba(75, 85, 99, 0.2)'
        : '1px solid rgba(229, 231, 235, 0.5)',
    };

    return <div {...props} style={trackStyle} />;
  };

  // تخصيص مظهر مسار التمرير الأفقي
  const renderTrackHorizontal = ({ style, ...props }: any) => {
    const trackStyle = {
      ...style,
      backgroundColor: 'transparent',
      borderRadius: '8px',
      left: '0px',
      right: '0px',
      bottom: '0px',
      height: '14px',
    };

    return <div {...props} style={trackStyle} />;
  };

  // تخصيص منطقة العرض
  const renderView = ({ style, ...props }: any) => {
    const viewStyle = {
      ...style,
      // إزالة الحشو الافتراضي
      paddingRight: 0,
      paddingBottom: 0,
    };

    return <div {...props} style={viewStyle} />;
  };

  return (
    <Scrollbars
      ref={scrollbarsRef}
      style={{
        width: '100%',
        height: '100vh',
        position: 'relative',
      }}
      renderView={renderView}
      renderThumbVertical={renderThumbVertical}
      renderThumbHorizontal={renderThumbHorizontal}
      renderTrackVertical={renderTrackVertical}
      renderTrackHorizontal={renderTrackHorizontal}
      autoHide={true}
      autoHideTimeout={1000}
      autoHideDuration={200}
      thumbMinSize={30}
      universal={true}
      hideTracksWhenNotNeeded={true}
    >
      <div style={{ minHeight: '100vh', width: '100%' }}>
        {children}
      </div>
    </Scrollbars>
  );
};

export default AppScrollbar;
