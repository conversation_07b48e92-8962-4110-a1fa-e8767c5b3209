/**
 * مكون إعدادات النصائح الذكية - SmartTipsSettings
 * يسمح للمستخدمين بتخصيص تجربة النصائح الذكية
 * 
 * الميزات:
 * - تفعيل/تعطيل النصائح
 * - اختيار فئات النصائح المفضلة
 * - تحديد معدل عرض النصائح
 * - مسح تاريخ النصائح
 * - معاينة النصائح
 */

import React, { useState, useEffect } from 'react';
import {
  FiSettings,
  FiToggleLeft,
  FiToggleRight,
  FiRefreshCw,
  FiTrash2,
  FiEye,
  FiBarChart,
  FiZap,
  FiTrendingUp,
  FiSun
} from 'react-icons/fi';
import { FaLightbulb } from 'react-icons/fa';
import { smartTipsService, TipCategory } from '../services/smartTipsService';
import { useSmartTips } from '../hooks/useSmartTips';

interface SmartTipsSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

// خريطة أيقونات الفئات
const categoryIcons = {
  welcome: FiSun,
  productivity: FiZap,
  features: FiSettings,
  shortcuts: FiRefreshCw,
  motivation: FiTrendingUp,
  business: FiBarChart
};

// أسماء الفئات بالعربية
const categoryNames = {
  welcome: 'رسائل الترحيب',
  productivity: 'نصائح الإنتاجية',
  features: 'ميزات النظام',
  shortcuts: 'الاختصارات السريعة',
  motivation: 'رسائل التحفيز',
  business: 'نصائح الأعمال'
};

export const SmartTipsSettings: React.FC<SmartTipsSettingsProps> = ({
  isOpen,
  onClose
}) => {
  const [settings, setSettings] = useState({
    enabled: true,
    showFrequency: 'medium' as 'high' | 'medium' | 'low',
    preferredCategories: ['welcome', 'productivity', 'business'] as TipCategory[]
  });

  const { refreshTip, tipHistory } = useSmartTips({ enabled: false });

  // تحميل الإعدادات عند فتح المودال
  useEffect(() => {
    if (isOpen) {
      loadCurrentSettings();
    }
  }, [isOpen]);

  /**
   * تحميل الإعدادات الحالية
   */
  const loadCurrentSettings = () => {
    try {
      const saved = localStorage.getItem('smartTipsSettings');
      if (saved) {
        const parsed = JSON.parse(saved);
        setSettings({
          enabled: parsed.enabled || true,
          showFrequency: parsed.showFrequency || 'medium',
          preferredCategories: parsed.preferredCategories || ['welcome', 'productivity', 'business']
        });
      }
    } catch (error) {
      console.error('خطأ في تحميل إعدادات النصائح:', error);
    }
  };

  /**
   * حفظ الإعدادات
   */
  const saveSettings = () => {
    smartTipsService.updateSettings(settings);
    onClose();
  };

  /**
   * تفعيل/تعطيل النصائح
   */
  const toggleTips = () => {
    setSettings(prev => ({ ...prev, enabled: !prev.enabled }));
  };

  /**
   * تحديث معدل العرض
   */
  const updateFrequency = (frequency: 'high' | 'medium' | 'low') => {
    setSettings(prev => ({ ...prev, showFrequency: frequency }));
  };

  /**
   * تحديث الفئات المفضلة
   */
  const toggleCategory = (category: TipCategory) => {
    setSettings(prev => ({
      ...prev,
      preferredCategories: prev.preferredCategories.includes(category)
        ? prev.preferredCategories.filter(c => c !== category)
        : [...prev.preferredCategories, category]
    }));
  };

  /**
   * مسح تاريخ النصائح
   */
  const clearHistory = () => {
    if (confirm('هل أنت متأكد من مسح تاريخ النصائح؟ سيتم إعادة عرض جميع النصائح.')) {
      smartTipsService.updateSettings({ tipHistory: {}, lastShownTips: [] });
      alert('تم مسح تاريخ النصائح بنجاح');
    }
  };

  /**
   * معاينة نصيحة
   */
  const previewTip = () => {
    refreshTip();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* رأس المودال */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary-100 dark:bg-primary-900/30 rounded-xl">
              <FaLightbulb className="w-5 h-5 text-primary-600 dark:text-primary-400" />
            </div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100">
              إعدادات النصائح الذكية
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <FiSettings className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* محتوى المودال */}
        <div className="p-6 space-y-6">
          {/* تفعيل/تعطيل النصائح */}
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                تفعيل النصائح الذكية
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                عرض نصائح مفيدة أثناء استخدام النظام
              </p>
            </div>
            <button
              onClick={toggleTips}
              className={`p-1 rounded-full transition-colors ${
                settings.enabled 
                  ? 'text-primary-600 dark:text-primary-400' 
                  : 'text-gray-400'
              }`}
            >
              {settings.enabled ? (
                <FiToggleRight className="w-8 h-8" />
              ) : (
                <FiToggleLeft className="w-8 h-8" />
              )}
            </button>
          </div>

          {/* معدل العرض */}
          <div className="space-y-3">
            <h3 className="font-semibold text-gray-900 dark:text-gray-100">
              معدل عرض النصائح
            </h3>
            <div className="grid grid-cols-3 gap-2">
              {(['high', 'medium', 'low'] as const).map((freq) => (
                <button
                  key={freq}
                  onClick={() => updateFrequency(freq)}
                  className={`p-3 rounded-xl text-sm font-medium transition-all ${
                    settings.showFrequency === freq
                      ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 border-2 border-primary-200 dark:border-primary-700'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 border-2 border-transparent hover:border-gray-300 dark:hover:border-gray-600'
                  }`}
                >
                  {freq === 'high' && 'عالي'}
                  {freq === 'medium' && 'متوسط'}
                  {freq === 'low' && 'منخفض'}
                </button>
              ))}
            </div>
          </div>

          {/* فئات النصائح */}
          <div className="space-y-3">
            <h3 className="font-semibold text-gray-900 dark:text-gray-100">
              فئات النصائح المفضلة
            </h3>
            <div className="space-y-2">
              {(Object.keys(categoryNames) as TipCategory[]).map((category) => {
                const IconComponent = categoryIcons[category];
                const isSelected = settings.preferredCategories.includes(category);
                
                return (
                  <button
                    key={category}
                    onClick={() => toggleCategory(category)}
                    className={`w-full flex items-center gap-3 p-3 rounded-xl transition-all ${
                      isSelected
                        ? 'bg-primary-50 dark:bg-primary-900/20 border-2 border-primary-200 dark:border-primary-700'
                        : 'bg-gray-50 dark:bg-gray-700 border-2 border-transparent hover:border-gray-200 dark:hover:border-gray-600'
                    }`}
                  >
                    <div className={`p-2 rounded-lg ${
                      isSelected 
                        ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400'
                        : 'bg-gray-100 dark:bg-gray-600 text-gray-500 dark:text-gray-400'
                    }`}>
                      <IconComponent className="w-4 h-4" />
                    </div>
                    <span className={`font-medium ${
                      isSelected 
                        ? 'text-primary-700 dark:text-primary-300'
                        : 'text-gray-700 dark:text-gray-300'
                    }`}>
                      {categoryNames[category]}
                    </span>
                  </button>
                );
              })}
            </div>
          </div>

          {/* إحصائيات */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
            <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
              إحصائيات النصائح
            </h3>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              <p>عدد النصائح المعروضة: {Object.keys(tipHistory).length}</p>
              <p>الفئات المفعلة: {settings.preferredCategories.length}</p>
            </div>
          </div>

          {/* أزرار الإجراءات */}
          <div className="flex gap-3">
            <button
              onClick={previewTip}
              className="flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-xl font-medium transition-colors"
            >
              <FiEye className="w-4 h-4" />
              معاينة
            </button>
            <button
              onClick={clearHistory}
              className="flex items-center justify-center gap-2 px-4 py-3 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-xl font-medium transition-colors"
            >
              <FiTrash2 className="w-4 h-4" />
              مسح التاريخ
            </button>
          </div>

          {/* زر الحفظ */}
          <button
            onClick={saveSettings}
            className="w-full px-4 py-3 bg-success-600 hover:bg-success-700 text-white rounded-xl font-medium transition-colors"
          >
            حفظ الإعدادات
          </button>
        </div>
      </div>
    </div>
  );
};

export default SmartTipsSettings;
