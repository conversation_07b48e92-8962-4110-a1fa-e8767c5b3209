import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import { FiLock, FiUser, FiArrowRight, FiShield } from 'react-icons/fi';

interface AuthGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
  showLoginPrompt?: boolean;
}

const AuthGuard: React.FC<AuthGuardProps> = ({
  children,
  requireAuth = true,
  redirectTo = '/login',
  showLoginPrompt = true
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated, isInitialized, isLoading, initialize } = useAuthStore();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      if (!isInitialized) {
        await initialize();
      }
      setIsChecking(false);
    };

    checkAuth();
  }, [isInitialized, initialize]);

  useEffect(() => {
    if (!isChecking && requireAuth && !isAuthenticated) {
      // حفظ الصفحة المطلوبة للعودة إليها بعد تسجيل الدخول
      const returnUrl = location.pathname + location.search;
      localStorage.setItem('returnUrl', returnUrl);
      
      if (redirectTo) {
        navigate(redirectTo);
      }
    }
  }, [isChecking, requireAuth, isAuthenticated, navigate, redirectTo, location]);

  // عرض شاشة التحميل أثناء التحقق من المصادقة
  if (isChecking || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">جاري التحقق من المصادقة...</p>
        </div>
      </div>
    );
  }

  // إذا كانت المصادقة مطلوبة والمستخدم غير مصادق
  if (requireAuth && !isAuthenticated) {
    if (showLoginPrompt) {
      return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-4">
          <div className="max-w-md w-full">
            <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 dark:border-gray-700/50 p-8 text-center">
              {/* أيقونة الأمان */}
              <div className="w-20 h-20 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
                <FiShield className="text-3xl text-primary-600 dark:text-primary-400" />
              </div>

              {/* العنوان */}
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                مطلوب تسجيل الدخول
              </h2>

              {/* الوصف */}
              <p className="text-gray-600 dark:text-gray-400 mb-8 leading-relaxed">
                للوصول إلى نظام إدارة المستودعات، يجب عليك تسجيل الدخول أولاً.
                سيتم توجيهك إلى الصفحة المطلوبة بعد تسجيل الدخول بنجاح.
              </p>

              {/* أزرار الإجراءات */}
              <div className="space-y-4">
                <button
                  onClick={() => navigate('/login')}
                  className="w-full bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl"
                >
                  <FiUser className="ml-2" />
                  تسجيل الدخول
                  <FiArrowRight className="mr-2" />
                </button>

                <button
                  onClick={() => navigate('/')}
                  className="w-full bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium"
                >
                  العودة للصفحة الرئيسية
                </button>
              </div>

              {/* معلومات إضافية */}
              <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <div className="flex items-start">
                  <FiLock className="text-blue-600 dark:text-blue-400 mt-1 ml-2 flex-shrink-0" />
                  <div className="text-right">
                    <p className="text-sm text-blue-800 dark:text-blue-300 font-medium mb-1">
                      نظام آمن
                    </p>
                    <p className="text-xs text-blue-600 dark:text-blue-400">
                      جميع البيانات محمية ومشفرة لضمان الأمان
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }
    
    // إذا لم نرد عرض شاشة تسجيل الدخول، نعيد null
    return null;
  }

  // إذا كان المستخدم مصادق أو المصادقة غير مطلوبة، نعرض المحتوى
  return <>{children}</>;
};

export default AuthGuard;
