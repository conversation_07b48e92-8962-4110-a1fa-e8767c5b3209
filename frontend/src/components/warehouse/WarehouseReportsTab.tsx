/**
 * تبويب تقارير المستودعات
 * يعرض إحصائيات وتقارير شاملة للمستودعات
 */

import React, { useState, useEffect } from 'react';
import {
  FiFileText,
  FiBarChart,
  FiTrendingUp,
  FiTrendingDown,
  FiDownload,
  FiRefreshCw,
  FiPackage,
  FiArrowUp,
  FiArrowDown,
  FiCalendar
} from 'react-icons/fi';
import { useWarehouseStore } from '../../stores/warehouseStore';

interface WarehouseReportsTabProps {
  className?: string;
}

const WarehouseReportsTab: React.FC<WarehouseReportsTabProps> = ({ className = '' }) => {
  const { warehouses, fetchWarehouses } = useWarehouseStore();

  const [selectedWarehouse, setSelectedWarehouse] = useState<number | null>(null);
  const [reportPeriod, setReportPeriod] = useState<'week' | 'month' | 'quarter' | 'year'>('month');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');

  useEffect(() => {
    fetchWarehouses();
  }, []); // إزالة fetchWarehouses من التبعيات لتجنب الحلقة اللا نهائية

  const handleRefresh = () => {
    console.log('تحديث التقارير - قيد التطوير');
  };

  const handleExportReport = (reportType: string) => {
    console.log(`تصدير تقرير ${reportType} - قيد التطوير`);
  };

  // Mock reports data for demonstration
  const mockReportsData = {
    summary: {
      total_warehouses: 5,
      total_products: 1250,
      total_value: 125000,
      low_stock_items: 15,
      out_of_stock_items: 3,
      movements_this_month: 450,
      transfers_this_month: 25
    },
    topProducts: [
      { name: 'منتج تجريبي 1', movements: 120, value: 15000 },
      { name: 'منتج تجريبي 2', movements: 95, value: 12000 },
      { name: 'منتج تجريبي 3', movements: 80, value: 10000 },
      { name: 'منتج تجريبي 4', movements: 65, value: 8500 },
      { name: 'منتج تجريبي 5', movements: 50, value: 7000 }
    ],
    warehousePerformance: [
      { name: 'المستودع الرئيسي', in_movements: 150, out_movements: 120, efficiency: 95 },
      { name: 'مستودع الفرع الأول', in_movements: 80, out_movements: 75, efficiency: 88 },
      { name: 'مستودع الفرع الثاني', in_movements: 60, out_movements: 55, efficiency: 82 },
      { name: 'مستودع الفرع الثالث', in_movements: 45, out_movements: 40, efficiency: 78 }
    ]
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center min-w-0 flex-1">
            <div className="p-3 bg-primary-100 dark:bg-primary-900/30 rounded-xl">
              <FiFileText className="w-6 h-6 text-primary-600 dark:text-primary-400" />
            </div>
            <div className="mr-4 min-w-0 flex-1">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                تقارير المستودعات
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                إحصائيات وتقارير شاملة لأداء المستودعات
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-3 flex-shrink-0">
            <button
              onClick={handleRefresh}
              className="bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium"
            >
              <FiRefreshCw className="ml-2" />
              تحديث
            </button>
            <button
              onClick={() => handleExportReport('comprehensive')}
              className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl"
            >
              <FiDownload className="ml-2" />
              تصدير التقرير
            </button>
          </div>
        </div>
      </div>

      {/* Report Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Warehouse Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              المستودع
            </label>
            <select
              value={selectedWarehouse || ''}
              onChange={(e) => setSelectedWarehouse(e.target.value ? Number(e.target.value) : null)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              <option value="">جميع المستودعات</option>
              {warehouses.map((warehouse) => (
                <option key={warehouse.id} value={warehouse.id}>
                  {warehouse.name}
                </option>
              ))}
            </select>
          </div>

          {/* Report Period */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              الفترة
            </label>
            <select
              value={reportPeriod}
              onChange={(e) => setReportPeriod(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              <option value="week">هذا الأسبوع</option>
              <option value="month">هذا الشهر</option>
              <option value="quarter">هذا الربع</option>
              <option value="year">هذا العام</option>
            </select>
          </div>

          {/* Date From */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              من تاريخ
            </label>
            <input
              type="date"
              value={dateFrom}
              onChange={(e) => setDateFrom(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            />
          </div>

          {/* Date To */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              إلى تاريخ
            </label>
            <input
              type="date"
              value={dateTo}
              onChange={(e) => setDateTo(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            />
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
          <div className="flex items-center">
            <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
              <FiPackage className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي المنتجات</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {mockReportsData.summary.total_products.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
          <div className="flex items-center">
            <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-xl">
              <FiTrendingUp className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">القيمة الإجمالية</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {mockReportsData.summary.total_value.toLocaleString()} ر.س
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
          <div className="flex items-center">
            <div className="p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-xl">
              <FiTrendingDown className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">مخزون قليل</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {mockReportsData.summary.low_stock_items}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
          <div className="flex items-center">
            <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-xl">
              <FiBarChart className="w-6 h-6 text-red-600 dark:text-red-400" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">نفد المخزون</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {mockReportsData.summary.out_of_stock_items}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts and Reports Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Products */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              أكثر المنتجات حركة
            </h3>
            <button
              onClick={() => handleExportReport('top-products')}
              className="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 text-sm font-medium"
            >
              تصدير
            </button>
          </div>
          <div className="space-y-4">
            {mockReportsData.topProducts.map((product, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {product.name}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {product.movements} حركة
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                    {product.value.toLocaleString()} ر.س
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Warehouse Performance */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              أداء المستودعات
            </h3>
            <button
              onClick={() => handleExportReport('warehouse-performance')}
              className="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 text-sm font-medium"
            >
              تصدير
            </button>
          </div>
          <div className="space-y-4">
            {mockReportsData.warehousePerformance.map((warehouse, index) => (
              <div key={index} className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {warehouse.name}
                  </p>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    warehouse.efficiency >= 90
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                      : warehouse.efficiency >= 80
                      ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                      : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                  }`}>
                    {warehouse.efficiency}% كفاءة
                  </span>
                </div>
                <div className="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400">
                  <div className="flex items-center">
                    <FiArrowDown className="w-3 h-3 text-green-600 dark:text-green-400 ml-1" />
                    <span>دخول: {warehouse.in_movements}</span>
                  </div>
                  <div className="flex items-center">
                    <FiArrowUp className="w-3 h-3 text-red-600 dark:text-red-400 ml-1" />
                    <span>خروج: {warehouse.out_movements}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Movement Statistics */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            إحصائيات الحركات الشهرية
          </h3>
          <button
            onClick={() => handleExportReport('movement-statistics')}
            className="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 text-sm font-medium"
          >
            تصدير
          </button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-xl w-fit mx-auto mb-3">
              <FiBarChart className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {mockReportsData.summary.movements_this_month}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              إجمالي الحركات
            </p>
          </div>
          
          <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-xl w-fit mx-auto mb-3">
              <FiTrendingUp className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {mockReportsData.summary.transfers_this_month}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              طلبات التحويل
            </p>
          </div>
          
          <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-xl w-fit mx-auto mb-3">
              <FiCalendar className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
            <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {Math.round(mockReportsData.summary.movements_this_month / 30)}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              متوسط يومي
            </p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          تقارير سريعة
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button
            onClick={() => handleExportReport('inventory-summary')}
            className="p-4 text-left bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
          >
            <FiPackage className="w-5 h-5 text-blue-600 dark:text-blue-400 mb-2" />
            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
              ملخص المخزون
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              تقرير شامل للمخزون الحالي
            </p>
          </button>

          <button
            onClick={() => handleExportReport('low-stock')}
            className="p-4 text-left bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
          >
            <FiTrendingDown className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mb-2" />
            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
              المخزون المنخفض
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              المنتجات التي تحتاج تجديد
            </p>
          </button>

          <button
            onClick={() => handleExportReport('movement-history')}
            className="p-4 text-left bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
          >
            <FiBarChart className="w-5 h-5 text-green-600 dark:text-green-400 mb-2" />
            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
              تاريخ الحركات
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              سجل كامل لحركات المستودع
            </p>
          </button>

          <button
            onClick={() => handleExportReport('transfer-analysis')}
            className="p-4 text-left bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
          >
            <FiTrendingUp className="w-5 h-5 text-purple-600 dark:text-purple-400 mb-2" />
            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
              تحليل التحويلات
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              إحصائيات طلبات التحويل
            </p>
          </button>
        </div>
      </div>
    </div>
  );
};

export default WarehouseReportsTab;
