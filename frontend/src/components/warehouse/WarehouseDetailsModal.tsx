/**
 * نافذة تفاصيل المستودع
 * تعرض جميع معلومات المستودع بشكل مفصل
 */

import React from 'react';
import {
  FiMapPin,
  FiUser,
  FiMail,
  FiPhone,
  FiPackage,
  FiStar,
  FiBarChart,
  FiCalendar,
  FiActivity
} from 'react-icons/fi';
import Modal from '../Modal';
import { Warehouse } from '../../services/warehouseService';
import { FormattedDateTime } from '../FormattedDateTime';

interface WarehouseDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  warehouse: Warehouse | null;
}

const WarehouseDetailsModal: React.FC<WarehouseDetailsModalProps> = ({
  isOpen,
  onClose,
  warehouse
}) => {
  if (!warehouse) return null;

  const getCapacityPercentage = (warehouse: Warehouse): number => {
    if (!warehouse.capacity_limit || warehouse.capacity_limit === 0) return 0;
    return Math.round(((warehouse.current_capacity || 0) / warehouse.capacity_limit) * 100);
  };

  const getCapacityColor = (warehouse: Warehouse): string => {
    const percentage = getCapacityPercentage(warehouse);
    if (percentage >= 90) return 'bg-danger-500';
    if (percentage >= 75) return 'bg-warning-500';
    if (percentage >= 50) return 'bg-primary-500';
    return 'bg-success-500';
  };



  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`تفاصيل المستودع: ${warehouse.name}`}
      size="lg"
    >
      <div className="space-y-6">
        {/* Header with Status */}
        <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-xl">
          <div className="flex items-center gap-3">
            <div className={`p-3 rounded-lg ${
              warehouse.is_main
                ? 'bg-warning-100 dark:bg-warning-900/30 text-warning-600 dark:text-warning-400'
                : warehouse.is_active
                  ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400'
            }`}>
              {warehouse.is_main ? (
                <FiStar className="w-6 h-6" />
              ) : (
                <FiPackage className="w-6 h-6" />
              )}
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">
                {warehouse.name}
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 font-mono">
                {warehouse.code}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <span className={`inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium ${
              warehouse.is_active
                ? 'bg-success-100 text-success-700 dark:bg-success-900/30 dark:text-success-400'
                : 'bg-danger-100 text-danger-700 dark:bg-danger-900/30 dark:text-danger-400'
            }`}>
              <div className={`w-2 h-2 rounded-full ml-2 ${
                warehouse.is_active ? 'bg-success-500' : 'bg-danger-500'
              }`}></div>
              {warehouse.is_active ? 'نشط' : 'غير نشط'}
            </span>

            {warehouse.is_main && (
              <span className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-warning-100 text-warning-700 dark:bg-warning-900/30 dark:text-warning-400">
                <FiStar className="w-3 h-3 ml-1" />
                رئيسي
              </span>
            )}
          </div>
        </div>

        {/* Basic Information */}
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
            المعلومات الأساسية
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Address */}
            <div className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="p-2 bg-primary-100 dark:bg-primary-900/30 rounded-lg">
                <FiMapPin className="w-4 h-4 text-primary-600 dark:text-primary-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">العنوان</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {warehouse.address || 'لم يتم تحديد العنوان'}
                </p>
              </div>
            </div>

            {/* Manager */}
            <div className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="p-2 bg-secondary-100 dark:bg-secondary-900/30 rounded-lg">
                <FiUser className="w-4 h-4 text-secondary-600 dark:text-secondary-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">المدير</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {warehouse.manager_name || 'لم يتم تحديد المدير'}
                </p>
              </div>
            </div>

            {/* Phone */}
            <div className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="p-2 bg-success-100 dark:bg-success-900/30 rounded-lg">
                <FiPhone className="w-4 h-4 text-success-600 dark:text-success-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">الهاتف</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {warehouse.phone || 'لم يتم تحديد الهاتف'}
                </p>
              </div>
            </div>

            {/* Email */}
            <div className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="p-2 bg-info-100 dark:bg-info-900/30 rounded-lg">
                <FiMail className="w-4 h-4 text-info-600 dark:text-info-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">البريد الإلكتروني</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {warehouse.email || 'لم يتم تحديد البريد الإلكتروني'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Capacity Information */}
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
            معلومات السعة التخزينية
          </h4>

          {warehouse.capacity_limit ? (
            <div className="p-4 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700/50 dark:to-gray-600/50 rounded-xl border border-gray-200 dark:border-gray-600">
              <div className="flex justify-between items-center mb-3">
                <div className="flex items-center gap-2">
                  <div className="p-2 bg-primary-500 rounded-lg">
                    <FiBarChart className="w-4 h-4 text-white" />
                  </div>
                  <span className="text-sm font-bold text-gray-700 dark:text-gray-300">السعة التخزينية</span>
                </div>
                <span className="text-lg font-bold text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800 px-3 py-1 rounded-lg shadow-sm">
                  {getCapacityPercentage(warehouse)}%
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-3 mb-3 shadow-inner">
                <div
                  className={`h-3 rounded-full transition-all duration-500 shadow-sm ${getCapacityColor(warehouse)}`}
                  style={{ width: `${getCapacityPercentage(warehouse)}%` }}
                />
              </div>
              <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 font-medium">
                <span>المستخدم: {warehouse.current_capacity || 0}</span>
                <span>الإجمالي: {warehouse.capacity_limit}</span>
              </div>
            </div>
          ) : (
            <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-xl text-center">
              <FiPackage className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500 dark:text-gray-400">لم يتم تحديد حد السعة التخزينية</p>
            </div>
          )}
        </div>

        {/* Timestamps */}
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
            معلومات التوقيت
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Created At */}
            <div className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="p-2 bg-success-100 dark:bg-success-900/30 rounded-lg">
                <FiCalendar className="w-4 h-4 text-success-600 dark:text-success-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">تاريخ الإنشاء</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {warehouse.created_at ? (
                    <FormattedDateTime
                      date={warehouse.created_at}
                      showTime={true}
                      fallback="غير محدد"
                    />
                  ) : (
                    'غير محدد'
                  )}
                </p>
              </div>
            </div>

            {/* Updated At */}
            <div className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="p-2 bg-warning-100 dark:bg-warning-900/30 rounded-lg">
                <FiActivity className="w-4 h-4 text-warning-600 dark:text-warning-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">آخر تحديث</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {warehouse.updated_at ? (
                    <FormattedDateTime
                      date={warehouse.updated_at}
                      showTime={true}
                      fallback="غير محدد"
                    />
                  ) : (
                    'غير محدد'
                  )}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Close Button */}
        <div className="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-6 py-3 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 rounded-xl transition-colors duration-200 font-medium"
          >
            إغلاق
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default WarehouseDetailsModal;
