/**
 * جدول مخزون المستودع
 * يعرض قائمة المنتجات في المستودع مع معلومات المخزون
 */

import React, { useState, useEffect } from 'react';
import { 
  FiPackage, 
  FiEdit, 
  FiAlertTriangle, 
  FiCheckCircle, 
  FiXCircle,
  FiSearch,
  FiFilter,
  FiDownload,
  FiRefreshCw
} from 'react-icons/fi';
import { useWarehouseInventoryStore, warehouseInventorySelectors } from '../../stores/warehouseInventoryStore';
import { WarehouseInventory, InventoryFilters } from '../../services/warehouseInventoryService';

interface WarehouseInventoryTableProps {
  warehouseId: number;
  warehouseName: string;
}

const WarehouseInventoryTable: React.FC<WarehouseInventoryTableProps> = ({
  warehouseId,
  warehouseName
}) => {
  const {
    fetchWarehouseInventory,
    loading,
    error,
    clearError
  } = useWarehouseInventoryStore();

  const inventory = warehouseInventorySelectors.getWarehouseInventory(
    useWarehouseInventoryStore.getState(),
    warehouseId
  );

  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<InventoryFilters>({
    low_stock_only: false
  });
  const [selectedItems, setSelectedItems] = useState<number[]>([]);

  useEffect(() => {
    fetchWarehouseInventory(warehouseId, filters);
  }, [warehouseId, filters]);

  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => clearError(), 5000);
      return () => clearTimeout(timer);
    }
  }, [error, clearError]);

  // فلترة المخزون حسب البحث
  const filteredInventory = inventory.filter(item => {
    const matchesSearch = !searchQuery || 
      item.product_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.product_barcode?.toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesSearch;
  });

  const handleRefresh = () => {
    fetchWarehouseInventory(warehouseId, filters);
  };

  const handleFilterChange = (newFilters: Partial<InventoryFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const getStockStatusIcon = (status: string) => {
    switch (status) {
      case 'normal':
        return <FiCheckCircle className="w-4 h-4 text-green-500" />;
      case 'low':
        return <FiAlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'out_of_stock':
        return <FiXCircle className="w-4 h-4 text-red-500" />;
      default:
        return <FiPackage className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStockStatusText = (status: string) => {
    switch (status) {
      case 'normal': return 'طبيعي';
      case 'low': return 'قليل';
      case 'out_of_stock': return 'نفد';
      default: return 'غير محدد';
    }
  };

  const getStockStatusColor = (status: string) => {
    switch (status) {
      case 'normal': return 'bg-green-100 text-green-800';
      case 'low': return 'bg-yellow-100 text-yellow-800';
      case 'out_of_stock': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleSelectItem = (itemId: number) => {
    setSelectedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const handleSelectAll = () => {
    if (selectedItems.length === filteredInventory.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(filteredInventory.map(item => item.id));
    }
  };

  // حساب الإحصائيات
  const stats = {
    total: inventory.length,
    normal: inventory.filter(item => item.stock_status === 'normal').length,
    low: inventory.filter(item => item.stock_status === 'low').length,
    out_of_stock: inventory.filter(item => item.stock_status === 'out_of_stock').length,
    total_value: inventory.reduce((sum, item) => sum + (item.item_value || 0), 0)
  };

  return (
    <div className="bg-white rounded-lg shadow border">
      {/* Header */}
      <div className="p-6 border-b">
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              مخزون {warehouseName}
            </h2>
            <p className="text-gray-600 mt-1">
              إجمالي {stats.total} منتج - قيمة إجمالية: {stats.total_value.toLocaleString()} د.ل
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={handleRefresh}
              disabled={loading}
              className="p-2 text-gray-600 hover:text-gray-800 transition-colors disabled:opacity-50"
              title="تحديث"
            >
              <FiRefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            </button>
            
            <button
              className="p-2 text-gray-600 hover:text-gray-800 transition-colors"
              title="تصدير"
            >
              <FiDownload className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
          <div className="bg-green-50 p-3 rounded-lg">
            <div className="flex items-center gap-2">
              <FiCheckCircle className="w-4 h-4 text-green-600" />
              <span className="text-sm text-green-800">طبيعي</span>
            </div>
            <p className="text-lg font-semibold text-green-900 mt-1">{stats.normal}</p>
          </div>
          
          <div className="bg-yellow-50 p-3 rounded-lg">
            <div className="flex items-center gap-2">
              <FiAlertTriangle className="w-4 h-4 text-yellow-600" />
              <span className="text-sm text-yellow-800">قليل</span>
            </div>
            <p className="text-lg font-semibold text-yellow-900 mt-1">{stats.low}</p>
          </div>
          
          <div className="bg-red-50 p-3 rounded-lg">
            <div className="flex items-center gap-2">
              <FiXCircle className="w-4 h-4 text-red-600" />
              <span className="text-sm text-red-800">نفد</span>
            </div>
            <p className="text-lg font-semibold text-red-900 mt-1">{stats.out_of_stock}</p>
          </div>
          
          <div className="bg-blue-50 p-3 rounded-lg">
            <div className="flex items-center gap-2">
              <FiPackage className="w-4 h-4 text-blue-600" />
              <span className="text-sm text-blue-800">الإجمالي</span>
            </div>
            <p className="text-lg font-semibold text-blue-900 mt-1">{stats.total}</p>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="p-4 border-b bg-gray-50">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="البحث في المنتجات..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          
          {/* Filters */}
          <div className="flex items-center gap-4">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={filters.low_stock_only}
                onChange={(e) => handleFilterChange({ low_stock_only: e.target.checked })}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">قليل المخزون فقط</span>
            </label>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="p-4 bg-red-50 border-b border-red-200">
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}

      {/* Loading */}
      {loading && (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Table */}
      {!loading && (
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    checked={selectedItems.length === filteredInventory.length && filteredInventory.length > 0}
                    onChange={handleSelectAll}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  المنتج
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الكمية
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  محجوز
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  متاح
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الحد الأدنى
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الموقع
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  القيمة
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredInventory.map((item) => (
                <tr key={item.id} className="hover:bg-gray-50">
                  <td className="px-4 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      checked={selectedItems.includes(item.id)}
                      onChange={() => handleSelectItem(item.id)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </td>
                  
                  <td className="px-4 py-4">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {item.product_name}
                      </div>
                      <div className="text-sm text-gray-500 font-mono">
                        {item.product_barcode}
                      </div>
                    </div>
                  </td>
                  
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className="text-sm font-medium text-gray-900">
                      {item.quantity}
                    </span>
                  </td>
                  
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-600">
                      {item.reserved_quantity}
                    </span>
                  </td>
                  
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className="text-sm font-medium text-gray-900">
                      {item.available_quantity}
                    </span>
                  </td>
                  
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-600">
                      {item.min_stock_level}
                    </span>
                  </td>
                  
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getStockStatusColor(item.stock_status || 'normal')}`}>
                      {getStockStatusIcon(item.stock_status || 'normal')}
                      {getStockStatusText(item.stock_status || 'normal')}
                    </span>
                  </td>
                  
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-600">
                      {item.location_code || '-'}
                    </span>
                  </td>
                  
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className="text-sm font-medium text-gray-900">
                      {(item.item_value || 0).toLocaleString()} د.ل
                    </span>
                  </td>
                  
                  <td className="px-4 py-4 whitespace-nowrap">
                    <button
                      className="text-blue-600 hover:text-blue-800 transition-colors"
                      title="تعديل"
                    >
                      <FiEdit className="w-4 h-4" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Empty State */}
      {!loading && filteredInventory.length === 0 && (
        <div className="text-center py-12">
          <FiPackage className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            لا توجد منتجات
          </h3>
          <p className="text-gray-600">
            {searchQuery ? 'لم يتم العثور على منتجات تطابق البحث' : 'لا توجد منتجات في هذا المستودع'}
          </p>
        </div>
      )}

      {/* Selected Actions */}
      {selectedItems.length > 0 && (
        <div className="p-4 bg-blue-50 border-t">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-800">
              تم تحديد {selectedItems.length} عنصر
            </span>
            <div className="flex items-center gap-2">
              <button className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors">
                تحديث مجمع
              </button>
              <button className="px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700 transition-colors">
                تصدير المحدد
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default WarehouseInventoryTable;
