/**
 * نافذة تعديل المستودع
 * تتيح للمستخدم تعديل بيانات المستودع الموجود
 */

import React, { useState, useEffect } from 'react';
import { FiSave, FiMapPin, FiUser, FiMail, FiPhone, FiPackage } from 'react-icons/fi';
import Modal from '../Modal';
import { TextInput, NumberInput } from '../inputs';
import ToggleSwitch from '../ToggleSwitch';
import { useWarehouseStore } from '../../stores/warehouseStore';
import { Warehouse, WarehouseUpdate } from '../../services/warehouseService';

interface EditWarehouseModalProps {
  isOpen: boolean;
  onClose: () => void;
  warehouse: Warehouse | null;
  onSuccess?: () => void;
}

const EditWarehouseModal: React.FC<EditWarehouseModalProps> = ({
  isOpen,
  onClose,
  warehouse,
  onSuccess
}) => {
  const { updateWarehouse, updating, error } = useWarehouseStore();

  const [formData, setFormData] = useState<WarehouseUpdate>({
    name: '',
    code: '',
    address: '',
    phone: '',
    manager_name: '',
    email: '',
    is_main: false,
    is_active: true,
    capacity_limit: undefined,
    current_capacity: 0
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // تحديث البيانات عند تغيير المستودع
  useEffect(() => {
    if (warehouse && isOpen) {
      setFormData({
        name: warehouse.name || '',
        code: warehouse.code || '',
        address: warehouse.address || '',
        phone: warehouse.phone || '',
        manager_name: warehouse.manager_name || '',
        email: warehouse.email || '',
        is_main: warehouse.is_main || false,
        is_active: warehouse.is_active !== undefined ? warehouse.is_active : true,
        capacity_limit: warehouse.capacity_limit || undefined,
        current_capacity: warehouse.current_capacity || 0
      });
      setValidationErrors({});
    }
  }, [warehouse, isOpen]);

  const handleInputChange = (field: keyof WarehouseUpdate, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // مسح خطأ التحقق عند التعديل
    if (validationErrors[field]) {
      setValidationErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name?.trim()) {
      errors.name = 'اسم المستودع مطلوب';
    }

    if (!formData.code?.trim()) {
      errors.code = 'كود المستودع مطلوب';
    } else if (!/^[A-Z0-9\-_]+$/.test(formData.code)) {
      errors.code = 'كود المستودع يجب أن يحتوي على أحرف إنجليزية كبيرة وأرقام فقط';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'البريد الإلكتروني غير صحيح';
    }

    if (formData.capacity_limit !== undefined && formData.capacity_limit < 0) {
      errors.capacity_limit = 'حد السعة يجب أن يكون رقماً موجباً';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!warehouse || !validateForm()) {
      return;
    }

    // تحويل الكود إلى أحرف كبيرة
    const dataToSubmit = {
      ...formData,
      code: formData.code?.toUpperCase(),
      // إزالة القيم الفارغة
      address: formData.address?.trim() || undefined,
      phone: formData.phone?.trim() || undefined,
      manager_name: formData.manager_name?.trim() || undefined,
      email: formData.email?.trim() || undefined,
      capacity_limit: formData.capacity_limit || undefined
    };

    const success = await updateWarehouse(warehouse.id, dataToSubmit);

    if (success) {
      onSuccess?.();
      onClose();
    }
  };

  const handleClose = () => {
    if (!updating) {
      setValidationErrors({});
      onClose();
    }
  };

  if (!warehouse) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={`تعديل المستودع: ${warehouse.name}`}
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Error Message */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-xl">
            {error}
          </div>
        )}

        {/* Basic Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
            المعلومات الأساسية
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Name */}
            <TextInput
              label="اسم المستودع"
              name="name"
              value={formData.name || ''}
              onChange={(value) => handleInputChange('name', value)}
              placeholder="أدخل اسم المستودع"
              required
              disabled={updating}
              error={validationErrors.name}
            />

            {/* Code */}
            <TextInput
              label="كود المستودع"
              name="code"
              value={formData.code || ''}
              onChange={(value) => handleInputChange('code', value.toUpperCase())}
              placeholder="WH-001"
              required
              disabled={updating}
              error={validationErrors.code}
            />
          </div>

          {/* Address */}
          <TextInput
            label="العنوان"
            name="address"
            value={formData.address || ''}
            onChange={(value) => handleInputChange('address', value)}
            placeholder="أدخل عنوان المستودع"
            disabled={updating}
            icon={<FiMapPin className="w-4 h-4" />}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Manager Name */}
            <TextInput
              label="اسم المدير"
              name="manager_name"
              value={formData.manager_name || ''}
              onChange={(value) => handleInputChange('manager_name', value)}
              placeholder="أدخل اسم مدير المستودع"
              disabled={updating}
              icon={<FiUser className="w-4 h-4" />}
            />

            {/* Phone */}
            <TextInput
              label="رقم الهاتف"
              name="phone"
              type="tel"
              value={formData.phone || ''}
              onChange={(value) => handleInputChange('phone', value)}
              placeholder="أدخل رقم الهاتف"
              disabled={updating}
              icon={<FiPhone className="w-4 h-4" />}
            />
          </div>

          {/* Email */}
          <TextInput
            label="البريد الإلكتروني"
            name="email"
            type="email"
            value={formData.email || ''}
            onChange={(value) => handleInputChange('email', value)}
            placeholder="أدخل البريد الإلكتروني"
            disabled={updating}
            error={validationErrors.email}
            icon={<FiMail className="w-4 h-4" />}
          />
        </div>

        {/* Capacity Settings */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
            إعدادات السعة
          </h3>

          <NumberInput
            label="حد السعة التخزينية"
            name="capacity_limit"
            value={formData.capacity_limit ? formData.capacity_limit.toString() : ''}
            onChange={(value) => handleInputChange('capacity_limit', value ? parseInt(value) : undefined)}
            placeholder="أدخل حد السعة (اختياري)"
            min={0}
            step="1"
            disabled={updating}
            error={validationErrors.capacity_limit}
            icon={<FiPackage className="w-4 h-4" />}
            allowDecimals={false}
          />
        </div>

        {/* Status Settings */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
            إعدادات الحالة
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Is Active */}
            <div className={`bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-10 flex items-center ${updating ? 'opacity-50 pointer-events-none' : ''}`}>
              <ToggleSwitch
                id="is_active"
                checked={formData.is_active || false}
                onChange={(checked) => !updating && handleInputChange('is_active', checked)}
                label="مستودع نشط"
                className="w-full"
              />
            </div>

            {/* Is Main - disabled if already main or if this would make no main warehouse */}
            <div className={`bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-10 flex items-center ${(updating || warehouse.is_main) ? 'opacity-50 pointer-events-none' : ''}`}>
              <ToggleSwitch
                id="is_main"
                checked={formData.is_main || false}
                onChange={(checked) => !(updating || warehouse.is_main) && handleInputChange('is_main', checked)}
                label="مستودع رئيسي"
                className="w-full"
              />
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            onClick={handleClose}
            disabled={updating}
            className="px-6 py-3 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-xl transition-colors duration-200 font-medium"
          >
            إلغاء
          </button>
          <button
            type="submit"
            disabled={updating}
            className="px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-xl transition-colors duration-200 font-medium flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {updating ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                جاري الحفظ...
              </>
            ) : (
              <>
                <FiSave className="w-4 h-4" />
                حفظ التغييرات
              </>
            )}
          </button>
        </div>
      </form>
    </Modal>
  );
};

export default EditWarehouseModal;
