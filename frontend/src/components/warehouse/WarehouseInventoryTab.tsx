/**
 * تبويب مخزون المستودعات
 * يعرض مخزون المنتجات في المستودعات المختلفة
 */

import React, { useState, useEffect } from 'react';
import {
  FiPackage,
  FiBarChart,
  FiDownload
} from 'react-icons/fi';
import { useWarehouseStore } from '../../stores/warehouseStore';
import { useWarehouseInventoryStore } from '../../stores/warehouseInventoryStore';
import { SelectInput } from '../inputs';
import SearchInput from '../SearchInput';
import TablePagination from '../catalog/TablePagination';

interface WarehouseInventoryTabProps {
  className?: string;
}

const WarehouseInventoryTab: React.FC<WarehouseInventoryTabProps> = ({ className = '' }) => {
  const { warehouses, fetchWarehouses } = useWarehouseStore();
  const warehouseInventoryStore = useWarehouseInventoryStore();

  const [selectedWarehouse, setSelectedWarehouse] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'normal' | 'low' | 'out'>('all');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  useEffect(() => {
    fetchWarehouses();
  }, []); // إزالة fetchWarehouses من التبعيات لتجنب الحلقة اللا نهائية

  useEffect(() => {
    if (selectedWarehouse && typeof warehouseInventoryStore.fetchWarehouseInventory === 'function') {
      // تعطيل مؤقت لاستدعاء API المخزون حتى يتم إصلاح مشكلة 403
      // warehouseInventoryStore.fetchWarehouseInventory(selectedWarehouse);
      console.log('تم اختيار المستودع:', selectedWarehouse, '- استدعاء API المخزون معطل مؤقتاً');
    }
  }, [selectedWarehouse]); // إزالة warehouseInventoryStore من التبعيات لتجنب الحلقة اللا نهائية

  const handleExport = () => {
    console.log('تصدير بيانات المخزون - قيد التطوير');
  };

  // Mock inventory data for demonstration
  const mockInventoryData = [
    {
      id: 1,
      product_name: 'منتج تجريبي 1',
      product_code: 'PROD001',
      current_stock: 150,
      min_stock: 50,
      max_stock: 500,
      unit: 'قطعة',
      warehouse_name: 'المستودع الرئيسي',
      status: 'normal' as const
    },
    {
      id: 2,
      product_name: 'منتج تجريبي 2',
      product_code: 'PROD002',
      current_stock: 25,
      min_stock: 50,
      max_stock: 300,
      unit: 'كيلو',
      warehouse_name: 'المستودع الرئيسي',
      status: 'low' as const
    },
    {
      id: 3,
      product_name: 'منتج تجريبي 3',
      product_code: 'PROD003',
      current_stock: 0,
      min_stock: 20,
      max_stock: 200,
      unit: 'لتر',
      warehouse_name: 'المستودع الفرعي',
      status: 'out' as const
    },
    {
      id: 4,
      product_name: 'منتج تجريبي 4',
      product_code: 'PROD004',
      current_stock: 75,
      min_stock: 30,
      max_stock: 150,
      unit: 'قطعة',
      warehouse_name: 'المستودع الفرعي',
      status: 'normal' as const
    },
    {
      id: 5,
      product_name: 'منتج تجريبي 5',
      product_code: 'PROD005',
      current_stock: 10,
      min_stock: 25,
      max_stock: 100,
      unit: 'علبة',
      warehouse_name: 'مستودع الطوارئ',
      status: 'low' as const
    },
    {
      id: 6,
      product_name: 'منتج تجريبي 6',
      product_code: 'PROD006',
      current_stock: 200,
      min_stock: 100,
      max_stock: 500,
      unit: 'قطعة',
      warehouse_name: 'المستودع الرئيسي',
      status: 'normal' as const
    },
    {
      id: 7,
      product_name: 'منتج تجريبي 7',
      product_code: 'PROD007',
      current_stock: 0,
      min_stock: 50,
      max_stock: 200,
      unit: 'كيلو',
      warehouse_name: 'المستودع الفرعي',
      status: 'out' as const
    },
    {
      id: 8,
      product_name: 'منتج تجريبي 8',
      product_code: 'PROD008',
      current_stock: 45,
      min_stock: 30,
      max_stock: 150,
      unit: 'لتر',
      warehouse_name: 'مستودع الطوارئ',
      status: 'normal' as const
    },
    {
      id: 9,
      product_name: 'منتج تجريبي 9',
      product_code: 'PROD009',
      current_stock: 15,
      min_stock: 40,
      max_stock: 120,
      unit: 'علبة',
      warehouse_name: 'المستودع الرئيسي',
      status: 'low' as const
    },
    {
      id: 10,
      product_name: 'منتج تجريبي 10',
      product_code: 'PROD010',
      current_stock: 80,
      min_stock: 20,
      max_stock: 100,
      unit: 'قطعة',
      warehouse_name: 'المستودع الفرعي',
      status: 'normal' as const
    },
    {
      id: 11,
      product_name: 'منتج تجريبي 11',
      product_code: 'PROD011',
      current_stock: 5,
      min_stock: 25,
      max_stock: 80,
      unit: 'كيلو',
      warehouse_name: 'مستودع الطوارئ',
      status: 'low' as const
    },
    {
      id: 12,
      product_name: 'منتج تجريبي 12',
      product_code: 'PROD012',
      current_stock: 120,
      min_stock: 50,
      max_stock: 200,
      unit: 'لتر',
      warehouse_name: 'المستودع الرئيسي',
      status: 'normal' as const
    }
  ];

  // Filter data
  const allFilteredInventory = mockInventoryData.filter(item => {
    const matchesSearch = item.product_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.product_code.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.warehouse_name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || item.status === statusFilter;

    // إذا لم يتم اختيار مستودع، اعرض جميع المنتجات
    // إذا تم اختيار مستودع، اعرض منتجات ذلك المستودع فقط
    const matchesWarehouse = !selectedWarehouse ||
      (selectedWarehouse && warehouses.find(w => w.id === selectedWarehouse)?.name === item.warehouse_name);

    return matchesSearch && matchesStatus && matchesWarehouse;
  });

  // Pagination calculations
  const totalItems = allFilteredInventory.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const filteredInventory = allFilteredInventory.slice(startIndex, endIndex);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, statusFilter, selectedWarehouse]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'normal':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'low':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'out':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'normal':
        return 'طبيعي';
      case 'low':
        return 'قليل';
      case 'out':
        return 'نفد';
      default:
        return 'غير محدد';
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Inventory Summary Cards - تظهر دائماً فوق الهيدر */}
      {(
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
            <div className="flex items-center">
              <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
                <FiPackage className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي المنتجات</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  {allFilteredInventory.length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
            <div className="flex items-center">
              <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-xl">
                <FiBarChart className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">مخزون طبيعي</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  {allFilteredInventory.filter(item => item.status === 'normal').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
            <div className="flex items-center">
              <div className="p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-xl">
                <FiBarChart className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">مخزون قليل</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  {allFilteredInventory.filter(item => item.status === 'low').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
            <div className="flex items-center">
              <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-xl">
                <FiBarChart className="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">مخزون نفد</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  {allFilteredInventory.filter(item => item.status === 'out').length}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Data Table with Header and Filters - نفس نمط تبويب المستودعات */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 overflow-hidden">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center p-6 gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <div className="min-w-0 flex-1">
                <h2 className="text-lg sm:text-xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FiBarChart className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                  <span className="truncate">مخزون المستودعات</span>
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  عرض وإدارة مخزون المنتجات في المستودعات
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3 flex-shrink-0">
              <button
                onClick={handleExport}
                className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl min-w-[120px]"
              >
                <FiDownload className="ml-2" />
                تصدير
              </button>
            </div>
          </div>
        </div>

        {/* Filters - ملتصقة بالهيدر */}
        <div className="bg-gray-50 dark:bg-gray-700/50 border-b border-gray-200 dark:border-gray-600 p-4">
          <div className="flex gap-4">
            {/* البحث - يأخذ المساحة المتبقية ويظهر أولاً */}
            <div className="flex-1">
              <SearchInput
                value={searchQuery}
                onChange={setSearchQuery}
                placeholder="البحث في المنتجات..."
                className="w-full"
              />
            </div>

            {/* اختيار المستودع */}
            <div className="w-56 flex-shrink-0">
              <SelectInput
                name="warehouse-selection"
                value={selectedWarehouse?.toString() || ''}
                onChange={(value) => setSelectedWarehouse(value ? Number(value) : null)}
                options={[
                  { value: '', label: 'جميع المستودعات' },
                  ...warehouses.map((warehouse) => ({
                    value: warehouse.id.toString(),
                    label: warehouse.name
                  }))
                ]}
              />
            </div>

            {/* فلتر حالة المخزون */}
            <div className="w-56 flex-shrink-0">
              <SelectInput
                name="status-filter"
                value={statusFilter}
                onChange={(value) => setStatusFilter(value as 'all' | 'normal' | 'low' | 'out')}
                options={[
                  { value: 'all', label: 'جميع الحالات' },
                  { value: 'normal', label: 'طبيعي' },
                  { value: 'low', label: 'قليل' },
                  { value: 'out', label: 'نفد' }
                ]}
              />
            </div>
          </div>
        </div>

        {/* Content */}
        {filteredInventory.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
              <FiBarChart className="w-10 h-10 text-gray-400 dark:text-gray-500" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
              لا توجد منتجات
            </h3>
            <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
              لا توجد منتجات تطابق معايير البحث والفلترة المحددة
            </p>
          </div>
        ) : (
          <>
            {/* Inventory Table */}
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      المنتج
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      الكود
                    </th>
                    {!selectedWarehouse && (
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        المستودع
                      </th>
                    )}
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      المخزون الحالي
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      الحد الأدنى
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      الحد الأقصى
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      الوحدة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      الحالة
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredInventory.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {item.product_name}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {item.product_code}
                        </div>
                      </td>
                      {!selectedWarehouse && (
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            {item.warehouse_name}
                          </div>
                        </td>
                      )}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {item.current_stock}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {item.min_stock}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {item.max_stock}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {item.unit}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                          {getStatusText(item.status)}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalItems > 0 && (
              <TablePagination
                currentPage={currentPage}
                totalPages={totalPages}
                itemsPerPage={itemsPerPage}
                totalItems={totalItems}
                onPageChange={setCurrentPage}
                onItemsPerPageChange={setItemsPerPage}
                itemsPerPageOptions={[10, 20, 50]}
              />
            )}
          </>
        )}
      </div>

    </div>
  );
};

export default WarehouseInventoryTab;
