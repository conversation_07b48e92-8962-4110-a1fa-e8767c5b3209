/**
 * تبويب طلبات التحويل
 * يعرض ويدير طلبات التحويل بين المستودعات
 */

import React, { useState, useEffect } from 'react';
import {
  FiTruck,
  FiPlus,
  FiCheck,
  FiX,
  FiClock,
  FiArrowRight,
  FiRefreshCw
} from 'react-icons/fi';
import { useWarehouseStore } from '../../stores/warehouseStore';
// import { useWarehouseMovementStore } from '../../stores/warehouseMovementStore'; // TODO: استخدام عند توفر الدوال
import { FormattedDate } from '../FormattedDateTime';
import { SelectInput } from '../inputs';
import SearchInput from '../SearchInput';

interface TransferRequestsTabProps {
  className?: string;
}

const TransferRequestsTab: React.FC<TransferRequestsTabProps> = ({ className = '' }) => {
  const { warehouses, fetchWarehouses } = useWarehouseStore();
  // const warehouseMovementStore = useWarehouseMovementStore(); // TODO: استخدام عند توفر الدوال

  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'approved' | 'rejected' | 'completed'>('all');
  const [fromWarehouseFilter, setFromWarehouseFilter] = useState<number | null>(null);
  const [toWarehouseFilter, setToWarehouseFilter] = useState<number | null>(null);

  useEffect(() => {
    fetchWarehouses();
    // TODO: Implement transfer requests loading when store method is available
    console.log('تحميل طلبات التحويل - قيد التطوير');
  }, []); // إزالة التبعيات لتجنب الحلقة اللا نهائية

  const handleRefresh = async () => {
    // TODO: Implement transfer requests refresh when store method is available
    console.log('تحديث طلبات التحويل - قيد التطوير');
  };

  const handleCreateTransfer = () => {
    console.log('إنشاء طلب تحويل جديد - قيد التطوير');
  };

  // Mock transfer requests data for demonstration
  const mockTransferRequests = [
    {
      id: 1,
      request_number: 'TRF-001',
      from_warehouse_name: 'المستودع الرئيسي',
      to_warehouse_name: 'مستودع الفرع الأول',
      product_name: 'منتج تجريبي 1',
      product_code: 'PROD001',
      requested_quantity: 100,
      approved_quantity: 100,
      unit: 'قطعة',
      status: 'completed' as const,
      priority: 'normal' as const,
      requested_by: 'أحمد محمد',
      approved_by: 'فاطمة علي',
      created_at: new Date().toISOString(),
      approved_at: new Date(Date.now() - 3600000).toISOString(),
      notes: 'طلب تحويل عاجل للفرع'
    },
    {
      id: 2,
      request_number: 'TRF-002',
      from_warehouse_name: 'مستودع الفرع الأول',
      to_warehouse_name: 'مستودع الفرع الثاني',
      product_name: 'منتج تجريبي 2',
      product_code: 'PROD002',
      requested_quantity: 50,
      approved_quantity: null,
      unit: 'كيلو',
      status: 'pending' as const,
      priority: 'high' as const,
      requested_by: 'محمد أحمد',
      approved_by: null,
      created_at: new Date(Date.now() - 86400000).toISOString(),
      approved_at: null,
      notes: 'طلب تحويل للمخزون المنخفض'
    },
    {
      id: 3,
      request_number: 'TRF-003',
      from_warehouse_name: 'المستودع الرئيسي',
      to_warehouse_name: 'مستودع الفرع الثاني',
      product_name: 'منتج تجريبي 3',
      product_code: 'PROD003',
      requested_quantity: 75,
      approved_quantity: 70,
      unit: 'لتر',
      status: 'approved' as const,
      priority: 'normal' as const,
      requested_by: 'سارة أحمد',
      approved_by: 'فاطمة علي',
      created_at: new Date(Date.now() - 172800000).toISOString(),
      approved_at: new Date(Date.now() - 86400000).toISOString(),
      notes: 'موافق عليه - جاهز للتنفيذ'
    },
    {
      id: 4,
      request_number: 'TRF-004',
      from_warehouse_name: 'مستودع الفرع الثاني',
      to_warehouse_name: 'المستودع الرئيسي',
      product_name: 'منتج تجريبي 4',
      product_code: 'PROD004',
      requested_quantity: 30,
      approved_quantity: null,
      unit: 'قطعة',
      status: 'rejected' as const,
      priority: 'low' as const,
      requested_by: 'علي محمد',
      approved_by: 'فاطمة علي',
      created_at: new Date(Date.now() - 259200000).toISOString(),
      approved_at: new Date(Date.now() - 172800000).toISOString(),
      notes: 'مرفوض - مخزون غير كافي'
    }
  ];

  const filteredRequests = mockTransferRequests.filter(request => {
    const matchesSearch = request.request_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         request.product_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         request.product_code.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || request.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <FiClock className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />;
      case 'approved':
        return <FiCheck className="w-4 h-4 text-blue-600 dark:text-blue-400" />;
      case 'completed':
        return <FiCheck className="w-4 h-4 text-green-600 dark:text-green-400" />;
      case 'rejected':
        return <FiX className="w-4 h-4 text-red-600 dark:text-red-400" />;
      default:
        return <FiClock className="w-4 h-4 text-gray-600 dark:text-gray-400" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'في الانتظار';
      case 'approved':
        return 'موافق عليه';
      case 'completed':
        return 'مكتمل';
      case 'rejected':
        return 'مرفوض';
      default:
        return 'غير محدد';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'approved':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      case 'normal':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
      case 'low':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'عالية';
      case 'normal':
        return 'عادية';
      case 'low':
        return 'منخفضة';
      default:
        return 'غير محدد';
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center min-w-0 flex-1">
            <div className="p-3 bg-primary-100 dark:bg-primary-900/30 rounded-xl">
              <FiTruck className="w-6 h-6 text-primary-600 dark:text-primary-400" />
            </div>
            <div className="mr-4 min-w-0 flex-1">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                طلبات التحويل
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                إدارة ومتابعة طلبات التحويل بين المستودعات
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-3 flex-shrink-0">
            <button
              onClick={handleRefresh}
              className="bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium"
            >
              <FiRefreshCw className="ml-2" />
              تحديث
            </button>
            <button
              onClick={handleCreateTransfer}
              className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl min-w-[140px]"
            >
              <FiPlus className="ml-2" />
              طلب تحويل جديد
            </button>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Status Filter */}
          <div>
            <SelectInput
              name="status-filter"
              label="الحالة"
              value={statusFilter}
              onChange={(value) => setStatusFilter(value as 'all' | 'pending' | 'approved' | 'rejected' | 'completed')}
              options={[
                { value: 'all', label: 'جميع الحالات' },
                { value: 'pending', label: 'في الانتظار' },
                { value: 'approved', label: 'موافق عليه' },
                { value: 'completed', label: 'مكتمل' },
                { value: 'rejected', label: 'مرفوض' }
              ]}
            />
          </div>

          {/* From Warehouse Filter */}
          <div>
            <SelectInput
              name="from-warehouse-filter"
              label="من مستودع"
              value={fromWarehouseFilter?.toString() || ''}
              onChange={(value) => setFromWarehouseFilter(value ? Number(value) : null)}
              options={[
                { value: '', label: 'جميع المستودعات' },
                ...warehouses.map((warehouse) => ({
                  value: warehouse.id.toString(),
                  label: warehouse.name
                }))
              ]}
            />
          </div>

          {/* To Warehouse Filter */}
          <div>
            <SelectInput
              name="to-warehouse-filter"
              label="إلى مستودع"
              value={toWarehouseFilter?.toString() || ''}
              onChange={(value) => setToWarehouseFilter(value ? Number(value) : null)}
              options={[
                { value: '', label: 'جميع المستودعات' },
                ...warehouses.map((warehouse) => ({
                  value: warehouse.id.toString(),
                  label: warehouse.name
                }))
              ]}
            />
          </div>

          {/* Search */}
          <div>
            <SearchInput
              label="البحث"
              value={searchQuery}
              onChange={setSearchQuery}
              placeholder="البحث..."
            />
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
          <div className="flex items-center">
            <div className="p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-xl">
              <FiClock className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">في الانتظار</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {filteredRequests.filter(r => r.status === 'pending').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
          <div className="flex items-center">
            <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
              <FiCheck className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">موافق عليها</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {filteredRequests.filter(r => r.status === 'approved').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
          <div className="flex items-center">
            <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-xl">
              <FiCheck className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">مكتملة</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {filteredRequests.filter(r => r.status === 'completed').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
          <div className="flex items-center">
            <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-xl">
              <FiX className="w-6 h-6 text-red-600 dark:text-red-400" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">مرفوضة</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {filteredRequests.filter(r => r.status === 'rejected').length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Transfer Requests Table */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  رقم الطلب
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  المنتج
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  التحويل
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  الكمية
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  الأولوية
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  التاريخ
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredRequests.map((request) => (
                <tr key={request.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {request.request_number}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {request.product_name}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {request.product_code}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                      <span className="truncate max-w-20">{request.from_warehouse_name}</span>
                      <FiArrowRight className="mx-2 flex-shrink-0" />
                      <span className="truncate max-w-20">{request.to_warehouse_name}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {request.requested_quantity} {request.unit}
                    </div>
                    {request.approved_quantity && (
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        موافق: {request.approved_quantity} {request.unit}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(request.priority)}`}>
                      {getPriorityText(request.priority)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getStatusIcon(request.status)}
                      <span className={`mr-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                        {getStatusText(request.status)}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      <FormattedDate date={request.created_at} />
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => console.log('عرض تفاصيل الطلب:', request.id)}
                      className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300"
                    >
                      عرض
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Empty State */}
      {filteredRequests.length === 0 && (
        <div className="text-center py-12">
          <div className="w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
            <FiTruck className="w-10 h-10 text-gray-400 dark:text-gray-500" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
            لا توجد طلبات تحويل
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
            {searchQuery ? 'لم يتم العثور على طلبات تطابق البحث' : 'لا توجد طلبات تحويل حالياً'}
          </p>
          {!searchQuery && (
            <button
              onClick={handleCreateTransfer}
              className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl mx-auto"
            >
              <FiPlus className="ml-2" />
              إنشاء طلب تحويل جديد
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default TransferRequestsTab;
