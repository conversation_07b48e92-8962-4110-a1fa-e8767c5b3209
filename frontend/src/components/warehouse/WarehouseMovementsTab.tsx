/**
 * تبويب حركات المستودعات
 * يعرض جميع حركات الدخول والخروج والتحويلات
 */

import React, { useState, useEffect } from 'react';
import {
  FiRefreshCw,
  FiArrowUp,
  FiArrowDown,
  FiArrowRight,
  FiSearch,
  FiPackage
} from 'react-icons/fi';
import { useWarehouseStore } from '../../stores/warehouseStore';
import { useWarehouseMovementStore } from '../../stores/warehouseMovementStore';
import { FormattedDate, FormattedTime } from '../FormattedDateTime';

interface WarehouseMovementsTabProps {
  className?: string;
}

const WarehouseMovementsTab: React.FC<WarehouseMovementsTabProps> = ({ className = '' }) => {
  const { warehouses, fetchWarehouses } = useWarehouseStore();
  const warehouseMovementStore = useWarehouseMovementStore();

  const [selectedWarehouse, setSelectedWarehouse] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [movementTypeFilter, setMovementTypeFilter] = useState<'all' | 'in' | 'out' | 'transfer' | 'adjustment'>('all');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');

  useEffect(() => {
    fetchWarehouses();
  }, []); // إزالة fetchWarehouses من التبعيات

  useEffect(() => {
    if (selectedWarehouse) {
      warehouseMovementStore.fetchWarehouseMovements(selectedWarehouse);
    }
  }, [selectedWarehouse]); // إزالة warehouseMovementStore من التبعيات

  const handleRefresh = async () => {
    if (selectedWarehouse) {
      await warehouseMovementStore.fetchWarehouseMovements(selectedWarehouse);
    }
  };

  // Mock movements data for demonstration
  const mockMovements = [
    {
      id: 1,
      type: 'in' as const,
      product_name: 'منتج تجريبي 1',
      product_code: 'PROD001',
      quantity: 100,
      unit: 'قطعة',
      reference_type: 'purchase',
      reference_number: 'PUR-001',
      created_at: new Date().toISOString(),
      user_name: 'أحمد محمد',
      notes: 'شراء جديد من المورد'
    },
    {
      id: 2,
      type: 'out' as const,
      product_name: 'منتج تجريبي 2',
      product_code: 'PROD002',
      quantity: 25,
      unit: 'كيلو',
      reference_type: 'sale',
      reference_number: 'SAL-001',
      created_at: new Date(Date.now() - 86400000).toISOString(),
      user_name: 'فاطمة علي',
      notes: 'بيع للعميل'
    },
    {
      id: 3,
      type: 'transfer' as const,
      product_name: 'منتج تجريبي 3',
      product_code: 'PROD003',
      quantity: 50,
      unit: 'لتر',
      reference_type: 'transfer',
      reference_number: 'TRF-001',
      created_at: new Date(Date.now() - 172800000).toISOString(),
      user_name: 'محمد أحمد',
      notes: 'تحويل من المستودع الرئيسي'
    },
    {
      id: 4,
      type: 'adjustment' as const,
      product_name: 'منتج تجريبي 4',
      product_code: 'PROD004',
      quantity: 10,
      unit: 'قطعة',
      reference_type: 'adjustment',
      reference_number: 'ADJ-001',
      created_at: new Date(Date.now() - 259200000).toISOString(),
      user_name: 'سارة أحمد',
      notes: 'تعديل جرد'
    }
  ];

  const filteredMovements = mockMovements.filter(movement => {
    const matchesSearch = movement.product_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         movement.product_code.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         movement.reference_number.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType = movementTypeFilter === 'all' || movement.type === movementTypeFilter;
    return matchesSearch && matchesType;
  });

  const getMovementIcon = (type: string) => {
    switch (type) {
      case 'in':
        return <FiArrowDown className="w-4 h-4 text-green-600 dark:text-green-400" />;
      case 'out':
        return <FiArrowUp className="w-4 h-4 text-red-600 dark:text-red-400" />;
      case 'transfer':
        return <FiArrowRight className="w-4 h-4 text-blue-600 dark:text-blue-400" />;
      case 'adjustment':
        return <FiRefreshCw className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />;
      default:
        return <FiPackage className="w-4 h-4 text-gray-600 dark:text-gray-400" />;
    }
  };

  const getMovementTypeText = (type: string) => {
    switch (type) {
      case 'in':
        return 'دخول';
      case 'out':
        return 'خروج';
      case 'transfer':
        return 'تحويل';
      case 'adjustment':
        return 'تعديل';
      default:
        return 'غير محدد';
    }
  };

  const getMovementTypeColor = (type: string) => {
    switch (type) {
      case 'in':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'out':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      case 'transfer':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
      case 'adjustment':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center min-w-0 flex-1">
            <div className="p-3 bg-primary-100 dark:bg-primary-900/30 rounded-xl">
              <FiRefreshCw className="w-6 h-6 text-primary-600 dark:text-primary-400" />
            </div>
            <div className="mr-4 min-w-0 flex-1">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                حركات المستودعات
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                عرض وتتبع جميع حركات الدخول والخروج والتحويلات
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-3 flex-shrink-0">
            <button
              onClick={handleRefresh}
              className="bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium"
            >
              <FiRefreshCw className="ml-2" />
              تحديث
            </button>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {/* Warehouse Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              المستودع
            </label>
            <select
              value={selectedWarehouse || ''}
              onChange={(e) => setSelectedWarehouse(e.target.value ? Number(e.target.value) : null)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              <option value="">جميع المستودعات</option>
              {warehouses.map((warehouse) => (
                <option key={warehouse.id} value={warehouse.id}>
                  {warehouse.name}
                </option>
              ))}
            </select>
          </div>

          {/* Movement Type Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              نوع الحركة
            </label>
            <select
              value={movementTypeFilter}
              onChange={(e) => setMovementTypeFilter(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              <option value="all">جميع الأنواع</option>
              <option value="in">دخول</option>
              <option value="out">خروج</option>
              <option value="transfer">تحويل</option>
              <option value="adjustment">تعديل</option>
            </select>
          </div>

          {/* Date From */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              من تاريخ
            </label>
            <input
              type="date"
              value={dateFrom}
              onChange={(e) => setDateFrom(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            />
          </div>

          {/* Date To */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              إلى تاريخ
            </label>
            <input
              type="date"
              value={dateTo}
              onChange={(e) => setDateTo(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            />
          </div>

          {/* Search */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              البحث
            </label>
            <div className="relative">
              <FiSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" />
              <input
                type="text"
                placeholder="البحث..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pr-10 pl-4 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
          <div className="flex items-center">
            <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-xl">
              <FiArrowDown className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">حركات الدخول</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {filteredMovements.filter(m => m.type === 'in').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
          <div className="flex items-center">
            <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-xl">
              <FiArrowUp className="w-6 h-6 text-red-600 dark:text-red-400" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">حركات الخروج</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {filteredMovements.filter(m => m.type === 'out').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
          <div className="flex items-center">
            <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
              <FiArrowRight className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">التحويلات</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {filteredMovements.filter(m => m.type === 'transfer').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
          <div className="flex items-center">
            <div className="p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-xl">
              <FiRefreshCw className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">التعديلات</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {filteredMovements.filter(m => m.type === 'adjustment').length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Movements Table */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  النوع
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  المنتج
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  الكمية
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  المرجع
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  التاريخ
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  المستخدم
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  الملاحظات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredMovements.map((movement) => (
                <tr key={movement.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getMovementIcon(movement.type)}
                      <span className={`mr-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getMovementTypeColor(movement.type)}`}>
                        {getMovementTypeText(movement.type)}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {movement.product_name}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {movement.product_code}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {movement.quantity} {movement.unit}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {movement.reference_number}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      <FormattedDate date={movement.created_at} />
                      <br />
                      <FormattedTime date={movement.created_at} />
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {movement.user_name}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-600 dark:text-gray-400 max-w-xs truncate">
                      {movement.notes}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Empty State */}
      {filteredMovements.length === 0 && (
        <div className="text-center py-12">
          <div className="w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
            <FiRefreshCw className="w-10 h-10 text-gray-400 dark:text-gray-500" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
            لا توجد حركات
          </h3>
          <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
            {searchQuery ? 'لم يتم العثور على حركات تطابق البحث' : 'لا توجد حركات مستودعات في الفترة المحددة'}
          </p>
        </div>
      )}
    </div>
  );
};

export default WarehouseMovementsTab;
