/**
 * تبويب المستودعات
 * يعرض قائمة المستودعات مع إمكانيات الإدارة الكاملة
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  FiPlus,
  FiEdit,
  FiTrash2,
  FiUser,
  FiStar,
  FiBarChart,
  FiPackage
} from 'react-icons/fi';
import { useWarehouseStore } from '../../stores/warehouseStore';
import { Warehouse } from '../../services/warehouseService';
import { SelectInput } from '../inputs';
import SearchInput from '../SearchInput';
import CreateWarehouseModal from './CreateWarehouseModal';
import EditWarehouseModal from './EditWarehouseModal';
import WarehouseDetailsModal from './WarehouseDetailsModal';

interface WarehousesTabProps {
  className?: string;
}

const WarehousesTab: React.FC<WarehousesTabProps> = ({ className = '' }) => {
  const {
    warehouses,
    warehouseSummary,
    loading,
    error,
    fetchWarehouses,
    fetchWarehouseSummary,
    deleteWarehouse,
    setMainWarehouse,
    clearError
  } = useWarehouseStore();

  const [searchQuery, setSearchQuery] = useState('');
  const [showInactive, setShowInactive] = useState(false);
  const [inventoryFilter, setInventoryFilter] = useState<'all' | 'with_inventory' | 'without_inventory'>('all');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [selectedWarehouse, setSelectedWarehouse] = useState<Warehouse | null>(null);

  // ref لتتبع التحميل ومنع التكرار
  const isLoadingRef = useRef(false);

  // تحميل البيانات عند تغيير showInactive فقط
  useEffect(() => {
    if (isLoadingRef.current) return; // منع التكرار إذا كان التحميل جارياً

    isLoadingRef.current = true;

    const loadData = async () => {
      try {
        await fetchWarehouses(showInactive);
        await fetchWarehouseSummary();
      } catch (error) {
        console.error('خطأ في تحميل بيانات المستودعات:', error);
      } finally {
        isLoadingRef.current = false;
      }
    };

    loadData();
  }, [showInactive]); // إزالة الدوال من التبعيات لتجنب الحلقة اللا نهائية

  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => clearError(), 5000);
      return () => clearTimeout(timer);
    }
  }, [error]); // إزالة clearError من التبعيات لتجنب الحلقة اللا نهائية

  // فلترة المستودعات حسب جميع الفلاتر
  const filteredWarehouses = React.useMemo(() => {
    let filtered = warehouses;

    // فلترة حسب الحالة النشطة
    if (!showInactive) {
      filtered = filtered.filter(w => w.is_active);
    }

    // فلترة حسب البحث
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(w =>
        w.name.toLowerCase().includes(query) ||
        w.code.toLowerCase().includes(query)
      );
    }

    // فلترة حسب حالة المخزون (محاكاة مؤقتة)
    if (inventoryFilter !== 'all') {
      filtered = filtered.filter(w => {
        // محاكاة مؤقتة: المستودعات الرئيسية لها مخزون، والفرعية قد تكون فارغة
        const hasInventory = w.is_main || w.current_capacity > 0;
        return inventoryFilter === 'with_inventory' ? hasInventory : !hasInventory;
      });
    }

    // ترتيب النتائج
    return filtered.sort((a, b) => {
      if (a.is_main && !b.is_main) return -1;
      if (!a.is_main && b.is_main) return 1;
      return a.name.localeCompare(b.name, 'ar');
    });
  }, [warehouses, searchQuery, showInactive, inventoryFilter]);

  const handleDeleteWarehouse = async (warehouse: Warehouse) => {
    if (warehouse.is_main) {
      alert('لا يمكن حذف المستودع الرئيسي');
      return;
    }

    if (confirm(`هل أنت متأكد من حذف المستودع "${warehouse.name}"؟`)) {
      const success = await deleteWarehouse(warehouse.id);
      if (success) {
        console.log('تم حذف المستودع بنجاح');
      }
    }
  };

  const handleSetMainWarehouse = async (warehouse: Warehouse) => {
    if (warehouse.is_main) return;

    if (confirm(`هل تريد تعيين "${warehouse.name}" كمستودع رئيسي؟`)) {
      const success = await setMainWarehouse(warehouse.id);
      if (success) {
        fetchWarehouses(showInactive);
        fetchWarehouseSummary();
      }
    }
  };

  const handleEditWarehouse = (warehouse: Warehouse) => {
    setSelectedWarehouse(warehouse);
    setIsEditModalOpen(true);
  };

  const handleViewDetails = (warehouse: Warehouse) => {
    setSelectedWarehouse(warehouse);
    setIsDetailsModalOpen(true);
  };

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
    setSelectedWarehouse(null);
  };

  const handleCloseDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedWarehouse(null);
  };

  const handleEditSuccess = async () => {
    try {
      await fetchWarehouses(showInactive);
      await fetchWarehouseSummary();
    } catch (e) {
      console.error('حدث خطأ أثناء تحديث بيانات المستودعات بعد التعديل:', e);
    }
  };

  const getCapacityColor = (warehouse: Warehouse) => {
    if (!warehouse.capacity_limit) return 'bg-gray-200';

    const percentage = (warehouse.current_capacity / warehouse.capacity_limit) * 100;
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const getCapacityPercentage = (warehouse: Warehouse) => {
    if (!warehouse.capacity_limit) return 0;
    return Math.round((warehouse.current_capacity / warehouse.capacity_limit) * 100);
  };

  return (
    <div className={`space-y-6 ${className}`}>




      {/* Summary Cards */}
      {warehouseSummary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
            <div className="flex items-center">
              <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
                <FiPackage className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي المستودعات</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  {warehouseSummary.total_warehouses || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
            <div className="flex items-center">
              <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-xl">
                <FiBarChart className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">المستودعات النشطة</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  {warehouseSummary.active_warehouses || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
            <div className="flex items-center">
              <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-xl">
                <FiPackage className="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">المستودعات غير النشطة</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  {warehouseSummary.inactive_warehouses || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
            <div className="flex items-center">
              <div className="p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-xl">
                <FiStar className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">المستودع الرئيسي</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  {warehouseSummary.main_warehouse?.name || 'غير محدد'}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Data Table with Header and Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 overflow-hidden">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center p-6 gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <div className="min-w-0 flex-1">
                <h2 className="text-lg sm:text-xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FiPackage className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                  <span className="truncate">إدارة المستودعات</span>
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  عرض وإدارة جميع المستودعات في النظام
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3 flex-shrink-0">
              <button
                onClick={() => setIsCreateModalOpen(true)}
                className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl min-w-[140px]"
              >
                <FiPlus className="ml-2" />
                إضافة مستودع
              </button>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-gray-50 dark:bg-gray-700/50 border-b border-gray-200 dark:border-gray-600 p-4">
          <div className="flex gap-4">
            {/* البحث - يأخذ المساحة المتبقية */}
            <div className="flex-1">
              <SearchInput
                value={searchQuery}
                onChange={setSearchQuery}
                placeholder="البحث في المستودعات..."
                className="w-full"
              />
            </div>

            {/* عرض غير النشط */}
            <div className="bg-white dark:bg-gray-700 px-4 py-2 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-10 flex items-center justify-between transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500 flex-shrink-0 min-w-[190px]">
              <label htmlFor="show-inactive-toggle" className="text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap cursor-pointer">
                عرض غير النشط
              </label>
              
              <label htmlFor="show-inactive-toggle" className="inline-flex cursor-pointer mr-2">
                <input
                  type="checkbox"
                  id="show-inactive-toggle"
                  checked={showInactive}
                  onChange={(e) => setShowInactive(e.target.checked)}
                  className="sr-only peer"
                />
                <div className={`
                  relative inline-flex h-6 w-12 shrink-0 cursor-pointer rounded-full
                  transition-colors duration-200 ease-in-out focus:outline-none
                  peer-focus:ring-2 peer-focus:ring-offset-2 peer-focus:ring-primary-500
                  border-2
                  ${showInactive
                    ? 'bg-primary-600 border-primary-700'
                    : 'bg-gray-200 border-gray-300 dark:bg-gray-700 dark:border-gray-500'
                  }
                `}>
                  <span
                    className={`
                      pointer-events-none inline-block h-4 w-4 transform rounded-full
                      bg-white shadow-md transition-transform duration-200 ease-in-out
                      absolute top-0.5 right-0.5
                      ${showInactive ? '-translate-x-6' : 'translate-x-0'}
                    `}
                  />
                </div>
              </label>
            </div>

            {/* فلتر المخزون */}
            <div className="w-56 flex-shrink-0">
              <SelectInput
                name="inventory-filter"
                value={inventoryFilter}
                onChange={(value) => setInventoryFilter(value as 'all' | 'with_inventory' | 'without_inventory')}
                options={[
                  { value: 'all', label: 'جميع المستودعات' },
                  { value: 'with_inventory', label: 'بها مخزون' },
                  { value: 'without_inventory', label: 'بدون مخزون' }
                ]}
              />
            </div>
          </div>
        </div>

        {/* Content */}
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            <span className="mr-3 text-gray-600 dark:text-gray-400">جاري التحميل...</span>
          </div>
        ) : filteredWarehouses.length === 0 ? (
          <div className="text-center py-12">
            <FiPackage className="mx-auto text-6xl text-gray-300 dark:text-gray-600 mb-4" />
            <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
              لا توجد مستودعات
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              {searchQuery || !showInactive
                ? 'جرب تغيير البحث أو إظهار المستودعات غير النشطة'
                : 'ابدأ بإضافة مستودع جديد لتنظيم مخزونك'}
            </p>
            {!searchQuery && (
              <button
                onClick={() => setIsCreateModalOpen(true)}
                className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl mx-auto"
              >
                <FiPlus className="ml-2" />
                إضافة مستودع جديد
              </button>
            )}
          </div>
        ) : (
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {filteredWarehouses.map((warehouse) => (
            <div
              key={warehouse.id}
              className={`bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-200 overflow-hidden flex flex-col h-full ${
                !warehouse.is_active ? 'opacity-75' : ''
              }`}
            >
              {/* Content */}
              <div className="flex-grow p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${
                      warehouse.is_main
                        ? 'bg-warning-100 dark:bg-warning-900/30 text-warning-600 dark:text-warning-400'
                        : warehouse.is_active
                          ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400'
                    }`}>
                      {warehouse.is_main ? (
                        <FiStar className="w-5 h-5" />
                      ) : (
                        <FiPackage className="w-5 h-5" />
                      )}
                    </div>
                    <div>
                      <h3 className="text-base font-semibold text-gray-900 dark:text-gray-100">
                        {warehouse.name}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400 font-mono">
                        {warehouse.code}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-1">
                    <button
                      onClick={() => handleEditWarehouse(warehouse)}
                      className="p-2 text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
                      title="تعديل"
                    >
                      <FiEdit className="w-4 h-4" />
                    </button>
                    {!warehouse.is_main && (
                      <button
                        onClick={() => handleDeleteWarehouse(warehouse)}
                        className="p-2 text-gray-400 hover:text-danger-600 dark:hover:text-danger-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
                        title="حذف"
                      >
                        <FiTrash2 className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>

                {/* Status Badges */}
                <div className="flex items-center gap-2 mb-4">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    warehouse.is_active
                      ? 'bg-success-100 text-success-700 dark:bg-success-900/30 dark:text-success-400'
                      : 'bg-danger-100 text-danger-700 dark:bg-danger-900/30 dark:text-danger-400'
                  }`}>
                    <div className={`w-1.5 h-1.5 rounded-full ml-1 ${
                      warehouse.is_active ? 'bg-success-500' : 'bg-danger-500'
                    }`}></div>
                    {warehouse.is_active ? 'نشط' : 'غير نشط'}
                  </span>

                  {warehouse.is_main && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-warning-100 text-warning-700 dark:bg-warning-900/30 dark:text-warning-400">
                      <FiStar className="w-3 h-3 ml-1" />
                      رئيسي
                    </span>
                  )}
                </div>

                {/* Info Section */}
                <div className="space-y-3 mb-4">
                  {/* Manager */}
                  <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                    <FiUser className="w-4 h-4 ml-2 text-gray-400" />
                    <span className="truncate">{warehouse.manager_name || 'لم يتم تحديد المدير'}</span>
                  </div>
                </div>

                {/* Capacity Section */}
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">السعة التخزينية</span>
                    <span className="text-sm font-bold text-gray-900 dark:text-gray-100">
                      {warehouse.capacity_limit ? `${getCapacityPercentage(warehouse)}%` : 'غير محدد'}
                    </span>
                  </div>
                  {warehouse.capacity_limit ? (
                    <>
                      <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mb-2">
                        <div
                          className={`h-2 rounded-full transition-all duration-300 ${getCapacityColor(warehouse)}`}
                          style={{ width: `${getCapacityPercentage(warehouse)}%` }}
                        />
                      </div>
                      <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                        <span>المستخدم: {warehouse.current_capacity || 0}</span>
                        <span>الإجمالي: {warehouse.capacity_limit}</span>
                      </div>
                    </>
                  ) : (
                    <div className="text-center py-2">
                      <span className="text-xs text-gray-500 dark:text-gray-400">لم يتم تحديد حد السعة</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Footer */}
              <div className="px-4 py-3 bg-gray-50 dark:bg-gray-700/30 border-t border-gray-200 dark:border-gray-600">
                <div className="flex justify-between items-center">
                  {!warehouse.is_main && warehouse.is_active ? (
                    <button
                      onClick={() => handleSetMainWarehouse(warehouse)}
                      className="text-sm text-warning-600 dark:text-warning-400 hover:text-warning-700 dark:hover:text-warning-300 font-medium transition-colors"
                    >
                      تعيين كرئيسي
                    </button>
                  ) : (
                    <div></div>
                  )}

                  <button
                    onClick={() => handleViewDetails(warehouse)}
                    className="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium transition-colors"
                  >
                    عرض التفاصيل
                  </button>
                </div>
              </div>
            </div>
          ))}
            </div>
          </div>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-xl">
          {error}
        </div>
      )}

      {/* Create Warehouse Modal */}
      <CreateWarehouseModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={async () => {
          try {
            await fetchWarehouses(showInactive);
            await fetchWarehouseSummary();
          } catch (e) {
            console.error('حدث خطأ أثناء تحديث بيانات المستودعات بعد الإضافة:', e);
          } finally {
            setIsCreateModalOpen(false);
          }
        }}
      />

      {/* Edit Warehouse Modal */}
      <EditWarehouseModal
        isOpen={isEditModalOpen}
        onClose={handleCloseEditModal}
        warehouse={selectedWarehouse}
        onSuccess={handleEditSuccess}
      />

      {/* Warehouse Details Modal */}
      <WarehouseDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={handleCloseDetailsModal}
        warehouse={selectedWarehouse}
      />
    </div>
  );
};

export default WarehousesTab;
