/**
 * نافذة إنشاء مستودع جديد
 * تتيح للمستخدم إدخال بيانات المستودع الجديد
 */

import React, { useState } from 'react';
import { FiSave, FiMapPin, FiUser, FiMail, FiPhone, FiPackage } from 'react-icons/fi';
import Modal from '../Modal';
import { TextInput, NumberInput } from '../inputs';
import ToggleSwitch from '../ToggleSwitch';
import { useWarehouseStore } from '../../stores/warehouseStore';
import { WarehouseCreate } from '../../services/warehouseService';

interface CreateWarehouseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

const CreateWarehouseModal: React.FC<CreateWarehouseModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const { createWarehouse, creating, error } = useWarehouseStore();

  const [formData, setFormData] = useState<WarehouseCreate>({
    name: '',
    code: '',
    address: '',
    phone: '',
    manager_name: '',
    email: '',
    is_main: false,
    is_active: true,
    capacity_limit: undefined,
    current_capacity: 0
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const handleInputChange = (field: keyof WarehouseCreate, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // مسح خطأ التحقق عند التعديل
    if (validationErrors[field]) {
      setValidationErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    // التحقق من الاسم
    if (!formData.name.trim()) {
      errors.name = 'اسم المستودع مطلوب';
    } else if (formData.name.length > 100) {
      errors.name = 'اسم المستودع يجب أن يكون أقل من 100 حرف';
    }

    // التحقق من الكود
    if (!formData.code.trim()) {
      errors.code = 'كود المستودع مطلوب';
    } else if (formData.code.length > 20) {
      errors.code = 'كود المستودع يجب أن يكون أقل من 20 حرف';
    } else if (!/^[A-Z0-9\-_]+$/i.test(formData.code)) {
      errors.code = 'كود المستودع يجب أن يحتوي على أحرف وأرقام فقط';
    }

    // التحقق من البريد الإلكتروني
    if (formData.email && formData.email.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        errors.email = 'البريد الإلكتروني غير صحيح';
      }
    }

    // التحقق من السعة
    if (formData.capacity_limit !== undefined && formData.capacity_limit < 0) {
      errors.capacity_limit = 'الحد الأقصى للسعة يجب أن يكون أكبر من أو يساوي صفر';
    }

    if (formData.current_capacity !== undefined && formData.current_capacity < 0) {
      errors.current_capacity = 'السعة الحالية يجب أن تكون أكبر من أو تساوي صفر';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // تحويل الكود إلى أحرف كبيرة
    const dataToSubmit = {
      ...formData,
      code: formData.code.toUpperCase(),
      // إزالة القيم الفارغة
      address: formData.address?.trim() || undefined,
      phone: formData.phone?.trim() || undefined,
      manager_name: formData.manager_name?.trim() || undefined,
      email: formData.email?.trim() || undefined,
      capacity_limit: formData.capacity_limit || undefined
    };

    const success = await createWarehouse(dataToSubmit);

    if (success) {
      // إعادة تعيين النموذج
      setFormData({
        name: '',
        code: '',
        address: '',
        phone: '',
        manager_name: '',
        email: '',
        is_main: false,
        is_active: true,
        capacity_limit: undefined,
        current_capacity: 0
      });
      setValidationErrors({});
      
      onSuccess?.();
      onClose();
    }
  };

  const handleClose = () => {
    if (!creating) {
      setValidationErrors({});
      onClose();
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="إضافة مستودع جديد"
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Error Message */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-xl">
            {error}
          </div>
        )}

        {/* Basic Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center gap-2">
            <FiPackage className="w-5 h-5 text-primary-600 dark:text-primary-400" />
            المعلومات الأساسية
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Name */}
            <TextInput
              label="اسم المستودع"
              name="name"
              value={formData.name}
              onChange={(value) => handleInputChange('name', value)}
              placeholder="أدخل اسم المستودع"
              required
              disabled={creating}
              error={validationErrors.name}
            />

            {/* Code */}
            <TextInput
              label="كود المستودع"
              name="code"
              value={formData.code}
              onChange={(value) => handleInputChange('code', value.toUpperCase())}
              placeholder="WH-001"
              required
              disabled={creating}
              error={validationErrors.code}
            />
          </div>

          {/* Address */}
          <TextInput
            label="العنوان"
            name="address"
            value={formData.address || ''}
            onChange={(value) => handleInputChange('address', value)}
            placeholder="أدخل عنوان المستودع"
            disabled={creating}
            icon={<FiMapPin className="w-4 h-4" />}
          />
        </div>

        {/* Contact Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center gap-2">
            <FiUser className="w-5 h-5 text-primary-600 dark:text-primary-400" />
            معلومات الاتصال
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Manager Name */}
            <TextInput
              label="اسم المدير"
              name="manager_name"
              value={formData.manager_name || ''}
              onChange={(value) => handleInputChange('manager_name', value)}
              placeholder="أدخل اسم مدير المستودع"
              disabled={creating}
              icon={<FiUser className="w-4 h-4" />}
            />

            {/* Phone */}
            <TextInput
              label="رقم الهاتف"
              name="phone"
              type="tel"
              value={formData.phone || ''}
              onChange={(value) => handleInputChange('phone', value)}
              placeholder="أدخل رقم الهاتف"
              disabled={creating}
              icon={<FiPhone className="w-4 h-4" />}
            />
          </div>

          {/* Email */}
          <TextInput
            label="البريد الإلكتروني"
            name="email"
            type="email"
            value={formData.email || ''}
            onChange={(value) => handleInputChange('email', value)}
            placeholder="أدخل البريد الإلكتروني"
            disabled={creating}
            error={validationErrors.email}
            icon={<FiMail className="w-4 h-4" />}
          />
        </div>

        {/* Capacity Settings */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">إعدادات السعة</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Capacity Limit */}
            <NumberInput
              label="الحد الأقصى للسعة"
              name="capacity_limit"
              value={formData.capacity_limit?.toString() || ''}
              onChange={(value) => handleInputChange('capacity_limit', value ? parseInt(value) : undefined)}
              placeholder="أدخل الحد الأقصى للسعة"
              min={0}
              step="1"
              disabled={creating}
              error={validationErrors.capacity_limit}
              useMonoFont={false}
              allowDecimals={false}
            />

            {/* Current Capacity */}
            <NumberInput
              label="السعة الحالية"
              name="current_capacity"
              value={formData.current_capacity?.toString() || '0'}
              onChange={(value) => handleInputChange('current_capacity', parseInt(value) || 0)}
              placeholder="أدخل السعة الحالية"
              min={0}
              step="1"
              disabled={creating}
              error={validationErrors.current_capacity}
              useMonoFont={false}
              allowDecimals={false}
            />
          </div>
        </div>

        {/* Status Settings */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">إعدادات الحالة</h3>

          <div className="space-y-4">
            {/* Is Main */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                المستودع الرئيسي
              </label>
              <div className="bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-10 flex items-center transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500">
                <ToggleSwitch
                  id="is_main"
                  checked={formData.is_main || false}
                  onChange={(checked) => handleInputChange('is_main', checked)}
                  label="تعيين كمستودع رئيسي"
                  className="w-full"
                />
              </div>
            </div>

            {/* Is Active */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                حالة المستودع
              </label>
              <div className="bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-10 flex items-center transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500">
                <ToggleSwitch
                  id="is_active"
                  checked={formData.is_active || false}
                  onChange={(checked) => handleInputChange('is_active', checked)}
                  label="مستودع نشط"
                  className="w-full"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-600">
          <button
            type="button"
            onClick={handleClose}
            disabled={creating}
            className="px-6 py-3 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-xl transition-all duration-200 ease-in-out disabled:opacity-50 border-2 border-gray-200 dark:border-gray-600 font-medium"
          >
            إلغاء
          </button>
          <button
            type="submit"
            disabled={creating}
            className="px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-xl transition-all duration-200 ease-in-out disabled:opacity-50 flex items-center gap-2 border-2 border-primary-600 hover:border-primary-700 font-medium shadow-lg hover:shadow-xl"
          >
            {creating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                جاري الحفظ...
              </>
            ) : (
              <>
                <FiSave className="w-4 h-4" />
                حفظ المستودع
              </>
            )}
          </button>
        </div>
      </form>
    </Modal>
  );
};

export default CreateWarehouseModal;
