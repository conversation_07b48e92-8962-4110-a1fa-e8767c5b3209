import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>aW<PERSON><PERSON>, FaExclamationTriangle } from 'react-icons/fa';
import Modal from './Modal';

interface LoadingModalProps {
  isOpen: boolean;
  onClose?: () => void;
  title?: string;
  message?: string;
  showProgress?: boolean;
  progress?: number;
  allowCancel?: boolean;
  onCancel?: () => void;
  type?: 'loading' | 'network-check' | 'error';
}

const LoadingModal: React.FC<LoadingModalProps> = ({
  isOpen,
  onClose,
  title = 'جاري التحميل...',
  message = 'يرجى الانتظار...',
  showProgress = false,
  progress = 0,
  allowCancel = false,
  onCancel,
  type = 'loading'
}) => {
  const getIcon = () => {
    switch (type) {
      case 'network-check':
        return <FaWifi className="h-8 w-8 text-blue-600 dark:text-blue-400 animate-pulse" />;
      case 'error':
        return <FaExclamationTriangle className="h-8 w-8 text-red-600 dark:text-red-400" />;
      default:
        return <FaSpinner className="h-8 w-8 text-primary-600 dark:text-primary-400 animate-spin" />;
    }
  };

  const getIconBgColor = () => {
    switch (type) {
      case 'network-check':
        return 'bg-blue-100 dark:bg-blue-900/30';
      case 'error':
        return 'bg-red-100 dark:bg-red-900/30';
      default:
        return 'bg-primary-100 dark:bg-primary-900/30';
    }
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose || (() => {})} 
      title={title} 
      size="sm" 
      zIndex="highest"
    >
      <div className="text-center">
        {/* Icon */}
        <div className={`mx-auto flex h-16 w-16 items-center justify-center rounded-full ${getIconBgColor()} mb-6`}>
          {getIcon()}
        </div>

        {/* Message */}
        <div className="mb-6">
          <p className="text-sm text-gray-600 dark:text-gray-300">
            {message}
          </p>
        </div>

        {/* Progress Bar */}
        {showProgress && (
          <div className="mb-6">
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
              ></div>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
              {Math.round(progress)}%
            </p>
          </div>
        )}

        {/* Cancel Button */}
        {allowCancel && onCancel && (
          <button
            onClick={onCancel}
            className="btn-secondary flex items-center justify-center min-w-[100px] mx-auto"
          >
            <span>إلغاء</span>
          </button>
        )}

        {/* Loading dots animation for better UX */}
        {type === 'loading' && (
          <div className="flex justify-center space-x-1 mt-4">
            <div className="w-2 h-2 bg-primary-600 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-primary-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-primary-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default LoadingModal;
