import React, { useState, useEffect } from 'react';
import {
  FaTachometerAlt,
  FaMemory,
  FaMicrochip,
  FaEye,
  FaDownload,
  FaChartLine,
  FaPowerOff,
  FaToggleOn,
  FaToggleOff,
  FaCircle,
  FaExclamationTriangle,
  FaCheckCircle,
  FaClock,
  FaDesktop,
  FaNetworkWired,
  FaHdd,
  FaBolt,
  FaShieldAlt,
  FaDatabase,
  FaSync,
  FaTools
} from 'react-icons/fa';
import ReactApexChart from 'react-apexcharts';
import performanceMonitor, { SystemPerformance, PerformanceMetric } from '../services/performanceMonitor';
import unifiedConnectionService from '../services/unifiedConnectionService';
import { appStateManager } from '../services/appStateManager';
import api from '../lib/axios';
import { getCurrentTripoliDateTime, formatDateTime } from '../services/dateTimeService';
interface PerformanceMonitorProps {
  className?: string;
  compact?: boolean;
}

const PerformanceMonitorComponent: React.FC<PerformanceMonitorProps> = ({
  className = '',
  compact = false
}) => {
  const [performance, setPerformance] = useState<SystemPerformance | null>(null);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [history, setHistory] = useState<SystemPerformance[]>([]);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // البيانات الجديدة من النظام المحسن
  const [backendPerformance, setBackendPerformance] = useState<any>(null);
  const [databaseHealth, setDatabaseHealth] = useState<any>(null);
  const [connectionState, setConnectionState] = useState<any>(null);
  const [isLoadingBackendData, setIsLoadingBackendData] = useState(false);
  const [lastBackendUpdate, setLastBackendUpdate] = useState<Date>(getCurrentTripoliDateTime());

  // حالات جديدة للإحصائيات المحسنة - مُعطلة مؤقتاً
  // const [loading, setLoading] = useState(false);
  // const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const unsubscribe = performanceMonitor.addListener((newPerformance) => {
      setPerformance(newPerformance);
      setHistory(prev => {
        const newHistory = [...prev, newPerformance];
        // الاحتفاظ بآخر 20 قراءة فقط
        return newHistory.slice(-20);
      });
    });

    // التحقق من وجود بيانات سابقة أو إنشاء بيانات وهمية
    const existingHistory = performanceMonitor.getPerformanceHistory();
    if (existingHistory && existingHistory.length > 0) {
      setHistory(existingHistory);
    }

    return unsubscribe;
  }, []);

  // بدء/إيقاف المراقبة
  const toggleMonitoring = () => {
    if (isMonitoring) {
      performanceMonitor.stopMonitoring();
      setIsMonitoring(false);
    } else {
      performanceMonitor.startMonitoring(5000); // كل 5 ثواني
      setIsMonitoring(true);

      // تحديث التاريخ فوراً عند بدء المراقبة
      const currentHistory = performanceMonitor.getPerformanceHistory();
      if (currentHistory && currentHistory.length > 0) {
        setHistory(currentHistory);
      }
    }
  };

  // تصدير تقرير الأداء
  const exportReport = () => {
    const report = {
      ...performanceMonitor.getPerformanceReport(),
      timestamp: getCurrentTripoliDateTime().toISOString(),
      backendPerformance,
      databaseHealth,
      connectionState
    };
    const dataStr = JSON.stringify(report, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `performance-report-${formatDateTime(getCurrentTripoliDateTime(), 'date')}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  // جلب البيانات من النظام المحسن
  const fetchBackendPerformanceData = async () => {
    if (isLoadingBackendData) return;

    setIsLoadingBackendData(true);
    try {
      // جلب بيانات الأداء من الخادم
      const [performanceResponse, databaseResponse] = await Promise.all([
        api.get('/api/system/performance').catch(() => ({ data: { data: null } })),
        api.get('/api/system/database/health').catch(() => ({ data: { data: null } }))
      ]);

      // تحديث البيانات
      if (performanceResponse.data?.data) {
        setBackendPerformance(performanceResponse.data.data);
      }

      if (databaseResponse.data?.data) {
        setDatabaseHealth(databaseResponse.data.data);
      }

      // تحديث حالة الاتصال
      const connState = unifiedConnectionService.getConnectionState();
      setConnectionState(connState);

      setLastBackendUpdate(getCurrentTripoliDateTime());
    } catch (error) {
      console.error('Failed to fetch backend performance data:', error);
      // لا نعرض خطأ للمستخدم لأن هذا اختياري
    } finally {
      setIsLoadingBackendData(false);
    }
  };

  // تحسين قاعدة البيانات
  const optimizeDatabase = async () => {
    try {
      setIsLoadingBackendData(true);
      await api.post('/api/system/database/optimize');
      appStateManager.addWarning('تم بدء تحسين قاعدة البيانات');
      // إعادة جلب البيانات بعد التحسين
      setTimeout(fetchBackendPerformanceData, 5000);
    } catch (error) {
      console.error('Failed to optimize database:', error);
      appStateManager.addError('فشل في تحسين قاعدة البيانات');
    } finally {
      setIsLoadingBackendData(false);
    }
  };

  // إعادة تعيين إحصائيات الأداء
  const resetPerformanceStats = async () => {
    try {
      setIsLoadingBackendData(true);
      await api.post('/api/system/performance/reset');
      appStateManager.addWarning('تم إعادة تعيين إحصائيات الأداء');
      fetchBackendPerformanceData();
    } catch (error) {
      console.error('Failed to reset performance stats:', error);
      appStateManager.addError('فشل في إعادة تعيين الإحصائيات');
    } finally {
      setIsLoadingBackendData(false);
    }
  };

  // useEffect لجلب البيانات من النظام المحسن
  useEffect(() => {
    // جلب البيانات فوراً
    fetchBackendPerformanceData();

    // جلب البيانات دورياً إذا كان التحديث التلقائي مفعل
    let interval: NodeJS.Timeout | null = null;
    if (autoRefresh) {
      interval = setInterval(fetchBackendPerformanceData, 30000); // كل 30 ثانية
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh]);

  // الحصول على أيقونة المقياس
  const getMetricIcon = (metricName: string) => {
    switch (metricName) {
      case 'CPU Usage':
        return <FaMicrochip />;
      case 'Memory Usage':
        return <FaMemory />;
      case 'Network Latency':
        return <FaNetworkWired />;
      case 'Database Response':
        return <FaHdd />;
      case 'Rendering Time':
        return <FaDesktop />;
      case 'Overall Performance':
        return <FaTachometerAlt />;
      default:
        return <FaChartLine />;
    }
  };

  // الحصول على اسم المقياس بالعربية
  const getMetricDisplayName = (metricName: string) => {
    switch (metricName) {
      case 'CPU Usage':
        return 'استخدام المعالج';
      case 'Memory Usage':
        return 'استخدام الذاكرة';
      case 'Network Latency':
        return 'زمن استجابة الشبكة';
      case 'Database Response':
        return 'استجابة قاعدة البيانات';
      case 'Rendering Time':
        return 'وقت العرض';
      case 'Overall Performance':
        return 'الأداء العام';
      default:
        return metricName;
    }
  };

  // حساب نسبة التقدم
  const getProgressPercentage = (metric: PerformanceMetric) => {
    if (metric.name === 'Overall Performance' || metric.name === 'CPU Usage') {
      return metric.value;
    } else if (metric.threshold) {
      return Math.min(100, (metric.value / metric.threshold) * 100);
    } else {
      return metric.status === 'good' ? 100 : metric.status === 'warning' ? 60 : 20;
    }
  };

  // الحصول على نص الحالة
  const getStatusText = (status: PerformanceMetric['status']) => {
    switch (status) {
      case 'good':
        return 'أداء ممتاز';
      case 'warning':
        return 'يحتاج مراقبة';
      case 'critical':
        return 'يتطلب تدخل فوري';
      default:
        return 'غير محدد';
    }
  };

  // الحصول على لون المقياس
  const getMetricColor = (status: PerformanceMetric['status']) => {
    switch (status) {
      case 'good':
        return 'text-green-600 bg-green-100 dark:bg-green-900/20 dark:text-green-400';
      case 'warning':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'critical':
        return 'text-red-600 bg-red-100 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-800 dark:text-gray-400';
    }
  };

  // الحصول على شريط التقدم
  const getProgressBar = (metric: PerformanceMetric) => {
    let percentage = 0;

    if (metric.name === 'Overall Performance' || metric.name === 'CPU Usage') {
      percentage = metric.value;
    } else if (metric.threshold) {
      percentage = Math.min(100, (metric.value / metric.threshold) * 100);
    } else {
      percentage = metric.status === 'good' ? 100 : metric.status === 'warning' ? 60 : 20;
    }

    return (
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2">
        <div
          className={`h-2 rounded-full transition-all duration-300 ${
            metric.status === 'good' ? 'bg-green-500' :
            metric.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
          }`}
          style={{ width: `${Math.max(0, Math.min(100, percentage))}%` }}
        />
      </div>
    );
  };

  if (compact) {
    return (
      <div className={`${className}`}>
        <div className="p-3">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-sm flex items-center gap-2">
              <FaTachometerAlt className="text-blue-500" />
              مراقب الأداء
            </h3>
            <button
              onClick={toggleMonitoring}
              className={`text-xs px-3 py-1.5 rounded-full flex items-center gap-1 transition-all duration-200 ${
                isMonitoring
                  ? 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-900/30'
                  : 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400 hover:bg-green-200 dark:hover:bg-green-900/30'
              }`}
            >
              {isMonitoring ? <FaToggleOn /> : <FaToggleOff />}
              <span>{isMonitoring ? 'تشغيل' : 'إيقاف'}</span>
            </button>
          </div>

          {performance && (
            <div className="space-y-2">
              {/* الأداء العام */}
              <div className={`p-2 rounded text-xs ${getMetricColor(performance.overall.status)}`}>
                <div className="flex items-center justify-between">
                  <span className="font-medium">الأداء العام</span>
                  <span>{performance.overall.value}%</span>
                </div>
                {getProgressBar(performance.overall)}
              </div>

              {/* مقاييس سريعة */}
              <div className="grid grid-cols-2 gap-1 text-xs">
                <div className={`p-1 rounded ${getMetricColor(performance.memory.status)}`}>
                  <div className="text-center">
                    <div>ذاكرة</div>
                    <div className="font-bold">{performance.memory.value}MB</div>
                  </div>
                </div>
                <div className={`p-1 rounded ${getMetricColor(performance.network.status)}`}>
                  <div className="text-center">
                    <div>شبكة</div>
                    <div className="font-bold">{performance.network.value}ms</div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-gradient-to-br from-gray-100 via-gray-50 to-gray-100
                     dark:from-gray-900 dark:via-gray-800 dark:to-gray-900
                     text-gray-900 dark:text-white
                     rounded-xl shadow-2xl border border-gray-300 dark:border-gray-700 ${className}`}>
      {/* شريط علوي بتصميم مراقبة احترافي */}
      <div className="relative bg-gradient-to-r from-blue-100/80 to-purple-100/80
                      dark:from-blue-900/50 dark:to-purple-900/50
                      p-4 border-b border-gray-300/50 dark:border-gray-600/50">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="relative">
              <FaShieldAlt className="text-2xl text-blue-600 dark:text-blue-400" />
              {isMonitoring && (
                <FaCircle className="absolute -top-1 -right-1 text-green-500 dark:text-green-400 text-xs animate-pulse" />
              )}
            </div>
            <div>
              <h2 className="font-bold text-lg text-gray-900 dark:text-white">مراقب النظام المتقدم</h2>
              <div className="flex items-center gap-2 text-sm">
                <FaClock className="text-gray-600 dark:text-gray-400" />
                <span className="text-gray-700 dark:text-gray-300">
                  {isMonitoring ? 'مراقبة نشطة' : 'في وضع الاستعداد'}
                </span>
                {isMonitoring && (
                  <div className="flex items-center gap-1 ml-2">
                    <FaCircle className="text-green-500 dark:text-green-400 text-xs animate-pulse" />
                    <span className="text-green-500 dark:text-green-400 text-xs">LIVE</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3">
            {/* مؤشر الحالة */}
            <div className="flex items-center gap-2 px-3 py-1 bg-white/20 dark:bg-black/30 rounded-full border border-gray-400 dark:border-gray-600">
              {isMonitoring ? (
                <>
                  <FaCheckCircle className="text-green-600 dark:text-green-400" />
                  <span className="text-green-600 dark:text-green-400 text-sm font-medium">نشط</span>
                </>
              ) : (
                <>
                  <FaExclamationTriangle className="text-yellow-600 dark:text-yellow-400" />
                  <span className="text-yellow-600 dark:text-yellow-400 text-sm font-medium">متوقف</span>
                </>
              )}
            </div>

            {/* أزرار التحكم */}
            <button
              onClick={toggleMonitoring}
              className={`px-4 py-2 rounded-lg font-medium transition-all duration-300 flex items-center gap-2 ${
                isMonitoring
                  ? 'bg-red-600 hover:bg-red-500 text-white shadow-lg hover:shadow-red-500/25'
                  : 'bg-green-600 hover:bg-green-500 text-white shadow-lg hover:shadow-green-500/25'
              }`}
            >
              {isMonitoring ? <FaPowerOff /> : <FaBolt />}
              <span>{isMonitoring ? 'إيقاف' : 'تشغيل'}</span>
            </button>

            <button
              onClick={exportReport}
              className="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600
                         text-gray-900 dark:text-white rounded-lg transition-all duration-300 flex items-center gap-2
                         border border-gray-300 dark:border-gray-600"
            >
              <FaDownload />
              <span>تصدير</span>
            </button>

            {/* تحكم التحديث التلقائي */}
            <button
              onClick={() => setAutoRefresh(!autoRefresh)}
              className={`px-3 py-2 rounded-lg transition-all duration-300 flex items-center gap-2 text-sm ${
                autoRefresh
                  ? 'bg-blue-600 hover:bg-blue-500 text-white'
                  : 'bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-white'
              }`}
              title={autoRefresh ? 'إيقاف التحديث التلقائي' : 'تشغيل التحديث التلقائي'}
            >
              {autoRefresh ? <FaToggleOn /> : <FaToggleOff />}
              <span className="hidden sm:inline">تحديث تلقائي</span>
            </button>
          </div>
        </div>
      </div>

      <div className="p-6">
        {!performance ? (
          <div className="text-center py-12">
            <div className="relative mb-6">
              <FaEye className="mx-auto h-16 w-16 text-gray-400 dark:text-gray-500 mb-4" />
              {isMonitoring && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="animate-spin rounded-full h-20 w-20 border-2 border-blue-600 dark:border-blue-400 border-t-transparent"></div>
                </div>
              )}
            </div>
            <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
              {isMonitoring ? 'جاري تحليل النظام...' : 'مراقب النظام في وضع الاستعداد'}
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {isMonitoring
                ? 'يتم جمع وتحليل بيانات الأداء في الوقت الفعلي'
                : 'اضغط على زر التشغيل لبدء مراقبة أداء النظام'
              }
            </p>
          </div>
        ) : (
          <div className="space-y-8">
            {/* لوحة الأداء العام */}
            <div className="relative">
              <div className="bg-gradient-to-r from-blue-100/60 to-purple-100/60 dark:from-blue-900/30 dark:to-purple-900/30
                              rounded-xl p-6 border border-blue-300/50 dark:border-blue-500/30">
                <div className="text-center mb-4">
                  <h3 className="text-lg font-semibold text-blue-700 dark:text-blue-300 mb-2">مؤشر الأداء العام</h3>
                  <div className="relative inline-block">
                    {/* دائرة الأداء */}
                    <div className="relative w-32 h-32 mx-auto">
                      <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
                        {/* الخلفية */}
                        <circle
                          cx="60"
                          cy="60"
                          r="50"
                          stroke="currentColor"
                          strokeWidth="8"
                          fill="none"
                          className="text-gray-300 dark:text-gray-700"
                        />
                        {/* المؤشر */}
                        <circle
                          cx="60"
                          cy="60"
                          r="50"
                          stroke="currentColor"
                          strokeWidth="8"
                          fill="none"
                          strokeDasharray={`${2 * Math.PI * 50}`}
                          strokeDashoffset={`${2 * Math.PI * 50 * (1 - performance.overall.value / 100)}`}
                          className={`transition-all duration-1000 ${
                            performance.overall.status === 'good' ? 'text-green-500 dark:text-green-400' :
                            performance.overall.status === 'warning' ? 'text-yellow-500 dark:text-yellow-400' : 'text-red-500 dark:text-red-400'
                          }`}
                          strokeLinecap="round"
                        />
                      </svg>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-center">
                          <div className="text-3xl font-bold text-gray-900 dark:text-white">{performance.overall.value}%</div>
                          <div className="text-xs text-gray-600 dark:text-gray-400">أداء النظام</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium ${
                    performance.overall.status === 'good' ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 border border-green-300 dark:border-green-500/30' :
                    performance.overall.status === 'warning' ? 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-700 dark:text-yellow-300 border border-yellow-300 dark:border-yellow-500/30' :
                    'bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300 border border-red-300 dark:border-red-500/30'
                  }`}>
                    {performance.overall.status === 'good' ? <FaCheckCircle /> :
                     performance.overall.status === 'warning' ? <FaExclamationTriangle /> : <FaExclamationTriangle />}
                    <span>
                      {performance.overall.status === 'good' ? 'ممتاز' :
                       performance.overall.status === 'warning' ? 'تحذير' : 'يحتاج انتباه'}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* مقاييس مفصلة */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
              {Object.entries(performance).filter(([key]) => key !== 'overall').map(([key, metric]) => (
                <div key={key} className="bg-white/90 dark:bg-gray-800/60 rounded-xl p-4 border border-gray-300/50 dark:border-gray-600/50
                                         hover:border-gray-400/50 dark:hover:border-gray-500/50 transition-all duration-300 shadow-lg
                                         hover:shadow-xl hover:scale-105 transform">
                  {/* رأس البطاقة */}
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <div className={`p-1.5 rounded-lg ${
                        metric.status === 'good' ? 'bg-green-100 dark:bg-green-900/50 text-green-600 dark:text-green-400' :
                        metric.status === 'warning' ? 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-600 dark:text-yellow-400' :
                        'bg-red-100 dark:bg-red-900/50 text-red-600 dark:text-red-400'
                      }`}>
                        {getMetricIcon(metric.name)}
                      </div>
                      <div>
                        <h4 className="font-semibold text-sm text-gray-900 dark:text-white">{getMetricDisplayName(metric.name)}</h4>
                        <div className="text-xs text-gray-600 dark:text-gray-400 flex items-center gap-1">
                          <FaClock />
                          {formatDateTime(metric.timestamp, 'time')}
                        </div>
                      </div>
                    </div>

                    {/* مؤشر الحالة */}
                    <div className={`w-2.5 h-2.5 rounded-full ${
                      metric.status === 'good' ? 'bg-green-500 dark:bg-green-400 shadow-lg shadow-green-500/50 dark:shadow-green-400/50' :
                      metric.status === 'warning' ? 'bg-yellow-500 dark:bg-yellow-400 shadow-lg shadow-yellow-500/50 dark:shadow-yellow-400/50' :
                      'bg-red-500 dark:bg-red-400 shadow-lg shadow-red-500/50 dark:shadow-red-400/50 animate-pulse'
                    }`} />
                  </div>

                  {/* القيمة الرئيسية */}
                  <div className="text-center mb-4">
                    <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                      {metric.value === -1 ? (
                        <span className="text-red-600 dark:text-red-400">خطأ</span>
                      ) : (
                        <>
                          <span>{metric.value}</span>
                          <span className="text-base text-gray-600 dark:text-gray-400 ml-1 font-medium">{metric.unit}</span>
                        </>
                      )}
                    </div>
                    {metric.threshold && metric.value !== -1 && (
                      <div className="text-sm text-gray-600 dark:text-gray-400 font-medium">
                        الحد الأقصى: {metric.threshold}{metric.unit}
                      </div>
                    )}
                  </div>

                  {/* شريط التقدم المحسن */}
                  <div className="relative mb-4">
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 overflow-hidden">
                      <div
                        className={`h-2.5 rounded-full transition-all duration-1000 ${
                          metric.status === 'good' ? 'bg-gradient-to-r from-green-500 to-green-400' :
                          metric.status === 'warning' ? 'bg-gradient-to-r from-yellow-500 to-yellow-400' :
                          'bg-gradient-to-r from-red-500 to-red-400'
                        }`}
                        style={{
                          width: `${Math.max(0, Math.min(100, getProgressPercentage(metric)))}%`
                        }}
                      />
                    </div>
                    {/* نسبة التقدم */}
                    <div className="text-center mt-1">
                      <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                        {Math.round(getProgressPercentage(metric))}%
                      </span>
                    </div>
                  </div>

                  {/* معلومات إضافية */}
                  <div className="text-center">
                    <div className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-bold ${
                      metric.status === 'good' ? 'bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-300' :
                      metric.status === 'warning' ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/50 dark:text-yellow-300' :
                      'bg-red-100 text-red-700 dark:bg-red-900/50 dark:text-red-300'
                    }`}>
                      {metric.status === 'good' ? '✓' : metric.status === 'warning' ? '⚠' : '✗'}
                      <span>{getStatusText(metric.status)}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* قسم البيانات المحسنة من النظام الجديد */}
            {(backendPerformance || databaseHealth || connectionState) && (
              <div className="touch-card bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
                <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
                      <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                        <FaDatabase className="text-blue-600 dark:text-blue-400 text-lg" />
                      </div>
                      إحصائيات النظام المتقدمة
                    </h3>
                    <div className="flex items-center gap-3">
                      <button
                        onClick={optimizeDatabase}
                        disabled={isLoadingBackendData}
                        className="btn-secondary text-sm flex items-center gap-2 disabled:opacity-50"
                      >
                        <FaTools />
                        {isLoadingBackendData ? 'جاري التحسين...' : 'تحسين قاعدة البيانات'}
                      </button>
                      <button
                        onClick={resetPerformanceStats}
                        disabled={isLoadingBackendData}
                        className="btn-outline text-sm flex items-center gap-2 disabled:opacity-50"
                      >
                        <FaSync />
                        إعادة تعيين
                      </button>
                    </div>
                  </div>
                </div>

                <div className="p-4">
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                    {/* إحصائيات الخادم */}
                    {backendPerformance && (
                      <div className="touch-card stats-card">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2">
                            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                              <FaDesktop className="text-blue-600 dark:text-blue-400 text-lg" />
                            </div>
                            <div>
                              <h4 className="font-semibold text-base text-gray-900 dark:text-white">موارد الخادم</h4>
                              <p className="text-xs text-gray-600 dark:text-gray-400">مراقبة الموارد</p>
                            </div>
                          </div>
                          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                        </div>
                        <div className="space-y-3">
                          {backendPerformance.system_resources && (
                            <>
                              {/* المعالج */}
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">المعالج</span>
                                <span className={`text-lg font-bold ${
                                  backendPerformance.system_resources.cpu_percent > 80 ? 'text-red-600' :
                                  backendPerformance.system_resources.cpu_percent > 60 ? 'text-yellow-600' : 'text-green-600'
                                }`}>
                                  {backendPerformance.system_resources.cpu_percent?.toFixed(1) || '0.0'}%
                                </span>
                              </div>
                              <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                                <div
                                  className={`h-2 rounded-full transition-all duration-500 ${
                                    backendPerformance.system_resources.cpu_percent > 80 ? 'bg-red-500' :
                                    backendPerformance.system_resources.cpu_percent > 60 ? 'bg-yellow-500' : 'bg-green-500'
                                  }`}
                                  style={{ width: `${Math.min(backendPerformance.system_resources.cpu_percent || 0, 100)}%` }}
                                ></div>
                              </div>

                              {/* الذاكرة */}
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">الذاكرة</span>
                                <span className={`text-lg font-bold ${
                                  backendPerformance.system_resources.memory_percent > 85 ? 'text-red-600' :
                                  backendPerformance.system_resources.memory_percent > 70 ? 'text-yellow-600' : 'text-green-600'
                                }`}>
                                  {backendPerformance.system_resources.memory_percent?.toFixed(1) || '0.0'}%
                                </span>
                              </div>
                              <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                                <div
                                  className={`h-2 rounded-full transition-all duration-500 ${
                                    backendPerformance.system_resources.memory_percent > 85 ? 'bg-red-500' :
                                    backendPerformance.system_resources.memory_percent > 70 ? 'bg-yellow-500' : 'bg-green-500'
                                  }`}
                                  style={{ width: `${Math.min(backendPerformance.system_resources.memory_percent || 0, 100)}%` }}
                                ></div>
                              </div>

                              {/* القرص */}
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">القرص</span>
                                <span className={`text-lg font-bold ${
                                  backendPerformance.system_resources.disk_percent > 90 ? 'text-red-600' :
                                  backendPerformance.system_resources.disk_percent > 80 ? 'text-yellow-600' : 'text-green-600'
                                }`}>
                                  {backendPerformance.system_resources.disk_percent?.toFixed(1) || '0.0'}%
                                </span>
                              </div>
                              <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                                <div
                                  className={`h-2 rounded-full transition-all duration-500 ${
                                    backendPerformance.system_resources.disk_percent > 90 ? 'bg-red-500' :
                                    backendPerformance.system_resources.disk_percent > 80 ? 'bg-yellow-500' : 'bg-green-500'
                                  }`}
                                  style={{ width: `${Math.min(backendPerformance.system_resources.disk_percent || 0, 100)}%` }}
                                ></div>
                              </div>
                            </>
                          )}
                        </div>
                      </div>
                    )}

                    {/* إحصائيات الطلبات */}
                    {backendPerformance && (
                      <div className="touch-card stats-card">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2">
                            <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                              <FaNetworkWired className="text-green-600 dark:text-green-400 text-lg" />
                            </div>
                            <div>
                              <h4 className="font-semibold text-base text-gray-900 dark:text-white">إحصائيات الطلبات</h4>
                              <p className="text-xs text-gray-600 dark:text-gray-400">حركة البيانات</p>
                            </div>
                          </div>
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        </div>
                        <div className="space-y-3">
                          {/* إجمالي الطلبات */}
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">إجمالي</span>
                            <span className="text-lg font-bold text-blue-600 dark:text-blue-400">
                              {(backendPerformance.total_requests || 0).toLocaleString('ar-LY')}
                            </span>
                          </div>

                          {/* الطلبات النشطة */}
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">نشطة</span>
                            <span className="text-lg font-bold text-green-600 dark:text-green-400 flex items-center gap-1">
                              {backendPerformance.active_requests || 0}
                              <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>
                            </span>
                          </div>

                          {/* الطلبات البطيئة */}
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">بطيئة</span>
                            <span className="text-lg font-bold text-yellow-600 dark:text-yellow-400">
                              {backendPerformance.slow_requests || 0}
                            </span>
                          </div>

                          {/* الطلبات الفاشلة */}
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">فاشلة</span>
                            <span className="text-lg font-bold text-red-600 dark:text-red-400">
                              {backendPerformance.failed_requests || 0}
                            </span>
                          </div>

                          {/* متوسط وقت الاستجابة */}
                          <div className="pt-2 border-t border-gray-200 dark:border-gray-600">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">متوسط الاستجابة</span>
                              <span className="text-lg font-bold text-purple-600 dark:text-purple-400">
                                {backendPerformance.average_response_time?.toFixed(0) || 0}ms
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* حالة قاعدة البيانات */}
                    {databaseHealth && (
                      <div className="touch-card stats-card">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2">
                            <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                              <FaDatabase className="text-purple-600 dark:text-purple-400 text-lg" />
                            </div>
                            <div>
                              <h4 className="font-semibold text-base text-gray-900 dark:text-white">قاعدة البيانات</h4>
                              <p className="text-xs text-gray-600 dark:text-gray-400">صحة النظام</p>
                            </div>
                          </div>
                          <div className={`w-2 h-2 rounded-full ${
                            databaseHealth.status === 'healthy' ? 'bg-green-500 animate-pulse' :
                            databaseHealth.status === 'warning' ? 'bg-yellow-500 animate-pulse' :
                            'bg-red-500 animate-pulse'
                          }`}></div>
                        </div>
                        <div className="space-y-3">
                          {/* حالة النظام */}
                          <div className="text-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                            <div className={`text-2xl mb-1 ${
                              databaseHealth.status === 'healthy' ? 'text-green-500' :
                              databaseHealth.status === 'warning' ? 'text-yellow-500' : 'text-red-500'
                            }`}>
                              {databaseHealth.status === 'healthy' ? '✓' :
                               databaseHealth.status === 'warning' ? '⚠' : '✗'}
                            </div>
                            <div className={`text-sm font-bold ${
                              databaseHealth.status === 'healthy' ? 'text-green-700 dark:text-green-300' :
                              databaseHealth.status === 'warning' ? 'text-yellow-700 dark:text-yellow-300' :
                              'text-red-700 dark:text-red-300'
                            }`}>
                              {databaseHealth.status === 'healthy' ? 'سليمة' :
                               databaseHealth.status === 'warning' ? 'تحذير' : 'خطأ'}
                            </div>
                          </div>

                          {/* تفاصيل الحالة */}
                          <div className="space-y-2">
                            {/* حالة الاتصال */}
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">الاتصال</span>
                              <span className={`text-sm font-bold ${
                                databaseHealth.connection === 'ok' ? 'text-green-600' : 'text-red-600'
                              }`}>
                                {databaseHealth.connection === 'ok' ? 'متصل' : 'منقطع'}
                              </span>
                            </div>

                            {/* حالة التحسين */}
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">التحسين</span>
                              <span className={`text-sm font-bold ${
                                databaseHealth.needs_optimization ? 'text-orange-600' : 'text-green-600'
                              }`}>
                                {databaseHealth.needs_optimization ? 'يحتاج تحسين' : 'محسنة'}
                              </span>
                            </div>
                          </div>

                          {/* آخر تحسين */}
                          {databaseHealth.last_optimization && (
                            <div className="pt-2 border-t border-gray-200 dark:border-gray-600">
                              <div className="text-center">
                                <div className="text-xs text-gray-600 dark:text-gray-400">آخر تحسين</div>
                                <div className="text-xs font-medium text-gray-900 dark:text-white">
                                  {formatDateTime(new Date(databaseHealth.last_optimization), 'datetime')}
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                </div>

                  {/* آخر تحديث */}
                  <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700 text-center">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      آخر تحديث: {formatDateTime(lastBackendUpdate, 'datetime')}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* رسم بياني محسن للتاريخ باستخدام ApexCharts */}
            {history.length > 0 && (() => {
              // تحضير البيانات للرسم البياني
              const chartData = history.map(perf => ({
                x: perf.overall?.timestamp ? formatDateTime(perf.overall.timestamp, 'time') : formatDateTime(getCurrentTripoliDateTime(), 'time'),
                y: perf.overall?.value || 0,
                status: perf.overall?.status || 'critical'
              }));

              // تحديد الألوان بناءً على الحالة
              const getColorByValue = (value: number) => {
                if (value >= 80) return '#10b981'; // أخضر
                if (value >= 60) return '#f59e0b'; // أصفر
                return '#ef4444'; // أحمر
              };

              // إعداد خيارات ApexCharts
              const chartOptions = {
                chart: {
                  type: 'area' as const,
                  fontFamily: 'almarai, sans-serif',
                  toolbar: {
                    show: false
                  },
                  zoom: {
                    enabled: false
                  },
                  animations: {
                    enabled: true,
                    speed: 800,
                    animateGradually: {
                      enabled: true,
                      delay: 150
                    },
                    dynamicAnimation: {
                      enabled: true,
                      speed: 350
                    }
                  },
                  background: 'transparent',
                  sparkline: {
                    enabled: false
                  }
                },
                theme: {
                  mode: 'light' as const
                },
                dataLabels: {
                  enabled: false
                },
                stroke: {
                  curve: 'smooth' as const,
                  width: 3,
                  colors: ['#3b82f6']
                },
                fill: {
                  type: 'gradient',
                  gradient: {
                    shadeIntensity: 1,
                    opacityFrom: 0.7,
                    opacityTo: 0.1,
                    stops: [0, 90, 100],
                    colorStops: [
                      {
                        offset: 0,
                        color: '#ef4444',
                        opacity: 0.7
                      },
                      {
                        offset: 50,
                        color: '#f59e0b',
                        opacity: 0.5
                      },
                      {
                        offset: 100,
                        color: '#10b981',
                        opacity: 0.3
                      }
                    ]
                  }
                },
                grid: {
                  show: true,
                  borderColor: '#e5e7eb',
                  strokeDashArray: 3,
                  position: 'back' as const,
                  xaxis: {
                    lines: {
                      show: false
                    }
                  },
                  yaxis: {
                    lines: {
                      show: true
                    }
                  },
                  row: {
                    colors: undefined,
                    opacity: 0.5
                  },
                  column: {
                    colors: undefined,
                    opacity: 0.5
                  },
                  padding: {
                    top: 0,
                    right: 0,
                    bottom: 0,
                    left: 0
                  }
                },
                markers: {
                  size: 6,
                  colors: chartData.map(point => getColorByValue(point.y)),
                  strokeColors: '#ffffff',
                  strokeWidth: 2,
                  shape: 'circle' as const,
                  hover: {
                    size: 8,
                    sizeOffset: 3
                  }
                },
                xaxis: {
                  categories: chartData.map(point => point.x),
                  labels: {
                    style: {
                      colors: '#6b7280',
                      fontSize: '11px',
                      fontFamily: 'almarai, sans-serif'
                    },
                    rotate: -45,
                    rotateAlways: false,
                    hideOverlappingLabels: true,
                    showDuplicates: false,
                    trim: false
                  },
                  axisBorder: {
                    show: true,
                    color: '#e5e7eb',
                    height: 1,
                    width: '100%',
                    offsetX: 0,
                    offsetY: 0
                  },
                  axisTicks: {
                    show: true,
                    borderType: 'solid',
                    color: '#e5e7eb',
                    height: 6,
                    offsetX: 0,
                    offsetY: 0
                  }
                },
                yaxis: {
                  min: 0,
                  max: 100,
                  labels: {
                    style: {
                      colors: '#6b7280',
                      fontSize: '11px',
                      fontFamily: 'almarai, sans-serif'
                    },
                    formatter: (value: number) => `${value}%`
                  },
                  title: {
                    text: 'نسبة الأداء (%)',
                    style: {
                      color: '#374151',
                      fontSize: '12px',
                      fontFamily: 'almarai, sans-serif',
                      fontWeight: 600
                    }
                  }
                },
                tooltip: {
                  enabled: true,
                  theme: 'dark',
                  style: {
                    fontSize: '12px',
                    fontFamily: 'almarai, sans-serif'
                  },
                  custom: ({ dataPointIndex }: { dataPointIndex: number }) => {
                    const point = chartData[dataPointIndex];
                    const statusText = point.status === 'good' ? 'ممتاز' :
                                     point.status === 'warning' ? 'تحذير' : 'حرج';
                    const statusColor = getColorByValue(point.y);

                    return `
                      <div class="bg-gray-900 text-white p-3 rounded-lg shadow-xl border border-gray-700">
                        <div class="font-bold text-sm" style="color: ${statusColor};">${point.y}%</div>
                        <div class="text-gray-300 text-xs mt-1">${point.x}</div>
                        <div class="text-xs mt-1" style="color: ${statusColor};">${statusText}</div>
                      </div>
                    `;
                  }
                },
                legend: {
                  show: false
                },
                responsive: [
                  {
                    breakpoint: 768,
                    options: {
                      chart: {
                        height: 200
                      },
                      xaxis: {
                        labels: {
                          style: {
                            fontSize: '10px'
                          }
                        }
                      }
                    }
                  }
                ]
              };

              // بيانات السلسلة
              const series = [{
                name: 'أداء النظام',
                data: chartData.map(point => point.y)
              }];

              return (
                <div className="bg-white/60 dark:bg-gray-800/30 rounded-xl p-6 border border-gray-300/50 dark:border-gray-600/30 shadow-lg">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                      <FaChartLine className="text-blue-600 dark:text-blue-400" />
                      تاريخ الأداء المباشر
                    </h3>
                    <div className="flex items-center gap-3">
                      <div className="text-xs text-gray-600 dark:text-gray-400">
                        آخر {history.length} قراءات
                      </div>
                      {/* مؤشر الحالة المباشرة */}
                      {(() => {
                        const lastPerf = history[history.length - 1];
                        const value = lastPerf.overall?.value || 0;
                        const status = lastPerf.overall?.status || 'critical';

                        return (
                          <div className={`px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1 ${
                            status === 'good' ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300' :
                            status === 'warning' ? 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-700 dark:text-yellow-300' :
                            'bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300'
                          }`}>
                            <FaCircle className="animate-pulse" />
                            <span>{value}% - {status === 'good' ? 'مستقر' : status === 'warning' ? 'تحذير' : 'حرج'}</span>
                          </div>
                        );
                      })()}
                    </div>
                  </div>

                  {/* الرسم البياني */}
                  <div className="h-64 md:h-72">
                    <ReactApexChart
                      type="area"
                      height="100%"
                      width="100%"
                      options={chartOptions}
                      series={series}
                    />
                  </div>
                </div>
              );
            })()}

            {/* معلومات إضافية محسنة */}
            {history.length > 0 && (
              <div className="bg-white/40 dark:bg-gray-800/20 rounded-xl p-4 border border-gray-300/30 dark:border-gray-600/20 mt-6">
                <div className="flex justify-between items-center text-xs text-gray-600 dark:text-gray-400">
                  <div className="flex items-center gap-4">
                    <span className="flex items-center gap-1">
                      <FaCircle className="text-green-500 dark:text-green-400 animate-pulse" />
                      مباشر
                    </span>
                    <span className="text-gray-500">
                      تحديث كل 3 ثواني
                    </span>
                  </div>
                  <div className="flex items-center gap-3 text-xs">
                    <span>المدى: {Math.min(...history.map(h => h.overall?.value || 0))}% - {Math.max(...history.map(h => h.overall?.value || 0))}%</span>
                  </div>
                </div>

                {/* مؤشرات الأداء السريعة */}
                <div className="flex justify-center gap-4 mt-3 text-xs">
                  {(() => {
                    const values = history.map(h => h.overall?.value || 0);
                    const avg = Math.round(values.reduce((a, b) => a + b, 0) / values.length);
                    const trend = values.length > 1 ? values[values.length - 1] - values[values.length - 2] : 0;

                    return (
                      <>
                        <div className="flex items-center gap-1 px-3 py-1.5 bg-gray-100 dark:bg-gray-800 rounded-full">
                          <span className="text-gray-600 dark:text-gray-400">متوسط:</span>
                          <span className="font-medium text-gray-800 dark:text-gray-200">{avg}%</span>
                        </div>
                        <div className="flex items-center gap-1 px-3 py-1.5 bg-gray-100 dark:bg-gray-800 rounded-full">
                          <span className="text-gray-600 dark:text-gray-400">الاتجاه:</span>
                          <span className={`font-medium flex items-center gap-1 ${
                            trend > 0 ? 'text-green-600 dark:text-green-400' :
                            trend < 0 ? 'text-red-600 dark:text-red-400' :
                            'text-gray-600 dark:text-gray-400'
                          }`}>
                            {trend > 0 ? '↗' : trend < 0 ? '↘' : '→'}
                            {Math.abs(trend).toFixed(1)}%
                          </span>
                        </div>
                      </>
                    );
                  })()}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default PerformanceMonitorComponent;
