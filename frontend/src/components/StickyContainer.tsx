import React, { useRef, useEffect } from 'react';

interface StickyContainerProps {
  children: React.ReactNode;
  className?: string;
  topOffset?: number;
  stickyClass?: string;
}

/**
 * مكون StickyContainer
 * يوفر حاوية تدعم خاصية sticky بشكل متوافق مع react-custom-scrollbars
 */
const StickyContainer: React.FC<StickyContainerProps> = ({
  children,
  className = '',
  topOffset = 24,
  stickyClass = 'sticky-content'
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const stickyRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    const container = containerRef.current;
    const stickyElement = stickyRef.current;
    
    if (!container || !stickyElement) return;
    
    // تحديد ما إذا كان المتصفح يدعم IntersectionObserver
    if ('IntersectionObserver' in window) {
      // إنشاء مراقب للتقاطع
      const observer = new IntersectionObserver(
        (entries) => {
          const [entry] = entries;
          
          // إضافة أو إزالة فئة CSS بناءً على ما إذا كان العنصر مرئيًا
          if (entry.isIntersecting) {
            // استخدام requestAnimationFrame لتحسين الأداء والسلاسة
            requestAnimationFrame(() => {
              stickyElement.style.position = 'static';
              stickyElement.style.transform = 'translateY(0)';
            });
          } else {
            requestAnimationFrame(() => {
              stickyElement.style.position = 'fixed';
              stickyElement.style.top = `${topOffset}px`;
              stickyElement.style.width = `${container.offsetWidth}px`;
              // إضافة تأثير حركة سلس
              stickyElement.style.transform = 'translateY(0)';
            });
          }
        },
        {
          // مراقبة الجزء العلوي من الحاوية
          threshold: 0,
          rootMargin: `-${topOffset}px 0px 0px 0px`
        }
      );
      
      // بدء مراقبة الحاوية
      observer.observe(container);
      
      // تنظيف المراقب عند إلغاء تحميل المكون
      return () => {
        observer.disconnect();
      };
    } else {
      // Fallback for browsers that don't support IntersectionObserver
      const findScrollableParent = (element: HTMLElement | null): EventTarget & { addEventListener: Function; removeEventListener: Function } => {
        if (!element) {
          return window as EventTarget & { addEventListener: Function; removeEventListener: Function };
        }
        let parent = element.parentElement;
        while (parent) {
          const style = window.getComputedStyle(parent);
          if (style.overflow === 'auto' || style.overflow === 'scroll' || style.overflowY === 'auto' || style.overflowY === 'scroll') {
            return parent as EventTarget & { addEventListener: Function; removeEventListener: Function };
          }
          parent = parent.parentElement;
        }
        return window as EventTarget & { addEventListener: Function; removeEventListener: Function };
      };

      const scrollableParent = findScrollableParent(container);
      
      let ticking = false;
      const handleScroll = () => {
        if (!container || !stickyElement) return;
        
        if (!ticking) {
          requestAnimationFrame(() => {
            const containerRect = container.getBoundingClientRect();
            
            if (containerRect.top <= topOffset) {
              stickyElement.style.position = 'fixed';
              stickyElement.style.top = `${topOffset}px`;
              stickyElement.style.width = `${container.offsetWidth}px`;
              stickyElement.style.transform = 'translateY(0)';
            } else {
              stickyElement.style.position = 'static';
              stickyElement.style.width = '100%';
              stickyElement.style.transform = 'translateY(0)';
            }
            
            ticking = false;
          });
          
          ticking = true;
        }
      };
      
      const handleResize = () => {
        if (!container || !stickyElement) return;
        
        if (stickyElement.style.position === 'fixed') {
          stickyElement.style.width = `${container.offsetWidth}px`;
        }
      };

      scrollableParent.addEventListener('scroll', handleScroll, { passive: true });
      (window as Window & typeof globalThis).addEventListener('resize', handleResize, { passive: true });
      
      handleScroll();
      
      return () => {
        scrollableParent.removeEventListener('scroll', handleScroll);
        (window as Window & typeof globalThis).removeEventListener('resize', handleResize);
      };
    }
  }, [topOffset]);
  
  return (
    <div ref={containerRef} className={className}>
      <div ref={stickyRef} className={stickyClass}>
        {children}
      </div>
    </div>
  );
};

export default StickyContainer;
