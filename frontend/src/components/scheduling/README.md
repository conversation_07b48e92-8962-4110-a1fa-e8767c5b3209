# نظام الجدولة المحسن - SmartPOS

## نظرة عامة

تم تطوير نظام جدولة محسن وديناميكي لتحسين تجربة المستخدم في إدارة المهام المجدولة. النظام يوفر واجهة بديهية وسهلة الاستخدام مع مكونات تفاعلية متقدمة.

## المكونات المطورة

### 1. CronBuilder المحسن
**الملف:** `frontend/src/components/CronBuilder.tsx`

#### الميزات الجديدة:
- واجهة أكثر بديهية مع أزرار تفاعلية
- معاينة فورية لوقت التشغيل التالي
- تكامل مع مكونات UI محسنة (TimeSelector, FrequencySelector)
- دعم كامل للوضع المظلم
- تصميم متجاوب

#### التحسينات:
- استبدال القوائم المنسدلة بأزرار تفاعلية
- إضافة أيقونات توضيحية لكل نمط
- تحسين التخطيط والتصميم
- إضافة معاينة الوقت التالي

### 1.1. مكونات الإدخال المحسنة
**الملفات:** `frontend/src/components/inputs/`

#### TextInput المحسن:
- دعم الأيقونات والتحقق من الصحة
- رسائل الخطأ والنجاح
- تصميم موحد مع المشروع
- دعم الاتجاهات (RTL/LTR)

#### TextArea المحسن:
- تحكم في حجم النص
- عداد الأحرف
- تصميم متجاوب
- دعم التحقق من الصحة

#### SelectInput المحسن:
- بحث في الخيارات
- دعم الأيقونات
- تصميم تفاعلي
- إمكانية المسح

### 2. FrequencySelector
**الملف:** `frontend/src/components/FrequencySelector.tsx`

#### الوظائف:
- اختيار نمط الجدولة (يومي، أسبوعي، شهري، أيام العمل، مخصص)
- عرض أمثلة حية لكل نمط
- واجهة بطاقات تفاعلية
- أيقونات توضيحية ملونة

#### الأنماط المدعومة:
- **يومياً**: تشغيل يومي في وقت محدد
- **أسبوعياً**: تشغيل أسبوعي في أيام ووقت محددين
- **شهرياً**: تشغيل شهري في اليوم الأول
- **أيام العمل**: من الاثنين إلى الجمعة
- **مخصص**: إعداد متقدم للمستخدمين المتقدمين

### 3. TimeSelector
**الملف:** `frontend/src/components/TimeSelector.tsx`

#### الميزات:
- أوقات محددة مسبقاً شائعة
- إدخال وقت مخصص
- أيقونات توضيحية (شمس/قمر)
- دعم تنسيق 12/24 ساعة
- معاينة الوقت المحدد

#### الأوقات المحددة مسبقاً:
- 12:00 ص، 2:00 ص، 6:00 ص، 8:00 ص
- 12:00 ظ، 6:00 م، 8:00 م، 10:00 م

### 4. RadioButton
**الملف:** `frontend/src/components/RadioButton.tsx`

#### الخصائص:
- تخطيطات متعددة (عمودي، أفقي، شبكة)
- أحجام مختلفة (صغير، متوسط، كبير)
- دعم الأيقونات والأوصاف
- تصميم متجاوب

### 5. TaskTypeSelector
**الملف:** `frontend/src/components/TaskTypeSelector.tsx`

#### الميزات:
- اختيار نوع المهمة بواجهة بطاقات تفاعلية
- معلومات تفصيلية لكل نوع مهمة
- أيقونات توضيحية ملونة
- معاينة المعاملات لكل نوع

#### الأنواع المدعومة:
- **نسخ احتياطي**: نسخ احتياطي كامل لقاعدة البيانات
- **تنظيف النسخ القديمة**: حذف النسخ الاحتياطية القديمة
- **صيانة النظام**: مهام صيانة عامة

### 6. StatusSelector
**الملف:** `frontend/src/components/StatusSelector.tsx`

#### الميزات:
- اختيار حالة المهمة بواجهة تفاعلية
- معلومات توضيحية لكل حالة
- ألوان مميزة لكل حالة
- تخطيطات متعددة (أفقي/عمودي)

#### الحالات المدعومة:
- **نشط**: المهمة تعمل وفقاً للجدولة
- **متوقف مؤقتاً**: المهمة متوقفة ويمكن تفعيلها
- **معطل**: المهمة معطلة بالكامل

### 7. SchedulingDemo
**الملف:** `frontend/src/components/SchedulingDemo.tsx`

#### الغرض:
- عرض تجريبي للنظام المحسن
- أمثلة سريعة للاستخدام
- معاينة النتائج
- واجهة تعليمية

### 8. EnhancedFormDemo
**الملف:** `frontend/src/components/EnhancedFormDemo.tsx`

#### الغرض:
- عرض شامل لجميع المكونات المحسنة
- نموذج تفاعلي كامل
- معاينة البيانات في الوقت الفعلي
- دليل استخدام المكونات

## نظام إدارة الأخطاء المحسن

### 9. ScheduledTaskErrorService
**الملف:** `frontend/src/services/scheduledTaskErrorService.ts`

#### الوظائف الرئيسية:
- **تسجيل الأخطاء**: تسجيل أخطاء المهام في نظام السجلات مع التصنيف
- **إدارة الأخطاء**: حل وحذف الأخطاء مع تتبع الحالة
- **التصنيف الذكي**: تصنيف تلقائي للأخطاء حسب النوع والشدة
- **الإحصائيات**: تتبع شامل لإحصائيات الأخطاء

#### أنواع الأخطاء:
- **الشدة**: WARNING, ERROR, CRITICAL
- **الفئة**: EXECUTION, CONFIGURATION, SYSTEM, NETWORK

### 10. TaskErrorDisplay
**الملف:** `frontend/src/components/TaskErrorDisplay.tsx`

#### الميزات:
- عرض أخطاء المهام في بطاقات تفاعلية
- تصنيف بصري للأخطاء حسب الشدة والفئة
- إمكانية حل وحذف الأخطاء
- عرض تفاصيل الأخطاء القابلة للتوسيع

### 11. TaskErrorStats
**الملف:** `frontend/src/components/TaskErrorStats.tsx`

#### الوظائف:
- عرض إحصائيات شاملة للأخطاء
- تصنيف الأخطاء حسب الفئة والمهمة
- مؤشرات بصرية لحالة النظام
- روابط سريعة لسجلات النظام

### 12. useTaskErrorMonitoring
**الملف:** `frontend/src/hooks/useTaskErrorMonitoring.ts`

#### الوظائف:
- **مراقبة تلقائية**: رصد الأخطاء الجديدة وتسجيلها
- **حل تلقائي**: حل الأخطاء عند اختفائها
- **مراقبة الصحة**: تتبع المهام الحرجة والمتوقفة
- **تنبيهات ذكية**: إرسال تنبيهات للمهام المشكوك فيها

### 13. TaskErrorSystemDemo
**الملف:** `frontend/src/components/TaskErrorSystemDemo.tsx`

#### الغرض:
- عرض تجريبي شامل لنظام الأخطاء
- إنشاء أخطاء تجريبية للاختبار
- توضيح جميع ميزات النظام
- دليل استخدام تفاعلي

## التحسينات في تجربة المستخدم

### 1. الواجهة البديهية
- استبدال القوائم المنسدلة المعقدة بأزرار تفاعلية
- تجميع الخيارات المترابطة
- استخدام الألوان والأيقونات للتوضيح

### 2. التفاعل المحسن
- معاينة فورية للتغييرات
- ردود فعل بصرية واضحة
- انتقالات سلسة بين الحالات

### 3. إمكانية الوصول
- دعم كامل للوضع المظلم
- تصميم متجاوب لجميع الأحجام
- استخدام الألوان المتباينة

### 4. التوجيه والمساعدة
- أمثلة حية لكل نمط
- أوصاف توضيحية
- معاينة النتائج

## الاستخدام

### التكامل الأساسي

#### استخدام مكونات الإدخال المحسنة
```tsx
import { TextInput, TextArea } from './components/inputs';

// حقل نص محسن
<TextInput
  name="taskName"
  label="اسم المهمة"
  value={name}
  onChange={setName}
  placeholder="أدخل اسم المهمة..."
  required={true}
  icon={<FaCalendarAlt />}
/>

// منطقة نص محسنة
<TextArea
  name="description"
  label="الوصف"
  value={description}
  onChange={setDescription}
  rows={3}
  maxLength={500}
/>
```

#### استخدام TaskTypeSelector
```tsx
import TaskTypeSelector from './components/TaskTypeSelector';

<TaskTypeSelector
  value={taskType}
  onChange={setTaskType}
  backupPath="/var/backups"
  disabled={false}
/>
```

#### استخدام StatusSelector
```tsx
import StatusSelector from './components/StatusSelector';

<StatusSelector
  value={status}
  onChange={setStatus}
  layout="horizontal"
  disabled={false}
/>
```

#### استخدام CronBuilder المحسن
```tsx
import CronBuilder from './components/CronBuilder';

<CronBuilder
  value={cronExpression}
  onChange={setCronExpression}
/>
```

### المكونات التجريبية

#### عرض النماذج المحسنة
```tsx
import EnhancedFormDemo from './components/EnhancedFormDemo';

<EnhancedFormDemo />
```

#### عرض الجدولة المحسنة
```tsx
import SchedulingDemo from './components/SchedulingDemo';

<SchedulingDemo />
```

### نظام إدارة الأخطاء

#### استخدام خدمة الأخطاء
```tsx
import scheduledTaskErrorService from './services/scheduledTaskErrorService';

// تسجيل خطأ مهمة
const errorId = await scheduledTaskErrorService.logTaskError(
  taskId,
  taskName,
  'رسالة الخطأ',
  { details: 'تفاصيل إضافية' },
  'ERROR',
  'EXECUTION'
);

// حل خطأ
await scheduledTaskErrorService.resolveTaskError(
  errorId,
  'تم حل المشكلة'
);

// حذف خطأ
await scheduledTaskErrorService.deleteTaskError(errorId);
```

#### استخدام مكون عرض الأخطاء
```tsx
import TaskErrorDisplay from './components/TaskErrorDisplay';

<TaskErrorDisplay
  taskId={task.id}
  taskName={task.name}
  className="mt-3"
/>
```

#### استخدام إحصائيات الأخطاء
```tsx
import TaskErrorStats from './components/TaskErrorStats';

<TaskErrorStats className="mb-4" />
```

#### استخدام مراقبة الأخطاء
```tsx
import { useTaskErrorMonitoring, useTaskHealthMonitoring } from './hooks/useTaskErrorMonitoring';

// في المكون
const { criticalTasksCount, staleTasksCount } = useTaskHealthMonitoring(tasks);
useTaskErrorMonitoring(tasks);
```

#### عرض نظام الأخطاء التجريبي
```tsx
import TaskErrorSystemDemo from './components/TaskErrorSystemDemo';

<TaskErrorSystemDemo />
```

## التوافق

- ✅ React 18+
- ✅ TypeScript
- ✅ Tailwind CSS
- ✅ React Icons
- ✅ الوضع المظلم
- ✅ التصميم المتجاوب

## المتطلبات

```json
{
  "react": "^18.0.0",
  "react-icons": "^4.0.0",
  "tailwindcss": "^3.0.0"
}
```

## الملاحظات التقنية

- استخدام خدمات التاريخ والوقت الموجودة في المشروع
- التوافق مع نمط التصميم الحالي
- دعم المنطقة الزمنية لطرابلس
- تحسين الأداء مع React.memo حيث أمكن

## التطوير المستقبلي

- إضافة المزيد من الأنماط المحددة مسبقاً
- دعم التقويم الهجري
- إضافة تحقق متقدم من صحة البيانات
- تحسين إمكانية الوصول أكثر
