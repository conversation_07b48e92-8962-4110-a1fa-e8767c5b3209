import React, { useState, useEffect, useRef } from 'react';
import { FaClock, FaInfoCircle, FaPlay, FaCalendarAlt } from 'react-icons/fa';
import { formatDateTime, getCurrentTripoliDateTime } from '../services/dateTimeService';
import TimeSelector from './TimeSelector';
import FrequencySelector from './FrequencySelector';

interface CronBuilderProps {
  value: string;
  onChange: (cronExpression: string) => void;
  className?: string;
}

interface CronParts {
  minute: string;
  hour: string;
  day: string;
  month: string;
  dayOfWeek: string;
}



const CronBuilder: React.FC<CronBuilderProps> = ({ value, onChange, className = '' }) => {
  const [cronParts, setCronParts] = useState<CronParts>({
    minute: '0',
    hour: '*',
    day: '*',
    month: '*',
    dayOfWeek: '*'
  });

  const [presetType, setPresetType] = useState<string>('daily');
  const [customTime, setCustomTime] = useState<string>('02:00');
  const [selectedDays, setSelectedDays] = useState<string[]>([]);
  const [nextRunTime, setNextRunTime] = useState<string>('');
  const isInitializing = useRef(true);

  // تحليل تعبير Cron الموجود
  useEffect(() => {
    if (value && value.trim()) {
      const parts = value.trim().split(' ');
      if (parts.length >= 5) {
        const newCronParts = {
          minute: parts[0] || '0',
          hour: parts[1] || '*',
          day: parts[2] || '*',
          month: parts[3] || '*',
          dayOfWeek: parts[4] || '*'
        };

        // فقط تحديث إذا كانت القيم مختلفة فعلاً
        const currentExpression = `${cronParts.minute} ${cronParts.hour} ${cronParts.day} ${cronParts.month} ${cronParts.dayOfWeek}`;
        const newExpression = `${newCronParts.minute} ${newCronParts.hour} ${newCronParts.day} ${newCronParts.month} ${newCronParts.dayOfWeek}`;

        if (currentExpression !== newExpression) {
          setCronParts(newCronParts);

          // تحديث الوقت المخصص
          if (newCronParts.hour !== '*' && newCronParts.minute !== '*') {
            const hour = parseInt(newCronParts.hour);
            const minute = parseInt(newCronParts.minute);
            setCustomTime(`${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`);
          }

          // تحديد نوع القالب
          const matchingPreset = presets.find(p => p.cron === value.trim());
          if (matchingPreset) {
            setPresetType(matchingPreset.id);
          } else {
            setPresetType('custom');
          }
        }
      }
    }
    isInitializing.current = false;
  }, [value]);

  // حساب وقت التشغيل التالي عند تغيير التعبير
  useEffect(() => {
    if (value) {
      const nextRun = calculateNextRun(value);
      setNextRunTime(nextRun);
    }
  }, [value]);

  // دالة لتحديث تعبير Cron
  const updateCronExpression = (newCronParts: CronParts) => {
    const cronExpression = `${newCronParts.minute} ${newCronParts.hour} ${newCronParts.day} ${newCronParts.month} ${newCronParts.dayOfWeek}`;
    if (cronExpression !== value) {
      onChange(cronExpression);
    }
  };

  // حساب وقت التشغيل التالي
  const calculateNextRun = (cronExpression: string): string => {
    try {
      // هذه دالة مبسطة لحساب وقت التشغيل التالي
      // في التطبيق الحقيقي، يمكن استخدام مكتبة مثل cron-parser
      const now = getCurrentTripoliDateTime();
      const parts = cronExpression.split(' ');

      if (parts.length >= 5) {
        const minute = parts[0];
        const hour = parts[1];

        if (minute !== '*' && hour !== '*') {
          const nextRun = new Date(now);
          nextRun.setHours(parseInt(hour), parseInt(minute), 0, 0);

          // إذا كان الوقت قد مضى اليوم، اجعله غداً
          if (nextRun <= now) {
            nextRun.setDate(nextRun.getDate() + 1);
          }

          return formatDateTime(nextRun, 'datetime');
        }
      }

      return 'غير محدد';
    } catch (error) {
      return 'خطأ في الحساب';
    }
  };

  // القوالب المحددة مسبقاً المحسنة
  const presets = [
    {
      id: 'daily',
      name: 'يومياً',
      description: 'تشغيل يومي في وقت محدد',
      cron: '0 2 * * *',
      icon: <FaClock className="text-blue-500" />
    },
    {
      id: 'weekly',
      name: 'أسبوعياً',
      description: 'تشغيل أسبوعي في يوم ووقت محددين',
      cron: '0 3 * * 0',
      icon: <FaCalendarAlt className="text-green-500" />
    },
    {
      id: 'monthly',
      name: 'شهرياً',
      description: 'تشغيل شهري في اليوم الأول',
      cron: '0 4 1 * *',
      icon: <FaCalendarAlt className="text-purple-500" />
    },
    {
      id: 'workdays',
      name: 'أيام العمل',
      description: 'من الاثنين إلى الجمعة',
      cron: '0 8 * * 1-5',
      icon: <FaClock className="text-orange-500" />
    },
    {
      id: 'custom',
      name: 'مخصص',
      description: 'إعداد مخصص متقدم',
      cron: '',
      icon: <FaInfoCircle className="text-gray-500" />
    }
  ];

  const handlePresetChange = (presetId: string) => {
    setPresetType(presetId);
    const preset = presets.find(p => p.id === presetId);
    if (preset && preset.cron) {
      const parts = preset.cron.split(' ');
      const newCronParts = {
        minute: parts[0] || '0',
        hour: parts[1] || '*',
        day: parts[2] || '*',
        month: parts[3] || '*',
        dayOfWeek: parts[4] || '*'
      };
      setCronParts(newCronParts);
      updateCronExpression(newCronParts);

      // تحديث الوقت المخصص بناءً على القالب
      if (parts[1] && parts[1] !== '*') {
        const hour = parseInt(parts[1]);
        const minute = parseInt(parts[0] || '0');
        setCustomTime(`${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`);
      }
    }
  };

  // دالة لتحديث الوقت المخصص
  const handleTimeChange = (time: string) => {
    setCustomTime(time);
    const [hour, minute] = time.split(':');

    let newCronParts = { ...cronParts };
    newCronParts.minute = minute;
    newCronParts.hour = hour;

    // تطبيق التغييرات حسب نوع القالب
    if (presetType === 'daily') {
      newCronParts.day = '*';
      newCronParts.month = '*';
      newCronParts.dayOfWeek = '*';
    } else if (presetType === 'workdays') {
      newCronParts.day = '*';
      newCronParts.month = '*';
      newCronParts.dayOfWeek = '1-5';
    }

    setCronParts(newCronParts);
    updateCronExpression(newCronParts);
  };

  // دالة لتحديد أيام الأسبوع
  const handleDayToggle = (dayValue: string) => {
    let newSelectedDays = [...selectedDays];

    if (newSelectedDays.includes(dayValue)) {
      newSelectedDays = newSelectedDays.filter(d => d !== dayValue);
    } else {
      newSelectedDays.push(dayValue);
    }

    setSelectedDays(newSelectedDays);

    // تحديث cron expression
    let newCronParts = { ...cronParts };
    if (newSelectedDays.length > 0) {
      newCronParts.dayOfWeek = newSelectedDays.sort().join(',');
    } else {
      newCronParts.dayOfWeek = '*';
    }

    setCronParts(newCronParts);
    updateCronExpression(newCronParts);
  };

  const handlePartChange = (part: keyof CronParts, newValue: string) => {
    const newCronParts = {
      ...cronParts,
      [part]: newValue
    };
    setCronParts(newCronParts);
    setPresetType('custom');
    updateCronExpression(newCronParts);
  };

  // خيارات الدقائق
  const minuteOptions = [
    { value: '*', label: 'كل دقيقة' },
    { value: '0', label: 'الدقيقة 0' },
    { value: '15', label: 'الدقيقة 15' },
    { value: '30', label: 'الدقيقة 30' },
    { value: '45', label: 'الدقيقة 45' },
    { value: '*/5', label: 'كل 5 دقائق' },
    { value: '*/10', label: 'كل 10 دقائق' },
    { value: '*/15', label: 'كل 15 دقيقة' },
    { value: '*/30', label: 'كل 30 دقيقة' }
  ];

  // خيارات الساعات
  const hourOptions = [
    { value: '*', label: 'كل ساعة' },
    ...Array.from({ length: 24 }, (_, i) => ({
      value: i.toString(),
      label: `${i.toString().padStart(2, '0')}:00`
    }))
  ];

  // خيارات أيام الشهر
  const dayOptions = [
    { value: '*', label: 'كل يوم' },
    ...Array.from({ length: 31 }, (_, i) => ({
      value: (i + 1).toString(),
      label: `اليوم ${i + 1}`
    }))
  ];

  // خيارات الشهور
  const monthOptions = [
    { value: '*', label: 'كل شهر' },
    { value: '1', label: 'يناير' },
    { value: '2', label: 'فبراير' },
    { value: '3', label: 'مارس' },
    { value: '4', label: 'أبريل' },
    { value: '5', label: 'مايو' },
    { value: '6', label: 'يونيو' },
    { value: '7', label: 'يوليو' },
    { value: '8', label: 'أغسطس' },
    { value: '9', label: 'سبتمبر' },
    { value: '10', label: 'أكتوبر' },
    { value: '11', label: 'نوفمبر' },
    { value: '12', label: 'ديسمبر' }
  ];

  // خيارات أيام الأسبوع
  const dayOfWeekOptions = [
    { value: '*', label: 'كل يوم' },
    { value: '0', label: 'الأحد' },
    { value: '1', label: 'الاثنين' },
    { value: '2', label: 'الثلاثاء' },
    { value: '3', label: 'الأربعاء' },
    { value: '4', label: 'الخميس' },
    { value: '5', label: 'الجمعة' },
    { value: '6', label: 'السبت' },
    { value: '1-5', label: 'أيام العمل' },
    { value: '0,6', label: 'عطلة نهاية الأسبوع' }
  ];

  // أيام الأسبوع
  const weekDays = [
    { value: '1', label: 'الاثنين', short: 'ن' },
    { value: '2', label: 'الثلاثاء', short: 'ث' },
    { value: '3', label: 'الأربعاء', short: 'ر' },
    { value: '4', label: 'الخميس', short: 'خ' },
    { value: '5', label: 'الجمعة', short: 'ج' },
    { value: '6', label: 'السبت', short: 'س' },
    { value: '0', label: 'الأحد', short: 'ح' }
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* عنوان القسم */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
          <FaClock className="ml-2 text-primary-600" />
          جدولة التشغيل
        </h3>
        {nextRunTime && (
          <div className="text-sm text-gray-600 dark:text-gray-400">
            <FaPlay className="inline ml-1 text-green-500" />
            التشغيل التالي: {nextRunTime}
          </div>
        )}
      </div>

      {/* اختيار نمط الجدولة */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          اختر نمط الجدولة
        </label>
        <FrequencySelector
          value={presetType}
          onChange={handlePresetChange}
          showExamples={true}
        />
      </div>

      {/* إعدادات الوقت */}
      {(presetType === 'daily' || presetType === 'weekly' || presetType === 'workdays' || presetType === 'monthly') && (
        <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <TimeSelector
            value={customTime}
            onChange={handleTimeChange}
            label="وقت التشغيل"
            showPresets={true}
            format24={true}
          />
        </div>
      )}

      {/* اختيار أيام الأسبوع للجدولة الأسبوعية */}
      {presetType === 'weekly' && (
        <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            اختر أيام الأسبوع
          </label>
          <div className="grid grid-cols-7 gap-2">
            {weekDays.map(day => (
              <button
                key={day.value}
                type="button"
                onClick={() => handleDayToggle(day.value)}
                className={`p-3 rounded-lg text-center transition-all duration-200 ${
                  selectedDays.includes(day.value)
                    ? 'bg-primary-600 text-white'
                    : 'bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
                }`}
                title={day.label}
              >
                <div className="text-xs font-medium">{day.short}</div>
                <div className="text-xs mt-1">{day.label}</div>
              </button>
            ))}
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
            انقر على الأيام لتحديدها أو إلغاء تحديدها
          </p>
        </div>
      )}

      {/* إعدادات مخصصة متقدمة */}
      {presetType === 'custom' && (
        <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            إعدادات مخصصة متقدمة
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {/* الدقيقة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                الدقيقة
              </label>
              <select
                value={cronParts.minute}
                onChange={(e) => handlePartChange('minute', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm"
              >
                {minuteOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* الساعة */}
            <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              الساعة
            </label>
            <select
              value={cronParts.hour}
              onChange={(e) => handlePartChange('hour', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm"
            >
              {hourOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            </div>

            {/* اليوم */}
            <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              اليوم
            </label>
            <select
              value={cronParts.day}
              onChange={(e) => handlePartChange('day', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm"
            >
              {dayOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            </div>

            {/* الشهر */}
            <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              الشهر
            </label>
            <select
              value={cronParts.month}
              onChange={(e) => handlePartChange('month', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm"
            >
              {monthOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            </div>

            {/* يوم الأسبوع */}
            <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              يوم الأسبوع
            </label>
            <select
              value={cronParts.dayOfWeek}
              onChange={(e) => handlePartChange('dayOfWeek', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm"
            >
              {dayOfWeekOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            </div>
          </div>
        </div>
      )}

      {/* عرض تعبير Cron */}
      <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            تعبير Cron:
          </span>
          <code className="bg-white dark:bg-gray-900 px-2 py-1 rounded text-sm font-mono text-primary-600 dark:text-primary-400 border border-gray-300 dark:border-gray-600">
            {value || `${cronParts.minute} ${cronParts.hour} ${cronParts.day} ${cronParts.month} ${cronParts.dayOfWeek}`}
          </code>
        </div>
      </div>

      {/* معلومات إضافية */}
      <div className="bg-blue-50 dark:bg-blue-900/30 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
        <div className="flex items-start">
          <FaInfoCircle className="text-blue-500 mt-0.5 ml-2 flex-shrink-0" />
          <div className="text-blue-700 dark:text-blue-300 text-sm">
            <p className="font-medium mb-1">تنسيق تعبير Cron:</p>
            <p className="text-xs">دقيقة ساعة يوم شهر يوم_الأسبوع</p>
            <p className="text-xs mt-1">استخدم * للدلالة على "أي قيمة"</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CronBuilder;
