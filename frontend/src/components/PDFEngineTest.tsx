/**
 * مكون اختبار محركات PDF
 * يوفر واجهة لاختبار وعرض حالة محركات PDF المختلفة
 */

import React, { useState, useEffect } from 'react';
import { FaFilePdf, FaCheckCircle, FaExclamationTriangle, FaTimesCircle, FaSync, FaCog } from 'react-icons/fa';
import { testServerPDFEngines, ServerPDFExportService } from '../services/ServerPDFExportService';

interface PDFEngine {
  available: boolean;
  description: string;
  priority: number;
}

interface PDFEnginesData {
  engines: { [key: string]: PDFEngine };
  preferred_engine: string;
  total_available: number;
}

const PDFEngineTest: React.FC = () => {
  const [enginesData, setEnginesData] = useState<PDFEnginesData | null>(null);
  const [testResult, setTestResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isTestingEngine, setIsTestingEngine] = useState(false);

  // جلب معلومات المحركات عند تحميل المكون
  useEffect(() => {
    loadEnginesData();
  }, []);

  const loadEnginesData = async () => {
    try {
      setIsLoading(true);
      const service = new ServerPDFExportService();
      const data = await service.getAvailableEngines();
      setEnginesData(data);
    } catch (error) {
      console.error('خطأ في جلب معلومات المحركات:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const testEngines = async () => {
    try {
      setIsTestingEngine(true);
      const result = await testServerPDFEngines();
      setTestResult(result);
    } catch (error) {
      console.error('خطأ في اختبار المحركات:', error);
      setTestResult({
        status: 'error',
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      });
    } finally {
      setIsTestingEngine(false);
    }
  };

  const getEngineStatusIcon = (engine: PDFEngine) => {
    if (engine.available) {
      return <FaCheckCircle className="text-green-500" />;
    } else {
      return <FaTimesCircle className="text-red-500" />;
    }
  };

  const getEngineStatusText = (engine: PDFEngine) => {
    return engine.available ? 'متاح' : 'غير متاح';
  };

  const getEngineStatusColor = (engine: PDFEngine) => {
    return engine.available 
      ? 'text-green-600 dark:text-green-400' 
      : 'text-red-600 dark:text-red-400';
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <FaCog className="text-2xl text-blue-600 dark:text-blue-400" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            اختبار محركات PDF
          </h2>
        </div>
        
        <div className="flex gap-2">
          <button
            onClick={loadEnginesData}
            disabled={isLoading}
            className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center gap-2 disabled:opacity-50"
          >
            <FaSync className={isLoading ? 'animate-spin' : ''} />
            تحديث
          </button>
          
          <button
            onClick={testEngines}
            disabled={isTestingEngine || !enginesData}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2 disabled:opacity-50"
          >
            <FaFilePdf className={isTestingEngine ? 'animate-pulse' : ''} />
            {isTestingEngine ? 'جاري الاختبار...' : 'اختبار المحركات'}
          </button>
        </div>
      </div>

      {/* معلومات المحركات */}
      {enginesData && (
        <div className="space-y-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {enginesData.total_available}
              </div>
              <div className="text-sm text-blue-700 dark:text-blue-300">محركات متاحة</div>
            </div>
            
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800">
              <div className="text-lg font-semibold text-green-600 dark:text-green-400">
                {enginesData.preferred_engine}
              </div>
              <div className="text-sm text-green-700 dark:text-green-300">المحرك المفضل</div>
            </div>
            
            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-800">
              <div className="text-lg font-semibold text-purple-600 dark:text-purple-400">
                {Object.keys(enginesData.engines).length}
              </div>
              <div className="text-sm text-purple-700 dark:text-purple-300">إجمالي المحركات</div>
            </div>
          </div>

          {/* قائمة المحركات */}
          <div className="space-y-3">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              تفاصيل المحركات
            </h3>
            
            {Object.entries(enginesData.engines).map(([engineName, engine]) => (
              <div
                key={engineName}
                className={`flex items-center justify-between p-4 rounded-lg border ${
                  engineName === enginesData.preferred_engine
                    ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
                    : 'bg-gray-50 dark:bg-gray-700/50 border-gray-200 dark:border-gray-600'
                }`}
              >
                <div className="flex items-center gap-3">
                  {getEngineStatusIcon(engine)}
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        {engineName}
                      </span>
                      {engineName === enginesData.preferred_engine && (
                        <span className="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900/40 text-blue-700 dark:text-blue-300 rounded-full">
                          مفضل
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {engine.description}
                    </div>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className={`font-medium ${getEngineStatusColor(engine)}`}>
                    {getEngineStatusText(engine)}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    أولوية: {engine.priority}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* نتائج الاختبار */}
      {testResult && (
        <div className="border-t border-gray-200 dark:border-gray-600 pt-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            نتائج الاختبار
          </h3>
          
          <div className={`p-4 rounded-lg border ${
            testResult.status === 'success'
              ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
              : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
          }`}>
            <div className="flex items-start gap-3">
              {testResult.status === 'success' ? (
                <FaCheckCircle className="text-green-500 mt-1" />
              ) : (
                <FaExclamationTriangle className="text-red-500 mt-1" />
              )}
              
              <div className="flex-1">
                <div className={`font-medium ${
                  testResult.status === 'success'
                    ? 'text-green-700 dark:text-green-300'
                    : 'text-red-700 dark:text-red-300'
                }`}>
                  {testResult.status === 'success' ? 'نجح الاختبار' : 'فشل الاختبار'}
                </div>
                
                <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {testResult.message}
                </div>
                
                {testResult.status === 'success' && (
                  <div className="mt-3 space-y-2">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium">المحرك المستخدم:</span>
                        <span className="ml-2 text-blue-600 dark:text-blue-400">
                          {testResult.engine}
                        </span>
                      </div>
                      <div>
                        <span className="font-medium">حجم الملف:</span>
                        <span className="ml-2 text-green-600 dark:text-green-400">
                          {testResult.file_size ? `${(testResult.file_size / 1024).toFixed(2)} KB` : 'غير معروف'}
                        </span>
                      </div>
                    </div>
                    
                    {testResult.output_path && (
                      <div className="text-xs text-gray-500 dark:text-gray-400 font-mono bg-gray-100 dark:bg-gray-700 p-2 rounded">
                        مسار الملف: {testResult.output_path}
                      </div>
                    )}
                  </div>
                )}
                
                {testResult.error && (
                  <div className="mt-2 text-sm text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20 p-2 rounded">
                    خطأ: {testResult.error}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* رسالة تحميل */}
      {isLoading && (
        <div className="text-center py-8">
          <FaSync className="animate-spin text-2xl text-blue-600 dark:text-blue-400 mx-auto mb-2" />
          <div className="text-gray-600 dark:text-gray-400">جاري تحميل معلومات المحركات...</div>
        </div>
      )}
    </div>
  );
};

export default PDFEngineTest;
