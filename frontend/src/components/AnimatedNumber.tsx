/**
 * مكون الأرقام المتحركة المحسن
 * يدمج React Count-up مع نظام الأرقام المختصرة الموجود
 * يدعم العملات والتنسيق العربي والوضع المظلم
 */

import React, { useState, useEffect, useRef } from 'react';
import CountUp from 'react-countup';
import { compactNumberService, type CompactNumberResult, type UnitType } from '../services/compactNumberService';

// واجهة خصائص المكون
interface AnimatedNumberProps {
  /** المبلغ المراد عرضه */
  end: number;
  /** القيمة الابتدائية (افتراضي: 0) */
  start?: number;
  /** مدة الرسم المتحرك بالثواني (افتراضي: 2) */
  duration?: number;
  /** تأخير البدء بالثواني (افتراضي: 0) */
  delay?: number;
  /** إظهار رمز العملة أم لا (افتراضي: false) */
  showCurrency?: boolean;
  /** فئة CSS إضافية */
  className?: string;
  /** الحد الأدنى لبدء الاختصار (افتراضي: 1000) */
  compactThreshold?: number;
  /** إظهار الرقم الكامل تحت المختصر (افتراضي: true) */
  showFullNumber?: boolean;
  /** نوع الوحدات (افتراضي: 'english') */
  unitType?: UnitType;
  /** عدد الأرقام العشرية للرقم المختصر (افتراضي: 1) */
  decimalPlaces?: number;
  /** حجم العرض */
  size?: 'small' | 'medium' | 'large';
  /** تفعيل الرسم المتحرك (افتراضي: true) */
  enableAnimation?: boolean;
  /** دالة callback عند انتهاء الرسم المتحرك */
  onEnd?: () => void;
  /** دالة callback أثناء الرسم المتحرك */
  onUpdate?: (args: { pauseResume: () => void; reset: () => void; start: () => void }) => void;
}

const AnimatedNumber: React.FC<AnimatedNumberProps> = ({
  end,
  start = 0,
  duration = 2,
  delay = 0,
  showCurrency = false,
  className = '',
  compactThreshold = 1000,
  showFullNumber = true,
  unitType = 'english',
  decimalPlaces = 1,
  size = 'medium',
  enableAnimation = true,
  onEnd,
  onUpdate
}) => {
  const [finalResult, setFinalResult] = useState<CompactNumberResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [shouldAnimate, setShouldAnimate] = useState(false);
  const animationTriggered = useRef(false);

  // تحديد أحجام النص حسب الحجم المطلوب
  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return {
          compact: 'text-lg font-bold',
          full: 'text-xs'
        };
      case 'large':
        return {
          compact: 'text-4xl font-bold',
          full: 'text-sm'
        };
      default: // medium
        return {
          compact: 'text-3xl font-bold',
          full: 'text-xs'
        };
    }
  };

  const sizeClasses = getSizeClasses();

  // تحضير النتيجة النهائية
  useEffect(() => {
    const prepareFinalResult = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const options = {
          compactThreshold,
          unitType,
          decimalPlaces,
          showFullNumber
        };

        let result: CompactNumberResult;
        if (showCurrency) {
          result = await compactNumberService.formatCompactCurrency(end, options);
        } else {
          result = await compactNumberService.formatCompact(end, options);
        }

        setFinalResult(result);
        setIsLoading(false);

        // تفعيل الرسم المتحرك بعد تحضير البيانات
        if (enableAnimation && !animationTriggered.current) {
          setTimeout(() => {
            setShouldAnimate(true);
            animationTriggered.current = true;
          }, delay * 1000);
        }

      } catch (err) {
        const error = err instanceof Error ? err : new Error('Unknown formatting error');
        setError(error);
        setIsLoading(false);
      }
    };

    prepareFinalResult();
  }, [end, showCurrency, compactThreshold, unitType, decimalPlaces, showFullNumber, enableAnimation, delay]);

  // دالة تنسيق الأرقام أثناء الرسم المتحرك
  const formatAnimatedValue = (value: number): string => {
    if (!finalResult) return value.toString();

    // إذا كان الرقم النهائي مختصر، نحتاج لحساب النسبة
    if (finalResult.isCompacted) {
      // استخراج الرقم من النتيجة المختصرة
      const compactMatch = finalResult.compact.match(/[\d.]+/);
      if (compactMatch) {
        const compactValue = parseFloat(compactMatch[0]);
        const ratio = value / end;
        const currentCompactValue = compactValue * ratio;
        
        // استخراج الوحدة والعملة
        const unit = finalResult.compact.replace(/[\d.\s]+/g, '').trim();
        
        return `${currentCompactValue.toFixed(decimalPlaces)} ${unit}`;
      }
    }

    // إذا لم يكن مختصر، عرض الرقم كما هو
    if (showCurrency) {
      return `${value.toLocaleString('ar-LY', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      })} د.ل`;
    }

    return value.toLocaleString('ar-LY', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    });
  };

  // معالجة حالات الخطأ والتحميل
  if (error) {
    return (
      <div className={`text-red-500 ${className}`}>
        <div className={`${sizeClasses.compact} text-red-500`}>
          خطأ
        </div>
        {showFullNumber && (
          <div className={`${sizeClasses.full} text-red-400 mt-1`}>
            خطأ في التحميل
          </div>
        )}
      </div>
    );
  }

  if (isLoading || !finalResult) {
    return (
      <div className={`text-gray-400 animate-pulse ${className}`}>
        <div className={`${sizeClasses.compact} text-gray-400`}>
          ...
        </div>
        {showFullNumber && (
          <div className={`${sizeClasses.full} text-gray-300 mt-1`}>
            جاري التحميل...
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`${className}`} title={`القيمة الأصلية: ${finalResult.originalValue}`}>
      {/* الرقم المختصر مع الرسم المتحرك */}
      <div className={`${sizeClasses.compact} text-secondary-900 dark:text-secondary-100`}>
        {enableAnimation && shouldAnimate ? (
          <CountUp
            start={start}
            end={end}
            duration={duration}
            formattingFn={formatAnimatedValue}
            onEnd={onEnd}
            onUpdate={onUpdate}
            preserveValue
          />
        ) : (
          finalResult.compact
        )}
      </div>
      
      {/* الرقم الكامل أو نقاط بديلة */}
      {showFullNumber && (
        <div className={`${sizeClasses.full} text-secondary-500 dark:text-secondary-400 mt-1`}>
          {finalResult.isCompacted ? finalResult.full : '• • •'}
        </div>
      )}
    </div>
  );
};

export default AnimatedNumber;

/**
 * Hook لاستخدام الأرقام المتحركة بشكل مباشر
 */
export const useAnimatedNumber = () => {
  const [isAnimating, setIsAnimating] = useState(false);

  const startAnimation = (callback?: () => void) => {
    setIsAnimating(true);
    if (callback) callback();
  };

  const stopAnimation = (callback?: () => void) => {
    setIsAnimating(false);
    if (callback) callback();
  };

  return {
    isAnimating,
    startAnimation,
    stopAnimation
  };
};

/**
 * مكون مبسط للأرقام المتحركة بدون عملة
 */
export const AnimatedCompactNumber: React.FC<Omit<AnimatedNumberProps, 'showCurrency'>> = (props) => {
  return <AnimatedNumber {...props} showCurrency={false} />;
};

/**
 * مكون مبسط للعملة المتحركة
 */
export const AnimatedCompactCurrency: React.FC<Omit<AnimatedNumberProps, 'showCurrency'>> = (props) => {
  return <AnimatedNumber {...props} showCurrency={true} />;
};
