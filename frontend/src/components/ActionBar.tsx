import React from 'react';

interface ActionBarProps {
  title?: string;
  subtitle?: string | React.ReactNode;
  primaryAction?: React.ReactNode;
  secondaryActions?: React.ReactNode;
  className?: string;
  layout?: 'default' | 'centered' | 'split';
}

const ActionBar: React.FC<ActionBarProps> = ({
  title,
  subtitle,
  primaryAction,
  secondaryActions,
  className = '',
  layout = 'default'
}) => {
  const getLayoutClasses = () => {
    switch (layout) {
      case 'centered':
        return 'flex flex-col items-center text-center gap-4';
      case 'split':
        return 'flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between';
      default:
        return 'flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between';
    }
  };

  return (
    <div className={`${getLayoutClasses()} ${className}`}>
      {/* العنوان والوصف */}
      {(title || subtitle) && (
        <div className="flex-1">
          {title && (
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              {title}
            </h2>
          )}
          {subtitle && (
            <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {subtitle}
            </div>
          )}
        </div>
      )}

      {/* الإجراءات */}
      <div className="flex flex-col sm:flex-row gap-3 items-end sm:items-end">
        {/* الإجراءات الثانوية */}
        {secondaryActions && (
          <div className="flex flex-wrap gap-2 items-end">
            {secondaryActions}
          </div>
        )}

        {/* الإجراء الرئيسي */}
        {primaryAction && (
          <div className="flex items-end">
            {primaryAction}
          </div>
        )}
      </div>
    </div>
  );
};

export default ActionBar;
