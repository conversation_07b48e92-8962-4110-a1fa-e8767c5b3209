import React from 'react';
import { FaTimes } from 'react-icons/fa';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full' | 'mobile-full';
  zIndex?: 'base' | 'high' | 'highest';
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children, size = 'md', zIndex = 'base' }) => {
  if (!isOpen) return null;

  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
    full: 'max-w-none w-full h-full',
    'mobile-full': 'w-full h-full md:max-w-6xl md:h-auto'
  };

  const zIndexClasses = {
    base: 'z-50',
    high: 'z-[10000]',
    highest: 'z-[10001]'
  };

  return (
    <div className={`fixed inset-0 ${zIndexClasses[zIndex]} overflow-y-auto modal-scrollbar`}>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      ></div>

      {/* Modal */}
      <div className={`flex min-h-full items-center justify-center ${size === 'full' || size === 'mobile-full' ? 'p-2 md:p-4' : 'p-4'}`}>
        <div className={`relative w-full ${sizeClasses[size]} transform overflow-hidden ${size === 'full' || size === 'mobile-full' ? 'rounded-lg md:rounded-2xl' : 'rounded-lg'} bg-white dark:bg-gray-800 shadow-xl transition-all modal-subtle-border ${size === 'full' || size === 'mobile-full' ? 'flex flex-col' : ''}`}>
          {/* Header */}
          <div className={`flex items-center justify-between ${size === 'full' || size === 'mobile-full' ? 'p-4 md:p-6' : 'p-6'} border-b border-gray-200/60 dark:border-gray-600/40 ${size === 'full' || size === 'mobile-full' ? 'flex-shrink-0' : ''}`}>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {title}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <FaTimes className="h-5 w-5" />
            </button>
          </div>

          {/* Content */}
          <div className={`${size === 'full' || size === 'mobile-full' ? 'p-4 md:p-6 flex-1 overflow-hidden' : 'p-6'}`}>
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Modal;
