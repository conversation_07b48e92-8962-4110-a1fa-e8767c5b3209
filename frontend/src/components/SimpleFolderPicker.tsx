import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaInfoCircle, FaDesktop } from 'react-icons/fa';
import FolderPickerModal from './FolderPickerModal';
import FolderSelectionSuccessModal from './FolderSelectionSuccessModal';
import FolderSelectionErrorModal from './FolderSelectionErrorModal';
import RemoteDeviceWarning from './RemoteDeviceWarning';
import api from '../lib/axios';
import { useIsMainServer } from '../hooks/useDeviceDetection';

interface SimpleFolderPickerProps {
  value: string;
  onChange: (path: string) => void;
  onTest?: () => void;
  placeholder?: string;
  disabled?: boolean;
}

const SimpleFolderPicker: React.FC<SimpleFolderPickerProps> = ({
  value,
  onChange,
  onTest,
  placeholder = "أدخل مسار المجلد...",
  disabled = false
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSelectingFromSystem, setIsSelectingFromSystem] = useState(false);

  // استخدام hook تمييز الأجهزة الجديد
  const { isMainServer } = useIsMainServer();

  // State للنوافذ الجديدة
  const [successModal, setSuccessModal] = useState({
    isOpen: false,
    folderPath: '',
    absolutePath: ''
  });
  const [errorModal, setErrorModal] = useState({
    isOpen: false,
    error: ''
  });



  const handleSelectFolder = () => {
    if (disabled) return;

    // التحقق من أن الجهاز هو الخادم الرئيسي
    if (!isMainServer) {
      setErrorModal({
        isOpen: true,
        error: 'هذه الميزة متاحة فقط على الخادم الرئيسي للتطبيق'
      });
      return;
    }

    setIsModalOpen(true);
  };

  const handleModalSelect = (path: string) => {
    onChange(path);
    setIsModalOpen(false);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  // اختيار المجلد من النظام باستخدام الخلفية
  const handleSelectFromSystem = async () => {
    if (disabled) return;

    // التحقق من أن الجهاز هو الخادم الرئيسي
    if (!isMainServer) {
      setErrorModal({
        isOpen: true,
        error: 'هذه الميزة متاحة فقط على الخادم الرئيسي للتطبيق'
      });
      return;
    }

    setIsSelectingFromSystem(true);
    try {
      const response = await api.post('/api/settings/select-folder', {}, {
        timeout: 300000 // 5 دقائق
      });
      const data = response.data;

      if (data.success && data.folder_path) {
        onChange(data.folder_path);
        // إظهار نافذة النجاح
        setSuccessModal({
          isOpen: true,
          folderPath: data.folder_path,
          absolutePath: data.absolute_path || data.folder_path
        });
      } else {
        // إظهار نافذة الخطأ
        setErrorModal({
          isOpen: true,
          error: data.error || 'خطأ غير معروف في اختيار المجلد'
        });
      }
    } catch (error: any) {
      console.error('Error selecting folder from system:', error);
      // إظهار نافذة الخطأ
      setErrorModal({
        isOpen: true,
        error: error.response?.data?.error || error.message || 'خطأ في الاتصال بالخادم'
      });
    } finally {
      setIsSelectingFromSystem(false);
    }
  };

  return (
    <>
      <div className="space-y-3">
        {/* حقل إدخال المسار مع أزرار التحكم */}
        <div className="flex items-center space-x-3 space-x-reverse">
          <div className="flex-1">
            <input
              type="text"
              value={value}
              onChange={(e) => onChange(e.target.value)}
              placeholder={
                !isMainServer
                  ? "إعداد مسار النسخ الاحتياطية متاح فقط على الخادم الرئيسي"
                  : placeholder
              }
              disabled={disabled || !isMainServer}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            />
          </div>

          <button
            type="button"
            onClick={handleSelectFromSystem}
            disabled={disabled || isSelectingFromSystem || !isMainServer}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center whitespace-nowrap disabled:opacity-50 disabled:cursor-not-allowed"
            title={
              !isMainServer
                ? "هذه الميزة متاحة فقط على الخادم الرئيسي"
                : "اختيار مجلد من النظام مباشرة"
            }
          >
            {isSelectingFromSystem ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                انتظار اختيار المجلد...
              </>
            ) : (
              <>
                <FaDesktop className="ml-2" />
                من النظام
              </>
            )}
          </button>

          <button
            type="button"
            onClick={handleSelectFolder}
            disabled={disabled || !isMainServer}
            className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition-colors flex items-center whitespace-nowrap disabled:opacity-50 disabled:cursor-not-allowed"
            title={
              !isMainServer
                ? "هذه الميزة متاحة فقط على الخادم الرئيسي"
                : "اختيار مجلد باستخدام النافذة المخصصة"
            }
          >
            <FaFolder className="ml-2" />
            نافذة مخصصة
          </button>

          {onTest && (
            <button
              type="button"
              onClick={onTest}
              disabled={disabled || !value.trim() || !isMainServer}
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors flex items-center whitespace-nowrap disabled:opacity-50 disabled:cursor-not-allowed"
              title={
                !isMainServer
                  ? "هذه الميزة متاحة فقط على الخادم الرئيسي"
                  : "اختبار المسار والتأكد من صحته"
              }
            >
              <FaCheck className="ml-2" />
              اختبار
            </button>
          )}
        </div>

        {/* تحذير للأجهزة البعيدة */}
        <RemoteDeviceWarning
          customMessage="إعداد مسار النسخ الاحتياطية متاح فقط على الخادم الرئيسي للتطبيق. هذا الإعداد يتطلب وصول مباشر إلى نظام الملفات المحلي للخادم."
          variant="error"
          hideIfMainServer={true}
        />

        {/* معلومات الحلول المتاحة */}
        <div className="space-y-2">
          <div className={`flex items-start p-3 rounded-lg text-sm ${
            isMainServer
              ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
              : 'bg-gray-50 dark:bg-gray-900/30 text-gray-500 dark:text-gray-400'
          }`}>
            <FaDesktop className="ml-2 mt-0.5 flex-shrink-0" />
            <div>
              <div className="font-medium">
                اختيار من النظام {isMainServer ? '(مُوصى به)' : '(غير متاح)'}
              </div>
              <div className="text-xs mt-1">
                {isMainServer
                  ? 'يفتح نافذة اختيار المجلدات الأصلية للنظام ويعطي المسار الفعلي'
                  : 'متاح فقط على الخادم الرئيسي - يتطلب وصول مباشر لنظام الملفات'
                }
              </div>
            </div>
          </div>

          <div className={`flex items-start p-3 rounded-lg text-sm ${
            isMainServer
              ? 'bg-green-50 dark:bg-green-900/30 text-green-700 dark:text-green-300'
              : 'bg-gray-50 dark:bg-gray-900/30 text-gray-500 dark:text-gray-400'
          }`}>
            <FaFolder className="ml-2 mt-0.5 flex-shrink-0" />
            <div>
              <div className="font-medium">
                النافذة المخصصة {isMainServer ? '(بديل)' : '(غير متاح)'}
              </div>
              <div className="text-xs mt-1">
                {isMainServer
                  ? 'نافذة اختيار مجلدات مصممة خصيصاً للتطبيق'
                  : 'متاح فقط على الخادم الرئيسي - يتطلب وصول مباشر لنظام الملفات'
                }
              </div>
            </div>
          </div>
        </div>

        {/* إرشادات الاستخدام */}
        <div className="bg-blue-50 dark:bg-blue-900/30 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="flex items-start">
            <FaInfoCircle className="text-blue-600 dark:text-blue-400 ml-2 mt-0.5 flex-shrink-0" />
            <div className="text-blue-700 dark:text-blue-300 text-sm">
              <p className="font-medium mb-2">إرشادات اختيار المجلد:</p>
              <ul className="space-y-1 text-xs">
                {isMainServer ? (
                  <>
                    <li>• استخدم زر "من النظام" لاختيار مجلد بنافذة النظام الأصلية (مُوصى به)</li>
                    <li>• <strong>مهم:</strong> بعد النقر على "من النظام"، انتظر حتى تظهر نافذة اختيار المجلد</li>
                    <li>• خذ وقتك في اختيار المجلد - لا يوجد حد زمني</li>
                    <li>• استخدم زر "نافذة مخصصة" كبديل إذا لم تعمل نافذة النظام</li>
                    <li>• يمكنك إدخال المسار يدوياً في الحقل</li>
                    <li>• استخدم زر "اختبار" للتأكد من صحة المسار</li>
                    <li>• أمثلة: C:\Backups أو /home/<USER>/backups أو backups</li>
                  </>
                ) : (
                  <>
                    <li>• <strong>تنبيه:</strong> إعداد مسار النسخ الاحتياطية غير متاح للأجهزة البعيدة</li>
                    <li>• هذا الإعداد يتطلب وصول مباشر إلى نظام الملفات المحلي للخادم</li>
                    <li>• يجب تكوين هذا الإعداد من الخادم الرئيسي مباشرة</li>
                    <li>• بعد التكوين، ستعمل النسخ الاحتياطية تلقائياً لجميع المستخدمين</li>
                  </>
                )}
              </ul>
            </div>
          </div>
        </div>

        {/* معلومات إضافية للأجهزة البعيدة */}
        {!isMainServer && (
          <div className="bg-blue-50 dark:bg-blue-900/30 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-start">
              <FaInfoCircle className="text-blue-600 dark:text-blue-400 ml-2 mt-0.5 flex-shrink-0" />
              <div className="text-blue-700 dark:text-blue-300 text-sm">
                <p className="font-medium mb-2">للمديرين: كيفية تكوين مسار النسخ الاحتياطية</p>
                <ul className="space-y-1 text-xs">
                  <li>• اذهب إلى الخادم الرئيسي مباشرة</li>
                  <li>• افتح التطبيق محلياً (localhost أو عنوان IP المحلي)</li>
                  <li>• اذهب إلى الإعدادات → النسخ الاحتياطية</li>
                  <li>• قم بتكوين مسار النسخ الاحتياطية</li>
                  <li>• بعد التكوين، ستعمل النسخ الاحتياطية لجميع المستخدمين</li>
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* نافذة اختيار المجلد المخصصة */}
      <FolderPickerModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        onSelect={handleModalSelect}
        currentPath={value}
      />

      {/* نافذة نجاح اختيار المجلد */}
      <FolderSelectionSuccessModal
        isOpen={successModal.isOpen}
        onClose={() => setSuccessModal({ isOpen: false, folderPath: '', absolutePath: '' })}
        folderPath={successModal.folderPath}
        absolutePath={successModal.absolutePath}
        autoClose={true}
        autoCloseDelay={4000}
      />

      {/* نافذة خطأ اختيار المجلد */}
      <FolderSelectionErrorModal
        isOpen={errorModal.isOpen}
        onClose={() => setErrorModal({ isOpen: false, error: '' })}
        error={errorModal.error}
        autoClose={false}
      />
    </>
  );
};

export default SimpleFolderPicker;
