# 🎛️ مكون مفتاح التبديل - ToggleSwitch Component

مكون مفتاح التبديل الحديث بتصميم يشبه مفاتيح iOS و Android مع أحجام متعددة قابلة للتخصيص.

## ✨ المزايا

- **حجم معقول**: الحجم الافتراضي 40px × 20px
- **أحجام متعددة**: صغير، متوسط، كبير
- **مظهر حديث**: تصميم مسطح أنيق
- **حركة سلسة**: انتقال سريع 200ms
- **دعم RTL مثالي**: مُصمم خصيصاً للتطبيقات العربية
- **السلوك الصحيح**: غير نشط = يمين، نشط = يسار
- **الوضع المظلم**: دعم كامل للوضع المظلم

## 📏 الأحجام المتاحة

| الحجم | العرض × الارتفاع | الاستخدام المناسب |
|-------|------------------|------------------|
| `small` | 40px × 20px | الافتراضي - معظم الحالات |
| `medium` | 48px × 24px | النماذج المهمة |
| `large` | 56px × 28px | الإعدادات الرئيسية |

## 🔧 الاستخدام

### استخدام أساسي
```typescript
import ToggleSwitch from './components/ToggleSwitch';

function MyComponent() {
  const [isEnabled, setIsEnabled] = useState(false);

  return (
    <ToggleSwitch
      id="basic-toggle"
      checked={isEnabled}
      onChange={setIsEnabled}
      label="تفعيل الخاصية"
    />
  );
}
```

### مع تحديد الحجم
```typescript
<ToggleSwitch
  id="important-setting"
  checked={isImportant}
  onChange={setIsImportant}
  label="إعداد مهم"
  size="medium"
/>
```

### في النماذج
```typescript
<ToggleSwitch
  id="product-active"
  checked={formData.is_active}
  onChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
  label="المنتج نشط ومتاح للبيع"
  size="medium"
/>
```

## 🎨 الألوان

- **مفعل**: أزرق (`bg-blue-500`)
- **غير مفعل**: رمادي (`bg-gray-300`)
- **الدائرة**: أبيض مع ظل خفيف

## 🔄 السلوك في التطبيقات العربية (RTL)

المكون مُصمم خصيصاً للتطبيقات العربية مع السلوك الصحيح:

| الحالة | موضع الدائرة | الوصف |
|--------|-------------|-------|
| **غير نشط (OFF)** | 🔴 اليمين | الحالة الافتراضية |
| **نشط (ON)** | 🟢 اليسار | الحالة المفعلة |

### مثال بصري:
```
غير نشط: [    ●] ← الدائرة في اليمين
نشط:     [●    ] ← الدائرة في اليسار
```

## ⚡ الخصائص (Props)

| الخاصية | النوع | الافتراضي | الوصف |
|---------|------|----------|-------|
| `id` | `string` | مطلوب | معرف فريد للمكون |
| `checked` | `boolean` | مطلوب | حالة المفتاح |
| `onChange` | `(checked: boolean) => void` | مطلوب | دالة تغيير الحالة |
| `label` | `string` | مطلوب | نص التسمية |
| `className` | `string` | `''` | فئات CSS إضافية |
| `size` | `'small' \| 'medium' \| 'large'` | `'small'` | حجم المفتاح |

## 🎯 أمثلة عملية

### إعدادات التطبيق
```typescript
const [settings, setSettings] = useState({
  notifications: true,
  darkMode: false,
  autoSave: true
});

return (
  <div className="space-y-4">
    <ToggleSwitch
      id="notifications"
      checked={settings.notifications}
      onChange={(checked) => setSettings(prev => ({ ...prev, notifications: checked }))}
      label="تفعيل الإشعارات"
      size="small"
    />
    
    <ToggleSwitch
      id="dark-mode"
      checked={settings.darkMode}
      onChange={(checked) => setSettings(prev => ({ ...prev, darkMode: checked }))}
      label="الوضع المظلم"
      size="medium"
    />
    
    <ToggleSwitch
      id="auto-save"
      checked={settings.autoSave}
      onChange={(checked) => setSettings(prev => ({ ...prev, autoSave: checked }))}
      label="الحفظ التلقائي"
      size="large"
    />
  </div>
);
```

### في قائمة الإعدادات
```typescript
const settingsConfig = [
  { key: 'notifications', label: 'الإشعارات', size: 'small' },
  { key: 'autoBackup', label: 'النسخ الاحتياطي التلقائي', size: 'medium' },
  { key: 'securityMode', label: 'وضع الأمان المتقدم', size: 'large' }
];

return (
  <div className="space-y-4">
    {settingsConfig.map(setting => (
      <ToggleSwitch
        key={setting.key}
        id={setting.key}
        checked={settings[setting.key]}
        onChange={(checked) => handleSettingChange(setting.key, checked)}
        label={setting.label}
        size={setting.size}
      />
    ))}
  </div>
);
```

## 🔍 اختبار المكون

يمكنك اختبار المكون باستخدام:
1. `ToggleSwitchDemo.tsx` - مثال تفاعلي كامل
2. `test-toggle-switch.html` - اختبار HTML بسيط

## 📱 التوافق

- ✅ جميع المتصفحات الحديثة
- ✅ الأجهزة المحمولة
- ✅ الوضع المظلم
- ✅ دعم RTL
- ✅ قارئات الشاشة

## 🚀 نصائح للاستخدام

1. **استخدم الحجم الصغير** للإعدادات العادية
2. **استخدم الحجم المتوسط** للإعدادات المهمة
3. **استخدم الحجم الكبير** للإعدادات الحرجة فقط
4. **اجعل التسميات واضحة** ومفهومة
5. **اختبر في الوضع المظلم** للتأكد من الوضوح