/* تحسينات للنافذة المتجاوبة */
.deviceDetailsModal {
  /* تأكد من أن النافذة تعمل بشكل جيد على جميع الأحجام */
}

/* تحسينات للجداول المتجاوبة */
.responsiveTable {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.responsiveTable table {
  min-width: 100%;
}

/* تحسينات للبطاقات المتجاوبة */
.fingerprintCard {
  word-break: break-all;
  overflow-wrap: break-word;
}

/* تحسينات للنصوص الطويلة */
.truncateText {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
  .deviceDetailsModal {
    margin: 0;
    border-radius: 0;
    height: 100vh;
    max-height: 100vh;
  }
  
  .responsiveTable {
    font-size: 0.875rem;
  }
  
  .responsiveTable th,
  .responsiveTable td {
    padding: 0.5rem;
  }
}

/* تحسينات للأجهزة اللوحية */
@media (min-width: 769px) and (max-width: 1024px) {
  .deviceDetailsModal {
    max-width: 90vw;
  }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 1025px) {
  .deviceDetailsModal {
    max-width: 1200px;
  }
}
