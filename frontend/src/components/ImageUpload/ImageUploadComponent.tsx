/**
 * مكون رفع الصور المتقدم
 * يدعم السحب والإفلات، المعاينة، والرفع المتعدد
 */

import React, { useState, useRef, useCallback } from 'react';
import { FiUpload, FiX, <PERSON>Check, FiAlertCircle } from 'react-icons/fi';
import { imageManagementService, ImageFolder, ImageUploadResult } from '../../services/imageManagementService';

interface ImageUploadComponentProps {
  folder: ImageFolder;
  onUploadSuccess?: (result: ImageUploadResult) => void;
  onUploadError?: (error: string) => void;
  multiple?: boolean;
  generateThumbnails?: boolean;
  maxFiles?: number;
  className?: string;
  disabled?: boolean;
}

interface PreviewImage {
  file: File;
  preview: string;
  uploading: boolean;
  uploaded: boolean;
  error?: string;
  result?: ImageUploadResult;
}

const ImageUploadComponent: React.FC<ImageUploadComponentProps> = ({
  folder,
  onUploadSuccess,
  onUploadError,
  multiple = false,
  generateThumbnails = true,
  maxFiles = 10,
  className = '',
  disabled = false
}) => {
  const [previewImages, setPreviewImages] = useState<PreviewImage[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // معالجة اختيار الملفات
  const handleFileSelect = useCallback(async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const fileArray = Array.from(files);
    
    // التحقق من عدد الملفات
    if (!multiple && fileArray.length > 1) {
      onUploadError?.('يمكن رفع ملف واحد فقط');
      return;
    }

    // إذا كان multiple=false ولدينا صورة بالفعل، استبدلها
    if (!multiple && previewImages.length > 0) {
      setPreviewImages([]);
    }

    if (previewImages.length + fileArray.length > maxFiles) {
      onUploadError?.(`يمكن رفع ${maxFiles} ملف كحد أقصى`);
      return;
    }

    // إنشاء معاينات للملفات
    const newPreviews: PreviewImage[] = [];
    
    for (const file of fileArray) {
      // التحقق من صحة الملف
      const validation = imageManagementService.validateImageFile(file);
      if (!validation.valid) {
        onUploadError?.(validation.error || 'ملف غير صالح');
        continue;
      }

      try {
        const preview = await imageManagementService.previewImage(file);
        newPreviews.push({
          file,
          preview,
          uploading: false,
          uploaded: false
        });
      } catch (error) {
        console.error('خطأ في إنشاء معاينة:', error);
      }
    }

    setPreviewImages(prev => [...prev, ...newPreviews]);
  }, [multiple, maxFiles, previewImages.length, onUploadError]);

  // معالجة السحب والإفلات
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (disabled) return;

    const files = e.dataTransfer.files;
    handleFileSelect(files);
  }, [disabled, handleFileSelect]);

  // رفع الصور
  const uploadImages = useCallback(async () => {
    if (isUploading) return;

    const imagesToUpload = previewImages.filter(img => !img.uploaded && !img.uploading);
    if (imagesToUpload.length === 0) return;

    setIsUploading(true);

    for (const imagePreview of imagesToUpload) {
      // تحديث حالة الرفع
      setPreviewImages(prev => 
        prev.map(img => 
          img.file === imagePreview.file 
            ? { ...img, uploading: true, error: undefined }
            : img
        )
      );

      try {
        const result = await imageManagementService.uploadImage(
          imagePreview.file,
          folder,
          generateThumbnails
        );

        if (result.success) {
          // تحديث حالة النجاح
          setPreviewImages(prev => 
            prev.map(img => 
              img.file === imagePreview.file 
                ? { ...img, uploading: false, uploaded: true, result }
                : img
            )
          );

          onUploadSuccess?.(result);
        } else {
          // تحديث حالة الخطأ
          setPreviewImages(prev => 
            prev.map(img => 
              img.file === imagePreview.file 
                ? { ...img, uploading: false, error: result.error }
                : img
            )
          );

          onUploadError?.(result.error || 'فشل في رفع الصورة');
        }

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف';
        
        setPreviewImages(prev => 
          prev.map(img => 
            img.file === imagePreview.file 
              ? { ...img, uploading: false, error: errorMessage }
              : img
          )
        );

        onUploadError?.(errorMessage);
      }
    }

    setIsUploading(false);
  }, [previewImages, isUploading, folder, generateThumbnails, onUploadSuccess, onUploadError]);

  // حذف صورة من المعاينة
  const removePreviewImage = useCallback((fileToRemove: File) => {
    setPreviewImages(prev => {
      const imageToRemove = prev.find(img => img.file === fileToRemove);
      if (imageToRemove) {
        URL.revokeObjectURL(imageToRemove.preview);
      }
      return prev.filter(img => img.file !== fileToRemove);
    });
  }, []);

  // مسح جميع الصور
  const clearAllImages = useCallback(() => {
    previewImages.forEach(img => {
      URL.revokeObjectURL(img.preview);
    });
    setPreviewImages([]);
  }, [previewImages]);

  // تنظيف الموارد عند إلغاء التحميل
  React.useEffect(() => {
    return () => {
      previewImages.forEach(img => {
        URL.revokeObjectURL(img.preview);
      });
    };
  }, []);

  const hasImages = previewImages.length > 0;
  const hasUploadedImages = previewImages.some(img => img.uploaded);

  return (
    <div className={`w-full ${className}`}>
      {/* منطقة الرفع */}
      <div
        className={`
          relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200
          ${isDragOver 
            ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20' 
            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => !disabled && fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple={multiple}
          accept="image/*"
          className="hidden"
          onChange={(e) => handleFileSelect(e.target.files)}
          disabled={disabled}
        />

        <div className="flex flex-col items-center space-y-4">
          <div className={`
            w-16 h-16 rounded-full flex items-center justify-center
            ${isDragOver 
              ? 'bg-primary-100 dark:bg-primary-800' 
              : 'bg-gray-100 dark:bg-gray-700'
            }
          `}>
            <FiUpload className={`
              w-8 h-8 
              ${isDragOver 
                ? 'text-primary-600 dark:text-primary-400' 
                : 'text-gray-500 dark:text-gray-400'
              }
            `} />
          </div>

          <div>
            <p className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              {isDragOver
                ? (multiple ? 'أفلت الصور هنا' : 'أفلت الصورة هنا')
                : (multiple ? 'اسحب الصور هنا أو انقر للاختيار' : 'اسحب الصورة هنا أو انقر للاختيار')
              }
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              الصيغ المدعومة: JPG, PNG, GIF, WebP, BMP
              {multiple && ` (حتى ${maxFiles} ملف)`}
            </p>
          </div>
        </div>
      </div>

      {/* معاينة الصور */}
      {hasImages && (
        <div className="mt-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              {multiple ? `الصور المحددة (${previewImages.length})` : 'الصورة المحددة'}
            </h3>
            <div className="flex space-x-2 space-x-reverse">
              {!isUploading && (
                <button
                  onClick={uploadImages}
                  disabled={hasUploadedImages && !previewImages.some(img => !img.uploaded)}
                  className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors text-sm flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <FiUpload className="ml-2" />
                  {multiple ? 'رفع الصور' : 'رفع الصورة'}
                </button>
              )}
              <button
                onClick={clearAllImages}
                disabled={isUploading}
                className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors text-sm flex items-center disabled:opacity-50"
              >
                <FiX className="ml-2" />
                مسح الكل
              </button>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {previewImages.map((imagePreview, index) => (
              <div
                key={index}
                className="relative bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 overflow-hidden"
              >
                {/* الصورة */}
                <div className="aspect-square relative">
                  <img
                    src={imagePreview.preview}
                    alt={`معاينة ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                  
                  {/* حالة الرفع */}
                  {imagePreview.uploading && (
                    <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                    </div>
                  )}
                  
                  {/* حالة النجاح */}
                  {imagePreview.uploaded && (
                    <div className="absolute top-2 right-2 bg-green-500 text-white rounded-full p-1">
                      <FiCheck className="w-4 h-4" />
                    </div>
                  )}
                  
                  {/* حالة الخطأ */}
                  {imagePreview.error && (
                    <div className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1">
                      <FiAlertCircle className="w-4 h-4" />
                    </div>
                  )}
                  
                  {/* زر الحذف */}
                  {!imagePreview.uploading && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        removePreviewImage(imagePreview.file);
                      }}
                      className="absolute top-2 left-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                    >
                      <FiX className="w-4 h-4" />
                    </button>
                  )}
                </div>

                {/* معلومات الملف */}
                <div className="p-3">
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                    {imagePreview.file.name}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {imageManagementService.formatFileSize(imagePreview.file.size)}
                  </p>
                  
                  {/* رسالة الخطأ */}
                  {imagePreview.error && (
                    <p className="text-xs text-red-500 mt-1">
                      {imagePreview.error}
                    </p>
                  )}
                  
                  {/* رسالة النجاح */}
                  {imagePreview.uploaded && imagePreview.result && (
                    <p className="text-xs text-green-500 mt-1">
                      تم الرفع بنجاح
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageUploadComponent;
