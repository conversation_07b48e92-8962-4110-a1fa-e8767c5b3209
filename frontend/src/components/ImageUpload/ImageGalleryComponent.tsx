/**
 * مكون عرض معرض الصور
 * يدعم العرض، الحذف، والإدارة المتقدمة للصور
 */

import React, { useState, useEffect, useCallback } from 'react';
import { FiImage, FiTrash2, FiDownload, FiRefreshCw, FiInfo, FiGrid, FiList } from 'react-icons/fi';
import { imageManagementService, ImageFolder, ImageFileInfo } from '../../services/imageManagementService';

interface ImageGalleryComponentProps {
  folder: ImageFolder;
  onImageSelect?: (image: ImageFileInfo) => void;
  onImageDelete?: (image: ImageFileInfo) => void;
  selectable?: boolean;
  deletable?: boolean;
  showThumbnails?: boolean;
  className?: string;
  refreshTrigger?: number; // لإعادة تحميل البيانات من الخارج
}

type ViewMode = 'grid' | 'list';

const ImageGalleryComponent: React.FC<ImageGalleryComponentProps> = ({
  folder,
  onImageSelect,
  onImageDelete,
  selectable = false,
  deletable = false,
  showThumbnails = true,
  className = '',
  refreshTrigger = 0
}) => {
  const [images, setImages] = useState<ImageFileInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedImages, setSelectedImages] = useState<Set<string>>(new Set());
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [deleteConfirm, setDeleteConfirm] = useState<ImageFileInfo | null>(null);
  const [deleteSelectedConfirm, setDeleteSelectedConfirm] = useState<boolean>(false);

  // تحميل الصور
  const loadImages = useCallback(async () => {
    try {
      console.log(`🔄 تحميل الصور من مجلد: ${folder}, showThumbnails: ${showThumbnails}, refreshTrigger: ${refreshTrigger}`);
      setLoading(true);
      setError(null);

      const result = await imageManagementService.listImages(folder, showThumbnails);
      console.log('📋 نتيجة جلب الصور:', result);

      if (result.success && result.images) {
        console.log(`✅ تم تحميل ${result.images.length} صورة`);
        setImages(result.images);
      } else {
        console.error('❌ فشل في تحميل الصور:', result.error);
        setError(result.error || 'فشل في تحميل الصور');
        setImages([]);
      }

    } catch (error) {
      console.error('❌ خطأ في تحميل الصور:', error);
      setError('خطأ في تحميل الصور');
      setImages([]);
    } finally {
      setLoading(false);
    }
  }, [folder, showThumbnails, refreshTrigger]);

  // تحميل الصور عند التحميل الأولي أو تغيير المجلد
  useEffect(() => {
    console.log(`🔄 useEffect triggered - folder: ${folder}, refreshTrigger: ${refreshTrigger}`);
    loadImages();
  }, [folder, showThumbnails, refreshTrigger]); // إزالة loadImages من dependencies

  // معالجة اختيار الصورة
  const handleImageSelect = useCallback((image: ImageFileInfo) => {
    if (selectable) {
      setSelectedImages(prev => {
        const newSet = new Set(prev);
        if (newSet.has(image.file_path)) {
          newSet.delete(image.file_path);
        } else {
          newSet.add(image.file_path);
        }
        return newSet;
      });
    }
    
    onImageSelect?.(image);
  }, [selectable, onImageSelect]);

  // معالجة حذف الصورة
  const handleImageDelete = useCallback(async (image: ImageFileInfo) => {
    try {
      setLoading(true);
      
      const result = await imageManagementService.deleteImage(image.file_path, true);
      
      if (result.success) {
        setImages(prev => prev.filter(img => img.file_path !== image.file_path));
        setSelectedImages(prev => {
          const newSet = new Set(prev);
          newSet.delete(image.file_path);
          return newSet;
        });
        
        onImageDelete?.(image);
      } else {
        setError(result.error || 'فشل في حذف الصورة');
      }

    } catch (error) {
      console.error('خطأ في حذف الصورة:', error);
      setError('خطأ في حذف الصورة');
    } finally {
      setLoading(false);
      setDeleteConfirm(null);
    }
  }, [onImageDelete]);

  // تحميل الصورة
  const downloadImage = useCallback((image: ImageFileInfo) => {
    const imageUrl = imageManagementService.getImageUrl(image.file_path);
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = image.filename;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, []);

  // مسح التحديد
  const clearSelection = useCallback(() => {
    setSelectedImages(new Set());
  }, []);

  // إظهار نافذة تأكيد حذف الصور المحددة
  const showDeleteSelectedConfirm = useCallback(() => {
    if (selectedImages.size === 0) return;
    setDeleteSelectedConfirm(true);
  }, [selectedImages.size]);

  // حذف الصور المحددة (بعد التأكيد)
  const deleteSelectedImages = useCallback(async () => {
    if (selectedImages.size === 0) return;

    try {
      setLoading(true);
      setDeleteSelectedConfirm(false);

      const imagesToDelete = images.filter(img => selectedImages.has(img.file_path));
      console.log(`🗑️ حذف ${imagesToDelete.length} صورة محددة`);

      let successCount = 0;
      let errorCount = 0;

      for (const image of imagesToDelete) {
        try {
          const result = await imageManagementService.deleteImage(image.file_path, true);
          if (result.success) {
            successCount++;
          } else {
            errorCount++;
            console.error(`فشل في حذف ${image.filename}:`, result.error);
          }
        } catch (error) {
          errorCount++;
          console.error(`خطأ في حذف ${image.filename}:`, error);
        }
      }

      // إعادة تحميل القائمة
      await loadImages();
      clearSelection();

      // إظهار رسالة النتيجة
      if (successCount > 0) {
        console.log(`✅ تم حذف ${successCount} صورة بنجاح`);
      }
      if (errorCount > 0) {
        setError(`فشل في حذف ${errorCount} صورة من أصل ${imagesToDelete.length}`);
      }

    } catch (error) {
      console.error('خطأ في حذف الصور المحددة:', error);
      setError('خطأ في حذف الصور المحددة');
    } finally {
      setLoading(false);
    }
  }, [selectedImages, images, loadImages, clearSelection]);

  if (loading && images.length === 0) {
    return (
      <div className={`flex items-center justify-center py-12 ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-500 dark:text-gray-400">جاري تحميل الصور...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <div className="text-red-500 mb-4">
          <FiImage className="w-12 h-12 mx-auto mb-2 opacity-50" />
          <p className="text-lg font-medium">{error}</p>
        </div>
        <button
          onClick={loadImages}
          className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
        >
          إعادة المحاولة
        </button>
      </div>
    );
  }

  if (images.length === 0) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <FiImage className="w-16 h-16 mx-auto mb-4 text-gray-400 dark:text-gray-500" />
        <p className="text-lg font-medium text-gray-500 dark:text-gray-400 mb-2">
          لا توجد صور في هذا المجلد
        </p>
        <p className="text-sm text-gray-400 dark:text-gray-500">
          ابدأ برفع صور جديدة
        </p>
      </div>
    );
  }

  return (
    <div className={`w-full ${className}`}>
      {/* شريط الأدوات */}
      <div className="flex items-center justify-between mb-6 p-4 bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
        <div className="flex items-center space-x-4 space-x-reverse">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            الصور ({images.length})
          </h3>

          {selectedImages.size > 0 && (
            <span className="text-sm text-primary-600 dark:text-primary-400">
              محدد: {selectedImages.size}
            </span>
          )}


        </div>

        <div className="flex items-center space-x-2 space-x-reverse">
          {/* أزرار العرض */}
          <div className="flex bg-gray-100 dark:bg-gray-600 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded transition-colors ${
                viewMode === 'grid'
                  ? 'bg-white dark:bg-gray-700 text-primary-600 dark:text-primary-400 shadow-sm'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
            >
              <FiGrid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded transition-colors ${
                viewMode === 'list'
                  ? 'bg-white dark:bg-gray-700 text-primary-600 dark:text-primary-400 shadow-sm'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
            >
              <FiList className="w-4 h-4" />
            </button>
          </div>



          {/* أزرار الإجراءات */}
          {selectedImages.size > 0 && deletable && (
            <>
              <button
                onClick={clearSelection}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 p-2"
              >
                مسح التحديد
              </button>
              <button
                onClick={showDeleteSelectedConfirm}
                disabled={loading}
                className="bg-red-500 text-white px-3 py-2 rounded-lg hover:bg-red-600 transition-colors text-sm flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FiTrash2 className="w-4 h-4 ml-1" />
                حذف المحدد ({selectedImages.size})
              </button>
            </>
          )}

          <button
            onClick={loadImages}
            disabled={loading}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 p-2 disabled:opacity-50"
          >
            <FiRefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      {/* عرض الصور */}
      {viewMode === 'grid' ? (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {images.map((image) => (
            <div
              key={image.file_path}
              className={`
                relative bg-white dark:bg-gray-700 rounded-lg border overflow-hidden cursor-pointer transition-all duration-200 hover:shadow-lg
                ${selectedImages.has(image.file_path)
                  ? 'border-primary-500 ring-2 ring-primary-500/20'
                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                }
              `}
              onClick={() => handleImageSelect(image)}
            >
              {/* الصورة */}
              <div className="aspect-square relative">
                <img
                  src={
                    showThumbnails
                      ? imageManagementService.getThumbnailUrl(image.file_path, 'medium', true)
                      : imageManagementService.getImageUrl(image.file_path, true)
                  }
                  alt={image.filename}
                  className="w-full h-full object-cover"
                  loading="lazy"
                  onLoad={() => {
                    console.log(`✅ تم تحميل الصورة بنجاح: ${image.filename}`);
                  }}
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    console.log(`❌ فشل تحميل الصورة: ${image.filename}`);
                    console.log(`   - URL الفاشل: ${target.src}`);

                    // إخفاء الصورة المكسورة
                    target.style.display = 'none';

                    // إضافة رسالة خطأ
                    const parent = target.parentElement;
                    if (parent && !parent.querySelector('.error-message')) {
                      const errorDiv = document.createElement('div');
                      errorDiv.className = 'error-message flex items-center justify-center h-full bg-gray-100 dark:bg-gray-600 text-gray-500 dark:text-gray-400 text-sm';
                      errorDiv.innerHTML = '🖼️<br>فشل التحميل';
                      parent.appendChild(errorDiv);
                    }
                  }}
                />
                
                {/* تحديد الصورة */}
                {selectable && (
                  <div className={`
                    absolute top-2 right-2 w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all
                    ${selectedImages.has(image.file_path)
                      ? 'bg-primary-500 border-primary-500 text-white'
                      : 'bg-white border-gray-300 hover:border-primary-500'
                    }
                  `}>
                    {selectedImages.has(image.file_path) && (
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    )}
                  </div>
                )}

                {/* أزرار الإجراءات */}
                <div className="absolute bottom-2 left-2 flex space-x-1 space-x-reverse opacity-0 group-hover:opacity-100 transition-opacity">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      downloadImage(image);
                    }}
                    className="bg-black bg-opacity-50 text-white p-1.5 rounded hover:bg-opacity-70 transition-all"
                    title="تحميل"
                  >
                    <FiDownload className="w-3 h-3" />
                  </button>
                  
                  {deletable && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setDeleteConfirm(image);
                      }}
                      className="bg-red-500 bg-opacity-80 text-white p-1.5 rounded hover:bg-opacity-100 transition-all"
                      title="حذف"
                    >
                      <FiTrash2 className="w-3 h-3" />
                    </button>
                  )}
                </div>
              </div>

              {/* معلومات الصورة */}
              <div className="p-3">
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate" title={image.filename}>
                  {image.filename}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {imageManagementService.formatFileSize(image.file_size)}
                </p>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="space-y-2">
          {images.map((image) => (
            <div
              key={image.file_path}
              className={`
                flex items-center p-4 bg-white dark:bg-gray-700 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-md
                ${selectedImages.has(image.file_path)
                  ? 'border-primary-500 ring-2 ring-primary-500/20'
                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                }
              `}
              onClick={() => handleImageSelect(image)}
            >
              {/* صورة مصغرة */}
              <div className="w-16 h-16 rounded-lg overflow-hidden flex-shrink-0 ml-4">
                <img
                  src={
                    showThumbnails
                      ? imageManagementService.getThumbnailUrl(image.file_path, 'small', true)
                      : imageManagementService.getImageUrl(image.file_path, true)
                  }
                  alt={image.filename}
                  className="w-full h-full object-cover"
                  loading="lazy"
                  onLoad={() => {
                    console.log(`✅ تم تحميل الصورة الصغيرة: ${image.filename}`);
                  }}
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    console.log(`❌ فشل تحميل الصورة الصغيرة: ${image.filename}`);

                    // إخفاء الصورة وإظهار أيقونة بديلة
                    target.style.display = 'none';
                    const parent = target.parentElement;
                    if (parent && !parent.querySelector('.error-icon')) {
                      const errorDiv = document.createElement('div');
                      errorDiv.className = 'error-icon flex items-center justify-center h-full bg-gray-100 dark:bg-gray-600 text-gray-400';
                      errorDiv.innerHTML = '🖼️';
                      parent.appendChild(errorDiv);
                    }
                  }}
                />
              </div>

              {/* معلومات الصورة */}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                  {image.filename}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {imageManagementService.formatFileSize(image.file_size)} • 
                  {new Date(image.created_time).toLocaleDateString('ar-LY')}
                </p>
              </div>

              {/* أزرار الإجراءات */}
              <div className="flex items-center space-x-2 space-x-reverse">
                {selectable && (
                  <div className={`
                    w-5 h-5 rounded border-2 flex items-center justify-center transition-all
                    ${selectedImages.has(image.file_path)
                      ? 'bg-primary-500 border-primary-500 text-white'
                      : 'border-gray-300 hover:border-primary-500'
                    }
                  `}>
                    {selectedImages.has(image.file_path) && (
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    )}
                  </div>
                )}

                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    downloadImage(image);
                  }}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 p-1"
                  title="تحميل"
                >
                  <FiDownload className="w-4 h-4" />
                </button>

                {deletable && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setDeleteConfirm(image);
                    }}
                    className="text-red-500 hover:text-red-700 p-1"
                    title="حذف"
                  >
                    <FiTrash2 className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* نافذة تأكيد الحذف المحسنة */}
      {deleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-6 max-w-md w-full mx-4 transform transition-all">
            {/* أيقونة التحذير */}
            <div className="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-red-100 dark:bg-red-900/30 rounded-full">
              <FiTrash2 className="w-8 h-8 text-red-600 dark:text-red-400" />
            </div>

            {/* العنوان */}
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4 text-center">
              تأكيد حذف الصورة
            </h3>

            {/* الرسالة */}
            <div className="text-center mb-6">
              <p className="text-gray-600 dark:text-gray-400 mb-2">
                هل أنت متأكد من حذف الصورة:
              </p>
              <p className="font-medium text-gray-900 dark:text-gray-100 bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg">
                "{deleteConfirm.filename}"
              </p>
              <p className="text-sm text-red-600 dark:text-red-400 mt-3">
                ⚠️ سيتم حذف الصورة والصور المصغرة المرتبطة بها نهائياً ولا يمكن التراجع عن هذا الإجراء.
              </p>
            </div>

            {/* الأزرار */}
            <div className="flex space-x-3 space-x-reverse">
              <button
                onClick={() => handleImageDelete(deleteConfirm)}
                disabled={loading}
                className="flex-1 bg-red-500 text-white px-4 py-3 rounded-lg hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-medium flex items-center justify-center"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                    جاري الحذف...
                  </>
                ) : (
                  <>
                    <FiTrash2 className="w-4 h-4 ml-2" />
                    حذف نهائياً
                  </>
                )}
              </button>
              <button
                onClick={() => setDeleteConfirm(null)}
                disabled={loading}
                className="flex-1 bg-gray-500 text-white px-4 py-3 rounded-lg hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-medium"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نافذة تأكيد حذف الصور المحددة */}
      {deleteSelectedConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-6 max-w-lg w-full mx-4 transform transition-all">
            {/* أيقونة التحذير */}
            <div className="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-red-100 dark:bg-red-900/30 rounded-full">
              <FiTrash2 className="w-8 h-8 text-red-600 dark:text-red-400" />
            </div>

            {/* العنوان */}
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4 text-center">
              تأكيد حذف الصور المحددة
            </h3>

            {/* الرسالة */}
            <div className="text-center mb-6">
              <p className="text-gray-600 dark:text-gray-400 mb-3">
                هل أنت متأكد من حذف الصور المحددة؟
              </p>

              {/* عدد الصور المحددة */}
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-4">
                <div className="flex items-center justify-center mb-2">
                  <span className="text-2xl font-bold text-red-600 dark:text-red-400">
                    {selectedImages.size}
                  </span>
                  <span className="text-gray-600 dark:text-gray-400 mr-2">صورة محددة</span>
                </div>

                {/* قائمة أسماء الصور */}
                <div className="max-h-32 overflow-y-auto">
                  {images
                    .filter(img => selectedImages.has(img.file_path))
                    .map((img, index) => (
                      <div key={img.file_path} className="text-sm text-gray-700 dark:text-gray-300 py-1">
                        {index + 1}. {img.filename}
                      </div>
                    ))
                  }
                </div>
              </div>

              <p className="text-sm text-red-600 dark:text-red-400">
                ⚠️ سيتم حذف جميع الصور المحددة والصور المصغرة المرتبطة بها نهائياً ولا يمكن التراجع عن هذا الإجراء.
              </p>
            </div>

            {/* الأزرار */}
            <div className="flex space-x-3 space-x-reverse">
              <button
                onClick={deleteSelectedImages}
                disabled={loading}
                className="flex-1 bg-red-500 text-white px-4 py-3 rounded-lg hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-medium flex items-center justify-center"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                    جاري الحذف...
                  </>
                ) : (
                  <>
                    <FiTrash2 className="w-4 h-4 ml-2" />
                    حذف {selectedImages.size} صورة نهائياً
                  </>
                )}
              </button>
              <button
                onClick={() => setDeleteSelectedConfirm(false)}
                disabled={loading}
                className="flex-1 bg-gray-500 text-white px-4 py-3 rounded-lg hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-medium"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageGalleryComponent;
