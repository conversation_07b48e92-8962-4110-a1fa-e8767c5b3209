import React from 'react';
import { useTheme } from '../contexts/ThemeContext';

interface EnhancedScrollbarProps {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  maxHeight?: string;
  showScrollbar?: 'always' | 'hover' | 'auto';
}

/**
 * مكون شريط التمرير المحسن - نهج CSS محسن
 * يوفر شريط تمرير مخصص بدون استخدام مكتبات خارجية
 * أداء أفضل وتوافق أكبر مع جميع المتصفحات
 */
const EnhancedScrollbar: React.FC<EnhancedScrollbarProps> = ({
  children,
  className = '',
  style = {},
  maxHeight = '100%',
  showScrollbar = 'hover'
}) => {
  const { currentTheme } = useTheme();
  const isDark = currentTheme === 'dark';

  // تحديد فئات CSS بناءً على الوضع والإعدادات
  const scrollbarClass = `
    enhanced-scrollbar
    ${showScrollbar === 'always' ? 'scrollbar-always' : ''}
    ${showScrollbar === 'hover' ? 'scrollbar-hover' : ''}
    ${showScrollbar === 'auto' ? 'scrollbar-auto' : ''}
    ${isDark ? 'dark' : 'light'}
    ${className}
  `.trim().replace(/\s+/g, ' ');

  const containerStyle: React.CSSProperties = {
    maxHeight,
    overflowY: 'auto',
    overflowX: 'hidden',
    ...style,
  };

  return (
    <div 
      className={scrollbarClass}
      style={containerStyle}
    >
      {children}
    </div>
  );
};

export default EnhancedScrollbar;
