import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useSidebarStore, MenuItem, SubMenuItem } from '../stores/sidebarStore';
import AboutModal from './AboutModal';
import SupportEmailModal from './SupportEmailModal';
import '../styles/sidebar-enhanced.css';
import {
  FiHome,
  FiShoppingCart,
  FiTrendingUp,
  FiPackage,
  FiUsers,
  FiUser,
  FiCreditCard,
  FiBarChart,
  FiSettings,
  FiChevronRight,
  FiX,
  FiDollarSign,
  FiHelpCircle,
  FiImage,
  FiShield,
  FiTruck,
  FiRefreshCw,
  FiRepeat,
  FiGitBranch
} from 'react-icons/fi';

// خريطة الأيقونات
const iconMap = {
  FiHome,
  FiShoppingCart,
  FiTrendingUp,
  FiPackage,
  FiUsers,
  FiUser,
  FiCreditCard,
  FiBarChart,
  FiSettings,
  FiDollarSign,
  FiHelpCircle,
  FiImage,
  FiShield,
  FiTruck,
  FiRefreshCw,
  FiRepeat,
  FiGitBranch
};

interface SidebarProps {
  className?: string;
}

const Sidebar: React.FC<SidebarProps> = ({ className = '' }) => {
  const location = useLocation();
  const {
    isOpen,
    isMobileMenuOpen,
    expandedMenus,
    menuItems,
    toggleMobileMenu,
    setActiveMenuItem,
    setActiveSubMenuItem,
    toggleMenuExpansion
  } = useSidebarStore();

  // حالة التوسع المؤقت عند الـ hover
  const [isHoverExpanded, setIsHoverExpanded] = React.useState(false);
  // حالات النوافذ
  const [aboutModalOpen, setAboutModalOpen] = React.useState(false);
  const [supportModalOpen, setSupportModalOpen] = React.useState(false);
  // مؤقت للـ hover
  const hoverTimeoutRef = React.useRef<NodeJS.Timeout>();
  const leaveTimeoutRef = React.useRef<NodeJS.Timeout>();

  // معالج دخول الماوس للشريط الجانبي
  const handleMouseEnter = () => {
    if (!isOpen) {
      // إلغاء أي مؤقت خروج سابق
      if (leaveTimeoutRef.current) {
        clearTimeout(leaveTimeoutRef.current);
      }

      // إلغاء أي مؤقت دخول سابق
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }

      // تأخير قصير قبل التوسع لتجنب التوسع العرضي
      hoverTimeoutRef.current = setTimeout(() => {
        setIsHoverExpanded(true);
      }, 150);
    }
  };

  // معالج خروج الماوس من الشريط الجانبي
  const handleMouseLeave = () => {
    if (!isOpen) {
      // إلغاء مؤقت التوسع إذا لم يكتمل
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }

      // تأخير قصير قبل الانكماش لتجنب الإغلاق العرضي
      leaveTimeoutRef.current = setTimeout(() => {
        setIsHoverExpanded(false);
      }, 100);
    }
  };

  // تنظيف المؤقتات عند إلغاء التحميل
  React.useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
      if (leaveTimeoutRef.current) {
        clearTimeout(leaveTimeoutRef.current);
      }
    };
  }, []);



  // الحصول على الأيقونة من الخريطة
  const getIcon = (iconName: string) => {
    const IconComponent = iconMap[iconName as keyof typeof iconMap];
    return IconComponent ? <IconComponent className={`${isOpen ? 'w-5 h-5' : 'w-5 h-5'}`} /> : null;
  };

  // التحقق من النشاط بناءً على المسار
  const isActiveRoute = (path: string) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  // التحقق من نشاط القائمة الفرعية
  const isSubMenuActive = (subItem: SubMenuItem) => {
    return isActiveRoute(subItem.path);
  };

  // التحقق من نشاط القائمة الرئيسية
  const isMainMenuActive = (item: MenuItem) => {
    if (isActiveRoute(item.path)) return true;
    
    // التحقق من القوائم الفرعية
    if (item.subItems) {
      return item.subItems.some(subItem => isActiveRoute(subItem.path));
    }
    
    return false;
  };

  // معالج النقر على القائمة الرئيسية
  const handleMainMenuClick = (item: MenuItem, event?: React.MouseEvent) => {
    setActiveMenuItem(item.id);

    if (item.subItems && item.subItems.length > 0) {
      if (isOpen || isHoverExpanded) {
        // توسيع/طي القائمة الفرعية في الوضع المفتوح أو عند الـ hover
        toggleMenuExpansion(item.id);
      } else {
        // في الوضع المصغر بدون hover، منع الانتقال المباشر للرابط إذا كان هناك قوائم فرعية
        if (event) {
          event.preventDefault();
          event.stopPropagation();
        }
      }
    } else {
      // إغلاق القائمة المحمولة عند النقر على رابط مباشر
      if (isMobileMenuOpen) {
        toggleMobileMenu();
      }
    }
  };

  // معالج النقر على القائمة الفرعية
  const handleSubMenuClick = (subItem: SubMenuItem, event?: React.MouseEvent) => {
    setActiveSubMenuItem(subItem.id);

    // التعامل مع المسارات الخاصة بالنوافذ
    if (subItem.path.startsWith('modal:')) {
      if (event) {
        event.preventDefault();
      }

      const modalType = subItem.path.replace('modal:', '');
      switch (modalType) {
        case 'about':
          setAboutModalOpen(true);
          break;
        case 'support':
          setSupportModalOpen(true);
          break;
      }
    }

    // إغلاق القائمة المحمولة عند النقر على رابط
    if (isMobileMenuOpen) {
      toggleMobileMenu();
    }
  };

  // التحقق من توسيع القائمة
  const isMenuExpanded = (menuId: string) => {
    return expandedMenus.includes(menuId) || isMainMenuActive(menuItems.find(item => item.id === menuId)!);
  };

  // عرض عنصر القائمة الفرعية
  const renderSubMenuItem = (subItem: SubMenuItem) => {
    const isActive = isSubMenuActive(subItem);
    const isModalPath = subItem.path.startsWith('modal:');

    const content = (
      <>
        <span className="truncate">{subItem.name}</span>
      </>
    );

    const className = `
      flex items-center px-4 py-2.5 text-sm rounded-lg cursor-pointer sidebar-submenu-item sidebar-submenu-spacing
      ${isActive
        ? 'active text-primary-700 dark:text-primary-300'
        : 'text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700/30 hover:text-gray-900 dark:hover:text-gray-200'
      }
    `;

    return (
      <li key={subItem.id}>
        {isModalPath ? (
          <div
            onClick={(e) => handleSubMenuClick(subItem, e)}
            className={className}
          >
            {content}
          </div>
        ) : (
          <Link
            to={subItem.path}
            onClick={(e) => handleSubMenuClick(subItem, e)}
            className={className}
          >
            {content}
          </Link>
        )}
      </li>
    );
  };

  // عرض عنصر القائمة الرئيسية
  const renderMenuItem = (item: MenuItem) => {
    const isActive = isMainMenuActive(item);
    const hasSubItems = item.subItems && item.subItems.length > 0;
    const isExpanded = isMenuExpanded(item.id);

    // تحديد ما إذا كان يجب إظهار النص (في الوضع المفتوح أو عند الـ hover)
    const shouldShowText = isOpen || isHoverExpanded;

    const menuContent = (
      <div
        className={`
          flex items-center w-full ${shouldShowText ? 'px-3' : 'px-2'} py-3 rounded-lg
          sidebar-item-hover cursor-pointer group relative sidebar-main-item sidebar-item-spacing
          ${isActive ? 'active' : ''}
          ${isActive
            ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 font-medium'
            : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-gray-200'
          }
          ${!isOpen && !isHoverExpanded ? 'sidebar-collapsed' : ''}
        `}
        onClick={(e) => handleMainMenuClick(item, e)}
      >
        <div className="flex items-center flex-1 min-w-0">
          <span className={`
            flex-shrink-0 sidebar-main-icon
            ${isActive ? 'text-primary-700 dark:text-primary-300' : item.iconColor || 'text-gray-500 dark:text-gray-400'}
            ${!shouldShowText ? 'mx-auto' : 'ml-3'}
          `}>
            {getIcon(item.icon)}
          </span>
          {shouldShowText && (
            <>
              <span className="font-medium text-sm truncate sidebar-main-text">{item.name}</span>
              {item.badge && item.badge > 0 && (
                <span className="mr-2 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[18px] text-center">
                  {item.badge}
                </span>
              )}
            </>
          )}
        </div>

        {hasSubItems && shouldShowText && (
          <span className="flex-shrink-0">
            <FiChevronRight className={`w-3 h-3 sidebar-chevron ${isExpanded ? 'expanded rotate-90' : ''}`} />
          </span>
        )}
      </div>
    );

    return (
      <li key={item.id} className="mb-1">
        {hasSubItems ? (
          <div>
            {menuContent}
            {/* إظهار القوائم الفرعية */}
            {shouldShowText && isExpanded && (
              <ul className="mt-2 space-y-1 sidebar-submenu-container">
                {item.subItems!.map(subItem => renderSubMenuItem(subItem))}
              </ul>
            )}
          </div>
        ) : (
          <Link to={item.path}>
            {menuContent}
          </Link>
        )}
      </li>
    );
  };

  return (
    <>
      {/* Overlay للشاشات المحمولة */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={toggleMobileMenu}
        />
      )}

      {/* الشريط الجانبي */}
      <aside
        className={`
          fixed top-14 right-0 h-[calc(100vh-3.5rem)] bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 transition-all duration-300 ease-in-out
          ${isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'}
          lg:translate-x-0
          ${isOpen ? 'lg:w-64' : isHoverExpanded ? 'lg:w-64' : 'lg:w-20'} w-64 shadow-xl lg:shadow-sm
          ${!isOpen && isHoverExpanded ? 'lg:z-50' : 'z-30'}
          ${className}
        `}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {/* رأس الشريط الجانبي - للشاشات المحمولة فقط */}
        <div className="lg:hidden flex items-center justify-end p-3 border-b border-gray-200 dark:border-gray-700">
          {/* زر الإغلاق للشاشات المحمولة */}
          <button
            onClick={toggleMobileMenu}
            className="p-1.5 rounded-lg text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <FiX className="w-4 h-4" />
          </button>
        </div>

        {/* محتوى الشريط الجانبي */}
        <div className={`flex flex-col h-full ${!isOpen ? 'overflow-visible' : ''}`}>
          {/* قائمة التنقل */}
          <nav className={`flex-1 px-3 pt-4 pb-3 min-h-0 sidebar-container ${!isOpen ? 'overflow-visible' : ''}`}>
            {isOpen || isHoverExpanded ? (
              // الوضع المفتوح أو المتوسع عند الـ hover - إخفاء شريط التمرير
              <div className="sidebar-content h-full overflow-y-auto scrollbar-hide">
                <ul className="space-y-1 pr-2">
                  {menuItems.map(renderMenuItem)}
                </ul>
              </div>
            ) : (
              // الوضع المصغر - إخفاء شريط التمرير
              <div className="h-full overflow-y-auto scrollbar-hide">
                <ul className="space-y-1">
                  {menuItems.map(renderMenuItem)}
                </ul>
              </div>
            )}
          </nav>

          {/* تذييل الشريط الجانبي */}
          {(isOpen || isHoverExpanded) && (
            <div className="flex-shrink-0 p-3 border-t border-gray-200 dark:border-gray-700">
              <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
                SmartPOS v1.0.0
              </div>
            </div>
          )}
        </div>
      </aside>

      {/* النوافذ المنبثقة */}
      <AboutModal
        isOpen={aboutModalOpen}
        onClose={() => setAboutModalOpen(false)}
      />

      <SupportEmailModal
        isOpen={supportModalOpen}
        onClose={() => setSupportModalOpen(false)}
      />
    </>
  );
};

export default Sidebar;
