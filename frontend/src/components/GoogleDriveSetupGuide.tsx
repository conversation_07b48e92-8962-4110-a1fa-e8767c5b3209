import React, { useState } from 'react';
import {
  FaInfoCircle,
  FaExternalLinkAlt,
  FaChevronDown,
  FaChevronUp,
  FaCheck,
  FaCopy
} from 'react-icons/fa';

interface GoogleDriveSetupGuideProps {
  className?: string;
}

const GoogleDriveSetupGuide: React.FC<GoogleDriveSetupGuideProps> = ({ className = '' }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [copiedStep, setCopiedStep] = useState<number | null>(null);

  const redirectUri = 'http://localhost:8000/api/google-drive/oauth-callback';

  const copyToClipboard = (text: string, stepNumber: number) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopiedStep(stepNumber);
      setTimeout(() => setCopiedStep(null), 2000);
    });
  };

  const steps = [
    {
      title: 'إنشاء مشروع Google Cloud',
      description: 'اذهب إلى Google Cloud Console وأنشئ مشروع جديد',
      link: 'https://console.cloud.google.com/',
      linkText: 'Google Cloud Console'
    },
    {
      title: 'تفعيل Google Drive API',
      description: 'في مشروعك، اذهب إلى APIs & Services > Library وابحث عن Google Drive API وفعله',
      details: [
        'انقر على "APIs & Services" في القائمة الجانبية',
        'اختر "Library"',
        'ابحث عن "Google Drive API"',
        'انقر على "Enable"'
      ]
    },
    {
      title: 'إعداد OAuth consent screen',
      description: 'قم بإعداد شاشة الموافقة للتطبيق',
      details: [
        'اذهب إلى "APIs & Services" > "OAuth consent screen"',
        'اختر "External" للاستخدام العام',
        'املأ المعلومات المطلوبة (اسم التطبيق، البريد الإلكتروني)',
        'أضف النطاق: https://www.googleapis.com/auth/drive.file',
        'احفظ الإعدادات'
      ]
    },
    {
      title: 'إنشاء OAuth 2.0 Client ID',
      description: 'أنشئ بيانات اعتماد OAuth للتطبيق',
      details: [
        'اذهب إلى "APIs & Services" > "Credentials"',
        'انقر على "Create Credentials" > "OAuth client ID"',
        'اختر "Web application"',
        'أدخل اسم للعميل (مثل: SmartPOS)',
        'في "Authorized redirect URIs"، أضف الرابط التالي:'
      ],
      copyableText: redirectUri,
      copyStep: 4
    },
    {
      title: 'الحصول على Client ID و Client Secret',
      description: 'انسخ بيانات الاعتماد واستخدمها في SmartPOS',
      details: [
        'بعد إنشاء OAuth client، ستظهر نافذة تحتوي على:',
        '• Client ID',
        '• Client Secret',
        'انسخ هذين القيمتين واستخدمهما في نموذج تسجيل الدخول'
      ]
    }
  ];

  return (
    <div className={`bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-lg ${className}`}>
      <div 
        className="p-4 cursor-pointer flex items-center justify-between"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center">
          <FaInfoCircle className="text-blue-600 dark:text-blue-400 ml-2" />
          <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
            دليل إعداد Google Cloud Console
          </span>
        </div>
        {isExpanded ? (
          <FaChevronUp className="text-blue-600 dark:text-blue-400" />
        ) : (
          <FaChevronDown className="text-blue-600 dark:text-blue-400" />
        )}
      </div>

      {isExpanded && (
        <div className="px-4 pb-4 border-t border-blue-200 dark:border-blue-700">
          <div className="mt-4 space-y-6">
            {steps.map((step, index) => (
              <div key={index} className="relative">
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                    {index + 1}
                  </div>
                  <div className="mr-4 flex-1">
                    <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
                      {step.title}
                    </h4>
                    <p className="text-sm text-blue-700 dark:text-blue-300 mb-2">
                      {step.description}
                    </p>
                    
                    {step.link && (
                      <a
                        href={step.link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center text-sm text-blue-600 dark:text-blue-400 hover:underline mb-2"
                      >
                        <FaExternalLinkAlt className="ml-1" />
                        {step.linkText}
                      </a>
                    )}

                    {step.details && (
                      <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                        {step.details.map((detail, detailIndex) => (
                          <li key={detailIndex} className="flex items-start">
                            <span className="text-blue-500 ml-2 mt-1">•</span>
                            <span>{detail}</span>
                          </li>
                        ))}
                      </ul>
                    )}

                    {step.copyableText && (
                      <div className="mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded border">
                        <div className="flex items-center justify-between">
                          <code className="text-sm text-gray-800 dark:text-gray-200 break-all">
                            {step.copyableText}
                          </code>
                          <button
                            onClick={() => copyToClipboard(step.copyableText!, step.copyStep!)}
                            className="mr-2 p-1 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                            title="نسخ"
                          >
                            {copiedStep === step.copyStep ? (
                              <FaCheck className="text-green-600" />
                            ) : (
                              <FaCopy />
                            )}
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                
                {index < steps.length - 1 && (
                  <div className="absolute right-4 top-8 w-px h-6 bg-blue-300 dark:bg-blue-600"></div>
                )}
              </div>
            ))}
          </div>

          <div className="mt-6 p-3 bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700 rounded">
            <div className="flex items-start">
              <FaCheck className="text-green-600 dark:text-green-400 ml-2 mt-0.5" />
              <div className="text-sm text-green-800 dark:text-green-200">
                <p className="font-medium mb-1">بعد إكمال هذه الخطوات:</p>
                <p>استخدم Client ID و Client Secret في نموذج "تسجيل الدخول مع Google" أعلاه.</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GoogleDriveSetupGuide;
