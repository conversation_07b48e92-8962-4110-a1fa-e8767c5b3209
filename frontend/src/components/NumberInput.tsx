import React from 'react';

interface NumberInputProps {
  value: string;
  onChange: (value: string) => void;
  name: string;
  placeholder?: string;
  className?: string;
  label?: string;
  min?: number;
  max?: number;
  step?: string;
  disabled?: boolean;
  dir?: 'rtl' | 'ltr';
}

const NumberInput: React.FC<NumberInputProps> = ({
  value,
  onChange,
  name,
  placeholder = '0.00',
  className = '',
  label,
  min,
  max,
  step = '0.01',
  disabled = false,
  dir = 'ltr'
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  };

  return (
    <div className="relative">
      {label && (
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          {label}
        </label>
      )}
      <div className="relative">
        <input
          type="number"
          name={name}
          value={value || ''}
          onChange={handleChange}
          placeholder={placeholder}
          min={min}
          max={max}
          step={step}
          disabled={disabled}
          dir={dir}
          className={`w-full border border-gray-300 dark:border-gray-600 rounded-lg py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
            disabled ? 'opacity-60 cursor-not-allowed' : ''
          } ${className}`}
        />
      </div>
    </div>
  );
};

export default NumberInput;
