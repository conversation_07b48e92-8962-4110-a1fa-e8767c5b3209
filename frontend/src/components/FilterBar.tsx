import React from 'react';
import { FaFilter } from 'react-icons/fa';
import FilterSelect from './FilterSelect';

interface FilterOption {
  value: string;
  label: string;
}

interface FilterBarProps {
  // Filter options
  levelOptions: FilterOption[];
  sourceOptions: FilterOption[];
  
  // Current values
  filterLevel: string;
  filterSource: string;
  
  // Change handlers
  onLevelChange: (value: string) => void;
  onSourceChange: (value: string) => void;
  
  // Additional buttons
  additionalButtons?: React.ReactNode;
  
  // Styling
  className?: string;
  showFilterLabel?: boolean;
}

const FilterBar: React.FC<FilterBarProps> = ({
  levelOptions,
  sourceOptions,
  filterLevel,
  filterSource,
  onLevelChange,
  onSourceChange,
  additionalButtons,
  className = '',
  showFilterLabel = true
}) => {
  return (
    <div className={`flex flex-wrap items-center gap-4 ${className}`}>
      {/* Filter Label */}
      {showFilterLabel && (
        <div className="flex items-center gap-2">
          <FaFilter className="text-gray-500 dark:text-gray-400" />
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">فلاتر:</span>
        </div>
      )}

      {/* Level Filter */}
      <FilterSelect
        name="filterLevel"
        value={filterLevel}
        onChange={onLevelChange}
        options={levelOptions}
        size="sm"
        className="min-w-[130px]"
      />

      {/* Source Filter */}
      <FilterSelect
        name="filterSource"
        value={filterSource}
        onChange={onSourceChange}
        options={sourceOptions}
        size="sm"
        className="min-w-[120px]"
      />

      {/* Additional Buttons */}
      {additionalButtons}
    </div>
  );
};

export default FilterBar;
