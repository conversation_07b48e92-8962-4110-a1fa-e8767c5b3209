import React, { useState } from 'react';
import { FaPrint, FaTimes, FaCog, FaFileAlt, FaMobile, FaDesktop, FaCopy, FaPalette } from 'react-icons/fa';
import Modal from './Modal';
import { ModalSelectInput, NumberInput } from './inputs';

interface PrintOptionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onPrint: (options: PrintOptions) => void;
  receiptSize: string;
  onReceiptSizeChange: (size: string) => void;
}

export interface PrintOptions {
  printerType: 'thermal' | 'laser' | 'inkjet';
  paperSize: string;
  orientation: 'portrait' | 'landscape';
  quality: 'draft' | 'normal' | 'high';
  copies: number;
  margins: 'none' | 'minimum' | 'normal';
  colorMode: 'color' | 'grayscale' | 'blackwhite';
}

const PrintOptionsModal: React.FC<PrintOptionsModalProps> = ({
  isOpen,
  onClose,
  onPrint,
  receiptSize,
  onReceiptSizeChange
}) => {
  const [printOptions, setPrintOptions] = useState<PrintOptions>({
    printerType: 'thermal',
    paperSize: receiptSize === 'small' ? '58mm' : receiptSize === 'medium' ? '80mm' : 'A4',
    orientation: 'portrait',
    quality: 'normal',
    copies: 1,
    margins: 'minimum',
    colorMode: 'blackwhite'
  });

  const handlePrint = () => {
    // إغلاق النافذة أولاً ثم تمرير خيارات الطباعة
    onClose();

    // انتظار قصير للتأكد من إغلاق النافذة قبل الطباعة
    setTimeout(() => {
      onPrint(printOptions);
    }, 150);
  };

  const handleReceiptSizeChange = (size: string) => {
    onReceiptSizeChange(size);
    setPrintOptions(prev => ({
      ...prev,
      paperSize: size === 'small' ? '58mm' : size === 'medium' ? '80mm' : 'A4',
      printerType: size === 'a4' ? 'laser' : 'thermal'
    }));
  };

  const printerTypes = [
    { value: 'thermal', label: 'طابعة حرارية', icon: FaMobile },
    { value: 'laser', label: 'طابعة ليزر', icon: FaDesktop },
    { value: 'inkjet', label: 'طابعة حبر', icon: FaFileAlt }
  ];

  const paperSizes = [
    { value: '58mm', label: '58mm (صغير)', description: 'للطابعات الحرارية الصغيرة' },
    { value: '80mm', label: '80mm (متوسط)', description: 'للطابعات الحرارية المتوسطة' },
    { value: 'A4', label: 'A4 (كبير)', description: 'للطابعات العادية' }
  ];

  const qualityOptions = [
    { value: 'draft', label: 'مسودة', description: 'سريع وموفر للحبر' },
    { value: 'normal', label: 'عادي', description: 'جودة متوازنة' },
    { value: 'high', label: 'عالي', description: 'أفضل جودة' }
  ];

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="خصائص الطباعة">
      <div className="space-y-6">
        {/* نوع الطابعة */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            <FaCog className="inline mr-2" />
            نوع الطابعة
          </label>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {printerTypes.map((type) => {
              const IconComponent = type.icon;
              return (
                <button
                  key={type.value}
                  onClick={() => setPrintOptions(prev => ({ ...prev, printerType: type.value as any }))}
                  className={`p-4 border-2 rounded-xl text-center transition-all duration-200 ${
                    printOptions.printerType === type.value
                      ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 shadow-md'
                      : 'border-gray-200 dark:border-gray-600 hover:border-primary-300 dark:hover:border-primary-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary-50 dark:hover:bg-primary-900/10'
                  }`}
                >
                  <IconComponent className={`mx-auto mb-2 text-xl ${
                    printOptions.printerType === type.value
                      ? 'text-primary-600 dark:text-primary-400'
                      : 'text-gray-500 dark:text-gray-400'
                  }`} />
                  <div className="text-sm font-medium">{type.label}</div>
                </button>
              );
            })}
          </div>
        </div>

        {/* حجم الورق */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            <FaFileAlt className="inline mr-2" />
            حجم الورق
          </label>
          <div className="space-y-2">
            {paperSizes.map((size) => (
              <label
                key={size.value}
                className={`flex items-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                  printOptions.paperSize === size.value
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20 shadow-md'
                    : 'border-gray-200 dark:border-gray-600 hover:border-primary-300 dark:hover:border-primary-600 bg-white dark:bg-gray-700 hover:bg-primary-50 dark:hover:bg-primary-900/10'
                }`}
              >
                <input
                  type="radio"
                  name="paperSize"
                  value={size.value}
                  checked={printOptions.paperSize === size.value}
                  onChange={(e) => {
                    setPrintOptions(prev => ({ ...prev, paperSize: e.target.value }));
                    const receiptSizeMap: { [key: string]: string } = {
                      '58mm': 'small',
                      '80mm': 'medium',
                      'A4': 'a4'
                    };
                    handleReceiptSizeChange(receiptSizeMap[e.target.value] || 'medium');
                  }}
                  className="sr-only"
                />
                <div className="flex-1">
                  <div className={`font-medium ${
                    printOptions.paperSize === size.value
                      ? 'text-primary-700 dark:text-primary-300'
                      : 'text-gray-900 dark:text-gray-100'
                  }`}>{size.label}</div>
                  <div className={`text-sm ${
                    printOptions.paperSize === size.value
                      ? 'text-primary-600 dark:text-primary-400'
                      : 'text-gray-500 dark:text-gray-400'
                  }`}>{size.description}</div>
                </div>
                {printOptions.paperSize === size.value && (
                  <div className="w-5 h-5 bg-primary-500 rounded-full flex items-center justify-center">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                )}
              </label>
            ))}
          </div>
        </div>

        {/* جودة الطباعة */}
        <div>
          <ModalSelectInput
            label="جودة الطباعة"
            name="quality"
            value={printOptions.quality}
            onChange={(value) => setPrintOptions(prev => ({ ...prev, quality: value as any }))}
            options={qualityOptions.map(option => ({
              value: option.value,
              label: `${option.label} - ${option.description}`
            }))}
            placeholder="اختر جودة الطباعة"
          />
        </div>

        {/* خيارات إضافية */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* عدد النسخ */}
          <div>
            <NumberInput
              label="عدد النسخ"
              name="copies"
              value={printOptions.copies.toString()}
              onChange={(value) => setPrintOptions(prev => ({ ...prev, copies: parseInt(value) || 1 }))}
              min={1}
              max={10}
              placeholder="1"
              icon={<FaCopy />}
            />
            {printOptions.copies > 1 && (
              <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-xl">
                <p className="text-sm text-blue-700 dark:text-blue-300 flex items-center">
                  <span className="ml-2">💡</span>
                  سيتم طباعة {printOptions.copies} نسخ في مستند واحد مع فاصل صفحات بين كل نسخة
                </p>
              </div>
            )}
          </div>

          {/* الهوامش */}
          <div>
            <ModalSelectInput
              label="الهوامش"
              name="margins"
              value={printOptions.margins}
              onChange={(value) => setPrintOptions(prev => ({ ...prev, margins: value as any }))}
              options={[
                { value: 'none', label: 'بدون هوامش' },
                { value: 'minimum', label: 'هوامش صغيرة' },
                { value: 'normal', label: 'هوامش عادية' }
              ]}
              placeholder="اختر نوع الهوامش"
            />
          </div>
        </div>

        {/* وضع الألوان */}
        <div>
          <ModalSelectInput
            label="وضع الألوان"
            name="colorMode"
            value={printOptions.colorMode}
            onChange={(value) => setPrintOptions(prev => ({ ...prev, colorMode: value as any }))}
            options={[
              { value: 'blackwhite', label: 'أبيض وأسود' },
              { value: 'grayscale', label: 'رمادي' },
              { value: 'color', label: 'ملون' }
            ]}
            placeholder="اختر وضع الألوان"
            icon={<FaPalette />}
          />
        </div>

        {/* أزرار التحكم */}
        <div className="flex flex-col sm:flex-row gap-3 sm:gap-3 justify-center pt-6 border-t border-gray-200 dark:border-gray-600">
          <button
            onClick={onClose}
            className="btn-secondary flex items-center justify-center min-w-[120px]"
          >
            <FaTimes className="ml-2" />
            <span>إلغاء</span>
          </button>
          <button
            onClick={handlePrint}
            className="btn-primary flex items-center justify-center min-w-[140px]"
          >
            <FaPrint className="ml-2" />
            <span>طباعة</span>
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default PrintOptionsModal;
