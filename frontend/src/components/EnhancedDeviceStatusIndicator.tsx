/**
 * مؤشر حالة الجهاز المحسن
 * يعرض حالة الجهاز في الوقت الفعلي مع معلومات دقيقة
 */

import React, { useState, useEffect } from 'react';
import { Check<PERSON>ircle, Clock, XCircle, Zap, User, Wifi, WifiOff } from './ui/icons';
import { FaCircle, FaExclamationTriangle } from 'react-icons/fa';

interface DeviceStatusIndicatorProps {
  device: {
    device_id: string;
    status: string;
    last_access: string;
    last_heartbeat?: string;
    websocket_connected?: boolean;
    current_user?: string;
    is_main_server?: boolean;
  };
  showDetails?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const EnhancedDeviceStatusIndicator: React.FC<DeviceStatusIndicatorProps> = ({
  device,
  showDetails = false,
  size = 'md'
}) => {
  const [timeAgo, setTimeAgo] = useState<string>('');

  // تحديث الوقت المنقضي كل ثانية
  useEffect(() => {
    const updateTimeAgo = () => {
      const lastActivity = device.last_heartbeat || device.last_access;
      if (!lastActivity) {
        setTimeAgo('غير معروف');
        return;
      }

      const now = new Date();
      const lastTime = new Date(lastActivity);
      const diffMs = now.getTime() - lastTime.getTime();
      const diffSeconds = Math.floor(diffMs / 1000);
      const diffMinutes = Math.floor(diffSeconds / 60);
      const diffHours = Math.floor(diffMinutes / 60);

      if (diffSeconds < 60) {
        setTimeAgo(`منذ ${diffSeconds} ثانية`);
      } else if (diffMinutes < 60) {
        setTimeAgo(`منذ ${diffMinutes} دقيقة`);
      } else if (diffHours < 24) {
        setTimeAgo(`منذ ${diffHours} ساعة`);
      } else {
        const diffDays = Math.floor(diffHours / 24);
        setTimeAgo(`منذ ${diffDays} يوم`);
      }
    };

    updateTimeAgo();
    const interval = setInterval(updateTimeAgo, 1000);

    return () => clearInterval(interval);
  }, [device.last_access, device.last_heartbeat]);

  // الحصول على أيقونة الحالة
  const getStatusIcon = () => {
    const iconSize = size === 'sm' ? 'w-3 h-3' : size === 'md' ? 'w-4 h-4' : 'w-5 h-5';
    
    switch (device.status) {
      case 'online':
        return (
          <div className="relative">
            <CheckCircle className={`text-green-600 ${iconSize} animate-pulse`} />
            {device.websocket_connected && (
              <Wifi className="absolute -top-1 -right-1 w-2 h-2 text-green-400" />
            )}
          </div>
        );
      case 'recently_active':
        return <Clock className={`text-yellow-600 ${iconSize}`} />;
      case 'approved_online':
        return (
          <div className="relative">
            <Zap className={`text-emerald-600 ${iconSize} animate-pulse`} />
            {device.websocket_connected && (
              <Wifi className="absolute -top-1 -right-1 w-2 h-2 text-emerald-400" />
            )}
          </div>
        );
      case 'approved_offline':
        return <CheckCircle className={`text-emerald-400 ${iconSize}`} />;
      case 'pending_approval':
        return <FaExclamationTriangle className={`text-orange-600 ${iconSize === 'w-3 h-3' ? 'text-xs' : iconSize === 'w-4 h-4' ? 'text-sm' : 'text-base'}`} />;
      case 'offline':
        return (
          <div className="relative">
            <XCircle className={`text-gray-500 ${iconSize}`} />
            <WifiOff className="absolute -top-1 -right-1 w-2 h-2 text-gray-400" />
          </div>
        );
      case 'unknown':
        return <FaCircle className={`text-gray-400 ${iconSize === 'w-3 h-3' ? 'text-xs' : iconSize === 'w-4 h-4' ? 'text-sm' : 'text-base'}`} />;
      default:
        return <FaCircle className={`text-gray-300 ${iconSize === 'w-3 h-3' ? 'text-xs' : iconSize === 'w-4 h-4' ? 'text-sm' : 'text-base'}`} />;
    }
  };

  // الحصول على نص الحالة
  const getStatusText = () => {
    switch (device.status) {
      case 'online':
        return 'متصل الآن';
      case 'recently_active':
        return 'نشط مؤخراً';
      case 'approved_online':
        return 'معتمد • متصل';
      case 'approved_offline':
        return 'معتمد • غير متصل';
      case 'pending_approval':
        return 'في انتظار الموافقة';
      case 'offline':
        return 'غير متصل';
      case 'unknown':
        return 'حالة غير معروفة';
      default:
        return 'حالة غير محددة';
    }
  };

  // الحصول على لون النص
  const getStatusColor = () => {
    switch (device.status) {
      case 'online':
        return 'text-green-600';
      case 'recently_active':
        return 'text-yellow-600';
      case 'approved_online':
        return 'text-emerald-600';
      case 'approved_offline':
        return 'text-emerald-400';
      case 'pending_approval':
        return 'text-orange-600';
      case 'offline':
        return 'text-gray-500';
      case 'unknown':
        return 'text-gray-400';
      default:
        return 'text-gray-300';
    }
  };

  // الحصول على لون الخلفية
  const getStatusBgColor = () => {
    switch (device.status) {
      case 'online':
        return 'bg-green-50 border-green-200';
      case 'recently_active':
        return 'bg-yellow-50 border-yellow-200';
      case 'approved_online':
        return 'bg-emerald-50 border-emerald-200';
      case 'approved_offline':
        return 'bg-emerald-50 border-emerald-200';
      case 'pending_approval':
        return 'bg-orange-50 border-orange-200';
      case 'offline':
        return 'bg-gray-50 border-gray-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  if (!showDetails) {
    // عرض مبسط - أيقونة فقط
    return (
      <div className="flex items-center gap-2" title={`${getStatusText()} - ${timeAgo}`}>
        {getStatusIcon()}
        {size !== 'sm' && (
          <span className={`text-xs font-medium ${getStatusColor()}`}>
            {getStatusText()}
          </span>
        )}
      </div>
    );
  }

  // عرض مفصل
  return (
    <div className={`p-3 rounded-lg border ${getStatusBgColor()}`}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          {getStatusIcon()}
          <span className={`text-sm font-medium ${getStatusColor()}`}>
            {getStatusText()}
          </span>
        </div>
        {device.is_main_server && (
          <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
            خادم رئيسي
          </span>
        )}
      </div>
      
      <div className="space-y-1 text-xs text-gray-600">
        <div className="flex items-center justify-between">
          <span>آخر نشاط:</span>
          <span className="font-medium">{timeAgo}</span>
        </div>
        
        {device.current_user && (
          <div className="flex items-center justify-between">
            <span>المستخدم:</span>
            <div className="flex items-center gap-1">
              <User className="w-3 h-3" />
              <span className="font-medium">{device.current_user}</span>
            </div>
          </div>
        )}
        
        <div className="flex items-center justify-between">
          <span>الاتصال المباشر:</span>
          <div className="flex items-center gap-1">
            {device.websocket_connected ? (
              <>
                <Wifi className="w-3 h-3 text-green-500" />
                <span className="text-green-600 font-medium">متصل</span>
              </>
            ) : (
              <>
                <WifiOff className="w-3 h-3 text-gray-400" />
                <span className="text-gray-500">غير متصل</span>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedDeviceStatusIndicator;
