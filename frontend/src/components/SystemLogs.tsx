import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  FaExclamationTriangle,
  FaInfoCircle,
  FaTimesCircle,
  FaBug,
  FaServer,
  FaDatabase,
  FaDesktop,
  FaCheck,
  FaPaperPlane,
  FaTrash,
  FaSync,
  FaTerminal,
  FaFilter,
  FaDownload,
  FaTools,
  FaBell,
  FaTachometerAlt,
  FaTimes,
  FaShieldAlt,
  FaEye,
  FaCheckCircle,
  FaClock,
  FaBolt
} from 'react-icons/fa';
import { useAutoResolveBackend } from '../hooks/useAutoResolveBackend';
import AutoResolveBackendModal from './AutoResolveBackendModal';
import { useReportsStore } from '../stores/reportsStore';
import errorLogger from '../services/errorLogger';
import { useSearchParams } from 'react-router-dom';
import SystemAlerts from './SystemAlerts';
import PerformanceMonitorComponent from './PerformanceMonitor';
import Modal from './Modal';
import LogDetailsModal from './LogDetailsModal';


import SimpleSuccessModal from './SimpleSuccessModal';
import SimpleErrorModal from './SimpleErrorModal';
import LoadingModal from './LoadingModal';

import ToggleSwitch from './ToggleSwitch';
import FilterSelect from './FilterSelect';
import SmallButton from './SmallButton';
import api from '../lib/axios';
import { unifiedConnectionService } from '../services/unifiedConnectionService';
import { FormattedDate, FormattedTime, FormattedDateTime } from './FormattedDateTime';

interface SystemLogsProps {
  className?: string;
}

const SystemLogs: React.FC<SystemLogsProps> = ({ className = '' }) => {
  const {
    systemLogs,
    systemHealth,
    isLoading,
    error,
    fetchSystemLogs,
    fetchSystemHealth,
    clearSystemLogs,
    resolveSystemLog
  } = useReportsStore();

  // حالة محلية للسجلات لضمان التحديث الفوري
  const [localSystemLogs, setLocalSystemLogs] = useState(systemLogs);

  // خدمة الحل التلقائي الاحترافية - معالجة في الخلفية
  const {
    isProcessing: isAutoResolving,
    progress: autoResolveProgress,
    result: autoResolveResult,
    error: autoResolveError,
    startAutoResolve,
    stopAutoResolve,
    reset: resetAutoResolve
  } = useAutoResolveBackend();

  // تحديث الحالة المحلية عند تغيير السجلات من الـ store
  useEffect(() => {
    if (!isAutoResolving) {
      setLocalSystemLogs(systemLogs);
    }
  }, [systemLogs, isAutoResolving]);

  const [searchParams] = useSearchParams();
  const logTabFromUrl = searchParams.get('logtab') as 'logs' | 'alerts' | 'performance' | null;
  const focusFromUrl = searchParams.get('focus'); // معامل التركيز

  // refs للتبويبات
  const logsTabRef = useRef<HTMLDivElement>(null);
  const alertsTabRef = useRef<HTMLDivElement>(null);
  const performanceTabRef = useRef<HTMLDivElement>(null);

  const [selectedLogs, setSelectedLogs] = useState<number[]>([]);
  const [filterLevel, setFilterLevel] = useState<string>('all');
  const [filterSource, setFilterSource] = useState<string>('all');
  const [showResolved, setShowResolved] = useState(false);
  const [terminalMode, setTerminalMode] = useState(false);
  const [isRunningDiagnostics, setIsRunningDiagnostics] = useState(false);
  const [deletedLogs, setDeletedLogs] = useState<number[]>([]);

  const [activeTab, setActiveTab] = useState<'logs' | 'alerts' | 'performance'>(logTabFromUrl || 'logs');
  const [dataLoaded, setDataLoaded] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  // Hook للتنسيق الموحد - تم إزالة formatDateTime لأنه غير مستخدم

  // حالات الـ Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20); // عدد السجلات في كل صفحة
  const [totalLogs, setTotalLogs] = useState(0);

  // حالة نافذة تفاصيل السجل
  const [selectedLogForDetails, setSelectedLogForDetails] = useState<any>(null);

  // حالات المودالات
  const [successModal, setSuccessModal] = useState({
    isOpen: false,
    message: ''
  });
  const [errorModal, setErrorModal] = useState({
    isOpen: false,
    message: ''
  });
  const [diagnosticsModal, setDiagnosticsModal] = useState({
    isOpen: false,
    data: null as any
  });
  const [loadingModal, setLoadingModal] = useState({
    isOpen: false,
    title: '',
    message: '',
    type: 'loading' as 'loading' | 'network-check' | 'error',
    showProgress: false,
    progress: 0
  });

  // دالة مساعدة لإغلاق نافذة التحميل
  const closeLoadingModal = () => {
    setLoadingModal({
      isOpen: false,
      title: '',
      message: '',
      type: 'loading',
      showProgress: false,
      progress: 0
    });
  };

  // مراقبة تغيير معامل logtab في URL
  useEffect(() => {
    if (logTabFromUrl && logTabFromUrl !== activeTab) {
      setActiveTab(logTabFromUrl);
    }
  }, [logTabFromUrl]);

  // التركيز على التبويبة المناسبة بعد التوجيه
  useEffect(() => {
    if (focusFromUrl === 'true') {
      // تأخير قصير للسماح بتحديث DOM
      setTimeout(() => {
        let targetRef: React.RefObject<HTMLDivElement> | null = null;

        switch (activeTab) {
          case 'logs':
            targetRef = logsTabRef;
            break;
          case 'alerts':
            targetRef = alertsTabRef;
            break;
          case 'performance':
            targetRef = performanceTabRef;
            break;
        }

        if (targetRef?.current) {
          targetRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
          // إضافة تأثير بصري للتركيز
          targetRef.current.style.outline = '2px solid #3B82F6';
          targetRef.current.style.outlineOffset = '4px';
          setTimeout(() => {
            if (targetRef?.current) {
              targetRef.current.style.outline = '';
              targetRef.current.style.outlineOffset = '';
            }
          }, 2000);
        }
      }, 300);
    }
  }, [activeTab, focusFromUrl]);

  useEffect(() => {
    // جلب البيانات مرة واحدة فقط عند أول تحميل للمكون
    if (!dataLoaded) {
      setDataLoaded(true);
      setIsInitialLoading(true);

      // جلب البيانات بشكل متوازي للسرعة
      Promise.all([
        fetchSystemLogs(),
        fetchSystemHealth()
      ]).finally(() => {
        setIsInitialLoading(false);
      });

      // إضافة سجل اختبار للتأكد من عمل النظام
      setTimeout(() => {
        errorLogger.logError('INFO', 'FRONTEND', 'تم تحميل صفحة سجلات النظام', {
          timestamp: new Date().toISOString(),
          page: 'SystemLogs'
        });
      }, 1000);
    }
  }, []); // بدون dependencies لضمان التشغيل مرة واحدة فقط

  // تعطيل التحديث الدوري للإحصائيات لتوفير موارد النظام
  // يمكن للمستخدم تحديث البيانات يدوياً عند الحاجة
  // useEffect(() => {
  //   const interval = setInterval(async () => {
  //     try {
  //       await fetchSystemHealth();
  //     } catch (error) {
  //       console.error('Error updating system health:', error);
  //     }
  //   }, 30000); // 30 ثانية

  //   return () => clearInterval(interval);
  // }, [fetchSystemHealth]);

  // تصفية السجلات
  const allFilteredLogs = localSystemLogs.filter(log => {
    // استبعاد السجلات المحذوفة محلياً
    if (deletedLogs.includes(log.id)) return false;
    if (filterLevel !== 'all' && log.level !== filterLevel) return false;
    if (filterSource !== 'all' && log.source !== filterSource) return false;
    if (!showResolved && log.resolved) return false;
    return true;
  });

  // حساب الـ Pagination
  const totalPages = Math.ceil(allFilteredLogs.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const filteredLogs = allFilteredLogs.slice(startIndex, endIndex);

  // تحديث إجمالي السجلات
  React.useEffect(() => {
    setTotalLogs(allFilteredLogs.length);
  }, [allFilteredLogs.length]);

  // الحصول على أيقونة المستوى
  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'CRITICAL':
        return <FaTimesCircle className="text-red-600" />;
      case 'ERROR':
        return <FaExclamationTriangle className="text-red-500" />;
      case 'WARNING':
        return <FaExclamationTriangle className="text-yellow-500" />;
      case 'INFO':
        return <FaInfoCircle className="text-blue-500" />;
      default:
        return <FaBug className="text-gray-500" />;
    }
  };

  // الحصول على أيقونة المصدر
  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'FRONTEND':
        return <FaDesktop className="text-blue-600" />;
      case 'BACKEND':
        return <FaServer className="text-green-600" />;
      case 'DATABASE':
        return <FaDatabase className="text-purple-600" />;
      case 'SYSTEM':
        return <FaTerminal className="text-gray-600" />;
      default:
        return <FaBug className="text-gray-500" />;
    }
  };



  // تحديد/إلغاء تحديد سجل
  const toggleLogSelection = (logId: number) => {
    setSelectedLogs(prev =>
      prev.includes(logId)
        ? prev.filter(id => id !== logId)
        : [...prev, logId]
    );
  };

  // تحديد/إلغاء تحديد جميع السجلات (في جميع الصفحات)
  const toggleAllLogs = () => {
    if (selectedLogs.length === allFilteredLogs.length && allFilteredLogs.length > 0) {
      // إلغاء تحديد الكل
      setSelectedLogs([]);
    } else {
      // تحديد جميع السجلات المفلترة في كل الصفحات
      setSelectedLogs(allFilteredLogs.map(log => log.id));
    }
  };

  // حل مشكلة سجل
  const [resolveModal, setResolveModal] = useState({
    isOpen: false,
    logId: 0,
    notes: ''
  });

  const handleResolveLog = (logId: number) => {
    setResolveModal({
      isOpen: true,
      logId,
      notes: ''
    });
  };

  const confirmResolveLog = async () => {
    try {
      await resolveSystemLog(resolveModal.logId, resolveModal.notes || undefined);

      // تحديث السجل في الحالة المحلية فوراً
      const updatedLogs = localSystemLogs.map(log =>
        log.id === resolveModal.logId
          ? { ...log, resolved: true, resolution_notes: resolveModal.notes || undefined }
          : log
      );
      setLocalSystemLogs(updatedLogs);

      setResolveModal({ isOpen: false, logId: 0, notes: '' });
      setSuccessModal({
        isOpen: true,
        message: 'تم حل السجل بنجاح وتحديث الإحصائيات'
      });

      // تحديث البيانات من الخادم بعد قليل
      setTimeout(async () => {
        await fetchSystemLogs();
        await fetchSystemHealth();
      }, 500);
    } catch (error) {
      console.error('خطأ في حل السجل:', error);
      setErrorModal({
        isOpen: true,
        message: 'حدث خطأ أثناء حل السجل. يرجى المحاولة مرة أخرى.'
      });
    }
  };

  // حذف سجل معلومات
  const [deleteModal, setDeleteModal] = useState({
    isOpen: false,
    logId: 0
  });

  const handleDeleteLog = (logId: number) => {
    setDeleteModal({
      isOpen: true,
      logId
    });
  };

  const confirmDeleteLog = async () => {
    // هنا يمكن إضافة API call لحذف السجل من الخادم
    // await deleteSystemLog(deleteModal.logId);

    // إضافة السجل إلى قائمة المحذوفة محلي<|im_start|>
    setDeletedLogs(prev => [...prev, deleteModal.logId]);

    // إزالة السجل من قائمة المحددة إذا كان محدداً
    setSelectedLogs(prev => prev.filter(id => id !== deleteModal.logId));

    setDeleteModal({ isOpen: false, logId: 0 });
    setSuccessModal({
      isOpen: true,
      message: 'تم حذف السجل بنجاح'
    });
  };





  // تصدير نتائج التشخيص
  const handleExportDiagnostics = () => {
    if (!diagnosticsModal.data) return;

    const dataStr = JSON.stringify(diagnosticsModal.data, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `system-diagnostics-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  // إرسال السجلات المحددة للدعم
  const handleSendToSupport = async () => {
    try {
      // التحقق من وجود سجلات محددة
      if (selectedLogs.length === 0) {
        setErrorModal({
          isOpen: true,
          message: 'يرجى تحديد السجلات المراد إرسالها للدعم'
        });
        return;
      }

      // عرض نافذة فحص الاتصال
      setLoadingModal({
        isOpen: true,
        title: 'فحص الاتصال',
        message: 'جاري التحقق من الاتصال بالإنترنت...',
        type: 'network-check',
        showProgress: false,
        progress: 0
      });

      // فحص الاتصال بالإنترنت
      const connectivityResult = await unifiedConnectionService.checkInternetConnectivity();

      if (!connectivityResult.isConnected) {
        closeLoadingModal();
        setErrorModal({
          isOpen: true,
          message: `لا يوجد اتصال بالإنترنت\n${connectivityResult.error || 'يرجى التحقق من اتصال الشبكة والمحاولة مرة أخرى'}`
        });
        return;
      }

      // تحديث نافذة التحميل لإظهار حالة الإرسال
      setLoadingModal({
        isOpen: true,
        title: 'إرسال للدعم',
        message: 'جاري إرسال السجلات للدعم الفني...',
        type: 'loading',
        showProgress: false,
        progress: 0
      });

      const response = await api.post('/api/system/logs/send-support', {
        logs: selectedLogs
      });

      const result = response.data;

      // إغلاق نافذة التحميل
      closeLoadingModal();

      if (result.success) {
        setSuccessModal({
          isOpen: true,
          message: `✅ ${result.message}\n📧 تم الإرسال إلى: ${result.support_email}\n📤 من: ${result.sender_email || '<EMAIL>'}\n🌐 زمن الاستجابة: ${connectivityResult.latency ? connectivityResult.latency + 'ms' : 'غير محدد'}`
        });
        setSelectedLogs([]);
      } else {
        setErrorModal({
          isOpen: true,
          message: result.message || 'فشل في إرسال السجلات للدعم'
        });
      }
    } catch (error) {
      console.error('Error sending logs to support:', error);

      // إغلاق نافذة التحميل في حالة الخطأ
      closeLoadingModal();

      // تحديد نوع الخطأ لعرض رسالة مناسبة
      let errorMessage = 'حدث خطأ أثناء إرسال السجلات للدعم';

      if (error && typeof error === 'object') {
        const err = error as any;
        if (err.code === 'NETWORK_ERROR' || err.message?.includes('Network Error')) {
          errorMessage = 'خطأ في الشبكة: تعذر الاتصال بالخادم\nيرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى';
        } else if (err.response?.status === 408 || err.code === 'ECONNABORTED') {
          errorMessage = 'انتهت مهلة الاتصال\nيرجى المحاولة مرة أخرى أو التحقق من سرعة الإنترنت';
        } else if (err.response?.status >= 500) {
          errorMessage = 'خطأ في الخادم\nيرجى المحاولة مرة أخرى لاحقاً';
        }
      }

      setErrorModal({
        isOpen: true,
        message: errorMessage
      });
    }
  };

  // إرسال خطأ حرج للدعم
  const handleSendCriticalToSupport = async (log: any) => {
    try {
      // عرض نافذة فحص الاتصال
      setLoadingModal({
        isOpen: true,
        title: 'إرسال خطأ حرج للدعم',
        message: 'جاري التحقق من الاتصال وإرسال الخطأ الحرج للدعم الفني...',
        type: 'network-check',
        showProgress: false,
        progress: 0
      });

      // فحص الاتصال بالإنترنت
      const connectivityResult = await unifiedConnectionService.checkInternetConnectivity();

      if (!connectivityResult.isConnected) {
        closeLoadingModal();
        setErrorModal({
          isOpen: true,
          message: `لا يوجد اتصال بالإنترنت\n${connectivityResult.error || 'يرجى التحقق من اتصال الشبكة والمحاولة مرة أخرى'}`
        });
        return;
      }

      // تحديث نافذة التحميل لإظهار حالة الإرسال
      setLoadingModal({
        isOpen: true,
        title: 'إرسال للدعم الفني',
        message: 'جاري إرسال الخطأ الحرج للدعم الفني...',
        type: 'loading',
        showProgress: false,
        progress: 0
      });

      const response = await api.post('/api/system/logs/send-support', {
        logs: [log.id],
        priority: 'critical',
        auto_send: true
      });

      const result = response.data;

      // إغلاق نافذة التحميل
      closeLoadingModal();

      if (result.success) {
        setSuccessModal({
          isOpen: true,
          message: `🚨 تم إرسال الخطأ الحرج للدعم الفني بنجاح\n\n📧 تم الإرسال إلى: ${result.support_email}\n📤 من: ${result.sender_email || '<EMAIL>'}\n⚡ زمن الاستجابة: ${connectivityResult.latency ? connectivityResult.latency + 'ms' : 'غير محدد'}\n\n⏰ سيتم الرد خلال 24 ساعة لمعالجة الخطأ الحرج`
        });

        // تحديث السجل ليصبح "تم إرساله للدعم"
        const updatedLogs = localSystemLogs.map(l =>
          l.id === log.id
            ? { ...l, sent_to_support: true, support_sent_at: new Date().toISOString() }
            : l
        );
        setLocalSystemLogs(updatedLogs);
      } else {
        setErrorModal({
          isOpen: true,
          message: result.message || 'فشل في إرسال الخطأ الحرج للدعم'
        });
      }
    } catch (error) {
      console.error('Error sending critical log to support:', error);

      // إغلاق نافذة التحميل في حالة الخطأ
      closeLoadingModal();

      setErrorModal({
        isOpen: true,
        message: 'حدث خطأ أثناء إرسال الخطأ الحرج للدعم الفني\nيرجى المحاولة مرة أخرى أو التواصل مع الدعم مباشرة'
      });
    }
  };

  // حالة كلمة المرور التأكيدية
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPasswordInput, setShowPasswordInput] = useState(false);
  const [pendingClearAction, setPendingClearAction] = useState<'normal' | 'complete' | null>(null);

  // حساب السجلات القابلة للحل التلقائي (استبعاد الأخطاء الحرجة والمعلومات)
  const selectedResolvableLogs = useMemo(() => {
    return localSystemLogs.filter(log =>
      selectedLogs.includes(log.id) &&
      !log.resolved &&
      log.level !== 'INFO' &&
      log.level !== 'CRITICAL' // استبعاد الأخطاء الحرجة من الحل التلقائي
    );
  }, [selectedLogs, localSystemLogs]);

  // تحديد إمكانية الحل التلقائي
  const canAutoResolve = selectedResolvableLogs.length > 0;

  // حساب الإحصائيات المحلية من السجلات المحدثة (فقط الأخطاء والتحذيرات)
  const localStats = useMemo(() => {
    // فلترة السجلات لتشمل فقط الأخطاء والتحذيرات (استبعاد INFO)
    const errorLogs = localSystemLogs.filter(log =>
      log.level === 'CRITICAL' || log.level === 'ERROR' || log.level === 'WARNING'
    );

    const totalErrors = errorLogs.length;
    const criticalErrors = localSystemLogs.filter(log => log.level === 'CRITICAL').length;
    const regularErrors = localSystemLogs.filter(log => log.level === 'ERROR').length;
    const warningErrors = localSystemLogs.filter(log => log.level === 'WARNING').length;
    const resolvedErrors = errorLogs.filter(log => log.resolved).length;
    const unresolvedErrors = errorLogs.filter(log => !log.resolved).length;

    // حساب أداء النظام بمنطق مبسط وواضح
    const calculateSystemPerformance = () => {
      // إذا لم تكن هناك أخطاء على الإطلاق، الأداء ممتاز
      if (totalErrors === 0) {
        return 100;
      }

      // حساب الأداء بناءً على نسبة الأخطاء المحلولة مع تعديلات
      let basePerformance = totalErrors > 0 ? Math.round((resolvedErrors / totalErrors) * 100) : 100;

      // تقليل الأداء بناءً على الأخطاء الحرجة غير المحلولة
      const unresolvedCritical = localSystemLogs.filter(log =>
        log.level === 'CRITICAL' && !log.resolved
      ).length;

      // كل خطأ حرج غير محلول يقلل الأداء بـ 15%
      basePerformance -= unresolvedCritical * 15;

      // تقليل إضافي إذا كان هناك الكثير من الأخطاء غير المحلولة
      const unresolvedPercentage = totalErrors > 0 ? (unresolvedErrors / totalErrors) * 100 : 0;
      if (unresolvedPercentage > 50) {
        basePerformance -= 10; // تقليل إضافي إذا كان أكثر من 50% غير محلول
      }

      // التأكد من أن النتيجة بين 0 و 100
      const finalPerformance = Math.max(0, Math.min(100, basePerformance));

      // تتبع القيم للتشخيص (يمكن حذفها لاحقاً)
      console.log('System Performance Debug:', {
        totalErrors,
        resolvedErrors,
        unresolvedErrors,
        unresolvedCritical,
        basePerformance,
        finalPerformance
      });

      return finalPerformance;
    };

    return {
      total_errors: totalErrors,
      critical_errors: criticalErrors,
      regular_errors: regularErrors,
      warning_errors: warningErrors,
      resolved_errors: resolvedErrors,
      unresolved_errors: unresolvedErrors,
      system_performance: calculateSystemPerformance()
    };
  }, [localSystemLogs, systemHealth]);

  // مسح جميع السجلات - يعرض نافذة اختيار نوع المسح
  const handleClearLogs = () => {
    setShowPasswordInput(true);
  };

  // تنفيذ المسح بعد تأكيد كلمة المرور
  const executeClearAction = async () => {
    if (confirmPassword !== 'DELETE') {
      setErrorModal({
        isOpen: true,
        message: 'كلمة التأكيد غير صحيحة. يجب كتابة "DELETE" بالأحرف الكبيرة.'
      });
      return;
    }

    try {
      if (pendingClearAction === 'complete') {
        await clearSystemLogs(true); // مسح كامل
        setSuccessModal({
          isOpen: true,
          message: '✅ تم المسح الكامل بنجاح\n🗑️ تم حذف جميع السجلات بدون ترك أثر\n⚠️ تذكر إرسال الأخطاء المهمة للدعم مستقبلاً'
        });
      } else {
        await clearSystemLogs(false); // مسح عادي
        setSuccessModal({
          isOpen: true,
          message: '✅ تم مسح جميع السجلات بنجاح\n📝 تم إنشاء سجل توثيق للعملية'
        });
      }

      setSelectedLogs([]);
      setDeletedLogs([]);

      // تحديث الإحصائيات بعد المسح
      setTimeout(async () => {
        await fetchSystemHealth();
      }, 500);
    } catch (error) {
      setErrorModal({
        isOpen: true,
        message: 'حدث خطأ أثناء مسح السجلات. يرجى المحاولة مرة أخرى.'
      });
    } finally {
      // إعادة تعيين الحالة
      setShowPasswordInput(false);
      setConfirmPassword('');
      setPendingClearAction(null);
    }
  };

  // إلغاء عملية المسح
  const cancelClearAction = () => {
    setShowPasswordInput(false);
    setConfirmPassword('');
    setPendingClearAction(null);
  };



  // تصدير السجلات
  const handleExportLogs = () => {
    const logsToExport = selectedLogs.length > 0
      ? localSystemLogs.filter(log => selectedLogs.includes(log.id))
      : filteredLogs;

    const dataStr = JSON.stringify(logsToExport, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `system-logs-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  // تشخيص النظام
  const handleRunDiagnostics = async () => {
    setIsRunningDiagnostics(true);
    try {
      const result = await errorLogger.runSystemDiagnostics();

      // عرض النتائج في مودال منفصل
      setDiagnosticsModal({
        isOpen: true,
        data: result
      });

      // تسجيل نتائج التشخيص
      errorLogger.logInfo('System diagnostics completed', result);

      setSuccessModal({
        isOpen: true,
        message: 'تم إجراء تشخيص النظام بنجاح'
      });
    } catch (error) {
      errorLogger.logError('ERROR', 'FRONTEND', 'Failed to run diagnostics', error);
      setSuccessModal({
        isOpen: true,
        message: 'فشل في إجراء تشخيص النظام'
      });
    } finally {
      setIsRunningDiagnostics(false);
    }
  };

  // الإصلاح التلقائي للمشاكل الشائعة (محلي)
  const handleLocalAutoFix = async () => {
    try {
      let fixesApplied = 0;

      // إصلاح مشاكل التخزين المحلي
      if (diagnosticsModal.data?.storage?.localStorage?.available === false) {
        try {
          localStorage.clear();
          errorLogger.logInfo('Auto-fix: Cleared localStorage');
          fixesApplied++;
        } catch (error) {
          errorLogger.logWarning('Auto-fix failed: Could not clear localStorage', error);
        }
      }

      // إصلاح مشاكل ذاكرة المتصفح
      if (diagnosticsModal.data?.performance?.memory?.usedJSHeapSize > diagnosticsModal.data?.performance?.memory?.jsHeapSizeLimit * 0.9) {
        try {
          // تنظيف الذاكرة
          if (window.gc) {
            window.gc();
            errorLogger.logInfo('Auto-fix: Triggered garbage collection');
            fixesApplied++;
          }
        } catch (error) {
          errorLogger.logWarning('Auto-fix failed: Could not trigger garbage collection', error);
        }
      }

      // إعادة تحميل البيانات المهمة (بدون await لتجنب الحلقة اللانهائية)
      try {
        fetchSystemLogs();
        fetchSystemHealth();
        errorLogger.logInfo('Auto-fix: Refreshed system data');
        fixesApplied++;
      } catch (error) {
        errorLogger.logWarning('Auto-fix failed: Could not refresh system data', error);
      }

      if (fixesApplied > 0) {
        errorLogger.logInfo(`Auto-fix completed: ${fixesApplied} fixes applied`);
        setSuccessModal({
          isOpen: true,
          message: `تم تطبيق ${fixesApplied} إصلاحات محلية بنجاح`
        });
      } else {
        errorLogger.logInfo('Auto-fix: No fixes needed');
        setSuccessModal({
          isOpen: true,
          message: 'لا توجد مشاكل محلية تحتاج إصلاح'
        });
      }
    } catch (error) {
      errorLogger.logError('ERROR', 'FRONTEND', 'Auto-fix process failed', error);
    }
  };



  // الحل التلقائي للسجلات المختارة - نسخة احترافية منفصلة
  const handleAutoResolveSelected = async () => {
    // فحص السجلات القابلة للحل (تجاهل الأخطاء الحرجة)
    if (selectedResolvableLogs.length === 0) {
      const selectedCriticalLogs = localSystemLogs.filter(log =>
        selectedLogs.includes(log.id) &&
        !log.resolved &&
        log.level === 'CRITICAL'
      );

      if (selectedCriticalLogs.length > 0) {
        setErrorModal({
          isOpen: true,
          message: `التحديد الحالي يحتوي على ${selectedCriticalLogs.length} خطأ حرج فقط.\n\nالأخطاء الحرجة تحتاج إرسال للدعم الفني.\nيرجى تحديد أخطاء عادية أو تحذيرات للحل التلقائي.`
        });
      } else {
        setErrorModal({
          isOpen: true,
          message: 'يرجى اختيار سجلات أخطاء أو تحذيرات غير محلولة للحل التلقائي.'
        });
      }
      return;
    }

    try {
      // تحويل السجلات إلى IDs للإرسال للخلفية
      const logIds = selectedResolvableLogs.map(log => log.id);

      // بدء العملية باستخدام الخدمة الاحترافية - معالجة كاملة في الخلفية
      await startAutoResolve(logIds);

      // إلغاء التحديد بعد بدء العملية
      setSelectedLogs([]);

      // ملاحظة: العملية تتم الآن بالكامل في الخلفية
      // لا حاجة لتحديث البيانات أثناء العملية

    } catch (error) {
      console.error('خطأ في بدء الحل التلقائي:', error);
      setErrorModal({
        isOpen: true,
        message: 'حدث خطأ في بدء الحل التلقائي. يرجى المحاولة مرة أخرى.'
      });
    }
  };

  // معالجة إغلاق نافذة الحل التلقائي - نسخة محسنة
  const handleAutoResolveModalClose = async () => {
    // حفظ النتيجة قبل إعادة التعيين
    const wasSuccessful = autoResolveResult && autoResolveResult.success;
    const resultData = autoResolveResult;

    // إعادة تعيين حالة الحل التلقائي
    resetAutoResolve();

    // تحديث البيانات من الخادم بعد اكتمال العملية بنجاح
    if (wasSuccessful && resultData) {
      try {
        // تحديث البيانات في الخلفية
        await Promise.all([
          fetchSystemLogs(),
          fetchSystemHealth()
        ]);

        // عرض رسالة النجاح بعد تحديث البيانات
        const failedMessage = resultData.failed > 0
          ? `\n❌ فشل في حل ${resultData.failed} سجل`
          : '';

        const errorsMessage = resultData.errors.length > 0
          ? `\n\n📋 تفاصيل الأخطاء:\n${resultData.errors.slice(0, 2).join('\n')}`
          : '';

        setSuccessModal({
          isOpen: true,
          message: `🎉 اكتملت عملية الحل التلقائي!\n\n✅ تم حل ${resultData.resolved} من ${resultData.total} سجل بنجاح\n🔧 تمت المعالجة في الخلفية\n📊 معدل النجاح: ${Math.round((resultData.resolved/resultData.total)*100)}%${failedMessage}${errorsMessage}\n\n🔄 تم تحديث البيانات من الخادم`
        });

      } catch (error) {
        console.error('خطأ في تحديث البيانات بعد الحل التلقائي:', error);

        // عرض رسالة النجاح حتى لو فشل التحديث
        setSuccessModal({
          isOpen: true,
          message: `🎉 اكتملت عملية الحل التلقائي!\n\n✅ تم حل ${resultData.resolved} من ${resultData.total} سجل بنجاح\n⚠️ قد تحتاج لتحديث الصفحة يدوياً لرؤية التغييرات`
        });
      }
    }
  };



  // شاشة تحميل محسنة
  if (isInitialLoading || (isLoading && localSystemLogs.length === 0)) {
    return (
      <div className={`flex flex-col justify-center items-center h-64 space-y-3 ${className}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <span className="text-sm text-gray-600 dark:text-gray-300">جاري تحميل السجلات...</span>
        <div className="text-xs text-gray-500">تحميل سريع - 25 سجل فقط</div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* حالة النظام */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="touch-card p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-300">حالة الواجهة الأمامية</p>
              <p className={`font-bold ${
                systemHealth.frontend_status === 'HEALTHY' ? 'text-green-600' :
                systemHealth.frontend_status === 'WARNING' ? 'text-yellow-600' : 'text-red-600'
              }`}>
                {systemHealth.frontend_status === 'HEALTHY' ? 'سليم' :
                 systemHealth.frontend_status === 'WARNING' ? 'تحذير' : 'خطأ'}
              </p>
            </div>
            <FaDesktop className={`text-2xl ${
              systemHealth.frontend_status === 'HEALTHY' ? 'text-green-600' :
              systemHealth.frontend_status === 'WARNING' ? 'text-yellow-600' : 'text-red-600'
            }`} />
          </div>
        </div>

        <div className="touch-card p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-300">حالة الخلفية</p>
              <p className={`font-bold ${
                systemHealth.backend_status === 'HEALTHY' ? 'text-green-600' :
                systemHealth.backend_status === 'WARNING' ? 'text-yellow-600' : 'text-red-600'
              }`}>
                {systemHealth.backend_status === 'HEALTHY' ? 'سليم' :
                 systemHealth.backend_status === 'WARNING' ? 'تحذير' : 'خطأ'}
              </p>
            </div>
            <FaServer className={`text-2xl ${
              systemHealth.backend_status === 'HEALTHY' ? 'text-green-600' :
              systemHealth.backend_status === 'WARNING' ? 'text-yellow-600' : 'text-red-600'
            }`} />
          </div>
        </div>

        <div className="touch-card p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-300">حالة قاعدة البيانات</p>
              <p className={`font-bold ${
                systemHealth.database_status === 'HEALTHY' ? 'text-green-600' :
                systemHealth.database_status === 'WARNING' ? 'text-yellow-600' : 'text-red-600'
              }`}>
                {systemHealth.database_status === 'HEALTHY' ? 'سليم' :
                 systemHealth.database_status === 'WARNING' ? 'تحذير' : 'خطأ'}
              </p>
            </div>
            <FaDatabase className={`text-2xl ${
              systemHealth.database_status === 'HEALTHY' ? 'text-green-600' :
              systemHealth.database_status === 'WARNING' ? 'text-yellow-600' : 'text-red-600'
            }`} />
          </div>
        </div>
      </div>

      {/* إحصائيات الأخطاء المحسنة */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-7 gap-4 mb-6">
        {/* إجمالي الأخطاء */}
        <div className="touch-card stats-card">
          <div className="flex justify-between items-start mb-4">
            <div>
              <p className="text-secondary-500 dark:text-secondary-400 text-sm font-medium mb-1">إجمالي الأخطاء</p>
              <p className="text-3xl font-bold text-secondary-900 dark:text-secondary-100">{localStats.total_errors}</p>
            </div>
            <div className="p-4 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">
              <FaServer className="text-xl" />
            </div>
          </div>
          <div className="flex items-center text-sm text-secondary-500 dark:text-secondary-400">
            <FaInfoCircle className="ml-1" />
            <span>جميع السجلات</span>
          </div>
        </div>

        {/* أخطاء حرجة */}
        <div className="touch-card stats-card">
          <div className="flex justify-between items-start mb-4">
            <div>
              <p className="text-secondary-500 dark:text-secondary-400 text-sm font-medium mb-1">أخطاء حرجة</p>
              <p className="text-3xl font-bold text-red-600 dark:text-red-400">{localStats.critical_errors}</p>
            </div>
            <div className="p-4 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400">
              <FaExclamationTriangle className="text-xl" />
            </div>
          </div>
          <div className="flex items-center text-sm text-red-600 dark:text-red-400">
            <FaShieldAlt className="ml-1" />
            <span>تحتاج دعم فني</span>
          </div>
        </div>

        {/* أخطاء عادية */}
        <div className="touch-card stats-card">
          <div className="flex justify-between items-start mb-4">
            <div>
              <p className="text-secondary-500 dark:text-secondary-400 text-sm font-medium mb-1">أخطاء عادية</p>
              <p className="text-3xl font-bold text-orange-600 dark:text-orange-400">{localStats.regular_errors}</p>
            </div>
            <div className="p-4 rounded-full bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400">
              <FaTimesCircle className="text-xl" />
            </div>
          </div>
          <div className="flex items-center text-sm text-orange-600 dark:text-orange-400">
            <FaTools className="ml-1" />
            <span>قابلة للحل</span>
          </div>
        </div>

        {/* تحذيرات */}
        <div className="touch-card stats-card">
          <div className="flex justify-between items-start mb-4">
            <div>
              <p className="text-secondary-500 dark:text-secondary-400 text-sm font-medium mb-1">تحذيرات</p>
              <p className="text-3xl font-bold text-yellow-600 dark:text-yellow-400">{localStats.warning_errors}</p>
            </div>
            <div className="p-4 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400">
              <FaExclamationTriangle className="text-xl" />
            </div>
          </div>
          <div className="flex items-center text-sm text-yellow-600 dark:text-yellow-400">
            <FaEye className="ml-1" />
            <span>تحتاج مراجعة</span>
          </div>
        </div>

        {/* أخطاء محلولة */}
        <div className="touch-card stats-card">
          <div className="flex justify-between items-start mb-4">
            <div>
              <p className="text-secondary-500 dark:text-secondary-400 text-sm font-medium mb-1">أخطاء محلولة</p>
              <p className="text-3xl font-bold text-green-600 dark:text-green-400">{localStats.resolved_errors}</p>
            </div>
            <div className="p-4 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400">
              <FaCheckCircle className="text-xl" />
            </div>
          </div>
          <div className="flex items-center text-sm text-green-600 dark:text-green-400">
            <FaCheck className="ml-1" />
            <span>تم الحل</span>
          </div>
        </div>

        {/* غير محلولة */}
        <div className="touch-card stats-card">
          <div className="flex justify-between items-start mb-4">
            <div>
              <p className="text-secondary-500 dark:text-secondary-400 text-sm font-medium mb-1">غير محلولة</p>
              <p className="text-3xl font-bold text-purple-600 dark:text-purple-400">{localStats.unresolved_errors}</p>
            </div>
            <div className="p-4 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400">
              <FaBug className="text-xl" />
            </div>
          </div>
          <div className="flex items-center text-sm text-purple-600 dark:text-purple-400">
            <FaClock className="ml-1" />
            <span>قيد المعالجة</span>
          </div>
        </div>

        {/* أداء النظام */}
        <div className="touch-card stats-card">
          <div className="flex justify-between items-start mb-4">
            <div>
              <p className="text-secondary-500 dark:text-secondary-400 text-sm font-medium mb-1">أداء النظام</p>
              <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">{localStats.system_performance}%</p>
            </div>
            <div className="p-4 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
              <FaTachometerAlt className="text-xl" />
            </div>
          </div>
          <div className={`flex items-center text-sm ${
            localStats.system_performance >= 80 ? 'text-green-600 dark:text-green-400' :
            localStats.system_performance >= 60 ? 'text-yellow-600 dark:text-yellow-400' :
            'text-red-600 dark:text-red-400'
          }`}>
            <FaBolt className="ml-1" />
            <span>
              {localStats.system_performance === 100 ? 'مثالي' :
               localStats.system_performance >= 80 ? 'ممتاز' :
               localStats.system_performance >= 60 ? 'جيد' :
               localStats.system_performance > 0 ? 'يحتاج تحسين' : 'متوقف'}
            </span>
          </div>
        </div>
      </div>

      {/* معلومات إضافية */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        {/* آخر خطأ */}
        <div className="touch-card stats-card">
          <div className="flex justify-between items-start mb-4">
            <div>
              <p className="text-secondary-500 dark:text-secondary-400 text-sm font-medium mb-1">آخر خطأ</p>
              <p className="text-lg font-bold text-secondary-900 dark:text-secondary-100">
                {systemHealth.last_error_time ? new Date(systemHealth.last_error_time).toLocaleString('ar-LY') : 'لا يوجد'}
              </p>
            </div>
            <div className="p-4 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
              <FaClock className="text-xl" />
            </div>
          </div>
          <div className="flex items-center text-sm text-blue-600 dark:text-blue-400">
            <FaInfoCircle className="ml-1" />
            <span>آخر نشاط</span>
          </div>
        </div>

        {/* معدل الحل */}
        <div className="touch-card stats-card">
          <div className="flex justify-between items-start mb-4">
            <div>
              <p className="text-secondary-500 dark:text-secondary-400 text-sm font-medium mb-1">معدل الحل</p>
              <p className="text-3xl font-bold text-secondary-900 dark:text-secondary-100">
                {localStats.total_errors > 0
                  ? `${Math.round((localStats.resolved_errors / localStats.total_errors) * 100)}%`
                  : '100%'
                }
              </p>
            </div>
            <div className={`p-4 rounded-full ${
              localStats.total_errors === 0 || (localStats.resolved_errors / localStats.total_errors) > 0.8
                ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400'
                : (localStats.resolved_errors / localStats.total_errors) > 0.5
                ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400'
                : 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400'
            }`}>
              <FaCheckCircle className="text-xl" />
            </div>
          </div>
          <div className={`flex items-center text-sm ${
            localStats.total_errors === 0 || (localStats.resolved_errors / localStats.total_errors) > 0.8
              ? 'text-green-600 dark:text-green-400'
              : (localStats.resolved_errors / localStats.total_errors) > 0.5
              ? 'text-yellow-600 dark:text-yellow-400'
              : 'text-red-600 dark:text-red-400'
          }`}>
            {localStats.total_errors === 0 || (localStats.resolved_errors / localStats.total_errors) > 0.8 ? (
              <FaCheckCircle className="ml-1" />
            ) : (localStats.resolved_errors / localStats.total_errors) > 0.5 ? (
              <FaExclamationTriangle className="ml-1" />
            ) : (
              <FaTimesCircle className="ml-1" />
            )}
            <span>
              {localStats.total_errors === 0 || (localStats.resolved_errors / localStats.total_errors) > 0.8
                ? 'ممتاز'
                : (localStats.resolved_errors / localStats.total_errors) > 0.5
                ? 'جيد'
                : 'يحتاج تحسين'
              }
            </span>
          </div>
        </div>
      </div>

      {/* التبويبات الفرعية */}
      <div className="touch-card bg-white dark:bg-gray-800 rounded-xl p-4 mb-6">
        <div className="flex border-b border-gray-200 dark:border-gray-700 mb-4 overflow-x-auto pb-1 custom-scrollbar-thin">
          <button
            onClick={() => setActiveTab('logs')}
            className={`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap transition-all duration-200 ${
              activeTab === 'logs'
                ? 'border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
            }`}
          >
            <FaBug className="ml-2" />
            <span>سجلات الأخطاء</span>
          </button>
          <button
            onClick={() => setActiveTab('alerts')}
            className={`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap transition-all duration-200 ${
              activeTab === 'alerts'
                ? 'border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
            }`}
          >
            <FaBell className="ml-2" />
            <span>التنبيهات</span>
          </button>
          <button
            onClick={() => setActiveTab('performance')}
            className={`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap transition-all duration-200 ${
              activeTab === 'performance'
                ? 'border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
            }`}
          >
            <FaTachometerAlt className="ml-2" />
            <span>مراقب الأداء</span>
          </button>
        </div>
      </div>

      {/* محتوى التبويبات */}
      {activeTab === 'logs' && (
        <div ref={logsTabRef}>
          {/* أدوات التحكم */}
      <div className="touch-card p-4">
        <div className="flex flex-wrap items-center justify-between gap-4">
          <div className="flex flex-wrap items-center gap-4">
            {/* فلاتر */}
            <div className="flex items-center gap-2">
              <FaFilter className="text-gray-500 dark:text-gray-400" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">فلاتر:</span>
            </div>

            <FilterSelect
              name="filterLevel"
              value={filterLevel}
              onChange={(value) => setFilterLevel(value)}
              options={[
                { value: 'all', label: 'جميع المستويات' },
                { value: 'CRITICAL', label: 'حرج' },
                { value: 'ERROR', label: 'خطأ' },
                { value: 'WARNING', label: 'تحذير' },
                { value: 'INFO', label: 'معلومات' }
              ]}
              size="sm"
              className="min-w-[130px]"
            />

            <FilterSelect
              name="filterSource"
              value={filterSource}
              onChange={(value) => setFilterSource(value)}
              options={[
                { value: 'all', label: 'جميع المصادر' },
                { value: 'FRONTEND', label: 'الواجهة الأمامية' },
                { value: 'BACKEND', label: 'الخلفية' },
                { value: 'DATABASE', label: 'قاعدة البيانات' },
                { value: 'SYSTEM', label: 'النظام' }
              ]}
              size="sm"
              className="min-w-[120px]"
            />

            <SmallButton
              onClick={() => setTerminalMode(!terminalMode)}
              variant="outline"
              className={terminalMode ? 'bg-primary-100 dark:bg-primary-900/30 border-primary-300 dark:border-primary-700 text-primary-700 dark:text-primary-300' : ''}
            >
              <FaTerminal />
              <span>{terminalMode ? 'وضع السجلات' : 'وضع الطرفية'}</span>
            </SmallButton>
          </div>

          <div className="flex items-center gap-2 flex-wrap">
            <button
              onClick={async () => {
                // تحديث البيانات والإحصائيات
                await fetchSystemLogs();
                await fetchSystemHealth();
              }}
              className="btn-outline-sm flex items-center gap-2"
              disabled={isLoading}
            >
              <FaSync className={isLoading ? 'animate-spin' : ''} />
              <span>تحديث البيانات</span>
            </button>

            <button
              onClick={() => {
                // إجبار التحديث بإعادة تحميل الصفحة
                window.location.reload();
              }}
              className="btn-secondary-sm flex items-center gap-2"
              disabled={isLoading}
            >
              <FaSync className={isLoading ? 'animate-spin' : ''} />
              <span>إعادة تحميل</span>
            </button>

            {selectedLogs.length > 0 && (
              <>
                <button
                  onClick={handleSendToSupport}
                  className="btn-primary-sm flex items-center gap-2"
                >
                  <FaPaperPlane />
                  <span>إرسال للدعم ({selectedLogs.length})</span>
                </button>

                <button
                  onClick={handleExportLogs}
                  className="btn-secondary-sm flex items-center gap-2"
                >
                  <FaDownload />
                  <span>تصدير</span>
                </button>
              </>
            )}

            <button
              onClick={handleRunDiagnostics}
              className="btn-secondary-sm flex items-center gap-2"
              disabled={isRunningDiagnostics}
            >
              <FaTools className={isRunningDiagnostics ? 'animate-spin' : ''} />
              <span>{isRunningDiagnostics ? 'جاري التشخيص...' : 'تشخيص النظام'}</span>
            </button>

            <button
              onClick={handleAutoResolveSelected}
              className={`btn-success-sm flex items-center gap-2 ${
                !canAutoResolve ? 'opacity-50 cursor-not-allowed' : ''
              }`}
              disabled={!canAutoResolve || isRunningDiagnostics}
              title={
                selectedLogs.length === 0
                  ? 'يرجى اختيار سجلات للحل التلقائي'
                  : selectedResolvableLogs.length === 0
                  ? 'السجلات المختارة لا يمكن حلها تلقائياً (أخطاء حرجة تحتاج دعم فني)'
                  : `حل تلقائي لـ ${selectedResolvableLogs.length} سجل (سيتم تجاهل الأخطاء الحرجة)`
              }
            >
              <FaTools className={isRunningDiagnostics ? 'animate-spin' : ''} />
              <span>
                {isRunningDiagnostics
                  ? 'جاري الحل...'
                  : selectedLogs.length > 0 && selectedResolvableLogs.length > 0
                  ? `حل تلقائي (${selectedResolvableLogs.length})`
                  : selectedLogs.length > 0 && selectedResolvableLogs.length === 0
                  ? 'حل تلقائي (0 قابل للحل)'
                  : 'حل تلقائي'
                }
              </span>
            </button>

            <button
              onClick={handleClearLogs}
              className="btn-danger-sm flex items-center gap-2"
              title="مسح جميع السجلات"
            >
              <FaTrash />
              <span>مسح الكل</span>
            </button>


          </div>
        </div>
      </div>



      {/* عرض السجلات */}
      {terminalMode ? (
        // وضع الطرفية
        <div className="touch-card p-0 overflow-hidden">
          <div className="bg-black text-green-400 font-mono text-sm">
            <div className="bg-gray-800 px-4 py-2 border-b border-gray-700">
              <div className="flex items-center justify-between">
                <span>SmartPOS System Logs Terminal</span>
                <span className="text-xs text-gray-400">
                  {filteredLogs.length} entries
                </span>
              </div>
            </div>
            <div className="p-4 h-96 overflow-y-auto custom-scrollbar" dir="ltr">
              {filteredLogs.length === 0 ? (
                <div className="text-center text-gray-500 py-8" dir="rtl">
                  <p>لا توجد سجلات للعرض</p>
                </div>
              ) : (
                filteredLogs.map((log) => (
                  <div key={log.id} className="mb-2 text-left">
                    <span className="text-gray-400">
                      [<FormattedDateTime date={log.timestamp} showTime={true} />]
                    </span>
                    <span className={`ml-2 ${
                      log.level === 'CRITICAL' ? 'text-red-400' :
                      log.level === 'ERROR' ? 'text-red-300' :
                      log.level === 'WARNING' ? 'text-yellow-400' :
                      'text-blue-400'
                    }`}>
                      [{log.level}]
                    </span>
                    <span className="text-purple-400 ml-2">[{log.source}]</span>
                    <span className="text-green-400">{log.message}</span>
                    {log.resolved && (
                      <span className="text-green-600 ml-2">[RESOLVED]</span>
                    )}
                    {log.details && (
                      <div className="text-gray-300 ml-4 mt-1 text-xs">
                        Details: {log.details}
                      </div>
                    )}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      ) : (
        // جدول البيانات الاحترافي
        <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden table-subtle-border">
          {/* Header مع أدوات التحكم */}
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              <div className="flex items-center gap-4">
                <h3 className="font-bold text-lg text-gray-900 dark:text-gray-100">سجلات النظام</h3>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  ({filteredLogs.length} من {allFilteredLogs.length} سجل)
                </span>
                {selectedLogs.length > 0 && (
                  <span className="text-sm bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 px-2 py-1 rounded-full">
                    {selectedLogs.length} محدد
                  </span>
                )}
              </div>
              <div className="flex items-center gap-3">
                <ToggleSwitch
                  id="show-resolved"
                  checked={showResolved}
                  onChange={(checked) => setShowResolved(checked)}
                  label="عرض المحلولة"
                  className="text-sm"
                />
                <ToggleSwitch
                  id="select-all-logs"
                  checked={selectedLogs.length === allFilteredLogs.length && allFilteredLogs.length > 0}
                  onChange={toggleAllLogs}
                  label={`تحديد الكل (${allFilteredLogs.length})`}
                  className="text-sm"
                />
              </div>
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-14 w-14 border-b-3 border-primary-600 dark:border-primary-400"></div>
            </div>
          ) : filteredLogs.length === 0 ? (
            <div className="text-center py-12">
              <FaInfoCircle className="mx-auto h-16 w-16 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">لا توجد سجلات للعرض</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
                {localSystemLogs.length === 0
                  ? 'لم يتم العثور على أي سجلات في قاعدة البيانات'
                  : 'جميع السجلات مخفية بواسطة الفلاتر الحالية'
                }
              </p>
              <div className="flex gap-3 justify-center">
                <button
                  onClick={() => fetchSystemLogs()}
                  className="btn-primary flex items-center gap-2"
                  disabled={isLoading}
                >
                  <FaSync className={isLoading ? 'animate-spin' : ''} />
                  <span>تحديث السجلات</span>
                </button>
                <button
                  onClick={() => window.location.reload()}
                  className="btn-secondary flex items-center gap-2"
                >
                  <FaSync />
                  <span>تحديث الصفحة</span>
                </button>
              </div>
            </div>
          ) : (
            <>
              {/* جدول السجلات */}
              <div className="overflow-x-auto custom-scrollbar">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-16">
                        <input
                          type="checkbox"
                          checked={selectedLogs.length === allFilteredLogs.length && allFilteredLogs.length > 0}
                          onChange={toggleAllLogs}
                          className="form-checkbox"
                          title={`تحديد/إلغاء تحديد جميع السجلات (${allFilteredLogs.length})`}
                        />
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        المستوى
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        المصدر
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        الرسالة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        التاريخ والوقت
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        الحالة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        الإجراءات
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {filteredLogs.map((log) => (
                      <tr
                        key={log.id}
                        className={`hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                          log.resolved ? 'opacity-60' : ''
                        }`}
                      >
                        {/* Checkbox */}
                        <td className="px-6 py-4 whitespace-nowrap">
                          <input
                            type="checkbox"
                            checked={selectedLogs.includes(log.id)}
                            onChange={() => toggleLogSelection(log.id)}
                            className="form-checkbox"
                          />
                        </td>

                        {/* المستوى */}
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center gap-2">
                            {getLevelIcon(log.level)}
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              log.level === 'CRITICAL' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' :
                              log.level === 'ERROR' ? 'bg-red-50 text-red-700 dark:bg-red-900/20 dark:text-red-400' :
                              log.level === 'WARNING' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                              'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                            }`}>
                              {log.level}
                            </span>
                          </div>
                        </td>

                        {/* المصدر */}
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center gap-2">
                            {getSourceIcon(log.source)}
                            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {log.source}
                            </span>
                          </div>
                        </td>

                        {/* الرسالة */}
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900 dark:text-gray-100">
                            <div className="line-clamp-2 max-w-md">
                              {log.message}
                            </div>
                            {(log.details || log.resolution_notes) && (
                              <button
                                onClick={() => setSelectedLogForDetails(log)}
                                className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 mt-1 transition-colors"
                              >
                                عرض التفاصيل
                              </button>
                            )}
                          </div>
                        </td>

                        {/* التاريخ والوقت */}
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            <div><FormattedDate date={log.timestamp} /></div>
                            <div className="text-xs"><FormattedTime date={log.timestamp} /></div>
                          </div>
                        </td>

                        {/* الحالة */}
                        <td className="px-6 py-4 whitespace-nowrap">
                          {log.resolved ? (
                            <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 text-xs rounded-full">
                              <FaCheck className="w-3 h-3" />
                              محلول
                            </span>
                          ) : log.sent_to_support ? (
                            // السجلات المرسلة للدعم
                            <span className="inline-flex items-center gap-1 px-2 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 text-xs rounded-full">
                              <FaPaperPlane className="w-3 h-3" />
                              تم إرساله للدعم
                            </span>
                          ) : (
                            // تصنيف الحالة حسب نوع السجل
                            log.level === 'INFO' ? (
                              <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs rounded-full">
                                <FaInfoCircle className="w-3 h-3" />
                                معلومة
                              </span>
                            ) : log.level === 'CRITICAL' ? (
                              <span className="inline-flex items-center gap-1 px-2 py-1 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 text-xs rounded-full">
                                <FaPaperPlane className="w-3 h-3" />
                                يتطلب دعم فني
                              </span>
                            ) : (
                              <span className="inline-flex items-center gap-1 px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 text-xs rounded-full">
                                <FaExclamationTriangle className="w-3 h-3" />
                                قيد المعالجة
                              </span>
                            )
                          )}
                        </td>

                        {/* الإجراءات */}
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => setSelectedLogForDetails(log)}
                              className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 transition-colors"
                              title="عرض التفاصيل"
                            >
                              <FaInfoCircle className="w-4 h-4" />
                            </button>
                            {!log.resolved && !log.sent_to_support && (
                              <>
                                {log.level === 'INFO' ? (
                                  // سجلات المعلومات - يمكن حذفها
                                  <button
                                    onClick={() => handleDeleteLog(log.id)}
                                    className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 transition-colors"
                                    title="حذف السجل"
                                  >
                                    <FaTimes className="w-4 h-4" />
                                  </button>
                                ) : log.level === 'CRITICAL' ? (
                                  // الأخطاء الحرجة - يجب إرسالها للدعم
                                  <button
                                    onClick={() => handleSendCriticalToSupport(log)}
                                    className="text-orange-600 dark:text-orange-400 hover:text-orange-900 dark:hover:text-orange-300 transition-colors"
                                    title="إرسال للدعم الفني"
                                  >
                                    <FaPaperPlane className="w-4 h-4" />
                                  </button>
                                ) : (
                                  // الأخطاء العادية والتحذيرات - يمكن حلها
                                  <button
                                    onClick={() => handleResolveLog(log.id)}
                                    className="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300 transition-colors"
                                    title="حل المشكلة"
                                  >
                                    <FaCheck className="w-4 h-4" />
                                  </button>
                                )}
                              </>
                            )}
                            {log.sent_to_support && !log.resolved && (
                              // السجلات المرسلة للدعم - في انتظار الرد
                              <span className="text-xs text-purple-600 dark:text-purple-400 italic">
                                في انتظار رد الدعم
                              </span>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              <div className="bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700 sm:px-6">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                  <div className="text-sm text-gray-700 dark:text-gray-300">
                    عرض <span className="font-medium">{Math.min(currentPage * pageSize, filteredLogs.length)}</span> من أصل{' '}
                    <span className="font-medium">{totalLogs}</span> سجل
                  </div>

                  {totalPages > 1 && (
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                        disabled={currentPage === 1 || isLoading}
                        className="btn-outline-sm flex items-center gap-1 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        السابق
                      </button>

                      <div className="flex items-center gap-1">
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                          const page = i + 1;
                          return (
                            <button
                              key={page}
                              onClick={() => setCurrentPage(page)}
                              className={`px-3 py-1 text-sm rounded ${
                                currentPage === page
                                  ? 'bg-primary-600 text-white'
                                  : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                              }`}
                            >
                              {page}
                            </button>
                          );
                        })}
                        {totalPages > 5 && (
                          <>
                            <span className="text-gray-500">...</span>
                            <button
                              onClick={() => setCurrentPage(totalPages)}
                              className={`px-3 py-1 text-sm rounded ${
                                currentPage === totalPages
                                  ? 'bg-primary-600 text-white'
                                  : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                              }`}
                            >
                              {totalPages}
                            </button>
                          </>
                        )}
                      </div>

                      <button
                        onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                        disabled={currentPage === totalPages || isLoading}
                        className="btn-outline-sm flex items-center gap-1 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        التالي
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}
        </div>
      )}

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center gap-2">
                <FaExclamationTriangle className="text-red-600" />
                <span className="text-red-800">{error}</span>
              </div>
            </div>
          )}
        </div>
      )}

      {/* تبويبة التنبيهات */}
      {activeTab === 'alerts' && (
        <div ref={alertsTabRef}>
          <SystemAlerts />
        </div>
      )}

      {/* تبويبة مراقب الأداء */}
      {activeTab === 'performance' && (
        <div ref={performanceTabRef}>
          <PerformanceMonitorComponent />
        </div>
      )}



      {/* مودال النجاح */}
      <SimpleSuccessModal
        isOpen={successModal.isOpen}
        onClose={() => setSuccessModal({ isOpen: false, message: '' })}
        message={successModal.message}
        autoClose={true}
        autoCloseDelay={3000}
      />

      {/* مودال حذف السجل */}
      <Modal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, logId: 0 })}
        title="حذف سجل المعلومات"
        size="md"
      >
        <div className="space-y-4">
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <FaExclamationTriangle className="text-yellow-600 dark:text-yellow-400" />
              <span className="font-medium text-yellow-800 dark:text-yellow-300">تحذير</span>
            </div>
            <p className="text-sm text-yellow-700 dark:text-yellow-400">
              هل أنت متأكد من حذف هذا السجل؟ هذا الإجراء لا يمكن التراجع عنه.
            </p>
          </div>

          <div className="flex gap-3 pt-4">
            <button
              onClick={confirmDeleteLog}
              className="flex-1 btn-danger flex items-center justify-center gap-2"
            >
              <FaTimes />
              <span>تأكيد الحذف</span>
            </button>
            <button
              onClick={() => setDeleteModal({ isOpen: false, logId: 0 })}
              className="flex-1 btn-secondary"
            >
              إلغاء
            </button>
          </div>
        </div>
      </Modal>

      {/* مودال حل السجل */}
      <Modal
        isOpen={resolveModal.isOpen}
        onClose={() => setResolveModal({ isOpen: false, logId: 0, notes: '' })}
        title="حل مشكلة السجل"
        size="md"
      >
        <div className="space-y-4">
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <FaInfoCircle className="text-blue-600 dark:text-blue-400" />
              <span className="font-medium text-blue-800 dark:text-blue-300">معلومات</span>
            </div>
            <p className="text-sm text-blue-700 dark:text-blue-400">
              سيتم وضع علامة "محلول" على هذا السجل. يمكنك إضافة ملاحظات حول كيفية حل المشكلة.
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              ملاحظات الحل (اختياري)
            </label>
            <textarea
              value={resolveModal.notes}
              onChange={(e) => setResolveModal({ ...resolveModal, notes: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-gray-100"
              rows={3}
              placeholder="اكتب ملاحظات حول كيفية حل المشكلة..."
            />
          </div>

          <div className="flex gap-3 pt-4">
            <button
              onClick={confirmResolveLog}
              className="flex-1 btn-primary flex items-center justify-center gap-2"
            >
              <FaCheck />
              <span>تأكيد الحل</span>
            </button>
            <button
              onClick={() => setResolveModal({ isOpen: false, logId: 0, notes: '' })}
              className="flex-1 btn-secondary"
            >
              إلغاء
            </button>
          </div>
        </div>
      </Modal>

      {/* مودال تشخيص النظام */}
      <Modal
        isOpen={diagnosticsModal.isOpen}
        onClose={() => setDiagnosticsModal({ isOpen: false, data: null })}
        title="نتائج تشخيص النظام"
        size="xl"
      >
        {diagnosticsModal.data && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* معلومات المتصفح */}
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <h4 className="font-semibold mb-3 text-blue-800 dark:text-blue-300 flex items-center gap-2">
                  <FaDesktop className="text-blue-600 dark:text-blue-400" />
                  معلومات المتصفح
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">اللغة:</span>
                    <span className="font-medium text-gray-900 dark:text-gray-100">{diagnosticsModal.data.browser?.language}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">الكوكيز:</span>
                    <span className={`font-medium ${diagnosticsModal.data.browser?.cookieEnabled ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                      {diagnosticsModal.data.browser?.cookieEnabled ? 'مفعلة' : 'معطلة'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">الاتصال:</span>
                    <span className={`font-medium ${diagnosticsModal.data.browser?.onLine ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                      {diagnosticsModal.data.browser?.onLine ? 'متصل' : 'غير متصل'}
                    </span>
                  </div>
                </div>
              </div>

              {/* الأداء */}
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                <h4 className="font-semibold mb-3 text-green-800 dark:text-green-300 flex items-center gap-2">
                  <FaTachometerAlt className="text-green-600 dark:text-green-400" />
                  الأداء
                </h4>
                <div className="space-y-2 text-sm">
                  {diagnosticsModal.data.performance?.memory && (
                    <>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">استخدام الذاكرة:</span>
                        <span className="font-medium text-gray-900 dark:text-gray-100">
                          {Math.round(diagnosticsModal.data.performance.memory.usedJSHeapSize / 1024 / 1024)} MB
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">إجمالي الذاكرة:</span>
                        <span className="font-medium text-gray-900 dark:text-gray-100">
                          {Math.round(diagnosticsModal.data.performance.memory.totalJSHeapSize / 1024 / 1024)} MB
                        </span>
                      </div>
                    </>
                  )}
                  {diagnosticsModal.data.performance?.timing && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">وقت التحميل:</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        {diagnosticsModal.data.performance.timing.loadTime} ms
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* التخزين */}
              <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
                <h4 className="font-semibold mb-3 text-purple-800 dark:text-purple-300 flex items-center gap-2">
                  <FaDatabase className="text-purple-600 dark:text-purple-400" />
                  التخزين
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">التخزين المحلي:</span>
                    <span className={`font-medium ${diagnosticsModal.data.storage?.localStorage?.available ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                      {diagnosticsModal.data.storage?.localStorage?.available ? 'متاح' : 'غير متاح'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">تخزين الجلسة:</span>
                    <span className={`font-medium ${diagnosticsModal.data.storage?.sessionStorage?.available ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                      {diagnosticsModal.data.storage?.sessionStorage?.available ? 'متاح' : 'غير متاح'}
                    </span>
                  </div>
                </div>
              </div>

              {/* الشبكة */}
              <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
                <h4 className="font-semibold mb-3 text-orange-800 dark:text-orange-300 flex items-center gap-2">
                  <FaServer className="text-orange-600 dark:text-orange-400" />
                  الشبكة
                </h4>
                <div className="space-y-2 text-sm">
                  {diagnosticsModal.data.network?.connection ? (
                    <>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">نوع الاتصال:</span>
                        <span className="font-medium text-gray-900 dark:text-gray-100">
                          {diagnosticsModal.data.network.connection.effectiveType}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">سرعة التحميل:</span>
                        <span className="font-medium text-gray-900 dark:text-gray-100">
                          {diagnosticsModal.data.network.connection.downlink} Mbps
                        </span>
                      </div>
                    </>
                  ) : (
                    <p className="text-gray-600 dark:text-gray-400">معلومات الشبكة غير متاحة</p>
                  )}
                </div>
              </div>
            </div>

            {/* أزرار الإجراءات */}
            <div className="flex gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={handleLocalAutoFix}
                className="btn-primary-sm flex items-center gap-2"
              >
                <FaTools />
                <span>إصلاح محلي</span>
              </button>
              <button
                onClick={handleExportDiagnostics}
                className="btn-secondary-sm flex items-center gap-2"
              >
                <FaDownload />
                <span>تصدير التشخيص</span>
              </button>
              <button
                onClick={() => setDiagnosticsModal({ isOpen: false, data: null })}
                className="btn-outline-sm"
              >
                إغلاق
              </button>
            </div>
          </div>
        )}
      </Modal>

      {/* مودال التحميل */}
      <LoadingModal
        isOpen={loadingModal.isOpen}
        onClose={closeLoadingModal}
        title={loadingModal.title}
        message={loadingModal.message}
        type={loadingModal.type}
        allowCancel={loadingModal.type === 'network-check'}
        onCancel={closeLoadingModal}
        showProgress={loadingModal.showProgress}
        progress={loadingModal.progress}
      />

      {/* نافذة كلمة المرور التأكيدية */}
      <Modal
        isOpen={showPasswordInput}
        onClose={cancelClearAction}
        title="مسح سجلات النظام"
        size="md"
      >
        <div className="space-y-6">
          {/* رمز التحذير */}
          <div className="text-center">
            <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/30 mb-4">
              <FaTrash className="h-8 w-8 text-red-600 dark:text-red-400" />
            </div>
          </div>

          {/* إحصائيات السجلات */}
          <div className="bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <FaInfoCircle className="h-5 w-5 mt-0.5 text-blue-600 dark:text-blue-400 flex-shrink-0" />
              <div className="flex-1">
                <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-3">
                  إحصائيات السجلات الحالية
                </h4>
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">الأخطاء الحرجة:</span>
                    <span className="font-medium text-red-600 dark:text-red-400">
                      {localStats.critical_errors}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">الأخطاء:</span>
                    <span className="font-medium text-red-500 dark:text-red-400">
                      {localStats.regular_errors}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">التحذيرات:</span>
                    <span className="font-medium text-yellow-600 dark:text-yellow-400">
                      {localStats.warning_errors}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">إجمالي السجلات:</span>
                    <span className="font-medium text-blue-600 dark:text-blue-400">
                      {localStats.total_errors}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* اختيار نوع المسح */}
          <div className="space-y-4">
            <h4 className="font-medium text-gray-800 dark:text-gray-200">
              اختر نوع المسح:
            </h4>

            <div className="space-y-3">
              {/* مسح عادي */}
              <label className={`flex items-start gap-3 p-4 border rounded-lg cursor-pointer transition-colors ${
                pendingClearAction === 'normal'
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
              }`}>
                <input
                  type="radio"
                  name="clearType"
                  value="normal"
                  checked={pendingClearAction === 'normal'}
                  onChange={() => setPendingClearAction('normal')}
                  className="mt-1 text-blue-600 focus:ring-blue-500"
                />
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium text-gray-900 dark:text-white">مسح عادي</span>
                    <span className="px-2 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs rounded-full">
                      موصى به
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    حذف جميع السجلات مع إنشاء سجل توثيق جديد للعملية
                  </p>
                </div>
              </label>

              {/* مسح كامل */}
              <label className={`flex items-start gap-3 p-4 border rounded-lg cursor-pointer transition-colors ${
                pendingClearAction === 'complete'
                  ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
              }`}>
                <input
                  type="radio"
                  name="clearType"
                  value="complete"
                  checked={pendingClearAction === 'complete'}
                  onChange={() => setPendingClearAction('complete')}
                  className="mt-1 text-red-600 focus:ring-red-500"
                />
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium text-gray-900 dark:text-white">مسح كامل</span>
                    <span className="px-2 py-0.5 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 text-xs rounded-full">
                      خطر
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    حذف جميع السجلات نهائياً بدون إنشاء أي سجل توثيق
                  </p>
                </div>
              </label>
            </div>
          </div>

          {/* رسالة التحذير */}
          <div className={`border rounded-lg p-4 ${
            pendingClearAction === 'complete'
              ? 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
              : 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'
          }`}>
            <div className="flex items-start gap-3">
              <FaExclamationTriangle className={`h-5 w-5 mt-0.5 flex-shrink-0 ${
                pendingClearAction === 'complete'
                  ? 'text-red-600 dark:text-red-400'
                  : 'text-yellow-600 dark:text-yellow-400'
              }`} />
              <div>
                <h4 className={`font-medium mb-2 ${
                  pendingClearAction === 'complete'
                    ? 'text-red-800 dark:text-red-300'
                    : 'text-yellow-800 dark:text-yellow-300'
                }`}>
                  {pendingClearAction === 'complete' ? 'تحذير شديد الأهمية' : 'تنبيه مهم'}
                </h4>
                <p className={`text-sm leading-relaxed ${
                  pendingClearAction === 'complete'
                    ? 'text-red-700 dark:text-red-400'
                    : 'text-yellow-700 dark:text-yellow-400'
                }`}>
                  {pendingClearAction === 'complete'
                    ? 'سيتم حذف جميع السجلات نهائياً بدون إمكانية الاسترداد ولن يتم إنشاء أي سجل توثيق للعملية.'
                    : 'سيتم حذف جميع السجلات مع إنشاء سجل توثيق جديد للعملية.'
                  }
                </p>
                {pendingClearAction === 'complete' && (
                  <div className="mt-3 space-y-1 text-xs text-red-600 dark:text-red-400">
                    <p>• لن يتم إنشاء سجل توثيق للعملية</p>
                    <p>• لا يمكن استرداد البيانات بعد الحذف</p>
                    <p>• قد تفقد معلومات مهمة لاستقرار النظام</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* تذكير الدعم */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <FaPaperPlane className="h-5 w-5 mt-0.5 text-blue-600 dark:text-blue-400 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-2">
                  تذكير: إرسال الأخطاء للدعم
                </h4>
                <p className="text-sm text-blue-700 dark:text-blue-400 leading-relaxed">
                  تأكد من إرسال الأخطاء الحرجة والتحذيرات المهمة لفريق الدعم الفني قبل المسح.
                  هذا يساعد في تحسين استقرار النظام وحل المشاكل مستقبلاً.
                </p>
                <p className="text-xs text-blue-600 dark:text-blue-500 mt-2">
                  📧 البريد الإلكتروني للدعم: <EMAIL>
                </p>
              </div>
            </div>
          </div>

          {/* حقل كلمة المرور */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              لتأكيد العملية، اكتب كلمة <span className="font-bold text-red-600 dark:text-red-400">"DELETE"</span> بالأحرف الكبيرة:
            </label>
            <input
              type="text"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              placeholder="اكتب DELETE للتأكيد"
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg
                       bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                       focus:ring-2 focus:ring-red-500 focus:border-red-500
                       text-center font-mono text-lg tracking-wider
                       placeholder:text-gray-400 dark:placeholder:text-gray-500"
              autoFocus
              dir="ltr"
            />
            {!pendingClearAction && (
              <p className="text-sm text-orange-600 dark:text-orange-400 text-center">
                يرجى اختيار نوع المسح أولاً
              </p>
            )}
            {pendingClearAction && confirmPassword && confirmPassword !== 'DELETE' && (
              <p className="text-sm text-red-600 dark:text-red-400 text-center">
                يجب كتابة "DELETE" بالضبط بالأحرف الكبيرة
              </p>
            )}
            {pendingClearAction && confirmPassword === 'DELETE' && (
              <p className="text-sm text-green-600 dark:text-green-400 text-center flex items-center justify-center gap-1">
                <FaCheck className="h-4 w-4" />
                تم التأكيد بنجاح - جاهز للمسح
              </p>
            )}
          </div>

          {/* أزرار الإجراءات */}
          <div className="flex gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={cancelClearAction}
              className="flex-1 btn-secondary flex items-center justify-center gap-2"
            >
              <FaTimes />
              <span>إلغاء</span>
            </button>

            <button
              onClick={executeClearAction}
              disabled={confirmPassword !== 'DELETE' || !pendingClearAction}
              className={`flex-1 btn-danger flex items-center justify-center gap-2 ${
                confirmPassword !== 'DELETE' || !pendingClearAction
                  ? 'opacity-50 cursor-not-allowed'
                  : ''
              }`}
            >
              <FaTrash />
              <span>
                {pendingClearAction === 'complete' ? 'مسح كامل' : 'مسح عادي'}
              </span>
            </button>
          </div>
        </div>
      </Modal>

      {/* مودال تفاصيل السجل */}
      <LogDetailsModal
        isOpen={!!selectedLogForDetails}
        onClose={() => setSelectedLogForDetails(null)}
        log={selectedLogForDetails}
        onResolve={handleResolveLog}
        onDelete={handleDeleteLog}
        onSendToSupport={handleSendCriticalToSupport}
      />

      {/* مودال الخطأ */}
      <SimpleErrorModal
        isOpen={errorModal.isOpen}
        onClose={() => setErrorModal({ isOpen: false, message: '' })}
        message={errorModal.message}
      />

      {/* نافذة الحل التلقائي الاحترافية - معالجة في الخلفية */}
      <AutoResolveBackendModal
        isOpen={isAutoResolving || autoResolveProgress !== null || autoResolveResult !== null || autoResolveError !== null}
        progress={autoResolveProgress}
        result={autoResolveResult}
        error={autoResolveError}
        onClose={handleAutoResolveModalClose}
        onStop={stopAutoResolve}
      />
    </div>
  );
};

export default SystemLogs;
