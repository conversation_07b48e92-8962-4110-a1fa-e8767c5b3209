import React, { useState, useEffect, useMemo } from 'react';
import { useLocation } from 'react-router-dom';
import {
  FiPlus,
  FiEdit,
  FiFileText,
  FiCheckCircle,
  FiXCircle,
  FiSearch,
  FiEye,
  FiClock
} from 'react-icons/fi';
import { useWarrantyClaimsStore } from '../../stores/warranty';
import { WarrantyClaim, ClaimFilters, CLAIM_STATUS_LABELS, CLAIM_TYPE_LABELS } from '../../types/warranty';
import Modal from '../Modal';
import SuccessModal from '../SuccessModal';
import { TextInput, TextArea, SelectInput, NumberInput } from '../inputs';
import WarrantySelector from '../WarrantySelector';
import { TopbarTooltip } from '../ui';
import TablePagination from '../catalog/TablePagination';
import FormattedCurrency from '../FormattedCurrency';
import { FormattedDate } from '../FormattedDateTime';
import api from '../../lib/axios';
import { StatusBadge, ClaimTypeBadge } from './WarrantyHelpers';

interface WarrantyClaimsTabProps {
  className?: string;
}

const WarrantyClaimsTab: React.FC<WarrantyClaimsTabProps> = ({ className = '' }) => {
  const location = useLocation();
  const {
    claims,
    loading,
    fetchClaims,
    createClaim,
    approveClaim,
    rejectClaim
  } = useWarrantyClaimsStore();

  // State
  const [filters, setFilters] = useState<ClaimFilters>({
    search: '',
    status: 'all',
    claim_type: 'all'
  });



  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Modal states
  const [createModal, setCreateModal] = useState({
    isOpen: false
  });

  const [processModal, setProcessModal] = useState({
    isOpen: false,
    claim: null as WarrantyClaim | null
  });

  const [statusHistoryModal, setStatusHistoryModal] = useState({
    isOpen: false,
    claim: null as WarrantyClaim | null,
    history: [] as any[]
  });

  // Dynamic status management
  const [availableStatuses, setAvailableStatuses] = useState<any[]>([]);
  const [selectedNewStatus, setSelectedNewStatus] = useState('');

  const [detailsModal, setDetailsModal] = useState({
    isOpen: false,
    claim: null as WarrantyClaim | null
  });

  const [successModal, setSuccessModal] = useState({
    isOpen: false,
    message: ''
  });

  // Form states
  const [createForm, setCreateForm] = useState({
    warranty_id: 0,
    claim_type: 'repair' as 'repair' | 'replacement' | 'refund',
    issue_description: '',
    notes: ''
  });

  // Selected warranty for form
  const [selectedWarranty, setSelectedWarranty] = useState<any>(null);

  const [processForm, setProcessForm] = useState({
    status: 'approved' as 'approved' | 'rejected' | 'in_progress' | 'completed',
    resolution: '',
    actual_cost: 0,
    notes: ''
  });

  // Load data on mount
  useEffect(() => {
    fetchClaims(filters);
  }, [fetchClaims]);

  // Handle warranty filter from navigation
  useEffect(() => {
    const state = location.state as { warrantyFilter?: string; warrantyId?: number } | null;
    if (state?.warrantyFilter) {
      setFilters(prev => ({
        ...prev,
        search: state.warrantyFilter
      }));
      // Clear the state to prevent re-filtering on subsequent renders
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);



  // Filter and paginate data
  const filteredClaims = useMemo(() => {
    return claims.filter(claim => {
      const matchesSearch = !filters.search ||
        claim.claim_number.toLowerCase().includes(filters.search.toLowerCase()) ||
        claim.warranty_number.toLowerCase().includes(filters.search.toLowerCase()) ||
        claim.product_name.toLowerCase().includes(filters.search.toLowerCase()) ||
        claim.issue_description.toLowerCase().includes(filters.search.toLowerCase()) ||
        (claim.customer_name && claim.customer_name.toLowerCase().includes(filters.search.toLowerCase()));

      const matchesStatus = filters.status === 'all' || claim.status === filters.status;
      const matchesType = filters.claim_type === 'all' || claim.claim_type === filters.claim_type;

      return matchesSearch && matchesStatus && matchesType;
    });
  }, [claims, filters]);

  const totalPages = Math.ceil(filteredClaims.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedClaims = filteredClaims.slice(startIndex, startIndex + itemsPerPage);

  // Handle filter changes
  const handleFilterChange = (key: keyof ClaimFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1);
  };

  // Reset forms
  const resetCreateForm = () => {
    setCreateForm({
      warranty_id: 0,
      claim_type: 'repair',
      issue_description: '',
      notes: ''
    });
    setSelectedWarranty(null);
  };

  const resetProcessForm = () => {
    setProcessForm({
      status: 'approved',
      resolution: '',
      actual_cost: 0,
      notes: ''
    });
  };

  // Validate create form
  const validateCreateForm = () => {
    const errors: string[] = [];

    if (!selectedWarranty) {
      errors.push('يجب تحديد الضمان');
    }

    if (!createForm.issue_description.trim()) {
      errors.push('يجب إدخال وصف المشكلة');
    }

    return errors;
  };

  // Check if create form is valid (for real-time validation)
  const isCreateFormValid = () => {
    return selectedWarranty &&
           createForm.issue_description.trim().length > 0;
  };

  // Check if process form is valid (for real-time validation)
  const isProcessFormValid = () => {
    return selectedNewStatus &&
           selectedNewStatus.trim().length > 0 &&
           processForm.notes.trim().length > 0;
  };

  // Handle create claim
  const handleCreateClaim = async () => {
    try {
      // Validate form
      const validationErrors = validateCreateForm();
      if (validationErrors.length > 0) {
        console.error('❌ أخطاء في النموذج:', validationErrors);
        alert('يرجى تصحيح الأخطاء التالية:\n' + validationErrors.join('\n'));
        return;
      }

      // Prepare claim data from selected warranty
      const claimData = {
        warranty_id: selectedWarranty?.id || 0,
        claim_type: createForm.claim_type,
        issue_description: createForm.issue_description,
        notes: createForm.notes || null
      };

      console.log('🔄 إنشاء مطالبة ضمان جديدة:', claimData);
      await createClaim(claimData);

      setCreateModal({ isOpen: false });
      resetCreateForm();
      setSuccessModal({
        isOpen: true,
        message: 'تم إنشاء مطالبة الضمان بنجاح'
      });

      console.log('✅ تم إنشاء مطالبة الضمان بنجاح');
    } catch (error: any) {
      console.error('❌ خطأ في إنشاء مطالبة الضمان:', error);

      let errorMessage = 'فشل في إنشاء مطالبة الضمان';

      if (error?.response?.data?.detail) {
        if (Array.isArray(error.response.data.detail)) {
          // Handle validation errors from FastAPI
          const validationErrors = error.response.data.detail.map((err: any) => {
            const field = err.loc?.[err.loc.length - 1] || 'حقل غير معروف';
            const fieldNames: { [key: string]: string } = {
              'warranty_id': 'الضمان',
              'claim_type': 'نوع المطالبة',
              'issue_description': 'وصف المشكلة',
              'notes': 'الملاحظات'
            };

            const arabicField = fieldNames[field] || field;
            return `${arabicField}: ${err.msg}`;
          });

          errorMessage = 'أخطاء في البيانات:\n' + validationErrors.join('\n');
        } else {
          errorMessage = error.response.data.detail;
        }
      } else if (error?.message) {
        errorMessage = error.message;
      }

      alert(errorMessage);
    }
  };

  // Fetch available next statuses for a claim
  const fetchAvailableStatuses = async (claimId: number) => {
    try {
      console.log('🔄 جلب الحالات المتاحة للمطالبة:', claimId);

      // استخدام api service بدلاً من fetch مباشرة لضمان استخدام URL الصحيح
      const response = await api.get(`/api/warranty-claims/${claimId}/next-statuses`);
      const data = response.data;

      setAvailableStatuses(data.available_statuses || []);
      console.log('✅ تم جلب الحالات المتاحة:', data.available_statuses);
    } catch (error: any) {
      console.error('❌ خطأ في جلب الحالات المتاحة:', error);
      console.error('تفاصيل الخطأ:', error.response?.data?.detail || error.message);
      setAvailableStatuses([]);
    }
  };

  // Fetch status history for a claim
  const fetchStatusHistory = async (claimId: number) => {
    try {
      console.log('🔄 جلب تاريخ تغييرات الحالة للمطالبة:', claimId);

      // استخدام api service بدلاً من fetch مباشرة لضمان استخدام URL الصحيح
      const response = await api.get(`/api/warranty-claims/${claimId}/status-history`);
      const data = response.data;

      return data.status_history || [];
    } catch (error: any) {
      console.error('❌ خطأ في جلب تاريخ التغييرات:', error);
      console.error('تفاصيل الخطأ:', error.response?.data?.detail || error.message);
      return [];
    }
  };

  // Handle dynamic status change
  const handleStatusChange = async (claimId: number, newStatus: string, additionalData: any = {}) => {
    try {
      console.log('🔄 تغيير حالة المطالبة:', claimId, 'إلى:', newStatus);

      // استخدام api service بدلاً من fetch مباشرة لضمان استخدام URL الصحيح
      const response = await api.post(`/api/warranty-claims/${claimId}/change-status`, {
        new_status: newStatus,
        ...additionalData
      });

      const data = response.data;

      // تحديث قائمة المطالبات
      await fetchClaims();

      setSuccessModal({
        isOpen: true,
        message: data.message || 'تم تغيير حالة المطالبة بنجاح'
      });

      console.log('✅ تم تغيير حالة المطالبة بنجاح');
      return data;
    } catch (error: any) {
      console.error('❌ خطأ في تغيير حالة المطالبة:', error);
      console.error('تفاصيل الخطأ:', error.response?.data?.detail || error.message);
      throw error;
    }
  };

  // Get suggested next status based on claim type
  const getSuggestedNextStatus = (claimType: string, currentStatus: string) => {
    if (currentStatus === 'approved') {
      if (claimType === 'refund') {
        return 'completed'; // الاسترداد يمكن إكماله مباشرة
      } else if (claimType === 'repair' || claimType === 'replacement') {
        return 'in_progress'; // الإصلاح والاستبدال يحتاجان وقت
      }
    } else if (currentStatus === 'in_progress') {
      return 'completed'; // من قيد التنفيذ إلى مكتمل
    }
    return null;
  };

  // Handle process claim with dynamic status
  const handleProcessClaim = async () => {
    if (!processModal.claim || !selectedNewStatus) return;

    try {
      console.log('🔄 معالجة مطالبة الضمان:', processModal.claim.id, processForm);

      // استخدام النظام الديناميكي لتغيير الحالة
      await handleStatusChange(processModal.claim.id, selectedNewStatus, {
        reason: processForm.notes, // استخدام notes كسبب
        notes: processForm.notes,
        resolution_details: processForm.resolution,
        estimated_cost: processForm.actual_cost?.toString(), // استخدام actual_cost
        actual_cost: processForm.actual_cost?.toString()
      });

      setProcessModal({ isOpen: false, claim: null });
      resetProcessForm();
      setSelectedNewStatus('');

      console.log('✅ تم معالجة مطالبة الضمان بنجاح');
    } catch (error: any) {
      console.error('❌ خطأ في معالجة مطالبة الضمان:', error);

      let errorMessage = 'فشل في معالجة مطالبة الضمان';
      if (error?.message) {
        errorMessage = error.message;
      }

      alert(errorMessage);
    }
  };

  // Handle quick approve
  const handleQuickApprove = async (claim: WarrantyClaim) => {
    try {
      console.log('🔄 الموافقة على المطالبة:', claim.id);
      await approveClaim(claim.id, 'تمت الموافقة على المطالبة');

      setSuccessModal({
        isOpen: true,
        message: 'تمت الموافقة على المطالبة بنجاح'
      });

      console.log('✅ تمت الموافقة على المطالبة بنجاح');
    } catch (error: any) {
      console.error('❌ خطأ في الموافقة على المطالبة:', error);

      let errorMessage = 'فشل في الموافقة على المطالبة';
      if (error?.response?.data?.detail) {
        errorMessage = Array.isArray(error.response.data.detail)
          ? error.response.data.detail.map((err: any) => err.msg).join('\n')
          : error.response.data.detail;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      alert(errorMessage);
    }
  };

  // Handle quick reject
  const handleQuickReject = async (claim: WarrantyClaim) => {
    try {
      console.log('🔄 رفض المطالبة:', claim.id);
      await rejectClaim(claim.id, 'تم رفض المطالبة');

      setSuccessModal({
        isOpen: true,
        message: 'تم رفض المطالبة بنجاح'
      });

      console.log('✅ تم رفض المطالبة بنجاح');
    } catch (error: any) {
      console.error('❌ خطأ في رفض المطالبة:', error);

      let errorMessage = 'فشل في رفض المطالبة';
      if (error?.response?.data?.detail) {
        errorMessage = Array.isArray(error.response.data.detail)
          ? error.response.data.detail.map((err: any) => err.msg).join('\n')
          : error.response.data.detail;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      alert(errorMessage);
    }
  };



  return (
    <div className={`space-y-6 ${className}`}>
      {/* Data Table with Header and Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 overflow-hidden">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center p-6 gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <div className="min-w-0 flex-1">
                <h2 className="text-lg sm:text-xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FiFileText className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                  <span className="truncate">مطالبات الضمان</span>
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  إدارة ومعالجة مطالبات الضمان
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 flex-shrink-0">
              <button
                onClick={() => {
                  resetCreateForm();
                  setCreateModal({ isOpen: true });
                }}
                className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl min-w-[140px]"
              >
                <FiPlus className="ml-2" />
                إضافة مطالبة
              </button>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-gray-50 dark:bg-gray-700/50 border-b border-gray-200 dark:border-gray-600 p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <TextInput
              name="search"
              value={filters.search || ''}
              onChange={(value) => handleFilterChange('search', value)}
              placeholder="البحث في المطالبات..."
              icon={<FiSearch />}
              className=""
            />

            <SelectInput
              label=""
              name="status-filter"
              value={filters.status || 'all'}
              onChange={(value) => handleFilterChange('status', value)}
              options={[
                { value: 'all', label: 'جميع الحالات' },
                { value: 'pending', label: 'في الانتظار' },
                { value: 'approved', label: 'موافق عليه' },
                { value: 'rejected', label: 'مرفوض' },
                { value: 'in_progress', label: 'قيد التنفيذ' },
                { value: 'completed', label: 'مكتمل' }
              ]}
              placeholder="فلترة حسب الحالة"
            />

            <SelectInput
              label=""
              name="type-filter"
              value={filters.claim_type || 'all'}
              onChange={(value) => handleFilterChange('claim_type', value)}
              options={[
                { value: 'all', label: 'جميع الأنواع' },
                { value: 'repair', label: 'إصلاح' },
                { value: 'replacement', label: 'استبدال' },
                { value: 'refund', label: 'استرداد' }
              ]}
              placeholder="فلترة حسب النوع"
            />
          </div>
        </div>

        {/* Table */}
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            <span className="mr-3 text-gray-600 dark:text-gray-400">جاري التحميل...</span>
          </div>
        ) : filteredClaims.length === 0 ? (
          <div className="text-center py-12">
            <FiFileText className="mx-auto text-6xl text-gray-300 dark:text-gray-600 mb-4" />
            <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
              لا توجد مطالبات ضمان
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              {filters.search || filters.status !== 'all' || filters.claim_type !== 'all'
                ? 'جرب تغيير الفلاتر أو البحث'
                : 'ابدأ بإضافة مطالبة ضمان جديدة'}
            </p>
            {(!filters.search && filters.status === 'all' && filters.claim_type === 'all') && (
              <button
                onClick={() => setCreateModal({ isOpen: true })}
                className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl mx-auto"
              >
                <FiPlus className="ml-2" />
                إضافة مطالبة جديدة
              </button>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-12">
                    #
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    رقم المطالبة
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    المنتج
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    العميل
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-32">
                    نوع المطالبة
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-24">
                    الحالة
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-24">
                    التكلفة
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-32">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {paginatedClaims.map((claim, index) => (
                <tr key={claim.id} className="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-150">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                    <span className="font-medium">{startIndex + index + 1}</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {claim.claim_number}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      <FormattedDate date={claim.claim_date} />
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {claim.product_name}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {claim.warranty_number}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                    {claim.customer_name || 'غير محدد'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <ClaimTypeBadge
                        type={claim.claim_type}
                        labels={CLAIM_TYPE_LABELS}
                      />
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <StatusBadge
                        status={claim.status}
                        labels={CLAIM_STATUS_LABELS}
                      />
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                    {(claim.actual_cost || claim.estimated_cost) ? (
                      <FormattedCurrency
                        amount={Number(claim.actual_cost || claim.estimated_cost || 0)}
                        className="font-medium"
                      />
                    ) : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center justify-end gap-1">
                      <TopbarTooltip text="عرض التفاصيل" position="top">
                        <button
                          onClick={() => setDetailsModal({ isOpen: true, claim })}
                          className="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-150 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/30 border border-transparent hover:border-gray-200 dark:hover:border-gray-600"
                        >
                          <FiEye className="w-4 h-4" />
                        </button>
                      </TopbarTooltip>

                      <TopbarTooltip text="تاريخ التغييرات" position="top">
                        <button
                          onClick={async () => {
                            const history = await fetchStatusHistory(claim.id);
                            setStatusHistoryModal({
                              isOpen: true,
                              claim,
                              history
                            });
                          }}
                          className="text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-200 transition-colors duration-150 p-2 rounded-lg hover:bg-purple-50 dark:hover:bg-purple-900/20 border border-transparent hover:border-purple-200 dark:hover:border-purple-700"
                        >
                          <FiClock className="w-4 h-4" />
                        </button>
                      </TopbarTooltip>
                      
                      {claim.status === 'pending' && (
                        <>
                          <TopbarTooltip text="موافقة سريعة" position="top">
                            <button
                              onClick={() => handleQuickApprove(claim)}
                              className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200 transition-colors duration-150 p-2 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20 border border-transparent hover:border-green-200 dark:hover:border-green-700"
                            >
                              <FiCheckCircle className="w-4 h-4" />
                            </button>
                          </TopbarTooltip>
                          <TopbarTooltip text="رفض سريع" position="top">
                            <button
                              onClick={() => handleQuickReject(claim)}
                              className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 transition-colors duration-150 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 border border-transparent hover:border-red-200 dark:hover:border-red-700"
                            >
                              <FiXCircle className="w-4 h-4" />
                            </button>
                          </TopbarTooltip>
                        </>
                      )}
                      
                      {(claim.status === 'pending' || claim.status === 'approved' || claim.status === 'in_progress') && (
                        <TopbarTooltip text="معالجة متقدمة" position="top">
                          <button
                            onClick={async () => {
                              resetProcessForm();
                              setSelectedNewStatus('');
                              await fetchAvailableStatuses(claim.id);

                              // اقتراح الحالة التالية المنطقية
                              const suggestedStatus = getSuggestedNextStatus(claim.claim_type, claim.status);
                              if (suggestedStatus) {
                                setSelectedNewStatus(suggestedStatus);
                              }

                              setProcessModal({ isOpen: true, claim });
                            }}
                            className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200 transition-colors duration-150 p-2 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20 border border-transparent hover:border-green-200 dark:hover:border-green-700"
                          >
                            <FiEdit className="w-4 h-4" />
                          </button>
                        </TopbarTooltip>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        )}

        {/* Pagination */}
        {filteredClaims.length > 0 && (
          <TablePagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={filteredClaims.length}
            onPageChange={setCurrentPage}
            onItemsPerPageChange={setItemsPerPage}
          />
        )}
      </div>

      {/* Create Claim Modal */}
      <Modal
        isOpen={createModal.isOpen}
        onClose={() => {
          setCreateModal({ isOpen: false });
          resetCreateForm();
        }}
        title="إنشاء مطالبة ضمان جديدة"
      >
        <div className="space-y-4">
          {/* Warranty Selection */}
          <WarrantySelector
            selectedWarranty={selectedWarranty}
            onWarrantySelect={(warranty) => {
              setSelectedWarranty(warranty);
              setCreateForm(prev => ({ ...prev, warranty_id: warranty?.id || 0 }));
            }}
          />

          {/* Claim Type */}
          <div>
            <SelectInput
              name="claim_type"
              label="نوع المطالبة *"
              value={createForm.claim_type}
              onChange={(value) => setCreateForm(prev => ({ ...prev, claim_type: value as 'repair' | 'replacement' | 'refund' }))}
              options={[
                { value: 'repair', label: 'إصلاح' },
                { value: 'replacement', label: 'استبدال' },
                { value: 'refund', label: 'استرداد' }
              ]}
              required
            />
          </div>

          {/* Issue Description */}
          <div>
            <TextArea
              name="issue_description"
              label="وصف المشكلة *"
              value={createForm.issue_description}
              onChange={(value) => setCreateForm(prev => ({ ...prev, issue_description: value }))}
              placeholder="اشرح المشكلة بالتفصيل..."
              rows={4}
              required
            />
          </div>

          {/* Notes */}
          <div>
            <TextArea
              name="notes"
              label="ملاحظات إضافية (اختياري)"
              value={createForm.notes}
              onChange={(value) => setCreateForm(prev => ({ ...prev, notes: value }))}
              placeholder="أي ملاحظات إضافية..."
              rows={3}
            />
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={() => {
                setCreateModal({ isOpen: false });
                resetCreateForm();
              }}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              إلغاء
            </button>
            <button
              type="button"
              onClick={handleCreateClaim}
              disabled={loading || !isCreateFormValid()}
              className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'جاري الإنشاء...' : 'إنشاء'}
            </button>
          </div>
        </div>
      </Modal>

      {/* Process Claim Modal */}
      <Modal
        isOpen={processModal.isOpen}
        onClose={() => {
          setProcessModal({ isOpen: false, claim: null });
          resetProcessForm();
        }}
        title="معالجة مطالبة الضمان"
      >
        <div className="space-y-4">
          {processModal.claim && (
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                تفاصيل المطالبة
              </h4>
              <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <div>رقم المطالبة: {processModal.claim.claim_number}</div>
                <div>المنتج: {processModal.claim.product_name}</div>
                <div className="flex items-center gap-2">
                  <span>نوع المطالبة:</span>
                  <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 rounded text-xs font-medium">
                    {CLAIM_TYPE_LABELS[processModal.claim.claim_type]}
                  </span>
                </div>
                <div>الحالة الحالية: {CLAIM_STATUS_LABELS[processModal.claim.status]}</div>
                {processModal.claim.claim_type === 'refund' && (
                  <div className="mt-2 p-2 bg-green-50 dark:bg-green-900/20 rounded border border-green-200 dark:border-green-800">
                    <p className="text-xs text-green-700 dark:text-green-300">
                      💡 مطالبات الاسترداد يمكن إكمالها مباشرة بعد الموافقة
                    </p>
                  </div>
                )}
                {(processModal.claim.claim_type === 'repair' || processModal.claim.claim_type === 'replacement') && (
                  <div className="mt-2 p-2 bg-orange-50 dark:bg-orange-900/20 rounded border border-orange-200 dark:border-orange-800">
                    <p className="text-xs text-orange-700 dark:text-orange-300">
                      ⏱️ مطالبات {CLAIM_TYPE_LABELS[processModal.claim.claim_type]} تحتاج وقت للتنفيذ
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* اختيار الحالة الجديدة */}
          <div>
            <SelectInput
              name="new_status"
              label="الحالة الجديدة *"
              value={selectedNewStatus}
              onChange={setSelectedNewStatus}
              options={[
                { value: '', label: 'اختر الحالة الجديدة' },
                ...availableStatuses.map(status => {
                  const suggested = processModal.claim && getSuggestedNextStatus(processModal.claim.claim_type, processModal.claim.status);
                  const isRecommended = status.value === suggested;
                  return {
                    value: status.value,
                    label: isRecommended ? `${status.label} (مقترح)` : status.label,
                    description: status.description
                  };
                })
              ]}
              placeholder="اختر الحالة الجديدة"
            />
            {selectedNewStatus && (
              <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                {availableStatuses.find(s => s.value === selectedNewStatus)?.description && (
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    {availableStatuses.find(s => s.value === selectedNewStatus)?.description}
                  </p>
                )}
                {availableStatuses.find(s => s.value === selectedNewStatus)?.is_final && (
                  <p className="text-xs text-orange-600 dark:text-orange-400 mt-1 font-medium">
                    ⚠️ هذه حالة نهائية ولا يمكن تغييرها لاحقاً
                  </p>
                )}
              </div>
            )}
          </div>

          <div className="grid grid-cols-1 gap-4">

            <TextArea
              label="القرار/الحل"
              name="resolution"
              value={processForm.resolution}
              onChange={(value) => setProcessForm(prev => ({ ...prev, resolution: value }))}
              placeholder="أدخل تفاصيل القرار أو الحل المقترح"
              rows={3}
              required
            />

            {(processForm.status === 'approved' || processForm.status === 'completed') && (
              <NumberInput
                label="التكلفة"
                name="actual_cost"
                value={processForm.actual_cost.toString()}
                onChange={(value) => setProcessForm(prev => ({ ...prev, actual_cost: parseFloat(value) || 0 }))}
                min={0}
                placeholder="أدخل تكلفة المعالجة (اختياري)"
              />
            )}

            <TextArea
              label="ملاحظات إضافية"
              name="notes"
              value={processForm.notes}
              onChange={(value) => setProcessForm(prev => ({ ...prev, notes: value }))}
              placeholder="أدخل أي ملاحظات إضافية (اختياري)"
              rows={2}
            />
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={() => {
                setProcessModal({ isOpen: false, claim: null });
                resetProcessForm();
              }}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              إلغاء
            </button>
            <button
              type="button"
              onClick={handleProcessClaim}
              disabled={loading || !isProcessFormValid()}
              className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'جاري التطبيق...' : 'تطبيق التغيير'}
            </button>
          </div>
        </div>
      </Modal>

      {/* Details Modal */}
      <Modal
        isOpen={detailsModal.isOpen}
        onClose={() => setDetailsModal({ isOpen: false, claim: null })}
        title="تفاصيل مطالبة الضمان"
      >
        {detailsModal.claim && (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  رقم المطالبة
                </label>
                <div className="text-sm text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-700 p-2 rounded">
                  {detailsModal.claim.claim_number}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  تاريخ المطالبة
                </label>
                <div className="text-sm text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-700 p-2 rounded">
                  <FormattedDate date={detailsModal.claim.claim_date} />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  المنتج
                </label>
                <div className="text-sm text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-700 p-2 rounded">
                  {detailsModal.claim.product_name}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  العميل
                </label>
                <div className="text-sm text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-700 p-2 rounded">
                  {detailsModal.claim.customer_name || 'غير محدد'}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  نوع المطالبة
                </label>
                <div className="text-sm text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-700 p-2 rounded">
                  {CLAIM_TYPE_LABELS[detailsModal.claim.claim_type]}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  الحالة
                </label>
                <div className="text-sm text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-700 p-2 rounded">
                  {CLAIM_STATUS_LABELS[detailsModal.claim.status]}
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                وصف المشكلة
              </label>
              <div className="text-sm text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-700 p-3 rounded min-h-[60px]">
                {detailsModal.claim.issue_description}
              </div>
            </div>

            {detailsModal.claim.resolution && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  القرار/الحل
                </label>
                <div className="text-sm text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-700 p-3 rounded min-h-[60px]">
                  {detailsModal.claim.resolution}
                </div>
              </div>
            )}

            {(detailsModal.claim.actual_cost || detailsModal.claim.estimated_cost) && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  التكلفة
                </label>
                <div className="text-sm text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-700 p-2 rounded">
                  <FormattedCurrency
                    amount={Number(detailsModal.claim.actual_cost || detailsModal.claim.estimated_cost || 0)}
                    className="font-medium"
                  />
                </div>
              </div>
            )}

            {detailsModal.claim.notes && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  ملاحظات
                </label>
                <div className="text-sm text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-700 p-3 rounded min-h-[60px]">
                  {detailsModal.claim.notes}
                </div>
              </div>
            )}

            <div className="flex justify-end pt-4">
              <button
                type="button"
                onClick={() => setDetailsModal({ isOpen: false, claim: null })}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                إغلاق
              </button>
            </div>
          </div>
        )}
      </Modal>

      {/* Status History Modal */}
      <Modal
        isOpen={statusHistoryModal.isOpen}
        onClose={() => setStatusHistoryModal({ isOpen: false, claim: null, history: [] })}
        title="تاريخ تغييرات الحالة"
      >
        <div className="space-y-4">
          {statusHistoryModal.claim && (
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                مطالبة الضمان: {statusHistoryModal.claim.claim_number}
              </h4>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                المنتج: {statusHistoryModal.claim.product_name}
              </div>
            </div>
          )}

          {statusHistoryModal.history.length > 0 ? (
            <div className="space-y-3">
              {statusHistoryModal.history.map((record: any, index: number) => (
                <div
                  key={record.id}
                  className={`p-4 rounded-lg border-r-4 ${
                    record.is_recent
                      ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-400'
                      : 'bg-gray-50 dark:bg-gray-700 border-gray-300 dark:border-gray-600'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="font-medium text-gray-900 dark:text-gray-100">
                          {record.description}
                        </span>
                        {record.is_recent && (
                          <span className="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded-full">
                            جديد
                          </span>
                        )}
                      </div>

                      <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                        <div>الوقت: {record.time_since}</div>
                        {record.reason && (
                          <div>السبب: {record.reason}</div>
                        )}
                        {record.notes && (
                          <div>ملاحظات: {record.notes}</div>
                        )}
                        {record.resolution_details && (
                          <div>تفاصيل الحل: {record.resolution_details}</div>
                        )}
                        {record.actual_cost && (
                          <div>التكلفة: {record.actual_cost}</div>
                        )}
                      </div>
                    </div>

                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      #{index + 1}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <FiClock className="mx-auto text-4xl text-gray-300 dark:text-gray-600 mb-2" />
              <p className="text-gray-500 dark:text-gray-400">
                لا يوجد تاريخ تغييرات متاح
              </p>
            </div>
          )}
        </div>
      </Modal>

      {/* Success Modal */}
      <SuccessModal
        isOpen={successModal.isOpen}
        onClose={() => setSuccessModal({ isOpen: false, message: '' })}
        title="نجح العملية"
        message={successModal.message}
      />
    </div>
  );
};

export default WarrantyClaimsTab;
