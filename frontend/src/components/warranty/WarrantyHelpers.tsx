/**
 * مكونات مساعدة لنظام إدارة الضمانات
 * تحتوي على مكونات مشتركة لعرض البيانات بتنسيق موحد
 * تستخدم خدمات التاريخ والوقت الموحدة
 */

import React from 'react';
import { FiClock, FiCheckCircle, FiXCircle, FiAlertTriangle } from 'react-icons/fi';

interface DaysRemainingProps {
  endDate: string;
  status: string;
  className?: string;
}

interface StatusBadgeProps {
  status: string;
  labels: Record<string, string>;
  className?: string;
}

/**
 * حساب الأيام المتبقية للضمان
 * يستخدم التاريخ الحالي بدقة أكبر
 */
export const getDaysRemaining = (endDate: string): number => {
  const end = new Date(endDate);
  const now = new Date();

  // تعيين الوقت إلى بداية اليوم للحصول على حساب دقيق للأيام
  end.setHours(23, 59, 59, 999);
  now.setHours(0, 0, 0, 0);

  const diffTime = end.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return Math.max(0, diffDays);
};

/**
 * مكون لعرض الأيام المتبقية مع التنسيق المناسب
 */
export const DaysRemaining: React.FC<DaysRemainingProps> = ({ 
  endDate, 
  status, 
  className = '' 
}) => {
  if (status !== 'active') {
    return null;
  }

  const daysRemaining = getDaysRemaining(endDate);
  
  const getColorClass = () => {
    if (daysRemaining <= 0) {
      return 'text-red-600 dark:text-red-400';
    } else if (daysRemaining <= 7) {
      return 'text-red-600 dark:text-red-400';
    } else if (daysRemaining <= 30) {
      return 'text-yellow-600 dark:text-yellow-400';
    } else {
      return 'text-gray-500 dark:text-gray-400';
    }
  };

  const getText = () => {
    if (daysRemaining <= 0) {
      return 'منتهي';
    } else {
      return `${daysRemaining} يوم متبقي`;
    }
  };

  return (
    <div className={`text-xs flex items-center ${getColorClass()} ${className}`}>
      <FiClock className="w-3 h-3 ml-1" />
      {getText()}
    </div>
  );
};

/**
 * مكون لعرض شارة الحالة مع الألوان والأيقونات المناسبة
 */
export const StatusBadge: React.FC<StatusBadgeProps> = ({ 
  status, 
  labels, 
  className = '' 
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'active':
        return {
          color: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 border-green-200 dark:border-green-800',
          icon: <FiCheckCircle className="w-3 h-3 ml-1" />
        };
      case 'expired':
        return {
          color: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 border-red-200 dark:border-red-800',
          icon: <FiXCircle className="w-3 h-3 ml-1" />
        };
      case 'voided':
        return {
          color: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400 border-gray-200 dark:border-gray-800',
          icon: <FiXCircle className="w-3 h-3 ml-1" />
        };

      case 'pending':
        return {
          color: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 border-blue-200 dark:border-blue-800',
          icon: <FiClock className="w-3 h-3 ml-1" />
        };
      case 'approved':
        return {
          color: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 border-green-200 dark:border-green-800',
          icon: <FiCheckCircle className="w-3 h-3 ml-1" />
        };
      case 'rejected':
        return {
          color: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 border-red-200 dark:border-red-800',
          icon: <FiXCircle className="w-3 h-3 ml-1" />
        };
      case 'in_progress':
        return {
          color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400 border-yellow-200 dark:border-yellow-800',
          icon: <FiClock className="w-3 h-3 ml-1" />
        };
      case 'completed':
        return {
          color: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 border-green-200 dark:border-green-800',
          icon: <FiCheckCircle className="w-3 h-3 ml-1" />
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400 border-gray-200 dark:border-gray-800',
          icon: <FiClock className="w-3 h-3 ml-1" />
        };
    }
  };

  const config = getStatusConfig();
  const label = labels[status] || status;

  return (
    <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${config.color} ${className}`}>
      {config.icon}
      {label}
    </span>
  );
};

/**
 * مكون لعرض شارة نوع المطالبة
 */
export const ClaimTypeBadge: React.FC<{ type: string; labels: Record<string, string>; className?: string }> = ({ 
  type, 
  labels, 
  className = '' 
}) => {
  const getTypeConfig = () => {
    switch (type) {
      case 'repair':
        return {
          color: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 border-blue-200 dark:border-blue-800',
          icon: <FiClock className="w-3 h-3 ml-1" />
        };
      case 'replacement':
        return {
          color: 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400 border-orange-200 dark:border-orange-800',
          icon: <FiAlertTriangle className="w-3 h-3 ml-1" />
        };
      case 'refund':
        return {
          color: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 border-green-200 dark:border-green-800',
          icon: <FiCheckCircle className="w-3 h-3 ml-1" />
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400 border-gray-200 dark:border-gray-800',
          icon: <FiClock className="w-3 h-3 ml-1" />
        };
    }
  };

  const config = getTypeConfig();
  const label = labels[type] || type;

  return (
    <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${config.color} ${className}`}>
      {config.icon}
      {label}
    </span>
  );
};

/**
 * مكون لعرض شارة الأيام المتبقية في التقارير
 */
export const ExpiringBadge: React.FC<{ daysRemaining: number; className?: string }> = ({ 
  daysRemaining, 
  className = '' 
}) => {
  const getConfig = () => {
    if (daysRemaining <= 7) {
      return {
        color: 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 border-red-200 dark:border-red-800',
        icon: <FiAlertTriangle className="w-3 h-3 ml-1" />
      };
    } else if (daysRemaining <= 15) {
      return {
        color: 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 border-yellow-200 dark:border-yellow-800',
        icon: <FiClock className="w-3 h-3 ml-1" />
      };
    } else {
      return {
        color: 'bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 border-orange-200 dark:border-orange-800',
        icon: <FiClock className="w-3 h-3 ml-1" />
      };
    }
  };

  const config = getConfig();

  return (
    <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${config.color} ${className}`}>
      {config.icon}
      {daysRemaining} يوم
    </span>
  );
};

export default {
  DaysRemaining,
  StatusBadge,
  ClaimTypeBadge,
  ExpiringBadge,
  getDaysRemaining
};
