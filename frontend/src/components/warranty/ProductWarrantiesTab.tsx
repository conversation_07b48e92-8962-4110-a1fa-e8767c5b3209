import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FiPlus,
  FiTrash,
  FiPackage,
  FiClock,
  FiSearch,
  FiShield,
  FiAlertTriangle,
  FiFileText
} from 'react-icons/fi';
import { useProductWarrantiesStore } from '../../stores/warranty';
import { ProductWarranty, WarrantyFilters, WARRANTY_STATUS_LABELS } from '../../types/warranty';
import Modal from '../Modal';
import SuccessModal from '../SuccessModal';
import { TextInput, SelectInput } from '../inputs';
import TextArea from '../inputs/TextArea';
import DatePicker from '../DatePicker';
import ToggleSwitch from '../ToggleSwitch';
import ProductSelector from '../ProductSelector';
import WarrantyTypeSelector from '../WarrantyTypeSelector';
import CustomerSelector from '../CustomerSelector';
import { TopbarTooltip } from '../ui';
import TablePagination from '../catalog/TablePagination';
import { FormattedDate } from '../FormattedDateTime';
import { WarrantyUtils } from '../../utils/warrantyUtils';
import { DaysRemaining, StatusBadge } from './WarrantyHelpers';

interface ProductWarrantiesTabProps {
  className?: string;
}

const ProductWarrantiesTab: React.FC<ProductWarrantiesTabProps> = ({ className = '' }) => {
  const navigate = useNavigate();
  const {
    warranties,
    loading,
    showClaimedOnly,
    fetchWarranties,
    createWarranty,
    extendWarranty,
    voidWarranty,
    deleteWarranty,
    setShowClaimedOnly
  } = useProductWarrantiesStore();

  // State
  const [filters, setFilters] = useState<WarrantyFilters>({
    search: '',
    status: 'all'
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Modal states
  const [createModal, setCreateModal] = useState({
    isOpen: false
  });

  const [extendModal, setExtendModal] = useState({
    isOpen: false,
    warranty: null as ProductWarranty | null
  });

  const [voidModal, setVoidModal] = useState({
    isOpen: false,
    warranty: null as ProductWarranty | null
  });

  const [deleteModal, setDeleteModal] = useState({
    isOpen: false,
    warranty: null as ProductWarranty | null
  });

  const [deleteConfirmation, setDeleteConfirmation] = useState('');

  const [successModal, setSuccessModal] = useState({
    isOpen: false,
    message: ''
  });

  // Form states
  const [createForm, setCreateForm] = useState({
    product_id: 0,
    warranty_type_id: 0,
    purchase_date: '',
    start_date: '',
    end_date: '',
    customer_id: 0,
    notes: ''
  });

  // Selected objects for form
  const [selectedProduct, setSelectedProduct] = useState<any>(null);
  const [selectedWarrantyType, setSelectedWarrantyType] = useState<any>(null);
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);

  // Auto-calculation state
  const [autoCalculateDates, setAutoCalculateDates] = useState(true);

  const [extendForm, setExtendForm] = useState({
    additional_months: 6,
    reason: ''
  });

  const [voidForm, setVoidForm] = useState({
    reason: ''
  });

  // Load data on mount
  useEffect(() => {
    fetchWarranties(filters);
  }, [fetchWarranties]);

  // Filter and paginate data
  const filteredWarranties = useMemo(() => {
    return warranties.filter(warranty => {
      const matchesSearch = !filters.search ||
        warranty.warranty_number.toLowerCase().includes(filters.search.toLowerCase()) ||
        warranty.product_name.toLowerCase().includes(filters.search.toLowerCase()) ||
        (warranty.customer_name && warranty.customer_name.toLowerCase().includes(filters.search.toLowerCase()));

      const matchesStatus = filters.status === 'all' || warranty.status === filters.status;

      // فلتر المطالب به - إذا كان مفتاح التبديل مفعل، عرض المطالب به فقط
      const matchesClaimed = !showClaimedOnly || warranty.has_claims;

      return matchesSearch && matchesStatus && matchesClaimed;
    });
  }, [warranties, filters, showClaimedOnly]);

  const totalPages = Math.ceil(filteredWarranties.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedWarranties = filteredWarranties.slice(startIndex, startIndex + itemsPerPage);

  // Handle filter changes
  const handleFilterChange = (key: keyof WarrantyFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1);
  };

  // Reset forms
  const resetCreateForm = () => {
    setCreateForm({
      product_id: 0,
      warranty_type_id: 0,
      purchase_date: '',
      start_date: '',
      end_date: '',
      customer_id: 0,
      notes: ''
    });
    setSelectedProduct(null);
    setSelectedWarrantyType(null);
    setSelectedCustomer(null);
    setAutoCalculateDates(true); // إعادة تفعيل الحساب التلقائي
  };

  const resetExtendForm = () => {
    setExtendForm({
      additional_months: 6,
      reason: ''
    });
  };

  const resetVoidForm = () => {
    setVoidForm({
      reason: ''
    });
  };

  // Auto-calculate warranty dates when purchase date or warranty type changes
  const calculateWarrantyDates = () => {
    if (!autoCalculateDates || !createForm.purchase_date || !selectedWarrantyType) {
      return;
    }

    try {
      console.log('🔄 حساب تواريخ الضمان تلقائياً...', {
        purchaseDate: createForm.purchase_date,
        warrantyType: selectedWarrantyType
      });

      const { startDate, endDate } = WarrantyUtils.calculateWarrantyDates(
        createForm.purchase_date,
        selectedWarrantyType
      );

      if (startDate && endDate) {
        setCreateForm(prev => ({
          ...prev,
          start_date: startDate,
          end_date: endDate
        }));

        console.log('✅ تم حساب تواريخ الضمان:', { startDate, endDate });
      }
    } catch (error) {
      console.error('❌ خطأ في حساب تواريخ الضمان:', error);
    }
  };

  // Effect to auto-calculate dates when dependencies change
  useEffect(() => {
    calculateWarrantyDates();
  }, [createForm.purchase_date, selectedWarrantyType, autoCalculateDates]);

  // Validate create form
  const validateCreateForm = () => {
    const errors: string[] = [];

    if (!selectedProduct) {
      errors.push('يجب تحديد المنتج');
    }

    if (!selectedWarrantyType) {
      errors.push('يجب تحديد نوع الضمان');
    }

    if (!createForm.purchase_date) {
      errors.push('يجب تحديد تاريخ الشراء');
    }

    if (!createForm.start_date) {
      errors.push('يجب تحديد تاريخ بداية الضمان');
    }

    if (!createForm.end_date) {
      errors.push('يجب تحديد تاريخ انتهاء الضمان');
    }

    // Check date logic
    if (createForm.start_date && createForm.end_date) {
      const startDate = new Date(createForm.start_date);
      const endDate = new Date(createForm.end_date);

      if (startDate >= endDate) {
        errors.push('تاريخ انتهاء الضمان يجب أن يكون بعد تاريخ البداية');
      }
    }

    if (createForm.purchase_date && createForm.start_date) {
      const purchaseDate = new Date(createForm.purchase_date);
      const startDate = new Date(createForm.start_date);

      if (purchaseDate > startDate) {
        errors.push('تاريخ بداية الضمان يجب أن يكون بعد أو يساوي تاريخ الشراء');
      }
    }

    return errors;
  };

  // Check if create form is valid (for real-time validation)
  const isCreateFormValid = () => {
    return selectedProduct &&
           selectedWarrantyType &&
           createForm.purchase_date.trim().length > 0 &&
           createForm.start_date.trim().length > 0 &&
           createForm.end_date.trim().length > 0;
  };

  // Check if extend form is valid (for real-time validation)
  const isExtendFormValid = () => {
    return extendForm.additional_months > 0 &&
           extendForm.reason.trim().length >= 3;
  };

  // Check if void form is valid (for real-time validation)
  const isVoidFormValid = () => {
    return voidForm.reason.trim().length >= 3;
  };

  // Check if delete form is valid (for real-time validation)
  const isDeleteFormValid = () => {
    return deleteConfirmation.trim().toLowerCase() === 'حذف';
  };

  // Handle create warranty
  const handleCreateWarranty = async () => {
    try {
      // Validate form
      const validationErrors = validateCreateForm();
      if (validationErrors.length > 0) {
        console.error('❌ أخطاء في النموذج:', validationErrors);
        alert('يرجى تصحيح الأخطاء التالية:\n' + validationErrors.join('\n'));
        return;
      }

      // Prepare warranty data from selected objects
      const warrantyData = {
        product_id: selectedProduct?.id || 0,
        warranty_type_id: selectedWarrantyType?.id || 0,
        purchase_date: createForm.purchase_date,
        start_date: createForm.start_date,
        end_date: createForm.end_date,
        customer_id: selectedCustomer?.id || null,
        notes: createForm.notes || null
      };

      console.log('🔄 إنشاء ضمان جديد:', warrantyData);
      await createWarranty(warrantyData);

      setCreateModal({ isOpen: false });
      resetCreateForm();
      setSuccessModal({
        isOpen: true,
        message: 'تم إنشاء الضمان بنجاح'
      });

      console.log('✅ تم إنشاء الضمان بنجاح');
    } catch (error: any) {
      console.error('❌ خطأ في إنشاء الضمان:', error);

      let errorMessage = 'فشل في إنشاء الضمان';

      if (error?.response?.data?.detail) {
        if (Array.isArray(error.response.data.detail)) {
          // Handle validation errors from FastAPI
          const validationErrors = error.response.data.detail.map((err: any) => {
            const field = err.loc?.[err.loc.length - 1] || 'حقل غير معروف';
            const fieldNames: { [key: string]: string } = {
              'product_id': 'المنتج',
              'warranty_type_id': 'نوع الضمان',
              'purchase_date': 'تاريخ الشراء',
              'start_date': 'تاريخ بداية الضمان',
              'end_date': 'تاريخ انتهاء الضمان',
              'customer_id': 'العميل'
            };

            const arabicField = fieldNames[field] || field;
            return `${arabicField}: ${err.msg}`;
          });

          errorMessage = 'أخطاء في البيانات:\n' + validationErrors.join('\n');
        } else {
          errorMessage = error.response.data.detail;
        }
      } else if (error?.message) {
        errorMessage = error.message;
      }

      alert(errorMessage);
    }
  };

  // Handle extend warranty
  const handleExtendWarranty = async () => {
    if (!extendModal.warranty) return;
    
    try {
      await extendWarranty(extendModal.warranty.id, extendForm);
      setExtendModal({ isOpen: false, warranty: null });
      resetExtendForm();
      setSuccessModal({
        isOpen: true,
        message: 'تم تمديد الضمان بنجاح'
      });
    } catch (error) {
      console.error('Error extending warranty:', error);
    }
  };

  // Handle void warranty
  const handleVoidWarranty = async () => {
    if (!voidModal.warranty) return;

    // التحقق من صحة البيانات
    if (!voidForm.reason || !voidForm.reason.trim()) {
      setSuccessModal({
        isOpen: true,
        message: 'يرجى إدخال سبب إلغاء الضمان'
      });
      return;
    }

    if (voidForm.reason.trim().length < 3) {
      setSuccessModal({
        isOpen: true,
        message: 'سبب الإلغاء يجب أن يكون 3 أحرف على الأقل'
      });
      return;
    }

    try {
      await voidWarranty(voidModal.warranty.id, voidForm);
      setVoidModal({ isOpen: false, warranty: null });
      resetVoidForm();

      // رسالة نجاح بسيطة (معطل مؤقتاً معلومات المطالبات)
      let successMessage = 'تم إلغاء الضمان بنجاح';

      // التحقق من وجود معلومات المطالبات في الاستجابة (معطل مؤقتاً)
      // if (result && typeof result === 'object' && 'claims_info' in result) {
      //   const claimsInfo = (result as any).claims_info;
      //   if (claimsInfo && claimsInfo.total_claims > 0) {
      //     successMessage += `\n\nتم التعامل مع ${claimsInfo.total_claims} مطالبة مرتبطة`;
      //     if (claimsInfo.processed_claims > 0) {
      //       successMessage += `\n- تم معالجة ${claimsInfo.processed_claims} مطالبة`;
      //       if (claimsInfo.rejected_claims > 0) {
      //         successMessage += `\n- تم رفض ${claimsInfo.rejected_claims} مطالبة`;
      //       }
      //       if (claimsInfo.completed_claims > 0) {
      //         successMessage += `\n- تم إكمال ${claimsInfo.completed_claims} مطالبة`;
      //       }
      //     }
      //   }
      // }

      setSuccessModal({
        isOpen: true,
        message: successMessage
      });

      // تحديث قائمة الضمانات
      await fetchWarranties();
    } catch (error: any) {
      console.error('Error voiding warranty:', error);

      let errorMessage = 'فشل في إلغاء الضمان';

      // التحقق من نوع الخطأ
      if (error.response) {
        // خطأ من الخادم
        const status = error.response.status;
        const detail = error.response.data?.detail;

        if (status === 400 && detail) {
          // خطأ في البيانات
          errorMessage = detail;
        } else if (status === 500) {
          // خطأ في الخادم
          errorMessage = 'حدث خطأ في الخادم. يرجى المحاولة مرة أخرى.';
        } else if (detail) {
          if (Array.isArray(detail)) {
            // Handle validation errors from FastAPI
            const validationErrors = detail.map((err: any) => {
              const field = err.loc?.[err.loc.length - 1] || 'حقل غير معروف';
              const fieldNames: { [key: string]: string } = {
                'reason': 'سبب الإلغاء'
              };

              const arabicField = fieldNames[field] || field;
              return `${arabicField}: ${err.msg}`;
            });

            errorMessage = 'أخطاء في البيانات:\n' + validationErrors.join('\n');
          } else {
            errorMessage = detail;
          }
        }
      } else if (error.message) {
        // خطأ في الشبكة أو محلي
        errorMessage = error.message;
      }

      setSuccessModal({
        isOpen: true,
        message: `خطأ: ${errorMessage}`
      });
    }
  };

  // Handle delete warranty
  const handleDeleteWarranty = async () => {
    if (!deleteModal.warranty) return;

    // التحقق من كلمة التأكيد
    if (deleteConfirmation.trim().toLowerCase() !== 'حذف') {
      setSuccessModal({
        isOpen: true,
        message: 'يجب كتابة كلمة "حذف" للتأكيد'
      });
      return;
    }

    try {
      console.log('🔄 حذف الضمان:', deleteModal.warranty.id);

      await deleteWarranty(deleteModal.warranty.id);

      setDeleteModal({ isOpen: false, warranty: null });
      setDeleteConfirmation(''); // إعادة تعيين كلمة التأكيد
      setSuccessModal({
        isOpen: true,
        message: 'تم حذف الضمان وجميع البيانات المرتبطة به بنجاح'
      });

      console.log('✅ تم حذف الضمان بنجاح');
    } catch (error: any) {
      console.error('❌ خطأ في حذف الضمان:', error);

      let errorMessage = 'فشل في حذف الضمان';
      if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.message) {
        errorMessage = error.message;
      }

      setSuccessModal({
        isOpen: true,
        message: `خطأ: ${errorMessage}`
      });
    }
  };

  // Reset delete form
  const resetDeleteForm = () => {
    setDeleteConfirmation('');
  };

  // Navigate to warranty claims tab with warranty filter
  const handleViewClaims = (warranty: ProductWarranty) => {
    // Navigate to warranty claims tab with warranty number filter
    navigate('/warranty-claims', {
      state: {
        warrantyFilter: warranty.warranty_number,
        warrantyId: warranty.id
      }
    });
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Data Table with Header and Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 overflow-hidden">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center p-6 gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <div className="min-w-0 flex-1">
                <h2 className="text-lg sm:text-xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FiPackage className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                  <span className="truncate">ضمانات المنتجات</span>
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  إدارة ومتابعة ضمانات المنتجات
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 flex-shrink-0">
              <button
                onClick={() => {
                  resetCreateForm();
                  setCreateModal({ isOpen: true });
                }}
                className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl min-w-[140px]"
              >
                <FiPlus className="ml-2" />
                إضافة ضمان
              </button>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-gray-50 dark:bg-gray-700/50 border-b border-gray-200 dark:border-gray-600 p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <TextInput
              name="search"
              value={filters.search || ''}
              onChange={(value) => handleFilterChange('search', value)}
              placeholder="البحث في الضمانات..."
              icon={<FiSearch />}
              className=""
            />

            <SelectInput
              label=""
              name="status-filter"
              value={filters.status || 'all'}
              onChange={(value) => handleFilterChange('status', value)}
              options={[
                { value: 'all', label: 'جميع الحالات' },
                { value: 'active', label: 'نشط' },
                { value: 'expired', label: 'منتهي' },
                { value: 'voided', label: 'ملغي' }
              ]}
              placeholder="فلترة حسب الحالة"
            />

            {/* مفتاح التبديل للمطالب به */}
            <div className="bg-white dark:bg-gray-700 px-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 flex items-center transition-all duration-200 ease-in-out hover:border-gray-400 dark:hover:border-gray-500 h-10">
              <ToggleSwitch
                id="show-claimed-only"
                checked={showClaimedOnly}
                onChange={(checked) => setShowClaimedOnly(checked)}
                label="عرض المطالب به فقط"
                className="w-full"
              />
            </div>
          </div>
        </div>

        {/* Table */}
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            <span className="mr-3 text-gray-600 dark:text-gray-400">جاري التحميل...</span>
          </div>
        ) : filteredWarranties.length === 0 ? (
          <div className="text-center py-12">
            <FiPackage className="mx-auto text-6xl text-gray-300 dark:text-gray-600 mb-4" />
            <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
              لا توجد ضمانات منتجات
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              {filters.search || filters.status !== 'all' || filters.warranty_type_id || filters.customer_id
                ? 'جرب تغيير الفلاتر أو البحث'
                : 'ابدأ بإضافة ضمان جديد لمنتجاتك'}
            </p>
            {(!filters.search && filters.status === 'all' && !filters.warranty_type_id && !filters.customer_id) && (
              <button
                onClick={() => setCreateModal({ isOpen: true })}
                className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl mx-auto"
              >
                <FiPlus className="ml-2" />
                إضافة ضمان جديد
              </button>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-12">
                    #
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    رقم الضمان
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    المنتج
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    العميل
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-32">
                    تاريخ الانتهاء
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-24">
                    الحالة
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-32">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {paginatedWarranties.map((warranty, index) => (
                  <tr key={warranty.id} className="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-150">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                      <span className="font-medium">{startIndex + index + 1}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        <div className="flex items-center gap-2">
                          <span>{warranty.warranty_number}</span>
                          {warranty.has_claims && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 border border-blue-200 dark:border-blue-800">
                              <FiFileText className="w-3 h-3 ml-1" />
                              مطالب به
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {warranty.warranty_type_name}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {warranty.product_name}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {warranty.product_barcode}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                      {warranty.customer_name || 'غير محدد'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-gray-100">
                        <FormattedDate date={warranty.end_date} />
                      </div>
                      <DaysRemaining
                        endDate={warranty.end_date}
                        status={warranty.status}
                        className="mt-1"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <StatusBadge
                          status={warranty.status}
                          labels={WARRANTY_STATUS_LABELS}
                        />
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center justify-end gap-1">
                        {/* أيقونة المطالبات - تظهر فقط للضمانات التي لها مطالبات */}
                        {warranty.has_claims && (
                          <TopbarTooltip text="عرض المطالبات" position="top">
                            <button
                              onClick={() => handleViewClaims(warranty)}
                              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 transition-colors duration-150 p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 border border-transparent hover:border-blue-200 dark:hover:border-blue-700"
                            >
                              <FiFileText className="w-4 h-4" />
                            </button>
                          </TopbarTooltip>
                        )}

                        {warranty.status === 'active' && (
                          <>
                            <TopbarTooltip text="تمديد الضمان" position="top">
                              <button
                                onClick={() => {
                                  resetExtendForm();
                                  setExtendModal({ isOpen: true, warranty });
                                }}
                                className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200 transition-colors duration-150 p-2 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20 border border-transparent hover:border-green-200 dark:hover:border-green-700"
                              >
                                <FiClock className="w-4 h-4" />
                              </button>
                            </TopbarTooltip>
                            <TopbarTooltip text="إلغاء الضمان" position="top">
                              <button
                                onClick={() => {
                                  resetVoidForm();
                                  setVoidModal({ isOpen: true, warranty });
                                }}
                                className="text-orange-600 dark:text-orange-400 hover:text-orange-800 dark:hover:text-orange-200 transition-colors duration-150 p-2 rounded-lg hover:bg-orange-50 dark:hover:bg-orange-900/20 border border-transparent hover:border-orange-200 dark:hover:border-orange-700"
                              >
                                <FiClock className="w-4 h-4" />
                              </button>
                            </TopbarTooltip>
                          </>
                        )}

                        {/* زر الحذف متاح لجميع الضمانات */}
                        <TopbarTooltip text="حذف الضمان" position="top">
                          <button
                            onClick={() => {
                              resetDeleteForm();
                              setDeleteModal({ isOpen: true, warranty });
                            }}
                            className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 transition-colors duration-150 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 border border-transparent hover:border-red-200 dark:hover:border-red-700"
                          >
                            <FiTrash className="w-4 h-4" />
                          </button>
                        </TopbarTooltip>
                      </div>
                    </td>
                  </tr>
                ))}
            </tbody>
          </table>
        </div>
        )}

        {/* Pagination */}
        {filteredWarranties.length > 0 && (
          <TablePagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={filteredWarranties.length}
            onPageChange={setCurrentPage}
            onItemsPerPageChange={setItemsPerPage}
          />
        )}
      </div>

      {/* Create Warranty Modal */}
      <Modal
        isOpen={createModal.isOpen}
        onClose={() => {
          setCreateModal({ isOpen: false });
          resetCreateForm();
        }}
        title="إنشاء ضمان جديد"
      >
        <div className="space-y-4">
          {/* Product Selection */}
          <ProductSelector
            selectedProduct={selectedProduct}
            onProductSelect={(product) => {
              setSelectedProduct(product);
              setCreateForm(prev => ({ ...prev, product_id: product?.id || 0 }));
            }}
          />

          {/* Warranty Type Selection */}
          <WarrantyTypeSelector
            selectedWarrantyType={selectedWarrantyType}
            onWarrantyTypeSelect={(warrantyType) => {
              setSelectedWarrantyType(warrantyType);
              setCreateForm(prev => ({ ...prev, warranty_type_id: warrantyType?.id || 0 }));
            }}
          />

          {/* Purchase Date */}
          <div>
            <DatePicker
              name="purchase_date"
              label="تاريخ الشراء *"
              value={createForm.purchase_date}
              onChange={(value: string) => setCreateForm(prev => ({ ...prev, purchase_date: value }))}
              placeholder="اختر تاريخ الشراء"
            />
          </div>

          {/* Auto-calculate toggle */}
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-xl border border-blue-200 dark:border-blue-800">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-1">
                  الحساب التلقائي للتواريخ
                </h4>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  حساب تاريخ البداية والانتهاء تلقائياً من تاريخ الشراء ومدة الضمان
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={autoCalculateDates}
                  onChange={(e) => setAutoCalculateDates(e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>

          {/* Start Date */}
          <div>
            <DatePicker
              name="start_date"
              label={`تاريخ بداية الضمان * ${autoCalculateDates ? '(محسوب تلقائياً)' : ''}`}
              value={createForm.start_date}
              onChange={(value: string) => {
                if (!autoCalculateDates) {
                  setCreateForm(prev => ({ ...prev, start_date: value }));
                }
              }}
              placeholder="اختر تاريخ بداية الضمان"
            />
            {autoCalculateDates && createForm.start_date && (
              <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                ✓ تم حساب التاريخ تلقائياً من تاريخ الشراء
              </p>
            )}
          </div>

          {/* End Date */}
          <div>
            <DatePicker
              name="end_date"
              label={`تاريخ انتهاء الضمان * ${autoCalculateDates ? '(محسوب تلقائياً)' : ''}`}
              value={createForm.end_date}
              onChange={(value: string) => {
                if (!autoCalculateDates) {
                  setCreateForm(prev => ({ ...prev, end_date: value }));
                }
              }}
              placeholder="اختر تاريخ انتهاء الضمان"
            />
            {autoCalculateDates && createForm.end_date && selectedWarrantyType && (
              <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                ✓ تم حساب التاريخ تلقائياً (مدة الضمان: {WarrantyUtils.formatWarrantyDuration(selectedWarrantyType.duration_months)})
              </p>
            )}
          </div>

          {/* Customer Selection */}
          <CustomerSelector
            selectedCustomer={selectedCustomer}
            onCustomerSelect={(customer) => {
              setSelectedCustomer(customer);
              setCreateForm(prev => ({ ...prev, customer_id: customer?.id || 0 }));
            }}
          />

          {/* Notes */}
          <div>
            <TextArea
              name="notes"
              label="ملاحظات (اختياري)"
              value={createForm.notes}
              onChange={(value) => setCreateForm(prev => ({ ...prev, notes: value }))}
              placeholder="أدخل أي ملاحظات إضافية"
              rows={3}
            />
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={() => {
                setCreateModal({ isOpen: false });
                resetCreateForm();
              }}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              إلغاء
            </button>
            <button
              type="button"
              onClick={handleCreateWarranty}
              disabled={loading || !isCreateFormValid()}
              className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'جاري الإنشاء...' : 'إنشاء'}
            </button>
          </div>
        </div>
      </Modal>

      {/* Extend Warranty Modal */}
      <Modal
        isOpen={extendModal.isOpen}
        onClose={() => {
          setExtendModal({ isOpen: false, warranty: null });
          resetExtendForm();
        }}
        title="تمديد الضمان"
      >
        <div className="space-y-4">
          {extendModal.warranty && (
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                تفاصيل الضمان
              </h4>
              <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <div>رقم الضمان: {extendModal.warranty.warranty_number}</div>
                <div>المنتج: {extendModal.warranty.product_name}</div>
                <div>تاريخ الانتهاء الحالي: {new Date(extendModal.warranty.end_date).toLocaleDateString('ar-SA')}</div>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 gap-4">
            <SelectInput
              label="فترة التمديد"
              name="additional_months"
              value={extendForm.additional_months.toString()}
              onChange={(value) => setExtendForm(prev => ({ ...prev, additional_months: parseInt(value) }))}
              options={[
                { value: '3', label: '3 أشهر' },
                { value: '6', label: '6 أشهر' },
                { value: '12', label: '12 شهر' },
                { value: '24', label: '24 شهر' }
              ]}
              required
            />

            <TextArea
              label="سبب التمديد"
              name="reason"
              value={extendForm.reason}
              onChange={(value) => setExtendForm(prev => ({ ...prev, reason: value }))}
              placeholder="أدخل سبب تمديد الضمان"
              rows={3}
              required
            />
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={() => {
                setExtendModal({ isOpen: false, warranty: null });
                resetExtendForm();
              }}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              إلغاء
            </button>
            <button
              type="button"
              onClick={handleExtendWarranty}
              disabled={loading || !isExtendFormValid()}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'جاري التمديد...' : 'تمديد'}
            </button>
          </div>
        </div>
      </Modal>

      {/* Void Warranty Modal */}
      <Modal
        isOpen={voidModal.isOpen}
        onClose={() => {
          setVoidModal({ isOpen: false, warranty: null });
          resetVoidForm();
        }}
        title="إلغاء الضمان"
      >
        <div className="space-y-4">
          {voidModal.warranty && (
            <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
              <h4 className="font-medium text-red-900 dark:text-red-100 mb-2">
                تحذير: إلغاء الضمان
              </h4>
              <div className="text-sm text-red-700 dark:text-red-300 space-y-1">
                <div>رقم الضمان: {voidModal.warranty.warranty_number}</div>
                <div>المنتج: {voidModal.warranty.product_name}</div>
                <div className="mt-2 font-medium">
                  سيتم إلغاء هذا الضمان نهائياً ولن يمكن استخدامه مرة أخرى.
                </div>
              </div>
            </div>
          )}

          <TextArea
            label="سبب الإلغاء"
            name="reason"
            value={voidForm.reason}
            onChange={(value) => setVoidForm(prev => ({ ...prev, reason: value }))}
            placeholder="أدخل سبب إلغاء الضمان"
            rows={3}
            required
          />

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={() => {
                setVoidModal({ isOpen: false, warranty: null });
                resetVoidForm();
              }}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              إلغاء
            </button>
            <button
              type="button"
              onClick={handleVoidWarranty}
              disabled={loading || !isVoidFormValid()}
              className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'جاري الإلغاء...' : 'إلغاء الضمان'}
            </button>
          </div>
        </div>
      </Modal>

      {/* Delete Warranty Modal */}
      <Modal
        isOpen={deleteModal.isOpen}
        onClose={() => {
          setDeleteModal({ isOpen: false, warranty: null });
          resetDeleteForm();
        }}
        title="حذف الضمان نهائياً"
      >
        <div className="space-y-6">
          {deleteModal.warranty && (
            <>
              {/* معلومات الضمان */}
              <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
                <h4 className="font-medium text-red-900 dark:text-red-100 mb-3 flex items-center">
                  <FiShield className="w-5 h-5 ml-2" />
                  تحذير: حذف نهائي للضمان
                </h4>
                <div className="text-sm text-red-700 dark:text-red-300 space-y-2">
                  <div className="grid grid-cols-2 gap-4">
                    <div><strong>رقم الضمان:</strong> {deleteModal.warranty.warranty_number}</div>
                    <div><strong>الحالة:</strong> {WARRANTY_STATUS_LABELS[deleteModal.warranty.status]}</div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div><strong>المنتج:</strong> {deleteModal.warranty.product_name}</div>
                    <div><strong>نوع الضمان:</strong> {deleteModal.warranty.warranty_type_name}</div>
                  </div>
                  {deleteModal.warranty.customer_name && (
                    <div><strong>العميل:</strong> {deleteModal.warranty.customer_name}</div>
                  )}
                </div>
              </div>

              {/* تحذير المخاطر */}
              <div className="bg-red-100 dark:bg-red-800/30 p-4 rounded-lg border-2 border-red-300 dark:border-red-600">
                <h5 className="font-bold text-red-900 dark:text-red-100 mb-2 flex items-center">
                  <FiAlertTriangle className="w-5 h-5 ml-2" />
                  سيتم حذف البيانات التالية نهائياً:
                </h5>
                <ul className="text-sm text-red-800 dark:text-red-200 space-y-1 list-disc list-inside">
                  <li>بيانات الضمان الأساسية</li>
                  <li>جميع مطالبات الضمان المرتبطة</li>
                  <li>سجلات التدقيق والتتبع</li>
                  <li>أي ملفات أو مرفقات مرتبطة</li>
                </ul>
                <p className="text-sm font-medium text-red-900 dark:text-red-100 mt-3 flex items-center">
                  <FiAlertTriangle className="w-4 h-4 ml-1" />
                  هذا الإجراء لا يمكن التراجع عنه نهائياً!
                </p>
              </div>

              {/* حقل التأكيد */}
              <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  للمتابعة، اكتب كلمة <span className="font-bold text-red-600">"حذف"</span> في الحقل أدناه:
                </label>
                <TextInput
                  name="delete_confirmation"
                  value={deleteConfirmation}
                  onChange={(value) => setDeleteConfirmation(value)}
                  placeholder="اكتب: حذف"
                  className="text-center font-bold"
                />
                {deleteConfirmation && deleteConfirmation.trim().toLowerCase() !== 'حذف' && (
                  <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                    يجب كتابة كلمة "حذف" بالضبط
                  </p>
                )}
              </div>
            </>
          )}

          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-600">
            <button
              type="button"
              onClick={() => {
                setDeleteModal({ isOpen: false, warranty: null });
                resetDeleteForm();
              }}
              className="px-6 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors"
            >
              إلغاء
            </button>
            <button
              type="button"
              onClick={handleDeleteWarranty}
              disabled={loading || !isDeleteFormValid()}
              className="px-6 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? (
                <div className="flex items-center">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2"></div>
                  جاري الحذف...
                </div>
              ) : (
                'حذف نهائياً'
              )}
            </button>
          </div>
        </div>
      </Modal>

      {/* Success Modal */}
      <SuccessModal
        isOpen={successModal.isOpen}
        onClose={() => setSuccessModal({ isOpen: false, message: '' })}
        title="نجح العملية"
        message={successModal.message}
      />
    </div>
  );
};

export default ProductWarrantiesTab;
