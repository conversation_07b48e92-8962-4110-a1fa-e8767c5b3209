import React, { useState, useEffect, useMemo } from 'react';
import {
  FiPlus,
  FiEdit,
  FiTrash,
  FiSettings,
  FiSearch,
  FiShield
} from 'react-icons/fi';
import { useWarrantyTypesStore } from '../../stores/warranty';
import { WarrantyType, WarrantyTypeFilters, COVERAGE_TYPE_LABELS } from '../../types/warranty';
import Modal from '../Modal';
import DeleteConfirmModal from '../DeleteConfirmModal';
import SuccessModal from '../SuccessModal';
import { TextInput, TextArea, SelectInput, NumberInput } from '../inputs';
import ToggleSwitch from '../ToggleSwitch';
import { TopbarTooltip } from '../ui';
import TablePagination from '../catalog/TablePagination';

interface WarrantyTypesTabProps {
  className?: string;
}

const WarrantyTypesTab: React.FC<WarrantyTypesTabProps> = ({ className = '' }) => {
  const {
    warrantyTypes,
    loading,
    fetchWarrantyTypes,
    createWarrantyType,
    updateWarrantyType,
    deleteWarrantyType
  } = useWarrantyTypesStore();

  // State
  const [filters, setFilters] = useState<WarrantyTypeFilters>({
    search: '',
    status: 'all',
    coverage_type: 'all'
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Modal states
  const [typeModal, setTypeModal] = useState({
    isOpen: false,
    mode: 'create' as 'create' | 'edit',
    type: null as WarrantyType | null
  });

  const [deleteModal, setDeleteModal] = useState({
    isOpen: false,
    id: 0,
    name: '',
    isLoading: false
  });

  const [successModal, setSuccessModal] = useState({
    isOpen: false,
    message: ''
  });

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    name_ar: '',
    description: '',
    duration_months: 12,
    coverage_type: 'full' as 'full' | 'partial' | 'limited',
    terms_conditions: '',
    is_active: true
  });

  // Load data on mount
  useEffect(() => {
    fetchWarrantyTypes(filters);
  }, [fetchWarrantyTypes]);

  // Filter and paginate data
  const filteredTypes = useMemo(() => {
    return warrantyTypes.filter(type => {
      const matchesSearch = !filters.search || 
        type.name.toLowerCase().includes(filters.search.toLowerCase()) ||
        type.name_ar.toLowerCase().includes(filters.search.toLowerCase()) ||
        (type.description && type.description.toLowerCase().includes(filters.search.toLowerCase()));

      const matchesStatus = filters.status === 'all' || 
        (filters.status === 'active' && type.is_active) ||
        (filters.status === 'inactive' && !type.is_active);

      const matchesCoverage = filters.coverage_type === 'all' || 
        type.coverage_type === filters.coverage_type;

      return matchesSearch && matchesStatus && matchesCoverage;
    });
  }, [warrantyTypes, filters]);

  const totalPages = Math.ceil(filteredTypes.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedTypes = filteredTypes.slice(startIndex, startIndex + itemsPerPage);

  // Handle filter changes
  const handleFilterChange = (key: keyof WarrantyTypeFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1); // Reset to first page
  };

  // Handle form changes
  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      name_ar: '',
      description: '',
      duration_months: 12,
      coverage_type: 'full',
      terms_conditions: '',
      is_active: true
    });
  };

  // Check if form is valid (for real-time validation)
  const isFormValid = () => {
    return formData.name.trim().length > 0 &&
           formData.name_ar.trim().length > 0 &&
           formData.duration_months > 0;
  };

  // Handle create/edit
  const handleSave = async () => {
    try {
      if (typeModal.mode === 'create') {
        await createWarrantyType(formData);
        setSuccessModal({
          isOpen: true,
          message: 'تم إنشاء نوع الضمان بنجاح'
        });
      } else if (typeModal.type) {
        await updateWarrantyType(typeModal.type.id, formData);
        setSuccessModal({
          isOpen: true,
          message: 'تم تحديث نوع الضمان بنجاح'
        });
      }
      
      setTypeModal({ isOpen: false, mode: 'create', type: null });
      resetForm();
    } catch (error) {
      console.error('Error saving warranty type:', error);
    }
  };

  // Handle edit
  const handleEdit = (type: WarrantyType) => {
    setFormData({
      name: type.name,
      name_ar: type.name_ar,
      description: type.description || '',
      duration_months: type.duration_months,
      coverage_type: type.coverage_type,
      terms_conditions: type.terms_conditions || '',
      is_active: type.is_active
    });
    setTypeModal({ isOpen: true, mode: 'edit', type });
  };

  // Handle delete
  const handleDelete = async () => {
    try {
      setDeleteModal(prev => ({ ...prev, isLoading: true }));
      await deleteWarrantyType(deleteModal.id);
      setDeleteModal({ isOpen: false, id: 0, name: '', isLoading: false });
      setSuccessModal({
        isOpen: true,
        message: 'تم حذف نوع الضمان بنجاح'
      });
    } catch (error) {
      setDeleteModal(prev => ({ ...prev, isLoading: false }));
      console.error('Error deleting warranty type:', error);
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Data Table with Header and Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 overflow-hidden">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center p-6 gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <div className="min-w-0 flex-1">
                <h2 className="text-lg sm:text-xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FiSettings className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                  <span className="truncate">إدارة أنواع الضمانات</span>
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  إدارة وتكوين أنواع الضمانات المختلفة
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 flex-shrink-0">
              <button
                onClick={() => {
                  resetForm();
                  setTypeModal({ isOpen: true, mode: 'create', type: null });
                }}
                className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl min-w-[140px]"
              >
                <FiPlus className="ml-2" />
                إضافة نوع ضمان
              </button>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-gray-50 dark:bg-gray-700/50 border-b border-gray-200 dark:border-gray-600 p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <TextInput
              name="search"
              value={filters.search || ''}
              onChange={(value) => handleFilterChange('search', value)}
              placeholder="البحث في أنواع الضمانات..."
              icon={<FiSearch />}
              className=""
            />

            <SelectInput
              label=""
              name="status-filter"
              value={filters.status || 'all'}
              onChange={(value) => handleFilterChange('status', value)}
              options={[
                { value: 'all', label: 'جميع الحالات' },
                { value: 'active', label: 'نشط' },
                { value: 'inactive', label: 'غير نشط' }
              ]}
              placeholder="فلترة حسب الحالة"
            />

            <SelectInput
              label=""
              name="coverage-filter"
              value={filters.coverage_type || 'all'}
              onChange={(value) => handleFilterChange('coverage_type', value)}
              options={[
                { value: 'all', label: 'جميع أنواع التغطية' },
                { value: 'full', label: 'شامل' },
                { value: 'partial', label: 'جزئي' },
                { value: 'limited', label: 'محدود' }
              ]}
              placeholder="فلترة حسب نوع التغطية"
            />
          </div>
        </div>

        {/* Table */}
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            <span className="mr-3 text-gray-600 dark:text-gray-400">جاري التحميل...</span>
          </div>
        ) : filteredTypes.length === 0 ? (
          <div className="text-center py-12">
            <FiShield className="mx-auto text-6xl text-gray-300 dark:text-gray-600 mb-4" />
            <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
              لا توجد أنواع ضمانات
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              {filters.search || filters.status !== 'all' || filters.coverage_type !== 'all'
                ? 'جرب تغيير الفلاتر أو البحث'
                : 'ابدأ بإضافة نوع ضمان جديد لتنظيم ضماناتك'}
            </p>
            {(!filters.search && filters.status === 'all' && filters.coverage_type === 'all') && (
              <button
                onClick={() => setTypeModal({ isOpen: true, mode: 'create', type: null })}
                className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl mx-auto"
              >
                <FiPlus className="ml-2" />
                إضافة نوع ضمان جديد
              </button>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-12">
                    #
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    اسم النوع
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-24">
                    المدة (شهر)
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-32">
                    نوع التغطية
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-24">
                    الحالة
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-32">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {paginatedTypes.map((type, index) => (
                <tr key={type.id} className="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-150">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                    <span className="font-medium">{startIndex + index + 1}</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {type.name_ar}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {type.name}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                    {type.duration_months} شهر
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${
                        type.coverage_type === 'full'
                          ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800'
                          : type.coverage_type === 'partial'
                          ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 border-yellow-200 dark:border-yellow-800'
                          : 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 border-red-200 dark:border-red-800'
                      }`}>
                        {type.coverage_type === 'full' && <FiShield className="w-3 h-3 ml-1" />}
                        {type.coverage_type === 'partial' && <FiShield className="w-3 h-3 ml-1" />}
                        {type.coverage_type === 'limited' && <FiShield className="w-3 h-3 ml-1" />}
                        {COVERAGE_TYPE_LABELS[type.coverage_type]}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {type.is_active ? (
                        <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800">
                          <FiShield className="w-3 h-3 ml-1" />
                          نشط
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 border border-gray-200 dark:border-gray-700">
                          <FiShield className="w-3 h-3 ml-1 opacity-50" />
                          غير نشط
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center justify-end gap-1">
                      <TopbarTooltip text="تعديل النوع" position="top">
                        <button
                          onClick={() => handleEdit(type)}
                          className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200 transition-colors duration-150 p-2 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20 border border-transparent hover:border-green-200 dark:hover:border-green-700"
                        >
                          <FiEdit className="w-4 h-4" />
                        </button>
                      </TopbarTooltip>
                      <TopbarTooltip text="حذف النوع" position="top">
                        <button
                          onClick={() => setDeleteModal({
                            isOpen: true,
                            id: type.id,
                            name: type.name_ar,
                            isLoading: false
                          })}
                          className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 transition-colors duration-150 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 border border-transparent hover:border-red-200 dark:hover:border-red-700"
                        >
                          <FiTrash className="w-4 h-4" />
                        </button>
                      </TopbarTooltip>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        )}

        {/* Pagination */}
        {filteredTypes.length > 0 && (
        <TablePagination
          currentPage={currentPage}
          totalPages={totalPages}
          itemsPerPage={itemsPerPage}
          totalItems={filteredTypes.length}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={setItemsPerPage}
        />
        )}
      </div>

      {/* Create/Edit Modal */}
      <Modal
        isOpen={typeModal.isOpen}
        onClose={() => {
          setTypeModal({ isOpen: false, mode: 'create', type: null });
          resetForm();
        }}
        title={typeModal.mode === 'create' ? 'إضافة نوع ضمان جديد' : 'تعديل نوع الضمان'}
      >
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TextInput
              label="الاسم بالإنجليزية"
              name="name"
              value={formData.name}
              onChange={(value) => handleFormChange('name', value)}
              placeholder="أدخل الاسم بالإنجليزية"
              required
            />

            <TextInput
              label="الاسم بالعربية"
              name="name_ar"
              value={formData.name_ar}
              onChange={(value) => handleFormChange('name_ar', value)}
              placeholder="أدخل الاسم بالعربية"
              required
            />
          </div>

          <TextArea
            label="الوصف"
            name="description"
            value={formData.description}
            onChange={(value) => handleFormChange('description', value)}
            placeholder="أدخل وصف نوع الضمان (اختياري)"
            rows={3}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <NumberInput
              label="مدة الضمان (بالأشهر)"
              name="duration_months"
              value={formData.duration_months.toString()}
              onChange={(value) => handleFormChange('duration_months', parseInt(value) || 1)}
              min={1}
              max={120}
              required
            />

            <SelectInput
              label="نوع التغطية"
              name="coverage_type"
              value={formData.coverage_type}
              onChange={(value) => handleFormChange('coverage_type', value)}
              options={[
                { value: 'full', label: 'شامل' },
                { value: 'partial', label: 'جزئي' },
                { value: 'limited', label: 'محدود' }
              ]}
              required
            />
          </div>

          <TextArea
            label="الشروط والأحكام"
            name="terms_conditions"
            value={formData.terms_conditions}
            onChange={(value) => handleFormChange('terms_conditions', value)}
            placeholder="أدخل شروط وأحكام الضمان (اختياري)"
            rows={4}
          />

          <div className="bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-10 flex items-center">
            <ToggleSwitch
              id="is_active"
              checked={formData.is_active}
              onChange={(checked) => handleFormChange('is_active', checked)}
              label="نوع ضمان نشط"
              className="w-full"
            />
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={() => {
                setTypeModal({ isOpen: false, mode: 'create', type: null });
                resetForm();
              }}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              إلغاء
            </button>
            <button
              type="button"
              onClick={handleSave}
              disabled={loading || !isFormValid()}
              className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'جاري الحفظ...' : 'حفظ'}
            </button>
          </div>
        </div>
      </Modal>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, id: 0, name: '', isLoading: false })}
        onConfirm={handleDelete}
        title="حذف نوع الضمان"
        message={`هل أنت متأكد من حذف نوع الضمان "${deleteModal.name}"؟`}
        itemName={deleteModal.name}
        isLoading={deleteModal.isLoading}
      />

      {/* Success Modal */}
      <SuccessModal
        isOpen={successModal.isOpen}
        onClose={() => setSuccessModal({ isOpen: false, message: '' })}
        title="نجح العملية"
        message={successModal.message}
      />
    </div>
  );
};

export default WarrantyTypesTab;
