import React, { useState, useEffect, useRef } from 'react';
import {
  FiBarChart,
  FiDownload,
  FiAlertTriangle,
  FiShield,
  FiPieChart,
  FiActivity,
  FiTarget,
  FiPercent,
  FiChevronDown,
  FiTool,
  FiRefreshCw,
  FiCornerUpLeft,
  FiDollarSign
} from 'react-icons/fi';
import { useWarrantyReportsStore } from '../../stores/warranty';
import { DateRange, ReportType } from '../../types/warranty';
import PeriodButtons from '../PeriodButtons';
import SuccessModal from '../SuccessModal';
import ReactApexChart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';
import { ProgressRing } from '../charts';

import { CompactStatCard } from '../CompactNumberDisplay';
import CountUp from 'react-countup';


interface WarrantyReportsTabProps {
  className?: string;
}

const WarrantyReportsTab: React.FC<WarrantyReportsTabProps> = ({ className = '' }) => {
  const {
    stats,
    claimStatistics,
    loading,
    fetchWarrantyStats,
    fetchClaimStatistics,
    exportWarrantyReport
  } = useWarrantyReportsStore();

  // State
  const [selectedPeriod, setSelectedPeriod] = useState('30d'); // فترة موحدة لجميع الأقسام
  const [dateRange, setDateRange] = useState<DateRange>({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
    end: new Date().toISOString().split('T')[0] // today
  });

  const [isExportDropdownOpen, setIsExportDropdownOpen] = useState(false);
  const exportDropdownRef = useRef<HTMLDivElement>(null);

  const [successModal, setSuccessModal] = useState({
    isOpen: false,
    message: ''
  });



  // Load data on mount only if no data exists
  useEffect(() => {
    // Only load if we don't have any data yet and not currently loading
    if ((!stats || !claimStatistics) && !loading) {
      console.log('🔄 [WarrantyReportsTab] تحميل البيانات الأولية...');
      loadAllData();
    }
  }, []); // إزالة dependencies لمنع إعادة التحميل المستمر

  // Debug: log data structure
  useEffect(() => {
    console.log('📊 [WarrantyReportsTab] البيانات الحالية:', {
      stats,
      claimStatistics,
      loading
    });
  }, [stats, claimStatistics, loading]);

  // إغلاق القائمة المنسدلة عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (exportDropdownRef.current && !exportDropdownRef.current.contains(event.target as Node)) {
        setIsExportDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);



  const loadAllData = async () => {
    try {
      await Promise.all([
        fetchWarrantyStats(dateRange),
        fetchClaimStatistics(dateRange)
      ]);
    } catch (error) {
      console.error('Error loading reports data:', error);
    }
  };

  // Handle period change for all sections (unified)
  const handlePeriodChange = async (period: string, days: number) => {
    try {
      console.log(`🔄 [WarrantyReports] تغيير الفترة إلى: ${period} (${days} أيام)`);
      setSelectedPeriod(period);

      // حساب التواريخ بناءً على الفترة المحددة
      const endDate = new Date().toISOString().split('T')[0];
      const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      const newDateRange = { start: startDate, end: endDate };
      setDateRange(newDateRange);

      console.log(`📅 [WarrantyReports] نطاق التاريخ الجديد: ${startDate} إلى ${endDate}`);

      // تحميل جميع البيانات (الضمانات والمطالبات)
      await Promise.all([
        fetchWarrantyStats(newDateRange),
        fetchClaimStatistics(newDateRange)
      ]);

      console.log(`✅ [WarrantyReports] تم تحديث البيانات للفترة: ${period}`);
    } catch (error) {
      console.error('❌ [WarrantyReports] خطأ في تحميل البيانات للفترة الجديدة:', error);
    }
  };





  // Handle export
  const handleExport = async (type: ReportType) => {
    try {
      setIsExportDropdownOpen(false); // إغلاق القائمة المنسدلة
      await exportWarrantyReport(type, dateRange);
      setSuccessModal({
        isOpen: true,
        message: 'تم تصدير التقرير بنجاح'
      });
    } catch (error) {
      console.error('Error exporting report:', error);
    }
  };

  // حساب مؤشرات الأداء المتقدمة
  const calculateKPIs = () => {
    if (!stats) return null;

    const totalWarranties = stats.total_warranties || 0;
    const activeWarranties = stats.active_warranties || 0;
    const expiredWarranties = stats.expired_warranties || 0;
    const totalClaims = stats.total_claims || 0;
    const pendingClaims = stats.pending_claims || 0;
    const approvedClaims = stats.approved_claims || 0;
    const rejectedClaims = stats.rejected_claims || 0;
    const totalClaimCost = stats.total_claim_cost || 0;
    const averageClaimCost = stats.average_claim_cost || 0;
    const claimRatePercentage = stats.claim_rate_percentage || 0;

    // معدل الضمانات النشطة
    const activeWarrantyRate = totalWarranties > 0 ? (activeWarranties / totalWarranties) * 100 : 0;

    // معدل الضمانات المنتهية
    const expiredWarrantyRate = totalWarranties > 0 ? (expiredWarranties / totalWarranties) * 100 : 0;

    // معدل النجاح في الموافقة على المطالبات
    const approvalSuccessRate = totalClaims > 0 ? (approvedClaims / totalClaims) * 100 : 0;

    // معدل رفض المطالبات
    const rejectionRate = totalClaims > 0 ? (rejectedClaims / totalClaims) * 100 : 0;

    return {
      claimRatePercentage,
      approvalSuccessRate,
      activeWarrantyRate,
      expiredWarrantyRate,
      rejectionRate,
      averageClaimCost,
      totalClaimCost,
      totalWarranties,
      totalClaims,
      pendingClaims
    };
  };

  const kpis = calculateKPIs();



  // مكون المخططات البيانية
  const WarrantyCharts: React.FC<{
    stats: any;
    claimStatistics: any;
    className?: string;
  }> = ({ stats, claimStatistics, className = '' }) => {
    // تسجيل البيانات للتحقق
    console.log('📊 [WarrantyCharts] البيانات المتاحة:', { stats, claimStatistics });

    // تحديد الوضع المظلم
    const isDark = document.documentElement.classList.contains('dark');

    if (!stats && !claimStatistics) {
      return (
        <div className={`text-center py-12 ${className}`}>
          <p className="text-gray-500 dark:text-gray-400">لا توجد بيانات لعرض المخططات</p>
        </div>
      );
    }

    // مخطط حالات الضمان
    const activeWarranties = stats?.active_warranties || 0;
    const expiredWarranties = stats?.expired_warranties || 0;
    const voidedWarranties = stats?.voided_warranties || 0;

    const warrantyStatusData = [activeWarranties, expiredWarranties, voidedWarranties];
    const hasWarrantyData = warrantyStatusData.some(value => value > 0);

    console.log('📊 [WarrantyCharts] بيانات الضمانات:', { activeWarranties, expiredWarranties, voidedWarranties, hasWarrantyData });

    const warrantyStatusOptions: ApexOptions = {
      chart: {
        type: 'donut',
        fontFamily: 'almarai, sans-serif',
        toolbar: {
          show: false
        },
        animations: {
          enabled: true,
          speed: 500,
          animateGradually: {
            enabled: true,
            delay: 150
          },
          dynamicAnimation: {
            enabled: true,
            speed: 350
          }
        },
        background: 'transparent'
      },
      theme: {
        mode: isDark ? 'dark' : 'light',
        palette: 'palette1'
      },
      colors: isDark
        ? ['#34D399', '#F87171', '#9CA3AF']
        : ['#10B981', '#DC2626', '#6B7280'],
      labels: ['نشط', 'منتهي', 'ملغي'],
      dataLabels: {
        enabled: true,
        formatter: (val: number) => {
          return `${val.toFixed(0)}%`;
        },
        style: {
          fontSize: '11px',
          fontFamily: 'almarai, sans-serif',
          fontWeight: '600',
          colors: ['#FFFFFF']
        },
        dropShadow: {
          enabled: true,
          top: 1,
          left: 1,
          blur: 2,
          color: '#000000',
          opacity: 0.6
        }
      },
      stroke: {
        show: true,
        curve: 'smooth',
        lineCap: 'round',
        width: 4,
        colors: [isDark ? '#1F2937' : '#FFFFFF']
      },
      fill: {
        type: 'solid'
      },
      plotOptions: {
        pie: {
          donut: {
            size: '60%',
            labels: {
              show: true,
              name: {
                show: true,
                fontSize: '12px',
                fontFamily: 'almarai, sans-serif',
                fontWeight: 500,
                color: isDark ? '#9CA3AF' : '#6B7280',
                offsetY: -20,
                formatter: function () {
                  return 'النسبة';
                }
              },
              value: {
                show: true,
                fontSize: '24px',
                fontFamily: 'almarai, sans-serif',
                fontWeight: 700,
                color: isDark ? '#60A5FA' : '#3B82F6',
                offsetY: 15,
                formatter: function (val: any) {
                  return `${val}%`;
                }
              },
              total: {
                show: true,
                showAlways: true,
                label: 'الحالات',
                fontSize: '20px',
                fontFamily: 'almarai, sans-serif',
                fontWeight: 700,
                color: isDark ? '#60A5FA' : '#2563EB',
                formatter: function () {
                  return '100%';
                }
              }
            }
          },
          expandOnClick: true
        }
      },
      legend: {
        show: false
      },
      tooltip: {
        theme: isDark ? 'dark' : 'light',
        style: {
          fontSize: '13px',
          fontFamily: 'almarai, sans-serif'
        },
        custom: function({ seriesIndex, w }) {
          const categoryName = w.config.labels[seriesIndex];

          // استخدام القيم الفعلية حسب الفهرس
          const actualValues = [activeWarranties, expiredWarranties, voidedWarranties];
          const value = actualValues[seriesIndex];

          // حساب النسبة المئوية الصحيحة
          const totalWarranties = activeWarranties + expiredWarranties + voidedWarranties;
          const percentage = totalWarranties > 0 ? (value / totalWarranties) * 100 : 0;

          return `
            <div class="${isDark ? 'bg-gray-900 text-white border-gray-700' : 'bg-white text-gray-900 border-gray-200'} p-3 rounded-lg shadow-xl border">
              <div class="font-bold text-sm">${categoryName}</div>
              <div class="text-xs mt-1">${value.toLocaleString()} ضمان</div>
              <div class="text-xs mt-1">${percentage.toFixed(1)}%</div>
            </div>
          `;
        }
      },
      responsive: [
        {
          breakpoint: 768,
          options: {
            chart: {
              height: 300
            },
            legend: {
              position: 'bottom',
              fontSize: '12px'
            },
            dataLabels: {
              enabled: false
            }
          }
        }
      ]
    };

    // مخطط المطالبات - استخدام البيانات الصحيحة من claimStatistics أو stats
    let claimsDataArray: number[] = [];
    let hasClaimsData = false;

    // أولاً: محاولة استخدام claimStatistics مباشرة (التنسيق الجديد)
    if (claimStatistics && (
      claimStatistics.pending_claims !== undefined ||
      claimStatistics.approved_claims !== undefined ||
      claimStatistics.rejected_claims !== undefined ||
      claimStatistics.in_progress_claims !== undefined ||
      claimStatistics.completed_claims !== undefined
    )) {
      claimsDataArray = [
        claimStatistics.pending_claims || 0,
        claimStatistics.approved_claims || 0,
        claimStatistics.rejected_claims || 0,
        claimStatistics.in_progress_claims || 0,
        claimStatistics.completed_claims || 0
      ];
      hasClaimsData = claimsDataArray.some(value => value > 0);
      console.log('📊 [WarrantyCharts] بيانات المطالبات من claimStatistics (مباشر):', claimsDataArray);
    }
    // ثانياً: محاولة استخدام claimStatistics.by_status (التنسيق القديم للتوافق)
    else if (claimStatistics?.by_status) {
      claimsDataArray = [
        claimStatistics.by_status.pending || 0,
        claimStatistics.by_status.approved || 0,
        claimStatistics.by_status.rejected || 0,
        claimStatistics.by_status.in_progress || 0,
        claimStatistics.by_status.completed || 0
      ];
      hasClaimsData = claimsDataArray.some(value => value > 0);
      console.log('📊 [WarrantyCharts] بيانات المطالبات من claimStatistics.by_status:', claimsDataArray);
    }
    // ثالثاً: استخدام stats كبديل (بيانات محدودة)
    else if (stats) {
      claimsDataArray = [
        stats.pending_claims || 0,
        stats.approved_claims || 0,
        stats.rejected_claims || 0,
        stats.completed_claims || 0, // متاح في stats الآن
        0  // in_progress غير متاح في stats
      ];
      hasClaimsData = claimsDataArray.some(value => value > 0);
      console.log('📊 [WarrantyCharts] بيانات المطالبات من stats (بديل):', claimsDataArray);
    }

    const claimsData = hasClaimsData ? [{ name: 'المطالبات', data: claimsDataArray }] : [];

    console.log('📊 [WarrantyCharts] النتيجة النهائية للمطالبات:', { claimsData, hasClaimsData });

    const claimsOptions: ApexOptions = {
      chart: {
        type: 'bar',
        fontFamily: 'Almarai, sans-serif',
        toolbar: { show: false },
        background: 'transparent'
      },
      theme: {
        mode: isDark ? 'dark' : 'light'
      },
      colors: [isDark ? '#60a5fa' : '#3b82f6'],
      plotOptions: {
        bar: {
          borderRadius: 8,
          columnWidth: '60%',
          distributed: false
        }
      },
      xaxis: {
        categories: ['في الانتظار', 'موافق عليها', 'مرفوضة', 'قيد المعالجة', 'مكتملة'],
        labels: {
          style: {
            fontSize: '12px',
            fontFamily: 'Almarai, sans-serif',
            colors: isDark ? '#9ca3af' : '#6b7280'
          }
        },
        axisBorder: {
          color: isDark ? '#374151' : '#e5e7eb'
        },
        axisTicks: {
          color: isDark ? '#374151' : '#e5e7eb'
        }
      },
      yaxis: {
        labels: {
          style: {
            fontSize: '12px',
            fontFamily: 'Almarai, sans-serif',
            colors: isDark ? '#9ca3af' : '#6b7280'
          },
          formatter: (val: number) => val.toLocaleString()
        }
      },
      grid: {
        borderColor: isDark ? '#374151' : '#e5e7eb',
        strokeDashArray: 3
      },
      dataLabels: {
        enabled: true,
        formatter: (val: number) => val.toLocaleString(),
        style: {
          fontSize: '12px',
          fontFamily: 'Almarai, sans-serif',
          colors: ['#ffffff']
        }
      },
      tooltip: {
        enabled: true,
        theme: isDark ? 'dark' : 'light',
        style: {
          fontSize: '13px',
          fontFamily: 'Almarai, sans-serif'
        },
        custom: function({ series, dataPointIndex, w }) {
          const categoryName = w.config.xaxis.categories[dataPointIndex];
          const value = series[0][dataPointIndex];

          // تحديد اللون حسب الحالة
          let statusColor = '';
          switch(categoryName) {
            case 'في الانتظار':
              statusColor = isDark ? '#fbbf24' : '#d97706';
              break;
            case 'موافق عليها':
              statusColor = isDark ? '#34d399' : '#10b981';
              break;
            case 'مرفوضة':
              statusColor = isDark ? '#f87171' : '#dc2626';
              break;
            case 'قيد المعالجة':
              statusColor = isDark ? '#60a5fa' : '#3b82f6';
              break;
            case 'مكتملة':
              statusColor = isDark ? '#a78bfa' : '#7c3aed';
              break;
            default:
              statusColor = isDark ? '#9ca3af' : '#6b7280';
          }

          return `
            <div class="${isDark ? 'bg-gray-900 text-white border-gray-700' : 'bg-white text-gray-900 border-gray-200'} p-3 rounded-lg shadow-xl border">
              <div class="font-bold text-sm" style="color: ${statusColor};">${categoryName}</div>
              <div class="text-xs mt-1">${value.toLocaleString()} مطالبة</div>
            </div>
          `;
        }
      }
    };

    return (
      <div className={`space-y-6 ${className}`}>
        {!hasWarrantyData && !hasClaimsData && (
          <div className="text-center py-12 bg-gray-50 dark:bg-gray-800 rounded-xl border-2 border-dashed border-gray-300 dark:border-gray-600">
            <FiBarChart className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" />
            <p className="text-lg font-medium text-gray-600 dark:text-gray-400 mb-2">لا توجد بيانات للمخططات</p>
            <p className="text-sm text-gray-500 dark:text-gray-500">
              تأكد من وجود ضمانات ومطالبات في النظام لعرض التحليلات البصرية
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* مخطط حالات الضمان */}
          {stats && hasWarrantyData && (
            <div className="touch-card">
              <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-6">
                <div className="flex items-center justify-between">
                  <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100 flex items-center">
                    <FiPieChart className="ml-2 text-blue-600 dark:text-blue-400" />
                    توزيع حالات الضمان
                  </h4>
                  <div className="flex items-center gap-2 bg-blue-50 dark:bg-blue-900/20 px-3 py-2 rounded-lg">
                    <span className="text-sm text-gray-600 dark:text-gray-400">إجمالي:</span>
                    <span className="text-lg font-bold text-blue-600 dark:text-blue-400">
                      {(stats.total_warranties || 0).toLocaleString()}
                    </span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">ضمان</span>
                  </div>
                </div>
              </div>

              {/* Chart Container */}
              <div className="h-80">
                <ReactApexChart
                  type="donut"
                  height="100%"
                  width="100%"
                  options={warrantyStatusOptions}
                  series={warrantyStatusData}
                />
              </div>

              {/* Custom Legend */}
              <div className="mt-6 flex flex-wrap justify-center gap-4">
                {[
                  { name: 'نشط', value: activeWarranties },
                  { name: 'منتهي', value: expiredWarranties },
                  { name: 'ملغي', value: voidedWarranties }
                ].map((status, index) => (
                  <div key={index} className="flex items-center gap-3 px-4 py-3 bg-gray-50 dark:bg-gray-700/50 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200">
                    <div
                      className="w-4 h-4 rounded-full shadow-sm"
                      style={{
                        backgroundColor: (isDark
                          ? ['#34D399', '#F87171', '#9CA3AF']
                          : ['#10B981', '#DC2626', '#6B7280'])[index]
                      }}
                    />
                    <div className="text-center">
                      <div className="text-sm font-semibold text-gray-800 dark:text-gray-100">
                        {status.name}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">
                        {status.value.toLocaleString()} ضمان
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* مخطط المطالبات */}
          {hasClaimsData && (
            <div className="touch-card">
              <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-6">
                <div className="flex items-center justify-between">
                  <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100 flex items-center">
                    <FiBarChart className="ml-2 text-orange-600 dark:text-orange-400" />
                    إحصائيات المطالبات
                  </h4>
                  <div className="flex items-center gap-2 bg-orange-50 dark:bg-orange-900/20 px-3 py-2 rounded-lg">
                    <span className="text-sm text-gray-600 dark:text-gray-400">إجمالي:</span>
                    <span className="text-lg font-bold text-orange-600 dark:text-orange-400">
                      {(() => {
                        // حساب إجمالي المطالبات من البيانات المتاحة
                        const totalClaims = claimStatistics?.total_claims ||
                                          stats?.total_claims ||
                                          claimsDataArray.reduce((sum, value) => sum + value, 0);
                        return totalClaims.toLocaleString();
                      })()}
                    </span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">مطالبة</span>
                  </div>
                </div>
              </div>

              {/* Chart Container */}
              <div className="h-80 md:h-96">
                <ReactApexChart
                  type="bar"
                  height="100%"
                  width="100%"
                  options={claimsOptions}
                  series={claimsData}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };



  return (
    <>
    <div className={`space-y-8 ${className}`}>
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
          <div className="flex items-center min-w-0 flex-1">
            <div className="min-w-0 flex-1">
              <h2 className="text-lg sm:text-xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                <FiBarChart className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                <span className="truncate">تقارير الضمانات</span>
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                تحليل شامل لأداء الضمانات والمطالبات مع مؤشرات الأداء الرئيسية
              </p>
            </div>
          </div>

          {/* زر التصدير مع القائمة المنسدلة */}
          <div className="relative" ref={exportDropdownRef}>
            {/* الزر الرئيسي */}
            <button
              onClick={() => setIsExportDropdownOpen(!isExportDropdownOpen)}
              disabled={loading}
              className="bg-success-600 hover:bg-success-700 text-white px-4 py-2 rounded-xl transition-all duration-200 ease-in-out border-2 border-success-600 hover:border-success-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-success-500/20 shadow-lg hover:shadow-xl min-w-[140px] disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <FiDownload className="ml-2" />
              تصدير التقارير
              <FiChevronDown className={`mr-2 transition-transform duration-200 ${isExportDropdownOpen ? 'rotate-180' : ''}`} />
            </button>

            {/* القائمة المنسدلة */}
            {isExportDropdownOpen && (
              <div className="absolute left-1/2 transform -translate-x-1/2 top-full mt-2 min-w-full w-max bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-gray-200 dark:border-gray-700 z-30 overflow-hidden">
                <div className="py-1">
                  <button
                    onClick={() => handleExport('warranties')}
                    disabled={loading}
                    className="w-full text-right px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 flex items-center disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap"
                  >
                    <FiShield className="ml-3 text-primary-600 dark:text-primary-400" />
                    تصدير الضمانات
                  </button>

                  <div className="border-t border-gray-100 dark:border-gray-600 mx-2"></div>

                  <button
                    onClick={() => handleExport('claims')}
                    disabled={loading}
                    className="w-full text-right px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 flex items-center disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap"
                  >
                    <FiActivity className="ml-3 text-secondary-600 dark:text-secondary-400" />
                    تصدير المطالبات
                  </button>

                  <div className="border-t border-gray-100 dark:border-gray-600 mx-2"></div>

                  <button
                    onClick={() => handleExport('expiring')}
                    disabled={loading}
                    className="w-full text-right px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 flex items-center disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap"
                  >
                    <FiAlertTriangle className="ml-3 text-warning-600 dark:text-warning-400" />
                    تصدير المنتهية
                  </button>

                  <div className="border-t border-gray-100 dark:border-gray-600 mx-2"></div>

                  <button
                    onClick={() => handleExport('statistics')}
                    disabled={loading}
                    className="w-full text-right px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 flex items-center disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap"
                  >
                    <FiBarChart className="ml-3 text-success-600 dark:text-success-400" />
                    تصدير الإحصائيات
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>



      {/* مؤشرات الأداء الرئيسية - بنفس نمط التطبيق */}
      {kpis && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 overflow-hidden">
          {/* العنوان مدمج مع المحتوى */}
          <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-6">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-4">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 flex items-center">
                  <FiTarget className="ml-2 text-blue-600 dark:text-blue-400" />
                  مؤشرات الأداء الرئيسية
                </h3>
                {loading && (
                  <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary-600 border-t-transparent"></div>
                    <span>تحديث البيانات...</span>
                  </div>
                )}
              </div>

              {/* أزرار الفترات الزمنية */}
              <PeriodButtons
                selectedPeriod={selectedPeriod}
                onPeriodChange={handlePeriodChange}
                size="md"
              />
            </div>
          </div>

          {/* البطاقات */}
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* معدل المطالبات */}
            <div className="touch-card stats-card">
              <div className="flex items-center justify-between h-full">
                {/* النص على اليمين */}
                <div className="flex-1 text-right">
                  <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">معدل المطالبات</h4>
                  <p className="text-3xl font-bold mb-1 text-gray-900 dark:text-gray-100">
                    <CountUp
                      end={kpis.claimRatePercentage}
                      duration={2}
                      delay={0.2}
                      decimals={1}
                      preserveValue
                    />%
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">من إجمالي الضمانات</p>
                  <span className={`text-sm font-medium px-2 py-1 rounded-full ${
                    kpis.claimRatePercentage < 10 ? 'bg-emerald-50 text-emerald-600 dark:bg-emerald-900/20 dark:text-emerald-400' :
                    kpis.claimRatePercentage > 20 ? 'bg-rose-50 text-rose-600 dark:bg-rose-900/20 dark:text-rose-400' :
                    'bg-amber-50 text-amber-600 dark:bg-amber-900/20 dark:text-amber-400'
                  }`}>
                    {kpis.claimRatePercentage < 10 ? 'ممتاز' : kpis.claimRatePercentage > 20 ? 'مرتفع' : 'مقبول'}
                  </span>
                </div>

                {/* المؤشر على اليسار */}
                <div className="flex-shrink-0 ml-4">
                  <ProgressRing
                    value={kpis.claimRatePercentage}
                    size={80}
                    strokeWidth={6}
                    variant={
                      kpis.claimRatePercentage < 10 ? 'success' :
                      kpis.claimRatePercentage > 20 ? 'danger' : 'warning'
                    }
                    showPercentage={false}
                    enableAnimation={true}
                    showShadow={false}
                    icon={<FiPercent className={`text-2xl ${
                      kpis.claimRatePercentage < 10 ? 'text-emerald-600' :
                      kpis.claimRatePercentage > 20 ? 'text-rose-600' : 'text-amber-600'
                    }`} />}
                  />
                </div>
              </div>
            </div>

            {/* معدل الموافقة */}
            <div className="touch-card stats-card">
              <div className="flex items-center justify-between h-full">
                {/* النص على اليمين */}
                <div className="flex-1 text-right">
                  <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">معدل الموافقة</h4>
                  <p className="text-3xl font-bold mb-1 text-gray-900 dark:text-gray-100">
                    <CountUp
                      end={kpis.approvalSuccessRate}
                      duration={2}
                      delay={0.4}
                      decimals={1}
                      preserveValue
                    />%
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">من إجمالي المطالبات</p>
                  <span className={`text-sm font-medium px-2 py-1 rounded-full ${
                    kpis.approvalSuccessRate > 80 ? 'bg-emerald-50 text-emerald-600 dark:bg-emerald-900/20 dark:text-emerald-400' :
                    kpis.approvalSuccessRate < 60 ? 'bg-rose-50 text-rose-600 dark:bg-rose-900/20 dark:text-rose-400' :
                    'bg-amber-50 text-amber-600 dark:bg-amber-900/20 dark:text-amber-400'
                  }`}>
                    {kpis.approvalSuccessRate > 80 ? 'ممتاز' : kpis.approvalSuccessRate < 60 ? 'ضعيف' : 'جيد'}
                  </span>
                </div>

                {/* المؤشر على اليسار */}
                <div className="flex-shrink-0 ml-4">
                  <ProgressRing
                    value={kpis.approvalSuccessRate}
                    size={80}
                    strokeWidth={6}
                    variant={
                      kpis.approvalSuccessRate > 80 ? 'success' :
                      kpis.approvalSuccessRate < 60 ? 'danger' : 'warning'
                    }
                    showPercentage={false}
                    enableAnimation={true}
                    showShadow={false}
                    icon={<FiActivity className={`text-2xl ${
                      kpis.approvalSuccessRate > 80 ? 'text-emerald-600' :
                      kpis.approvalSuccessRate < 60 ? 'text-rose-600' : 'text-amber-600'
                    }`} />}
                  />
                </div>
              </div>
            </div>

            {/* الضمانات النشطة */}
            <div className="touch-card stats-card">
              <div className="flex items-center justify-between h-full">
                {/* النص على اليمين */}
                <div className="flex-1 text-right">
                  <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">الضمانات النشطة</h4>
                  <p className="text-3xl font-bold mb-1 text-gray-900 dark:text-gray-100">
                    <CountUp
                      end={kpis.activeWarrantyRate}
                      duration={2}
                      delay={0.6}
                      decimals={1}
                      preserveValue
                    />%
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">من إجمالي الضمانات</p>
                  <span className={`text-sm font-medium px-2 py-1 rounded-full ${
                    kpis.activeWarrantyRate > 70 ? 'bg-emerald-50 text-emerald-600 dark:bg-emerald-900/20 dark:text-emerald-400' :
                    kpis.activeWarrantyRate < 50 ? 'bg-rose-50 text-rose-600 dark:bg-rose-900/20 dark:text-rose-400' :
                    'bg-amber-50 text-amber-600 dark:bg-amber-900/20 dark:text-amber-400'
                  }`}>
                    {kpis.activeWarrantyRate > 70 ? 'ممتاز' : kpis.activeWarrantyRate < 50 ? 'ضعيف' : 'جيد'}
                  </span>
                </div>

                {/* المؤشر على اليسار */}
                <div className="flex-shrink-0 ml-4">
                  <ProgressRing
                    value={kpis.activeWarrantyRate}
                    size={80}
                    strokeWidth={6}
                    variant={
                      kpis.activeWarrantyRate > 70 ? 'success' :
                      kpis.activeWarrantyRate < 50 ? 'danger' : 'warning'
                    }
                    showPercentage={false}
                    enableAnimation={true}
                    showShadow={false}
                    icon={<FiShield className={`text-2xl ${
                      kpis.activeWarrantyRate > 70 ? 'text-emerald-600' :
                      kpis.activeWarrantyRate < 50 ? 'text-rose-600' : 'text-amber-600'
                    }`} />}
                  />
                </div>
              </div>
            </div>


            {/* معدل الرفض */}
            <div className="touch-card stats-card">
              <div className="flex items-center justify-between h-full">
                {/* النص على اليمين */}
                <div className="flex-1 text-right">
                  <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">معدل الرفض</h4>
                  <p className="text-3xl font-bold mb-1 text-gray-900 dark:text-gray-100">
                    <CountUp
                      end={kpis.rejectionRate}
                      duration={2}
                      delay={0.8}
                      decimals={1}
                      preserveValue
                    />%
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">من إجمالي المطالبات</p>
                  <span className={`text-sm font-medium px-2 py-1 rounded-full ${
                    kpis.rejectionRate < 20 ? 'bg-emerald-50 text-emerald-600 dark:bg-emerald-900/20 dark:text-emerald-400' :
                    kpis.rejectionRate > 40 ? 'bg-rose-50 text-rose-600 dark:bg-rose-900/20 dark:text-rose-400' :
                    'bg-amber-50 text-amber-600 dark:bg-amber-900/20 dark:text-amber-400'
                  }`}>
                    {kpis.rejectionRate < 20 ? 'منخفض' : kpis.rejectionRate > 40 ? 'مرتفع' : 'متوسط'}
                  </span>
                </div>

                {/* المؤشر على اليسار */}
                <div className="flex-shrink-0 ml-4">
                  <ProgressRing
                    value={Math.min(100, (kpis.rejectionRate / 50) * 100)}
                    size={80}
                    strokeWidth={6}
                    variant={
                      kpis.rejectionRate < 20 ? 'success' :
                      kpis.rejectionRate > 40 ? 'danger' : 'warning'
                    }
                    showPercentage={false}
                    enableAnimation={true}
                    showShadow={false}
                    icon={<FiAlertTriangle className={`text-2xl ${
                      kpis.rejectionRate < 20 ? 'text-emerald-600' :
                      kpis.rejectionRate > 40 ? 'text-rose-600' : 'text-amber-600'
                    }`} />}
                  />
                </div>
              </div>
            </div>

            </div>
          </div>
        </div>
      )}

      {/* أنواع المطالبات والتكاليف */}
      {stats && claimStatistics && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 overflow-hidden">
          {/* العنوان مدمج مع المحتوى */}
          <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-6">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-4">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 flex items-center">
                  <FiActivity className="ml-2 text-purple-600 dark:text-purple-400" />
                  أنواع المطالبات والتكاليف
                </h3>
                {loading && (
                  <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary-600 border-t-transparent"></div>
                    <span>تحديث البيانات...</span>
                  </div>
                )}
              </div>

              {/* أزرار الفترات الزمنية */}
              <PeriodButtons
                selectedPeriod={selectedPeriod}
                onPeriodChange={handlePeriodChange}
                size="md"
              />
            </div>
          </div>

          {/* البطاقات */}
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">

              {/* مطالبات الإصلاح */}
              <CompactStatCard
                title="مطالبات الإصلاح"
                amount={claimStatistics?.repair_claims || 0}
                icon={<FiTool className="text-blue-600 dark:text-blue-400" />}
                showCurrency={false}
                compactThreshold={1000}
                changeText="طلبات إصلاح المنتجات المعيبة"
                enableAnimation={true}
                animationDuration={2}
                animationDelay={0.2}
              />

              {/* مطالبات الاستبدال */}
              <CompactStatCard
                title="مطالبات الاستبدال"
                amount={claimStatistics?.replacement_claims || 0}
                icon={<FiRefreshCw className="text-green-600 dark:text-green-400" />}
                showCurrency={false}
                compactThreshold={1000}
                changeText="طلبات استبدال المنتجات التالفة"
                enableAnimation={true}
                animationDuration={2}
                animationDelay={0.4}
              />

              {/* مطالبات الاسترداد */}
              <CompactStatCard
                title="مطالبات الاسترداد"
                amount={claimStatistics?.refund_claims || 0}
                icon={<FiCornerUpLeft className="text-orange-600 dark:text-orange-400" />}
                showCurrency={false}
                compactThreshold={1000}
                changeText="طلبات استرداد قيمة المنتجات"
                enableAnimation={true}
                animationDuration={2}
                animationDelay={0.6}
              />

              {/* إجمالي التكاليف */}
              <CompactStatCard
                title="إجمالي التكاليف"
                amount={claimStatistics?.total_actual_cost || kpis?.totalClaimCost || 0}
                icon={<FiDollarSign className="text-purple-600 dark:text-purple-400" />}
                showCurrency={true}
                compactThreshold={1000}
                changeText="التكلفة الإجمالية لجميع المطالبات"
                enableAnimation={true}
                animationDuration={2}
                animationDelay={0.8}
              />

            </div>
          </div>
        </div>
      )}

      {/* المخططات البيانية والإحصائيات المرئية */}
      {stats && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 overflow-hidden">
          {/* العنوان مدمج مع المحتوى */}
          <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-6">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-4">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 flex items-center">
                  <FiPieChart className="ml-2 text-indigo-600 dark:text-indigo-400" />
                  التحليلات البصرية والإحصائيات
                </h3>
                {loading && (
                  <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary-600 border-t-transparent"></div>
                    <span>تحديث البيانات...</span>
                  </div>
                )}
              </div>

              {/* أزرار الفترات الزمنية */}
              <PeriodButtons
                selectedPeriod={selectedPeriod}
                onPeriodChange={handlePeriodChange}
                size="md"
              />
            </div>
          </div>

          {/* المخططات البيانية */}
          <div className="p-6">
            <WarrantyCharts
              stats={stats}
              claimStatistics={claimStatistics}
              className=""
            />
          </div>
        </div>
      )}









      </div>

      {/* Success Modal */}
      <SuccessModal
        isOpen={successModal.isOpen}
        onClose={() => setSuccessModal({ isOpen: false, message: '' })}
        title="نجح العملية"
        message={successModal.message}
      />
    </>
  );
};

export default WarrantyReportsTab;
