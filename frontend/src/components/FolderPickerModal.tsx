import React, { useState, useRef, useEffect } from 'react';
import { FaFolder, FaCheck, FaHome, FaDesktop, FaDownload, FaHdd } from 'react-icons/fa';
import Modal from './Modal';

interface FolderPickerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (path: string) => void;
  currentPath: string;
}

const FolderPickerModal: React.FC<FolderPickerModalProps> = ({
  isOpen,
  onClose,
  onSelect,
  currentPath
}) => {
  const [customPath, setCustomPath] = useState(currentPath || '');

  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // إدارة إظهار/إخفاء شريط التمرير
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    let scrollTimeout: NodeJS.Timeout;

    const handleScroll = () => {
      scrollContainer.classList.add('scrolling');

      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        scrollContainer.classList.remove('scrolling');
      }, 1000); // إخفاء بعد ثانية من توقف التمرير
    };

    scrollContainer.addEventListener('scroll', handleScroll);

    return () => {
      scrollContainer.removeEventListener('scroll', handleScroll);
      clearTimeout(scrollTimeout);
    };
  }, [isOpen]);



  // مجلدات شائعة مبسطة
  const commonFolders = [
    { name: 'النسخ الاحتياطية (افتراضي)', path: 'backups', icon: <FaFolder className="text-blue-500" /> },
    { name: 'سطح المكتب', path: '~/Desktop', icon: <FaDesktop className="text-green-500" /> },
    { name: 'المستندات', path: '~/Documents', icon: <FaHome className="text-orange-500" /> },
    { name: 'التحميلات', path: '~/Downloads', icon: <FaDownload className="text-purple-500" /> },
    { name: 'القرص الصلب C:', path: 'C:\\Backups', icon: <FaHdd className="text-gray-600" /> },
    { name: 'القرص الصلب D:', path: 'D:\\Backups', icon: <FaHdd className="text-gray-600" /> }
  ];



  const handleFolderSelect = (path: string) => {
    setCustomPath(path);
  };

  const handleConfirm = () => {
    if (customPath.trim()) {
      onSelect(customPath.trim());
      onClose();
    }
  };

  const handleCancel = () => {
    setCustomPath(currentPath || '');
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleCancel}
      title="� اختيار مجلد النسخ الاحتياطية (نافذة مخصصة)"
      size="lg"
      zIndex="highest"
    >
      <div className="space-y-4">

          {/* حقل إدخال المسار */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              أدخل مسار المجلد يدوياً:
            </label>
            <input
              type="text"
              value={customPath}
              onChange={(e) => setCustomPath(e.target.value)}
              placeholder="أدخل مسار المجلد..."
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            />
          </div>

          {/* مجلدات شائعة */}
          <div className="mb-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50/50 dark:bg-gray-800/50">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              أو اختر من المجلدات الشائعة:
            </label>
            <div
              ref={scrollContainerRef}
              className="max-h-48 overflow-y-auto folder-picker-scrollbar pr-3"
            >
              <div className="grid grid-cols-1 gap-2 mx-3">
                {commonFolders.map((folder, index) => (
                  <button
                    key={index}
                    onClick={() => handleFolderSelect(folder.path)}
                    className={`flex items-center p-3 rounded-lg border transition-all text-left ${
                      customPath === folder.path
                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                        : 'border-gray-200 dark:border-gray-700 hover:border-primary-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                    }`}
                  >
                    <div className="ml-3">
                      {folder.icon}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                        {folder.name}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {folder.path}
                      </div>
                    </div>
                    {customPath === folder.path && (
                      <FaCheck className="text-primary-600" />
                    )}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* معلومات إضافية */}
          <div className="bg-blue-50 dark:bg-blue-900/30 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="text-blue-700 dark:text-blue-300 text-sm">
              <p className="font-medium mb-2">💡 كيفية معرفة المسار الصحيح:</p>
              <ul className="text-xs space-y-1">
                <li>• <strong>لينوكس:</strong> افتح مدير الملفات واذهب للمجلد، انسخ المسار من شريط العنوان</li>
                <li>• <strong>ويندوز:</strong> افتح File Explorer، اذهب للمجلد، انسخ المسار من شريط العنوان</li>
                <li>• <strong>ماك:</strong> افتح Finder، اذهب للمجلد، انقر بالزر الأيمن واختر "Get Info"</li>
                <li>• <strong>مثال صحيح:</strong> /home/<USER>/Documents أو C:\Users\<USER>\Documents</li>
                <li>• <strong>للأقراص الخارجية:</strong> /media/disk أو D:\folder</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between pt-4 mt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            المسار: <span className="font-mono text-primary-600 dark:text-primary-400">{customPath || 'غير محدد'}</span>
          </div>
          <div className="flex space-x-3 space-x-reverse">
            <button
              onClick={handleCancel}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-md hover:bg-gray-50 dark:hover:bg-gray-500 transition-colors"
            >
              إلغاء
            </button>
            <button
              onClick={handleConfirm}
              disabled={!customPath.trim()}
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              تأكيد
            </button>
          </div>
        </div>
    </Modal>
  );
};

export default FolderPickerModal;