/**
 * مكون إدارة أمان الأجهزة
 */

import React, { useState, useEffect } from 'react';
import {
  FaUserShield,
  FaLock,
  FaUnlock,
  FaExclamationTriangle,
  FaCheck,
  FaTimes,
  FaNetworkWired
} from 'react-icons/fa';
import api from '../lib/axios';



interface BlockedDevice {
  id: number;
  device_id: string;
  client_ip: string;
  hostname: string;
  device_type: string;
  system: string;
  blocked_by: string;
  blocked_at: string;
  block_reason: string;
  access_count: number;
}

interface PendingDevice {
  id: number;
  device_id: string;
  client_ip: string;
  hostname: string;
  device_type: string;
  system: string;
  current_user?: string;
  requested_at: string;
  access_count: number;
  status: string;
}

const DeviceSecurityManager: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'blocked' | 'pending'>('blocked');
  const [blockedDevices, setBlockedDevices] = useState<BlockedDevice[]>([]);
  const [pendingDevices, setPendingDevices] = useState<PendingDevice[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // تحميل البيانات
  useEffect(() => {
    loadBlockedDevices();
    loadPendingDevices();
  }, []);



  const loadBlockedDevices = async () => {
    try {
      const response = await api.get('/api/device-security/blocked-devices');
      if (response.data.success) {
        setBlockedDevices(response.data.blocked_devices);
      }
    } catch (error) {
      console.error('خطأ في تحميل الأجهزة المحظورة:', error);
    }
  };

  const loadPendingDevices = async () => {
    try {
      const response = await api.get('/api/device-security/pending-devices');
      if (response.data.success) {
        setPendingDevices(response.data.pending_devices);
      }
    } catch (error) {
      console.error('خطأ في تحميل الأجهزة المعلقة:', error);
    }
  };



  const unblockDevice = async (deviceId: string) => {
    try {
      setIsLoading(true);
      await api.post(`/api/device-security/unblock-device/${deviceId}`);
      await loadBlockedDevices();
    } catch (error) {
      console.error('خطأ في إلغاء حظر الجهاز:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const approveDevice = async (deviceId: string) => {
    try {
      setIsLoading(true);
      await api.post(`/api/device-security/approve-device/${deviceId}`);
      await loadPendingDevices();
    } catch (error) {
      console.error('خطأ في الموافقة على الجهاز:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const rejectDevice = async (deviceId: string) => {
    try {
      setIsLoading(true);
      await api.post(`/api/device-security/reject-device/${deviceId}`);
      await loadPendingDevices();
      await loadBlockedDevices();
    } catch (error) {
      console.error('خطأ في رفض الجهاز:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('ar-LY');
  };

  return (
    <div className="space-y-6">
      {/* العنوان الرئيسي */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center gap-4">
          <div className="bg-red-100 dark:bg-red-900/30 p-3 rounded-xl">
            <FaUserShield className="text-2xl text-red-600 dark:text-red-400" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              إدارة الأجهزة المحظورة والمعلقة
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              مراجعة الأجهزة المحظورة والموافقة على الأجهزة الجديدة
            </p>
          </div>
        </div>
      </div>

      {/* التبويبات */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('blocked')}
              className={`py-4 px-2 border-b-2 font-medium text-sm ${
                activeTab === 'blocked'
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <FaLock className="inline ml-2" />
              الأجهزة المحظورة ({blockedDevices.length})
            </button>
            <button
              onClick={() => setActiveTab('pending')}
              className={`py-4 px-2 border-b-2 font-medium text-sm ${
                activeTab === 'pending'
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <FaExclamationTriangle className="inline ml-2" />
              في انتظار الموافقة ({pendingDevices.length})
            </button>
          </nav>
        </div>

        <div className="p-6">
          {/* تبويبة الأجهزة المحظورة */}
          {activeTab === 'blocked' && (
            <div className="space-y-4">
              {blockedDevices.length === 0 ? (
                <div className="text-center py-8">
                  <FaLock className="text-4xl text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 dark:text-gray-400">لا توجد أجهزة محظورة</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 gap-4">
                  {blockedDevices.map((device) => (
                    <div
                      key={device.id}
                      className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <FaNetworkWired className="text-red-600 dark:text-red-400" />
                          <div>
                            <h4 className="font-medium text-gray-900 dark:text-gray-100">
                              {device.hostname}
                            </h4>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {device.client_ip} • {device.device_type}
                            </p>
                            <p className="text-xs text-red-600 dark:text-red-400">
                              محظور بواسطة: {device.blocked_by} • {formatDate(device.blocked_at)}
                            </p>
                          </div>
                        </div>
                        <button
                          onClick={() => unblockDevice(device.device_id)}
                          disabled={isLoading}
                          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors disabled:opacity-50"
                        >
                          <FaUnlock className="inline ml-1" />
                          إلغاء الحظر
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* تبويبة الأجهزة المعلقة */}
          {activeTab === 'pending' && (
            <div className="space-y-4">
              {pendingDevices.length === 0 ? (
                <div className="text-center py-8">
                  <FaExclamationTriangle className="text-4xl text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 dark:text-gray-400">لا توجد أجهزة في انتظار الموافقة</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 gap-4">
                  {pendingDevices.map((device) => (
                    <div
                      key={device.id}
                      className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <FaNetworkWired className="text-yellow-600 dark:text-yellow-400" />
                          <div>
                            <h4 className="font-medium text-gray-900 dark:text-gray-100">
                              {device.hostname}
                            </h4>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {device.client_ip} • {device.device_type}
                            </p>
                            {device.current_user && (
                              <p className="text-sm text-primary-600 dark:text-primary-400">
                                👤 {device.current_user}
                              </p>
                            )}
                            <p className="text-xs text-yellow-600 dark:text-yellow-400">
                              طلب الوصول: {formatDate(device.requested_at)}
                            </p>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <button
                            onClick={() => approveDevice(device.device_id)}
                            disabled={isLoading}
                            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors disabled:opacity-50"
                          >
                            <FaCheck className="inline ml-1" />
                            موافقة
                          </button>
                          <button
                            onClick={() => rejectDevice(device.device_id)}
                            disabled={isLoading}
                            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors disabled:opacity-50"
                          >
                            <FaTimes className="inline ml-1" />
                            رفض
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DeviceSecurityManager;
