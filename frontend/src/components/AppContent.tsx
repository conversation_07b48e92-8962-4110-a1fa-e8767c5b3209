import React, { Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import LoadingSpinner from './LoadingSpinner';

// Lazy load pages
const Dashboard = React.lazy(() => import('../pages/Dashboard'));
const POS = React.lazy(() => import('../pages/POS'));
const ProductManagement = React.lazy(() => import('../pages/ProductManagement'));
const CreateProduct = React.lazy(() => import('../pages/CreateProduct'));
const Sales = React.lazy(() => import('../pages/Sales'));
const Users = React.lazy(() => import('../pages/Users'));
const Settings = React.lazy(() => import('../pages/Settings'));
const Receipt = React.lazy(() => import('../pages/Receipt'));
const Reports = React.lazy(() => import('../pages/Reports'));
const Customers = React.lazy(() => import('../pages/Customers'));
const Debts = React.lazy(() => import('../pages/Debts'));
const HelpCenter = React.lazy(() => import('../pages/HelpCenter'));
const CatalogManagement = React.lazy(() => import('../pages/CatalogManagement'));
const ImageManagement = React.lazy(() => import('../pages/ImageManagementPage'));
const WarrantyManagement = React.lazy(() => import('../pages/WarrantyManagement'));
// const WarehousesPage = React.lazy(() => import('../pages/WarehousesPage')); // Not used currently
const WarehouseManagement = React.lazy(() => import('../pages/WarehouseManagement'));
const BranchManagement = React.lazy(() => import('../pages/BranchManagement'));

// Test components
const TestEnhancedArabicEditor = React.lazy(() => import('./TestEnhancedArabicEditor'));
const TestArabicRichTextEditorWithButtons = React.lazy(() => import('../pages/TestArabicRichTextEditorWithButtons'));

// Admin Route component
const AdminRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, isAuthenticated } = useAuthStore();

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (user?.role !== 'admin') {
    return <Navigate to="/" replace />;
  }

  return <>{children}</>;
};

/**
 * مكون المحتوى الرئيسي للتطبيق
 * يحتوي على جميع الصفحات داخل Layout مستمر
 */
const AppContent: React.FC = () => {
  return (
    <Suspense fallback={<LoadingSpinner size="sm" message="تحميل الصفحة..." fullScreen />}>
      <Routes>
        {/* Dashboard Route */}
        <Route path="/" element={<Dashboard />} />

        {/* POS Route - بدون Layout منفصل */}
        <Route path="/pos" element={<POS />} />

        {/* Products Routes */}
        <Route path="/products" element={<ProductManagement />} />
        <Route path="/products/new" element={<CreateProduct />} />
        <Route path="/products/:id" element={<CreateProduct />} />

        {/* Catalog Management Routes */}
        <Route path="/categories" element={<CatalogManagement />} />
        <Route path="/brands" element={<CatalogManagement />} />
        <Route path="/units" element={<CatalogManagement />} />
        <Route path="/variant-attributes" element={<CatalogManagement />} />
        <Route path="/tax-types" element={<CatalogManagement />} />

        {/* Warehouse Management Routes - New Unified Page */}
        <Route path="/warehouse-management" element={<WarehouseManagement />} />
        <Route path="/warehouse-management/warehouses" element={<WarehouseManagement />} />
        <Route path="/warehouse-management/inventory" element={<WarehouseManagement />} />
        <Route path="/warehouse-management/movements" element={<WarehouseManagement />} />
        <Route path="/warehouse-management/transfers" element={<WarehouseManagement />} />
        <Route path="/warehouse-management/reports" element={<WarehouseManagement />} />

        {/* Branch Management Routes - Separate Page */}
        <Route path="/branch-management" element={<BranchManagement />} />
        <Route path="/branch-management/branch-warehouses" element={<BranchManagement />} />

        {/* Legacy Warehouse Routes - Redirect to new unified page */}
        <Route path="/warehouses" element={<WarehouseManagement />} />
        <Route path="/warehouse-inventory" element={<WarehouseManagement />} />
        <Route path="/warehouse-movements" element={<WarehouseManagement />} />
        <Route path="/transfer-requests" element={<WarehouseManagement />} />
        <Route path="/warehouse-reports" element={<WarehouseManagement />} />

        {/* Warranty Management Routes */}
        <Route path="/warranties" element={<WarrantyManagement />} />
        <Route path="/warranty-types" element={<WarrantyManagement />} />
        <Route path="/product-warranties" element={<WarrantyManagement />} />
        <Route path="/warranty-claims" element={<WarrantyManagement />} />
        <Route path="/warranty-reports" element={<WarrantyManagement />} />

        {/* Sales Routes */}
        <Route path="/sales" element={<Sales />} />
        <Route path="/sales/:id" element={<Sales />} />

        {/* Receipt Routes */}
        <Route path="/sales/:id/print" element={<Receipt />} />
        <Route path="/receipt/:id" element={<Receipt />} />

        {/* Reports Routes */}
        <Route path="/reports" element={<Reports />} />
        <Route path="/reports/:type" element={<Reports />} />

        {/* Customers Routes */}
        <Route path="/customers" element={<Customers />} />
        <Route path="/customers/:id" element={<Customers />} />

        {/* Debts Routes */}
        <Route path="/debts" element={<Debts />} />

        {/* Help Center Route */}
        <Route path="/help" element={<HelpCenter />} />

        {/* Image Management Route */}
        <Route path="/images" element={<ImageManagement />} />

        {/* Test Routes - للتطوير والاختبار */}
        <Route path="/test-arabic-editor" element={<TestEnhancedArabicEditor />} />
        <Route path="/test-arabic-editor-buttons" element={<TestArabicRichTextEditorWithButtons />} />

        {/* Admin Only Routes */}
        <Route
          path="/users"
          element={
            <AdminRoute>
              <Users />
            </AdminRoute>
          }
        />

        <Route
          path="/settings"
          element={
            <AdminRoute>
              <Settings />
            </AdminRoute>
          }
        />

        {/* Catch all route */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Suspense>
  );
};

export default AppContent;
