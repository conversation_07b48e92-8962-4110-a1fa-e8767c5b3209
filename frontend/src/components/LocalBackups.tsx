import React, { useState, useEffect } from 'react';
import { FaHdd, FaSync, FaTrash, FaUndo, FaCalendarAlt, FaDatabase, FaExclamationTriangle } from 'react-icons/fa';
import apiClient from '../lib/axios';
import { FormattedDateTime } from './FormattedDateTime';

interface LocalBackup {
  name: string;
  size: string;
  created_date: string;
  created_time: string;
  created_at?: string;
}

interface LocalBackupsProps {
  onError?: (error: string) => void;
  onSuccess?: (message: string) => void;
  onShowAllBackups?: () => void;
  onRestoreBackup?: (backupName: string) => void;
  onDeleteBackup?: (backupName: string) => void;
}

const LocalBackups: React.FC<LocalBackupsProps> = ({
  onError,
  onSuccess,
  onShowAllBackups,
  onRestoreBackup,
  onDeleteBackup
}) => {
  const [backups, setBackups] = useState<LocalBackup[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // جلب قائمة النسخ الاحتياطية المحلية
  const fetchLocalBackups = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiClient.get('/api/dashboard/backups');
      const data = response.data;

      setBackups(data.backups || []);
    } catch (error: any) {
      console.error('Error fetching local backups:', error);
      setError('فشل في جلب قائمة النسخ الاحتياطية المحلية');
      setBackups([]);
      onError?.('فشل في جلب قائمة النسخ الاحتياطية المحلية');
    } finally {
      setLoading(false);
    }
  };

  // حذف نسخة احتياطية محلية
  const deleteBackup = async (backupName: string) => {
    // استخدام callback إذا كان متوفر، وإلا استخدام الحذف المباشر
    if (onDeleteBackup) {
      onDeleteBackup(backupName);
      return;
    }

    if (!window.confirm(`هل أنت متأكد من حذف النسخة الاحتياطية "${backupName}"؟`)) {
      return;
    }

    try {
      setLoading(true);
      const response = await apiClient.delete(`/api/dashboard/backups/${backupName}`);
      const data = response.data;

      if (data.success) {
        onSuccess?.(`تم حذف النسخة الاحتياطية "${backupName}" بنجاح`);
        fetchLocalBackups(); // تحديث القائمة
      } else {
        onError?.(data.message || 'فشل في حذف النسخة الاحتياطية');
      }
    } catch (error: any) {
      console.error('Error deleting backup:', error);
      onError?.('فشل في حذف النسخة الاحتياطية');
    } finally {
      setLoading(false);
    }
  };

  // استعادة نسخة احتياطية
  const restoreBackup = (backupName: string) => {
    onRestoreBackup?.(backupName);
  };

  // تحديث البيانات
  const handleRefresh = async () => {
    await fetchLocalBackups();
  };

  // تحميل البيانات عند تحميل المكون
  useEffect(() => {
    fetchLocalBackups();
  }, []);

  // تنسيق التاريخ والوقت
  const getBackupDate = (backup: LocalBackup) => {
    if (backup.created_at) {
      return backup.created_at;
    }
    // fallback للتنسيق القديم
    return `${backup.created_date} ${backup.created_time}`;
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      {/* Header */}
      <div className="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <div className="bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 p-2 rounded-lg ml-3">
            <FaHdd className="text-xl" />
          </div>
          <div>
            <h3 className="font-bold text-lg text-gray-900 dark:text-gray-100">النسخ المحلية</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              النسخ الاحتياطية المحفوظة محلياً على الخادم
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2 space-x-reverse">
          <button
            onClick={onShowAllBackups}
            className="btn-outline-sm flex items-center"
          >
            <FaDatabase className="ml-2" />
            <span>عرض الكل</span>
          </button>
          <button
            onClick={handleRefresh}
            className="btn-outline-sm flex items-center"
            disabled={loading}
          >
            <FaSync className={`ml-2 ${loading ? 'animate-spin' : ''}`} />
            <span>تحديث</span>
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {loading && backups.length === 0 ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">جاري تحميل النسخ الاحتياطية...</p>
          </div>
        ) : error && backups.length === 0 ? (
          <div className="text-center py-8">
            <div className="bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 p-4 rounded-full mb-4 inline-block">
              <FaExclamationTriangle className="text-3xl" />
            </div>
            <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">خطأ في التحميل</h4>
            <p className="text-gray-600 dark:text-gray-400">{error}</p>
          </div>
        ) : backups.length === 0 ? (
          <div className="text-center py-8">
            <div className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 p-4 rounded-full mb-4 inline-block">
              <FaHdd className="text-3xl" />
            </div>
            <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">لا توجد نسخ احتياطية</h4>
            <p className="text-gray-600 dark:text-gray-400">قم بإنشاء نسخة احتياطية أولاً</p>
          </div>
        ) : (
          <div className="space-y-3">
            {backups.slice(0, 4).map((backup, index) => {
              return (
                <div
                  key={backup.name}
                  className="bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 rounded-lg p-3 hover:shadow-md transition-shadow duration-200"
                >
                  {/* Desktop Layout */}
                  <div className="hidden sm:flex items-center justify-between">
                    {/* File Info */}
                    <div className="flex-1">
                      <div className="flex items-center mb-1">
                        <FaHdd className="text-green-600 dark:text-green-400 ml-2 flex-shrink-0" />
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-0.5">
                            <h5 className="font-medium text-gray-900 dark:text-gray-100 break-all text-sm">
                              {backup.name}
                            </h5>
                            {index === 0 && (
                              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 flex-shrink-0">
                                أحدث
                              </span>
                            )}
                          </div>
                          <div className="flex items-center space-x-3 space-x-reverse">
                            <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                              <FaCalendarAlt className="ml-1 text-xs" />
                              <FormattedDateTime date={getBackupDate(backup)} showTime={true} />
                            </div>
                            <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                              <FaDatabase className="ml-1 text-xs" />
                              <span>{backup.size}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                    </div>

                    {/* Actions */}
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <button
                        onClick={() => restoreBackup(backup.name)}
                        className="btn-outline-sm flex items-center"
                        title="استعادة النسخة الاحتياطية"
                        disabled={loading}
                      >
                        <FaUndo className="text-sm" />
                      </button>
                      <button
                        onClick={() => deleteBackup(backup.name)}
                        className="btn-danger-sm flex items-center"
                        title="حذف النسخة الاحتياطية"
                        disabled={loading}
                      >
                        <FaTrash className="text-sm" />
                      </button>
                    </div>
                  </div>

                  {/* Mobile Layout */}
                  <div className="sm:hidden">
                    <div className="flex items-start mb-2">
                      <FaHdd className="text-green-600 dark:text-green-400 ml-2 mt-0.5 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h5 className="font-medium text-gray-900 dark:text-gray-100 break-all text-sm">
                            {backup.name}
                          </h5>
                          {index === 0 && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 flex-shrink-0">
                              أحدث
                            </span>
                          )}
                        </div>
                        <div className="space-y-0.5">
                          <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                            <FaCalendarAlt className="ml-1 text-xs flex-shrink-0" />
                            <span className="truncate">
                              <FormattedDateTime date={getBackupDate(backup)} showTime={true} />
                            </span>
                          </div>
                          <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                            <FaDatabase className="ml-1 text-xs flex-shrink-0" />
                            <span>{backup.size}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex justify-end space-x-2 space-x-reverse">
                      <button
                        onClick={() => restoreBackup(backup.name)}
                        className="btn-outline-sm flex items-center"
                        title="استعادة النسخة الاحتياطية"
                        disabled={loading}
                      >
                        <FaUndo className="ml-1 text-sm" />
                        <span>استعادة</span>
                      </button>
                      <button
                        onClick={() => deleteBackup(backup.name)}
                        className="btn-danger-sm flex items-center"
                        title="حذف النسخة الاحتياطية"
                        disabled={loading}
                      >
                        <FaTrash className="ml-1 text-sm" />
                        <span>حذف</span>
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}

            {/* رسالة عرض المزيد */}
            {backups.length > 4 && (
              <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 mt-4 rounded-lg border border-gray-200 dark:border-gray-600 text-center">
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  يتم عرض 4 نسخ من أصل {backups.length} نسخة احتياطية.{' '}
                  <button
                    onClick={onShowAllBackups}
                    className="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 font-medium"
                  >
                    عرض جميع النسخ
                  </button>
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default LocalBackups;
