import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import { useTheme } from '../contexts/ThemeContext';
import { useFullscreen } from '../hooks/useFullscreen';
import { useAutoUserActivity } from '../hooks/useUserActivity';
import AboutModal from './AboutModal';
import SystemAlerts from './SystemAlerts';
import SystemStatusIndicators from './SystemStatusIndicators';
import ScrollToTopButton from './ScrollToTopButton';
import { ChatHeaderButton } from './Chat';
import ChatNotificationManager from './Chat/ChatNotificationManager';
import {
  FaSignOutAlt,
  FaUserCircle,
  FaSun,
  FaMoon,
  FaBars,
  FaTimes,
  FaInfoCircle,
  FaExpand,
  FaCompress,
  FaQuestionCircle
} from 'react-icons/fa';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const { logout, user } = useAuthStore();
  const { toggleTheme, currentTheme } = useTheme();
  const { isFullscreen, toggleFullscreen } = useFullscreen();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [aboutModalOpen, setAboutModalOpen] = useState(false);

  // تفعيل تتبع نشاط المستخدم التلقائي
  useAutoUserActivity();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      {/* Header - simplified */}
      <header className="bg-white dark:bg-gray-800 sticky top-0 z-10 card-subtle-border">
        <div className="container mx-auto px-4 sm:px-6 py-3">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-4">
              <Link to="/" className="flex items-center">
                <span className="text-xl sm:text-2xl font-bold text-primary-600">
                  Smart<span className="text-secondary-800 dark:text-secondary-200">POS</span>
                </span>
              </Link>

              {/* System Status Indicators next to app name */}
              <div className="hidden sm:block">
                <SystemStatusIndicators />
              </div>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                aria-label={mobileMenuOpen ? 'إغلاق القائمة' : 'فتح القائمة'}
              >
                {mobileMenuOpen ? <FaTimes /> : <FaBars />}
              </button>
            </div>

            {/* Desktop menu */}
            <div className="hidden md:flex items-center gap-3">
              {/* System Alerts in Header */}
              <SystemAlerts showInHeader={true} />

              {/* Chat Button in Header */}
              <ChatHeaderButton />

              {/* Help Center Button */}
              <button
                onClick={() => navigate('/help')}
                className="p-2 rounded-full bg-gray-100 dark:bg-gray-700/50 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600/60 transition-colors"
                aria-label="مركز المساعدة"
                title="مركز المساعدة"
              >
                <FaQuestionCircle className="text-gray-600 dark:text-gray-400" />
              </button>

              {/* About Button */}
              <button
                onClick={() => setAboutModalOpen(true)}
                className="p-2 rounded-full bg-gray-100 dark:bg-gray-700/50 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600/60 transition-colors"
                aria-label="حول التطبيق"
                title="حول التطبيق"
              >
                <FaInfoCircle className="text-gray-600 dark:text-gray-400" />
              </button>

              {/* Fullscreen Toggle Button */}
              <button
                onClick={toggleFullscreen}
                className="p-2 rounded-full bg-gray-100 dark:bg-gray-700/50 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600/60 transition-colors"
                aria-label={isFullscreen ? 'الخروج من ملء الشاشة' : 'ملء الشاشة'}
                title={isFullscreen ? 'الخروج من ملء الشاشة' : 'ملء الشاشة'}
              >
                {isFullscreen ?
                  <FaCompress className="text-gray-600 dark:text-gray-400" /> :
                  <FaExpand className="text-gray-600 dark:text-gray-400" />
                }
              </button>

              {/* Theme Toggle Button */}
              <button
                onClick={toggleTheme}
                className={`p-2 rounded-full transition-colors ${
                  currentTheme === 'dark'
                    ? 'bg-amber-50 dark:bg-amber-900/20 text-amber-600 dark:text-amber-300 hover:bg-amber-100 dark:hover:bg-amber-800/30'
                    : 'bg-slate-100 dark:bg-slate-700/50 text-slate-600 dark:text-slate-300 hover:bg-slate-200 dark:hover:bg-slate-600/60'
                }`}
                aria-label={currentTheme === 'dark' ? 'التبديل إلى الوضع المضيء' : 'التبديل إلى الوضع المظلم'}
              >
                {currentTheme === 'dark' ?
                  <FaSun className="text-amber-500 dark:text-amber-400" /> :
                  <FaMoon className="text-slate-600 dark:text-slate-400" />
                }
              </button>

              <div className="bg-secondary-50 dark:bg-secondary-900 px-4 py-2 rounded-lg text-secondary-800 dark:text-secondary-100 font-medium flex items-center">
                <FaUserCircle className="ml-2 text-primary-600 dark:text-primary-400" />
                <span>{user?.full_name}</span>
              </div>
              <button
                onClick={handleLogout}
                className="bg-red-100 hover:bg-red-200 dark:bg-red-900/30 dark:hover:bg-red-900/40 text-red-700 dark:text-red-300 px-4 py-2 rounded-lg transition-colors duration-200 flex items-center font-medium shadow-sm hover:shadow-md"
              >
                <FaSignOutAlt className="ml-2 text-red-600 dark:text-red-400" />
                <span>تسجيل الخروج</span>
              </button>
            </div>
          </div>

          {/* Mobile menu dropdown */}
          {mobileMenuOpen && (
            <div className="md:hidden mt-3 pt-3 border-t border-gray-200 dark:border-gray-700 animate-slideDown">
              <div className="flex flex-col gap-3">
                {/* System Status Indicators in Mobile Menu */}
                <div className="animate-slideUp" style={{ animationDelay: '0.01s' }}>
                  <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm p-3">
                    <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">حالة النظام</div>
                    <SystemStatusIndicators />
                  </div>
                </div>

                {/* System Alerts in Mobile Menu */}
                <div className="animate-slideUp" style={{ animationDelay: '0.02s' }}>
                  <SystemAlerts showInSidebar={true} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm" />
                </div>

                {/* Chat Button in Mobile Menu */}
                <div className="animate-slideUp" style={{ animationDelay: '0.03s' }}>
                  <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm p-3">
                    <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المحادثة الفورية</div>
                    <ChatHeaderButton className="w-full" />
                  </div>
                </div>

                <div className="flex items-center justify-between animate-slideUp" style={{ animationDelay: '0.05s' }}>
                  <div className="flex items-center">
                    <FaUserCircle className="ml-2 text-primary-600 dark:text-primary-400" />
                    <span className="text-secondary-800 dark:text-secondary-100 font-medium truncate max-w-[150px]">{user?.full_name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => {
                        navigate('/help');
                        setMobileMenuOpen(false);
                      }}
                      className="p-2 rounded-full bg-gray-100 dark:bg-gray-700/50 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600/60 transition-colors"
                      aria-label="مركز المساعدة"
                      title="مركز المساعدة"
                    >
                      <FaQuestionCircle className="text-gray-600 dark:text-gray-400" />
                    </button>
                    <button
                      onClick={() => {
                        setAboutModalOpen(true);
                        setMobileMenuOpen(false);
                      }}
                      className="p-2 rounded-full bg-gray-100 dark:bg-gray-700/50 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600/60 transition-colors"
                      aria-label="حول التطبيق"
                      title="حول التطبيق"
                    >
                      <FaInfoCircle className="text-gray-600 dark:text-gray-400" />
                    </button>
                    <button
                      onClick={() => {
                        toggleFullscreen();
                        setMobileMenuOpen(false);
                      }}
                      className="p-2 rounded-full bg-gray-100 dark:bg-gray-700/50 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600/60 transition-colors"
                      aria-label={isFullscreen ? 'الخروج من ملء الشاشة' : 'ملء الشاشة'}
                      title={isFullscreen ? 'الخروج من ملء الشاشة' : 'ملء الشاشة'}
                    >
                      {isFullscreen ?
                        <FaCompress className="text-gray-600 dark:text-gray-400" /> :
                        <FaExpand className="text-gray-600 dark:text-gray-400" />
                      }
                    </button>
                    <button
                      onClick={toggleTheme}
                      className={`p-2 rounded-full transition-colors ${
                        currentTheme === 'dark'
                          ? 'bg-amber-50 dark:bg-amber-900/20 text-amber-600 dark:text-amber-300 hover:bg-amber-100 dark:hover:bg-amber-800/30'
                          : 'bg-slate-100 dark:bg-slate-700/50 text-slate-600 dark:text-slate-300 hover:bg-slate-200 dark:hover:bg-slate-600/60'
                      }`}
                      aria-label={currentTheme === 'dark' ? 'التبديل إلى الوضع المضيء' : 'التبديل إلى الوضع المظلم'}
                    >
                      {currentTheme === 'dark' ?
                        <FaSun className="text-amber-500 dark:text-amber-400" /> :
                        <FaMoon className="text-slate-600 dark:text-slate-400" />
                      }
                    </button>
                  </div>
                </div>
                <button
                  onClick={() => {
                    handleLogout();
                    setMobileMenuOpen(false);
                  }}
                  className="bg-red-100 hover:bg-red-200 dark:bg-red-900/30 dark:hover:bg-red-900/40 text-red-700 dark:text-red-300 px-4 py-2 rounded-lg transition-colors duration-200 flex items-center justify-center font-medium shadow-sm hover:shadow-md w-full animate-slideUp"
                  style={{ animationDelay: '0.1s' }}
                >
                  <FaSignOutAlt className="ml-2 text-red-600 dark:text-red-400" />
                  <span>تسجيل الخروج</span>
                </button>
              </div>
            </div>
          )}
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>

      {/* About Modal */}
      <AboutModal
        isOpen={aboutModalOpen}
        onClose={() => setAboutModalOpen(false)}
      />

      {/* Chat Notifications */}
      {user?.id && (
        <ChatNotificationManager
          onOpenChat={(senderId) => {
            console.log('🔔 Layout: طلب فتح محادثة مع المستخدم:', senderId);
            // إرسال حدث لفتح المحادثة مع المستخدم المحدد
            const event = new CustomEvent('openChatWithUser', {
              detail: { userId: senderId }
            });
            window.dispatchEvent(event);
          }}
        />
      )}

      {/* Scroll to Top Button */}
      <ScrollToTopButton />
    </div>
  );
};

export default Layout;