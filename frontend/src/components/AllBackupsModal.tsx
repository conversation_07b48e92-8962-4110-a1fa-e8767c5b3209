import React, { useState, useEffect } from 'react';
import { FaDatabase, FaUndo, FaTrash, FaSync, FaCalendarAlt, FaHdd } from 'react-icons/fa';
import Modal from './Modal';

interface BackupInfo {
  name: string;
  size: string;
  size_bytes: number;
  created_at: string;
  created_date: string;
  created_time: string;
}

interface AllBackupsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onRestore: (backupName: string) => void;
  onDelete: (backupName: string) => void;
}

const AllBackupsModal: React.FC<AllBackupsModalProps> = ({
  isOpen,
  onClose,
  onRestore,
  onDelete
}) => {
  const [backups, setBackups] = useState<BackupInfo[]>([]);
  const [loading, setLoading] = useState(false);

  // جلب قائمة النسخ الاحتياطية
  const fetchBackups = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/dashboard/backups');
      const data = await response.json();

      if (response.ok) {
        setBackups(data.backups || []);
      } else {
        console.error('Error fetching backups:', data);
        setBackups([]);
      }
    } catch (error) {
      console.error('Error fetching backups:', error);
      setBackups([]);
    } finally {
      setLoading(false);
    }
  };

  // جلب البيانات عند فتح النافذة
  useEffect(() => {
    if (isOpen) {
      fetchBackups();
    }
  }, [isOpen]);

  const handleRestore = (backupName: string) => {
    onRestore(backupName);
    onClose();
  };

  const handleDelete = async (backupName: string) => {
    onDelete(backupName);
    // تحديث القائمة بعد الحذف
    setTimeout(() => {
      fetchBackups();
    }, 1000); // انتظار ثانية واحدة للتأكد من اكتمال عملية الحذف
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="جميع النسخ الاحتياطية" size="lg">
      <div>
        {/* Header with refresh button */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 gap-3">
          <div className="flex items-center">
            <FaDatabase className="text-primary-600 dark:text-primary-400 ml-3 flex-shrink-0" />
            <div className="min-w-0">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                إدارة النسخ الاحتياطية
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                عرض وإدارة جميع النسخ الاحتياطية المتاحة
              </p>
            </div>
          </div>
          <button
            onClick={fetchBackups}
            className="btn-outline-sm flex items-center justify-center sm:justify-start"
            disabled={loading}
          >
            <FaSync className={`ml-2 ${loading ? 'animate-spin' : ''}`} />
            <span>تحديث</span>
          </button>
        </div>

        {/* Content */}
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-300">جاري تحميل النسخ الاحتياطية...</p>
          </div>
        ) : backups.length === 0 ? (
          <div className="text-center py-12">
            <FaDatabase className="mx-auto h-16 w-16 text-gray-400 dark:text-gray-500 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              لا توجد نسخ احتياطية
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              لم يتم العثور على أي نسخ احتياطية. قم بإنشاء نسخة احتياطية أولاً.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Stats */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div className="flex items-center justify-center sm:justify-start">
                  <div className="bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 p-2 rounded-lg ml-3">
                    <FaHdd className="text-lg" />
                  </div>
                  <div className="text-center sm:text-right">
                    <div className="text-lg font-bold text-gray-900 dark:text-gray-100">{backups.length}</div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">إجمالي النسخ</div>
                  </div>
                </div>
                <div className="flex items-center justify-center sm:justify-start">
                  <div className="bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 p-2 rounded-lg ml-3">
                    <FaDatabase className="text-lg" />
                  </div>
                  <div className="text-center sm:text-right">
                    <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
                      {backups.reduce((total, backup) => total + backup.size_bytes, 0) > 1024 * 1024
                        ? `${(backups.reduce((total, backup) => total + backup.size_bytes, 0) / (1024 * 1024)).toFixed(1)} MB`
                        : `${(backups.reduce((total, backup) => total + backup.size_bytes, 0) / 1024).toFixed(1)} KB`
                      }
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">الحجم الإجمالي</div>
                  </div>
                </div>
                <div className="flex items-center justify-center sm:justify-start">
                  <div className="bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 p-2 rounded-lg ml-3">
                    <FaCalendarAlt className="text-lg" />
                  </div>
                  <div className="text-center sm:text-right">
                    <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
                      {backups.length > 0 ? backups[0].created_date : '-'}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">آخر نسخة</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Backups List */}
            <div className="space-y-3">
              {backups.map((backup, index) => (
                <div
                  key={backup.name}
                  className="bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 rounded-lg p-3 hover:shadow-md transition-shadow duration-200"
                >
                  {/* Desktop Layout */}
                  <div className="hidden sm:flex items-center justify-between">
                    {/* Backup Info */}
                    <div className="flex-1">
                      <div className="flex items-center mb-1">
                        <FaHdd className="text-green-600 dark:text-green-400 ml-2 flex-shrink-0" />
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-0.5">
                            <h5 className="font-medium text-gray-900 dark:text-gray-100 break-all text-sm">
                              {backup.name}
                            </h5>
                            {index === 0 && (
                              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 flex-shrink-0">
                                أحدث
                              </span>
                            )}
                          </div>
                          <div className="flex items-center space-x-3 space-x-reverse">
                            <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                              <FaCalendarAlt className="ml-1 text-xs" />
                              <span>{backup.created_date} - {backup.created_time}</span>
                            </div>
                            <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                              <FaDatabase className="ml-1 text-xs" />
                              <span>{backup.size}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <button
                        onClick={() => handleRestore(backup.name)}
                        className="btn-outline-sm flex items-center"
                        title="استعادة النسخة الاحتياطية"
                      >
                        <FaUndo className="text-sm" />
                      </button>
                      <button
                        onClick={() => handleDelete(backup.name)}
                        className="btn-danger-sm flex items-center"
                        title="حذف النسخة الاحتياطية"
                      >
                        <FaTrash className="text-sm" />
                      </button>
                    </div>
                  </div>

                  {/* Mobile Layout */}
                  <div className="sm:hidden">
                    <div className="flex items-start mb-2">
                      <FaHdd className="text-green-600 dark:text-green-400 ml-2 mt-0.5 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h5 className="font-medium text-gray-900 dark:text-gray-100 break-all text-sm">
                            {backup.name}
                          </h5>
                          {index === 0 && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 flex-shrink-0">
                              أحدث
                            </span>
                          )}
                        </div>
                        <div className="space-y-0.5">
                          <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                            <FaCalendarAlt className="ml-1 text-xs flex-shrink-0" />
                            <span className="truncate">{backup.created_date} - {backup.created_time}</span>
                          </div>
                          <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                            <FaDatabase className="ml-1 text-xs flex-shrink-0" />
                            <span>{backup.size}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex justify-end space-x-2 space-x-reverse">
                      <button
                        onClick={() => handleRestore(backup.name)}
                        className="btn-outline-sm flex items-center"
                        title="استعادة النسخة الاحتياطية"
                      >
                        <FaUndo className="ml-1 text-sm" />
                        <span>استعادة</span>
                      </button>
                      <button
                        onClick={() => handleDelete(backup.name)}
                        className="btn-danger-sm flex items-center"
                        title="حذف النسخة الاحتياطية"
                      >
                        <FaTrash className="ml-1 text-sm" />
                        <span>حذف</span>
                      </button>
                    </div>
                  </div>


                </div>
              ))}
            </div>

            {/* Warning about limit */}
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3 mt-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-300">
                    حد النسخ الاحتياطية
                  </h3>
                  <p className="mt-1 text-sm text-yellow-700 dark:text-yellow-400">
                    يحتفظ النظام بحد أقصى 10 نسخ احتياطية. عند إنشاء النسخة الحادية عشرة، سيتم حذف أقدم نسخة تلقائياً.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="flex justify-end mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="btn-secondary"
          >
            إغلاق
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default AllBackupsModal;
