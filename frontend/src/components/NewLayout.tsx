import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useSidebarStore } from '../stores/sidebarStore';
import { useAutoUserActivity } from '../hooks/useUserActivity';
import { useAuthStore } from '../stores/authStore';
import Sidebar from './Sidebar';
import Topbar from './Topbar';

import ScrollToTopButton from './ScrollToTopButton';
import ChatNotificationManager from './Chat/ChatNotificationManager';

interface NewLayoutProps {
  children: React.ReactNode;
}

const NewLayout: React.FC<NewLayoutProps> = ({ children }) => {
  const location = useLocation();
  const { user } = useAuthStore();
  const { isOpen, setActiveMenuItem, setActiveSubMenuItem, menuItems } = useSidebarStore();
  
  // تفعيل تتبع نشاط المستخدم التلقائي
  useAutoUserActivity();

  // تحديث القائمة النشطة بناءً على المسار الحالي
  useEffect(() => {
    const currentPath = location.pathname;
    
    // البحث عن القائمة المناسبة
    for (const item of menuItems) {
      // التحقق من القائمة الرئيسية
      if (currentPath === item.path || (item.path !== '/' && currentPath.startsWith(item.path))) {
        setActiveMenuItem(item.id);
        
        // التحقق من القوائم الفرعية
        if (item.subItems) {
          for (const subItem of item.subItems) {
            if (currentPath === subItem.path || currentPath.startsWith(subItem.path)) {
              setActiveSubMenuItem(subItem.id);
              break;
            }
          }
        }
        break;
      }
      
      // التحقق من القوائم الفرعية فقط
      if (item.subItems) {
        for (const subItem of item.subItems) {
          if (currentPath === subItem.path || currentPath.startsWith(subItem.path)) {
            setActiveMenuItem(item.id);
            setActiveSubMenuItem(subItem.id);
            break;
          }
        }
      }
    }
  }, [location.pathname, menuItems, setActiveMenuItem, setActiveSubMenuItem]);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      {/* الشريط العلوي */}
      <Topbar />
      
      {/* المحتوى الرئيسي مع الشريط الجانبي */}
      <div className="flex flex-1 relative">
        {/* الشريط الجانبي */}
        <Sidebar />
        
        {/* منطقة المحتوى */}
        <main
          className={`
            flex-1 transition-all duration-300 ease-in-out
            ${isOpen ? 'lg:mr-64' : 'lg:mr-16'}
            min-h-0 pt-0
          `}
        >
          {/* حاوي المحتوى مع التمرير */}
          <div className="h-full overflow-auto">
            <div className="p-4 sm:p-6 lg:p-6">
              {children}
            </div>
          </div>
        </main>
      </div>

      {/* إشعارات المحادثة */}
      {user?.id && (
        <ChatNotificationManager
          onOpenChat={(senderId) => {
            console.log('🔔 NewLayout: طلب فتح محادثة مع المستخدم:', senderId);
            // إرسال حدث لفتح المحادثة مع المستخدم المحدد
            const event = new CustomEvent('openChatWithUser', {
              detail: { userId: senderId }
            });
            window.dispatchEvent(event);
          }}
        />
      )}

      {/* زر العودة للأعلى */}
      <ScrollToTopButton />
    </div>
  );
};

export default NewLayout;
