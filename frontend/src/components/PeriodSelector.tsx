/**
 * مكون اختيار الفترات الزمنية
 * يتيح للمستخدم اختيار فترة زمنية محددة مسبقاً (7 أيام، شهر، سنة، إلخ)
 * يطبق مبادئ البرمجة الكائنية والتصميم الموحد للنظام
 */

import React, { useState, useRef, useEffect } from 'react';
import {
  FiCalendar,
  FiChevronDown,
  FiClock,
  FiTrendingUp
} from 'react-icons/fi';

export interface PeriodOption {
  value: string;
  label: string;
  days: number;
  icon: React.ReactNode;
  description?: string;
}

interface PeriodSelectorProps {
  value: string;
  onChange: (period: string, days: number) => void;
  className?: string;
  disabled?: boolean;
  label?: string;
  customOptions?: PeriodOption[];
}

/**
 * خدمة إدارة الفترات الزمنية
 * تطبق مبادئ البرمجة الكائنية مع نمط Singleton
 */
class PeriodService {
  private static instance: PeriodService;

  private constructor() {}

  public static getInstance(): PeriodService {
    if (!PeriodService.instance) {
      PeriodService.instance = new PeriodService();
    }
    return PeriodService.instance;
  }

  /**
   * الحصول على الفترات الافتراضية
   */
  public getDefaultPeriods(): PeriodOption[] {
    return [
      {
        value: '7d',
        label: '7 أيام',
        days: 7,
        icon: <FiClock className="text-primary-600 dark:text-primary-400" />,
        description: 'آخر أسبوع'
      },
      {
        value: '30d',
        label: '30 يوم',
        days: 30,
        icon: <FiCalendar className="text-success-600 dark:text-success-400" />,
        description: 'آخر شهر'
      },
      {
        value: '90d',
        label: '90 يوم',
        days: 90,
        icon: <FiCalendar className="text-warning-600 dark:text-warning-400" />,
        description: 'آخر 3 أشهر'
      },
      {
        value: '180d',
        label: '180 يوم',
        days: 180,
        icon: <FiTrendingUp className="text-secondary-600 dark:text-secondary-400" />,
        description: 'آخر 6 أشهر'
      },
      {
        value: '365d',
        label: 'سنة',
        days: 365,
        icon: <FiCalendar className="text-indigo-600 dark:text-indigo-400" />,
        description: 'آخر سنة'
      }
    ];
  }

  /**
   * حساب تاريخ البداية بناءً على عدد الأيام
   */
  public calculateStartDate(days: number): string {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    return startDate.toISOString().split('T')[0];
  }

  /**
   * حساب تاريخ النهاية (اليوم الحالي)
   */
  public calculateEndDate(): string {
    return new Date().toISOString().split('T')[0];
  }
}

const PeriodSelector: React.FC<PeriodSelectorProps> = ({
  value,
  onChange,
  className = '',
  disabled = false,
  label = 'فترة التقرير',
  customOptions
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const periodService = PeriodService.getInstance();

  // استخدام الفترات المخصصة أو الافتراضية
  const periods = customOptions || periodService.getDefaultPeriods();

  // إغلاق القائمة عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const selectedPeriod = periods.find(period => period.value === value);

  const handlePeriodSelect = (period: PeriodOption) => {
    onChange(period.value, period.days);
    setIsOpen(false);
  };

  return (
    <div className={`relative ${className}`}>
      {/* Label */}
      {label && (
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {label}
        </label>
      )}

      {/* Selector Button */}
      <div ref={dropdownRef} className="relative">
        <button
          type="button"
          onClick={() => !disabled && setIsOpen(!isOpen)}
          disabled={disabled}
          className={`
            w-full bg-white dark:bg-gray-700 border-2 border-gray-300 dark:border-gray-600
            rounded-xl px-4 py-3 text-right transition-all duration-200 ease-in-out
            flex items-center justify-between
            ${disabled 
              ? 'opacity-50 cursor-not-allowed' 
              : 'hover:border-gray-400 dark:hover:border-gray-500 focus:outline-none focus:ring-4 focus:ring-primary-500/20 focus:border-primary-500'
            }
            ${isOpen ? 'border-primary-500 ring-4 ring-primary-500/20' : ''}
          `}
        >
          <div className="flex items-center">
            {selectedPeriod?.icon}
            <span className="mr-3 text-gray-900 dark:text-gray-100 font-medium">
              {selectedPeriod?.label || 'اختر الفترة'}
            </span>
          </div>
          <FiChevronDown 
            className={`text-gray-500 dark:text-gray-400 transition-transform duration-200 ${
              isOpen ? 'rotate-180' : ''
            }`} 
          />
        </button>

        {/* Dropdown Menu */}
        {isOpen && (
          <div className="absolute z-50 mt-2 w-full bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div className="py-1">
              {periods.map((period) => (
                <button
                  key={period.value}
                  type="button"
                  onClick={() => handlePeriodSelect(period)}
                  className={`
                    w-full text-right px-4 py-3 transition-colors duration-200
                    flex items-center hover:bg-gray-50 dark:hover:bg-gray-700
                    ${period.value === value 
                      ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300' 
                      : 'text-gray-700 dark:text-gray-300'
                    }
                  `}
                >
                  <div className="flex items-center flex-1">
                    {period.icon}
                    <div className="mr-3 flex-1">
                      <div className="font-medium">{period.label}</div>
                      {period.description && (
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          {period.description}
                        </div>
                      )}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PeriodSelector;
export { PeriodService };
