/**
 * مكون أيقونات الوحدات المختصرة
 * يعرض رموز الوحدات (K, M, B, T) بألوان مختلفة ومميزة
 */

import React from 'react';

// أنواع الوحدات المدعومة
export type UnitType = 'K' | 'M' | 'B' | 'T';

// واجهة خصائص المكون
interface UnitIconProps {
  /** نوع الوحدة */
  unit: UnitType;
  /** حجم الأيقونة (افتراضي: 'medium') */
  size?: 'small' | 'medium' | 'large';
  /** فئة CSS إضافية */
  className?: string;
}

// تحديد الألوان والأنماط لكل وحدة
const getUnitStyle = (unit: UnitType) => {
  switch (unit) {
    case 'K': // آلاف
      return {
        bgColor: 'bg-blue-100 dark:bg-blue-900/30',
        textColor: 'text-blue-600 dark:text-blue-400',
        borderColor: 'border-blue-200 dark:border-blue-800',
        name: 'آلا<PERSON>'
      };
    case 'M': // ملايين
      return {
        bgColor: 'bg-green-100 dark:bg-green-900/30',
        textColor: 'text-green-600 dark:text-green-400',
        borderColor: 'border-green-200 dark:border-green-800',
        name: 'ملايين'
      };
    case 'B': // مليارات
      return {
        bgColor: 'bg-purple-100 dark:bg-purple-900/30',
        textColor: 'text-purple-600 dark:text-purple-400',
        borderColor: 'border-purple-200 dark:border-purple-800',
        name: 'مليارات'
      };
    case 'T': // تريليونات
      return {
        bgColor: 'bg-red-100 dark:bg-red-900/30',
        textColor: 'text-red-600 dark:text-red-400',
        borderColor: 'border-red-200 dark:border-red-800',
        name: 'تريليونات'
      };
    default:
      return {
        bgColor: 'bg-gray-100 dark:bg-gray-900/30',
        textColor: 'text-gray-600 dark:text-gray-400',
        borderColor: 'border-gray-200 dark:border-gray-800',
        name: 'غير محدد'
      };
  }
};

// تحديد أحجام الأيقونة
const getSizeClasses = (size: 'small' | 'medium' | 'large') => {
  switch (size) {
    case 'small':
      return {
        container: 'w-6 h-6',
        text: 'text-xs'
      };
    case 'large':
      return {
        container: 'w-10 h-10',
        text: 'text-lg'
      };
    default: // medium
      return {
        container: 'w-8 h-8',
        text: 'text-sm'
      };
  }
};

const UnitIcon: React.FC<UnitIconProps> = ({
  unit,
  size = 'medium',
  className = ''
}) => {
  const unitStyle = getUnitStyle(unit);
  const sizeClasses = getSizeClasses(size);

  return (
    <span
      className={`
        inline-flex items-center justify-center
        ${sizeClasses.container}
        ${unitStyle.bgColor}
        ${unitStyle.textColor}
        ${unitStyle.borderColor}
        border-2 rounded-full
        ${sizeClasses.text}
        font-bold
        transition-all duration-200
        hover:scale-110 hover:shadow-md
        ${className}
      `}
      title={`${unitStyle.name} (${unit})`}
    >
      {unit}
    </span>
  );
};

export default UnitIcon;

/**
 * دالة مساعدة لتحديد نوع الوحدة حسب القيمة
 */
export const getUnitType = (value: number): UnitType | null => {
  if (value >= 1000000000000) return 'T'; // تريليون
  if (value >= 1000000000) return 'B'; // مليار
  if (value >= 1000000) return 'M'; // مليون
  if (value >= 1000) return 'K'; // ألف
  return null;
};

/**
 * دالة مساعدة لحساب القيمة المختصرة بدقة
 */
export const calculateCompactValue = (value: number): { compactValue: number; unit: UnitType | null } => {
  if (value >= 1000000000000) {
    return {
      compactValue: Math.floor((value / 1000000000000) * 10) / 10,
      unit: 'T'
    };
  }
  if (value >= 1000000000) {
    return {
      compactValue: Math.floor((value / 1000000000) * 10) / 10,
      unit: 'B'
    };
  }
  if (value >= 1000000) {
    return {
      compactValue: Math.floor((value / 1000000) * 10) / 10,
      unit: 'M'
    };
  }
  if (value >= 1000) {
    return {
      compactValue: Math.floor((value / 1000) * 10) / 10,
      unit: 'K'
    };
  }
  return {
    compactValue: value,
    unit: null
  };
};

/**
 * مكونات مبسطة لكل نوع وحدة
 */
export const KIcon: React.FC<Omit<UnitIconProps, 'unit'>> = (props) => (
  <UnitIcon {...props} unit="K" />
);

export const MIcon: React.FC<Omit<UnitIconProps, 'unit'>> = (props) => (
  <UnitIcon {...props} unit="M" />
);

export const BIcon: React.FC<Omit<UnitIconProps, 'unit'>> = (props) => (
  <UnitIcon {...props} unit="B" />
);

export const TIcon: React.FC<Omit<UnitIconProps, 'unit'>> = (props) => (
  <UnitIcon {...props} unit="T" />
);

/**
 * مكون ديناميكي يختار الأيقونة المناسبة حسب القيمة
 */
interface DynamicUnitIconProps {
  value: number;
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

export const DynamicUnitIcon: React.FC<DynamicUnitIconProps> = ({
  value,
  size = 'medium',
  className = ''
}) => {
  const unitType = getUnitType(value);
  
  if (!unitType) return null;
  
  return (
    <UnitIcon
      unit={unitType}
      size={size}
      className={className}
    />
  );
};
