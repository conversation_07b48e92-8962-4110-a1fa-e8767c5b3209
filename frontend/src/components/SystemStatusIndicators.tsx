/**
 * مؤشرات حالة النظام في الشريط العلوي
 */

import React, { useState, useEffect } from 'react';
import {
  FaWifi,
  FaDatabase,
  FaTachometerAlt,
  FaExclamationTriangle,
  FaTimes
} from 'react-icons/fa';
import { TopbarTooltip } from './ui';
import { appStateManager } from '../services/appStateManager';
import { chatWebSocketService } from '../services/chatWebSocketService';
import api from '../lib/axios';

interface SystemStatus {
  connection: 'connected' | 'disconnected' | 'warning';
  performance: 'good' | 'warning' | 'critical';
  database: 'healthy' | 'warning' | 'error';
}

const SystemStatusIndicators: React.FC = () => {
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    connection: 'connected', // نبدأ بحالة متصل افتراضياً
    performance: 'good',
    database: 'healthy'
  });


  const [isChecking, setIsChecking] = useState(false);

  useEffect(() => {
    let isComponentMounted = true;
    let checkTimeout: NodeJS.Timeout | null = null;

    const checkSystemStatus = async () => {
      // منع التداخل في الفحص
      if (isChecking || !isComponentMounted) {
        return;
      }

      setIsChecking(true);

      try {
        // فحص حالة الاتصال (فحص مباشر مع الخادم)
        let connectionStatus: 'connected' | 'disconnected' | 'warning' = 'disconnected';

        try {
          // فحص مباشر مع الخادم باستخدام endpoint بسيط
          const controller = new AbortController();
          const timeoutId = setTimeout(() => {
            if (!controller.signal.aborted) {
              controller.abort();
            }
          }, 5000); // تقليل timeout لتجنب التجمد

          const response = await fetch('/api/settings/public', {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json'
            },
            signal: controller.signal
          });

          clearTimeout(timeoutId);

          if (process.env.NODE_ENV === 'development') {
            console.log('✅ Server responded with status:', response.status);
          }

          // إذا وصلنا هنا، فالخادم يستجيب
          if (response.ok) {
            connectionStatus = 'connected';
          } else if (response.status >= 400 && response.status < 500) {
            // خطأ في الطلب لكن الخادم يستجيب (مثل 403, 404)
            connectionStatus = 'connected';
          } else if (response.status >= 500) {
            // خطأ في الخادم - يعتبر خادم متوقف للمستخدم
            connectionStatus = 'disconnected';
          } else {
            connectionStatus = 'warning';
          }
        } catch (error: any) {
          // تحليل نوع الخطأ لتحديد الحالة الصحيحة
          if (process.env.NODE_ENV === 'development') {
            console.warn('🔴 Connection check failed:', {
              name: error.name,
              message: error.message,
              type: typeof error,
              stack: error.stack?.substring(0, 200)
            });
          }

          // في جميع حالات الخطأ: الخادم متوقف
          connectionStatus = 'disconnected';
        }

        // فحص حالة الأداء (فوري)
        const appState = appStateManager.getState();
        let performanceStatus: 'good' | 'warning' | 'critical' = 'good';

        if (appState.performance.averageResponseTime > 5000) {
          performanceStatus = 'critical';
        } else if (appState.performance.averageResponseTime > 2000 || appState.activeRequests > 5) {
          performanceStatus = 'warning';
        }

        // تحديث الحالة فوراً للاتصال والأداء إذا كان المكون ما زال mounted
        if (isComponentMounted) {
          setSystemStatus(prev => ({
            ...prev,
            connection: connectionStatus,
            performance: performanceStatus
          }));
        }

        // فحص حالة قاعدة البيانات (غير متزامن) مع timeout
        try {
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 5000);

          const dbResponse = await api.get('/api/system/database/health', {
            signal: controller.signal
          });

          clearTimeout(timeoutId);
          const dbHealth = dbResponse.data;

          let databaseStatus: 'healthy' | 'warning' | 'error' = 'healthy';
          if (dbHealth.status === 'error' || dbHealth.connection === 'failed') {
            databaseStatus = 'error';
          } else if (dbHealth.status === 'warning' || dbHealth.needs_optimization) {
            databaseStatus = 'warning';
          }

          // تحديث حالة قاعدة البيانات إذا كان المكون ما زال mounted
          if (isComponentMounted) {
            setSystemStatus(prev => ({
              ...prev,
              database: databaseStatus
            }));
          }
        } catch (error) {
          if (isComponentMounted) {
            setSystemStatus(prev => ({
              ...prev,
              database: 'error'
            }));
          }
        }

      } catch (error) {
        console.error('Error checking system status:', error);
        if (isComponentMounted) {
          setSystemStatus({
            connection: 'disconnected',
            performance: 'critical',
            database: 'error'
          });
        }
      } finally {
        // تنظيف حالة الفحص
        if (isComponentMounted) {
          setIsChecking(false);
        }
      }
    };

    // فحص فوري
    checkSystemStatus();

    // فحص دوري كل 3 دقائق (تقليل الضغط على الخادم أكثر)
    const interval = setInterval(() => {
      if (isComponentMounted) {
        checkSystemStatus();
      }
    }, 180000); // 3 دقائق

    // مستمع لتغييرات حالة التطبيق (استجابة فورية)
    const listenerId = appStateManager.addListener(async () => {
      if (!isComponentMounted) return;

      // فحص سريع للأداء فقط (الاتصال يتم فحصه بشكل دوري)
      const appState = appStateManager.getState();

      let performanceStatus: 'good' | 'warning' | 'critical' = 'good';
      if (appState.performance.averageResponseTime > 5000) {
        performanceStatus = 'critical';
      } else if (appState.performance.averageResponseTime > 2000 || appState.activeRequests > 5) {
        performanceStatus = 'warning';
      }

      setSystemStatus(prev => ({
        ...prev,
        performance: performanceStatus,
        connection: appState.isConnected ? 'connected' : 'disconnected'
      }));
    }, 'high');

    // مستمع لتغييرات حالة الاتصال من خدمة المحادثة (استجابة فورية)
    const handleConnectionStatusChanged = (data: { isConnected: boolean; isConnecting: boolean }) => {
      if (!isComponentMounted) return;

      let connectionStatus: 'connected' | 'disconnected' | 'warning' = 'disconnected';
      if (data.isConnected) {
        connectionStatus = 'connected';
      } else if (data.isConnecting) {
        connectionStatus = 'warning';
      }

      setSystemStatus(prev => ({
        ...prev,
        connection: connectionStatus
      }));
    };

    chatWebSocketService.on('connection_status_changed', handleConnectionStatusChanged);

    return () => {
      isComponentMounted = false;
      clearInterval(interval);
      if (checkTimeout) {
        clearTimeout(checkTimeout);
      }
      appStateManager.removeListener(listenerId);
      chatWebSocketService.off('connection_status_changed', handleConnectionStatusChanged);
    };
  }, []);

  const getConnectionIcon = () => {
    switch (systemStatus.connection) {
      case 'connected':
        return <FaWifi className="text-green-600 dark:text-green-400 text-sm" />;
      case 'warning':
        return <FaWifi className="text-yellow-600 dark:text-yellow-400 text-sm" />;
      case 'disconnected':
        return <FaTimes className="text-red-600 dark:text-red-400 text-sm" />;
      default:
        return <FaTimes className="text-gray-600 dark:text-gray-400 text-sm" />;
    }
  };

  const getPerformanceIcon = () => {
    switch (systemStatus.performance) {
      case 'good':
        return <FaTachometerAlt className="text-green-600 dark:text-green-400 text-sm" />;
      case 'warning':
        return <FaTachometerAlt className="text-yellow-600 dark:text-yellow-400 text-sm" />;
      case 'critical':
        return <FaTachometerAlt className="text-red-600 dark:text-red-400 text-sm" />;
      default:
        return <FaTachometerAlt className="text-gray-600 dark:text-gray-400 text-sm" />;
    }
  };

  const getDatabaseIcon = () => {
    switch (systemStatus.database) {
      case 'healthy':
        return <FaDatabase className="text-green-600 dark:text-green-400 text-sm" />;
      case 'warning':
        return <FaDatabase className="text-yellow-600 dark:text-yellow-400 text-sm" />;
      case 'error':
        return <FaDatabase className="text-red-600 dark:text-red-400 text-sm" />;
      default:
        return <FaDatabase className="text-gray-600 dark:text-gray-400 text-sm" />;
    }
  };

  const getConnectionText = () => {
    switch (systemStatus.connection) {
      case 'connected': return 'متصل';
      case 'warning': return 'بطيء';
      case 'disconnected': return 'متوقف';
      default: return 'غير معروف';
    }
  };

  const getPerformanceText = () => {
    switch (systemStatus.performance) {
      case 'good': return 'جيد';
      case 'warning': return 'بطيء';
      case 'critical': return 'بطيء جداً';
      default: return 'غير معروف';
    }
  };

  const getDatabaseText = () => {
    switch (systemStatus.database) {
      case 'healthy': return 'سليمة';
      case 'warning': return 'تحتاج تحسين';
      case 'error': return 'خطأ';
      default: return 'غير معروف';
    }
  };

  return (
    <div className="flex items-center gap-2">
      {/* مؤشر الاتصال */}
      <TopbarTooltip
        text={`الاتصال: ${getConnectionText()}`}
        position="bottom"
        variant={systemStatus.connection === 'connected' ? 'success' : systemStatus.connection === 'warning' ? 'warning' : 'error'}
      >
        <button className="p-1.5 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
          {getConnectionIcon()}
        </button>
      </TopbarTooltip>

      {/* مؤشر الأداء */}
      <TopbarTooltip
        text={`الأداء: ${getPerformanceText()}`}
        position="bottom"
        variant={systemStatus.performance === 'good' ? 'success' : systemStatus.performance === 'warning' ? 'warning' : 'error'}
      >
        <button className="p-1.5 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
          {getPerformanceIcon()}
        </button>
      </TopbarTooltip>

      {/* مؤشر قاعدة البيانات */}
      <TopbarTooltip
        text={`قاعدة البيانات: ${getDatabaseText()}`}
        position="bottom"
        variant={systemStatus.database === 'healthy' ? 'success' : systemStatus.database === 'warning' ? 'warning' : 'error'}
      >
        <button className="p-1.5 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
          {getDatabaseIcon()}
        </button>
      </TopbarTooltip>

      {/* مؤشر الحالة العامة */}
      {(systemStatus.connection === 'disconnected' ||
        systemStatus.performance === 'critical' ||
        systemStatus.database === 'error') && (
        <div className="flex items-center gap-1 px-2 py-1 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-lg text-xs font-medium">
          <FaExclamationTriangle className="text-xs" />
          <span>مشكلة</span>
        </div>
      )}
    </div>
  );
};

export default SystemStatusIndicators;
