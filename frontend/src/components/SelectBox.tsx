import React, { useState, useEffect, useRef } from 'react';
import { FaChevronDown, FaCheck } from 'react-icons/fa';

interface Option {
  value: string;
  label: string;
}

interface SelectBoxProps {
  options: Option[];
  value: string;
  onChange: (value: string) => void;
  name: string;
  placeholder?: string;
  className?: string;
  label?: string;
  disabled?: boolean;
}

const SelectBox: React.FC<SelectBoxProps> = ({
  options,
  value,
  onChange,
  name,
  placeholder = 'اختر...',
  className = '',
  label,
  disabled = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedLabel, setSelectedLabel] = useState('');
  const selectRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Update selected label when value changes
  useEffect(() => {
    const option = options.find(opt => opt.value === value);
    setSelectedLabel(option ? option.label : placeholder);
  }, [value, options, placeholder]);

  // Handle option selection
  const handleSelect = (optionValue: string) => {
    onChange(optionValue);
    setIsOpen(false);
  };

  return (
    <div className="relative" ref={selectRef}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          {label}
        </label>
      )}
      <div className="relative">
        <button
          type="button"
          onClick={() => !disabled && setIsOpen(!isOpen)}
          className={`w-full border border-gray-300 dark:border-gray-600 rounded-lg py-2.5 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-right h-[42px] ${
            disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'
          } ${className}`}
          disabled={disabled}
        >
          <span className={`block truncate ${!value ? 'text-gray-500 dark:text-gray-400' : ''}`}>
            {selectedLabel}
          </span>
          <span className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <FaChevronDown className={`text-gray-400 dark:text-gray-500 transition-transform ${isOpen ? 'transform rotate-180' : ''}`} />
          </span>
        </button>

        {/* Hidden input for form submission */}
        <input type="hidden" name={name} value={value} />
      </div>

      {isOpen && (
        <div className="absolute z-50 mt-1 w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 max-h-60 overflow-auto custom-scrollbar">
          <ul className="py-1">
            {options.map((option) => (
              <li
                key={option.value}
                onClick={() => handleSelect(option.value)}
                className={`px-3 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center justify-between ${
                  option.value === value ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-300' : 'text-gray-900 dark:text-gray-100'
                }`}
              >
                <span className="block truncate">{option.label}</span>
                {option.value === value && (
                  <span className="text-primary-600 dark:text-primary-400">
                    <FaCheck className="h-4 w-4" />
                  </span>
                )}
              </li>
            ))}
            {options.length === 0 && (
              <li className="px-3 py-2 text-gray-500 dark:text-gray-400">
                لا توجد خيارات
              </li>
            )}
          </ul>
        </div>
      )}
    </div>
  );
};

export default SelectBox;
