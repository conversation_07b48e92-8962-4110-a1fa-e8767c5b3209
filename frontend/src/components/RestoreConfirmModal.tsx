import React from 'react';
import { FaUndo, FaExclamationTriangle, FaDatabase, FaCalendarAlt, FaHdd } from 'react-icons/fa';
import Modal from './Modal';

interface RestoreConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  backupInfo: {
    name: string;
    size: string;
    created_date: string;
    created_time: string;
  } | null;
  isLoading?: boolean;
}

const RestoreConfirmModal: React.FC<RestoreConfirmModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  backupInfo,
  isLoading = false
}) => {
  if (!backupInfo) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="تأكيد استعادة النسخة الاحتياطية" size="lg" zIndex="high">
      <div className="text-center">
        {/* Warning Icon */}
        <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/30 mb-6">
          <FaExclamationTriangle className="h-8 w-8 text-orange-600 dark:text-orange-400" />
        </div>

        {/* Warning Message */}
        <div className="mb-6">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">
            ⚠️ تحذير مهم
          </h4>
          <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
            هل أنت متأكد من استعادة النسخة الاحتياطية التالية؟
          </p>
        </div>

        {/* Backup Info Card */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6 text-right">
          <div className="grid grid-cols-1 gap-3">
            <div className="flex items-center">
              <FaDatabase className="text-primary-600 dark:text-primary-400 ml-3 flex-shrink-0" />
              <div className="flex-1">
                <p className="text-xs text-gray-500 dark:text-gray-400">اسم الملف</p>
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100 break-all">
                  {backupInfo.name}
                </p>
              </div>
            </div>

            <div className="flex items-center">
              <FaHdd className="text-success-600 dark:text-success-400 ml-3 flex-shrink-0" />
              <div className="flex-1">
                <p className="text-xs text-gray-500 dark:text-gray-400">حجم الملف</p>
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {backupInfo.size}
                </p>
              </div>
            </div>

            <div className="flex items-center">
              <FaCalendarAlt className="text-warning-600 dark:text-warning-400 ml-3 flex-shrink-0" />
              <div className="flex-1">
                <p className="text-xs text-gray-500 dark:text-gray-400">تاريخ الإنشاء</p>
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {backupInfo.created_date} - {backupInfo.created_time}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Warning Details */}
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
          <h5 className="text-sm font-semibold text-red-800 dark:text-red-300 mb-2">
            سيتم تنفيذ العمليات التالية:
          </h5>
          <ul className="text-xs text-red-700 dark:text-red-400 space-y-1 text-right">
            <li>• إنشاء نسخة احتياطية من البيانات الحالية تلقائياً</li>
            <li>• استبدال جميع البيانات الحالية بالنسخة المحددة</li>
            <li>• فقدان جميع التغييرات التي تمت بعد تاريخ هذه النسخة</li>
            <li>• إعادة تحميل النظام لتطبيق التغييرات</li>
          </ul>
          <p className="text-xs font-semibold text-red-800 dark:text-red-300 mt-3">
            هذا الإجراء لا يمكن التراجع عنه!
          </p>
        </div>

        {/* Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 sm:gap-3 justify-center">
          <button
            onClick={onClose}
            disabled={isLoading}
            className="btn-secondary flex items-center justify-center min-w-[140px]"
          >
            <span>إلغاء</span>
          </button>
          <button
            onClick={onConfirm}
            disabled={isLoading}
            className="btn-warning flex items-center justify-center min-w-[140px]"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                <span>جاري الاستعادة...</span>
              </>
            ) : (
              <>
                <FaUndo className="ml-2" />
                <span>تأكيد الاستعادة</span>
              </>
            )}
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default RestoreConfirmModal;
