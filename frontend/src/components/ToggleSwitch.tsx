import React from 'react';

interface ToggleSwitchProps {
  id: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  label: string;
  className?: string;
  size?: 'small' | 'medium' | 'large';
}

const ToggleSwitch: React.FC<ToggleSwitchProps> = ({
  id,
  checked,
  onChange,
  label,
  className = '',
  size = 'medium' // الحجم الافتراضي هو المتوسط حسب قواعد النظام
}) => {
  // تحديد أحجام المفتاح حسب الحجم المطلوب
  // تم تحديث قيم الحركة لتكون أكثر دقة
  // RTL: غير نشط = يمين (translate-x-0)، نشط = يسار (transformX)
  const sizeClasses = {
    small: {
      container: 'h-5 w-10', // 20px × 40px
      thumb: 'h-3 w-3',      // 12px × 12px
      translate: '-translate-x-5' // حركة 20px
    },
    medium: {
      container: 'h-6 w-12', // 24px × 48px
      thumb: 'h-4 w-4',      // 16px × 16px
      translate: '-translate-x-6' // حركة 24px
    },
    large: {
      container: 'h-7 w-14', // 28px × 56px
      thumb: 'h-5 w-5',      // 20px × 20px
      translate: '-translate-x-7' // حركة 28px
    }
  };

  const currentSize = sizeClasses[size];

  return (
    <div className={`flex items-center justify-between ${className}`}>
      <label className="inline-flex items-center cursor-pointer w-full justify-between group">
        {label && (
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-gray-100 transition-colors duration-200">
            {label}
          </span>
        )}
        <input
          type="checkbox"
          id={id}
          checked={checked}
          onChange={(e) => onChange(e.target.checked)}
          className="sr-only peer"
        />
        <div className={`
          relative inline-flex ${currentSize.container} shrink-0 cursor-pointer rounded-full
          transition-colors duration-200 ease-in-out focus:outline-none
          peer-focus:ring-2 peer-focus:ring-offset-2 peer-focus:ring-primary-500
          border-2
          ${checked
            ? 'bg-primary-600 border-primary-700'
            : 'bg-gray-200 border-gray-300 dark:bg-gray-700 dark:border-gray-500'
          }
        `}>
          <span
            className={`
              pointer-events-none inline-block ${currentSize.thumb} transform rounded-full
              bg-white shadow-md transition-transform duration-200 ease-in-out
              absolute top-0.5 right-0.5
              ${checked ? currentSize.translate : 'translate-x-0'}
            `}
          />
        </div>
      </label>
    </div>
  );
};

export default ToggleSwitch;
