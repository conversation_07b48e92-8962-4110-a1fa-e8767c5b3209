import React from 'react';
import EnhancedScrollbar from './EnhancedScrollbar';
import { useTheme } from '../contexts/ThemeContext';

/**
 * مكون اختبار شريط التمرير المحسن
 * يعرض أمثلة مختلفة لاستخدام شريط التمرير
 */
const ScrollbarTest: React.FC = () => {
  const { currentTheme } = useTheme();
  const isDark = currentTheme === 'dark';

  // محتوى طويل للاختبار
  const longContent = Array.from({ length: 50 }, (_, i) => (
    <div 
      key={i} 
      className={`p-4 mb-2 rounded-lg ${
        isDark ? 'bg-gray-700 text-gray-100' : 'bg-gray-100 text-gray-900'
      }`}
    >
      <h3 className="font-semibold mb-2">عنصر رقم {i + 1}</h3>
      <p>
        هذا نص تجريبي لاختبار شريط التمرير المحسن. يحتوي على محتوى كافي لإظهار 
        شريط التمرير والتأكد من أنه يعمل بالشكل المطلوب في الوضع المظلم والفاتح.
      </p>
    </div>
  ));

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold mb-6">اختبار شريط التمرير المحسن</h1>
      
      {/* اختبار شريط التمرير العادي */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">شريط التمرير العادي (CSS فقط)</h2>
        <div 
          className={`
            h-64 overflow-y-auto p-4 rounded-lg border-2
            ${isDark 
              ? 'bg-gray-800 border-gray-600' 
              : 'bg-white border-gray-300'
            }
            app-enhanced-scrollbar
          `}
        >
          {longContent}
        </div>
      </div>

      {/* اختبار شريط التمرير مع مكون EnhancedScrollbar */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">شريط التمرير المحسن (مكون)</h2>
        <div 
          className={`
            rounded-lg border-2 p-4
            ${isDark 
              ? 'bg-gray-800 border-gray-600' 
              : 'bg-white border-gray-300'
            }
          `}
        >
          <EnhancedScrollbar 
            maxHeight="16rem" 
            showScrollbar="hover"
            className="pr-2"
          >
            {longContent}
          </EnhancedScrollbar>
        </div>
      </div>

      {/* اختبار شريط التمرير دائم الظهور */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">شريط التمرير دائم الظهور</h2>
        <div 
          className={`
            rounded-lg border-2 p-4
            ${isDark 
              ? 'bg-gray-800 border-gray-600' 
              : 'bg-white border-gray-300'
            }
          `}
        >
          <EnhancedScrollbar 
            maxHeight="16rem" 
            showScrollbar="always"
            className="pr-2"
          >
            {longContent}
          </EnhancedScrollbar>
        </div>
      </div>

      {/* اختبار شريط التمرير الرفيع */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">شريط التمرير الرفيع</h2>
        <div 
          className={`
            h-64 overflow-y-auto p-4 rounded-lg border-2
            ${isDark 
              ? 'bg-gray-800 border-gray-600' 
              : 'bg-white border-gray-300'
            }
            enhanced-scrollbar-thin app-enhanced-scrollbar
          `}
        >
          {longContent}
        </div>
      </div>

      {/* معلومات الاختبار */}
      <div 
        className={`
          p-4 rounded-lg border-2
          ${isDark 
            ? 'bg-gray-700 border-gray-500 text-gray-100' 
            : 'bg-blue-50 border-blue-300 text-gray-900'
          }
        `}
      >
        <h3 className="font-semibold mb-2">معلومات الاختبار:</h3>
        <ul className="space-y-1 text-sm">
          <li>• الوضع الحالي: {isDark ? 'مظلم' : 'فاتح'}</li>
          <li>• شريط التمرير يجب أن يظهر عند التحويم أو التمرير</li>
          <li>• لا توجد أسهم في شريط التمرير</li>
          <li>• الألوان تتغير حسب الوضع المظلم/الفاتح</li>
          <li>• انتقالات سلسة عند التفاعل</li>
        </ul>
      </div>
    </div>
  );
};

export default ScrollbarTest;
