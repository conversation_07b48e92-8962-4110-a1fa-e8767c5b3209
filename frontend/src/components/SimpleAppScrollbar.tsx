import React, { useEffect } from 'react';
import { useTheme } from '../contexts/ThemeContext';

interface SimpleAppScrollbarProps {
  children: React.ReactNode;
}

/**
 * مكون شريط التمرير البسيط للتطبيق
 * نهج محسن يعتمد على CSS فقط بدون مكتبات خارجية
 * أداء أفضل وتوافق أكبر مع جميع المتصفحات
 */
const SimpleAppScrollbar: React.FC<SimpleAppScrollbarProps> = ({ children }) => {
  const { currentTheme } = useTheme();
  const isDark = currentTheme === 'dark';

  useEffect(() => {
    // تطبيق فئة شريط التمرير المحسن على الجسم
    document.body.classList.add('app-enhanced-scrollbar');
    
    // تطبيق الوضع المظلم أو الفاتح
    if (isDark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    return () => {
      // تنظيف عند إلغاء التحميل
      document.body.classList.remove('app-enhanced-scrollbar');
    };
  }, [isDark]);

  return (
    <div 
      className="w-full h-screen overflow-auto app-enhanced-scrollbar"
      style={{ 
        minHeight: '100vh',
        width: '100%'
      }}
    >
      {children}
    </div>
  );
};

export default SimpleAppScrollbar;
