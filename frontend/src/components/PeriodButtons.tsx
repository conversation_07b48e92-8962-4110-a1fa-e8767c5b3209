/**
 * مكون أزرار الفترات الزمنية
 * يتيح للمستخدم اختيار فترة زمنية من خيارات محددة مسبقاً
 * يطبق مبادئ البرمجة الكائنية والتصميم الموحد للنظام
 * يستخدم TopbarTooltip المحسن للتعامل مع مشاكل المواضع في الحاويات المعقدة
 */

import React from 'react';
import { TopbarTooltip } from './ui';

export interface PeriodOption {
  value: string;
  label: string;
  days: number;
}

interface PeriodButtonsProps {
  selectedPeriod: string;
  onPeriodChange: (period: string, days: number) => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  customPeriods?: PeriodOption[];
}

/**
 * خدمة إدارة الفترات الزمنية
 * تطبق مبادئ البرمجة الكائنية مع نمط Singleton
 */
class PeriodButtonsService {
  private static instance: PeriodButtonsService;

  private constructor() {}

  public static getInstance(): PeriodButtonsService {
    if (!PeriodButtonsService.instance) {
      PeriodButtonsService.instance = new PeriodButtonsService();
    }
    return PeriodButtonsService.instance;
  }

  /**
   * الحصول على الفترات الافتراضية
   */
  public getDefaultPeriods(): PeriodOption[] {
    return [
      { value: '7d', label: '7د', days: 7 },
      { value: '30d', label: '1ش', days: 30 },
      { value: '90d', label: '3ش', days: 90 },
      { value: '180d', label: '6ش', days: 180 },
      { value: '365d', label: '1س', days: 365 }
    ];
  }

  /**
   * الحصول على أنماط الأحجام
   */
  public getSizeStyles(size: 'sm' | 'md' | 'lg') {
    const styles = {
      sm: {
        container: 'p-0.5 rounded-lg',
        button: 'px-2 py-1 text-xs',
        separator: 'my-0.5'
      },
      md: {
        container: 'p-0.5 rounded-lg',
        button: 'px-3 py-1.5 text-xs',
        separator: 'my-1'
      },
      lg: {
        container: 'p-1 rounded-xl',
        button: 'px-4 py-2 text-sm',
        separator: 'my-1.5'
      }
    };

    return styles[size];
  }

  /**
   * حساب تاريخ البداية بناءً على عدد الأيام
   */
  public calculateStartDate(days: number): string {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    return startDate.toISOString().split('T')[0];
  }

  /**
   * حساب تاريخ النهاية (اليوم الحالي)
   */
  public calculateEndDate(): string {
    return new Date().toISOString().split('T')[0];
  }
}

const PeriodButtons: React.FC<PeriodButtonsProps> = ({
  selectedPeriod,
  onPeriodChange,
  className = '',
  size = 'md',
  customPeriods
}) => {
  const service = PeriodButtonsService.getInstance();
  const periods = customPeriods || service.getDefaultPeriods();
  const sizeStyles = service.getSizeStyles(size);

  const handlePeriodClick = (period: PeriodOption) => {
    onPeriodChange(period.value, period.days);
  };

  return (
    <div className={`flex bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-600 shadow-sm ${sizeStyles.container} ${className}`}>
      {periods.map((period, index, array) => (
        <React.Fragment key={period.value}>
          <TopbarTooltip
            text={`آخر ${period.days} يوم`}
            position="top"
            variant="default"
            size="sm"
            delay={300}
          >
            <button
              onClick={() => handlePeriodClick(period)}
              className={`${sizeStyles.button} font-medium transition-all duration-200 ease-in-out ${
                selectedPeriod === period.value
                  ? 'bg-primary-600 text-white shadow-sm hover:bg-primary-700'
                  : 'text-gray-600 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white hover:shadow-sm'
              } ${
                index === 0 ? 'rounded-r-md' :
                index === array.length - 1 ? 'rounded-l-md' : ''
              }`}
            >
              {period.label}
            </button>
          </TopbarTooltip>
          {index < array.length - 1 && (
            <div className={`w-px bg-gray-200 dark:bg-gray-600 ${sizeStyles.separator}`}></div>
          )}
        </React.Fragment>
      ))}
    </div>
  );
};

export default PeriodButtons;
export { PeriodButtonsService };

/**
 * أمثلة على الاستخدام:
 *
 * // الاستخدام الأساسي - مع التول تايب المخصص
 * <PeriodButtons
 *   selectedPeriod={selectedPeriod}
 *   onPeriodChange={(period, days) => {
 *     setSelectedPeriod(period);
 *     // تحديث البيانات بناءً على الفترة الجديدة
 *   }}
 * />
 *
 * // مع حجم مخصص - التول تايب يتكيف تلقائياً
 * <PeriodButtons
 *   selectedPeriod={selectedPeriod}
 *   onPeriodChange={handlePeriodChange}
 *   size="sm"
 *   className="ml-4"
 * />
 *
 * // مع فترات مخصصة - التول تايب يعرض عدد الأيام المناسب
 * <PeriodButtons
 *   selectedPeriod={selectedPeriod}
 *   onPeriodChange={handlePeriodChange}
 *   customPeriods={[
 *     { value: '1d', label: '1د', days: 1 },
 *     { value: '7d', label: '7د', days: 7 },
 *     { value: '30d', label: '1ش', days: 30 }
 *   ]}
 * />
 *
 * ملاحظة: يستخدم المكون الآن TopbarTooltip المحسن
 * بدلاً من التول تايب العادي للمتصفح لحل مشاكل المواضع في الحاويات المعقدة
 */
