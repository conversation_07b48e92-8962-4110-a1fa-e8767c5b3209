import { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { FiPackage, FiSearch, FiX } from 'react-icons/fi';
import api from '../lib/axios';

interface Product {
  id: number;
  name: string;
  barcode?: string;
  category?: string;
  brand?: string;
  price: number;
  stock_quantity: number;
  is_active: boolean;
}

interface ProductSelectorProps {
  selectedProduct: Product | null;
  onProductSelect: (product: Product | null) => void;
  className?: string;
}

export interface ProductSelectorRef {
  refreshProducts: () => void;
}

const ProductSelector = forwardRef<ProductSelectorRef, ProductSelectorProps>(({
  selectedProduct,
  onProductSelect,
  className = ''
}, ref) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    fetchProducts();
  }, []);

  // Expose refresh function to parent component
  useImperativeHandle(ref, () => ({
    refreshProducts: fetchProducts
  }));

  const fetchProducts = async (page = 1, isLoadMore = false, search = '') => {
    try {
      if (isLoadMore) {
        setLoadingMore(true);
      } else {
        setLoading(true);
        setCurrentPage(1);
        setHasMore(true);
      }

      const limit = 10;
      const response = await api.get('/api/products', {
        params: {
          page: page,
          limit: limit,
          search: search || searchTerm,
          is_active: true
        }
      });

      const newProducts = response.data || [];
      console.log(`ProductSelector: Fetched ${newProducts.length} products for page ${page}`);

      const hasMoreData = newProducts.length === limit;
      setHasMore(hasMoreData);

      if (isLoadMore) {
        setProducts(prev => [...prev, ...newProducts]);
        setCurrentPage(page);
      } else {
        setProducts(newProducts);
        setCurrentPage(1);
      }
    } catch (error) {
      console.error('Error fetching products:', error);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const loadMoreProducts = () => {
    if (hasMore && !loadingMore) {
      fetchProducts(currentPage + 1, true, searchTerm);
    }
  };

  // Handle search with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchProducts(1, false, searchTerm);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  // Function to highlight search term in text
  const highlightSearchTerm = (text: string, searchTerm: string) => {
    if (!searchTerm) return text;

    const regex = new RegExp(`(${searchTerm})`, 'gi');
    const parts = text.split(regex);

    return parts.map((part, index) =>
      regex.test(part) ? (
        <span key={index} className="bg-yellow-200 dark:bg-yellow-800 text-yellow-900 dark:text-yellow-100 px-1 rounded">
          {part}
        </span>
      ) : part
    );
  };

  // Handle scroll to load more products
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

    if (scrollHeight - scrollTop - clientHeight < 50) {
      loadMoreProducts();
    }
  };

  const handleProductSelect = (product: Product) => {
    onProductSelect(product);
    setIsDropdownOpen(false);
    setSearchTerm('');
  };

  const handleClearSelection = () => {
    onProductSelect(null);
    setSearchTerm('');
  };

  return (
    <div className={`relative ${className}`}>
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        المنتج *
      </label>

      {selectedProduct ? (
        // Selected product display
        <div className="flex items-center justify-between h-10 px-4 bg-primary-50 dark:bg-primary-900/20 border-2 border-primary-200 dark:border-primary-700 rounded-xl">
          <div className="flex items-center flex-1 min-w-0">
            <div className="flex-shrink-0 w-6 h-6 ml-1 mr-2 flex items-center justify-center">
              <div className="flex w-6 h-6 bg-primary-100 dark:bg-primary-900/30 rounded-full items-center justify-center">
                <FiPackage className="text-primary-600 dark:text-primary-400 text-xs" />
              </div>
            </div>
            <div className="flex-1 min-w-0 flex items-center">
              <span className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                {selectedProduct.name}
              </span>
              {selectedProduct.barcode && (
                <span className="text-xs text-gray-500 dark:text-gray-400 mr-2 flex-shrink-0">
                  - {selectedProduct.barcode}
                </span>
              )}
              <span className="text-xs text-green-600 dark:text-green-400 mr-2 flex-shrink-0">
                - {selectedProduct.price.toFixed(2)} د.ل
              </span>
            </div>
          </div>
          <button
            onClick={handleClearSelection}
            className="flex-shrink-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1"
            title="إلغاء الاختيار"
          >
            <FiX className="h-4 w-4" />
          </button>
        </div>
      ) : (
        // Product selection interface
        <div>
          <div className="relative">
            <input
              type="text"
              placeholder="ابحث عن منتج بالاسم أو الباركود..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setIsDropdownOpen(true);
              }}
              onFocus={() => setIsDropdownOpen(true)}
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              onKeyDown={(e) => {
                if (e.key === 'Escape') {
                  setSearchTerm('');
                  setIsDropdownOpen(false);
                } else if (e.key === 'Enter' && products.length > 0) {
                  handleProductSelect(products[0]);
                }
              }}
              className="w-full h-10 px-4 pr-10 pl-4 border-2 border-gray-300/60 dark:border-gray-600/40 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-4 focus:ring-primary-500/20 focus:border-primary-500 transition-all duration-200 ease-in-out"
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center">
              <FiSearch className="text-gray-400 dark:text-gray-500 h-4 w-4" />
            </div>
            {searchTerm && (
              <button
                onClick={() => {
                  setSearchTerm('');
                  setIsDropdownOpen(false);
                }}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <FiX className="h-4 w-4" />
              </button>
            )}
          </div>

          {/* Dropdown */}
          {isDropdownOpen && (
            <div className="absolute z-50 w-full mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-xl overflow-hidden">
              {/* Search results header */}
              {searchTerm && (
                <div className="px-4 py-2 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      نتائج البحث عن: "{searchTerm}"
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-500">
                      {products.length} نتيجة
                    </span>
                  </div>
                </div>
              )}
              
              {loading ? (
                <div className="p-6 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-3"></div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {searchTerm ? 'جاري البحث...' : 'جاري تحميل المنتجات...'}
                  </div>
                </div>
              ) : (
                <div
                  className="max-h-60 overflow-y-auto custom-scrollbar-thin"
                  onScroll={handleScroll}
                >
                  {products.length === 0 ? (
                    <div className="p-6 text-center">
                      <div className="text-gray-400 dark:text-gray-500 mb-2">
                        <FiSearch className="h-8 w-8 mx-auto mb-2" />
                      </div>
                      <div className="text-gray-500 dark:text-gray-400 font-medium">
                        {searchTerm ? 'لا توجد نتائج للبحث' : 'لا توجد منتجات'}
                      </div>
                    </div>
                  ) : (
                    <>
                      {products.map((product) => (
                        <button
                          key={product.id}
                          onClick={() => handleProductSelect(product)}
                          className="w-full px-4 py-3 text-right hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-200 dark:border-gray-700 last:border-b-0"
                        >
                          <div className="flex items-center w-full">
                            <div className="flex-shrink-0 w-8 h-8 ml-1 mr-3">
                              <div className="flex w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-full items-center justify-center">
                                <FiPackage className="text-gray-600 dark:text-gray-400 text-sm" />
                              </div>
                            </div>
                            <div className="flex-1">
                              <div className="text-sm font-medium text-gray-900 dark:text-gray-100 text-right">
                                {highlightSearchTerm(product.name, searchTerm)}
                              </div>
                              <div className="flex justify-between items-center">
                                {product.barcode && (
                                  <span className="text-xs text-gray-500 dark:text-gray-400">
                                    {highlightSearchTerm(product.barcode, searchTerm)}
                                  </span>
                                )}
                                <span className="text-xs text-green-600 dark:text-green-400">
                                  {product.price.toFixed(2)} د.ل
                                </span>
                              </div>
                            </div>
                          </div>
                        </button>
                      ))}
                      
                      {/* Load more indicator */}
                      {loadingMore && (
                        <div className="p-4 text-center">
                          <div className="animate-spin h-5 w-5 border-2 border-gray-300 border-t-primary-500 rounded-full mx-auto"></div>
                        </div>
                      )}
                    </>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Backdrop to close dropdown */}
      {isDropdownOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsDropdownOpen(false)}
        />
      )}
    </div>
  );
});

ProductSelector.displayName = 'ProductSelector';

export default ProductSelector;
