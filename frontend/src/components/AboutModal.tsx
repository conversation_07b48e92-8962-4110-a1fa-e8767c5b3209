import React, { useState, useEffect } from 'react';
import Modal from './Modal';
import api from '../lib/axios';
import {
  FaReact,
  FaPython,
  FaDatabase,
  FaShieldAlt,
  FaCode,
  FaHeart,
  FaGithub,
  FaEnvelope,
  FaPhone,
  FaCopyright,
  FaServer,
  FaMobile,
  FaDesktop,
  FaCloud,
  FaLock,
  FaUsers,
  FaChartLine,
  FaPrint,
  FaBarcode,
  FaLanguage,
  FaKey,
  FaCheck,
  FaExclamationTriangle,
  FaCertificate,
  FaCalendarAlt,
  FaInfoCircle,
  FaRocket
} from 'react-icons/fa';

interface AboutModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface LicenseInfo {
  license_key: string;
  license_status: string;
  license_expires: string;
  system_activated: string;
}

const AboutModal: React.FC<AboutModalProps> = ({ isOpen, onClose }) => {
  const currentYear = new Date().getFullYear();
  const [licenseInfo, setLicenseInfo] = useState<LicenseInfo>({
    license_key: '',
    license_status: '',
    license_expires: '',
    system_activated: ''
  });
  const [isLoading, setIsLoading] = useState(false);

  // جلب معلومات الترخيص عند فتح النافذة
  useEffect(() => {
    if (isOpen) {
      fetchLicenseInfo();
    }
  }, [isOpen]);

  const fetchLicenseInfo = async () => {
    setIsLoading(true);
    try {
      const response = await api.get('/api/settings');
      const settingsData = response.data.reduce((acc: Record<string, string>, curr: any) => {
        acc[curr.key] = curr.value;
        return acc;
      }, {});

      setLicenseInfo({
        license_key: settingsData.license_key || '',
        license_status: settingsData.license_status || 'غير محدد',
        license_expires: settingsData.license_expires || '',
        system_activated: settingsData.system_activated || 'false'
      });
    } catch (error) {
      console.error('Error fetching license info:', error);
      setLicenseInfo({
        license_key: '',
        license_status: 'غير متاح',
        license_expires: '',
        system_activated: 'false'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatLicenseKey = (key: string) => {
    if (!key) return 'غير محدد';
    if (key.length <= 8) return key;
    return key.substring(0, 4) + '****' + key.substring(key.length - 4);
  };

  const getLicenseStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'نشط':
        return 'text-green-600 dark:text-green-400';
      case 'منتهي':
      case 'غير نشط':
        return 'text-red-600 dark:text-red-400';
      case 'تجريبي':
        return 'text-yellow-600 dark:text-yellow-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getLicenseStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'نشط':
        return <FaCheck className="text-green-600 dark:text-green-400" />;
      case 'منتهي':
      case 'غير نشط':
        return <FaExclamationTriangle className="text-red-600 dark:text-red-400" />;
      case 'تجريبي':
        return <FaInfoCircle className="text-yellow-600 dark:text-yellow-400" />;
      default:
        return <FaKey className="text-gray-600 dark:text-gray-400" />;
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="حول SmartPOS"
      size="lg"
    >
      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm z-10 flex items-center justify-center rounded-lg">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 mb-2"></div>
            <span className="text-sm text-gray-600 dark:text-gray-400">جارٍ تحميل المعلومات...</span>
          </div>
        </div>
      )}

      {/* Scrollable Content Container */}
      <div className="max-h-[65vh] overflow-y-auto overflow-x-hidden px-4 py-4 pr-6 custom-scrollbar-auto">
        <div className="space-y-4">
          {/* معلومات التطبيق الأساسية */}
          <div className="text-center bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-primary-900/20 dark:to-secondary-900/20 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="flex justify-center items-center mb-4">
              <div className="bg-primary-600 dark:bg-primary-500 p-3 rounded-full mr-3">
                <FaRocket className="text-white text-xl" />
              </div>
              <div className="text-left">
                <h1 className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                  Smart<span className="text-secondary-600 dark:text-secondary-400">POS</span>
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-400">نظام نقاط البيع الذكي</p>
              </div>
            </div>

            <div className="flex items-center justify-center gap-3 mb-3">
              <span className="bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 px-3 py-1 rounded-full text-sm font-medium">
                الإصدار 1.0.0
              </span>
              <span className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-3 py-1 rounded-full text-sm font-medium">
                احترافي
              </span>
            </div>

            <p className="text-gray-600 dark:text-gray-400 text-sm">
              تطبيق ويب حديث للمتاجر والمطاعم مع واجهة عربية احترافية
            </p>
          </div>

          {/* المميزات الرئيسية */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <FaChartLine className="ml-2 text-primary-600 dark:text-primary-400" />
              المميزات الرئيسية
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <FaBarcode className="text-primary-600 dark:text-primary-400 ml-3" />
                <span className="text-gray-700 dark:text-gray-300 text-sm">نقطة البيع السريعة</span>
              </div>
              <div className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <FaDatabase className="text-primary-600 dark:text-primary-400 ml-3" />
                <span className="text-gray-700 dark:text-gray-300 text-sm">إدارة المخزون</span>
              </div>
              <div className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <FaPrint className="text-primary-600 dark:text-primary-400 ml-3" />
                <span className="text-gray-700 dark:text-gray-300 text-sm">طباعة الفواتير</span>
              </div>
              <div className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <FaUsers className="text-primary-600 dark:text-primary-400 ml-3" />
                <span className="text-gray-700 dark:text-gray-300 text-sm">إدارة العملاء</span>
              </div>
              <div className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <FaChartLine className="text-primary-600 dark:text-primary-400 ml-3" />
                <span className="text-gray-700 dark:text-gray-300 text-sm">التقارير والإحصائيات</span>
              </div>
              <div className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <FaLanguage className="text-primary-600 dark:text-primary-400 ml-3" />
                <span className="text-gray-700 dark:text-gray-300 text-sm">دعم اللغة العربية</span>
              </div>
            </div>
          </div>

          {/* معلومات الترخيص والتفعيل */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <FaCertificate className="ml-2 text-primary-600 dark:text-primary-400" />
              معلومات الترخيص والتفعيل
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* حالة النظام */}
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-800 dark:text-gray-200 flex items-center text-sm">
                    <FaRocket className="ml-2 text-primary-600 dark:text-primary-400" />
                    حالة النظام
                  </h4>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    licenseInfo.system_activated === 'true'
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                      : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                  }`}>
                    {licenseInfo.system_activated === 'true' ? 'مفعل' : 'غير مفعل'}
                  </span>
                </div>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  {licenseInfo.system_activated === 'true'
                    ? 'النظام مفعل ويعمل بكامل الميزات'
                    : 'النظام غير مفعل - قم بتفعيله من الإعدادات'}
                </p>
              </div>

              {/* حالة الترخيص */}
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-800 dark:text-gray-200 flex items-center text-sm">
                    {getLicenseStatusIcon(licenseInfo.license_status)}
                    <span className="mr-2">حالة الترخيص</span>
                  </h4>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${getLicenseStatusColor(licenseInfo.license_status)}`}>
                    {licenseInfo.license_status || 'غير محدد'}
                  </span>
                </div>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  {licenseInfo.license_status === 'نشط'
                    ? 'الترخيص نشط وصالح للاستخدام'
                    : 'يرجى التحقق من صحة الترخيص'}
                </p>
              </div>

              {/* مفتاح الترخيص */}
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-2 flex items-center text-sm">
                  <FaKey className="ml-2 text-primary-600 dark:text-primary-400" />
                  مفتاح الترخيص
                </h4>
                <div className="bg-white dark:bg-gray-800 p-2 rounded border">
                  <code className="text-xs font-mono text-gray-700 dark:text-gray-300 break-all">
                    {formatLicenseKey(licenseInfo.license_key)}
                  </code>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  يمكن تعديله من الإعدادات
                </p>
              </div>

              {/* تاريخ انتهاء الترخيص */}
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-2 flex items-center text-sm">
                  <FaCalendarAlt className="ml-2 text-primary-600 dark:text-primary-400" />
                  تاريخ الانتهاء
                </h4>
                <div className="bg-white dark:bg-gray-800 p-2 rounded border">
                  <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                    {licenseInfo.license_expires || 'غير محدد'}
                  </span>
                </div>
                {licenseInfo.license_expires && (
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {new Date(licenseInfo.license_expires) > new Date()
                      ? 'الترخيص صالح'
                      : 'انتهت الصلاحية'}
                  </p>
                )}
              </div>
            </div>

            {/* تنبيه الترخيص */}
            {licenseInfo.license_status !== 'نشط' && (
              <div className="mt-4 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-700 rounded-lg p-3">
                <div className="flex items-center">
                  <FaExclamationTriangle className="text-orange-600 dark:text-orange-400 ml-2 flex-shrink-0" />
                  <div>
                    <h5 className="font-medium text-orange-800 dark:text-orange-300 text-sm">تنبيه الترخيص</h5>
                    <p className="text-xs text-orange-700 dark:text-orange-400">
                      للحصول على كامل الميزات، يرجى تفعيل الترخيص من الإعدادات.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* التقنيات المستخدمة */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <FaCode className="ml-2 text-primary-600 dark:text-primary-400" />
              التقنيات المستخدمة
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* الواجهة الأمامية */}
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-3 flex items-center text-sm">
                  <FaDesktop className="ml-2" />
                  الواجهة الأمامية
                </h4>
                <div className="space-y-2">
                  <div className="flex items-center text-xs text-blue-700 dark:text-blue-300">
                    <FaReact className="ml-2" />
                    React 18 + TypeScript
                  </div>
                  <div className="flex items-center text-xs text-blue-700 dark:text-blue-300">
                    <FaMobile className="ml-2" />
                    TailwindCSS + Responsive
                  </div>
                  <div className="flex items-center text-xs text-blue-700 dark:text-blue-300">
                    <FaRocket className="ml-2" />
                    Vite + Zustand
                  </div>
                </div>
              </div>

              {/* الخادم الخلفي */}
              <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                <h4 className="font-medium text-green-800 dark:text-green-300 mb-3 flex items-center text-sm">
                  <FaServer className="ml-2" />
                  الخادم الخلفي
                </h4>
                <div className="space-y-2">
                  <div className="flex items-center text-xs text-green-700 dark:text-green-300">
                    <FaPython className="ml-2" />
                    FastAPI + Python 3.8+
                  </div>
                  <div className="flex items-center text-xs text-green-700 dark:text-green-300">
                    <FaDatabase className="ml-2" />
                    PostgreSQL + SQLAlchemy
                  </div>
                  <div className="flex items-center text-xs text-green-700 dark:text-green-300">
                    <FaCloud className="ml-2" />
                    Uvicorn + RESTful API
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* الأمان والحماية */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <FaShieldAlt className="ml-2 text-green-600 dark:text-green-400" />
              الأمان والحماية
            </h3>
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className="flex items-center text-green-700 dark:text-green-300 text-sm">
                  <FaLock className="ml-2" />
                  <span>مصادقة JWT آمنة</span>
                </div>
                <div className="flex items-center text-green-700 dark:text-green-300 text-sm">
                  <FaShieldAlt className="ml-2" />
                  <span>تشفير كلمات المرور</span>
                </div>
                <div className="flex items-center text-green-700 dark:text-green-300 text-sm">
                  <FaDatabase className="ml-2" />
                  <span>حماية قاعدة البيانات</span>
                </div>
                <div className="flex items-center text-green-700 dark:text-green-300 text-sm">
                  <FaUsers className="ml-2" />
                  <span>نظام صلاحيات متقدم</span>
                </div>
              </div>
            </div>
          </div>

          {/* معلومات الدعم */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <FaEnvelope className="ml-2 text-primary-600 dark:text-primary-400" />
              الدعم والتواصل
            </h3>
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className="flex items-center text-gray-700 dark:text-gray-300 text-sm">
                  <FaEnvelope className="ml-2 text-primary-600 dark:text-primary-400" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center text-gray-700 dark:text-gray-300 text-sm">
                  <FaPhone className="ml-2 text-primary-600 dark:text-primary-400" />
                  <span>+966 50 123 4567</span>
                </div>
                <div className="flex items-center text-gray-700 dark:text-gray-300 text-sm">
                  <FaGithub className="ml-2 text-primary-600 dark:text-primary-400" />
                  <span>github.com/smartpos</span>
                </div>
                <div className="flex items-center text-gray-700 dark:text-gray-300 text-sm">
                  <FaCode className="ml-2 text-primary-600 dark:text-primary-400" />
                  <span>docs.smartpos.com</span>
                </div>
              </div>
            </div>
          </div>

          {/* معلومات المطور */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <FaCode className="ml-2 text-primary-600 dark:text-primary-400" />
              معلومات المطور
            </h3>
            <div className="bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-primary-900/20 dark:to-secondary-900/20 p-4 rounded-lg">
              <div className="text-center">
                <p className="text-gray-700 dark:text-gray-300 mb-2 text-sm">
                  تم تطوير هذا النظام بواسطة فريق SmartPOS
                </p>
                <p className="text-xs text-gray-600 dark:text-gray-400 mb-3">
                  فريق متخصص في تطوير حلول نقاط البيع الذكية
                </p>
                <div className="flex justify-center items-center text-primary-600 dark:text-primary-400 text-sm">
                  <span>صنع بـ</span>
                  <FaHeart className="mx-2 text-red-500" />
                  <span>للمجتمع الليبي</span>
                </div>
              </div>
            </div>
          </div>

          {/* معلومات حقوق الطبع والنشر */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <FaCopyright className="ml-2 text-primary-600 dark:text-primary-400" />
              حقوق الطبع والنشر
            </h3>
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <div className="text-center">
                <p className="text-gray-700 dark:text-gray-300 mb-2 text-sm">
                  هذا النظام مرخص تحت رخصة MIT
                </p>
                <p className="text-xs text-gray-600 dark:text-gray-400 mb-3">
                  يمكن استخدامه وتعديله وتوزيعه بحرية
                </p>
                <div className="flex justify-center items-center text-xs text-gray-600 dark:text-gray-400">
                  <FaCopyright className="ml-1" />
                  <span>{currentYear} SmartPOS. جميع الحقوق محفوظة</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default AboutModal;
