import React from 'react';
import { useAuthStore } from '../stores/authStore';
import WelcomeManImage from '../assets/images/Welcome_Man.png';
import SmartTipsBubble from './SmartTipsBubble';
import { useSmartTips } from '../hooks/useSmartTips';

/**
 * مكون حاوية الترحيب بالمستخدم - مع تأثيرات بصرية جميلة ونصائح ذكية
 * يعرض رسالة ترحيب مع صورة رجل يرحب ونصائح تفاعلية ذكية
 * الصورة تكون داخل الحاوية من الأسفل وخارجة من الأعلى
 *
 * التصميم:
 * - تدرج لوني خفيف يبدأ من جهة الصورة (من اليمين إلى اليسار)
 * - تأثيرات نقاط متحركة خلف الصورة
 * - تأثيرات ضوئية وتدرجات لونية جميلة
 * - خط نقش علوي وسفلي للتأثير المميز
 * - دعم كامل للوضع المظلم والمضيء
 * - تصميم نظيف وأنيق بدون حدود
 * - نصائح ذكية تفاعلية تظهر كأنها من الرجل في الصورة
 */
export const WelcomeContainer: React.FC = () => {
  const { user } = useAuthStore();

  // استخدام النصائح الذكية
  const {
    currentTip,
    isLoading,
    closeTip,
    refreshTip
  } = useSmartTips({
    autoRefresh: true,
    refreshInterval: 300, // تحديث كل 5 دقائق لتوفير الموارد
    categories: ['welcome', 'productivity', 'business', 'motivation'],
    enabled: true
  });

  // إزالة console.log لتوفير الموارد

  // تحديد رسالة الترحيب حسب الوقت
  const getWelcomeMessage = () => {
    const hour = new Date().getHours();
    
    if (hour < 12) {
      return 'صباح الخير';
    } else if (hour < 17) {
      return 'مساء الخير';
    } else {
      return 'مساء الخير';
    }
  };

  const welcomeMessage = getWelcomeMessage();
  const userName = user?.full_name || user?.username || 'المستخدم';

  return (
    <div className="relative mb-8" style={{ overflow: 'visible' }}>
      {/* الحاوية الرئيسية للترحيب مع تدرج لوني بسيط من جهة الصورة */}
      <div className="relative rounded-xl p-4 sm:p-5 bg-gradient-to-l from-primary-50/40 via-primary-50/20 to-white dark:from-primary-900/20 dark:via-primary-900/10 dark:to-gray-800" style={{ overflow: 'visible', boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)' }}>

        {/* خط نقش علوي للتأثير المميز */}
        <div className="absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-primary-300/30 to-transparent dark:from-transparent dark:via-primary-600/20 dark:to-transparent"></div>

        {/* المحتوى الرئيسي */}
        <div className="relative flex items-center justify-between">
          {/* النص الترحيبي */}
          <div className="flex-1 pr-6">
            <h2 className="text-2xl sm:text-3xl font-bold mb-2">
              <span className="text-primary-600 dark:text-primary-400">{welcomeMessage}</span>، <span className="text-gray-900 dark:text-gray-100">{userName}</span>!
            </h2>
            <p className="text-secondary-600 dark:text-secondary-300 text-lg mb-1">
              مرحباً بك في <span className="text-gray-900 dark:text-white font-semibold">لوحة التحكم</span>
            </p>
            <p className="text-secondary-500 dark:text-secondary-400 text-sm">
              نتمنى لك يوماً مليئاً بالإنجازات والنجاح
            </p>
          </div>



          {/* حاوية الصورة مع التأثيرات خلف الصورة الشفافة */}
          <div className="relative flex-shrink-0" style={{ zIndex: 10 }}>

            {/* النصائح الذكية - بجانب الصورة من اليسار داخل الحاوية */}
            {isLoading && (
              <div
                className="absolute z-40"
                style={{
                  top: '-10px',
                  left: '150px',
                  minWidth: 'max-content',
                  maxWidth: '140px'
                }}
              >
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-600 px-2 py-1.5 flex items-center gap-2">
                  <div className="animate-spin w-3 h-3 border border-primary-500 border-t-transparent rounded-full"></div>
                  <span className="text-xs text-gray-600 dark:text-gray-400">جاري التحميل...</span>
                </div>
              </div>
            )}

            {currentTip && !isLoading && (
              <div
                className="absolute z-40"
                style={{
                  top: '-10px',
                  left: '150px',
                  minWidth: 'max-content',
                  maxWidth: '140px'
                }}
              >
                <SmartTipsBubble
                  tip={currentTip}
                  onClose={closeTip}
                  showAnimation={true}
                  className="smart-tip-floating"
                />
              </div>
            )}

            {/* التأثيرات المتحركة الواضحة خلف منتصف الصورة الشفافة */}
            <div className="absolute" style={{
              bottom: '-20px',
              left: '50%',
              transform: 'translateX(-50%)',
              width: '140px',
              height: '180px',
              zIndex: 5
            }}>

              {/* هالة ضوئية خفيفة ومتناسقة في المنتصف */}
              <div
                className="absolute animate-gentle-glow"
                style={{
                  top: '45%',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: '45px',
                  height: '45px',
                  background: 'radial-gradient(circle, rgba(59, 130, 246, 0.08) 0%, rgba(59, 130, 246, 0.04) 60%, transparent 100%)',
                  filter: 'blur(20px)',
                  borderRadius: '50%',
                  animationDelay: '0s'
                }}
              />

              {/* تأثير التنفس الضوئي الخفيف */}
              <div
                className="absolute animate-breathing-glow"
                style={{
                  top: '42%',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: '35px',
                  height: '35px',
                  background: 'radial-gradient(circle, rgba(147, 197, 253, 0.1) 0%, rgba(147, 197, 253, 0.05) 60%, transparent 100%)',
                  filter: 'blur(18px)',
                  borderRadius: '50%',
                  animationDelay: '1.5s'
                }}
              />

              {/* فقاعات طبيعية تبدو كفقاعات حقيقية في كلا الوضعين */}
              <div
                className="absolute w-4 h-4 animate-floating-dots"
                style={{
                  top: '25%',
                  left: '25%',
                  background: `
                    radial-gradient(circle at 30% 30%,
                      rgba(255, 255, 255, 0.8) 0%,
                      rgba(255, 255, 255, 0.4) 15%,
                      rgba(59, 130, 246, 0.15) 40%,
                      rgba(59, 130, 246, 0.08) 70%,
                      transparent 100%
                    )
                  `,
                  filter: 'blur(0.3px)',
                  borderRadius: '50%',
                  animationDelay: '0.5s',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  boxShadow: '0 2px 8px rgba(59, 130, 246, 0.15)'
                }}
              />

              <div
                className="absolute w-3 h-3 animate-floating-dots"
                style={{
                  top: '65%',
                  left: '75%',
                  background: `
                    radial-gradient(circle at 35% 25%,
                      rgba(255, 255, 255, 0.75) 0%,
                      rgba(255, 255, 255, 0.35) 18%,
                      rgba(147, 197, 253, 0.18) 45%,
                      rgba(147, 197, 253, 0.1) 75%,
                      transparent 100%
                    )
                  `,
                  filter: 'blur(0.2px)',
                  borderRadius: '50%',
                  animationDelay: '1.8s',
                  border: '1px solid rgba(255, 255, 255, 0.15)',
                  boxShadow: '0 2px 6px rgba(147, 197, 253, 0.18)'
                }}
              />

              <div
                className="absolute w-3.5 h-3.5 animate-floating-dots"
                style={{
                  top: '30%',
                  left: '80%',
                  background: `
                    radial-gradient(circle at 25% 35%,
                      rgba(255, 255, 255, 0.7) 0%,
                      rgba(255, 255, 255, 0.3) 20%,
                      rgba(96, 165, 250, 0.2) 50%,
                      rgba(96, 165, 250, 0.12) 80%,
                      transparent 100%
                    )
                  `,
                  filter: 'blur(0.25px)',
                  borderRadius: '50%',
                  animationDelay: '2.5s',
                  border: '1px solid rgba(255, 255, 255, 0.18)',
                  boxShadow: '0 2px 7px rgba(96, 165, 250, 0.2)'
                }}
              />

              <div
                className="absolute w-2.5 h-2.5 animate-floating-dots"
                style={{
                  top: '70%',
                  left: '20%',
                  background: `
                    radial-gradient(circle at 40% 30%,
                      rgba(255, 255, 255, 0.7) 0%,
                      rgba(255, 255, 255, 0.3) 22%,
                      rgba(59, 130, 246, 0.16) 55%,
                      rgba(59, 130, 246, 0.08) 85%,
                      transparent 100%
                    )
                  `,
                  filter: 'blur(0.2px)',
                  borderRadius: '50%',
                  animationDelay: '3.2s',
                  border: '1px solid rgba(255, 255, 255, 0.15)',
                  boxShadow: '0 2px 5px rgba(59, 130, 246, 0.18)'
                }}
              />

              {/* فقاعات إضافية طبيعية */}
              <div
                className="absolute w-2.5 h-2.5 animate-floating-dots"
                style={{
                  top: '20%',
                  left: '60%',
                  background: `
                    radial-gradient(circle at 30% 25%,
                      rgba(255, 255, 255, 0.65) 0%,
                      rgba(255, 255, 255, 0.25) 25%,
                      rgba(147, 197, 253, 0.15) 60%,
                      rgba(147, 197, 253, 0.08) 85%,
                      transparent 100%
                    )
                  `,
                  filter: 'blur(0.15px)',
                  borderRadius: '50%',
                  animationDelay: '4s',
                  border: '1px solid rgba(255, 255, 255, 0.12)',
                  boxShadow: '0 1px 4px rgba(147, 197, 253, 0.2)'
                }}
              />

              <div
                className="absolute w-2 h-2 animate-floating-dots"
                style={{
                  top: '80%',
                  left: '50%',
                  background: `
                    radial-gradient(circle at 35% 30%,
                      rgba(255, 255, 255, 0.6) 0%,
                      rgba(255, 255, 255, 0.2) 30%,
                      rgba(59, 130, 246, 0.12) 65%,
                      rgba(59, 130, 246, 0.06) 90%,
                      transparent 100%
                    )
                  `,
                  filter: 'blur(0.1px)',
                  borderRadius: '50%',
                  animationDelay: '4.8s',
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                  boxShadow: '0 1px 3px rgba(59, 130, 246, 0.15)'
                }}
              />

              {/* فقاعة عادية متناسقة مع الباقي */}
              <div
                className="absolute w-3 h-3 animate-floating-dots"
                style={{
                  top: '38%',
                  left: '45%',
                  background: `
                    radial-gradient(circle at 30% 30%,
                      rgba(255, 255, 255, 0.7) 0%,
                      rgba(255, 255, 255, 0.3) 20%,
                      rgba(59, 130, 246, 0.15) 50%,
                      rgba(59, 130, 246, 0.08) 80%,
                      transparent 100%
                    )
                  `,
                  borderRadius: '50%',
                  animationDelay: '2.2s',
                  border: '1px solid rgba(255, 255, 255, 0.15)',
                  boxShadow: '0 2px 6px rgba(59, 130, 246, 0.15)',
                  filter: 'blur(0.2px)'
                }}
              />

            </div>

            {/* الحاوية المقطوعة للصورة */}
            <div
              className="relative w-36 h-24 rounded-lg"
              style={{
                zIndex: 5,
                background: 'transparent',
                overflow: 'visible'
              }}
            />

            {/* الصورة الشفافة PNG - موضوعة فوق التأثيرات */}
            <img
              src={WelcomeManImage}
              alt="رجل يرحب"
              className="absolute object-contain transition-all duration-500 hover:scale-105"
              style={{
                bottom: '-20px',
                left: '50%',
                transform: 'translateX(-50%)',
                width: '140px',
                height: '180px',
                zIndex: 20,
                objectPosition: 'center bottom',
                filter: 'drop-shadow(0 8px 16px rgba(59, 130, 246, 0.1))'
              }}
            />



            {/* تأثير إضافي ناعم تحت الصورة مباشرة */}
            <div
              className="absolute pointer-events-none animate-breathing-glow"
              style={{
                bottom: '-25px',
                left: '50%',
                transform: 'translateX(-50%)',
                width: '100px',
                height: '30px',
                background: 'radial-gradient(ellipse at center, rgba(59, 130, 246, 0.3) 0%, rgba(147, 197, 253, 0.15) 40%, transparent 80%)',
                filter: 'blur(15px)',
                zIndex: 2,
                animationDelay: '2s'
              }}
            />

            {/* ظل أرضي ناعم */}
            <div
              className="absolute pointer-events-none"
              style={{
                bottom: '-35px',
                left: '50%',
                transform: 'translateX(-50%)',
                width: '80px',
                height: '12px',
                background: 'radial-gradient(ellipse at center, rgba(0, 0, 0, 0.06) 0%, rgba(0, 0, 0, 0.03) 50%, transparent 100%)',
                filter: 'blur(4px)',
                zIndex: 0,
                opacity: 0.7
              }}
            />
          </div>
        </div>

        {/* خط زخرفي بسيط في الأسفل */}
        <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-primary-300/40 to-transparent dark:from-transparent dark:via-primary-600/30 dark:to-transparent"></div>
      </div>

      {/* زر اختبار مؤقت - للتطوير فقط */}
      {/*{process.env.NODE_ENV === 'development' && (
        <div className="mt-4 flex justify-center">
          <button
            onClick={refreshTip}
            className="px-4 py-2 bg-primary-600 text-white rounded-lg text-sm hover:bg-primary-700 transition-colors"
          >
            اختبار النصائح الذكية
          </button>
        </div>
      )}*/}

    </div>
  );
};

export default WelcomeContainer;
