import React from 'react';

interface RadioOption {
  value: string;
  label: string;
  description?: string;
  icon?: React.ReactNode;
}

interface RadioButtonProps {
  name: string;
  options: RadioOption[];
  value: string;
  onChange: (value: string) => void;
  className?: string;
  layout?: 'vertical' | 'horizontal' | 'grid';
  size?: 'sm' | 'md' | 'lg';
}

const RadioButton: React.FC<RadioButtonProps> = ({
  name,
  options,
  value,
  onChange,
  className = '',
  layout = 'vertical',
  size = 'md'
}) => {
  const getLayoutClasses = () => {
    switch (layout) {
      case 'horizontal':
        return 'flex flex-wrap gap-4';
      case 'grid':
        return 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3';
      default:
        return 'space-y-3';
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'p-2 text-sm';
      case 'lg':
        return 'p-4 text-base';
      default:
        return 'p-3 text-sm';
    }
  };

  return (
    <div className={`${getLayoutClasses()} ${className}`}>
      {options.map((option) => (
        <label
          key={option.value}
          className={`
            relative cursor-pointer rounded-lg border-2 transition-all duration-200
            ${getSizeClasses()}
            ${value === option.value
              ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
              : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300'
            }
          `}
        >
          <input
            type="radio"
            name={name}
            value={option.value}
            checked={value === option.value}
            onChange={(e) => onChange(e.target.value)}
            className="sr-only"
          />
          
          <div className="flex items-start">
            {/* Radio Circle */}
            <div className={`
              flex-shrink-0 w-4 h-4 rounded-full border-2 mr-3 mt-0.5 transition-all duration-200
              ${value === option.value
                ? 'border-primary-500 bg-primary-500'
                : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700'
              }
            `}>
              {value === option.value && (
                <div className="w-full h-full rounded-full bg-white scale-50"></div>
              )}
            </div>

            {/* Content */}
            <div className="flex-1">
              <div className="flex items-center">
                {option.icon && (
                  <span className="ml-2">{option.icon}</span>
                )}
                <span className="font-medium">{option.label}</span>
              </div>
              
              {option.description && (
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {option.description}
                </p>
              )}
            </div>
          </div>
        </label>
      ))}
    </div>
  );
};

export default RadioButton;
