import React from 'react';

interface SmallButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'outline';
  size?: 'xs' | 'sm' | 'md';
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
  title?: string;
}

const SmallButton: React.FC<SmallButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'sm',
  disabled = false,
  loading = false,
  className = '',
  type = 'button',
  title
}) => {
  // Get base classes
  const getBaseClasses = () => {
    return 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';
  };

  // Get size classes
  const getSizeClasses = () => {
    switch (size) {
      case 'xs':
        return 'px-2 py-1 text-xs h-6 gap-1';
      case 'sm':
        return 'px-3 py-1.5 text-xs h-8 gap-2';
      case 'md':
        return 'px-4 py-2 text-sm h-10 gap-2';
      default:
        return 'px-3 py-1.5 text-xs h-8 gap-2';
    }
  };

  // Get variant classes
  const getVariantClasses = () => {
    switch (variant) {
      case 'primary':
        return 'bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500 border border-primary-600 hover:border-primary-700';
      case 'secondary':
        return 'bg-gray-600 hover:bg-gray-700 text-white focus:ring-gray-500 border border-gray-600 hover:border-gray-700';
      case 'success':
        return 'bg-green-600 hover:bg-green-700 text-white focus:ring-green-500 border border-green-600 hover:border-green-700';
      case 'danger':
        return 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500 border border-red-600 hover:border-red-700';
      case 'warning':
        return 'bg-yellow-600 hover:bg-yellow-700 text-white focus:ring-yellow-500 border border-yellow-600 hover:border-yellow-700';
      case 'info':
        return 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500 border border-blue-600 hover:border-blue-700';
      case 'outline':
        return 'bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 focus:ring-gray-500 border border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500';
      default:
        return 'bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500 border border-primary-600 hover:border-primary-700';
    }
  };

  const baseClasses = getBaseClasses();
  const sizeClasses = getSizeClasses();
  const variantClasses = getVariantClasses();

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || loading}
      title={title}
      className={`${baseClasses} ${sizeClasses} ${variantClasses} ${className}`}
    >
      {loading && (
        <svg className="animate-spin h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      )}
      {children}
    </button>
  );
};

export default SmallButton;
