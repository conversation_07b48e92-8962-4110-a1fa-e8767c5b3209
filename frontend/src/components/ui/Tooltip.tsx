import React, { useState, useRef, useEffect, ReactNode } from 'react';

export type TooltipPosition = 'top' | 'bottom' | 'left' | 'right' | 'top-start' | 'top-end' | 'bottom-start' | 'bottom-end';
export type TooltipVariant = 'default' | 'dark' | 'light' | 'primary' | 'success' | 'warning' | 'error';
export type TooltipSize = 'sm' | 'md' | 'lg';

interface TooltipProps {
  children: ReactNode;
  content: ReactNode;
  position?: TooltipPosition;
  variant?: TooltipVariant;
  size?: TooltipSize;
  disabled?: boolean;
  delay?: number;
  hideDelay?: number;
  showArrow?: boolean;
  className?: string;
  contentClassName?: string;
  maxWidth?: string;
  offset?: number;
  interactive?: boolean;
  trigger?: 'hover' | 'click' | 'focus' | 'manual';
  visible?: boolean;
  onVisibilityChange?: (visible: boolean) => void;
}

const Tooltip: React.FC<TooltipProps> = ({
  children,
  content,
  position = 'top',
  variant = 'default',
  size = 'md',
  disabled = false,
  delay = 300,
  hideDelay = 100,
  showArrow = true,
  className = '',
  contentClassName = '',
  maxWidth = '200px',
  offset = 8,
  interactive = false,
  trigger = 'hover',
  visible,
  onVisibilityChange
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position_, setPosition_] = useState(position);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const hideTimeoutRef = useRef<NodeJS.Timeout>();
  const triggerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);

  // التحكم في الرؤية
  const controlledVisible = visible !== undefined ? visible : isVisible;

  // تنظيف المؤقتات
  const clearTimeouts = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = undefined;
    }
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
      hideTimeoutRef.current = undefined;
    }
  };

  // إظهار التلميح
  const showTooltip = () => {
    if (disabled) return;
    
    clearTimeouts();
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true);
      onVisibilityChange?.(true);
    }, delay);
  };

  // إخفاء التلميح
  const hideTooltip = () => {
    clearTimeouts();
    hideTimeoutRef.current = setTimeout(() => {
      setIsVisible(false);
      onVisibilityChange?.(false);
    }, hideDelay);
  };

  // معالجات الأحداث
  const handleMouseEnter = () => {
    if (trigger === 'hover') showTooltip();
  };

  const handleMouseLeave = () => {
    if (trigger === 'hover') hideTooltip();
  };

  const handleClick = () => {
    if (trigger === 'click') {
      if (controlledVisible) {
        hideTooltip();
      } else {
        showTooltip();
      }
    }
  };

  const handleFocus = () => {
    if (trigger === 'focus') showTooltip();
  };

  const handleBlur = () => {
    if (trigger === 'focus') hideTooltip();
  };

  // تنظيف المؤقتات عند إلغاء التحميل
  useEffect(() => {
    return () => clearTimeouts();
  }, []);

  // حساب موضع التلميح التلقائي
  useEffect(() => {
    if (!controlledVisible || !triggerRef.current || !tooltipRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const tooltipRect = tooltipRef.current.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };

    let newPosition = position;

    // التحقق من المساحة المتاحة وتعديل الموضع إذا لزم الأمر
    switch (position) {
      case 'top':
        if (triggerRect.top - tooltipRect.height - offset < 0) {
          newPosition = 'bottom';
        }
        break;
      case 'bottom':
        if (triggerRect.bottom + tooltipRect.height + offset > viewport.height) {
          newPosition = 'top';
        }
        break;
      case 'left':
        if (triggerRect.left - tooltipRect.width - offset < 0) {
          newPosition = 'right';
        }
        break;
      case 'right':
        if (triggerRect.right + tooltipRect.width + offset > viewport.width) {
          newPosition = 'left';
        }
        break;
    }

    setPosition_(newPosition);
  }, [controlledVisible, position, offset]);

  // أنماط المتغيرات
  const getVariantStyles = () => {
    const variants = {
      default: 'tooltip-default',
      dark: 'tooltip-default',
      light: 'tooltip-light',
      primary: 'tooltip-primary',
      success: 'tooltip-success',
      warning: 'tooltip-warning',
      error: 'tooltip-error'
    };
    return variants[variant];
  };

  // أنماط الأحجام
  const getSizeStyles = () => {
    const sizes = {
      sm: 'tooltip-sm',
      md: 'tooltip-md',
      lg: 'tooltip-lg'
    };
    return sizes[size];
  };

  // أنماط المواضع
  const getPositionStyles = () => {
    const positions = {
      top: 'bottom-full left-1/2 transform -translate-x-1/2 mb-2',
      bottom: 'top-full left-1/2 transform -translate-x-1/2 mt-2',
      left: 'right-full top-1/2 transform -translate-y-1/2 mr-2',
      right: 'left-full top-1/2 transform -translate-y-1/2 ml-2',
      'top-start': 'bottom-full left-0 mb-2',
      'top-end': 'bottom-full right-0 mb-2',
      'bottom-start': 'top-full left-0 mt-2',
      'bottom-end': 'top-full right-0 mt-2'
    };
    return positions[position_];
  };



  if (disabled) {
    return <>{children}</>;
  }

  return (
    <div
      ref={triggerRef}
      className={`relative inline-block ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleClick}
      onFocus={handleFocus}
      onBlur={handleBlur}
    >
      {children}
      
      {controlledVisible && content && (
        <div
          ref={tooltipRef}
          className={`
            tooltip-content visible
            ${getPositionStyles()}
            ${interactive ? 'interactive' : ''}
          `}
          style={{ maxWidth }}
          onMouseEnter={interactive ? () => clearTimeouts() : undefined}
          onMouseLeave={interactive ? hideTooltip : undefined}
        >
          <div
            className={`
              font-medium whitespace-nowrap
              ${getVariantStyles()}
              ${getSizeStyles()}
              ${contentClassName}
            `}
          >
            {content}
            {showArrow && (
              <div className={`tooltip-arrow ${position_.split('-')[0]}`} />
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Tooltip;
