import React, { useState, useRef, useEffect, ReactNode } from 'react';

export type TopbarTooltipPosition = 'top' | 'bottom' | 'left' | 'right';
export type TopbarTooltipVariant = 'default' | 'light' | 'primary' | 'success' | 'warning' | 'error';
export type TopbarTooltipSize = 'sm' | 'md' | 'lg';

interface TopbarTooltipProps {
  children: ReactNode;
  text: string;
  position?: TopbarTooltipPosition;
  variant?: TopbarTooltipVariant;
  size?: TopbarTooltipSize;
  disabled?: boolean;
  delay?: number;
  className?: string;
  maxWidth?: string;
}

/**
 * مكون تلميح محسن خصيصاً للشريط العلوي
 * يستخدم position: fixed لحل مشاكل المواضع في الشريط العلوي
 */
const TopbarTooltip: React.FC<TopbarTooltipProps> = ({
  children,
  text,
  position = 'bottom',
  variant = 'default',
  size = 'md',
  disabled = false,
  delay = 500,
  className = '',
  maxWidth = '200px'
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [tooltipStyle, setTooltipStyle] = useState<React.CSSProperties>({});
  const timeoutRef = useRef<NodeJS.Timeout>();
  const hideTimeoutRef = useRef<NodeJS.Timeout>();
  const triggerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);

  // تنظيف المؤقتات
  const clearTimeouts = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = undefined;
    }
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
      hideTimeoutRef.current = undefined;
    }
  };

  // حساب موضع التلميح
  const calculatePosition = () => {
    if (!triggerRef.current || !tooltipRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const tooltipRect = tooltipRef.current.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };

    let top = 0;
    let left = 0;

    switch (position) {
      case 'top':
        top = triggerRect.top - tooltipRect.height - 10;
        left = triggerRect.left + (triggerRect.width - tooltipRect.width) / 2;
        break;
      case 'bottom':
        top = triggerRect.bottom + 10;
        left = triggerRect.left + (triggerRect.width - tooltipRect.width) / 2;
        break;
      case 'left':
        top = triggerRect.top + (triggerRect.height - tooltipRect.height) / 2;
        left = triggerRect.left - tooltipRect.width - 10;
        break;
      case 'right':
        top = triggerRect.top + (triggerRect.height - tooltipRect.height) / 2;
        left = triggerRect.right + 10;
        break;
    }

    // التأكد من أن التلميح لا يخرج من حدود الشاشة
    if (left < 10) left = 10;
    if (left + tooltipRect.width > viewport.width - 10) {
      left = viewport.width - tooltipRect.width - 10;
    }
    if (top < 10) top = 10;
    if (top + tooltipRect.height > viewport.height - 10) {
      top = viewport.height - tooltipRect.height - 10;
    }

    setTooltipStyle({
      position: 'fixed',
      top: `${top}px`,
      left: `${left}px`,
      zIndex: 50000,
      maxWidth
    });
  };

  // إظهار التلميح
  const showTooltip = () => {
    if (disabled) return;

    clearTimeouts();
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true);
      // حساب الموضع بعد الظهور
      setTimeout(calculatePosition, 0);
    }, delay);
  };

  // إخفاء التلميح
  const hideTooltip = () => {
    clearTimeouts();
    hideTimeoutRef.current = setTimeout(() => {
      setIsVisible(false);
    }, 100);
  };

  // تحديث الموضع عند تغيير حجم النافذة
  useEffect(() => {
    if (!isVisible) return;

    const handleResize = () => calculatePosition();
    const handleScroll = () => calculatePosition();

    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [isVisible]);

  // تنظيف المؤقتات عند إلغاء التحميل
  useEffect(() => {
    return () => clearTimeouts();
  }, []);

  // أنماط المتغيرات
  const getVariantStyles = () => {
    const variants = {
      default: 'tooltip-default',
      light: 'tooltip-light',
      primary: 'tooltip-primary',
      success: 'tooltip-success',
      warning: 'tooltip-warning',
      error: 'tooltip-error'
    };
    return variants[variant];
  };

  // أنماط الأحجام
  const getSizeStyles = () => {
    const sizes = {
      sm: 'tooltip-sm',
      md: 'tooltip-md',
      lg: 'tooltip-lg'
    };
    return sizes[size];
  };

  if (!text || disabled) {
    return <>{children}</>;
  }

  return (
    <>
      <div
        ref={triggerRef}
        className={`relative inline-block ${className}`}
        onMouseEnter={showTooltip}
        onMouseLeave={hideTooltip}
      >
        {children}
      </div>
      
      <div
        ref={tooltipRef}
        style={{
          ...tooltipStyle,
          opacity: isVisible ? 1 : 0,
          pointerEvents: isVisible ? 'auto' : 'none',
          visibility: isVisible ? 'visible' : 'hidden'
        }}
        className={`
          tooltip-content
          font-medium whitespace-nowrap
          ${getVariantStyles()}
          ${getSizeStyles()}
          shadow-lg
          transition-opacity duration-200 ease-in-out
        `}
      >
        {text}
        {/* السهم المؤشر */}
        <div className={`tooltip-arrow ${position}`} />
      </div>
    </>
  );
};

export default TopbarTooltip;
