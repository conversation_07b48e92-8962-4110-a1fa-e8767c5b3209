import React, { ReactNode } from 'react';
import Tooltip, { TooltipPosition, TooltipVariant, TooltipSize } from './Tooltip';

interface SimpleTooltipProps {
  children: ReactNode;
  text: string;
  position?: TooltipPosition;
  variant?: TooltipVariant;
  size?: TooltipSize;
  disabled?: boolean;
  delay?: number;
  className?: string;
  maxWidth?: string;
}

/**
 * مكون تلميح مبسط للاستخدام السريع
 * يوفر واجهة مبسطة لعرض نص تلميح بسيط
 */
const SimpleTooltip: React.FC<SimpleTooltipProps> = ({
  children,
  text,
  position = 'top',
  variant = 'default',
  size = 'md',
  disabled = false,
  delay = 300,
  className = '',
  maxWidth = '200px'
}) => {
  if (!text || disabled) {
    return <>{children}</>;
  }

  return (
    <Tooltip
      content={text}
      position={position}
      variant={variant}
      size={size}
      delay={delay}
      className={className}
      maxWidth={maxWidth}
      showArrow={true}
    >
      {children}
    </Tooltip>
  );
};

export default SimpleTooltip;
