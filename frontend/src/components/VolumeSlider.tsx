/**
 * مكون شريط تمرير مستوى الصوت المحسن
 * يوفر واجهة سلسة وسهلة لتحديد مستوى الصوت مع تفاعل محسن
 */

import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  FiVolumeX,
  FiVolume1,
  FiVolume2,
  FiVolume
} from 'react-icons/fi';

interface VolumeSliderProps {
  value: number; // 0-100
  onChange: (value: number) => void;
  disabled?: boolean;
  className?: string;
  showIcon?: boolean;
  showValue?: boolean;
  size?: 'sm' | 'md' | 'lg';
  step?: number; // خطوة التغيير (افتراضي: 1)
  debounceMs?: number; // تأخير التحديث (افتراضي: 0)
}

const VolumeSlider: React.FC<VolumeSliderProps> = ({
  value,
  onChange,
  disabled = false,
  className = '',
  showIcon = true,
  showValue = true,
  size = 'md',
  step = 1,
  debounceMs = 0
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const [localValue, setLocalValue] = useState(value);
  const sliderRef = useRef<HTMLDivElement>(null);
  const thumbRef = useRef<HTMLDivElement>(null);
  const debounceRef = useRef<NodeJS.Timeout | null>(null);

  // تحديث القيمة المحلية عند تغيير القيمة الخارجية
  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  // الحصول على أيقونة الصوت المناسبة
  const getVolumeIcon = () => {
    if (disabled) return <FiVolume className="text-gray-400" />;
    if (localValue === 0) return <FiVolumeX className="text-red-500" />;
    if (localValue < 30) return <FiVolume1 className="text-yellow-500" />;
    if (localValue < 70) return <FiVolume1 className="text-blue-500" />;
    return <FiVolume2 className="text-green-500" />;
  };

  // الحصول على لون الشريط
  const getSliderColor = () => {
    if (disabled) return 'bg-gray-300 dark:bg-gray-600';
    if (localValue === 0) return 'bg-red-500';
    if (localValue < 30) return 'bg-red-400';
    if (localValue < 70) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  // الحصول على أحجام المكون
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          container: 'h-6',
          track: 'h-1.5',
          thumb: 'w-4 h-4',
          icon: 'w-4 h-4',
          text: 'text-xs'
        };
      case 'lg':
        return {
          container: 'h-10',
          track: 'h-3',
          thumb: 'w-6 h-6',
          icon: 'w-6 h-6',
          text: 'text-base'
        };
      default: // md
        return {
          container: 'h-8',
          track: 'h-2',
          thumb: 'w-5 h-5',
          icon: 'w-5 h-5',
          text: 'text-sm'
        };
    }
  };

  const sizeClasses = getSizeClasses();

  // حساب القيمة من موضع الماوس مع تحسينات
  const calculateValueFromPosition = useCallback((clientX: number) => {
    if (!sliderRef.current) return localValue;

    const rect = sliderRef.current.getBoundingClientRect();
    const percentage = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width));
    const rawValue = percentage * 100;

    // تطبيق الخطوة
    const steppedValue = Math.round(rawValue / step) * step;
    return Math.max(0, Math.min(100, steppedValue));
  }, [localValue, step]);

  // دالة تحديث القيمة مع debounce
  const updateValue = useCallback((newValue: number) => {
    setLocalValue(newValue);

    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    if (debounceMs > 0) {
      debounceRef.current = setTimeout(() => {
        onChange(newValue);
      }, debounceMs);
    } else {
      onChange(newValue);
    }
  }, [onChange, debounceMs]);

  // معالجة بداية السحب
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (disabled) return;

    setIsDragging(true);
    const newValue = calculateValueFromPosition(e.clientX);
    updateValue(newValue);

    // منع تحديد النص
    e.preventDefault();

    // إضافة تأثير بصري
    if (thumbRef.current) {
      thumbRef.current.style.transform = 'scale(1.1)';
    }
  }, [disabled, calculateValueFromPosition, updateValue]);

  // معالجة السحب
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || disabled) return;

    const newValue = calculateValueFromPosition(e.clientX);
    updateValue(newValue);
  }, [isDragging, disabled, calculateValueFromPosition, updateValue]);

  // معالجة انتهاء السحب
  const handleMouseUp = useCallback(() => {
    setIsDragging(false);

    // إزالة التأثير البصري
    if (thumbRef.current) {
      thumbRef.current.style.transform = '';
    }
  }, []);

  // معالجة التحويم
  const handleMouseEnter = useCallback(() => {
    if (!disabled) {
      setIsHovering(true);
    }
  }, [disabled]);

  const handleMouseLeave = useCallback(() => {
    setIsHovering(false);
  }, []);

  // إضافة مستمعي الأحداث للسحب
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.addEventListener('mouseleave', handleMouseUp); // للتعامل مع خروج الماوس من النافذة

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.removeEventListener('mouseleave', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // تنظيف debounce عند إلغاء المكون
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, []);

  // معالجة النقر المباشر على الشريط
  const handleTrackClick = useCallback((e: React.MouseEvent) => {
    if (disabled || isDragging) return;

    const newValue = calculateValueFromPosition(e.clientX);
    updateValue(newValue);

    // تأثير بصري سريع
    if (thumbRef.current) {
      thumbRef.current.style.transform = 'scale(1.05)';
      setTimeout(() => {
        if (thumbRef.current) {
          thumbRef.current.style.transform = '';
        }
      }, 150);
    }
  }, [disabled, isDragging, calculateValueFromPosition, updateValue]);

  // معالجة لوحة المفاتيح
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (disabled) return;

    let newValue = localValue;

    switch (e.key) {
      case 'ArrowLeft':
      case 'ArrowDown':
        newValue = Math.max(0, localValue - step);
        break;
      case 'ArrowRight':
      case 'ArrowUp':
        newValue = Math.min(100, localValue + step);
        break;
      case 'Home':
        newValue = 0;
        break;
      case 'End':
        newValue = 100;
        break;
      default:
        return;
    }

    e.preventDefault();
    updateValue(newValue);
  }, [disabled, localValue, step, updateValue]);

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      {/* أيقونة الصوت */}
      {showIcon && (
        <div className="flex-shrink-0">
          {getVolumeIcon()}
        </div>
      )}

      {/* شريط التمرير */}
      <div
        className={`flex-1 relative ${sizeClasses.container} flex items-center`}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {/* المسار الخلفي */}
        <div
          ref={sliderRef}
          className={`
            relative w-full ${sizeClasses.track}
            bg-gray-200 dark:bg-gray-700 rounded-full
            cursor-pointer transition-all duration-300 ease-out
            ${disabled ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-300 dark:hover:bg-gray-600'}
            ${isHovering && !disabled ? 'shadow-md' : ''}
          `}
          onClick={handleTrackClick}
          onKeyDown={handleKeyDown}
          tabIndex={disabled ? -1 : 0}
          role="slider"
          aria-valuemin={0}
          aria-valuemax={100}
          aria-valuenow={localValue}
          aria-label="مستوى الصوت"
        >
          {/* المسار النشط */}
          <div
            className={`
              absolute left-0 top-0 ${sizeClasses.track} rounded-full
              transition-all duration-300 ease-out ${getSliderColor()}
              ${isHovering && !disabled ? 'shadow-sm' : ''}
            `}
            style={{ width: `${disabled ? 0 : localValue}%` }}
          />

          {/* المقبض */}
          <div
            ref={thumbRef}
            className={`
              absolute top-1/2 transform -translate-y-1/2 ${sizeClasses.thumb}
              ${getSliderColor()} rounded-full shadow-lg
              transition-all duration-200 ease-out cursor-grab select-none
              ${isDragging ? 'cursor-grabbing shadow-2xl z-10' : ''}
              ${disabled ? 'cursor-not-allowed opacity-50' : 'hover:shadow-xl'}
              ${isHovering && !disabled ? 'scale-105' : ''}
              focus:outline-none focus:ring-4 focus:ring-primary-500/20
            `}
            style={{
              left: `calc(${disabled ? 0 : localValue}% - ${parseInt(sizeClasses.thumb.split(' ')[0].replace('w-', '')) * 0.25}rem)`,
              transform: `translateY(-50%) ${isDragging ? 'scale(1.1)' : isHovering && !disabled ? 'scale(1.05)' : 'scale(1)'}`
            }}
            onMouseDown={handleMouseDown}
            tabIndex={disabled ? -1 : 0}
          >
            {/* نقطة في المنتصف */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className={`
                w-1.5 h-1.5 bg-white rounded-full transition-opacity duration-200
                ${isDragging ? 'opacity-100' : 'opacity-70'}
              `} />
            </div>

            {/* حلقة خارجية للتأثير */}
            {(isDragging || isHovering) && !disabled && (
              <div className="absolute inset-0 rounded-full border-2 border-white/30 animate-pulse" />
            )}
          </div>
        </div>
      </div>

      {/* عرض القيمة */}
      {showValue && (
        <div className={`flex-shrink-0 min-w-[3rem] text-right ${sizeClasses.text}`}>
          <span className={`font-medium ${
            disabled 
              ? 'text-gray-400' 
              : 'text-gray-700 dark:text-gray-300'
          }`}>
            {localValue}%
          </span>
        </div>
      )}
    </div>
  );
};

export default VolumeSlider;
