import React, { useState, useEffect } from 'react';
import {
  FaCloud,
  FaCheck,
  FaTimes,
  FaSpinner,
  FaUpload,
  FaList,
  FaTrash,
  FaExclamationTriangle,
  FaInfoCircle,
  FaSync,
  FaSignOutAlt,
  FaRedo,
  FaHdd,
  FaFile,
  FaChartPie
} from 'react-icons/fa';
import api from '../lib/axios';
import Modal from './Modal';
import { formatDateTime } from '../services/dateTimeService';
import SimpleConfirmModal from './SimpleConfirmModal';
import { GoogleOAuthManager } from '../services/GoogleOAuthManager';

interface GoogleDriveStatus {
  available: boolean;
  configured: boolean;
  enabled: boolean;
  user_email?: string;
  error?: string;
}

interface GoogleDriveFile {
  id: string;
  name: string;
  size: string;
  createdTime: string;
  modifiedTime: string;
}

interface GoogleDriveStats {
  success: boolean;
  user_email?: string;
  display_name?: string;
  total_storage?: number;
  used_storage?: number;
  available_storage?: number;
  total_files?: number | string;
  storage_percentage?: number;
  error?: string;
}

interface GoogleDriveManagerProps {
  className?: string;
}

const GoogleDriveManager: React.FC<GoogleDriveManagerProps> = ({ className = '' }) => {
  const [status, setStatus] = useState<GoogleDriveStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [testing, setTesting] = useState(false);
  const [files, setFiles] = useState<GoogleDriveFile[]>([]);
  const [showFilesModal, setShowFilesModal] = useState(false);
  const [showCredentialsModal, setShowCredentialsModal] = useState(false);
  const [credentialsJson, setCredentialsJson] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [oauthLoading, setOauthLoading] = useState(false);
  const [logoutLoading, setLogoutLoading] = useState(false);
  const [showLogoutModal, setShowLogoutModal] = useState(false);
  const [showCleanupModal, setShowCleanupModal] = useState(false);
  const [cleanupLoading, setCleanupLoading] = useState(false);
  const [showResetModal, setShowResetModal] = useState(false);
  const [resetLoading, setResetLoading] = useState(false);
  const [stats, setStats] = useState<GoogleDriveStats | null>(null);
  const [statsLoading, setStatsLoading] = useState(false);

  useEffect(() => {
    fetchStatus();

    // استقبال رسائل من نافذة OAuth
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'GOOGLE_OAUTH_SUCCESS') {
        setSuccess(event.data.message);
        setOauthLoading(false);
        // تحديث الحالة والإحصائيات
        setTimeout(() => {
          fetchStatus();
          fetchStats();
        }, 1000);
      } else if (event.data.type === 'GOOGLE_OAUTH_ERROR') {
        setError(event.data.message);
        setOauthLoading(false);
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  const fetchStatus = async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/google-drive/status');
      setStatus(response.data);

      // جلب الإحصائيات إذا كان متصل
      if (response.data.configured && response.data.user_email) {
        fetchStats();
      }
    } catch (error: any) {
      console.error('Error fetching Google Drive status:', error);
      setError('فشل في جلب حالة Google Drive');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      setStatsLoading(true);
      const response = await api.get('/api/google-drive/stats');
      setStats(response.data);
    } catch (error: any) {
      console.error('Error fetching Google Drive stats:', error);
      // لا نعرض خطأ للإحصائيات لأنها ليست حرجة
    } finally {
      setStatsLoading(false);
    }
  };

  const testConnection = async () => {
    try {
      setTesting(true);
      setError('');
      const response = await api.post('/api/google-drive/test-connection');
      if (response.data.success) {
        setSuccess(`تم الاتصال بنجاح مع Google Drive (${response.data.user_email})`);
        await fetchStatus(); // Refresh status
        await fetchStats(); // Refresh stats
      } else {
        setError(response.data.error || 'فشل في اختبار الاتصال');
      }
    } catch (error: any) {
      console.error('Error testing connection:', error);
      setError(error.response?.data?.detail || 'فشل في اختبار الاتصال');
    } finally {
      setTesting(false);
    }
  };

  const startOAuthFlow = async () => {
    try {
      setOauthLoading(true);
      setError('');
      setSuccess('');

      const response = await api.post('/api/google-drive/start-oauth');

      if (response.data.auth_url) {
        // استخدام فئة إدارة OAuth المحسنة
        const oauthManager = new GoogleOAuthManager({
          authUrl: response.data.auth_url,
          onSuccess: () => {
            setOauthLoading(false);
            setSuccess('تم تسجيل الدخول بنجاح!');
            fetchStatus();
          },
          onError: (error: string) => {
            setOauthLoading(false);
            setError(error);
          },
          onCancel: () => {
            setOauthLoading(false);
            setError('تم إلغاء عملية تسجيل الدخول');
          }
        });

        oauthManager.startFlow();

      } else {
        setError('فشل في بدء عملية التفويض');
        setOauthLoading(false);
      }
    } catch (error: any) {
      console.error('Error starting OAuth flow:', error);
      setError(error.response?.data?.detail || 'فشل في بدء عملية التفويض');
      setOauthLoading(false);
    }
  };

  const handleLogoutClick = () => {
    setShowLogoutModal(true);
  };

  const handleLogoutConfirm = async () => {
    try {
      setLogoutLoading(true);
      setError('');

      const response = await api.post('/api/google-drive/logout');

      if (response.data.success) {
        setSuccess('تم تسجيل الخروج من Google Drive بنجاح');
        setShowLogoutModal(false);
        setStats(null); // Clear stats
        await fetchStatus(); // Refresh status
      } else {
        setError('فشل في تسجيل الخروج من Google Drive');
      }
    } catch (error: any) {
      console.error('Error logging out from Google Drive:', error);
      setError(error.response?.data?.detail || 'فشل في تسجيل الخروج من Google Drive');
    } finally {
      setLogoutLoading(false);
    }
  };

  const handleLogoutCancel = () => {
    setShowLogoutModal(false);
  };

  const uploadCredentials = async () => {
    try {
      if (!credentialsJson.trim()) {
        setError('يرجى إدخال بيانات الاعتماد');
        return;
      }

      const response = await api.post('/api/google-drive/upload-credentials', {
        credentials_json: credentialsJson
      });

      if (response.data.success) {
        setSuccess('تم حفظ بيانات الاعتماد بنجاح');
        setShowCredentialsModal(false);
        setCredentialsJson('');
        await fetchStatus(); // Refresh status
      } else {
        setError(response.data.error || 'فشل في حفظ بيانات الاعتماد');
      }
    } catch (error: any) {
      console.error('Error uploading credentials:', error);
      setError(error.response?.data?.detail || 'فشل في حفظ بيانات الاعتماد');
    }
  };

  const fetchFiles = async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/google-drive/files');
      if (response.data.success) {
        setFiles(response.data.files);
        setShowFilesModal(true);

        // إذا كان هناك رسالة (مثل المجلد فارغ)، عرضها كرسالة نجاح
        if (response.data.message) {
          setSuccess(response.data.message);
        }
      } else {
        setError(response.data.error || 'فشل في جلب قائمة الملفات');
      }
    } catch (error: any) {
      console.error('Error fetching files:', error);
      setError(error.response?.data?.detail || 'فشل في جلب قائمة الملفات');
    } finally {
      setLoading(false);
    }
  };

  const handleCleanupClick = () => {
    setShowCleanupModal(true);
  };

  const handleCleanupConfirm = async () => {
    try {
      setCleanupLoading(true);
      setError('');

      const response = await api.post('/api/google-drive/cleanup', null, {
        params: { keep_count: 10 }
      });

      if (response.data.success) {
        setSuccess(response.data.message);
        setShowCleanupModal(false);
      } else {
        setError('فشل في تنظيف النسخ الاحتياطية');
      }
    } catch (error: any) {
      console.error('Error cleaning up backups:', error);
      setError(error.response?.data?.detail || 'فشل في تنظيف النسخ الاحتياطية');
    } finally {
      setCleanupLoading(false);
    }
  };

  const handleCleanupCancel = () => {
    setShowCleanupModal(false);
  };

  const handleResetClick = () => {
    setShowResetModal(true);
  };

  const handleResetConfirm = async () => {
    try {
      setResetLoading(true);
      setError('');

      const response = await api.post('/api/google-drive/reset');

      if (response.data.success) {
        setSuccess('تم إعادة تعيين حالة Google Drive بنجاح');
        setShowResetModal(false);
        setStats(null); // Clear stats
        await fetchStatus(); // Refresh status
      } else {
        setError('فشل في إعادة تعيين حالة Google Drive');
      }
    } catch (error: any) {
      console.error('Error resetting Google Drive state:', error);
      setError(error.response?.data?.detail || 'فشل في إعادة تعيين حالة Google Drive');
    } finally {
      setResetLoading(false);
    }
  };

  const handleResetCancel = () => {
    setShowResetModal(false);
  };

  const formatFileSize = (sizeStr: string) => {
    const size = parseInt(sizeStr);
    if (size < 1024) return `${size} B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
    if (size < 1024 * 1024 * 1024) return `${(size / (1024 * 1024)).toFixed(1)} MB`;
    return `${(size / (1024 * 1024 * 1024)).toFixed(1)} GB`;
  };

  const formatDate = (dateStr: string) => {
    return formatDateTime(dateStr, 'datetime'); // استخدام خدمة التواريخ المستخدمة في المشروع
  };

  const formatStorageSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatFileCount = (count: number | string) => {
    if (typeof count === 'string') return count;
    return count.toLocaleString('en-US'); // استخدام الأرقام الإنجليزية
  };

  if (loading && !status) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <FaSpinner className="animate-spin text-2xl text-primary-600" />
        <span className="mr-2">جاري تحميل حالة Google Drive...</span>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <FaCloud className="text-2xl text-blue-500 ml-3" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Google Drive
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              إدارة النسخ الاحتياطية في Google Drive
            </p>
          </div>
        </div>
        <button
          onClick={fetchStatus}
          className="p-2 text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
          title="تحديث الحالة"
        >
          <FaSync className="text-sm" />
        </button>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Availability */}
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              التوفر
            </span>
            {status?.available ? (
              <FaCheck className="text-green-500" />
            ) : (
              <FaTimes className="text-red-500" />
            )}
          </div>
          <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
            {status?.available ? 'مكتبات Google APIs متوفرة' : 'مكتبات Google APIs غير متوفرة'}
          </p>
        </div>

        {/* Configuration */}
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              التكوين
            </span>
            {status?.configured ? (
              <FaCheck className="text-green-500" />
            ) : (
              <FaTimes className="text-red-500" />
            )}
          </div>
          <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
            {status?.configured ? 'تم تكوين بيانات الاعتماد' : 'لم يتم تكوين بيانات الاعتماد'}
          </p>
        </div>

        {/* Status */}
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              الحالة
            </span>
            {status?.enabled ? (
              <FaCheck className="text-green-500" />
            ) : (
              <FaTimes className="text-red-500" />
            )}
          </div>
          <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
            {status?.enabled ? 'مفعل' : 'غير مفعل'}
          </p>
        </div>
      </div>

      {/* User Email */}
      {status?.user_email && (
        <div className="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <FaInfoCircle className="text-green-600 dark:text-green-400 ml-2" />
              <span className="text-sm text-green-800 dark:text-green-200">
                متصل بحساب: {status.user_email}
              </span>
            </div>
            <button
              onClick={handleLogoutClick}
              disabled={logoutLoading}
              className="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 text-sm flex items-center transition-colors"
            >
              {logoutLoading ? (
                <FaSpinner className="animate-spin ml-1" />
              ) : (
                <FaSignOutAlt className="ml-1" />
              )}
              تسجيل الخروج
            </button>
          </div>
        </div>
      )}

      {/* Storage Statistics */}
      {status?.user_email && stats?.success && (
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center">
              <FaChartPie className="text-blue-500 ml-2" />
              إحصائيات التخزين
            </h3>
            <button
              onClick={fetchStats}
              disabled={statsLoading}
              className="p-1 text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
              title="تحديث الإحصائيات"
            >
              {statsLoading ? (
                <FaSpinner className="animate-spin text-sm" />
              ) : (
                <FaSync className="text-sm" />
              )}
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Total Files */}
            <div className="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-lg p-3">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-700 dark:text-blue-300">
                    إجمالي الملفات
                  </p>
                  <p className="text-lg font-bold text-blue-900 dark:text-blue-100">
                    {formatFileCount(stats.total_files || 0)}
                  </p>
                </div>
                <FaFile className="text-2xl text-blue-500" />
              </div>
            </div>

            {/* Used Storage */}
            <div className="bg-orange-50 dark:bg-orange-900/30 border border-orange-200 dark:border-orange-700 rounded-lg p-3">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-orange-700 dark:text-orange-300">
                    المساحة المستخدمة
                  </p>
                  <p className="text-lg font-bold text-orange-900 dark:text-orange-100">
                    {formatStorageSize(stats.used_storage || 0)}
                  </p>
                  <p className="text-xs text-orange-600 dark:text-orange-400">
                    {stats.storage_percentage?.toFixed(1)}% من الإجمالي
                  </p>
                </div>
                <FaHdd className="text-2xl text-orange-500" />
              </div>
            </div>

            {/* Available Storage */}
            <div className="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700 rounded-lg p-3">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-700 dark:text-green-300">
                    المساحة المتبقية
                  </p>
                  <p className="text-lg font-bold text-green-900 dark:text-green-100">
                    {formatStorageSize(stats.available_storage || 0)}
                  </p>
                  <p className="text-xs text-green-600 dark:text-green-400">
                    من أصل {formatStorageSize(stats.total_storage || 0)}
                  </p>
                </div>
                <FaCloud className="text-2xl text-green-500" />
              </div>
            </div>
          </div>

          {/* Storage Progress Bar */}
          {stats.total_storage && stats.total_storage > 0 && (
            <div className="mt-4">
              <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                <span>استخدام التخزين</span>
                <span>{stats.storage_percentage?.toFixed(1)}%</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    (stats.storage_percentage || 0) > 90
                      ? 'bg-red-500'
                      : (stats.storage_percentage || 0) > 75
                      ? 'bg-orange-500'
                      : 'bg-green-500'
                  }`}
                  style={{ width: `${Math.min(stats.storage_percentage || 0, 100)}%` }}
                ></div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700 rounded-lg p-4">
          <div className="flex items-center">
            <FaExclamationTriangle className="text-red-600 dark:text-red-400 ml-2" />
            <span className="text-sm text-red-800 dark:text-red-200">{error}</span>
          </div>
          <button
            onClick={() => setError('')}
            className="mt-2 text-xs text-red-600 dark:text-red-400 hover:underline"
          >
            إخفاء
          </button>
        </div>
      )}

      {/* Success Display */}
      {success && (
        <div className="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700 rounded-lg p-4">
          <div className="flex items-center">
            <FaCheck className="text-green-600 dark:text-green-400 ml-2" />
            <span className="text-sm text-green-800 dark:text-green-200">{success}</span>
          </div>
          <button
            onClick={() => setSuccess('')}
            className="mt-2 text-xs text-green-600 dark:text-green-400 hover:underline"
          >
            إخفاء
          </button>
        </div>
      )}

      {/* Setup Guide */}
      {!status?.configured && (
        <div className="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-4">
          <div className="flex items-center mb-2">
            <FaInfoCircle className="text-blue-600 dark:text-blue-400 ml-2 flex-shrink-0" />
            <h3 className="text-blue-800 dark:text-blue-200 font-medium">دليل الإعداد</h3>
          </div>
          <div className="text-sm text-blue-700 dark:text-blue-300 mr-6 space-y-2">
            <p>تم تكوين بيانات الاعتماد مسبقاً. انقر على زر "تسجيل الدخول مع Google" أدناه للمتابعة.</p>
            <p>سيتم فتح نافذة جديدة لتسجيل الدخول باستخدام حساب Google الخاص بك.</p>
            <p>بعد تسجيل الدخول، سيتم توجيهك تلقائياً مرة أخرى إلى هذه الصفحة.</p>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-3">
        {!status?.configured && (
          <>
            <button
              onClick={startOAuthFlow}
              disabled={oauthLoading}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm flex items-center disabled:opacity-50"
            >
              {oauthLoading ? (
                <FaSpinner className="animate-spin ml-2" />
              ) : (
                <FaCloud className="ml-2" />
              )}
              تسجيل الدخول مع Google
            </button>
            <button
              onClick={() => setShowCredentialsModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm flex items-center"
            >
              <FaUpload className="ml-2" />
              رفع ملف JSON يدوياً
            </button>
          </>
        )}

        {status?.configured && (
          <button
            onClick={testConnection}
            disabled={testing}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm flex items-center disabled:opacity-50"
          >
            {testing ? (
              <FaSpinner className="animate-spin ml-2" />
            ) : (
              <FaCheck className="ml-2" />
            )}
            اختبار الاتصال
          </button>
        )}

        {status?.configured && (
          <button
            onClick={fetchFiles}
            className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors text-sm flex items-center"
          >
            <FaList className="ml-2" />
            عرض الملفات
          </button>
        )}

        {status?.configured && (
          <button
            onClick={handleCleanupClick}
            className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors text-sm flex items-center"
          >
            <FaTrash className="ml-2" />
            تنظيف النسخ القديمة
          </button>
        )}

        {status?.configured && (
          <button
            onClick={handleLogoutClick}
            disabled={logoutLoading}
            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm flex items-center disabled:opacity-50"
          >
            {logoutLoading ? (
              <FaSpinner className="animate-spin ml-2" />
            ) : (
              <FaSignOutAlt className="ml-2" />
            )}
            تسجيل الخروج
          </button>
        )}

        {/* Reset Button - Always available for troubleshooting */}
        <button
          onClick={handleResetClick}
          disabled={resetLoading}
          className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors text-sm flex items-center disabled:opacity-50"
        >
          {resetLoading ? (
            <FaSpinner className="animate-spin ml-2" />
          ) : (
            <FaRedo className="ml-2" />
          )}
          إعادة تعيين
        </button>
      </div>



      {/* Credentials Upload Modal */}
      <Modal
        isOpen={showCredentialsModal}
        onClose={() => setShowCredentialsModal(false)}
        title="رفع ملف JSON يدوياً"
      >
        <div className="space-y-4">
          <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
            <div className="flex items-start">
              <FaInfoCircle className="text-yellow-600 dark:text-yellow-400 ml-2 mt-0.5" />
              <div className="text-sm text-yellow-800 dark:text-yellow-200">
                <p className="font-medium mb-2">طريقة بديلة:</p>
                <p>إذا كنت تفضل الطريقة اليدوية، يمكنك الحصول على ملف JSON من Google Cloud Console واستخدام OAuth Playground لتوليد refresh token.</p>
                <p className="mt-2">
                  <strong>نوصي باستخدام "تسجيل الدخول مع Google" أعلاه لأنه أسهل وأكثر أماناً.</strong>
                </p>
              </div>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              بيانات الاعتماد (JSON)
            </label>
            <textarea
              value={credentialsJson}
              onChange={(e) => setCredentialsJson(e.target.value)}
              placeholder="الصق بيانات الاعتماد JSON هنا..."
              className="w-full h-40 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100 text-sm font-mono"
            />
          </div>
          <div className="flex justify-end space-x-3 space-x-reverse">
            <button
              onClick={() => setShowCredentialsModal(false)}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              إلغاء
            </button>
            <button
              onClick={uploadCredentials}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              حفظ
            </button>
          </div>
        </div>
      </Modal>

      {/* Files List Modal */}
      <Modal
        isOpen={showFilesModal}
        onClose={() => setShowFilesModal(false)}
        title="النسخ الاحتياطية في Google Drive"
        size="lg"
      >
        <div className="space-y-4">
          {files.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <FaCloud className="mx-auto text-4xl mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">لا توجد نسخ احتياطية في Google Drive بعد</p>
              <p className="text-sm">
                تم إنشاء مجلد النسخ الاحتياطية بنجاح. سيتم إنشاء النسخ الاحتياطية تلقائياً حسب الجدولة المحددة في إعدادات النظام.
              </p>
            </div>
          ) : (
            <div className="space-y-2">
              {files.map((file) => (
                <div
                  key={file.id}
                  className="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-3"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                        {file.name}
                      </h4>
                      <div className="flex items-center space-x-4 space-x-reverse text-xs text-gray-600 dark:text-gray-400 mt-1">
                        <span>الحجم: {formatFileSize(file.size)}</span>
                        <span>تاريخ الإنشاء: {formatDate(file.createdTime)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </Modal>

      {/* Logout Confirmation Modal */}
      <SimpleConfirmModal
        isOpen={showLogoutModal}
        onClose={handleLogoutCancel}
        onConfirm={handleLogoutConfirm}
        title="تأكيد تسجيل الخروج"
        message="هل أنت متأكد من تسجيل الخروج من Google Drive؟ سيتم حذف جميع بيانات الاعتماد المحفوظة."
        confirmText="تسجيل الخروج"
        cancelText="إلغاء"
        isLoading={logoutLoading}
      />

      {/* Cleanup Confirmation Modal */}
      <SimpleConfirmModal
        isOpen={showCleanupModal}
        onClose={handleCleanupCancel}
        onConfirm={handleCleanupConfirm}
        title="تأكيد تنظيف النسخ الاحتياطية"
        message="هل أنت متأكد من حذف النسخ الاحتياطية القديمة من Google Drive؟ سيتم الاحتفاظ بآخر 10 نسخ فقط."
        confirmText="حذف النسخ القديمة"
        cancelText="إلغاء"
        isLoading={cleanupLoading}
      />

      {/* Reset Confirmation Modal */}
      <SimpleConfirmModal
        isOpen={showResetModal}
        onClose={handleResetCancel}
        onConfirm={handleResetConfirm}
        title="تأكيد إعادة تعيين Google Drive"
        message="هل أنت متأكد من إعادة تعيين حالة Google Drive؟ سيتم حذف جميع البيانات المحفوظة بما في ذلك بيانات الاعتماد ومعرف مجلد النسخ الاحتياطية. هذا الإجراء مفيد لحل مشاكل الاتصال."
        confirmText="إعادة تعيين"
        cancelText="إلغاء"
        isLoading={resetLoading}
      />
    </div>
  );
};

export default GoogleDriveManager;
