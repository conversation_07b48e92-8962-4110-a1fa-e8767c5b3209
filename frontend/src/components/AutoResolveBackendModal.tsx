import React from 'react';
import { <PERSON>a<PERSON><PERSON>ner, FaCheck, FaTimes, FaCog, FaServer } from 'react-icons/fa';
import Modal from './Modal';
import { AutoResolveProgress, AutoResolveBackendResponse } from '../services/autoResolveBackendService';

interface AutoResolveBackendModalProps {
  isOpen: boolean;
  progress: AutoResolveProgress | null;
  result: AutoResolveBackendResponse | null;
  error: string | null;
  onClose: () => void;
  onStop: () => void;
}

const AutoResolveBackendModal: React.FC<AutoResolveBackendModalProps> = ({
  isOpen,
  progress,
  result,
  error,
  onClose,
  onStop
}) => {
  // تحديد حالة النافذة
  const getModalState = () => {
    if (error) return 'error';
    if (result) return 'completed';
    if (progress) return progress.stage;
    return 'idle';
  };

  const modalState = getModalState();

  // تحدي<PERSON> العنوان
  const getTitle = () => {
    switch (modalState) {
      case 'starting':
        return 'بدء الحل التلقائي';
      case 'processing':
        return `الحل التلقائي - معالجة في الخلفية`;
      case 'completed':
        return 'اكتملت العملية';
      case 'error':
        return 'حدث خطأ';
      default:
        return 'الحل التلقائي الاحترافي';
    }
  };

  // تحديد الأيقونة
  const getIcon = () => {
    switch (modalState) {
      case 'starting':
        return <FaCog className="text-4xl text-blue-500 animate-spin" />;
      case 'processing':
        return <FaServer className="text-4xl text-blue-500 animate-pulse" />;
      case 'completed':
        return <FaCheck className="text-4xl text-green-500" />;
      case 'error':
        return <FaTimes className="text-4xl text-red-500" />;
      default:
        return <FaSpinner className="text-4xl text-gray-400" />;
    }
  };

  // تحديد الرسالة
  const getMessage = () => {
    if (error) {
      return error;
    }

    if (result) {
      const failedMessage = result.failed > 0 
        ? `\n❌ فشل في حل ${result.failed} سجل`
        : '';
      
      const errorsMessage = result.errors.length > 0
        ? `\n\n📋 تفاصيل الأخطاء:\n${result.errors.slice(0, 3).join('\n')}${result.errors.length > 3 ? '\n...' : ''}`
        : '';

      return `🎉 اكتملت عملية الحل التلقائي!\n\n✅ تم حل ${result.resolved} من ${result.total} سجل بنجاح\n🔧 تمت المعالجة في الخلفية\n📊 معدل النجاح: ${Math.round((result.resolved/result.total)*100)}%${failedMessage}${errorsMessage}`;
    }

    if (progress) {
      return progress.message;
    }

    return 'جاري تحضير عملية الحل التلقائي...';
  };

  // تحديد النسبة المئوية
  const getPercentage = () => {
    if (progress) {
      return progress.percentage;
    }
    return 0;
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={modalState === 'processing' || modalState === 'starting' ? () => {} : onClose}
      title={getTitle()}
      size="md"
    >
      <div className="p-6">
        {/* الأيقونة والرسالة */}
        <div className="text-center mb-6">
          <div className="flex justify-center mb-4">
            {getIcon()}
          </div>
          
          <div className="text-gray-700 dark:text-gray-300 whitespace-pre-line text-sm leading-relaxed">
            {getMessage()}
          </div>
        </div>

        {/* شريط التقدم */}
        {progress && (modalState === 'starting' || modalState === 'processing') && (
          <div className="mb-6">
            <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
              <span>التقدم</span>
              <span>{getPercentage()}%</span>
            </div>
            
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
              <div 
                className="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500 ease-out"
                style={{ width: `${getPercentage()}%` }}
              ></div>
            </div>

            {/* معلومات إضافية */}
            {progress.total && (
              <div className="mt-3 text-xs text-gray-500 dark:text-gray-400 text-center">
                {progress.resolved !== undefined && progress.failed !== undefined ? (
                  <>
                    ✅ محلول: {progress.resolved} | ❌ فاشل: {progress.failed} | 📊 المجموع: {progress.total}
                  </>
                ) : (
                  <>
                    📊 إجمالي السجلات: {progress.total}
                  </>
                )}
              </div>
            )}
          </div>
        )}

        {/* رسالة خاصة للمعالجة في الخلفية */}
        {modalState === 'processing' && (
          <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-center gap-3">
              <FaServer className="text-blue-500 text-lg" />
              <div className="text-sm text-blue-700 dark:text-blue-300">
                <div className="font-medium">معالجة احترافية في الخلفية</div>
                <div className="text-xs mt-1">
                  العملية تتم في خادم النظام بدون تأثير على الواجهة
                </div>
              </div>
            </div>
          </div>
        )}

        {/* أزرار التحكم */}
        <div className="flex justify-end gap-3">
          {(modalState === 'processing' || modalState === 'starting') && (
            <button
              onClick={onStop}
              className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors duration-200 flex items-center gap-2"
              disabled={modalState === 'processing'} // تعطيل الإيقاف أثناء المعالجة في الخلفية
            >
              <FaTimes className="text-sm" />
              {modalState === 'processing' ? 'لا يمكن الإيقاف' : 'إيقاف العملية'}
            </button>
          )}
          
          {(modalState === 'completed' || modalState === 'error') && (
            <button
              onClick={onClose}
              className="px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-lg transition-colors duration-200 flex items-center gap-2"
            >
              <FaCheck className="text-sm" />
              إغلاق
            </button>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default AutoResolveBackendModal;
