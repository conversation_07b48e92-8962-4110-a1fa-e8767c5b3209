import React, { useEffect, useState } from 'react';
import { Shield, CheckCircle, AlertTriangle, XCircle, Activity, Clock, Eye, Zap } from './ui/icons';

interface FingerprintAnalytics {
  fingerprint_id: string;
  created_at: string | null;
  last_seen: string | null;
  is_active: boolean;
  auto_approved: boolean;
  stability: {
    stability_score: number;
    stability_level: string;
    stability_color: string;
    issues: string[];
    is_stable: boolean;
    recommendation: string;
  };
  access_stats: {
    total_events: number;
    access_count: number;
    unique_ips: number;
    unique_user_agents: number;
  };
  security_flags: {
    multiple_ips: boolean;
    multiple_browsers: boolean;
    frequent_access: boolean;
  };
}

interface FingerprintValidation {
  device_fingerprint: string;
  hardware_fingerprint: string;
  storage_fingerprint: string;
  stability: {
    stability_score: number;
    stability_level: string;
    stability_color: string;
    issues: string[];
    is_stable: boolean;
    recommendation: string;
  };
  exists_in_database: boolean;
  is_active: boolean;
  analytics: FingerprintAnalytics | null;
}

interface Props {
  deviceId: string;
}

const FingerprintAnalytics: React.FC<Props> = ({ deviceId }) => {
  const [validation, setValidation] = useState<FingerprintValidation | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchValidation = async () => {
      try {
        setLoading(true);
        setError(null);

        // الحصول على بصمة الجهاز الحالي من الخدمة الموحدة
        const unifiedDeviceFingerprint = (await import('../services/unifiedDeviceFingerprint')).default;
        const fingerprint = await unifiedDeviceFingerprint.getFingerprint();

        if (!fingerprint) {
          setError('لا يمكن الحصول على بصمة الجهاز');
          return;
        }

        // إنشاء headers مع البصمة الموحدة
        const headers: Record<string, string> = {
          'Content-Type': 'application/json',
          'X-Device-Fingerprint': fingerprint.deviceId || '',
          'X-Device-Hardware': fingerprint.hardwareFingerprint || '',
          'X-Device-Storage': fingerprint.storageFingerprint || '',
          'X-Device-System': fingerprint.systemFingerprint || '',
        };

        const response = await fetch('/api/device/fingerprint-validation', {
          method: 'GET',
          headers
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setValidation(data.validation);
          } else {
            setError(data.message || 'فشل في الحصول على تحليلات البصمة');
          }
        } else {
          setError('خطأ في الاتصال بالخادم');
        }
      } catch (err) {
        setError('خطأ في الشبكة');
        console.error('خطأ في جلب تحليلات البصمة:', err);
      } finally {
        setLoading(false);
      }
    };

    if (deviceId) {
      fetchValidation();
    }
  }, [deviceId]);

  const getStabilityIcon = (level: string) => {
    switch (level) {
      case 'ممتاز':
        return <CheckCircle className="text-green-600" size={20} />;
      case 'جيد':
        return <CheckCircle className="text-blue-600" size={20} />;
      case 'متوسط':
        return <AlertTriangle className="text-orange-600" size={20} />;
      default:
        return <XCircle className="text-red-600" size={20} />;
    }
  };

  const getStabilityColor = (color: string) => {
    const colorMap: Record<string, string> = {
      'green': 'bg-green-100 text-green-800 border-green-200',
      'blue': 'bg-blue-100 text-blue-800 border-blue-200',
      'orange': 'bg-orange-100 text-orange-800 border-orange-200',
      'red': 'bg-red-100 text-red-800 border-red-200'
    };
    return colorMap[color] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  if (loading) {
    return (
      <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
        <div className="flex items-center justify-center space-x-3 space-x-reverse">
          <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
            <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          </div>
          <div className="text-center">
            <p className="text-sm font-medium text-gray-900 dark:text-white">
              جاري تحليل بصمة الجهاز...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-xl p-6 border border-red-200 dark:border-red-800">
        <div className="flex items-center space-x-3 space-x-reverse">
          <XCircle className="text-red-600" size={24} />
          <div>
            <h4 className="font-semibold text-red-900 dark:text-red-100">خطأ في تحليل البصمة</h4>
            <p className="text-red-700 dark:text-red-300 text-sm">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!validation) {
    return null;
  }

  const { stability, exists_in_database, analytics } = validation;

  return (
    <div className="space-y-6">
      {/* حالة البصمة الأساسية */}
      <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
        <h4 className="font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <Shield className="text-blue-600 ml-2" size={20} />
          حالة البصمة
        </h4>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center space-x-3 space-x-reverse">
            {getStabilityIcon(stability.stability_level)}
            <div>
              <p className="font-medium text-gray-900 dark:text-white">مستوى الثبات</p>
              <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStabilityColor(stability.stability_color)}`}>
                {stability.stability_level} ({stability.stability_score}/100)
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3 space-x-reverse">
            {exists_in_database ? (
              <CheckCircle className="text-green-600" size={20} />
            ) : (
              <XCircle className="text-red-600" size={20} />
            )}
            <div>
              <p className="font-medium text-gray-900 dark:text-white">حالة قاعدة البيانات</p>
              <p className={`text-xs ${exists_in_database ? 'text-green-600' : 'text-red-600'}`}>
                {exists_in_database ? 'مسجلة في النظام' : 'غير مسجلة'}
              </p>
            </div>
          </div>
        </div>

        {/* التوصية */}
        <div className="mt-4 p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
          <p className="text-sm text-gray-700 dark:text-gray-300">
            <strong>التوصية:</strong> {stability.recommendation}
          </p>
        </div>

        {/* المشاكل إن وجدت */}
        {stability.issues.length > 0 && (
          <div className="mt-4 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
            <h5 className="font-medium text-orange-900 dark:text-orange-100 mb-2 flex items-center">
              <AlertTriangle className="text-orange-600 ml-1" size={16} />
              مشاكل محتملة
            </h5>
            <ul className="text-sm text-orange-700 dark:text-orange-300 space-y-1">
              {stability.issues.map((issue, index) => (
                <li key={index} className="flex items-start space-x-2 space-x-reverse">
                  <span className="text-orange-500 mt-1">•</span>
                  <span>{issue}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* إحصائيات الوصول */}
      {analytics && (
        <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-6 border border-green-200 dark:border-green-800">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <Activity className="text-green-600 ml-2" size={20} />
            إحصائيات الوصول
          </h4>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
              <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mx-auto mb-2">
                <Eye className="text-blue-600" size={16} />
              </div>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{analytics.access_stats.total_events}</p>
              <p className="text-xs text-gray-600 dark:text-gray-400">إجمالي الأحداث</p>
            </div>

            <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
              <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mx-auto mb-2">
                <Zap className="text-green-600" size={16} />
              </div>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{analytics.access_stats.access_count}</p>
              <p className="text-xs text-gray-600 dark:text-gray-400">مرات الوصول</p>
            </div>

            <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
              <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mx-auto mb-2">
                <Shield className="text-purple-600" size={16} />
              </div>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{analytics.access_stats.unique_ips}</p>
              <p className="text-xs text-gray-600 dark:text-gray-400">عناوين IP</p>
            </div>

            <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
              <div className="w-8 h-8 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center mx-auto mb-2">
                <Activity className="text-orange-600" size={16} />
              </div>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{analytics.access_stats.unique_user_agents}</p>
              <p className="text-xs text-gray-600 dark:text-gray-400">متصفحات</p>
            </div>
          </div>

          {/* تواريخ مهمة */}
          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            {analytics.created_at && (
              <div className="flex items-center space-x-3 space-x-reverse p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
                <Clock className="text-blue-600" size={16} />
                <div>
                  <p className="font-medium text-gray-900 dark:text-white text-sm">تاريخ الإنشاء</p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {new Date(analytics.created_at).toLocaleString('ar-LY')}
                  </p>
                </div>
              </div>
            )}

            {analytics.last_seen && (
              <div className="flex items-center space-x-3 space-x-reverse p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
                <Activity className="text-green-600" size={16} />
                <div>
                  <p className="font-medium text-gray-900 dark:text-white text-sm">آخر نشاط</p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {new Date(analytics.last_seen).toLocaleString('ar-LY')}
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* تحذيرات أمنية */}
          {(analytics.security_flags.multiple_ips || analytics.security_flags.multiple_browsers || analytics.security_flags.frequent_access) && (
            <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
              <h5 className="font-medium text-yellow-900 dark:text-yellow-100 mb-2 flex items-center">
                <AlertTriangle className="text-yellow-600 ml-1" size={16} />
                ملاحظات أمنية
              </h5>
              <div className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                {analytics.security_flags.multiple_ips && (
                  <p>• تم الوصول من عدة عناوين IP مختلفة</p>
                )}
                {analytics.security_flags.multiple_browsers && (
                  <p>• تم الوصول من متصفحات مختلفة</p>
                )}
                {analytics.security_flags.frequent_access && (
                  <p>• وصول متكرر للنظام</p>
                )}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default FingerprintAnalytics;
