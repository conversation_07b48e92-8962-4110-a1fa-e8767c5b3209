<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مفتاح التبديل للغة العربية (RTL)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* محاكاة دقيقة للمكون المُصحح للعربية */
        .toggle-container {
            position: relative;
            display: inline-flex;
            cursor: pointer;
            border-radius: 9999px;
            transition: all 0.2s ease-in-out;
        }
        
        /* الأحجام */
        .toggle-small {
            height: 20px;  /* h-5 */
            width: 40px;   /* w-10 */
        }
        
        .toggle-medium {
            height: 24px;  /* h-6 */
            width: 48px;   /* w-12 */
        }
        
        .toggle-large {
            height: 28px;  /* h-7 */
            width: 56px;   /* w-14 */
        }
        
        .toggle-thumb {
            position: absolute;
            top: 2px;    /* top-0.5 */
            background: white;
            border-radius: 50%;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease-in-out;
        }
        
        /* أحجام الدوائر */
        .toggle-thumb-small {
            height: 16px;  /* h-4 */
            width: 16px;   /* w-4 */
        }
        
        .toggle-thumb-medium {
            height: 20px;  /* h-5 */
            width: 20px;   /* w-5 */
        }
        
        .toggle-thumb-large {
            height: 24px;  /* h-6 */
            width: 24px;   /* w-6 */
        }
        
        /* السلوك الصحيح للعربية (RTL) */
        /* غير نشط: الدائرة في اليمين */
        .toggle-inactive .toggle-thumb-small {
            right: 2px; /* right-0.5 */
            transform: translateX(0);
        }
        
        .toggle-inactive .toggle-thumb-medium {
            right: 2px; /* right-0.5 */
            transform: translateX(0);
        }
        
        .toggle-inactive .toggle-thumb-large {
            right: 2px; /* right-0.5 */
            transform: translateX(0);
        }
        
        /* نشط: الدائرة تتحرك لليسار */
        .toggle-active .toggle-thumb-small {
            right: 2px; /* right-0.5 */
            transform: translateX(-16px); /* -translate-x-4 */
        }
        
        .toggle-active .toggle-thumb-medium {
            right: 2px; /* right-0.5 */
            transform: translateX(-20px); /* -translate-x-5 */
        }
        
        .toggle-active .toggle-thumb-large {
            right: 2px; /* right-0.5 */
            transform: translateX(-24px); /* -translate-x-6 */
        }
        
        .toggle-active {
            background-color: #3b82f6; /* bg-blue-500 */
        }
        
        .toggle-inactive {
            background-color: #d1d5db; /* bg-gray-300 */
        }
        
        .toggle-active:hover {
            background-color: #2563eb; /* bg-blue-600 */
        }
        
        .toggle-inactive:hover {
            background-color: #9ca3af; /* bg-gray-400 */
        }
        
        /* خطوط مساعدة لإظهار الحدود */
        .debug-border {
            border: 1px dashed #ef4444;
        }
        
        .calculation-info {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #6b7280;
        }
        
        /* مؤشرات الاتجاه */
        .direction-indicator {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .right-indicator {
            background-color: #fef3c7;
            color: #92400e;
        }
        
        .left-indicator {
            background-color: #dbeafe;
            color: #1e40af;
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto space-y-8">
        <div class="text-center">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">
                اختبار مفتاح التبديل للغة العربية (RTL)
            </h1>
            <p class="text-gray-600">
                السلوك الصحيح: غير نشط = يمين، نشط = يسار
            </p>
        </div>

        <!-- شرح السلوك المطلوب -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-6">
            <h2 class="text-xl font-semibold text-yellow-900 mb-4">
                📋 السلوك المطلوب في التطبيقات العربية
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="bg-white p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-900 mb-2">
                        🔴 حالة غير نشط (OFF)
                    </h3>
                    <p class="text-sm text-gray-600 mb-2">الدائرة في اليمين</p>
                    <span class="direction-indicator right-indicator">يمين ←</span>
                </div>
                <div class="bg-white p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-900 mb-2">
                        🟢 حالة نشط (ON)
                    </h3>
                    <p class="text-sm text-gray-600 mb-2">الدائرة في اليسار</p>
                    <span class="direction-indicator left-indicator">→ يسار</span>
                </div>
            </div>
        </div>

        <!-- اختبار الحجم الصغير -->
        <div class="bg-white rounded-xl p-6 shadow-lg">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">
                الحجم الصغير - 40px × 20px
            </h2>
            
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                        <span class="text-sm font-medium text-gray-700">
                            حالة غير نشط
                        </span>
                        <span class="direction-indicator right-indicator">يمين</span>
                    </div>
                    <div class="toggle-container toggle-small toggle-inactive debug-border" onclick="toggleSwitch(this)">
                        <div class="toggle-thumb toggle-thumb-small"></div>
                    </div>
                </div>
                
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                        <span class="text-sm font-medium text-gray-700">
                            حالة نشط
                        </span>
                        <span class="direction-indicator left-indicator">يسار</span>
                    </div>
                    <div class="toggle-container toggle-small toggle-active debug-border" onclick="toggleSwitch(this)">
                        <div class="toggle-thumb toggle-thumb-small"></div>
                    </div>
                </div>
                
                <div class="calculation-info bg-gray-50 p-3 rounded">
                    <strong>الحسابات للحجم الصغير:</strong><br>
                    عرض الحاوية: 40px<br>
                    عرض الدائرة: 16px<br>
                    غير نشط: الدائرة في اليمين (right: 2px)<br>
                    نشط: الدائرة تتحرك -16px لليسار ✅
                </div>
            </div>
        </div>

        <!-- اختبار الحجم المتوسط -->
        <div class="bg-white rounded-xl p-6 shadow-lg">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">
                الحجم المتوسط - 48px × 24px
            </h2>
            
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                        <span class="text-sm font-medium text-gray-700">
                            حالة غير نشط
                        </span>
                        <span class="direction-indicator right-indicator">يمين</span>
                    </div>
                    <div class="toggle-container toggle-medium toggle-inactive debug-border" onclick="toggleSwitch(this)">
                        <div class="toggle-thumb toggle-thumb-medium"></div>
                    </div>
                </div>
                
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                        <span class="text-sm font-medium text-gray-700">
                            حالة نشط
                        </span>
                        <span class="direction-indicator left-indicator">يسار</span>
                    </div>
                    <div class="toggle-container toggle-medium toggle-active debug-border" onclick="toggleSwitch(this)">
                        <div class="toggle-thumb toggle-thumb-medium"></div>
                    </div>
                </div>
                
                <div class="calculation-info bg-gray-50 p-3 rounded">
                    <strong>الحسابات للحجم المتوسط:</strong><br>
                    عرض الحاوية: 48px<br>
                    عرض الدائرة: 20px<br>
                    غير نشط: الدائرة في اليمين (right: 2px)<br>
                    نشط: الدائرة تتحرك -20px لليسار ✅
                </div>
            </div>
        </div>

        <!-- اختبار الحجم الكبير -->
        <div class="bg-white rounded-xl p-6 shadow-lg">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">
                الحجم الكبير - 56px × 28px
            </h2>
            
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                        <span class="text-sm font-medium text-gray-700">
                            حالة غير نشط
                        </span>
                        <span class="direction-indicator right-indicator">يمين</span>
                    </div>
                    <div class="toggle-container toggle-large toggle-inactive debug-border" onclick="toggleSwitch(this)">
                        <div class="toggle-thumb toggle-thumb-large"></div>
                    </div>
                </div>
                
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                        <span class="text-sm font-medium text-gray-700">
                            حالة نشط
                        </span>
                        <span class="direction-indicator left-indicator">يسار</span>
                    </div>
                    <div class="toggle-container toggle-large toggle-active debug-border" onclick="toggleSwitch(this)">
                        <div class="toggle-thumb toggle-thumb-large"></div>
                    </div>
                </div>
                
                <div class="calculation-info bg-gray-50 p-3 rounded">
                    <strong>الحسابات للحجم الكبير:</strong><br>
                    عرض الحاوية: 56px<br>
                    عرض الدائرة: 24px<br>
                    غير نشط: الدائرة في اليمين (right: 2px)<br>
                    نشط: الدائرة تتحرك -24px لليسار ✅
                </div>
            </div>
        </div>

        <!-- ملاحظات الإصلاح -->
        <div class="bg-green-50 rounded-xl p-6">
            <h2 class="text-xl font-semibold text-green-900 mb-4">
                ✅ تم إصلاح السلوك للغة العربية
            </h2>
            <ul class="space-y-2 text-sm text-green-800">
                <li class="flex items-center">
                    <span class="w-2 h-2 bg-green-500 rounded-full ml-3"></span>
                    <strong>غير نشط:</strong> الدائرة تبدأ من اليمين (right-0.5)
                </li>
                <li class="flex items-center">
                    <span class="w-2 h-2 bg-green-500 rounded-full ml-3"></span>
                    <strong>نشط:</strong> الدائرة تتحرك لليسار باستخدام translate سالب
                </li>
                <li class="flex items-center">
                    <span class="w-2 h-2 bg-green-500 rounded-full ml-3"></span>
                    السلوك يتناسب مع التطبيقات العربية (RTL)
                </li>
                <li class="flex items-center">
                    <span class="w-2 h-2 bg-green-500 rounded-full ml-3"></span>
                    الدائرة تبقى داخل الحاوية في جميع الحالات
                </li>
            </ul>
        </div>

        <!-- تعليمات الاختبار -->
        <div class="bg-blue-50 rounded-xl p-6">
            <h2 class="text-xl font-semibold text-blue-900 mb-4">
                🔍 تعليمات الاختبار
            </h2>
            <ol class="space-y-2 text-sm text-blue-800">
                <li><strong>1.</strong> انقر على أي مفتاح لتغيير حالته</li>
                <li><strong>2.</strong> تأكد من السلوك الصحيح:</li>
                <li class="mr-4">• <strong>غير نشط:</strong> الدائرة في اليمين</li>
                <li class="mr-4">• <strong>نشط:</strong> الدائرة في اليسار</li>
                <li><strong>3.</strong> الدائرة يجب أن تبقى داخل الحدود الحمراء المنقطة</li>
                <li><strong>4.</strong> الحركة يجب أن تكون سلسة وسريعة</li>
            </ol>
        </div>
    </div>

    <script>
        function toggleSwitch(element) {
            if (element.classList.contains('toggle-active')) {
                element.classList.remove('toggle-active');
                element.classList.add('toggle-inactive');
            } else {
                element.classList.remove('toggle-inactive');
                element.classList.add('toggle-active');
            }
        }
    </script>
</body>
</html>