import React, { useEffect, useState, useMemo } from 'react';
import { Shield, AlertCircle, RefreshCw } from '../components/ui/icons';
import { formatDateTime, getCurrentTripoliDateTime } from '../services/dateTimeService';

const DeviceBlocked: React.FC = () => {
  // استخدام خدمة التاريخ والوقت الموجودة في النظام
  const formattedTime = useMemo(() => {
    const time = getCurrentTripoliDateTime();
    return formatDateTime(time, 'datetime');
  }, []);

  const [deviceInfo, setDeviceInfo] = useState({
    publicIP: 'جاري التحميل...',
    localIP: 'جاري التحميل...'
  });

  // دالة للحصول على العنوان العام (الدولي)
  const getPublicIP = async (): Promise<string> => {
    try {
      // محاولة الحصول على IP من خدمات خارجية
      const ipServices = [
        'https://api.ipify.org?format=json',
        'https://ipapi.co/json/',
        'https://httpbin.org/ip'
      ];

      for (const service of ipServices) {
        try {
          const response = await fetch(service, { timeout: 5000 } as any);
          if (response.ok) {
            const data = await response.json();
            const ip = data.ip || data.origin;
            if (ip && ip !== '127.0.0.1' && ip !== 'localhost') {
              console.log(`✅ تم الحصول على IP من ${service}: ${ip}`);
              return ip;
            }
          }
        } catch (error) {
          console.warn(`⚠️ فشل في الحصول على IP من ${service}:`, error);
          continue;
        }
      }

      return 'غير متوفر';
    } catch (error) {
      console.error('❌ خطأ في الحصول على العنوان العام:', error);
      return 'غير متوفر';
    }
  };

  // دالة للحصول على العنوان المحلي من الخلفية
  const getLocalIP = async (): Promise<string> => {
    try {
      // محاولة الحصول على معرف الجهاز من localStorage
      let deviceId = '';
      try {
        const storedDeviceInfo = localStorage.getItem('device_info');
        if (storedDeviceInfo) {
          const deviceInfo = JSON.parse(storedDeviceInfo);
          deviceId = deviceInfo.device_id || deviceInfo.deviceId || '';
        }
      } catch (e) {
        console.warn('⚠️ خطأ في قراءة device_id من localStorage:', e);
      }

      // إنشاء URL مع معرف الجهاز إذا كان متوفراً
      const apiUrl = deviceId ? `/api/device/client-ip?device_id=${encodeURIComponent(deviceId)}` : '/api/device/client-ip';

      // استخدام API endpoint الصحيح
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          // إضافة معرف الجهاز في headers أيضاً
          ...(deviceId && { 'X-Device-Fingerprint': deviceId })
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.client_ip && data.client_ip !== '127.0.0.1' && data.client_ip !== 'غير متوفر') {
          console.log(`✅ تم الحصول على عنوان IP من الخلفية: ${data.client_ip} (المصدر: ${data.source})`);
          return data.client_ip;
        }
      } else {
        console.warn(`⚠️ فشل في الحصول على IP من API: ${response.status} ${response.statusText}`);
      }

      // محاولة الحصول على IP من localStorage إذا كان متوفراً
      const storedDeviceInfo = localStorage.getItem('device_info');
      if (storedDeviceInfo) {
        try {
          const deviceInfo = JSON.parse(storedDeviceInfo);
          if (deviceInfo.client_ip && deviceInfo.client_ip !== '127.0.0.1' && deviceInfo.client_ip !== 'localhost') {
            console.log(`� تم الحصول على IP من localStorage: ${deviceInfo.client_ip}`);
            return deviceInfo.client_ip;
          }
        } catch (e) {
          console.warn('⚠️ خطأ في قراءة device_info من localStorage:', e);
        }
      }

      // fallback: محاولة الحصول على IP من خدمة خارجية
      try {
        const ipResponse = await fetch('https://api.ipify.org?format=json', {
          method: 'GET',
          timeout: 3000
        } as any);
        if (ipResponse.ok) {
          const ipData = await ipResponse.json();
          if (ipData.ip && ipData.ip !== '127.0.0.1') {
            console.log(`🌐 تم الحصول على IP من خدمة خارجية: ${ipData.ip}`);
            return ipData.ip;
          }
        }
      } catch (e) {
        console.warn('⚠️ فشل في الحصول على IP من خدمة خارجية:', e);
      }

      return 'غير متوفر';
    } catch (error) {
      console.error('❌ خطأ في الحصول على العنوان المحلي:', error);
      return 'غير متوفر';
    }
  };

  // تحميل معلومات الجهاز عند بدء التشغيل
  useEffect(() => {
    const loadDeviceInfo = async () => {
      const [publicIP, localIP] = await Promise.all([
        getPublicIP(),
        getLocalIP()
      ]);

      setDeviceInfo({
        publicIP,
        localIP
      });
    };

    loadDeviceInfo();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-red-100 dark:from-gray-900 dark:via-gray-800 dark:to-red-900 flex items-center justify-center p-4 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5 dark:opacity-10">
        <div className="absolute top-10 left-10 w-20 h-20 bg-red-500 rounded-full blur-xl"></div>
        <div className="absolute top-40 right-20 w-32 h-32 bg-red-600 rounded-full blur-xl"></div>
        <div className="absolute bottom-20 left-1/4 w-24 h-24 bg-red-400 rounded-full blur-xl"></div>
        <div className="absolute bottom-40 right-10 w-16 h-16 bg-red-500 rounded-full blur-xl"></div>
      </div>

      <div className="max-w-2xl w-full relative z-10">
        {/* الشعار والعنوان الرئيسي */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl shadow-lg mb-4 transform hover:scale-105 transition-transform duration-300">
            <Shield className="text-white" size={32} />
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-red-600 to-red-700 bg-clip-text text-transparent mb-2">
            تم حظر الجهاز
          </h1>
          <p className="text-gray-600 dark:text-gray-400 text-sm font-medium">
            عذراً، لا يمكنك الوصول إلى هذا النظام. تم حظر جهازك من قبل مدير النظام لأسباب أمنية.
          </p>
        </div>

        {/* البطاقة الرئيسية */}
        <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl shadow-2xl p-8 border border-red-200 dark:border-red-700">
          {/* رسالة التحذير */}
          <div className="relative z-10 flex items-start space-x-4 space-x-reverse mb-8">
            <div className="flex-shrink-0">
              <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center">
                <AlertCircle className="text-white" size={24} />
              </div>
            </div>
            <div className="flex-1">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                تم حظر هذا الجهاز من قبل الخادم
              </h2>
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                تم منع جهازك من الوصول إلى نظام نقاط البيع الذكي لأسباب أمنية.
                هذا الإجراء تم اتخاذه من قبل مدير النظام لحماية البيانات والموارد.
              </p>
            </div>
          </div>

          {/* معلومات الجهاز */}
          <div className="bg-red-50 dark:bg-red-900/30 rounded-xl p-6 mb-8">
            <h3 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-4">
              معلومات الجهاز المحظور:
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-red-700 dark:text-red-300">عنوان IP المحلي:</span>
                <span className="text-red-600 dark:text-red-400 mr-2" id="device-local-ip">
                  {deviceInfo.localIP}
                </span>
              </div>
              <div>
                <span className="font-medium text-red-700 dark:text-red-300">عنوان IP العام:</span>
                <span className="text-red-600 dark:text-red-400 mr-2" id="device-public-ip">
                  {deviceInfo.publicIP}
                </span>
              </div>
              <div>
                <span className="font-medium text-red-700 dark:text-red-300">وقت الحظر:</span>
                <span className="text-red-600 dark:text-red-400 mr-2">
                  {formattedTime}
                </span>
              </div>
            </div>
          </div>

          {/* خطوات الحل */}
          <div className="mb-8">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              ماذا يمكنك فعله؟
            </h3>
            <div className="space-y-4">
              <div className="flex items-start space-x-4 space-x-reverse">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 dark:text-blue-300 font-bold text-sm">1</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white">تواصل مع مدير النظام</h4>
                  <p className="text-gray-600 dark:text-gray-400">
                    اتصل بمدير النظام لمعرفة سبب الحظر وطلب إلغاء الحظر إذا كان خطأً
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4 space-x-reverse">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 dark:text-blue-300 font-bold text-sm">2</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white">تحقق من صلاحياتك</h4>
                  <p className="text-gray-600 dark:text-gray-400">
                    تأكد من أن لديك الصلاحية للوصول إلى هذا النظام من هذا الجهاز
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4 space-x-reverse">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 dark:text-blue-300 font-bold text-sm">3</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white">استخدم جهاز مصرح به</h4>
                  <p className="text-gray-600 dark:text-gray-400">
                    حاول الوصول إلى النظام من جهاز آخر مصرح له بالوصول
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* زر المحاولة مرة أخرى */}
          <div className="mt-8 text-center">
            <button
              onClick={() => window.location.reload()}
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              <RefreshCw className="ml-2" size={18} />
              المحاولة مرة أخرى
            </button>
          </div>
        </div>

        {/* تذييل */}
        <div className="text-center mt-8">
          <p className="text-red-600 dark:text-red-400 text-sm font-medium">
            نظام نقاط البيع الذكي - حماية أمنية متقدمة
          </p>
          <p className="text-red-500 dark:text-red-500 text-xs mt-2 opacity-75">
            رمز الخطأ: DEVICE_BLOCKED_403 | الوقت: {formattedTime}
          </p>
        </div>
      </div>
    </div>
  );
};

export default DeviceBlocked;
