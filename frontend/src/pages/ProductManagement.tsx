/**
 * صفحة إدارة المنتجات
 * إدارة شاملة للمنتجات - البيانات الأساسية والمعلومات (منفصلة عن إدارة المخزون)
 * تتبع النمط الموحد للتطبيق مثل CatalogManagement و Sales
 */

import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FaArrowLeft,
  FaSync,
  FaPlus,
  FaSearch,
  FaFilter,
  FaTrash
} from 'react-icons/fa';
import {
  FiPackage,
  FiSettings,
  FiImage,
  FiBarChart,
  FiAlertTriangle,
  FiCheckCircle,
  FiEye,
  FiEdit,
  FiTrash
} from 'react-icons/fi';

// Import unified components
import { SelectInput, NumberInput } from '../components/inputs';
import FilterActionButtons from '../components/FilterActionButtons';
import SearchInput from '../components/SearchInput';

// Import stores
import useProductStore from '../stores/productStore';

// Import components
import Modal from '../components/Modal';

// Filters interface
interface ProductFilters {
  categoryId: string;
  brandId: string;
  status: string;
  minPrice: string;
  maxPrice: string;
}

const initialFilters: ProductFilters = {
  categoryId: '',
  brandId: '',
  status: '',
  minPrice: '',
  maxPrice: ''
};

interface ProductManagementProps {}

const ProductManagement: React.FC<ProductManagementProps> = () => {
  const navigate = useNavigate();

  // Store hooks
  const {
    products,
    loading,
    error,
    fetchProducts,
    deleteProductAdvanced
  } = useProductStore();

  // Local state
  const [showFilters, setShowFilters] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  
  // Filter states
  const [filters, setFilters] = useState<ProductFilters>(initialFilters);
  const [tempFilters, setTempFilters] = useState<ProductFilters>(initialFilters);

  // Delete modal state
  const [deleteModal, setDeleteModal] = useState({
    isOpen: false,
    productId: null as number | null,
    productName: '',
    isLoading: false
  });

  // Refs
  const initialLoadDone = useRef(false);

  // Initialize data - تحميل المنتجات فقط (المخزون منفصل في نظام المستودعات)
  useEffect(() => {
    if (initialLoadDone.current) return;

    const initializeData = async () => {
      try {
        await fetchProducts();
      } catch (error) {
        console.error('خطأ في تحميل بيانات المنتجات:', error);
      }
    };

    initializeData();
    initialLoadDone.current = true;
  }, []);



  // Refresh data
  const handleRefresh = async () => {
    try {
      await fetchProducts();
    } catch (error) {
      console.error('خطأ في تحديث البيانات:', error);
    }
  };

  // Handle product actions
  const handleAddProduct = () => {
    navigate('/products/new');
  };

  const handleEditProduct = (product: any) => {
    navigate(`/products/${product.id}`);
  };

  const handleDeleteProduct = (product: any) => {
    setDeleteModal({
      isOpen: true,
      productId: product.id,
      productName: product.name,
      isLoading: false
    });
  };

  const confirmDelete = async () => {
    if (!deleteModal.productId) return;

    setDeleteModal(prev => ({ ...prev, isLoading: true }));

    try {
      await deleteProductAdvanced(deleteModal.productId, false);
      setDeleteModal({
        isOpen: false,
        productId: null,
        productName: '',
        isLoading: false
      });
    } catch (error) {
      console.error('خطأ في حذف المنتج:', error);
      setDeleteModal(prev => ({ ...prev, isLoading: false }));
    }
  };

  // Handle view product details
  const handleViewProduct = (product: any) => {
    // Navigate to product details page or show details modal
    console.log('عرض تفاصيل المنتج:', product);
  };

  // Filter functions
  const applyFilters = () => {
    setFilters(tempFilters);
    // Here you would typically call an API with the filters
    console.log('Applying filters:', tempFilters);
  };

  const resetFilters = () => {
    setTempFilters(initialFilters);
    setFilters(initialFilters);
    console.log('Resetting filters');
  };



  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8">
      {/* Header - Following the exact pattern from Sales page */}
      <div className="mb-6">
        <div className="relative rounded-xl bg-gradient-to-l from-primary-50/40 via-primary-50/20 to-white dark:from-primary-900/20 dark:via-primary-900/10 dark:to-gray-800 border border-gray-200 dark:border-gray-700" style={{ boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)' }}>
          {/* خط نقش علوي للتأثير المميز */}
          <div className="absolute -top-0.5 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-primary-300/30 to-transparent dark:from-transparent dark:via-primary-600/20 dark:to-transparent"></div>

          {/* خط نقش سفلي للتأثير المميز */}
          <div className="absolute -bottom-0.5 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-primary-300/30 to-transparent dark:from-transparent dark:via-primary-600/20 dark:to-transparent"></div>
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center p-4 sm:p-6 gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <button
                onClick={() => navigate('/')}
                className="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-xl p-3 hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-200 ease-in-out shadow-lg hover:shadow-xl flex-shrink-0 border-2 border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                title="العودة للرئيسية"
              >
                <FaArrowLeft className="text-sm" />
              </button>
              <div className="mr-3 sm:mr-4 min-w-0 flex-1">
                <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FiPackage className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                  <span className="truncate">إدارة المنتجات</span>
                </h1>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 hidden sm:block">
                  عرض وإدارة جميع المنتجات المسجلة في النظام
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3 sm:gap-4 flex-wrap lg:flex-nowrap">
              <button
                onClick={handleRefresh}
                disabled={loading}
                className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-3 rounded-xl hover:bg-white/50 dark:hover:bg-gray-700/50 transition-all duration-200 ease-in-out backdrop-blur-sm border-2 border-gray-300 dark:border-gray-600 hover:border-primary-400 dark:hover:border-primary-500 hover:shadow-md"
                title="تحديث"
              >
                <FaSync className={`text-sm ${loading ? 'animate-spin' : ''}`} />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4 mb-6">
          <div className="flex items-center">
            <div className="text-red-600 dark:text-red-400 text-sm">
              {error}
            </div>
          </div>
        </div>
      )}

      {/* Stats Cards - بطاقات إحصائية بسيطة وأنيقة مثل صفحة المبيعات */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mb-6">
        {/* عدد المنتجات */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">إجمالي المنتجات</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{products.length}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
              <FiPackage className="text-blue-600 dark:text-blue-400 text-xl" />
            </div>
          </div>
        </div>

        {/* المنتجات النشطة */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="min-w-0 flex-1">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">المنتجات النشطة</p>
              <p className="text-lg font-bold text-green-600 dark:text-green-400 truncate">
                {products.filter((p: any) => p.is_active).length}
              </p>
              <p className="text-xs text-green-500 dark:text-green-400">متاحة للبيع</p>
            </div>
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
              <FiCheckCircle className="text-green-600 dark:text-green-400 text-xl" />
            </div>
          </div>
        </div>

        {/* المنتجات غير النشطة */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="min-w-0 flex-1">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">المنتجات غير النشطة</p>
              <p className="text-lg font-bold text-orange-600 dark:text-orange-400 truncate">
                {products.filter((p: any) => !p.is_active).length}
              </p>
              <p className="text-xs text-orange-500 dark:text-orange-400">غير متاحة</p>
            </div>
            <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
              <FiAlertTriangle className="text-orange-600 dark:text-orange-400 text-xl" />
            </div>
          </div>
        </div>

        {/* المنتجات المصورة */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="min-w-0 flex-1">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">المنتجات المصورة</p>
              <p className="text-lg font-bold text-purple-600 dark:text-purple-400 truncate">
                {products.filter((p: any) => p.image_url || (p.images && p.images.length > 0)).length}
              </p>
              <p className="text-xs text-purple-500 dark:text-purple-400">لها صور</p>
            </div>
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
              <FiImage className="text-purple-600 dark:text-purple-400 text-xl" />
            </div>
          </div>
        </div>

        {/* متوسط السعر */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="min-w-0 flex-1">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">متوسط السعر</p>
              <p className="text-lg font-bold text-indigo-600 dark:text-indigo-400 truncate">
                {products.length > 0
                  ? (products.reduce((sum: number, p: any) => sum + p.price, 0) / products.length).toFixed(2)
                  : '0.00'
                } د.ل
              </p>
              <p className="text-xs text-indigo-500 dark:text-indigo-400">سعر البيع</p>
            </div>
            <div className="w-12 h-12 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
              <FiBarChart className="text-indigo-600 dark:text-indigo-400 text-xl" />
            </div>
          </div>
        </div>

        {/* إجمالي قيمة المخزون */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="min-w-0 flex-1">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">قيمة المخزون</p>
              <p className="text-lg font-bold text-teal-600 dark:text-teal-400 truncate">
                {products.reduce((sum: number, p: any) => sum + (p.price * (p.quantity || 0)), 0).toFixed(2)} د.ل
              </p>
              <p className="text-xs text-teal-500 dark:text-teal-400">بسعر البيع</p>
            </div>
            <div className="w-12 h-12 bg-teal-100 dark:bg-teal-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
              <FiSettings className="text-teal-600 dark:text-teal-400 text-xl" />
            </div>
          </div>
        </div>
      </div>


      {/* Products Table - Following the exact pattern from Sales page */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 overflow-hidden">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center p-6 gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <div className="min-w-0 flex-1">
                <h2 className="text-lg sm:text-xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FiPackage className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                  <span className="truncate">إدارة المنتجات</span>
                </h2>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  عرض وإدارة جميع المنتجات المسجلة في النظام
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <button
                onClick={handleAddProduct}
                className="h-10 bg-primary-600 hover:bg-primary-700 text-white px-6 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium min-w-[140px] focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl"
              >
                <FaPlus className="ml-2 text-sm" />
                <span className="hidden sm:inline lg:inline">إضافة منتج جديد</span>
                <span className="sm:hidden lg:hidden">منتج جديد</span>
              </button>
            </div>
          </div>
        </div>

        {/* Search and Filters Section - Following Sales page pattern */}
        <div className="bg-gray-50 dark:bg-gray-700/30 border-b border-gray-200 dark:border-gray-700 p-6">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
            {/* Search - Using unified SearchInput */}
            <SearchInput
              value={searchTerm}
              onChange={setSearchTerm}
              placeholder="البحث في المنتجات بالاسم، الباركود، الوصف..."
              className="flex-1 min-w-0"
              disabled={loading}
            />

            {/* Filter Toggle - Following Sales page style */}
            <div className="flex-shrink-0">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`h-10 px-4 rounded-xl font-medium transition-all duration-200 flex items-center justify-center min-w-[160px] ${
                  showFilters
                    ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border-2 border-primary-200 dark:border-primary-800'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 border-2 border-gray-200 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600'
                }`}
              >
                <FaFilter className="ml-2" />
                فلاتر متقدمة
              </button>
            </div>
          </div>
        </div>

        {/* Advanced Filters Panel - Following Sales page pattern */}
        {showFilters && (
          <div className="p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/30">
            <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-end">
              {/* Category Filter */}
              <div className="flex-1 min-w-[150px]">
                <SelectInput
                  label="الفئة"
                  name="categoryId"
                  value={tempFilters.categoryId}
                  onChange={(value: string) => {
                    setTempFilters({
                      ...tempFilters,
                      categoryId: value
                    });
                  }}
                  options={[
                    { value: '', label: 'جميع الفئات' },
                    // TODO: Add dynamic category options from store
                    { value: '1', label: 'إلكترونيات' },
                    { value: '2', label: 'ملابس' },
                    { value: '3', label: 'مواد غذائية' },
                    { value: '4', label: 'أجهزة منزلية' }
                  ]}
                  placeholder="اختر الفئة..."
                />
              </div>

              {/* Brand Filter */}
              <div className="flex-1 min-w-[150px]">
                <SelectInput
                  label="العلامة التجارية"
                  name="brandId"
                  value={tempFilters.brandId}
                  onChange={(value: string) => {
                    setTempFilters({
                      ...tempFilters,
                      brandId: value
                    });
                  }}
                  options={[
                    { value: '', label: 'جميع العلامات' },
                    // TODO: Add dynamic brand options from store
                    { value: '1', label: 'سامسونج' },
                    { value: '2', label: 'آبل' },
                    { value: '3', label: 'نايكي' },
                    { value: '4', label: 'أديداس' }
                  ]}
                  placeholder="اختر العلامة التجارية..."
                />
              </div>

              {/* Status Filter */}
              <div className="flex-1 min-w-[120px]">
                <SelectInput
                  label="الحالة"
                  name="status"
                  value={tempFilters.status}
                  onChange={(value: string) => {
                    setTempFilters({
                      ...tempFilters,
                      status: value
                    });
                  }}
                  options={[
                    { value: '', label: 'جميع الحالات' },
                    { value: 'active', label: 'نشط' },
                    { value: 'inactive', label: 'غير نشط' }
                  ]}
                  placeholder="اختر الحالة..."
                />
              </div>

              {/* Price Range - Min Price */}
              <div className="flex-1 min-w-[120px]">
                <NumberInput
                  label="السعر من"
                  name="minPrice"
                  value={tempFilters.minPrice}
                  onChange={(value) => {
                    setTempFilters({
                      ...tempFilters,
                      minPrice: value
                    });
                  }}
                  step="0.01"
                  min={0}
                  placeholder="0.00"
                  dir="ltr"
                  currency="د.ل"
                />
              </div>

              {/* Price Range - Max Price */}
              <div className="flex-1 min-w-[120px]">
                <NumberInput
                  label="السعر إلى"
                  name="maxPrice"
                  value={tempFilters.maxPrice}
                  onChange={(value) => {
                    setTempFilters({
                      ...tempFilters,
                      maxPrice: value
                    });
                  }}
                  step="0.01"
                  min={0}
                  placeholder="0.00"
                  dir="ltr"
                  currency="د.ل"
                />
              </div>

              {/* Action Buttons - في نفس الصف مع مكونات الفلترة */}
              <div className="flex-shrink-0">
                <FilterActionButtons
                  onApplyFilters={() => {
                    console.log('Applying filters and resetting to page 1');
                    setFilters({...tempFilters});
                    setSearchTerm('');
                    // TODO: Fetch data from server with new filters
                  }}
                  onResetFilters={resetFilters}
                  isLoading={loading}
                  className="min-w-[280px]"
                />
              </div>
            </div>
          </div>
        )}

        {/* Products Content - Integrated directly */}
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          </div>
        ) : products.length === 0 ? (
          <div className="text-center py-12">
            <FiPackage className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              لا توجد منتجات
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              ابدأ بإضافة منتجك الأول
            </p>
            <button
              onClick={handleAddProduct}
              className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl flex items-center gap-2 mx-auto transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              <FaPlus className="w-4 h-4" />
              إضافة منتج جديد
            </button>
          </div>
        ) : (
          <div className="overflow-x-auto relative">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700 relative">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    المنتج
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الباركود
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    التسعير
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الفئة والعلامة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider sticky right-0 bg-gray-50 dark:bg-gray-700 z-10 min-w-[140px]">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {products.map((product) => (
                  <tr key={product.id} className="group hover:bg-gray-50 dark:hover:bg-gray-700/50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-lg bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center">
                            <FiPackage className="h-5 w-5 text-primary-600 dark:text-primary-400" />
                          </div>
                        </div>
                        <div className="mr-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {product.name}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {product.description ? product.description.substring(0, 50) + '...' : 'لا يوجد وصف'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white font-mono">
                        {product.barcode || 'غير محدد'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-900 dark:text-white">
                        <span className="text-green-500 ml-1">د.ل</span>
                        {product.price.toLocaleString()}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        التكلفة: {product.cost_price.toLocaleString()} د.ل
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {product.category_rel?.name || 'غير محدد'}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {product.brand_rel?.name || 'غير محدد'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        product.is_active
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                          : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                      }`}>
                        {product.is_active ? 'نشط' : 'غير نشط'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium sticky right-0 bg-white dark:bg-gray-800 group-hover:bg-gray-50 dark:group-hover:bg-gray-700/50 z-10 min-w-[140px]">
                      <div className="flex items-center justify-center gap-1">
                        <button
                          onClick={() => handleViewProduct(product)}
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 transition-colors duration-150 p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 border border-transparent hover:border-blue-200 dark:hover:border-blue-700"
                          title="عرض التفاصيل"
                        >
                          <FiEye className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleEditProduct(product)}
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 transition-colors duration-150 p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 border border-transparent hover:border-blue-200 dark:hover:border-blue-700"
                          title="تعديل المنتج"
                        >
                          <FiEdit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteProduct(product)}
                          className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 transition-colors duration-150 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 border border-transparent hover:border-red-200 dark:hover:border-red-700"
                          title="حذف المنتج"
                        >
                          <FiTrash className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({
          isOpen: false,
          productId: null,
          productName: '',
          isLoading: false
        })}
        title="تأكيد حذف المنتج"
        size="sm"
      >
        <div className="p-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
              <FaTrash className="w-8 h-8 text-red-600 dark:text-red-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              حذف المنتج
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              هل أنت متأكد من رغبتك في حذف المنتج "{deleteModal.productName}"؟
              <br />
              لا يمكن التراجع عن هذا الإجراء.
            </p>
            <div className="flex items-center gap-3 justify-center">
              <button
                onClick={() => setDeleteModal({
                  isOpen: false,
                  productId: null,
                  productName: '',
                  isLoading: false
                })}
                disabled={deleteModal.isLoading}
                className="px-6 py-3 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-xl transition-all duration-200 disabled:opacity-50"
              >
                إلغاء
              </button>
              <button
                onClick={confirmDelete}
                disabled={deleteModal.isLoading}
                className="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-xl flex items-center gap-2 transition-all duration-200 disabled:opacity-50"
              >
                {deleteModal.isLoading ? (
                  <FaSync className="w-4 h-4 animate-spin" />
                ) : (
                  <FaTrash className="w-4 h-4" />
                )}
                حذف
              </button>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default ProductManagement;
