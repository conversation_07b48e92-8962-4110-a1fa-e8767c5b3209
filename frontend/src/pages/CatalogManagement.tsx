import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  FaArrowLeft,
  FaTag,
  FaTags,
  FaCertificate,
  FaRuler,
  FaSync
} from 'react-icons/fa';
import { FiLayers, FiPercent } from 'react-icons/fi';
import { useTheme } from '../contexts/ThemeContext';
import useCategoryStore from '../stores/categoryStore';
import useBrandStore from '../stores/brandStore';
import useUnitStore from '../stores/unitStore';
import useVariantAttributeStore from '../stores/variantAttributeStore';
import { CategoriesTab, BrandsTab, UnitsTab, VariantAttributesTab, TaxTypesTab } from '../components/catalog';

const CatalogManagement: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  useTheme();
  
  // Store hooks
  const {
    loading: categoriesLoading,
    error: categoriesError,
    fetchCategories,
    clearError: clearCategoriesError
  } = useCategoryStore();

  const {
    loading: brandsLoading,
    error: brandsError,
    fetchBrands,
    clearError: clearBrandsError
  } = useBrandStore();

  const {
    loading: unitsLoading,
    error: unitsError,
    fetchUnits,
    clearError: clearUnitsError
  } = useUnitStore();

  const {
    loading: variantAttributesLoading,
    error: variantAttributesError,
    fetchAttributes,
    clearError: clearVariantAttributesError
  } = useVariantAttributeStore();

  // State
  const [activeTab, setActiveTab] = useState<'categories' | 'brands' | 'units' | 'variant-attributes' | 'tax-types'>('categories');
  
  // Refs for initial load control
  const initialLoadDone = useRef(false);

  // Set active tab based on current route
  useEffect(() => {
    const path = location.pathname;
    if (path === '/categories') {
      setActiveTab('categories');
    } else if (path === '/brands') {
      setActiveTab('brands');
    } else if (path === '/units') {
      setActiveTab('units');
    } else if (path === '/variant-attributes') {
      setActiveTab('variant-attributes');
    } else if (path === '/tax-types') {
      setActiveTab('tax-types');
    }
  }, [location.pathname]);

  // Load data on component mount
  useEffect(() => {
    if (initialLoadDone.current) return;

    // Load initial data
    fetchCategories();
    fetchBrands();
    fetchUnits();
    fetchAttributes();

    initialLoadDone.current = true;
  }, []);

  // Clear errors when switching tabs
  useEffect(() => {
    clearCategoriesError();
    clearBrandsError();
    clearUnitsError();
    clearVariantAttributesError();
  }, [activeTab]);

  const handleRefresh = () => {
    switch (activeTab) {
      case 'categories':
        fetchCategories();
        break;
      case 'brands':
        fetchBrands();
        break;
      case 'units':
        fetchUnits();
        break;
      case 'variant-attributes':
        fetchAttributes();
        break;
    }
  };

  const isLoading = categoriesLoading || brandsLoading || unitsLoading || variantAttributesLoading;
  const currentError = categoriesError || brandsError || unitsError || variantAttributesError;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8">
      {/* Header */}
      <div className="mb-6">
        <div className="relative rounded-xl bg-gradient-to-l from-primary-50/40 via-primary-50/20 to-white dark:from-primary-900/20 dark:via-primary-900/10 dark:to-gray-800 border border-gray-200 dark:border-gray-700" style={{ boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)' }}>
          {/* خط نقش علوي للتأثير المميز */}
          <div className="absolute -top-0.5 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-primary-300/30 to-transparent dark:from-transparent dark:via-primary-600/20 dark:to-transparent"></div>

          {/* خط نقش سفلي للتأثير المميز */}
          <div className="absolute -bottom-0.5 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-primary-300/30 to-transparent dark:from-transparent dark:via-primary-600/20 dark:to-transparent"></div>
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center p-4 sm:p-6 gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <button
                onClick={() => navigate('/')}
                className="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-xl p-3 hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-200 ease-in-out shadow-lg hover:shadow-xl flex-shrink-0 border-2 border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                title="العودة للرئيسية"
              >
                <FaArrowLeft className="text-sm" />
              </button>
              <div className="mr-3 sm:mr-4 min-w-0 flex-1">
                <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FaTags className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                  <span className="truncate">إدارة الفهرس</span>
                </h1>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 hidden sm:block">
                  إدارة الفئات والعلامات التجارية والوحدات وخصائص المتغيرات
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 sm:gap-4 flex-wrap lg:flex-nowrap">
              <button
                onClick={handleRefresh}
                disabled={isLoading}
                className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-3 rounded-xl hover:bg-white/50 dark:hover:bg-gray-700/50 transition-all duration-200 ease-in-out backdrop-blur-sm border-2 border-gray-300 dark:border-gray-600 hover:border-primary-400 dark:hover:border-primary-500 hover:shadow-md disabled:opacity-50"
                title="تحديث"
              >
                <FaSync className={`text-sm ${isLoading ? 'animate-spin' : ''}`} />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {currentError && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4 mb-6">
          <div className="flex items-center">
            <div className="text-red-600 dark:text-red-400 text-sm">
              {currentError}
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft mb-6 border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex">
            <button
              onClick={() => {
                setActiveTab('categories');
                navigate('/categories');
              }}
              className={`py-4 px-6 border-b-2 font-medium text-sm flex items-center transition-all duration-200 ease-in-out ${
                activeTab === 'categories'
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
              }`}
            >
              <FaTag className="ml-2" />
              الفئات
            </button>

            <button
              onClick={() => {
                setActiveTab('brands');
                navigate('/brands');
              }}
              className={`py-4 px-6 border-b-2 font-medium text-sm flex items-center transition-all duration-200 ease-in-out ${
                activeTab === 'brands'
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
              }`}
            >
              <FaCertificate className="ml-2" />
              العلامات التجارية
            </button>

            <button
              onClick={() => {
                setActiveTab('units');
                navigate('/units');
              }}
              className={`py-4 px-6 border-b-2 font-medium text-sm flex items-center transition-all duration-200 ease-in-out ${
                activeTab === 'units'
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
              }`}
            >
              <FaRuler className="ml-2" />
              الوحدات
            </button>

            <button
              onClick={() => {
                setActiveTab('variant-attributes');
                navigate('/variant-attributes');
              }}
              className={`py-4 px-6 border-b-2 font-medium text-sm flex items-center transition-all duration-200 ease-in-out ${
                activeTab === 'variant-attributes'
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
              }`}
            >
              <FiLayers className="ml-2" />
              خصائص المتغيرات
            </button>

            <button
              onClick={() => {
                setActiveTab('tax-types');
                navigate('/tax-types');
              }}
              className={`py-4 px-6 border-b-2 font-medium text-sm flex items-center transition-all duration-200 ease-in-out ${
                activeTab === 'tax-types'
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
              }`}
            >
              <FiPercent className="ml-2" />
              أنواع الضرائب
            </button>
          </nav>
        </div>
      </div>



      {/* Content based on active tab */}
      {activeTab === 'categories' && <CategoriesTab />}
      {activeTab === 'brands' && <BrandsTab />}
      {activeTab === 'units' && <UnitsTab />}
      {activeTab === 'variant-attributes' && <VariantAttributesTab />}
      {activeTab === 'tax-types' && <TaxTypesTab />}
    </div>
  );
};

export default CatalogManagement;
