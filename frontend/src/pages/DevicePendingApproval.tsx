import React, { useEffect, useState, useRef, useMemo } from 'react';
import { Clock, RefreshCw, Shield, Globe, FileText, User, AlertCircle, MessageCircle, Phone, Mail, Wifi } from '../components/ui/icons';
import { formatDateTime, getCurrentTripoliDateTime } from '../services/dateTimeService';
// import FingerprintAnalytics from '../components/FingerprintAnalytics'; // معطل لتوفير موارد النظام

const DevicePendingApproval: React.FC = () => {
  // استخدام useMemo لحفظ الوقت المنسق مرة واحدة فقط
  const formattedTime = useMemo(() => {
    const time = getCurrentTripoliDateTime();
    return formatDateTime(time, 'datetime');
  }, []); // dependency array فارغة = يتم حسابها مرة واحدة فقط

  const [isChecking, setIsChecking] = useState(false);
  const hasInitialized = useRef(false);
  const [deviceInfo, setDeviceInfo] = useState({
    publicIP: 'جاري التحميل...',
    localIP: 'جاري التحميل...',
    fingerprintId: 'جاري التحميل...',
    fullFingerprintId: '', // المعرف الكامل للبصمة
    requestId: 'جاري التحميل...',
    hardwareFingerprint: 'جاري التحميل...',
    storageFingerprint: 'جاري التحميل...',
    systemFingerprint: 'جاري التحميل...',
    deviceStability: 'جاري التحقق...'
  });

  // دالة للحصول على العنوان العام (الدولي)
  const getPublicIP = async (): Promise<string> => {
    try {
      // محاولة الحصول على IP من خدمات خارجية
      const ipServices = [
        'https://api.ipify.org?format=json',
        'https://ipapi.co/json/',
        'https://httpbin.org/ip'
      ];

      for (const service of ipServices) {
        try {
          const response = await fetch(service, { timeout: 5000 } as any);
          if (response.ok) {
            const data = await response.json();
            const ip = data.ip || data.origin;
            if (ip && ip !== '127.0.0.1' && ip !== 'localhost') {
              console.log(`✅ تم الحصول على IP من ${service}: ${ip}`);
              return ip;
            }
          }
        } catch (error) {
          console.warn(`⚠️ فشل في الحصول على IP من ${service}:`, error);
          continue;
        }
      }

      // إذا فشلت الخدمات الخارجية
      console.warn('⚠️ فشل في الحصول على العنوان العام من جميع الخدمات');
      return 'غير متوفر';
    } catch (error) {
      console.error('❌ خطأ في الحصول على العنوان العام:', error);
      return 'غير متوفر';
    }
  };

  // دالة للحصول على العنوان المحلي للجهاز من الخلفية
  const getLocalIP = async (): Promise<string> => {
    try {
      // الحصول على معرف الجهاز الحالي من localStorage
      const deviceId = localStorage.getItem('device_fingerprint') ||
                      localStorage.getItem('device_id') ||
                      sessionStorage.getItem('device_fingerprint') ||
                      sessionStorage.getItem('device_id');

      console.log(`🔍 البحث عن عنوان IP للجهاز: ${deviceId || 'غير محدد'}`);

      // استدعاء API مع معرف الجهاز
      const url = deviceId ? `/api/device/client-ip?device_id=${encodeURIComponent(deviceId)}` : '/api/device/client-ip';

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.client_ip && data.client_ip !== '127.0.0.1' && data.client_ip !== 'غير متوفر') {
          console.log(`✅ تم الحصول على عنوان IP من الخلفية: ${data.client_ip} (المصدر: ${data.source})`);
          return data.client_ip;
        }
      }

      // fallback: استخدام عنوان الخادم
      const serverIP = window.location.hostname;
      if (serverIP && serverIP !== 'localhost' && serverIP !== '127.0.0.1') {
        console.log(`📍 استخدام عنوان الخادم كـ fallback: ${serverIP}`);
        return serverIP;
      }

      return 'غير متوفر';
    } catch (error) {
      console.error('❌ خطأ في الحصول على العنوان المحلي:', error);
      return 'غير متوفر';
    }
  };

  // دالة فحص حالة الجهاز
  const checkDeviceStatus = async () => {
    if (isChecking) {
      console.log('🔍 Already checking device status, skipping...');
      return;
    }

    setIsChecking(true);
    try {
      // الحصول على بصمة الجهاز من الخدمة الموحدة
      const unifiedDeviceFingerprint = (await import('../services/unifiedDeviceFingerprint')).default;
      const fingerprint = await unifiedDeviceFingerprint.getFingerprint();

      // إنشاء headers مع البصمة
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (fingerprint) {
        headers['X-Device-Fingerprint'] = fingerprint.deviceId || '';
        headers['X-Device-Hardware'] = fingerprint.hardwareFingerprint || '';
        headers['X-Device-Storage'] = fingerprint.storageFingerprint || '';
        headers['X-Device-System'] = fingerprint.systemFingerprint || '';
        headers['X-Device-Timestamp'] = fingerprint.timestamp || '';
      }

      console.log('🔍 Checking device status with fingerprint:', fingerprint?.deviceId);

      const response = await fetch('/api/device/status', {
        method: 'GET',
        headers
      });

      if (response.ok) {
        const data = await response.json();
        console.log('🔍 Device status check result:', data);

        if (data.status === 'allowed') {
          console.log('✅ Device approved, redirecting to main app');

          // الحصول على الرابط الذكي من الاستجابة أولاً، ثم الرابط الأصلي
          const urlParams = new URLSearchParams(window.location.search);
          const originalUrl = data.smart_redirect_url || data.original_url || urlParams.get('original_url') || data.redirect_url;

          console.log('🔍 Available redirect URLs:');
          console.log('   - smart_redirect_url:', data.smart_redirect_url);
          console.log('   - original_url:', data.original_url);
          console.log('   - url_param original_url:', urlParams.get('original_url'));
          console.log('   - redirect_url:', data.redirect_url);
          console.log('   - Final selected URL:', originalUrl);

          let redirectUrl;
          if (originalUrl && originalUrl !== 'null' && originalUrl !== 'undefined') {
            // التحقق من صحة الرابط الأصلي
            try {
              const parsedUrl = new URL(originalUrl);
              // التأكد من أن الرابط محلي وآمن
              if (parsedUrl.hostname === window.location.hostname ||
                  parsedUrl.hostname === '*************' ||
                  parsedUrl.hostname === 'localhost') {
                redirectUrl = originalUrl;
                console.log('🔄 Redirecting to original URL:', redirectUrl);
              } else {
                throw new Error('رابط غير آمن');
              }
            } catch (error) {
              console.warn('⚠️ رابط أصلي غير صحيح، استخدام الرابط الافتراضي:', error);
              redirectUrl = `http://${window.location.hostname}:5175/`;
            }
          } else {
            // استخدام الرابط الافتراضي مع المنفذ الصحيح
            redirectUrl = `http://${window.location.hostname}:5175/`;
            console.log('🔄 No original URL found, using default:', redirectUrl);
          }

          console.log('🔄 Final redirect URL:', redirectUrl);

          // إضافة تأخير قصير لضمان عرض الرسالة
          console.log('✅ تم قبول جهازك! جاري التوجيه...');
          setTimeout(() => {
            window.location.href = redirectUrl;
          }, 1000);

          return;
        } else if (data.status === 'blocked') {
          console.log('❌ Device blocked, redirecting to blocked page');
          const frontendUrl = `http://${window.location.hostname}:5175/device-blocked`;
          console.log('🔄 Redirecting to:', frontendUrl);
          window.location.href = frontendUrl;
          return;
        } else {
          console.log('⏳ Device still pending approval');
        }
      } else {
        console.warn('Failed to check device status:', response.status);
      }
    } catch (error) {
      console.warn('Failed to check device status:', error);
    } finally {
      setIsChecking(false);
    }
  };

  const storeComprehensiveFingerprint = async (deviceInfo: any) => {
    try {
      console.log('📝 تخزين البصمة الشاملة...');

      const response = await fetch('/api/comprehensive-fingerprint/store-pending-approval', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Device-Fingerprint': deviceInfo.fullFingerprintId,
          'X-Device-Hardware': deviceInfo.fullHardwareFingerprint,
          'X-Device-Storage': deviceInfo.fullStorageFingerprint,
          'X-Device-Screen': deviceInfo.screenFingerprint,
          'X-Device-System': deviceInfo.systemFingerprint,
          'X-Device-Timestamp': Date.now().toString()
        }
      });

      const data = await response.json();
      if (data.success) {
        console.log('✅ تم تخزين البصمة الشاملة بنجاح:', data.fingerprint_id);
        console.log('📊 معلومات الجهاز المحفوظة:', data.data);
      } else {
        console.warn('⚠️ فشل في تخزين البصمة الشاملة:', data.error);
      }
    } catch (error) {
      console.error('❌ خطأ في تخزين البصمة الشاملة:', error);
    }
  };

  useEffect(() => {
    // منع التحميل المتكرر في React StrictMode
    if (hasInitialized.current) {
      console.log('🔍 DevicePendingApproval already initialized, skipping...');
      return;
    }

    hasInitialized.current = true;
    console.log('🔍 DevicePendingApproval component mounted');
    console.log('🔍 Current URL:', window.location.href);
    console.log('🔍 Current hostname:', window.location.hostname);
    console.log('🔍 Current port:', window.location.port);

    // تحديث معلومات الجهاز
    const updateDeviceInfo = async () => {
      try {
        const unifiedDeviceFingerprint = (await import('../services/unifiedDeviceFingerprint')).default;
        const fingerprint = await unifiedDeviceFingerprint.getFingerprint();

        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 7);
        const requestId = `REQ-${timestamp}-${random}`.toUpperCase();

        // الحصول على العنوانين العام والمحلي
        const [publicIP, localIP] = await Promise.all([
          getPublicIP(),
          getLocalIP()
        ]);

        const newDeviceInfo = {
          publicIP: publicIP,
          localIP: localIP,
          fingerprintId: fingerprint?.deviceId ? fingerprint.deviceId.slice(0, 12) + '...' : 'غير متوفر',
          fullFingerprintId: fingerprint?.deviceId || '', // المعرف الكامل
          requestId: requestId,
          hardwareFingerprint: fingerprint?.hardwareFingerprint ? fingerprint.hardwareFingerprint.slice(0, 8) + '...' : 'غير متوفر',
          storageFingerprint: fingerprint?.storageFingerprint ? fingerprint.storageFingerprint.slice(0, 8) + '...' : 'غير متوفر',
          systemFingerprint: fingerprint?.systemFingerprint || 'غير متوفر',
          deviceStability: 'ثابتة - لا تتأثر بالإعدادات',
          // إضافة البصمات الكاملة للتخزين
          fullHardwareFingerprint: fingerprint?.hardwareFingerprint || '',
          fullStorageFingerprint: fingerprint?.storageFingerprint || ''
        };

        setDeviceInfo(newDeviceInfo);

        console.log('🔍 Device info updated:', {
          publicIP: publicIP,
          localIP: localIP,
          fingerprintId: fingerprint?.deviceId,
          requestId: requestId
        });

        // تخزين البصمة الشاملة في قاعدة البيانات
        await storeComprehensiveFingerprint(newDeviceInfo);
      } catch (error) {
        console.warn('خطأ في تحديث معلومات الجهاز:', error);
      }
    };

    updateDeviceInfo();

    // تعطيل تحديث الوقت المستمر لتجنب إعادة التحديث المزعجة
    // الوقت سيتم تعيينه مرة واحدة عند تحميل الصفحة
    // const timer = setInterval(() => {
    //   setCurrentTime(getCurrentTripoliDateTime());
    // }, 60000);

    // تعطيل الفحص الدوري التلقائي لتجنب إعادة التحميل المستمر
    // المستخدم يمكنه استخدام زر "فحص الحالة الآن" للفحص اليدوي
    // const statusCheckInterval = setInterval(() => {
    //   console.log('🔄 Automatic status check...');
    //   checkDeviceStatus();
    // }, 30000);

    // تعطيل الفحص الأولي التلقائي لتجنب إزعاج النظام
    // المستخدم يمكنه استخدام زر "فحص الحالة الآن" للفحص اليدوي
    // const initialCheckTimeout = setTimeout(() => {
    //   console.log('🔄 Initial status check...');
    //   checkDeviceStatus();
    // }, 5000);

    return () => {
      // clearInterval(timer); // معطل لأن timer معطل
      // clearInterval(statusCheckInterval); // معطل لأن statusCheckInterval معطل
      // clearTimeout(initialCheckTimeout); // معطل لأن initialCheckTimeout معطل
    };
  }, []);

  const handleRefresh = () => {
    console.log('🔄 Manual refresh requested...');
    // بدلاً من إعادة تحميل الصفحة، نقوم بفحص الحالة مرة أخرى
    checkDeviceStatus();
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-4 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5 dark:opacity-10">
        <div className="absolute top-10 left-10 w-20 h-20 bg-primary-500 rounded-full blur-xl"></div>
        <div className="absolute top-40 right-20 w-32 h-32 bg-purple-500 rounded-full blur-xl"></div>
        <div className="absolute bottom-20 left-1/4 w-24 h-24 bg-blue-500 rounded-full blur-xl"></div>
        <div className="absolute bottom-40 right-10 w-16 h-16 bg-green-500 rounded-full blur-xl"></div>
      </div>

      <div className="max-w-2xl w-full relative z-10">
        {/* الشعار والعنوان الرئيسي */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl shadow-lg mb-4 transform hover:scale-105 transition-transform duration-300">
            <Shield className="text-white" size={32} />
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent mb-2">
            النظام قيد وصول جهازك
          </h1>
          <p className="text-gray-600 dark:text-gray-400 text-sm font-medium">
            نظام نقاط البيع الذكي محمي وآمن. جهازك قيد المراجعة حتى يتم التحقق من صلاحياتك والموافقة على وصولك للنظام.
          </p>
        </div>

        {/* البطاقة الرئيسية */}
        <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 dark:border-gray-700/50 p-8 relative overflow-hidden">
          {/* Card Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute -top-10 -right-10 w-40 h-40 bg-primary-500 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-10 -left-10 w-32 h-32 bg-purple-500 rounded-full blur-3xl"></div>
          </div>

          {/* رسالة الانتظار */}
          <div className="relative z-10 flex items-start space-x-4 space-x-reverse mb-8">
            <div className="flex-shrink-0">
              <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                <Shield className="text-white" size={24} />
              </div>
            </div>
            <div className="flex-1">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                نظام محمي وآمن
              </h2>
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                جهازك قيد المراجعة الأمنية. النظام يتحقق من صلاحياتك وسيتم منحك الوصول فور الموافقة على طلبك.
              </p>
            </div>
          </div>

          {/* معلومات الجهاز */}
          <div className="relative z-10 bg-gray-50 dark:bg-gray-700/50 rounded-xl p-6 mb-8">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <Shield className="text-green-600 ml-2" size={20} />
              معلومات الجهاز قيد المراجعة
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
              <div className="flex items-center p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center ml-3">
                  <Globe className="text-blue-600 dark:text-blue-400" size={16} />
                </div>
                <div className="flex-1">
                  <span className="font-medium text-gray-700 dark:text-gray-300 block">العنوان العام</span>
                  <span className="text-blue-600 dark:text-blue-400 font-mono text-xs">
                    {deviceInfo.publicIP}
                  </span>
                </div>
              </div>
              <div className="flex items-center p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                <div className="w-8 h-8 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center ml-3">
                  <Wifi className="text-orange-600 dark:text-orange-400" size={16} />
                </div>
                <div className="flex-1">
                  <span className="font-medium text-gray-700 dark:text-gray-300 block">العنوان المحلي</span>
                  <span className="text-orange-600 dark:text-orange-400 font-mono text-xs">
                    {deviceInfo.localIP}
                  </span>
                </div>
              </div>
              <div className="flex items-center p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center ml-3">
                  <Clock className="text-green-600 dark:text-green-400" size={16} />
                </div>
                <div className="flex-1">
                  <span className="font-medium text-gray-700 dark:text-gray-300 block">وقت الطلب</span>
                  <span className="text-green-600 dark:text-green-400 text-xs">
                    {formattedTime}
                  </span>
                </div>
              </div>
              <div className="flex items-center p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center ml-3">
                  <FileText className="text-purple-600 dark:text-purple-400" size={16} />
                </div>
                <div className="flex-1">
                  <span className="font-medium text-gray-700 dark:text-gray-300 block">معرف الطلب</span>
                  <span className="text-purple-600 dark:text-purple-400 font-mono text-xs">
                    {deviceInfo.requestId}
                  </span>
                </div>
              </div>
              <div className="flex items-center p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                <div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg flex items-center justify-center ml-3">
                  <User className="text-indigo-600 dark:text-indigo-400" size={16} />
                </div>
                <div className="flex-1">
                  <span className="font-medium text-gray-700 dark:text-gray-300 block">معرف الجهاز</span>
                  <span className="text-indigo-600 dark:text-indigo-400 font-mono text-xs">
                    {deviceInfo.fingerprintId}
                  </span>
                </div>
              </div>
              <div className="flex items-center p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                <div className="w-8 h-8 bg-amber-100 dark:bg-amber-900/30 rounded-lg flex items-center justify-center ml-3">
                  <AlertCircle className="text-amber-600 dark:text-amber-400" size={16} />
                </div>
                <div className="flex-1">
                  <span className="font-medium text-gray-700 dark:text-gray-300 block">حالة الجهاز</span>
                  <span className="text-amber-600 dark:text-amber-400 font-medium">
                    قيد المراجعة الأمنية
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* معلومات البصمة الأمنية */}
          <div className="relative z-10 mb-8">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
              <Shield className="text-green-600 ml-2" size={20} />
              بصمة الجهاز الأمنية
            </h3>
            <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-6 border border-green-200 dark:border-green-800">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                  <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center ml-3">
                    <Shield className="text-blue-600 dark:text-blue-400" size={16} />
                  </div>
                  <div className="flex-1">
                    <span className="font-medium text-gray-700 dark:text-gray-300 block">بصمة الأجهزة</span>
                    <span className="text-blue-600 dark:text-blue-400 font-mono text-xs">
                      {deviceInfo.hardwareFingerprint}
                    </span>
                  </div>
                </div>
                <div className="flex items-center p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                  <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center ml-3">
                    <FileText className="text-purple-600 dark:text-purple-400" size={16} />
                  </div>
                  <div className="flex-1">
                    <span className="font-medium text-gray-700 dark:text-gray-300 block">بصمة التخزين</span>
                    <span className="text-purple-600 dark:text-purple-400 font-mono text-xs">
                      {deviceInfo.storageFingerprint}
                    </span>
                  </div>
                </div>
                <div className="flex items-center p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                  <div className="w-8 h-8 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center ml-3">
                    <Globe className="text-orange-600 dark:text-orange-400" size={16} />
                  </div>
                  <div className="flex-1">
                    <span className="font-medium text-gray-700 dark:text-gray-300 block">بصمة النظام</span>
                    <span className="text-orange-600 dark:text-orange-400 font-mono text-xs">
                      {deviceInfo.systemFingerprint}
                    </span>
                  </div>
                </div>
                <div className="flex items-center p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                  <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center ml-3">
                    <Shield className="text-green-600 dark:text-green-400" size={16} />
                  </div>
                  <div className="flex-1">
                    <span className="font-medium text-gray-700 dark:text-gray-300 block">ثبات البصمة</span>
                    <span className="text-green-600 dark:text-green-400 font-medium text-xs">
                      {deviceInfo.deviceStability}
                    </span>
                  </div>
                </div>
              </div>

              {/* شرح البصمة الأمنية */}
              <div className="mt-4 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2 flex items-center">
                  <Shield className="text-green-600 ml-2" size={16} />
                  ما هي بصمة الجهاز؟
                </h4>
                <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed mb-3">
                  بصمة الجهاز هي معرف فريد وثابت يتم إنشاؤه بناءً على خصائص جهازك الثابتة مثل المعالج وكرت الرسوميات والذاكرة.
                  هذه البصمة لا تتغير حتى لو قمت بتغيير المتصفح أو اللغة أو المنطقة الزمنية.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-xs">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-gray-600 dark:text-gray-400">مقاومة للتغيير</span>
                  </div>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-600 dark:text-gray-400">مشفرة بأمان</span>
                  </div>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span className="text-gray-600 dark:text-gray-400">فريدة لكل جهاز</span>
                  </div>
                </div>
              </div>
            </div>
          </div>



          {/* خطوات التالية */}
          <div className="relative z-10 mb-8">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
              <Shield className="text-green-600 ml-2" size={20} />
              مراحل التحقق الأمني
            </h3>
            <div className="space-y-4">
              <div className="flex items-start space-x-4 space-x-reverse">
                <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-lg">
                  <span className="text-white font-bold text-sm">1</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white">فحص بصمة الجهاز</h4>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    النظام يتحقق من هوية جهازك وخصائصه الأمنية
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4 space-x-reverse">
                <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg">
                  <span className="text-white font-bold text-sm">2</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white">مراجعة الصلاحيات</h4>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    مدير النظام يراجع صلاحياتك ويتخذ قرار الموافقة
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4 space-x-reverse">
                <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center shadow-lg">
                  <span className="text-white font-bold text-sm">3</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white">منح الوصول الآمن</h4>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    ستحصل على وصول آمن للنظام فور اكتمال المراجعة
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* معلومات مهمة */}
          <div className="relative z-10 bg-green-50 dark:bg-green-900/20 rounded-xl p-6 mb-6 border border-green-200 dark:border-green-800">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <Shield className="text-green-600 ml-2" size={20} />
              معلومات الأمان
            </h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-3 space-x-reverse">
                <div className="w-6 h-6 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mt-0.5">
                  <Shield className="text-green-600 dark:text-green-400" size={12} />
                </div>
                <p className="text-gray-700 dark:text-gray-300 text-sm">
                  النظام محمي بطبقات أمان متعددة لضمان سلامة بياناتك
                </p>
              </div>
              <div className="flex items-start space-x-3 space-x-reverse">
                <div className="w-6 h-6 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mt-0.5">
                  <RefreshCw className="text-green-600 dark:text-green-400" size={12} />
                </div>
                <p className="text-gray-700 dark:text-gray-300 text-sm">
                  استخدم زر "فحص الحالة الآن" للتحقق من حالة المراجعة الأمنية
                </p>
              </div>
            </div>
          </div>

          {/* معلومات الاتصال */}
          <div className="relative z-10 bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6 mb-6 border border-blue-200 dark:border-blue-800">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <MessageCircle className="text-blue-600 ml-2" size={20} />
              تحتاج مساعدة؟
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-3 space-x-reverse p-3 bg-white dark:bg-gray-800 rounded-lg">
                <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                  <Phone className="text-blue-600 dark:text-blue-400" size={18} />
                </div>
                <div>
                  <p className="font-medium text-gray-900 dark:text-white text-sm">اتصل بالمدير</p>
                  <p className="text-gray-600 dark:text-gray-400 text-xs">للاستفسار عن حالة طلبك</p>
                </div>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse p-3 bg-white dark:bg-gray-800 rounded-lg">
                <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                  <Mail className="text-green-600 dark:text-green-400" size={18} />
                </div>
                <div>
                  <p className="font-medium text-gray-900 dark:text-white text-sm">راسل الدعم</p>
                  <p className="text-gray-600 dark:text-gray-400 text-xs">للحصول على مساعدة فنية</p>
                </div>
              </div>
            </div>
          </div>

          {/* حالة الفحص */}
          {isChecking && (
            <div className="relative z-10 bg-primary-50 dark:bg-primary-900/20 rounded-xl p-4 mb-6 border border-primary-200 dark:border-primary-800">
              <div className="flex items-center justify-center space-x-3 space-x-reverse">
                <div className="w-8 h-8 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center">
                  <div className="w-4 h-4 border-2 border-primary-600 border-t-transparent rounded-full animate-spin"></div>
                </div>
                <div className="text-center">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    جاري فحص حالة الجهاز...
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* زر فحص الحالة */}
          <div className="relative z-10 text-center">
            <button
              onClick={handleRefresh}
              disabled={isChecking}
              className={`inline-flex items-center justify-center px-8 py-3 font-medium rounded-xl transition-all duration-200 transform shadow-lg ${
                isChecking
                  ? 'bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                  : 'bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white hover:scale-[1.02]'
              }`}
            >
              <RefreshCw className={`ml-2 ${isChecking ? 'animate-spin' : ''}`} size={18} />
              {isChecking ? 'جاري الفحص...' : 'فحص الحالة الآن'}
            </button>
            <p className="text-gray-500 dark:text-gray-400 text-xs mt-3">
              💡 استخدم الزر أعلاه للفحص اليدوي - لا يوجد تحديث تلقائي لتوفير موارد النظام
            </p>
          </div>
        </div>

        {/* تذييل */}
        <div className="text-center mt-8">
          <div className="flex items-center justify-center space-x-4 space-x-reverse mb-4">
            <div className="h-px bg-gray-300 dark:bg-gray-600 flex-1"></div>
            <span className="text-xs text-gray-500 dark:text-gray-400 px-3 font-medium">نظام آمن ومحمي</span>
            <div className="h-px bg-gray-300 dark:bg-gray-600 flex-1"></div>
          </div>
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            Smart POS - نظام نقاط البيع الذكي
          </p>
          <p className="text-gray-500 dark:text-gray-500 text-xs mt-2 leading-relaxed">
            الإصدار 1.0.0 | جميع الحقوق محفوظة © 2025
          </p>
        </div>
      </div>
    </div>
  );
};

export default DevicePendingApproval;
