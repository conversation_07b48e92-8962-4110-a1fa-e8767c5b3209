import React, { useState } from 'react';
import RichTextEditor from '../components/inputs/RichTextEditor';

/**
 * صفحة اختبار بسيطة لمحرر النصوص المحسن
 * تركز على اختبار المشاكل المحددة فقط
 */
const TestSimpleRichTextEditor: React.FC = () => {
  const [content, setContent] = useState<string>('');

  const handleContentChange = (value: string) => {
    setContent(value);
  };

  const loadTestContent = () => {
    setContent(`
      <h1>اختبار العناوين</h1>
      <h2>عنوان متوسط</h2>
      <h3>عنوان صغير</h3>
      <p>هذا نص عادي لاختبار المحرر.</p>
      <ul>
        <li>قائمة نقطية - العنصر الأول</li>
        <li>قائمة نقطية - العنصر الثاني</li>
        <li>قائمة نقطية - العنصر الثالث</li>
      </ul>
      <ol>
        <li>قائمة رقمية - العنصر الأول</li>
        <li>قائمة رقمية - العنصر الثاني</li>
        <li>قائمة رقمية - العنصر الثالث</li>
      </ol>
    `);
  };

  const clearContent = () => {
    setContent('');
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100 mb-4">
            اختبار محرر النصوص المحسن
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400">
            اختبار بسيط للتحسينات: حجم الخط بالعربية + إصلاح القوائم
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
            المشاكل التي تم حلها:
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
              <h3 className="font-medium text-green-800 dark:text-green-200 mb-2">
                ✅ حجم الخط بالعربية
              </h3>
              <p className="text-sm text-green-700 dark:text-green-300">
                عنصر اختيار حجم الخط يظهر بالعربية ومتوافق مع الاتجاه العربي
              </p>
            </div>
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
              <h3 className="font-medium text-green-800 dark:text-green-200 mb-2">
                ✅ إصلاح القوائم
              </h3>
              <p className="text-sm text-green-700 dark:text-green-300">
                القوائم النقطية والرقمية تظهر بشكل صحيح ولا تختفي
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100">
              المحرر:
            </h2>
            <div className="flex gap-3">
              <button
                onClick={loadTestContent}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
              >
                تحميل نص تجريبي
              </button>
              <button
                onClick={clearContent}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm"
              >
                مسح المحتوى
              </button>
            </div>
          </div>
          
          <RichTextEditor
            value={content}
            onChange={handleContentChange}
            placeholder="اكتب النص هنا... جرب استخدام قائمة حجم الخط والقوائم النقطية والرقمية"
          />
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
            تعليمات الاختبار:
          </h2>
          <div className="space-y-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
              <h3 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
                1. اختبار حجم الخط:
              </h3>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                انقر على قائمة حجم الخط في شريط الأدوات - يجب أن تظهر الخيارات بالعربية (عنوان كبير، عنوان متوسط، إلخ)
              </p>
            </div>
            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
              <h3 className="font-medium text-purple-800 dark:text-purple-200 mb-2">
                2. اختبار القوائم:
              </h3>
              <p className="text-sm text-purple-700 dark:text-purple-300">
                انقر على أزرار القائمة النقطية أو الرقمية - يجب أن تظهر القوائم فوراً ولا تختفي
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
            النتيجة (HTML):
          </h2>
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <pre className="text-sm text-gray-600 dark:text-gray-300 whitespace-pre-wrap break-all max-h-64 overflow-y-auto">
              {content || 'لا يوجد محتوى بعد...'}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestSimpleRichTextEditor;
