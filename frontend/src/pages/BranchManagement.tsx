/**
 * صفحة إدارة فروع البيع
 * صفحة منفصلة لإدارة فروع البيع والعلاقات مع المستودعات
 */

import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  FaArrowLeft,
  FaSync
} from 'react-icons/fa';
import {
  FiGitBranch,
  FiLink
} from 'react-icons/fi';

// Import stores
import { useBranchStore } from '../stores/branchStore';
import { useBranchWarehouseStore } from '../stores/branchWarehouseStore';
import { useWarehouseStore } from '../stores/warehouseStore';

// Import tab components
import BranchesTab from '../components/branch/BranchesTab';
import BranchWarehousesTab from '../components/branch/BranchWarehousesTab';

const BranchManagement: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Stores
  const branchStore = useBranchStore();
  const branchWarehouseStore = useBranchWarehouseStore();
  const warehouseStore = useWarehouseStore();

  // State
  const [activeTab, setActiveTab] = useState<'branches' | 'branch-warehouses'>('branches');
  
  // Refs for tracking loaded tabs
  const loadedTabs = useRef(new Set<string>());

  // Set active tab based on current route
  useEffect(() => {
    const path = location.pathname;
    if (path.includes('/branch-management/branch-warehouses')) {
      setActiveTab('branch-warehouses');
    } else {
      setActiveTab('branches');
    }
  }, [location.pathname]);

  // Load data for active tab
  useEffect(() => {
    const loadTabData = async () => {
      if (loadedTabs.current.has(activeTab)) return;

      try {
        console.log(`🔄 [BranchManagement] تحميل بيانات التبويب: ${activeTab}`);

        switch (activeTab) {
          case 'branches':
            await branchStore.fetchBranches();
            break;
          case 'branch-warehouses':
            await Promise.all([
              branchStore.fetchBranches(),
              warehouseStore.fetchWarehouses()
            ]);
            break;
        }

        loadedTabs.current.add(activeTab);
        console.log(`✅ [BranchManagement] تم تحميل بيانات التبويب: ${activeTab}`);
      } catch (error) {
        console.error(`❌ [BranchManagement] خطأ في تحميل بيانات التبويب ${activeTab}:`, error);
      }
    };

    loadTabData();
  }, [activeTab]);

  // Handle refresh
  const handleRefresh = async () => {
    try {
      console.log('🔄 [BranchManagement] تحديث البيانات...');

      // إعادة تعيين التبويب المحمل لإجبار إعادة التحميل
      loadedTabs.current.delete(activeTab);

      switch (activeTab) {
        case 'branches':
          await branchStore.fetchBranches();
          break;
        case 'branch-warehouses':
          await Promise.all([
            branchStore.fetchBranches(),
            warehouseStore.fetchWarehouses()
          ]);
          branchWarehouseStore.reset();
          break;
      }

      // إعادة إضافة التبويب كمحمل
      loadedTabs.current.add(activeTab);
      console.log('✅ [BranchManagement] تم تحديث البيانات');
    } catch (error) {
      console.error('❌ [BranchManagement] خطأ في تحديث البيانات:', error);
    }
  };

  // Get current error from active store
  const getCurrentError = () => {
    switch (activeTab) {
      case 'branches':
        return branchStore.error;
      case 'branch-warehouses':
        return branchWarehouseStore.error;
      default:
        return null;
    }
  };

  const currentError = getCurrentError();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8">
      {/* Header */}
      <div className="mb-6">
        <div className="relative rounded-xl p-4 sm:p-6 bg-gradient-to-l from-primary-50/40 via-primary-50/20 to-white dark:from-primary-900/20 dark:via-primary-900/10 dark:to-gray-800 border border-gray-200 dark:border-gray-700" style={{ boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)' }}>
          {/* خط نقش علوي للتأثير المميز */}
          <div className="absolute -top-0.5 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-primary-300/30 to-transparent dark:from-transparent dark:via-primary-600/20 dark:to-transparent"></div>

          {/* خط نقش سفلي للتأثير المميز */}
          <div className="absolute -bottom-0.5 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-primary-300/30 to-transparent dark:from-transparent dark:via-primary-600/20 dark:to-transparent"></div>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <button
                onClick={() => navigate('/')}
                className="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-xl p-3 hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-200 ease-in-out shadow-lg hover:shadow-xl flex-shrink-0 border-2 border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                title="العودة للرئيسية"
              >
                <FaArrowLeft className="text-sm" />
              </button>
              <div className="mr-3 sm:mr-4 min-w-0 flex-1">
                <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FiGitBranch className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                  <span className="truncate">إدارة فروع البيع</span>
                </h1>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 hidden sm:block">
                  إدارة شاملة لجميع فروع البيع والعلاقات مع المستودعات
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 flex-shrink-0">
              <button
                onClick={handleRefresh}
                className="bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium min-w-[100px]"
                title="تحديث البيانات"
              >
                <FaSync className="ml-2" />
                تحديث
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs - Separate Section */}
      <div className="mb-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 overflow-hidden">
          <nav className="flex overflow-x-auto">
            <button
              onClick={() => {
                setActiveTab('branches');
                navigate('/branch-management');
              }}
              className={`py-4 px-6 border-b-2 font-medium text-sm flex items-center transition-all duration-200 ease-in-out whitespace-nowrap ${
                activeTab === 'branches'
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
              }`}
            >
              <FiGitBranch className="ml-2" />
              فروع البيع
            </button>

            <button
              onClick={() => {
                setActiveTab('branch-warehouses');
                navigate('/branch-management/branch-warehouses');
              }}
              className={`py-4 px-6 border-b-2 font-medium text-sm flex items-center transition-all duration-200 ease-in-out whitespace-nowrap ${
                activeTab === 'branch-warehouses'
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
              }`}
            >
              <FiLink className="ml-2" />
              فروع البيع والمستودعات
            </button>
          </nav>
        </div>
      </div>

      {/* Error Display */}
      {currentError && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4 mb-6">
          <div className="flex items-center">
            <div className="text-red-600 dark:text-red-400 text-sm">
              {currentError}
            </div>
          </div>
        </div>
      )}



      {/* Content based on active tab */}
      {activeTab === 'branches' && <BranchesTab />}
      {activeTab === 'branch-warehouses' && <BranchWarehousesTab />}
    </div>
  );
};

export default BranchManagement;
