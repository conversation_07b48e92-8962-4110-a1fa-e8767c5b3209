import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  FaArrowLeft
} from 'react-icons/fa';
import {
  FiShield,
  FiSettings,
  FiPackage,
  FiFileText,
  FiBarChart,
  FiRefreshCw
} from 'react-icons/fi';
// import { useTheme } from '../contexts/ThemeContext'; // Not used
import {
  useWarrantyTypesStore,
  useProductWarrantiesStore,
  useWarrantyClaimsStore,
  useWarrantyReportsStore
} from '../stores/warranty';
import WarrantyTypesTab from '../components/warranty/WarrantyTypesTab';
import ProductWarrantiesTab from '../components/warranty/ProductWarrantiesTab';
import WarrantyClaimsTab from '../components/warranty/WarrantyClaimsTab';
import WarrantyReportsTab from '../components/warranty/WarrantyReportsTab';

const WarrantyManagement: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  // const { isDarkMode } = useTheme(); // Not used in this component

  // Stores
  const warrantyTypesStore = useWarrantyTypesStore();
  const productWarrantiesStore = useProductWarrantiesStore();
  const warrantyClaimsStore = useWarrantyClaimsStore();
  const warrantyReportsStore = useWarrantyReportsStore();

  // State
  const [activeTab, setActiveTab] = useState<'warranty-types' | 'product-warranties' | 'warranty-claims' | 'warranty-reports'>('warranty-types');
  
  // Refs for tracking loaded tabs
  const loadedTabs = useRef(new Set<string>());

  // Set active tab based on current route
  useEffect(() => {
    const path = location.pathname;
    if (path === '/warranty-types') {
      setActiveTab('warranty-types');
    } else if (path === '/product-warranties') {
      setActiveTab('product-warranties');
    } else if (path === '/warranty-claims') {
      setActiveTab('warranty-claims');
    } else if (path === '/warranty-reports') {
      setActiveTab('warranty-reports');
    } else if (path === '/warranties') {
      setActiveTab('warranty-types'); // Default tab
    }
  }, [location.pathname]);

  // Load data when tab changes
  useEffect(() => {
    // Skip if this tab has already been loaded
    if (loadedTabs.current.has(activeTab)) return;

    const loadTabData = async () => {
      try {
        console.log(`🔄 [WarrantyManagement] تحميل بيانات التبويب: ${activeTab}`);

        // Load data based on active tab
        switch (activeTab) {
          case 'warranty-types':
            await warrantyTypesStore.fetchWarrantyTypes();
            break;
          case 'product-warranties':
            await productWarrantiesStore.fetchWarranties();
            break;
          case 'warranty-claims':
            await warrantyClaimsStore.fetchClaims();
            break;
          case 'warranty-reports':
            await warrantyReportsStore.fetchWarrantyStats();
            await warrantyReportsStore.fetchExpiringWarranties(30);
            await warrantyReportsStore.fetchClaimStatistics();
            break;
        }

        // Mark this tab as loaded
        loadedTabs.current.add(activeTab);
        console.log(`✅ [WarrantyManagement] تم تحميل بيانات التبويب: ${activeTab}`);
      } catch (error) {
        console.error(`❌ [WarrantyManagement] خطأ في تحميل بيانات التبويب ${activeTab}:`, error);
      }
    };

    loadTabData();
  }, [activeTab]); // إزالة stores من dependency array لمنع الحلقة اللا نهائية

  // Check if any store is loading
  const isLoading = warrantyTypesStore.loading || 
                   productWarrantiesStore.loading || 
                   warrantyClaimsStore.loading || 
                   warrantyReportsStore.loading;

  // Get current error
  const currentError = warrantyTypesStore.error || 
                      productWarrantiesStore.error || 
                      warrantyClaimsStore.error || 
                      warrantyReportsStore.error;

  // Handle refresh
  const handleRefresh = async () => {
    try {
      console.log('🔄 [WarrantyManagement] تحديث البيانات...');
      
      switch (activeTab) {
        case 'warranty-types':
          await warrantyTypesStore.fetchWarrantyTypes();
          break;
        case 'product-warranties':
          await productWarrantiesStore.fetchWarranties();
          break;
        case 'warranty-claims':
          await warrantyClaimsStore.fetchClaims();
          break;
        case 'warranty-reports':
          await warrantyReportsStore.fetchWarrantyStats();
          await warrantyReportsStore.fetchExpiringWarranties(30);
          await warrantyReportsStore.fetchClaimStatistics();
          break;
      }
      
      console.log('✅ [WarrantyManagement] تم تحديث البيانات');
    } catch (error) {
      console.error('❌ [WarrantyManagement] خطأ في تحديث البيانات:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8">
      {/* Header */}
      <div className="mb-6">
        <div className="relative rounded-xl bg-gradient-to-l from-primary-50/40 via-primary-50/20 to-white dark:from-primary-900/20 dark:via-primary-900/10 dark:to-gray-800 border border-gray-200 dark:border-gray-700" style={{ boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)' }}>
          {/* خط نقش علوي للتأثير المميز */}
          <div className="absolute -top-0.5 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-primary-300/30 to-transparent dark:from-transparent dark:via-primary-600/20 dark:to-transparent"></div>

          {/* خط نقش سفلي للتأثير المميز */}
          <div className="absolute -bottom-0.5 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-primary-300/30 to-transparent dark:from-transparent dark:via-primary-600/20 dark:to-transparent"></div>
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center p-4 sm:p-6 gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <button
                onClick={() => navigate('/')}
                className="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-xl p-3 hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-200 ease-in-out shadow-lg hover:shadow-xl flex-shrink-0 border-2 border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                title="العودة للرئيسية"
              >
                <FaArrowLeft className="text-sm" />
              </button>
              <div className="mr-3 sm:mr-4 min-w-0 flex-1">
                <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FiShield className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                  <span className="truncate">إدارة الضمانات</span>
                </h1>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 hidden sm:block">
                  إدارة أنواع الضمانات وضمانات المنتجات والمطالبات والتقارير
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3 sm:gap-4 flex-wrap lg:flex-nowrap">
              <button
                onClick={handleRefresh}
                disabled={isLoading}
                className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-3 rounded-xl hover:bg-white/50 dark:hover:bg-gray-700/50 transition-all duration-200 ease-in-out backdrop-blur-sm border-2 border-gray-300 dark:border-gray-600 hover:border-primary-400 dark:hover:border-primary-500 hover:shadow-md disabled:opacity-50"
                title="تحديث"
              >
                <FiRefreshCw className={`text-sm ${isLoading ? 'animate-spin' : ''}`} />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {currentError && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4 mb-6">
          <div className="flex items-center">
            <div className="text-red-600 dark:text-red-400 text-sm">
              {currentError}
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft mb-6 border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="-mb-px flex">
              <button
                onClick={() => {
                  setActiveTab('warranty-types');
                  navigate('/warranty-types');
                }}
                className={`py-4 px-6 border-b-2 font-medium text-sm flex items-center transition-all duration-200 ease-in-out ${
                  activeTab === 'warranty-types'
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
                }`}
              >
                <FiSettings className="ml-2" />
                أنواع الضمانات
              </button>

              <button
                onClick={() => {
                  setActiveTab('product-warranties');
                  navigate('/product-warranties');
                }}
                className={`py-4 px-6 border-b-2 font-medium text-sm flex items-center transition-all duration-200 ease-in-out ${
                  activeTab === 'product-warranties'
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
                }`}
              >
                <FiPackage className="ml-2" />
                ضمانات المنتجات
              </button>

              <button
                onClick={() => {
                  setActiveTab('warranty-claims');
                  navigate('/warranty-claims');
                }}
                className={`py-4 px-6 border-b-2 font-medium text-sm flex items-center transition-all duration-200 ease-in-out ${
                  activeTab === 'warranty-claims'
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
                }`}
              >
                <FiFileText className="ml-2" />
                مطالبات الضمان
              </button>

              <button
                onClick={() => {
                  setActiveTab('warranty-reports');
                  navigate('/warranty-reports');
                }}
                className={`py-4 px-6 border-b-2 font-medium text-sm flex items-center transition-all duration-200 ease-in-out ${
                  activeTab === 'warranty-reports'
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
                }`}
              >
                <FiBarChart className="ml-2" />
                تقارير الضمانات
              </button>
            </nav>
          </div>
        </div>

      {/* Content based on active tab */}
      {activeTab === 'warranty-types' && <WarrantyTypesTab />}
      {activeTab === 'product-warranties' && <ProductWarrantiesTab />}
      {activeTab === 'warranty-claims' && <WarrantyClaimsTab />}
      {activeTab === 'warranty-reports' && <WarrantyReportsTab />}
    </div>
  );
};

export default WarrantyManagement;
