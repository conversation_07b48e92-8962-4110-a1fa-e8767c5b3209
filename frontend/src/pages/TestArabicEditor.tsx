import React from 'react';
import ArabicRichTextEditorWithButtons from '../components/inputs/ArabicRichTextEditorWithButtons';
import RichTextEditor from '../components/inputs/RichTextEditor';
import RichTextEditorTest from '../components/inputs/RichTextEditorTest';

/**
 * صفحة اختبار محررات النصوص العربية
 * لمقارنة الأداء والتوافق مع اللغة العربية
 */
const TestArabicEditor: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* العنوان الرئيسي */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            اختبار محررات النصوص العربية
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400">
            مقارنة بين المحرر الأصلي والمحرر المحسن للغة العربية
          </p>
        </div>

        {/* مقارنة المحررات */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          
          {/* المحرر الأصلي */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4 flex items-center">
              <span className="w-3 h-3 bg-red-500 rounded-full ml-2"></span>
              المحرر الأصلي (ReactQuill)
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              المحرر الأساسي مع تحسينات محدودة للعربية
            </p>
            <RichTextEditor
              value=""
              onChange={() => {}}
              placeholder="اختبر الكتابة هنا... جرب إضافة قوائم نقطية ورقمية"
            />
            
            <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
              <h4 className="font-medium text-red-800 dark:text-red-200 mb-2">المشاكل المعروفة:</h4>
              <ul className="text-sm text-red-700 dark:text-red-300 space-y-1">
                <li>• شريط الأدوات ليس RTL</li>
                <li>• مشاكل في القوائم النقطية والرقمية</li>
                <li>• تداخل الأرقام والنقاط مع النص</li>
                <li>• مشاكل في المحاذاة</li>
              </ul>
            </div>
          </div>

          {/* المحرر المحسن */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4 flex items-center">
              <span className="w-3 h-3 bg-green-500 rounded-full ml-2"></span>
              المحرر المحسن للعربية
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              محرر محسن خصيصاً للغة العربية مع إصلاح جميع المشاكل
            </p>
            <ArabicRichTextEditorWithButtons
              value=""
              onChange={() => {}}
              placeholder="اختبر الكتابة هنا... جرب إضافة قوائم نقطية ورقمية"
              minHeight="180px"
            />
            
            <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">التحسينات:</h4>
              <ul className="text-sm text-green-700 dark:text-green-300 space-y-1">
                <li>✅ شريط أدوات RTL كامل</li>
                <li>✅ قوائم نقطية ورقمية صحيحة</li>
                <li>✅ فصل الأرقام والنقاط عن النص</li>
                <li>✅ محاذاة مثالية للعربية</li>
                <li>✅ تلميحات عربية للأزرار</li>
                <li>✅ دعم كامل للوضع المظلم</li>
              </ul>
            </div>
          </div>
        </div>

        {/* اختبار شامل */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
            اختبار شامل للمحرر المحسن
          </h2>
          <RichTextEditorTest />
        </div>

        {/* تعليمات الاستخدام */}
        <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-4">
            كيفية استخدام المحرر المحسن:
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-blue-700 dark:text-blue-300 mb-2">في المكونات:</h4>
              <pre className="bg-blue-100 dark:bg-blue-800 p-3 rounded text-sm overflow-x-auto">
{`import ArabicRichTextEditorWithButtons from './ArabicRichTextEditorWithButtons';

<ArabicRichTextEditorWithButtons
  value={content}
  onChange={setContent}
  placeholder="اكتب النص هنا..."
  height={200}
/>`}
              </pre>
            </div>
            <div>
              <h4 className="font-medium text-blue-700 dark:text-blue-300 mb-2">الميزات المتاحة:</h4>
              <ul className="text-sm text-blue-600 dark:text-blue-400 space-y-1">
                <li>• تنسيق النص (عريض، مائل، تسطير)</li>
                <li>• العناوين بأحجام مختلفة</li>
                <li>• القوائم النقطية والرقمية</li>
                <li>• المحاذاة والمسافات البادئة</li>
                <li>• الروابط والصور</li>
                <li>• إزالة التنسيق</li>
                <li>• دعم كامل للـ RTL</li>
                <li>• تلميحات عربية</li>
              </ul>
            </div>
          </div>
        </div>

        {/* ملاحظات التطوير */}
        <div className="mt-8 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-4">
            ملاحظات للمطورين:
          </h3>
          <div className="text-sm text-yellow-700 dark:text-yellow-300 space-y-2">
            <p>
              <strong>1. استبدال المحرر الحالي:</strong> يمكن استبدال RichTextEditor بـ ArabicRichTextEditorWithButtons في جميع المكونات
            </p>
            <p>
              <strong>2. التخصيص:</strong> يمكن تخصيص الأنماط والألوان من خلال تعديل arabicEditorStyles
            </p>
            <p>
              <strong>3. الأداء:</strong> المحرر محسن للأداء مع معالجة ذكية للأحداث
            </p>
            <p>
              <strong>4. التوافق:</strong> متوافق مع جميع ميزات ReactQuill الأساسية
            </p>
          </div>
        </div>

      </div>
    </div>
  );
};

export default TestArabicEditor;
