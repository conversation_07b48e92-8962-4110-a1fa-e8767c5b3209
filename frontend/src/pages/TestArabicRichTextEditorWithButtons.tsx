import React, { useState } from 'react';
import ArabicRichTextEditorWithButtons from '../components/inputs/ArabicRichTextEditorWithButtons';

const TestArabicRichTextEditorWithButtons: React.FC = () => {
  const [content, setContent] = useState('<p>اكتب النص هنا وجرب أزرار زيادة وتقليل حجم العناوين...</p>');

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
          اختبار محرر النصوص العربي مع أزرار العناوين
        </h1>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            المحرر الجديد مع أزرار زيادة وتقليل حجم العناوين
          </h2>
          
          <ArabicRichTextEditorWithButtons
            value={content}
            onChange={setContent}
            placeholder="اكتب وصف المنتج هنا..."
            className="mb-4"
          />
          
          <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200 mb-2">
              المحتوى الحالي:
            </h3>
            <div className="text-sm text-gray-600 dark:text-gray-300 font-mono bg-white dark:bg-gray-800 p-3 rounded border max-h-40 overflow-y-auto">
              {content}
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            تعليمات الاستخدام
          </h2>
          
          <div className="space-y-4 text-gray-700 dark:text-gray-300">
            <div className="flex items-start space-x-3 space-x-reverse">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                <span className="text-blue-600 dark:text-blue-300 font-bold text-sm">1</span>
              </div>
              <div>
                <h3 className="font-medium mb-1">أزرار العناوين</h3>
                <p className="text-sm">استخدم زر "A+" لزيادة حجم العنوان وزر "A-" لتقليله. المستويات: نص عادي ← عنوان صغير ← عنوان متوسط ← عنوان كبير</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3 space-x-reverse">
              <div className="flex-shrink-0 w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                <span className="text-green-600 dark:text-green-300 font-bold text-sm">2</span>
              </div>
              <div>
                <h3 className="font-medium mb-1">تنسيق النص</h3>
                <p className="text-sm">استخدم أزرار B (عريض)، I (مائل)، U (تحته خط) لتنسيق النص</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3 space-x-reverse">
              <div className="flex-shrink-0 w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center">
                <span className="text-purple-600 dark:text-purple-300 font-bold text-sm">3</span>
              </div>
              <div>
                <h3 className="font-medium mb-1">القوائم</h3>
                <p className="text-sm">استخدم زر "1." للقوائم المرقمة وزر "•" للقوائم النقطية</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3 space-x-reverse">
              <div className="flex-shrink-0 w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center">
                <span className="text-orange-600 dark:text-orange-300 font-bold text-sm">4</span>
              </div>
              <div>
                <h3 className="font-medium mb-1">المؤشر الحالي</h3>
                <p className="text-sm">يظهر مستوى العنوان الحالي في المنطقة الوسطى بين أزرار زيادة وتقليل الحجم</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestArabicRichTextEditorWithButtons;