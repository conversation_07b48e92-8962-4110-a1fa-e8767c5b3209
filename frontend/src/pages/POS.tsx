import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import {
  FaSearch,
  FaBarcode,
  FaPlus,
  FaMinus,
  FaTrash,
  FaMoneyBill,
  FaCreditCard,
  FaPrint,
  FaTimes,
  FaArrowLeft,
  FaShoppingCart,
  FaCheck,
  FaBox,
  FaBoxOpen,
  FaCartPlus,
  FaReceipt,
  FaUserCircle,
  FaSun,
  FaMoon,
  FaCamera,
  FaThLarge,
  FaHistory,
  FaEdit,
  FaUser,
  FaMoneyBillWave,
  FaPhone,
  FaClock,
  FaTag
} from 'react-icons/fa';
import { useAuthStore } from '../stores/authStore';
import api from '../lib/axios';
import { useTheme } from '../contexts/ThemeContext';
import ToggleSwitch from '../components/ToggleSwitch';
// Removed unused imports
import Modal from '../components/Modal';
import { NumberInput, TextInput } from '../components/inputs';
import CustomerSelector, { CustomerSelectorRef } from '../components/CustomerSelector';
import PaymentOptions from '../components/PaymentOptions';

import FormattedCurrency from '../components/FormattedCurrency';
import { FormattedDate, FormattedTime } from '../components/FormattedDateTime';
import { numberFormattingService } from '../services/numberFormattingService';
import ScrollToTopButton from '../components/ScrollToTopButton';
import { ChatHeaderButton } from '../components/Chat';
import ChatNotificationManager from '../components/Chat/ChatNotificationManager';

interface Product {
  id: number;
  name: string;
  barcode: string | null;
  description: string | null;
  price: number;
  cost_price: number;
  quantity: number;
  min_quantity: number;
  category: string | null;
  unit: string;
  is_active: boolean;
  created_at: string;
  updated_at: string | null;
  created_by: number;
  updated_by: number | null;
  profit_margin: number;
  stock_value: number;
}

interface CartItem extends Product {
  cartQuantity: number;
  subtotal: number;
}

interface SaleItem {
  id: number;
  product_id: number;
  quantity: number;
  unit_price: number;
  subtotal: number;
  discount: number;
  product?: {
    id: number;
    name: string;
    barcode: string;
    price: number;
  };
}

interface Customer {
  id: number;
  name: string;
  phone?: string;
  email?: string;
  total_debt: number;
  is_active: boolean;
}

interface Sale {
  id: number;
  user_id: number;
  customer_id?: number;
  total_amount: number;
  payment_method: string;
  tax_amount: number;
  discount_amount: number;
  discount_type: string;
  customer_name: string | null;
  notes: string | null;
  amount_paid: number;
  payment_status: string;
  created_at: string;
  updated_at: string | null;
  items: SaleItem[];
  user?: {
    id: number;
    username: string;
    full_name: string;
  };
  customer?: Customer;
}



const POS: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [showPayment, setShowPayment] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'card' | string>('cash');
  const [amountPaid, setAmountPaid] = useState<string>('');
  const [discountAmount, setDiscountAmount] = useState<string>('0');
  const [discountType, setDiscountType] = useState<'fixed' | 'percentage'>('fixed');
  const [saleCompleted, setSaleCompleted] = useState(false);
  const [saleId, setSaleId] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [storeSettings, setStoreSettings] = useState<any>(null);
  const [categoryFilter, setCategoryFilter] = useState<string | null>(null);
  const [categories, setCategories] = useState<string[]>([]);
  const [hideZeroStock, setHideZeroStock] = useState<boolean>(false);
  const [showMoreCategories, setShowMoreCategories] = useState<boolean>(false);
  const [visibleCategoriesCount, setVisibleCategoriesCount] = useState<number>(categories.length);
  const [dropdownPosition, setDropdownPosition] = useState<{top: number, left: number}>({top: 0, left: 0});
  const [showScanner, setShowScanner] = useState<boolean>(false);
  const [showMobileProducts, setShowMobileProducts] = useState<boolean>(false);

  // Previous invoices state
  const [showPreviousInvoices, setShowPreviousInvoices] = useState<boolean>(false);
  const [editingInvoice, setEditingInvoice] = useState<Sale | null>(null);
  const [previousInvoices, setPreviousInvoices] = useState<Sale[]>([]);
  const [isLoadingInvoices, setIsLoadingInvoices] = useState<boolean>(false);
  const [isLoadingMoreInvoices, setIsLoadingMoreInvoices] = useState<boolean>(false);
  const [invoicesPage, setInvoicesPage] = useState<number>(1);
  const [hasMoreInvoices, setHasMoreInvoices] = useState<boolean>(true);
  const [invoiceSearchTerm, setInvoiceSearchTerm] = useState<string>('');

  // Pagination state
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);

  // Retry mechanism state
  const [retryCount, setRetryCount] = useState<number>(0);
  const [isRetrying, setIsRetrying] = useState<boolean>(false);
  const maxRetries = 3;

  // State for quantity modal
  const [showQuantityModal, setShowQuantityModal] = useState<boolean>(false);
  const [selectedCartItem, setSelectedCartItem] = useState<CartItem | null>(null);
  const [newQuantity, setNewQuantity] = useState<string>('');

  // Customer and payment states
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [paymentType, setPaymentType] = useState<'full' | 'partial' | 'credit'>('full');

  // Add customer modal states
  const [showAddCustomer, setShowAddCustomer] = useState(false);
  const [newCustomerName, setNewCustomerName] = useState('');
  const [newCustomerPhone, setNewCustomerPhone] = useState('');
  const [isAddingCustomer, setIsAddingCustomer] = useState(false);

  const { user } = useAuthStore();
  const { currentTheme, toggleTheme } = useTheme();
  const navigate = useNavigate();

  // استخدام خدمة تنسيق الأرقام الجديدة
  const [numberFormatSettings, setNumberFormatSettings] = useState<any>(null);

  // تحميل إعدادات تنسيق الأرقام
  useEffect(() => {
    const loadNumberFormatSettings = async () => {
      try {
        const settings = await numberFormattingService.getCurrentSettings();
        setNumberFormatSettings(settings);
      } catch (error) {
        console.error('Error loading number format settings:', error);
      }
    };

    loadNumberFormatSettings();
  }, []);

  // الحصول على الإعدادات مع القيم الافتراضية
  const currencySymbol = numberFormatSettings?.currencySymbol || 'د.ل';
  const decimalPlaces = numberFormatSettings?.decimalPlaces || 2;
  const searchInputRef = useRef<HTMLInputElement>(null);
  const barcodeInputRef = useRef<HTMLInputElement>(null);
  const desktopBarcodeInputRef = useRef<HTMLInputElement>(null);
  const customerSelectorRef = useRef<CustomerSelectorRef>(null);
  const categoriesContainerRef = useRef<HTMLDivElement>(null);
  const moreButtonRef = useRef<HTMLButtonElement>(null);
  const mobileProductsContainerRef = useRef<HTMLDivElement>(null);

  // For barcode scanner
  const [barcodeBuffer, setBarcodeBuffer] = useState<string>('');
  const [lastKeyTime, setLastKeyTime] = useState<number>(0);
  const BARCODE_DELAY = 50; // Typical barcode scanners send characters within 50ms
  const [showBarcodeIndicator, setShowBarcodeIndicator] = useState(false);

  // Calculate totals
  const cartSubtotal = cart.reduce((sum, item) => sum + item.subtotal, 0);
  const cartItemCount = cart.reduce((sum, item) => sum + item.cartQuantity, 0);

  // Calculate discount
  const discountValue = discountType === 'percentage'
    ? parseFloat(((cartSubtotal * parseFloat(discountAmount || '0')) / 100).toFixed(decimalPlaces))
    : parseFloat(parseFloat(discountAmount || '0').toFixed(decimalPlaces));

  // Calculate subtotal after discount
  const subtotalAfterDiscount = Math.max(0, parseFloat((cartSubtotal - discountValue).toFixed(decimalPlaces)));

  // Calculate tax using store settings
  const taxRate = storeSettings?.tax_rate || 0;
  const taxValue = parseFloat(((subtotalAfterDiscount * parseFloat(taxRate.toString())) / 100).toFixed(decimalPlaces));

  // Calculate final total after discount and tax
  const cartTotal = parseFloat((subtotalAfterDiscount + taxValue).toFixed(decimalPlaces));

  // Calculate change amount
  const changeAmount = paymentMethod === 'cash' && amountPaid
    ? parseFloat(amountPaid) - cartTotal
    : 0;

  // Add product to cart - defined before it's used in handleKeyDown
  const addToCart = useCallback((product: Product) => {
    // Check if product quantity is 0
    if (product.quantity === 0) {
      setError(`لا يمكن إضافة ${product.name}. المنتج غير متوفر في المخزون.`);
      setTimeout(() => setError(null), 3000);
      return;
    }

    // Check if product already in cart
    const existingItem = cart.find(item => item.id === product.id);

    // Check if adding one more would exceed available quantity
    if (existingItem && existingItem.cartQuantity >= product.quantity) {
      setError(`لا يمكن إضافة المزيد من ${product.name}. الكمية المتوفرة: ${product.quantity}`);
      setTimeout(() => setError(null), 3000);
      return;
    }

    setCart(prevCart => {
      // Check if product already in cart
      const existingItemInPrev = prevCart.find(item => item.id === product.id);

      if (existingItemInPrev) {
        // Update quantity
        return prevCart.map(item =>
          item.id === product.id
            ? {
                ...item,
                cartQuantity: item.cartQuantity + 1,
                subtotal: (item.cartQuantity + 1) * item.price
              }
            : item
        );
      } else {
        // Add new item
        return [...prevCart, {
          ...product,
          cartQuantity: 1,
          subtotal: product.price
        }];
      }
    });
  }, [cart, setError]);

  // Handle barcode scanner input
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    // Check if user is typing in an input field, textarea, or modal
    const activeElement = document.activeElement;
    const isInputField = activeElement && (
      activeElement.tagName === 'INPUT' ||
      activeElement.tagName === 'TEXTAREA' ||
      activeElement.getAttribute('contenteditable') === 'true'
    );

    // Check if any modal is open
    const isModalOpen = showPayment || showPreviousInvoices || showMobileProducts || showScanner;

    // Don't process barcode scanning if user is typing in input fields or modals are open
    if (isInputField || isModalOpen) {
      return;
    }

    const currentTime = new Date().getTime();
    const timeDiff = currentTime - lastKeyTime;

    // If it's a rapid succession of keys (typical of barcode scanners)
    if (timeDiff < BARCODE_DELAY) {
      setShowBarcodeIndicator(true); // Show indicator when barcode scanning is detected

      // Ignore certain keys that are not part of barcodes
      if (e.key !== 'Shift' && e.key !== 'Control' && e.key !== 'Alt' && e.key !== 'Tab') {
        if (e.key === 'Enter') {
          // Enter key signals the end of barcode input
          if (barcodeBuffer.length > 5) { // Most barcodes are longer than 5 characters
            // Search for product by barcode from backend
            searchProductByBarcode(barcodeBuffer)
              .then(product => {
                if (product) {
                  addToCart(product);

                  // Also update the barcode input fields
                  if (barcodeInputRef.current) {
                    barcodeInputRef.current.value = '';
                  }
                  if (desktopBarcodeInputRef.current) {
                    desktopBarcodeInputRef.current.value = '';
                  }

                  // Play success sound
                  const audio = new Audio('/beep-success.mp3');
                  audio.play().catch(err => console.log('Audio play failed:', err));
                } else {
                  setError(`لم يتم العثور على منتج بالباركود: ${barcodeBuffer}`);
                  setTimeout(() => setError(null), 3000);

                  // Play error sound
                  const audio = new Audio('/beep-error.mp3');
                  audio.play().catch(err => console.log('Audio play failed:', err));
                }
              })
              .catch(error => {
                setError(error.message);
                setTimeout(() => setError(null), 3000);

                // Play error sound
                const audio = new Audio('/beep-error.mp3');
                audio.play().catch(err => console.log('Audio play failed:', err));
              });
          }
          // Reset buffer after processing
          setBarcodeBuffer('');
          setTimeout(() => setShowBarcodeIndicator(false), 500); // Hide indicator after processing
        } else if (e.key.length === 1 || e.key === 'Backspace') {
          // For regular characters or backspace
          if (e.key === 'Backspace') {
            setBarcodeBuffer(prev => prev.slice(0, -1));
          } else {
            setBarcodeBuffer(prev => prev + e.key);
          }
        }
      }
    } else if (e.key.length === 1) {
      // Start a new barcode buffer
      setBarcodeBuffer(e.key);
      setShowBarcodeIndicator(true);
    }

    setLastKeyTime(currentTime);
  }, [barcodeBuffer, lastKeyTime, products, addToCart, setError, showPayment, showPreviousInvoices, showMobileProducts, showScanner]);

  // Set up event listener for barcode scanner
  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showMoreCategories) {
        const target = event.target as Element;
        // Check if click is outside both the button and the dropdown
        if (!moreButtonRef.current?.contains(target) &&
            !target.closest('[data-dropdown="categories"]')) {
          setShowMoreCategories(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showMoreCategories]);

  // Calculate visible categories based on available space
  const calculateVisibleCategories = useCallback(() => {
    if (!categoriesContainerRef.current || categories.length === 0) {
      setVisibleCategoriesCount(categories.length);
      return;
    }

    // Use setTimeout to ensure DOM is ready
    setTimeout(() => {
      if (!categoriesContainerRef.current) return;

      const container = categoriesContainerRef.current;
      const containerWidth = container.offsetWidth;

      // Reserve space for the separator (1px + 16px gap) and toggle switch
      const separatorWidth = 17; // 1px separator + 16px gap
      const availableWidth = containerWidth - separatorWidth;

      // Create temporary elements to measure width
      const tempContainer = document.createElement('div');
      tempContainer.style.position = 'absolute';
      tempContainer.style.visibility = 'hidden';
      tempContainer.style.whiteSpace = 'nowrap';
      tempContainer.style.display = 'flex';
      tempContainer.style.gap = '8px'; // gap-2
      tempContainer.style.fontSize = '14px'; // text-sm
      tempContainer.style.fontFamily = getComputedStyle(container).fontFamily;
      document.body.appendChild(tempContainer);

      // Measure "الكل" button
      const allButton = document.createElement('button');
      allButton.className = 'px-3 py-1.5 rounded-lg text-sm font-medium flex-shrink-0';
      allButton.style.padding = '6px 12px';
      allButton.innerHTML = '<span style="display: flex; align-items: center; gap: 6px;"><svg style="width: 12px; height: 12px;"></svg>الكل</span>';
      tempContainer.appendChild(allButton);

      let totalWidth = allButton.offsetWidth + 8; // 8px gap
      let visibleCount = 0;

      // Measure each category button
      for (let i = 0; i < categories.length; i++) {
        const categoryButton = document.createElement('button');
        categoryButton.className = 'px-3 py-1.5 rounded-lg text-sm font-medium whitespace-nowrap';
        categoryButton.style.padding = '6px 12px';
        categoryButton.innerHTML = `<span style="display: flex; align-items: center; gap: 6px;"><svg style="width: 12px; height: 12px;"></svg>${categories[i]}</span>`;
        tempContainer.appendChild(categoryButton);

        const buttonWidth = categoryButton.offsetWidth;

        // Check if we need space for "more" button
        const remainingCategories = categories.length - i - 1;
        const moreButtonWidth = remainingCategories > 0 ? 70 : 0; // Approximate width for "+X" button

        if (totalWidth + buttonWidth + moreButtonWidth <= availableWidth) {
          totalWidth += buttonWidth + 8; // 8px gap
          visibleCount++;
        } else {
          break;
        }
      }

      document.body.removeChild(tempContainer);
      setVisibleCategoriesCount(Math.max(0, visibleCount));
    }, 100);
  }, [categories]);

  // Recalculate when categories change or window resizes
  useEffect(() => {
    calculateVisibleCategories();
  }, [categories, calculateVisibleCategories]);

  useEffect(() => {
    const handleResize = () => {
      calculateVisibleCategories();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [calculateVisibleCategories]);

  // Reference for the products container to detect scroll
  const productsContainerRef = useRef<HTMLDivElement>(null);

  // Function to fetch products with pagination
  const fetchProducts = useCallback(async (page: number = 1, limit: number = 15, isLoadMore: boolean = false) => {
    try {
      if (isLoadMore) {
        setIsLoadingMore(true);
      } else {
        setIsLoading(true);
      }
      setError(null);

      // Build query parameters
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('limit', limit.toString());

      // Add search term if present
      if (searchTerm) {
        params.append('search', searchTerm);
      }

      // Add category filter if present
      if (categoryFilter) {
        params.append('category', categoryFilter);
      }

      // Try to fetch from API
      const response = await api.get(`/api/products/?${params.toString()}`);
      console.log('📦 Products fetched from API:', {
        page,
        count: response.data?.length || 0,
        isLoadMore,
        expectedBatchSize: isLoadMore ? LOAD_MORE_BATCH_SIZE : INITIAL_BATCH_SIZE
      });

      // Get pagination metadata from headers
      const totalPages = parseInt(response.headers["x-pages"] || "1");
      const currentPageFromHeader = parseInt(response.headers["x-page"] || "1");
      const totalCount = parseInt(response.headers["x-total-count"] || "0");

      console.log('📊 Pagination info:', {
        currentPage: currentPageFromHeader,
        totalPages,
        totalCount,
        hasMore: currentPageFromHeader < totalPages,
        isLoadMore
      });

      setCurrentPage(currentPageFromHeader);
      setHasMore(currentPageFromHeader < totalPages);

      if (response.data && Array.isArray(response.data)) {
        const productsData = response.data;

        // Apply filters to new products
        const applyFilters = (products: any[]) => {
          let filtered = products;

          // Always filter out inactive products
          filtered = filtered.filter(product => product.is_active === true);

          // Apply zero stock filter if enabled
          if (hideZeroStock) {
            filtered = filtered.filter(product => product.quantity > 0);
          }

          return filtered;
        };

        if (isLoadMore) {
          // For load more, append to existing products
          setProducts(prevProducts => {
            // Remove duplicates before adding
            const existingIds = new Set(prevProducts.map(p => p.id));
            const uniqueNewProducts = productsData.filter(product => !existingIds.has(product.id));

            console.log('🔄 Appending products:', {
              existing: prevProducts.length,
              new: productsData.length,
              unique: uniqueNewProducts.length,
              duplicatesFiltered: productsData.length - uniqueNewProducts.length,
              totalAfterAppend: prevProducts.length + uniqueNewProducts.length
            });

            return [...prevProducts, ...uniqueNewProducts];
          });

          // Update filtered products separately to avoid conflicts
          setFilteredProducts(prevFiltered => {
            const filteredNewProducts = applyFilters(productsData);
            const existingIds = new Set(prevFiltered.map(p => p.id));
            const uniqueFilteredProducts = filteredNewProducts.filter(product => !existingIds.has(product.id));

            console.log('🎯 Appending filtered products:', {
              existingFiltered: prevFiltered.length,
              newFiltered: filteredNewProducts.length,
              uniqueFiltered: uniqueFilteredProducts.length
            });

            return [...prevFiltered, ...uniqueFilteredProducts];
          });
        } else {
          // For initial load or filter change, replace all products
          setProducts(productsData);
          const filteredProducts = applyFilters(productsData);
          setFilteredProducts(filteredProducts);

          console.log('🔄 Replacing products:', {
            total: productsData.length,
            filtered: filteredProducts.length
          });
        }
      } else {
        console.error('❌ API returned empty or invalid data');
        if (!isLoadMore) {
          setError('لم يتم العثور على منتجات. يرجى التحقق من قاعدة البيانات.');
          setProducts([]);
          setFilteredProducts([]);
        }
      }
    } catch (err) {
      console.error('❌ Error fetching products from API:', err);

      // Implement retry mechanism for failed requests
      if (retryCount < maxRetries && !isLoadMore) {
        console.log(`🔄 Retrying fetch products (attempt ${retryCount + 1}/${maxRetries})`);
        setIsRetrying(true);
        setRetryCount(prev => prev + 1);

        // Exponential backoff: wait longer between retries
        const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 5000);

        setTimeout(() => {
          setIsRetrying(false);
          fetchProducts(page, limit, isLoadMore);
        }, retryDelay);

        setError(`فشل في التحميل، جاري المحاولة مرة أخرى... (${retryCount + 1}/${maxRetries})`);
        return; // Don't execute finally block yet
      }

      // Reset retry count after max retries reached or for load more errors
      setRetryCount(0);
      setIsRetrying(false);

      // More specific error handling
      if (isLoadMore) {
        // For load more errors, just stop loading but don't clear existing data
        setHasMore(false);
        setError('فشل في تحميل الـ 50 منتج التالية. يرجى التمرير للأعلى ثم للأسفل للمحاولة مرة أخرى.');
      } else {
        // For initial load errors, show full error
        setError('فشل في تحميل المنتجات الأولى (50 منتج). يرجى التحقق من الاتصال والمحاولة مرة أخرى.');
      }

      // Clear error after 8 seconds
      setTimeout(() => setError(null), 8000);
    } finally {
      // Only reset loading states if not retrying
      if (!isRetrying) {
        setIsLoading(false);
        setIsLoadingMore(false);
        // Reset retry count on successful completion
        if (retryCount > 0) {
          setRetryCount(0);
        }
      }
    }
  }, [searchTerm, categoryFilter, hideZeroStock]);

  // Fixed batch sizes for consistent loading
  const INITIAL_BATCH_SIZE = 50; // First load: 50 products
  const LOAD_MORE_BATCH_SIZE = 50; // Each subsequent load: 50 products

  // Function to search for product by barcode from backend
  const searchProductByBarcode = useCallback(async (barcode: string): Promise<Product | null> => {
    try {
      console.log('🔍 Searching for product by barcode:', barcode);

      // First check in currently loaded products for quick response
      const localProduct = products.find(p => p.barcode === barcode && p.is_active === true);
      if (localProduct) {
        console.log('✅ Found product in local cache:', localProduct.name);
        return localProduct;
      }

      // If not found locally, search in backend
      const response = await api.get(`/api/products/search-by-barcode/${encodeURIComponent(barcode)}`);

      if (response.data && response.data.is_active) {
        console.log('✅ Found product in backend:', response.data.name);
        return response.data;
      } else if (response.data && !response.data.is_active) {
        console.log('❌ Product found but inactive:', response.data.name);
        throw new Error(`المنتج ${response.data.name} غير نشط ولا يمكن بيعه`);
      } else {
        console.log('❌ Product not found in backend');
        return null;
      }
    } catch (error: any) {
      console.error('❌ Error searching for product by barcode:', error);

      // If it's a specific error message, throw it
      if (error.message && error.message.includes('غير نشط')) {
        throw error;
      }

      // For other errors (like 404), return null
      if (error.response?.status === 404) {
        return null;
      }

      // For network errors, throw them
      throw new Error('فشل في البحث عن المنتج. يرجى التحقق من الاتصال.');
    }
  }, [products]);

  // Function to reset all loading states and start fresh
  const resetAndReloadProducts = useCallback((clearFilters: boolean = false) => {
    console.log('🔄 Resetting all states and reloading products from scratch', { clearFilters });

    // إعادة تعيين جميع الحالات
    setCurrentPage(1);
    setHasMore(true);
    setRetryCount(0);
    setIsRetrying(false);
    setIsLoading(false);
    setIsLoadingMore(false);
    setError(null);

    // مسح الفلاتر إذا طُلب ذلك
    if (clearFilters) {
      setSearchTerm('');
      setCategoryFilter(null);
      setHideZeroStock(false);
    }

    // مسح المنتجات الحالية
    setProducts([]);
    setFilteredProducts([]);

    // بدء التحميل من جديد
    fetchProducts(1, INITIAL_BATCH_SIZE, false);
  }, [fetchProducts]);

  // Function to load more products - improved with better error handling
  const loadMoreProducts = useCallback(() => {
    console.log('🔄 loadMoreProducts called:', {
      hasMore,
      isLoadingMore,
      isLoading,
      currentPage,
      filteredCount: filteredProducts.length
    });

    if (hasMore && !isLoadingMore && !isLoading) {
      console.log('✅ Loading more products, page:', currentPage + 1);

      // Use fixed batch size for consistent loading
      const batchSize = LOAD_MORE_BATCH_SIZE;
      console.log('📦 Using batch size:', batchSize);

      try {
        fetchProducts(currentPage + 1, batchSize, true);
      } catch (error) {
        console.error('❌ Error in loadMoreProducts:', error);
        // Don't set hasMore to false here, let fetchProducts handle it
      }
    } else {
      console.log('❌ loadMoreProducts blocked:', {
        hasMore,
        isLoadingMore,
        isLoading,
        reason: !hasMore ? 'no more pages' : isLoadingMore ? 'already loading more' : 'initial loading'
      });
    }
  }, [fetchProducts, currentPage, hasMore, isLoadingMore, isLoading, filteredProducts.length]);

  // Fetch categories separately
  const fetchCategories = useCallback(async () => {
    try {
      const response = await api.get('/api/products/categories/list');
      if (response.data && Array.isArray(response.data) && response.data.length > 0) {
        setCategories(response.data);
      }
    } catch (err) {
      console.error('Error fetching categories:', err);
      // Categories will be extracted from products if this fails
    }
  }, []);

  // Fetch store settings
  const fetchStoreSettings = useCallback(async () => {
    try {
      const response = await api.get('/api/settings/public');
      if (response.data) {
        setStoreSettings(response.data);
      }
    } catch (err) {
      console.error('Error fetching store settings:', err);
      // Set default settings if fetch fails
      setStoreSettings({ tax_rate: 0 });
    }
  }, []);

  // Initial data loading
  useEffect(() => {
    // Only run on initial mount
    const loadInitialData = async () => {
      await fetchStoreSettings();
      await fetchCategories();
      await fetchProducts(1, INITIAL_BATCH_SIZE, false);

      // Focus barcode input on load instead of search input
      if (barcodeInputRef.current) {
        barcodeInputRef.current.focus();
      }
    };

    loadInitialData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Create refs to hold current state values for scroll handlers
  const stateRef = useRef({
    hasMore,
    isLoadingMore,
    isLoading,
    currentPage,
    loadMoreProducts
  });

  // Update state ref whenever values change
  useEffect(() => {
    stateRef.current = {
      hasMore,
      isLoadingMore,
      isLoading,
      currentPage,
      loadMoreProducts
    };
  }, [hasMore, isLoadingMore, isLoading, currentPage, loadMoreProducts]);

  // Add scroll event listener to products container (Desktop) - Improved version
  useEffect(() => {
    const container = productsContainerRef.current;
    if (!container) return;

    console.log('🔧 Setting up desktop scroll listener');

    let lastScrollTime = 0;
    let lastLoadTime = 0;
    let isNearBottomTriggered = false;
    let lastScrollTop = 0;
    let scrollVelocity = 0;
    let retryCount = 0;
    const maxRetries = 3;

    // Create a stable scroll handler that doesn't change
    const scrollHandler = (event: Event) => {
      const now = Date.now();

      // Reduced throttling for more responsive loading
      if (now - lastScrollTime < 100) return;
      lastScrollTime = now;

      const target = event.target as HTMLElement;
      if (!target) return;

      const { scrollTop, scrollHeight, clientHeight } = target;
      const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

      // Calculate scroll velocity for adaptive threshold
      scrollVelocity = Math.abs(scrollTop - lastScrollTop);
      lastScrollTop = scrollTop;

      // Only log occasionally to avoid spam
      if (Math.random() < 0.005) {
        console.log('🖥️ Desktop scroll (sampled):', {
          scrollTop: Math.round(scrollTop),
          scrollHeight: Math.round(scrollHeight),
          clientHeight: Math.round(clientHeight),
          distanceFromBottom: Math.round(distanceFromBottom),
          velocity: Math.round(scrollVelocity)
        });
      }

      // Optimized threshold for 50-item batches
      const baseThreshold = 300; // Increased for larger batches
      const velocityMultiplier = Math.min(scrollVelocity / 12, 2); // Adjusted for smoother loading
      const threshold = baseThreshold + (velocityMultiplier * 200);
      const shouldLoadMore = distanceFromBottom <= threshold &&
                            scrollHeight > clientHeight;

      if (shouldLoadMore && !isNearBottomTriggered) {
        // Get current state values from ref
        const { hasMore: currentHasMore, isLoadingMore: currentIsLoadingMore,
                isLoading: currentIsLoading, loadMoreProducts: currentLoadMoreProducts } = stateRef.current;

        // Reduced minimum time between loads for smoother experience
        const timeSinceLastLoad = now - lastLoadTime;
        if (currentHasMore && !currentIsLoadingMore && !currentIsLoading && timeSinceLastLoad > 500) {
          console.log('🎯 Desktop: Triggering loadMoreProducts from scroll', {
            distanceFromBottom: Math.round(distanceFromBottom),
            threshold: Math.round(threshold),
            retryCount
          });

          isNearBottomTriggered = true;
          lastLoadTime = now;

          // Load more products with simple retry logic
          try {
            currentLoadMoreProducts();
            retryCount = 0; // Reset retry count on successful trigger
          } catch (error) {
            console.error('❌ Load more trigger failed:', error);
            if (retryCount < maxRetries) {
              retryCount++;
              console.log(`🔄 Retrying load more trigger (attempt ${retryCount}/${maxRetries})`);
              setTimeout(() => {
                try {
                  currentLoadMoreProducts();
                } catch (retryError) {
                  console.error('❌ Retry failed:', retryError);
                }
              }, 500 * retryCount); // Progressive delay
            }
          }

          // Reset trigger after a shorter delay for better responsiveness
          setTimeout(() => {
            isNearBottomTriggered = false;
          }, 1000); // Reduced from 2000
        }
      }

      // Reset trigger if user scrolls back up significantly
      if (distanceFromBottom > threshold + 50) { // Reduced from 100
        isNearBottomTriggered = false;
        retryCount = 0; // Reset retry count when scrolling up
      }
    };

    container.addEventListener('scroll', scrollHandler, { passive: true });

    return () => {
      console.log('🔧 Removing desktop scroll listener');
      container.removeEventListener('scroll', scrollHandler);
    };
  }, []); // Empty dependency array for stability

  // Mobile products modal - using button-based loading instead of scroll detection
  // This is more reliable and user-friendly on mobile devices

  // Filter products when search term or category changes
  const prevSearchTermRef = useRef(searchTerm);
  const prevCategoryFilterRef = useRef(categoryFilter);

  // Apply local filtering for zero stock products and inactive products
  // Only run this when hideZeroStock changes, not when products change
  // to avoid conflicts with infinite scroll
  useEffect(() => {
    if (products.length > 0) {
      let filtered = products;

      // Always filter out inactive products
      filtered = filtered.filter(product => product.is_active === true);

      // Filter out zero stock products if option is enabled
      if (hideZeroStock) {
        filtered = filtered.filter(product => product.quantity > 0);
      }

      // Only update if this is not during a load more operation
      if (!isLoadingMore) {
        console.log('🔄 Applying local filters:', {
          total: products.length,
          filtered: filtered.length,
          hideZeroStock
        });
        setFilteredProducts(filtered);
      }
    }
  }, [hideZeroStock]); // Removed products dependency to avoid conflicts

  useEffect(() => {
    // Only fetch if the filters have actually changed
    if (prevSearchTermRef.current !== searchTerm || prevCategoryFilterRef.current !== categoryFilter) {
      // Reset pagination and fetch products with new filters
      setCurrentPage(1);
      setHasMore(true); // Reset hasMore flag
      setRetryCount(0); // Reset retry count for new search

      // Use initial batch size for filter changes
      fetchProducts(1, INITIAL_BATCH_SIZE, false);

      // Update refs
      prevSearchTermRef.current = searchTerm;
      prevCategoryFilterRef.current = categoryFilter;
    }
  }, [searchTerm, categoryFilter, fetchProducts]);

  // Update amount paid when cart total changes (including discount changes)
  // Only update if payment modal is not open or if payment type is 'full'
  useEffect(() => {
    if (showPayment && cartTotal > 0 && paymentType === 'full') {
      const roundedAmount = cartTotal.toFixed(decimalPlaces);
      console.log('🔄 Auto-updating amountPaid (showPayment effect):', roundedAmount);
      setAmountPaid(roundedAmount);
    }
  }, [cartTotal, showPayment, paymentType, decimalPlaces]);

  // Update amount paid when discount changes (only for full payments and when payment modal is open)
  useEffect(() => {
    if (showPayment && cartTotal > 0 && paymentType === 'full') {
      // Only update amount paid to match cart total for full payments when payment modal is open
      const roundedAmount = cartTotal.toFixed(decimalPlaces);
      console.log('🔄 Auto-updating amountPaid (discount effect):', roundedAmount);
      setAmountPaid(roundedAmount);
    }
  }, [discountAmount, discountType, cartTotal, paymentType, showPayment, decimalPlaces]);

  // Track payment type changes
  useEffect(() => {
    console.log('🔄 Payment type changed:', paymentType);
    if (showPayment) {
      if (paymentType === 'full') {
        const roundedAmount = cartTotal.toFixed(decimalPlaces);
        console.log('🔄 Setting amountPaid for full payment (type change):', roundedAmount);
        setAmountPaid(roundedAmount);
      } else if (paymentType === 'credit') {
        console.log('🔄 Setting amountPaid for credit payment (type change): 0');
        setAmountPaid('0');
      } else if (paymentType === 'partial') {
        console.log('🔄 Clearing amountPaid for partial payment (type change)');
        setAmountPaid('');
      }
    }
  }, [paymentType, showPayment, cartTotal]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleBarcodeSearch = (e: React.FormEvent) => {
    e.preventDefault();

    console.log('🔍 Barcode search triggered');

    // Get the form that was submitted
    const form = e.target as HTMLFormElement;
    const barcodeInput = form.querySelector('input[type="text"]') as HTMLInputElement;

    if (!barcodeInput) {
      console.error('❌ No barcode input found in form');
      return;
    }

    const barcode = barcodeInput.value.trim();

    if (!barcode) {
      setError('يرجى إدخال باركود المنتج');
      setTimeout(() => setError(null), 3000);
      return;
    }

    console.log('🔍 Searching for barcode:', barcode);

    // Search for product by barcode from backend
    searchProductByBarcode(barcode)
      .then(product => {
        if (product) {
          console.log('✅ Product found:', product.name);
          addToCart(product);
          barcodeInput.value = '';

          // Show success feedback
          setError(null);
        } else {
          console.log('❌ Product not found');
          setError(`لم يتم العثور على منتج بالباركود: ${barcode}`);
          setTimeout(() => setError(null), 3000);
        }
      })
      .catch(error => {
        console.error('❌ Error searching for product:', error);
        setError(error.message || 'فشل في البحث عن المنتج');
        setTimeout(() => setError(null), 3000);
      });

    // Focus the input field again
    setTimeout(() => {
      barcodeInput.focus();
    }, 100);
  };

  const handleCategoryFilter = (category: string | null) => {
    setCategoryFilter(category === categoryFilter ? null : category);
  };

  // Barcode scanning functionality removed for simplicity



  const updateCartItemQuantity = (productId: number, newQuantity: number) => {
    if (newQuantity < 1) return;

    // Find the product in the cart
    const cartItem = cart.find(item => item.id === productId);
    if (!cartItem) return;

    // Find the original product to check available quantity
    const originalProduct = filteredProducts.find(product => product.id === productId);
    if (!originalProduct) return;

    // Check if the new quantity exceeds available quantity
    if (newQuantity > originalProduct.quantity) {
      setError(`لا يمكن إضافة كمية أكبر من المتوفرة. الكمية المتوفرة: ${originalProduct.quantity}`);
      setTimeout(() => setError(null), 3000);
      return;
    }

    setCart(prevCart =>
      prevCart.map(item =>
        item.id === productId
          ? {
              ...item,
              cartQuantity: newQuantity,
              subtotal: newQuantity * item.price
            }
          : item
      )
    );
  };

  const removeFromCart = (productId: number) => {
    setCart(prevCart => prevCart.filter(item => item.id !== productId));
  };

  // Handle quantity modal
  const handleQuantityClick = (item: CartItem) => {
    setSelectedCartItem(item);
    setNewQuantity(item.cartQuantity.toString());
    setShowQuantityModal(true);
  };

  const handleQuantityModalSubmit = () => {
    if (!selectedCartItem) return;

    const quantity = parseInt(newQuantity);
    if (isNaN(quantity) || quantity < 1) {
      setError('يرجى إدخال كمية صحيحة');
      return;
    }

    updateCartItemQuantity(selectedCartItem.id, quantity);
    setShowQuantityModal(false);
    setSelectedCartItem(null);
    setNewQuantity('');
  };

  const handleQuantityModalCancel = () => {
    setShowQuantityModal(false);
    setSelectedCartItem(null);
    setNewQuantity('');
  };

  const clearCart = () => {
    setCart([]);
    setShowPayment(false);
    setPaymentMethod('cash');
    setAmountPaid('');
    setDiscountAmount('0');
    setDiscountType('fixed');
    setSaleCompleted(false);
    setSaleId(null);
    setSelectedCustomer(null);
    setPaymentType('full');
  };

  const proceedToPayment = () => {
    if (cart.length === 0) {
      setError('لا يمكن إتمام عملية الدفع، السلة فارغة.');
      setTimeout(() => setError(null), 3000);
      return;
    }

    // Set default amount based on payment type
    if (paymentType === 'full') {
      const roundedAmount = cartTotal.toFixed(decimalPlaces);
      console.log('💰 Setting amountPaid for full payment:', roundedAmount);
      setAmountPaid(roundedAmount); // Full amount
    } else if (paymentType === 'credit') {
      console.log('💰 Setting amountPaid for credit payment: 0');
      setAmountPaid('0'); // No payment for credit
    } else {
      console.log('💰 Setting amountPaid for partial payment: empty (user input)');
      setAmountPaid(''); // Let user enter partial amount
    }

    setShowPayment(true);
  };

  const handlePaymentSubmit = async () => {
    try {
      const amountPaidValue = parseFloat(amountPaid || '0');

      // Debug logging
      console.log('🔍 Payment validation:', {
        paymentType,
        amountPaid,
        amountPaidValue,
        cartTotal,
        cartSubtotal,
        discountValue,
        taxValue
      });

      // Validation based on payment type
      // Use tolerance for floating point comparison
      const tolerance = 0.01;
      const difference = cartTotal - amountPaidValue;

      if (paymentType === 'full' && difference > tolerance) {
        console.log('❌ Payment validation failed: Amount paid less than cart total');
        console.log('❌ Comparison details:', {
          amountPaidValue,
          cartTotal,
          difference,
          tolerance
        });
        setError('المبلغ المدفوع أقل من إجمالي الفاتورة.');
        return;
      }

      if (paymentType === 'partial' && amountPaidValue >= cartTotal) {
        setError('للدفع الجزئي، يجب أن يكون المبلغ المدفوع أقل من الإجمالي.');
        return;
      }

      if ((paymentType === 'partial' || paymentType === 'credit') && !selectedCustomer) {
        setError('يجب اختيار عميل للدفع الجزئي أو البيع الآجل.');
        return;
      }

      setIsLoading(true);

      // Create sale data according to the API schema
      const saleData = {
        payment_method: paymentType === 'credit' ? 'آجل' : paymentMethod, // Use "آجل" for credit payments
        total_amount: cartSubtotal, // Store amount BEFORE discount and tax
        tax_amount: taxValue, // Store calculated tax amount
        discount_amount: discountValue, // Store discount amount
        discount_type: discountType, // Store discount type ('fixed' or 'percentage')
        customer_id: selectedCustomer?.id || null, // Link to customer
        customer_name: selectedCustomer?.name || null, // Keep for backward compatibility
        notes: null, // Optional: Could add notes input
        amount_paid: paymentType === 'credit' ? 0 : amountPaidValue, // Force 0 for credit payments
        payment_status: paymentType === 'full' ? 'paid' : paymentType === 'partial' ? 'partial' : 'unpaid',
        items: cart.map(item => {
          return {
            product_id: item.id,
            quantity: item.cartQuantity,
            unit_price: item.price,
            discount: 0 // No individual discount - discount is now at sale level
          };
        })
      };

      console.log('Submitting sale:', saleData);

      try {
        // Get the current token from the auth store
        const authStore = useAuthStore.getState();
        const currentToken = authStore.token;

        if (!currentToken) {
          // Try to refresh the token first
          try {
            await authStore.refreshAccessToken();
            console.log('Token refreshed successfully');
          } catch (refreshError) {
            console.error('Failed to refresh token:', refreshError);
            // Continue with the sale process even if refresh fails
          }
        }

        // Get the token again (might be refreshed now)
        const token = authStore.token;

        if (!token) {
          throw new Error('No authentication token available');
        }

        // Submit the sale to the API
        console.log('Submitting sale to API with correct path');
        // First, check if there's a recent sale with the same total
        // This will help us detect if a sale was already processed
        let existingSale = null;
        try {
          const latestSalesResponse = await api.get('/api/sales/?limit=1');
          if (latestSalesResponse.data && latestSalesResponse.data.length > 0) {
            const latestSale = latestSalesResponse.data[0];
            // If the latest sale has the same total as our current cart, it might be a duplicate
            // Compare with cartSubtotal since total_amount in DB is before discount and tax
            if (Math.abs(latestSale.total_amount - cartSubtotal) < 0.01) {
              existingSale = latestSale;
              console.log('Found existing sale with matching total:', existingSale);
            }
          }
        } catch (error) {
          console.error('Error checking for existing sales:', error);
        }

        // If we already found an existing sale, use that instead of creating a new one
        if (existingSale) {
          console.log('Using existing sale instead of creating a new one:', existingSale.id);

          // Set the sale ID from the existing sale
          setSaleId(existingSale.id);
          setSaleCompleted(true);

          // Update product quantities in state
          setProducts(prevProducts =>
            prevProducts.map(product => {
              const soldItem = cart.find(item => item.id === product.id);
              if (soldItem) {
                return {
                  ...product,
                  quantity: product.quantity - soldItem.cartQuantity
                };
              }
              return product;
            })
          );

          // Update filtered products as well to reflect the changes in the UI
          setFilteredProducts(prevFiltered =>
            prevFiltered.map(product => {
              const soldItem = cart.find(item => item.id === product.id);
              if (soldItem) {
                return {
                  ...product,
                  quantity: product.quantity - soldItem.cartQuantity
                };
              }
              return product;
            })
          );

          setIsLoading(false);
          return;
        }

        try {
          const response = await api.post('/api/sales/', saleData);
          console.log('Sale response:', response.data);

          // Set the actual sale ID from the response
          setSaleId(response.data.id);
          setSaleCompleted(true);

          // Show success message
          setError(null); // Clear any previous errors

          // Update product quantities in state to reflect the changes
          setProducts(prevProducts =>
            prevProducts.map(product => {
              const soldItem = cart.find(item => item.id === product.id);
              if (soldItem) {
                return {
                  ...product,
                  quantity: product.quantity - soldItem.cartQuantity
                };
              }
              return product;
            })
          );

          // Update filtered products as well to reflect the changes in the UI
          setFilteredProducts(prevFiltered =>
            prevFiltered.map(product => {
              const soldItem = cart.find(item => item.id === product.id);
              if (soldItem) {
                return {
                  ...product,
                  quantity: product.quantity - soldItem.cartQuantity
                };
              }
              return product;
            })
          );

          setIsLoading(false);
        } catch (apiError: any) {
          console.error('API error when completing sale:', apiError);

          // Always check the latest sale regardless of the error type
          try {
            // Wait a short time to ensure the sale is processed
            await new Promise(resolve => setTimeout(resolve, 500));

            // Fetch the latest sale
            const latestSalesResponse = await api.get('/api/sales/?limit=1');
            console.log('Latest sales response after error:', latestSalesResponse.data);

            if (latestSalesResponse.data && latestSalesResponse.data.length > 0) {
              const latestSale = latestSalesResponse.data[0];

              // If we already checked for an existing sale before, make sure this is a different one
              if (!existingSale || latestSale.id !== existingSale.id) {
                console.log('Found new sale after error:', latestSale);

                // Check if this sale matches our cart total (simple verification)
                // Compare with cartSubtotal since total_amount in DB is before discount and tax
                if (Math.abs(latestSale.total_amount - cartSubtotal) < 0.01) {
                  console.log('Sale was created successfully despite error');

                  // Set the sale ID from the latest sale
                  setSaleId(latestSale.id);
                  setSaleCompleted(true);

                  // Update product quantities in state
                  setProducts(prevProducts =>
                    prevProducts.map(product => {
                      const soldItem = cart.find(item => item.id === product.id);
                      if (soldItem) {
                        return {
                          ...product,
                          quantity: product.quantity - soldItem.cartQuantity
                        };
                      }
                      return product;
                    })
                  );

                  // Update filtered products as well to reflect the changes in the UI
                  setFilteredProducts(prevFiltered =>
                    prevFiltered.map(product => {
                      const soldItem = cart.find(item => item.id === product.id);
                      if (soldItem) {
                        return {
                          ...product,
                          quantity: product.quantity - soldItem.cartQuantity
                        };
                      }
                      return product;
                    })
                  );

                  setIsLoading(false);
                  return;
                }
              }
            }
          } catch (fetchError) {
            console.error('Error fetching latest sale after error:', fetchError);
          }

          // If we get here, we couldn't verify the sale was successful
          setError('فشل في إتمام عملية البيع. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.');
          setIsLoading(false);
        }
      } catch (err) {
        setError('فشل في إتمام عملية البيع. يرجى المحاولة مرة أخرى.');
        setIsLoading(false);
      }
    } catch (err) {
      setError('فشل في إتمام عملية البيع. يرجى المحاولة مرة أخرى.');
      setIsLoading(false);
    }
  };

  const handlePrintReceipt = () => {
    if (saleId) {
      // Ensure we're using the correct sale ID from the API response
      // and not a simulated one
      navigate(`/sales/${saleId}/print`);
    } else {
      setError('لا يمكن طباعة الفاتورة. لم يتم إتمام عملية البيع بنجاح.');
      setTimeout(() => setError(null), 3000);
    }
  };

  const handleNewSale = () => {
    clearCart();
    setEditingInvoice(null);
    if (barcodeInputRef.current) {
      barcodeInputRef.current.focus();
    }
  };

  // Handle add customer
  const handleAddCustomer = () => {
    setShowAddCustomer(true);
  };

  const handleAddCustomerSubmit = async () => {
    if (!newCustomerName.trim()) {
      setError('يرجى إدخال اسم العميل');
      return;
    }

    try {
      setIsAddingCustomer(true);
      const customerData = {
        name: newCustomerName.trim(),
        phone: newCustomerPhone.trim() || null,
        email: null,
        address: null,
        is_active: true
      };

      const response = await api.post('/api/customers/', customerData);

      // Add total_debt property for consistency
      const newCustomer = {
        ...response.data,
        total_debt: 0
      };

      // Select the new customer
      setSelectedCustomer(newCustomer);

      // Refresh customer list in CustomerSelector
      if (customerSelectorRef.current && customerSelectorRef.current.refreshCustomers) {
        customerSelectorRef.current.refreshCustomers();
      }

      // Close modal and reset form
      setShowAddCustomer(false);
      setNewCustomerName('');
      setNewCustomerPhone('');

      setError(null);
    } catch (error: any) {
      console.error('Error adding customer:', error);
      if (error.response?.data?.detail) {
        setError(error.response.data.detail);
      } else {
        setError('فشل في إضافة العميل. يرجى المحاولة مرة أخرى.');
      }
    } finally {
      setIsAddingCustomer(false);
    }
  };

  const handleCancelAddCustomer = () => {
    setShowAddCustomer(false);
    setNewCustomerName('');
    setNewCustomerPhone('');
    setError(null);
  };

  // Fetch previous invoices with pagination and search
  const fetchPreviousInvoices = useCallback(async (page: number = 1, isLoadMore: boolean = false, searchTerm: string = '') => {
    try {
      if (isLoadMore) {
        setIsLoadingMoreInvoices(true);
      } else {
        setIsLoadingInvoices(true);
        setPreviousInvoices([]); // Clear existing invoices for fresh load
        setInvoicesPage(1);
        setHasMoreInvoices(true);
      }

      // Build query parameters
      const params = new URLSearchParams();
      params.append('limit', '10');
      params.append('page', page.toString());

      // Add search parameter (searches in invoice ID and customer name)
      if (searchTerm.trim()) {
        params.append('search', searchTerm.trim());
      }

      // Get sales with pagination and search
      const response = await api.get(`/api/sales/?${params.toString()}`);

      if (response.data && Array.isArray(response.data)) {
        // Get pagination info from headers
        const totalPages = parseInt(response.headers["x-pages"] || "1");
        const currentPageFromHeader = parseInt(response.headers["x-page"] || "1");

        setInvoicesPage(currentPageFromHeader);
        setHasMoreInvoices(currentPageFromHeader < totalPages);

        if (isLoadMore) {
          // Append new invoices to existing ones
          setPreviousInvoices(prev => [...prev, ...response.data]);
        } else {
          // Replace with new invoices
          setPreviousInvoices(response.data);
        }
      }
    } catch (error) {
      console.error('Error fetching previous invoices:', error);
      setError('فشل في تحميل الفواتير السابقة');
    } finally {
      setIsLoadingInvoices(false);
      setIsLoadingMoreInvoices(false);
    }
  }, []);

  // Load more invoices
  const loadMoreInvoices = useCallback(() => {
    if (hasMoreInvoices && !isLoadingMoreInvoices && !isLoadingInvoices) {
      fetchPreviousInvoices(invoicesPage + 1, true, invoiceSearchTerm);
    }
  }, [fetchPreviousInvoices, invoicesPage, hasMoreInvoices, isLoadingMoreInvoices, isLoadingInvoices, invoiceSearchTerm]);

  // Handle edit invoice
  const handleEditInvoice = useCallback(async (invoice: Sale) => {
    try {
      // Clear current cart
      clearCart();

      // Set editing invoice
      setEditingInvoice(invoice);

      // Load invoice items into cart
      if (invoice.items && invoice.items.length > 0) {
        for (const item of invoice.items) {
          // Find the product
          const product = products.find(p => p.id === item.product_id);
          if (product) {
            // Add to cart with the invoice quantity
            for (let i = 0; i < item.quantity; i++) {
              addToCart(product);
            }
          }
        }
      }

      // Set customer if exists
      if (invoice.customer_id) {
        try {
          const customerResponse = await api.get(`/api/customers/${invoice.customer_id}`);
          setSelectedCustomer(customerResponse.data);
        } catch (error) {
          console.error('Error fetching customer:', error);
        }
      }

      // Set payment details
      if (invoice.payment_status === 'unpaid') {
        setPaymentType('credit');
        setPaymentMethod('آجل'); // Force to آجل for credit
      } else if (invoice.payment_status === 'partial') {
        setPaymentType('partial');
        setPaymentMethod('جزئي'); // Force to جزئي for partial
      } else {
        setPaymentType('full');
        // For full payments, determine the actual payment method
        if (invoice.payment_method === 'آجل' || invoice.payment_method === 'جزئي') {
          setPaymentMethod('cash'); // Default to cash for full payments
        } else {
          setPaymentMethod(invoice.payment_method || 'cash');
        }
      }

      setAmountPaid(invoice.amount_paid?.toString() || '0');

      // Close previous invoices modal
      setShowPreviousInvoices(false);

    } catch (error) {
      console.error('Error loading invoice for editing:', error);
      setError('فشل في تحميل الفاتورة للتعديل');
    }
  }, [products, addToCart, clearCart]);

  // Handle search invoices
  const handleSearchInvoices = useCallback(() => {
    fetchPreviousInvoices(1, false, invoiceSearchTerm);
  }, [fetchPreviousInvoices, invoiceSearchTerm]);

  // Clear search and reload all invoices
  const handleClearSearch = useCallback(() => {
    setInvoiceSearchTerm('');
    fetchPreviousInvoices(1, false, '');
  }, [fetchPreviousInvoices]);

  // Open previous invoices modal
  const handleShowPreviousInvoices = useCallback(() => {
    setShowPreviousInvoices(true);
    setInvoiceSearchTerm('');
    fetchPreviousInvoices(1, false, ''); // Start from page 1 with no search
  }, [fetchPreviousInvoices]);

  // Update existing invoice
  const updateInvoice = useCallback(async () => {
    if (!editingInvoice) return;

    setIsLoading(true);
    try {
      // Create update data according to the API schema
      const updateData = {
        payment_method: paymentType === 'credit' ? 'آجل' : paymentType === 'partial' ? 'جزئي' : (paymentMethod === 'آجل' || paymentMethod === 'جزئي') ? 'cash' : paymentMethod, // Use correct payment method based on type
        total_amount: cartSubtotal, // Store amount BEFORE discount and tax
        tax_amount: taxValue,
        discount_amount: discountValue, // Store discount amount
        discount_type: discountType, // Store discount type
        customer_id: selectedCustomer?.id || editingInvoice.customer_id,
        customer_name: selectedCustomer?.name || editingInvoice.customer_name,
        notes: editingInvoice.notes,
        amount_paid: paymentType === 'credit' ? 0 : parseFloat(amountPaid || '0'), // Force 0 for credit payments
        payment_status: paymentType === 'full' ? 'paid' : paymentType === 'partial' ? 'partial' : 'unpaid',
        items: cart.map(item => {
          return {
            product_id: item.id,
            quantity: item.cartQuantity,
            unit_price: item.price,
            discount: 0 // No individual discount - discount is now at sale level
          };
        })
      };

      const response = await api.put(`/api/sales/${editingInvoice.id}`, updateData);
      console.log('Invoice updated:', response.data);

      // Set the sale ID and mark as completed
      setSaleId(response.data.id);
      setSaleCompleted(true);
      setError(null);

      // Update products state to reflect inventory changes
      setProducts(prevProducts =>
        prevProducts.map(product => {
          const cartItem = cart.find(item => item.id === product.id);
          const originalItem = editingInvoice.items.find(item => item.product_id === product.id);

          if (cartItem && originalItem) {
            // Calculate the difference in quantity
            const quantityDiff = cartItem.cartQuantity - originalItem.quantity;
            return {
              ...product,
              quantity: product.quantity - quantityDiff
            };
          } else if (cartItem && !originalItem) {
            // New item added
            return {
              ...product,
              quantity: product.quantity - cartItem.cartQuantity
            };
          } else if (!cartItem && originalItem) {
            // Item removed
            return {
              ...product,
              quantity: product.quantity + originalItem.quantity
            };
          }
          return product;
        })
      );

      setIsLoading(false);
    } catch (err) {
      console.error('Error updating invoice:', err);
      setError('فشل في تحديث الفاتورة. يرجى المحاولة مرة أخرى.');
      setIsLoading(false);
    }
  }, [editingInvoice, paymentMethod, paymentType, cartSubtotal, taxValue, discountValue, discountType, cart, selectedCustomer, amountPaid]);

  return (
    <div className="flex flex-col h-screen w-full bg-gray-50 dark:bg-gray-900 overflow-hidden">
      {/* Global App Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 py-2 px-4 flex justify-between items-center">
        <div className="flex items-center">
          <Link to="/" className="flex items-center">
            <span className="text-xl font-bold text-primary-600">
              Smart<span className="text-secondary-800 dark:text-secondary-200">POS</span>
            </span>
          </Link>
        </div>
        <div className="flex items-center gap-2">
          {/* Barcode Indicator */}
          {showBarcodeIndicator && (
            <div className="bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 px-3 py-1 rounded-lg font-medium flex items-center text-sm animate-pulse">
              <FaBarcode className="ml-1" />
              <span>جاري المسح...</span>
            </div>
          )}

          {/* Chat Button */}
          <ChatHeaderButton />

          {/* Theme Toggle Button */}
          <button
            onClick={toggleTheme}
            className="p-2 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            aria-label={currentTheme === 'dark' ? 'التبديل إلى الوضع المضيء' : 'التبديل إلى الوضع المظلم'}
          >
            {currentTheme === 'dark' ? <FaSun className="text-yellow-400" /> : <FaMoon className="text-gray-700" />}
          </button>
          <span className="bg-blue-50 dark:bg-blue-900/40 px-3 py-1 rounded-lg text-blue-700 dark:text-blue-200 text-sm font-medium flex items-center">
            <FaUserCircle className="ml-1 text-blue-500 dark:text-blue-300" />
            <span>{user?.full_name || 'مستخدم'}</span>
          </span>
          <span className="bg-green-50 dark:bg-green-900/40 text-green-700 dark:text-green-200 px-3 py-1 rounded-lg font-medium flex items-center text-sm">
            <FaReceipt className="ml-1" />
            <span><FormattedCurrency amount={cartTotal} /></span>
          </span>
        </div>
      </div>

      {/* POS Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm py-2 px-4 border-b border-gray-200 dark:border-gray-700 z-40 flex items-center">
        <button
          onClick={() => navigate('/')}
          className="text-secondary-600 dark:text-secondary-300 hover:bg-secondary-50 dark:hover:bg-secondary-700 h-8 w-8 flex items-center justify-center rounded-full mr-2"
        >
          <FaArrowLeft className="text-lg" />
        </button>
        <h1 className="text-lg font-bold text-secondary-900 dark:text-secondary-100">نقطة البيع</h1>
      </div>

      {/* Main Content */}
      <div className="flex flex-col md:flex-row flex-1 overflow-hidden h-full w-full">
        {/* Mobile Barcode Search - Only visible on mobile */}
        <div className="md:hidden p-2 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 z-20">
          <form onSubmit={handleBarcodeSearch} className="flex gap-2">
            <div className="relative flex-1">
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <FaBarcode className="text-gray-400 dark:text-gray-500" />
              </div>
              <input
                ref={barcodeInputRef}
                type="text"
                placeholder="باركود المنتج..."
                className="input-field pr-10 text-base w-full py-2 focus:ring-2 focus:ring-primary-500"
                autoFocus
              />
            </div>
            <button
              type="submit"
              className="btn-secondary py-2 px-3 whitespace-nowrap text-sm"
            >
              بحث
            </button>
            {/* Camera button for mobile */}
            <button
              type="button"
              onClick={() => setShowScanner(true)}
              className="btn-secondary py-2 px-3 whitespace-nowrap text-sm flex items-center justify-center"
              aria-label="فتح الكاميرا لمسح الباركود"
            >
              <FaCamera className="text-lg" />
            </button>
          </form>
        </div>

        {/* Mobile Products Modal - Only visible on mobile when showMobileProducts is true */}
        {showMobileProducts && (
          <Modal
            isOpen={showMobileProducts}
            onClose={() => setShowMobileProducts(false)}
            title="المنتجات"
            size="full"
          >
            <div className="h-full flex flex-col">
                  {/* Mobile Search */}
                  <div className="mb-4">
                    <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4">
                      <div className="flex gap-2">
                        <div className="relative flex-1">
                          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <FaSearch className="text-gray-400 dark:text-gray-500" />
                          </div>
                          <input
                            type="text"
                            placeholder="البحث عن منتج..."
                            value={searchTerm}
                            onChange={handleSearchChange}
                            className="w-full px-4 py-3 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
                            autoFocus
                          />
                        </div>

                        {/* Clear Search Button */}
                        {searchTerm && (
                          <button
                            onClick={() => setSearchTerm('')}
                            className="flex-shrink-0 bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 p-3 rounded-lg transition-all duration-200 border border-gray-300 dark:border-gray-600 shadow-sm hover:shadow-md"
                            title="مسح البحث"
                          >
                            <FaTimes className="text-sm" />
                          </button>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Mobile Filters - Fixed Container */}
                  <div className="mb-4 sticky top-0 z-30 bg-gray-50 dark:bg-gray-900 pb-2 -mx-4 px-4">
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700 shadow-lg backdrop-blur-sm">
                      {/* Categories */}
                      {categories.length > 0 && (
                        <div className="mb-4">
                          <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2">
                            <FaThLarge className="text-primary-500 text-xs" />
                            الفئات
                          </h3>
                          {/* Horizontal scrollable categories */}
                          <div className="relative">
                            {/* Scroll indicators */}
                            <div className="absolute left-0 top-0 bottom-0 w-4 bg-gradient-to-r from-white dark:from-gray-800 to-transparent z-10 pointer-events-none opacity-50"></div>
                            <div className="absolute right-0 top-0 bottom-0 w-4 bg-gradient-to-l from-white dark:from-gray-800 to-transparent z-10 pointer-events-none opacity-50"></div>

                            <div
                              className="flex gap-2 overflow-x-auto pb-2 modal-scrollbar scroll-smooth px-2"
                              style={{
                                scrollbarWidth: 'thin',
                                scrollBehavior: 'smooth',
                                scrollbarColor: '#cbd5e1 transparent'
                              }}
                            >
                            <button
                              onClick={() => setCategoryFilter(null)}
                              className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 flex-shrink-0 ${
                                categoryFilter === null
                                  ? 'bg-primary-500 text-white'
                                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                              }`}
                            >
                              <span className="flex items-center gap-1.5">
                                <FaThLarge className="text-xs" />
                                الكل
                              </span>
                            </button>

                            {categories.map(category => (
                              <button
                                key={category}
                                onClick={() => handleCategoryFilter(category)}
                                className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 flex-shrink-0 whitespace-nowrap ${
                                  categoryFilter === category
                                    ? 'bg-primary-500 text-white'
                                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                                }`}
                              >
                                <span className="flex items-center gap-1.5">
                                  <FaTag className="text-xs" />
                                  {category}
                                </span>
                              </button>
                            ))}
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Hide Zero Stock Toggle */}
                      <div className={`${categories.length > 0 ? 'pt-4 border-t border-gray-200 dark:border-gray-700' : ''}`}>
                        <div className="bg-white dark:bg-gray-700 p-3 rounded-xl border-2 border-gray-300 dark:border-gray-600 transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500">
                          <ToggleSwitch
                            id="mobile-hide-zero-stock"
                            checked={hideZeroStock}
                            onChange={(checked) => setHideZeroStock(checked)}
                            label="إخفاء الغير متوفر"
                            className="text-sm w-full"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Mobile Products List */}
                  <div
                    ref={mobileProductsContainerRef}
                    className="flex-1 overflow-y-auto modal-scrollbar"
                  >
                    {isLoading ? (
                      <div className="flex flex-col justify-center items-center h-full py-20">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mb-4"></div>
                        <p className="text-gray-600 dark:text-gray-400">جاري تحميل المنتجات...</p>
                      </div>
                    ) : filteredProducts.length > 0 ? (
                      <div className="space-y-3 p-1">
                        {filteredProducts.map((product, index) => (
                          <div
                            key={`mobile-${product.id}-${index}`}
                            onClick={() => {
                              if (product.quantity > 0) {
                                addToCart(product);
                                setShowMobileProducts(false); // Close the modal after adding to cart
                              }
                            }}
                            className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border transition-all duration-300 ${
                              product.quantity > 0
                                ? 'border-gray-200 dark:border-gray-700 hover:border-primary-300 dark:hover:border-primary-500 hover:shadow-md cursor-pointer active:scale-[0.98]'
                                : 'border-gray-200 dark:border-gray-700 opacity-70 cursor-not-allowed'
                            }`}
                          >
                            <div className="flex items-center p-4 gap-3">
                              {/* Product Icon */}
                              <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 rounded-lg flex items-center justify-center border border-primary-200 dark:border-primary-700">
                                <FaBox className="text-primary-500 dark:text-primary-400 text-sm" />
                              </div>

                              {/* Product Info */}
                              <div className="flex-1 min-w-0">
                                <div className="flex items-start justify-between mb-3">
                                  <h3 className="font-semibold text-gray-900 dark:text-gray-100 text-base line-clamp-2 flex-1 pr-3">
                                    {product.name}
                                  </h3>

                                  {/* Status Badge */}
                                  <span className={`text-xs font-medium px-2.5 py-1 rounded-full flex-shrink-0 ${
                                    product.quantity === 0
                                      ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
                                      : product.quantity > product.min_quantity
                                        ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                                        : 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400'
                                  }`}>
                                    {product.quantity === 0
                                      ? 'نفد'
                                      : product.quantity > product.min_quantity
                                        ? 'متوفر'
                                        : 'قليل'}
                                  </span>
                                </div>

                                <div className="flex items-center justify-between">
                                  {/* Price */}
                                  <div className="text-primary-600 dark:text-primary-400 font-bold text-lg">
                                    {product.price.toFixed(2)} د.ل
                                  </div>

                                  {/* Quantity, Unit and Add to Cart */}
                                  <div className="flex items-center gap-2">
                                    <div className="flex items-center bg-gray-100 dark:bg-gray-700 px-2.5 py-1.5 rounded-lg">
                                      <FaBoxOpen className="ml-1.5 text-gray-500 dark:text-gray-400 text-xs" />
                                      <span className="text-sm text-gray-600 dark:text-gray-300 font-medium">
                                        {product.quantity}
                                      </span>
                                    </div>

                                    <span className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2.5 py-1.5 rounded-lg font-medium">
                                      {product.unit}
                                    </span>

                                    {/* Add to Cart Icon */}
                                    {product.quantity > 0 && (
                                      <div className="bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 p-2.5 rounded-lg border border-primary-200 dark:border-primary-700">
                                        <FaCartPlus className="text-sm" />
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}

                        {/* Load More Button for mobile - Simple and reliable */}
                        {hasMore && !isLoadingMore && !isLoading && filteredProducts.length > 0 && (
                          <div className="flex justify-center items-center py-6 px-4">
                            <button
                              onClick={loadMoreProducts}
                              className="w-full bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white font-semibold py-4 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] border border-primary-400"
                            >
                              <div className="flex items-center justify-center gap-3">
                                <div className="bg-white/20 p-2 rounded-lg">
                                  <FaPlus className="text-white text-sm" />
                                </div>
                                <div className="text-center">
                                  <div className="text-base font-bold">تحميل المزيد من المنتجات</div>
                                  <div className="text-xs text-primary-100 mt-1">
                                    تحميل 50 منتج إضافي • المعروض حالياً: {filteredProducts.length}
                                  </div>
                                </div>
                                <div className="bg-white/20 p-2 rounded-lg">
                                  <FaBox className="text-white text-sm" />
                                </div>
                              </div>
                            </button>
                          </div>
                        )}

                        {/* Loading more indicator for mobile */}
                        {isLoadingMore && (
                          <div className="flex justify-center items-center py-8 animate-fadeIn">
                            <div className="flex items-center gap-3 bg-gradient-to-r from-white to-gray-50 dark:from-gray-800 dark:to-gray-750 rounded-xl p-4 shadow-lg border border-gray-200 dark:border-gray-700 mx-4 backdrop-blur-sm">
                              <div className="relative">
                                <div className="animate-spin rounded-full h-6 w-6 border-2 border-primary-200 dark:border-primary-800"></div>
                                <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-primary-600 dark:border-primary-400 absolute top-0 left-0"></div>
                              </div>
                              <span className="text-gray-600 dark:text-gray-400 font-medium">جاري تحميل 50 منتج...</span>
                              <div className="flex gap-1">
                                <div className="w-1 h-1 bg-primary-400 rounded-full animate-pulse"></div>
                                <div className="w-1 h-1 bg-primary-400 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
                                <div className="w-1 h-1 bg-primary-400 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
                              </div>
                            </div>
                          </div>
                        )}

                        {/* End of products message for mobile */}
                        {!hasMore && filteredProducts.length > 0 && !isLoadingMore && (
                          <div className="text-center py-6">
                            <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-xl p-4 mx-4 border border-green-200 dark:border-green-700 shadow-sm">
                              <div className="flex items-center justify-center mb-3">
                                <div className="bg-green-100 dark:bg-green-800/30 p-2 rounded-full">
                                  <FaCheck className="text-green-600 dark:text-green-400 text-lg" />
                                </div>
                              </div>
                              <h3 className="text-base font-semibold text-gray-800 dark:text-gray-200 mb-2">
                                تم تحميل جميع المنتجات بنجاح
                              </h3>
                              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                                إجمالي: <span className="font-semibold text-green-600 dark:text-green-400">{filteredProducts.length}</span> منتج
                                {categoryFilter && (
                                  <span className="inline-block text-xs mt-1 bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 px-2 py-1 rounded-full">
                                    في فئة: {categoryFilter}
                                  </span>
                                )}
                              </p>
                              <button
                                onClick={() => resetAndReloadProducts(false)}
                                className="text-xs bg-green-500 hover:bg-green-600 text-white px-3 py-1.5 rounded-lg transition-all duration-200 hover:shadow-md"
                              >
                               إعادة تحديث
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center h-full text-center py-12">
                        <div className="bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 p-12 rounded-2xl mb-8 shadow-inner">
                          <FaBox className="text-6xl text-gray-400 dark:text-gray-500" />
                        </div>
                        <h3 className="text-xl mb-3 font-bold text-gray-900 dark:text-gray-100">
                          لم يتم العثور على منتجات مطابقة
                        </h3>
                        <p className="text-gray-500 dark:text-gray-400 max-w-md mb-6">
                          حاول تغيير معايير البحث أو تصفية الفئات للعثور على المنتجات المطلوبة
                        </p>
                        <button
                          onClick={() => resetAndReloadProducts(true)}
                          className="px-6 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-lg font-medium transition-all duration-200 hover:shadow-md"
                        >
                          🔄 إعادة ضبط البحث
                        </button>
                      </div>
                    )}
              </div>
            </div>
          </Modal>
        )}

        {/* Products Panel - Hidden on mobile, visible on desktop */}
        <div className="hidden md:flex w-full md:w-2/3 p-4 flex-col overflow-hidden order-2 md:order-1 h-full">
          {/* Search */}
          <div className="flex flex-col sm:flex-row mb-4 gap-3">
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <FaSearch className="text-gray-400 dark:text-gray-500" />
              </div>
              <input
                ref={searchInputRef}
                type="text"
                placeholder="البحث عن منتج..."
                value={searchTerm}
                onChange={handleSearchChange}
                className="input-field pr-10 text-base w-full py-2 focus:ring-1"
              />
            </div>

            <form onSubmit={handleBarcodeSearch} className="flex-1 flex gap-2">
              <div className="relative flex-1">
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <FaBarcode className="text-gray-400 dark:text-gray-500" />
                </div>
                <input
                  ref={desktopBarcodeInputRef}
                  type="text"
                  placeholder="باركود المنتج..."
                  className="input-field pr-10 text-base w-full py-2 focus:ring-2 focus:ring-primary-500"
                />
              </div>
              <button
                type="submit"
                className="btn-secondary py-2 px-3 whitespace-nowrap text-sm"
              >
                بحث
              </button>
            </form>
          </div>

          {/* Filters Section */}
          <div className="mb-4" style={{ overflow: 'visible' }}>
            <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700 shadow-sm" style={{ overflow: 'visible' }}>
              <div className="flex items-center gap-4">
                {/* Categories Section */}
                <div className="flex-1 min-w-0" ref={categoriesContainerRef}>
                  {categories.length > 0 && (
                    <div className="flex items-center gap-2" style={{ overflow: 'visible' }}>
                      <button
                        onClick={() => setCategoryFilter(null)}
                        className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 flex-shrink-0 ${
                          categoryFilter === null
                            ? 'bg-primary-500 text-white'
                            : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                        }`}
                      >
                        <span className="flex items-center gap-1.5">
                          <FaThLarge className="text-xs" />
                          الكل
                        </span>
                      </button>

                      {/* Categories container with dynamic overflow handling */}
                      <div className="flex items-center gap-2 overflow-hidden">
                        {/* Show categories that fit dynamically */}
                        <div className="flex items-center gap-2 flex-shrink-0">
                          {categories.slice(0, visibleCategoriesCount).map(category => (
                            <button
                              key={category}
                              onClick={() => handleCategoryFilter(category)}
                              className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 whitespace-nowrap ${
                                categoryFilter === category
                                  ? 'bg-primary-500 text-white'
                                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                              }`}
                            >
                              <span className="flex items-center gap-1.5">
                                <FaTag className="text-xs" />
                                {category}
                              </span>
                            </button>
                          ))}
                        </div>

                        {/* More categories dropdown - only show if there are hidden categories */}
                        {visibleCategoriesCount < categories.length && (
                          <div className="relative flex-shrink-0">
                            <button
                              ref={moreButtonRef}
                              onClick={(e) => {
                                const rect = e.currentTarget.getBoundingClientRect();
                                setDropdownPosition({
                                  top: rect.bottom + window.scrollY + 8,
                                  left: rect.left + window.scrollX
                                });
                                setShowMoreCategories(!showMoreCategories);
                              }}
                              className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 border ${
                                showMoreCategories
                                  ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 border-primary-200 dark:border-primary-700'
                                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600'
                              }`}
                            >
                              <span className="flex items-center gap-1.5">
                                <FaPlus className={`text-xs transition-transform duration-200 ${showMoreCategories ? 'rotate-45' : ''}`} />
                                +{categories.length - visibleCategoriesCount}
                              </span>
                            </button>
                          </div>
                        )}
                      </div>

                      {/* Dropdown menu - positioned dynamically */}
                      {showMoreCategories && visibleCategoriesCount < categories.length && (
                        <div
                          data-dropdown="categories"
                          className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-xl min-w-[200px] max-h-80 overflow-hidden"
                          style={{
                            position: 'fixed',
                            top: `${dropdownPosition.top}px`,
                            left: `${dropdownPosition.left}px`,
                            zIndex: 10000,
                            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
                          }}
                        >
                          <div className="p-3">
                            <div className="text-xs font-semibold text-primary-600 dark:text-primary-400 px-3 py-2 bg-primary-50 dark:bg-primary-900/30 rounded-lg mb-2 text-center border border-primary-200 dark:border-primary-700">
                              <FaThLarge className="inline-block ml-2 text-xs" />
                              الفئات الإضافية ({categories.length - visibleCategoriesCount})
                            </div>
                            <div className="space-y-1 max-h-60 overflow-y-auto modal-scrollbar">
                              {categories.slice(visibleCategoriesCount).map((category) => (
                                <button
                                  key={category}
                                  onClick={() => {
                                    handleCategoryFilter(category);
                                    setShowMoreCategories(false);
                                  }}
                                  className={`w-full text-right px-3 py-2.5 text-sm rounded-lg transition-all duration-200 flex items-center gap-2 ${
                                    categoryFilter === category
                                      ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 border border-primary-200 dark:border-primary-700'
                                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50'
                                  }`}
                                >
                                  <FaTag className="text-xs flex-shrink-0" />
                                  <span className="flex-1 text-right">{category}</span>
                                  {categoryFilter === category && (
                                    <FaCheck className="text-xs text-primary-500" />
                                  )}
                                </button>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Separator */}
                {categories.length > 0 && (
                  <div className="flex-shrink-0">
                    <div className="h-8 w-px bg-gradient-to-b from-transparent via-gray-300 dark:via-gray-600 to-transparent"></div>
                  </div>
                )}

                {/* Hide Zero Stock Toggle - Fixed at the end */}
                <div className="flex-shrink-0">
                  <div className="bg-white dark:bg-gray-700 p-3 rounded-xl border-2 border-gray-300 dark:border-gray-600 transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500">
                    <div className="flex items-center gap-2 text-sm w-full">
                      <label htmlFor="hide-zero-stock" className="text-gray-800 dark:text-gray-200">
                        إخفاء الغير متوفر
                        </label>
                        <ToggleSwitch
                          id="hide-zero-stock"
                          checked={hideZeroStock}
                          onChange={(checked) => setHideZeroStock(checked)}
                          label="" // <-- تمرير قيمة فارغة لتجنب الخطأ
                        />
                    </div>
                  </div>
                </div>    
              </div>
            </div>
          </div>

          {/* Barcode Buffer Display */}
          {barcodeBuffer.length > 0 && !showPayment && !showPreviousInvoices && !showMobileProducts && !showScanner && (
            <div className="bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 p-2 rounded-xl mb-2 shadow-sm flex items-center animate-fadeIn">
              <FaBarcode className="ml-2 flex-shrink-0 text-primary-600 dark:text-primary-400" />
              <span className="text-sm font-mono">{barcodeBuffer}</span>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className={`p-3 rounded-xl mb-3 shadow-sm flex items-center animate-fadeIn ${
              isRetrying
                ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300'
                : 'bg-danger-100 dark:bg-danger-900/30 text-danger-700 dark:text-danger-300'
            }`}>
              {isRetrying ? (
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-yellow-600 dark:border-yellow-400 border-t-transparent ml-2 flex-shrink-0"></div>
              ) : (
                <FaTimes className="ml-2 flex-shrink-0 text-danger-600 dark:text-danger-400" />
              )}
              <span className="text-sm">{error}</span>
            </div>
          )}

          {/* Products Grid */}
          <div
            ref={productsContainerRef}
            className="flex-1 overflow-y-auto overflow-x-hidden px-4 py-4 pr-6 custom-scrollbar-auto"
          >
            {isLoading && !isLoadingMore ? (
              <div className="flex justify-center items-center h-full py-20">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
              </div>
            ) : filteredProducts.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 auto-rows-fr gap-4">
                {filteredProducts.map((product, index) => (
                  <div
                    key={`desktop-${product.id}-${index}`}
                    onClick={() => product.quantity > 0 && addToCart(product)}
                    className={`product-card group ${product.quantity === 0 ? 'opacity-70 cursor-not-allowed' : ''}`}
                  >
                    <div className="product-card-inner">
                      {/* Product status badge */}
                      <div className="absolute top-3 left-3 z-10">
                        <span className={`text-xs font-medium px-2.5 py-1 rounded-full shadow-sm ${
                          product.quantity === 0
                            ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
                            : product.quantity > product.min_quantity
                              ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                              : 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400'
                        }`}>
                          {product.quantity === 0
                            ? 'نفد'
                            : product.quantity > product.min_quantity
                              ? 'متوفر'
                              : 'قليل'}
                        </span>
                      </div>

                      {/* Product unit badge */}
                      <div className="absolute top-3 right-3 z-10">
                        <span className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2.5 py-1 rounded-full font-medium shadow-sm">
                          {product.unit}
                        </span>
                      </div>

                      {/* Product content */}
                      <div className="flex flex-col h-full justify-between pt-8">
                        <div className="flex-1">
                          {/* Product name */}
                          <h3 className="font-bold text-base text-secondary-900 dark:text-secondary-100 line-clamp-2 mb-2">{product.name}</h3>

                          {/* Product description if available */}
                          {product.description && (
                            <p className="text-secondary-500 dark:text-secondary-400 text-xs line-clamp-2 mb-3">{product.description}</p>
                          )}
                        </div>

                        <div className="mt-auto pt-3 border-t border-gray-100 dark:border-gray-700">
                          <div className="flex justify-between items-center">
                            <span className="text-primary-600 dark:text-primary-400 font-bold text-base">
                              <FormattedCurrency amount={product.price} />
                            </span>
                            <div className="flex items-center gap-1.5 bg-gray-100 dark:bg-gray-700 px-2.5 py-1.5 rounded-lg">
                              <FaBoxOpen className="text-gray-400 dark:text-gray-500 text-xs" />
                              <span className="text-gray-700 dark:text-gray-300 text-xs font-medium">
                                {product.quantity}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Hover overlay */}
                      <div className="product-card-overlay">
                        <button className={`add-to-cart-btn ${product.quantity === 0 ? 'bg-gray-500 hover:bg-gray-600' : ''}`}>
                          {product.quantity === 0 ? (
                            <>
                              <FaTimes className="text-xl" />
                              <span>غير متوفر</span>
                            </>
                          ) : (
                            <>
                              <FaCartPlus className="text-xl" />
                              <span>إضافة للسلة</span>
                            </>
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                ))}

                {/* Loading more indicator */}
                {isLoadingMore && (
                  <div className="col-span-full flex justify-center items-center py-8 animate-fadeIn">
                    <div className="flex items-center gap-3 bg-gradient-to-r from-white to-gray-50 dark:from-gray-800 dark:to-gray-750 rounded-xl p-4 shadow-lg border border-gray-200 dark:border-gray-700 backdrop-blur-sm">
                      <div className="relative">
                        <div className="animate-spin rounded-full h-6 w-6 border-2 border-primary-200 dark:border-primary-800"></div>
                        <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-primary-600 dark:border-primary-400 absolute top-0 left-0"></div>
                      </div>
                      <span className="text-gray-600 dark:text-gray-400 font-medium">جاري تحميل 50 منتج إضافي...</span>
                      <div className="flex gap-1">
                        <div className="w-1 h-1 bg-primary-400 rounded-full animate-pulse"></div>
                        <div className="w-1 h-1 bg-primary-400 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
                        <div className="w-1 h-1 bg-primary-400 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
                      </div>
                    </div>
                  </div>
                )}

                {/* End of products message */}
                {!hasMore && filteredProducts.length > 0 && !isLoadingMore && (
                  <div className="col-span-full text-center py-8">
                    <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-xl p-6 border border-green-200 dark:border-green-700 max-w-sm mx-auto shadow-sm">
                      <div className="flex items-center justify-center mb-3">
                        <div className="bg-green-100 dark:bg-green-800/30 p-2 rounded-full">
                          <FaCheck className="text-green-600 dark:text-green-400 text-xl" />
                        </div>
                      </div>
                      <h3 className="text-base font-semibold text-gray-800 dark:text-gray-200 mb-2">
                        تم تحميل جميع المنتجات بنجاح
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                        إجمالي: <span className="font-semibold text-green-600 dark:text-green-400">{filteredProducts.length}</span> منتج
                        {categoryFilter && (
                          <span className="inline-block text-xs mt-1 bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 px-2 py-1 rounded-full">
                            في فئة: {categoryFilter}
                          </span>
                        )}
                      </p>
                      <button
                        onClick={() => resetAndReloadProducts(false)}
                        className="text-xs bg-green-500 hover:bg-green-600 text-white px-3 py-1.5 rounded-lg transition-all duration-200 hover:shadow-md"
                      >
                      إعادة تحديث
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex flex-col justify-center items-center h-full text-secondary-500 dark:text-secondary-400 py-20">
                <div className="bg-gray-100 dark:bg-gray-700 p-6 rounded-full mb-4">
                  <FaBox className="text-4xl text-gray-400 dark:text-gray-500" />
                </div>
                <p className="text-lg mb-2">لم يتم العثور على منتجات مطابقة</p>
                <p className="text-sm text-gray-400 dark:text-gray-500 text-center">حاول تغيير معايير البحث أو تصفية الفئات</p>
              </div>
            )}
          </div>
        </div>

        {/* Cart Panel - Full width on mobile */}
        <div className="w-full md:w-1/3 bg-white dark:bg-gray-800 shadow-soft flex flex-col overflow-hidden order-1 md:order-2 md:border-r md:border-gray-200 dark:md:border-gray-700 h-full flex-1">
          {/* Cart Header */}
          <div className="bg-primary-600 dark:bg-primary-700 text-white py-3 px-4 sticky top-0 z-10 shadow-lg border-b border-primary-500 dark:border-primary-600">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <div className="bg-white/15 p-2 rounded-lg ml-3 border border-white/15">
                  <FaShoppingCart className="text-white text-base" />
                </div>
                <div>
                  <h2 className="text-lg font-bold text-white leading-tight">
                    {editingInvoice ? `تعديل فاتورة #${editingInvoice.id}` : 'سلة المبيعات'}
                  </h2>
                  <div className="flex items-center text-white/80 text-xs font-medium mt-0.5">
                    <span>{cartItemCount} {cartItemCount === 1 ? 'عنصر' : 'عناصر'}</span>
                    {editingInvoice && (
                      <>
                        <span className="mx-1.5">•</span>
                        <span className="bg-orange-400/70 text-white px-1.5 py-0.5 rounded-full text-xs">
                          وضع التعديل
                        </span>
                      </>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-1.5">
                {/* Cancel Edit Button - Only show when editing */}
                {editingInvoice && (
                  <button
                    onClick={() => {
                      setEditingInvoice(null);
                      clearCart();
                    }}
                    className="bg-red-500/80 hover:bg-red-500 text-white p-2 rounded-lg transition-all duration-300 hover:scale-105 active:scale-95 border border-red-400/50"
                    title="إلغاء التعديل"
                  >
                    <FaTimes className="text-sm" />
                  </button>
                )}

                {/* Previous Invoices Button - Hide when editing */}
                {!editingInvoice && (
                  <button
                    onClick={handleShowPreviousInvoices}
                    className="bg-white/15 hover:bg-white/25 text-white p-2 rounded-lg transition-all duration-300 hover:scale-105 active:scale-95 border border-white/15"
                    title="الفواتير السابقة"
                  >
                    <FaHistory className="text-sm" />
                  </button>
                )}

                {/* Mobile Products Toggle Button - Only visible on mobile */}
                <button
                  onClick={() => setShowMobileProducts(!showMobileProducts)}
                  className="md:hidden bg-white/15 hover:bg-white/25 text-white p-2 rounded-lg transition-all duration-300 hover:scale-105 active:scale-95 border border-white/15"
                  title="عرض المنتجات"
                >
                  {showMobileProducts ? <FaTimes className="text-sm" /> : <FaThLarge className="text-sm" />}
                </button>

                {/* Clear Cart Button - Hide when editing */}
                {cart.length > 0 && !editingInvoice && (
                  <button
                    onClick={clearCart}
                    className="bg-red-400/60 hover:bg-red-500/70 text-white p-2 rounded-lg transition-all duration-300 hover:scale-105 active:scale-95 border border-white/15"
                    title="إفراغ السلة"
                  >
                    <FaTrash className="text-sm" />
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Cart Items */}
          <div className="flex-1 overflow-y-auto p-2 md:p-3 custom-scrollbar pr-2">
            {cart.length > 0 ? (
              <div className="space-y-2">
                {cart.map((item, index) => (
                  <div
                    key={item.id}
                    className={`${
                      index % 2 === 0
                        ? 'bg-white dark:bg-gray-800'
                        : 'bg-gray-50 dark:bg-gray-775'
                    } rounded-lg shadow-sm hover:shadow-md border border-gray-200 dark:border-gray-700 hover:border-primary-300 dark:hover:border-primary-500 transition-all duration-200`}
                  >
                    {/* Compact Product Info */}
                    <div className="p-3">
                      <div className="flex items-center justify-between">
                        {/* Product Details */}
                        <div className="flex items-center flex-1 min-w-0">
                          <div className="bg-primary-50 dark:bg-primary-900/30 p-1.5 rounded-lg ml-2 flex-shrink-0">
                            <FaBox className="text-primary-600 dark:text-primary-400 text-sm" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 text-sm mb-1 truncate">{item.name}</h4>
                            <div className="flex items-center text-xs text-gray-600 dark:text-gray-300">
                              <span className="bg-green-50 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-1.5 py-0.5 rounded font-medium">
                                <FormattedCurrency amount={item.price} />
                              </span>
                              <span className="mx-1 text-gray-400">×</span>
                              <span className="bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 px-1.5 py-0.5 rounded font-medium">
                                {item.cartQuantity}
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Total Price */}
                        <div className="text-left ml-2 flex-shrink-0">
                          <div className="bg-green-50 dark:bg-green-900/30 px-2 py-1 rounded-lg">
                            <div className="text-green-700 dark:text-green-200 font-bold text-sm">
                              <FormattedCurrency amount={item.subtotal} />
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Compact Controls Section */}
                      <div className="flex items-center justify-between mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                        {/* Quantity Controls */}
                        <div className="flex items-center">
                          <div className="flex items-center bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                updateCartItemQuantity(item.id, item.cartQuantity - 1);
                              }}
                              className="text-gray-600 dark:text-gray-400 hover:bg-red-50 dark:hover:bg-red-900/30 hover:text-red-600 dark:hover:text-red-400 h-7 w-7 flex items-center justify-center transition-all duration-200"
                            >
                              <FaMinus className="text-xs" />
                            </button>

                            {/* Quantity display - clickable */}
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleQuantityClick(item);
                              }}
                              className="px-2 py-1 bg-white dark:bg-gray-700 border-x border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors cursor-pointer"
                              title="اضغط لتعديل الكمية يدوياً"
                            >
                              <span className="font-bold text-gray-900 dark:text-gray-100 text-sm min-w-[1.5rem] text-center block">{item.cartQuantity}</span>
                            </button>

                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                updateCartItemQuantity(item.id, item.cartQuantity + 1);
                              }}
                              className="text-gray-600 dark:text-gray-400 hover:bg-green-50 dark:hover:bg-green-900/30 hover:text-green-600 dark:hover:text-green-400 h-7 w-7 flex items-center justify-center transition-all duration-200"
                            >
                              <FaPlus className="text-xs" />
                            </button>
                          </div>
                        </div>

                        {/* Remove Button */}
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            removeFromCart(item.id);
                          }}
                          className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/40 hover:text-red-700 dark:hover:text-red-300 px-2 py-1 rounded-lg transition-all duration-200 flex items-center gap-1"
                        >
                          <FaTrash className="text-xs" />
                          <span className="text-xs font-medium">حذف</span>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="h-full flex flex-col justify-center items-center py-12">
                <div className="bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 p-12 rounded-2xl mb-8 shadow-inner">
                  <FaShoppingCart className="text-6xl text-gray-400 dark:text-gray-500" />
                </div>
                <h3 className="text-xl mb-3 font-bold text-gray-900 dark:text-gray-100">
                  السلة فارغة
                </h3>
                <p className="text-gray-500 dark:text-gray-400 text-center max-w-sm">
                  قم بإضافة منتجات من القائمة لبدء عملية البيع. يمكنك البحث عن المنتجات أو تصفح الفئات المختلفة.
                </p>
              </div>
            )}
          </div>

          {/* Cart Footer */}
          <div className="p-4 md:p-5 border-t border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 sticky bottom-0 left-0 right-0 z-10">
            {/* Customer Info Section */}
            {selectedCustomer && (
              <div className="bg-white dark:bg-gray-800 rounded-lg p-3 mb-3 border border-gray-200 dark:border-gray-700 shadow-sm">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="bg-blue-50 dark:bg-blue-900/30 p-1.5 rounded-lg ml-2">
                      <FaUser className="text-blue-600 dark:text-blue-400 text-sm" />
                    </div>
                    <div>
                      <div className="text-sm text-gray-600 dark:text-gray-400 font-medium">العميل المحدد</div>
                      <div className="font-bold text-gray-900 dark:text-gray-100 text-sm">{selectedCustomer.name}</div>
                    </div>
                  </div>
                  <div className="text-left">
                    {selectedCustomer.total_debt > 0 && (
                      <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700 px-2 py-1 rounded-lg">
                        <div className="text-red-700 dark:text-red-300 font-bold text-xs">
                          دين: <FormattedCurrency amount={selectedCustomer.total_debt} />
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Total Section */}
            <div className="bg-white dark:bg-gray-800 rounded-lg p-3 mb-3 border border-gray-200 dark:border-gray-700 shadow-sm">
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <div className="bg-primary-50 dark:bg-primary-900/30 p-1.5 rounded-lg ml-2">
                    <FaShoppingCart className="text-primary-600 dark:text-primary-400 text-sm" />
                  </div>
                  <div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 font-medium">إجمالي السلة</div>
                    <div className="text-xs text-gray-500 dark:text-gray-500">{cart.length} عنصر</div>
                  </div>
                </div>
                <div className="text-left">
                  <div className="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700 px-3 py-1.5 rounded-lg">
                    <div className="text-green-700 dark:text-green-200 font-bold text-lg">
                      <FormattedCurrency amount={cartTotal} />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Button */}
            <button
              onClick={editingInvoice ? () => setShowPayment(true) : proceedToPayment}
              disabled={cart.length === 0 || isLoading}
              className={`w-full py-3 text-base rounded-lg font-bold transition-all duration-300 flex items-center justify-center gap-2 ${
                cart.length === 0 || isLoading
                  ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                  : editingInvoice
                    ? 'bg-gradient-to-r from-orange-400 to-orange-500 hover:from-orange-500 hover:to-orange-600 dark:from-orange-500 dark:to-orange-600 dark:hover:from-orange-600 dark:hover:to-orange-700 text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]'
                    : 'bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 dark:from-primary-600 dark:to-primary-700 dark:hover:from-primary-700 dark:hover:to-primary-800 text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]'
              }`}
            >
              {isLoading ? (
                <>
                  <div className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full"></div>
                  <span>جاري المعالجة...</span>
                </>
              ) : editingInvoice ? (
                <>
                  <FaEdit className="text-lg" />
                  <span>تحديث الفاتورة</span>
                </>
              ) : (
                <>
                  <FaMoneyBill className="text-lg" />
                  <span>إتمام الدفع</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Payment Modal */}
      <Modal
        isOpen={showPayment && !saleCompleted}
        onClose={() => setShowPayment(false)}
        title="إتمام الدفع"
        size="lg"
        zIndex="highest"
      >
        <div className="space-y-4">
          {/* Order Summary - Compact */}
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 border border-gray-100 dark:border-gray-600">
            <div className="space-y-1.5 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">إجمالي العناصر:</span>
                <span className="font-medium text-gray-900 dark:text-gray-100">{cartItemCount}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">المجموع الفرعي:</span>
                <span className="font-medium text-gray-900 dark:text-gray-100">
                  <FormattedCurrency amount={cartSubtotal} />
                </span>
              </div>
              {discountValue > 0 && (
                <div className="flex justify-between text-success-600 dark:text-success-400">
                  <span>الخصم:</span>
                  <span>- <FormattedCurrency amount={discountValue} /></span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">المجموع بعد الخصم:</span>
                <span className="font-medium text-gray-900 dark:text-gray-100">
                  <FormattedCurrency amount={subtotalAfterDiscount} />
                </span>
              </div>
              <div className="flex justify-between text-warning-600 dark:text-warning-400">
                <span>الضريبة ({taxRate.toString()}%):</span>
                <span>{taxValue > 0 ? '+' : ''} <FormattedCurrency amount={taxValue} /></span>
              </div>
              <div className="flex justify-between font-bold text-base pt-1.5 border-t border-gray-200 dark:border-gray-600 mt-1.5">
                <span className="text-gray-900 dark:text-gray-100">المبلغ الإجمالي:</span>
                <span className="text-primary-600 dark:text-primary-400">
                  <FormattedCurrency amount={cartTotal} />
                </span>
              </div>
            </div>
          </div>

          {/* Customer Selection */}
          <div>
            <CustomerSelector
              ref={customerSelectorRef}
              selectedCustomer={selectedCustomer}
              onCustomerSelect={setSelectedCustomer}
              onAddCustomer={handleAddCustomer}
            />
          </div>

          {/* Payment Type Selection */}
          <div>
            <PaymentOptions
              paymentType={paymentType}
              onPaymentTypeChange={setPaymentType}
              totalAmount={cartTotal}
              hasCustomer={!!selectedCustomer}
            />
          </div>

          {/* Discount Section */}
          <div>
            <label className="block mb-2 text-sm font-medium text-gray-900 dark:text-gray-100">الخصم</label>
            <div className="grid grid-cols-2 gap-3 mb-3">
              <button
                onClick={() => {
                  setDiscountType('fixed');
                  // Amount paid will be updated automatically by useEffect
                }}
                className={`flex items-center justify-center gap-2 p-2.5 rounded-lg border-2 transition-all duration-300 text-sm ${
                  discountType === 'fixed'
                    ? 'border-success-500 bg-success-50 dark:bg-success-900/30 text-success-700 dark:text-success-300 shadow-sm'
                    : 'border-gray-200 dark:border-gray-600 hover:border-success-300 dark:hover:border-success-600 hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                <span className="font-medium">مبلغ ثابت</span>
              </button>
              <button
                onClick={() => {
                  setDiscountType('percentage');
                  // Amount paid will be updated automatically by useEffect
                }}
                className={`flex items-center justify-center gap-2 p-2.5 rounded-lg border-2 transition-all duration-300 text-sm ${
                  discountType === 'percentage'
                    ? 'border-success-500 bg-success-50 dark:bg-success-900/30 text-success-700 dark:text-success-300 shadow-sm'
                    : 'border-gray-200 dark:border-gray-600 hover:border-success-300 dark:hover:border-success-600 hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                <span className="font-medium">نسبة مئوية %</span>
              </button>
            </div>

            <NumberInput
              name="discountAmount"
              value={discountAmount}
              onChange={(value) => {
                const numValue = typeof value === 'string' ? parseFloat(value) : value;
                // Validate percentage (0-100) or fixed amount (not greater than subtotal)
                if (discountType === 'percentage') {
                  if (numValue <= 100) {
                    setDiscountAmount(numValue.toString());
                  }
                } else {
                  if (numValue <= cartSubtotal) {
                    setDiscountAmount(numValue.toString());
                  }
                }
              }}
              min={0}
              max={discountType === 'percentage' ? 100 : cartSubtotal}
              step={0.01}
              precision={decimalPlaces}
              placeholder={discountType === 'percentage' ? "نسبة الخصم" : "مبلغ الخصم"}
              currency={discountType === 'percentage' ? undefined : currencySymbol}
              showControls={true}
              icon={discountType === 'percentage' ? <span className="text-lg">%</span> : <FaMoneyBillWave />}
            />
          </div>



          {/* Payment Method Section */}
          <div>
            <label className="block mb-2 text-sm font-medium text-gray-900 dark:text-gray-100">طريقة الدفع</label>
            {paymentType === 'credit' ? (
              // For credit payment, show disabled "آجل" option
              <div className="p-3 rounded-lg border-2 border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-700 text-center">
                <div className="flex items-center justify-center gap-2 text-gray-600 dark:text-gray-400">
                  <FaClock className="text-lg" />
                  <span className="font-medium">آجل</span>
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  طريقة الدفع محددة تلقائياً للبيع الآجل
                </div>
              </div>
            ) : (
              // For other payment types, show normal options
              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={() => setPaymentMethod('cash')}
                  className={`flex items-center justify-center gap-2 p-3 rounded-lg border-2 transition-all duration-300 text-sm ${
                    paymentMethod === 'cash'
                      ? 'border-primary-500 dark:border-primary-400 bg-primary-50 dark:bg-primary-900/40 text-primary-600 dark:text-primary-300 shadow-sm'
                      : 'border-gray-200 dark:border-gray-600 hover:border-primary-300 dark:hover:border-primary-500 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  <FaMoneyBill className="text-lg" />
                  <span className="font-medium">نقداً</span>
                </button>
                <button
                  onClick={() => setPaymentMethod('card')}
                  className={`flex items-center justify-center gap-2 p-3 rounded-lg border-2 transition-all duration-300 text-sm ${
                    paymentMethod === 'card'
                      ? 'border-primary-500 dark:border-primary-400 bg-primary-50 dark:bg-primary-900/40 text-primary-600 dark:text-primary-300 shadow-sm'
                      : 'border-gray-200 dark:border-gray-600 hover:border-primary-300 dark:hover:border-primary-500 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  <FaCreditCard className="text-lg" />
                  <span className="font-medium">بطاقة</span>
                </button>
              </div>
            )}
          </div>

          {/* Amount Paid Section */}
          <div>
            <NumberInput
              name="amountPaid"
              label={
                paymentType === 'full'
                  ? "المبلغ المستلم"
                  : paymentType === 'partial'
                    ? "المبلغ المدفوع (جزئي)"
                    : "المبلغ المدفوع (اختياري)"
              }
              value={paymentType === 'credit' ? '0' : amountPaid}
              onChange={(value) => {
                if (paymentType !== 'credit') {
                  setAmountPaid(value.toString());
                }
              }}
              min={paymentType === 'credit' ? 0 : paymentType === 'partial' ? 0.01 : cartTotal}
              max={paymentType === 'partial' ? cartTotal - 0.01 : undefined}
              step={0.01}
              precision={decimalPlaces}
              placeholder={
                paymentType === 'full'
                  ? "أدخل المبلغ المستلم"
                  : paymentType === 'partial'
                    ? "أدخل المبلغ المدفوع"
                    : paymentType === 'credit'
                      ? "لا يوجد دفع مقدم للبيع الآجل"
                      : "أدخل المبلغ (اختياري)"
              }
              currency={currencySymbol}
              showControls={paymentType !== 'credit'}
              disabled={paymentType === 'credit'}
              icon={paymentType === 'credit' ? <FaClock /> : <FaMoneyBill />}
            />

            {paymentType === 'full' && paymentMethod === 'cash' && (
              <div className="flex justify-between bg-success-50 dark:bg-success-900/30 p-2.5 rounded-lg text-success-900 dark:text-success-300 border border-success-100 dark:border-success-800 mt-2 text-sm">
                <span className="font-medium">المتبقي:</span>
                <span className="font-bold">
                  <FormattedCurrency amount={changeAmount} />
                </span>
              </div>
            )}

            {paymentType === 'partial' && (
              <div className="flex justify-between bg-orange-50 dark:bg-orange-900/30 p-2.5 rounded-lg text-orange-900 dark:text-orange-300 border border-orange-100 dark:border-orange-800 mt-2 text-sm">
                <span className="font-medium">المبلغ المتبقي (دين):</span>
                <span className="font-bold">
                  <FormattedCurrency amount={cartTotal - parseFloat(amountPaid || '0')} />
                </span>
              </div>
            )}

            {paymentType === 'credit' && (
              <div className="flex justify-between bg-red-50 dark:bg-red-900/30 p-2.5 rounded-lg text-red-900 dark:text-red-300 border border-red-100 dark:border-red-800 mt-2 text-sm">
                <span className="font-medium">إجمالي الدين:</span>
                <span className="font-bold">
                  <FormattedCurrency amount={cartTotal} />
                </span>
              </div>
            )}
          </div>

          {/* Action Button */}
          <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={editingInvoice ? updateInvoice : handlePaymentSubmit}
              disabled={isLoading}
              className={`w-full py-3 text-base rounded-lg transition-all duration-300 font-medium ${
                isLoading
                  ? 'bg-primary-400 dark:bg-primary-500 cursor-not-allowed'
                  : editingInvoice
                    ? 'bg-orange-500 hover:bg-orange-600 active:bg-orange-700 dark:bg-orange-600 dark:hover:bg-orange-700 dark:active:bg-orange-800 shadow-sm hover:shadow-md'
                    : 'bg-primary-500 hover:bg-primary-600 active:bg-primary-700 dark:bg-primary-600 dark:hover:bg-primary-700 dark:active:bg-primary-800 shadow-sm hover:shadow-md'
              } text-white`}
            >
              <div className="flex items-center justify-center">
                {isLoading ? (
                  <>
                    <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full ml-2"></div>
                    <span>{editingInvoice ? 'جاري تحديث الفاتورة...' : 'جاري تسجيل البيع...'}</span>
                  </>
                ) : (
                  <>
                    {editingInvoice ? <FaEdit className="ml-2 text-sm" /> : <FaCheck className="ml-2 text-sm" />}
                    <span>{editingInvoice ? 'تحديث الفاتورة' : 'تأكيد الدفع'}</span>
                  </>
                )}
              </div>
            </button>
          </div>
        </div>
      </Modal>

      {/* Sale Completed Modal */}
      <Modal
        isOpen={saleCompleted}
        onClose={() => {}}
        title=""
        size="md"
        zIndex="highest"
      >
        <div className="text-center">
          {/* Success Icon */}
          <div className="mx-auto mb-3 w-12 h-12 bg-success-100 dark:bg-success-900/30 rounded-full flex items-center justify-center">
            <FaCheck className="text-xl text-success-600 dark:text-success-400" />
          </div>

          {/* Title */}
          <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">
            تمت العملية بنجاح
          </h3>

          {/* Sale Details */}
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 mb-3">
            <div className="text-xs text-gray-600 dark:text-gray-400 mb-1">
              رقم الفاتورة
            </div>
            <div className="text-base font-bold text-primary-600 dark:text-primary-400">
              #{saleId}
            </div>
            {selectedCustomer && paymentType !== 'full' && (
              <div className="mt-1 text-xs text-gray-600 dark:text-gray-400">
                {paymentType === 'partial' ? 'دفع جزئي' : 'بيع آجل'} - {selectedCustomer.name}
              </div>
            )}
          </div>

          {/* Payment Info */}
          {paymentType === 'full' && paymentMethod === 'cash' && changeAmount > 0 && (
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-2.5 mb-3">
              <div className="flex justify-between items-center text-xs">
                <span className="text-blue-700 dark:text-blue-300">الباقي للعميل:</span>
                <span className="font-bold text-blue-800 dark:text-blue-200">
                  <FormattedCurrency amount={changeAmount} />
                </span>
              </div>
            </div>
          )}

          {/* Debt Info */}
          {paymentType !== 'full' && selectedCustomer && (
            <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-2.5 mb-3">
              <div className="flex justify-between items-center text-xs">
                <span className="text-orange-700 dark:text-orange-300">المبلغ المتبقي:</span>
                <span className="font-bold text-orange-800 dark:text-orange-200">
                  <FormattedCurrency amount={cartTotal - parseFloat(amountPaid || '0')} />
                </span>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-2">
            <button
              onClick={handlePrintReceipt}
              className="w-full py-2.5 flex items-center justify-center gap-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 font-medium rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200 text-sm"
            >
              <FaPrint className="text-xs" />
              <span>طباعة الفاتورة</span>
            </button>
            <button
              onClick={handleNewSale}
              className="w-full py-2.5 flex items-center justify-center gap-2 bg-primary-500 hover:bg-primary-600 text-white font-medium rounded-lg transition-all duration-200 shadow-sm hover:shadow-md text-sm"
            >
              <FaPlus className="text-xs" />
              <span>عملية جديدة</span>
            </button>
          </div>
        </div>
      </Modal>

      {/* Quantity Modal */}
      <Modal
        isOpen={showQuantityModal}
        onClose={handleQuantityModalCancel}
        title="تعديل الكمية"
        size="sm"
        zIndex="highest"
      >
        <div className="space-y-4">
          {selectedCartItem && (
            <>
              {/* Product Info */}
              <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600">
                <div className="flex items-center gap-3">
                  <div className="bg-primary-100 dark:bg-primary-900/30 p-2 rounded-lg">
                    <FaBox className="text-primary-600 dark:text-primary-400" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100">
                      {selectedCartItem.name}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      السعر: <FormattedCurrency amount={selectedCartItem.price} />
                    </p>
                  </div>
                </div>
              </div>

              {/* Available Quantity Info */}
              <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-3 border border-blue-100 dark:border-blue-800">
                <div className="flex items-center justify-between">
                  <span className="text-blue-700 dark:text-blue-300 font-medium">الكمية المتاحة:</span>
                  <span className="text-blue-800 dark:text-blue-200 font-bold">
                    {selectedCartItem.quantity} قطعة
                  </span>
                </div>
              </div>

              {/* Current Quantity */}
              <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 border border-gray-100 dark:border-gray-600">
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium">الكمية الحالية:</span>
                  <span className="text-gray-800 dark:text-gray-200 font-bold">
                    {selectedCartItem.cartQuantity} قطعة
                  </span>
                </div>
              </div>

              {/* New Quantity Input */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  الكمية الجديدة
                </label>
                <input
                  type="number"
                  min="1"
                  max={selectedCartItem.quantity}
                  value={newQuantity}
                  onChange={(e) => setNewQuantity(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleQuantityModalSubmit();
                    } else if (e.key === 'Escape') {
                      handleQuantityModalCancel();
                    }
                  }}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-center text-lg font-bold focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="أدخل الكمية"
                  autoFocus
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 text-center">
                  الحد الأقصى: {selectedCartItem.quantity} قطعة
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-2">
                <button
                  onClick={handleQuantityModalCancel}
                  className="flex-1 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium"
                >
                  إلغاء
                </button>
                <button
                  onClick={handleQuantityModalSubmit}
                  className="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl"
                >
                  تأكيد
                </button>
              </div>
            </>
          )}
        </div>
      </Modal>

      {/* Previous Invoices Modal */}
      {showPreviousInvoices && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex justify-between items-center mb-4">
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
                    <FaHistory className="ml-3 text-primary-600 dark:text-primary-400" />
                    الفواتير السابقة
                  </h3>
                  {user?.role === 'cashier' && (
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      عرض فواتيرك الخاصة فقط
                    </p>
                  )}
                  {user?.role === 'admin' && (
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      عرض جميع الفواتير
                    </p>
                  )}
                </div>
                <button
                  onClick={() => setShowPreviousInvoices(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  <FaTimes className="text-xl" />
                </button>
              </div>

              {/* Search Section */}
              <div className="flex flex-col sm:flex-row gap-3">
                <div className="flex-1">
                  <div className="relative">
                    <input
                      type="text"
                      value={invoiceSearchTerm}
                      onChange={(e) => setInvoiceSearchTerm(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && handleSearchInvoices()}
                      placeholder="ابحث برقم الفاتورة أو اسم العميل..."
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                    <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" />
                  </div>
                </div>

                <div className="flex gap-2">
                  <button
                    onClick={handleSearchInvoices}
                    className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium min-w-[140px] focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl gap-2"
                  >
                    <FaSearch className="text-sm" />
                    بحث
                  </button>

                  {invoiceSearchTerm && (
                    <button
                      onClick={handleClearSearch}
                      className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-500 hover:border-gray-600 flex items-center justify-center text-sm font-medium min-w-[140px] focus:outline-none focus:ring-4 focus:ring-gray-500/20 shadow-lg hover:shadow-xl gap-2"
                    >
                      <FaTimes className="text-sm" />
                      مسح
                    </button>
                  )}
                </div>
              </div>
            </div>

            <div
              className="p-6 overflow-y-auto max-h-[calc(90vh-120px)] modal-scrollbar"
              onScroll={(e) => {
                const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
                // If scrolled to bottom (with a threshold of 100px)
                if (scrollHeight - scrollTop - clientHeight < 100) {
                  loadMoreInvoices();
                }
              }}
            >
              {isLoadingInvoices && previousInvoices.length === 0 ? (
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin h-8 w-8 border-2 border-primary-500 border-t-transparent rounded-full ml-3"></div>
                  <span className="text-gray-600 dark:text-gray-400">جاري تحميل الفواتير...</span>
                </div>
              ) : previousInvoices.length === 0 ? (
                <div className="text-center py-12">
                  <FaReceipt className="text-4xl text-gray-400 dark:text-gray-500 mx-auto mb-4" />
                  <p className="text-gray-600 dark:text-gray-400 text-lg">لا توجد فواتير سابقة</p>
                  <p className="text-gray-500 dark:text-gray-500 text-sm mt-2">ستظهر الفواتير هنا بعد إتمام عمليات البيع</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {previousInvoices.map((invoice) => (
                    <div
                      key={invoice.id}
                      className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600 hover:shadow-md transition-all duration-200"
                    >
                      <div className="flex justify-between items-start mb-3">
                        <div className="flex items-center">
                          <div className="bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-300 p-2 rounded-lg ml-3">
                            <FaReceipt className="text-lg" />
                          </div>
                          <div>
                            <h4 className="font-semibold text-gray-900 dark:text-white">
                              فاتورة رقم #{invoice.id}
                            </h4>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              <FormattedDate date={invoice.created_at} /> - <FormattedTime date={invoice.created_at} />
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-bold text-lg text-gray-900 dark:text-white">
                            <FormattedCurrency amount={(invoice.total_amount || 0) - (invoice.discount_amount || 0) + (invoice.tax_amount || 0)} />
                          </p>
                          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                            invoice.payment_status === 'paid'
                              ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                              : invoice.payment_status === 'partial'
                              ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300'
                              : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                          }`}>
                            {invoice.payment_status === 'paid' ? 'مدفوع' :
                             invoice.payment_status === 'partial' ? 'مدفوع جزئياً' : 'غير مدفوع'}
                          </span>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 text-sm">
                        <div>
                          <span className="text-gray-500 dark:text-gray-400">العميل:</span>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {invoice.customer_name || 'عميل مباشر'}
                          </p>
                        </div>
                        <div>
                          <span className="text-gray-500 dark:text-gray-400">طريقة الدفع:</span>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {invoice.payment_method || 'نقدي'}
                          </p>
                        </div>
                        <div>
                          <span className="text-gray-500 dark:text-gray-400">العناصر:</span>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {invoice.items?.length || 0} عنصر
                          </p>
                        </div>
                        <div>
                          <span className="text-gray-500 dark:text-gray-400">المبلغ المدفوع:</span>
                          <p className="font-medium text-gray-900 dark:text-white">
                            <FormattedCurrency amount={invoice.amount_paid || 0} />
                          </p>
                        </div>
                      </div>

                      <div className="flex justify-end gap-2">
                        <button
                          onClick={() => handleEditInvoice(invoice)}
                          className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-orange-500 hover:border-orange-600 flex items-center justify-center text-sm font-medium min-w-[120px] focus:outline-none focus:ring-4 focus:ring-orange-500/20 shadow-lg hover:shadow-xl gap-2"
                        >
                          <FaEdit className="text-xs" />
                          تعديل
                        </button>
                        <button
                          onClick={() => navigate(`/sales/${invoice.id}/print`)}
                          className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-500 hover:border-gray-600 flex items-center justify-center text-sm font-medium min-w-[120px] focus:outline-none focus:ring-4 focus:ring-gray-500/20 shadow-lg hover:shadow-xl gap-2"
                        >
                          <FaPrint className="text-xs" />
                          طباعة
                        </button>
                      </div>
                    </div>
                  ))}

                  {/* Loading more indicator */}
                  {isLoadingMoreInvoices && (
                    <div className="flex items-center justify-center py-6">
                      <div className="animate-spin h-6 w-6 border-2 border-primary-500 border-t-transparent rounded-full ml-2"></div>
                      <span className="text-gray-600 dark:text-gray-400 text-sm">جاري تحميل المزيد...</span>
                    </div>
                  )}

                  {/* End of list indicator */}
                  {!hasMoreInvoices && previousInvoices.length > 0 && (
                    <div className="text-center py-4">
                      <p className="text-gray-500 dark:text-gray-400 text-sm">تم عرض جميع الفواتير</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Add Customer Modal */}
      <Modal
        isOpen={showAddCustomer}
        onClose={handleCancelAddCustomer}
        title="إضافة عميل جديد"
        size="md"
        zIndex="highest"
      >
        <div className="space-y-4">
          {/* Customer Name */}
          <TextInput
            name="customerName"
            label="اسم العميل"
            value={newCustomerName}
            onChange={setNewCustomerName}
            placeholder="أدخل اسم العميل"
            required
            autoFocus
            icon={<FaUser />}
          />

          {/* Customer Phone */}
          <TextInput
            name="customerPhone"
            label="رقم الهاتف"
            type="tel"
            value={newCustomerPhone}
            onChange={setNewCustomerPhone}
            placeholder="أدخل رقم الهاتف (اختياري)"
            icon={<FaPhone />}
          />

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <button
              onClick={handleCancelAddCustomer}
              className="flex-1 py-2.5 px-4 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200 font-medium text-sm"
            >
              إلغاء
            </button>
            <button
              onClick={handleAddCustomerSubmit}
              disabled={isAddingCustomer || !newCustomerName.trim()}
              className={`flex-1 py-2.5 px-4 rounded-lg font-medium text-sm transition-all duration-200 ${
                isAddingCustomer || !newCustomerName.trim()
                  ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                  : 'bg-primary-500 hover:bg-primary-600 text-white shadow-sm hover:shadow-md'
              }`}
            >
              {isAddingCustomer ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full ml-2"></div>
                  <span>جاري الإضافة...</span>
                </div>
              ) : (
                'إضافة العميل'
              )}
            </button>
          </div>
        </div>
      </Modal>

      {/* Barcode Scanner Modal - Simplified */}
      {/* Scanner functionality removed for simplicity */}

      {/* Scroll to Top Button */}
      <ScrollToTopButton />

      {/* Chat Notifications */}
      {user && (
        <ChatNotificationManager
          onOpenChat={(senderId) => {
            console.log('🔔 POS: طلب فتح محادثة مع المستخدم:', senderId);
            // إرسال حدث لفتح المحادثة مع المستخدم المحدد
            const event = new CustomEvent('openChatWithUser', {
              detail: { userId: senderId }
            });
            window.dispatchEvent(event);
          }}
        />
      )}
    </div>
  );
};

export default POS;