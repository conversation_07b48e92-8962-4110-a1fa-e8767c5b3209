import React, { useState } from 'react';
import {
  FaCashRegister,
  FaBoxOpen,
  FaShoppingCart,
  FaUsers,
  FaFileInvoiceDollar,
  FaChartBar,
  FaChartLine,
  FaCog,
  FaUserShield,
  FaImage,
  FaQuestionCircle,
  FaBook,
  FaPlayCircle,
  FaLightbulb,
  FaCheckCircle,
  FaInfoCircle,
  FaMousePointer,
  FaPrint,
  FaCalculator,
  FaEye,
  FaEdit,
  FaPlus,
  FaEnvelope,
  FaFilter,
  FaCalendarAlt,
  FaExclamationTriangle,
  FaPaperPlane,
  FaBell,
  FaTimes,
  FaCode,
  FaTools,
  FaRocket,
  FaShieldAlt
} from 'react-icons/fa';
import SupportEmailModal from '../components/SupportEmailModal';

// استيراد الصور التوضيحية - الوضع المضيء
import DashboardImg from '../Img/Dashboard.png';
import POSImg from '../Img/POS.png';
import ProductsImg from '../Img/products.png';
import SalesImg from '../Img/sales.png';
import CustomersImg from '../Img/customers.png';
import DebtsImg from '../Img/debts.png';
import ReportImg from '../Img/Report.png';
import SettingsImg from '../Img/settings.png';
import UsersImg from '../Img/users.png';

// استيراد الصور التوضيحية - الوضع المظلم
import DashboardImgDark from '../Img/Dashboard_Dark.png';
import POSImgDark from '../Img/POS_Dark.png';
import ProductsImgDark from '../Img/products_Dark.png';
import SalesImgDark from '../Img/sales_Dark.png';
import CustomersImgDark from '../Img/customers_Dark.png';
import DebtsImgDark from '../Img/debts_Dark.png';
import ReportImgDark from '../Img/Report_Dark.png';
import SettingsImgDark from '../Img/settings_Dark.png';
import UsersImgDark from '../Img/users_Dark.png';

// مكون لعرض الصور مع دعم الوضع المظلم
interface ResponsiveImageProps {
  lightSrc: string;
  darkSrc: string;
  alt: string;
  title: string;
  description: string;
  iconColor: string;
}

const ResponsiveImage: React.FC<ResponsiveImageProps> = ({
  lightSrc,
  darkSrc,
  alt,
  title,
  description,
  iconColor
}) => {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 flex items-center gap-2">
          <FaImage className={iconColor} />
          {title}
        </h4>
        <p className="text-sm text-secondary-600 dark:text-secondary-400 mt-1">
          {description}
        </p>
      </div>
      <div className="p-4">
        {/* صورة للوضع المضيء */}
        <img
          src={lightSrc}
          alt={alt}
          className="w-full h-auto rounded-lg border border-gray-200 dark:border-gray-600 shadow-sm hover:shadow-md transition-shadow duration-200 block dark:hidden"
        />
        {/* صورة للوضع المظلم */}
        <img
          src={darkSrc}
          alt={alt}
          className="w-full h-auto rounded-lg border border-gray-200 dark:border-gray-600 shadow-sm hover:shadow-md transition-shadow duration-200 hidden dark:block"
        />
      </div>
    </div>
  );
};

// مكون التبويبات الداخلية لإدارة المنتجات
const ProductsTabContent: React.FC = () => {
  const [activeProductTab, setActiveProductTab] = useState('overview');

  // التبويبات الداخلية لإدارة المنتجات
  const productTabs = [
    {
      id: 'overview',
      title: 'نظرة عامة',
      icon: <FaInfoCircle />,
      color: 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-300'
    },
    {
      id: 'filters-cards',
      title: 'الفلاتر والبطاقات',
      icon: <FaFilter />,
      color: 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-300'
    },
    {
      id: 'sales-analysis',
      title: 'تحليل المبيعات',
      icon: <FaChartBar />,
      color: 'bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-300'
    },
    {
      id: 'risk-management',
      title: 'إدارة المخاطر',
      icon: <FaExclamationTriangle />,
      color: 'bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-300'
    },
    {
      id: 'performance-analysis',
      title: 'تحليل الأداء',
      icon: <FaChartBar />,
      color: 'bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-300'
    },
    {
      id: 'tools-examples',
      title: 'أدوات وأمثلة',
      icon: <FaCog />,
      color: 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
    },
    {
      id: 'faq',
      title: 'أسئلة شائعة',
      icon: <FaQuestionCircle />,
      color: 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-300'
    }
  ];

  const getProductTabContent = (tabId: string) => {
    switch (tabId) {
      case 'overview':
        return (
          <div className="space-y-6">
            <div className="bg-secondary-50 dark:bg-secondary-900/20 p-6 rounded-lg border border-secondary-200 dark:border-secondary-700">
              <h3 className="text-xl font-semibold text-secondary-800 dark:text-secondary-200 mb-3 flex items-center gap-2">
                <FaBoxOpen />
                إدارة المنتجات - أساس المتجر
              </h3>
              <p className="text-secondary-700 dark:text-secondary-300 leading-relaxed">
                قسم المنتجات هو المكان الذي تدير فيه جميع السلع والخدمات التي تبيعها. يمكنك إضافة منتجات جديدة،
                تعديل الأسعار، تتبع الكميات المتاحة، وتنظيم المنتجات في فئات لسهولة الوصول إليها.
              </p>
            </div>

            {/* دليل تحليل المنتجات الجديد */}
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-6 rounded-lg border border-blue-200 dark:border-blue-700">
              <h3 className="text-xl font-semibold text-blue-800 dark:text-blue-200 mb-4 flex items-center gap-2">
                <FaChartBar className="text-blue-600" />
                ميزة جديدة: تحليل المنتجات الذكي
              </h3>
              <p className="text-blue-700 dark:text-blue-300 leading-relaxed mb-4">
                تم إضافة نظام تحليل متقدم للمنتجات يساعدك في اتخاذ قرارات ذكية لإدارة المتجر وزيادة الأرباح.
                هذا النظام يحلل أداء منتجاتك ويقدم توصيات مفيدة لتحسين المبيعات وإدارة المخزون بكفاءة.
              </p>

              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-blue-200 dark:border-blue-600 mb-4">
                <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2 flex items-center gap-2">
                  <FaMousePointer className="text-blue-600" />
                  كيفية الوصول لتحليل المنتجات
                </h4>
                <ol className="space-y-2 text-gray-700 dark:text-gray-300 text-sm">
                  <li className="flex items-start gap-2">
                    <span className="text-blue-600 font-semibold">1.</span>
                    <span>اذهب إلى صفحة "المنتجات" من الصفحة الرئيسية</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-blue-600 font-semibold">2.</span>
                    <span>ستجد تبويبتين في أعلى الصفحة: "قائمة المنتجات" و "تحليلات المنتجات"</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-blue-600 font-semibold">3.</span>
                    <span>اضغط على تبويبة "تحليلات المنتجات" للدخول إلى النظام الجديد</span>
                  </li>
                </ol>
              </div>
            </div>

            {/* صورة توضيحية لصفحة المنتجات */}
            <ResponsiveImage
              lightSrc={ProductsImg}
              darkSrc={ProductsImgDark}
              alt="صفحة إدارة المنتجات"
              title="صورة توضيحية - صفحة إدارة المنتجات"
              description="لقطة شاشة من واجهة إدارة المنتجات وتحليلاتها"
              iconColor="text-blue-600"
            />
          </div>
        );

      case 'filters-cards':
        return (
          <div className="space-y-6">
            {/* شرح الفلاتر */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-4 flex items-center gap-2">
                <FaFilter className="text-secondary-600" />
                الفلاتر الذكية - اختر ما تريد تحليله
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-700">
                  <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center gap-2">
                    <FaCalendarAlt className="text-blue-600" />
                    فترة التحليل الديناميكية
                  </h5>
                  <p className="text-blue-700 dark:text-blue-300 text-sm mb-2">
                    النظام يعرض الفترات المتاحة فعلياً بناءً على بيانات المبيعات في متجرك:
                  </p>
                  <ul className="space-y-1 text-blue-600 dark:text-blue-400 text-xs">
                    <li>• <strong>فترات ذكية:</strong> يتم حساب الفترات المتاحة تلقائياً</li>
                    <li>• <strong>بيانات حقيقية:</strong> فقط الفترات التي تحتوي على مبيعات</li>
                    <li>• <strong>تحديث مستمر:</strong> تتحدث الفترات مع كل عملية بيع جديدة</li>
                    <li>• <strong>مرونة كاملة:</strong> من أسبوع واحد إلى سنة كاملة</li>
                    <li>• <strong>تفاصيل شاملة:</strong> عدد المبيعات والمنتجات لكل فترة</li>
                  </ul>

                  <div className="mt-3 p-3 bg-blue-100 dark:bg-blue-800/30 rounded-lg border border-blue-300 dark:border-blue-600">
                    <h6 className="font-medium text-blue-800 dark:text-blue-200 text-xs mb-1">كيف يعمل النظام الجديد:</h6>
                    <p className="text-blue-700 dark:text-blue-300 text-xs">
                      عند فتح صفحة التحليل، يقوم النظام بفحص قاعدة البيانات ويعرض فقط الفترات التي تحتوي على مبيعات فعلية.
                      هذا يضمن أن التحليل دقيق ومفيد دائماً.
                    </p>
                  </div>
                </div>

                <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-700">
                  <h5 className="font-medium text-green-800 dark:text-green-200 mb-2 flex items-center gap-2">
                    <FaBoxOpen className="text-green-600" />
                    فئة المنتجات
                  </h5>
                  <p className="text-green-700 dark:text-green-300 text-sm mb-2">
                    ركز التحليل على فئة معينة من المنتجات:
                  </p>
                  <ul className="space-y-1 text-green-600 dark:text-green-400 text-xs">
                    <li>• جميع الفئات - تحليل شامل</li>
                    <li>• مشروبات - تحليل المشروبات فقط</li>
                    <li>• حلويات - تحليل الحلويات فقط</li>
                    <li>• أي فئة أخرى في متجرك</li>
                  </ul>
                </div>

                <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg border border-orange-200 dark:border-orange-700">
                  <h5 className="font-medium text-orange-800 dark:text-orange-200 mb-2 flex items-center gap-2">
                    <FaExclamationTriangle className="text-orange-600" />
                    مستوى المخاطر
                  </h5>
                  <p className="text-orange-700 dark:text-orange-300 text-sm mb-2">
                    ركز على المنتجات حسب مستوى المخاطر:
                  </p>
                  <ul className="space-y-1 text-orange-600 dark:text-orange-400 text-xs">
                    <li>• جميع المستويات</li>
                    <li>• مخاطر عالية - تحتاج تدخل فوري</li>
                    <li>• مخاطر متوسطة - تحتاج مراقبة</li>
                    <li>• مخاطر منخفضة - وضع آمن</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* شرح النظام الديناميكي الجديد */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-4 flex items-center gap-2">
                <FaChartLine className="text-primary-600" />
                النظام الديناميكي الجديد - ثورة في تحليل البيانات
              </h4>

              <p className="text-secondary-600 dark:text-secondary-400 mb-4">
                تم تطوير نظام ذكي جديد لاختيار فترات التحليل يعتمد على البيانات الفعلية في متجرك، مما يضمن دقة وفائدة أكبر للتحليلات:
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg border border-green-200 dark:border-green-700">
                    <h5 className="font-medium text-green-800 dark:text-green-200 mb-1 flex items-center gap-2">
                      <FaCheckCircle className="text-green-600" />
                      المزايا الجديدة
                    </h5>
                    <ul className="space-y-1 text-green-700 dark:text-green-300 text-xs">
                      <li>• فترات مبنية على بيانات حقيقية</li>
                      <li>• عرض عدد المبيعات لكل فترة</li>
                      <li>• تحديث تلقائي مع المبيعات الجديدة</li>
                      <li>• تخزين مؤقت ذكي لتحسين الأداء</li>
                      <li>• واجهة سهلة ومفهومة</li>
                    </ul>
                  </div>

                  <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border border-blue-200 dark:border-blue-700">
                    <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-1 flex items-center gap-2">
                      <FaInfoCircle className="text-blue-600" />
                      كيفية الاستخدام
                    </h5>
                    <ul className="space-y-1 text-blue-700 dark:text-blue-300 text-xs">
                      <li>• اضغط على قائمة "فترة التحليل"</li>
                      <li>• ستظهر الفترات المتاحة مع التفاصيل</li>
                      <li>• اختر الفترة المناسبة لاحتياجك</li>
                      <li>• سيتم تحديث جميع التحليلات تلقائياً</li>
                    </ul>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-lg border border-yellow-200 dark:border-yellow-700">
                    <h5 className="font-medium text-yellow-800 dark:text-yellow-200 mb-1 flex items-center gap-2">
                      <FaExclamationTriangle className="text-yellow-600" />
                      ملاحظات مهمة
                    </h5>
                    <ul className="space-y-1 text-yellow-700 dark:text-yellow-300 text-xs">
                      <li>• إذا لم تظهر فترات، تأكد من وجود مبيعات</li>
                      <li>• الفترات تتحدث كل 5 دقائق تلقائياً</li>
                      <li>• يمكن إجبار التحديث بزر "تحديث"</li>
                      <li>• النظام يدعم من أسبوع إلى سنة كاملة</li>
                    </ul>
                  </div>

                  <div className="bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg border border-purple-200 dark:border-purple-700">
                    <h5 className="font-medium text-purple-800 dark:text-purple-200 mb-1 flex items-center gap-2">
                      <FaLightbulb className="text-purple-600" />
                      نصائح للاستفادة القصوى
                    </h5>
                    <ul className="space-y-1 text-purple-700 dark:text-purple-300 text-xs">
                      <li>• استخدم فترات قصيرة للمراجعة اليومية</li>
                      <li>• استخدم فترات طويلة للتخطيط الاستراتيجي</li>
                      <li>• قارن بين فترات مختلفة لفهم الاتجاهات</li>
                      <li>• راقب تفاصيل كل فترة لاتخاذ قرارات أفضل</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* شرح بطاقات الملخص */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-4 flex items-center gap-2">
                <FaInfoCircle className="text-secondary-600" />
                بطاقات الملخص - نظرة سريعة على أداء المتجر
              </h4>

              <p className="text-secondary-600 dark:text-secondary-400 mb-4">
                بطاقات الملخص تعطيك نظرة سريعة وشاملة على أداء منتجاتك. هذه البطاقات تظهر في أعلى صفحة التحليل وتحتوي على أهم المعلومات:
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border border-blue-200 dark:border-blue-700">
                    <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-1">إجمالي المنتجات</h5>
                    <p className="text-blue-700 dark:text-blue-300 text-sm">العدد الكلي للمنتجات النشطة في متجرك</p>
                  </div>

                  <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg border border-green-200 dark:border-green-700">
                    <h5 className="font-medium text-green-800 dark:text-green-200 mb-1">منتجات مباعة</h5>
                    <p className="text-green-700 dark:text-green-300 text-sm">عدد المنتجات التي تم بيعها خلال الفترة المحددة</p>
                  </div>

                  <div className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-lg border border-yellow-200 dark:border-yellow-700">
                    <h5 className="font-medium text-yellow-800 dark:text-yellow-200 mb-1">منتجات غير مباعة</h5>
                    <p className="text-yellow-700 dark:text-yellow-300 text-sm">المنتجات التي لم تحقق أي مبيعات - تحتاج انتباه</p>
                  </div>

                  <div className="bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg border border-purple-200 dark:border-purple-700">
                    <h5 className="font-medium text-purple-800 dark:text-purple-200 mb-1">قيمة المخزون</h5>
                    <p className="text-purple-700 dark:text-purple-300 text-sm">القيمة الإجمالية للمخزون بأسعار البيع</p>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg border border-gray-200 dark:border-gray-600">
                    <h5 className="font-medium text-gray-800 dark:text-gray-200 mb-1">قيمة التكلفة</h5>
                    <p className="text-gray-700 dark:text-gray-300 text-sm">القيمة الإجمالية للمخزون بأسعار الشراء + الربح المحتمل</p>
                  </div>

                  <div className="bg-emerald-50 dark:bg-emerald-900/20 p-3 rounded-lg border border-emerald-200 dark:border-emerald-700">
                    <h5 className="font-medium text-emerald-800 dark:text-emerald-200 mb-1">متوسط هامش الربح</h5>
                    <p className="text-emerald-700 dark:text-emerald-300 text-sm">متوسط نسبة الربح مرجح بالمبيعات الفعلية (دقيق جداً)</p>
                  </div>

                  <div className="bg-indigo-50 dark:bg-indigo-900/20 p-3 rounded-lg border border-indigo-200 dark:border-indigo-700">
                    <h5 className="font-medium text-indigo-800 dark:text-indigo-200 mb-1">فترة التحليل</h5>
                    <p className="text-indigo-700 dark:text-indigo-300 text-sm">عدد الأيام التي يغطيها التحليل الحالي</p>
                  </div>
                </div>
              </div>
            </div>

            {/* أمثلة عملية للنظام الجديد */}
            <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 p-6 rounded-lg border border-green-200 dark:border-green-700">
              <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-4 flex items-center gap-2">
                <FaLightbulb className="text-green-600" />
                أمثلة عملية - كيف يساعدك النظام الجديد؟
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h5 className="font-medium text-secondary-800 dark:text-secondary-200">مثال 1: متجر جديد</h5>
                  <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <p className="text-secondary-600 dark:text-secondary-400 text-sm mb-3">
                      <strong>الحالة:</strong> متجر جديد بدأ العمل منذ أسبوعين فقط.
                    </p>
                    <p className="text-secondary-600 dark:text-secondary-400 text-sm mb-3">
                      <strong>النظام القديم:</strong> يعرض فترات ثابتة (30، 60، 90 يوم) حتى لو لم تكن هناك مبيعات.
                    </p>
                    <p className="text-secondary-600 dark:text-secondary-400 text-sm mb-3">
                      <strong>النظام الجديد:</strong> يعرض فقط فترة "7 أيام" و "14 يوم" لأنها الفترات التي تحتوي على مبيعات فعلية.
                    </p>
                    <p className="text-green-600 dark:text-green-400 text-sm font-medium">
                      ✅ النتيجة: تحليل دقيق ومفيد بدلاً من بيانات فارغة!
                    </p>
                  </div>
                </div>

                <div className="space-y-4">
                  <h5 className="font-medium text-secondary-800 dark:text-secondary-200">مثال 2: متجر موسمي</h5>
                  <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <p className="text-secondary-600 dark:text-secondary-400 text-sm mb-3">
                      <strong>الحالة:</strong> متجر ملابس شتوية توقف عن البيع في الصيف.
                    </p>
                    <p className="text-secondary-600 dark:text-secondary-400 text-sm mb-3">
                      <strong>النظام القديم:</strong> يعرض جميع الفترات حتى لو لم تكن هناك مبيعات في الأشهر الأخيرة.
                    </p>
                    <p className="text-secondary-600 dark:text-secondary-400 text-sm mb-3">
                      <strong>النظام الجديد:</strong> يعرض فقط الفترات التي تحتوي على مبيعات فعلية مع تفاصيل كل فترة.
                    </p>
                    <p className="text-green-600 dark:text-green-400 text-sm font-medium">
                      ✅ النتيجة: تركيز على البيانات المفيدة وتجنب الالتباس!
                    </p>
                  </div>
                </div>
              </div>

              <div className="mt-6 bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-700">
                <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center gap-2">
                  <FaInfoCircle className="text-blue-600" />
                  معلومات إضافية يعرضها النظام الجديد
                </h5>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-3">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">127</div>
                    <div className="text-xs text-blue-700 dark:text-blue-300">عمليات بيع</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600 dark:text-green-400">45</div>
                    <div className="text-xs text-green-700 dark:text-green-300">منتجات مختلفة</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">30 يوم</div>
                    <div className="text-xs text-purple-700 dark:text-purple-300">فترة التحليل</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'sales-analysis':
        return (
          <div className="space-y-6">
            {/* شرح المنتجات الأكثر مبيعاً */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-4 flex items-center gap-2">
                <FaChartBar className="text-secondary-600" />
                جدول المنتجات الأكثر مبيعاً - اكتشف نجوم متجرك
              </h4>

              <p className="text-secondary-600 dark:text-secondary-400 mb-4">
                هذا الجدول يعرض أفضل 10 منتجات من حيث المبيعات، مرتبة حسب الكمية المباعة. كل منتج يحصل على ترتيب بصري مع ألوان مميزة:
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-lg border border-yellow-200 dark:border-yellow-700">
                    <h5 className="font-medium text-yellow-800 dark:text-yellow-200 mb-1 flex items-center gap-2">
                      <span className="w-6 h-6 bg-yellow-500 text-white rounded-full flex items-center justify-center text-xs font-bold">1</span>
                      المركز الأول - ذهبي
                    </h5>
                    <p className="text-yellow-700 dark:text-yellow-300 text-sm">المنتج الأكثر مبيعاً - نجم المتجر</p>
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg border border-gray-200 dark:border-gray-600">
                    <h5 className="font-medium text-gray-800 dark:text-gray-200 mb-1 flex items-center gap-2">
                      <span className="w-6 h-6 bg-gray-400 text-white rounded-full flex items-center justify-center text-xs font-bold">2</span>
                      المركز الثاني - فضي
                    </h5>
                    <p className="text-gray-700 dark:text-gray-300 text-sm">ثاني أفضل منتج في المبيعات</p>
                  </div>

                  <div className="bg-orange-50 dark:bg-orange-900/20 p-3 rounded-lg border border-orange-200 dark:border-orange-700">
                    <h5 className="font-medium text-orange-800 dark:text-orange-200 mb-1 flex items-center gap-2">
                      <span className="w-6 h-6 bg-orange-600 text-white rounded-full flex items-center justify-center text-xs font-bold">3</span>
                      المركز الثالث - برونزي
                    </h5>
                    <p className="text-orange-700 dark:text-orange-300 text-sm">ثالث أفضل منتج في المبيعات</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h5 className="font-medium text-secondary-800 dark:text-secondary-200">معلومات كل منتج تشمل:</h5>
                  <ul className="space-y-1 text-secondary-600 dark:text-secondary-400 text-sm">
                    <li>• <strong>الكمية المباعة:</strong> العدد الكلي المباع خلال الفترة</li>
                    <li>• <strong>إجمالي الإيرادات:</strong> المبلغ الكلي من مبيعات هذا المنتج</li>
                    <li>• <strong>إجمالي الأرباح:</strong> صافي الربح من هذا المنتج</li>
                    <li>• <strong>هامش الربح:</strong> نسبة الربح من سعر البيع</li>
                    <li>• <strong>نسبة المبيعات:</strong> نسبة مساهمة هذا المنتج في إجمالي المبيعات</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* شرح المنتجات غير المباعة */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-4 flex items-center gap-2">
                <FaExclamationTriangle className="text-yellow-600" />
                المنتجات غير المباعة - اكتشف المشاكل مبكراً
              </h4>

              <p className="text-secondary-600 dark:text-secondary-400 mb-4">
                هذا الجدول يعرض المنتجات التي لم تحقق أي مبيعات خلال الفترة المحددة. هذه المعلومات مهمة جداً لاتخاذ قرارات سريعة:
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h5 className="font-medium text-secondary-800 dark:text-secondary-200">معلومات كل منتج تشمل:</h5>
                  <ul className="space-y-1 text-secondary-600 dark:text-secondary-400 text-sm">
                    <li>• <strong>المخزون الحالي:</strong> الكمية المتبقية في المخزن</li>
                    <li>• <strong>قيمة المخزون:</strong> قيمة الكمية بسعر البيع</li>
                    <li>• <strong>قيمة التكلفة:</strong> قيمة الكمية بسعر الشراء</li>
                    <li>• <strong>أيام في المخزون:</strong> كم يوم والمنتج لم يُباع</li>
                    <li>• <strong>الخسارة المحتملة:</strong> تقدير للخسارة إذا لم يُباع</li>
                  </ul>
                </div>

                <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-700">
                  <h5 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">ماذا تفعل مع هذه المنتجات؟</h5>
                  <ul className="space-y-1 text-yellow-700 dark:text-yellow-300 text-sm">
                    <li>• راجع أسعار هذه المنتجات - قد تكون مرتفعة</li>
                    <li>• فكر في عروض ترويجية أو خصومات</li>
                    <li>• تأكد من وضع المنتج في مكان بارز</li>
                    <li>• قد تحتاج لتغيير المورد أو المنتج</li>
                    <li>• راجع تاريخ الصلاحية إن وجد</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        );

      case 'risk-management':
        return (
          <div className="space-y-6">
            {/* شرح الخسائر المتوقعة */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-4 flex items-center gap-2">
                <FaExclamationTriangle className="text-red-600" />
                الخسائر المتوقعة - تحذير مبكر لحماية أرباحك
              </h4>

              <p className="text-secondary-600 dark:text-secondary-400 mb-4">
                هذا النظام الذكي يحلل المنتجات ويتنبأ بالخسائر المحتملة بناءً على عدة عوامل. يصنف المخاطر إلى ثلاث مستويات:
              </p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-700">
                  <h5 className="font-medium text-red-800 dark:text-red-200 mb-2 flex items-center gap-2">
                    <span className="w-3 h-3 bg-red-600 rounded-full"></span>
                    مخاطر عالية
                  </h5>
                  <p className="text-red-700 dark:text-red-300 text-sm mb-2">منتجات تحتاج تدخل فوري:</p>
                  <ul className="space-y-1 text-red-600 dark:text-red-400 text-xs">
                    <li>• أكثر من 60 يوم بدون مبيعات</li>
                    <li>• مخزون كبير معرض للتلف</li>
                    <li>• خسارة متوقعة عالية</li>
                  </ul>
                </div>

                <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-700">
                  <h5 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2 flex items-center gap-2">
                    <span className="w-3 h-3 bg-yellow-600 rounded-full"></span>
                    مخاطر متوسطة
                  </h5>
                  <p className="text-yellow-700 dark:text-yellow-300 text-sm mb-2">منتجات تحتاج مراقبة:</p>
                  <ul className="space-y-1 text-yellow-600 dark:text-yellow-400 text-xs">
                    <li>• 30-60 يوم بدون مبيعات</li>
                    <li>• مبيعات ضعيفة مؤخراً</li>
                    <li>• خسارة متوقعة متوسطة</li>
                  </ul>
                </div>

                <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-700">
                  <h5 className="font-medium text-green-800 dark:text-green-200 mb-2 flex items-center gap-2">
                    <span className="w-3 h-3 bg-green-600 rounded-full"></span>
                    مخاطر منخفضة
                  </h5>
                  <p className="text-green-700 dark:text-green-300 text-sm mb-2">منتجات في وضع آمن:</p>
                  <ul className="space-y-1 text-green-600 dark:text-green-400 text-xs">
                    <li>• أقل من 30 يوم بدون مبيعات</li>
                    <li>• مبيعات منتظمة عادة</li>
                    <li>• خسارة متوقعة قليلة</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* شرح حالة المخزون */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-4 flex items-center gap-2">
                <FaBoxOpen className="text-blue-600" />
                حالة المخزون وتوصيات الطلب - إدارة ذكية للمخزون
              </h4>

              <p className="text-secondary-600 dark:text-secondary-400 mb-4">
                هذا الجدول يعطيك نظرة شاملة على حالة كل منتج في المخزون مع توصيات ذكية للطلب. النظام يحسب كل شيء تلقائياً:
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border border-blue-200 dark:border-blue-700">
                    <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-1">أيام التوريد</h5>
                    <p className="text-blue-700 dark:text-blue-300 text-sm">كم يوم سيكفي المخزون الحالي بناءً على معدل المبيعات</p>
                  </div>

                  <div className="bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg border border-purple-200 dark:border-purple-700">
                    <h5 className="font-medium text-purple-800 dark:text-purple-200 mb-1">نقطة إعادة الطلب</h5>
                    <p className="text-purple-700 dark:text-purple-300 text-sm">الكمية التي عندها يجب طلب مخزون جديد</p>
                  </div>

                  <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg border border-green-200 dark:border-green-700">
                    <h5 className="font-medium text-green-800 dark:text-green-200 mb-1">الكمية المقترحة</h5>
                    <p className="text-green-700 dark:text-green-300 text-sm">كم وحدة يُنصح بطلبها للحفاظ على مخزون مثالي</p>
                  </div>
                </div>

                <div className="space-y-3">
                  <h5 className="font-medium text-secondary-800 dark:text-secondary-200">حالات المخزون:</h5>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm">
                      <span className="px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 rounded-full text-xs font-semibold">صحي</span>
                      <span className="text-secondary-600 dark:text-secondary-400">مخزون مناسب</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <span className="px-2 py-1 bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300 rounded-full text-xs font-semibold">منخفض</span>
                      <span className="text-secondary-600 dark:text-secondary-400">يحتاج إعادة طلب</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 rounded-full text-xs font-semibold">مفرط</span>
                      <span className="text-secondary-600 dark:text-secondary-400">مخزون زائد</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <span className="px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 rounded-full text-xs font-semibold">نفد</span>
                      <span className="text-secondary-600 dark:text-secondary-400">طلب عاجل</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'performance-analysis':
        return (
          <div className="space-y-6">
            {/* شرح تحليل الأداء */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-4 flex items-center gap-2">
                <FaChartBar className="text-purple-600" />
                تحليل أداء المنتجات - فهم عميق لسلوك المبيعات
              </h4>

              <p className="text-secondary-600 dark:text-secondary-400 mb-4">
                هذا الجدول الشامل يحلل أداء كل منتج بتفصيل دقيق ويقدم توصيات مخصصة. النظام يستخدم خوارزميات ذكية لتقييم الأداء:
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <h5 className="font-medium text-secondary-800 dark:text-secondary-200">اتجاهات المبيعات:</h5>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm">
                      <span className="px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 rounded-full text-xs font-semibold">متزايد</span>
                      <span className="text-secondary-600 dark:text-secondary-400">2+ وحدة يومياً - أداء ممتاز</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 rounded-full text-xs font-semibold">مستقر</span>
                      <span className="text-secondary-600 dark:text-secondary-400">0.5-2 وحدة يومياً - أداء جيد</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <span className="px-2 py-1 bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300 rounded-full text-xs font-semibold">متناقص</span>
                      <span className="text-secondary-600 dark:text-secondary-400">أقل من 0.5 وحدة يومياً</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <span className="px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 rounded-full text-xs font-semibold">لا مبيعات</span>
                      <span className="text-secondary-600 dark:text-secondary-400">لم يُباع في الفترة</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h5 className="font-medium text-secondary-800 dark:text-secondary-200">نقاط الأداء:</h5>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm">
                      <div className="w-16 bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                        <div className="h-2 rounded-full bg-green-600" style={{ width: '85%' }}></div>
                      </div>
                      <span className="text-secondary-600 dark:text-secondary-400">85 نقطة - ممتاز</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <div className="w-16 bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                        <div className="h-2 rounded-full bg-blue-600" style={{ width: '65%' }}></div>
                      </div>
                      <span className="text-secondary-600 dark:text-secondary-400">65 نقطة - جيد</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <div className="w-16 bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                        <div className="h-2 rounded-full bg-yellow-600" style={{ width: '35%' }}></div>
                      </div>
                      <span className="text-secondary-600 dark:text-secondary-400">35 نقطة - يحتاج تحسين</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <div className="w-16 bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                        <div className="h-2 rounded-full bg-red-600" style={{ width: '0%' }}></div>
                      </div>
                      <span className="text-secondary-600 dark:text-secondary-400">0 نقطة - مشكلة</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-4 bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-700">
                <h5 className="font-medium text-purple-800 dark:text-purple-200 mb-2">أمثلة على التوصيات الذكية:</h5>
                <ul className="space-y-1 text-purple-700 dark:text-purple-300 text-sm">
                  <li>• <strong>"أداء ممتاز - حافظ على المستوى"</strong> - للمنتجات عالية الأداء</li>
                  <li>• <strong>"يحتاج إعادة تخزين عاجل"</strong> - للمنتجات الناجحة قليلة المخزون</li>
                  <li>• <strong>"تصفية عاجلة - مخزون راكد"</strong> - للمنتجات غير المباعة</li>
                  <li>• <strong>"تقليل الطلبات المستقبلية"</strong> - للمنتجات ذات المخزون المفرط</li>
                  <li>• <strong>"مراقبة الأداء وتحسين التسويق"</strong> - للمنتجات متوسطة الأداء</li>
                </ul>
              </div>
            </div>
          </div>
        );

      case 'tools-examples':
        return (
          <div className="space-y-6">
            {/* الميزات الإضافية */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-4 flex items-center gap-2">
                <FaCog className="text-gray-600" />
                الميزات الإضافية - أدوات مساعدة
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-700">
                    <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center gap-2">
                      <FaEdit className="text-blue-600" />
                      زر التحديث
                    </h5>
                    <p className="text-blue-700 dark:text-blue-300 text-sm">
                      يحدث البيانات فوراً ويطبق الفلاتر الجديدة. مفيد عند تغيير الفترة الزمنية أو الفئة.
                    </p>
                  </div>

                  <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-700">
                    <h5 className="font-medium text-green-800 dark:text-green-200 mb-2 flex items-center gap-2">
                      <FaEnvelope className="text-green-600" />
                      تصدير البيانات
                    </h5>
                    <p className="text-green-700 dark:text-green-300 text-sm">
                      يحفظ جميع التحليلات في ملف JSON يمكن فتحه لاحقاً أو مشاركته مع المحاسب.
                    </p>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-700">
                    <h5 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2 flex items-center gap-2">
                      <FaEye className="text-yellow-600" />
                      التحديث التلقائي
                    </h5>
                    <p className="text-yellow-700 dark:text-yellow-300 text-sm">
                      البيانات تتحدث تلقائياً عند تغيير الفلاتر. لا حاجة للضغط على تحديث في كل مرة.
                    </p>
                  </div>

                  <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-700">
                    <h5 className="font-medium text-purple-800 dark:text-purple-200 mb-2 flex items-center gap-2">
                      <FaInfoCircle className="text-purple-600" />
                      رسائل الأخطاء
                    </h5>
                    <p className="text-purple-700 dark:text-purple-300 text-sm">
                      إذا حدث خطأ، ستظهر رسالة واضحة تشرح المشكلة مع زر لإغلاق الرسالة.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* خلاصة وأمثلة عملية */}
            <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 p-6 rounded-lg border border-green-200 dark:border-green-700">
              <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-4 flex items-center gap-2">
                <FaCheckCircle className="text-green-600" />
                خلاصة: كيف تستفيد من تحليل المنتجات بأقصى شكل؟
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h5 className="font-medium text-secondary-800 dark:text-secondary-200">سيناريو عملي - مقهى صغير:</h5>
                  <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <p className="text-secondary-600 dark:text-secondary-400 text-sm mb-3">
                      <strong>المشكلة:</strong> أحمد يدير مقهى ولاحظ انخفاض الأرباح مؤخراً.
                    </p>
                    <p className="text-secondary-600 dark:text-secondary-400 text-sm mb-3">
                      <strong>الحل باستخدام التحليل:</strong>
                    </p>
                    <ol className="space-y-1 text-secondary-600 dark:text-secondary-400 text-xs">
                      <li>1. فتح تحليل المنتجات لآخر 30 يوم</li>
                      <li>2. وجد أن القهوة العربية (الأكثر مبيعاً) مخزونها منخفض</li>
                      <li>3. اكتشف أن الكيك مخزونه مفرط ولم يُباع كثيراً</li>
                      <li>4. قرر طلب المزيد من القهوة وعمل عرض على الكيك</li>
                      <li>5. النتيجة: زيادة الأرباح بنسبة 25% في الشهر التالي!</li>
                    </ol>
                  </div>
                </div>

                <div className="space-y-4">
                  <h5 className="font-medium text-secondary-800 dark:text-secondary-200">روتين أسبوعي مقترح:</h5>
                  <div className="space-y-3">
                    <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border border-blue-200 dark:border-blue-700">
                      <h6 className="font-medium text-blue-800 dark:text-blue-200 text-sm mb-1">الاثنين - مراجعة سريعة</h6>
                      <p className="text-blue-700 dark:text-blue-300 text-xs">افتح التحليل لآخر 7 أيام وتأكد من عدم نفاد المنتجات المهمة</p>
                    </div>

                    <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg border border-green-200 dark:border-green-700">
                      <h6 className="font-medium text-green-800 dark:text-green-200 text-sm mb-1">الأربعاء - تحليل عميق</h6>
                      <p className="text-green-700 dark:text-green-300 text-xs">راجع تحليل 30 يوم وركز على المنتجات غير المباعة</p>
                    </div>

                    <div className="bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg border border-purple-200 dark:border-purple-700">
                      <h6 className="font-medium text-purple-800 dark:text-purple-200 text-sm mb-1">الجمعة - اتخاذ قرارات</h6>
                      <p className="text-purple-700 dark:text-purple-300 text-xs">اطلب المنتجات المطلوبة وخطط للعروض الترويجية</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'faq':
        return (
          <div className="space-y-6">
            {/* أسئلة شائعة */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-4 flex items-center gap-2">
                <FaQuestionCircle className="text-blue-600" />
                أسئلة شائعة حول تحليل المنتجات
              </h4>

              <div className="space-y-4">
                <div className="border-b border-gray-200 dark:border-gray-700 pb-3">
                  <h5 className="font-medium text-secondary-800 dark:text-secondary-200 mb-2">س: لماذا لا تظهر بعض المنتجات في التحليل؟</h5>
                  <p className="text-secondary-600 dark:text-secondary-400 text-sm">
                    ج: التحليل يعرض فقط المنتجات النشطة. تأكد من أن المنتج مفعل في قائمة المنتجات.
                  </p>
                </div>

                <div className="border-b border-gray-200 dark:border-gray-700 pb-3">
                  <h5 className="font-medium text-secondary-800 dark:text-secondary-200 mb-2">س: ما هي أفضل فترة للتحليل؟</h5>
                  <p className="text-secondary-600 dark:text-secondary-400 text-sm">
                    ج: للمراجعة اليومية استخدم 30 يوم، للتخطيط الشهري استخدم 90 يوم، وللمراجعة السنوية استخدم سنة كاملة.
                  </p>
                </div>

                <div className="border-b border-gray-200 dark:border-gray-700 pb-3">
                  <h5 className="font-medium text-secondary-800 dark:text-secondary-200 mb-2">س: كيف أفهم "أيام التوريد"؟</h5>
                  <p className="text-secondary-600 dark:text-secondary-400 text-sm">
                    ج: إذا كان المنتج يُباع بمعدل وحدة واحدة يومياً ولديك 30 وحدة، فأيام التوريد = 30 يوم.
                  </p>
                </div>

                <div>
                  <h5 className="font-medium text-secondary-800 dark:text-secondary-200 mb-2">س: متى أطلب مخزون جديد؟</h5>
                  <p className="text-secondary-600 dark:text-secondary-400 text-sm">
                    ج: عندما يصل المخزون إلى "نقطة إعادة الطلب" أو عندما تظهر حالة "منخفض" في جدول حالة المخزون.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg border border-orange-200 dark:border-orange-700">
              <div className="flex items-center gap-2 mb-2">
                <FaLightbulb className="text-orange-600 dark:text-orange-400" />
                <h4 className="font-medium text-orange-800 dark:text-orange-200">نصائح مهمة لإدارة المنتجات</h4>
              </div>
              <ul className="space-y-1 text-orange-700 dark:text-orange-300 text-sm">
                <li>• استخدم أسماء واضحة ومفهومة للمنتجات</li>
                <li>• احرص على تحديث الأسعار بانتظام حسب تغيرات السوق</li>
                <li>• استخدم الباركود لتسريع عمليات البيع</li>
                <li>• راجع تقارير المنتجات لمعرفة الأكثر مبيعاً</li>
                <li>• احتفظ بمخزون احتياطي للمنتجات الأساسية</li>
                <li>• <strong>جديد:</strong> استخدم تحليل المنتجات لاتخاذ قرارات ذكية</li>
                <li>• <strong>مهم:</strong> راجع التحليلات أسبوعياً لأفضل النتائج</li>
              </ul>
            </div>
          </div>
        );

      default:
        return <div>المحتوى قيد التطوير...</div>;
    }
  };

  return (
    <div className="space-y-6">
      {/* التبويبات الداخلية */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-wrap gap-1 p-2">
            {productTabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveProductTab(tab.id)}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 text-sm font-medium ${
                  activeProductTab === tab.id
                    ? `${tab.color} shadow-sm`
                    : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                {tab.icon}
                {tab.title}
              </button>
            ))}
          </div>
        </div>
        <div className="p-6">
          {getProductTabContent(activeProductTab)}
        </div>
      </div>
    </div>
  );
};

const HelpCenter: React.FC = () => {
  const [activeTab, setActiveTab] = useState('introduction');
  const [isSupportEmailModalOpen, setIsSupportEmailModalOpen] = useState(false);

  // أقسام المساعدة
  const helpSections = [
    {
      id: 'introduction',
      title: 'مقدمة عن التطبيق',
      icon: <FaBook />,
      color: 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-300'
    },
    {
      id: 'pos',
      title: 'نقطة البيع (POS)',
      icon: <FaCashRegister />,
      color: 'bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-300'
    },
    {
      id: 'products',
      title: 'إدارة المنتجات',
      icon: <FaBoxOpen />,
      color: 'bg-secondary-100 dark:bg-secondary-900/30 text-secondary-600 dark:text-secondary-300'
    },
    {
      id: 'sales',
      title: 'إدارة المبيعات',
      icon: <FaShoppingCart />,
      color: 'bg-success-100 dark:bg-success-900/30 text-success-600 dark:text-success-300'
    },
    {
      id: 'customers',
      title: 'إدارة العملاء',
      icon: <FaUsers />,
      color: 'bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-300'
    },
    {
      id: 'debts',
      title: 'إدارة الديون',
      icon: <FaFileInvoiceDollar />,
      color: 'bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-300'
    },
    {
      id: 'reports',
      title: 'التقارير والإحصائيات',
      icon: <FaChartBar />,
      color: 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-300'
    },
    {
      id: 'alerts',
      title: 'دليل التنبيهات',
      icon: <FaBell />,
      color: 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-300'
    },
    {
      id: 'settings',
      title: 'الإعدادات (للمدراء)',
      icon: <FaCog />,
      color: 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
    },
    {
      id: 'users',
      title: 'إدارة المستخدمين (للمدراء)',
      icon: <FaUserShield />,
      color: 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-300'
    }
  ];

  // محتوى مفصل لكل قسم
  const getTabContent = (tabId: string) => {
    switch (tabId) {
      case 'introduction':
        return (
          <div className="space-y-6">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg border border-blue-200 dark:border-blue-700">
              <h3 className="text-xl font-semibold text-blue-800 dark:text-blue-200 mb-3 flex items-center gap-2">
                <FaInfoCircle />
                ما هو نظام SmartPOS؟
              </h3>
              <p className="text-blue-700 dark:text-blue-300 leading-relaxed">
                نظام SmartPOS هو نظام نقاط بيع ذكي وسهل الاستخدام مصمم خصيصاً لإدارة المحلات التجارية والمتاجر الصغيرة والمتوسطة. 
                يساعدك النظام في تنظيم عمليات البيع، تتبع المخزون، إدارة العملاء، ومتابعة الأرباح بطريقة بسيطة وفعالة.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-3 flex items-center gap-2">
                  <FaCheckCircle className="text-green-600" />
                  الميزات الرئيسية
                </h4>
                <ul className="space-y-2 text-secondary-600 dark:text-secondary-400">
                  <li className="flex items-start gap-2">
                    <span className="text-green-600 mt-1">•</span>
                    <span>إدارة المبيعات والفواتير بسهولة</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-green-600 mt-1">•</span>
                    <span>تتبع المخزون والمنتجات تلقائياً</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-green-600 mt-1">•</span>
                    <span>إدارة بيانات العملاء والديون</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-green-600 mt-1">•</span>
                    <span>تقارير مفصلة عن الأرباح والمبيعات</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-green-600 mt-1">•</span>
                    <span>واجهة سهلة ومناسبة للجميع</span>
                  </li>
                </ul>
              </div>

              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-3 flex items-center gap-2">
                  <FaPlayCircle className="text-primary-600" />
                  كيفية البدء
                </h4>
                <ol className="space-y-2 text-secondary-600 dark:text-secondary-400">
                  <li className="flex items-start gap-2">
                    <span className="text-primary-600 font-semibold">1.</span>
                    <span>قم بتسجيل الدخول باستخدام اسم المستخدم وكلمة المرور</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-primary-600 font-semibold">2.</span>
                    <span>ستظهر لك الصفحة الرئيسية مع جميع الأقسام</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-primary-600 font-semibold">3.</span>
                    <span>اختر القسم الذي تريد العمل عليه</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-primary-600 font-semibold">4.</span>
                    <span>ابدأ بإضافة المنتجات أو العملاء حسب الحاجة</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-primary-600 font-semibold">5.</span>
                    <span>يمكنك البدء في عمليات البيع فوراً</span>
                  </li>
                </ol>
              </div>
            </div>

            {/* صورة توضيحية للصفحة الرئيسية */}
            <ResponsiveImage
              lightSrc={DashboardImg}
              darkSrc={DashboardImgDark}
              alt="الصفحة الرئيسية للنظام"
              title="صورة توضيحية - الصفحة الرئيسية للنظام"
              description="لقطة شاشة من الواجهة الرئيسية للنظام"
              iconColor="text-primary-600"
            />

            <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-700">
              <div className="flex items-center gap-2 mb-2">
                <FaLightbulb className="text-yellow-600 dark:text-yellow-400" />
                <h4 className="font-medium text-yellow-800 dark:text-yellow-200">نصائح مهمة للمبتدئين</h4>
              </div>
              <ul className="space-y-1 text-yellow-700 dark:text-yellow-300 text-sm">
                <li>• ابدأ بإضافة بعض المنتجات الأساسية قبل البدء في البيع</li>
                <li>• تأكد من ضبط إعدادات الشركة والضرائب من قسم الإعدادات</li>
                <li>• استخدم خاصية البحث للعثور على المنتجات بسرعة</li>
                <li>• احفظ بيانات العملاء المهمين لتسهيل التعامل معهم لاحقاً</li>
                <li>• راجع التقارير بانتظام لمتابعة أداء المتجر</li>
              </ul>
            </div>
          </div>
        );

      case 'pos':
        return (
          <div className="space-y-6">
            <div className="bg-primary-50 dark:bg-primary-900/20 p-6 rounded-lg border border-primary-200 dark:border-primary-700">
              <h3 className="text-xl font-semibold text-primary-800 dark:text-primary-200 mb-3 flex items-center gap-2">
                <FaCashRegister />
                نقطة البيع - قلب النظام
              </h3>
              <p className="text-primary-700 dark:text-primary-300 leading-relaxed">
                نقطة البيع هي المكان الذي تتم فيه جميع عمليات البيع اليومية. من هنا يمكنك إنشاء الفواتير، 
                إضافة المنتجات، تطبيق الخصومات، واستلام المدفوعات بطريقة سريعة وسهلة.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 flex items-center gap-2">
                  <FaMousePointer className="text-primary-600" />
                  خطوات إنشاء فاتورة جديدة
                </h4>
                <div className="space-y-3">
                  {[
                    { step: 1, text: 'اضغط على "نقطة البيع" من الصفحة الرئيسية', icon: <FaMousePointer /> },
                    { step: 2, text: 'ابحث عن المنتج باستخدام اسمه أو الباركود', icon: <FaEye /> },
                    { step: 3, text: 'اضغط على المنتج لإضافته إلى السلة', icon: <FaPlus /> },
                    { step: 4, text: 'كرر العملية لإضافة منتجات أخرى', icon: <FaEdit /> },
                    { step: 5, text: 'اختر العميل إذا كان مسجلاً (اختياري)', icon: <FaUsers /> },
                    { step: 6, text: 'طبق خصم إذا لزم الأمر', icon: <FaCalculator /> },
                    { step: 7, text: 'اختر طريقة الدفع (نقدي/آجل)', icon: <FaCalculator /> },
                    { step: 8, text: 'اضغط "إتمام البيع" واطبع الفاتورة', icon: <FaPrint /> }
                  ].map((item, index) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                      <div className="flex items-center justify-center w-8 h-8 bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-300 rounded-full font-semibold text-sm">
                        {item.step}
                      </div>
                      <div className="flex-1">
                        <p className="text-secondary-700 dark:text-secondary-300">{item.text}</p>
                      </div>
                      <div className="text-primary-600 dark:text-primary-400">
                        {item.icon}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 flex items-center gap-2">
                  <FaLightbulb className="text-yellow-600" />
                  نصائح لاستخدام نقطة البيع بكفاءة
                </h4>
                <div className="space-y-3">
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-700">
                    <h5 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">استخدام الباركود</h5>
                    <p className="text-yellow-700 dark:text-yellow-300 text-sm">
                      إذا كانت منتجاتك تحتوي على باركود، يمكنك مسحه ضوئياً أو كتابته مباشرة لإضافة المنتج بسرعة.
                    </p>
                  </div>
                  <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-700">
                    <h5 className="font-medium text-green-800 dark:text-green-200 mb-2">الخصومات</h5>
                    <p className="text-green-700 dark:text-green-300 text-sm">
                      يمكنك تطبيق خصم على منتج واحد أو على إجمالي الفاتورة. الخصم يمكن أن يكون مبلغ ثابت أو نسبة مئوية.
                    </p>
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-700">
                    <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-2">حفظ كمسودة</h5>
                    <p className="text-blue-700 dark:text-blue-300 text-sm">
                      إذا لم تكمل الفاتورة، يمكنك حفظها كمسودة والعودة إليها لاحقاً لإكمالها.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* صورة توضيحية لنقطة البيع */}
            <ResponsiveImage
              lightSrc={POSImg}
              darkSrc={POSImgDark}
              alt="واجهة نقطة البيع"
              title="صورة توضيحية - واجهة نقطة البيع"
              description="لقطة شاشة من واجهة نقطة البيع وعملية إنشاء الفواتير"
              iconColor="text-primary-600"
            />
          </div>
        );

      case 'products':
        return <ProductsTabContent />;

      case 'sales':
        return (
          <div className="space-y-6">
            <div className="bg-success-50 dark:bg-success-900/20 p-6 rounded-lg border border-success-200 dark:border-success-700">
              <h3 className="text-xl font-semibold text-success-800 dark:text-success-200 mb-3 flex items-center gap-2">
                <FaShoppingCart />
                إدارة المبيعات - متابعة الفواتير
              </h3>
              <p className="text-success-700 dark:text-success-300 leading-relaxed">
                قسم المبيعات يعرض جميع الفواتير والمعاملات التي تمت في المتجر. يمكنك البحث في الفواتير،
                طباعتها مرة أخرى، تتبع حالة الدفع، وإدارة المرتجعات بسهولة.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-3 flex items-center gap-2">
                  <FaEye className="text-success-600" />
                  عرض الفواتير
                </h4>
                <ul className="space-y-2 text-secondary-600 dark:text-secondary-400 text-sm">
                  <li>• عرض جميع الفواتير مرتبة حسب التاريخ</li>
                  <li>• البحث بالرقم أو اسم العميل</li>
                  <li>• تصفية حسب التاريخ أو حالة الدفع</li>
                  <li>• عرض تفاصيل كل فاتورة</li>
                </ul>
              </div>

              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-3 flex items-center gap-2">
                  <FaPrint className="text-success-600" />
                  طباعة الفواتير
                </h4>
                <ul className="space-y-2 text-secondary-600 dark:text-secondary-400 text-sm">
                  <li>• طباعة فاتورة واحدة أو عدة فواتير</li>
                  <li>• اختيار تنسيق الطباعة</li>
                  <li>• طباعة نسخة للعميل ونسخة للمحاسبة</li>
                  <li>• حفظ الفاتورة كملف PDF</li>
                </ul>
              </div>

              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-3 flex items-center gap-2">
                  <FaEdit className="text-success-600" />
                  إدارة المرتجعات
                </h4>
                <ul className="space-y-2 text-secondary-600 dark:text-secondary-400 text-sm">
                  <li>• إنشاء فاتورة مرتجع</li>
                  <li>• ربط المرتجع بالفاتورة الأصلية</li>
                  <li>• إعادة المنتجات للمخزون</li>
                  <li>• تسجيل سبب الإرجاع</li>
                </ul>
              </div>
            </div>

            {/* صورة توضيحية لإدارة المبيعات */}
            <ResponsiveImage
              lightSrc={SalesImg}
              darkSrc={SalesImgDark}
              alt="صفحة إدارة المبيعات"
              title="صورة توضيحية - صفحة إدارة المبيعات"
              description="لقطة شاشة من صفحة إدارة المبيعات والفواتير"
              iconColor="text-success-600"
            />
          </div>
        );

      case 'customers':
        return (
          <div className="space-y-6">
            <div className="bg-purple-50 dark:bg-purple-900/20 p-6 rounded-lg border border-purple-200 dark:border-purple-700">
              <h3 className="text-xl font-semibold text-purple-800 dark:text-purple-200 mb-3 flex items-center gap-2">
                <FaUsers />
                إدارة العملاء - بناء علاقات قوية
              </h3>
              <p className="text-purple-700 dark:text-purple-300 leading-relaxed">
                قسم العملاء يساعدك في إدارة قاعدة بيانات العملاء وبناء علاقات تجارية قوية. يمكنك حفظ معلومات العملاء،
                تتبع مشترياتهم، إدارة الديون، وتقديم خدمة عملاء أفضل.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-3 flex items-center gap-2">
                  <FaPlus className="text-purple-600" />
                  إضافة عميل جديد
                </h4>
                <ol className="space-y-2 text-secondary-600 dark:text-secondary-400 text-sm">
                  <li className="flex items-start gap-2">
                    <span className="text-purple-600 font-semibold">1.</span>
                    <span>اذهب إلى قسم "العملاء"</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-purple-600 font-semibold">2.</span>
                    <span>اضغط على "إضافة عميل جديد"</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-purple-600 font-semibold">3.</span>
                    <span>املأ الاسم الكامل للعميل</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-purple-600 font-semibold">4.</span>
                    <span>أضف رقم الهاتف والعنوان</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-purple-600 font-semibold">5.</span>
                    <span>أدخل البريد الإلكتروني إذا كان متوفراً</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-purple-600 font-semibold">6.</span>
                    <span>احفظ بيانات العميل</span>
                  </li>
                </ol>
              </div>

              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-3 flex items-center gap-2">
                  <FaEye className="text-purple-600" />
                  متابعة العملاء
                </h4>
                <ul className="space-y-2 text-secondary-600 dark:text-secondary-400 text-sm">
                  <li className="flex items-start gap-2">
                    <span className="text-purple-600 mt-1">•</span>
                    <span><strong>تاريخ المشتريات:</strong> عرض جميع فواتير العميل</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-purple-600 mt-1">•</span>
                    <span><strong>إجمالي المشتريات:</strong> مبلغ إجمالي ما اشتراه العميل</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-purple-600 mt-1">•</span>
                    <span><strong>الديون المستحقة:</strong> المبالغ غير المدفوعة</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-purple-600 mt-1">•</span>
                    <span><strong>آخر زيارة:</strong> تاريخ آخر عملية شراء</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-purple-600 mt-1">•</span>
                    <span><strong>ملاحظات:</strong> إضافة ملاحظات خاصة بالعميل</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* صورة توضيحية لإدارة العملاء */}
            <ResponsiveImage
              lightSrc={CustomersImg}
              darkSrc={CustomersImgDark}
              alt="صفحة إدارة العملاء"
              title="صورة توضيحية - صفحة إدارة العملاء"
              description="لقطة شاشة من صفحة إدارة العملاء وبياناتهم"
              iconColor="text-purple-600"
            />
          </div>
        );

      case 'debts':
        return (
          <div className="space-y-6">
            <div className="bg-orange-50 dark:bg-orange-900/20 p-6 rounded-lg border border-orange-200 dark:border-orange-700">
              <h3 className="text-xl font-semibold text-orange-800 dark:text-orange-200 mb-3 flex items-center gap-2">
                <FaFileInvoiceDollar />
                إدارة الديون - متابعة المستحقات
              </h3>
              <p className="text-orange-700 dark:text-orange-300 leading-relaxed">
                قسم الديون يساعدك في تتبع جميع المبالغ المستحقة من العملاء. يمكنك تسجيل المدفوعات الجزئية،
                متابعة تواريخ الاستحقاق، وإرسال تذكيرات للعملاء.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-3 flex items-center gap-2">
                  <FaEye className="text-orange-600" />
                  عرض الديون
                </h4>
                <ul className="space-y-2 text-secondary-600 dark:text-secondary-400 text-sm">
                  <li>• عرض جميع الديون المستحقة</li>
                  <li>• تصفية حسب العميل أو التاريخ</li>
                  <li>• ترتيب حسب المبلغ أو تاريخ الاستحقاق</li>
                  <li>• عرض تفاصيل كل دين</li>
                </ul>
              </div>

              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-3 flex items-center gap-2">
                  <FaCalculator className="text-orange-600" />
                  تسجيل المدفوعات
                </h4>
                <ul className="space-y-2 text-secondary-600 dark:text-secondary-400 text-sm">
                  <li>• تسجيل دفعة جزئية</li>
                  <li>• تسجيل دفع كامل للدين</li>
                  <li>• إضافة ملاحظات للدفعة</li>
                  <li>• طباعة إيصال الدفع</li>
                </ul>
              </div>

              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-3 flex items-center gap-2">
                  <FaChartBar className="text-orange-600" />
                  تقارير الديون
                </h4>
                <ul className="space-y-2 text-secondary-600 dark:text-secondary-400 text-sm">
                  <li>• إجمالي الديون المستحقة</li>
                  <li>• الديون المتأخرة</li>
                  <li>• تقرير المدفوعات</li>
                  <li>• تحليل العملاء المدينين</li>
                </ul>
              </div>
            </div>

            {/* صورة توضيحية لإدارة الديون */}
            <ResponsiveImage
              lightSrc={DebtsImg}
              darkSrc={DebtsImgDark}
              alt="صفحة إدارة الديون"
              title="صورة توضيحية - صفحة إدارة الديون"
              description="لقطة شاشة من صفحة إدارة الديون والمستحقات"
              iconColor="text-orange-600"
            />
          </div>
        );

      case 'reports':
        return (
          <div className="space-y-6">
            <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg border border-green-200 dark:border-green-700">
              <h3 className="text-xl font-semibold text-green-800 dark:text-green-200 mb-3 flex items-center gap-2">
                <FaChartBar />
                التقارير والإحصائيات - تحليل شامل للأعمال
              </h3>
              <p className="text-green-700 dark:text-green-300 leading-relaxed">
                قسم التقارير يوفر لك تحليلات مفصلة عن أداء متجرك. يمكنك مراجعة المبيعات، الأرباح، أداء المنتجات،
                وسلوك العملاء من خلال تقارير تفاعلية ومرئية سهلة الفهم.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-3 flex items-center gap-2">
                  <FaChartBar className="text-green-600" />
                  تقارير المبيعات
                </h4>
                <ul className="space-y-2 text-secondary-600 dark:text-secondary-400 text-sm">
                  <li>• تقرير المبيعات اليومية والشهرية</li>
                  <li>• تحليل الأرباح والخسائر</li>
                  <li>• مقارنة الأداء بين الفترات</li>
                  <li>• تقرير أفضل المنتجات مبيعاً</li>
                  <li>• تحليل طرق الدفع المستخدمة</li>
                </ul>
              </div>

              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-3 flex items-center gap-2">
                  <FaUsers className="text-green-600" />
                  تقارير العملاء
                </h4>
                <ul className="space-y-2 text-secondary-600 dark:text-secondary-400 text-sm">
                  <li>• قائمة العملاء الأكثر شراءً</li>
                  <li>• تحليل سلوك العملاء</li>
                  <li>• تقرير الديون المستحقة</li>
                  <li>• إحصائيات العملاء الجدد</li>
                  <li>• تقرير العملاء غير النشطين</li>
                </ul>
              </div>
            </div>

            {/* صورة توضيحية للتقارير */}
            <ResponsiveImage
              lightSrc={ReportImg}
              darkSrc={ReportImgDark}
              alt="صفحة التقارير والإحصائيات"
              title="صورة توضيحية - صفحة التقارير والإحصائيات"
              description="لقطة شاشة من صفحة التقارير والرسوم البيانية"
              iconColor="text-green-600"
            />
          </div>
        );

      case 'settings':
        return (
          <div className="space-y-6">
            <div className="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg border border-gray-200 dark:border-gray-600">
              <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center gap-2">
                <FaCog />
                الإعدادات - تخصيص النظام (للمدراء فقط)
              </h3>
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                قسم الإعدادات يسمح للمدراء بتخصيص النظام حسب احتياجات المتجر. يمكن ضبط معلومات الشركة،
                إعدادات الضرائب، تخصيص الفواتير، وإدارة النسخ الاحتياطية.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-3 flex items-center gap-2">
                  <FaInfoCircle className="text-gray-600" />
                  إعدادات الشركة
                </h4>
                <ul className="space-y-2 text-secondary-600 dark:text-secondary-400 text-sm">
                  <li>• اسم الشركة أو المتجر</li>
                  <li>• عنوان المؤسسة ومعلومات الاتصال</li>
                  <li>• شعار الشركة للفواتير</li>
                  <li>• رقم السجل التجاري</li>
                  <li>• معلومات إضافية للفواتير</li>
                </ul>
              </div>

              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-3 flex items-center gap-2">
                  <FaCalculator className="text-gray-600" />
                  إعدادات الضرائب والعملة
                </h4>
                <ul className="space-y-2 text-secondary-600 dark:text-secondary-400 text-sm">
                  <li>• نسبة ضريبة القيمة المضافة</li>
                  <li>• العملة المستخدمة</li>
                  <li>• طريقة حساب الضرائب</li>
                  <li>• إعدادات التقريب</li>
                  <li>• رقم التسجيل الضريبي</li>
                </ul>
              </div>
            </div>

            {/* صورة توضيحية للإعدادات */}
            <ResponsiveImage
              lightSrc={SettingsImg}
              darkSrc={SettingsImgDark}
              alt="صفحة الإعدادات"
              title="صورة توضيحية - صفحة الإعدادات"
              description="لقطة شاشة من صفحة إعدادات النظام"
              iconColor="text-gray-600"
            />
          </div>
        );

      case 'alerts':
        return (
          <div className="space-y-6">
            {/* مقدمة عن نظام التنبيهات */}
            <div className="bg-yellow-50 dark:bg-yellow-900/20 p-6 rounded-lg border border-yellow-200 dark:border-yellow-700">
              <h3 className="text-xl font-semibold text-yellow-800 dark:text-yellow-200 mb-3 flex items-center gap-2">
                <FaBell />
                نظام التنبيهات الذكي - مساعدك الشخصي في إدارة المتجر
              </h3>
              <p className="text-yellow-700 dark:text-yellow-300 leading-relaxed mb-4">
                نظام التنبيهات في SmartPOS هو مساعدك الذكي الذي يراقب متجرك على مدار الساعة ويخبرك بكل ما يحتاج انتباهك.
                من نفاد المخزون إلى المبيعات الناجحة، ومن تذكيرك بالمهام إلى تنبيهك للمشاكل قبل حدوثها.
              </p>
              <div className="bg-yellow-100 dark:bg-yellow-800/30 p-4 rounded-lg">
                <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">💡 لماذا التنبيهات مهمة؟</h4>
                <ul className="text-yellow-700 dark:text-yellow-300 text-sm space-y-1">
                  <li>• تساعدك في اتخاذ قرارات سريعة ومدروسة</li>
                  <li>• تمنع فقدان المبيعات بسبب نفاد المخزون</li>
                  <li>• تحافظ على رضا العملاء بالتنبيه للمشاكل مبكراً</li>
                  <li>• توفر عليك الوقت في مراقبة النظام يدوياً</li>
                </ul>
              </div>
            </div>

            {/* أنواع التنبيهات */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-4 flex items-center gap-2">
                <FaInfoCircle className="text-blue-600" />
                أنواع التنبيهات التي ستراها في متجرك
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-700">
                  <h5 className="font-medium text-green-800 dark:text-green-200 mb-2 flex items-center gap-2">
                    <FaCheckCircle className="text-green-600" />
                    تنبيهات النجاح ✅
                  </h5>
                  <p className="text-green-700 dark:text-green-300 text-sm mb-2">
                    تخبرك عندما تتم العمليات بنجاح:
                  </p>
                  <ul className="space-y-1 text-green-600 dark:text-green-400 text-sm">
                    <li>• "تم حفظ بيانات المنتج بنجاح"</li>
                    <li>• "تمت عملية البيع وطباعة الفاتورة"</li>
                    <li>• "تم إضافة عميل جديد"</li>
                    <li>• "تم تحديث أسعار المنتجات"</li>
                    <li>• "تم إنشاء تقرير المبيعات"</li>
                  </ul>
                  <div className="mt-3 p-2 bg-green-100 dark:bg-green-800/30 rounded text-xs text-green-700 dark:text-green-300">
                    💡 هذه التنبيهات تختفي تلقائياً بعد 3 ثواني لأنها مجرد تأكيد
                  </div>
                </div>

                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-700">
                  <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center gap-2">
                    <FaInfoCircle className="text-blue-600" />
                    تنبيهات المعلومات ℹ️
                  </h5>
                  <p className="text-blue-700 dark:text-blue-300 text-sm mb-2">
                    تعطيك معلومات مفيدة ونصائح:
                  </p>
                  <ul className="space-y-1 text-blue-600 dark:text-blue-400 text-sm">
                    <li>• "يمكنك استخدام Ctrl+S للحفظ السريع"</li>
                    <li>• "تم إضافة ميزة جديدة لإدارة العملاء"</li>
                    <li>• "تذكير: موعد إنشاء التقرير الشهري"</li>
                    <li>• "النظام يعمل بشكل طبيعي"</li>
                  </ul>
                  <div className="mt-3 p-2 bg-blue-100 dark:bg-blue-800/30 rounded text-xs text-blue-700 dark:text-blue-300">
                    💡 تختفي بعد 5 ثواني - معلومات مفيدة لكن غير عاجلة
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-700">
                  <h5 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2 flex items-center gap-2">
                    <FaExclamationTriangle className="text-yellow-600" />
                    تنبيهات التحذير ⚠️
                  </h5>
                  <p className="text-yellow-700 dark:text-yellow-300 text-sm mb-2">
                    تحذيرات مهمة تحتاج انتباهك:
                  </p>
                  <ul className="space-y-1 text-yellow-600 dark:text-yellow-400 text-sm">
                    <li>• "المنتج 'قلم أزرق' وصل للحد الأدنى (5 قطع)"</li>
                    <li>• "منتجات ستنتهي صلاحيتها خلال أسبوع"</li>
                    <li>• "العميل 'أحمد محمد' تجاوز حد الائتمان"</li>
                    <li>• "بيانات المنتج ناقصة - يرجى إكمالها"</li>
                  </ul>
                  <div className="mt-3 p-2 bg-yellow-100 dark:bg-yellow-800/30 rounded text-xs text-yellow-700 dark:text-yellow-300">
                    ⚠️ تبقى 7 ثواني - مهمة لكن ليست طارئة
                  </div>
                </div>

                <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-700">
                  <h5 className="font-medium text-red-800 dark:text-red-200 mb-2 flex items-center gap-2">
                    <FaExclamationTriangle className="text-red-600" />
                    تنبيهات الأخطاء ❌
                  </h5>
                  <p className="text-red-700 dark:text-red-300 text-sm mb-2">
                    أخطاء تحتاج حل سريع:
                  </p>
                  <ul className="space-y-1 text-red-600 dark:text-red-400 text-sm">
                    <li>• "فشل في حفظ بيانات المنتج - حاول مرة أخرى"</li>
                    <li>• "مشكلة في الاتصال بالإنترنت"</li>
                    <li>• "الطابعة غير متصلة أو بها مشكلة"</li>
                    <li>• "بيانات الفاتورة غير صحيحة"</li>
                  </ul>
                  <div className="mt-3 p-2 bg-red-100 dark:bg-red-800/30 rounded text-xs text-red-700 dark:text-red-300">
                    🔴 تبقى 10 ثواني - تحتاج حل سريع لمتابعة العمل
                  </div>
                </div>
              </div>

              <div className="mt-4">
                <div className="bg-red-100 dark:bg-red-800/30 p-4 rounded-lg border border-red-300 dark:border-red-600">
                  <h5 className="font-medium text-red-900 dark:text-red-100 mb-2 flex items-center gap-2">
                    <FaShieldAlt className="text-red-700" />
                    التنبيهات الحرجة 🚨
                  </h5>
                  <p className="text-red-800 dark:text-red-200 text-sm mb-2">
                    مشاكل خطيرة تحتاج تدخل فوري - لا تتجاهلها!
                  </p>
                  <ul className="space-y-1 text-red-700 dark:text-red-300 text-sm">
                    <li>• "فقدان الاتصال بقاعدة البيانات - النظام قد لا يعمل"</li>
                    <li>• "خطأ في النظام - يرجى إعادة تشغيل التطبيق"</li>
                    <li>• "محاولة دخول غير مصرح بها للنظام"</li>
                    <li>• "فشل في النسخ الاحتياطي - البيانات في خطر"</li>
                  </ul>
                  <div className="mt-3 p-2 bg-red-200 dark:bg-red-700/50 rounded text-xs text-red-800 dark:text-red-200">
                    🚨 <strong>مهم جداً:</strong> هذه التنبيهات لا تختفي تلقائياً - يجب إغلاقها يدوياً بعد حل المشكلة
                  </div>
                </div>
              </div>
            </div>

            {/* كيف يساعدك نظام التنبيهات في إدارة متجرك */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-4 flex items-center gap-2">
                <FaRocket className="text-purple-600" />
                كيف يساعدك نظام التنبيهات في إدارة متجرك بذكاء
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-700">
                    <h5 className="font-medium text-purple-800 dark:text-purple-200 mb-2 flex items-center gap-2">
                      <FaShieldAlt className="text-purple-600" />
                      🛡️ حماية من الإزعاج
                    </h5>
                    <p className="text-purple-700 dark:text-purple-300 text-sm mb-2">
                      النظام ذكي ولا يزعجك بتنبيهات مكررة:
                    </p>
                    <ul className="space-y-1 text-purple-700 dark:text-purple-300 text-sm">
                      <li>• لن ترى نفس التنبيه عدة مرات</li>
                      <li>• يجمع التنبيهات المتشابهة في تنبيه واحد</li>
                      <li>• يمنع إغراق الشاشة بالتنبيهات</li>
                      <li>• يعطي الأولوية للتنبيهات المهمة</li>
                    </ul>
                  </div>

                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-700">
                    <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center gap-2">
                      <FaFilter className="text-blue-600" />
                      🎯 تحكم كامل
                    </h5>
                    <p className="text-blue-700 dark:text-blue-300 text-sm mb-2">
                      يمكنك التحكم في ما تريد رؤيته:
                    </p>
                    <ul className="space-y-1 text-blue-700 dark:text-blue-300 text-sm">
                      <li>• كتم تنبيهات معينة إذا كانت تزعجك</li>
                      <li>• اختيار أنواع التنبيهات التي تهمك</li>
                      <li>• مراجعة تاريخ جميع التنبيهات</li>
                      <li>• إحصائيات مفصلة عن نشاط متجرك</li>
                    </ul>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-700">
                    <h5 className="font-medium text-green-800 dark:text-green-200 mb-2 flex items-center gap-2">
                      <FaTools className="text-green-600" />
                      🔧 مساعد ذكي
                    </h5>
                    <p className="text-green-700 dark:text-green-300 text-sm mb-2">
                      النظام يساعدك في حل المشاكل:
                    </p>
                    <ul className="space-y-1 text-green-700 dark:text-green-300 text-sm">
                      <li>• يقترح حلول للمشاكل الشائعة</li>
                      <li>• يرشدك لصفحات الحل المناسبة</li>
                      <li>• ينظف التنبيهات القديمة تلقائياً</li>
                      <li>• يراقب أداء النظام باستمرار</li>
                    </ul>
                  </div>

                  <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg border border-orange-200 dark:border-orange-700">
                    <h5 className="font-medium text-orange-800 dark:text-orange-200 mb-2 flex items-center gap-2">
                      <FaBell className="text-orange-600" />
                      📱 يعمل في كل مكان
                    </h5>
                    <p className="text-orange-700 dark:text-orange-300 text-sm mb-2">
                      التنبيهات تعمل على جميع الأجهزة:
                    </p>
                    <ul className="space-y-1 text-orange-700 dark:text-orange-300 text-sm">
                      <li>• الكمبيوتر المكتبي واللابتوب</li>
                      <li>• الهواتف الذكية والأجهزة اللوحية</li>
                      <li>• تصميم متجاوب يتكيف مع الشاشة</li>
                      <li>• سرعة عالية وأداء ممتاز</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* التنبيهات في أجزاء مختلفة من التطبيق */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-4 flex items-center gap-2">
                <FaShoppingCart className="text-green-600" />
                التنبيهات في أجزاء مختلفة من متجرك
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-700">
                  <h5 className="font-medium text-green-800 dark:text-green-200 mb-3 flex items-center gap-2">
                    <FaCashRegister className="text-green-600" />
                    نقطة البيع (POS)
                  </h5>
                  <ul className="space-y-2 text-green-700 dark:text-green-300 text-sm">
                    <li>• "تمت عملية البيع بنجاح - 150 د.ل."</li>
                    <li>• "تم طباعة الفاتورة"</li>
                    <li>• "المنتج غير متوفر في المخزون"</li>
                    <li>• "خطأ في الطابعة - تحقق من الاتصال"</li>
                    <li>• "العميل لديه دين مستحق - 200 د.ل."</li>
                  </ul>
                </div>

                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-700">
                  <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-3 flex items-center gap-2">
                    <FaBoxOpen className="text-blue-600" />
                    إدارة المنتجات
                  </h5>
                  <ul className="space-y-2 text-blue-700 dark:text-blue-300 text-sm">
                    <li>• "تم إضافة منتج جديد بنجاح"</li>
                    <li>• "تم تحديث أسعار 15 منتج"</li>
                    <li>• "تحذير: 5 منتجات وصلت للحد الأدنى"</li>
                    <li>• "منتجات ستنتهي صلاحيتها خلال أسبوع"</li>
                    <li>• "تم استيراد 100 منتج من ملف Excel"</li>
                  </ul>
                </div>

                <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-700">
                  <h5 className="font-medium text-purple-800 dark:text-purple-200 mb-3 flex items-center gap-2">
                    <FaUsers className="text-purple-600" />
                    إدارة العملاء
                  </h5>
                  <ul className="space-y-2 text-purple-700 dark:text-purple-300 text-sm">
                    <li>• "تم إضافة عميل جديد: أحمد محمد"</li>
                    <li>• "تم تحديث بيانات العميل"</li>
                    <li>• "العميل تجاوز حد الائتمان المسموح"</li>
                    <li>• "تذكير: دين مستحق منذ 30 يوم"</li>
                    <li>• "تم تسديد دين بقيمة 500 د.ل."</li>
                  </ul>
                </div>

                <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg border border-orange-200 dark:border-orange-700">
                  <h5 className="font-medium text-orange-800 dark:text-orange-200 mb-3 flex items-center gap-2">
                    <FaFileInvoiceDollar className="text-orange-600" />
                    إدارة الديون
                  </h5>
                  <ul className="space-y-2 text-orange-700 dark:text-orange-300 text-sm">
                    <li>• "تم تسجيل دين جديد - 300 د.ل."</li>
                    <li>• "تم تسديد دين جزئي - 150 د.ل."</li>
                    <li>• "تحذير: ديون متأخرة تحتاج متابعة"</li>
                    <li>• "تم إنشاء تقرير الديون الشهري"</li>
                    <li>• "إجمالي الديون المستحقة: 2,500 د.ل."</li>
                  </ul>
                </div>

                <div className="bg-indigo-50 dark:bg-indigo-900/20 p-4 rounded-lg border border-indigo-200 dark:border-indigo-700">
                  <h5 className="font-medium text-indigo-800 dark:text-indigo-200 mb-3 flex items-center gap-2">
                    <FaChartBar className="text-indigo-600" />
                    التقارير والإحصائيات
                  </h5>
                  <ul className="space-y-2 text-indigo-700 dark:text-indigo-300 text-sm">
                    <li>• "تم إنشاء تقرير المبيعات اليومي"</li>
                    <li>• "تقرير المخزون جاهز للتحميل"</li>
                    <li>• "مبيعات اليوم: 1,200 د.ل. (+15%)"</li>
                    <li>• "أفضل منتج مبيعاً: قلم أزرق"</li>
                    <li>• "تم تصدير التقرير إلى Excel"</li>
                  </ul>
                </div>

                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                  <h5 className="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center gap-2">
                    <FaCog className="text-gray-600" />
                    الإعدادات والنظام
                  </h5>
                  <ul className="space-y-2 text-gray-700 dark:text-gray-300 text-sm">
                    <li>• "تم حفظ إعدادات النظام"</li>
                    <li>• "تم تحديث بيانات المتجر"</li>
                    <li>• "تم إنشاء نسخة احتياطية"</li>
                    <li>• "تحديث جديد متوفر للنظام"</li>
                    <li>• "تم تغيير كلمة مرور المستخدم"</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* أزرار الإغلاق */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-4 flex items-center gap-2">
                <FaTimes className="text-red-600" />
                أزرار الإغلاق - تحكم كامل في التنبيهات
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-700">
                    <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center gap-2">
                      <FaTimes className="text-blue-600 bg-blue-200 rounded-full p-1" />
                      زر الإغلاق الفردي
                    </h5>
                    <ul className="space-y-1 text-blue-700 dark:text-blue-300 text-sm">
                      <li>• يظهر في الزاوية العلوية اليمنى لكل تنبيه</li>
                      <li>• مرئي دائماً بشفافية 70%</li>
                      <li>• يصبح أوضح (100%) عند التمرير فوقه</li>
                      <li>• يكبر قليلاً عند التمرير للتأكيد</li>
                      <li>• يغلق التنبيه فوراً عند الضغط عليه</li>
                    </ul>
                  </div>

                  <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-700">
                    <h5 className="font-medium text-red-800 dark:text-red-200 mb-2 flex items-center gap-2">
                      <FaTimes className="text-red-600 bg-red-200 rounded-full p-1" />
                      زر مسح الكل
                    </h5>
                    <ul className="space-y-1 text-red-700 dark:text-red-300 text-sm">
                      <li>• يظهر في شريط التحكم العلوي</li>
                      <li>• يظهر فقط عند وجود تنبيهات</li>
                      <li>• لونه أحمر للتنبيه من خطورة الإجراء</li>
                      <li>• يمسح جميع التنبيهات دفعة واحدة</li>
                      <li>• مفيد لتنظيف النظام بسرعة</li>
                    </ul>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-700">
                    <h5 className="font-medium text-green-800 dark:text-green-200 mb-2 flex items-center gap-2">
                      <FaCheckCircle className="text-green-600" />
                      الإغلاق التلقائي
                    </h5>
                    <ul className="space-y-1 text-green-700 dark:text-green-300 text-sm">
                      <li>• تنبيهات النجاح: 3 ثواني</li>
                      <li>• تنبيهات المعلومات: 5 ثواني</li>
                      <li>• تنبيهات التحذير: 7 ثواني</li>
                      <li>• تنبيهات الأخطاء: 10 ثواني</li>
                      <li>• التنبيهات الحرجة: لا تختفي تلقائياً</li>
                    </ul>
                  </div>

                  <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-700">
                    <h5 className="font-medium text-purple-800 dark:text-purple-200 mb-2 flex items-center gap-2">
                      <FaMousePointer className="text-purple-600" />
                      نصائح للاستخدام
                    </h5>
                    <ul className="space-y-1 text-purple-700 dark:text-purple-300 text-sm">
                      <li>• اضغط على زر X لإغلاق فوري</li>
                      <li>• لا تتجاهل التنبيهات الحرجة</li>
                      <li>• استخدم مسح الكل عند الحاجة</li>
                      <li>• التنبيهات المهمة لا تختفي تلقائياً</li>
                      <li>• يمكن إغلاق التنبيه حتى لو كان له إجراءات</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* اختبار أزرار الإغلاق */}
              <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-700">
                <h5 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2 flex items-center gap-2">
                  <FaCode className="text-yellow-600" />
                  اختبار أزرار الإغلاق
                </h5>
                <p className="text-yellow-700 dark:text-yellow-300 text-sm mb-2">
                  لاختبار أزرار الإغلاق، افتح وحدة التحكم (F12) واكتب:
                </p>
                <code className="bg-gray-800 text-green-400 p-2 rounded text-xs block">
                  const script = document.createElement('script');<br/>
                  script.src = '/test-close-buttons.js';<br/>
                  document.head.appendChild(script);
                </code>
                <p className="text-yellow-600 dark:text-yellow-400 text-xs mt-2">
                  سيقوم هذا بإنشاء 5 تنبيهات مختلفة لاختبار أزرار الإغلاق.
                </p>
              </div>
            </div>

            {/* كيفية التعامل مع التنبيهات */}
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-6 rounded-lg border border-blue-200 dark:border-blue-700">
              <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-4 flex items-center gap-2">
                <FaPlayCircle className="text-blue-600" />
                كيفية التعامل مع التنبيهات بفعالية
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                  <h5 className="font-medium text-secondary-900 dark:text-secondary-100 mb-3 flex items-center gap-2">
                    <FaMousePointer className="text-blue-600" />
                    📱 أين تظهر التنبيهات وكيف تتفاعل معها
                  </h5>
                  <div className="space-y-3 text-secondary-600 dark:text-secondary-400 text-sm">
                    <div className="flex items-start gap-2">
                      <span className="text-blue-600 font-semibold">👀</span>
                      <span><strong>مكان الظهور:</strong> التنبيهات تظهر في الزاوية العلوية اليمنى من الشاشة</span>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="text-blue-600 font-semibold">❌</span>
                      <span><strong>الإغلاق:</strong> اضغط على زر <FaTimes className="inline text-gray-500 bg-gray-200 rounded-full p-0.5 mx-1" /> لإغلاق التنبيه فوراً</span>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="text-blue-600 font-semibold">⏰</span>
                      <span><strong>الإخفاء التلقائي:</strong> معظم التنبيهات تختفي تلقائياً، عدا الحرجة</span>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="text-blue-600 font-semibold">🔘</span>
                      <span><strong>الأزرار:</strong> بعض التنبيهات تحتوي على أزرار للإجراءات السريعة</span>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="text-blue-600 font-semibold">📱</span>
                      <span><strong>الهاتف:</strong> على الهاتف، التنبيهات تتكيف مع حجم الشاشة</span>
                    </div>
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                  <h5 className="font-medium text-secondary-900 dark:text-secondary-100 mb-3 flex items-center gap-2">
                    <FaLightbulb className="text-purple-600" />
                    💡 نصائح للاستفادة القصوى
                  </h5>
                  <div className="space-y-3 text-secondary-600 dark:text-secondary-400 text-sm">
                    <div className="flex items-start gap-2">
                      <span className="text-purple-600 font-semibold">🚨</span>
                      <span><strong>التنبيهات الحرجة:</strong> لا تتجاهلها - تحتاج حل فوري</span>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="text-purple-600 font-semibold">⚠️</span>
                      <span><strong>التحذيرات:</strong> انتبه لها لتجنب مشاكل أكبر لاحقاً</span>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="text-purple-600 font-semibold">📊</span>
                      <span><strong>المخزون:</strong> تابع تنبيهات المخزون لتجنب فقدان المبيعات</span>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="text-purple-600 font-semibold">💰</span>
                      <span><strong>الديون:</strong> تابع تنبيهات الديون للحفاظ على السيولة</span>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="text-purple-600 font-semibold">🔔</span>
                      <span><strong>الصوت:</strong> يمكنك تفعيل الصوت للتنبيهات المهمة</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6 p-4 bg-blue-100 dark:bg-blue-900/30 rounded-lg border border-blue-200 dark:border-blue-700">
                <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center gap-2">
                  <FaInfoCircle className="text-blue-600" />
                  مثال عملي: كيف تتعامل مع تنبيه نفاد المخزون
                </h5>
                <div className="text-blue-700 dark:text-blue-300 text-sm space-y-2">
                  <p><strong>1.</strong> يظهر تنبيه: "المنتج 'قلم أزرق' وصل للحد الأدنى (5 قطع)"</p>
                  <p><strong>2.</strong> اضغط على زر "عرض المنتج" في التنبيه</p>
                  <p><strong>3.</strong> ستنتقل لصفحة المنتج لتحديث الكمية</p>
                  <p><strong>4.</strong> أو اضغط على زر X لإغلاق التنبيه إذا كنت ستتعامل معه لاحقاً</p>
                </div>
              </div>
            </div>

            {/* حل المشاكل الشائعة */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-4 flex items-center gap-2">
                <FaTools className="text-red-600" />
                حل المشاكل الشائعة مع التنبيهات
              </h4>

              <div className="space-y-6">
                <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-700">
                  <h5 className="font-medium text-red-800 dark:text-red-200 mb-3 flex items-center gap-2">
                    <FaExclamationTriangle className="text-red-600" />
                    ❓ لا أرى أي تنبيهات رغم وجود مشاكل
                  </h5>
                  <div className="space-y-3 text-red-700 dark:text-red-300 text-sm">
                    <div><strong>السبب المحتمل:</strong> قد تكون التنبيهات مكتومة أو معطلة</div>
                    <div><strong>الحل:</strong></div>
                    <ul className="list-disc list-inside space-y-1 mr-4">
                      <li>تحقق من إعدادات المتصفح - تأكد أن الإشعارات مسموحة</li>
                      <li>أعد تحميل الصفحة (F5 أو Ctrl+R)</li>
                      <li>تحقق من اتصال الإنترنت</li>
                      <li>إذا استمرت المشكلة، اتصل بالدعم الفني</li>
                    </ul>
                  </div>
                </div>

                <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-700">
                  <h5 className="font-medium text-yellow-800 dark:text-yellow-200 mb-3 flex items-center gap-2">
                    <FaExclamationTriangle className="text-yellow-600" />
                    ❓ التنبيهات تظهر كثيراً وتزعجني
                  </h5>
                  <div className="space-y-3 text-yellow-700 dark:text-yellow-300 text-sm">
                    <div><strong>السبب:</strong> قد تكون هناك مشاكل متكررة في النظام</div>
                    <div><strong>الحل:</strong></div>
                    <ul className="list-disc list-inside space-y-1 mr-4">
                      <li>حل المشاكل الأساسية (مثل نفاد المخزون)</li>
                      <li>يمكنك إغلاق التنبيهات بالضغط على زر X</li>
                      <li>راجع سبب التنبيهات وحلها من المصدر</li>
                      <li>إذا كانت تنبيهات غير مهمة، يمكن تجاهلها</li>
                    </ul>
                  </div>
                </div>

                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-700">
                  <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-3 flex items-center gap-2">
                    <FaInfoCircle className="text-blue-600" />
                    ❓ لا أفهم ما يعنيه التنبيه
                  </h5>
                  <div className="space-y-3 text-blue-700 dark:text-blue-300 text-sm">
                    <div><strong>الحل:</strong></div>
                    <ul className="list-disc list-inside space-y-1 mr-4">
                      <li>اقرأ رسالة التنبيه بعناية - عادة تحتوي على تفسير واضح</li>
                      <li>ابحث عن أزرار في التنبيه تقودك للحل</li>
                      <li>راجع هذا الدليل للتعرف على أنواع التنبيهات</li>
                      <li>اسأل زميل أو مدير المتجر</li>
                      <li>اتصل بالدعم الفني إذا كان التنبيه غامضاً</li>
                    </ul>
                  </div>
                </div>

                <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-700">
                  <h5 className="font-medium text-green-800 dark:text-green-200 mb-3 flex items-center gap-2">
                    <FaCheckCircle className="text-green-600" />
                    ❓ التنبيه لا يختفي رغم حل المشكلة
                  </h5>
                  <div className="space-y-3 text-green-700 dark:text-green-300 text-sm">
                    <div><strong>السبب:</strong> قد يكون تنبيه حرج يحتاج إغلاق يدوي</div>
                    <div><strong>الحل:</strong></div>
                    <ul className="list-disc list-inside space-y-1 mr-4">
                      <li>اضغط على زر X في التنبيه لإغلاقه</li>
                      <li>التنبيهات الحرجة لا تختفي تلقائياً</li>
                      <li>تأكد من حل المشكلة فعلاً قبل الإغلاق</li>
                      <li>إذا عاد التنبيه، فالمشكلة لم تُحل بعد</li>
                    </ul>
                  </div>
                </div>

                <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-700">
                  <h5 className="font-medium text-purple-800 dark:text-purple-200 mb-3 flex items-center gap-2">
                    <FaQuestionCircle className="text-purple-600" />
                    ❓ أريد تعطيل تنبيهات معينة
                  </h5>
                  <div className="space-y-3 text-purple-700 dark:text-purple-300 text-sm">
                    <div><strong>ملاحظة مهمة:</strong> لا ننصح بتعطيل التنبيهات لأنها مهمة لسلامة متجرك</div>
                    <div><strong>البديل:</strong></div>
                    <ul className="list-disc list-inside space-y-1 mr-4">
                      <li>حل المشاكل التي تسبب التنبيهات</li>
                      <li>يمكنك إغلاق التنبيهات يدوياً عند ظهورها</li>
                      <li>راجع إعدادات النظام لتخصيص التنبيهات</li>
                      <li>اطلب من مدير النظام تعديل إعدادات التنبيهات</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="mt-6 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
                <h5 className="font-medium text-gray-800 dark:text-gray-200 mb-2 flex items-center gap-2">
                  <FaEnvelope className="text-gray-600" />
                  💬 تحتاج مساعدة إضافية؟
                </h5>
                <p className="text-gray-700 dark:text-gray-300 text-sm">
                  إذا لم تجد حلاً لمشكلتك هنا، لا تتردد في التواصل مع فريق الدعم الفني.
                  نحن هنا لمساعدتك في الاستفادة القصوى من نظام التنبيهات.
                </p>
              </div>
            </div>

            {/* نصائح للاستفادة القصوى */}
            <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 p-6 rounded-lg border border-green-200 dark:border-green-700">
              <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-4 flex items-center gap-2">
                <FaLightbulb className="text-green-600" />
                نصائح ذهبية للاستفادة القصوى من التنبيهات
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <h5 className="font-medium text-green-800 dark:text-green-200 mb-3 flex items-center gap-2">
                      <FaCheckCircle className="text-green-600" />
                      ✅ أفضل الممارسات
                    </h5>
                    <ul className="space-y-2 text-green-700 dark:text-green-300 text-sm">
                      <li>• <strong>اقرأ التنبيه كاملاً</strong> - لا تتسرع في إغلاقه</li>
                      <li>• <strong>تعامل مع الحرجة فوراً</strong> - لا تؤجلها</li>
                      <li>• <strong>تابع تنبيهات المخزون</strong> - لتجنب فقدان المبيعات</li>
                      <li>• <strong>راجع التنبيهات يومياً</strong> - لمتابعة صحة المتجر</li>
                      <li>• <strong>استخدم الأزرار السريعة</strong> - توفر عليك الوقت</li>
                    </ul>
                  </div>

                  <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <h5 className="font-medium text-orange-800 dark:text-orange-200 mb-3 flex items-center gap-2">
                      <FaCalendarAlt className="text-orange-600" />
                      📅 روتين يومي مقترح
                    </h5>
                    <ul className="space-y-2 text-orange-700 dark:text-orange-300 text-sm">
                      <li>• <strong>بداية اليوم:</strong> تحقق من التنبيهات الحرجة</li>
                      <li>• <strong>منتصف اليوم:</strong> راجع تنبيهات المخزون</li>
                      <li>• <strong>نهاية اليوم:</strong> تابع تنبيهات المبيعات والديون</li>
                      <li>• <strong>أسبوعياً:</strong> راجع إحصائيات التنبيهات</li>
                    </ul>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <h5 className="font-medium text-red-800 dark:text-red-200 mb-3 flex items-center gap-2">
                      <FaExclamationTriangle className="text-red-600" />
                      ⚠️ أخطاء شائعة تجنبها
                    </h5>
                    <ul className="space-y-2 text-red-700 dark:text-red-300 text-sm">
                      <li>• <strong>لا تتجاهل التنبيهات الحرجة</strong> - قد تؤثر على العمل</li>
                      <li>• <strong>لا تغلق التنبيه دون قراءته</strong> - قد تفوت معلومة مهمة</li>
                      <li>• <strong>لا تؤجل حل مشاكل المخزون</strong> - قد تفقد مبيعات</li>
                      <li>• <strong>لا تتجاهل تنبيهات الديون</strong> - تؤثر على السيولة</li>
                    </ul>
                  </div>

                  <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-3 flex items-center gap-2">
                      <FaChartBar className="text-blue-600" />
                      📊 مؤشرات النجاح
                    </h5>
                    <ul className="space-y-2 text-blue-700 dark:text-blue-300 text-sm">
                      <li>• <strong>قلة التنبيهات الحرجة</strong> - يعني نظام صحي</li>
                      <li>• <strong>سرعة الاستجابة</strong> - حل المشاكل بسرعة</li>
                      <li>• <strong>عدم تكرار نفس التنبيه</strong> - يعني حل جذري</li>
                      <li>• <strong>استقرار المخزون</strong> - تنبيهات أقل لنفاد المخزون</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="mt-6 p-4 bg-gradient-to-r from-yellow-100 to-orange-100 dark:from-yellow-900/30 dark:to-orange-900/30 rounded-lg border border-yellow-200 dark:border-yellow-700">
                <h5 className="font-medium text-yellow-800 dark:text-yellow-200 mb-3 flex items-center gap-2">
                  <FaRocket className="text-yellow-600" />
                  🚀 نصيحة ذهبية للنجاح
                </h5>
                <div className="text-yellow-700 dark:text-yellow-300 text-sm space-y-2">
                  <p><strong>اجعل التنبيهات صديقك وليس عدوك!</strong></p>
                  <p>
                    التنبيهات ليست مجرد إزعاج - إنها نظام إنذار مبكر يحمي متجرك ويساعدك في اتخاذ قرارات ذكية.
                    كلما تفاعلت معها بإيجابية وسرعة، كلما أصبح متجرك أكثر نجاحاً واستقراراً.
                  </p>
                  <p className="font-medium">
                    💡 تذكر: التنبيه الذي تتجاهله اليوم قد يصبح مشكلة كبيرة غداً!
                  </p>
                </div>
              </div>
            </div>
          </div>
        );

      case 'users':
        return (
          <div className="space-y-6">
            <div className="bg-red-50 dark:bg-red-900/20 p-6 rounded-lg border border-red-200 dark:border-red-700">
              <h3 className="text-xl font-semibold text-red-800 dark:text-red-200 mb-3 flex items-center gap-2">
                <FaUserShield />
                إدارة المستخدمين - التحكم في الصلاحيات (للمدراء فقط)
              </h3>
              <p className="text-red-700 dark:text-red-300 leading-relaxed">
                قسم إدارة المستخدمين يسمح للمدراء بإضافة موظفين جدد، تحديد صلاحياتهم، وإدارة حساباتهم.
                يمكن إنشاء حسابات للكاشيرين مع صلاحيات محدودة أو مدراء مع صلاحيات كاملة.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-3 flex items-center gap-2">
                  <FaPlus className="text-red-600" />
                  إضافة مستخدم جديد
                </h4>
                <ol className="space-y-2 text-secondary-600 dark:text-secondary-400 text-sm">
                  <li className="flex items-start gap-2">
                    <span className="text-red-600 font-semibold">1.</span>
                    <span>اذهب إلى قسم "إدارة المستخدمين"</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-red-600 font-semibold">2.</span>
                    <span>اضغط على "إضافة مستخدم جديد"</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-red-600 font-semibold">3.</span>
                    <span>املأ اسم المستخدم وكلمة المرور</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-red-600 font-semibold">4.</span>
                    <span>اختر نوع المستخدم (مدير/كاشير)</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-red-600 font-semibold">5.</span>
                    <span>احفظ بيانات المستخدم</span>
                  </li>
                </ol>
              </div>

              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-3 flex items-center gap-2">
                  <FaUserShield className="text-red-600" />
                  أنواع المستخدمين
                </h4>
                <div className="space-y-3">
                  <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded-lg border border-red-200 dark:border-red-700">
                    <h5 className="font-medium text-red-800 dark:text-red-200 mb-1">المدير</h5>
                    <p className="text-red-700 dark:text-red-300 text-sm">صلاحيات كاملة لجميع أقسام النظام</p>
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border border-blue-200 dark:border-blue-700">
                    <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-1">الكاشير</h5>
                    <p className="text-blue-700 dark:text-blue-300 text-sm">صلاحيات محدودة (البيع والعملاء فقط)</p>
                  </div>
                </div>
              </div>
            </div>

            {/* صورة توضيحية لإدارة المستخدمين */}
            <ResponsiveImage
              lightSrc={UsersImg}
              darkSrc={UsersImgDark}
              alt="صفحة إدارة المستخدمين"
              title="صورة توضيحية - صفحة إدارة المستخدمين"
              description="لقطة شاشة من صفحة إدارة المستخدمين والصلاحيات"
              iconColor="text-red-600"
            />
          </div>
        );

      default:
        return (
          <div className="text-center py-12">
            <FaQuestionCircle className="text-6xl text-secondary-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-secondary-900 dark:text-secondary-100 mb-2">
              قريباً...
            </h3>
            <p className="text-secondary-600 dark:text-secondary-400">
              سيتم إضافة شرح مفصل لهذا القسم قريباً
            </p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700 rounded-lg mb-6">
          <div className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-3 rounded-full bg-primary-100 dark:bg-primary-900/30">
                <FaQuestionCircle className="text-2xl text-primary-600 dark:text-primary-300" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-secondary-900 dark:text-secondary-100">
                  مركز المساعدة والتوثيق
                </h1>
                <p className="text-secondary-600 dark:text-secondary-400">
                  دليل شامل ومفصل لاستخدام نظام SmartPOS بسهولة
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 sticky top-6">
              <div className="p-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600 rounded-t-lg">
                <h3 className="font-semibold text-gray-800 dark:text-gray-200 text-center">أقسام المساعدة</h3>
              </div>
              <div className="p-2">
                {helpSections.map((section) => (
                  <button
                    key={section.id}
                    onClick={() => setActiveTab(section.id)}
                    className={`w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200 text-right ${
                      activeTab === section.id
                        ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-700'
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                    }`}
                  >
                    <div className={`p-2 rounded-lg ${section.color}`}>
                      {section.icon}
                    </div>
                    <span className="font-medium text-sm">{section.title}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-3">
                  <div className={`p-3 rounded-lg ${helpSections.find(s => s.id === activeTab)?.color}`}>
                    {helpSections.find(s => s.id === activeTab)?.icon}
                  </div>
                  <h2 className="text-2xl font-bold text-secondary-900 dark:text-secondary-100">
                    {helpSections.find(s => s.id === activeTab)?.title}
                  </h2>
                </div>
              </div>
              <div className="p-6">
                {getTabContent(activeTab)}
              </div>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mt-12">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <h2 className="text-2xl font-bold text-secondary-900 dark:text-secondary-100 flex items-center gap-3">
                <FaQuestionCircle className="text-primary-600 dark:text-primary-400" />
                الأسئلة الشائعة
              </h2>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
                  <h3 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-2">
                    كيف يمكنني إعادة تعيين كلمة المرور؟
                  </h3>
                  <p className="text-secondary-600 dark:text-secondary-400 text-sm">
                    يمكن للمدير إعادة تعيين كلمة المرور من قسم "إدارة المستخدمين". أو يمكنك التواصل مع المدير مباشرة لطلب إعادة تعيين كلمة المرور.
                  </p>
                </div>
                <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
                  <h3 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-2">
                    كيف يمكنني طباعة الفواتير؟
                  </h3>
                  <p className="text-secondary-600 dark:text-secondary-400 text-sm">
                    يمكن طباعة الفواتير من نقطة البيع مباشرة بعد إتمام البيع، أو من قسم "المبيعات" بالضغط على أيقونة الطباعة بجانب كل فاتورة.
                  </p>
                </div>
                <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
                  <h3 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-2">
                    كيف يمكنني تتبع المخزون؟
                  </h3>
                  <p className="text-secondary-600 dark:text-secondary-400 text-sm">
                    يتم تتبع المخزون تلقائياً مع كل عملية بيع. يمكنك مراجعة الكميات المتاحة من قسم "المنتجات" وستحصل على تنبيهات عندما تقل الكمية عن الحد الأدنى.
                  </p>
                </div>
                <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
                  <h3 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-2">
                    كيف يمكنني إنشاء تقرير مخصص؟
                  </h3>
                  <p className="text-secondary-600 dark:text-secondary-400 text-sm">
                    انتقل إلى قسم "التقارير" واختر نوع التقرير المطلوب، ثم حدد الفترة الزمنية والمعايير المطلوبة. يمكنك تصدير التقارير كملف PDF أو Excel.
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-2">
                    ماذا أفعل إذا واجهت مشكلة تقنية؟
                  </h3>
                  <p className="text-secondary-600 dark:text-secondary-400 text-sm">
                    يمكنك التواصل مع فريق الدعم الفني عبر البريد الإلكتروني أدناه، أو مراجعة سجل الأخطاء في قسم "النظام" إذا كنت مديراً.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Support */}
        <div className="mt-8">
          <div className="bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-primary-900/20 dark:to-secondary-900/20 rounded-lg p-6 border border-primary-200 dark:border-primary-700">
            <div className="flex items-center gap-4 mb-4">
              <div className="p-3 rounded-full bg-primary-100 dark:bg-primary-900/30">
                <FaQuestionCircle className="text-2xl text-primary-600 dark:text-primary-300" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-secondary-900 dark:text-secondary-100">
                  هل تحتاج مساعدة إضافية؟
                </h3>
                <p className="text-secondary-600 dark:text-secondary-400">
                  فريق الدعم الفني جاهز لمساعدتك في أي وقت
                </p>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-2 flex items-center gap-2">
                  <FaEnvelope className="text-primary-600" />
                  البريد الإلكتروني
                </h4>
                <p className="text-secondary-600 dark:text-secondary-400 text-sm">
                  <EMAIL>
                </p>
                <p className="text-xs text-secondary-500 dark:text-secondary-500 mt-1">
                  سنرد عليك خلال 24 ساعة
                </p>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                <h4 className="font-semibold text-secondary-900 dark:text-secondary-100 mb-2 flex items-center gap-2">
                  <FaInfoCircle className="text-primary-600" />
                  ساعات العمل
                </h4>
                <p className="text-secondary-600 dark:text-secondary-400 text-sm">
                  الأحد - الخميس: 9:00 ص - 5:00 م
                </p>
                <p className="text-xs text-secondary-500 dark:text-secondary-500 mt-1">
                  التوقيت المحلي (ليبيا)
                </p>
              </div>
            </div>

            {/* زر إرسال رسالة للدعم */}
            <div className="mt-6 text-center">
              <button
                onClick={() => setIsSupportEmailModalOpen(true)}
                className="btn-primary inline-flex items-center gap-2 px-6 py-3 text-lg font-medium"
              >
                <FaPaperPlane />
                إرسال رسالة للدعم الفني
              </button>
              <p className="text-sm text-secondary-500 dark:text-secondary-400 mt-2">
                أرسل رسالة مباشرة لفريق الدعم مع تضمين معلومات متجرك تلقائياً
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* نافذة إرسال رسالة الدعم */}
      <SupportEmailModal
        isOpen={isSupportEmailModalOpen}
        onClose={() => setIsSupportEmailModalOpen(false)}
      />
    </div>
  );
};

export default HelpCenter;
