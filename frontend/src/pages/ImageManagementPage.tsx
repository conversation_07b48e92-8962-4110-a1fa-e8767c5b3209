/**
 * صفحة إدارة الصور - مثال شامل على استخدام خدمة إدارة الصور
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FiImage, FiUpload, FiSettings, FiBarChart, FiFolder } from 'react-icons/fi';
import { FaArrowLeft, FaSync } from 'react-icons/fa';
import { ImageUploadComponent, ImageGalleryComponent, ImageFolder } from '../components/ImageUpload';
import { imageManagementService, StorageStats, SupportedFormats } from '../services/imageManagementService';
import SuccessModal from '../components/SuccessModal';

const ImageManagementPage: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'upload' | 'gallery' | 'stats'>('gallery');
  const [selectedFolder, setSelectedFolder] = useState<ImageFolder>('products');
  const [storageStats, setStorageStats] = useState<StorageStats | null>(null);
  const [supportedFormats, setSupportedFormats] = useState<SupportedFormats | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [loading, setLoading] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  const folders: { value: ImageFolder; label: string; icon: React.ReactNode }[] = [
    { value: 'products', label: 'المنتجات', icon: <FiImage className="w-4 h-4" /> },
    { value: 'categories', label: 'الفئات', icon: <FiFolder className="w-4 h-4" /> },
    { value: 'brands', label: 'العلامات التجارية', icon: <FiSettings className="w-4 h-4" /> },
    { value: 'customers', label: 'العملاء', icon: <FiSettings className="w-4 h-4" /> },
    { value: 'users', label: 'المستخدمين', icon: <FiSettings className="w-4 h-4" /> },
    { value: 'general', label: 'عام', icon: <FiFolder className="w-4 h-4" /> }
  ];

  // تحميل البيانات الأولية
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      
      // تحميل إحصائيات التخزين
      const statsResult = await imageManagementService.getStorageStatistics();
      if (statsResult.success) {
        setStorageStats(statsResult);
      }

      // تحميل الصيغ المدعومة
      const formatsResult = await imageManagementService.getSupportedFormats();
      if (formatsResult.success) {
        setSupportedFormats(formatsResult);
      }

    } catch (error) {
      console.error('خطأ في تحميل البيانات الأولية:', error);
    } finally {
      setLoading(false);
    }
  };

  // معالجة نجاح الرفع
  const handleUploadSuccess = (result: any) => {
    console.log('✅ نجح رفع الصورة، تحديث المعرض...', result);

    // تحديث المعرض فوراً
    setRefreshTrigger(prev => {
      const newValue = prev + 1;
      console.log(`🔄 تحديث refreshTrigger من ${prev} إلى ${newValue}`);
      return newValue;
    });

    // إعادة تحميل الإحصائيات
    loadInitialData();

    // عرض رسالة النجاح
    setSuccessMessage(`تم رفع الصورة "${result.filename}" بنجاح!`);
    setShowSuccessModal(true);

    // تأخير إضافي للتأكد من التحديث (مرة واحدة فقط)
    setTimeout(() => {
      console.log('🔄 تحديث إضافي بعد ثانيتين');
      setRefreshTrigger(prev => prev + 1);
    }, 2000);
  };

  // معالجة حذف الصورة
  const handleImageDelete = (deletedImage: any) => {
    setRefreshTrigger(prev => prev + 1);
    loadInitialData(); // إعادة تحميل الإحصائيات

    // عرض رسالة النجاح
    setSuccessMessage(`تم حذف الصورة "${deletedImage.filename}" بنجاح!`);
    setShowSuccessModal(true);
  };

  // معالجة أخطاء الرفع
  const handleUploadError = (error: string) => {
    setErrorMessage(error);
    console.error('خطأ في رفع الصورة:', error);

    // إزالة رسالة الخطأ تلقائياً بعد 5 ثوانٍ
    setTimeout(() => {
      setErrorMessage('');
    }, 5000);
  };

  // تنظيف الملفات المهجورة
  const handleCleanupOrphanedFiles = async () => {
    try {
      setLoading(true);
      const result = await imageManagementService.cleanupOrphanedFiles(selectedFolder);

      if (result.success) {
        setSuccessMessage(`تم تنظيف ${result.total_cleaned} ملف مهجور بنجاح!`);
        setShowSuccessModal(true);
        setRefreshTrigger(prev => prev + 1);
        loadInitialData();
      } else {
        setErrorMessage(result.error || 'فشل في تنظيف الملفات المهجورة');
      }
    } catch (error) {
      console.error('خطأ في تنظيف الملفات:', error);
      setErrorMessage('خطأ في تنظيف الملفات المهجورة');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-xl mb-6 overflow-hidden card-subtle-border">
        <div className="bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center p-4 sm:p-6 gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <button
                onClick={() => navigate('/')}
                className="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-xl p-3 hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-200 ease-in-out shadow-lg hover:shadow-xl flex-shrink-0 border-2 border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 ml-3 sm:ml-4"
                title="العودة للرئيسية"
              >
                <FaArrowLeft className="text-sm" />
              </button>
              <div className="mr-3 sm:mr-4 min-w-0 flex-1">
                <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FiImage className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                  <span className="truncate">إدارة الصور</span>
                </h1>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 hidden sm:block">
                  رفع وإدارة صور المنتجات والفئات والعلامات التجارية
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3 sm:gap-4 flex-wrap lg:flex-nowrap">
              <button
                onClick={() => loadInitialData()}
                className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-3 rounded-xl hover:bg-white/50 dark:hover:bg-gray-700/50 transition-all duration-200 ease-in-out backdrop-blur-sm border-2 border-gray-300 dark:border-gray-600 hover:border-primary-400 dark:hover:border-primary-500 hover:shadow-md"
                title="تحديث"
              >
                <FaSync className={`text-sm ${loading ? "animate-spin" : ""}`} />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      {storageStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
            <div className="flex items-center">
              <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
                <FiImage className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي الملفات</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {storageStats.total_files}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
            <div className="flex items-center">
              <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-xl">
                <FiBarChart className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">حجم التخزين</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {storageStats.total_size_formatted}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
            <div className="flex items-center">
              <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-xl">
                <FiFolder className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">المجلدات</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {storageStats.folders ? Object.keys(storageStats.folders).length : 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
            <div className="flex items-center">
              <div className="p-3 bg-orange-100 dark:bg-orange-900/30 rounded-xl">
                <FiSettings className="w-6 h-6 text-orange-600 dark:text-orange-400" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">الصيغ المدعومة</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {supportedFormats?.supported_extensions?.length || 0}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* اختيار المجلد */}
      <div className="bg-white dark:bg-gray-800 rounded-xl mb-6 p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
          اختر المجلد:
        </label>
        <div className="flex flex-wrap gap-3">
          {folders.map((folder) => (
            <button
              key={folder.value}
              onClick={() => setSelectedFolder(folder.value)}
              className={`
                flex items-center px-4 py-3 rounded-xl border-2 transition-all duration-200 font-medium
                ${selectedFolder === folder.value
                  ? 'bg-primary-600 text-white border-primary-600 shadow-lg'
                  : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600 hover:border-primary-500 hover:shadow-md'
                }
              `}
            >
              {folder.icon}
              <span className="mr-2">{folder.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* التبويبات */}
      <div className="bg-white dark:bg-gray-800 rounded-xl mb-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 space-x-reverse px-6">
            <button
              onClick={() => setActiveTab('gallery')}
              className={`
                py-4 px-2 border-b-2 font-medium text-sm transition-all duration-200 flex items-center
                ${activeTab === 'gallery'
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'
                }
              `}
            >
              <FiImage className="w-5 h-5 ml-2" />
              معرض الصور
            </button>

            <button
              onClick={() => setActiveTab('upload')}
              className={`
                py-4 px-2 border-b-2 font-medium text-sm transition-all duration-200 flex items-center
                ${activeTab === 'upload'
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'
                }
              `}
            >
              <FiUpload className="w-5 h-5 ml-2" />
              رفع الصور
            </button>

            <button
              onClick={() => setActiveTab('stats')}
              className={`
                py-4 px-2 border-b-2 font-medium text-sm transition-all duration-200 flex items-center
                ${activeTab === 'stats'
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'
                }
              `}
            >
              <FiBarChart className="w-5 h-5 ml-2" />
              الإحصائيات
            </button>
          </nav>
        </div>

        {/* محتوى التبويبات */}
        <div className="p-6">
          {activeTab === 'gallery' && (
            <div>
              <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-4">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                  معرض صور {folders.find(f => f.value === selectedFolder)?.label}
                </h2>
                <button
                  onClick={handleCleanupOrphanedFiles}
                  disabled={loading}
                  className="bg-orange-500 text-white px-4 py-3 rounded-xl hover:bg-orange-600 transition-all duration-200 text-sm disabled:opacity-50 font-medium shadow-md hover:shadow-lg"
                >
                  تنظيف الملفات المهجورة
                </button>
              </div>
              
              <ImageGalleryComponent
                folder={selectedFolder}
                selectable={true}
                deletable={true}
                showThumbnails={true}
                onImageDelete={handleImageDelete}
                refreshTrigger={refreshTrigger}
              />
            </div>
          )}

          {activeTab === 'upload' && (
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-6">
                رفع صور جديدة إلى {folders.find(f => f.value === selectedFolder)?.label}
              </h2>
              
              <ImageUploadComponent
                folder={selectedFolder}
                multiple={true}
                generateThumbnails={true}
                maxFiles={20}
                onUploadSuccess={handleUploadSuccess}
                onUploadError={handleUploadError}
              />
            </div>
          )}

          {activeTab === 'stats' && (
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-6">
                إحصائيات التخزين التفصيلية
              </h2>

              {storageStats && storageStats.folders && (
                <div className="space-y-6">
                  {Object.entries(storageStats.folders).map(([folderName, stats]) => (
                    <div key={folderName} className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 border border-gray-200 dark:border-gray-600">
                      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                        مجلد {folders.find(f => f.value === folderName)?.label || folderName}
                      </h3>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                        <div className="text-center">
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">إجمالي الملفات</p>
                          <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                            {stats.total_files}
                          </p>
                        </div>

                        <div className="text-center">
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">الصور الأصلية</p>
                          <p className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                            {stats.images}
                          </p>
                        </div>

                        <div className="text-center">
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">الصور المصغرة</p>
                          <p className="text-2xl font-bold text-secondary-600 dark:text-secondary-400">
                            {stats.thumbnails}
                          </p>
                        </div>

                        <div className="text-center">
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">حجم المجلد</p>
                          <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                            {stats.total_size_formatted}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {supportedFormats && (
                <div className="mt-8 bg-gray-50 dark:bg-gray-700 rounded-xl p-6 border border-gray-200 dark:border-gray-600">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                    معلومات النظام
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">الصيغ المدعومة:</p>
                      <div className="flex flex-wrap gap-2">
                        {supportedFormats.supported_extensions?.map((ext) => (
                          <span
                            key={ext}
                            className="px-3 py-1 bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-200 rounded-full text-sm font-medium"
                          >
                            {ext}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">الحد الأقصى لحجم الملف:</p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        {supportedFormats.max_file_size_formatted}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* نافذة رسالة النجاح */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title="تم بنجاح!"
        message={successMessage}
      />

      {/* عرض رسائل الخطأ */}
      {errorMessage && (
        <div className="fixed bottom-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
          <div className="flex items-center justify-between">
            <span>{errorMessage}</span>
            <button
              onClick={() => setErrorMessage('')}
              className="mr-4 text-white hover:text-gray-200"
            >
              ×
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageManagementPage;
