/**
 * صفحة إدارة المستودعات
 * تعرض قائمة المستودعات مع إمكانيات الإدارة الكاملة
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FiPlus,
  FiEdit,
  FiTrash2,
  FiUser,
  FiStar,
  FiBarChart,
  FiPackage,
  FiSearch
} from 'react-icons/fi';
import { FaArrowLeft, FaSync } from 'react-icons/fa';
import { useWarehouseStore, warehouseSelectors } from '../stores/warehouseStore';
import { Warehouse } from '../services/warehouseService';
import { useAuthStore } from '../stores/authStore';
import CreateWarehouseModal from '../components/warehouse/CreateWarehouseModal';
import EditWarehouseModal from '../components/warehouse/EditWarehouseModal';
import WarehouseDetailsModal from '../components/warehouse/WarehouseDetailsModal';

const WarehousesPage: React.FC = () => {
  const navigate = useNavigate();
  const { isAuthenticated, isInitialized } = useAuthStore();
  const {
    warehouses,
    warehouseSummary,
    loading,
    error,
    fetchWarehouses,
    fetchWarehouseSummary,
    deleteWarehouse,
    setMainWarehouse,
    clearError
  } = useWarehouseStore();

  const [searchQuery, setSearchQuery] = useState('');
  const [showInactive, setShowInactive] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [selectedWarehouse, setSelectedWarehouse] = useState<Warehouse | null>(null);

  useEffect(() => {
    // Load warehouses and summary only when user is authenticated
    if (isAuthenticated && isInitialized) {
      fetchWarehouses(showInactive);
      fetchWarehouseSummary();
    }
  }, [isAuthenticated, isInitialized, showInactive, fetchWarehouses, fetchWarehouseSummary]);

  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => clearError(), 5000);
      return () => clearTimeout(timer);
    }
  }, [error, clearError]);

  // Handle refresh
  const handleRefresh = async () => {
    try {
      console.log('🔄 [WarehousesPage] تحديث البيانات...');
      if (isAuthenticated && isInitialized) {
        await Promise.all([
          fetchWarehouses(showInactive),
          fetchWarehouseSummary()
        ]);
      }
      console.log('✅ [WarehousesPage] تم تحديث البيانات');
    } catch (error) {
      console.error('❌ [WarehousesPage] خطأ في تحديث البيانات:', error);
    }
  };

  // فلترة المستودعات حسب البحث
  const filteredWarehouses = warehouseSelectors.searchWarehouses(
    { warehouses } as any, 
    searchQuery
  );

  const handleDeleteWarehouse = async (warehouse: Warehouse) => {
    if (warehouse.is_main) {
      alert('لا يمكن حذف المستودع الرئيسي');
      return;
    }

    const success = await deleteWarehouse(warehouse.id);
    if (success) {
      // تم الحذف بنجاح
      console.log('تم حذف المستودع بنجاح');
    }
  };

  const handleSetMainWarehouse = async (warehouse: Warehouse) => {
    if (warehouse.is_main) return;
    
    const success = await setMainWarehouse(warehouse.id);
    if (success) {
      // تحديث البيانات
      fetchWarehouses(showInactive);
    }
  };

  const getCapacityColor = (warehouse: Warehouse) => {
    if (!warehouse.capacity_limit) return 'bg-gray-200';
    
    const percentage = (warehouse.current_capacity / warehouse.capacity_limit) * 100;
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const getCapacityPercentage = (warehouse: Warehouse) => {
    if (!warehouse.capacity_limit) return 0;
    return Math.round((warehouse.current_capacity / warehouse.capacity_limit) * 100);
  };

  const handleEditWarehouse = (warehouse: Warehouse) => {
    setSelectedWarehouse(warehouse);
    setIsEditModalOpen(true);
  };

  const handleViewDetails = (warehouse: Warehouse) => {
    setSelectedWarehouse(warehouse);
    setIsDetailsModalOpen(true);
  };

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
    setSelectedWarehouse(null);
  };

  const handleCloseDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedWarehouse(null);
  };

  const handleEditSuccess = async () => {
    try {
      await fetchWarehouses(showInactive);
      await fetchWarehouseSummary();
    } catch (e) {
      console.error('حدث خطأ أثناء تحديث بيانات المستودعات بعد التعديل:', e);
    }
  };

  return (
    <div className="container mx-auto px-4 py-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-xl mb-6 overflow-hidden card-subtle-border">
        <div className="bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center p-4 sm:p-6 gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <button
                onClick={() => navigate('/')}
                className="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-xl p-3 hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-200 ease-in-out shadow-lg hover:shadow-xl flex-shrink-0 border-2 border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 ml-3 sm:ml-4"
                title="العودة للرئيسية"
              >
                <FaArrowLeft className="text-sm" />
              </button>
              <div className="mr-3 sm:mr-4 min-w-0 flex-1">
                <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FiPackage className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                  <span className="truncate">إدارة المستودعات</span>
                </h1>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 hidden sm:block">
                  إدارة وتتبع جميع المستودعات والمخزون
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3 sm:gap-4 flex-wrap lg:flex-nowrap">
              <button
                onClick={handleRefresh}
                className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-3 rounded-xl hover:bg-white/50 dark:hover:bg-gray-700/50 transition-all duration-200 ease-in-out backdrop-blur-sm border-2 border-gray-300 dark:border-gray-600 hover:border-primary-400 dark:hover:border-primary-500 hover:shadow-md"
                title="تحديث"
              >
                <FaSync className={`text-sm ${loading ? "animate-spin" : ""}`} />
              </button>
              <button
                onClick={() => setIsCreateModalOpen(true)}
                className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-3 rounded-xl flex items-center gap-2 transition-all duration-200 ease-in-out shadow-lg hover:shadow-xl border-2 border-primary-600 hover:border-primary-700 text-sm font-medium"
              >
                <FiPlus className="w-4 h-4" />
                إضافة مستودع
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      {warehouseSummary && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
            <div className="flex items-center">
              <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
                <FiPackage className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي المستودعات</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {warehouseSummary.total_warehouses}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
            <div className="flex items-center">
              <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-xl">
                <FiBarChart className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">المستودعات النشطة</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {warehouseSummary.active_warehouses}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
            <div className="flex items-center">
              <div className="p-3 bg-orange-100 dark:bg-orange-900/30 rounded-xl">
                <FiBarChart className="w-6 h-6 text-orange-600 dark:text-orange-400" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">السعة المستخدمة</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {warehouseSummary.capacity.usage_percentage}%
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
            <div className="flex items-center">
              <div className="p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-xl">
                <FiStar className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">المستودع الرئيسي</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  {warehouseSummary.main_warehouse?.name || 'غير محدد'}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* الفلاتر والبحث */}
      <div className="bg-white dark:bg-gray-800 rounded-xl mb-6 border border-gray-200 dark:border-gray-700 card-subtle-border">
        <div className="p-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              قائمة المستودعات
            </h2>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 w-4 h-4" />
                <input
                  type="text"
                  placeholder="البحث في المستودعات..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full sm:w-64 pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
                />
              </div>

              <label className="flex items-center gap-2 px-4 py-3 bg-gray-50 dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600">
                <input
                  type="checkbox"
                  checked={showInactive}
                  onChange={(e) => setShowInactive(e.target.checked)}
                  className="rounded border-gray-300 dark:border-gray-600 text-primary-600 focus:ring-primary-500 bg-white dark:bg-gray-600"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300 font-medium">عرض غير النشطة</span>
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* رسالة الخطأ */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-6 py-4 rounded-xl mb-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="mr-3">
              <p className="text-sm font-medium">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* شاشة التحميل */}
      {loading && (
        <div className="flex justify-center items-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">جاري تحميل المستودعات...</p>
          </div>
        </div>
      )}

      {/* شبكة المستودعات */}
      {!loading && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {filteredWarehouses.map((warehouse) => (
            <div
              key={warehouse.id}
              className={`bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-200 overflow-hidden flex flex-col h-full ${
                !warehouse.is_active ? 'opacity-75' : ''
              }`}
            >
              {/* Content */}
              <div className="flex-grow p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${
                      warehouse.is_main
                        ? 'bg-warning-100 dark:bg-warning-900/30 text-warning-600 dark:text-warning-400'
                        : warehouse.is_active
                          ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400'
                    }`}>
                      {warehouse.is_main ? (
                        <FiStar className="w-5 h-5" />
                      ) : (
                        <FiPackage className="w-5 h-5" />
                      )}
                    </div>
                    <div>
                      <h3 className="text-base font-semibold text-gray-900 dark:text-gray-100">
                        {warehouse.name}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400 font-mono">
                        {warehouse.code}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-1">
                    <button
                      onClick={() => handleEditWarehouse(warehouse)}
                      className="p-2 text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
                      title="تعديل"
                    >
                      <FiEdit className="w-4 h-4" />
                    </button>
                    {!warehouse.is_main && (
                      <button
                        onClick={() => handleDeleteWarehouse(warehouse)}
                        className="p-2 text-gray-400 hover:text-danger-600 dark:hover:text-danger-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
                        title="حذف"
                      >
                        <FiTrash2 className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>

                {/* Status Badges */}
                <div className="flex items-center gap-2 mb-4">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    warehouse.is_active
                      ? 'bg-success-100 text-success-700 dark:bg-success-900/30 dark:text-success-400'
                      : 'bg-danger-100 text-danger-700 dark:bg-danger-900/30 dark:text-danger-400'
                  }`}>
                    <div className={`w-1.5 h-1.5 rounded-full ml-1 ${
                      warehouse.is_active ? 'bg-success-500' : 'bg-danger-500'
                    }`}></div>
                    {warehouse.is_active ? 'نشط' : 'غير نشط'}
                  </span>

                  {warehouse.is_main && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-warning-100 text-warning-700 dark:bg-warning-900/30 dark:text-warning-400">
                      <FiStar className="w-3 h-3 ml-1" />
                      رئيسي
                    </span>
                  )}
                </div>

                {/* Info Section */}
                <div className="space-y-3 mb-4">
                  {/* Manager */}
                  <div className="flex items-center gap-2">
                    <FiUser className="w-3.5 h-3.5 text-gray-400 dark:text-gray-500 flex-shrink-0" />
                    <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
                      {warehouse.manager_name || 'لم يتم تحديد المدير'}
                    </p>
                  </div>
                </div>

                {/* Capacity */}
                <div className="pt-3 border-t border-gray-100 dark:border-gray-700">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-xs text-gray-500 dark:text-gray-400">السعة التخزينية</span>
                    <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                      {warehouse.capacity_limit ? `${getCapacityPercentage(warehouse)}%` : 'غير محدد'}
                    </span>
                  </div>
                  {warehouse.capacity_limit ? (
                    <>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                        <div
                          className={`h-1.5 rounded-full ${getCapacityColor(warehouse)}`}
                          style={{ width: `${getCapacityPercentage(warehouse)}%` }}
                        />
                      </div>
                      <div className="flex justify-between items-center mt-1">
                        <span className="text-xs text-gray-400 dark:text-gray-500">
                          المستخدم: {warehouse.current_capacity || 0}
                        </span>
                        <span className="text-xs text-gray-400 dark:text-gray-500">
                          الإجمالي: {warehouse.capacity_limit}
                        </span>
                      </div>
                    </>
                  ) : (
                    <div className="text-center py-1">
                      <span className="text-xs text-gray-500 dark:text-gray-400">لم يتم تحديد حد السعة</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Actions */}
              <div className="px-4 py-3 border-t border-gray-100 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/30">
                <div className="flex justify-between items-center">
                  {!warehouse.is_main && warehouse.is_active && (
                    <button
                      onClick={() => handleSetMainWarehouse(warehouse)}
                      className="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium transition-colors"
                    >
                      تعيين كرئيسي
                    </button>
                  )}

                  <button
                    onClick={() => handleViewDetails(warehouse)}
                    className="text-xs text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors mr-auto"
                  >
                    عرض التفاصيل
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* حالة فارغة */}
      {!loading && filteredWarehouses.length === 0 && (
        <div className="text-center py-12">
          <div className="w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
            <FiPackage className="w-10 h-10 text-gray-400 dark:text-gray-500" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
            لا توجد مستودعات
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
            {searchQuery ? 'لم يتم العثور على مستودعات تطابق البحث' : 'ابدأ بإضافة مستودع جديد لإدارة المخزون'}
          </p>
          {!searchQuery && (
            <button
              onClick={() => setIsCreateModalOpen(true)}
              className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out shadow-lg hover:shadow-xl border-2 border-primary-600 hover:border-primary-700 font-medium"
            >
              إضافة مستودع جديد
            </button>
          )}
        </div>
      )}

      {/* Modals */}
      <CreateWarehouseModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={async () => {
          await fetchWarehouses(showInactive);
          await fetchWarehouseSummary();
        }}
      />

      {/* Edit Warehouse Modal */}
      <EditWarehouseModal
        isOpen={isEditModalOpen}
        onClose={handleCloseEditModal}
        warehouse={selectedWarehouse}
        onSuccess={handleEditSuccess}
      />

      {/* Warehouse Details Modal */}
      <WarehouseDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={handleCloseDetailsModal}
        warehouse={selectedWarehouse}
      />
    </div>
  );
};

export default WarehousesPage;
