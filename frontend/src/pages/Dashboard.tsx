import React, { useEffect, useState, useMemo } from 'react';
import { useDashboardStore } from '../stores/dashboardStore';
import { useAuthStore } from '../stores/authStore';
import {
  FiShoppingCart as FaCashRegister,
  FiPackage as FaBox,
  FiSettings as FaCog,
  FiUsers,
  FiShoppingCart,

  FiEdit,
  FiPlus,
  FiArrowRight,
  FiPackage as FaBoxOpen,

  FiPrinter as FaPrint,
  FiSearch,

  FiFileText as FaReceipt,
  FiCheck,
  FiBarChart as FaChartBar,
  FiCalendar as FaCalendarDay,
  FiCalendar as FaCalendarWeek,
  FiCalendar as FaCalendarAlt,
  FiCalendar,
  FiTrendingUp as FaChartLine,
  FiAward as FaTrophy,
  FiEye,

  FiDollarSign as FaMoneyBillWave,
  FiCircle as FaCoins
} from 'react-icons/fi';
import { useNavigate, Link } from 'react-router-dom';
// استيراد مكتبة ApexCharts
import ReactApexChart from 'react-apexcharts';
// The import is unused and can be removed
// استيراد خدمة التعامل مع الوقت والتاريخ
import {
  getArabicMonthAbbr,
  getPreviousDays,
  getPreviousMonths,
  fetchDateTimeSettings
} from '../services/dateTimeService';
import useSettings from '../hooks/useSettings';
// تم إزالة useDateTimeFormatters لتجنب infinite re-renders
import FormattedCurrency from '../components/FormattedCurrency';
import { useCurrencySettings } from '../utils/currencyUtils';
import { FormattedDate, FormattedTime } from '../components/FormattedDateTime';
import { CompactStatCard } from '../components/CompactNumberDisplay';
import PeriodButtons from '../components/PeriodButtons';
import { useComparisonIndicators } from '../hooks/useComparisonIndicators';
import WelcomeContainer from '../components/WelcomeContainer';

// Sales Chart Component
const SalesChart = () => {
  const {
    salesTrends,
    isTrendsLoading,
    fetchSalesTrends
  } = useDashboardStore();
  const { user } = useAuthStore();
  // تم إزالة formatDate و formatTime لتجنب infinite re-renders
  const { formatCurrency } = useCurrencySettings();
  const [processedChartData, setProcessedChartData] = useState<any[]>([]);
  const [formattedTimeLabels, setFormattedTimeLabels] = useState<{[key: string]: string}>({});
  // حالة منفصلة لمخطط المبيعات لا تؤثر على بطاقات الإحصائيات
  const [chartDateRange, setChartDateRange] = useState<'today' | 'week' | 'month' | 'year'>('today');



  // Process the sales trends data to ensure it's in the correct order and time zone
  const processChartData = async () => {
    // If no data is available, return empty array
    if (!salesTrends || salesTrends.length === 0) {
      return [];
    }

    try {
      // First, normalize the data to ensure all items have a valid amount
      const normalizedData = salesTrends.map(item => {
        // Ensure amount is a valid number
        let amount = 0;

        // Check if amount exists and is a number
        if (typeof item.amount === 'number') {
          amount = item.amount;
        }
        // If amount doesn't exist but total does, use total
        else if (typeof (item as any).total === 'number') {
          amount = (item as any).total;
        }
        // Otherwise, default to 0
        else {
          amount = 0;
        }

        return {
          date: item.date,
          amount: amount
        };
      });



      // For day period, ensure the data is in chronological order
      if (chartDateRange === 'today') {
        // Create a map of hours to amounts
        const hourMap = new Map<number, number>();

        // Initialize all 24 hours with 0 (to ensure we have all hours)
        for (let i = 0; i <= 23; i++) {
          hourMap.set(i, 0);
        }

        // Fill in the actual data
        normalizedData.forEach(item => {
          try {
            if (!item.date) {
              return;
            }

            // Parse the hour from the date string (format: "HH:00")
            const hourStr = item.date; // The API already returns the hour in "HH:00" format
            const hour = parseInt(hourStr.split(':')[0]);

            if (!isNaN(hour) && hour >= 0 && hour <= 23) {
              // Simply add the amount to the corresponding hour
              hourMap.set(hour, item.amount);
            }
          } catch (error) {
            // Silently handle errors
          }
        });

        // Convert the map to an array of objects, include all 24 hours
        const processedData = Array.from(hourMap.entries())
          .map(([hour, amount]) => ({
            date: `${hour.toString().padStart(2, '0')}:00`,
            amount
          }));

        // Sort by hour
        processedData.sort((a, b) => {
          const hourA = parseInt(a.date.split(':')[0]);
          const hourB = parseInt(b.date.split(':')[0]);
          return hourA - hourB;
        });



        return processedData;
      } else if (chartDateRange === 'week' || chartDateRange === 'month') {
        // For week and month periods, we need to ensure we have all days in the range
        // and that they are in chronological order

        // Create a map of dates to amounts
        const dateMap = new Map<string, number>();

        // Get the appropriate number of previous days
        const days = chartDateRange === 'week' ? 7 : 30;

        // Generate date range using our utility function
        const dateRange = getPreviousDays(days);

        // Initialize all dates with 0
        dateRange.forEach(date => {
          dateMap.set(date, 0);
        });

        // Fill in the actual data
        normalizedData.forEach((item) => {
          try {
            if (!item.date) {
              return;
            }

            // Ensure the date is in the correct format (YYYY-MM-DD)
            let dateKey = item.date;

            // Check if the date is in the expected range
            if (dateMap.has(dateKey)) {
              dateMap.set(dateKey, item.amount);
            } else {
              // Try to parse and format the date to ISO format
              try {
                const dateObj = new Date(item.date);
                if (!isNaN(dateObj.getTime())) {
                  // Use ISO format for chart data (YYYY-MM-DD)
                  const isoDate = dateObj.toISOString().split('T')[0];
                  if (dateMap.has(isoDate)) {
                    dateMap.set(isoDate, item.amount);
                  }
                }
              } catch (parseError) {
                // Silently handle errors
              }
            }
          } catch (error) {
            // Silently handle errors
          }
        });

        // Convert the map to an array of objects
        const processedData = Array.from(dateMap.entries()).map(([date, amount]) => ({
          date,
          amount
        }));

        // Sort by date
        processedData.sort((a, b) => {
          return a.date.localeCompare(b.date);
        });



        return processedData;
      } else if (chartDateRange === 'year') {
        // For year period, we need to ensure we have all months in the range
        // and that they are in chronological order

        // Create a map of year-month to amounts
        const monthMap = new Map<string, number>();

        // Get the previous 12 months
        const monthRange = getPreviousMonths(12);

        // Initialize all months with 0
        monthRange.forEach(month => {
          monthMap.set(month, 0);
        });

        // Fill in the actual data
        normalizedData.forEach((item) => {
          try {
            if (!item.date) {
              return;
            }

            // Ensure the date is in the correct format (YYYY-MM)
            let monthKey = item.date;

            // Check if the month is in the expected range
            if (monthMap.has(monthKey)) {
              monthMap.set(monthKey, item.amount);
            } else {
              // Try to parse and format the date to YYYY-MM format
              try {
                const dateObj = new Date(item.date);
                if (!isNaN(dateObj.getTime())) {
                  // Use YYYY-MM format for chart data
                  const year = dateObj.getFullYear();
                  const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
                  const yearMonth = `${year}-${month}`;
                  if (monthMap.has(yearMonth)) {
                    monthMap.set(yearMonth, item.amount);
                  }
                }
              } catch (parseError) {
                // Silently handle errors
              }
            }
          } catch (error) {
            // Silently handle errors
          }
        });

        // Convert the map to an array of objects
        const processedData = Array.from(monthMap.entries()).map(([month, amount]) => ({
          date: month,
          amount
        }));

        // Sort by year-month
        processedData.sort((a, b) => {
          return a.date.localeCompare(b.date);
        });



        return processedData;
      }

      // For other periods, return normalized data
      return normalizedData;
    } catch (error) {
      return [];
    }
  };

  // useEffect لتنسيق الأوقات مسبقاً للمخطط
  useEffect(() => {
    const formatTimeLabels = async () => {
      if (chartDateRange === 'today') {
        const timeLabels: {[key: string]: string} = {};

        // جلب إعدادات التنسيق فقط (بدون تحويل منطقة زمنية إضافي)
        try {
          const settings = await fetchDateTimeSettings();

          // تنسيق جميع الساعات من 0 إلى 23 حسب تنسيق الوقت المحدد فقط
          for (let hour = 0; hour <= 23; hour++) {
            const hourStr = `${hour.toString().padStart(2, '0')}:00`;
            try {
              // تطبيق تنسيق الوقت حسب الإعدادات فقط (بدون تحويل منطقة زمنية)
              let formattedTime = '';
              switch (settings.timeFormat) {
                case '24h':
                  formattedTime = `${hour.toString().padStart(2, '0')}${settings.timeSeparator}00`;
                  break;
                case '12h':
                  const hour12 = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
                  const ampm = hour >= 12 ? 'PM' : 'AM';
                  formattedTime = `${hour12}${settings.timeSeparator}00 ${ampm}`;
                  break;
                case '12h_ar':
                  const hour12Ar = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
                  const ampmAr = hour >= 12 ? 'م' : 'ص';
                  formattedTime = `${hour12Ar}${settings.timeSeparator}00 ${ampmAr}`;
                  break;
                default:
                  formattedTime = `${hour.toString().padStart(2, '0')}${settings.timeSeparator}00`;
              }

              timeLabels[hourStr] = formattedTime;
            } catch (error) {
              console.error(`Error formatting time for hour ${hour}:`, error);
              timeLabels[hourStr] = hourStr; // fallback إلى التنسيق الأصلي
            }
          }

          setFormattedTimeLabels(timeLabels);
        } catch (error) {
          console.error('Error getting datetime settings:', error);
          // fallback إلى تنسيق افتراضي
          const fallbackLabels: {[key: string]: string} = {};
          for (let hour = 0; hour <= 23; hour++) {
            const hourStr = `${hour.toString().padStart(2, '0')}:00`;
            fallbackLabels[hourStr] = hourStr;
          }
          setFormattedTimeLabels(fallbackLabels);
        }
      } else {
        setFormattedTimeLabels({});
      }
    };

    formatTimeLabels();
  }, [chartDateRange]);

  // useEffect لمعالجة بيانات المخطط
  useEffect(() => {
    const processData = async () => {
      try {
        const data = await processChartData();
        setProcessedChartData(data);
      } catch (error) {
        console.error('Error processing chart data:', error);
        setProcessedChartData([]);
      }
    };

    if (salesTrends && salesTrends.length > 0) {
      processData();
    } else {
      setProcessedChartData([]);
    }
  }, [salesTrends, chartDateRange]);

  // Generate chart data
  const chartData = processedChartData;

  // إنشاء فترات مخصصة لمكون SalesChart
  const dashboardPeriods = [
    { value: 'today', label: 'اليوم', days: 1 },
    { value: 'week', label: 'أسبوع', days: 7 },
    { value: 'month', label: 'شهر', days: 30 },
    { value: 'year', label: 'سنة', days: 365 }
  ];

  const handlePeriodChange = async (periodValue: string, _days: number) => {
    const period = periodValue as 'today' | 'week' | 'month' | 'year';
    // تحديث حالة المخطط فقط، بدون تأثير على بطاقات الإحصائيات
    setChartDateRange(period);

    // Map the period to the API parameter
    let apiPeriod: 'day' | 'week' | 'month' | 'year';
    switch (period) {
      case 'today':
        apiPeriod = 'day';
        break;
      case 'week':
        apiPeriod = 'week';
        break;
      case 'month':
        apiPeriod = 'month';
        break;
      case 'year':
        apiPeriod = 'year';
        break;
      default:
        apiPeriod = 'day';
    }

    try {
      // Fetch the data for the selected period - هذا لا يؤثر على بطاقات الإحصائيات
      await fetchSalesTrends(apiPeriod);
    } catch (error) {
      // Silently handle errors
    }
  };

  const getPeriodLabel = () => {
    switch (chartDateRange) {
      case 'today':
        return 'مبيعات اليوم';
      case 'week':
        return 'مبيعات الأسبوع';
      case 'month':
        return 'مبيعات الشهر';
      case 'year':
        return 'مبيعات السنة';
      default:
        return 'مبيعات الأسبوع';
    }
  };

  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Function to get the current period icon
  const getCurrentPeriodIcon = () => {
    switch (chartDateRange) {
      case 'today':
        return <FaCalendarDay className="ml-1.5" />;
      case 'week':
        return <FaCalendarWeek className="ml-1.5" />;
      case 'month':
        return <FaCalendarAlt className="ml-1.5" />;
      case 'year':
        return <FiCalendar className="ml-1.5" />;
      default:
        return <FaCalendarDay className="ml-1.5" />;
    }
  };

  // Function to get the current period text
  const getCurrentPeriodText = () => {
    switch (chartDateRange) {
      case 'today':
        return 'اليوم';
      case 'week':
        return 'الأسبوع';
      case 'month':
        return 'الشهر';
      case 'year':
        return 'السنة';
      default:
        return 'اليوم';
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 mb-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold text-gray-800 dark:text-gray-100">{getPeriodLabel()}</h2>

        {/* Desktop view - PeriodButtons component */}
        <div className="hidden md:block">
          <PeriodButtons
            selectedPeriod={chartDateRange}
            onPeriodChange={handlePeriodChange}
            customPeriods={dashboardPeriods}
            size="md"
          />
        </div>

        {/* Mobile view - Dropdown */}
        <div className="md:hidden relative">
          <button
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            className={`flex items-center px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
              'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300'
            }`}
          >
            {getCurrentPeriodIcon()}
            <span>{getCurrentPeriodText()}</span>
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={isDropdownOpen ? "M5 15l7-7 7 7" : "M19 9l-7 7-7-7"}></path>
            </svg>
          </button>

          {isDropdownOpen && (
            <div className="absolute left-0 mt-2 w-40 bg-white dark:bg-gray-800 rounded-lg shadow-lg z-10 border border-gray-200 dark:border-gray-700">
              <button
                onClick={() => {
                  handlePeriodChange('today', 1);
                  setIsDropdownOpen(false);
                }}
                className={`flex items-center px-3 py-2 w-full text-right text-sm transition-colors ${
                  chartDateRange === 'today'
                    ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <FaCalendarDay className="ml-1.5" />
                <span>اليوم</span>
              </button>
              <button
                onClick={() => {
                  handlePeriodChange('week', 7);
                  setIsDropdownOpen(false);
                }}
                className={`flex items-center px-3 py-2 w-full text-right text-sm transition-colors ${
                  chartDateRange === 'week'
                    ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <FaCalendarWeek className="ml-1.5" />
                <span>الأسبوع</span>
              </button>
              <button
                onClick={() => {
                  handlePeriodChange('month', 30);
                  setIsDropdownOpen(false);
                }}
                className={`flex items-center px-3 py-2 w-full text-right text-sm transition-colors ${
                  chartDateRange === 'month'
                    ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <FaCalendarAlt className="ml-1.5" />
                <span>الشهر</span>
              </button>
              <button
                onClick={() => {
                  handlePeriodChange('year', 365);
                  setIsDropdownOpen(false);
                }}
                className={`flex items-center px-3 py-2 w-full text-right text-sm transition-colors ${
                  chartDateRange === 'year'
                    ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <FiCalendar className="ml-1.5" />
                <span>السنة</span>
              </button>
            </div>
          )}
        </div>
      </div>

      {isTrendsLoading ? (
        <div className="flex justify-center items-center h-64 md:h-80">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      ) : chartData && chartData.length > 0 ? (
        <div>
          {/* Chart Legend - Mobile Only */}
          <div className="md:hidden flex justify-center items-center mb-3 bg-gray-50 dark:bg-gray-700 rounded-lg p-2">
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-primary-600 mr-1"></div>
              <span className="text-xs text-gray-700 dark:text-gray-300">المبيعات</span>
            </div>
          </div>

          {/* Chart Container - Taller on mobile for better visibility */}
          <div className="h-80 md:h-64">
            <ReactApexChart
              type="area"
              height="100%"
              width="100%"
              series={[
                {
                  name: 'المبيعات',
                  data: chartData.map(item => {
                    // Ensure amount is a number
                    const amount = typeof item.amount === 'number' ? item.amount : 0;
                    // Round to 2 decimal places for better display
                    const roundedAmount = Math.round(amount * 100) / 100;
                    console.log(`Chart data point: ${item.date} = ${roundedAmount}`);
                    return roundedAmount;
                  })
                }
              ]}
              options={{
                chart: {
                  type: 'area',
                  fontFamily: 'almarai, sans-serif',
                  toolbar: {
                    show: false
                  },
                  zoom: {
                    enabled: false
                  },
                  animations: {
                    enabled: true,
                    speed: 800,
                    animateGradually: {
                      enabled: true,
                      delay: 150
                    },
                    dynamicAnimation: {
                      enabled: true,
                      speed: 350
                    }
                  },
                  background: 'transparent',
                  locales: [{
                    name: 'ar',
                    options: {
                      months: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                      shortMonths: ['ي', 'ف', 'م', 'أ', 'م', 'ي', 'ي', 'أ', 'س', 'أ', 'ن', 'د'],
                      days: ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
                      shortDays: ['أحد', 'إثن', 'ثلا', 'أرب', 'خمي', 'جمع', 'سبت']
                    }
                  }],
                  defaultLocale: 'ar'
                },
                colors: ['#4F46E5'],
                fill: {
                  type: 'gradient',
                  gradient: {
                    shadeIntensity: 1,
                    opacityFrom: 0.4,
                    opacityTo: 0.1,
                    stops: [0, 90, 100]
                  }
                },
                dataLabels: {
                  enabled: false
                },
                stroke: {
                  curve: 'smooth',
                  width: 3
                },
                grid: {
                  borderColor: document.documentElement.classList.contains('dark')
                    ? 'rgba(107, 114, 128, 0.2)'
                    : 'rgba(107, 114, 128, 0.1)',
                  row: {
                    colors: ['transparent', 'transparent'],
                    opacity: 0.5
                  },
                  padding: {
                    top: 0,
                    right: 0,
                    bottom: 0,
                    left: 10
                  },
                  xaxis: {
                    lines: {
                      show: true
                    }
                  },
                  yaxis: {
                    lines: {
                      show: true
                    }
                  }
                },
                markers: {
                  size: 4,
                  colors: ['#fff'],
                  strokeColors: '#4F46E5',
                  strokeWidth: 2,
                  hover: {
                    size: 7
                  }
                },
                xaxis: {
                  categories: chartData.map(item => item.date),
                  labels: {
                    style: {
                      colors: '#6B7280',
                      fontSize: window.innerWidth < 768 ? '10px' : '12px'
                    },
                    formatter: (value: string) => {
                      if (!value) return '';

                      try {
                        if (chartDateRange === 'today') {
                          // For day period, use formatted time from settings
                          return formattedTimeLabels[value] || value;
                        } else if (chartDateRange === 'week' || chartDateRange === 'month') {
                          // For week/month periods, format the date to show day only
                          // Extract the day from the date (e.g., "2023-05-15" -> "15")
                          if (value.includes('-')) {
                            const parts = value.split('-');
                            if (parts.length === 3) {
                              return parts[2]; // Return only the day
                            }
                          }
                          return value;
                        } else {
                          // For year period, format the month to show month abbreviation
                          // Convert YYYY-MM to month abbreviation (e.g., "2023-05" -> "مايو")
                          if (value.includes('-')) {
                            const parts = value.split('-');
                            if (parts.length === 2) {
                              const monthIndex = parseInt(parts[1]);
                              if (!isNaN(monthIndex) && monthIndex >= 1 && monthIndex <= 12) {
                                return getArabicMonthAbbr(monthIndex);
                              }
                            }
                          }
                          return value;
                        }
                      } catch (error) {
                        console.error('Error formatting chart label:', error);
                        return value;
                      }
                    }
                  },
                  axisBorder: {
                    show: true,
                    color: 'rgba(107, 114, 128, 0.3)'
                  },
                  axisTicks: {
                    show: true,
                    color: 'rgba(107, 114, 128, 0.3)'
                  },
                  tickAmount: chartDateRange === 'today' ? 12 : undefined
                },
                yaxis: {
                  labels: {
                    style: {
                      colors: '#6B7280',
                      fontSize: window.innerWidth < 768 ? '10px' : '12px'
                    },
                    formatter: (value: number) => {
                      return value.toFixed(0);
                    }
                  }
                },
                tooltip: {
                  enabled: true,
                  shared: false,
                  intersect: false,
                  theme: document.documentElement.classList.contains('dark') ? 'dark' : 'light',
                  style: {
                    fontSize: '13px',
                    fontFamily: 'almarai, sans-serif'
                  },
                  custom: function({ series, seriesIndex, dataPointIndex }) {
                    try {
                      if (dataPointIndex < 0 || dataPointIndex >= chartData.length) {
                        return '';
                      }

                      const value = series[seriesIndex][dataPointIndex];
                      const dateValue = chartData[dataPointIndex].date;

                      // تنسيق التاريخ/الوقت
                      let formattedDate = '';
                      if (chartDateRange === 'today') {
                        const formattedTime = formattedTimeLabels[dateValue] || dateValue;
                        formattedDate = `الساعة ${formattedTime}`;
                      } else if (chartDateRange === 'week' || chartDateRange === 'month') {
                        if (dateValue && dateValue.includes('-')) {
                          const parts = dateValue.split('-');
                          if (parts.length === 3) {
                            const year = parts[0];
                            const month = parseInt(parts[1]);
                            const day = parseInt(parts[2]);
                            if (!isNaN(month) && !isNaN(day) && month >= 1 && month <= 12 && day >= 1 && day <= 31) {
                              const arabicMonths = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
                              formattedDate = `${day} ${arabicMonths[month - 1]} ${year}`;
                            }
                          }
                        } else {
                          formattedDate = dateValue || '';
                        }
                      } else {
                        if (dateValue && dateValue.includes('-')) {
                          const parts = dateValue.split('-');
                          if (parts.length === 2) {
                            const year = parts[0];
                            const month = parseInt(parts[1]);
                            if (!isNaN(month) && month >= 1 && month <= 12) {
                              const arabicMonths = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
                              formattedDate = `${arabicMonths[month - 1]} ${year}`;
                            }
                          }
                        } else {
                          formattedDate = dateValue || '';
                        }
                      }

                      // تنسيق المبلغ
                      const formattedValue = formatCurrency(value);

                      const isDark = document.documentElement.classList.contains('dark');

                      return `
                        <div style="
                          background: ${isDark ? 'rgba(31, 41, 55, 0.95)' : 'rgba(255, 255, 255, 0.96)'};
                          border: 1px solid ${isDark ? 'rgba(55, 65, 81, 0.3)' : 'rgba(229, 231, 235, 0.4)'};
                          border-radius: 8px;
                          padding: 8px 12px;
                          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                          backdrop-filter: blur(8px);
                          direction: rtl;
                          text-align: right;
                          font-family: 'almarai', sans-serif;
                          min-width: 120px;
                        ">
                          <div style="
                            color: ${isDark ? '#9CA3AF' : '#6B7280'};
                            font-size: 11px;
                            margin-bottom: 4px;
                            font-weight: 500;
                          ">
                            ${formattedDate}
                          </div>
                          <div style="
                            color: ${isDark ? '#F3F4F6' : '#1F2937'};
                            font-size: 13px;
                            font-weight: 600;
                            direction: rtl;
                          ">
                            ${formattedValue}
                          </div>
                        </div>
                      `;
                    } catch (error) {
                      console.error('Error formatting tooltip:', error);
                      return '';
                    }
                  }
                },
                responsive: [
                  {
                    breakpoint: 768,
                    options: {
                      chart: {
                        height: 280
                      },
                      markers: {
                        size: 3
                      },
                      xaxis: {
                        labels: {
                          style: {
                            fontSize: '10px'
                          },
                          rotate: 0,
                          offsetY: 0
                        }
                      },
                      yaxis: {
                        labels: {
                          style: {
                            fontSize: '10px'
                          },
                          formatter: (value: number) => {
                            // Simplify numbers on mobile
                            if (value >= 1000) {
                              return `${(value / 1000).toFixed(1)}K`;
                            }
                            return value.toFixed(0);
                          }
                        }
                      },
                      legend: {
                        position: 'bottom',
                        offsetY: 0
                      }
                    }
                  }
                ]
              }}
            />
          </div>

          {/* Chart Summary - Mobile Only */}
          <div className="md:hidden mt-3 bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm border border-gray-100 dark:border-gray-700">
            <div className="flex justify-between items-center">
              <div>
                <span className="text-xs text-gray-500 dark:text-gray-400">أعلى قيمة</span>
                <p className="font-bold text-primary-700 dark:text-primary-300">
                  {formatCurrency(Math.max(...chartData.map(item => typeof item.amount === 'number' ? item.amount : 0)))}
                </p>
                <span className="text-xs text-gray-500 dark:text-gray-400 block mt-1">
                  {user?.role === 'admin' ? 'جميع المستخدمين' : 'مبيعاتك فقط'}
                </span>
              </div>
              <div>
                <span className="text-xs text-gray-500 dark:text-gray-400">متوسط المبيعات</span>
                <p className="font-bold text-primary-700 dark:text-primary-300">
                  {formatCurrency(Math.round(chartData.reduce((sum, item) => sum + (typeof item.amount === 'number' ? item.amount : 0), 0) / (chartData.length || 1)))}
                </p>
                <span className="text-xs text-gray-500 dark:text-gray-400 block mt-1">
                  {user?.role === 'admin' ? 'جميع المستخدمين' : 'مبيعاتك فقط'}
                </span>
              </div>
              <div>
                <span className="text-xs text-gray-500 dark:text-gray-400">إجمالي المبيعات</span>
                <p className="font-bold text-primary-700 dark:text-primary-300">
                  {formatCurrency(chartData.reduce((sum, item) => sum + (typeof item.amount === 'number' ? item.amount : 0), 0))}
                </p>
                <span className="text-xs text-gray-500 dark:text-gray-400 block mt-1">
                  {user?.role === 'admin' ? 'جميع المستخدمين' : 'مبيعاتك فقط'}
                </span>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex justify-center items-center h-64 md:h-80">
          <div className="text-center text-gray-500 dark:text-gray-400">
            <p>لا توجد بيانات مبيعات لعرضها</p>
            <p className="text-sm mt-2">قم بإجراء بعض المبيعات لرؤية المخطط</p>
          </div>
        </div>
      )}
    </div>
  );
};

interface DashboardStat {
  title: string;
  value: string | number | React.ReactNode;
  icon: React.ReactNode;
  change: number;
  changeText: string;
}

interface DashboardData {
  stats: {
    totalSales: number;
    todaySales: number;
    todayProfits: number;
    totalDebts: number;
    unpaidDebts: number;
    lowStockProducts: number;
  };
  recentSales: {
    id: number;
    date: string;
    total: number;
    items: number;
    payment_method: string;
    amount_paid: number;
    total_amount: number;
    payment_status: string;
  }[];
  topProducts: {
    id: number;
    name: string;
    sold: number;
    revenue: number;
  }[];
  lowStockProductsList: {
    id: number;
    name: string;
    currentStock: number;
    minRequired: number;
    unit: string;
    category: string | null;
  }[];
}

// Helper function to get payment type display text and color
const getPaymentTypeInfo = (paymentMethod: string, paymentStatus: string) => {
  if (paymentMethod === 'آجل') {
    return {
      text: 'آجل',
      colorClass: 'bg-red-50 dark:bg-red-900/30 text-red-700 dark:text-red-300'
    };
  }
  if (paymentStatus === 'partial' || paymentMethod === 'جزئي') {
    return {
      text: 'جزئي',
      colorClass: 'bg-orange-50 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300'
    };
  }
  if (paymentMethod === 'مختلط') {
    return {
      text: 'مختلط',
      colorClass: 'bg-purple-50 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300'
    };
  }
  if (paymentMethod === 'بطاقة' || paymentMethod === 'card') {
    return {
      text: 'بطاقة',
      colorClass: 'bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
    };
  }
  return {
    text: 'نقدي',
    colorClass: 'bg-green-50 dark:bg-green-900/30 text-green-700 dark:text-green-300'
  };
};

const Dashboard: React.FC = () => {
  const {
    fetchStats,
    fetchLowStockProducts,
    fetchSalesTrends,
    updateStockLevel
  } = useDashboardStore();
  const { user } = useAuthStore();
  const { getBooleanSetting } = useSettings();

  // استخدام مؤشرات المقارنة مع الأمس
  const { comparison: comparisonData } = useComparisonIndicators({
    autoRefresh: true,
    refreshInterval: 300, // 5 دقائق
    fetchOnMount: true
  });

  const [showStockModal, setShowStockModal] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<{id: number, name: string, quantity: number} | null>(null);
  const [newQuantity, setNewQuantity] = useState(0);
  const [showActionPanel, setShowActionPanel] = useState(false);
  const [actionType] = useState<'sales' | 'products' | 'reports' | null>(null);

  const [stockUpdateSuccess, setStockUpdateSuccess] = useState(false);
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  // تتبع ما إذا كان هذا هو التحميل الأولي لتفعيل الرسوم المتحركة
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  // تفعيل الرسوم المتحركة عند وجود بيانات (سواء كان التحميل الأولي أو تحديث)
  const shouldEnableAnimation = isInitialLoad && !!dashboardData;
  const navigate = useNavigate();



  useEffect(() => {
    // Fetch real data from API
    const fetchDashboardData = async () => {
      setIsLoading(true);
      try {
        // Fetch stats, low stock products, and sales trends
        await fetchStats();
        await fetchLowStockProducts();
        await fetchSalesTrends('day');

        // Get the data from the store
        const { stats, lowStockProducts } = useDashboardStore.getState();

        // Create dashboard data from real API data
        // معالجة بيانات المبيعات (بدون تنسيق التواريخ مسبقاً)
        const formattedRecentSales = stats.recentSales.map((sale) => {
          return {
            id: sale.id,
            date: sale.createdAt, // تمرير التاريخ الخام بدون تنسيق
            total: sale.total,
            items: sale.items,
            payment_method: sale.payment_method || 'نقدي',
            amount_paid: sale.amount_paid || 0,
            total_amount: sale.total_amount || sale.total,
            payment_status: sale.payment_status || 'paid'
          };
        });

        const realData: DashboardData = {
          stats: {
            totalSales: stats.totalSales || 0,
            todaySales: stats.todaySales || 0, // Use today's sales from the API
            todayProfits: stats.todayProfits || 0, // Add today's profits from the API
            totalDebts: stats.totalDebts || 0,
            unpaidDebts: stats.unpaidDebts || 0,
            lowStockProducts: stats.lowStockCount || 0
          },
          recentSales: formattedRecentSales,
          topProducts: stats.topProducts.map(product => ({
            id: product.id,
            name: product.name,
            sold: product.quantity,
            revenue: product.total
          })),
          lowStockProductsList: lowStockProducts || []
        };

        setDashboardData(realData);
        setIsLoading(false);
        // بعد التحميل الأولي، تعطيل الرسوم المتحركة للتحديثات المستقبلية
        setTimeout(() => setIsInitialLoad(false), 4000); // انتظار انتهاء الرسوم المتحركة
      } catch (error) {
        // Fallback to mock data if API fails
        const mockData: DashboardData = {
          stats: {
            totalSales: 0,
            todaySales: 0,
            todayProfits: 0,
            totalDebts: 0,
            unpaidDebts: 0,
            lowStockProducts: 0
          },
          recentSales: [],
          topProducts: [],
          lowStockProductsList: []
        };
        setDashboardData(mockData);
        setIsLoading(false);
      }
    };

    fetchDashboardData();

    // Clean up any intervals on unmount
    return () => {
      const { refreshInterval } = useDashboardStore.getState();
      if (refreshInterval !== null) {
        window.clearInterval(refreshInterval);
      }
    };
  }, []); // تشغيل مرة واحدة فقط عند تحميل المكون

  // useEffect منفصل لتحميل بيانات المخطط عند بدء التطبيق
  useEffect(() => {
    const loadInitialChartData = async () => {
      try {
        // تحميل بيانات المخطط للفترة الافتراضية (اليوم)
        await fetchSalesTrends('day');
      } catch (error) {
        console.error('Error loading initial chart data:', error);
      }
    };

    loadInitialChartData();
  }, []); // تشغيل مرة واحدة فقط عند تحميل المكون

  useEffect(() => {
    if (stockUpdateSuccess) {
      const timer = setTimeout(() => {
        setStockUpdateSuccess(false);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [stockUpdateSuccess]);



  const handleModuleClick = (moduleId: string) => {
    if (moduleId !== 'dashboard') {
      navigate(`/${moduleId}`);
    }
  };

  const handleUpdateStock = async () => {
    if (selectedProduct && newQuantity >= 0) {
      try {
        // Show loading state
        await updateStockLevel(selectedProduct.id, newQuantity);



        // Close modal and show success message
        setShowStockModal(false);
        setSelectedProduct(null);
        setStockUpdateSuccess(true);

        // Refresh the data to show updated inventory
        fetchLowStockProducts();

      } catch (error) {
        alert("حدث خطأ أثناء تحديث المخزون. يرجى المحاولة مرة أخرى.");
      }
    }
  };



  const handleQuickAction = (action: string) => {
    switch(action) {
      case 'new-sale':
        navigate('/pos');
        break;
      case 'add-product':
        navigate('/products/new');
        break;
      case 'update-inventory':
        navigate('/products?filter=low_stock');
        break;
      case 'view-sales':
        navigate('/sales');
        break;
      case 'print-report':
        navigate('/reports/sales');
        break;
      default:
        break;
    }
    setShowActionPanel(false);
  };



  const handleManageLowStock = () => {
    navigate('/products', {
      state: {
        zeroStock: true
      }
    });
  };

  // Using the formatDateTime function from dateUtils

  // Check if cashier profits should be shown
  const showCashierProfits = getBooleanSetting('show_cashier_profits', true);
  const shouldShowProfits = user?.role === 'admin' || showCashierProfits;

  const mainStats: DashboardStat[] = useMemo(() => {
    return dashboardData ? [
      {
        title: 'إجمالي المبيعات',
        value: `${dashboardData.stats.totalSales.toLocaleString()} عملية`,
        icon: <FiShoppingCart className="text-primary-500" />,
        change: 0,
        changeText: user?.role === 'admin' ? 'إجمالي عمليات البيع لجميع المستخدمين' : 'إجمالي عمليات البيع الخاصة بك'
      },
      {
        title: 'مبيعات اليوم',
        value: <FormattedCurrency amount={dashboardData.stats.todaySales} />,
        icon: <FaReceipt className="text-success-500" />,
        change: 0,
        changeText: user?.role === 'admin' ? 'إجمالي مبيعات اليوم لجميع المستخدمين' : 'إجمالي مبيعات اليوم الخاصة بك'
      },
      {
        title: 'ديون اليوم',
        value: <FormattedCurrency amount={dashboardData.stats.totalDebts} />,
        icon: <FaMoneyBillWave className="text-warning-500" />,
        change: 0,
        changeText: user?.role === 'admin' ? 'ديون اليوم لجميع المستخدمين' : 'ديون اليوم التي أنشأتها'
      },
      // Only show profits if user is admin OR if cashier profits are enabled
      ...(shouldShowProfits ? [{
        title: 'أرباح اليوم',
        value: <FormattedCurrency amount={dashboardData.stats.todayProfits || 0} />,
        icon: <FaCoins className="text-success-600" />,
        change: 0,
        changeText: user?.role === 'admin' ? 'أرباح اليوم لجميع المستخدمين' : 'أرباح اليوم الخاصة بك'
      }] : []),
      {
        title: 'منتجات منخفضة المخزون',
        value: dashboardData.stats.lowStockProducts,
        icon: <FaBox className="text-warning-500" />,
        change: 0,
        changeText: 'تحتاج إلى تجديد المخزون'
      }
    ] : [];
  }, [dashboardData, shouldShowProfits, user?.role]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen dark:bg-gray-900">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 dark:border-primary-400"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Success Notification */}
      {stockUpdateSuccess && (
        <div className="fixed top-5 right-5 bg-white dark:bg-gray-800 text-success-700 dark:text-success-300 px-5 py-4 rounded-xl shadow-lg transition-all z-50 flex items-center border-r-4 border-success-500 dark:border-success-400 animate-fadeIn">
          <div className="bg-success-100 dark:bg-success-900/30 p-2 rounded-xl mr-3">
            <FiCheck className="text-success-600 dark:text-success-300" />
          </div>
          <div>
            <h4 className="font-bold text-secondary-900 dark:text-secondary-100">تم بنجاح</h4>
            <p className="text-secondary-600 dark:text-secondary-400 text-sm">تم تحديث المخزون بنجاح</p>
          </div>
        </div>
      )}



      {/* Welcome Container */}
      <WelcomeContainer />

      {/* Main Stats - Enhanced with Compact Numbers */}
      <div className={`mb-8 transition-all duration-300 ${
        mainStats.length === 4
          ? 'stats-grid-4'
          : mainStats.length === 5
          ? 'stats-grid-5'
          : 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6'
      }`}>
        {dashboardData && (
          <>
            {/* إجمالي المبيعات */}
            <CompactStatCard
              title="إجمالي المبيعات"
              amount={dashboardData.stats.totalSales}
              icon={<FiShoppingCart className="text-primary-500" />}
              showCurrency={false}
              compactThreshold={1000}
              changeText={user?.role === 'admin' ? 'إجمالي عمليات البيع لجميع المستخدمين' : 'إجمالي عمليات البيع الخاصة بك'}
              enableAnimation={shouldEnableAnimation}
              animationDuration={2.5}
              animationDelay={0.2}
            />

            {/* مبيعات اليوم */}
            <CompactStatCard
              title="مبيعات اليوم"
              amount={dashboardData.stats.todaySales}
              icon={<FaReceipt className="text-success-500" />}
              showCurrency={true}
              compactThreshold={1000}
              comparison={comparisonData?.todaySales}
              comparisonText="مقارنة بمبيعات أمس"
              enableAnimation={shouldEnableAnimation}
              animationDuration={2.5}
              animationDelay={0.4}
            />

            {/* ديون اليوم */}
            <CompactStatCard
              title="ديون اليوم"
              amount={Math.abs(dashboardData.stats.totalDebts)}
              icon={<FaMoneyBillWave className="text-warning-500" />}
              showCurrency={true}
              compactThreshold={1000}
              comparison={comparisonData?.todayDebts}
              comparisonText="مقارنة بديون أمس"
              enableAnimation={shouldEnableAnimation}
              animationDuration={2.5}
              animationDelay={0.6}
            />

            {/* أرباح اليوم - إذا كان مسموحاً */}
            {shouldShowProfits && (
              <CompactStatCard
                title="أرباح اليوم"
                amount={dashboardData.stats.todayProfits || 0}
                icon={<FaCoins className="text-success-600" />}
                showCurrency={true}
                compactThreshold={1000}
                comparison={comparisonData?.todayProfits}
                comparisonText="مقارنة بأرباح أمس"
                enableAnimation={shouldEnableAnimation}
                animationDuration={2.5}
                animationDelay={0.8}
              />
            )}

            {/* منتجات منخفضة المخزون */}
            <CompactStatCard
              title="منتجات منخفضة المخزون"
              amount={dashboardData.stats.lowStockProducts}
              icon={<FaBox className="text-warning-500" />}
              showCurrency={false}
              compactThreshold={100}
              changeText="تحتاج إلى تجديد المخزون"
              enableAnimation={shouldEnableAnimation}
              animationDuration={2}
              animationDelay={shouldShowProfits ? 1.0 : 0.8}
            />
          </>
        )}
      </div>

      {/* Navigation Cards - Enhanced Responsive Grid Layout */}
      <div className="mb-8 overflow-visible">
        <div className="nav-cards-container">
          <div
            className="nav-cards-grid"
            data-card-count={user?.role === 'admin' ? '8' : '6'}
          >
          <button
            onClick={() => handleModuleClick('pos')}
            className="touch-card nav-card nav-card-content flex flex-col items-center text-center h-full"
          >
            <div className="nav-card-icon bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-300 rounded-xl mb-2 sm:mb-3">
              <FaCashRegister />
            </div>
            <h3 className="nav-card-title font-bold dark:text-white">نقطة البيع</h3>
            <p className="nav-card-description text-secondary-500 dark:text-secondary-400">إنشاء وإدارة المبيعات</p>
          </button>

          <button
            onClick={() => handleModuleClick('products')}
            className="touch-card nav-card nav-card-content flex flex-col items-center text-center h-full"
          >
            <div className="nav-card-icon bg-secondary-100 dark:bg-secondary-900/30 text-secondary-600 dark:text-secondary-300 rounded-xl mb-2 sm:mb-3">
              <FaBoxOpen />
            </div>
            <h3 className="nav-card-title font-bold dark:text-white">المنتجات</h3>
            <p className="nav-card-description text-secondary-500 dark:text-secondary-400">إدارة المنتجات والمخزون</p>
          </button>

          <button
            onClick={() => handleModuleClick('sales')}
            className="touch-card nav-card nav-card-content flex flex-col items-center text-center h-full"
          >
            <div className="nav-card-icon bg-success-100 dark:bg-success-900/30 text-success-600 dark:text-success-300 rounded-xl mb-2 sm:mb-3">
              <FiShoppingCart />
            </div>
            <h3 className="nav-card-title font-bold dark:text-white">المبيعات</h3>
            <p className="nav-card-description text-secondary-500 dark:text-secondary-400">سجل وتفاصيل المبيعات</p>
          </button>

          <button
            onClick={() => handleModuleClick('customers')}
            className="touch-card nav-card nav-card-content flex flex-col items-center text-center h-full"
          >
            <div className="nav-card-icon bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-300 rounded-xl mb-2 sm:mb-3">
              <FiUsers />
            </div>
            <h3 className="nav-card-title font-bold dark:text-white">العملاء</h3>
            <p className="nav-card-description text-secondary-500 dark:text-secondary-400">إدارة بيانات العملاء</p>
          </button>

          <button
            onClick={() => handleModuleClick('debts')}
            className="touch-card nav-card nav-card-content flex flex-col items-center text-center h-full"
          >
            <div className="nav-card-icon bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-300 rounded-xl mb-2 sm:mb-3">
              <FaMoneyBillWave />
            </div>
            <h3 className="nav-card-title font-bold dark:text-white">المديونية</h3>
            <p className="nav-card-description text-secondary-500 dark:text-secondary-400">إدارة ديون العملاء</p>
          </button>

          {user?.role === 'admin' && (
            <button
              onClick={() => handleModuleClick('users')}
              className="touch-card nav-card nav-card-content flex flex-col items-center text-center h-full"
            >
              <div className="nav-card-icon bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-300 rounded-xl mb-2 sm:mb-3">
                <FiUsers />
              </div>
              <h3 className="nav-card-title font-bold dark:text-white">المستخدمين</h3>
              <p className="nav-card-description text-secondary-500 dark:text-secondary-400">إدارة المستخدمين</p>
            </button>
          )}

          <button
            onClick={() => handleModuleClick('reports')}
            className="touch-card nav-card nav-card-content flex flex-col items-center text-center h-full"
          >
            <div className="nav-card-icon bg-warning-100 dark:bg-warning-900/30 text-warning-600 dark:text-warning-300 rounded-xl mb-2 sm:mb-3">
              <FaChartBar />
            </div>
            <h3 className="nav-card-title font-bold dark:text-white">التقارير</h3>
            <p className="nav-card-description text-secondary-500 dark:text-secondary-400">إحصائيات وتحليلات</p>
          </button>

          {user?.role === 'admin' && (
            <button
              onClick={() => handleModuleClick('settings')}
              className="touch-card nav-card nav-card-content flex flex-col items-center text-center h-full"
            >
              <div className="nav-card-icon bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded-xl mb-2 sm:mb-3">
                <FaCog />
              </div>
              <h3 className="nav-card-title font-bold dark:text-white">الإعدادات</h3>
              <p className="nav-card-description text-secondary-500 dark:text-secondary-400">إعدادات النظام والمتجر</p>
            </button>
          )}
          </div>
        </div>
      </div>

      {/* Sales Chart */}
      <SalesChart />

      {/* Recent Sales & Top Products */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Recent Sales */}
        <div className="touch-card">
          <div className="flex justify-between items-center mb-5">
            <div className="flex items-center">
              <div className="bg-success-100 dark:bg-success-900/30 text-success-600 dark:text-success-300 p-2 rounded-xl ml-3">
                <FaReceipt className="text-xl" />
              </div>
              <h2 className="font-bold text-xl text-secondary-900 dark:text-secondary-100">آخر المبيعات</h2>
            </div>
            <button
              onClick={() => handleModuleClick('sales')}
              className="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium flex items-center gap-1 transition-colors duration-200 px-2 py-1 rounded-lg hover:bg-primary-50 dark:hover:bg-primary-900/20"
            >
              <span>عرض الكل</span>
              <FiArrowRight className="text-sm" />
            </button>
          </div>

          {/* Desktop View */}
          <div className="hidden md:block">
            <div className="overflow-x-auto custom-scrollbar">
              <table className="touch-table">
                <thead>
                  <tr>
                    <th className="text-right">#</th>
                    <th className="text-right">التاريخ</th>
                    <th className="text-right">العناصر</th>
                    <th className="text-right">نوع الدفع</th>
                    <th className="text-right">المبلغ المدفوع</th>
                  </tr>
                </thead>
                <tbody>
                  {dashboardData?.recentSales.map((sale) => (
                    <tr key={sale.id} className="border-b border-gray-200/60 dark:border-gray-700/40 last:border-b-0">
                      <td className="py-3 px-4 text-secondary-900 dark:text-secondary-100">
                        <Link
                          to={`/receipt/${sale.id}`}
                          className="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 hover:underline font-medium flex items-center"
                        >
                          <span className="bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 py-1 px-2 rounded-lg text-sm ml-2">
                            #{sale.id}
                          </span>
                          <FiEye className="text-sm" />
                        </Link>
                      </td>
                      <td className="py-3 px-4 text-secondary-700 dark:text-secondary-300">
                        <div className="flex items-start">
                          <FaCalendarAlt className="text-secondary-500 dark:text-secondary-400 ml-2 mt-0.5" />
                          <div className="flex flex-col">
                            <FormattedDate date={sale.date} className="text-sm font-medium" />
                            <FormattedTime date={sale.date} className="text-xs text-secondary-500 dark:text-secondary-400" />
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4 text-secondary-700 dark:text-secondary-300">
                        <div className="flex items-center">
                          <FaBoxOpen className="text-secondary-500 dark:text-secondary-400 ml-2" />
                          <span className="bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 py-1 px-2 rounded-lg text-sm">
                            {sale.items}
                          </span>
                        </div>
                      </td>
                      <td className="py-3 px-4 text-secondary-700 dark:text-secondary-300">
                        <div className="flex items-center">
                          <span className={`py-1 px-2 rounded-lg text-sm font-medium ${getPaymentTypeInfo(sale.payment_method, sale.payment_status).colorClass}`}>
                            {getPaymentTypeInfo(sale.payment_method, sale.payment_status).text}
                          </span>
                        </div>
                      </td>
                      <td className="py-3 px-4 font-medium text-secondary-900 dark:text-secondary-100">
                        <div className="flex flex-col">
                          <span className="bg-success-50 dark:bg-success-900/30 text-success-700 dark:text-success-300 py-1 px-2 rounded-lg text-sm">
                            <FormattedCurrency amount={sale.amount_paid} />
                          </span>
                          {sale.payment_status === 'partial' && sale.total_amount > sale.amount_paid && (
                            <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              من أصل <FormattedCurrency amount={sale.total_amount} />
                            </span>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Mobile View */}
          <div className="md:hidden">
            <div className="grid grid-cols-1 gap-3">
              {dashboardData?.recentSales.map((sale) => (
                <div
                  key={sale.id}
                  className="p-3 rounded-lg bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                >
                  <div className="flex justify-between items-center mb-2">
                    <Link
                      to={`/receipt/${sale.id}`}
                      className="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium flex items-center"
                    >
                      <span className="bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 py-1 px-2 rounded-lg text-sm ml-1">
                        #{sale.id}
                      </span>
                      <FiEye className="text-xs" />
                    </Link>
                    <div className="text-xs text-secondary-500 dark:text-secondary-400">
                      <div className="flex flex-col">
                        <FormattedDate date={sale.date} className="text-xs font-medium" />
                        <FormattedTime date={sale.date} className="text-xs text-secondary-400 dark:text-secondary-500" />
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-between items-center mb-2">
                    <div className="flex items-center">
                      <FaBoxOpen className="text-secondary-500 dark:text-secondary-400 ml-1 text-xs" />
                      <span className="text-sm text-secondary-700 dark:text-secondary-300">
                        {sale.items} عناصر
                      </span>
                    </div>
                    <span className={`py-1 px-2 rounded-lg text-xs font-medium ${getPaymentTypeInfo(sale.payment_method, sale.payment_status).colorClass}`}>
                      {getPaymentTypeInfo(sale.payment_method, sale.payment_status).text}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-secondary-500 dark:text-secondary-400">المبلغ المدفوع:</span>
                    <div className="flex flex-col items-end">
                      <span className="font-medium text-success-700 dark:text-success-300 bg-success-50 dark:bg-success-900/30 py-1 px-2 rounded-lg text-sm">
                        <FormattedCurrency amount={sale.amount_paid} />
                      </span>
                      {sale.payment_status === 'partial' && sale.total_amount > sale.amount_paid && (
                        <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          من أصل <FormattedCurrency amount={sale.total_amount} />
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Empty State */}
          {(!dashboardData?.recentSales || dashboardData.recentSales.length === 0) && (
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 text-center">
              <div className="text-secondary-400 dark:text-secondary-500 mb-2">
                <FaReceipt className="text-3xl mx-auto" />
              </div>
              <p className="text-secondary-600 dark:text-secondary-300 font-medium">لا توجد مبيعات حديثة</p>
              <p className="text-sm text-secondary-500 dark:text-secondary-400 mt-1">ستظهر آخر المبيعات هنا بمجرد إتمام عمليات البيع</p>
            </div>
          )}
        </div>

        {/* Top Products */}
        <div className="touch-card">
          <div className="flex justify-between items-center mb-5">
            <div className="flex items-center">
              <div className="bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-300 p-2 rounded-xl ml-3">
                <FaChartLine className="text-xl" />
              </div>
              <h2 className="font-bold text-xl text-secondary-900 dark:text-secondary-100">أفضل المنتجات مبيعاً</h2>
            </div>
            <button
              onClick={() => navigate('/products', {
                state: {
                  activeTab: 'analytics',
                  analyticsTab: 'bestselling',
                  analyticsFilters: {
                    bestselling: {
                      sortBy: 'total_revenue'
                    }
                  }
                }
              })}
              className="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium flex items-center gap-1 transition-colors duration-200 px-2 py-1 rounded-lg hover:bg-primary-50 dark:hover:bg-primary-900/20"
            >
              <span>عرض الكل</span>
              <FiArrowRight className="text-sm" />
            </button>
          </div>

          {/* Desktop View */}
          <div className="hidden md:block">
            <div className="grid grid-cols-1 gap-3">
              {dashboardData?.topProducts.map((product, index) => (
                <div
                  key={product.id}
                  className="flex items-center justify-between p-4 rounded-xl border border-gray-100 dark:border-gray-700 hover:shadow-md dark:hover:border-gray-600 transition-all duration-200"
                >
                  <div className="flex items-center">
                    <div className={`h-12 w-12 rounded-xl flex items-center justify-center ml-4 ${
                      index === 0 ? 'bg-warning-100 dark:bg-warning-900/50 text-warning-700 dark:text-warning-300' :
                      index === 1 ? 'bg-primary-100 dark:bg-primary-900/50 text-primary-700 dark:text-primary-300' :
                      index === 2 ? 'bg-success-100 dark:bg-success-900/50 text-success-700 dark:text-success-300' :
                      'bg-gray-100 dark:bg-gray-600 text-secondary-600 dark:text-secondary-300'
                    }`}>
                      {index === 0 ? (
                        <FaTrophy className="text-lg" />
                      ) : (
                        <span className="font-bold text-lg">{index + 1}</span>
                      )}
                    </div>
                    <div>
                      <p className="font-medium text-secondary-900 dark:text-secondary-100 text-lg">{product.name}</p>
                      <div className="flex items-center mt-1">
                        <FaBoxOpen className="text-secondary-500 dark:text-secondary-400 ml-1" />
                        <p className="text-sm text-secondary-500 dark:text-secondary-400">{product.sold} مبيعات</p>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col items-end">
                    <p className="font-bold text-lg text-secondary-900 dark:text-secondary-100">
                      <FormattedCurrency amount={product.revenue} />
                    </p>
                    <div className={`mt-1 px-2 py-0.5 rounded-full text-xs ${
                      index === 0 ? 'bg-warning-100 dark:bg-warning-900/30 text-warning-700 dark:text-warning-300' :
                      index === 1 ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300' :
                      index === 2 ? 'bg-success-100 dark:bg-success-900/30 text-success-700 dark:text-success-300' :
                      'bg-gray-100 dark:bg-gray-700 text-secondary-600 dark:text-secondary-300'
                    }`}>
                      {index === 0 ? 'الأكثر مبيعاً' : `المركز ${index + 1}`}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Mobile View */}
          <div className="md:hidden">
            <div className="grid grid-cols-1 gap-3">
              {dashboardData?.topProducts.map((product, index) => (
                <div
                  key={product.id}
                  className="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                >
                  <div className="flex items-center">
                    <div className={`h-10 w-10 rounded-xl flex items-center justify-center ml-3 ${
                      index === 0 ? 'bg-warning-100 dark:bg-warning-900/50 text-warning-700 dark:text-warning-300' :
                      index === 1 ? 'bg-primary-100 dark:bg-primary-900/50 text-primary-700 dark:text-primary-300' :
                      index === 2 ? 'bg-success-100 dark:bg-success-900/50 text-success-700 dark:text-success-300' :
                      'bg-gray-100 dark:bg-gray-600 text-secondary-600 dark:text-secondary-300'
                    }`}>
                      {index === 0 ? (
                        <FaTrophy className="text-sm" />
                      ) : (
                        <span className="font-bold">{index + 1}</span>
                      )}
                    </div>
                    <div>
                      <p className="font-medium text-secondary-900 dark:text-secondary-100">{product.name}</p>
                      <p className="text-xs text-secondary-500 dark:text-secondary-400">{product.sold} مبيعات</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-secondary-900 dark:text-secondary-100">
                      <FormattedCurrency amount={product.revenue} />
                    </p>
                    <p className="text-xs text-secondary-500 dark:text-secondary-400">
                      {index === 0 ? 'الأكثر مبيعاً' : `المركز ${index + 1}`}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Empty State */}
          {(!dashboardData?.topProducts || dashboardData.topProducts.length === 0) && (
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 text-center">
              <div className="text-secondary-400 dark:text-secondary-500 mb-2">
                <FaChartBar className="text-3xl mx-auto" />
              </div>
              <p className="text-secondary-600 dark:text-secondary-300 font-medium">لا توجد بيانات مبيعات بعد</p>
              <p className="text-sm text-secondary-500 dark:text-secondary-400 mt-1">ستظهر أفضل المنتجات مبيعاً هنا بمجرد إتمام عمليات البيع</p>
            </div>
          )}
        </div>
      </div>

      {/* Zero Stock Products */}
      <div className="touch-card mb-8">
        <div className="flex justify-between items-center mb-5">
          <h2 className="font-bold text-xl text-secondary-900 dark:text-secondary-100">منتجات نفذت كميتها</h2>
          <button
            onClick={handleManageLowStock}
            className="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium flex items-center gap-1"
          >
            <span>عرض الكل</span>
            <FiArrowRight className="text-sm" />
          </button>
        </div>

        <div className="overflow-x-auto custom-scrollbar">
          <table className="touch-table">
            <thead>
              <tr>
                <th className="text-right">المنتج</th>
                <th className="text-right">الكمية الحالية</th>
                <th className="text-right">الحد الأدنى</th>
                <th className="text-right">الحالة</th>
                <th className="text-right">الإجراء</th>
              </tr>
            </thead>
            <tbody>
              {(dashboardData?.lowStockProductsList?.length || 0) > 0 ? (
                dashboardData?.lowStockProductsList?.map((product) => (
                  <tr key={product.id} className="border-b dark:border-gray-700 last:border-b-0 hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="py-3 px-4 text-secondary-900 dark:text-secondary-100 font-medium">{product.name}</td>
                    <td className="py-3 px-4 text-secondary-700 dark:text-secondary-300">{product.currentStock} {product.unit}</td>
                    <td className="py-3 px-4 text-secondary-700 dark:text-secondary-300">{product.minRequired} {product.unit}</td>
                    <td className="py-3 px-4">
                      <span className="py-1 px-2 rounded-lg text-sm bg-danger-50 dark:bg-danger-900/30 text-danger-700 dark:text-danger-300">
                        نفذت الكمية
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <button
                        onClick={() => {
                          setSelectedProduct({
                            id: product.id,
                            name: product.name,
                            quantity: product.currentStock
                          });
                          setNewQuantity(product.currentStock + product.minRequired);
                          setShowStockModal(true);
                        }}
                        className="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium"
                      >
                        تحديث المخزون
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="py-8 text-center text-secondary-500 dark:text-secondary-400">
                    لا توجد منتجات نفذت كميتها حال
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Stock Update Modal */}
      {showStockModal && selectedProduct && (
        <div className="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-60 flex items-center justify-center">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6 w-full max-w-md mx-4">
            <div className="flex justify-between items-center mb-5">
              <h3 className="text-xl font-bold text-secondary-900 dark:text-secondary-100">تحديث المخزون</h3>
              <button
                onClick={() => setShowStockModal(false)}
                className="text-secondary-500 dark:text-secondary-400 hover:text-secondary-700 dark:hover:text-secondary-300 text-2xl"
              >
                &times;
              </button>
            </div>
            <div className="mb-5">
              <label className="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-2">
                اسم المنتج
              </label>
              <div className="text-secondary-900 dark:text-secondary-100 bg-gray-50 dark:bg-gray-700 p-3 rounded-lg font-medium">
                {selectedProduct.name}
              </div>
            </div>
            <div className="mb-6">
              <label className="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-2">
                الكمية الجديدة
              </label>
              <input
                type="number"
                min="0"
                value={newQuantity}
                onChange={(e) => setNewQuantity(parseInt(e.target.value) || 0)}
                className="input-field"
              />
            </div>
            <div className="flex justify-end gap-3">
              <button
                type="button"
                onClick={() => setShowStockModal(false)}
                className="btn-secondary"
              >
                إلغاء
              </button>
              <button
                type="button"
                onClick={handleUpdateStock}
                className="btn-primary"
              >
                تحديث
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Action Panel Modal */}
      {showActionPanel && (
        <div className="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-60 flex items-center justify-center">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6 w-full max-w-md mx-4">
            <div className="flex justify-between items-center mb-5">
              <h3 className="text-xl font-bold text-secondary-900 dark:text-secondary-100">
                {actionType === 'sales' ? 'إدارة المبيعات' :
                 actionType === 'products' ? 'إدارة المنتجات' : 'التقارير'}
              </h3>
              <button
                onClick={() => setShowActionPanel(false)}
                className="text-secondary-500 dark:text-secondary-400 hover:text-secondary-700 dark:hover:text-secondary-300 text-2xl"
              >
                &times;
              </button>
            </div>
            <div className="grid grid-cols-1 gap-3 mb-5">
              {actionType === 'sales' && (
                <>
                  <button
                    onClick={() => handleQuickAction('new-sale')}
                    className="btn-secondary flex items-center justify-center gap-2 py-3 text-base"
                  >
                    <FiPlus />
                    <span>عملية بيع جديدة</span>
                  </button>
                  <button
                    onClick={() => handleQuickAction('view-sales')}
                    className="btn-secondary flex items-center justify-center gap-2 py-3 text-base"
                  >
                    <FiSearch />
                    <span>عرض المبيعات</span>
                  </button>
                </>
              )}

              {actionType === 'products' && (
                <>
                  <button
                    onClick={() => handleQuickAction('add-product')}
                    className="btn-secondary flex items-center justify-center gap-2 py-3 text-base"
                  >
                    <FiPlus />
                    <span>إضافة منتج جديد</span>
                  </button>
                  <button
                    onClick={() => handleQuickAction('update-inventory')}
                    className="btn-secondary flex items-center justify-center gap-2 py-3 text-base"
                  >
                    <FiEdit />
                    <span>تحديث المخزون</span>
                  </button>
                </>
              )}

              {actionType === 'reports' && (
                <>
                  <button
                    onClick={() => handleQuickAction('print-report')}
                    className="btn-secondary flex items-center justify-center gap-2 py-3 text-base"
                  >
                    <FaPrint />
                    <span>طباعة تقرير المبيعات</span>
                  </button>
                </>
              )}
            </div>
            <div className="flex justify-end">
              <button
                type="button"
                onClick={() => setShowActionPanel(false)}
                className="btn-secondary"
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default Dashboard;
