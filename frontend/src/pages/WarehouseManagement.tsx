/**
 * صفحة إدارة المستودعات
 * تحتوي على تبويبات داخلية لجميع وظائف المستودعات
 */

import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  FaArrowLeft,
  FaSync
} from 'react-icons/fa';
import {
  FiPackage,
  FiBarChart,
  FiTruck,
  FiRefreshCw,
  FiFileText
} from 'react-icons/fi';

// Import stores
import { useWarehouseStore } from '../stores/warehouseStore';
import { useWarehouseInventoryStore } from '../stores/warehouseInventoryStore';
import { useWarehouseMovementStore } from '../stores/warehouseMovementStore';

// Import tab components
import {
  WarehousesTab,
  WarehouseInventoryTab,
  WarehouseMovementsTab,
  TransferRequestsTab,
  WarehouseReportsTab
} from '../components/warehouse';



const WarehouseManagement: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Stores
  const warehouseStore = useWarehouseStore();
  const warehouseInventoryStore = useWarehouseInventoryStore();
  const warehouseMovementStore = useWarehouseMovementStore();

  // State
  const [activeTab, setActiveTab] = useState<'warehouses' | 'inventory' | 'movements' | 'transfers' | 'reports'>('warehouses');
  
  // Refs for tracking loaded tabs
  const loadedTabs = useRef(new Set<string>());

  // Set active tab based on current route
  useEffect(() => {
    const path = location.pathname;
    if (path.includes('/warehouse-management/warehouses') || path === '/warehouse-management') {
      setActiveTab('warehouses');
    } else if (path.includes('/warehouse-management/inventory')) {
      setActiveTab('inventory');
    } else if (path.includes('/warehouse-management/movements')) {
      setActiveTab('movements');
    } else if (path.includes('/warehouse-management/transfers')) {
      setActiveTab('transfers');
    } else if (path.includes('/warehouse-management/reports')) {
      setActiveTab('reports');
    }
  }, [location.pathname]);

  // Load data for active tab
  useEffect(() => {
    const loadTabData = async () => {
      if (loadedTabs.current.has(activeTab)) return;

      try {
        console.log(`🔄 [WarehouseManagement] تحميل بيانات التبويب: ${activeTab}`);

        switch (activeTab) {
          case 'warehouses':
            await warehouseStore.fetchWarehouses();
            await warehouseStore.fetchWarehouseSummary();
            break;
          case 'inventory':
            // TODO: Implement inventory data loading when store method is available
            console.log('تحميل بيانات المخزون - قيد التطوير');
            break;
          case 'movements':
            // TODO: Implement movements data loading when store method is available
            console.log('تحميل بيانات الحركات - قيد التطوير');
            break;
          case 'transfers':
            // TODO: Implement transfer requests data loading when store method is available
            console.log('تحميل بيانات طلبات التحويل - قيد التطوير');
            break;

          case 'reports':
            // Load reports data
            break;
        }

        loadedTabs.current.add(activeTab);
        console.log(`✅ [WarehouseManagement] تم تحميل بيانات التبويب: ${activeTab}`);
      } catch (error) {
        console.error(`❌ [WarehouseManagement] خطأ في تحميل بيانات التبويب ${activeTab}:`, error);
      }
    };

    loadTabData();
  }, [activeTab]); // إزالة المخازن من التبعيات لتجنب الحلقة اللا نهائية

  // Handle refresh
  const handleRefresh = async () => {
    try {
      console.log('🔄 [WarehouseManagement] تحديث البيانات...');

      // إعادة تعيين التبويب المحمل لإجبار إعادة التحميل
      loadedTabs.current.delete(activeTab);

      switch (activeTab) {
        case 'warehouses':
          await warehouseStore.fetchWarehouses();
          await warehouseStore.fetchWarehouseSummary();
          break;
        case 'inventory':
          // TODO: Implement inventory data refresh when store method is available
          console.log('تحديث بيانات المخزون - قيد التطوير');
          break;
        case 'movements':
          // TODO: Implement movements data refresh when store method is available
          console.log('تحديث بيانات الحركات - قيد التطوير');
          break;
        case 'transfers':
          // TODO: Implement transfer requests data refresh when store method is available
          console.log('تحديث بيانات طلبات التحويل - قيد التطوير');
          break;

        case 'reports':
          // Refresh reports data
          break;
      }

      // إعادة إضافة التبويب كمحمل
      loadedTabs.current.add(activeTab);
      console.log('✅ [WarehouseManagement] تم تحديث البيانات');
    } catch (error) {
      console.error('❌ [WarehouseManagement] خطأ في تحديث البيانات:', error);
    }
  };

  // Get current error from active store
  const getCurrentError = () => {
    switch (activeTab) {
      case 'warehouses':
        return warehouseStore.error;
      case 'inventory':
        return warehouseInventoryStore.error;
      case 'movements':
      case 'transfers':
        return warehouseMovementStore.error;

      default:
        return null;
    }
  };

  const currentError = getCurrentError();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8">
      {/* Header */}
      <div className="mb-6">
        <div className="relative rounded-xl p-4 sm:p-6 bg-gradient-to-l from-primary-50/40 via-primary-50/20 to-white dark:from-primary-900/20 dark:via-primary-900/10 dark:to-gray-800 border border-gray-200 dark:border-gray-700" style={{ boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)' }}>
          {/* خط نقش علوي للتأثير المميز */}
          <div className="absolute -top-0.5 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-primary-300/30 to-transparent dark:from-transparent dark:via-primary-600/20 dark:to-transparent"></div>

          {/* خط نقش سفلي للتأثير المميز */}
          <div className="absolute -bottom-0.5 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-primary-300/30 to-transparent dark:from-transparent dark:via-primary-600/20 dark:to-transparent"></div>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <button
                onClick={() => navigate('/')}
                className="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-xl p-3 hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-200 ease-in-out shadow-lg hover:shadow-xl flex-shrink-0 border-2 border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                title="العودة للرئيسية"
              >
                <FaArrowLeft className="text-sm" />
              </button>
              <div className="mr-3 sm:mr-4 min-w-0 flex-1">
                <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FiPackage className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                  <span className="truncate">إدارة المستودعات</span>
                </h1>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 hidden sm:block">
                  إدارة شاملة لجميع عمليات المستودعات والمخزون
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 flex-shrink-0">
              <button
                onClick={handleRefresh}
                className="bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium min-w-[100px]"
                title="تحديث البيانات"
              >
                <FaSync className="ml-2" />
                تحديث
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {currentError && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4 mb-6">
          <div className="flex items-center">
            <div className="text-red-600 dark:text-red-400 text-sm">
              {currentError}
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft mb-6 border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex overflow-x-auto">
            <button
              onClick={() => {
                setActiveTab('warehouses');
                navigate('/warehouse-management/warehouses');
              }}
              className={`py-4 px-6 border-b-2 font-medium text-sm flex items-center transition-all duration-200 ease-in-out whitespace-nowrap ${
                activeTab === 'warehouses'
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
              }`}
            >
              <FiPackage className="ml-2" />
              المستودعات
            </button>

            <button
              onClick={() => {
                setActiveTab('inventory');
                navigate('/warehouse-management/inventory');
              }}
              className={`py-4 px-6 border-b-2 font-medium text-sm flex items-center transition-all duration-200 ease-in-out whitespace-nowrap ${
                activeTab === 'inventory'
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
              }`}
            >
              <FiBarChart className="ml-2" />
              مخزون المستودعات
            </button>

            <button
              onClick={() => {
                setActiveTab('movements');
                navigate('/warehouse-management/movements');
              }}
              className={`py-4 px-6 border-b-2 font-medium text-sm flex items-center transition-all duration-200 ease-in-out whitespace-nowrap ${
                activeTab === 'movements'
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
              }`}
            >
              <FiRefreshCw className="ml-2" />
              حركات المستودعات
            </button>

            <button
              onClick={() => {
                setActiveTab('transfers');
                navigate('/warehouse-management/transfers');
              }}
              className={`py-4 px-6 border-b-2 font-medium text-sm flex items-center transition-all duration-200 ease-in-out whitespace-nowrap ${
                activeTab === 'transfers'
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
              }`}
            >
              <FiTruck className="ml-2" />
              طلبات التحويل
            </button>


            <button
              onClick={() => {
                setActiveTab('reports');
                navigate('/warehouse-management/reports');
              }}
              className={`py-4 px-6 border-b-2 font-medium text-sm flex items-center transition-all duration-200 ease-in-out whitespace-nowrap ${
                activeTab === 'reports'
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
              }`}
            >
              <FiFileText className="ml-2" />
              تقارير المستودعات
            </button>
          </nav>
        </div>
      </div>

      {/* Content based on active tab */}
      {activeTab === 'warehouses' && <WarehousesTab />}
      {activeTab === 'inventory' && <WarehouseInventoryTab />}
      {activeTab === 'movements' && <WarehouseMovementsTab />}
      {activeTab === 'transfers' && <TransferRequestsTab />}

      {activeTab === 'reports' && <WarehouseReportsTab />}
    </div>
  );
};

export default WarehouseManagement;
