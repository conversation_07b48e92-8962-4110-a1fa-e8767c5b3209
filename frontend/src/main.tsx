import ReactDOM from 'react-dom/client';
import App from './App';
import './index.css'

// Import accessibility enhancements (auto-initializes in development)
import './utils/accessibilityEnhancer';
import './styles/no-scrollbar-arrows.css';  // إزالة أسهم شريط التمرير أولاً
import './styles/scrollbar.css';            // تخصيص شريط التمرير الأساسي
import './styles/enhanced-scrollbar.css';   // شريط التمرير المحسن الجديد
import './styles/sidebar-no-animations.css'; // إزالة الحركات من القائمة الجانبية
import './styles/sidebar-enhanced.css'; // تحسينات القائمة الجانبية الجديدة
import './styles/emoji.css';
import { applyCSP } from './utils/csp';

// Import scrollbar utilities (auto-initializes)
import './utils/scrollbarUtils';

// تطبيق CSP بناءً على البيئة
applyCSP();

// تعطيل StrictMode لتجنب التحميل المتكرر في بيئة التطوير
// StrictMode يسبب تشغيل useEffect مرتين في بيئة التطوير
ReactDOM.createRoot(document.getElementById('root')!).render(
  <App />
);