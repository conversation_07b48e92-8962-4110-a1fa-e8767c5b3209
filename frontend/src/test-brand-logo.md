# اختبار رفع صورة الشعار للعلامات التجارية

## الميزات المضافة

### 1. رفع صورة واحدة فقط
- تم تعديل مكون `ImageUploadComponent` لدعم رفع صورة واحدة فقط عندما `multiple=false`
- يتم استبدال الصورة الحالية عند اختيار صورة جديدة
- النصوص تتغير من "الصور" إلى "الصورة" عندما `multiple=false`

### 2. عرض الشعار في الجدول
- تم إضافة عمود "الشعار" في جدول العلامات التجارية
- يعرض صورة مصغرة 10x10 بكسل
- يعرض أيقونة افتراضية إذا لم تكن هناك صورة
- معالجة أخطاء تحميل الصور

### 3. إدارة الشعار في النموذج
- قسم مخصص لرفع الشعار في نموذج إنشاء/تعديل العلامة التجارية
- عرض الصورة الحالية مع إمكانية حذفها
- زر لتغيير الصورة أو رفع صورة جديدة
- حفظ مسار الصورة في حقل `logo_url`

## خطوات الاختبار

### اختبار إنشاء علامة تجارية جديدة مع شعار:
1. انتقل إلى صفحة إدارة الكتالوج
2. اختر تبويب "العلامات التجارية"
3. انقر على "إضافة علامة تجارية جديدة"
4. املأ البيانات الأساسية
5. في قسم "شعار العلامة التجارية"، انقر على "رفع شعار العلامة التجارية"
6. اختر صورة واحدة فقط
7. تأكد من ظهور معاينة الصورة
8. انقر على "رفع الصورة"
9. احفظ العلامة التجارية
10. تحقق من ظهور الشعار في الجدول

### اختبار تعديل شعار علامة تجارية موجودة:
1. انقر على زر التعديل لعلامة تجارية موجودة
2. في قسم الشعار، انقر على "تغيير الشعار"
3. اختر صورة جديدة
4. احفظ التغييرات
5. تحقق من تحديث الشعار في الجدول

### اختبار حذف الشعار:
1. افتح نموذج تعديل علامة تجارية لها شعار
2. انقر على أيقونة الحذف بجانب الصورة الحالية
3. احفظ التغييرات
4. تحقق من اختفاء الشعار من الجدول

## الملفات المعدلة

1. `frontend/src/components/catalog/BrandsDataTable.tsx`
   - إضافة عمود الشعار في الجدول
   - إضافة قسم رفع الشعار في النموذج
   - إضافة دوال معالجة رفع وحذف الصور

2. `frontend/src/components/ImageUpload/ImageUploadComponent.tsx`
   - تحسين النصوص للصورة الواحدة
   - إضافة منطق استبدال الصورة عند `multiple=false`

## التحقق من الأخطاء

- ✅ لا توجد أخطاء TypeScript في الملفات المعدلة
- ✅ تم تطبيق مبادئ البرمجة الكائنية
- ✅ استخدام خدمة إدارة الصور الموجودة
- ✅ دعم رفع صورة واحدة فقط
- ✅ عرض الصور في الجدول والنموذج
- ✅ حفظ مسار الصورة في قاعدة البيانات

## ملاحظات

- يتم حفظ الصور في مجلد `static/uploads/brands/`
- يتم إنشاء صور مصغرة تلقائياً
- مسار الصورة يُحفظ في حقل `logo_url` في قاعدة البيانات
- الخدمة تدعم جميع صيغ الصور المعتادة (JPG, PNG, GIF, WebP, BMP)
