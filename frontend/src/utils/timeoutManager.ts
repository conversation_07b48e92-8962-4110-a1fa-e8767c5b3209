/**
 * مدير المهلة الزمنية - يدير جميع العمليات المتعلقة بالـ timeout بطريقة محسنة
 */

interface TimeoutOperation {
  id: string;
  controller: AbortController;
  timeoutId: NodeJS.Timeout;
  startTime: number;
  maxDuration: number;
}

class TimeoutManager {
  private static instance: TimeoutManager;
  private operations = new Map<string, TimeoutOperation>();
  private cleanupInterval: NodeJS.Timeout | null = null;

  private constructor() {
    // تنظيف دوري للعمليات المنتهية الصلاحية (كل دقيقة)
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredOperations();
    }, 60000); // كل دقيقة لتقليل الضغط
  }

  public static getInstance(): TimeoutManager {
    if (!TimeoutManager.instance) {
      TimeoutManager.instance = new TimeoutManager();
    }
    return TimeoutManager.instance;
  }

  /**
   * إنشاء عملية جديدة مع timeout
   */
  public createOperation(
    id: string,
    timeoutMs: number,
    onTimeout?: () => void
  ): { controller: AbortController; cleanup: () => void } {
    // تنظيف العملية السابقة إذا كانت موجودة
    this.cleanup(id);

    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      if (!controller.signal.aborted) {
        controller.abort();
        if (onTimeout) {
          onTimeout();
        }
      }
      this.cleanup(id);
    }, timeoutMs);

    const operation: TimeoutOperation = {
      id,
      controller,
      timeoutId,
      startTime: Date.now(),
      maxDuration: timeoutMs
    };

    this.operations.set(id, operation);

    return {
      controller,
      cleanup: () => this.cleanup(id)
    };
  }

  /**
   * تنظيف عملية محددة
   */
  public cleanup(id: string): void {
    const operation = this.operations.get(id);
    if (operation) {
      clearTimeout(operation.timeoutId);
      if (!operation.controller.signal.aborted) {
        operation.controller.abort();
      }
      this.operations.delete(id);
    }
  }

  /**
   * تنظيف جميع العمليات
   */
  public cleanupAll(): void {
    for (const [id] of this.operations) {
      this.cleanup(id);
    }
  }

  /**
   * تنظيف العمليات المنتهية الصلاحية
   */
  private cleanupExpiredOperations(): void {
    const now = Date.now();
    const expiredIds: string[] = [];

    for (const [id, operation] of this.operations) {
      if (now - operation.startTime > operation.maxDuration + 10000) { // 10 ثوانٍ إضافية لتجنب التنظيف المبكر
        expiredIds.push(id);
      }
    }

    expiredIds.forEach(id => this.cleanup(id));

    if (expiredIds.length > 0) {
      console.log(`🧹 Cleaned up ${expiredIds.length} expired timeout operations`);
    }
  }

  /**
   * الحصول على إحصائيات العمليات النشطة
   */
  public getStats(): { activeOperations: number; oldestOperation: number | null } {
    const now = Date.now();
    let oldestOperation: number | null = null;

    for (const operation of this.operations.values()) {
      const age = now - operation.startTime;
      if (oldestOperation === null || age > oldestOperation) {
        oldestOperation = age;
      }
    }

    return {
      activeOperations: this.operations.size,
      oldestOperation
    };
  }

  /**
   * إيقاف المدير وتنظيف جميع الموارد
   */
  public destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.cleanupAll();
  }
}

export const timeoutManager = TimeoutManager.getInstance();

/**
 * دالة مساعدة لإنشاء fetch مع timeout محسن
 */
export async function fetchWithTimeout(
  url: string,
  options: RequestInit & { timeout?: number } = {},
  operationId?: string
): Promise<Response> {
  const { timeout = 15000, ...fetchOptions } = options; // تقليل timeout الافتراضي إلى 15 ثانية
  const id = operationId || `fetch-${Date.now()}-${Math.random()}`;

  const { controller, cleanup } = timeoutManager.createOperation(
    id,
    timeout,
    () => console.warn(`⏰ Fetch timeout for ${url}`)
  );

  try {
    const response = await fetch(url, {
      ...fetchOptions,
      signal: controller.signal
    });

    cleanup();
    return response;
  } catch (error) {
    cleanup();
    throw error;
  }
}

/**
 * دالة مساعدة لإنشاء Promise مع timeout
 */
export function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number,
  operationId?: string
): Promise<T> {
  const id = operationId || `promise-${Date.now()}-${Math.random()}`;

  return new Promise<T>((resolve, reject) => {
    const { cleanup } = timeoutManager.createOperation(
      id,
      timeoutMs,
      () => reject(new Error(`Operation timed out after ${timeoutMs}ms`))
    );

    promise
      .then(result => {
        cleanup();
        resolve(result);
      })
      .catch(error => {
        cleanup();
        reject(error);
      });
  });
}

/**
 * Hook للاستخدام في React components
 */
export function useTimeoutManager() {
  return {
    createOperation: (id: string, timeout: number, onTimeout?: () => void) =>
      timeoutManager.createOperation(id, timeout, onTimeout),
    cleanup: (id: string) => timeoutManager.cleanup(id),
    getStats: () => timeoutManager.getStats(),
    fetchWithTimeout,
    withTimeout
  };
}

// تنظيف عند إغلاق النافذة
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    timeoutManager.destroy();
  });
}
