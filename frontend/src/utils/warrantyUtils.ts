/**
 * أدوات مساعدة لحساب تواريخ الضمان
 * تطبق مبادئ البرمجة الكائنية مع معالجة شاملة للأخطاء
 */

import { WarrantyType } from '../types/warranty';

/**
 * فئة أدوات الضمان
 * تحتوي على دوال حساب التواريخ والتحقق من صحة البيانات
 */
export class WarrantyUtils {
  /**
   * حساب تاريخ بداية وانتهاء الضمان تلقائياً
   * @param purchaseDate تاريخ الشراء (YYYY-MM-DD)
   * @param warrantyType نوع الضمان المحدد
   * @returns كائن يحتوي على تاريخ البداية والانتهاء
   */
  public static calculateWarrantyDates(
    purchaseDate: string,
    warrantyType: WarrantyType | null
  ): { startDate: string; endDate: string } {
    try {
      // التحقق من صحة المدخلات
      if (!purchaseDate || !warrantyType) {
        console.warn('⚠️ [WarrantyUtils] بيانات غير مكتملة لحساب تواريخ الضمان');
        return { startDate: '', endDate: '' };
      }

      // التحقق من صحة تاريخ الشراء
      const purchaseDateObj = new Date(purchaseDate);
      if (isNaN(purchaseDateObj.getTime())) {
        console.error('❌ [WarrantyUtils] تاريخ شراء غير صحيح:', purchaseDate);
        return { startDate: '', endDate: '' };
      }

      // التحقق من صحة مدة الضمان
      if (!warrantyType.duration_months || warrantyType.duration_months <= 0) {
        console.error('❌ [WarrantyUtils] مدة ضمان غير صحيحة:', warrantyType.duration_months);
        return { startDate: '', endDate: '' };
      }

      // حساب تاريخ البداية (نفس تاريخ الشراء)
      const startDate = new Date(purchaseDateObj);
      
      // حساب تاريخ الانتهاء (إضافة الأشهر)
      const endDate = new Date(purchaseDateObj);
      endDate.setMonth(endDate.getMonth() + warrantyType.duration_months);

      // تنسيق التواريخ بصيغة YYYY-MM-DD
      const formattedStartDate = this.formatDateForInput(startDate);
      const formattedEndDate = this.formatDateForInput(endDate);

      console.log('✅ [WarrantyUtils] تم حساب تواريخ الضمان:', {
        purchaseDate,
        warrantyType: warrantyType.name_ar,
        duration: warrantyType.duration_months,
        startDate: formattedStartDate,
        endDate: formattedEndDate
      });

      return {
        startDate: formattedStartDate,
        endDate: formattedEndDate
      };

    } catch (error) {
      console.error('❌ [WarrantyUtils] خطأ في حساب تواريخ الضمان:', error);
      return { startDate: '', endDate: '' };
    }
  }

  /**
   * تنسيق التاريخ لحقول الإدخال (YYYY-MM-DD)
   * @param date كائن التاريخ
   * @returns التاريخ منسق بصيغة YYYY-MM-DD
   */
  private static formatDateForInput(date: Date): string {
    try {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      
      return `${year}-${month}-${day}`;
    } catch (error) {
      console.error('❌ [WarrantyUtils] خطأ في تنسيق التاريخ:', error);
      return '';
    }
  }

  /**
   * التحقق من صحة تواريخ الضمان
   * @param purchaseDate تاريخ الشراء
   * @param startDate تاريخ بداية الضمان
   * @param endDate تاريخ انتهاء الضمان
   * @returns true إذا كانت التواريخ صحيحة
   */
  public static validateWarrantyDates(
    purchaseDate: string,
    startDate: string,
    endDate: string
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    try {
      // التحقق من وجود التواريخ
      if (!purchaseDate) errors.push('تاريخ الشراء مطلوب');
      if (!startDate) errors.push('تاريخ بداية الضمان مطلوب');
      if (!endDate) errors.push('تاريخ انتهاء الضمان مطلوب');

      if (errors.length > 0) {
        return { isValid: false, errors };
      }

      // تحويل التواريخ إلى كائنات Date
      const purchaseDateObj = new Date(purchaseDate);
      const startDateObj = new Date(startDate);
      const endDateObj = new Date(endDate);

      // التحقق من صحة التواريخ
      if (isNaN(purchaseDateObj.getTime())) errors.push('تاريخ الشراء غير صحيح');
      if (isNaN(startDateObj.getTime())) errors.push('تاريخ بداية الضمان غير صحيح');
      if (isNaN(endDateObj.getTime())) errors.push('تاريخ انتهاء الضمان غير صحيح');

      if (errors.length > 0) {
        return { isValid: false, errors };
      }

      // التحقق من التسلسل المنطقي للتواريخ
      if (startDateObj < purchaseDateObj) {
        errors.push('تاريخ بداية الضمان لا يمكن أن يكون قبل تاريخ الشراء');
      }

      if (endDateObj <= startDateObj) {
        errors.push('تاريخ انتهاء الضمان يجب أن يكون بعد تاريخ البداية');
      }

      // التحقق من أن تاريخ الانتهاء ليس في الماضي البعيد
      const today = new Date();
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(today.getFullYear() - 1);

      if (endDateObj < oneYearAgo) {
        errors.push('تاريخ انتهاء الضمان قديم جداً');
      }

      return { isValid: errors.length === 0, errors };

    } catch (error) {
      console.error('❌ [WarrantyUtils] خطأ في التحقق من التواريخ:', error);
      return { isValid: false, errors: ['خطأ في التحقق من صحة التواريخ'] };
    }
  }

  /**
   * حساب عدد الأيام المتبقية في الضمان
   * @param endDate تاريخ انتهاء الضمان
   * @returns عدد الأيام المتبقية
   */
  public static calculateDaysRemaining(endDate: string): number {
    try {
      const endDateObj = new Date(endDate);
      const today = new Date();
      
      // إزالة الوقت للمقارنة بالتاريخ فقط
      today.setHours(0, 0, 0, 0);
      endDateObj.setHours(0, 0, 0, 0);

      const diffTime = endDateObj.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      return Math.max(0, diffDays);
    } catch (error) {
      console.error('❌ [WarrantyUtils] خطأ في حساب الأيام المتبقية:', error);
      return 0;
    }
  }

  /**
   * التحقق من انتهاء الضمان قريباً
   * @param endDate تاريخ انتهاء الضمان
   * @param warningDays عدد الأيام للتحذير (افتراضي: 30)
   * @returns true إذا كان الضمان سينتهي قريباً
   */
  public static isWarrantyExpiringSoon(endDate: string, warningDays: number = 30): boolean {
    try {
      const daysRemaining = this.calculateDaysRemaining(endDate);
      return daysRemaining > 0 && daysRemaining <= warningDays;
    } catch (error) {
      console.error('❌ [WarrantyUtils] خطأ في التحقق من انتهاء الضمان:', error);
      return false;
    }
  }

  /**
   * تنسيق مدة الضمان للعرض
   * @param months عدد الأشهر
   * @returns النص المنسق للمدة
   */
  public static formatWarrantyDuration(months: number): string {
    try {
      if (months <= 0) return 'غير محدد';
      
      if (months === 1) return 'شهر واحد';
      if (months === 2) return 'شهران';
      if (months < 12) return `${months} أشهر`;
      
      const years = Math.floor(months / 12);
      const remainingMonths = months % 12;
      
      let result = '';
      if (years === 1) result += 'سنة واحدة';
      else if (years === 2) result += 'سنتان';
      else if (years > 2) result += `${years} سنوات`;
      
      if (remainingMonths > 0) {
        if (result) result += ' و';
        if (remainingMonths === 1) result += 'شهر واحد';
        else if (remainingMonths === 2) result += 'شهران';
        else result += `${remainingMonths} أشهر`;
      }
      
      return result;
    } catch (error) {
      console.error('❌ [WarrantyUtils] خطأ في تنسيق مدة الضمان:', error);
      return 'غير محدد';
    }
  }
}

export default WarrantyUtils;
