/**
 * إدارة Content Security Policy ديناميكياً
 */

export class CSPManager {
  private static instance: CSPManager;
  
  private constructor() {}
  
  public static getInstance(): CSPManager {
    if (!CSPManager.instance) {
      CSPManager.instance = new CSPManager();
    }
    return CSPManager.instance;
  }

  /**
   * تطبيق CSP مناسب للبيئة الحالية
   */
  public applyEnvironmentCSP(): void {
    try {
      const isDevelopment = process.env.NODE_ENV === 'development';
      const isLocalhost = window.location.hostname === 'localhost' || 
                         window.location.hostname === '127.0.0.1';
      
      // إزالة CSP الموجود إذا كان في التطوير المحلي
      if (isDevelopment && isLocalhost) {
        this.removeExistingCSP();
        console.log('🔓 [CSP] Development mode: CSP restrictions relaxed');
        return;
      }

      // تطبيق CSP مناسب للإنتاج أو الشبكة
      this.applyProductionCSP();
      console.log('🔒 [CSP] Production mode: CSP restrictions applied');
      
    } catch (error) {
      console.warn('⚠️ [CSP] Failed to apply CSP:', error);
    }
  }

  /**
   * إزالة CSP الموجود
   */
  private removeExistingCSP(): void {
    const existingCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    if (existingCSP) {
      existingCSP.remove();
    }
  }

  /**
   * تطبيق CSP للإنتاج
   */
  private applyProductionCSP(): void {
    this.removeExistingCSP();
    
    const cspMeta = document.createElement('meta');
    cspMeta.setAttribute('http-equiv', 'Content-Security-Policy');
    cspMeta.setAttribute('content', this.getProductionCSP());
    
    document.head.appendChild(cspMeta);
  }

  /**
   * الحصول على CSP للإنتاج
   */
  private getProductionCSP(): string {
    return `
      default-src 'self';
      script-src 'self' https://fonts.googleapis.com;
      style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
      font-src 'self' https://fonts.gstatic.com;
      connect-src 'self' http://*************:8002 ws://*************:*;
      img-src 'self' data: blob:;
      object-src 'none';
      base-uri 'self';
      form-action 'self';
    `.replace(/\s+/g, ' ').trim();
  }

  /**
   * فحص ما إذا كان CSP يحجب eval
   */
  public checkEvalSupport(): boolean {
    try {
      // محاولة تنفيذ eval بسيط
      eval('1+1');
      return true;
    } catch (error) {
      if (error instanceof EvalError || 
          (error instanceof Error && error.message.includes('Content Security Policy'))) {
        console.warn('⚠️ [CSP] eval() is blocked by Content Security Policy');
        return false;
      }
      return true;
    }
  }

  /**
   * تطبيق CSP مؤقت للتطوير
   */
  public applyDevelopmentCSP(): void {
    this.removeExistingCSP();
    
    const cspMeta = document.createElement('meta');
    cspMeta.setAttribute('http-equiv', 'Content-Security-Policy');
    cspMeta.setAttribute('content', this.getDevelopmentCSP());
    
    document.head.appendChild(cspMeta);
    console.log('🔓 [CSP] Development CSP applied with unsafe-eval');
  }

  /**
   * الحصول على CSP للتطوير
   */
  private getDevelopmentCSP(): string {
    return `
      default-src 'self' 'unsafe-inline' 'unsafe-eval';
      script-src 'self' 'unsafe-eval' 'unsafe-inline' https: data:;
      style-src 'self' 'unsafe-inline' https:;
      font-src 'self' https: data:;
      connect-src 'self' http: https: ws: wss:;
      img-src 'self' data: blob: https:;
      object-src 'none';
    `.replace(/\s+/g, ' ').trim();
  }
}

// تصدير instance واحد
export const cspManager = CSPManager.getInstance();
