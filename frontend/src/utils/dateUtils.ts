/**
 * Date and time utilities for the application
 * Handles timezone conversion and formatting
 */

// Set the timezone offset for Tripoli, Libya (UTC+2)
/**
 * Creates a date object adjusted to Tripoli timezone
 * @param dateInput Optional date input (string, Date, or timestamp)
 * @returns Date object adjusted to Tripoli timezone
 */
export const createTripoliDate = (dateInput?: string | Date | number): Date => {
  // Create a new date object from the input or current time
  const date = dateInput ? new Date(dateInput) : new Date();

  // Now that the backend is storing dates in Tripoli time correctly,
  // we don't need to add 2 hours anymore
  return date;
};

/**
 * Format date as DD/MM/YYYY
 * @param dateInput Date to format
 * @returns Formatted date string
 */
export const formatDate = (dateInput: string | Date | number): string => {
  const date = createTripoliDate(dateInput);
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();

  return `${day}/${month}/${year}`;
};

/**
 * Format time as HH:MM in 24-hour format
 * @param dateInput Date to format
 * @returns Formatted time string
 */
export const formatTime = (dateInput: string | Date | number): string => {
  const date = createTripoliDate(dateInput);
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  return `${hours}:${minutes}`;
};

/**
 * Format date and time as DD/MM/YYYY HH:MM
 * @param dateInput Date to format
 * @returns Formatted date and time string
 */
export const formatDateTime = (dateInput: string | Date | number): string => {
  return `${formatDate(dateInput)} ${formatTime(dateInput)}`;
};

/**
 * Get current date and time in Tripoli timezone
 * @returns Current date and time object in Tripoli timezone
 */
export const getCurrentTripoliDateTime = (): Date => {
  return createTripoliDate();
};
