/**
 * أداة تشخيص التنبيهات - لحل مشاكل التنبيهات والأخطاء الحرجة
 */

import { alertService } from '../services/alertService';

export class AlertDiagnostics {
  
  /**
   * تشخيص شامل لنظام التنبيهات
   */
  static async runFullDiagnostics(): Promise<{
    status: 'healthy' | 'warning' | 'critical';
    issues: string[];
    recommendations: string[];
    alertStats: any;
  }> {
    console.log('🔍 بدء التشخيص الشامل لنظام التنبيهات...');

    const issues: string[] = [];
    const recommendations: string[] = [];
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';

    try {
      // 1. فحص إحصائيات التنبيهات
      const alertStats = alertService.getAlertStatistics();
      console.log('📊 إحصائيات التنبيهات:', alertStats);

      // 2. فحص التنبيهات المعطوبة
      const alerts = alertService.getAlerts();
      const corruptedAlerts = alerts.filter(alert =>
        !alert.id || !alert.title || !alert.message || !alert.timestamp ||
        alert.title.trim() === '' || alert.message.trim() === ''
      );

      if (corruptedAlerts.length > 0) {
        issues.push(`وجود ${corruptedAlerts.length} تنبيهات معطوبة`);
        recommendations.push('تشغيل تنظيف التنبيهات المعطوبة');
        status = 'warning';
      }

      // 3. فحص التنبيهات القديمة
      const now = new Date();
      const threeDaysAgo = new Date(now.getTime() - 259200000);
      const oldAlerts = alerts.filter(alert => alert.timestamp < threeDaysAgo && !alert.persistent);

      if (oldAlerts.length > 10) {
        issues.push(`وجود ${oldAlerts.length} تنبيهات قديمة`);
        recommendations.push('تشغيل تنظيف التنبيهات القديمة');
        // تحديث الحالة فقط إذا لم تكن حرجة بالفعل
        if (status === 'healthy') {
          status = 'warning';
        }
      }

      // 4. فحص التنبيهات الحرجة
      const criticalAlerts = alerts.filter(alert => alert.type === 'critical');
      if (criticalAlerts.length > 5) {
        issues.push(`وجود ${criticalAlerts.length} تنبيهات حرجة`);
        recommendations.push('مراجعة التنبيهات الحرجة وحل المشاكل');
        status = 'critical';
      }

      // 5. فحص التنبيهات المكررة
      const duplicateGroups = this.findDuplicateAlerts(alerts);
      if (duplicateGroups.length > 0) {
        issues.push(`وجود ${duplicateGroups.length} مجموعات تنبيهات مكررة`);
        recommendations.push('إزالة التنبيهات المكررة');
        // تحديث الحالة فقط إذا لم تكن حرجة بالفعل
        if (status === 'healthy') {
          status = 'warning';
        }
      }

      // 6. فحص أداء النظام
      if (alerts.length > 100) {
        issues.push(`عدد كبير من التنبيهات (${alerts.length})`);
        recommendations.push('تنظيف شامل للتنبيهات');
        // تحديث الحالة فقط إذا لم تكن حرجة بالفعل
        if (status === 'healthy') {
          status = 'warning';
        }
      }

      return {
        status,
        issues,
        recommendations,
        alertStats
      };

    } catch (error) {
      console.error('❌ خطأ في التشخيص:', error);
      return {
        status: 'critical',
        issues: ['فشل في تشغيل التشخيص'],
        recommendations: ['إعادة تحميل الصفحة والمحاولة مرة أخرى'],
        alertStats: {}
      };
    }
  }

  /**
   * البحث عن التنبيهات المكررة
   */
  private static findDuplicateAlerts(alerts: any[]): any[] {
    const groups: { [key: string]: any[] } = {};
    
    alerts.forEach(alert => {
      const key = `${alert.title}-${alert.source}-${alert.type}`;
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(alert);
    });

    return Object.values(groups).filter(group => group.length > 1);
  }

  /**
   * إصلاح سريع للمشاكل الشائعة
   */
  static async quickFix(): Promise<void> {
    console.log('🔧 بدء الإصلاح السريع...');

    try {
      // 1. تنظيف التنبيهات المعطوبة
      alertService.cleanupCorruptedAlerts();
      console.log('✅ تم تنظيف التنبيهات المعطوبة');

      // 2. تنظيف التنبيهات القديمة
      alertService.cleanupOldAlerts();
      console.log('✅ تم تنظيف التنبيهات القديمة');

      // 3. إعادة تعيين عداد الأخطاء الحرجة
      alertService.resetCriticalErrorCount();
      console.log('✅ تم إعادة تعيين عداد الأخطاء الحرجة');

      // 4. تنظيف شامل
      alertService.performFullCleanup();
      console.log('✅ تم التنظيف الشامل');

      console.log('🎉 تم الإصلاح السريع بنجاح!');

    } catch (error) {
      console.error('❌ فشل الإصلاح السريع:', error);
    }
  }

  /**
   * عرض تقرير مفصل عن التنبيهات
   */
  static displayDetailedReport(): void {
    console.log('📋 تقرير مفصل عن التنبيهات:');
    console.log('================================');

    const alerts = alertService.getAlerts();
    const stats = alertService.getAlertStatistics();

    console.log('📊 الإحصائيات العامة:');
    console.table(stats);

    console.log('\n🔍 تفاصيل التنبيهات:');
    alerts.forEach((alert, index) => {
      console.log(`${index + 1}. [${alert.type.toUpperCase()}] ${alert.title}`);
      console.log(`   المصدر: ${alert.source}`);
      console.log(`   الوقت: ${alert.timestamp.toLocaleString('ar-SA')}`);
      console.log(`   الرسالة: ${alert.message}`);
      console.log('   ---');
    });

    console.log('\n🎯 التوصيات:');
    if (alerts.length === 0) {
      console.log('✅ لا توجد تنبيهات - النظام يعمل بشكل طبيعي');
    } else if (alerts.length > 50) {
      console.log('⚠️ عدد كبير من التنبيهات - يُنصح بالتنظيف');
    } else {
      console.log('ℹ️ عدد طبيعي من التنبيهات');
    }
  }

  /**
   * حل مشكلة التنبيه المحدد
   */
  static async resolveSpecificAlert(alertId: string): Promise<boolean> {
    console.log(`🔧 محاولة حل التنبيه: ${alertId}`);

    try {
      const alerts = alertService.getAlerts();
      const targetAlert = alerts.find(alert => alert.id === alertId);

      if (!targetAlert) {
        console.log('❌ التنبيه غير موجود');
        return false;
      }

      // إزالة التنبيه
      alertService.removeAlert(alertId);
      console.log('✅ تم حذف التنبيه بنجاح');

      // إذا كان تنبيه حرج، إعادة تعيين العداد
      if (targetAlert.type === 'critical') {
        alertService.resetCriticalErrorCount();
        console.log('✅ تم إعادة تعيين عداد الأخطاء الحرجة');
      }

      return true;

    } catch (error) {
      console.error('❌ فشل في حل التنبيه:', error);
      return false;
    }
  }

  /**
   * إنشاء تقرير للدعم الفني
   */
  static generateSupportReport(): string {
    const alerts = alertService.getAlerts();
    const stats = alertService.getAlertStatistics();
    const timestamp = new Date().toISOString();

    const report = {
      timestamp,
      systemInfo: {
        userAgent: navigator.userAgent,
        url: window.location.href,
        localStorage: !!window.localStorage,
        sessionStorage: !!window.sessionStorage
      },
      alertStats: stats,
      recentAlerts: alerts.slice(0, 10).map(alert => ({
        id: alert.id,
        type: alert.type,
        title: alert.title,
        source: alert.source,
        timestamp: alert.timestamp.toISOString()
      }))
    };

    const reportString = JSON.stringify(report, null, 2);
    console.log('📄 تقرير الدعم الفني:', reportString);
    
    return reportString;
  }
}

// إتاحة الأداة في وحدة التحكم
if (typeof window !== 'undefined') {
  (window as any).AlertDiagnostics = AlertDiagnostics;
}

export default AlertDiagnostics;
