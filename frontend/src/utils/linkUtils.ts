/**
 * أدوات مساعدة للتعامل مع الروابط في الرسائل
 */

// تعبير منتظم محسن لاكتشاف الروابط - يدعم الروابط الطويلة والمعقدة
const URL_REGEX = /(https?:\/\/(?:[-\w.])+(?:\:[0-9]+)?(?:\/(?:[\w\/_.\-~!$&'()*+,;=:@])*)?(?:\?(?:[\w&=%.\-~!$'()*+,;:@/?])*)?(?:\#(?:[\w.\-~!$&'()*+,;=:@/?])*)?)/gi;

// تعبير منتظم شامل لاكتشاف جميع أنواع الروابط
const URL_REGEX_EXTENDED = /(?:(?:https?:\/\/)|(?:www\.)|(?:[a-zA-Z0-9][-a-zA-Z0-9]*[a-zA-Z0-9]\.))(?:[-\w.])+(?:\:[0-9]+)?(?:\/(?:[\w\/_.\-~!$&'()*+,;=:@])*)?(?:\?(?:[\w&=%.\-~!$'()*+,;:@/?])*)?(?:\#(?:[\w.\-~!$&'()*+,;=:@/?])*)?/gi;

// تعبير منتظم خاص للروابط المحلية (IP addresses)
const LOCAL_URL_REGEX = /(?:https?:\/\/)?(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(?:\:[0-9]+)?(?:\/(?:[\w\/_.\-~!$&'()*+,;=:@])*)?(?:\?(?:[\w&=%.\-~!$'()*+,;:@/?])*)?(?:\#(?:[\w.\-~!$&'()*+,;=:@/?])*)?/gi;

export interface LinkInfo {
  url: string;
  displayText: string;
  startIndex: number;
  endIndex: number;
}

/**
 * اكتشاف الروابط في النص
 */
export const detectLinks = (text: string): LinkInfo[] => {
  const links: LinkInfo[] = [];
  let match: RegExpExecArray | null;

  // البحث عن الروابط المحلية أولاً (IP addresses)
  LOCAL_URL_REGEX.lastIndex = 0;
  while ((match = LOCAL_URL_REGEX.exec(text)) !== null) {
    const url = match[0];
    let fullUrl = url;

    // إضافة البروتوكول إذا لم يكن موجوداً للروابط المحلية
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      fullUrl = `http://${url}`;
    }

    links.push({
      url: fullUrl,
      displayText: url,
      startIndex: match.index,
      endIndex: match.index + url.length
    });
  }

  // البحث عن الروابط العادية
  URL_REGEX_EXTENDED.lastIndex = 0;
  while ((match = URL_REGEX_EXTENDED.exec(text)) !== null) {
    const url = match[0];
    let fullUrl = url;

    // التحقق من عدم تداخل الرابط مع الروابط المحلية المكتشفة
    const isOverlapping = links.some(existingLink =>
      match!.index < existingLink.endIndex &&
      match!.index + url.length > existingLink.startIndex
    );

    if (isOverlapping) {
      continue;
    }

    // إضافة البروتوكول إذا لم يكن موجوداً
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      fullUrl = `https://${url}`;
    }

    links.push({
      url: fullUrl,
      displayText: url,
      startIndex: match.index,
      endIndex: match.index + url.length
    });
  }

  // ترتيب الروابط حسب موقعها في النص
  return links.sort((a, b) => a.startIndex - b.startIndex);
};

/**
 * تحويل النص إلى أجزاء مع تمييز الروابط
 */
export interface TextPart {
  type: 'text' | 'link';
  content: string;
  url?: string;
}

export const parseTextWithLinks = (text: string): TextPart[] => {
  const links = detectLinks(text);
  
  if (links.length === 0) {
    return [{ type: 'text', content: text }];
  }

  const parts: TextPart[] = [];
  let lastIndex = 0;

  // ترتيب الروابط حسب موقعها في النص
  links.sort((a, b) => a.startIndex - b.startIndex);

  links.forEach(link => {
    // إضافة النص قبل الرابط
    if (link.startIndex > lastIndex) {
      const textBefore = text.substring(lastIndex, link.startIndex);
      if (textBefore) {
        parts.push({ type: 'text', content: textBefore });
      }
    }

    // إضافة الرابط
    parts.push({
      type: 'link',
      content: link.displayText,
      url: link.url
    });

    lastIndex = link.endIndex;
  });

  // إضافة النص المتبقي بعد آخر رابط
  if (lastIndex < text.length) {
    const textAfter = text.substring(lastIndex);
    if (textAfter) {
      parts.push({ type: 'text', content: textAfter });
    }
  }

  return parts;
};

/**
 * التحقق من صحة الرابط
 */
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * استخراج اسم النطاق من الرابط
 */
export const extractDomain = (url: string): string => {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch {
    return url;
  }
};

/**
 * تقصير الرابط للعرض
 */
export const shortenUrl = (url: string, maxLength: number = 50): string => {
  if (url.length <= maxLength) {
    return url;
  }

  const domain = extractDomain(url);
  if (domain.length < maxLength - 3) {
    return `${domain}...`;
  }

  return `${url.substring(0, maxLength - 3)}...`;
};
