/**
 * Content Security Policy utilities
 * إدارة CSP بناءً على البيئة
 */

export const CSP_POLICIES = {
  // CSP للتطوير - متساهل للسماح بـ HMR و eval()
  development: {
    'default-src': "'self' 'unsafe-inline' 'unsafe-eval'",
    'script-src': "'self' 'unsafe-eval' 'unsafe-inline' https://fonts.googleapis.com",
    'style-src': "'self' 'unsafe-inline' https://fonts.googleapis.com",
    'font-src': "'self' https://fonts.gstatic.com data:",
    'connect-src': "'self' http://localhost:* http://127.0.0.1:* http://192.168.1.*:* ws://localhost:* ws://127.0.0.1:* ws://192.168.1.*:*",
    'img-src': "'self' data: blob: https:",
    'worker-src': "'self' blob:",
    'child-src': "'self' blob:",
  },

  // CSP للإنتاج - أمان قوي بدون eval()
  production: {
    'default-src': "'self'",
    'script-src': "'self' https://fonts.googleapis.com",
    'style-src': "'self' 'unsafe-inline' https://fonts.googleapis.com",
    'font-src': "'self' https://fonts.gstatic.com",
    'connect-src': "'self' http://*************:8002 ws://*************:*",
    'img-src': "'self' data: blob:",
    'object-src': "'none'",
    'base-uri': "'self'",
    'form-action': "'self'",
  }
};

/**
 * تطبيق CSP بناءً على البيئة
 */
export const applyCSP = () => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  if (isDevelopment) {
    // في التطوير، لا نطبق CSP لتجنب مشاكل eval()
    console.log('🔓 CSP disabled for development environment');
    return;
  }

  // في الإنتاج، طبق CSP قوي
  const policy = CSP_POLICIES.production;
  const cspString = Object.entries(policy)
    .map(([directive, value]) => `${directive} ${value}`)
    .join('; ');

  const meta = document.createElement('meta');
  meta.httpEquiv = 'Content-Security-Policy';
  meta.content = cspString;
  document.head.appendChild(meta);

  console.log('🔒 CSP applied for production environment');
};

/**
 * فحص ما إذا كان CSP مفعل
 */
export const isCSPEnabled = (): boolean => {
  const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
  return !!cspMeta && !!cspMeta.getAttribute('content');
};
