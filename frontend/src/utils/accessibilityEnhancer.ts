/**
 * Comprehensive accessibility enhancer for the entire application
 */

import { initializeAccessibility } from './accessibilityUtils';

/**
 * Global accessibility configuration
 */
interface AccessibilityConfig {
  autoFix: boolean;
  enhance: boolean;
  audit: boolean;
  logLevel: 'none' | 'error' | 'warn' | 'info' | 'debug';
}

const defaultConfig: AccessibilityConfig = {
  autoFix: true,
  enhance: true,
  audit: process.env.NODE_ENV === 'development',
  logLevel: process.env.NODE_ENV === 'development' ? 'info' : 'error'
};

/**
 * Enhanced accessibility manager
 */
class AccessibilityManager {
  private config: AccessibilityConfig;
  private observer: MutationObserver | null = null;
  private initialized = false;

  constructor(config: Partial<AccessibilityConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
  }

  /**
   * Initialize accessibility enhancements
   */
  public initialize(): void {
    if (this.initialized) {
      this.log('warn', 'Accessibility manager already initialized');
      return;
    }

    this.log('info', 'Initializing accessibility enhancements...');

    // Apply initial fixes
    this.applyInitialFixes();

    // Set up mutation observer for dynamic content
    this.setupMutationObserver();

    // Add global event listeners
    this.setupGlobalListeners();

    this.initialized = true;
    this.log('info', '✅ Accessibility enhancements initialized successfully');
  }

  /**
   * Apply initial accessibility fixes
   */
  private applyInitialFixes(): void {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.processDocument();
      });
    } else {
      this.processDocument();
    }
  }

  /**
   * Process the entire document for accessibility
   */
  private processDocument(): void {
    if (this.config.autoFix || this.config.enhance) {
      initializeAccessibility();
    }

    // Fix common accessibility issues
    this.fixCommonIssues();

    // Add ARIA landmarks
    this.addAriaLandmarks();

    // Enhance keyboard navigation
    this.enhanceKeyboardNavigation();
  }

  /**
   * Fix common accessibility issues
   */
  private fixCommonIssues(): void {
    // Fix images without alt text
    const images = document.querySelectorAll('img:not([alt])');
    images.forEach((img) => {
      const imgElement = img as HTMLImageElement;
      imgElement.alt = imgElement.title || 'صورة';
    });

    // Fix buttons without accessible names
    const buttons = document.querySelectorAll('button:not([aria-label]):not([title])');
    buttons.forEach((button) => {
      const buttonElement = button as HTMLButtonElement;
      if (!buttonElement.textContent?.trim()) {
        buttonElement.setAttribute('aria-label', 'زر');
      }
    });

    // Fix links without accessible names
    const links = document.querySelectorAll('a:not([aria-label]):not([title])');
    links.forEach((link) => {
      const linkElement = link as HTMLAnchorElement;
      if (!linkElement.textContent?.trim()) {
        linkElement.setAttribute('aria-label', 'رابط');
      }
    });
  }

  /**
   * Add ARIA landmarks
   */
  private addAriaLandmarks(): void {
    // Add main landmark
    const main = document.querySelector('main');
    if (main && !main.getAttribute('role')) {
      main.setAttribute('role', 'main');
    }

    // Add navigation landmarks
    const navs = document.querySelectorAll('nav');
    navs.forEach((nav) => {
      if (!nav.getAttribute('role')) {
        nav.setAttribute('role', 'navigation');
      }
    });

    // Add banner landmark
    const header = document.querySelector('header');
    if (header && !header.getAttribute('role')) {
      header.setAttribute('role', 'banner');
    }

    // Add contentinfo landmark
    const footer = document.querySelector('footer');
    if (footer && !footer.getAttribute('role')) {
      footer.setAttribute('role', 'contentinfo');
    }
  }

  /**
   * Enhance keyboard navigation
   */
  private enhanceKeyboardNavigation(): void {
    // Add skip links
    this.addSkipLinks();

    // Enhance focus management
    this.enhanceFocusManagement();

    // Add keyboard shortcuts
    this.addKeyboardShortcuts();
  }

  /**
   * Add skip links for keyboard navigation
   */
  private addSkipLinks(): void {
    const existingSkipLink = document.querySelector('.skip-link');
    if (existingSkipLink) return;

    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.textContent = 'تخطي إلى المحتوى الرئيسي';
    skipLink.className = 'skip-link sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:bg-primary-600 focus:text-white focus:px-4 focus:py-2 focus:rounded';
    
    document.body.insertBefore(skipLink, document.body.firstChild);
  }

  /**
   * Enhance focus management
   */
  private enhanceFocusManagement(): void {
    // Add focus indicators
    const style = document.createElement('style');
    style.textContent = `
      .focus-visible:focus {
        outline: 2px solid #3b82f6;
        outline-offset: 2px;
      }
      
      .skip-link {
        position: absolute;
        left: -10000px;
        top: auto;
        width: 1px;
        height: 1px;
        overflow: hidden;
      }
      
      .skip-link:focus {
        position: absolute;
        left: 6px;
        top: 7px;
        width: auto;
        height: auto;
        overflow: visible;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * Add keyboard shortcuts
   */
  private addKeyboardShortcuts(): void {
    document.addEventListener('keydown', (event) => {
      // Alt + M: Focus main content
      if (event.altKey && event.key === 'm') {
        event.preventDefault();
        const main = document.querySelector('main, #main-content, [role="main"]') as HTMLElement;
        if (main) {
          main.focus();
        }
      }

      // Alt + N: Focus navigation
      if (event.altKey && event.key === 'n') {
        event.preventDefault();
        const nav = document.querySelector('nav, [role="navigation"]') as HTMLElement;
        if (nav) {
          nav.focus();
        }
      }
    });
  }

  /**
   * Setup mutation observer for dynamic content
   */
  private setupMutationObserver(): void {
    this.observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              this.processElement(element);
            }
          });
        }
      });
    });

    this.observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  /**
   * Process a single element for accessibility
   */
  private processElement(element: Element): void {
    // Process forms
    if (element.tagName === 'FORM') {
      initializeAccessibility();
    }

    // Process new form fields
    const formFields = element.querySelectorAll('input, select, textarea');
    formFields.forEach((field) => {
      const fieldElement = field as HTMLInputElement;
      if (!fieldElement.id && fieldElement.name) {
        fieldElement.id = `${fieldElement.name}-${Math.random().toString(36).substring(2, 11)}`;
      }
    });
  }

  /**
   * Setup global event listeners
   */
  private setupGlobalListeners(): void {
    // Listen for form submissions to validate accessibility
    document.addEventListener('submit', (event) => {
      if (this.config.audit) {
        const form = event.target as HTMLFormElement;
        this.auditForm(form);
      }
    });
  }

  /**
   * Audit a form for accessibility issues
   */
  private auditForm(form: HTMLFormElement): void {
    const issues: string[] = [];
    
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach((input, index) => {
      const element = input as HTMLInputElement;
      
      if (!element.id) {
        issues.push(`Form field at index ${index} missing id attribute`);
      }
      
      if (!element.name) {
        issues.push(`Form field at index ${index} missing name attribute`);
      }
    });

    if (issues.length > 0) {
      this.log('warn', 'Form accessibility issues found:', issues);
    }
  }

  /**
   * Log messages based on configuration
   */
  private log(level: 'error' | 'warn' | 'info' | 'debug', message: string, ...args: any[]): void {
    const levels = ['none', 'error', 'warn', 'info', 'debug'];
    const configLevel = levels.indexOf(this.config.logLevel);
    const messageLevel = levels.indexOf(level);

    if (messageLevel <= configLevel) {
      console[level](`[A11Y] ${message}`, ...args);
    }
  }

  /**
   * Destroy the accessibility manager
   */
  public destroy(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
    this.initialized = false;
    this.log('info', 'Accessibility manager destroyed');
  }
}

// Create global instance
export const accessibilityManager = new AccessibilityManager();

// Auto-initialize in development
if (process.env.NODE_ENV === 'development') {
  accessibilityManager.initialize();
}

export default AccessibilityManager;
