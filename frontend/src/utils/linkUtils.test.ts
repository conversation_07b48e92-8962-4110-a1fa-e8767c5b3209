/**
 * اختبارات لأدوات الروابط
 */

import { detectLinks, parseTextWithLinks, isValidUrl, extractDomain, shortenUrl } from './linkUtils';

// اختبار اكتشاف الروابط
console.log('=== اختبار اكتشاف الروابط ===');

const testTexts = [
  'تفضل هذا الرابط https://www.google.com للبحث',
  'زر موقعنا www.example.com أو http://test.com',
  'رابط بدون بروتوكول: github.com/user/repo',
  'عدة روابط: https://facebook.com و https://twitter.com',
  'نص بدون روابط',
  'رابط مع معاملات: https://example.com/page?id=123&name=test#section'
];

testTexts.forEach((text, index) => {
  console.log(`\nالنص ${index + 1}: "${text}"`);
  const links = detectLinks(text);
  console.log('الروابط المكتشفة:', links);
  
  const parts = parseTextWithLinks(text);
  console.log('أجزاء النص:', parts);
});

// اختبار التحقق من صحة الروابط
console.log('\n=== اختبار التحقق من صحة الروابط ===');
const urlsToTest = [
  'https://www.google.com',
  'http://example.com',
  'invalid-url',
  'ftp://files.example.com',
  'https://subdomain.example.com/path/to/page?param=value#anchor'
];

urlsToTest.forEach(url => {
  console.log(`${url}: ${isValidUrl(url) ? 'صحيح' : 'غير صحيح'}`);
});

// اختبار استخراج النطاق
console.log('\n=== اختبار استخراج النطاق ===');
urlsToTest.forEach(url => {
  if (isValidUrl(url)) {
    console.log(`${url} -> ${extractDomain(url)}`);
  }
});

// اختبار تقصير الروابط
console.log('\n=== اختبار تقصير الروابط ===');
const longUrls = [
  'https://www.example.com/very/long/path/to/some/resource',
  'https://github.com/user/repository/blob/main/src/components/Chat/MessagesList.tsx',
  'short.com'
];

longUrls.forEach(url => {
  console.log(`الأصلي: ${url}`);
  console.log(`مقصر (30): ${shortenUrl(url, 30)}`);
  console.log(`مقصر (50): ${shortenUrl(url, 50)}`);
  console.log('---');
});

export {};
