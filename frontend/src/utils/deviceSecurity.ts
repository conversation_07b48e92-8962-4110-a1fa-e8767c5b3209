/**
 * Device Security Utility
 * للتحقق من حظر الجهاز في الواجهة الأمامية
 */

// تعريف API_BASE_URL محلياً لتجنب مشاكل الاستيراد
const API_BASE_URL = 'http://localhost:8002';

interface DeviceSecurityResponse {
  isBlocked: boolean;
  deviceId?: string;
  message?: string;
  redirectUrl?: string;
}

class DeviceSecurityManager {
  private checkInterval: number | null = null;
  private isChecking = false;

  /**
   * بدء فحص أمان الجهاز بشكل دوري - معطل لتوفير موارد النظام
   */
  startSecurityCheck() {
    // فحص فوري فقط - بدون تحديث دوري
    this.checkDeviceSecurity();

    // تعطيل الفحص الدوري لتوفير موارد النظام
    // this.checkInterval = window.setInterval(() => {
    //   this.checkDeviceSecurity();
    // }, 30000);
  }

  /**
   * إيقاف فحص أمان الجهاز
   */
  stopSecurityCheck() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  /**
   * فحص أمان الجهاز
   */
  private async checkDeviceSecurity(): Promise<void> {
    if (this.isChecking) return;

    this.isChecking = true;

    try {
      // محاولة طلب بسيط للتحقق من حالة الجهاز
      const response = await fetch(`${API_BASE_URL}/api/settings/public`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      // إذا كان الطلب محظور (403)
      if (response.status === 403) {
        const data = await response.json().catch(() => ({}));

        if (data.error_code === 'DEVICE_BLOCKED') {
          this.handleDeviceBlocked(data);
          return;
        }
      }

      // إذا كان هناك خطأ في الشبكة أو الخادم غير متاح
      if (!response.ok && response.status >= 500) {
        console.warn('خطأ في الاتصال بالخادم:', response.status);
        return;
      }

    } catch (error) {
      // تجاهل أخطاء الشبكة لتجنب إزعاج المستخدم
      console.warn('خطأ في فحص أمان الجهاز:', error);
    } finally {
      this.isChecking = false;
    }
  }

  /**
   * التعامل مع الجهاز المحظور
   */
  private handleDeviceBlocked(data: any): void {
    console.warn('تم اكتشاف حظر الجهاز:', data);

    // إيقاف الفحص الدوري
    this.stopSecurityCheck();

    // إعادة توجيه إلى صفحة الحظر
    const blockUrl = data.redirect_url || '/device-blocked';

    // التأكد من أن الرابط يشير إلى الخادم الصحيح
    const fullBlockUrl = blockUrl.startsWith('http')
      ? blockUrl
      : `${API_BASE_URL}${blockUrl}`;

    // إعادة توجيه فورية
    window.location.href = fullBlockUrl;
  }

  /**
   * فحص فوري لحالة الجهاز
   */
  async checkDeviceStatus(): Promise<DeviceSecurityResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/settings/public`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.status === 403) {
        const data = await response.json().catch(() => ({}));

        if (data.error_code === 'DEVICE_BLOCKED') {
          return {
            isBlocked: true,
            deviceId: data.device_id,
            message: data.message || 'تم حظر هذا الجهاز',
            redirectUrl: data.redirect_url
          };
        }
      }

      return { isBlocked: false };

    } catch (error) {
      console.warn('خطأ في فحص حالة الجهاز:', error);
      return { isBlocked: false };
    }
  }
}

// إنشاء مثيل واحد للاستخدام في التطبيق
export const deviceSecurity = new DeviceSecurityManager();

// تصدير الكلاس للاستخدام المتقدم
export { DeviceSecurityManager };
export type { DeviceSecurityResponse };
