/**
 * Currency formatting utilities
 * Handles currency symbol and decimal places from settings
 * Updated to integrate with NumberFormattingService for enhanced formatting
 */

import { useEffect, useState } from 'react';
import api from '../lib/axios';
import { numberFormattingService } from '../services/numberFormattingService';

// Default values
const DEFAULT_CURRENCY_SYMBOL = 'د.ل';
const DEFAULT_DECIMAL_PLACES = 2;

// Settings interface
interface CurrencySettings {
  currencySymbol: string;
  decimalPlaces: number;
}

// Cache for settings to avoid repeated API calls
let settingsCache: CurrencySettings | null = null;
let lastFetchTime = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Fetch currency settings from API
 */
export const fetchCurrencySettings = async (): Promise<CurrencySettings> => {
  const now = Date.now();
  
  // Return cached settings if still valid
  if (settingsCache && (now - lastFetchTime) < CACHE_DURATION) {
    return settingsCache;
  }

  try {
    const response = await api.get('/api/settings/public');
    const settings = response.data;
    
    const currencySettings: CurrencySettings = {
      currencySymbol: settings.currency_symbol || DEFAULT_CURRENCY_SYMBOL,
      decimalPlaces: parseInt(settings.decimal_places) || DEFAULT_DECIMAL_PLACES
    };
    
    // Update cache
    settingsCache = currencySettings;
    lastFetchTime = now;
    
    return currencySettings;
  } catch (error) {
    console.warn('Failed to fetch currency settings, using defaults:', error);
    
    // Return defaults if API fails
    const defaultSettings: CurrencySettings = {
      currencySymbol: DEFAULT_CURRENCY_SYMBOL,
      decimalPlaces: DEFAULT_DECIMAL_PLACES
    };
    
    return defaultSettings;
  }
};

/**
 * Format a number as currency with proper decimal places and symbol
 */
export const formatCurrency = async (amount: number): Promise<string> => {
  const settings = await fetchCurrencySettings();
  return `${amount.toFixed(settings.decimalPlaces)} ${settings.currencySymbol}`;
};

/**
 * Format a number with proper decimal places (without currency symbol)
 */
export const formatNumber = async (amount: number): Promise<string> => {
  const settings = await fetchCurrencySettings();
  return amount.toFixed(settings.decimalPlaces);
};

/**
 * Get currency symbol from settings
 */
export const getCurrencySymbol = async (): Promise<string> => {
  const settings = await fetchCurrencySettings();
  return settings.currencySymbol;
};

/**
 * Get decimal places from settings
 */
export const getDecimalPlaces = async (): Promise<number> => {
  const settings = await fetchCurrencySettings();
  return settings.decimalPlaces;
};

/**
 * React hook for currency settings
 */
export const useCurrencySettings = () => {
  const [settings, setSettings] = useState<CurrencySettings>({
    currencySymbol: DEFAULT_CURRENCY_SYMBOL,
    decimalPlaces: DEFAULT_DECIMAL_PLACES
  });
  const [loading, setLoading] = useState(true);
  const [advancedSettings, setAdvancedSettings] = useState<any>(null);

  useEffect(() => {
    const loadSettings = async () => {
      try {
        const currencySettings = await fetchCurrencySettings();
        setSettings(currencySettings);

        // تحميل الإعدادات المتقدمة أيضاً
        try {
          const advanced = await numberFormattingService.getCurrentSettings();
          setAdvancedSettings(advanced);
        } catch (error) {
          console.warn('Could not load advanced formatting settings:', error);
        }
      } catch (error) {
        console.error('Error loading currency settings:', error);
      } finally {
        setLoading(false);
      }
    };

    loadSettings();
  }, []);

  const formatCurrency = (amount: number): string => {
    // استخدام الإعدادات المتقدمة إذا كانت متاحة
    if (advancedSettings) {
      try {
        const fixedAmount = advancedSettings.showDecimals ?
          amount.toFixed(advancedSettings.decimalPlaces) :
          Math.round(amount).toString();

        // تطبيق الفواصل
        let formattedNumber = fixedAmount;
        if (advancedSettings.separatorType !== 'none') {
          const parts = fixedAmount.split('.');
          const integerPart = parts[0];
          const decimalPart = parts[1];
          const separator = advancedSettings.separatorType === 'comma' ? ',' : ' ';
          const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, separator);
          formattedNumber = decimalPart ? `${formattedInteger}.${decimalPart}` : formattedInteger;
        }

        // إضافة رمز العملة
        if (advancedSettings.symbolPosition === 'before') {
          return `${advancedSettings.currencySymbol} ${formattedNumber}`;
        } else {
          return `${formattedNumber} ${advancedSettings.currencySymbol}`;
        }
      } catch (error) {
        console.warn('Error using advanced formatting, falling back to basic:', error);
      }
    }

    // fallback للتنسيق البسيط
    return `${amount.toFixed(settings.decimalPlaces)} ${settings.currencySymbol}`;
  };

  const formatNumber = (amount: number): string => {
    return amount.toFixed(settings.decimalPlaces);
  };

  return {
    currencySymbol: settings.currencySymbol,
    decimalPlaces: settings.decimalPlaces,
    formatCurrency,
    formatNumber,
    loading
  };
};

/**
 * Enhanced currency formatting using NumberFormattingService
 * Provides advanced number formatting with separators and custom options
 */
export const formatCurrencyAdvanced = async (amount: number): Promise<string> => {
  try {
    return await numberFormattingService.formatCurrency(amount);
  } catch (error) {
    console.warn('Failed to use advanced formatting, falling back to basic:', error);
    // Fallback to basic formatting
    const settings = await fetchCurrencySettings();
    return `${amount.toFixed(settings.decimalPlaces)} ${settings.currencySymbol}`;
  }
};

/**
 * Enhanced number formatting using NumberFormattingService
 * Provides advanced number formatting with separators (without currency symbol)
 */
export const formatNumberAdvanced = async (amount: number): Promise<string> => {
  try {
    return await numberFormattingService.formatNumber(amount);
  } catch (error) {
    console.warn('Failed to use advanced formatting, falling back to basic:', error);
    // Fallback to basic formatting
    const settings = await fetchCurrencySettings();
    return amount.toFixed(settings.decimalPlaces);
  }
};

/**
 * Get formatted example for preview purposes
 */
export const getFormattedExample = async (amount: number): Promise<string> => {
  try {
    return await numberFormattingService.getFormattedExample(amount);
  } catch (error) {
    console.warn('Failed to get formatted example:', error);
    return await formatCurrencyAdvanced(amount);
  }
};

/**
 * Clear all caches (both currency and number formatting)
 */
export const clearAllCaches = (): void => {
  // Clear currency cache
  settingsCache = null;
  lastFetchTime = 0;

  // Clear number formatting cache
  numberFormattingService.clearCache();
};

/**
 * Clear settings cache (useful when settings are updated)
 * @deprecated Use clearAllCaches() instead for comprehensive cache clearing
 */
export const clearCurrencyCache = (): void => {
  clearAllCaches();
};
