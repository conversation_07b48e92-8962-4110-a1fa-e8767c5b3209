/**
 * Accessibility utilities for form elements
 */

/**
 * Generate unique ID for form elements
 */
export const generateUniqueId = (prefix: string): string => {
  return `${prefix}-${Math.random().toString(36).substring(2, 11)}`;
};

/**
 * Validate form accessibility
 */
export const validateFormAccessibility = (formElement: HTMLFormElement): {
  isValid: boolean;
  issues: string[];
} => {
  const issues: string[] = [];
  
  // Check for inputs without id or name
  const inputs = formElement.querySelectorAll('input, select, textarea');
  inputs.forEach((input, index) => {
    const element = input as HTMLInputElement;
    
    if (!element.id && !element.name) {
      issues.push(`Form field at index ${index} has neither id nor name attribute`);
    }
    
    if (!element.id) {
      issues.push(`Form field "${element.name || `at index ${index}`}" missing id attribute`);
    }
    
    if (!element.name) {
      issues.push(`Form field "${element.id || `at index ${index}`}" missing name attribute`);
    }
  });
  
  // Check for labels without proper association
  const labels = formElement.querySelectorAll('label');
  labels.forEach((label, index) => {
    const labelElement = label as HTMLLabelElement;
    
    if (!labelElement.htmlFor && !labelElement.querySelector('input, select, textarea')) {
      issues.push(`Label at index ${index} is not associated with any form field`);
    }
  });
  
  return {
    isValid: issues.length === 0,
    issues
  };
};

/**
 * Auto-fix form accessibility issues
 */
export const autoFixFormAccessibility = (formElement: HTMLFormElement): void => {
  // Fix inputs without id
  const inputs = formElement.querySelectorAll('input, select, textarea');
  inputs.forEach((input) => {
    const element = input as HTMLInputElement;
    
    if (!element.id && element.name) {
      element.id = generateUniqueId(element.name);
    }
  });
  
  // Fix labels without proper association
  const labels = formElement.querySelectorAll('label');
  labels.forEach((label) => {
    const labelElement = label as HTMLLabelElement;
    
    if (!labelElement.htmlFor) {
      const associatedInput = labelElement.querySelector('input, select, textarea') as HTMLInputElement;
      if (associatedInput && associatedInput.id) {
        labelElement.htmlFor = associatedInput.id;
      }
    }
  });
};

/**
 * Add ARIA attributes for better accessibility
 */
export const enhanceFormAccessibility = (formElement: HTMLFormElement): void => {
  // Add role and aria-label to form if missing
  if (!formElement.getAttribute('role')) {
    formElement.setAttribute('role', 'form');
  }
  
  // Add aria-required to required fields
  const requiredInputs = formElement.querySelectorAll('input[required], select[required], textarea[required]');
  requiredInputs.forEach((input) => {
    const element = input as HTMLInputElement;
    if (!element.getAttribute('aria-required')) {
      element.setAttribute('aria-required', 'true');
    }
  });
  
  // Add aria-invalid to fields with errors
  const errorInputs = formElement.querySelectorAll('.border-red-500, .error, [data-error]');
  errorInputs.forEach((input) => {
    const element = input as HTMLInputElement;
    if (!element.getAttribute('aria-invalid')) {
      element.setAttribute('aria-invalid', 'true');
    }
  });
};

/**
 * Check and report accessibility issues in console
 */
export const auditFormAccessibility = (formElement: HTMLFormElement): void => {
  const { isValid, issues } = validateFormAccessibility(formElement);
  
  if (!isValid) {
    console.group('🔍 Form Accessibility Issues Found:');
    issues.forEach((issue, index) => {
      console.warn(`${index + 1}. ${issue}`);
    });
    console.groupEnd();
    
    console.info('💡 Run autoFixFormAccessibility() to automatically fix these issues.');
  } else {
    console.log('✅ Form accessibility validation passed!');
  }
};

/**
 * Initialize accessibility improvements for all forms on page
 */
export const initializeAccessibility = (): void => {
  const forms = document.querySelectorAll('form');
  
  forms.forEach((form) => {
    autoFixFormAccessibility(form);
    enhanceFormAccessibility(form);
  });
  
  console.log(`✅ Accessibility improvements applied to ${forms.length} forms`);
};
