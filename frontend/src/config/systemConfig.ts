/**
 * إعدادات النظام المحسنة للأداء
 * تحكم في جميع جوانب نظام تسجيل الأخطاء ومراقبة الأداء
 */

export interface SystemConfig {
  // إعدادات تسجيل الأخطاء
  errorLogging: {
    enabled: boolean;
    maxQueueSize: number;
    flushInterval: number; // بالميلي ثانية
    logLevels: string[];
    enablePerformanceLogging: boolean;
    enableDebugMode: boolean;
    enableAutoFlush: boolean;
  };
  
  // إعدادات مراقبة الأداء
  performanceMonitoring: {
    enabled: boolean;
    interval: number; // بالميلي ثانية
    maxHistorySize: number;
    enableCPUMonitoring: boolean;
    enableNetworkMonitoring: boolean;
    enableDatabaseMonitoring: boolean;
    enableMemoryMonitoring: boolean;
    enableRenderingMonitoring: boolean;
  };
  
  // إعدادات واجهة المستخدم
  ui: {
    enableTerminalMode: boolean;
    maxLogsDisplay: number;
    autoRefreshInterval: number; // 0 = إيقاف التحديث التلقائي
    enableAnimations: boolean;
    compactMode: boolean;
  };
  
  // إعدادات الشبكة
  network: {
    requestTimeout: number;
    maxRetries: number;
    enableRequestCaching: boolean;
    cacheTimeout: number;
  };
}

// الإعدادات الافتراضية المحسنة للأداء
export const defaultSystemConfig: SystemConfig = {
  errorLogging: {
    enabled: true,
    maxQueueSize: 15, // تقليل حجم القائمة
    flushInterval: 300000, // 5 دقائق
    logLevels: ['ERROR', 'CRITICAL'], // الأخطاء المهمة فقط
    enablePerformanceLogging: false, // إيقاف تسجيل الأداء
    enableDebugMode: false,
    enableAutoFlush: true
  },
  
  performanceMonitoring: {
    enabled: true,
    interval: 60000, // دقيقة واحدة
    maxHistorySize: 5, // 5 قراءات فقط
    enableCPUMonitoring: false, // إيقاف مراقبة المعالج
    enableNetworkMonitoring: true,
    enableDatabaseMonitoring: false, // إيقاف مراقبة قاعدة البيانات
    enableMemoryMonitoring: true,
    enableRenderingMonitoring: false // إيقاف مراقبة الرسم
  },
  
  ui: {
    enableTerminalMode: true,
    maxLogsDisplay: 50, // تحديد عدد السجلات المعروضة
    autoRefreshInterval: 0, // إيقاف التحديث التلقائي
    enableAnimations: false, // إيقاف الرسوم المتحركة
    compactMode: true
  },
  
  network: {
    requestTimeout: 45000, // 45 ثانية للخادم المحسن
    maxRetries: 2,
    enableRequestCaching: true,
    cacheTimeout: 300000 // 5 دقائق
  }
};

// إعدادات الأداء العالي (للأجهزة القوية)
export const highPerformanceConfig: SystemConfig = {
  ...defaultSystemConfig,
  errorLogging: {
    ...defaultSystemConfig.errorLogging,
    maxQueueSize: 30,
    flushInterval: 120000, // دقيقتان
    logLevels: ['WARNING', 'ERROR', 'CRITICAL'],
    enablePerformanceLogging: true
  },
  
  performanceMonitoring: {
    ...defaultSystemConfig.performanceMonitoring,
    interval: 30000, // 30 ثانية
    maxHistorySize: 10,
    enableCPUMonitoring: true,
    enableDatabaseMonitoring: true,
    enableRenderingMonitoring: true
  },
  
  ui: {
    ...defaultSystemConfig.ui,
    maxLogsDisplay: 100,
    autoRefreshInterval: 30000, // 30 ثانية
    enableAnimations: true,
    compactMode: false
  }
};

// إعدادات الأداء المنخفض (للأجهزة الضعيفة)
export const lowPerformanceConfig: SystemConfig = {
  ...defaultSystemConfig,
  errorLogging: {
    ...defaultSystemConfig.errorLogging,
    maxQueueSize: 10,
    flushInterval: 600000, // 10 دقائق
    logLevels: ['CRITICAL'], // الأخطاء الحرجة فقط
    enablePerformanceLogging: false
  },
  
  performanceMonitoring: {
    ...defaultSystemConfig.performanceMonitoring,
    enabled: false, // إيقاف مراقبة الأداء تماماً
    interval: 120000, // دقيقتان
    maxHistorySize: 3
  },
  
  ui: {
    ...defaultSystemConfig.ui,
    maxLogsDisplay: 25,
    autoRefreshInterval: 0,
    enableAnimations: false,
    compactMode: true
  }
};

// كلاس لإدارة الإعدادات
class SystemConfigManager {
  private config: SystemConfig;
  private listeners: ((config: SystemConfig) => void)[] = [];

  constructor(initialConfig: SystemConfig = defaultSystemConfig) {
    this.config = { ...initialConfig };
    this.loadFromStorage();
  }

  // تحميل الإعدادات من التخزين المحلي
  private loadFromStorage(): void {
    try {
      const stored = localStorage.getItem('smartpos_system_config');
      if (stored) {
        const parsedConfig = JSON.parse(stored);
        this.config = { ...this.config, ...parsedConfig };
      }
    } catch (error) {
      console.warn('Failed to load system config from storage:', error);
    }
  }

  // حفظ الإعدادات في التخزين المحلي
  private saveToStorage(): void {
    try {
      localStorage.setItem('smartpos_system_config', JSON.stringify(this.config));
    } catch (error) {
      console.warn('Failed to save system config to storage:', error);
    }
  }

  // الحصول على الإعدادات الحالية
  getConfig(): SystemConfig {
    return { ...this.config };
  }

  // تحديث الإعدادات
  updateConfig(newConfig: Partial<SystemConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.saveToStorage();
    this.notifyListeners();
  }

  // تطبيق إعدادات محددة مسبقاً
  applyPreset(preset: 'default' | 'high-performance' | 'low-performance'): void {
    switch (preset) {
      case 'high-performance':
        this.config = { ...highPerformanceConfig };
        break;
      case 'low-performance':
        this.config = { ...lowPerformanceConfig };
        break;
      default:
        this.config = { ...defaultSystemConfig };
    }
    this.saveToStorage();
    this.notifyListeners();
  }

  // إضافة مستمع للتغييرات
  addListener(listener: (config: SystemConfig) => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  // إشعار المستمعين بالتغييرات
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.config);
      } catch (error) {
        console.error('Error in config listener:', error);
      }
    });
  }

  // إعادة تعيين الإعدادات للافتراضية
  reset(): void {
    this.config = { ...defaultSystemConfig };
    this.saveToStorage();
    this.notifyListeners();
  }

  // تشخيص الأداء واقتراح الإعدادات المناسبة
  autoDetectPerformance(): 'high-performance' | 'default' | 'low-performance' {
    const memory = (performance as any).memory;
    const connection = (navigator as any).connection;
    
    let score = 0;
    
    // فحص الذاكرة
    if (memory) {
      const memoryGB = memory.jsHeapSizeLimit / (1024 * 1024 * 1024);
      if (memoryGB > 4) score += 2;
      else if (memoryGB > 2) score += 1;
    }
    
    // فحص الاتصال
    if (connection) {
      if (connection.effectiveType === '4g') score += 2;
      else if (connection.effectiveType === '3g') score += 1;
    }
    
    // فحص عدد المعالجات
    if (navigator.hardwareConcurrency > 4) score += 2;
    else if (navigator.hardwareConcurrency > 2) score += 1;
    
    if (score >= 5) return 'high-performance';
    if (score >= 3) return 'default';
    return 'low-performance';
  }
}

// إنشاء مثيل واحد للاستخدام في جميع أنحاء التطبيق
export const systemConfigManager = new SystemConfigManager();

// تطبيق الإعدادات المناسبة تلقائياً
const detectedPerformance = systemConfigManager.autoDetectPerformance();
console.log(`Detected performance level: ${detectedPerformance}`);

export default systemConfigManager;
