/**
 * أنواع البيانات للمنتجات
 * Types for product data structures
 */

// Product variant for multiple choice products
export interface ProductVariant {
  id: string;
  combination: Record<number, number>; // attributeId -> valueId
  combinationText: string;
  price: number;
  costPrice: number;
  sku?: string;
  isActive: boolean;
}

// Base product interface
export interface Product {
  id?: number;
  name: string;
  name_ar: string;
  description?: string;
  sku: string;
  barcode?: string;
  barcode_type: string;
  
  // Basic info
  category_id: number;
  brand_id?: number | null;
  unit_id: number;
  
  // Product type and variants
  product_type: 'simple' | 'variable';
  variants?: ProductVariant[];
  selected_variant_attributes?: number[];
  
  // Pricing (for simple products)
  price: number;
  cost_price: number;
  
  // Tax settings
  tax_type_id?: number | null;
  tax_rate_id?: number | null;
  
  // Discount settings
  discount_type?: 'percentage' | 'fixed';
  discount_value?: number;
  
  // Images
  images?: string[];
  main_image?: string;
  
  // Status
  is_active: boolean;
  
  // Timestamps
  created_at?: string;
  updated_at?: string;
}

// Create product form data
export interface CreateProductData {
  name: string;
  name_ar: string;
  description?: string;
  sku: string;
  barcode?: string;
  barcode_type: string;
  
  category_id: number;
  brand_id?: number | null;
  unit_id: number;
  
  product_type: 'simple' | 'variable';
  variants?: ProductVariant[];
  selected_variant_attributes?: number[];
  
  price: number;
  cost_price: number;
  
  tax_type_id?: number | null;
  tax_rate_id?: number | null;
  
  discount_type?: 'percentage' | 'fixed';
  discount_value?: number;
  
  images?: File[];
  
  is_active: boolean;
}

// Update product form data
export interface UpdateProductData extends Partial<CreateProductData> {
  id: number;
}

// Product filters
export interface ProductFilters {
  search: string;
  category_id?: number | null;
  brand_id?: number | null;
  product_type?: 'all' | 'simple' | 'variable';
  status: 'all' | 'active' | 'inactive';
  price_min?: number;
  price_max?: number;
}

// Product form validation errors
export interface ProductFormErrors {
  name?: string;
  name_ar?: string;
  sku?: string;
  category_id?: string;
  unit_id?: string;
  product_type?: string;
  price?: string;
  cost_price?: string;
  tax_type_id?: string;
  tax_rate_id?: string;
  variants?: string;
  [key: string]: string | undefined;
}

// Product form state
export interface ProductFormState {
  data: CreateProductData;
  errors: ProductFormErrors;
  loading: boolean;
  submitted: boolean;
}

// Barcode types
export const BARCODE_TYPES = {
  CODE128: { value: 'CODE128', label: 'Code 128' },
  CODE39: { value: 'CODE39', label: 'Code 39' },
  EAN13: { value: 'EAN13', label: 'EAN-13' },
  EAN8: { value: 'EAN8', label: 'EAN-8' },
  UPC: { value: 'UPC', label: 'UPC' },
  QRCODE: { value: 'QRCODE', label: 'QR Code' }
} as const;

export type BarcodeType = keyof typeof BARCODE_TYPES;