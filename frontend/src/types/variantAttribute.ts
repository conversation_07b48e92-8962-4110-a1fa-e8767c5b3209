/**
 * أنواع البيانات لخصائص المتغيرات
 * تطبق مبادئ TypeScript مع دعم كامل للواجهة العربية
 */

export interface VariantValue {
  id: number;
  attribute_id: number;
  value: string;
  value_ar: string;
  color_code?: string | null;
  is_active: boolean;
  sort_order: number;
  created_at: string;
}

export interface VariantAttribute {
  id: number;
  name: string;
  name_ar: string;
  description?: string | null;
  attribute_type: 'text' | 'color' | 'list' | 'number';
  is_required: boolean;
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at?: string | null;
  created_by: number;
  updated_by?: number | null;
  values: VariantValue[];
}

export interface CreateVariantValueData {
  value: string;
  value_ar: string;
  color_code?: string | null;
  is_active?: boolean;
  sort_order?: number;
}

export interface CreateVariantAttributeData {
  name: string;
  name_ar: string;
  description?: string | null;
  attribute_type: 'text' | 'color' | 'list' | 'number';
  is_required?: boolean;
  is_active?: boolean;
  sort_order?: number;
  values?: CreateVariantValueData[];
}

export interface UpdateVariantValueData {
  value?: string;
  value_ar?: string;
  color_code?: string | null;
  is_active?: boolean;
  sort_order?: number;
}

export interface UpdateVariantAttributeData {
  name?: string;
  name_ar?: string;
  description?: string | null;
  attribute_type?: 'text' | 'color' | 'list' | 'number';
  is_required?: boolean;
  is_active?: boolean;
  sort_order?: number;
}

export interface AttributeOrderUpdate {
  id: number;
  sort_order: number;
}

export interface ValueOrderUpdate {
  id: number;
  sort_order: number;
}

export interface VariantAttributeFilters {
  search: string;
  status: 'all' | 'active' | 'inactive';
  attributeType: 'all' | 'text' | 'color' | 'list' | 'number';
  hasValues: 'all' | 'yes' | 'no';
}

export interface VariantAttributeModalState {
  isOpen: boolean;
  mode: 'create' | 'edit';
  attribute: VariantAttribute | null;
}

export interface VariantValueModalState {
  isOpen: boolean;
  mode: 'create' | 'edit';
  attributeId: number | null;
  value: VariantValue | null;
}

export interface DeleteConfirmState {
  isOpen: boolean;
  type: 'attribute' | 'value';
  id: number | null;
  name: string;
}

export interface SuccessMessageState {
  isOpen: boolean;
  message: string;
}

export interface AttributeUsageInfo {
  is_used: boolean;
  values_count: number;
  products_count: number;
  can_delete: boolean;
  warning_message?: string;
}

export interface DeleteAttributeResult {
  success: boolean;
  message: string;
  deleted_values_count: number;
  was_forced: boolean;
}

// أنواع الخصائص المدعومة مع التسميات العربية
export const ATTRIBUTE_TYPES = {
  text: { value: 'text', label: 'نص', label_en: 'Text' },
  color: { value: 'color', label: 'لون', label_en: 'Color' },
  list: { value: 'list', label: 'قائمة', label_en: 'List' },
  number: { value: 'number', label: 'رقم', label_en: 'Number' }
} as const;

// الخصائص الافتراضية
export const DEFAULT_ATTRIBUTES: CreateVariantAttributeData[] = [
  {
    name: 'Size',
    name_ar: 'الحجم',
    attribute_type: 'list',
    sort_order: 1,
    values: [
      { value: 'XS', value_ar: 'صغير جداً', sort_order: 1 },
      { value: 'S', value_ar: 'صغير', sort_order: 2 },
      { value: 'M', value_ar: 'متوسط', sort_order: 3 },
      { value: 'L', value_ar: 'كبير', sort_order: 4 },
      { value: 'XL', value_ar: 'كبير جداً', sort_order: 5 },
      { value: 'XXL', value_ar: 'كبير جداً جداً', sort_order: 6 }
    ]
  },
  {
    name: 'Color',
    name_ar: 'اللون',
    attribute_type: 'color',
    sort_order: 2,
    values: [
      { value: 'Red', value_ar: 'أحمر', color_code: '#FF0000', sort_order: 1 },
      { value: 'Blue', value_ar: 'أزرق', color_code: '#0000FF', sort_order: 2 },
      { value: 'Green', value_ar: 'أخضر', color_code: '#00FF00', sort_order: 3 },
      { value: 'Black', value_ar: 'أسود', color_code: '#000000', sort_order: 4 },
      { value: 'White', value_ar: 'أبيض', color_code: '#FFFFFF', sort_order: 5 }
    ]
  },
  {
    name: 'Material',
    name_ar: 'المادة',
    attribute_type: 'list',
    sort_order: 3,
    values: [
      { value: 'Cotton', value_ar: 'قطن', sort_order: 1 },
      { value: 'Leather', value_ar: 'جلد', sort_order: 2 },
      { value: 'Synthetic', value_ar: 'صناعي', sort_order: 3 },
      { value: 'Silk', value_ar: 'حرير', sort_order: 4 }
    ]
  }
];
