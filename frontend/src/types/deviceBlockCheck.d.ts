/**
 * تعريفات TypeScript لخدمة فحص حظر الأجهزة
 */

export interface DeviceBlockStatus {
  isBlocked: boolean;
  isPending: boolean;
  reason?: string;
  message?: string;
  errorCode?: string;
}

export declare function checkDeviceBlocked(): Promise<DeviceBlockStatus>;

export declare function startDeviceBlockCheck(
  onBlocked: () => void,
  onPending: () => void,
  intervalMs?: number
): () => void;
