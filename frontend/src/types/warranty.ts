/**
 * أنواع البيانات لنظام إدارة الضمانات
 * تطبق مبادئ TypeScript مع دعم كامل للواجهة العربية
 */

// ===== أنواع الضمانات =====
export interface WarrantyType {
  id: number;
  name: string;
  name_ar: string;
  description?: string | null;
  duration_months: number;
  coverage_type: 'full' | 'partial' | 'limited';
  terms_conditions?: string | null;
  is_active: boolean;
  created_at: string;
  updated_at?: string | null;
  created_by: number;
  updated_by?: number | null;
}

export interface CreateWarrantyTypeData {
  name: string;
  name_ar: string;
  description?: string | null;
  duration_months: number;
  coverage_type: 'full' | 'partial' | 'limited';
  terms_conditions?: string | null;
  is_active?: boolean;
}

export interface UpdateWarrantyTypeData {
  name?: string;
  name_ar?: string;
  description?: string | null;
  duration_months?: number;
  coverage_type?: 'full' | 'partial' | 'limited';
  terms_conditions?: string | null;
  is_active?: boolean;
}

// ===== ضمانات المنتجات =====
export interface ProductWarranty {
  id: number;
  product_id: number;
  product_name: string;
  product_barcode: string;
  warranty_type_id: number;
  warranty_type_name: string;
  warranty_number: string;
  purchase_date: string;
  start_date: string;
  end_date: string;
  customer_id?: number | null;
  customer_name?: string | null;
  status: 'active' | 'expired' | 'voided';
  has_claims: boolean; // هل تم المطالبة بهذا الضمان مسبقاً
  notes?: string | null;
  created_at: string;
  updated_at?: string | null;
  created_by: number;
  updated_by?: number | null;
}

export interface CreateWarrantyData {
  product_id: number;
  warranty_type_id: number;
  purchase_date: string;
  start_date: string;
  end_date: string;
  customer_id?: number | null;
  notes?: string | null;
}

export interface ExtendWarrantyData {
  additional_months: number;
  reason: string;
}

export interface VoidWarrantyData {
  reason: string;
}

// ===== مطالبات الضمان =====
export interface WarrantyClaim {
  id: number;
  warranty_id: number;
  warranty_number: string;
  product_name: string;
  customer_name?: string | null;
  claim_number: string;
  claim_type: 'repair' | 'replacement' | 'refund';
  issue_description: string;
  claim_date: string;
  status: 'pending' | 'approved' | 'rejected' | 'in_progress' | 'completed';
  resolution?: string | null;
  resolution_date?: string | null;
  estimated_cost?: number | null;
  actual_cost?: number | null;
  notes?: string | null;
  created_at: string;
  updated_at?: string | null;
  created_by: number;
  updated_by?: number | null;
}

export interface CreateClaimData {
  warranty_id: number;
  claim_type: 'repair' | 'replacement' | 'refund';
  issue_description: string;
  notes?: string | null;
}

export interface ProcessClaimData {
  status: 'approved' | 'rejected' | 'in_progress' | 'completed';
  resolution?: string | null;
  actual_cost?: number | null;
  notes?: string | null;
}

// ===== الفلاتر والبحث =====
export interface WarrantyTypeFilters {
  search?: string;
  status?: 'all' | 'active' | 'inactive';
  coverage_type?: 'all' | 'full' | 'partial' | 'limited';
}

export interface WarrantyFilters {
  search?: string;
  status?: 'all' | 'active' | 'expired' | 'voided' | 'claimed';
  warranty_type_id?: number | null;
  customer_id?: number | null;
  date_range?: {
    start: string;
    end: string;
  };
}

export interface ClaimFilters {
  search?: string;
  status?: 'all' | 'pending' | 'approved' | 'rejected' | 'in_progress' | 'completed';
  claim_type?: 'all' | 'repair' | 'replacement' | 'refund';
  date_range?: {
    start: string;
    end: string;
  };
}

// ===== الإحصائيات والتقارير =====
export interface WarrantyStats {
  total_warranties: number;
  active_warranties: number;
  expired_warranties: number;
  voided_warranties: number;
  total_claims: number;
  pending_claims: number;
  approved_claims: number;
  rejected_claims: number;
  total_claim_cost: number;
  average_claim_cost: number;
  claim_rate_percentage: number;
}

export interface ClaimStatistics {
  // إحصائيات أساسية
  total_claims: number;
  pending_claims: number;
  approved_claims: number;
  rejected_claims: number;
  in_progress_claims: number;
  completed_claims: number;

  // إحصائيات حسب النوع
  repair_claims: number;
  replacement_claims: number;
  refund_claims: number;

  // إحصائيات التكلفة
  average_resolution_days?: number | null;
  total_estimated_cost: number;
  total_actual_cost: number;
  cost_savings: number;

  // للتوافق مع النمط القديم (اختياري)
  by_type?: {
    repair: number;
    replacement: number;
    refund: number;
  };
  by_status?: {
    pending: number;
    approved: number;
    rejected: number;
    in_progress: number;
    completed: number;
  };
  monthly_trends?: {
    month: string;
    claims_count: number;
    total_cost: number;
  }[];
}

export interface ExpiringWarranty {
  id: number;
  warranty_number: string;
  product_name: string;
  customer_name?: string | null;
  end_date: string;
  days_remaining: number;
}

// ===== أنواع التقارير =====
export type ReportType = 'warranties' | 'claims' | 'expiring' | 'statistics';

export interface DateRange {
  start: string;
  end: string;
}

// ===== الثوابت =====
export const WARRANTY_STATUS_LABELS = {
  active: 'نشط',
  expired: 'منتهي',
  voided: 'ملغي'
} as const;

export const CLAIM_STATUS_LABELS = {
  pending: 'في الانتظار',
  approved: 'موافق عليه',
  rejected: 'مرفوض',
  in_progress: 'قيد التنفيذ',
  completed: 'مكتمل'
} as const;

export const CLAIM_TYPE_LABELS = {
  repair: 'إصلاح',
  replacement: 'استبدال',
  refund: 'استرداد'
} as const;

export const COVERAGE_TYPE_LABELS = {
  full: 'شامل',
  partial: 'جزئي',
  limited: 'محدود'
} as const;
