/**
 * أنواع البيانات المتعلقة بالصوت في نظام المحادثة
 */

export interface SoundOption {
  id: string;
  name: string;
  nameAr: string;
  description: string;
  descriptionAr: string;
  mp3Path: string;
  wavPath: string;
  duration: number; // بالثواني
  category: 'classic' | 'modern' | 'gentle' | 'alert';
  preview?: string; // وصف قصير للمعاينة
}

export interface AudioSettings {
  enabled: boolean;
  volume: number; // 0.0 إلى 1.0
  soundId: string; // معرف الصوت المختار
}

export interface AudioState {
  isInitialized: boolean;
  isPlaying: boolean;
  lastPlayTime: number;
  playCount: number;
  currentSoundId: string;
}

// قائمة الأصوات المتاحة
export const AVAILABLE_SOUNDS: SoundOption[] = [
  {
    id: 'notification',
    name: 'Default',
    nameAr: 'افتراضي',
    description: 'Classic notification sound',
    descriptionAr: 'صوت التنبيه الكلاسيكي الافتراضي',
    mp3Path: '/sounds/notification.mp3',
    wavPath: '/sounds/notification.wav',
    duration: 0.8,
    category: 'classic',
    preview: 'نغمة متوازنة ومألوفة'
  },
  {
    id: 'classic',
    name: 'Classic',
    nameAr: 'كلاسيكي',
    description: 'Two-tone classic notification',
    descriptionAr: 'تنبيه كلاسيكي بنغمتين متتاليتين',
    mp3Path: '/sounds/classic.mp3',
    wavPath: '/sounds/classic.wav',
    duration: 0.6,
    category: 'classic',
    preview: 'نغمتان واضحتان ومميزتان'
  },
  {
    id: 'modern',
    name: 'Modern',
    nameAr: 'حديث',
    description: 'Contemporary layered sound',
    descriptionAr: 'صوت عصري بنغمات متداخلة',
    mp3Path: '/sounds/modern.mp3',
    wavPath: '/sounds/modern.wav',
    duration: 0.8,
    category: 'modern',
    preview: 'نغمات متطورة ومتناغمة'
  },
  {
    id: 'gentle',
    name: 'Gentle',
    nameAr: 'لطيف',
    description: 'Soft and pleasant tone',
    descriptionAr: 'نغمة ناعمة ومريحة للأذن',
    mp3Path: '/sounds/gentle.mp3',
    wavPath: '/sounds/gentle.wav',
    duration: 1.0,
    category: 'gentle',
    preview: 'صوت هادئ وغير مزعج'
  },
  {
    id: 'chime',
    name: 'Chime',
    nameAr: 'جرس',
    description: 'Harmonious bell-like sound',
    descriptionAr: 'صوت جرس متناغم وجميل',
    mp3Path: '/sounds/chime.mp3',
    wavPath: '/sounds/chime.wav',
    duration: 1.2,
    category: 'classic',
    preview: 'نغمات جرس متناغمة'
  },
  {
    id: 'pop',
    name: 'Pop',
    nameAr: 'منبثق',
    description: 'Quick and distinctive pop',
    descriptionAr: 'صوت سريع ومميز',
    mp3Path: '/sounds/pop.mp3',
    wavPath: '/sounds/pop.wav',
    duration: 0.4,
    category: 'alert',
    preview: 'نقرة سريعة ومميزة'
  },
  {
    id: 'subtle',
    name: 'Subtle',
    nameAr: 'خفيف',
    description: 'Very gentle and unobtrusive',
    descriptionAr: 'خفيف جداً وغير مزعج',
    mp3Path: '/sounds/subtle.mp3',
    wavPath: '/sounds/subtle.wav',
    duration: 0.7,
    category: 'gentle',
    preview: 'همسة صوتية لطيفة'
  }
];

// دالة للحصول على صوت بالمعرف
export const getSoundById = (id: string): SoundOption | undefined => {
  return AVAILABLE_SOUNDS.find(sound => sound.id === id);
};

// دالة للحصول على الأصوات حسب الفئة
export const getSoundsByCategory = (category: SoundOption['category']): SoundOption[] => {
  return AVAILABLE_SOUNDS.filter(sound => sound.category === category);
};

// الصوت الافتراضي
export const DEFAULT_SOUND_ID = 'notification';
