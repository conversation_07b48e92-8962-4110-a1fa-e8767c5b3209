// أنواع البيانات الخاصة بنظام المستودعات

export interface Warehouse {
  id: number;
  name: string;
  code: string;
  address?: string;
  phone?: string;
  manager_name?: string;
  email?: string;
  is_main: boolean;
  is_active: boolean;
  capacity_limit?: number;
  current_capacity: number;
  created_at?: string;
  updated_at?: string;
}

export interface WarehouseInventory {
  id: number;
  warehouse_id: number;
  product_id: number;
  quantity: number;
  reserved_quantity: number;
  min_stock_level: number;
  max_stock_level?: number;
  location_code?: string;
  last_updated?: string;
  // معلومات المنتج
  product_name?: string;
  product_barcode?: string;
  product_unit?: string;
}

export interface WarehouseMovement {
  id: number;
  movement_type: 'IN' | 'OUT' | 'TRANSFER' | 'ADJUSTMENT';
  from_warehouse_id?: number;
  to_warehouse_id?: number;
  product_id: number;
  quantity: number;
  unit_cost?: number;
  total_cost?: number;
  reference_type?: string;
  reference_id?: number;
  notes?: string;
  created_by?: number;
  created_at: string;
  // معلومات إضافية
  product_name?: string;
  product_barcode?: string;
  from_warehouse_name?: string;
  to_warehouse_name?: string;
}

export interface TransferRequest {
  id: number;
  from_warehouse_id: number;
  to_warehouse_id: number;
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'COMPLETED' | 'CANCELLED';
  requested_by: number;
  approved_by?: number;
  notes?: string;
  requested_at: string;
  approved_at?: string;
  completed_at?: string;
  // معلومات إضافية
  from_warehouse_name?: string;
  to_warehouse_name?: string;
  total_items?: number;
  items?: TransferRequestItem[];
}

export interface TransferRequestItem {
  id: number;
  transfer_request_id: number;
  product_id: number;
  requested_quantity: number;
  approved_quantity?: number;
  transferred_quantity: number;
  notes?: string;
  // معلومات المنتج
  product_name?: string;
  product_barcode?: string;
  product_unit?: string;
}

// استجابات API
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface WarehouseMovementsResponse {
  success: boolean;
  movements: WarehouseMovement[];
  total?: number;
  page?: number;
  per_page?: number;
}

export interface TransferRequestsResponse {
  success: boolean;
  pending_transfers?: TransferRequest[];
  transfers?: TransferRequest[];
  total?: number;
  page?: number;
  per_page?: number;
}

// فلاتر البحث
export interface WarehouseFilters {
  search?: string;
  is_active?: boolean;
  is_main?: boolean;
}

export interface MovementFilters {
  movement_type?: string;
  product_id?: number;
  date_from?: string;
  date_to?: string;
  page?: number;
  per_page?: number;
}

export interface InventoryFilters {
  search?: string;
  min_quantity?: number;
  max_quantity?: number;
  low_stock?: boolean;
}

// إحصائيات
export interface WarehouseSummary {
  total_warehouses: number;
  active_warehouses: number;
  inactive_warehouses: number;
  main_warehouse?: {
    id: number;
    name: string;
    code: string;
  };
  capacity: {
    total_capacity: number;
    used_capacity: number;
    available_capacity: number;
    usage_percentage: number;
  };
}

export interface CapacityStatus {
  warehouse_id: number;
  warehouse_name: string;
  capacity_limit?: number;
  current_capacity: number;
  available_capacity?: number;
  usage_percentage: number;
  is_full: boolean;
  is_near_full: boolean;
}
