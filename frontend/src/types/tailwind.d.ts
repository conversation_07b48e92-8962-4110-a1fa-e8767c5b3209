// Tailwind CSS type definitions
declare module 'tailwindcss' {
  interface Config {
    content: string[];
    theme?: {
      extend?: Record<string, any>;
    };
    plugins?: any[];
    darkMode?: 'media' | 'class' | false;
    safelist?: string[];
  }
  
  const config: Config;
  export default config;
}

// CSS Modules type definitions for Tailwind
declare module '*.module.css' {
  const classes: { [key: string]: string };
  export default classes;
}

// Global CSS type definitions
declare module '*.css' {
  const content: string;
  export default content;
}
