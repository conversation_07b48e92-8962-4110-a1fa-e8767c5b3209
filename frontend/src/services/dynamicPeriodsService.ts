/**
 * خدمة الفترات الديناميكية لتحليل المنتجات
 * تجلب الفترات المتاحة فعلياً في البيانات مع تفاصيل كل فترة
 */

import api from '../lib/axios';

export interface AvailablePeriod {
  period_days: number;
  period_label: string;
  sales_count: number;
  products_count: number;
  total_amount: number;
  start_date: string;
  end_date: string;
  has_data: boolean;
}

export interface PeriodOption {
  value: string;
  label: string;
  icon?: React.ReactNode;
  details: string;
  salesCount: number;
  productsCount: number;
  totalAmount: number;
  disabled?: boolean;
}

class DynamicPeriodsService {
  private static instance: DynamicPeriodsService;
  private cachedPeriods: AvailablePeriod[] | null = null;
  private lastFetchTime: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 دقائق

  private constructor() {}

  static getInstance(): DynamicPeriodsService {
    if (!DynamicPeriodsService.instance) {
      DynamicPeriodsService.instance = new DynamicPeriodsService();
    }
    return DynamicPeriodsService.instance;
  }

  /**
   * جلب الفترات المتاحة من الخادم
   */
  async fetchAvailablePeriods(): Promise<AvailablePeriod[]> {
    try {
      console.log('جلب الفترات المتاحة للتحليل...');
      
      const response = await api.get('/api/product-analytics/available-periods');
      const periods: AvailablePeriod[] = response.data;
      
      // تحديث الكاش
      this.cachedPeriods = periods;
      this.lastFetchTime = Date.now();
      
      console.log(`تم جلب ${periods.length} فترة متاحة:`, periods);
      return periods;
      
    } catch (error) {
      console.error('خطأ في جلب الفترات المتاحة:', error);
      
      // إرجاع فترات افتراضية في حالة الخطأ
      return this.getDefaultPeriods();
    }
  }

  /**
   * جلب الفترات المتاحة مع استخدام الكاش
   */
  async getAvailablePeriods(forceRefresh: boolean = false): Promise<AvailablePeriod[]> {
    const now = Date.now();
    const isCacheValid = this.cachedPeriods && (now - this.lastFetchTime) < this.CACHE_DURATION;
    
    if (!forceRefresh && isCacheValid) {
      console.log('استخدام الفترات المحفوظة في الكاش');
      return this.cachedPeriods!;
    }
    
    return await this.fetchAvailablePeriods();
  }

  /**
   * تحويل الفترات المتاحة إلى خيارات للقائمة المنسدلة
   */
  async getPeriodOptions(): Promise<PeriodOption[]> {
    const periods = await this.getAvailablePeriods();

    console.log('تحويل الفترات إلى خيارات:', periods);

    const options = periods.map(period => {
      const option = {
        value: period.period_days.toString(),
        label: period.period_label,
        details: this.formatPeriodDetails(period),
        salesCount: period.sales_count,
        productsCount: period.products_count,
        totalAmount: period.total_amount,
        disabled: false // السماح بجميع الفترات حتى لو لم تحتوي على بيانات
      };

      console.log(`خيار الفترة ${period.period_days} يوم:`, option);
      return option;
    });

    return options;
  }

  /**
   * تنسيق تفاصيل الفترة للعرض
   */
  private formatPeriodDetails(period: AvailablePeriod): string {
    const salesText = period.sales_count === 0 ? 'لا توجد مبيعات' :
                     period.sales_count === 1 ? 'عملية بيع واحدة' :
                     `${period.sales_count} عمليات بيع`;

    const productsText = period.products_count === 0 ? 'لا توجد منتجات' :
                        period.products_count === 1 ? 'منتج واحد' :
                        `${period.products_count} منتجات`;

    return `${salesText} • ${productsText}`;
  }

  /**
   * تنسيق المبلغ للعرض
   */
  formatAmount(amount: number): string {
    return new Intl.NumberFormat('ar-LY', {
      style: 'currency',
      currency: 'LYD',
      minimumFractionDigits: 2
    }).format(amount);
  }

  /**
   * الحصول على فترات افتراضية في حالة الخطأ
   */
  private getDefaultPeriods(): AvailablePeriod[] {
    const currentDate = new Date();
    
    return [
      {
        period_days: 7,
        period_label: 'أسبوع واحد',
        sales_count: 0,
        products_count: 0,
        total_amount: 0,
        start_date: new Date(currentDate.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        end_date: currentDate.toISOString().split('T')[0],
        has_data: false
      },
      {
        period_days: 30,
        period_label: '30 يوم',
        sales_count: 0,
        products_count: 0,
        total_amount: 0,
        start_date: new Date(currentDate.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        end_date: currentDate.toISOString().split('T')[0],
        has_data: false
      }
    ];
  }

  /**
   * تحديث الكاش (يستخدم عند إضافة مبيعات جديدة)
   */
  invalidateCache(): void {
    this.cachedPeriods = null;
    this.lastFetchTime = 0;
    console.log('تم مسح كاش الفترات المتاحة');
  }

  /**
   * الحصول على تفاصيل فترة محددة
   */
  async getPeriodDetails(periodDays: number): Promise<AvailablePeriod | null> {
    const periods = await this.getAvailablePeriods();
    return periods.find(p => p.period_days === periodDays) || null;
  }

  /**
   * التحقق من وجود بيانات في فترة محددة
   */
  async hasPeriodData(periodDays: number): Promise<boolean> {
    const period = await this.getPeriodDetails(periodDays);
    return period ? period.has_data : false;
  }

  /**
   * الحصول على أفضل فترة متاحة (التي تحتوي على أكبر عدد من المبيعات)
   */
  async getBestAvailablePeriod(): Promise<AvailablePeriod | null> {
    const periods = await this.getAvailablePeriods();
    
    if (periods.length === 0) return null;
    
    return periods.reduce((best, current) => 
      current.sales_count > best.sales_count ? current : best
    );
  }

  /**
   * الحصول على إحصائيات سريعة للفترات المتاحة
   */
  async getPeriodsStats(): Promise<{
    totalPeriods: number;
    totalSales: number;
    totalProducts: number;
    totalAmount: number;
  }> {
    const periods = await this.getAvailablePeriods();
    
    return {
      totalPeriods: periods.length,
      totalSales: periods.reduce((sum, p) => sum + p.sales_count, 0),
      totalProducts: periods.reduce((sum, p) => sum + p.products_count, 0),
      totalAmount: periods.reduce((sum, p) => sum + p.total_amount, 0)
    };
  }
}

// تصدير مثيل واحد من الخدمة
export const dynamicPeriodsService = DynamicPeriodsService.getInstance();
export default dynamicPeriodsService;
