/**
 * خدمة إدارة الأصوات في نظام المحادثة الفورية
 * تدير تشغيل أصوات التنبيه المتعددة عند وصول رسائل جديدة
 * تطبق مبادئ البرمجة الكائنية مع نمط Singleton
 */

import {
  AudioSettings,
  AudioState,
  SoundOption,
  AVAILABLE_SOUNDS,
  getSoundById,
  DEFAULT_SOUND_ID
} from '../types/audioTypes';

class ChatAudioService {
  private static instance: ChatAudioService;
  private audioElement: HTMLAudioElement | null = null;
  private audioCache: Map<string, HTMLAudioElement> = new Map();
  private settings: AudioSettings;
  private state: AudioState;
  private readonly STORAGE_KEY = 'smartpos_chat_audio_settings';
  private readonly MIN_PLAY_INTERVAL = 1000; // الحد الأدنى بين تشغيل الأصوات (1 ثانية)
  private readonly MAX_PLAYS_PER_MINUTE = 10; // الحد الأقصى للتشغيل في الدقيقة

  private constructor() {
    // إعدادات افتراضية مؤقتة
    this.settings = {
      enabled: true,
      volume: 0.7,
      soundId: DEFAULT_SOUND_ID
    };

    this.state = {
      isInitialized: false,
      isPlaying: false,
      lastPlayTime: 0,
      playCount: 0,
      currentSoundId: DEFAULT_SOUND_ID
    };

    // تحميل الإعدادات الفعلية بشكل غير متزامن
    this.initializeAsync();
  }

  /**
   * تهيئة غير متزامنة للخدمة
   */
  private async initializeAsync(): Promise<void> {
    try {
      this.settings = await this.loadSettings();
      this.state.currentSoundId = this.settings.soundId;

      this.initializeAudio();
      this.setupCleanupInterval();

      console.log('🔊 تم تهيئة خدمة الصوت للمحادثة:', {
        enabled: this.settings.enabled,
        volume: this.settings.volume,
        soundId: this.settings.soundId
      });
    } catch (error) {
      console.error('❌ خطأ في تهيئة خدمة الصوت:', error);
    }
  }

  /**
   * الحصول على مثيل الخدمة (Singleton Pattern)
   */
  public static getInstance(): ChatAudioService {
    if (!ChatAudioService.instance) {
      ChatAudioService.instance = new ChatAudioService();
    }
    return ChatAudioService.instance;
  }

  /**
   * تهيئة عنصر الصوت
   */
  private initializeAudio(): void {
    try {
      this.audioElement = new Audio();
      this.audioElement.preload = 'auto';
      this.audioElement.volume = this.settings.volume;

      // إعداد مصادر الصوت
      this.setupAudioSources();

      // معالجة الأحداث
      this.audioElement.addEventListener('canplaythrough', () => {
        this.state.isInitialized = true;
        console.log('✅ تم تحميل ملف الصوت بنجاح');
      });

      this.audioElement.addEventListener('error', (error) => {
        console.error('❌ خطأ في تحميل ملف الصوت:', error);
        this.state.isInitialized = false;
      });

      this.audioElement.addEventListener('ended', () => {
        this.state.isPlaying = false;
      });

    } catch (error) {
      console.error('❌ خطأ في تهيئة خدمة الصوت:', error);
      this.state.isInitialized = false;
    }
  }

  /**
   * إعداد مصادر الصوت
   */
  private setupAudioSources(): void {
    if (!this.audioElement) return;

    const sound = getSoundById(this.settings.soundId);
    if (sound) {
      this.audioElement.src = sound.mp3Path;
      this.state.currentSoundId = this.settings.soundId;
    } else {
      // استخدام الصوت الافتراضي
      const defaultSound = getSoundById(DEFAULT_SOUND_ID);
      this.audioElement.src = defaultSound?.mp3Path || '/sounds/notification.mp3';
      this.state.currentSoundId = DEFAULT_SOUND_ID;
    }
  }

  /**
   * تحميل صوت مسبقاً في الذاكرة المؤقتة
   */
  private async preloadSound(soundId: string): Promise<HTMLAudioElement | null> {
    if (this.audioCache.has(soundId)) {
      return this.audioCache.get(soundId)!;
    }

    const sound = getSoundById(soundId);
    if (!sound) return null;

    try {
      const audio = new Audio();
      audio.preload = 'auto';
      audio.volume = this.settings.volume;
      audio.src = sound.mp3Path;

      // انتظار التحميل
      await new Promise((resolve, reject) => {
        audio.addEventListener('canplaythrough', resolve);
        audio.addEventListener('error', reject);
      });

      this.audioCache.set(soundId, audio);
      return audio;
    } catch (error) {
      console.error(`❌ خطأ في تحميل الصوت ${soundId}:`, error);
      return null;
    }
  }

  /**
   * تشغيل صوت التنبيه
   */
  public async playNotificationSound(): Promise<boolean> {
    try {
      // التحقق من الإعدادات
      if (!this.settings.enabled) {
        console.log('🔇 الصوت معطل في الإعدادات');
        return false;
      }

      // التحقق من التهيئة
      if (!this.state.isInitialized || !this.audioElement) {
        console.warn('⚠️ خدمة الصوت غير مهيأة');
        return false;
      }

      // التحقق من الحد الأدنى للوقت بين التشغيل
      const now = Date.now();
      if (now - this.state.lastPlayTime < this.MIN_PLAY_INTERVAL) {
        console.log('⏱️ تم تجاهل التشغيل - فترة قصيرة جداً');
        return false;
      }

      // التحقق من الحد الأقصى للتشغيل
      if (this.state.playCount >= this.MAX_PLAYS_PER_MINUTE) {
        console.log('🚫 تم تجاهل التشغيل - تم الوصول للحد الأقصى');
        return false;
      }

      // إيقاف التشغيل الحالي إن وجد
      if (this.state.isPlaying) {
        this.audioElement.pause();
        this.audioElement.currentTime = 0;
      }

      // تحديث مستوى الصوت
      this.audioElement.volume = this.settings.volume;

      // تشغيل الصوت
      this.state.isPlaying = true;
      await this.audioElement.play();

      // تحديث الإحصائيات
      this.state.lastPlayTime = now;
      this.state.playCount++;

      console.log('🔊 تم تشغيل صوت التنبيه بنجاح');
      return true;

    } catch (error) {
      console.error('❌ خطأ في تشغيل صوت التنبيه:', error);
      this.state.isPlaying = false;
      return false;
    }
  }

  /**
   * تحديث إعدادات الصوت
   */
  public async updateSettings(newSettings: Partial<AudioSettings>): Promise<void> {
    this.settings = { ...this.settings, ...newSettings };

    try {
      await this.saveSettings();
    } catch (error) {
      console.error('❌ خطأ في حفظ الإعدادات:', error);
    }

    // تحديث مستوى الصوت فوراً
    if (this.audioElement && newSettings.volume !== undefined) {
      this.audioElement.volume = newSettings.volume;
    }

    // إعادة إعداد مصادر الصوت إذا تغير الصوت
    if (newSettings.soundId && newSettings.soundId !== this.state.currentSoundId) {
      this.setupAudioSources();
    }

    console.log('⚙️ تم تحديث إعدادات الصوت:', this.settings);
  }

  /**
   * تغيير الصوت المختار
   */
  public async changeSoundId(soundId: string): Promise<void> {
    const sound = getSoundById(soundId);
    if (!sound) {
      console.error(`❌ صوت غير موجود: ${soundId}`);
      return;
    }

    await this.updateSettings({ soundId });
    console.log(`🔄 تم تغيير الصوت إلى: ${sound.nameAr}`);
  }

  /**
   * الحصول على قائمة الأصوات المتاحة
   */
  public getAvailableSounds(): SoundOption[] {
    return AVAILABLE_SOUNDS;
  }

  /**
   * الحصول على الصوت الحالي
   */
  public getCurrentSound(): SoundOption | undefined {
    return getSoundById(this.settings.soundId);
  }

  /**
   * إعادة تحميل الإعدادات من الخادم
   */
  public async reloadSettings(): Promise<void> {
    try {
      const serverSettings = await this.loadSettingsFromServer();
      if (serverSettings) {
        this.settings = serverSettings;
        this.state.currentSoundId = serverSettings.soundId;

        // تحديث مستوى الصوت
        if (this.audioElement) {
          this.audioElement.volume = serverSettings.volume;
        }

        // إعادة إعداد مصادر الصوت
        this.setupAudioSources();

        console.log('🔄 تم إعادة تحميل إعدادات الصوت من الخادم:', this.settings);
      }
    } catch (error) {
      console.error('❌ خطأ في إعادة تحميل الإعدادات:', error);
    }
  }

  /**
   * الحصول على الإعدادات الحالية
   */
  public getSettings(): AudioSettings {
    return { ...this.settings };
  }

  /**
   * الحصول على حالة الخدمة
   */
  public getState(): AudioState {
    return { ...this.state };
  }

  /**
   * تمكين/تعطيل الصوت
   */
  public setEnabled(enabled: boolean): void {
    this.updateSettings({ enabled });
  }

  /**
   * تحديث مستوى الصوت
   */
  public setVolume(volume: number): void {
    const clampedVolume = Math.max(0, Math.min(1, volume));
    this.updateSettings({ volume: clampedVolume });
  }

  /**
   * اختبار تشغيل الصوت
   */
  public async testSound(): Promise<boolean> {
    console.log('🧪 اختبار تشغيل الصوت...');
    return await this.playNotificationSound();
  }

  /**
   * تحميل الإعدادات من الخادم
   */
  private async loadSettings(): Promise<AudioSettings> {
    try {
      // محاولة تحميل من الخادم أولاً
      const serverSettings = await this.loadSettingsFromServer();
      if (serverSettings) {
        return serverSettings;
      }

      // في حالة فشل التحميل من الخادم، استخدم localStorage كبديل
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        return {
          enabled: parsed.enabled ?? true,
          volume: parsed.volume ?? 0.7,
          soundId: parsed.soundId ?? DEFAULT_SOUND_ID
        };
      }
    } catch (error) {
      console.error('❌ خطأ في تحميل إعدادات الصوت:', error);
    }

    // الإعدادات الافتراضية
    return {
      enabled: true,
      volume: 0.7,
      soundId: DEFAULT_SOUND_ID
    };
  }

  /**
   * تحميل الإعدادات من الخادم
   */
  private async loadSettingsFromServer(): Promise<AudioSettings | null> {
    try {
      const response = await fetch('/api/settings/public', {
        headers: {
          'Authorization': `Bearer ${this.getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const settings = await response.json();

      return {
        enabled: settings.chat_sound_enabled === 'true',
        volume: parseFloat(settings.chat_sound_volume || '70') / 100, // تحويل من 0-100 إلى 0.0-1.0
        soundId: settings.chat_sound_id || DEFAULT_SOUND_ID
      };
    } catch (error) {
      console.error('❌ خطأ في تحميل إعدادات الصوت من الخادم:', error);
      return null;
    }
  }

  /**
   * حفظ الإعدادات في الخادم و localStorage
   */
  private async saveSettings(): Promise<void> {
    try {
      // حفظ في الخادم
      await this.saveSettingsToServer();

      // حفظ في localStorage كنسخة احتياطية
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.settings));
    } catch (error) {
      console.error('❌ خطأ في حفظ إعدادات الصوت:', error);

      // في حالة فشل الحفظ في الخادم، احفظ في localStorage فقط
      try {
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.settings));
      } catch (localError) {
        console.error('❌ خطأ في حفظ إعدادات الصوت محلياً:', localError);
      }
    }
  }

  /**
   * حفظ الإعدادات في الخادم
   */
  private async saveSettingsToServer(): Promise<void> {
    try {
      const settingsToSave = [
        {
          key: 'chat_sound_enabled',
          value: this.settings.enabled.toString()
        },
        {
          key: 'chat_sound_volume',
          value: Math.round(this.settings.volume * 100).toString() // تحويل من 0.0-1.0 إلى 0-100
        },
        {
          key: 'chat_sound_id',
          value: this.settings.soundId
        }
      ];

      const response = await fetch('/api/settings/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`
        },
        body: JSON.stringify({ settings: settingsToSave })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      console.log('✅ تم حفظ إعدادات الصوت في الخادم بنجاح');
    } catch (error) {
      console.error('❌ خطأ في حفظ إعدادات الصوت في الخادم:', error);
      throw error;
    }
  }

  /**
   * الحصول على رمز المصادقة
   */
  private getAuthToken(): string {
    return localStorage.getItem('token') || '';
  }

  /**
   * إعداد فترة تنظيف الإحصائيات
   */
  private setupCleanupInterval(): void {
    // إعادة تعيين عداد التشغيل كل دقيقة
    setInterval(() => {
      this.state.playCount = 0;
    }, 60000);
  }

  /**
   * تنظيف الموارد
   */
  public dispose(): void {
    if (this.audioElement) {
      this.audioElement.pause();
      this.audioElement.src = '';
      this.audioElement = null;
    }
    this.state.isInitialized = false;
    console.log('🧹 تم تنظيف موارد خدمة الصوت');
  }
}

// تصدير مثيل واحد من الخدمة
export const chatAudioService = ChatAudioService.getInstance();
export default chatAudioService;
