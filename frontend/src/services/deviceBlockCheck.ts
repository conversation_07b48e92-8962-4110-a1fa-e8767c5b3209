/**
 * خدمة فحص حظر الجهاز - محسنة لتجنب الحلقات اللا نهائية
 */

import { unifiedConnectionService } from './unifiedConnectionService';

export interface DeviceBlockStatus {
  isBlocked: boolean;
  isPending: boolean;
  reason?: string;
  message?: string;
  errorCode?: string;
  redirectUrl?: string;
}

/**
 * فئة إدارة فحص حظر الأجهزة مع Circuit Breaker Pattern
 */
class DeviceBlockChecker {
  private static instance: DeviceBlockChecker;
  private failureCount: number = 0;
  private lastFailureTime: number = 0;
  private isCircuitOpen: boolean = false;

  // إعدادات Circuit Breaker
  private readonly MAX_FAILURES = 3;
  private readonly CIRCUIT_TIMEOUT = 30000; // 30 ثانية

  private constructor() {}

  public static getInstance(): DeviceBlockChecker {
    if (!DeviceBlockChecker.instance) {
      DeviceBlockChecker.instance = new DeviceBlockChecker();
    }
    return DeviceBlockChecker.instance;
  }

  /**
   * فحص ما إذا كان الجهاز محظور - مع Circuit Breaker
   */
  public async checkDeviceBlocked(): Promise<DeviceBlockStatus> {
    // فحص Circuit Breaker
    if (this.isCircuitOpen) {
      const timeSinceLastFailure = Date.now() - this.lastFailureTime;
      if (timeSinceLastFailure < this.CIRCUIT_TIMEOUT) {
        console.warn('🔒 Circuit breaker is open, skipping device check');
        return {
          isBlocked: false,
          isPending: false
        };
      } else {
        // إعادة تعيين Circuit Breaker
        this.resetCircuitBreaker();
      }
    }

    try {
      const result = await this.performDeviceCheck();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      console.error('خطأ في فحص حظر الجهاز:', error);

      // إرجاع حالة آمنة بدلاً من إعادة المحاولة
      return {
        isBlocked: false,
        isPending: false
      };
    }
  }

  /**
   * تنفيذ فحص الجهاز الفعلي
   */
  private async performDeviceCheck(): Promise<DeviceBlockStatus> {
    // استخدام الخدمة الموحدة للاتصال
    const connectionState = unifiedConnectionService.getConnectionState();

    if (!connectionState.isHealthy) {
      throw new Error('Backend server is not available');
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 8000);

    try {
      const backendURL = await unifiedConnectionService.getBestBackendUrl();
      const response = await fetch(`${backendURL}/api/device/status`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      return this.parseDeviceResponse(response);

    } catch (fetchError) {
      clearTimeout(timeoutId);
      throw fetchError;
    }
  }

  /**
   * تحليل استجابة الخادم
   */
  private async parseDeviceResponse(response: Response): Promise<DeviceBlockStatus> {
    if (response.status === 403) {
      const data = await response.json().catch(() => ({}));
      const errorCode = data.detail?.error_code || data.error_code || 'unknown';

      console.log('🔍 Device status 403 response:', data);

      if (errorCode === 'DEVICE_PENDING_APPROVAL' || data.status === 'pending_approval') {
        return {
          isBlocked: false,
          isPending: true,
          reason: data.detail?.reason || data.reason || 'device_pending',
          message: data.detail?.message || data.message || 'جهازك في انتظار موافقة مدير النظام',
          errorCode,
          redirectUrl: data.redirect_url
        };
      } else if (errorCode === 'DEVICE_BLOCKED' || data.status === 'blocked') {
        return {
          isBlocked: true,
          isPending: false,
          reason: data.detail?.reason || data.reason || 'device_blocked',
          message: data.detail?.message || data.message || 'هذا الجهاز محظور من قبل مدير النظام',
          errorCode,
          redirectUrl: data.redirect_url
        };
      }
    }

    if (response.ok) {
      return {
        isBlocked: false,
        isPending: false
      };
    }

    // حالة افتراضية آمنة
    return {
      isBlocked: false,
      isPending: false
    };
  }

  /**
   * معالجة النجاح
   */
  private onSuccess(): void {
    this.failureCount = 0;
    this.isCircuitOpen = false;
  }

  /**
   * معالجة الفشل
   */
  private onFailure(): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();

    if (this.failureCount >= this.MAX_FAILURES) {
      this.isCircuitOpen = true;
      console.warn(`🔒 Circuit breaker opened after ${this.failureCount} failures`);
    }
  }

  /**
   * إعادة تعيين Circuit Breaker
   */
  private resetCircuitBreaker(): void {
    console.log('🔓 Circuit breaker reset, attempting to reconnect');
    this.isCircuitOpen = false;
    this.failureCount = 0;
  }
}

// إنشاء instance واحدة
const deviceBlockChecker = DeviceBlockChecker.getInstance();

/**
 * دالة عامة لفحص حظر الجهاز
 */
export async function checkDeviceBlocked(): Promise<DeviceBlockStatus> {
  return await deviceBlockChecker.checkDeviceBlocked();
}

/**
 * فحص دوري لحظر الجهاز
 */
export function startDeviceBlockCheck(
  onBlocked: () => void,
  onPending: () => void,
  intervalMs: number = 120000 // دقيقتان لتقليل الضغط على الخادم أكثر
): () => void {
  const checkInterval = setInterval(async () => {
    const status = await checkDeviceBlocked();
    if (status.isBlocked) {
      clearInterval(checkInterval);
      onBlocked();
    } else if (status.isPending) {
      clearInterval(checkInterval);
      onPending();
    }
  }, intervalMs);

  return () => clearInterval(checkInterval);
}
