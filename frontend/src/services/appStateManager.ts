/**
 * مدير حالة التطبيق المحسن - يمنع التجميد ويدير الحالة بكفاءة
 */

interface AppState {
  isLoading: boolean;
  isConnected: boolean;
  activeRequests: number;
  lastActivity: Date;
  errors: string[];
  warnings: string[];
  performance: {
    averageResponseTime: number;
    errorRate: number;
    memoryUsage: number;
  };
}

interface StateListener {
  id: string;
  callback: (state: AppState) => void;
  priority: 'high' | 'medium' | 'low';
}

class AppStateManager {
  private static instance: AppStateManager;
  private state: AppState;
  private listeners: StateListener[] = [];
  private updateQueue: (() => void)[] = [];
  private isUpdating = false;
  private cleanupInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.state = {
      isLoading: false,
      isConnected: true,
      activeRequests: 0,
      lastActivity: new Date(),
      errors: [],
      warnings: [],
      performance: {
        averageResponseTime: 0,
        errorRate: 0,
        memoryUsage: 0
      }
    };

    this.startCleanup();
  }

  public static getInstance(): AppStateManager {
    if (!AppStateManager.instance) {
      AppStateManager.instance = new AppStateManager();
    }
    return AppStateManager.instance;
  }

  /**
   * تحديث حالة التطبيق
   */
  public updateState(updates: Partial<AppState>): void {
    this.updateQueue.push(() => {
      this.state = { ...this.state, ...updates };
      this.state.lastActivity = new Date();
      this.notifyListeners();
    });

    this.processUpdateQueue();
  }

  /**
   * معالجة طابور التحديثات
   */
  private async processUpdateQueue(): Promise<void> {
    if (this.isUpdating || this.updateQueue.length === 0) {
      return;
    }

    this.isUpdating = true;

    // معالجة التحديثات في دفعات
    const batchSize = 5;
    while (this.updateQueue.length > 0) {
      const batch = this.updateQueue.splice(0, batchSize);
      batch.forEach(update => update());

      // إعطاء فرصة للمتصفح للتنفس
      await new Promise(resolve => setTimeout(resolve, 0));
    }

    this.isUpdating = false;
  }

  /**
   * إضافة مستمع للحالة
   */
  public addListener(
    callback: (state: AppState) => void,
    priority: 'high' | 'medium' | 'low' = 'medium'
  ): string {
    const id = `listener_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const listener: StateListener = {
      id,
      callback,
      priority
    };

    // إدراج حسب الأولوية
    this.insertListenerByPriority(listener);

    return id;
  }

  /**
   * إزالة مستمع
   */
  public removeListener(id: string): void {
    this.listeners = this.listeners.filter(l => l.id !== id);
  }

  /**
   * إدراج المستمع حسب الأولوية
   */
  private insertListenerByPriority(listener: StateListener): void {
    const priorityOrder = { high: 0, medium: 1, low: 2 };

    let insertIndex = this.listeners.length;
    for (let i = 0; i < this.listeners.length; i++) {
      if (priorityOrder[listener.priority] < priorityOrder[this.listeners[i].priority]) {
        insertIndex = i;
        break;
      }
    }

    this.listeners.splice(insertIndex, 0, listener);
  }

  /**
   * إشعار المستمعين
   */
  private notifyListeners(): void {
    // إشعار المستمعين بشكل غير متزامن لمنع التجميد
    setTimeout(() => {
      this.listeners.forEach(listener => {
        try {
          listener.callback({ ...this.state });
        } catch (error) {
          console.error('Error in state listener:', error);
        }
      });
    }, 0);
  }

  /**
   * الحصول على الحالة الحالية
   */
  public getState(): AppState {
    return { ...this.state };
  }

  /**
   * تحديث عداد الطلبات النشطة
   */
  public updateActiveRequests(count: number): void {
    this.updateState({
      activeRequests: Math.max(0, count),
      isLoading: count > 0
    });
  }

  /**
   * إضافة خطأ
   */
  public addError(error: string): void {
    const errors = [...this.state.errors, error];
    if (errors.length > 10) {
      errors.shift(); // الاحتفاظ بآخر 10 أخطاء فقط
    }
    this.updateState({ errors });
  }

  /**
   * إضافة تحذير
   */
  public addWarning(warning: string): void {
    const warnings = [...this.state.warnings, warning];
    if (warnings.length > 10) {
      warnings.shift(); // الاحتفاظ بآخر 10 تحذيرات فقط
    }
    this.updateState({ warnings });
  }

  /**
   * مسح الأخطاء والتحذيرات
   */
  public clearMessages(): void {
    this.updateState({ errors: [], warnings: [] });
  }

  /**
   * تحديث مقاييس الأداء
   */
  public updatePerformance(performance: Partial<AppState['performance']>): void {
    this.updateState({
      performance: { ...this.state.performance, ...performance }
    });
  }

  /**
   * تحديث حالة الاتصال بناءً على مصدر محدد
   */
  public updateConnectionStatus(isConnected: boolean, source: string = 'general'): void {
    // تجنب التحديثات المتكررة للحالة نفسها
    if (this.state.isConnected === isConnected) {
      return;
    }

    console.log(`🔄 [AppStateManager] تحديث حالة الاتصال من ${source}: ${isConnected ? 'متصل' : 'منقطع'}`);

    this.updateState({
      isConnected,
      performance: {
        ...this.state.performance,
        errorRate: isConnected ? Math.max(0, this.state.performance.errorRate - 0.1) : this.state.performance.errorRate + 0.1
      }
    });
  }

  /**
   * فحص صحة التطبيق
   */
  public getHealthStatus(): {
    status: 'healthy' | 'warning' | 'critical';
    issues: string[];
  } {
    const issues: string[] = [];
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';

    // فحص الاتصال
    if (!this.state.isConnected) {
      issues.push('لا يوجد اتصال بالخادم');
      status = 'critical';
    }

    // فحص الطلبات النشطة
    if (this.state.activeRequests > 10) {
      issues.push(`عدد كبير من الطلبات النشطة: ${this.state.activeRequests}`);
      status = 'warning';
    }

    // فحص الأخطاء
    if (this.state.errors.length > 5) {
      issues.push(`عدد كبير من الأخطاء: ${this.state.errors.length}`);
      status = 'critical';
    }

    // فحص الأداء
    if (this.state.performance.averageResponseTime > 5000) {
      issues.push('وقت استجابة بطيء');
      status = 'warning';
    }

    return { status, issues };
  }

  /**
   * بدء تنظيف دوري
   */
  private startCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      // تنظيف الرسائل القديمة
      const now = Date.now();
      const maxAge = 5 * 60 * 1000; // 5 دقائق

      if (now - this.state.lastActivity.getTime() > maxAge) {
        this.clearMessages();
      }

      // تنظيف المستمعين غير النشطين
      this.listeners = this.listeners.filter((_listener) => {
        // يمكن إضافة منطق للتحقق من نشاط المستمع
        return true;
      });

    }, 120000); // كل دقيقتين لتقليل الضغط
  }

  /**
   * تنظيف الموارد
   */
  public cleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    this.listeners = [];
    this.updateQueue = [];
  }

  /**
   * إعادة تعيين الحالة
   */
  public reset(): void {
    this.state = {
      isLoading: false,
      isConnected: true,
      activeRequests: 0,
      lastActivity: new Date(),
      errors: [],
      warnings: [],
      performance: {
        averageResponseTime: 0,
        errorRate: 0,
        memoryUsage: 0
      }
    };
    this.notifyListeners();
  }
}

export const appStateManager = AppStateManager.getInstance();

/**
 * Hook للاستخدام في React components
 */
export function useAppState() {
  return {
    getState: () => appStateManager.getState(),
    updateState: (updates: Partial<AppState>) => appStateManager.updateState(updates),
    addListener: (callback: (state: AppState) => void, priority?: 'high' | 'medium' | 'low') =>
      appStateManager.addListener(callback, priority),
    removeListener: (id: string) => appStateManager.removeListener(id),
    addError: (error: string) => appStateManager.addError(error),
    addWarning: (warning: string) => appStateManager.addWarning(warning),
    getHealth: () => appStateManager.getHealthStatus()
  };
}
