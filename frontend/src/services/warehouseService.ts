/**
 * خدمة إدارة المستودعات
 * تطبق مبادئ البرمجة الكائنية مع معالجة شاملة للأخطاء
 */

import api from '../lib/axios';
import { AxiosResponse } from 'axios';

// Types
export interface InventoryStatus {
  has_inventory: boolean;
  total_quantity: number;
  products_count: number;
}

export interface Warehouse {
  id: number;
  name: string;
  code: string;
  address?: string;
  phone?: string;
  manager_name?: string;
  email?: string;
  is_main: boolean;
  is_active: boolean;
  capacity_limit?: number;
  current_capacity: number;
  created_at?: string;
  updated_at?: string;
  inventory_status?: InventoryStatus;
}

export interface WarehouseCreate {
  name: string;
  code: string;
  address?: string;
  phone?: string;
  manager_name?: string;
  email?: string;
  is_main?: boolean;
  is_active?: boolean;
  capacity_limit?: number;
  current_capacity?: number;
}

export interface WarehouseUpdate {
  name?: string;
  code?: string;
  address?: string;
  phone?: string;
  manager_name?: string;
  email?: string;
  is_main?: boolean;
  is_active?: boolean;
  capacity_limit?: number;
  current_capacity?: number;
}

export interface WarehouseCapacityStatus {
  warehouse_id: number;
  warehouse_name: string;
  capacity_limit?: number;
  current_capacity: number;
  total_items: number;
  usage_percentage: number;
  available_capacity?: number;
  status: 'normal' | 'warning' | 'critical';
}

export interface WarehouseSummary {
  total_warehouses: number;
  active_warehouses: number;
  inactive_warehouses: number;
  main_warehouse?: {
    id: number;
    name: string;
    code: string;
  };
  capacity: {
    total_capacity: number;
    used_capacity: number;
    available_capacity: number;
    usage_percentage: number;
  };
}

export interface ApiResponse<T> {
  success: boolean;
  error?: string;
  message?: string;
  warehouse?: T;
  warehouses?: T[];
  capacity_status?: WarehouseCapacityStatus;
  summary?: WarehouseSummary;
  total_count?: number;
}

/**
 * خدمة إدارة المستودعات
 * تطبق مبادئ البرمجة الكائنية مع نمط Singleton
 */
export class WarehouseService {
  private static instance: WarehouseService;
  private readonly baseURL: string;

  private constructor() {
    this.baseURL = '/api/warehouses';
  }

  /**
   * الحصول على instance وحيد من الخدمة
   */
  public static getInstance(): WarehouseService {
    if (!WarehouseService.instance) {
      WarehouseService.instance = new WarehouseService();
    }
    return WarehouseService.instance;
  }

  /**
   * إنشاء مستودع جديد
   */
  public async createWarehouse(warehouseData: WarehouseCreate): Promise<ApiResponse<Warehouse>> {
    try {
      const response: AxiosResponse<ApiResponse<Warehouse>> = await api.post(
        this.baseURL,
        warehouseData
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في إنشاء المستودع:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في إنشاء المستودع'
      };
    }
  }

  /**
   * الحصول على جميع المستودعات
   */
  public async getAllWarehouses(includeInactive: boolean = false): Promise<ApiResponse<Warehouse>> {
    try {
      const response: AxiosResponse<ApiResponse<Warehouse>> = await api.get(
        `${this.baseURL}?include_inactive=${includeInactive}`
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في جلب المستودعات:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في جلب المستودعات'
      };
    }
  }

  /**
   * الحصول على مستودع بالمعرف
   */
  public async getWarehouseById(warehouseId: number): Promise<ApiResponse<Warehouse>> {
    try {
      const response: AxiosResponse<ApiResponse<Warehouse>> = await api.get(
        `${this.baseURL}/${warehouseId}`
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في جلب المستودع:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في جلب المستودع'
      };
    }
  }

  /**
   * تحديث بيانات المستودع
   */
  public async updateWarehouse(warehouseId: number, updateData: WarehouseUpdate): Promise<ApiResponse<Warehouse>> {
    try {
      const response: AxiosResponse<ApiResponse<Warehouse>> = await api.put(
        `${this.baseURL}/${warehouseId}`,
        updateData
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في تحديث المستودع:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في تحديث المستودع'
      };
    }
  }

  /**
   * حذف المستودع
   */
  public async deleteWarehouse(warehouseId: number): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await api.delete(
        `${this.baseURL}/${warehouseId}`
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في حذف المستودع:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في حذف المستودع'
      };
    }
  }

  /**
   * تعيين المستودع الرئيسي
   */
  public async setMainWarehouse(warehouseId: number): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await api.post(
        `${this.baseURL}/${warehouseId}/set-main`
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في تعيين المستودع الرئيسي:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في تعيين المستودع الرئيسي'
      };
    }
  }

  /**
   * الحصول على حالة سعة المستودع
   */
  public async getWarehouseCapacity(warehouseId: number): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await api.get(
        `${this.baseURL}/${warehouseId}/capacity`
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في جلب حالة السعة:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في جلب حالة السعة'
      };
    }
  }

  /**
   * الحصول على ملخص المستودعات
   */
  public async getWarehousesSummary(): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await api.get(
        `${this.baseURL}/summary/overview`
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في جلب ملخص المستودعات:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في جلب ملخص المستودعات'
      };
    }
  }

  /**
   * التحقق من صحة بيانات المستودع
   */
  public validateWarehouseData(data: WarehouseCreate | WarehouseUpdate): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // التحقق من الاسم
    if ('name' in data && data.name !== undefined) {
      if (!data.name || data.name.trim().length === 0) {
        errors.push('اسم المستودع مطلوب');
      } else if (data.name.length > 100) {
        errors.push('اسم المستودع يجب أن يكون أقل من 100 حرف');
      }
    }

    // التحقق من الكود
    if ('code' in data && data.code !== undefined) {
      if (!data.code || data.code.trim().length === 0) {
        errors.push('كود المستودع مطلوب');
      } else if (data.code.length > 20) {
        errors.push('كود المستودع يجب أن يكون أقل من 20 حرف');
      } else if (!/^[A-Z0-9\-_]+$/.test(data.code.toUpperCase())) {
        errors.push('كود المستودع يجب أن يحتوي على أحرف وأرقام فقط');
      }
    }

    // التحقق من البريد الإلكتروني
    if (data.email && data.email.trim().length > 0) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(data.email)) {
        errors.push('البريد الإلكتروني غير صحيح');
      }
    }

    // التحقق من السعة
    if (data.capacity_limit !== undefined && data.capacity_limit !== null) {
      if (data.capacity_limit < 0) {
        errors.push('الحد الأقصى للسعة يجب أن يكون أكبر من أو يساوي صفر');
      }
    }

    if (data.current_capacity !== undefined && data.current_capacity !== null) {
      if (data.current_capacity < 0) {
        errors.push('السعة الحالية يجب أن تكون أكبر من أو تساوي صفر');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * جلب المستودعات مع معلومات حالة المخزون
   */
  public async getWarehousesWithInventoryStatus(includeInactive: boolean = false): Promise<Warehouse[]> {
    try {
      console.log('🔄 جلب المستودعات مع معلومات المخزون...');

      const response: AxiosResponse<{
        success: boolean;
        warehouses: Warehouse[];
        total_count: number;
      }> = await api.get('/api/warehouses/with-inventory-status', {
        params: {
          include_inactive: includeInactive
        }
      });

      if (response.data.success) {
        console.log(`✅ تم جلب ${response.data.warehouses.length} مستودع مع معلومات المخزون`);
        return response.data.warehouses;
      } else {
        throw new Error('فشل في جلب المستودعات مع معلومات المخزون');
      }
    } catch (error: any) {
      console.error('❌ خطأ في جلب المستودعات مع معلومات المخزون:', error);

      let errorMessage = 'خطأ في جلب المستودعات مع معلومات المخزون';

      if (error.response) {
        // خطأ من الخادم
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
        errorMessage = error.response.data?.detail || `خطأ من الخادم: ${error.response.status}`;
      } else if (error.request) {
        // خطأ في الشبكة
        console.error('Network error:', error.request);
        errorMessage = 'خطأ في الاتصال بالخادم';
      } else {
        // خطأ آخر
        console.error('Error message:', error.message);
        errorMessage = error.message || errorMessage;
      }

      throw new Error(errorMessage);
    }
  }

  /**
   * تنسيق بيانات المستودع للعرض
   */
  public formatWarehouseForDisplay(warehouse: Warehouse): any {
    return {
      ...warehouse,
      capacity_usage_percentage: warehouse.capacity_limit
        ? Math.round((warehouse.current_capacity / warehouse.capacity_limit) * 100)
        : 0,
      status_text: warehouse.is_active ? 'نشط' : 'غير نشط',
      main_text: warehouse.is_main ? 'رئيسي' : 'فرعي',
      formatted_capacity: warehouse.capacity_limit
        ? `${warehouse.current_capacity} / ${warehouse.capacity_limit}`
        : warehouse.current_capacity.toString(),
      inventory_text: warehouse.inventory_status?.has_inventory
        ? `${warehouse.inventory_status.products_count} منتج (${warehouse.inventory_status.total_quantity} قطعة)`
        : 'لا يوجد مخزون'
    };
  }
}

// تصدير instance وحيد
export const warehouseService = WarehouseService.getInstance();
