/**
 * خدمة الكشف التلقائي عن المنطقة الزمنية والتحديث من الإنترنت
 * تتيح للتطبيق الكشف عن المنطقة الزمنية للمستخدم تلقائياً
 * والحصول على التوقيت الدقيق من خوادم الإنترنت
 * تطبق مبادئ البرمجة الكائنية (OOP) مع نمط Singleton
 */

// واجهة معلومات المنطقة الزمنية
export interface TimezoneInfo {
  timezone: string;
  offset: number;
  offsetString: string;
  isDST: boolean;
  country: string;
  city: string;
  region: string;
  abbreviation: string;
}

// واجهة معلومات الوقت من الإنترنت
export interface InternetTimeInfo {
  datetime: string;
  timezone: string;
  utc_datetime: string;
  utc_offset: string;
  day_of_week: number;
  day_of_year: number;
  week_number: number;
  dst: boolean;
  dst_offset: number;
  raw_offset: number;
}

// واجهة معلومات الموقع الجغرافي
export interface LocationInfo {
  country: string;
  countryCode: string;
  region: string;
  regionName: string;
  city: string;
  timezone: string;
  lat: number;
  lon: number;
}

/**
 * خدمة الكشف التلقائي عن المنطقة الزمنية
 * تطبق نمط Singleton لضمان وجود نسخة واحدة فقط
 */
export class TimezoneDetectionService {
  private static instance: TimezoneDetectionService;
  private cachedTimezoneInfo: TimezoneInfo | null = null;
  private lastDetectionTime = 0;
  private readonly CACHE_DURATION = 60 * 60 * 1000; // ساعة واحدة

  private constructor() {}

  /**
   * الحصول على نسخة الخدمة (Singleton)
   */
  public static getInstance(): TimezoneDetectionService {
    if (!TimezoneDetectionService.instance) {
      TimezoneDetectionService.instance = new TimezoneDetectionService();
    }
    return TimezoneDetectionService.instance;
  }

  /**
   * الكشف عن المنطقة الزمنية للمستخدم من المتصفح
   */
  public detectBrowserTimezone(): TimezoneInfo {
    try {
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const now = new Date();
      
      // حساب الإزاحة الزمنية
      const offset = -now.getTimezoneOffset() / 60;
      const offsetString = this.formatOffset(offset);
      
      // الكشف عن التوقيت الصيفي
      const january = new Date(now.getFullYear(), 0, 1);
      const july = new Date(now.getFullYear(), 6, 1);
      const isDST = now.getTimezoneOffset() < Math.max(january.getTimezoneOffset(), july.getTimezoneOffset());
      
      // استخراج معلومات المنطقة
      const parts = timezone.split('/');
      const region = parts[0] || '';
      const city = parts[1]?.replace(/_/g, ' ') || '';
      
      // الحصول على اختصار المنطقة الزمنية
      const abbreviation = this.getTimezoneAbbreviation(timezone);
      
      return {
        timezone,
        offset,
        offsetString,
        isDST,
        country: this.getCountryFromTimezone(timezone),
        city,
        region,
        abbreviation
      };
    } catch (error) {
      console.error('Error detecting browser timezone:', error);
      return this.getDefaultTimezoneInfo();
    }
  }

  /**
   * الحصول على معلومات الوقت من خادم إنترنت
   */
  public async getInternetTime(timezone?: string): Promise<InternetTimeInfo | null> {
    try {
      const targetTimezone = timezone || this.detectBrowserTimezone().timezone;
      
      // استخدام WorldTimeAPI للحصول على الوقت الدقيق
      const response = await fetch(`https://worldtimeapi.org/api/timezone/${targetTimezone}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      return data as InternetTimeInfo;
    } catch (error) {
      console.warn('Failed to get internet time, trying alternative service:', error);
      return await this.getAlternativeInternetTime(timezone);
    }
  }

  /**
   * خدمة بديلة للحصول على الوقت من الإنترنت
   */
  private async getAlternativeInternetTime(timezone?: string): Promise<InternetTimeInfo | null> {
    try {
      // استخدام TimeAPI كبديل
      const response = await fetch('https://timeapi.io/api/Time/current/zone?timeZone=' + 
        encodeURIComponent(timezone || 'UTC'));
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      // تحويل البيانات لتتوافق مع واجهة InternetTimeInfo
      return {
        datetime: data.dateTime,
        timezone: data.timeZone,
        utc_datetime: data.utcDateTime,
        utc_offset: data.utcOffset,
        day_of_week: data.dayOfWeek,
        day_of_year: data.dayOfYear,
        week_number: data.weekNumber || 0,
        dst: data.dstActive || false,
        dst_offset: 0,
        raw_offset: 0
      };
    } catch (error) {
      console.error('All internet time services failed:', error);
      return null;
    }
  }

  /**
   * الكشف عن الموقع الجغرافي والمنطقة الزمنية من IP
   */
  public async detectLocationAndTimezone(): Promise<LocationInfo | null> {
    try {
      // استخدام ip-api للكشف عن الموقع
      const response = await fetch('http://ip-api.com/json/?fields=status,country,countryCode,region,regionName,city,timezone,lat,lon');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.status === 'success') {
        return data as LocationInfo;
      } else {
        throw new Error('Location detection failed');
      }
    } catch (error) {
      console.warn('Failed to detect location, trying alternative service:', error);
      return await this.getAlternativeLocation();
    }
  }

  /**
   * خدمة بديلة للكشف عن الموقع
   */
  private async getAlternativeLocation(): Promise<LocationInfo | null> {
    try {
      // استخدام ipapi كبديل
      const response = await fetch('https://ipapi.co/json/');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      return {
        country: data.country_name,
        countryCode: data.country_code,
        region: data.region_code,
        regionName: data.region,
        city: data.city,
        timezone: data.timezone,
        lat: data.latitude,
        lon: data.longitude
      };
    } catch (error) {
      console.error('All location services failed:', error);
      return null;
    }
  }

  /**
   * الكشف الشامل عن المنطقة الزمنية (متصفح + إنترنت + موقع)
   */
  public async comprehensiveTimezoneDetection(): Promise<TimezoneInfo> {
    const now = Date.now();
    
    // استخدام الكاش إذا كان متاحاً وحديثاً
    if (this.cachedTimezoneInfo && (now - this.lastDetectionTime) < this.CACHE_DURATION) {
      return this.cachedTimezoneInfo;
    }

    try {
      // الكشف من المتصفح أولاً
      const browserInfo = this.detectBrowserTimezone();
      
      // محاولة الحصول على معلومات إضافية من الإنترنت
      const [internetTime, locationInfo] = await Promise.allSettled([
        this.getInternetTime(browserInfo.timezone),
        this.detectLocationAndTimezone()
      ]);

      let finalInfo = browserInfo;

      // تحسين المعلومات بناءً على البيانات من الإنترنت
      if (locationInfo.status === 'fulfilled' && locationInfo.value) {
        finalInfo = {
          ...browserInfo,
          timezone: locationInfo.value.timezone || browserInfo.timezone,
          country: locationInfo.value.country || browserInfo.country,
          city: locationInfo.value.city || browserInfo.city,
          region: locationInfo.value.regionName || browserInfo.region
        };
      }

      if (internetTime.status === 'fulfilled' && internetTime.value) {
        finalInfo = {
          ...finalInfo,
          isDST: internetTime.value.dst
        };
      }

      // تحديث الكاش
      this.cachedTimezoneInfo = finalInfo;
      this.lastDetectionTime = now;

      return finalInfo;
    } catch (error) {
      console.error('Comprehensive timezone detection failed:', error);
      return this.detectBrowserTimezone();
    }
  }

  /**
   * مسح الكاش لإجبار إعادة الكشف
   */
  public clearCache(): void {
    this.cachedTimezoneInfo = null;
    this.lastDetectionTime = 0;
  }

  /**
   * تنسيق الإزاحة الزمنية
   */
  private formatOffset(offset: number): string {
    const sign = offset >= 0 ? '+' : '-';
    const absOffset = Math.abs(offset);
    const hours = Math.floor(absOffset);
    const minutes = Math.round((absOffset - hours) * 60);
    
    if (minutes === 0) {
      return `UTC${sign}${hours}`;
    } else {
      return `UTC${sign}${hours}:${minutes.toString().padStart(2, '0')}`;
    }
  }

  /**
   * الحصول على اختصار المنطقة الزمنية
   */
  private getTimezoneAbbreviation(timezone: string): string {
    try {
      const formatter = new Intl.DateTimeFormat('en', {
        timeZone: timezone,
        timeZoneName: 'short'
      });
      
      const parts = formatter.formatToParts(new Date());
      const timeZonePart = parts.find(part => part.type === 'timeZoneName');
      return timeZonePart?.value || '';
    } catch (error) {
      return '';
    }
  }

  /**
   * استخراج اسم الدولة من المنطقة الزمنية
   */
  private getCountryFromTimezone(timezone: string): string {
    const countryMap: Record<string, string> = {
      'Africa/Tripoli': 'ليبيا',
      'Africa/almarai': 'مصر',
      'Asia/Riyadh': 'السعودية',
      'Asia/Dubai': 'الإمارات',
      'Europe/London': 'بريطانيا',
      'America/New_York': 'أمريكا',
      'Asia/Tokyo': 'اليابان',
      'Asia/Shanghai': 'الصين',
      'Europe/Paris': 'فرنسا',
      'Europe/Berlin': 'ألمانيا'
    };
    
    return countryMap[timezone] || timezone.split('/')[0];
  }

  /**
   * الحصول على معلومات المنطقة الزمنية الافتراضية
   */
  private getDefaultTimezoneInfo(): TimezoneInfo {
    return {
      timezone: 'Africa/Tripoli',
      offset: 2,
      offsetString: 'UTC+2',
      isDST: false,
      country: 'ليبيا',
      city: 'طرابلس',
      region: 'أفريقيا',
      abbreviation: 'EET'
    };
  }
}

// تصدير نسخة واحدة من الخدمة
export const timezoneDetectionService = TimezoneDetectionService.getInstance();
