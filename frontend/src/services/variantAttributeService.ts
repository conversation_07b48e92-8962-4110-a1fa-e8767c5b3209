import api from '../lib/axios';
import {
  VariantAttribute,
  VariantValue,
  CreateVariantAttributeData,
  UpdateVariantAttributeData,
  CreateVariantValueData,
  UpdateVariantValueData,
  AttributeOrderUpdate,
  ValueOrderUpdate,
  AttributeUsageInfo,
  DeleteAttributeResult
} from '../types/variantAttribute';

/**
 * خدمة إدارة خصائص المتغيرات
 * تطبق مبادئ البرمجة الكائنية مع معالجة شاملة للأخطاء
 */
export class VariantAttributeService {
  private static instance: VariantAttributeService;
  private baseUrl = '/api/variant-attributes';

  private constructor() {}

  public static getInstance(): VariantAttributeService {
    if (!VariantAttributeService.instance) {
      VariantAttributeService.instance = new VariantAttributeService();
    }
    return VariantAttributeService.instance;
  }

  // ==================== إدارة الخصائص ====================

  /**
   * جلب جميع خصائص المتغيرات
   */
  public async getAllAttributes(includeInactive: boolean = false): Promise<VariantAttribute[]> {
    try {
      console.log('🌐 إرسال طلب API إلى:', `${this.baseUrl}/`);
      const response = await api.get<VariantAttribute[]>(`${this.baseUrl}/`, {
        params: { include_inactive: includeInactive }
      });
      console.log('📡 استجابة API:', response.status, 'البيانات:', response.data?.length, 'عنصر');
      return response.data;
    } catch (error) {
      console.error('❌ خطأ في جلب خصائص المتغيرات:', error);
      throw new Error('فشل في جلب خصائص المتغيرات');
    }
  }

  /**
   * جلب خاصية محددة بواسطة المعرف
   */
  public async getAttributeById(attributeId: number): Promise<VariantAttribute> {
    try {
      const response = await api.get<VariantAttribute>(`${this.baseUrl}/${attributeId}`);
      return response.data;
    } catch (error) {
      console.error(`خطأ في جلب الخاصية ${attributeId}:`, error);
      throw new Error('فشل في جلب الخاصية');
    }
  }

  /**
   * إنشاء خاصية جديدة
   */
  public async createAttribute(attributeData: CreateVariantAttributeData): Promise<VariantAttribute> {
    try {
      const response = await api.post<VariantAttribute>(`${this.baseUrl}/`, attributeData);
      return response.data;
    } catch (error: any) {
      console.error('خطأ في إنشاء الخاصية:', error);
      const errorMessage = error.response?.data?.detail || 'فشل في إنشاء الخاصية';
      throw new Error(errorMessage);
    }
  }

  /**
   * تحديث خاصية موجودة
   */
  public async updateAttribute(
    attributeId: number, 
    attributeData: UpdateVariantAttributeData
  ): Promise<VariantAttribute> {
    try {
      const response = await api.put<VariantAttribute>(`${this.baseUrl}/${attributeId}`, attributeData);
      return response.data;
    } catch (error: any) {
      console.error(`خطأ في تحديث الخاصية ${attributeId}:`, error);
      const errorMessage = error.response?.data?.detail || 'فشل في تحديث الخاصية';
      throw new Error(errorMessage);
    }
  }

  /**
   * فحص استخدام الخاصية في المنتجات
   */
  public async checkAttributeUsage(attributeId: number): Promise<AttributeUsageInfo> {
    try {
      const response = await api.get<AttributeUsageInfo>(`${this.baseUrl}/${attributeId}/usage`);
      return response.data;
    } catch (error: any) {
      console.error(`خطأ في فحص استخدام الخاصية ${attributeId}:`, error);
      const errorMessage = error.response?.data?.detail || 'فشل في فحص استخدام الخاصية';
      throw new Error(errorMessage);
    }
  }

  /**
   * حذف خاصية مع فحص الاستخدام
   */
  public async deleteAttribute(
    attributeId: number,
    forceDelete: boolean = false,
    permanentDelete: boolean = true
  ): Promise<DeleteAttributeResult> {
    try {
      const response = await api.delete<DeleteAttributeResult>(`${this.baseUrl}/${attributeId}`, {
        params: {
          force_delete: forceDelete,
          permanent_delete: permanentDelete
        }
      });
      return response.data;
    } catch (error: any) {
      console.error(`خطأ في حذف الخاصية ${attributeId}:`, error);
      const errorMessage = error.response?.data?.detail || 'فشل في حذف الخاصية';
      throw new Error(errorMessage);
    }
  }

  /**
   * إعادة ترتيب الخصائص
   */
  public async reorderAttributes(attributeOrders: AttributeOrderUpdate[]): Promise<void> {
    try {
      await api.put(`${this.baseUrl}/reorder`, attributeOrders);
    } catch (error: any) {
      console.error('خطأ في إعادة ترتيب الخصائص:', error);
      const errorMessage = error.response?.data?.detail || 'فشل في إعادة ترتيب الخصائص';
      throw new Error(errorMessage);
    }
  }

  // ==================== إدارة قيم الخصائص ====================

  /**
   * جلب قيم خاصية معينة
   */
  public async getAttributeValues(
    attributeId: number, 
    includeInactive: boolean = false
  ): Promise<VariantValue[]> {
    try {
      const response = await api.get<VariantValue[]>(`${this.baseUrl}/${attributeId}/values`, {
        params: { include_inactive: includeInactive }
      });
      return response.data;
    } catch (error) {
      console.error(`خطأ في جلب قيم الخاصية ${attributeId}:`, error);
      throw new Error('فشل في جلب قيم الخاصية');
    }
  }

  /**
   * إضافة قيمة جديدة لخاصية
   */
  public async addAttributeValue(
    attributeId: number, 
    valueData: CreateVariantValueData
  ): Promise<VariantValue> {
    try {
      const response = await api.post<VariantValue>(`${this.baseUrl}/${attributeId}/values`, valueData);
      return response.data;
    } catch (error: any) {
      console.error(`خطأ في إضافة قيمة للخاصية ${attributeId}:`, error);
      const errorMessage = error.response?.data?.detail || 'فشل في إضافة القيمة';
      throw new Error(errorMessage);
    }
  }

  /**
   * تحديث قيمة خاصية
   */
  public async updateAttributeValue(
    valueId: number, 
    valueData: UpdateVariantValueData
  ): Promise<VariantValue> {
    try {
      const response = await api.put<VariantValue>(`${this.baseUrl}/values/${valueId}`, valueData);
      return response.data;
    } catch (error: any) {
      console.error(`خطأ في تحديث القيمة ${valueId}:`, error);
      const errorMessage = error.response?.data?.detail || 'فشل في تحديث القيمة';
      throw new Error(errorMessage);
    }
  }

  /**
   * حذف قيمة خاصية
   */
  public async deleteAttributeValue(valueId: number, permanentDelete: boolean = true): Promise<void> {
    try {
      await api.delete(`${this.baseUrl}/values/${valueId}`, {
        params: { permanent_delete: permanentDelete }
      });
    } catch (error: any) {
      console.error(`خطأ في حذف القيمة ${valueId}:`, error);
      const errorMessage = error.response?.data?.detail || 'فشل في حذف القيمة';
      throw new Error(errorMessage);
    }
  }

  /**
   * إعادة ترتيب قيم الخصائص
   */
  public async reorderAttributeValues(valueOrders: ValueOrderUpdate[]): Promise<void> {
    try {
      await api.put(`${this.baseUrl}/values/reorder`, valueOrders);
    } catch (error: any) {
      console.error('خطأ في إعادة ترتيب قيم الخصائص:', error);
      const errorMessage = error.response?.data?.detail || 'فشل في إعادة ترتيب قيم الخصائص';
      throw new Error(errorMessage);
    }
  }
}

// تصدير instance واحد للاستخدام في التطبيق
export const variantAttributeService = VariantAttributeService.getInstance();
