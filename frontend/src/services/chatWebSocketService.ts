/**
 * خدمة WebSocket للمحادثة الفورية
 * تدير الاتصال والرسائل والإشعارات
 */

import { appStateManager } from './appStateManager';

interface ChatMessage {
  id: number;
  sender_id: number;
  receiver_id: number;
  content: string;
  message_type: string;
  status: string;
  created_at: string;
  delivered_at?: string;
  read_at?: string;
  sender_username?: string;
  sender_full_name?: string;
}

interface WebSocketMessage {
  type: string;
  data?: any;
  message?: ChatMessage;
  user_id?: number;
  timestamp: string;
}



class ChatWebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 3; // تقليل عدد المحاولات
  private reconnectDelay = 3000; // زيادة التأخير إلى 3 ثوان
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private listeners: Map<string, Set<Function>> = new Map();
  private currentUserId: number | null = null;
  private connectionTimeout: NodeJS.Timeout | null = null;
  private lastConnectAttempt = 0;
  private minConnectInterval = 5000; // الحد الأدنى بين محاولات الاتصال (5 ثوان)
  private isManualDisconnect = false;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private networkStatusListener: (() => void) | null = null;

  constructor() {
    // تهيئة المستمعين
    this.setupEventListeners();
    // إعداد مراقبة حالة الشبكة
    this.setupNetworkMonitoring();
  }

  private setupEventListeners() {
    // إعداد المستمعين الأساسيين
    this.on('connection_established', (data: any) => {
      console.log('🎉 تم تأسيس اتصال المحادثة:', data);
    });

    this.on('new_message', (data: any) => {
      console.log('📨 رسالة جديدة:', data);
      // يمكن إضافة منطق الإشعارات هنا
    });

    this.on('user_status_change', (data: any) => {
      console.log('👤 تغيير حالة المستخدم:', data);
    });
  }

  private setupNetworkMonitoring() {
    // مراقبة حالة الشبكة للتفاعل الفوري
    const handleOnline = () => {
      console.log('🌐 الشبكة متاحة - محاولة إعادة الاتصال');
      if (this.currentUserId && !this.isConnected()) {
        this.connect(this.currentUserId).catch(console.error);
      }
      // تحديث حالة التطبيق
      appStateManager.updateConnectionStatus(true, 'network-online');
      this.emit('connection_status_changed', { isConnected: true, isConnecting: this.isConnecting });
    };

    const handleOffline = () => {
      console.log('🌐 الشبكة غير متاحة');
      // تحديث حالة التطبيق
      appStateManager.updateConnectionStatus(false, 'network-offline');
      this.emit('connection_status_changed', { isConnected: false, isConnecting: false });
    };

    // إضافة مستمعي الأحداث
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // حفظ المرجع لإزالة المستمعين لاحقاً
    this.networkStatusListener = () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }

  async connect(userId: number): Promise<void> {
    // منع الاتصالات المتكررة
    const now = Date.now();
    if (now - this.lastConnectAttempt < this.minConnectInterval) {
      console.log('🔄 تم تجاهل محاولة الاتصال - محاولة حديثة جداً');
      return;
    }

    return new Promise((resolve, reject) => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN && this.currentUserId === userId) {
        console.log('✅ الاتصال موجود بالفعل');
        resolve();
        return;
      }

      if (this.isConnecting) {
        console.log('🔄 الاتصال قيد التقدم بالفعل...');
        reject(new Error('الاتصال قيد التقدم'));
        return;
      }

      // إغلاق الاتصال السابق إذا وُجد
      if (this.ws) {
        this.cleanupConnection();
      }

      this.lastConnectAttempt = now;
      this.isManualDisconnect = false;
      this.isConnecting = true;
      this.currentUserId = userId;

      // إعداد timeout للاتصال
      this.connectionTimeout = setTimeout(() => {
        if (this.isConnecting) {
          console.warn('⏰ انتهت مهلة الاتصال');
          this.isConnecting = false;
          if (this.ws) {
            this.ws.close();
          }
          reject(new Error('Connection timeout'));
        }
      }, 10000); // 10 ثوان timeout

      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.hostname}:8002/ws/chat/${userId}`;

      console.log(`🔌 محاولة الاتصال بخدمة المحادثة للمستخدم ${userId}...`);

      try {
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log('🔌 تم الاتصال بخدمة المحادثة بنجاح');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          this.clearConnectionTimeout();
          this.startHeartbeat();

          // تحديث حالة التطبيق
          appStateManager.updateState({ isConnected: true });

          this.emit('connected', { userId });
          this.emit('connection_status_changed', { isConnected: true, isConnecting: false });
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const data: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(data);
          } catch (error) {
            console.warn('فشل في تحليل رسالة WebSocket:', error);
          }
        };

        this.ws.onclose = (event) => {
          console.log('🔌 انقطع اتصال المحادثة:', event.code, event.reason);
          this.isConnecting = false;
          this.clearConnectionTimeout();
          this.stopHeartbeat();

          // تحديث حالة التطبيق
          appStateManager.updateState({ isConnected: false });

          this.emit('disconnected', { code: event.code, reason: event.reason });
          this.emit('connection_status_changed', { isConnected: false, isConnecting: false });

          // إعادة الاتصال التلقائي فقط إذا لم يكن إغلاق يدوي
          if (!this.isManualDisconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('❌ خطأ في WebSocket المحادثة:', error);
          this.isConnecting = false;
          this.clearConnectionTimeout();
          this.emit('error', error);
          reject(error);
        };

      } catch (error) {
        this.isConnecting = false;
        this.clearConnectionTimeout();
        reject(error);
      }
    });
  }

  disconnect() {
    this.isManualDisconnect = true;
    this.cleanupConnection();
    this.emit('disconnected', {});
  }

  private cleanupConnection() {
    this.stopHeartbeat();
    this.clearConnectionTimeout();
    this.clearReconnectTimeout();

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    this.currentUserId = null;
    this.isConnecting = false;
  }

  private clearConnectionTimeout() {
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = null;
    }
  }

  private clearReconnectTimeout() {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
  }

  private handleMessage(data: WebSocketMessage) {
    const { type } = data;

    switch (type) {
      case 'connection_established':
        this.emit('connection_established', data);
        break;

      case 'new_message':
        this.emit('new_message', data);
        break;

      case 'message_deleted':
        this.emit('message_deleted', data);
        break;

      case 'message_edited':
        this.emit('message_edited', data);
        break;

      case 'messages_read':
        this.emit('messages_read', data);
        break;

      case 'user_status_change':
        this.emit('user_status_change', data);
        break;

      case 'user_typing':
        this.emit('user_typing', data);
        break;

      case 'user_stop_typing':
        this.emit('user_stop_typing', data);
        break;

      case 'pong':
        // استجابة heartbeat
        break;

      default:
        console.log('📨 نوع رسالة غير معروف:', type, data);
        this.emit('message', data);
    }
  }

  private startHeartbeat() {
    this.stopHeartbeat();
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.sendMessage({
          type: 'ping',
          timestamp: new Date().toISOString()
        });
      }
    }, 30000); // كل 30 ثانية
  }

  private stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  private scheduleReconnect() {
    // تنظيف timeout السابق إذا وُجد
    this.clearReconnectTimeout();

    this.reconnectAttempts++;
    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), 30000); // حد أقصى 30 ثانية

    console.log(`🔄 محاولة إعادة الاتصال ${this.reconnectAttempts}/${this.maxReconnectAttempts} خلال ${delay}ms`);

    // تحديث حالة التطبيق - محاولة إعادة الاتصال
    this.emit('connection_status_changed', { isConnected: false, isConnecting: true });

    this.reconnectTimeout = setTimeout(() => {
      if (this.currentUserId && !this.isManualDisconnect && this.reconnectAttempts <= this.maxReconnectAttempts) {
        this.connect(this.currentUserId).catch((error) => {
          console.error('فشل في إعادة الاتصال:', error);

          // إذا فشلت المحاولة، جدولة محاولة أخرى
          if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          } else {
            console.error('❌ تم استنفاد جميع محاولات إعادة الاتصال');
            this.emit('max_reconnect_attempts_reached', {});
            this.emit('connection_status_changed', { isConnected: false, isConnecting: false });
          }
        });
      }
    }, delay);
  }

  sendMessage(message: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('⚠️ WebSocket غير متصل، لا يمكن إرسال الرسالة');
    }
  }

  // إرسال إشعار الكتابة
  sendTypingNotification(receiverId: number) {
    this.sendMessage({
      type: 'typing',
      receiver_id: receiverId,
      timestamp: new Date().toISOString()
    });
  }

  // إرسال إشعار توقف الكتابة
  sendStopTypingNotification(receiverId: number) {
    this.sendMessage({
      type: 'stop_typing',
      receiver_id: receiverId,
      timestamp: new Date().toISOString()
    });
  }

  // إدارة المستمعين
  on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(callback);
  }

  off(event: string, callback: Function) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.delete(callback);
    }
  }

  private emit(event: string, data: any) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`خطأ في مستمع الحدث ${event}:`, error);
        }
      });
    }
  }

  // الحصول على حالة الاتصال الحالية
  getConnectionState() {
    return {
      isConnected: this.isConnected(),
      isConnecting: this.isConnecting,
      reconnectAttempts: this.reconnectAttempts,
      maxReconnectAttempts: this.maxReconnectAttempts,
      userId: this.currentUserId
    };
  }

  // تنظيف الموارد
  cleanup() {
    // إزالة مستمعي الشبكة
    if (this.networkStatusListener) {
      this.networkStatusListener();
      this.networkStatusListener = null;
    }

    // إغلاق الاتصال
    this.disconnect();

    // تنظيف المؤقتات
    this.clearConnectionTimeout();
    this.clearReconnectTimeout();
    this.stopHeartbeat();

    // مسح المستمعين
    this.listeners.clear();
  }

  // فحص ما إذا كان متصلاً
  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN || false;
  }
}

// إنشاء مثيل واحد من الخدمة
export const chatWebSocketService = new ChatWebSocketService();
export default chatWebSocketService;
