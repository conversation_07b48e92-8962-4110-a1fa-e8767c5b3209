/**
 * خدمة التخزين المؤقت للـ Layout
 * تحسن أداء القائمة الجانبية والشريط العلوي
 * تطبق مبادئ البرمجة الكائنية (OOP)
 */

interface LayoutCacheData {
  sidebarState: {
    isOpen: boolean;
    activeMenuItem: string | null;
    activeSubMenuItem: string | null;
    expandedMenus: string[];
  };
  topbarState: {
    searchQuery: string;
    showUserMenu: boolean;
    showQuickActions: boolean;
  };
  timestamp: number;
  version: string;
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  hits: number;
}

export class LayoutCacheService {
  private static instance: LayoutCacheService;
  private cache = new Map<string, CacheEntry<any>>();
  private readonly CACHE_VERSION = '1.0.0';
  private readonly DEFAULT_TTL = 30 * 60 * 1000; // 30 دقيقة
  private readonly MAX_CACHE_SIZE = 50;
  private readonly STORAGE_KEY = 'smartpos_layout_cache';

  private constructor() {
    this.loadFromStorage();
    this.startCleanupInterval();
  }

  public static getInstance(): LayoutCacheService {
    if (!LayoutCacheService.instance) {
      LayoutCacheService.instance = new LayoutCacheService();
    }
    return LayoutCacheService.instance;
  }

  /**
   * حفظ حالة الـ Layout في الكاش
   */
  public cacheLayoutState(
    sidebarState: LayoutCacheData['sidebarState'],
    topbarState: LayoutCacheData['topbarState']
  ): void {
    try {
      const cacheData: LayoutCacheData = {
        sidebarState,
        topbarState,
        timestamp: Date.now(),
        version: this.CACHE_VERSION
      };

      this.set('layout_state', cacheData, this.DEFAULT_TTL);
      this.saveToStorage();
      
      console.log('✅ [LayoutCache] تم حفظ حالة Layout في الكاش');
    } catch (error) {
      console.error('❌ [LayoutCache] خطأ في حفظ حالة Layout:', error);
    }
  }

  /**
   * استرجاع حالة الـ Layout من الكاش
   */
  public getLayoutState(): LayoutCacheData | null {
    try {
      const cached = this.get<LayoutCacheData>('layout_state');
      
      if (cached && cached.version === this.CACHE_VERSION) {
        console.log('✅ [LayoutCache] تم استرجاع حالة Layout من الكاش');
        return cached;
      }
      
      console.log('ℹ️ [LayoutCache] لا توجد حالة Layout محفوظة أو الإصدار قديم');
      return null;
    } catch (error) {
      console.error('❌ [LayoutCache] خطأ في استرجاع حالة Layout:', error);
      return null;
    }
  }

  /**
   * حفظ قيمة في الكاش
   */
  private set<T>(key: string, data: T, ttl: number = this.DEFAULT_TTL): void {
    // تنظيف الكاش إذا امتلأ
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      this.evictOldest();
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      hits: 0
    };

    this.cache.set(key, entry);
  }

  /**
   * جلب قيمة من الكاش
   */
  private get<T>(key: string): T | null {
    const entry = this.cache.get(key) as CacheEntry<T> | undefined;
    
    if (!entry) {
      return null;
    }

    // فحص انتهاء الصلاحية
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    // تحديث عداد الاستخدام
    entry.hits++;
    
    return entry.data;
  }

  /**
   * إزالة أقدم العناصر من الكاش
   */
  private evictOldest(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      console.log(`🗑️ [LayoutCache] تم حذف العنصر الأقدم: ${oldestKey}`);
    }
  }

  /**
   * حفظ الكاش في localStorage
   */
  private saveToStorage(): void {
    try {
      const cacheData = Array.from(this.cache.entries());
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(cacheData));
    } catch (error) {
      console.warn('⚠️ [LayoutCache] فشل في حفظ الكاش في localStorage:', error);
    }
  }

  /**
   * تحميل الكاش من localStorage
   */
  private loadFromStorage(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const cacheData = JSON.parse(stored) as Array<[string, CacheEntry<any>]>;
        this.cache = new Map(cacheData);
        
        // تنظيف العناصر المنتهية الصلاحية
        this.cleanupExpired();
        
        console.log(`✅ [LayoutCache] تم تحميل ${this.cache.size} عنصر من localStorage`);
      }
    } catch (error) {
      console.warn('⚠️ [LayoutCache] فشل في تحميل الكاش من localStorage:', error);
      this.cache.clear();
    }
  }

  /**
   * تنظيف العناصر المنتهية الصلاحية
   */
  private cleanupExpired(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.cache.delete(key));
    
    if (expiredKeys.length > 0) {
      console.log(`🗑️ [LayoutCache] تم حذف ${expiredKeys.length} عنصر منتهي الصلاحية`);
    }
  }

  /**
   * بدء عملية التنظيف الدورية
   */
  private startCleanupInterval(): void {
    setInterval(() => {
      this.cleanupExpired();
      this.saveToStorage();
    }, 5 * 60 * 1000); // كل 5 دقائق
  }

  /**
   * مسح جميع البيانات المحفوظة
   */
  public clearCache(): void {
    this.cache.clear();
    localStorage.removeItem(this.STORAGE_KEY);
    console.log('🗑️ [LayoutCache] تم مسح جميع البيانات المحفوظة');
  }

  /**
   * الحصول على إحصائيات الكاش
   */
  public getCacheStats(): {
    size: number;
    totalHits: number;
    oldestEntry: number;
    newestEntry: number;
  } {
    let totalHits = 0;
    let oldestEntry = Date.now();
    let newestEntry = 0;

    for (const entry of this.cache.values()) {
      totalHits += entry.hits;
      oldestEntry = Math.min(oldestEntry, entry.timestamp);
      newestEntry = Math.max(newestEntry, entry.timestamp);
    }

    return {
      size: this.cache.size,
      totalHits,
      oldestEntry,
      newestEntry
    };
  }
}

// إنشاء مثيل واحد للخدمة
export const layoutCacheService = LayoutCacheService.getInstance();
