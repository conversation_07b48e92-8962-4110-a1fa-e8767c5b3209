/**
 * خدمة الحل التلقائي الاحترافية - معالجة في الخلفية
 * هذه الخدمة تستدعي API واحد في الخلفية لمعالجة جميع الأخطاء
 * بدلاً من معالجة كل خطأ منفرد من الواجهة
 */

import api from '../lib/axios';

export interface AutoResolveBackendRequest {
  log_ids: number[];
}

export interface AutoResolveBackendResponse {
  success: boolean;
  total: number;
  resolved: number;
  failed: number;
  errors: string[];
  message: string;
}

export interface AutoResolveProgress {
  stage: 'starting' | 'processing' | 'completed' | 'error';
  percentage: number;
  message: string;
  resolved?: number;
  failed?: number;
  total?: number;
}

class AutoResolveBackendService {
  private progressCallback: ((progress: AutoResolveProgress) => void) | null = null;
  private isProcessing = false;

  // تعيين callback للتقدم
  setProgressCallback(callback: (progress: AutoResolveProgress) => void) {
    this.progressCallback = callback;
  }

  // تحديث التقدم
  private updateProgress(progress: AutoResolveProgress) {
    if (this.progressCallback) {
      this.progressCallback(progress);
    }
  }

  // الدالة الرئيسية للحل التلقائي
  async autoResolveSystemLogs(logIds: number[]): Promise<AutoResolveBackendResponse> {
    if (this.isProcessing) {
      throw new Error('عملية الحل التلقائي قيد التشغيل بالفعل');
    }

    this.isProcessing = true;

    try {
      // مرحلة البداية
      this.updateProgress({
        stage: 'starting',
        percentage: 0,
        message: `بدء عملية الحل التلقائي لـ ${logIds.length} سجل...`,
        total: logIds.length
      });

      // تأخير قصير لعرض البداية
      await this.delay(800);

      // مرحلة المعالجة
      this.updateProgress({
        stage: 'processing',
        percentage: 50,
        message: 'جاري معالجة السجلات في الخلفية...',
        total: logIds.length
      });

      // استدعاء API الخلفية باستخدام axios instance المُعد مسبقاً
      // هذا سيتولى إضافة التوكن وإدارة المصادقة تلقائياً
      const response = await api.post('/api/system/logs/auto-resolve', {
        log_ids: logIds
      });

      const result: AutoResolveBackendResponse = response.data;

      // مرحلة الاكتمال
      this.updateProgress({
        stage: 'completed',
        percentage: 100,
        message: result.message,
        resolved: result.resolved,
        failed: result.failed,
        total: result.total
      });

      // تأخير لعرض النتيجة النهائية
      await this.delay(1500);

      return result;

    } catch (error) {
      // مرحلة الخطأ
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير متوقع';
      
      this.updateProgress({
        stage: 'error',
        percentage: 0,
        message: `خطأ في العملية: ${errorMessage}`,
        total: logIds.length
      });

      throw error;

    } finally {
      this.isProcessing = false;
    }
  }

  // دالة مساعدة للتأخير
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // فحص حالة المعالجة
  isCurrentlyProcessing(): boolean {
    return this.isProcessing;
  }

  // إيقاف العملية (في هذه الحالة لا يمكن إيقافها لأنها في الخلفية)
  stop() {
    // لا يمكن إيقاف العملية لأنها تحدث في الخلفية
    // لكن يمكن تحديث الواجهة لإظهار أن المستخدم طلب الإيقاف
    if (this.isProcessing) {
      this.updateProgress({
        stage: 'error',
        percentage: 0,
        message: 'لا يمكن إيقاف العملية - تتم المعالجة في الخلفية',
        total: 0
      });
    }
  }
}

// إنشاء instance واحد للخدمة
export const autoResolveBackendService = new AutoResolveBackendService();
