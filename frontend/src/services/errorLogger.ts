import apiClient from '../lib/axios';
import { systemConfigManager } from '../config/systemConfig';

export interface ErrorLogData {
  level: 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL';
  source: 'FRONTEND' | 'BACKEND' | 'DATABASE' | 'SYSTEM';
  message: string;
  details?: string;
  stack_trace?: string;
  session_id?: string;
}

interface LoggerConfig {
  maxQueueSize: number;
  flushInterval: number;
  enableAutoFlush: boolean;
  logLevels: string[];
  enablePerformanceLogging: boolean;
  enableDebugMode: boolean;
}

class ErrorLogger {
  private sessionId: string;
  private isEnabled: boolean = true;
  private logQueue: ErrorLogData[] = [];
  private isProcessing: boolean = false;
  private flushTimer: NodeJS.Timeout | null = null;
  private recentErrors: Map<string, { count: number; lastSeen: Date; level: string }> = new Map();
  private config: LoggerConfig = {
    maxQueueSize: 20, // تقليل حجم القائمة
    flushInterval: 120000, // زيادة الفترة إلى دقيقتين
    enableAutoFlush: true,
    logLevels: ['ERROR', 'CRITICAL'], // تسجيل الأخطاء المهمة فقط
    enablePerformanceLogging: false,
    enableDebugMode: false
  };

  constructor(config?: Partial<LoggerConfig>) {
    if (config) {
      this.config = { ...this.config, ...config };
    }

    this.sessionId = this.generateSessionId();
    this.setupGlobalErrorHandlers();

    if (this.config.enableAutoFlush) {
      this.startPeriodicFlush();
    }

    // إرسال السجلات المحفوظة محلياً عند بدء التطبيق (بتأخير)
    setTimeout(() => {
      this.sendPendingLogs();
    }, 100);
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private setupGlobalErrorHandlers(): void {
    // معالج الأخطاء العامة
    window.addEventListener('error', (event) => {
      // تجاهل أخطاء Chrome Extensions
      if (event.filename && event.filename.includes('chrome-extension://')) {
        return;
      }

      // تجاهل أخطاء React DevTools المعروفة
      if (event.message && event.message.includes('React is running in production mode')) {
        return;
      }

      this.logError('ERROR', 'FRONTEND', event.message, {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack
      });
    });

    // معالج أخطاء Promise غير المعالجة
    window.addEventListener('unhandledrejection', (event) => {
      // تجاهل أخطاء Chrome Extensions
      const reason = event.reason?.toString() || '';
      if (reason.includes('chrome-extension://') ||
          reason.includes('React is running in production mode')) {
        return;
      }

      this.logError('ERROR', 'FRONTEND', 'Unhandled Promise Rejection', {
        reason: event.reason,
        stack: event.reason?.stack
      });
    });

    // معالج أخطاء React (يتطلب إعداد Error Boundary)
    const originalConsoleError = console.error;
    console.error = (...args) => {
      // تسجيل أخطاء React فقط (تجاهل أخطاء Chrome Extensions)
      if (args[0] && typeof args[0] === 'string') {
        const message = args[0];

        // تجاهل أخطاء Chrome Extensions و React DevTools
        if (message.includes('chrome-extension://') ||
            message.includes('React is running in production mode') ||
            message.includes('installHook.js')) {
          originalConsoleError.apply(console, args);
          return;
        }

        // تسجيل أخطاء React الحقيقية فقط
        if (message.includes('React') && !message.includes('production mode')) {
          this.logError('ERROR', 'FRONTEND', message, {
            details: args.slice(1).join(' ')
          });
        }
      }
      originalConsoleError.apply(console, args);
    };
  }

  private startPeriodicFlush(): void {
    // تعطيل الإرسال الدوري للسجلات لتوفير موارد النظام
    // this.flushTimer = setInterval(() => {
    //   if (this.logQueue.length > 0) {
    //     this.flushLogs();
    //   }
    // }, this.config.flushInterval);

    // إرسال السجلات عند إغلاق الصفحة
    window.addEventListener('beforeunload', () => {
      this.flushLogs(true);
    });

    // إرسال السجلات عند إخفاء الصفحة (تبديل التبويبات)
    document.addEventListener('visibilitychange', () => {
      if (document.hidden && this.logQueue.length > 0) {
        this.flushLogs(true);
      }
    });
  }

  private sanitizeDetails(details: any): any {
    if (!details || typeof details !== 'object') return details;

    const maxDepth = 3;
    const maxStringLength = 500;

    const sanitizeValue = (value: any, depth: number): any => {
      if (depth > maxDepth) return '[Max depth reached]';

      if (typeof value === 'string') {
        return value.length > maxStringLength ?
          value.substring(0, maxStringLength) + '...' : value;
      }

      if (Array.isArray(value)) {
        return value.slice(0, 10).map(item => sanitizeValue(item, depth + 1));
      }

      if (value && typeof value === 'object') {
        const result: any = {};
        let count = 0;
        for (const [key, val] of Object.entries(value)) {
          if (count >= 20) break; // حد أقصى للخصائص
          result[key] = sanitizeValue(val, depth + 1);
          count++;
        }
        return result;
      }

      return value;
    };

    return sanitizeValue(details, 0);
  }

  // التحقق من صحة الخطأ قبل تسجيله
  private validateError(level: ErrorLogData['level'], source: ErrorLogData['source'], message: string, details?: any): boolean {
    // تجاهل الرسائل الفارغة أو غير المفيدة
    if (!message || message.trim().length === 0) {
      return false;
    }

    // تجاهل الأخطاء المعروفة وغير المفيدة
    const ignoredPatterns = [
      /chrome-extension:\/\//i,
      /react is running in production mode/i,
      /installhook\.js/i,
      /non-passive event listener/i,
      /script error/i,
      /network error/i,
      /loading chunk \d+ failed/i,
      /chunkloaderror/i,
      /failed to fetch/i,
      /the request timed out/i,
      /cancelled/i,
      /aborted/i
    ];

    for (const pattern of ignoredPatterns) {
      if (pattern.test(message)) {
        return false;
      }
    }

    // التحقق من تفاصيل الخطأ
    if (details && typeof details === 'object') {
      const detailsStr = JSON.stringify(details).toLowerCase();
      for (const pattern of ignoredPatterns) {
        if (pattern.test(detailsStr)) {
          return false;
        }
      }
    }

    // التحقق من صحة المصدر
    const validSources = ['FRONTEND', 'BACKEND', 'DATABASE', 'SYSTEM'];
    if (!validSources.includes(source)) {
      return false;
    }

    // التحقق من صحة المستوى
    const validLevels = ['INFO', 'WARNING', 'ERROR', 'CRITICAL'];
    if (!validLevels.includes(level)) {
      return false;
    }

    return true;
  }

  // التحقق من الأخطاء المكررة
  private isDuplicateError(message: string, level: string): boolean {
    const errorKey = `${level}:${message}`;
    const now = new Date();
    const existing = this.recentErrors.get(errorKey);

    if (existing) {
      const timeDiff = now.getTime() - existing.lastSeen.getTime();

      // إذا كان الخطأ نفسه حدث خلال آخر 5 دقائق
      if (timeDiff < 300000) { // 5 دقائق
        existing.count++;
        existing.lastSeen = now;

        // السماح بتسجيل الخطأ كل 10 مرات للأخطاء الحرجة
        if (level === 'CRITICAL' && existing.count % 10 === 0) {
          return false;
        }

        // السماح بتسجيل الخطأ كل 20 مرة للأخطاء العادية
        if (level === 'ERROR' && existing.count % 20 === 0) {
          return false;
        }

        return true; // تجاهل الخطأ المكرر
      }
    }

    return false; // ليس مكرراً
  }

  // إضافة خطأ إلى قائمة الأخطاء الأخيرة
  private addToRecentErrors(message: string, level: string): void {
    const errorKey = `${level}:${message}`;
    const now = new Date();

    this.recentErrors.set(errorKey, {
      count: 1,
      lastSeen: now,
      level
    });

    // تنظيف الأخطاء القديمة (أكثر من ساعة)
    for (const [key, error] of this.recentErrors.entries()) {
      const timeDiff = now.getTime() - error.lastSeen.getTime();
      if (timeDiff > 3600000) { // ساعة واحدة
        this.recentErrors.delete(key);
      }
    }

    // الحد الأقصى لحجم الخريطة
    if (this.recentErrors.size > 100) {
      const oldestKey = this.recentErrors.keys().next().value;
      if (oldestKey) {
        this.recentErrors.delete(oldestKey);
      }
    }
  }

  public logInfo(message: string, details?: any): void {
    this.logError('INFO', 'FRONTEND', message, details);
  }

  public logWarning(message: string, details?: any): void {
    this.logError('WARNING', 'FRONTEND', message, details);
  }

  public logError(level: ErrorLogData['level'], source: ErrorLogData['source'], message: string, details?: any): void {
    if (!this.isEnabled) return;

    // فلترة حسب مستوى السجل المسموح
    if (!this.config.logLevels.includes(level)) {
      if (this.config.enableDebugMode) {
        console.debug(`Log filtered: ${level} - ${message}`);
      }
      return;
    }

    // التحقق من صحة الخطأ قبل تسجيله
    if (!this.validateError(level, source, message, details)) {
      if (this.config.enableDebugMode) {
        console.debug(`Error validation failed: ${level} - ${message}`);
      }
      return;
    }

    // التحقق من الأخطاء المكررة
    if (this.isDuplicateError(message, level)) {
      if (this.config.enableDebugMode) {
        console.debug(`Duplicate error filtered: ${message}`);
      }
      return;
    }

    const logData: ErrorLogData = {
      level,
      source,
      message,
      session_id: this.sessionId
    };

    if (details) {
      try {
        if (typeof details === 'object') {
          // تقليل حجم البيانات المرسلة
          const sanitizedDetails = this.sanitizeDetails(details);
          logData.details = JSON.stringify(sanitizedDetails);
          if (details.stack) {
            logData.stack_trace = details.stack;
          }
        } else {
          logData.details = String(details).substring(0, 1000); // تحديد الحد الأقصى
        }
      } catch (error) {
        logData.details = 'Error serializing details';
      }
    }

    // إدارة حجم القائمة
    if (this.logQueue.length >= this.config.maxQueueSize) {
      this.logQueue.shift(); // إزالة أقدم سجل
    }

    this.logQueue.push(logData);

    // تسجيل الخطأ في قائمة الأخطاء الأخيرة للتحقق من التكرار
    this.addToRecentErrors(message, level);

    // إرسال فوري للأخطاء الحرجة
    if (level === 'CRITICAL') {
      this.flushLogs();
    }
  }

  public logCritical(message: string, details?: any): void {
    this.logError('CRITICAL', 'FRONTEND', message, details);
  }

  public logApiError(error: any, endpoint: string): void {
    const message = `API Error: ${endpoint}`;
    const details = {
      endpoint,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message
    };

    this.logError('ERROR', 'FRONTEND', message, details);
  }

  public logUserAction(action: string, details?: any): void {
    this.logError('INFO', 'FRONTEND', `User Action: ${action}`, details);
  }

  public logPerformance(metric: string, value: number, details?: any): void {
    if (!this.config.enablePerformanceLogging) return;

    const message = `Performance: ${metric} = ${value}ms`;
    this.logError('INFO', 'FRONTEND', message, details);
  }

  // الحصول على إحصائيات الأخطاء الأخيرة
  public getErrorStatistics(): {
    totalErrors: number;
    criticalErrors: number;
    recentErrors: number;
    duplicateErrors: number;
    errorsByLevel: Record<string, number>;
  } {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 3600000);

    let totalErrors = 0;
    let criticalErrors = 0;
    let recentErrors = 0;
    let duplicateErrors = 0;
    const errorsByLevel: Record<string, number> = {};

    for (const [, error] of this.recentErrors.entries()) {
      totalErrors++;

      if (error.level === 'CRITICAL') {
        criticalErrors++;
      }

      if (error.lastSeen > oneHourAgo) {
        recentErrors++;
      }

      if (error.count > 1) {
        duplicateErrors++;
      }

      errorsByLevel[error.level] = (errorsByLevel[error.level] || 0) + 1;
    }

    return {
      totalErrors,
      criticalErrors,
      recentErrors,
      duplicateErrors,
      errorsByLevel
    };
  }

  // تنظيف الأخطاء القديمة يدوياً
  public cleanupOldErrors(): void {
    const now = new Date();
    const cutoffTime = new Date(now.getTime() - 3600000); // ساعة واحدة

    for (const [key, error] of this.recentErrors.entries()) {
      if (error.lastSeen < cutoffTime) {
        this.recentErrors.delete(key);
      }
    }
  }

  // الحصول على قائمة الأخطاء المكررة
  public getDuplicateErrors(): Array<{
    message: string;
    level: string;
    count: number;
    lastSeen: Date;
  }> {
    const duplicates: Array<{
      message: string;
      level: string;
      count: number;
      lastSeen: Date;
    }> = [];

    for (const [key, error] of this.recentErrors.entries()) {
      if (error.count > 1) {
        const [level, message] = key.split(':', 2);
        duplicates.push({
          message,
          level,
          count: error.count,
          lastSeen: error.lastSeen
        });
      }
    }

    return duplicates.sort((a, b) => b.count - a.count);
  }

  // تقييم جودة الأخطاء
  public assessErrorQuality(): {
    qualityScore: number;
    issues: string[];
    recommendations: string[];
  } {
    const stats = this.getErrorStatistics();
    const duplicates = this.getDuplicateErrors();

    let qualityScore = 100;
    const issues: string[] = [];
    const recommendations: string[] = [];

    // تقليل النقاط للأخطاء المكررة
    if (duplicates.length > 5) {
      qualityScore -= 20;
      issues.push(`يوجد ${duplicates.length} أخطاء مكررة`);
      recommendations.push('مراجعة الأخطاء المكررة وإصلاح مصادرها');
    }

    // تقليل النقاط للأخطاء الحرجة
    if (stats.criticalErrors > 3) {
      qualityScore -= 30;
      issues.push(`يوجد ${stats.criticalErrors} أخطاء حرجة`);
      recommendations.push('إصلاح الأخطاء الحرجة فوراً');
    }

    // تقليل النقاط للأخطاء الأخيرة الكثيرة
    if (stats.recentErrors > 10) {
      qualityScore -= 15;
      issues.push(`يوجد ${stats.recentErrors} أخطاء في الساعة الأخيرة`);
      recommendations.push('مراقبة النظام وتحديد مصادر الأخطاء');
    }

    qualityScore = Math.max(0, qualityScore);

    return {
      qualityScore,
      issues,
      recommendations
    };
  }

  // دالة لتنظيف الموارد
  public destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }

    // إرسال السجلات المتبقية
    if (this.logQueue.length > 0) {
      this.flushLogs(true);
    }

    this.isEnabled = false;
  }

  // دالة لتحديث الإعدادات
  public updateConfig(newConfig: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...newConfig };

    // إعادة تشغيل المؤقت إذا تغيرت الفترة
    if (newConfig.flushInterval && this.flushTimer) {
      clearInterval(this.flushTimer);
      this.startPeriodicFlush();
    }
  }

  private async flushLogs(isSync: boolean = false): Promise<void> {
    if (this.isProcessing || this.logQueue.length === 0) return;

    this.isProcessing = true;
    const logsToSend = [...this.logQueue];
    this.logQueue = [];

    try {
      if (isSync) {
        // إرسال متزامن عند إغلاق الصفحة - تعطيل مؤقتاً لتجنب أخطاء 404
        // sendBeacon لا يدعم headers للمصادقة، لذا سنحفظ السجلات محلياً
        try {
          const savedLogs = JSON.parse(localStorage.getItem('pending_error_logs') || '[]');
          savedLogs.push(...logsToSend);
          localStorage.setItem('pending_error_logs', JSON.stringify(savedLogs.slice(-50))); // حفظ آخر 50 سجل فقط
        } catch (error) {
          console.error('Failed to save logs to localStorage:', error);
        }
      } else {
        // إرسال غير متزامن
        for (const log of logsToSend) {
          await this.sendLog(log);
        }
      }
    } catch (error) {
      // إعادة إضافة السجلات إلى القائمة في حالة الفشل
      this.logQueue.unshift(...logsToSend);
      console.error('Failed to send logs:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  private async sendLog(logData: ErrorLogData): Promise<void> {
    try {
      await apiClient.post('/api/system/logs', logData);
    } catch (error) {
      // تجنب الحلقة اللانهائية من تسجيل أخطاء إرسال السجلات
      console.error('Failed to send log:', error);
      throw error;
    }
  }

  private async sendPendingLogs(): Promise<void> {
    try {
      const pendingLogs = localStorage.getItem('pending_error_logs');
      if (!pendingLogs) return;

      const logs = JSON.parse(pendingLogs);
      if (!Array.isArray(logs) || logs.length === 0) return;

      // إرسال السجلات المحفوظة
      for (const log of logs) {
        try {
          await this.sendLog(log);
        } catch (error) {
          console.error('Failed to send pending log:', error);
          // في حالة فشل الإرسال، نتوقف لتجنب إرسال نفس السجلات مرة أخرى
          break;
        }
      }

      // مسح السجلات المرسلة بنجاح
      localStorage.removeItem('pending_error_logs');
    } catch (error) {
      console.error('Failed to process pending logs:', error);
    }
  }

  public enable(): void {
    this.isEnabled = true;
  }

  public disable(): void {
    this.isEnabled = false;
  }

  public getSessionId(): string {
    return this.sessionId;
  }

  public async getSystemHealth(): Promise<any> {
    try {
      const response = await apiClient.get('/api/system/health');
      return response.data;
    } catch (error) {
      this.logApiError(error, '/api/system/health');
      throw error;
    }
  }

  public async getSystemLogs(): Promise<any> {
    try {
      const response = await apiClient.get('/api/system/logs');
      return response.data;
    } catch (error) {
      this.logApiError(error, '/api/system/logs');
      throw error;
    }
  }

  public async clearSystemLogs(complete: boolean = false): Promise<any> {
    try {
      const params = complete ? { complete: 'true' } : {};
      const response = await apiClient.delete('/api/system/logs', { params });

      if (!complete) {
        this.logInfo('System logs cleared by user');
      }

      return response.data;
    } catch (error) {
      this.logApiError(error, '/api/system/logs');
      throw error;
    }
  }

  public async resolveLog(logId: number, notes?: string): Promise<any> {
    try {
      const response = await apiClient.patch(`/api/system/logs/${logId}/resolve`, {
        resolution_notes: notes
      });
      this.logInfo(`System log resolved: ${logId}`, { notes });
      return response.data;
    } catch (error) {
      this.logApiError(error, `/api/system/logs/${logId}/resolve`);
      throw error;
    }
  }

  public async sendLogsToSupport(logIds: number[]): Promise<any> {
    try {
      const response = await apiClient.post('/api/system/logs/send-support', {
        logs: logIds
      });
      this.logInfo('Logs sent to support', { count: logIds.length });
      return response.data;
    } catch (error) {
      this.logApiError(error, '/api/system/logs/send-support');
      throw error;
    }
  }

  // دالة لتشخيص النظام وإصلاح المشاكل الشائعة
  public async runSystemDiagnostics(): Promise<any> {
    this.logInfo('Running system diagnostics');

    const diagnostics = {
      timestamp: new Date().toISOString(),
      browser: {
        userAgent: navigator.userAgent,
        language: navigator.language,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine
      },
      performance: {
        memory: (performance as any).memory ? {
          usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
          totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
          jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit
        } : null,
        timing: performance.timing ? {
          loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart,
          domReady: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart,
          firstPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-paint')?.startTime
        } : null
      },
      storage: {
        localStorage: this.checkLocalStorage(),
        sessionStorage: this.checkSessionStorage()
      },
      network: {
        connection: (navigator as any).connection ? {
          effectiveType: (navigator as any).connection.effectiveType,
          downlink: (navigator as any).connection.downlink,
          rtt: (navigator as any).connection.rtt
        } : null
      }
    };

    this.logInfo('System diagnostics completed', diagnostics);
    return diagnostics;
  }

  private checkLocalStorage(): any {
    try {
      const testKey = 'test_storage';
      localStorage.setItem(testKey, 'test');
      localStorage.removeItem(testKey);
      return {
        available: true,
        size: JSON.stringify(localStorage).length
      };
    } catch (error) {
      return {
        available: false,
        error: (error as any)?.message || 'Unknown error'
      };
    }
  }

  private checkSessionStorage(): any {
    try {
      const testKey = 'test_storage';
      sessionStorage.setItem(testKey, 'test');
      sessionStorage.removeItem(testKey);
      return {
        available: true,
        size: JSON.stringify(sessionStorage).length
      };
    } catch (error) {
      return {
        available: false,
        error: (error as any)?.message || 'Unknown error'
      };
    }
  }
}

// إنشاء مثيل واحد للاستخدام في جميع أنحاء التطبيق مع إعدادات محسنة
const config = systemConfigManager.getConfig();
const errorLogger = new ErrorLogger({
  maxQueueSize: config.errorLogging.maxQueueSize,
  flushInterval: config.errorLogging.flushInterval,
  enableAutoFlush: config.errorLogging.enableAutoFlush,
  logLevels: config.errorLogging.logLevels,
  enablePerformanceLogging: config.errorLogging.enablePerformanceLogging,
  enableDebugMode: config.errorLogging.enableDebugMode
});

// الاستماع لتغييرات الإعدادات
systemConfigManager.addListener((newConfig) => {
  errorLogger.updateConfig({
    maxQueueSize: newConfig.errorLogging.maxQueueSize,
    flushInterval: newConfig.errorLogging.flushInterval,
    enableAutoFlush: newConfig.errorLogging.enableAutoFlush,
    logLevels: newConfig.errorLogging.logLevels,
    enablePerformanceLogging: newConfig.errorLogging.enablePerformanceLogging,
    enableDebugMode: newConfig.errorLogging.enableDebugMode
  });
});

export default errorLogger;
export { ErrorLogger, type LoggerConfig };
