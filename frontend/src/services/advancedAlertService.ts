import { customToast } from '../components/alerts/CustomToast';
import errorLogger from './errorLogger';

export interface AdvancedSystemAlert {
  id: string;
  type: 'critical' | 'error' | 'warning' | 'info' | 'success';
  title: string;
  message: string;
  timestamp: Date;
  source: string;
  category?: string;
  priority: number; // 1-10 (10 = أعلى أولوية)
  persistent?: boolean;
  autoHide?: boolean;
  hideAfter?: number; // بالميلي ثانية
  actions?: AlertAction[];
  metadata?: Record<string, any>;
  groupKey?: string; // لتجميع التنبيهات المتشابهة
  fingerprint?: string; // لمنع التكرار
  toastId?: string; // معرف التنبيه في react-hot-toast
}

export interface AlertAction {
  id: string;
  label: string;
  action: () => void | Promise<void>;
  style?: 'primary' | 'secondary' | 'danger' | 'success';
  icon?: string;
  disabled?: boolean;
}

export interface AlertFilter {
  types?: AdvancedSystemAlert['type'][];
  sources?: string[];
  categories?: string[];
  minPriority?: number;
  maxAge?: number; // بالدقائق
  persistent?: boolean;
}

export interface AlertStats {
  total: number;
  byType: Record<string, number>;
  bySource: Record<string, number>;
  byCategory: Record<string, number>;
  recent: number; // آخر ساعة
  critical: number;
  unread: number;
}

class AdvancedAlertService {
  private alerts: AdvancedSystemAlert[] = [];
  private listeners: ((alerts: AdvancedSystemAlert[]) => void)[] = [];
  private readAlerts: Set<string> = new Set();
  private mutedSources: Set<string> = new Set();
  private mutedCategories: Set<string> = new Set();
  private alertHistory: AdvancedSystemAlert[] = [];
  private maxHistorySize = 1000;
  private duplicateThreshold = 5 * 60 * 1000; // 5 دقائق
  private rateLimitMap: Map<string, number[]> = new Map();
  private maxAlertsPerMinute = 10;

  constructor() {
    this.setupPeriodicCleanup();
    this.setupRateLimiting();
    this.loadSettings();
  }

  /**
   * إضافة تنبيه جديد مع منع التكرار والتحكم في المعدل
   */
  addAlert(alert: Omit<AdvancedSystemAlert, 'id' | 'timestamp' | 'fingerprint'>): string | null {
    // إنشاء بصمة للتنبيه لمنع التكرار
    const fingerprint = this.generateFingerprint(alert);

    // فحص التكرار
    if (this.isDuplicate(fingerprint)) {
      console.debug('Duplicate alert filtered:', alert.title);
      return null;
    }

    // فحص معدل التنبيهات
    if (this.isRateLimited(alert.source)) {
      console.warn('Rate limited alert from source:', alert.source);
      return null;
    }

    // فحص الكتم
    if (this.isMuted(alert.source, alert.category)) {
      console.debug('Muted alert:', alert.title);
      return null;
    }

    const newAlert: AdvancedSystemAlert = {
      ...alert,
      id: this.generateAlertId(),
      timestamp: new Date(),
      fingerprint,
      priority: alert.priority || this.calculatePriority(alert.type),
      autoHide: alert.autoHide !== false, // افتراضي true
      hideAfter: alert.hideAfter || this.getDefaultHideTime(alert.type)
    };

    // إضافة التنبيه
    this.alerts.unshift(newAlert);
    this.alertHistory.unshift({ ...newAlert });

    // تحديد الحد الأقصى للتنبيهات النشطة
    if (this.alerts.length > 100) {
      this.alerts = this.alerts.slice(0, 100);
    }

    // تحديد الحد الأقصى للتاريخ
    if (this.alertHistory.length > this.maxHistorySize) {
      this.alertHistory = this.alertHistory.slice(0, this.maxHistorySize);
    }

    // إشعار المستمعين
    this.notifyListeners();

    // عرض التنبيه
    this.displayAlert(newAlert);

    // تسجيل التنبيه
    this.logAlert(newAlert);

    // جدولة الإخفاء التلقائي
    if (newAlert.autoHide && newAlert.hideAfter) {
      setTimeout(() => {
        this.removeAlert(newAlert.id);
      }, newAlert.hideAfter);
    }

    return newAlert.id;
  }

  /**
   * إزالة تنبيه
   */
  removeAlert(alertId: string): boolean {
    const initialLength = this.alerts.length;
    this.alerts = this.alerts.filter(alert => alert.id !== alertId);

    if (this.alerts.length < initialLength) {
      this.notifyListeners();
      return true;
    }
    return false;
  }

  /**
   * مسح جميع التنبيهات
   */
  clearAllAlerts(): void {
    this.alerts = [];
    this.readAlerts.clear();
    this.notifyListeners();
  }

  /**
   * الحصول على التنبيهات مع الفلترة
   */
  getAlerts(filter?: AlertFilter): AdvancedSystemAlert[] {
    let filtered = [...this.alerts];

    if (filter) {
      if (filter.types) {
        filtered = filtered.filter(alert => filter.types!.includes(alert.type));
      }

      if (filter.sources) {
        filtered = filtered.filter(alert => filter.sources!.includes(alert.source));
      }

      if (filter.categories) {
        filtered = filtered.filter(alert =>
          alert.category && filter.categories!.includes(alert.category)
        );
      }

      if (filter.minPriority) {
        filtered = filtered.filter(alert => alert.priority >= filter.minPriority!);
      }

      if (filter.maxAge) {
        const cutoff = new Date(Date.now() - filter.maxAge * 60 * 1000);
        filtered = filtered.filter(alert => alert.timestamp > cutoff);
      }

      if (filter.persistent !== undefined) {
        filtered = filtered.filter(alert => alert.persistent === filter.persistent);
      }
    }

    return filtered;
  }

  /**
   * الحصول على إحصائيات التنبيهات
   */
  getStats(): AlertStats {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

    const stats: AlertStats = {
      total: this.alerts.length,
      byType: {},
      bySource: {},
      byCategory: {},
      recent: 0,
      critical: 0,
      unread: 0
    };

    this.alerts.forEach(alert => {
      // حسب النوع
      stats.byType[alert.type] = (stats.byType[alert.type] || 0) + 1;

      // حسب المصدر
      stats.bySource[alert.source] = (stats.bySource[alert.source] || 0) + 1;

      // حسب الفئة
      if (alert.category) {
        stats.byCategory[alert.category] = (stats.byCategory[alert.category] || 0) + 1;
      }

      // الحديثة
      if (alert.timestamp > oneHourAgo) {
        stats.recent++;
      }

      // الحرجة
      if (alert.type === 'critical') {
        stats.critical++;
      }

      // غير المقروءة
      if (!this.readAlerts.has(alert.id)) {
        stats.unread++;
      }
    });

    return stats;
  }

  /**
   * تمييز التنبيه كمقروء
   */
  markAsRead(alertId: string): void {
    this.readAlerts.add(alertId);
    this.notifyListeners();
  }

  /**
   * تمييز جميع التنبيهات كمقروءة
   */
  markAllAsRead(): void {
    this.alerts.forEach(alert => this.readAlerts.add(alert.id));
    this.notifyListeners();
  }

  /**
   * كتم مصدر تنبيهات
   */
  muteSource(source: string, duration?: number): void {
    this.mutedSources.add(source);
    this.saveSettings();

    if (duration) {
      setTimeout(() => {
        this.mutedSources.delete(source);
        this.saveSettings();
      }, duration);
    }
  }

  /**
   * إلغاء كتم مصدر
   */
  unmuteSource(source: string): void {
    this.mutedSources.delete(source);
    this.saveSettings();
  }

  /**
   * كتم فئة تنبيهات
   */
  muteCategory(category: string, duration?: number): void {
    this.mutedCategories.add(category);
    this.saveSettings();

    if (duration) {
      setTimeout(() => {
        this.mutedCategories.delete(category);
        this.saveSettings();
      }, duration);
    }
  }

  /**
   * إلغاء كتم فئة
   */
  unmuteCategory(category: string): void {
    this.mutedCategories.delete(category);
    this.saveSettings();
  }

  /**
   * إضافة مستمع للتنبيهات
   */
  addListener(listener: (alerts: AdvancedSystemAlert[]) => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  /**
   * تنبيهات سريعة للأخطاء الشائعة
   */
  createNetworkError(details?: string): string | null {
    return this.addAlert({
      type: 'error',
      title: 'خطأ في الشبكة',
      message: details || 'فشل في الاتصال بالخادم',
      source: 'NETWORK',
      category: 'connectivity',
      priority: 7,
      actions: [
        {
          id: 'retry',
          label: 'إعادة المحاولة',
          action: () => window.location.reload(),
          style: 'primary'
        }
      ]
    });
  }

  createDatabaseError(details?: string): string | null {
    return this.addAlert({
      type: 'critical',
      title: 'خطأ في قاعدة البيانات',
      message: details || 'مشكلة في قاعدة البيانات',
      source: 'DATABASE',
      category: 'system',
      priority: 9,
      persistent: true,
      actions: [
        {
          id: 'refresh',
          label: 'تحديث',
          action: () => window.location.reload(),
          style: 'primary'
        }
      ]
    });
  }

  createSuccessAlert(title: string, message: string): string | null {
    return this.addAlert({
      type: 'success',
      title,
      message,
      source: 'USER_ACTION',
      category: 'feedback',
      priority: 3,
      hideAfter: 3000
    });
  }

  // الدوال الخاصة
  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private generateFingerprint(alert: Omit<AdvancedSystemAlert, 'id' | 'timestamp' | 'fingerprint'>): string {
    return `${alert.type}_${alert.source}_${alert.title}_${alert.category || ''}`;
  }

  private isDuplicate(fingerprint: string): boolean {
    const now = Date.now();
    return this.alerts.some(alert =>
      alert.fingerprint === fingerprint &&
      (now - alert.timestamp.getTime()) < this.duplicateThreshold
    );
  }

  private isRateLimited(source: string): boolean {
    const now = Date.now();
    const oneMinuteAgo = now - 60 * 1000;

    if (!this.rateLimitMap.has(source)) {
      this.rateLimitMap.set(source, []);
    }

    const timestamps = this.rateLimitMap.get(source)!;

    // إزالة الطوابع الزمنية القديمة
    const recentTimestamps = timestamps.filter(ts => ts > oneMinuteAgo);
    this.rateLimitMap.set(source, recentTimestamps);

    // فحص الحد الأقصى
    if (recentTimestamps.length >= this.maxAlertsPerMinute) {
      return true;
    }

    // إضافة الطابع الزمني الحالي
    recentTimestamps.push(now);
    return false;
  }

  private isMuted(source: string, category?: string): boolean {
    return this.mutedSources.has(source) ||
           (!!category && this.mutedCategories.has(category));
  }

  private calculatePriority(type: AdvancedSystemAlert['type']): number {
    switch (type) {
      case 'critical': return 10;
      case 'error': return 7;
      case 'warning': return 5;
      case 'info': return 3;
      case 'success': return 2;
      default: return 1;
    }
  }

  private getDefaultHideTime(type: AdvancedSystemAlert['type']): number {
    switch (type) {
      case 'critical': return 0; // لا يختفي تلقائياً
      case 'error': return 10000; // 10 ثواني
      case 'warning': return 7000; // 7 ثواني
      case 'info': return 5000; // 5 ثواني
      case 'success': return 3000; // 3 ثواني
      default: return 5000;
    }
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener([...this.alerts]));
  }

  private displayAlert(alert: AdvancedSystemAlert): void {
    // عرض التنبيه باستخدام التنبيهات المخصصة مع زر إغلاق
    const duration = alert.autoHide ? (alert.hideAfter || 5000) : Infinity;

    // استخدام التنبيهات المخصصة مع زر إغلاق
    const toastId = customToast[alert.type](
      alert.title,
      alert.message,
      {
        duration,
        onClose: () => {
          // إزالة التنبيه من النظام عند الإغلاق
          this.removeAlert(alert.id);
        }
      }
    );

    // حفظ معرف التنبيه للمرجع
    alert.toastId = toastId;
  }



  private logAlert(alert: AdvancedSystemAlert): void {
    if (alert.type === 'critical' || alert.type === 'error') {
      const logLevel = alert.type === 'critical' ? 'CRITICAL' : 'ERROR';
      const logSource = alert.source === 'SYSTEM' ? 'SYSTEM' : 'FRONTEND';
      errorLogger.logError(logLevel, logSource, `Alert: ${alert.title}`, {
        alertId: alert.id,
        type: alert.type,
        title: alert.title,
        source: alert.source,
        category: alert.category
      });
    }
  }

  private setupPeriodicCleanup(): void {
    // تعطيل التنظيف الدوري لتوفير موارد النظام
    // setInterval(() => {
    //   const now = new Date();
    //   const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    //   // تنظيف التنبيهات القديمة
    //   this.alerts = this.alerts.filter(alert =>
    //     alert.persistent || alert.timestamp > oneDayAgo
    //   );

    //   // تنظيف التاريخ القديم
    //   this.alertHistory = this.alertHistory.filter(alert =>
    //     alert.timestamp > new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    //   );

    //   this.notifyListeners();
    // }, 60 * 60 * 1000); // كل ساعة - معطل لتوفير موارد النظام
  }

  private setupRateLimiting(): void {
    // تعطيل تنظيف معدل التنبيهات الدوري لتوفير موارد النظام
    // setInterval(() => {
    //   // تنظيف خريطة معدل التنبيهات
    //   const oneMinuteAgo = Date.now() - 60 * 1000;
    //   this.rateLimitMap.forEach((timestamps, source) => {
    //     const recent = timestamps.filter(ts => ts > oneMinuteAgo);
    //     if (recent.length === 0) {
    //       this.rateLimitMap.delete(source);
    //     } else {
    //       this.rateLimitMap.set(source, recent);
    //     }
    //   });
    // }, 60 * 1000); // كل دقيقة - معطل لتوفير موارد النظام
  }

  private loadSettings(): void {
    try {
      const settings = localStorage.getItem('alertSettings');
      if (settings) {
        const parsed = JSON.parse(settings);
        this.mutedSources = new Set(parsed.mutedSources || []);
        this.mutedCategories = new Set(parsed.mutedCategories || []);
      }
    } catch (error) {
      console.warn('Failed to load alert settings:', error);
    }
  }

  private saveSettings(): void {
    try {
      const settings = {
        mutedSources: Array.from(this.mutedSources),
        mutedCategories: Array.from(this.mutedCategories)
      };
      localStorage.setItem('alertSettings', JSON.stringify(settings));
    } catch (error) {
      console.warn('Failed to save alert settings:', error);
    }
  }
}

// إنشاء مثيل واحد
export const advancedAlertService = new AdvancedAlertService();

// إتاحة الخدمة في وحدة التحكم للتطوير
if (typeof window !== 'undefined') {
  (window as any).advancedAlertService = advancedAlertService;
}

export default advancedAlertService;
