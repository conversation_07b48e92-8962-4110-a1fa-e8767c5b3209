/**
 * خدمة بصمة الجهاز الموحدة - SmartPOS
 * خدمة مبسطة ومحسنة لإنشاء بصمات ثابتة للأجهزة
 */

interface UnifiedDeviceFingerprint {
  deviceId: string;
  hardwareFingerprint: string;
  storageFingerprint: string;
  systemFingerprint: string;
  timestamp: string;
}

class UnifiedDeviceFingerprintService {
  private static instance: UnifiedDeviceFingerprintService;
  private deviceId: string | null = null;
  private fingerprint: UnifiedDeviceFingerprint | null = null;
  private readonly STORAGE_KEY = 'smartpos_unified_fingerprint';

  private constructor() {}

  public static getInstance(): UnifiedDeviceFingerprintService {
    if (!UnifiedDeviceFingerprintService.instance) {
      UnifiedDeviceFingerprintService.instance = new UnifiedDeviceFingerprintService();
    }
    return UnifiedDeviceFingerprintService.instance;
  }

  /**
   * إنشاء بصمة الأجهزة المحسنة (ثابتة ومبسطة)
   */
  private async generateHardwareFingerprint(): Promise<string> {
    try {
      console.log('🔍 [UNIFIED] بدء إنشاء بصمة الأجهزة الموحدة...');

      // استخدام خصائص ثابتة فقط
      const stableComponents = [
        `cpu:${navigator.hardwareConcurrency || 4}`,
        `memory:${(navigator as any).deviceMemory || 8}`,
        `touch:${navigator.maxTouchPoints || 0}`,
        `platform:${navigator.platform}`,
        `color_depth:${screen.colorDepth}`,
        `pixel_ratio:${Math.round(window.devicePixelRatio || 1)}`
      ];

      // إضافة معلومات WebGL الثابتة فقط
      try {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        if (gl && 'getParameter' in gl && 'RENDERER' in gl) {
          const renderer = (gl as WebGLRenderingContext).getParameter((gl as WebGLRenderingContext).RENDERER);
          if (renderer && typeof renderer === 'string') {
            // استخراج المعلومات الثابتة فقط من renderer
            const stableRenderer = renderer.split(' ')[0] || 'unknown';
            stableComponents.push(`gpu:${stableRenderer}`);
          }
        }
      } catch (webglError) {
        stableComponents.push('gpu:not_available');
      }

      const hardwareString = stableComponents.join('_');
      const hardwareFingerprint = this.hashString(hardwareString);

      console.log('✅ [UNIFIED] تم إنشاء بصمة الأجهزة:', hardwareFingerprint.slice(0, 8) + '...');
      return hardwareFingerprint;
    } catch (error) {
      console.error('❌ [UNIFIED] خطأ في إنشاء بصمة الأجهزة:', error);
      // بصمة احتياطية بسيطة
      const fallbackComponents = [
        `cpu:${navigator.hardwareConcurrency || 4}`,
        `memory:${(navigator as any).deviceMemory || 8}`,
        'hardware_fallback'
      ];
      return this.hashString(fallbackComponents.join('_'));
    }
  }

  /**
   * إنشاء بصمة التخزين المحسنة (ثابتة)
   */
  private async generateStorageFingerprint(): Promise<string> {
    try {
      console.log('🔍 [UNIFIED] بدء إنشاء بصمة التخزين الموحدة...');

      const storageComponents = [];

      // فحص localStorage
      try {
        const testKey = 'smartpos_storage_test';
        localStorage.setItem(testKey, 'test');
        localStorage.removeItem(testKey);
        storageComponents.push('localStorage:available');
      } catch {
        storageComponents.push('localStorage:not_available');
      }

      // فحص sessionStorage
      try {
        const testKey = 'smartpos_session_test';
        sessionStorage.setItem(testKey, 'test');
        sessionStorage.removeItem(testKey);
        storageComponents.push('sessionStorage:available');
      } catch {
        storageComponents.push('sessionStorage:not_available');
      }

      // فحص IndexedDB
      try {
        if ('indexedDB' in window) {
          storageComponents.push('indexedDB:available');
        } else {
          storageComponents.push('indexedDB:not_available');
        }
      } catch {
        storageComponents.push('indexedDB:not_available');
      }

      // إضافة معرف ثابت للنظام
      storageComponents.push('smartpos_unified_storage');

      const storageString = storageComponents.join('_');
      const storageFingerprint = this.hashString(storageString);

      console.log('✅ [UNIFIED] تم إنشاء بصمة التخزين:', storageFingerprint.slice(0, 8) + '...');
      return storageFingerprint;
    } catch (error) {
      console.error('❌ [UNIFIED] خطأ في إنشاء بصمة التخزين:', error);
      return this.hashString('storage_fallback_unified');
    }
  }

  /**
   * كشف بصمة النظام المبسطة
   */
  private detectSystemFingerprint(): string {
    try {
      const userAgent = navigator.userAgent;
      
      if (userAgent.includes('Windows')) {
        return 'Windows_unified';
      } else if (userAgent.includes('Mac')) {
        return 'Mac_unified';
      } else if (userAgent.includes('Linux')) {
        return 'Linux_unified';
      } else if (userAgent.includes('Android')) {
        return 'Android_unified';
      } else if (userAgent.includes('iOS')) {
        return 'iOS_unified';
      } else {
        return 'unknown_unified';
      }
    } catch (error) {
      console.error('❌ [UNIFIED] خطأ في كشف النظام:', error);
      return 'fallback_unified';
    }
  }

  /**
   * إنشاء hash من النص
   */
  private hashString(str: string): string {
    let hash = 0;
    if (str.length === 0) return hash.toString();
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // تحويل إلى 32bit integer
    }
    
    return Math.abs(hash).toString(36);
  }

  /**
   * إنشاء البصمة الموحدة الكاملة
   */
  public async generateUnifiedFingerprint(): Promise<UnifiedDeviceFingerprint> {
    try {
      console.log('🔍 [UNIFIED] إنشاء البصمة الموحدة الكاملة...');

      // التحقق من وجود بصمة محفوظة
      const savedFingerprint = localStorage.getItem(this.STORAGE_KEY);
      if (savedFingerprint) {
        try {
          const parsed = JSON.parse(savedFingerprint);
          console.log('✅ [UNIFIED] تم العثور على بصمة محفوظة:', parsed.deviceId);
          this.fingerprint = parsed;
          return parsed;
        } catch (parseError) {
          console.warn('⚠️ [UNIFIED] فشل في تحليل البصمة المحفوظة، إنشاء جديدة');
        }
      }

      console.log('🔍 [UNIFIED] إنشاء بصمة الأجهزة...');
      const hardwareFingerprint = await this.generateHardwareFingerprint();

      console.log('🔍 [UNIFIED] إنشاء بصمة التخزين...');
      const storageFingerprint = await this.generateStorageFingerprint();

      console.log('🔍 [UNIFIED] كشف بصمة النظام...');
      const systemFingerprint = this.detectSystemFingerprint();

      // إنشاء معرف الجهاز المركب
      console.log('🔍 [UNIFIED] إنشاء معرف الجهاز المركب...');
      const deviceId = this.hashString(`${hardwareFingerprint}_${storageFingerprint}`);

      this.fingerprint = {
        deviceId: `fp_${deviceId}`,
        hardwareFingerprint,
        storageFingerprint,
        systemFingerprint,
        timestamp: new Date().toISOString()
      };

      // حفظ البصمة
      try {
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.fingerprint));
        console.log('✅ [UNIFIED] تم حفظ البصمة الموحدة');
      } catch (saveError) {
        console.warn('⚠️ [UNIFIED] فشل في حفظ البصمة:', saveError);
      }

      console.log('✅ [UNIFIED] تم إنشاء البصمة الموحدة بنجاح:', this.fingerprint.deviceId);
      return this.fingerprint;

    } catch (error) {
      console.error('❌ [UNIFIED] خطأ في إنشاء البصمة الموحدة:', error);

      // إنشاء بصمة احتياطية
      console.log('🔄 [UNIFIED] إنشاء بصمة احتياطية...');
      const fallbackId = this.hashString(`fallback_${Date.now()}_${Math.random()}`);
      
      this.fingerprint = {
        deviceId: `fallback_${fallbackId}`,
        hardwareFingerprint: 'fallback',
        storageFingerprint: 'fallback',
        systemFingerprint: this.detectSystemFingerprint(),
        timestamp: new Date().toISOString()
      };

      console.log('🔄 [UNIFIED] تم إنشاء بصمة احتياطية:', this.fingerprint.deviceId);
      return this.fingerprint;
    }
  }

  /**
   * الحصول على معرف الجهاز
   */
  public async getDeviceId(): Promise<string> {
    if (!this.deviceId) {
      const fingerprint = await this.generateUnifiedFingerprint();
      this.deviceId = fingerprint.deviceId;
    }
    return this.deviceId;
  }

  /**
   * الحصول على البصمة الكاملة
   */
  public async getFingerprint(): Promise<UnifiedDeviceFingerprint> {
    if (!this.fingerprint) {
      this.fingerprint = await this.generateUnifiedFingerprint();
    }
    return this.fingerprint;
  }

  /**
   * إعادة تعيين البصمة
   */
  public reset(): void {
    this.deviceId = null;
    this.fingerprint = null;
    localStorage.removeItem(this.STORAGE_KEY);
    console.log('🔄 [UNIFIED] تم إعادة تعيين البصمة الموحدة');
  }

  /**
   * التحقق من استقرار البصمة
   */
  public async validateFingerprint(): Promise<boolean> {
    try {
      const currentFingerprint = await this.generateUnifiedFingerprint();
      const storedFingerprint = localStorage.getItem(this.STORAGE_KEY);

      if (!storedFingerprint) {
        return true; // البصمة جديدة
      }

      const stored = JSON.parse(storedFingerprint);
      const isValid = (
        stored.hardwareFingerprint === currentFingerprint.hardwareFingerprint &&
        stored.storageFingerprint === currentFingerprint.storageFingerprint
      );

      if (!isValid) {
        console.warn('⚠️ [UNIFIED] تغيرت البصمة الموحدة - قد يكون جهاز مختلف');
      }

      return isValid;
    } catch (error) {
      console.error('❌ [UNIFIED] خطأ في التحقق من البصمة:', error);
      return false;
    }
  }

  /**
   * الحصول على إحصائيات البصمة
   */
  public getStats(): any {
    return {
      hasFingerprint: !!this.fingerprint,
      deviceId: this.deviceId,
      timestamp: this.fingerprint?.timestamp,
      storageAvailable: !!localStorage.getItem(this.STORAGE_KEY)
    };
  }
}

// تصدير instance واحد
export const unifiedDeviceFingerprint = UnifiedDeviceFingerprintService.getInstance();
export default unifiedDeviceFingerprint;
