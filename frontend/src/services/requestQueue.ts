/**
 * نظام إدارة طوابير الطلبات - يمنع تراكم الطلبات والتجميد
 */

interface QueuedRequest {
  id: string;
  url: string;
  method: string;
  data?: any;
  headers?: Record<string, string>;
  timestamp: number;
  retryCount: number;
  priority: 'high' | 'medium' | 'low';
  resolve: (value: any) => void;
  reject: (error: any) => void;
}

interface QueueConfig {
  maxConcurrentRequests: number;
  maxQueueSize: number;
  requestTimeout: number;
  maxRetries: number;
  retryDelay: number;
}

class RequestQueue {
  private static instance: RequestQueue;
  private queue: QueuedRequest[] = [];
  private activeRequests: Set<string> = new Set();
  private config: QueueConfig;
  private isProcessing: boolean = false;

  private constructor() {
    this.config = {
      maxConcurrentRequests: 3, // حد أقصى 3 طلبات متزامنة
      maxQueueSize: 50,
      requestTimeout: 45000,
      maxRetries: 2,
      retryDelay: 1000
    };
  }

  public static getInstance(): RequestQueue {
    if (!RequestQueue.instance) {
      RequestQueue.instance = new RequestQueue();
    }
    return RequestQueue.instance;
  }

  /**
   * إضافة طلب إلى الطابور
   */
  public async enqueue(
    url: string,
    method: string = 'GET',
    data?: any,
    headers?: Record<string, string>,
    priority: 'high' | 'medium' | 'low' = 'medium'
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      // فحص حجم الطابور
      if (this.queue.length >= this.config.maxQueueSize) {
        // إزالة الطلبات القديمة ذات الأولوية المنخفضة
        this.cleanupOldRequests();
        
        if (this.queue.length >= this.config.maxQueueSize) {
          reject(new Error('Request queue is full'));
          return;
        }
      }

      const request: QueuedRequest = {
        id: this.generateRequestId(),
        url,
        method,
        data,
        headers,
        timestamp: Date.now(),
        retryCount: 0,
        priority,
        resolve,
        reject
      };

      // إدراج الطلب حسب الأولوية
      this.insertByPriority(request);
      
      // بدء معالجة الطابور
      this.processQueue();
    });
  }

  /**
   * معالجة الطابور
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.queue.length === 0) {
      return;
    }

    this.isProcessing = true;

    while (this.queue.length > 0 && this.activeRequests.size < this.config.maxConcurrentRequests) {
      const request = this.queue.shift();
      if (request) {
        this.executeRequest(request);
      }
    }

    this.isProcessing = false;
  }

  /**
   * تنفيذ طلب واحد
   */
  private async executeRequest(request: QueuedRequest): Promise<void> {
    this.activeRequests.add(request.id);

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.config.requestTimeout);

      const response = await fetch(request.url, {
        method: request.method,
        headers: {
          'Content-Type': 'application/json',
          ...request.headers
        },
        body: request.data ? JSON.stringify(request.data) : undefined,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const data = await response.json().catch(() => ({}));
        request.resolve(data);
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

    } catch (error) {
      console.error(`Request failed for ${request.url}:`, error);

      // إعادة المحاولة إذا لم نصل للحد الأقصى
      if (request.retryCount < this.config.maxRetries) {
        request.retryCount++;
        console.log(`Retrying request ${request.id} (attempt ${request.retryCount})`);
        
        // إعادة إدراج الطلب في الطابور بعد تأخير
        setTimeout(() => {
          this.insertByPriority(request);
          this.processQueue();
        }, this.config.retryDelay * request.retryCount);
      } else {
        request.reject(error);
      }
    } finally {
      this.activeRequests.delete(request.id);
      
      // متابعة معالجة الطابور
      setTimeout(() => this.processQueue(), 100);
    }
  }

  /**
   * إدراج الطلب حسب الأولوية
   */
  private insertByPriority(request: QueuedRequest): void {
    const priorityOrder = { high: 0, medium: 1, low: 2 };
    
    let insertIndex = this.queue.length;
    for (let i = 0; i < this.queue.length; i++) {
      if (priorityOrder[request.priority] < priorityOrder[this.queue[i].priority]) {
        insertIndex = i;
        break;
      }
    }
    
    this.queue.splice(insertIndex, 0, request);
  }

  /**
   * تنظيف الطلبات القديمة
   */
  private cleanupOldRequests(): void {
    const now = Date.now();
    const maxAge = 60000; // دقيقة واحدة

    this.queue = this.queue.filter(request => {
      const isOld = (now - request.timestamp) > maxAge;
      const isLowPriority = request.priority === 'low';
      
      if (isOld && isLowPriority) {
        request.reject(new Error('Request expired'));
        return false;
      }
      return true;
    });
  }

  /**
   * توليد معرف فريد للطلب
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * الحصول على إحصائيات الطابور
   */
  public getStats(): {
    queueLength: number;
    activeRequests: number;
    isProcessing: boolean;
  } {
    return {
      queueLength: this.queue.length,
      activeRequests: this.activeRequests.size,
      isProcessing: this.isProcessing
    };
  }

  /**
   * مسح الطابور
   */
  public clear(): void {
    this.queue.forEach(request => {
      request.reject(new Error('Queue cleared'));
    });
    this.queue = [];
    this.activeRequests.clear();
    this.isProcessing = false;
  }

  /**
   * تحديث إعدادات الطابور
   */
  public updateConfig(newConfig: Partial<QueueConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

export const requestQueue = RequestQueue.getInstance();

/**
 * دالة مساعدة لإرسال الطلبات عبر الطابور
 */
export async function queuedRequest(
  url: string,
  method: string = 'GET',
  data?: any,
  headers?: Record<string, string>,
  priority: 'high' | 'medium' | 'low' = 'medium'
): Promise<any> {
  return requestQueue.enqueue(url, method, data, headers, priority);
}
