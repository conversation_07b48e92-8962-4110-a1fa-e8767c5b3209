/**
 * خدمة حركات المستودعات
 * تطبق مبادئ البرمجة الكائنية مع معالجة شاملة للأخطاء
 */

import axios, { AxiosResponse } from 'axios';

// Types
export type MovementType = 'IN' | 'OUT' | 'TRANSFER' | 'ADJUSTMENT';
export type ReferenceType = 'PURCHASE' | 'SALE' | 'TRANSFER' | 'ADJUSTMENT' | 'RETURN';

export interface WarehouseMovement {
  id: number;
  movement_type: MovementType;
  direction?: 'in' | 'out';
  from_warehouse_id?: number;
  to_warehouse_id?: number;
  product_id: number;
  product_name?: string;
  product_barcode?: string;
  quantity: number;
  unit_cost?: number;
  total_cost?: number;
  reference_type?: ReferenceType;
  reference_id?: number;
  notes?: string;
  created_by?: number;
  created_by_username?: string;
  created_at?: string;
}

export interface MovementCreate {
  movement_type: MovementType;
  from_warehouse_id?: number;
  to_warehouse_id?: number;
  product_id: number;
  quantity: number;
  unit_cost?: number;
  total_cost?: number;
  reference_type?: ReferenceType;
  reference_id?: number;
  notes?: string;
}

export interface MovementFilters {
  movement_type?: MovementType;
  product_id?: number;
  date_from?: string;
  date_to?: string;
  reference_type?: ReferenceType;
  page?: number;
  per_page?: number;
}

export interface StockAdjustment {
  warehouse_id: number;
  product_id: number;
  new_quantity: number;
  reason: string;
}

export interface MovementSummary {
  warehouse_id: number;
  date_range: {
    from: string;
    to: string;
  };
  movements_in: {
    count: number;
    total_quantity: number;
    total_value: number;
  };
  movements_out: {
    count: number;
    total_quantity: number;
    total_value: number;
  };
  net_movement: {
    quantity: number;
    value: number;
  };
  by_movement_type: Record<MovementType, {
    count: number;
    total_quantity: number;
    total_value: number;
  }>;
}

export interface ApiResponse<T> {
  success: boolean;
  error?: string;
  message?: string;
  movement?: T;
  movements?: T[];
  warehouse?: any;
  product?: any;
  adjustment?: any;
  summary?: MovementSummary;
  pagination?: {
    page: number;
    per_page: number;
    total_count: number;
    total_pages: number;
  };
  total_count?: number;
}

/**
 * خدمة حركات المستودعات
 * تطبق مبادئ البرمجة الكائنية مع نمط Singleton
 */
export class WarehouseMovementService {
  private static instance: WarehouseMovementService;
  private readonly baseURL: string;

  private constructor() {
    this.baseURL = '/api/warehouse-movements';
  }

  /**
   * الحصول على instance وحيد من الخدمة
   */
  public static getInstance(): WarehouseMovementService {
    if (!WarehouseMovementService.instance) {
      WarehouseMovementService.instance = new WarehouseMovementService();
    }
    return WarehouseMovementService.instance;
  }

  /**
   * تسجيل حركة مستودع
   */
  public async recordMovement(movementData: MovementCreate): Promise<ApiResponse<WarehouseMovement>> {
    try {
      const response: AxiosResponse<ApiResponse<WarehouseMovement>> = await axios.post(
        this.baseURL,
        movementData
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في تسجيل حركة المستودع:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في تسجيل حركة المستودع'
      };
    }
  }

  /**
   * الحصول على حركات المستودع
   */
  public async getWarehouseMovements(
    warehouseId: number,
    filters?: MovementFilters
  ): Promise<ApiResponse<WarehouseMovement[]>> {
    try {
      const params = new URLSearchParams();
      
      if (filters) {
        if (filters.movement_type) params.append('movement_type', filters.movement_type);
        if (filters.product_id) params.append('product_id', filters.product_id.toString());
        if (filters.date_from) params.append('date_from', filters.date_from);
        if (filters.date_to) params.append('date_to', filters.date_to);
        if (filters.reference_type) params.append('reference_type', filters.reference_type);
        if (filters.page) params.append('page', filters.page.toString());
        if (filters.per_page) params.append('per_page', filters.per_page.toString());
      }

      const url = `${this.baseURL}/warehouse/${warehouseId}${params.toString() ? '?' + params.toString() : ''}`;
      const response: AxiosResponse<ApiResponse<WarehouseMovement[]>> = await axios.get(url);
      return response.data;
    } catch (error: any) {
      console.error('خطأ في جلب حركات المستودع:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في جلب حركات المستودع'
      };
    }
  }

  /**
   * الحصول على تاريخ حركات المنتج
   */
  public async getProductMovementHistory(
    productId: number,
    filters?: Omit<MovementFilters, 'product_id'>
  ): Promise<ApiResponse<WarehouseMovement[]>> {
    try {
      const params = new URLSearchParams();
      
      if (filters) {
        if (filters.movement_type) params.append('movement_type', filters.movement_type);
        if (filters.date_from) params.append('date_from', filters.date_from);
        if (filters.date_to) params.append('date_to', filters.date_to);
      }

      const url = `${this.baseURL}/product/${productId}/history${params.toString() ? '?' + params.toString() : ''}`;
      const response: AxiosResponse<ApiResponse<WarehouseMovement[]>> = await axios.get(url);
      return response.data;
    } catch (error: any) {
      console.error('خطأ في جلب تاريخ حركات المنتج:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في جلب تاريخ حركات المنتج'
      };
    }
  }

  /**
   * معالجة تعديل المخزون
   */
  public async processStockAdjustment(adjustmentData: StockAdjustment): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await axios.post(
        `${this.baseURL}/adjustment`,
        adjustmentData
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في تعديل المخزون:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في تعديل المخزون'
      };
    }
  }

  /**
   * الحصول على ملخص حركات المستودع
   */
  public async getMovementSummary(
    warehouseId: number,
    dateFrom?: string,
    dateTo?: string
  ): Promise<ApiResponse<MovementSummary>> {
    try {
      const params = new URLSearchParams();
      if (dateFrom) params.append('date_from', dateFrom);
      if (dateTo) params.append('date_to', dateTo);

      const url = `${this.baseURL}/warehouse/${warehouseId}/summary${params.toString() ? '?' + params.toString() : ''}`;
      const response: AxiosResponse<ApiResponse<MovementSummary>> = await axios.get(url);
      return response.data;
    } catch (error: any) {
      console.error('خطأ في جلب ملخص الحركات:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في جلب ملخص الحركات'
      };
    }
  }

  /**
   * التحقق من صحة بيانات الحركة
   */
  public validateMovementData(data: MovementCreate): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // التحقق من نوع الحركة
    if (!data.movement_type) {
      errors.push('نوع الحركة مطلوب');
    }

    // التحقق من معرف المنتج
    if (!data.product_id || data.product_id <= 0) {
      errors.push('معرف المنتج مطلوب ويجب أن يكون أكبر من صفر');
    }

    // التحقق من الكمية
    if (!data.quantity || data.quantity <= 0) {
      errors.push('الكمية مطلوبة ويجب أن تكون أكبر من صفر');
    }

    // التحقق من المستودعات حسب نوع الحركة
    switch (data.movement_type) {
      case 'IN':
        if (!data.to_warehouse_id) {
          errors.push('مستودع الوجهة مطلوب لحركة الدخول');
        }
        break;
      case 'OUT':
        if (!data.from_warehouse_id) {
          errors.push('مستودع المصدر مطلوب لحركة الخروج');
        }
        break;
      case 'TRANSFER':
        if (!data.from_warehouse_id || !data.to_warehouse_id) {
          errors.push('مستودع المصدر والوجهة مطلوبان لحركة التحويل');
        } else if (data.from_warehouse_id === data.to_warehouse_id) {
          errors.push('لا يمكن التحويل من وإلى نفس المستودع');
        }
        break;
    }

    // التحقق من التكلفة
    if (data.unit_cost !== undefined && data.unit_cost < 0) {
      errors.push('تكلفة الوحدة يجب أن تكون أكبر من أو تساوي صفر');
    }

    if (data.total_cost !== undefined && data.total_cost < 0) {
      errors.push('التكلفة الإجمالية يجب أن تكون أكبر من أو تساوي صفر');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * التحقق من صحة بيانات تعديل المخزون
   */
  public validateAdjustmentData(data: StockAdjustment): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!data.warehouse_id || data.warehouse_id <= 0) {
      errors.push('معرف المستودع مطلوب');
    }

    if (!data.product_id || data.product_id <= 0) {
      errors.push('معرف المنتج مطلوب');
    }

    if (data.new_quantity < 0) {
      errors.push('الكمية الجديدة يجب أن تكون أكبر من أو تساوي صفر');
    }

    if (!data.reason || data.reason.trim().length === 0) {
      errors.push('سبب التعديل مطلوب');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * تنسيق بيانات الحركة للعرض
   */
  public formatMovementForDisplay(movement: WarehouseMovement): any {
    return {
      ...movement,
      movement_type_text: this.getMovementTypeText(movement.movement_type),
      movement_type_color: this.getMovementTypeColor(movement.movement_type),
      direction_text: movement.direction === 'in' ? 'دخول' : 'خروج',
      direction_color: movement.direction === 'in' ? 'green' : 'red',
      formatted_date: movement.created_at ? new Date(movement.created_at).toLocaleDateString('ar-EG') : '',
      formatted_time: movement.created_at ? new Date(movement.created_at).toLocaleTimeString('ar-EG') : '',
      total_value: movement.total_cost || (movement.unit_cost ? movement.unit_cost * movement.quantity : 0)
    };
  }

  /**
   * الحصول على نص نوع الحركة
   */
  private getMovementTypeText(type: MovementType): string {
    switch (type) {
      case 'IN': return 'دخول';
      case 'OUT': return 'خروج';
      case 'TRANSFER': return 'تحويل';
      case 'ADJUSTMENT': return 'تعديل';
      default: return 'غير محدد';
    }
  }

  /**
   * الحصول على لون نوع الحركة
   */
  private getMovementTypeColor(type: MovementType): string {
    switch (type) {
      case 'IN': return 'green';
      case 'OUT': return 'red';
      case 'TRANSFER': return 'blue';
      case 'ADJUSTMENT': return 'orange';
      default: return 'gray';
    }
  }

  /**
   * حساب إحصائيات الحركات
   */
  public calculateMovementStats(movements: WarehouseMovement[]): any {
    const stats = {
      total_movements: movements.length,
      movements_in: movements.filter(m => m.direction === 'in').length,
      movements_out: movements.filter(m => m.direction === 'out').length,
      total_quantity_in: movements.filter(m => m.direction === 'in').reduce((sum, m) => sum + m.quantity, 0),
      total_quantity_out: movements.filter(m => m.direction === 'out').reduce((sum, m) => sum + m.quantity, 0),
      total_value: movements.reduce((sum, m) => sum + (m.total_cost || 0), 0),
      by_type: {} as Record<MovementType, number>
    };

    // إحصائيات حسب النوع
    ['IN', 'OUT', 'TRANSFER', 'ADJUSTMENT'].forEach(type => {
      stats.by_type[type as MovementType] = movements.filter(m => m.movement_type === type).length;
    });

    return stats;
  }
}

// تصدير instance وحيد
export const warehouseMovementService = WarehouseMovementService.getInstance();
