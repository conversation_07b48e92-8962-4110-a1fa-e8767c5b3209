/**
 * خدمة API للمحادثة الفورية
 * تدير جميع طلبات HTTP المتعلقة بالمحادثة
 */

import api from '../lib/axios';

export interface ChatMessage {
  id: number;
  sender_id: number;
  receiver_id: number;
  content: string;
  message_type: string;
  status: string;
  is_edited: boolean;
  created_at: string;
  delivered_at?: string;
  read_at?: string;
  sender_username?: string;
  sender_full_name?: string;
}

export interface Conversation {
  user_id: number;
  username: string;
  full_name?: string;
  is_online: boolean;
  last_seen?: string;
  last_message?: ChatMessage;
  unread_count: number;
}

export interface ConversationsListResponse {
  conversations: Conversation[];
  total_count: number;
  page: number;
  limit: number;
  has_more: boolean;
  total_pages: number;
}

export interface UserOnlineStatus {
  user_id: number;
  username: string;
  full_name?: string;
  is_online: boolean;
  last_seen?: string;
}

export interface MessageCreateRequest {
  receiver_id: number;
  content: string;
  message_type?: string;
}

export interface MessagesListResponse {
  messages: ChatMessage[];
  total_count: number;
  has_more: boolean;
  page: number;
  limit: number;
}

class ChatApiService {
  private baseUrl = '/api/chat';

  /**
   * إرسال رسالة جديدة
   */
  async sendMessage(messageData: MessageCreateRequest): Promise<ChatMessage> {
    try {
      const response = await api.post(`${this.baseUrl}/send`, messageData);
      return response.data;
    } catch (error) {
      console.error('خطأ في إرسال الرسالة:', error);
      throw error;
    }
  }

  /**
   * جلب الرسائل بين المستخدم الحالي ومستخدم آخر
   */
  async getMessages(
    otherUserId: number,
    page: number = 1,
    limit: number = 20,  // تقليل الحد الافتراضي
    beforeMessageId?: number,
    loadDirection: 'older' | 'newer' = 'newer'
  ): Promise<MessagesListResponse> {
    try {
      const params: any = { page, limit, load_direction: loadDirection };
      if (beforeMessageId) {
        params.before_message_id = beforeMessageId;
      }

      const response = await api.get(`${this.baseUrl}/messages/${otherUserId}`, {
        params
      });
      return response.data;
    } catch (error) {
      console.error('خطأ في جلب الرسائل:', error);
      throw error;
    }
  }

  /**
   * تحديد الرسائل كمقروءة
   */
  async markMessagesAsRead(
    otherUserId: number,
    messageIds?: number[]
  ): Promise<{ success: boolean; updated_count: number; message: string }> {
    try {
      const response = await api.post(`${this.baseUrl}/mark-as-read`, {
        other_user_id: otherUserId,
        message_ids: messageIds
      });
      return response.data;
    } catch (error) {
      console.error('خطأ في تحديد الرسائل كمقروءة:', error);
      throw error;
    }
  }

  /**
   * جلب قائمة المحادثات مع دعم التحميل التدريجي
   */
  async getConversations(
    page: number = 1,
    limit: number = 20
  ): Promise<ConversationsListResponse> {
    try {
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('limit', limit.toString());

      const response = await api.get(`${this.baseUrl}/conversations?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('خطأ في جلب المحادثات:', error);
      throw error;
    }
  }

  /**
   * جلب قائمة المحادثات (النسخة القديمة للتوافق)
   */
  async getAllConversations(): Promise<Conversation[]> {
    try {
      const response = await this.getConversations(1, 1000); // جلب عدد كبير
      return response.conversations;
    } catch (error) {
      console.error('خطأ في جلب جميع المحادثات:', error);
      throw error;
    }
  }

  /**
   * جلب عدد الرسائل غير المقروءة
   */
  async getUnreadCount(): Promise<{ unread_count: number }> {
    try {
      const response = await api.get(`${this.baseUrl}/unread-count`);
      return response.data;
    } catch (error) {
      console.error('خطأ في جلب عدد الرسائل غير المقروءة:', error);
      throw error;
    }
  }

  /**
   * البحث في الرسائل
   */
  async searchMessages(
    query: string,
    limit: number = 20
  ): Promise<{ messages: ChatMessage[]; query: string; count: number }> {
    try {
      const response = await api.get(`${this.baseUrl}/search`, {
        params: { query, limit }
      });
      return response.data;
    } catch (error) {
      console.error('خطأ في البحث في الرسائل:', error);
      throw error;
    }
  }

  /**
   * البحث عن المستخدمين
   */
  async searchUsers(
    query: string,
    limit: number = 10
  ): Promise<{ users: UserOnlineStatus[]; total_count: number }> {
    try {
      const response = await api.get(`${this.baseUrl}/users/search`, {
        params: { query, limit }
      });
      return response.data;
    } catch (error) {
      console.error('خطأ في البحث عن المستخدمين:', error);
      throw error;
    }
  }

  /**
   * جلب المستخدمين المتصلين
   */
  async getOnlineUsers(): Promise<UserOnlineStatus[]> {
    try {
      const response = await api.get(`${this.baseUrl}/users/online`);
      return response.data;
    } catch (error) {
      console.error('خطأ في جلب المستخدمين المتصلين:', error);
      throw error;
    }
  }

  /**
   * جلب جميع المستخدمين النشطين في النظام
   */
  async getAllUsers(): Promise<UserOnlineStatus[]> {
    try {
      const response = await api.get(`${this.baseUrl}/users/all`);
      return response.data;
    } catch (error) {
      console.error('خطأ في جلب جميع المستخدمين:', error);
      throw error;
    }
  }

  /**
   * جلب حالة نظام المحادثة
   */
  async getChatStatus(): Promise<{
    user_id: number;
    username: string;
    is_online: boolean;
    total_online_users: number;
    online_users: number[];
  }> {
    try {
      const response = await api.get(`${this.baseUrl}/status`);
      return response.data;
    } catch (error) {
      console.error('خطأ في جلب حالة المحادثة:', error);
      throw error;
    }
  }

  /**
   * حذف رسالة
   */
  async deleteMessage(messageId: number): Promise<{ success: boolean; message: string }> {
    try {
      const response = await api.delete(`${this.baseUrl}/messages/${messageId}`);
      return response.data;
    } catch (error) {
      console.error('خطأ في حذف الرسالة:', error);
      throw error;
    }
  }

  /**
   * تعديل رسالة
   */
  async editMessage(
    messageId: number,
    newContent: string
  ): Promise<{
    success: boolean;
    message: string;
    updated_message: {
      id: number;
      content: string;
      is_edited: boolean;
      edited_at: string;
    };
  }> {
    try {
      const response = await api.put(`${this.baseUrl}/messages/${messageId}`, null, {
        params: { new_content: newContent }
      });
      return response.data;
    } catch (error) {
      console.error('خطأ في تعديل الرسالة:', error);
      throw error;
    }
  }

  /**
   * حذف المحادثة بالكامل مع مستخدم محدد
   */
  async deleteConversation(userId: number): Promise<{
    success: boolean;
    message: string;
    deleted_messages_count: number;
  }> {
    try {
      const response = await api.delete(`${this.baseUrl}/conversations/${userId}`);
      return response.data;
    } catch (error) {
      console.error('خطأ في حذف المحادثة:', error);
      throw error;
    }
  }

  /**
   * جلب المزيد من الرسائل (للتصفح)
   */
  async loadMoreMessages(
    otherUserId: number,
    beforeMessageId: number,
    limit: number = 50
  ): Promise<MessagesListResponse> {
    return this.getMessages(otherUserId, 1, limit, beforeMessageId);
  }

  /**
   * تحديث حالة الرسائل بشكل دوري
   */
  async refreshConversations(): Promise<Conversation[]> {
    const response = await this.getConversations();
    return response.conversations;
  }

  /**
   * فحص الرسائل الجديدة
   */
  async checkNewMessages(): Promise<{ unread_count: number }> {
    return this.getUnreadCount();
  }
}

// إنشاء مثيل واحد من الخدمة
export const chatApiService = new ChatApiService();
export default chatApiService;
