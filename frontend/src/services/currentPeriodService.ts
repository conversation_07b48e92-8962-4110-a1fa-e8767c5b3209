/**
 * خدمة جلب بيانات الفترة الحالية للمبيعات
 * هذا الملف مختص بجلب وتنظيم بيانات المبيعات للفترة الحالية فقط
 * يتعامل مع التاريخ والوقت بدقة عالية لضمان عدم تعارض البيانات
 */

import api from '../lib/axios';

export interface SalesTrendData {
  date: string;
  amount: number;
}

export interface CurrentPeriodResponse {
  data: SalesTrendData[];
  period: string;
  total: number;
}

/**
 * خدمة الفترة الحالية
 * تتعامل مع جلب وتنظيم بيانات المبيعات للفترة الحالية بدقة
 */
export class CurrentPeriodService {
  private static instance: CurrentPeriodService;

  /**
   * الحصول على مثيل واحد من الخدمة (Singleton Pattern)
   */
  public static getInstance(): CurrentPeriodService {
    if (!CurrentPeriodService.instance) {
      CurrentPeriodService.instance = new CurrentPeriodService();
    }
    return CurrentPeriodService.instance;
  }

  /**
   * جلب بيانات مبيعات اليوم الحالي (24 ساعة كاملة)
   *
   * @returns قائمة بيانات المبيعات لكل ساعة في اليوم الحالي (من 00:00 إلى 23:59)
   */
  async getCurrentDaySales(): Promise<SalesTrendData[]> {
    try {
      console.log('جلب بيانات مبيعات اليوم الحالي (24 ساعة كاملة)');

      const response = await api.get('/api/dashboard/sales-trends/day', {
        params: { previous: false }
      });

      console.log('تم جلب بيانات اليوم الحالي بنجاح (24 ساعة كاملة):', response.data);
      return response.data;
    } catch (error) {
      console.error('خطأ في جلب بيانات اليوم الحالي:', error);
      // إرجاع بيانات فارغة في حالة الخطأ (24 ساعة)
      return Array.from({ length: 24 }, (_, hour) => ({
        date: `${hour.toString().padStart(2, '0')}:00`,
        amount: 0
      }));
    }
  }

  /**
   * جلب بيانات مبيعات الأسبوع الحالي (7 أيام)
   *
   * @returns قائمة بيانات المبيعات لكل يوم في الأسبوع الحالي
   */
  async getCurrentWeekSales(): Promise<SalesTrendData[]> {
    try {
      console.log('جلب بيانات مبيعات الأسبوع الحالي');

      const response = await api.get('/api/dashboard/sales-trends/week', {
        params: { previous: false }
      });

      console.log('تم جلب بيانات الأسبوع الحالي بنجاح:', response.data);
      return response.data;
    } catch (error) {
      console.error('خطأ في جلب بيانات الأسبوع الحالي:', error);
      // إرجاع بيانات فارغة في حالة الخطأ
      const today = new Date();
      const weekData: SalesTrendData[] = [];
      for (let i = 6; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(today.getDate() - i);
        weekData.push({
          date: date.toISOString().split('T')[0],
          amount: 0
        });
      }
      return weekData;
    }
  }

  /**
   * جلب بيانات مبيعات الشهر الحالي (30 يوم)
   *
   * @returns قائمة بيانات المبيعات لكل يوم في الشهر الحالي
   */
  async getCurrentMonthSales(): Promise<SalesTrendData[]> {
    try {
      console.log('جلب بيانات مبيعات الشهر الحالي');

      const response = await api.get('/api/dashboard/sales-trends/month', {
        params: { previous: false }
      });

      console.log('تم جلب بيانات الشهر الحالي بنجاح:', response.data);
      return response.data;
    } catch (error) {
      console.error('خطأ في جلب بيانات الشهر الحالي:', error);
      // إرجاع بيانات فارغة في حالة الخطأ
      const today = new Date();
      const monthData: SalesTrendData[] = [];
      for (let i = 29; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(today.getDate() - i);
        monthData.push({
          date: date.toISOString().split('T')[0],
          amount: 0
        });
      }
      return monthData;
    }
  }

  /**
   * جلب بيانات مبيعات السنة الحالية (12 شهر)
   *
   * @returns قائمة بيانات المبيعات لكل شهر في السنة الحالية
   */
  async getCurrentYearSales(): Promise<SalesTrendData[]> {
    try {
      console.log('جلب بيانات مبيعات السنة الحالية');

      const response = await api.get('/api/dashboard/sales-trends/year', {
        params: { previous: false }
      });

      console.log('تم جلب بيانات السنة الحالية بنجاح:', response.data);
      return response.data;
    } catch (error) {
      console.error('خطأ في جلب بيانات السنة الحالية:', error);
      // إرجاع بيانات فارغة في حالة الخطأ
      const today = new Date();
      const yearData: SalesTrendData[] = [];
      for (let i = 11; i >= 0; i--) {
        const date = new Date(today);
        date.setMonth(today.getMonth() - i);
        yearData.push({
          date: `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`,
          amount: 0
        });
      }
      return yearData;
    }
  }

  /**
   * جلب بيانات المبيعات حسب الفترة المحددة
   *
   * @param period نوع الفترة (day, week, month, year)
   * @returns قائمة بيانات المبيعات للفترة المحددة
   */
  async getSalesByPeriod(period: 'day' | 'week' | 'month' | 'year'): Promise<SalesTrendData[]> {
    console.log(`جلب بيانات الفترة الحالية: ${period}`);

    switch (period) {
      case 'day':
        return this.getCurrentDaySales();
      case 'week':
        return this.getCurrentWeekSales();
      case 'month':
        return this.getCurrentMonthSales();
      case 'year':
        return this.getCurrentYearSales();
      default:
        console.error(`نوع فترة غير مدعوم: ${period}`);
        return [];
    }
  }

  /**
   * جلب إجمالي المبيعات للفترة الحالية
   *
   * @param period نوع الفترة (day, week, month, year)
   * @returns إجمالي المبيعات للفترة الحالية
   */
  async getCurrentPeriodTotal(period: 'day' | 'week' | 'month' | 'year'): Promise<number> {
    try {
      console.log(`جلب إجمالي الفترة الحالية: ${period}`);

      const salesData = await this.getSalesByPeriod(period);
      const total = salesData.reduce((sum, item) => sum + item.amount, 0);

      console.log(`إجمالي الفترة الحالية ${period}: ${total}`);
      return total;
    } catch (error) {
      console.error('خطأ في جلب إجمالي الفترة الحالية:', error);
      return 0;
    }
  }

  /**
   * تحديث البيانات وإعادة جلبها
   *
   * @param period نوع الفترة المراد تحديثها
   * @returns البيانات المحدثة
   */
  async refreshPeriodData(period: 'day' | 'week' | 'month' | 'year'): Promise<SalesTrendData[]> {
    console.log(`تحديث بيانات الفترة الحالية: ${period}`);

    // إعادة جلب البيانات من الخادم
    return this.getSalesByPeriod(period);
  }

  /**
   * التحقق من صحة البيانات
   *
   * @param data البيانات المراد التحقق منها
   * @returns true إذا كانت البيانات صحيحة
   */
  validateSalesData(data: SalesTrendData[]): boolean {
    if (!Array.isArray(data)) {
      console.error('البيانات ليست مصفوفة صحيحة');
      return false;
    }

    for (const item of data) {
      if (!item.date || typeof item.amount !== 'number') {
        console.error('عنصر بيانات غير صحيح:', item);
        return false;
      }
    }

    return true;
  }

  /**
   * تنسيق البيانات للعرض في المخططات
   *
   * @param data البيانات الخام
   * @param period نوع الفترة
   * @returns البيانات المنسقة
   */
  formatDataForChart(data: SalesTrendData[]): SalesTrendData[] {
    if (!this.validateSalesData(data)) {
      console.warn('بيانات غير صحيحة، إرجاع بيانات فارغة');
      return [];
    }

    // تنسيق التواريخ حسب نوع الفترة
    return data.map(item => ({
      ...item,
      amount: Math.round(item.amount * 100) / 100 // تقريب إلى منزلتين عشريتين
    }));
  }
}

// تصدير مثيل واحد من الخدمة
export const currentPeriodService = CurrentPeriodService.getInstance();
