/**
 * خدمة تحسين الأداء للواجهة الأمامية
 * تحتوي على تحسينات للتعامل مع البيانات الكبيرة
 */

// دالة debounce بسيطة محلية
const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

// تكوين التحسينات
export const PERFORMANCE_CONFIG = {
  // إعدادات pagination
  DEFAULT_PAGE_SIZE: 50,
  MAX_PAGE_SIZE: 200,
  VIRTUAL_LIST_THRESHOLD: 100,
  
  // إعدادات debouncing
  SEARCH_DEBOUNCE_MS: 300,
  FILTER_DEBOUNCE_MS: 500,
  RESIZE_DEBOUNCE_MS: 100,
  
  // إعدادات التخزين المؤقت
  CACHE_TTL_MS: 5 * 60 * 1000, // 5 دقائق
  MAX_CACHE_SIZE: 100,
  
  // إعدادات lazy loading
  INTERSECTION_THRESHOLD: 0.1,
  ROOT_MARGIN: '50px',
  
  // إعدادات الذاكرة
  MEMORY_CLEANUP_INTERVAL: 30000, // 30 ثانية
  MAX_MEMORY_USAGE_MB: 100,
};

/**
 * مدير الذاكرة والأداء
 */
export class PerformanceManager {
  private static instance: PerformanceManager;
  private memoryUsage: number = 0;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private performanceObserver: PerformanceObserver | null = null;
  
  private constructor() {
    this.initializeMemoryMonitoring();
    this.initializePerformanceMonitoring();
  }
  
  static getInstance(): PerformanceManager {
    if (!PerformanceManager.instance) {
      PerformanceManager.instance = new PerformanceManager();
    }
    return PerformanceManager.instance;
  }
  
  /**
   * تهيئة مراقبة الذاكرة
   */
  private initializeMemoryMonitoring(): void {
    this.cleanupInterval = setInterval(() => {
      this.checkMemoryUsage();
      this.performGarbageCollection();
    }, PERFORMANCE_CONFIG.MEMORY_CLEANUP_INTERVAL);
  }
  
  /**
   * تهيئة مراقبة الأداء
   */
  private initializePerformanceMonitoring(): void {
    if ('PerformanceObserver' in window) {
      this.performanceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'measure') {
            console.log(`Performance: ${entry.name} took ${entry.duration}ms`);
          }
        });
      });
      
      this.performanceObserver.observe({ entryTypes: ['measure', 'navigation'] });
    }
  }
  
  /**
   * فحص استخدام الذاكرة
   */
  private checkMemoryUsage(): void {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.memoryUsage = memory.usedJSHeapSize / (1024 * 1024); // MB
      
      if (this.memoryUsage > PERFORMANCE_CONFIG.MAX_MEMORY_USAGE_MB) {
        console.warn(`High memory usage detected: ${this.memoryUsage.toFixed(2)}MB`);
        this.performGarbageCollection();
      }
    }
  }
  
  /**
   * تنظيف الذاكرة
   */
  private performGarbageCollection(): void {
    // تنظيف المتغيرات غير المستخدمة
    if (window.gc) {
      window.gc();
    }
    
    // تنظيف event listeners غير المستخدمة
    this.cleanupEventListeners();
  }
  
  /**
   * تنظيف event listeners
   */
  private cleanupEventListeners(): void {
    // إزالة event listeners المؤقتة
    const elements = document.querySelectorAll('[data-temp-listener]');
    elements.forEach(element => {
      element.removeAttribute('data-temp-listener');
    });
  }
  
  /**
   * قياس أداء دالة
   */
  measurePerformance<T>(name: string, fn: () => T): T {
    performance.mark(`${name}-start`);
    const result = fn();
    performance.mark(`${name}-end`);
    performance.measure(name, `${name}-start`, `${name}-end`);
    return result;
  }
  
  /**
   * الحصول على إحصائيات الأداء
   */
  getPerformanceStats(): {
    memoryUsage: number;
    performanceEntries: PerformanceEntry[];
  } {
    return {
      memoryUsage: this.memoryUsage,
      performanceEntries: performance.getEntriesByType('measure'),
    };
  }
  
  /**
   * تنظيف الموارد
   */
  cleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
    }
  }
}

/**
 * خدمة التخزين المؤقت للواجهة الأمامية
 */
export class FrontendCacheService {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  private maxSize = PERFORMANCE_CONFIG.MAX_CACHE_SIZE;
  
  /**
   * حفظ البيانات في التخزين المؤقت
   */
  set(key: string, data: any, ttl: number = PERFORMANCE_CONFIG.CACHE_TTL_MS): void {
    // تنظيف التخزين المؤقت إذا امتلأ
    if (this.cache.size >= this.maxSize) {
      this.evictOldest();
    }
    
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }
  
  /**
   * جلب البيانات من التخزين المؤقت
   */
  get(key: string): any | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }
    
    // فحص انتهاء الصلاحية
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data;
  }
  
  /**
   * حذف البيانات من التخزين المؤقت
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }
  
  /**
   * مسح جميع البيانات
   */
  clear(): void {
    this.cache.clear();
  }
  
  /**
   * إزالة أقدم العناصر
   */
  private evictOldest(): void {
    const oldestKey = this.cache.keys().next().value;
    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }
  
  /**
   * الحصول على إحصائيات التخزين المؤقت
   */
  getStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
  } {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: 0, // يمكن تحسينه لاحقاً
    };
  }
}

/**
 * دوال مساعدة للتحسين
 */

/**
 * debounced search function
 */
export const createDebouncedSearch = (
  searchFn: (query: string) => void,
  delay: number = PERFORMANCE_CONFIG.SEARCH_DEBOUNCE_MS
) => {
  return debounce(searchFn, delay);
};

/**
 * debounced filter function
 */
export const createDebouncedFilter = (
  filterFn: (filters: any) => void,
  delay: number = PERFORMANCE_CONFIG.FILTER_DEBOUNCE_MS
) => {
  return debounce(filterFn, delay);
};

/**
 * Intersection Observer للـ lazy loading
 */
export const createIntersectionObserver = (
  callback: (entries: IntersectionObserverEntry[]) => void,
  options?: IntersectionObserverInit
): IntersectionObserver => {
  const defaultOptions: IntersectionObserverInit = {
    threshold: PERFORMANCE_CONFIG.INTERSECTION_THRESHOLD,
    rootMargin: PERFORMANCE_CONFIG.ROOT_MARGIN,
    ...options,
  };
  
  return new IntersectionObserver(callback, defaultOptions);
};

/**
 * تحسين الصور للتحميل السريع
 */
export const optimizeImageLoading = (img: HTMLImageElement): void => {
  // تحميل lazy
  img.loading = 'lazy';
  
  // تحسين الجودة
  img.decoding = 'async';
  
  // إضافة placeholder
  if (!img.src && !img.dataset.src) {
    img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB2aWV3Qm94PSIwIDAgMSAxIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9IiNGNUY1RjUiLz48L3N2Zz4=';
  }
};

/**
 * تحسين الجداول الكبيرة
 */
export const optimizeTableRendering = (
  tableElement: HTMLTableElement,
  data: any[]
): void => {
  // استخدام virtual scrolling للجداول الكبيرة
  if (data.length > PERFORMANCE_CONFIG.VIRTUAL_LIST_THRESHOLD) {
    tableElement.style.height = '400px';
    tableElement.style.overflowY = 'auto';
  }
  
  // تحسين الرسم
  tableElement.style.willChange = 'transform';
  tableElement.style.contain = 'layout style paint';
};

/**
 * تحسين النماذج الكبيرة
 */
export const optimizeFormPerformance = (form: HTMLFormElement): void => {
  // تأجيل التحقق من صحة البيانات
  const inputs = form.querySelectorAll('input, select, textarea');
  inputs.forEach(input => {
    if (input instanceof HTMLInputElement || input instanceof HTMLSelectElement || input instanceof HTMLTextAreaElement) {
      input.addEventListener('blur', debounce(() => {
        // تحقق من صحة البيانات
        input.checkValidity();
      }, 300));
    }
  });
};

// إنشاء مثيلات عامة
export const performanceManager = PerformanceManager.getInstance();
export const frontendCache = new FrontendCacheService();

// تصدير الدوال المحسنة
export const debouncedSearch = createDebouncedSearch;
export const debouncedFilter = createDebouncedFilter;
