/**
 * خدمة الكشف التلقائي عن عناوين الخادم
 * تحدد العنوان الصحيح للخادم الخلفي بناءً على موقع الجهاز
 */

interface URLConfig {
  backendURL: string;
  frontendURL: string;
  isLocalAccess: boolean;
  detectionMethod: string;
}

class URLDetectionService {
  private static instance: URLDetectionService;
  private cachedConfig: URLConfig | null = null;
  private detectionPromise: Promise<URLConfig> | null = null;

  private constructor() {}

  public static getInstance(): URLDetectionService {
    if (!URLDetectionService.instance) {
      URLDetectionService.instance = new URLDetectionService();
    }
    return URLDetectionService.instance;
  }

  /**
   * الحصول على إعدادات URL المناسبة
   */
  public async getURLConfig(): Promise<URLConfig> {
    // إذا كان هناك كشف جاري، انتظر النتيجة
    if (this.detectionPromise) {
      return this.detectionPromise;
    }

    // إذا كان لدينا cache صالح، استخدمه
    if (this.cachedConfig) {
      return this.cachedConfig;
    }

    // بدء عملية الكشف
    this.detectionPromise = this.detectURLs();
    
    try {
      const config = await this.detectionPromise;
      this.cachedConfig = config;
      return config;
    } finally {
      this.detectionPromise = null;
    }
  }

  /**
   * كشف العناوين المناسبة
   */
  private async detectURLs(): Promise<URLConfig> {
    const currentHost = window.location.hostname;
    const currentPort = window.location.port;

    console.log('🔍 [URLDetection] Current location:', { host: currentHost, port: currentPort });

    // تحديد نوع الوصول
    const isLocalAccess = this.isLocalAccess(currentHost, currentPort);

    if (isLocalAccess) {
      // وصول محلي - استخدم localhost
      const config: URLConfig = {
        backendURL: 'http://localhost:8002',
        frontendURL: `http://localhost:${currentPort || '5173'}`,
        isLocalAccess: true,
        detectionMethod: 'local_access'
      };

      console.log('🏠 [URLDetection] Local access detected:', config);
      return config;
    }

    // وصول عبر الشبكة - اكتشف أفضل عنوان للخادم
    const backendURL = await this.detectBestBackendURL(currentHost);
    
    const config: URLConfig = {
      backendURL,
      frontendURL: `http://${currentHost}:${currentPort || '5173'}`,
      isLocalAccess: false,
      detectionMethod: 'network_access'
    };

    console.log('🌐 [URLDetection] Network access detected:', config);
    return config;
  }

  /**
   * تحديد ما إذا كان الوصول محلي
   */
  private isLocalAccess(host: string, port: string): boolean {
    const isLocalHost = host === 'localhost' || host === '127.0.0.1';
    const isDevPort = ['5173', '5175', '3000', ''].includes(port);
    
    return isLocalHost && isDevPort;
  }

  /**
   * اكتشاف أفضل عنوان للخادم الخلفي
   */
  private async detectBestBackendURL(currentHost: string): Promise<string> {
    // قائمة العناوين المحتملة - أولوية للـ host الحالي
    const possibleURLs = [
      `http://${currentHost}:8002`, // أولوية للـ host الحالي
      'http://*************:8002', // عنوان IP الافتراضي للخادم
    ];

    // إزالة التكرارات
    const uniqueURLs = [...new Set(possibleURLs)];

    for (const url of uniqueURLs) {
      try {
        console.log('🔍 [URLDetection] Testing backend URL:', url);

        // استخدام AbortController للتحكم في timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 3000); // تقليل timeout إلى 3 ثوانٍ

        const response = await fetch(`${url}/api/system/health`, {
          method: 'GET',
          signal: controller.signal,
          headers: {
            'Accept': 'application/json',
          }
        });

        clearTimeout(timeoutId);

        // قبول أي استجابة (حتى 403) كدليل على أن الخادم يعمل
        if (response.status < 500) {
          console.log('✅ [URLDetection] Found working backend URL:', url, 'Status:', response.status);
          return url;
        }
      } catch (error) {
        console.log('❌ [URLDetection] Failed to connect to:', url);
        continue;
      }
    }

    // fallback للعنوان الافتراضي
    const fallbackURL = `http://${currentHost}:8002`;
    console.warn('⚠️ [URLDetection] No working backend found, using fallback:', fallbackURL);
    return fallbackURL;
  }

  /**
   * الحصول على عنوان الخادم الخلفي فقط
   */
  public async getBackendURL(): Promise<string> {
    const config = await this.getURLConfig();
    return config.backendURL;
  }

  /**
   * الحصول على عنوان الواجهة الأمامية فقط
   */
  public async getFrontendURL(): Promise<string> {
    const config = await this.getURLConfig();
    return config.frontendURL;
  }

  /**
   * التحقق من نوع الوصول
   */
  public async isLocalAccessMode(): Promise<boolean> {
    const config = await this.getURLConfig();
    return config.isLocalAccess;
  }

  /**
   * إعادة تعيين cache وإعادة الكشف
   */
  public async resetDetection(): Promise<URLConfig> {
    this.cachedConfig = null;
    this.detectionPromise = null;
    return this.getURLConfig();
  }

  /**
   * تحديث العنوان يدوياً (للاختبار)
   */
  public setManualConfig(backendURL: string, frontendURL?: string): void {
    this.cachedConfig = {
      backendURL,
      frontendURL: frontendURL || window.location.origin,
      isLocalAccess: backendURL.includes('localhost') || backendURL.includes('127.0.0.1'),
      detectionMethod: 'manual'
    };

    console.log('🔧 [URLDetection] Manual config set:', this.cachedConfig);
  }

  /**
   * الحصول على معلومات الكشف
   */
  public getDetectionInfo(): URLConfig | null {
    return this.cachedConfig;
  }
}

// إنشاء instance واحد للاستخدام في جميع أنحاء التطبيق
export const urlDetectionService = URLDetectionService.getInstance();

// دالة مساعدة للحصول على عنوان الخادم الخلفي بسرعة
export const getBackendURL = async (): Promise<string> => {
  return urlDetectionService.getBackendURL();
};

// دالة مساعدة للحصول على عنوان الواجهة الأمامية بسرعة
export const getFrontendURL = async (): Promise<string> => {
  return urlDetectionService.getFrontendURL();
};

export default urlDetectionService;
