/**
 * خدمة WebSocket محسنة للتواصل مع الخادم في الوقت الفعلي
 * تدعم heartbeat وتحديث حالة الأجهزة
 */

class EnhancedWebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // 1 second
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private listeners: Map<string, Set<Function>> = new Map();
  private deviceInfo: any = null;

  constructor() {
    this.setupDeviceInfo();
  }

  private setupDeviceInfo() {
    // جمع معلومات الجهاز للإرسال مع heartbeat
    this.deviceInfo = {
      hostname: window.location.hostname,
      platform: navigator.platform,
      system: navigator.userAgent.includes('Windows') ? 'Windows' : 
              navigator.userAgent.includes('Mac') ? 'macOS' : 
              navigator.userAgent.includes('Linux') ? 'Linux' : 'Unknown',
      browser: navigator.userAgent.includes('Chrome') ? 'Chrome' :
               navigator.userAgent.includes('Firefox') ? 'Firefox' :
               navigator.userAgent.includes('Safari') ? 'Safari' : 'Unknown',
      device_type: navigator.userAgent.includes('Mobile') ? 'هاتف ذكي' : 'جهاز بعيد',
      screen_resolution: `${screen.width}x${screen.height}`,
      user_agent: navigator.userAgent
    };
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      if (this.isConnecting) {
        reject(new Error('Connection already in progress'));
        return;
      }

      this.isConnecting = true;
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.hostname}:8002/ws/devices`;

      try {
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log('🔌 WebSocket connected successfully');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          this.emit('connected', {});
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
          } catch (error) {
            console.warn('Failed to parse WebSocket message:', error);
          }
        };

        this.ws.onclose = (event) => {
          console.log('🔌 WebSocket disconnected:', event.code, event.reason);
          this.isConnecting = false;
          this.stopHeartbeat();
          this.emit('disconnected', { code: event.code, reason: event.reason });
          
          // محاولة إعادة الاتصال إذا لم يكن الإغلاق مقصوداً
          if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('🔌 WebSocket error:', error);
          this.isConnecting = false;
          this.emit('error', error);
          reject(error);
        };

      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  private handleMessage(data: any) {
    const { type } = data;

    switch (type) {
      case 'connection_established':
        console.log('🎉 Connection established with device ID:', data.device_id);
        this.emit('connection_established', data);
        break;

      case 'heartbeat_ack':
        // تأكيد استلام heartbeat
        this.emit('heartbeat_ack', data);
        break;

      case 'device_status_update':
        console.log('📱 Device status update:', data);
        this.emit('device_status_update', data);
        break;

      case 'devices_update':
        console.log('📱 Devices list update');
        this.emit('devices_update', data);
        break;

      default:
        console.log('📨 Unknown message type:', type, data);
        this.emit('message', data);
    }
  }

  private startHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    this.heartbeatInterval = setInterval(() => {
      this.sendHeartbeat();
    }, 30000); // كل 30 ثانية

    // إرسال heartbeat فوري
    this.sendHeartbeat();
  }

  private stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  private sendHeartbeat() {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      // الحصول على المستخدم الحالي
      let currentUser = null;
      try {
        const authData = localStorage.getItem('auth-storage');
        if (authData) {
          const parsedAuth = JSON.parse(authData);
          currentUser = parsedAuth?.state?.user?.username;
        }
      } catch (e) {
        // تجاهل أخطاء parsing
      }

      const heartbeatData = {
        type: 'heartbeat',
        timestamp: new Date().toISOString(),
        current_user: currentUser,
        ...this.deviceInfo
      };

      this.ws.send(JSON.stringify(heartbeatData));
    }
  }

  private scheduleReconnect() {
    this.reconnectAttempts++;
    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), 30000);
    
    console.log(`🔄 Scheduling reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);
    
    setTimeout(() => {
      if (this.reconnectAttempts <= this.maxReconnectAttempts) {
        this.connect().catch(error => {
          console.error('Reconnect failed:', error);
        });
      }
    }, delay);
  }

  disconnect() {
    this.stopHeartbeat();
    if (this.ws) {
      this.ws.close(1000, 'Manual disconnect');
      this.ws = null;
    }
  }

  // نظام الأحداث
  on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(callback);
  }

  off(event: string, callback: Function) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.delete(callback);
    }
  }

  private emit(event: string, data: any) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  // إرسال تحديث معلومات الجهاز
  updateDeviceInfo(deviceData: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message = {
        type: 'device_info_update',
        device_data: {
          ...this.deviceInfo,
          ...deviceData
        },
        timestamp: new Date().toISOString()
      };

      this.ws.send(JSON.stringify(message));
    }
  }

  // الحصول على حالة الاتصال
  getConnectionState() {
    return {
      connected: this.ws?.readyState === WebSocket.OPEN,
      connecting: this.isConnecting,
      reconnectAttempts: this.reconnectAttempts
    };
  }
}

// إنشاء مثيل واحد من الخدمة
export const enhancedWebSocketService = new EnhancedWebSocketService();
export default enhancedWebSocketService;
