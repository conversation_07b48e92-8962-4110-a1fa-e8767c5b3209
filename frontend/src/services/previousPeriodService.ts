/**
 * خدمة جلب بيانات الفترة السابقة للمبيعات
 * هذا الملف مختص بجلب وتنظيم بيانات المبيعات للفترة السابقة فقط
 * يتعامل مع التاريخ والوقت بدقة عالية لضمان عدم تعارض البيانات
 */

import api from '../lib/axios';

export interface SalesTrendData {
  date: string;
  amount: number;
}

export interface PreviousPeriodResponse {
  data: SalesTrendData[];
  period: string;
  total: number;
}

/**
 * خدمة الفترة السابقة
 * تتعامل مع جلب وتنظيم بيانات المبيعات للفترة السابقة بدقة
 */
export class PreviousPeriodService {
  private static instance: PreviousPeriodService;

  /**
   * الحصول على مثيل واحد من الخدمة (Singleton Pattern)
   */
  public static getInstance(): PreviousPeriodService {
    if (!PreviousPeriodService.instance) {
      PreviousPeriodService.instance = new PreviousPeriodService();
    }
    return PreviousPeriodService.instance;
  }

  /**
   * جلب بيانات مبيعات اليوم السابق (أمس - 24 ساعة كاملة)
   *
   * @returns قائمة بيانات المبيعات لكل ساعة في اليوم السابق (أمس كاملاً من 00:00 إلى 23:59)
   */
  async getPreviousDaySales(): Promise<SalesTrendData[]> {
    try {
      console.log('جلب بيانات مبيعات اليوم السابق (أمس - 24 ساعة كاملة)');

      const response = await api.get('/api/dashboard/sales-trends/day', {
        params: { previous: true }
      });

      console.log('تم جلب بيانات اليوم السابق بنجاح (أمس كاملاً):', response.data);
      return response.data;
    } catch (error) {
      console.error('خطأ في جلب بيانات اليوم السابق:', error);
      // إرجاع بيانات فارغة في حالة الخطأ (24 ساعة)
      return Array.from({ length: 24 }, (_, hour) => ({
        date: `${hour.toString().padStart(2, '0')}:00`,
        amount: 0
      }));
    }
  }

  /**
   * جلب بيانات مبيعات الأسبوع السابق (7 أيام قبل الأسبوع الحالي)
   *
   * @returns قائمة بيانات المبيعات لكل يوم في الأسبوع السابق
   */
  async getPreviousWeekSales(): Promise<SalesTrendData[]> {
    try {
      console.log('جلب بيانات مبيعات الأسبوع السابق');

      const response = await api.get('/api/dashboard/sales-trends/week', {
        params: { previous: true }
      });

      console.log('تم جلب بيانات الأسبوع السابق بنجاح:', response.data);
      return response.data;
    } catch (error) {
      console.error('خطأ في جلب بيانات الأسبوع السابق:', error);
      // إرجاع بيانات فارغة في حالة الخطأ
      const today = new Date();
      const weekData: SalesTrendData[] = [];
      for (let i = 13; i >= 7; i--) { // الأسبوع السابق (من 13 إلى 7 أيام مضت)
        const date = new Date(today);
        date.setDate(today.getDate() - i);
        weekData.push({
          date: date.toISOString().split('T')[0],
          amount: 0
        });
      }
      return weekData;
    }
  }

  /**
   * جلب بيانات مبيعات الشهر السابق (30 يوم قبل الشهر الحالي)
   *
   * @returns قائمة بيانات المبيعات لكل يوم في الشهر السابق
   */
  async getPreviousMonthSales(): Promise<SalesTrendData[]> {
    try {
      console.log('جلب بيانات مبيعات الشهر السابق');

      const response = await api.get('/api/dashboard/sales-trends/month', {
        params: { previous: true }
      });

      console.log('تم جلب بيانات الشهر السابق بنجاح:', response.data);
      return response.data;
    } catch (error) {
      console.error('خطأ في جلب بيانات الشهر السابق:', error);
      // إرجاع بيانات فارغة في حالة الخطأ
      const today = new Date();
      const monthData: SalesTrendData[] = [];
      for (let i = 59; i >= 30; i--) { // الشهر السابق (من 59 إلى 30 يوم مضى)
        const date = new Date(today);
        date.setDate(today.getDate() - i);
        monthData.push({
          date: date.toISOString().split('T')[0],
          amount: 0
        });
      }
      return monthData;
    }
  }

  /**
   * جلب بيانات مبيعات السنة السابقة (12 شهر قبل السنة الحالية)
   *
   * @returns قائمة بيانات المبيعات لكل شهر في السنة السابقة
   */
  async getPreviousYearSales(): Promise<SalesTrendData[]> {
    try {
      console.log('جلب بيانات مبيعات السنة السابقة');

      const response = await api.get('/api/dashboard/sales-trends/year', {
        params: { previous: true }
      });

      console.log('تم جلب بيانات السنة السابقة بنجاح:', response.data);
      return response.data;
    } catch (error) {
      console.error('خطأ في جلب بيانات السنة السابقة:', error);
      // إرجاع بيانات فارغة في حالة الخطأ
      const today = new Date();
      const yearData: SalesTrendData[] = [];
      for (let i = 23; i >= 12; i--) { // السنة السابقة (من 23 إلى 12 شهر مضى)
        const date = new Date(today);
        date.setMonth(today.getMonth() - i);
        yearData.push({
          date: `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`,
          amount: 0
        });
      }
      return yearData;
    }
  }

  /**
   * جلب بيانات المبيعات حسب الفترة المحددة
   *
   * @param period نوع الفترة (day, week, month, year)
   * @returns قائمة بيانات المبيعات للفترة السابقة المحددة
   */
  async getSalesByPeriod(period: 'day' | 'week' | 'month' | 'year'): Promise<SalesTrendData[]> {
    console.log(`جلب بيانات الفترة السابقة: ${period}`);

    switch (period) {
      case 'day':
        return this.getPreviousDaySales();
      case 'week':
        return this.getPreviousWeekSales();
      case 'month':
        return this.getPreviousMonthSales();
      case 'year':
        return this.getPreviousYearSales();
      default:
        console.error(`نوع فترة غير مدعوم: ${period}`);
        return [];
    }
  }

  /**
   * جلب إجمالي المبيعات للفترة السابقة من الخادم مباشرة
   *
   * @param period نوع الفترة (day, week, month, year)
   * @returns إجمالي المبيعات للفترة السابقة
   */
  async getPreviousPeriodTotal(period: 'day' | 'week' | 'month' | 'year'): Promise<number> {
    try {
      console.log(`جلب إجمالي الفترة السابقة من الخادم: ${period}`);

      // استدعاء endpoint الخادم المخصص لجلب إجمالي الفترة السابقة
      const response = await api.get(`/api/dashboard/previous-period-total/${period}`);
      const total = response.data.previous_total || 0;

      console.log(`إجمالي الفترة السابقة ${period} من الخادم:`, {
        total,
        responseData: response.data,
        period,
        timestamp: new Date().toISOString()
      });
      return total;
    } catch (error) {
      console.error('خطأ في جلب إجمالي الفترة السابقة من الخادم:', error);

      // في حالة فشل الخادم، احسب الإجمالي محلياً كبديل
      try {
        console.log('محاولة حساب الإجمالي محلياً كبديل...');
        const salesData = await this.getSalesByPeriod(period);
        const total = salesData.reduce((sum, item) => sum + item.amount, 0);
        console.log(`إجمالي الفترة السابقة ${period} محلياً: ${total}`);
        return total;
      } catch (fallbackError) {
        console.error('فشل في الحساب المحلي أيضاً:', fallbackError);
        return 0;
      }
    }
  }

  /**
   * مقارنة الفترة الحالية مع السابقة
   *
   * @param period نوع الفترة
   * @param currentTotal إجمالي الفترة الحالية
   * @returns نسبة النمو والمقارنة
   */
  async compareWithCurrentPeriod(period: 'day' | 'week' | 'month' | 'year', currentTotal: number) {
    try {
      console.log(`مقارنة الفترة ${period} مع السابقة`);

      const previousTotal = await this.getPreviousPeriodTotal(period);

      let growthRate = 0;
      if (previousTotal > 0) {
        growthRate = ((currentTotal - previousTotal) / previousTotal) * 100;
      } else if (currentTotal > 0) {
        growthRate = 100; // نمو 100% إذا لم تكن هناك مبيعات سابقة
      }

      const comparison = {
        current: currentTotal,
        previous: previousTotal,
        growthRate: Math.round(growthRate * 100) / 100,
        isPositive: growthRate >= 0,
        difference: currentTotal - previousTotal
      };

      console.log(`نتيجة المقارنة للفترة ${period}:`, comparison);
      return comparison;
    } catch (error) {
      console.error('خطأ في مقارنة الفترات:', error);
      return {
        current: currentTotal,
        previous: 0,
        growthRate: 0,
        isPositive: true,
        difference: 0
      };
    }
  }

  /**
   * تحديث البيانات وإعادة جلبها
   *
   * @param period نوع الفترة المراد تحديثها
   * @returns البيانات المحدثة
   */
  async refreshPeriodData(period: 'day' | 'week' | 'month' | 'year'): Promise<SalesTrendData[]> {
    console.log(`تحديث بيانات الفترة السابقة: ${period}`);

    // إعادة جلب البيانات من الخادم
    return this.getSalesByPeriod(period);
  }

  /**
   * التحقق من صحة البيانات
   *
   * @param data البيانات المراد التحقق منها
   * @returns true إذا كانت البيانات صحيحة
   */
  validateSalesData(data: SalesTrendData[]): boolean {
    if (!Array.isArray(data)) {
      console.error('البيانات ليست مصفوفة صحيحة');
      return false;
    }

    for (const item of data) {
      if (!item.date || typeof item.amount !== 'number') {
        console.error('عنصر بيانات غير صحيح:', item);
        return false;
      }
    }

    return true;
  }

  /**
   * تنسيق البيانات للعرض في المخططات
   *
   * @param data البيانات الخام
   * @param period نوع الفترة
   * @returns البيانات المنسقة
   */
  formatDataForChart(data: SalesTrendData[]): SalesTrendData[] {
    if (!this.validateSalesData(data)) {
      console.warn('بيانات غير صحيحة، إرجاع بيانات فارغة');
      return [];
    }

    // تنسيق التواريخ حسب نوع الفترة
    return data.map(item => ({
      ...item,
      amount: Math.round(item.amount * 100) / 100 // تقريب إلى منزلتين عشريتين
    }));
  }
}

// تصدير مثيل واحد من الخدمة
export const previousPeriodService = PreviousPeriodService.getInstance();
