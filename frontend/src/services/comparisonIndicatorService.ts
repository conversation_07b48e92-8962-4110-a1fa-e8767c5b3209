/**
 * خدمة مؤشرات المقارنة مع الأمس
 * تحسب المؤشرات والنسب المئوية للمقارنة مع اليوم السابق
 * تطبق مبادئ البرمجة الكائنية والتصميم الموحد للنظام
 */

import api from '../lib/axios';

export interface ComparisonData {
  current: number;
  previous: number;
  difference: number;
  percentageChange: number;
  trend: 'up' | 'down' | 'neutral';
  isPositive: boolean;
}

export interface DashboardComparison {
  todaySales: ComparisonData;
  todayDebts: ComparisonData;
  todayProfits: ComparisonData;
}

/**
 * خدمة مؤشرات المقارنة مع الأمس
 * تطبق نمط Singleton لضمان وجود نسخة واحدة فقط
 */
export class ComparisonIndicatorService {
  private static instance: ComparisonIndicatorService;
  private cache: Map<string, { data: any; timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 دقائق

  private constructor() {
    console.log('تم تهيئة خدمة مؤشرات المقارنة');
  }

  /**
   * الحصول على نسخة الخدمة (Singleton Pattern)
   */
  public static getInstance(): ComparisonIndicatorService {
    if (!ComparisonIndicatorService.instance) {
      ComparisonIndicatorService.instance = new ComparisonIndicatorService();
    }
    return ComparisonIndicatorService.instance;
  }

  /**
   * حساب بيانات المقارنة
   */
  private calculateComparison(current: number, previous: number): ComparisonData {
    const difference = current - previous;
    let percentageChange = 0;

    if (previous > 0) {
      percentageChange = (difference / previous) * 100;
    } else if (current > 0) {
      percentageChange = 100; // نمو 100% إذا لم تكن هناك قيمة سابقة
    }

    let trend: 'up' | 'down' | 'neutral' = 'neutral';
    if (percentageChange > 0) {
      trend = 'up';
    } else if (percentageChange < 0) {
      trend = 'down';
    }

    return {
      current,
      previous,
      difference,
      percentageChange: Math.round(percentageChange * 100) / 100,
      trend,
      isPositive: percentageChange >= 0
    };
  }

  /**
   * جلب بيانات مبيعات اليوم
   */
  private async getTodaySalesData(): Promise<number> {
    try {
      const response = await api.get('/api/dashboard/stats');
      return response.data.todaySales || 0;
    } catch (error) {
      console.error('خطأ في جلب بيانات مبيعات اليوم:', error);
      return 0;
    }
  }

  /**
   * جلب بيانات مبيعات الأمس
   */
  private async getYesterdaySalesData(): Promise<number> {
    try {
      const response = await api.get('/api/dashboard/previous-period-total/day');
      return response.data.previous_total || 0;
    } catch (error) {
      console.error('خطأ في جلب بيانات مبيعات الأمس:', error);
      return 0;
    }
  }

  /**
   * جلب بيانات ديون اليوم
   */
  private async getTodayDebtsData(): Promise<number> {
    try {
      const response = await api.get('/api/dashboard/stats');
      return Math.abs(response.data.totalDebts || 0);
    } catch (error) {
      console.error('خطأ في جلب بيانات ديون اليوم:', error);
      return 0;
    }
  }

  /**
   * جلب بيانات ديون الأمس
   */
  private async getYesterdayDebtsData(): Promise<number> {
    try {
      const response = await api.get('/api/dashboard/yesterday-debts');
      return response.data.yesterday_debts || 0;
    } catch (error) {
      console.error('خطأ في جلب بيانات ديون الأمس:', error);
      return 0;
    }
  }

  /**
   * جلب بيانات أرباح اليوم
   */
  private async getTodayProfitsData(): Promise<number> {
    try {
      const response = await api.get('/api/dashboard/stats');
      return response.data.todayProfits || 0;
    } catch (error) {
      console.error('خطأ في جلب بيانات أرباح اليوم:', error);
      return 0;
    }
  }

  /**
   * جلب بيانات أرباح الأمس
   */
  private async getYesterdayProfitsData(): Promise<number> {
    try {
      const response = await api.get('/api/dashboard/yesterday-profits');
      return response.data.yesterday_profits || 0;
    } catch (error) {
      console.error('خطأ في جلب بيانات أرباح الأمس:', error);
      return 0;
    }
  }

  /**
   * جلب جميع مؤشرات المقارنة للوحة التحكم
   */
  public async getDashboardComparison(): Promise<DashboardComparison> {
    try {
      console.log('جلب مؤشرات المقارنة للوحة التحكم');

      // التحقق من الكاش
      const cacheKey = 'dashboard_comparison';
      const cached = this.cache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
        console.log('استخدام البيانات المخزنة مؤقتاً');
        return cached.data;
      }

      // جلب البيانات بشكل متوازي
      const [
        todaySales,
        yesterdaySales,
        todayDebts,
        yesterdayDebts,
        todayProfits,
        yesterdayProfits
      ] = await Promise.all([
        this.getTodaySalesData(),
        this.getYesterdaySalesData(),
        this.getTodayDebtsData(),
        this.getYesterdayDebtsData(),
        this.getTodayProfitsData(),
        this.getYesterdayProfitsData()
      ]);

      // حساب المقارنات
      const comparison: DashboardComparison = {
        todaySales: this.calculateComparison(todaySales, yesterdaySales),
        todayDebts: this.calculateComparison(todayDebts, yesterdayDebts),
        todayProfits: this.calculateComparison(todayProfits, yesterdayProfits)
      };

      // حفظ في الكاش
      this.cache.set(cacheKey, {
        data: comparison,
        timestamp: Date.now()
      });

      console.log('تم حساب مؤشرات المقارنة بنجاح:', comparison);
      return comparison;

    } catch (error) {
      console.error('خطأ في جلب مؤشرات المقارنة:', error);
      
      // إرجاع قيم افتراضية في حالة الخطأ
      return {
        todaySales: this.calculateComparison(0, 0),
        todayDebts: this.calculateComparison(0, 0),
        todayProfits: this.calculateComparison(0, 0)
      };
    }
  }

  /**
   * مسح الكاش
   */
  public clearCache(): void {
    this.cache.clear();
    console.log('تم مسح كاش مؤشرات المقارنة');
  }

  /**
   * تحديث البيانات وإعادة جلبها
   */
  public async refreshData(): Promise<DashboardComparison> {
    this.clearCache();
    return this.getDashboardComparison();
  }
}

// تصدير نسخة الخدمة
export const comparisonIndicatorService = ComparisonIndicatorService.getInstance();
