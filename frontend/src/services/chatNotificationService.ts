/**
 * خدمة إدارة تنبيهات الرسائل الجديدة
 * تدير عرض التنبيهات عندما يكون المستخدم متصل ولكن نافذة المحادثة مغلقة
 * تشمل تشغيل الأصوات للتنبيهات الجديدة
 */

import { chatAudioService } from './chatAudioService';

interface ChatMessage {
  id: number;
  sender_id: number;
  receiver_id: number;
  content: string;
  message_type: string;
  status: string;
  created_at: string;
  sender_username?: string;
  sender_full_name?: string;
}

interface NotificationState {
  isChatWindowOpen: boolean;
  currentConversationId: number | null;
  isUserActive: boolean;
  notificationsEnabled: boolean;
}

type NotificationListener = (message: ChatMessage) => void;

class ChatNotificationService {
  private state: NotificationState = {
    isChatWindowOpen: false,
    currentConversationId: null,
    isUserActive: true,
    notificationsEnabled: true
  };

  private listeners: Set<NotificationListener> = new Set();
  private activeNotifications: Map<number, ChatMessage> = new Map();
  private lastActivityTime: number = Date.now();
  private activityCheckInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.setupActivityMonitoring();
    this.setupVisibilityMonitoring();

    // ✅ تسجيل محدود: فقط عند التهيئة
    console.log('🔔 تم تهيئة خدمة تنبيهات المحادثة:', {
      notificationsEnabled: this.state.notificationsEnabled,
      isUserActive: this.state.isUserActive
    });
  }

  /**
   * مراقبة نشاط المستخدم
   */
  private setupActivityMonitoring() {
    // ✅ تحسين: تقليل التسجيل المفرط - تسجيل فقط عند تغيير الحالة
    let lastLogTime = 0;
    const LOG_THROTTLE_MS = 60000; // تسجيل مرة واحدة كل دقيقة كحد أقصى

    const updateActivity = () => {
      const now = Date.now();
      const wasActive = this.state.isUserActive;

      this.lastActivityTime = now;
      this.state.isUserActive = true;

      // ✅ تسجيل محدود: فقط عند تغيير الحالة أو كل دقيقة
      if (!wasActive || (now - lastLogTime) > LOG_THROTTLE_MS) {
        console.log('👤 تم تحديث نشاط المستخدم:', new Date().toLocaleTimeString());
        lastLogTime = now;
      }
    };

    // مراقبة الأحداث التي تدل على نشاط المستخدم
    // إضافة المزيد من الأحداث لتتبع أفضل للنشاط
    [
      'mousedown', 'mousemove', 'mouseup', 'click',
      'keypress', 'keydown', 'keyup',
      'scroll', 'wheel',
      'touchstart', 'touchmove', 'touchend',
      'focus', 'blur'
    ].forEach(event => {
      document.addEventListener(event, updateActivity, { passive: true });
    });

    // فحص دوري لحالة النشاط
    this.activityCheckInterval = setInterval(() => {
      const inactiveTime = Date.now() - this.lastActivityTime;
      const wasActive = this.state.isUserActive;
      // زيادة المدة إلى 30 دقيقة بدلاً من دقيقة واحدة
      this.state.isUserActive = inactiveTime < 1800000; // 30 دقيقة = 30 * 60 * 1000

      // ✅ تسجيل محدود: فقط عند تغيير الحالة أو كل 5 دقائق
      const shouldLog = wasActive !== this.state.isUserActive ||
                       (Date.now() - lastLogTime) > 300000; // 5 دقائق

      if (shouldLog) {
        console.log('🔍 فحص نشاط المستخدم:', {
          inactiveTime: this.formatInactiveTime(inactiveTime),
          isActive: this.state.isUserActive,
          wasActive
        });
        lastLogTime = Date.now();
      }

      // إذا أصبح المستخدم غير نشط، إخفاء التنبيهات النشطة
      if (wasActive && !this.state.isUserActive) {
        console.log('😴 المستخدم أصبح غير نشط، إخفاء التنبيهات');
        this.clearAllNotifications();
      }
    }, 30000); // فحص كل 30 ثانية بدلاً من 10
  }

  /**
   * تنسيق وقت عدم النشاط بشكل قابل للقراءة
   */
  private formatInactiveTime(inactiveTimeMs: number): string {
    const seconds = Math.round(inactiveTimeMs / 1000);

    if (seconds < 60) {
      return `${seconds} ثانية`;
    } else if (seconds < 3600) {
      const minutes = Math.round(seconds / 60);
      return `${minutes} دقيقة`;
    } else {
      const hours = Math.round(seconds / 3600);
      return `${hours} ساعة`;
    }
  }

  /**
   * مراقبة رؤية الصفحة
   */
  private setupVisibilityMonitoring() {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // الصفحة مخفية - لا نغير حالة النشاط فوراً
        console.log('👁️ الصفحة أصبحت مخفية');
        // لا نغير isUserActive هنا، سنتركه للفحص الدوري
      } else {
        // الصفحة مرئية - تحديث وقت النشاط
        console.log('👁️ الصفحة أصبحت مرئية');
        this.lastActivityTime = Date.now();
        this.state.isUserActive = true;
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
  }

  /**
   * تحديث حالة نافذة المحادثة
   */
  setChatWindowState(isOpen: boolean, currentConversationId: number | null = null) {
    const wasOpen = this.state.isChatWindowOpen;
    const previousConversationId = this.state.currentConversationId;

    this.state.isChatWindowOpen = isOpen;
    this.state.currentConversationId = currentConversationId;

    // ✅ تسجيل محدود: فقط عند تغيير الحالة
    if (wasOpen !== isOpen || previousConversationId !== currentConversationId) {
      console.log(`💬 تحديث حالة نافذة المحادثة:`, {
        isOpen: isOpen ? 'مفتوحة' : 'مغلقة',
        currentConversation: currentConversationId,
        activeNotifications: this.activeNotifications.size
      });
    }

    // إذا تم فتح النافذة، إخفاء جميع التنبيهات
    if (!wasOpen && isOpen) {
      console.log('🗑️ إخفاء جميع التنبيهات لأن النافذة تم فتحها');
      this.clearAllNotifications();
    }

    // إذا تم إغلاق النافذة، إعادة تعيين الحالة بالكامل
    if (wasOpen && !isOpen) {
      console.log('🔄 إعادة تعيين حالة التنبيهات بعد إغلاق النافذة');
      this.state.currentConversationId = null;
      // لا نحذف التنبيهات النشطة عند الإغلاق، فقط نعيد تعيين الحالة
    }
  }

  /**
   * تفعيل/تعطيل التنبيهات
   */
  setNotificationsEnabled(enabled: boolean) {
    this.state.notificationsEnabled = enabled;
    
    if (!enabled) {
      this.clearAllNotifications();
    }
  }

  /**
   * معالجة رسالة جديدة
   */
  handleNewMessage(message: ChatMessage) {
    // تحديث نشاط المستخدم عند وصول رسالة جديدة
    this.lastActivityTime = Date.now();
    this.state.isUserActive = true;

    // ✅ تسجيل محدود: فقط للرسائل المهمة
    console.log('📨 معالجة رسالة جديدة:', {
      messageId: message.id,
      senderId: message.sender_id,
      content: message.content.substring(0, 30) + '...'
    });

    // التحقق من الشروط لإظهار التنبيه
    if (!this.shouldShowNotification(message)) {
      // ✅ تسجيل محدود: فقط في وضع التطوير
      if (process.env.NODE_ENV === 'development') {
        console.log('🔕 لن يتم إظهار تنبيه للرسالة - السبب:', this.getNotificationBlockReason(message));
      }
      return;
    }

    // إضافة الرسالة للتنبيهات النشطة
    this.activeNotifications.set(message.id, message);

    // تشغيل صوت التنبيه
    this.playNotificationSound();

    // إشعار المستمعين
    this.notifyListeners(message);

    // ✅ تسجيل محدود: فقط عند إظهار التنبيه فعلياً
    console.log('🔔 تم إظهار تنبيه للرسالة الجديدة:', {
      messageId: message.id,
      activeNotificationsCount: this.activeNotifications.size
    });
  }

  /**
   * التحقق من ضرورة إظهار التنبيه
   */
  private shouldShowNotification(message: ChatMessage): boolean {
    // التنبيهات معطلة
    if (!this.state.notificationsEnabled) {
      return false;
    }

    // المستخدم غير نشط
    if (!this.state.isUserActive) {
      return false;
    }

    // نافذة المحادثة مفتوحة
    if (this.state.isChatWindowOpen) {
      // إذا كانت المحادثة الحالية مع نفس المرسل، لا نظهر تنبيه
      if (this.state.currentConversationId === message.sender_id) {
        return false;
      }
    }

    // تجنب التنبيهات المكررة
    if (this.activeNotifications.has(message.id)) {
      return false;
    }

    // إزالة أي تنبيهات موجودة قبل إضافة الجديد (تنبيه واحد فقط)
    if (this.activeNotifications.size > 0) {
      console.log('🔄 إزالة التنبيهات الموجودة لعرض التنبيه الجديد');
      this.activeNotifications.clear();
    }

    return true;
  }

  /**
   * الحصول على سبب عدم إظهار التنبيه (للتشخيص)
   */
  private getNotificationBlockReason(message: ChatMessage): string {
    if (!this.state.notificationsEnabled) {
      return 'التنبيهات معطلة';
    }

    if (!this.state.isUserActive) {
      return 'المستخدم غير نشط';
    }

    if (this.state.isChatWindowOpen && this.state.currentConversationId === message.sender_id) {
      return 'المحادثة مفتوحة مع نفس المرسل';
    }

    if (this.activeNotifications.has(message.id)) {
      return 'تنبيه مكرر';
    }

    // لا نحتاج لفحص كثرة التنبيهات لأننا نعرض تنبيه واحد فقط

    return 'سبب غير معروف';
  }

  /**
   * إزالة تنبيه معين
   */
  dismissNotification(messageId: number) {
    this.activeNotifications.delete(messageId);
    console.log(`🗑️ تم إزالة تنبيه الرسالة ${messageId}`);
  }

  /**
   * إزالة جميع التنبيهات
   */
  clearAllNotifications() {
    const count = this.activeNotifications.size;
    this.activeNotifications.clear();
    
    if (count > 0) {
      console.log(`🗑️ تم إزالة ${count} تنبيه`);
    }
  }

  /**
   * إضافة مستمع للتنبيهات
   */
  addListener(listener: NotificationListener): () => void {
    this.listeners.add(listener);
    
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * إشعار جميع المستمعين
   */
  private notifyListeners(message: ChatMessage) {
    // ✅ تسجيل محدود: فقط عدد المستمعين
    console.log(`📢 إشعار ${this.listeners.size} مستمع بالرسالة الجديدة`);

    let index = 0;
    this.listeners.forEach(listener => {
      try {
        index++;
        // ✅ إزالة التسجيل المفرط لكل مستمع
        listener(message);
      } catch (error) {
        console.error(`❌ خطأ في مستمع التنبيهات ${index}:`, error);
      }
    });
  }

  /**
   * الحصول على التنبيهات النشطة
   */
  getActiveNotifications(): ChatMessage[] {
    return Array.from(this.activeNotifications.values());
  }

  /**
   * الحصول على حالة الخدمة
   */
  getState(): NotificationState {
    return { ...this.state };
  }

  /**
   * إعادة تفعيل نشاط المستخدم يدوياً
   */
  refreshUserActivity() {
    console.log('🔄 إعادة تفعيل نشاط المستخدم يدوياً');
    this.lastActivityTime = Date.now();
    this.state.isUserActive = true;
  }

  /**
   * إعادة تعيين حالة النظام (للتشخيص)
   */
  resetState() {
    console.log('🔄 إعادة تعيين حالة نظام التنبيهات...');
    this.state.isChatWindowOpen = false;
    this.state.currentConversationId = null;
    this.state.isUserActive = true;
    this.state.notificationsEnabled = true;
    this.lastActivityTime = Date.now();
    this.clearAllNotifications();
  }

  /**
   * اختبار فتح المحادثة (للتشخيص)
   */
  testOpenChat(userId: number = 999) {
    console.log('🧪 اختبار فتح المحادثة مع المستخدم:', userId);
    const event = new CustomEvent('openChatWithUser', {
      detail: { userId }
    });
    window.dispatchEvent(event);
  }

  /**
   * اختبار النظام (للتشخيص)
   */
  testNotification(senderId: number = 999) {
    const testMessage: ChatMessage = {
      id: Date.now(),
      sender_id: senderId,
      receiver_id: 1,
      content: `رسالة اختبار من المستخدم ${senderId}`,
      message_type: 'text',
      status: 'sent',
      created_at: new Date().toISOString(),
      sender_username: `test_user_${senderId}`,
      sender_full_name: `مستخدم اختبار ${senderId}`
    };

    console.log('🧪 اختبار نظام التنبيهات مع المستخدم:', senderId);
    this.handleNewMessage(testMessage);
  }

  /**
   * تشغيل صوت التنبيه
   */
  private async playNotificationSound(): Promise<void> {
    try {
      const success = await chatAudioService.playNotificationSound();
      if (success) {
        console.log('🔊 تم تشغيل صوت التنبيه بنجاح');
      } else {
        console.log('🔇 لم يتم تشغيل صوت التنبيه (معطل أو خطأ)');
      }
    } catch (error) {
      console.error('❌ خطأ في تشغيل صوت التنبيه:', error);
    }
  }

  /**
   * الحصول على إعدادات الصوت
   */
  getAudioSettings() {
    return chatAudioService.getSettings();
  }

  /**
   * تحديث إعدادات الصوت
   */
  async updateAudioSettings(settings: any) {
    await chatAudioService.updateSettings(settings);
  }

  /**
   * اختبار تشغيل الصوت
   */
  async testAudioNotification(): Promise<boolean> {
    console.log('🧪 اختبار تشغيل صوت التنبيه...');
    return await chatAudioService.testSound();
  }

  /**
   * تنظيف الموارد
   */
  destroy() {
    if (this.activityCheckInterval) {
      clearInterval(this.activityCheckInterval);
    }

    this.listeners.clear();
    this.activeNotifications.clear();

    // تنظيف موارد الصوت
    chatAudioService.dispose();
  }
}

// إنشاء مثيل واحد للخدمة
export const chatNotificationService = new ChatNotificationService();

// إتاحة الخدمة للاختبار من وحدة التحكم (فقط في وضع التطوير)
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).chatNotificationService = chatNotificationService;
  console.log('🔔 خدمة التنبيهات متاحة في window.chatNotificationService');
  console.log('💡 للاختبار:');
  console.log('  - window.chatNotificationService.testNotification(999)');
  console.log('  - window.chatNotificationService.testOpenChat(999)');
  console.log('  - window.chatNotificationService.resetState()');
}

export default chatNotificationService;
