import apiClient from '../lib/axios'

export interface LoginCredentials {
  username: string
  password: string
}

export interface RegisterData {
  username: string
  email?: string
  fullName?: string
  password: string
  role?: 'admin' | 'cashier'
}

export interface LoginResponse {
  access_token: string
  token_type: string
}

export interface UserResponse {
  id: number
  username: string
  email: string
  full_name: string
  role: 'admin' | 'cashier'
  is_active: boolean
}

export const authApi = {
  login: async (credentials: LoginCredentials): Promise<LoginResponse> => {
    const formData = new URLSearchParams()
    formData.append('username', credentials.username)
    formData.append('password', credentials.password)

    const response = await apiClient.post<LoginResponse>('/api/auth/token', 
      formData.toString(),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    )
    return response.data
  },

  register: async (data: RegisterData): Promise<UserResponse> => {
    const response = await apiClient.post<UserResponse>('/api/auth/register', data)
    return response.data
  },

  getCurrentUser: async (): Promise<UserResponse> => {
    const response = await apiClient.get<UserResponse>('/api/auth/me')
    return response.data
  },
}

export const userApi = {
  getUsers: async (): Promise<UserResponse[]> => {
    const response = await apiClient.get<UserResponse[]>('/api/users')
    return response.data
  },

  getUser: async (id: number): Promise<UserResponse> => {
    const response = await apiClient.get<UserResponse>(`/api/users/${id}`)
    return response.data
  },

  updateUser: async (id: number, data: Partial<RegisterData>): Promise<UserResponse> => {
    const response = await apiClient.put<UserResponse>(`/api/users/${id}`, data)
    return response.data
  },

  deleteUser: async (id: number): Promise<void> => {
    await apiClient.delete(`/api/users/${id}`)
  },
} 