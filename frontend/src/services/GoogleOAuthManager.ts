/**
 * مدير OAuth لـ Google Drive - حل مشاكل Cross-Origin-Opener-Policy
 */

interface GoogleOAuthConfig {
  authUrl: string;
  onSuccess: () => void;
  onError: (error: string) => void;
  onCancel: () => void;
}

/**
 * فئة إدارة OAuth مع Google Drive
 * تحل مشاكل Cross-Origin-Opener-Policy باستخدام PostMessage API
 */
export class GoogleOAuthManager {
  private config: GoogleOAuthConfig;
  private authWindow: Window | null = null;
  private messageListener: ((event: MessageEvent) => void) | null = null;
  private timeoutId: NodeJS.Timeout | null = null;
  private checkInterval: NodeJS.Timeout | null = null;
  private isCompleted: boolean = false;

  constructor(config: GoogleOAuthConfig) {
    this.config = config;
  }

  /**
   * بدء عملية OAuth
   */
  public startFlow(): void {
    try {
      this.cleanup();
      this.isCompleted = false;

      // فتح نافذة OAuth
      this.authWindow = window.open(
        this.config.authUrl,
        'google_oauth',
        this.getWindowFeatures()
      );

      if (!this.authWindow) {
        this.handleError('فشل في فتح نافذة تسجيل الدخول. تأكد من السماح للنوافذ المنبثقة.');
        return;
      }

      // التركيز على النافذة
      this.authWindow.focus();

      // إعداد مستمع الرسائل
      this.setupMessageListener();

      // إعداد مراقبة النافذة (بديل آمن)
      this.setupWindowMonitoring();

      // إعداد timeout
      this.setupTimeout();

    } catch (error) {
      this.handleError(`خطأ في بدء OAuth: ${error}`);
    }
  }

  /**
   * إعداد مستمع الرسائل من النافذة المنبثقة
   */
  private setupMessageListener(): void {
    this.messageListener = (event: MessageEvent) => {
      // التحقق من مصدر الرسالة للأمان
      if (!this.isValidOrigin(event.origin)) {
        return;
      }

      try {
        const data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;
        
        if (data.type === 'GOOGLE_OAUTH_SUCCESS') {
          this.handleSuccess();
        } else if (data.type === 'GOOGLE_OAUTH_ERROR') {
          this.handleError(data.error || 'حدث خطأ في تسجيل الدخول');
        } else if (data.type === 'GOOGLE_OAUTH_CANCEL') {
          this.handleCancel();
        }
      } catch (error) {
        console.warn('تم تجاهل رسالة غير صالحة من النافذة المنبثقة:', error);
      }
    };

    window.addEventListener('message', this.messageListener);
  }

  /**
   * إعداد مراقبة النافذة بطريقة آمنة
   */
  private setupWindowMonitoring(): void {
    this.checkInterval = setInterval(() => {
      if (!this.authWindow || this.isCompleted) {
        return;
      }

      try {
        // محاولة آمنة للتحقق من حالة النافذة
        // استخدام try-catch لتجنب Cross-Origin-Opener-Policy errors
        if (this.authWindow.closed) {
          this.handleCancel();
        }
      } catch (error) {
        // تجاهل أخطاء Cross-Origin-Opener-Policy
        // النافذة لا تزال مفتوحة لكن لا يمكن الوصول إليها
        console.debug('Cannot access window.closed due to CORS policy - this is normal');
      }
    }, 2000); // فحص كل ثانيتين بدلاً من كل ثانية لتقليل الضغط
  }

  /**
   * إعداد timeout للعملية
   */
  private setupTimeout(): void {
    this.timeoutId = setTimeout(() => {
      if (!this.isCompleted) {
        this.handleError('انتهت مهلة تسجيل الدخول. يرجى المحاولة مرة أخرى.');
      }
    }, 300000); // 5 دقائق
  }

  /**
   * التحقق من صحة مصدر الرسالة
   */
  private isValidOrigin(origin: string): boolean {
    const validOrigins = [
      'http://localhost:8002',
      'http://127.0.0.1:8002',
      window.location.origin
    ];
    
    return validOrigins.includes(origin) || origin.includes('googleapis.com');
  }

  /**
   * الحصول على خصائص النافذة المنبثقة
   */
  private getWindowFeatures(): string {
    const width = 500;
    const height = 600;
    const left = window.screen.width / 2 - width / 2;
    const top = window.screen.height / 2 - height / 2;

    return [
      `width=${width}`,
      `height=${height}`,
      `left=${left}`,
      `top=${top}`,
      'scrollbars=yes',
      'resizable=yes',
      'status=no',
      'toolbar=no',
      'menubar=no',
      'location=no'
    ].join(',');
  }

  /**
   * معالجة النجاح
   */
  private handleSuccess(): void {
    if (this.isCompleted) return;
    
    this.isCompleted = true;
    this.cleanup();
    this.config.onSuccess();
  }

  /**
   * معالجة الخطأ
   */
  private handleError(error: string): void {
    if (this.isCompleted) return;
    
    this.isCompleted = true;
    this.cleanup();
    this.config.onError(error);
  }

  /**
   * معالجة الإلغاء
   */
  private handleCancel(): void {
    if (this.isCompleted) return;
    
    this.isCompleted = true;
    this.cleanup();
    this.config.onCancel();
  }

  /**
   * تنظيف الموارد
   */
  private cleanup(): void {
    // إزالة مستمع الرسائل
    if (this.messageListener) {
      window.removeEventListener('message', this.messageListener);
      this.messageListener = null;
    }

    // إلغاء timeout
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }

    // إلغاء interval
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }

    // إغلاق النافذة إذا كانت مفتوحة
    if (this.authWindow && !this.authWindow.closed) {
      try {
        this.authWindow.close();
      } catch (error) {
        // تجاهل أخطاء إغلاق النافذة
        console.debug('Could not close auth window:', error);
      }
    }
    
    this.authWindow = null;
  }

  /**
   * إلغاء العملية يدوياً
   */
  public cancel(): void {
    this.handleCancel();
  }
}
