/**
 * خدمة طلبات التحويل
 * تطبق مبادئ البرمجة الكائنية مع معالجة شاملة للأخطاء
 */

import axios, { AxiosResponse } from 'axios';

// Types
export type TransferStatus = 'PENDING' | 'APPROVED' | 'IN_TRANSIT' | 'COMPLETED' | 'CANCELLED';

export interface TransferRequestItem {
  id?: number;
  transfer_request_id?: number;
  product_id: number;
  product_name?: string;
  product_barcode?: string;
  requested_quantity: number;
  approved_quantity?: number;
  transferred_quantity?: number;
  unit_cost?: number;
  notes?: string;
}

export interface TransferRequest {
  id?: number;
  request_number?: string;
  from_warehouse_id: number;
  to_warehouse_id: number;
  status?: TransferStatus;
  requested_by?: number;
  approved_by?: number;
  notes?: string;
  requested_at?: string;
  approved_at?: string;
  completed_at?: string;
  items?: TransferRequestItem[];
  from_warehouse_name?: string;
  from_warehouse_code?: string;
  to_warehouse_name?: string;
  to_warehouse_code?: string;
  requested_by_username?: string;
  approved_by_username?: string;
  items_count?: number;
}

export interface TransferRequestCreate {
  from_warehouse_id: number;
  to_warehouse_id: number;
  notes?: string;
  items: TransferRequestItem[];
}

export interface TransferRequestFilters {
  status?: TransferStatus;
  from_warehouse_id?: number;
  to_warehouse_id?: number;
  date_from?: string;
  date_to?: string;
  page?: number;
  per_page?: number;
}

export interface TransferApproval {
  items?: Array<{
    product_id: number;
    approved_quantity: number;
  }>;
}

export interface ApiResponse<T> {
  success: boolean;
  error?: string;
  message?: string;
  transfer_request?: T;
  transfers?: T[];
  pending_transfers?: T[];
  pagination?: {
    page: number;
    per_page: number;
    total_count: number;
    total_pages: number;
  };
  total_count?: number;
}

/**
 * خدمة طلبات التحويل
 * تطبق مبادئ البرمجة الكائنية مع نمط Singleton
 */
export class TransferRequestService {
  private static instance: TransferRequestService;
  private readonly baseURL: string;

  private constructor() {
    this.baseURL = '/api/transfer-requests';
  }

  /**
   * الحصول على instance وحيد من الخدمة
   */
  public static getInstance(): TransferRequestService {
    if (!TransferRequestService.instance) {
      TransferRequestService.instance = new TransferRequestService();
    }
    return TransferRequestService.instance;
  }

  /**
   * إنشاء طلب تحويل جديد
   */
  public async createTransferRequest(requestData: TransferRequestCreate): Promise<ApiResponse<TransferRequest>> {
    try {
      const response: AxiosResponse<ApiResponse<TransferRequest>> = await axios.post(
        this.baseURL,
        requestData
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في إنشاء طلب التحويل:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في إنشاء طلب التحويل'
      };
    }
  }

  /**
   * الحصول على طلبات التحويل المعلقة
   */
  public async getPendingTransfers(): Promise<ApiResponse<TransferRequest[]>> {
    try {
      const response: AxiosResponse<ApiResponse<TransferRequest[]>> = await axios.get(
        `${this.baseURL}/pending`
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في جلب طلبات التحويل المعلقة:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في جلب طلبات التحويل المعلقة'
      };
    }
  }

  /**
   * الحصول على تاريخ طلبات التحويل
   */
  public async getTransferHistory(filters?: TransferRequestFilters): Promise<ApiResponse<TransferRequest[]>> {
    try {
      const params = new URLSearchParams();
      
      if (filters) {
        if (filters.status) params.append('status_filter', filters.status);
        if (filters.from_warehouse_id) params.append('from_warehouse_id', filters.from_warehouse_id.toString());
        if (filters.to_warehouse_id) params.append('to_warehouse_id', filters.to_warehouse_id.toString());
        if (filters.date_from) params.append('date_from', filters.date_from);
        if (filters.date_to) params.append('date_to', filters.date_to);
        if (filters.page) params.append('page', filters.page.toString());
        if (filters.per_page) params.append('per_page', filters.per_page.toString());
      }

      const url = `${this.baseURL}/history${params.toString() ? '?' + params.toString() : ''}`;
      const response: AxiosResponse<ApiResponse<TransferRequest[]>> = await axios.get(url);
      return response.data;
    } catch (error: any) {
      console.error('خطأ في جلب تاريخ طلبات التحويل:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في جلب تاريخ طلبات التحويل'
      };
    }
  }

  /**
   * الموافقة على طلب التحويل
   */
  public async approveTransferRequest(
    requestId: number,
    approvalData?: TransferApproval
  ): Promise<ApiResponse<TransferRequest>> {
    try {
      const response: AxiosResponse<ApiResponse<TransferRequest>> = await axios.post(
        `${this.baseURL}/${requestId}/approve`,
        approvalData || {}
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في الموافقة على طلب التحويل:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في الموافقة على طلب التحويل'
      };
    }
  }

  /**
   * معالجة التحويل (تغيير الحالة إلى في الطريق)
   */
  public async processTransfer(requestId: number): Promise<ApiResponse<TransferRequest>> {
    try {
      const response: AxiosResponse<ApiResponse<TransferRequest>> = await axios.post(
        `${this.baseURL}/${requestId}/process`
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في معالجة طلب التحويل:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في معالجة طلب التحويل'
      };
    }
  }

  /**
   * إكمال التحويل
   */
  public async completeTransfer(requestId: number): Promise<ApiResponse<TransferRequest>> {
    try {
      const response: AxiosResponse<ApiResponse<TransferRequest>> = await axios.post(
        `${this.baseURL}/${requestId}/complete`
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في إكمال طلب التحويل:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في إكمال طلب التحويل'
      };
    }
  }

  /**
   * إلغاء طلب التحويل
   */
  public async cancelTransferRequest(requestId: number, reason: string): Promise<ApiResponse<TransferRequest>> {
    try {
      const response: AxiosResponse<ApiResponse<TransferRequest>> = await axios.post(
        `${this.baseURL}/${requestId}/cancel?reason=${encodeURIComponent(reason)}`
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في إلغاء طلب التحويل:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في إلغاء طلب التحويل'
      };
    }
  }

  /**
   * التحقق من صحة بيانات طلب التحويل
   */
  public validateTransferRequestData(data: TransferRequestCreate): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // التحقق من المستودعات
    if (!data.from_warehouse_id || data.from_warehouse_id <= 0) {
      errors.push('مستودع المصدر مطلوب');
    }

    if (!data.to_warehouse_id || data.to_warehouse_id <= 0) {
      errors.push('مستودع الوجهة مطلوب');
    }

    if (data.from_warehouse_id === data.to_warehouse_id) {
      errors.push('لا يمكن التحويل من وإلى نفس المستودع');
    }

    // التحقق من العناصر
    if (!data.items || data.items.length === 0) {
      errors.push('يجب إضافة عنصر واحد على الأقل للطلب');
    } else {
      data.items.forEach((item, index) => {
        if (!item.product_id || item.product_id <= 0) {
          errors.push(`معرف المنتج مطلوب للعنصر ${index + 1}`);
        }

        if (!item.requested_quantity || item.requested_quantity <= 0) {
          errors.push(`الكمية المطلوبة يجب أن تكون أكبر من صفر للعنصر ${index + 1}`);
        }

        if (item.unit_cost !== undefined && item.unit_cost < 0) {
          errors.push(`تكلفة الوحدة يجب أن تكون أكبر من أو تساوي صفر للعنصر ${index + 1}`);
        }
      });
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * تنسيق بيانات طلب التحويل للعرض
   */
  public formatTransferRequestForDisplay(request: TransferRequest): any {
    return {
      ...request,
      status_text: this.getStatusText(request.status || 'PENDING'),
      status_color: this.getStatusColor(request.status || 'PENDING'),
      formatted_requested_date: request.requested_at ? new Date(request.requested_at).toLocaleDateString('ar-EG') : '',
      formatted_approved_date: request.approved_at ? new Date(request.approved_at).toLocaleDateString('ar-EG') : '',
      formatted_completed_date: request.completed_at ? new Date(request.completed_at).toLocaleDateString('ar-EG') : '',
      can_approve: request.status === 'PENDING',
      can_process: request.status === 'APPROVED',
      can_complete: request.status === 'IN_TRANSIT',
      can_cancel: request.status === 'PENDING' || request.status === 'APPROVED',
      total_items: request.items?.length || request.items_count || 0,
      total_requested_quantity: request.items?.reduce((sum, item) => sum + item.requested_quantity, 0) || 0
    };
  }

  /**
   * الحصول على نص الحالة
   */
  private getStatusText(status: TransferStatus): string {
    switch (status) {
      case 'PENDING': return 'معلق';
      case 'APPROVED': return 'موافق عليه';
      case 'IN_TRANSIT': return 'في الطريق';
      case 'COMPLETED': return 'مكتمل';
      case 'CANCELLED': return 'ملغي';
      default: return 'غير محدد';
    }
  }

  /**
   * الحصول على لون الحالة
   */
  private getStatusColor(status: TransferStatus): string {
    switch (status) {
      case 'PENDING': return 'orange';
      case 'APPROVED': return 'blue';
      case 'IN_TRANSIT': return 'purple';
      case 'COMPLETED': return 'green';
      case 'CANCELLED': return 'red';
      default: return 'gray';
    }
  }

  /**
   * حساب إحصائيات طلبات التحويل
   */
  public calculateTransferStats(transfers: TransferRequest[]): any {
    const stats = {
      total_requests: transfers.length,
      pending: transfers.filter(t => t.status === 'PENDING').length,
      approved: transfers.filter(t => t.status === 'APPROVED').length,
      in_transit: transfers.filter(t => t.status === 'IN_TRANSIT').length,
      completed: transfers.filter(t => t.status === 'COMPLETED').length,
      cancelled: transfers.filter(t => t.status === 'CANCELLED').length,
      total_items: transfers.reduce((sum, t) => sum + (t.items_count || 0), 0),
      completion_rate: 0
    };

    if (stats.total_requests > 0) {
      stats.completion_rate = Math.round((stats.completed / stats.total_requests) * 100);
    }

    return stats;
  }

  /**
   * تجميع العناصر المتشابهة في طلب التحويل
   */
  public consolidateTransferItems(items: TransferRequestItem[]): TransferRequestItem[] {
    const consolidated = new Map<number, TransferRequestItem>();

    items.forEach(item => {
      const existing = consolidated.get(item.product_id);
      if (existing) {
        existing.requested_quantity += item.requested_quantity;
        if (item.approved_quantity) {
          existing.approved_quantity = (existing.approved_quantity || 0) + item.approved_quantity;
        }
      } else {
        consolidated.set(item.product_id, { ...item });
      }
    });

    return Array.from(consolidated.values());
  }
}

// تصدير instance وحيد
export const transferRequestService = TransferRequestService.getInstance();
