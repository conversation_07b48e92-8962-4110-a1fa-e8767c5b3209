/**
 * خدمة تصدير PDF من الخلفية
 * تستخدم API الخلفية لإنشاء ملفات PDF عالية الجودة مع دعم متقدم للغة العربية
 * تطبق مبادئ البرمجة الكائنية وتدمج مع خدمات النظام الموجودة
 *
 * <AUTHOR> Team
 * @version 1.0.0
 * @date 2025-06-30
 */

import api from '../lib/axios';
import { formatDateTime, getCurrentTripoliDateTime } from './dateTimeService';

/**
 * واجهة إعدادات تصدير PDF من الخلفية
 */
export interface ServerPDFExportOptions {
  title?: string;
  filename?: string;
  config?: {
    company_name?: string;
    theme?: 'light' | 'dark';
    include_header?: boolean;
    include_footer?: boolean;
    page_size?: 'A4' | 'A3' | 'Letter';
    orientation?: 'portrait' | 'landscape';
  };
}

/**
 * واجهة استجابة محركات PDF
 */
export interface PDFEnginesResponse {
  engines: {
    [key: string]: {
      available: boolean;
      description: string;
      priority: number;
    };
  };
  preferred_engine: string;
  total_available: number;
}

/**
 * كلاس خدمة تصدير PDF من الخلفية
 * يوفر واجهة موحدة للتعامل مع API الخلفية لتصدير PDF
 */
export class ServerPDFExportService {
  private baseURL: string;
  private defaultOptions: ServerPDFExportOptions;

  constructor(options: ServerPDFExportOptions = {}) {
    this.baseURL = '/api/pdf';
    this.defaultOptions = {
      config: {
        company_name: 'نظام SmartPOS المتقدم',
        theme: 'light',
        include_header: true,
        include_footer: true,
        page_size: 'A4',
        orientation: 'portrait'
      },
      ...options
    };
  }

  /**
   * تصدير سجل وصول الجهاز إلى PDF من الخلفية
   */
  public async exportDeviceAccessLog(
    deviceId: string,
    options: ServerPDFExportOptions = {}
  ): Promise<void> {
    try {
      console.log(`🚀 بدء تصدير سجل وصول الجهاز من الخلفية: ${deviceId}`);

      // دمج الإعدادات
      const mergedOptions = {
        ...this.defaultOptions,
        ...options,
        config: {
          ...this.defaultOptions.config,
          ...options.config
        }
      };

      // إرسال طلب إلى الخلفية
      const response = await api.post(
        `${this.baseURL}/device-access-log/${deviceId}`,
        mergedOptions,
        {
          responseType: 'blob', // مهم لتحميل الملفات
          headers: {
            'Accept': 'application/pdf, application/json'
          }
        }
      );

      // التحقق من نوع الاستجابة
      const contentType = response.headers['content-type'] || '';
      
      if (contentType.includes('application/pdf')) {
        // تحميل ملف PDF
        await this._downloadFile(response.data, `device_access_log_${deviceId}.pdf`, 'application/pdf');
        console.log('✅ تم تحميل PDF بنجاح من الخلفية');
      } else if (contentType.includes('application/json')) {
        // تحميل ملف JSON (طريقة احتياطية)
        await this._downloadFile(response.data, `device_access_log_${deviceId}.json`, 'application/json');
        console.log('⚠️ تم تحميل JSON كبديل لـ PDF');
      } else {
        throw new Error('نوع ملف غير مدعوم من الخلفية');
      }

    } catch (error: any) {
      console.error('❌ خطأ في تصدير PDF من الخلفية:', error);
      
      // معالجة أخطاء مختلفة
      if (error.response?.status === 404) {
        throw new Error('الجهاز غير موجود');
      } else if (error.response?.status === 500) {
        throw new Error('خطأ في الخادم أثناء إنشاء PDF');
      } else if (error.code === 'NETWORK_ERROR') {
        throw new Error('خطأ في الاتصال بالخادم');
      } else {
        throw new Error(`فشل في تصدير PDF: ${error.message}`);
      }
    }
  }

  /**
   * تصدير تقرير مخصص إلى PDF
   */
  public async exportCustomReport(
    title: string,
    data: any[],
    additionalInfo?: any,
    options: ServerPDFExportOptions = {}
  ): Promise<void> {
    try {
      console.log(`🚀 بدء تصدير تقرير مخصص من الخلفية: ${title}`);

      const mergedOptions = {
        ...this.defaultOptions,
        ...options,
        config: {
          ...this.defaultOptions.config,
          ...options.config
        }
      };

      const requestData = {
        title,
        data,
        additional_info: additionalInfo,
        config: mergedOptions.config
      };

      const response = await api.post(
        `${this.baseURL}/custom-report`,
        requestData,
        {
          responseType: 'blob',
          headers: {
            'Accept': 'application/pdf, application/json'
          }
        }
      );

      const contentType = response.headers['content-type'] || '';
      const timestamp = formatDateTime(getCurrentTripoliDateTime(), 'date');
      const filename = options.filename || `${title.replace(/\s+/g, '-')}_${timestamp}`;

      if (contentType.includes('application/pdf')) {
        await this._downloadFile(response.data, `${filename}.pdf`, 'application/pdf');
        console.log('✅ تم تحميل التقرير المخصص PDF بنجاح');
      } else {
        await this._downloadFile(response.data, `${filename}.json`, 'application/json');
        console.log('⚠️ تم تحميل التقرير المخصص JSON كبديل');
      }

    } catch (error: any) {
      console.error('❌ خطأ في تصدير التقرير المخصص:', error);
      throw new Error(`فشل في تصدير التقرير: ${error.message}`);
    }
  }

  /**
   * اختبار محركات PDF المتاحة
   */
  public async testPDFEngines(): Promise<any> {
    try {
      console.log('🔧 اختبار محركات PDF المتاحة...');

      const response = await api.get(`${this.baseURL}/test-pdf-engines`);
      
      console.log('✅ نتائج اختبار محركات PDF:', response.data);
      return response.data;

    } catch (error: any) {
      console.error('❌ خطأ في اختبار محركات PDF:', error);
      throw new Error(`فشل في اختبار محركات PDF: ${error.message}`);
    }
  }

  /**
   * الحصول على محركات PDF المتاحة
   */
  public async getAvailableEngines(): Promise<PDFEnginesResponse> {
    try {
      console.log('📋 جلب محركات PDF المتاحة...');

      const response = await api.get(`${this.baseURL}/available-engines`);
      
      console.log('✅ محركات PDF المتاحة:', response.data);
      return response.data;

    } catch (error: any) {
      console.error('❌ خطأ في جلب محركات PDF:', error);
      return {
        engines: {},
        preferred_engine: 'fallback',
        total_available: 0
      };
    }
  }

  /**
   * تحميل ملف من البيانات
   */
  private async _downloadFile(
    data: Blob,
    filename: string,
    mimeType: string
  ): Promise<void> {
    try {
      // إنشاء Blob مع البيانات
      const blob = new Blob([data], { type: mimeType });

      // إنشاء URL آمن للملف
      const url = window.URL.createObjectURL(blob);

      // إنشاء رابط تحميل مع إعدادات أمان
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.rel = 'noopener noreferrer'; // إضافة أمان إضافي
      link.target = '_blank'; // فتح في نافذة جديدة

      // إضافة الرابط إلى DOM وتفعيله
      document.body.appendChild(link);

      // محاولة التحميل المباشر أولاً
      try {
        link.click();
        console.log(`📥 تم تحميل الملف: ${filename}`);
      } catch (clickError) {
        console.warn('⚠️ فشل التحميل المباشر، محاولة بديلة...');

        // طريقة بديلة للتحميل
        const downloadEvent = new MouseEvent('click', {
          view: window,
          bubbles: true,
          cancelable: true
        });
        link.dispatchEvent(downloadEvent);
      }

      // تنظيف الموارد بعد تأخير قصير
      setTimeout(() => {
        try {
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
        } catch (cleanupError) {
          console.warn('⚠️ تحذير في تنظيف الموارد:', cleanupError);
        }
      }, 100);

    } catch (error) {
      console.error('❌ خطأ في تحميل الملف:', error);
      throw new Error(`فشل في تحميل الملف: ${filename}`);
    }
  }

  /**
   * التحقق من حالة خدمة PDF
   */
  public async checkServiceHealth(): Promise<boolean> {
    try {
      const engines = await this.getAvailableEngines();
      return engines.total_available > 0;
    } catch (error) {
      console.error('❌ خطأ في فحص حالة خدمة PDF:', error);
      return false;
    }
  }

  /**
   * تنظيف الموارد
   */
  public cleanup(): void {
    console.log('🧹 تم تنظيف موارد خدمة PDF الخلفية');
  }
}

/**
 * دالة مساعدة لتصدير سريع لسجل وصول الجهاز
 */
export const exportDeviceAccessLogFromServer = async (
  deviceId: string,
  options: ServerPDFExportOptions = {}
): Promise<void> => {
  const service = new ServerPDFExportService(options);
  
  try {
    await service.exportDeviceAccessLog(deviceId, options);
  } finally {
    service.cleanup();
  }
};

/**
 * دالة مساعدة لتصدير تقرير مخصص
 */
export const exportCustomReportFromServer = async (
  title: string,
  data: any[],
  additionalInfo?: any,
  options: ServerPDFExportOptions = {}
): Promise<void> => {
  const service = new ServerPDFExportService(options);
  
  try {
    await service.exportCustomReport(title, data, additionalInfo, options);
  } finally {
    service.cleanup();
  }
};

/**
 * دالة مساعدة لاختبار محركات PDF
 */
export const testServerPDFEngines = async (): Promise<any> => {
  const service = new ServerPDFExportService();
  
  try {
    return await service.testPDFEngines();
  } finally {
    service.cleanup();
  }
};
