import apiClient from '../lib/axios';
import errorLogger from './errorLogger';

export interface TaskError {
  id: string;
  taskId: number;
  taskName: string;
  errorMessage: string;
  errorDetails?: string;
  stackTrace?: string;
  timestamp: string;
  severity: 'WARNING' | 'ERROR' | 'CRITICAL';
  category: 'EXECUTION' | 'CONFIGURATION' | 'SYSTEM' | 'NETWORK';
  resolved: boolean;
  resolutionNotes?: string;
  logId?: number; // ربط مع سجل الأخطاء
}

export interface TaskErrorStats {
  totalErrors: number;
  unresolvedErrors: number;
  criticalErrors: number;
  errorsByCategory: Record<string, number>;
  errorsByTask: Record<string, number>;
}

class ScheduledTaskErrorService {
  private taskErrors: Map<string, TaskError> = new Map();
  private errorListeners: ((errors: TaskError[]) => void)[] = [];

  /**
   * تسجيل خطأ مهمة مجدولة في نظام السجلات
   */
  async logTaskError(
    taskId: number,
    taskName: string,
    errorMessage: string,
    errorDetails?: any,
    severity: TaskError['severity'] = 'ERROR',
    category: TaskError['category'] = 'EXECUTION'
  ): Promise<string> {
    try {
      // إنشاء معرف فريد للخطأ
      const errorId = `task_${taskId}_${Date.now()}`;
      
      // تحديد مستوى السجل
      const logLevel = severity === 'CRITICAL' ? 'CRITICAL' : 
                      severity === 'ERROR' ? 'ERROR' : 'WARNING';

      // تسجيل الخطأ في نظام السجلات
      const logDetails = {
        taskId,
        taskName,
        category,
        severity,
        errorDetails: typeof errorDetails === 'object' ? JSON.stringify(errorDetails) : errorDetails,
        timestamp: new Date().toISOString()
      };

      // تسجيل في نظام السجلات
      errorLogger.logError(
        logLevel,
        'SYSTEM',
        `خطأ في المهمة المجدولة: ${taskName} - ${errorMessage}`,
        logDetails
      );

      // إنشاء كائن الخطأ
      const taskError: TaskError = {
        id: errorId,
        taskId,
        taskName,
        errorMessage,
        errorDetails: typeof errorDetails === 'string' ? errorDetails : JSON.stringify(errorDetails),
        timestamp: new Date().toISOString(),
        severity,
        category,
        resolved: false
      };

      // حفظ الخطأ محلياً
      this.taskErrors.set(errorId, taskError);

      // إشعار المستمعين
      this.notifyListeners();

      // إرسال إلى الخادم
      try {
        const response = await apiClient.post('/api/system/logs', {
          level: logLevel,
          source: 'SYSTEM',
          message: `خطأ في المهمة المجدولة: ${taskName} - ${errorMessage}`,
          details: JSON.stringify(logDetails)
        });

        // ربط معرف السجل
        if (response.data?.id) {
          taskError.logId = response.data.id;
          this.taskErrors.set(errorId, taskError);
        }
      } catch (apiError) {
        console.error('فشل في إرسال خطأ المهمة إلى الخادم:', apiError);
      }

      return errorId;
    } catch (error) {
      console.error('خطأ في تسجيل خطأ المهمة:', error);
      throw error;
    }
  }

  /**
   * حل خطأ مهمة وإزالته من العرض
   */
  async resolveTaskError(errorId: string, resolutionNotes?: string): Promise<void> {
    try {
      const taskError = this.taskErrors.get(errorId);
      if (!taskError) {
        throw new Error('خطأ المهمة غير موجود');
      }

      // تحديث حالة الخطأ
      taskError.resolved = true;
      taskError.resolutionNotes = resolutionNotes;
      this.taskErrors.set(errorId, taskError);

      // تحديث السجل في الخادم إذا كان مربوطاً
      if (taskError.logId) {
        try {
          await apiClient.put(`/api/system/logs/${taskError.logId}/resolve`, {
            resolution_notes: resolutionNotes || 'تم حل المشكلة'
          });
        } catch (apiError) {
          console.error('فشل في تحديث حالة السجل في الخادم:', apiError);
        }
      }

      // تسجيل حل المشكلة
      errorLogger.logInfo(
        `تم حل خطأ المهمة: ${taskError.taskName}`,
        {
          errorId,
          taskId: taskError.taskId,
          resolutionNotes,
          originalError: taskError.errorMessage
        }
      );

      // إشعار المستمعين
      this.notifyListeners();
    } catch (error) {
      console.error('خطأ في حل خطأ المهمة:', error);
      throw error;
    }
  }

  /**
   * حذف خطأ مهمة نهائياً
   */
  async deleteTaskError(errorId: string): Promise<void> {
    try {
      const taskError = this.taskErrors.get(errorId);
      if (!taskError) {
        return;
      }

      // حذف من الذاكرة
      this.taskErrors.delete(errorId);

      // تسجيل الحذف
      errorLogger.logInfo(
        `تم حذف خطأ المهمة: ${taskError.taskName}`,
        {
          errorId,
          taskId: taskError.taskId,
          originalError: taskError.errorMessage
        }
      );

      // إشعار المستمعين
      this.notifyListeners();
    } catch (error) {
      console.error('خطأ في حذف خطأ المهمة:', error);
      throw error;
    }
  }

  /**
   * الحصول على جميع أخطاء المهام
   */
  getTaskErrors(): TaskError[] {
    return Array.from(this.taskErrors.values()).sort(
      (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  }

  /**
   * الحصول على أخطاء مهمة محددة
   */
  getTaskErrorsById(taskId: number): TaskError[] {
    return this.getTaskErrors().filter(error => error.taskId === taskId);
  }

  /**
   * الحصول على الأخطاء غير المحلولة
   */
  getUnresolvedErrors(): TaskError[] {
    return this.getTaskErrors().filter(error => !error.resolved);
  }

  /**
   * الحصول على إحصائيات الأخطاء
   */
  getErrorStats(): TaskErrorStats {
    const errors = this.getTaskErrors();
    const unresolvedErrors = errors.filter(e => !e.resolved);
    const criticalErrors = errors.filter(e => e.severity === 'CRITICAL');

    const errorsByCategory: Record<string, number> = {};
    const errorsByTask: Record<string, number> = {};

    errors.forEach(error => {
      errorsByCategory[error.category] = (errorsByCategory[error.category] || 0) + 1;
      errorsByTask[error.taskName] = (errorsByTask[error.taskName] || 0) + 1;
    });

    return {
      totalErrors: errors.length,
      unresolvedErrors: unresolvedErrors.length,
      criticalErrors: criticalErrors.length,
      errorsByCategory,
      errorsByTask
    };
  }

  /**
   * إضافة مستمع للتغييرات
   */
  addErrorListener(listener: (errors: TaskError[]) => void): () => void {
    this.errorListeners.push(listener);
    return () => {
      const index = this.errorListeners.indexOf(listener);
      if (index > -1) {
        this.errorListeners.splice(index, 1);
      }
    };
  }

  /**
   * إشعار جميع المستمعين
   */
  private notifyListeners(): void {
    const errors = this.getTaskErrors();
    this.errorListeners.forEach(listener => {
      try {
        listener(errors);
      } catch (error) {
        console.error('خطأ في إشعار مستمع الأخطاء:', error);
      }
    });
  }

  /**
   * تصنيف الخطأ تلقائياً
   */
  categorizeError(errorMessage: string): TaskError['category'] {
    const message = errorMessage.toLowerCase();
    
    if (message.includes('network') || message.includes('connection') || message.includes('timeout')) {
      return 'NETWORK';
    }
    if (message.includes('config') || message.includes('parameter') || message.includes('setting')) {
      return 'CONFIGURATION';
    }
    if (message.includes('system') || message.includes('permission') || message.includes('access')) {
      return 'SYSTEM';
    }
    
    return 'EXECUTION';
  }

  /**
   * تحديد شدة الخطأ تلقائياً
   */
  determineSeverity(errorMessage: string, failureCount: number): TaskError['severity'] {
    const message = errorMessage.toLowerCase();
    
    // أخطاء حرجة
    if (message.includes('critical') || message.includes('fatal') || failureCount >= 5) {
      return 'CRITICAL';
    }
    
    // تحذيرات
    if (message.includes('warning') || message.includes('timeout') || failureCount <= 2) {
      return 'WARNING';
    }
    
    return 'ERROR';
  }
}

// إنشاء مثيل واحد للاستخدام في جميع أنحاء التطبيق
export const scheduledTaskErrorService = new ScheduledTaskErrorService();
export default scheduledTaskErrorService;
