/**
 * خدمة تحديث معلومات الأجهزة في الوقت الفعلي
 */

import api from '../lib/axios';

/**
 * تحديث معلومات المستخدم للجهاز الحالي
 * يتم استدعاؤها عند تسجيل الدخول/الخروج
 */
export const updateDeviceUser = async (): Promise<boolean> => {
  try {
    // إضافة معلومات إضافية للتحديث الفوري
    const headers: Record<string, string> = {};

    // محاولة الحصول على المستخدم الحالي
    try {
      const authData = localStorage.getItem('auth-storage');
      if (authData) {
        const parsedAuth = JSON.parse(authData);
        const currentUser = parsedAuth?.state?.user?.username;
        if (currentUser) {
          // تشفير النص العربي إلى base64 لتجنب مشاكل encoding في headers
          headers['X-Current-User'] = btoa(encodeURIComponent(currentUser));
        }
      }
    } catch (e) {
      console.debug('لا يمكن الحصول على معلومات المستخدم من localStorage');
    }

    // إضافة معلومات الجهاز للتتبع الدقيق
    headers['X-Device-Update-Type'] = 'user_change';
    headers['X-Device-Timestamp'] = new Date().toISOString();

    const response = await api.post('/api/settings/update-device-user', {}, { headers });

    if (response.data?.success) {
      console.log('✅ تم تحديث معلومات المستخدم للجهاز:', response.data);

      // إشعار النظام بالتحديث الفوري
      try {
        // إرسال حدث مخصص للتحديث الفوري
        window.dispatchEvent(new CustomEvent('deviceUserUpdated', {
          detail: {
            success: true,
            timestamp: new Date().toISOString(),
            user: response.data.current_user
          }
        }));
      } catch (eventError) {
        console.debug('خطأ في إرسال حدث التحديث:', eventError);
      }

      return true;
    } else {
      console.warn('⚠️ فشل في تحديث معلومات المستخدم للجهاز:', response.data);
      return false;
    }
  } catch (error: any) {
    console.error('❌ خطأ في تحديث معلومات المستخدم للجهاز:', error);
    return false;
  }
};

/**
 * تحديث نشاط الجهاز الحالي
 * يتم استدعاؤها لجعل الجهاز يظهر كـ online
 */
export const updateDeviceActivity = async (): Promise<boolean> => {
  try {
    // استدعاء endpoint الأجهزة المتصلة لتسجيل النشاط
    const response = await api.get('/api/settings/connected-devices');

    if (response.data?.success) {
      console.log('✅ تم تحديث نشاط الجهاز');
      return true;
    } else {
      console.warn('⚠️ فشل في تحديث نشاط الجهاز');
      return false;
    }
  } catch (error: any) {
    console.error('❌ خطأ في تحديث نشاط الجهاز:', error);
    return false;
  }
};

/**
 * تحديث شامل للجهاز (المستخدم + النشاط)
 */
export const updateDeviceComplete = async (): Promise<boolean> => {
  try {
    // تحديث معلومات المستخدم أولاً
    const userUpdateSuccess = await updateDeviceUser();

    // ثم تحديث النشاط
    const activityUpdateSuccess = await updateDeviceActivity();

    return userUpdateSuccess && activityUpdateSuccess;
  } catch (error: any) {
    console.error('❌ خطأ في التحديث الشامل للجهاز:', error);
    return false;
  }
};

/**
 * Hook لتحديث الجهاز تلقائياً عند تغيير حالة المصادقة
 */
export const useDeviceUpdate = () => {
  const updateOnAuthChange = async () => {
    try {
      await updateDeviceComplete();
    } catch (error) {
      console.error('خطأ في تحديث الجهاز عند تغيير المصادقة:', error);
    }
  };

  return {
    updateOnAuthChange,
    updateDeviceUser,
    updateDeviceActivity,
    updateDeviceComplete
  };
};

/**
 * بدء تحديث دوري لحالة الجهاز - للأجهزة البعيدة
 */
export const startPeriodicDeviceUpdate = (intervalMs: number = 30000): (() => void) => {
  let intervalId: NodeJS.Timeout;

  const updateLoop = async () => {
    try {
      await updateDeviceActivity();
    } catch (error) {
      console.debug('خطأ في التحديث الدوري للجهاز:', error);
    }
  };

  // تحديث فوري
  updateLoop();

  // تحديث دوري
  intervalId = setInterval(updateLoop, intervalMs);

  // إرجاع دالة الإيقاف
  return () => {
    if (intervalId) {
      clearInterval(intervalId);
    }
  };
};

export default {
  updateDeviceUser,
  updateDeviceActivity,
  updateDeviceComplete,
  useDeviceUpdate
};
