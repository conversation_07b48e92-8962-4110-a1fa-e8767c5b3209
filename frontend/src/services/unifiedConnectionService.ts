/**
 * خدمة الاتصال الموحدة - SmartPOS
 * خدمة شاملة ومحسنة لإدارة الاتصالات ومراقبة صحة الخادم والاستعادة
 */

import { timeoutManager } from '../utils/timeoutManager';
import { urlDetectionService } from './urlDetectionService';
import { appStateManager } from './appStateManager';

interface UnifiedConnectionState {
  isConnected: boolean;
  isHealthy: boolean;
  lastSuccessfulConnection: Date | null;
  lastHealthCheck: Date | null;
  failedAttempts: number;
  consecutiveFailures: number;
  currentBackendUrl: string | null;
  isRetrying: boolean;
  isRecovering: boolean;
  responseTime: number;
}

interface HealthMetrics {
  averageResponseTime: number;
  successRate: number;
  uptime: number;
  lastError: string | null;
}

interface NetworkStatus {
  isOnline: boolean;
  connectionType?: string;
  effectiveType?: string;
  downlink?: number;
  rtt?: number;
}

interface ConnectivityCheckResult {
  isConnected: boolean;
  latency?: number;
  error?: string;
}

class UnifiedConnectionService {
  private static instance: UnifiedConnectionService;
  private state: UnifiedConnectionState;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private activeControllers: Map<string, AbortController> = new Map();
  private isDestroyed: boolean = false;
  
  // إعدادات محسنة
  private readonly MAX_CONSECUTIVE_FAILURES = 5;
  private readonly HEALTH_CHECK_INTERVAL = 45000; // 45 ثانية
  private readonly HEALTH_CHECK_TIMEOUT = 8000; // 8 ثوانٍ
  private readonly RECOVERY_COOLDOWN = 30000; // 30 ثانية بين محاولات الاستعادة
  private readonly MAX_RETRY_ATTEMPTS = 3;

  private constructor() {
    this.state = {
      isConnected: false,
      isHealthy: true,
      lastSuccessfulConnection: null,
      lastHealthCheck: null,
      failedAttempts: 0,
      consecutiveFailures: 0,
      currentBackendUrl: null,
      isRetrying: false,
      isRecovering: false,
      responseTime: 0
    };

    this.startHealthMonitoring();
  }

  public static getInstance(): UnifiedConnectionService {
    if (!UnifiedConnectionService.instance) {
      UnifiedConnectionService.instance = new UnifiedConnectionService();
    }
    return UnifiedConnectionService.instance;
  }

  /**
   * بدء مراقبة صحة الاتصال
   */
  private startHealthMonitoring(): void {
    if (this.isDestroyed || this.healthCheckInterval) {
      return;
    }

    console.log('🔄 [UNIFIED] بدء مراقبة الاتصال الموحدة...');
    
    // فحص فوري
    this.performHealthCheck();
    
    // فحص دوري
    this.healthCheckInterval = setInterval(() => {
      if (!this.isDestroyed && !this.state.isRetrying) {
        this.performHealthCheck();
      }
    }, this.HEALTH_CHECK_INTERVAL);
  }

  /**
   * فحص صحة الخادم المحسن
   */
  private async performHealthCheck(): Promise<boolean> {
    if (this.isDestroyed) {
      return false;
    }

    const startTime = Date.now();
    const operationId = `unified-health-${Date.now()}`;

    try {
      const { controller, cleanup } = timeoutManager.createOperation(
        operationId,
        this.HEALTH_CHECK_TIMEOUT,
        () => {
          console.warn('⏰ [UNIFIED] Health check timeout');
          this.handleHealthCheckFailure();
        }
      );

      this.activeControllers.set(operationId, controller);

      const backendURL = await urlDetectionService.getBackendURL();
      const response = await fetch(`${backendURL}/api/system/health`, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      cleanup();
      this.activeControllers.delete(operationId);

      const responseTime = Date.now() - startTime;

      if (response.ok) {
        this.handleHealthCheckSuccess(responseTime, backendURL);
        return true;
      } else {
        console.warn(`🔴 [UNIFIED] Health check failed with status: ${response.status}`);
        this.handleHealthCheckFailure();
        return false;
      }

    } catch (error: any) {
      this.activeControllers.delete(operationId);
      
      if (error.name !== 'AbortError') {
        console.error('❌ [UNIFIED] Health check error:', error.message);
      }
      
      this.handleHealthCheckFailure();
      return false;
    }
  }

  /**
   * معالجة نجاح فحص الصحة
   */
  private handleHealthCheckSuccess(responseTime: number, backendURL: string): void {
    const wasConnected = this.state.isConnected;

    this.state.isConnected = true;
    this.state.isHealthy = true;
    this.state.lastSuccessfulConnection = new Date();
    this.state.lastHealthCheck = new Date();
    this.state.failedAttempts = 0;
    this.state.consecutiveFailures = 0;

    // تحديث حالة التطبيق إذا تغيرت الحالة
    if (!wasConnected) {
      appStateManager.updateConnectionStatus(true, 'unified-connection');
    }
    this.state.currentBackendUrl = backendURL;
    this.state.responseTime = responseTime;
    this.state.isRecovering = false;

    console.log(`✅ [UNIFIED] Server healthy - Response time: ${responseTime}ms`);
  }

  /**
   * معالجة فشل فحص الصحة
   */
  private handleHealthCheckFailure(): void {
    const wasConnected = this.state.isConnected;

    this.state.isConnected = false;
    this.state.isHealthy = false;
    this.state.lastHealthCheck = new Date();
    this.state.failedAttempts++;
    this.state.consecutiveFailures++;

    // تحديث حالة التطبيق إذا تغيرت الحالة
    if (wasConnected) {
      appStateManager.updateConnectionStatus(false, 'unified-connection');
    }

    console.warn(`🔴 [UNIFIED] Health check failed (${this.state.consecutiveFailures}/${this.MAX_CONSECUTIVE_FAILURES})`);

    // بدء الاستعادة إذا تجاوز الحد المسموح
    if (this.state.consecutiveFailures >= this.MAX_CONSECUTIVE_FAILURES && !this.state.isRecovering) {
      this.initiateRecovery();
    }
  }

  /**
   * بدء عملية الاستعادة الذكية
   */
  private async initiateRecovery(): Promise<void> {
    if (this.state.isRecovering) {
      return;
    }

    this.state.isRecovering = true;
    console.warn('🚨 [UNIFIED] Server appears frozen, initiating recovery...');
    console.info('💡 تأكد من أن الخادم الخلفي يعمل على المنفذ 8002');
    console.info('💡 يمكنك تشغيله بالأمر: cd backend && python main.py');

    try {
      // تنظيف جميع العمليات المعلقة
      this.cleanupAllControllers();

      // انتظار قصير قبل المحاولة
      await this.sleep(3000);

      // محاولة إعادة الاتصال
      const recovered = await this.attemptRecovery();

      if (recovered) {
        console.log('✅ [UNIFIED] Server recovery successful');
        this.state.isRecovering = false;
      } else {
        console.warn('❌ [UNIFIED] Server recovery failed');
        // إعادة تعيين العدادات لمنع التكرار المفرط
        this.state.consecutiveFailures = Math.floor(this.MAX_CONSECUTIVE_FAILURES / 2);
        
        // انتظار cooldown قبل المحاولة التالية
        setTimeout(() => {
          this.state.isRecovering = false;
        }, this.RECOVERY_COOLDOWN);
      }

    } catch (error) {
      console.error('❌ [UNIFIED] Recovery attempt failed:', error);
      this.state.isRecovering = false;
    }
  }

  /**
   * محاولة استعادة الاتصال
   */
  private async attemptRecovery(): Promise<boolean> {
    console.log('🔄 [UNIFIED] Attempting connection recovery...');

    for (let attempt = 1; attempt <= this.MAX_RETRY_ATTEMPTS; attempt++) {
      console.log(`🔄 [UNIFIED] Recovery attempt ${attempt}/${this.MAX_RETRY_ATTEMPTS}`);

      try {
        // محاولة إعادة كشف URL الخادم
        await urlDetectionService.getBackendURL();
        
        // فحص الصحة
        const isHealthy = await this.performHealthCheck();
        
        if (isHealthy) {
          console.log(`✅ [UNIFIED] Recovery successful on attempt ${attempt}`);
          return true;
        }

        // انتظار بين المحاولات
        if (attempt < this.MAX_RETRY_ATTEMPTS) {
          await this.sleep(2000 * attempt); // انتظار متزايد
        }

      } catch (error) {
        console.warn(`❌ [UNIFIED] Recovery attempt ${attempt} failed:`, error);
      }
    }

    return false;
  }

  /**
   * إعادة محاولة الاتصال (للاستخدام الخارجي)
   */
  public async retryConnection(): Promise<boolean> {
    if (this.state.isRetrying) {
      return false;
    }

    this.state.isRetrying = true;
    console.log('🔄 [UNIFIED] Manual connection retry...');

    try {
      const success = await this.attemptRecovery();
      return success;
    } finally {
      this.state.isRetrying = false;
    }
  }

  /**
   * الحصول على أفضل URL للخادم
   */
  public async getBestBackendUrl(): Promise<string> {
    try {
      if (this.state.currentBackendUrl && this.state.isConnected) {
        return this.state.currentBackendUrl;
      }

      const detectedUrl = await urlDetectionService.getBackendURL();
      return detectedUrl;
    } catch (error) {
      console.error('❌ [UNIFIED] Failed to get backend URL:', error);
      return 'http://localhost:8002'; // fallback
    }
  }

  /**
   * تنظيف جميع العمليات المعلقة
   */
  private cleanupAllControllers(): void {
    console.log(`🧹 [UNIFIED] Cleaning up ${this.activeControllers.size} active controllers`);
    
    for (const [operationId, controller] of this.activeControllers) {
      try {
        if (!controller.signal.aborted) {
          controller.abort();
        }
      } catch (error) {
        console.warn(`⚠️ [UNIFIED] Error aborting controller ${operationId}:`, error);
      }
    }
    
    this.activeControllers.clear();
    timeoutManager.cleanupAll();
  }

  /**
   * إعادة تعيين الخدمة
   */
  public reset(): void {
    console.log('🔄 [UNIFIED] Resetting connection service...');
    
    this.cleanupAllControllers();
    
    this.state = {
      isConnected: false,
      isHealthy: true,
      lastSuccessfulConnection: null,
      lastHealthCheck: null,
      failedAttempts: 0,
      consecutiveFailures: 0,
      currentBackendUrl: null,
      isRetrying: false,
      isRecovering: false,
      responseTime: 0
    };
  }

  /**
   * إيقاف الخدمة
   */
  public destroy(): void {
    console.log('🛑 [UNIFIED] Destroying connection service...');
    
    this.isDestroyed = true;
    
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
    
    this.cleanupAllControllers();
  }

  /**
   * الحصول على حالة الاتصال
   */
  public getConnectionState(): UnifiedConnectionState {
    return { ...this.state };
  }

  /**
   * الحصول على معلومات الأداء
   */
  public getHealthMetrics(): HealthMetrics {
    const uptime = this.state.lastSuccessfulConnection 
      ? Date.now() - this.state.lastSuccessfulConnection.getTime()
      : 0;

    const successRate = this.state.failedAttempts > 0 
      ? Math.max(0, 100 - (this.state.consecutiveFailures / this.state.failedAttempts) * 100)
      : 100;

    return {
      averageResponseTime: this.state.responseTime,
      successRate: Math.round(successRate),
      uptime,
      lastError: this.state.isHealthy ? null : 'Connection failed'
    };
  }

  /**
   * فحص ما إذا كان الخادم يحتاج لاستعادة
   */
  public needsRecovery(): boolean {
    return this.state.consecutiveFailures >= this.MAX_CONSECUTIVE_FAILURES;
  }

  /**
   * دالة مساعدة للانتظار
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * فحص حالة الاتصال بالإنترنت الأساسية
   */
  public isOnline(): boolean {
    return navigator.onLine;
  }

  /**
   * الحصول على معلومات الشبكة (إذا كانت متاحة)
   */
  public getNetworkInfo(): NetworkStatus {
    const connection = (navigator as any).connection ||
                      (navigator as any).mozConnection ||
                      (navigator as any).webkitConnection;

    return {
      isOnline: navigator.onLine,
      connectionType: connection?.type,
      effectiveType: connection?.effectiveType,
      downlink: connection?.downlink,
      rtt: connection?.rtt
    };
  }

  /**
   * فحص الاتصال بالإنترنت عبر محاولة الوصول للخادم
   */
  public async checkInternetConnectivity(): Promise<ConnectivityCheckResult> {
    if (!this.isOnline()) {
      return {
        isConnected: false,
        error: 'لا يوجد اتصال بالإنترنت'
      };
    }

    // محاولة فحص الاتصال مع الخادم المحلي أولاً
    try {
      const result = await this.pingUrl('/api/system/health');
      if (result.isConnected) {
        return result;
      }
    } catch (error) {
      console.warn('Local server check failed:', error);
    }

    // إذا فشل الاتصال مع الخادم المحلي، جرب الخوادم الخارجية
    const fallbackUrls = [
      'https://www.google.com/favicon.ico',
      'https://cloudflare.com/favicon.ico'
    ];

    for (const url of fallbackUrls) {
      try {
        const result = await this.pingUrl(url);
        if (result.isConnected) {
          return {
            isConnected: true,
            latency: result.latency,
            error: 'الاتصال متاح ولكن الخادم المحلي غير متاح'
          };
        }
      } catch (error) {
        continue;
      }
    }

    return {
      isConnected: false,
      error: 'لا يوجد اتصال بالإنترنت'
    };
  }

  /**
   * فحص سريع للاتصال (للاستخدام قبل العمليات الحرجة)
   */
  public async quickConnectivityCheck(): Promise<boolean> {
    if (!this.isOnline()) {
      return false;
    }

    try {
      const result = await this.pingUrl('/api/system/health');
      return result.isConnected;
    } catch (error) {
      return false;
    }
  }

  /**
   * فحص URL محدد
   */
  private async pingUrl(url: string): Promise<ConnectivityCheckResult> {
    const startTime = Date.now();
    const operationId = `ping-${Date.now()}`;

    try {
      const controller = new AbortController();
      this.activeControllers.set(operationId, controller);

      const timeoutId = setTimeout(() => controller.abort(), 8000);

      const response = await fetch(url, {
        method: 'HEAD',
        mode: url.startsWith('http') ? 'no-cors' : 'cors',
        cache: 'no-cache',
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      this.activeControllers.delete(operationId);

      const latency = Date.now() - startTime;

      return {
        isConnected: response.ok || (url.startsWith('http') && latency < 8000),
        latency: Math.round(latency)
      };

    } catch (error: any) {
      this.activeControllers.delete(operationId);
      const latency = Date.now() - startTime;

      // في حالة no-cors، قد نحصل على خطأ ولكن الطلب نجح فعلياً
      if (url.startsWith('http') && latency < 8000) {
        return {
          isConnected: true,
          latency: Math.round(latency)
        };
      }

      return {
        isConnected: false,
        error: error?.message || 'فشل في الاتصال'
      };
    }
  }

  /**
   * مراقبة تغييرات حالة الاتصال
   */
  public onConnectionChange(callback: (isOnline: boolean) => void): () => void {
    const handleOnline = () => callback(true);
    const handleOffline = () => callback(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // إرجاع دالة لإلغاء المراقبة
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }
}

// تصدير instance واحد
export const unifiedConnectionService = UnifiedConnectionService.getInstance();

/**
 * Hook للاستخدام في React components
 */
export function useUnifiedConnection() {
  return {
    // وظائف الاتصال الأساسية
    getState: () => unifiedConnectionService.getConnectionState(),
    getMetrics: () => unifiedConnectionService.getHealthMetrics(),
    retry: () => unifiedConnectionService.retryConnection(),
    getBestUrl: () => unifiedConnectionService.getBestBackendUrl(),
    reset: () => unifiedConnectionService.reset(),
    needsRecovery: () => unifiedConnectionService.needsRecovery(),

    // وظائف الشبكة الجديدة
    isOnline: () => unifiedConnectionService.isOnline(),
    getNetworkInfo: () => unifiedConnectionService.getNetworkInfo(),
    checkConnectivity: () => unifiedConnectionService.checkInternetConnectivity(),
    quickCheck: () => unifiedConnectionService.quickConnectivityCheck(),
    onConnectionChange: (callback: (isOnline: boolean) => void) =>
      unifiedConnectionService.onConnectionChange(callback)
  };
}

export default unifiedConnectionService;
