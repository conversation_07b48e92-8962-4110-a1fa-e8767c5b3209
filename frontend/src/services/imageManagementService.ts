/**
 * خدمة إدارة الصور المتقدمة للواجهة الأمامية
 * تطبق مبادئ البرمجة الكائنية مع دعم متعدد الاستخدامات
 */

import apiClient from '../lib/axios';

// أنواع البيانات
export interface ImageUploadResult {
  success: boolean;
  filename?: string;
  file_path?: string;
  file_size?: number;
  folder?: string;
  upload_time?: string;
  thumbnails?: Record<string, ThumbnailInfo>;
  error?: string;
  message?: string;
}

export interface ThumbnailInfo {
  path: string;
  size: [number, number];
  file_size: number;
}

export interface ImageInfo {
  success: boolean;
  file_path?: string;
  filename?: string;
  file_size?: number;
  created_time?: string;
  modified_time?: string;
  image_format?: string;
  image_mode?: string;
  image_size?: [number, number];
  width?: number;
  height?: number;
  has_transparency?: boolean;
  thumbnails?: Record<string, ThumbnailInfo>;
  error?: string;
}

export interface ImageListResult {
  success: boolean;
  folder?: string;
  total_images?: number;
  images?: ImageFileInfo[];
  error?: string;
}

export interface ImageFileInfo {
  filename: string;
  file_path: string;
  file_size: number;
  created_time: string;
  modified_time: string;
  thumbnails?: Record<string, ThumbnailInfo>;
}

export interface StorageStats {
  success: boolean;
  total_size?: number;
  total_files?: number;
  total_size_formatted?: string;
  folders?: Record<string, FolderStats>;
  error?: string;
}

export interface FolderStats {
  total_size: number;
  total_files: number;
  images: number;
  thumbnails: number;
  total_size_formatted: string;
}

export interface SupportedFormats {
  success: boolean;
  supported_extensions?: string[];
  max_file_size?: number;
  max_file_size_formatted?: string;
  thumbnail_sizes?: Record<string, [number, number]>;
  upload_directory?: string;
  error?: string;
}

export type ImageFolder = 'products' | 'categories' | 'users' | 'brands' | 'customers' | 'general';

/**
 * خدمة إدارة الصور المتقدمة
 */
export class ImageManagementService {
  private static instance: ImageManagementService;
  private baseURL = '/api/images';

  private constructor() {
    console.log('🖼️ تم تهيئة خدمة إدارة الصور');
  }

  /**
   * الحصول على مثيل وحيد من الخدمة (Singleton Pattern)
   */
  public static getInstance(): ImageManagementService {
    if (!ImageManagementService.instance) {
      ImageManagementService.instance = new ImageManagementService();
    }
    return ImageManagementService.instance;
  }

  /**
   * رفع صورة جديدة
   */
  public async uploadImage(
    file: File,
    folder: ImageFolder,
    generateThumbnails: boolean = true
  ): Promise<ImageUploadResult> {
    try {
      console.log(`📤 رفع صورة إلى مجلد: ${folder}`);

      // التحقق من صحة الملف
      const validation = this.validateImageFile(file);
      if (!validation.valid) {
        return {
          success: false,
          error: validation.error
        };
      }

      // إنشاء FormData
      const formData = new FormData();
      formData.append('file', file);

      // رفع الصورة
      const response = await apiClient.post(
        `${this.baseURL}/upload/${folder}?generate_thumbnails=${generateThumbnails}`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      console.log('✅ تم رفع الصورة بنجاح:', response.data);
      return response.data;

    } catch (error: any) {
      console.error('❌ خطأ في رفع الصورة:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'فشل في رفع الصورة'
      };
    }
  }

  /**
   * حذف صورة
   */
  public async deleteImage(
    filePath: string,
    deleteThumbnails: boolean = true
  ): Promise<{ success: boolean; deleted_files?: string[]; message?: string; error?: string }> {
    try {
      console.log(`🗑️ حذف صورة: ${filePath}`);

      const response = await apiClient.delete(
        `${this.baseURL}/delete?file_path=${encodeURIComponent(filePath)}&delete_thumbnails=${deleteThumbnails}`
      );

      console.log('✅ تم حذف الصورة بنجاح:', response.data);
      return response.data;

    } catch (error: any) {
      console.error('❌ خطأ في حذف الصورة:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'فشل في حذف الصورة'
      };
    }
  }

  /**
   * الحصول على معلومات صورة
   */
  public async getImageInfo(filePath: string): Promise<ImageInfo> {
    try {
      console.log(`ℹ️ جلب معلومات الصورة: ${filePath}`);

      const response = await apiClient.get(
        `${this.baseURL}/info?file_path=${encodeURIComponent(filePath)}`
      );

      return response.data;

    } catch (error: any) {
      console.error('❌ خطأ في جلب معلومات الصورة:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'فشل في جلب معلومات الصورة'
      };
    }
  }

  /**
   * جلب قائمة الصور في مجلد
   */
  public async listImages(
    folder: ImageFolder,
    includeThumbnails: boolean = false
  ): Promise<ImageListResult> {
    try {
      const url = `${this.baseURL}/list/${folder}?include_thumbnails=${includeThumbnails}`;
      console.log(`📋 جلب قائمة الصور من مجلد: ${folder}, URL: ${url}`);

      const response = await apiClient.get(url);

      console.log('📋 استجابة API:', response.data);
      console.log('📋 عدد الصور المُرجعة:', response.data.images?.length || 0);

      return response.data;

    } catch (error: any) {
      console.error('❌ خطأ في جلب قائمة الصور:', error);
      console.error('❌ تفاصيل الخطأ:', error.response?.data);
      return {
        success: false,
        error: error.response?.data?.detail || 'فشل في جلب قائمة الصور'
      };
    }
  }

  /**
   * تنظيف الملفات المهجورة
   */
  public async cleanupOrphanedFiles(folder: ImageFolder): Promise<{
    success: boolean;
    cleaned_files?: string[];
    total_cleaned?: number;
    message?: string;
    error?: string;
  }> {
    try {
      console.log(`🧹 تنظيف الملفات المهجورة في مجلد: ${folder}`);

      const response = await apiClient.post(`${this.baseURL}/cleanup/${folder}`);

      console.log('✅ تم تنظيف الملفات المهجورة:', response.data);
      return response.data;

    } catch (error: any) {
      console.error('❌ خطأ في تنظيف الملفات المهجورة:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'فشل في تنظيف الملفات المهجورة'
      };
    }
  }

  /**
   * الحصول على إحصائيات التخزين
   */
  public async getStorageStatistics(): Promise<StorageStats> {
    try {
      console.log('📊 جلب إحصائيات التخزين');

      const response = await apiClient.get(`${this.baseURL}/storage-stats`);

      return response.data;

    } catch (error: any) {
      console.error('❌ خطأ في جلب إحصائيات التخزين:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'فشل في جلب إحصائيات التخزين'
      };
    }
  }

  /**
   * إعادة إنشاء الصور المصغرة
   */
  public async regenerateThumbnails(filePath: string): Promise<{
    success: boolean;
    thumbnails?: Record<string, ThumbnailInfo>;
    message?: string;
    error?: string;
  }> {
    try {
      console.log(`🔄 إعادة إنشاء الصور المصغرة: ${filePath}`);

      const response = await apiClient.post(
        `${this.baseURL}/regenerate-thumbnails?file_path=${encodeURIComponent(filePath)}`
      );

      console.log('✅ تم إعادة إنشاء الصور المصغرة:', response.data);
      return response.data;

    } catch (error: any) {
      console.error('❌ خطأ في إعادة إنشاء الصور المصغرة:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'فشل في إعادة إنشاء الصور المصغرة'
      };
    }
  }

  /**
   * الحصول على الصيغ المدعومة
   */
  public async getSupportedFormats(): Promise<SupportedFormats> {
    try {
      console.log('📋 جلب الصيغ المدعومة');

      const response = await apiClient.get(`${this.baseURL}/supported-formats`);

      return response.data;

    } catch (error: any) {
      console.error('❌ خطأ في جلب الصيغ المدعومة:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'فشل في جلب الصيغ المدعومة'
      };
    }
  }

  /**
   * التحقق من صحة ملف الصورة قبل الرفع
   */
  public validateImageFile(file: File): { valid: boolean; error?: string } {
    try {
      // التحقق من وجود الملف
      if (!file) {
        return { valid: false, error: 'لم يتم تحديد ملف' };
      }

      // التحقق من نوع الملف
      const allowedTypes = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/webp',
        'image/bmp'
      ];

      if (!allowedTypes.includes(file.type)) {
        return {
          valid: false,
          error: `نوع الملف غير مدعوم. الأنواع المدعومة: ${allowedTypes.join(', ')}`
        };
      }

      // التحقق من حجم الملف (10 MB)
      const maxSize = 10 * 1024 * 1024;
      if (file.size > maxSize) {
        return {
          valid: false,
          error: `حجم الملف كبير جداً. الحد الأقصى: ${this.formatFileSize(maxSize)}`
        };
      }

      if (file.size === 0) {
        return { valid: false, error: 'الملف فارغ' };
      }

      return { valid: true };

    } catch (error) {
      return { valid: false, error: 'خطأ في التحقق من الملف' };
    }
  }

  /**
   * تحويل حجم الملف إلى وحدة قابلة للقراءة
   */
  public formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * الحصول على URL للصورة مع كسر cache
   */
  public getImageUrl(filePath: string, bustCache: boolean = true): string {
    if (!filePath) return '';

    // تنظيف المسار
    const cleanPath = filePath.startsWith('/') ? filePath.slice(1) : filePath;
    const pathParts = cleanPath.split('/');

    if (pathParts.length < 2) return '';

    const folder = pathParts[0];
    const filename = pathParts[pathParts.length - 1];

    // استخدام endpoint المخصص
    let imageUrl = `${this.baseURL}/serve/${folder}/${filename}`;

    // إضافة cache buster لتجنب مشاكل cache المتصفح
    if (bustCache) {
      const timestamp = Date.now();
      imageUrl += `?t=${timestamp}`;
    }

    console.log(`📸 URL الصورة الأصلية:`, imageUrl);

    return imageUrl;
  }

  /**
   * الحصول على URL للصورة المصغرة مع كسر cache
   */
  public getThumbnailUrl(filePath: string, size: 'small' | 'medium' | 'large' = 'medium', bustCache: boolean = true): string {
    if (!filePath) return '';

    // تنظيف المسار
    const cleanPath = filePath.startsWith('/') ? filePath.slice(1) : filePath;
    const pathParts = cleanPath.split('/');

    if (pathParts.length < 2) return this.getImageUrl(filePath, bustCache);

    const folder = pathParts[0];
    const filename = pathParts[pathParts.length - 1];

    // استخدام endpoint المخصص مع معامل size
    let thumbnailUrl = `${this.baseURL}/serve/${folder}/${filename}?size=${size}`;

    // إضافة cache buster
    if (bustCache) {
      const timestamp = Date.now();
      thumbnailUrl += `&t=${timestamp}`;
    }

    console.log(`🖼️ URL الصورة المصغرة (${size}):`, thumbnailUrl);

    return thumbnailUrl;
  }

  /**
   * معاينة الصورة قبل الرفع
   */
  public previewImage(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      try {
        const reader = new FileReader();

        reader.onload = (e) => {
          if (e.target?.result) {
            resolve(e.target.result as string);
          } else {
            reject(new Error('فشل في قراءة الملف'));
          }
        };

        reader.onerror = () => {
          reject(new Error('خطأ في قراءة الملف'));
        };

        reader.readAsDataURL(file);

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * ضغط الصورة قبل الرفع (اختياري)
   */
  public compressImage(
    file: File,
    maxWidth: number = 1920,
    maxHeight: number = 1080,
    quality: number = 0.8
  ): Promise<File> {
    return new Promise((resolve, reject) => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();

        img.onload = () => {
          // حساب الأبعاد الجديدة
          let { width, height } = img;

          if (width > maxWidth || height > maxHeight) {
            const ratio = Math.min(maxWidth / width, maxHeight / height);
            width *= ratio;
            height *= ratio;
          }

          // تعيين أبعاد الـ canvas
          canvas.width = width;
          canvas.height = height;

          // رسم الصورة المضغوطة
          ctx?.drawImage(img, 0, 0, width, height);

          // تحويل إلى Blob
          canvas.toBlob(
            (blob) => {
              if (blob) {
                const compressedFile = new File([blob], file.name, {
                  type: file.type,
                  lastModified: Date.now()
                });
                resolve(compressedFile);
              } else {
                reject(new Error('فشل في ضغط الصورة'));
              }
            },
            file.type,
            quality
          );
        };

        img.onerror = () => {
          reject(new Error('فشل في تحميل الصورة'));
        };

        img.src = URL.createObjectURL(file);

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * رفع متعدد للصور
   */
  public async uploadMultipleImages(
    files: File[],
    folder: ImageFolder,
    generateThumbnails: boolean = true,
    onProgress?: (progress: number, currentFile: string) => void
  ): Promise<ImageUploadResult[]> {
    const results: ImageUploadResult[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      try {
        onProgress?.(((i + 1) / files.length) * 100, file.name);

        const result = await this.uploadImage(file, folder, generateThumbnails);
        results.push(result);

      } catch (error) {
        results.push({
          success: false,
          error: `فشل في رفع ${file.name}: ${error}`
        });
      }
    }

    return results;
  }

  /**
   * تنظيف الموارد
   */
  public cleanup(): void {
    console.log('🧹 تم تنظيف موارد خدمة إدارة الصور');
  }
}

// تصدير مثيل وحيد من الخدمة
export const imageManagementService = ImageManagementService.getInstance();

// تصدير الخدمة كـ default
export default imageManagementService;
