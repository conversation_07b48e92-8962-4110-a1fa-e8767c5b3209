/**
 * خدمة OpenStreetMap للعناوين والمواقع الجغرافية
 * تستخدم Nominatim API للبحث عن العناوين وتحويل الإحداثيات إلى عناوين
 * خدمة مجانية بديلة لـ Google Maps API
 */

export interface LocationCoordinates {
  lat: number;
  lng: number;
}

export interface AddressComponents {
  house_number?: string;
  road?: string;
  neighbourhood?: string;
  suburb?: string;
  quarter?: string;
  residential?: string;
  city?: string;
  town?: string;
  village?: string;
  municipality?: string;
  state?: string;
  country?: string;
  postcode?: string;
  county?: string;
  region?: string;
  amenity?: string;
  shop?: string;
  building?: string;
}

export interface SearchResult {
  place_id: string;
  display_name: string;
  lat: string;
  lon: string;
  address: AddressComponents;
  importance: number;
  type: string;
  class: string;
}

export interface ReverseGeocodeResult {
  place_id: string;
  display_name: string;
  address: AddressComponents;
  lat: string;
  lon: string;
}

/**
 * خدمة OpenStreetMap للتعامل مع العناوين والمواقع
 */
export class OpenStreetMapService {
  private static instance: OpenStreetMapService;
  private readonly baseUrl = 'https://nominatim.openstreetmap.org';
  private readonly userAgent = 'SmartPOS/1.1 (https://smartpos.ly)';
  private requestCache = new Map<string, any>();
  private readonly cacheTimeout = 5 * 60 * 1000; // 5 دقائق
  private lastRequestTime = 0;
  private readonly requestDelay = 1000; // تأخير ثانية واحدة بين الطلبات

  private constructor() {}

  /**
   * إدارة التأخير بين الطلبات لتجنب تجاوز حدود الخدمة
   */
  private async ensureRequestDelay(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    if (timeSinceLastRequest < this.requestDelay) {
      const delayNeeded = this.requestDelay - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, delayNeeded));
    }

    this.lastRequestTime = Date.now();
  }

  public static getInstance(): OpenStreetMapService {
    if (!OpenStreetMapService.instance) {
      OpenStreetMapService.instance = new OpenStreetMapService();
    }
    return OpenStreetMapService.instance;
  }

  /**
   * البحث عن العناوين باستخدام النص - محسن لمطابقة الموقع الرسمي
   */
  public async searchAddresses(query: string, limit: number = 5): Promise<SearchResult[]> {
    if (!query.trim()) {
      return [];
    }

    const cacheKey = `search_${query.trim()}_${limit}`;
    const cached = this.getFromCache(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      // إدارة التأخير بين الطلبات
      await this.ensureRequestDelay();

      // معاملات محسنة للحصول على أفضل النتائج العربية
      const params = new URLSearchParams({
        q: query.trim(),
        format: 'jsonv2', // تنسيق محسن
        addressdetails: '1',
        limit: Math.min(limit, 8).toString(), // تقليل العدد للحصول على نتائج أكثر دقة
        'accept-language': 'ar,ar-LY,ar-EG,ar-SA,en', // أولوية للعربية مع اللهجات المحلية
        countrycodes: 'ly,eg,sa,ae,kw,qa,bh,jo,sy,iq,ma,tn,dz,sd,ye,om,lb,ps',
        extratags: '1',
        namedetails: '1',
        dedupe: '1',
        polygon_geojson: '0',
        polygon_kml: '0',
        polygon_svg: '0',
        polygon_text: '0',
        bounded: '0',
        viewbox: '', // سيتم تحديده حسب البلد
        email: '<EMAIL>' // بريد إلكتروني صحيح
      });

      const response = await fetch(`${this.baseUrl}/search?${params}`, {
        headers: {
          'User-Agent': this.userAgent,
          'Accept': 'application/json',
          'Accept-Language': 'ar,ar-LY,ar-EG,ar-SA;q=0.9,en;q=0.5',
          'Accept-Charset': 'utf-8'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: SearchResult[] = await response.json();

      // فلترة وترتيب النتائج للحصول على أفضل النتائج العربية
      const filteredResults = data
        .filter(result => result.address) // فقط النتائج التي تحتوي على عنوان
        .filter(result => this.isRelevantResult(result)) // فلترة النتائج ذات الصلة
        .map(result => this.enhanceResultWithArabicAddress(result)) // تحسين العناوين
        .sort((a, b) => this.calculateResultScore(b) - this.calculateResultScore(a)); // ترتيب ذكي

      this.setCache(cacheKey, filteredResults);
      return filteredResults;

    } catch (error) {
      console.error('خطأ في البحث عن العناوين:', error);
      throw new Error('فشل في البحث عن العناوين. يرجى المحاولة مرة أخرى.');
    }
  }

  /**
   * تحويل الإحداثيات إلى عنوان (Reverse Geocoding) - محسن للدقة القصوى
   */
  public async reverseGeocode(lat: number, lng: number): Promise<ReverseGeocodeResult | null> {
    const cacheKey = `reverse_${lat.toFixed(7)}_${lng.toFixed(7)}`;
    const cached = this.getFromCache(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      // إدارة التأخير بين الطلبات
      await this.ensureRequestDelay();

      // معاملات مطابقة تماماً للموقع الرسمي للحصول على أفضل النتائج
      const params = new URLSearchParams({
        lat: lat.toFixed(7),
        lon: lng.toFixed(7),
        format: 'jsonv2',
        addressdetails: '1',
        extratags: '1',
        namedetails: '1',
        'accept-language': 'ar,en',
        zoom: '18'
        // إزالة layer تماماً للحصول على جميع الطبقات والتفاصيل
      });

      const response = await fetch(`${this.baseUrl}/reverse?${params}`, {
        headers: {
          'User-Agent': this.userAgent,
          'Accept': 'application/json',
          'Accept-Language': 'ar,ar-LY,ar-EG,ar-SA;q=0.9,en;q=0.5',
          'Accept-Charset': 'utf-8'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: ReverseGeocodeResult = await response.json();

      if (data && data.address) {
        // الاحتفاظ بالعنوان الأصلي كما هو من الخدمة
        this.setCache(cacheKey, data);
        return data;
      }

      return null;

    } catch (error) {
      console.error('خطأ في تحويل الإحداثيات إلى عنوان:', error);
      throw new Error('فشل في الحصول على العنوان. يرجى المحاولة مرة أخرى.');
    }
  }

  /**
   * الحصول على الموقع الحالي للمستخدم - محسن مع معالجة أخطاء أفضل
   */
  public async getCurrentLocation(): Promise<LocationCoordinates> {
    return new Promise((resolve, reject) => {
      console.log('🔍 فحص دعم المتصفح لتحديد الموقع...');

      if (!navigator.geolocation) {
        console.error('❌ خدمة تحديد الموقع غير مدعومة');
        reject(new Error('خدمة تحديد الموقع غير مدعومة في هذا المتصفح'));
        return;
      }

      console.log('✅ خدمة تحديد الموقع مدعومة، جاري الطلب...');

      // محاولة أولى بدقة عالية
      navigator.geolocation.getCurrentPosition(
        (position) => {
          console.log('✅ تم الحصول على الموقع بنجاح:', {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
            accuracy: position.coords.accuracy
          });

          resolve({
            lat: position.coords.latitude,
            lng: position.coords.longitude
          });
        },
        (error) => {
          console.error('❌ فشل في الحصول على الموقع (المحاولة الأولى):', error);

          // محاولة ثانية بإعدادات أقل دقة
          console.log('🔄 محاولة ثانية بإعدادات أقل دقة...');

          navigator.geolocation.getCurrentPosition(
            (position) => {
              console.log('✅ تم الحصول على الموقع في المحاولة الثانية:', {
                lat: position.coords.latitude,
                lng: position.coords.longitude,
                accuracy: position.coords.accuracy
              });

              resolve({
                lat: position.coords.latitude,
                lng: position.coords.longitude
              });
            },
            (secondError) => {
              console.error('❌ فشل في المحاولة الثانية أيضاً:', secondError);

              let errorMessage = 'فشل في تحديد الموقع الحالي';

              switch (secondError.code) {
                case secondError.PERMISSION_DENIED:
                  errorMessage = 'تم رفض الإذن لتحديد الموقع. يرجى السماح بالوصول للموقع في إعدادات المتصفح.';
                  break;
                case secondError.POSITION_UNAVAILABLE:
                  errorMessage = 'معلومات الموقع غير متاحة. تأكد من تفعيل خدمات الموقع.';
                  break;
                case secondError.TIMEOUT:
                  errorMessage = 'انتهت مهلة طلب تحديد الموقع. يرجى المحاولة مرة أخرى.';
                  break;
                default:
                  errorMessage = `خطأ غير معروف في تحديد الموقع (كود: ${secondError.code})`;
              }

              reject(new Error(errorMessage));
            },
            {
              enableHighAccuracy: false, // دقة أقل للمحاولة الثانية
              timeout: 15000, // وقت أطول
              maximumAge: 300000 // قبول موقع أقدم (5 دقائق)
            }
          );
        },
        {
          enableHighAccuracy: true, // دقة عالية للمحاولة الأولى
          timeout: 8000, // 8 ثوان
          maximumAge: 60000 // دقيقة واحدة
        }
      );
    });
  }



  /**
   * تنسيق العنوان للحفظ في قاعدة البيانات - محسن للعربية مع الحفاظ على التفاصيل
   */
  public formatAddressForStorage(result: SearchResult | ReverseGeocodeResult): string {
    // أولاً، جرب استخدام display_name إذا كان يحتوي على محتوى عربي جيد
    if (result.display_name) {
      const cleanedDisplayName = this.improveDisplayName(result.display_name);
      if (cleanedDisplayName && cleanedDisplayName.length > 10) {
        return cleanedDisplayName;
      }
    }

    // إذا لم ينجح، استخدم التنسيق المخصص
    return this.formatArabicAddress(result.address);
  }

  /**
   * تحسين display_name مع الحفاظ على التفاصيل المهمة
   */
  private improveDisplayName(displayName: string): string {
    if (!displayName) return '';

    // تنظيف خفيف للعنوان مع الحفاظ على التفاصيل
    let improved = displayName;

    // إزالة الكلمات الإنجليزية الشائعة فقط (ليس كل شيء)
    const wordsToRemove = ['District', 'Governorate', 'Province', 'Region'];
    wordsToRemove.forEach(word => {
      const regex = new RegExp(`\\b${word}\\b`, 'gi');
      improved = improved.replace(regex, '');
    });

    // تنظيف المسافات الزائدة والفواصل المتكررة
    improved = improved.replace(/\s+/g, ' ');
    improved = improved.replace(/,\s*,/g, ',');
    improved = improved.replace(/,\s*$/, '');
    improved = improved.trim();

    // ترجمة أسماء البلدان إذا وجدت
    improved = improved.replace(/\bLibya\b/gi, 'ليبيا');
    improved = improved.replace(/\bEgypt\b/gi, 'مصر');
    improved = improved.replace(/\bSaudi Arabia\b/gi, 'السعودية');

    return improved;
  }

  /**
   * تنسيق العنوان باللغة العربية مع الحفاظ على التفاصيل المهمة
   */
  private formatArabicAddress(address: AddressComponents): string {
    const parts: string[] = [];

    // 1. المعالم والمباني المهمة (amenity, building, shop)
    if (address.amenity) {
      const cleanAmenity = this.smartCleanText(address.amenity);
      if (cleanAmenity) {
        parts.push(cleanAmenity);
      }
    }

    // 2. رقم المنزل والشارع
    if (address.house_number && address.road) {
      const cleanRoad = this.smartCleanText(address.road);
      if (cleanRoad) {
        parts.push(`${address.house_number} ${cleanRoad}`);
      }
    } else if (address.road) {
      const cleanRoad = this.smartCleanText(address.road);
      if (cleanRoad) {
        parts.push(cleanRoad);
      }
    }

    // 3. الحي أو المنطقة السكنية (بترتيب الأولوية)
    const neighborhood = address.neighbourhood || address.suburb || address.quarter || address.residential;
    if (neighborhood) {
      const cleanNeighborhood = this.smartCleanText(neighborhood);
      if (cleanNeighborhood) {
        parts.push(cleanNeighborhood);
      }
    }

    // 4. المنطقة الإدارية الأكبر (county, region)
    if (address.county && address.county !== address.city) {
      const cleanCounty = this.smartCleanText(address.county);
      if (cleanCounty) {
        parts.push(cleanCounty);
      }
    }

    // 5. المدينة أو البلدة
    const city = address.city || address.town || address.village || address.municipality;
    if (city) {
      const cleanCity = this.smartCleanText(city);
      if (cleanCity) {
        parts.push(cleanCity);
      }
    }

    // 6. المحافظة أو الولاية (إذا كانت مختلفة عن المدينة)
    if (address.state && address.state !== city) {
      const cleanState = this.smartCleanText(address.state);
      if (cleanState) {
        parts.push(cleanState);
      }
    }

    // 7. البلد (مع ترجمة للعربية)
    if (address.country) {
      const arabicCountry = this.translateCountryToArabic(address.country);
      if (arabicCountry) {
        parts.push(arabicCountry);
      }
    }

    // إذا لم نحصل على أي أجزاء، استخدم التنسيق البسيط
    if (parts.length === 0) {
      return this.formatSimpleAddress(address);
    }

    return parts.join('، ');
  }

  /**
   * تنظيف ذكي للنص مع الحفاظ على المعلومات المهمة
   */
  private smartCleanText(text: string): string {
    if (!text) return '';

    let cleaned = text;

    // إزالة الكلمات الإنجليزية الشائعة فقط (ليس كل شيء)
    const commonWords = ['District', 'Area', 'Region', 'Zone', 'Sector'];
    commonWords.forEach(word => {
      const regex = new RegExp(`\\b${word}\\b`, 'gi');
      cleaned = cleaned.replace(regex, '');
    });

    // تنظيف المسافات الزائدة
    cleaned = cleaned.replace(/\s+/g, ' ').trim();

    // إذا كان النص قصيراً جداً أو فارغاً، أرجع النص الأصلي
    if (!cleaned || cleaned.length < 2) {
      return text;
    }

    return cleaned;
  }

  /**
   * تنظيف الأسماء الإنجليزية وإبقاء العربية فقط - نظام متقدم
   */
  private cleanEnglishNames(text: string): string {
    if (!text) return '';

    let cleanText = text;

    // 1. إزالة الكلمات الإنجليزية الشائعة في العناوين
    const commonEnglishWords = [
      'Street', 'Road', 'Avenue', 'Boulevard', 'Lane', 'Drive', 'Way', 'Place',
      'District', 'Area', 'Region', 'Zone', 'Sector', 'Block', 'Quarter',
      'City', 'Town', 'Village', 'Municipality', 'Governorate', 'Province',
      'North', 'South', 'East', 'West', 'Central', 'Upper', 'Lower',
      'New', 'Old', 'Al', 'The', 'Of', 'And', 'In', 'At', 'To', 'From',
      'First', 'Second', 'Third', 'Main', 'Primary', 'Secondary'
    ];

    // إزالة الكلمات الإنجليزية
    commonEnglishWords.forEach(word => {
      const regex = new RegExp(`\\b${word}\\b`, 'gi');
      cleanText = cleanText.replace(regex, '');
    });

    // 2. إزالة الأنماط الإنجليزية المعقدة
    const complexPatterns = [
      /\b[A-Za-z]+\s+[A-Za-z]+\s+[A-Za-z]+\s+[A-Za-z]+\b/gi, // 4 كلمات إنجليزية متتالية
      /\b[A-Za-z]+\s+[A-Za-z]+\s+[A-Za-z]+\b/gi, // 3 كلمات إنجليزية متتالية
      /\b[A-Za-z]{2,}\s*-\s*[A-Za-z]{2,}\b/gi, // أسماء مركبة بشرطة
      /\b[A-Za-z]+\s*\/\s*[A-Za-z]+\b/gi, // أسماء مركبة بشرطة مائلة
      /\([A-Za-z\s]+\)/gi, // نص إنجليزي بين أقواس
      /\[[A-Za-z\s]+\]/gi, // نص إنجليزي بين أقواس مربعة
      /\b[A-Z]{2,}\b/gi, // اختصارات بأحرف كبيرة
      /\b\d+[A-Za-z]+\b/gi, // أرقام متبوعة بأحرف
      /\b[A-Za-z]+\d+\b/gi // أحرف متبوعة بأرقام
    ];

    // إزالة الأنماط المعقدة
    complexPatterns.forEach(pattern => {
      cleanText = cleanText.replace(pattern, '');
    });

    // 3. إزالة الأرقام المنفصلة والرموز غير المرغوب فيها
    cleanText = cleanText.replace(/\b\d+\b/g, ''); // أرقام منفصلة
    cleanText = cleanText.replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s،,.-]/g, '');

    // 4. تنظيف المسافات والفواصل الزائدة
    cleanText = cleanText.replace(/\s+/g, ' '); // مسافات متعددة إلى مسافة واحدة
    cleanText = cleanText.replace(/[،,.-]+/g, ''); // إزالة الفواصل والنقاط
    cleanText = cleanText.replace(/^\s*[،,.-]+\s*|\s*[،,.-]+\s*$/g, ''); // إزالة الفواصل من البداية والنهاية
    cleanText = cleanText.trim();

    // 5. فحص جودة النتيجة
    const arabicCharCount = (cleanText.match(/[\u0600-\u06FF]/g) || []).length;
    const totalCharCount = cleanText.replace(/\s/g, '').length;

    // إذا كان النص فارغاً أو لا يحتوي على أحرف عربية كافية، أرجع النص الأصلي
    if (!cleanText || cleanText.length < 2 || arabicCharCount < totalCharCount * 0.3) {
      return this.extractArabicParts(text);
    }

    return cleanText;
  }

  /**
   * استخراج الأجزاء العربية من النص المختلط
   */
  private extractArabicParts(text: string): string {
    if (!text) return '';

    // استخراج الكلمات العربية فقط
    const arabicWords = text.match(/[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+/g);

    if (!arabicWords || arabicWords.length === 0) {
      return text; // إرجاع النص الأصلي إذا لم توجد كلمات عربية
    }

    // دمج الكلمات العربية
    let result = arabicWords.join(' ');

    // تنظيف النتيجة
    result = result.replace(/\s+/g, ' ').trim();

    return result || text;
  }

  /**
   * ترجمة أسماء البلدان والمناطق إلى العربية - قاموس شامل
   */
  private translateCountryToArabic(country: string): string {
    const countryTranslations: { [key: string]: string } = {
      // البلدان العربية
      'Libya': 'ليبيا',
      'Egypt': 'مصر',
      'Saudi Arabia': 'السعودية',
      'United Arab Emirates': 'الإمارات العربية المتحدة',
      'Kuwait': 'الكويت',
      'Qatar': 'قطر',
      'Bahrain': 'البحرين',
      'Jordan': 'الأردن',
      'Syria': 'سوريا',
      'Iraq': 'العراق',
      'Morocco': 'المغرب',
      'Tunisia': 'تونس',
      'Algeria': 'الجزائر',
      'Sudan': 'السودان',
      'Yemen': 'اليمن',
      'Oman': 'عُمان',
      'Lebanon': 'لبنان',
      'Palestine': 'فلسطين',

      // أسماء بديلة للبلدان العربية
      'Libyan Arab Jamahiriya': 'ليبيا',
      'Arab Republic of Egypt': 'مصر',
      'Kingdom of Saudi Arabia': 'السعودية',
      'UAE': 'الإمارات العربية المتحدة',
      'State of Kuwait': 'الكويت',
      'State of Qatar': 'قطر',
      'Kingdom of Bahrain': 'البحرين',
      'Hashemite Kingdom of Jordan': 'الأردن',
      'Syrian Arab Republic': 'سوريا',
      'Republic of Iraq': 'العراق',
      'Kingdom of Morocco': 'المغرب',
      'Republic of Tunisia': 'تونس',
      'People\'s Democratic Republic of Algeria': 'الجزائر',
      'Republic of the Sudan': 'السودان',
      'Republic of Yemen': 'اليمن',
      'Sultanate of Oman': 'عُمان',
      'Lebanese Republic': 'لبنان',
      'State of Palestine': 'فلسطين',

      // مناطق ليبية مهمة
      'Tripoli': 'طرابلس',
      'Benghazi': 'بنغازي',
      'Misrata': 'مصراتة',
      'Zawiya': 'الزاوية',
      'Sabha': 'سبها',
      'Tobruk': 'طبرق',
      'Sirte': 'سرت',
      'Derna': 'درنة',
      'Ghat': 'غات',
      'Murzuq': 'مرزق',

      // بلدان أخرى مهمة
      'Turkey': 'تركيا',
      'Iran': 'إيران',
      'Israel': 'إسرائيل',
      'Cyprus': 'قبرص',
      'Malta': 'مالطا',
      'Italy': 'إيطاليا',
      'Greece': 'اليونان',
      'Chad': 'تشاد',
      'Niger': 'النيجر',
      'Nigeria': 'نيجيريا'
    };

    // البحث عن ترجمة مطابقة تماماً
    if (countryTranslations[country]) {
      return countryTranslations[country];
    }

    // البحث عن ترجمة جزئية (case-insensitive)
    const lowerCountry = country.toLowerCase();
    for (const [key, value] of Object.entries(countryTranslations)) {
      if (key.toLowerCase() === lowerCountry) {
        return value;
      }
    }

    // إذا لم توجد ترجمة، أرجع النص الأصلي منظفاً
    return this.cleanEnglishNames(country) || country;
  }

  /**
   * تنسيق عنوان بسيط في حالة عدم وجود display_name (احتياطي)
   */
  private formatSimpleAddress(address: AddressComponents): string {
    const parts: string[] = [];

    // ترتيب بسيط مطابق للموقع الرسمي
    if (address.house_number && address.road) {
      parts.push(`${address.house_number} ${address.road}`);
    } else if (address.road) {
      parts.push(address.road);
    }

    if (address.neighbourhood || address.suburb) {
      parts.push((address.neighbourhood || address.suburb) as string);
    }

    if (address.city || address.town || address.village) {
      parts.push((address.city || address.town || address.village) as string);
    }

    if (address.state) {
      parts.push(address.state);
    }

    if (address.country) {
      parts.push(address.country);
    }

    return parts.join('، ');
  }

  /**
   * إدارة الكاش
   */
  private getFromCache(key: string): any {
    const cached = this.requestCache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    this.requestCache.delete(key);
    return null;
  }

  private setCache(key: string, data: any): void {
    this.requestCache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * مسح الكاش
   */
  public clearCache(): void {
    this.requestCache.clear();
  }

  /**
   * التحقق من صحة الإحداثيات
   */
  public isValidCoordinates(lat: number, lng: number): boolean {
    return lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180;
  }

  /**
   * الحصول على عنوان مفصل ودقيق باللغة العربية فقط
   */
  public async getDetailedAddress(lat: number, lng: number): Promise<string> {
    try {
      // الحصول على النتيجة من reverse geocoding
      const result = await this.reverseGeocode(lat, lng);

      if (result && result.address) {
        // استخدام التنسيق العربي المحسن
        const arabicAddress = this.formatArabicAddress(result.address);

        // إذا كان العنوان العربي فارغاً أو قصيراً جداً، استخدم التنسيق البسيط
        if (!arabicAddress || arabicAddress.length < 5) {
          return this.formatSimpleAddress(result.address);
        }

        return arabicAddress;
      }

      // إذا لم نحصل على نتيجة، أرجع الإحداثيات
      return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;

    } catch (error) {
      console.error('خطأ في الحصول على العنوان المفصل:', error);
      return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
    }
  }

  /**
   * فحص ما إذا كانت النتيجة ذات صلة ومفيدة
   */
  private isRelevantResult(result: SearchResult): boolean {
    // تجاهل النتائج التي لا تحتوي على معلومات كافية
    if (!result.address || !result.display_name) {
      return false;
    }

    // تجاهل النتائج التي تحتوي على أسماء إنجليزية فقط
    const hasArabicContent = /[\u0600-\u06FF]/.test(result.display_name);
    const hasUsefulAddress = !!(result.address.city || result.address.town || result.address.village ||
                               result.address.neighbourhood || result.address.suburb || result.address.road);

    return hasUsefulAddress && (hasArabicContent || result.importance > 0.5);
  }

  /**
   * تحسين النتيجة بإضافة العنوان العربي المحسن
   */
  private enhanceResultWithArabicAddress(result: SearchResult): SearchResult {
    // إنشاء نسخة محسنة من النتيجة
    const enhancedResult = { ...result };

    // إضافة العنوان العربي المحسن
    const arabicAddress = this.formatArabicAddress(result.address);
    if (arabicAddress && arabicAddress.length > 5) {
      enhancedResult.display_name = arabicAddress;
    }

    return enhancedResult;
  }

  /**
   * حساب نقاط جودة النتيجة للترتيب الذكي
   */
  private calculateResultScore(result: SearchResult): number {
    let score = result.importance || 0;

    // إضافة نقاط للمحتوى العربي
    const arabicCharCount = (result.display_name.match(/[\u0600-\u06FF]/g) || []).length;
    score += arabicCharCount * 0.01;

    // إضافة نقاط للعناوين المفصلة
    if (result.address.road) score += 0.1;
    if (result.address.house_number) score += 0.1;
    if (result.address.neighbourhood || result.address.suburb) score += 0.1;
    if (result.address.city || result.address.town) score += 0.2;

    // تقليل النقاط للنتائج التي تحتوي على كلمات إنجليزية كثيرة
    const englishWordCount = (result.display_name.match(/\b[A-Za-z]{3,}\b/g) || []).length;
    score -= englishWordCount * 0.05;

    return Math.max(0, score);
  }

  /**
   * حساب المسافة بين نقطتين (بالكيلومتر)
   */
  public calculateDistance(
    lat1: number, lng1: number,
    lat2: number, lng2: number
  ): number {
    const R = 6371; // نصف قطر الأرض بالكيلومتر
    const dLat = this.toRadians(lat2 - lat1);
    const dLng = this.toRadians(lng2 - lng1);

    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }
}

// إنشاء instance مشترك
export const openStreetMapService = OpenStreetMapService.getInstance();
