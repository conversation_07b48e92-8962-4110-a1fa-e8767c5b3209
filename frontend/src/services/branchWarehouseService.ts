/**
 * خدمة إدارة العلاقات بين الفروع والمستودعات
 * تطبق مبادئ البرمجة الكائنية مع معالجة شاملة للأخطاء
 */

import api from '../lib/axios';
import { AxiosResponse } from 'axios';

// Types
export interface BranchWarehouseLink {
  branch_id: number;
  warehouse_id: number;
  is_primary: boolean;
  priority: number;
  created_at?: string;
}

export interface BranchWarehouseLinkCreate {
  branch_id: number;
  warehouse_id: number;
  is_primary?: boolean;
  priority?: number;
}

export interface BranchWarehouseLinkUpdate {
  is_primary?: boolean;
  priority?: number;
}

export interface WarehouseForBranch {
  id: number;
  name: string;
  code: string;
  address?: string;
  is_active: boolean;
  is_primary: boolean;
  priority: number;
  created_at?: string;
}

export interface BranchForWarehouse {
  id: number;
  name: string;
  code: string;
  address?: string;
  is_active: boolean;
  is_primary: boolean;
  priority: number;
  created_at?: string;
}

export interface AvailableWarehouse {
  id: number;
  name: string;
  code: string;
  address?: string;
  is_active: boolean;
}

export interface AvailableBranch {
  id: number;
  name: string;
  code: string;
  address?: string;
  is_active: boolean;
}

interface ApiResponse<T> {
  success: boolean;
  warehouses?: T[];
  branches?: T[];
  warehouse?: T;
  branch?: T;
  available_warehouses?: AvailableWarehouse[];
  available_branches?: AvailableBranch[];
  total_count?: number;
  message?: string;
  error?: string;
}

/**
 * خدمة إدارة العلاقات بين الفروع والمستودعات - تطبق مبادئ البرمجة الكائنية
 */
export class BranchWarehouseService {
  private static instance: BranchWarehouseService;
  private readonly baseURL = '/api/branch-warehouses';

  private constructor() {
    // منع الإنشاء المباشر - استخدم getInstance()
  }

  /**
   * الحصول على مثيل وحيد من الخدمة (Singleton Pattern)
   */
  public static getInstance(): BranchWarehouseService {
    if (!BranchWarehouseService.instance) {
      BranchWarehouseService.instance = new BranchWarehouseService();
    }
    return BranchWarehouseService.instance;
  }

  /**
   * ربط فرع بمستودع
   */
  public async linkBranchToWarehouse(linkData: BranchWarehouseLinkCreate): Promise<ApiResponse<any>> {
    try {
      console.log('🔄 ربط فرع بمستودع:', linkData);
      
      const response: AxiosResponse<ApiResponse<any>> = await api.post(
        `${this.baseURL}/link`,
        linkData
      );
      
      if (response.data.success) {
        console.log('✅ تم ربط الفرع بالمستودع بنجاح');
      }
      
      return response.data;
    } catch (error: any) {
      console.error('❌ خطأ في ربط الفرع بالمستودع:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في ربط الفرع بالمستودع'
      };
    }
  }

  /**
   * إلغاء ربط فرع من مستودع
   */
  public async unlinkBranchFromWarehouse(branchId: number, warehouseId: number): Promise<ApiResponse<any>> {
    try {
      console.log('🔄 إلغاء ربط فرع من مستودع:', { branchId, warehouseId });
      
      const response: AxiosResponse<ApiResponse<any>> = await api.delete(
        `${this.baseURL}/unlink`,
        {
          data: {
            branch_id: branchId,
            warehouse_id: warehouseId
          }
        }
      );
      
      if (response.data.success) {
        console.log('✅ تم إلغاء ربط الفرع من المستودع بنجاح');
      }
      
      return response.data;
    } catch (error: any) {
      console.error('❌ خطأ في إلغاء ربط الفرع من المستودع:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في إلغاء ربط الفرع من المستودع'
      };
    }
  }

  /**
   * تعيين مستودع أساسي للفرع
   */
  public async setPrimaryWarehouseForBranch(branchId: number, warehouseId: number): Promise<ApiResponse<any>> {
    try {
      console.log('🔄 تعيين مستودع أساسي للفرع:', { branchId, warehouseId });
      
      const response: AxiosResponse<ApiResponse<any>> = await api.post(
        `${this.baseURL}/set-primary`,
        {
          branch_id: branchId,
          warehouse_id: warehouseId
        }
      );
      
      if (response.data.success) {
        console.log('✅ تم تعيين المستودع الأساسي بنجاح');
      }
      
      return response.data;
    } catch (error: any) {
      console.error('❌ خطأ في تعيين المستودع الأساسي:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في تعيين المستودع الأساسي'
      };
    }
  }

  /**
   * تحديث أولوية مستودع للفرع
   */
  public async updateWarehousePriorityForBranch(
    branchId: number, 
    warehouseId: number, 
    priority: number
  ): Promise<ApiResponse<any>> {
    try {
      console.log('🔄 تحديث أولوية المستودع:', { branchId, warehouseId, priority });
      
      const response: AxiosResponse<ApiResponse<any>> = await api.put(
        `${this.baseURL}/update-priority`,
        {
          branch_id: branchId,
          warehouse_id: warehouseId,
          priority: priority
        }
      );
      
      if (response.data.success) {
        console.log('✅ تم تحديث أولوية المستودع بنجاح');
      }
      
      return response.data;
    } catch (error: any) {
      console.error('❌ خطأ في تحديث أولوية المستودع:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في تحديث أولوية المستودع'
      };
    }
  }

  /**
   * الحصول على المستودعات المرتبطة بالفرع
   */
  public async getWarehousesForBranch(
    branchId: number, 
    includeInactive: boolean = false
  ): Promise<ApiResponse<WarehouseForBranch>> {
    try {
      const response: AxiosResponse<ApiResponse<WarehouseForBranch>> = await api.get(
        `${this.baseURL}/branch/${branchId}/warehouses?include_inactive=${includeInactive}`
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في جلب مستودعات الفرع:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في جلب مستودعات الفرع'
      };
    }
  }

  /**
   * الحصول على الفروع المرتبطة بالمستودع
   */
  public async getBranchesForWarehouse(
    warehouseId: number, 
    includeInactive: boolean = false
  ): Promise<ApiResponse<BranchForWarehouse>> {
    try {
      const response: AxiosResponse<ApiResponse<BranchForWarehouse>> = await api.get(
        `${this.baseURL}/warehouse/${warehouseId}/branches?include_inactive=${includeInactive}`
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في جلب فروع المستودع:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في جلب فروع المستودع'
      };
    }
  }

  /**
   * الحصول على المستودع الأساسي للفرع
   */
  public async getPrimaryWarehouseForBranch(branchId: number): Promise<ApiResponse<WarehouseForBranch>> {
    try {
      const response: AxiosResponse<ApiResponse<WarehouseForBranch>> = await api.get(
        `${this.baseURL}/branch/${branchId}/primary-warehouse`
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في جلب المستودع الأساسي للفرع:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في جلب المستودع الأساسي للفرع'
      };
    }
  }

  /**
   * الحصول على المستودعات المتاحة للربط بالفرع
   */
  public async getAvailableWarehousesForBranch(branchId: number): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await api.get(
        `${this.baseURL}/branch/${branchId}/available-warehouses`
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في جلب المستودعات المتاحة:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في جلب المستودعات المتاحة'
      };
    }
  }

  /**
   * الحصول على الفروع المتاحة للربط بالمستودع
   */
  public async getAvailableBranchesForWarehouse(warehouseId: number): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await api.get(
        `${this.baseURL}/warehouse/${warehouseId}/available-branches`
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في جلب الفروع المتاحة:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في جلب الفروع المتاحة'
      };
    }
  }
}

// تصدير مثيل وحيد من الخدمة
export const branchWarehouseService = BranchWarehouseService.getInstance();
export default branchWarehouseService;
