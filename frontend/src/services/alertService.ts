import { customToast } from '../components/alerts/CustomToast';
import errorLogger from './errorLogger';

export interface SystemAlert {
  id: string;
  type: 'error' | 'warning' | 'info' | 'critical';
  title: string;
  message: string;
  timestamp: Date;
  source: string;
  persistent?: boolean;
  actions?: AlertAction[];
}

export interface AlertAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary' | 'danger';
}

class AlertService {
  private alerts: SystemAlert[] = [];
  private listeners: ((alerts: SystemAlert[]) => void)[] = [];
  private criticalErrorCount = 0;
  private lastCriticalError?: Date;

  constructor() {
    this.setupPeriodicHealthCheck();
    this.setupLogMonitoring();
    this.setupPeriodicCleanup();
  }

  // إضافة تنبيه جديد مع التحقق من التكرار
  addAlert(alert: Omit<SystemAlert, 'id' | 'timestamp'>): string {
    // التحقق من التنبيهات المكررة
    const isDuplicate = this.alerts.some(existingAlert =>
      existingAlert.title === alert.title &&
      existingAlert.source === alert.source &&
      existingAlert.type === alert.type &&
      new Date().getTime() - existingAlert.timestamp.getTime() < 300000 // 5 دقائق
    );

    if (isDuplicate) {
      console.debug('Duplicate alert filtered:', alert.title);
      return '';
    }

    const newAlert: SystemAlert = {
      ...alert,
      id: this.generateAlertId(),
      timestamp: new Date()
    };

    this.alerts.unshift(newAlert);

    // الحد الأقصى للتنبيهات المحفوظة
    if (this.alerts.length > 50) {
      this.alerts = this.alerts.slice(0, 50);
    }

    this.notifyListeners();

    // عرض التنبيه في الواجهة
    this.showToast(newAlert);

    // تسجيل التنبيه في السجلات (فقط للتنبيهات المهمة)
    if (alert.type === 'critical' || alert.type === 'error') {
      errorLogger.logInfo('System alert created', {
        alertId: newAlert.id,
        type: newAlert.type,
        title: newAlert.title,
        source: newAlert.source
      });
    }

    // معالجة التنبيهات الحرجة
    if (alert.type === 'critical') {
      this.handleCriticalAlert(newAlert);
    }

    return newAlert.id;
  }

  // إزالة تنبيه
  removeAlert(alertId: string): void {
    this.alerts = this.alerts.filter(alert => alert.id !== alertId);
    this.notifyListeners();
  }

  // مسح جميع التنبيهات
  clearAllAlerts(): void {
    this.alerts = [];
    this.notifyListeners();
  }

  // الحصول على جميع التنبيهات
  getAlerts(): SystemAlert[] {
    return [...this.alerts];
  }

  // الحصول على التنبيهات حسب النوع
  getAlertsByType(type: SystemAlert['type']): SystemAlert[] {
    return this.alerts.filter(alert => alert.type === type);
  }

  // الحصول على عدد التنبيهات حسب النوع مع فلترة التنبيهات القديمة
  getAlertCounts(): { total: number; critical: number; error: number; warning: number; info: number } {
    const counts = { total: 0, critical: 0, error: 0, warning: 0, info: 0 };
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 86400000); // 24 ساعة

    // فلترة التنبيهات الحديثة فقط
    const recentAlerts = this.alerts.filter(alert => alert.timestamp > oneDayAgo);

    recentAlerts.forEach(alert => {
      counts.total++;
      counts[alert.type]++;
    });

    return counts;
  }

  // تنظيف التنبيهات القديمة
  cleanupOldAlerts(): void {
    const now = new Date();
    const threeDaysAgo = new Date(now.getTime() - 259200000); // 3 أيام

    const initialCount = this.alerts.length;
    this.alerts = this.alerts.filter(alert =>
      alert.timestamp > threeDaysAgo || alert.persistent
    );

    const removedCount = initialCount - this.alerts.length;
    if (removedCount > 0) {
      console.debug(`Cleaned up ${removedCount} old alerts`);
      this.notifyListeners();
    }
  }

  // تنظيف التنبيهات الفارغة أو المعطوبة
  cleanupCorruptedAlerts(): void {
    const initialCount = this.alerts.length;

    this.alerts = this.alerts.filter(alert => {
      // التحقق من صحة التنبيه
      if (!alert.id || !alert.title || !alert.message || !alert.timestamp) {
        console.warn('Removing corrupted alert:', alert);
        return false;
      }

      // إزالة التنبيهات التي تحتوي على بيانات فارغة
      if (alert.title.trim() === '' || alert.message.trim() === '') {
        console.warn('Removing empty alert:', alert);
        return false;
      }

      return true;
    });

    const removedCount = initialCount - this.alerts.length;
    if (removedCount > 0) {
      console.log(`تم إزالة ${removedCount} تنبيهات معطوبة`);
      this.notifyListeners();
    }
  }

  // إعادة تعيين عداد الأخطاء الحرجة
  resetCriticalErrorCount(): void {
    this.criticalErrorCount = 0;
    this.lastCriticalError = undefined;
    console.log('تم إعادة تعيين عداد الأخطاء الحرجة');
  }

  // تنظيف شامل للتنبيهات
  performFullCleanup(): void {
    console.log('بدء التنظيف الشامل للتنبيهات...');

    // تنظيف التنبيهات المعطوبة
    this.cleanupCorruptedAlerts();

    // تنظيف التنبيهات القديمة
    this.cleanupOldAlerts();

    // إزالة التنبيهات المكررة من ALERT_SYSTEM
    this.removeDuplicateSystemAlerts();

    // إعادة تعيين عداد الأخطاء الحرجة
    this.resetCriticalErrorCount();

    console.log(`التنظيف الشامل مكتمل. التنبيهات المتبقية: ${this.alerts.length}`);
  }

  // إزالة التنبيهات المكررة من النظام
  private removeDuplicateSystemAlerts(): void {
    const systemAlerts = this.alerts.filter(alert => alert.source === 'ALERT_SYSTEM');

    if (systemAlerts.length > 1) {
      // الاحتفاظ بأحدث تنبيه فقط
      const latestSystemAlert = systemAlerts.reduce((latest, current) =>
        current.timestamp > latest.timestamp ? current : latest
      );

      this.alerts = this.alerts.filter(alert =>
        alert.source !== 'ALERT_SYSTEM' || alert.id === latestSystemAlert.id
      );

      console.log(`تم إزالة ${systemAlerts.length - 1} تنبيه مكرر من النظام`);
      this.notifyListeners();
    }
  }

  // الحصول على إحصائيات التنبيهات
  getAlertStatistics(): {
    totalAlerts: number;
    alertsByType: Record<string, number>;
    alertsBySource: Record<string, number>;
    recentAlerts: number;
    oldestAlert: Date | null;
    newestAlert: Date | null;
  } {
    const alertsByType: Record<string, number> = {};
    const alertsBySource: Record<string, number> = {};
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 3600000);

    let recentAlerts = 0;
    let oldestAlert: Date | null = null;
    let newestAlert: Date | null = null;

    this.alerts.forEach(alert => {
      // إحصائيات حسب النوع
      alertsByType[alert.type] = (alertsByType[alert.type] || 0) + 1;

      // إحصائيات حسب المصدر
      alertsBySource[alert.source] = (alertsBySource[alert.source] || 0) + 1;

      // التنبيهات الأخيرة
      if (alert.timestamp > oneHourAgo) {
        recentAlerts++;
      }

      // أقدم وأحدث تنبيه
      if (!oldestAlert || alert.timestamp < oldestAlert) {
        oldestAlert = alert.timestamp;
      }
      if (!newestAlert || alert.timestamp > newestAlert) {
        newestAlert = alert.timestamp;
      }
    });

    return {
      totalAlerts: this.alerts.length,
      alertsByType,
      alertsBySource,
      recentAlerts,
      oldestAlert,
      newestAlert
    };
  }

  // إضافة مستمع للتنبيهات
  addListener(listener: (alerts: SystemAlert[]) => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  // إشعار المستمعين
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener([...this.alerts]));
  }

  // توليد معرف فريد للتنبيه
  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  // عرض التنبيه كـ toast مخصص مع زر إغلاق
  private showToast(alert: SystemAlert): void {
    const duration = alert.persistent ? Infinity : this.getToastDuration(alert.type);

    // تحويل نوع التنبيه للنظام الجديد
    let alertType: 'success' | 'info' | 'warning' | 'error' | 'critical';
    switch (alert.type) {
      case 'critical':
        alertType = 'critical';
        break;
      case 'error':
        alertType = 'error';
        break;
      case 'warning':
        alertType = 'warning';
        break;
      case 'info':
      default:
        alertType = 'info';
        break;
    }

    // استخدام التنبيهات المخصصة مع زر إغلاق
    customToast[alertType](
      alert.title,
      alert.message,
      {
        duration,
        onClose: () => {
          // إزالة التنبيه من النظام عند الإغلاق
          this.removeAlert(alert.id);
        }
      }
    );
  }

  // مدة عرض التنبيه
  private getToastDuration(type: SystemAlert['type']): number {
    switch (type) {
      case 'critical':
        return 10000; // 10 ثواني
      case 'error':
        return 6000;  // 6 ثواني
      case 'warning':
        return 4000;  // 4 ثواني
      case 'info':
        return 3000;  // 3 ثواني
      default:
        return 4000;
    }
  }





  // معالجة التنبيهات الحرجة
  private handleCriticalAlert(alert: SystemAlert): void {
    // منع الحلقة المفرغة - تجاهل التنبيهات من ALERT_SYSTEM
    if (alert.source === 'ALERT_SYSTEM') {
      return;
    }

    this.criticalErrorCount++;
    this.lastCriticalError = new Date();

    // إذا كان هناك أكثر من 3 أخطاء حرجة في آخر 10 دقائق
    if (this.criticalErrorCount >= 3) {
      // التحقق من عدم وجود تنبيه مماثل بالفعل
      const existingSystemAlert = this.alerts.find(existingAlert =>
        existingAlert.source === 'ALERT_SYSTEM' &&
        existingAlert.type === 'critical' &&
        existingAlert.title.includes('أخطاء حرجة متكررة')
      );

      if (!existingSystemAlert) {
        this.addAlert({
          type: 'error', // ⚠️ تغيير من critical إلى error لمنع الحلقة المفرغة
          title: 'تحذير: أخطاء حرجة متكررة',
          message: `تم رصد ${this.criticalErrorCount} أخطاء حرجة. يُنصح بفحص النظام فوراً.`,
          source: 'ALERT_SYSTEM',
          persistent: true,
          actions: [
            {
              label: 'فحص النظام',
              action: () => this.runEmergencyDiagnostics(),
              style: 'primary'
            },
            {
              label: 'إرسال تقرير',
              action: () => this.sendEmergencyReport(),
              style: 'secondary'
            }
          ]
        });
      }
    }

    // تسجيل الخطأ الحرج
    errorLogger.logCritical('Critical alert triggered', {
      alertId: alert.id,
      criticalErrorCount: this.criticalErrorCount,
      lastCriticalError: this.lastCriticalError
    });
  }

  // فحص طارئ للنظام
  private async runEmergencyDiagnostics(): Promise<void> {
    try {
      const diagnostics = await errorLogger.runSystemDiagnostics();

      this.addAlert({
        type: 'info',
        title: 'تم إجراء فحص طارئ للنظام',
        message: 'تم الانتهاء من الفحص الطارئ. راجع نتائج التشخيص.',
        source: 'EMERGENCY_DIAGNOSTICS'
      });

      // محاولة إصلاح تلقائي
      await this.attemptAutoRepair(diagnostics);
    } catch (error) {
      this.addAlert({
        type: 'error',
        title: 'فشل الفحص الطارئ',
        message: 'لم يتمكن النظام من إجراء الفحص الطارئ.',
        source: 'EMERGENCY_DIAGNOSTICS'
      });
    }
  }

  // إرسال تقرير طارئ
  private async sendEmergencyReport(): Promise<void> {
    try {
      // جمع معلومات النظام
      const systemInfo = {
        criticalErrorCount: this.criticalErrorCount,
        lastCriticalError: this.lastCriticalError,
        alerts: this.alerts.filter(alert => alert.type === 'critical'),
        timestamp: new Date().toISOString()
      };

      // إرسال التقرير (يمكن تطوير هذا لاحقاً)
      console.log('Emergency report:', systemInfo);

      this.addAlert({
        type: 'info',
        title: 'تم إرسال التقرير الطارئ',
        message: 'تم إرسال تقرير الأخطاء الحرجة للدعم الفني.',
        source: 'EMERGENCY_REPORT'
      });
    } catch (error) {
      this.addAlert({
        type: 'error',
        title: 'فشل إرسال التقرير',
        message: 'لم يتمكن النظام من إرسال التقرير الطارئ.',
        source: 'EMERGENCY_REPORT'
      });
    }
  }

  // محاولة إصلاح تلقائي
  private async attemptAutoRepair(diagnostics: any): Promise<void> {
    let repairsAttempted = 0;

    // إصلاح مشاكل الذاكرة
    if (diagnostics.performance?.memory?.usedJSHeapSize > diagnostics.performance?.memory?.jsHeapSizeLimit * 0.9) {
      try {
        if (window.gc) {
          window.gc();
          repairsAttempted++;
        }
      } catch (error) {
        console.warn('Could not trigger garbage collection:', error);
      }
    }

    // إصلاح مشاكل التخزين
    if (diagnostics.storage?.localStorage?.available === false) {
      try {
        localStorage.clear();
        repairsAttempted++;
      } catch (error) {
        console.warn('Could not clear localStorage:', error);
      }
    }

    if (repairsAttempted > 0) {
      this.addAlert({
        type: 'info',
        title: 'تم تطبيق إصلاحات تلقائية',
        message: `تم تطبيق ${repairsAttempted} إصلاحات تلقائية للنظام.`,
        source: 'AUTO_REPAIR'
      });
    }
  }

  // مراقبة السجلات وإنشاء تنبيهات - معطل لتوفير موارد النظام
  private setupLogMonitoring(): void {
    // تعطيل فحص السجلات الدوري لتوفير موارد النظام
    // setInterval(async () => {
    //   try {
    //     const logs = await errorLogger.getSystemLogs();

    //     // فحص السجلات الحرجة الجديدة (آخر 5 دقائق)
    //     const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    //     const recentCriticalLogs = logs.filter((log: any) =>
    //       log.level === 'CRITICAL' &&
    //       new Date(log.timestamp) > fiveMinutesAgo
    //     );

    //     // إنشاء تنبيهات للسجلات الحرجة الجديدة (مع منع التكرار المحسن)
    //     if (recentCriticalLogs.length > 0) {
    //       // التحقق من عدم وجود تنبيه مماثل بالفعل (فترة أطول)
    //       const existingCriticalAlert = this.alerts.find(alert =>
    //         (alert.title === 'خطأ حرج في النظام' || alert.source === 'SYSTEM_LOGS') &&
    //         alert.type === 'critical' &&
    //         new Date().getTime() - alert.timestamp.getTime() < 900000 // آخر 15 دقيقة
    //       );

    //       // تحقق من عدد التنبيهات الحرجة الحالية
    //       const currentCriticalCount = this.alerts.filter(alert =>
    //         alert.type === 'critical' &&
    //         alert.source !== 'ALERT_SYSTEM'
    //       ).length;

    //       // إنشاء التنبيه فقط إذا لم يكن موجوداً وعدد التنبيهات الحرجة أقل من 3
    //       if (!existingCriticalAlert && currentCriticalCount < 3) {
    //         this.addAlert({
    //           type: 'critical',
    //           title: 'خطأ حرج في النظام',
    //           message: recentCriticalLogs.length === 1
    //             ? (recentCriticalLogs[0].message || 'تم رصد خطأ حرج في النظام')
    //             : `تم رصد ${recentCriticalLogs.length} أخطاء حرجة في النظام`,
    //           source: 'SYSTEM_LOGS',
    //           persistent: false, // ⚠️ تغيير إلى false لتجنب التراكم
    //           actions: [
    //             {
    //               label: 'عرض السجلات',
    //               action: () => window.open('/reports?tab=system&subtab=system-logs&logtab=logs&focus=true', '_blank'),
    //               style: 'primary'
    //             },
    //             {
    //               label: 'إرسال للدعم',
    //               action: () => this.sendEmergencyReport(),
    //               style: 'secondary'
    //             }
    //           ]
    //         });
    //       }
    //     }

    //     // فحص الأخطاء المتكررة
    //     const recentErrorLogs = logs.filter((log: any) =>
    //       log.level === 'ERROR' &&
    //       new Date(log.timestamp) > fiveMinutesAgo
    //     );

    //     if (recentErrorLogs.length >= 5) {
    //       this.addAlert({
    //         type: 'warning',
    //         title: 'أخطاء متكررة',
    //         message: `تم رصد ${recentErrorLogs.length} أخطاء في آخر 5 دقائق`,
    //         source: 'MONITORING',
    //         actions: [
    //           {
    //             label: 'فحص السجلات',
    //             action: () => window.open('/reports?tab=system&subtab=system-logs&logtab=logs', '_blank'),
    //             style: 'primary'
    //           }
    //         ]
    //       });
    //     }

    //   } catch (error) {
    //     console.warn('Log monitoring failed:', error);
    //   }
    // }, 60000); // كل دقيقة - معطل لتوفير موارد النظام
  }

  // فحص دوري لحالة النظام
  private setupPeriodicHealthCheck(): void {
    setInterval(async () => {
      try {
        // فحص إحصائيات الأخطاء المحلية
        const errorStats = errorLogger.getErrorStatistics();
        const errorQuality = errorLogger.assessErrorQuality();

        // فحص جودة الأخطاء
        if (errorQuality.qualityScore < 70) {
          this.addAlert({
            type: 'warning',
            title: 'جودة الأخطاء منخفضة',
            message: `نقاط الجودة: ${errorQuality.qualityScore}/100. ${errorQuality.issues.join(', ')}`,
            source: 'ERROR_QUALITY',
            actions: [
              {
                label: 'عرض التفاصيل',
                action: () => window.open('/reports?tab=system&subtab=system-logs&logtab=logs&focus=true', '_blank'),
                style: 'primary'
              }
            ]
          });
        }

        // فحص الأخطاء المكررة
        const duplicateErrors = errorLogger.getDuplicateErrors();
        if (duplicateErrors.length > 5) {
          this.addAlert({
            type: 'warning',
            title: 'أخطاء مكررة مكتشفة',
            message: `يوجد ${duplicateErrors.length} أخطاء مكررة. أكثرها تكراراً: "${duplicateErrors[0]?.message}" (${duplicateErrors[0]?.count} مرة)`,
            source: 'DUPLICATE_ERRORS',
            actions: [
              {
                label: 'عرض الأخطاء المكررة',
                action: () => {
                  console.table(duplicateErrors);
                  window.open('/reports?tab=system&subtab=system-logs&logtab=logs&focus=true', '_blank');
                },
                style: 'primary'
              }
            ]
          });
        }

        // فحص الأخطاء الحرجة (مع منع التكرار المحسن)
        if (errorStats.criticalErrors > 0) {
          // التحقق من عدم وجود تنبيه مماثل بالفعل (فترة أطول)
          const existingCriticalErrorAlert = this.alerts.find(alert =>
            alert.source === 'CRITICAL_ERRORS' &&
            alert.type === 'critical' &&
            new Date().getTime() - alert.timestamp.getTime() < 3600000 // آخر ساعة كاملة
          );

          // تحقق إضافي من عدد الأخطاء الحرجة الفعلية
          const actualCriticalAlerts = this.alerts.filter(alert =>
            alert.type === 'critical' &&
            alert.source !== 'ALERT_SYSTEM' &&
            alert.source !== 'CRITICAL_ERRORS'
          ).length;

          // إنشاء التنبيه فقط إذا لم يكن موجوداً ولديه أخطاء حرجة فعلية
          if (!existingCriticalErrorAlert && actualCriticalAlerts > 0) {
            this.addAlert({
              type: 'warning', // ⚠️ تغيير من critical إلى warning لمنع التكرار
              title: 'أخطاء حرجة مكتشفة',
              message: `يوجد ${actualCriticalAlerts} أخطاء حرجة تحتاج انتباه فوري.`,
              source: 'CRITICAL_ERRORS',
              persistent: false, // ⚠️ تغيير إلى false لتجنب التراكم
              actions: [
                {
                  label: 'عرض الأخطاء الحرجة',
                  action: () => window.open('/reports?tab=system&subtab=system-logs&logtab=logs&focus=true', '_blank'),
                  style: 'primary'
                },
                {
                  label: 'إرسال للدعم',
                  action: () => this.sendEmergencyReport(),
                  style: 'secondary'
                }
              ]
            });
          }
        }

        // فحص الأخطاء الأخيرة
        if (errorStats.recentErrors > 15) {
          this.addAlert({
            type: 'error',
            title: 'نشاط أخطاء مرتفع',
            message: `تم رصد ${errorStats.recentErrors} أخطاء في الساعة الأخيرة.`,
            source: 'HIGH_ERROR_ACTIVITY',
            actions: [
              {
                label: 'مراقبة النظام',
                action: () => window.open('/reports?tab=system&subtab=monitor', '_blank'),
                style: 'primary'
              }
            ]
          });
        }

        // تنظيف الأخطاء القديمة
        errorLogger.cleanupOldErrors();

        // إعادة تعيين عداد الأخطاء الحرجة كل ساعة
        if (this.lastCriticalError &&
            new Date().getTime() - this.lastCriticalError.getTime() > 3600000) {
          this.criticalErrorCount = 0;
        }

      } catch (error) {
        console.warn('Health check failed:', error);
      }
    }, 300000); // كل 5 دقائق
  }

  // إعداد تنظيف دوري للتنبيهات القديمة (محسن)
  private setupPeriodicCleanup(): void {
    // تنظيف التنبيهات القديمة كل 30 دقيقة
    setInterval(() => {
      this.cleanupOldAlerts();
      this.removeDuplicateSystemAlerts();

      // تنظيف الأخطاء القديمة من errorLogger أيضاً
      errorLogger.cleanupOldErrors();

      // تنظيف إضافي للتنبيهات الحرجة المتراكمة
      const criticalAlerts = this.alerts.filter(alert => alert.type === 'critical');
      if (criticalAlerts.length > 5) {
        // الاحتفاظ بأحدث 3 تنبيهات حرجة فقط
        const sortedCritical = criticalAlerts.sort((a, b) =>
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        );

        const toRemove = sortedCritical.slice(3);
        toRemove.forEach(alert => {
          this.removeAlert(alert.id);
        });

        if (toRemove.length > 0) {
          console.log(`تم إزالة ${toRemove.length} تنبيه حرج قديم`);
        }
      }

      // إعادة تعيين العداد إذا لم تكن هناك أخطاء حرجة فعلية
      const actualCriticalAlerts = this.alerts.filter(alert =>
        alert.type === 'critical' &&
        alert.source !== 'ALERT_SYSTEM' &&
        alert.source !== 'CRITICAL_ERRORS'
      );

      if (actualCriticalAlerts.length === 0) {
        this.resetCriticalErrorCount();
      }

      // إحصائيات للمراقبة
      const stats = this.getAlertStatistics();
      if (stats.totalAlerts > 50) {
        console.warn(`عدد كبير من التنبيهات: ${stats.totalAlerts}. يُنصح بالفحص.`);

        // تنظيف قوي إذا كان العدد كبير جداً
        if (stats.totalAlerts > 100) {
          this.performFullCleanup();
        }
      }

    }, 1800000); // كل 30 دقيقة
  }

  // تنبيهات مخصصة للأخطاء الشائعة مع تحسينات
  createNetworkErrorAlert(details?: string): void {
    this.addAlert({
      type: 'error',
      title: 'مشكلة في الاتصال',
      message: details || 'تعذر الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت.',
      source: 'NETWORK',
      actions: [
        {
          label: 'إعادة المحاولة',
          action: () => window.location.reload(),
          style: 'primary'
        },
        {
          label: 'فحص الشبكة',
          action: () => window.open('/reports?tab=system&subtab=monitor', '_blank'),
          style: 'secondary'
        }
      ]
    });
  }

  createDatabaseErrorAlert(details?: string): void {
    this.addAlert({
      type: 'critical',
      title: 'خطأ في قاعدة البيانات',
      message: details || 'حدث خطأ في قاعدة البيانات. قد تتأثر بعض الوظائف.',
      source: 'DATABASE',
      persistent: true,
      actions: [
        {
          label: 'تحديث الصفحة',
          action: () => window.location.reload(),
          style: 'primary'
        },
        {
          label: 'فحص السجلات',
          action: () => window.open('/reports?tab=system&subtab=logs', '_blank'),
          style: 'secondary'
        },
        {
          label: 'إرسال تقرير',
          action: () => this.sendEmergencyReport(),
          style: 'secondary'
        }
      ]
    });
  }

  createAuthErrorAlert(reason?: string): void {
    this.addAlert({
      type: 'warning',
      title: 'مشكلة في المصادقة',
      message: reason || 'انتهت صلاحية جلسة المستخدم. يرجى تسجيل الدخول مرة أخرى.',
      source: 'AUTH',
      actions: [
        {
          label: 'تسجيل الدخول',
          action: () => window.location.href = '/login',
          style: 'primary'
        },
        {
          label: 'إعدادات المستخدمين',
          action: () => window.open('/settings?tab=users', '_blank'),
          style: 'secondary'
        }
      ]
    });
  }

  // تنبيهات جديدة للأخطاء المختلفة
  createPerformanceAlert(performanceLevel: number): void {
    const severity = performanceLevel < 30 ? 'critical' : performanceLevel < 60 ? 'error' : 'warning';

    this.addAlert({
      type: severity,
      title: 'تحذير أداء النظام',
      message: `أداء النظام منخفض (${performanceLevel}%). قد تواجه بطء في الاستجابة.`,
      source: 'SYSTEM',
      actions: [
        {
          label: 'مراقب الأداء',
          action: () => window.open('/reports?tab=system&subtab=monitor', '_blank'),
          style: 'primary'
        },
        {
          label: 'تحسين تلقائي',
          action: () => this.attemptAutoRepair({}),
          style: 'secondary'
        }
      ]
    });
  }

  createInventoryAlert(productName: string, currentStock: number, minStock: number): void {
    this.addAlert({
      type: 'warning',
      title: 'تحذير مخزون منخفض',
      message: `المنتج "${productName}" وصل إلى الحد الأدنى للمخزون (${currentStock}/${minStock}).`,
      source: 'INVENTORY',
      actions: [
        {
          label: 'إدارة المنتجات',
          action: () => window.open('/products', '_blank'),
          style: 'primary'
        },
        {
          label: 'تقرير المخزون',
          action: () => window.open('/reports?tab=inventory', '_blank'),
          style: 'secondary'
        }
      ]
    });
  }

  createSalesAlert(message: string, invoiceId?: string): void {
    this.addAlert({
      type: 'info',
      title: 'تنبيه مبيعات',
      message: message,
      source: 'SALES',
      actions: [
        {
          label: 'صفحة المبيعات',
          action: () => window.open('/sales', '_blank'),
          style: 'primary'
        },
        ...(invoiceId ? [{
          label: 'عرض الفاتورة',
          action: () => window.open(`/sales/invoice/${invoiceId}`, '_blank'),
          style: 'secondary' as const
        }] : [])
      ]
    });
  }

  createCustomerDebtAlert(customerName: string, debtAmount: number): void {
    this.addAlert({
      type: 'warning',
      title: 'تحذير ديون العملاء',
      message: `العميل "${customerName}" لديه دين مستحق بقيمة ${debtAmount} د.ل.`,
      source: 'CUSTOMER',
      actions: [
        {
          label: 'إدارة العملاء',
          action: () => window.open('/customers', '_blank'),
          style: 'primary'
        },
        {
          label: 'تقرير الديون',
          action: () => window.open('/reports?tab=debts', '_blank'),
          style: 'secondary'
        }
      ]
    });
  }
}

// إنشاء مثيل واحد للاستخدام في جميع أنحاء التطبيق
const alertService = new AlertService();

// إتاحة الخدمة في وحدة التحكم للتطوير
if (typeof window !== 'undefined') {
  (window as any).alertService = alertService;
}

export { alertService };
export default alertService;
