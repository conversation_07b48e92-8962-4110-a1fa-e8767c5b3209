/**
 * خدمة معاينة الروابط
 * تستخرج معلومات الروابط مثل العنوان والوصف والصورة
 */

export interface LinkPreview {
  url: string;
  title?: string;
  description?: string;
  image?: string;
  siteName?: string;
  favicon?: string;
  domain: string;
  isLoading: boolean;
  error?: string;
  type?: 'website' | 'video' | 'image';
  videoId?: string;
  embedUrl?: string;
}

class LinkPreviewService {
  private cache = new Map<string, LinkPreview>();
  private pendingRequests = new Map<string, Promise<LinkPreview>>();

  /**
   * الحصول على معاينة الرابط
   */
  async getPreview(url: string): Promise<LinkPreview> {
    // التحقق من الكاش أولاً
    if (this.cache.has(url)) {
      return this.cache.get(url)!;
    }

    // التحقق من الطلبات المعلقة
    if (this.pendingRequests.has(url)) {
      return this.pendingRequests.get(url)!;
    }

    // إنشاء طلب جديد
    const request = this.fetchPreview(url);
    this.pendingRequests.set(url, request);

    try {
      const preview = await request;
      this.cache.set(url, preview);
      return preview;
    } finally {
      this.pendingRequests.delete(url);
    }
  }

  /**
   * استخراج معلومات الرابط
   */
  private async fetchPreview(url: string): Promise<LinkPreview> {
    const domain = this.extractDomain(url);
    
    const preview: LinkPreview = {
      url,
      domain,
      isLoading: true
    };

    try {
      // استخدام خدمة proxy لتجنب مشاكل CORS
      const response = await fetch(`/api/link-preview?url=${encodeURIComponent(url)}`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      
      return {
        ...preview,
        title: data.title || this.generateFallbackTitle(url),
        description: data.description,
        image: data.image,
        siteName: data.siteName || domain,
        favicon: data.favicon || `https://www.google.com/s2/favicons?domain=${domain}`,
        type: data.type || 'website',
        videoId: data.videoId,
        embedUrl: data.embedUrl,
        isLoading: false
      };

    } catch (error) {
      console.warn('فشل في جلب معاينة الرابط:', error);
      
      return {
        ...preview,
        title: this.generateFallbackTitle(url),
        siteName: domain,
        favicon: `https://www.google.com/s2/favicons?domain=${domain}`,
        isLoading: false,
        error: 'فشل في تحميل المعاينة'
      };
    }
  }

  /**
   * استخراج النطاق من الرابط
   */
  private extractDomain(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace('www.', '');
    } catch {
      return url;
    }
  }

  /**
   * إنشاء عنوان احتياطي
   */
  private generateFallbackTitle(url: string): string {
    const domain = this.extractDomain(url);
    return domain.charAt(0).toUpperCase() + domain.slice(1);
  }

  /**
   * مسح الكاش
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * إزالة رابط من الكاش
   */
  removeFromCache(url: string): void {
    this.cache.delete(url);
  }
}

// إنشاء instance واحد للخدمة
export const linkPreviewService = new LinkPreviewService();

/**
 * Hook لاستخدام معاينة الروابط
 */
import { useState, useEffect } from 'react';

export const useLinkPreview = (url: string) => {
  const [preview, setPreview] = useState<LinkPreview>({
    url,
    domain: linkPreviewService['extractDomain'](url),
    isLoading: true
  });

  useEffect(() => {
    let isMounted = true;

    const loadPreview = async () => {
      try {
        const result = await linkPreviewService.getPreview(url);
        if (isMounted) {
          setPreview(result);
        }
      } catch (error) {
        if (isMounted) {
          setPreview(prev => ({
            ...prev,
            isLoading: false,
            error: 'فشل في تحميل المعاينة'
          }));
        }
      }
    };

    loadPreview();

    return () => {
      isMounted = false;
    };
  }, [url]);

  return preview;
};
