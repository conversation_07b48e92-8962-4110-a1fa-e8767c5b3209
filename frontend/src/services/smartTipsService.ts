/**
 * خدمة النصائح الذكية - SmartTipsService
 * تدير عرض النصائح التفاعلية للمستخدمين حسب نوعهم والوقت والسلوك
 * تطبق مبادئ البرمجة الكائنية مع نمط Singleton
 * 
 * الميزات:
 * - نصائح مخصصة حسب نوع المستخدم (مدير/موظف)
 * - نصائح حسب الوقت (صباح/مساء/ليل)
 * - نصائح حسب السلوك والاستخدام
 * - نظام دوران ذكي للنصائح
 * - حفظ تفضيلات المستخدم
 */

import { useAuthStore } from '../stores/authStore';

// أنواع النصائح
export type TipCategory = 'welcome' | 'productivity' | 'features' | 'shortcuts' | 'motivation' | 'business';
export type UserType = 'admin' | 'employee' | 'new_user';
export type TimeOfDay = 'morning' | 'afternoon' | 'evening' | 'night';

// واجهة النصيحة
export interface SmartTip {
  id: string;
  category: TipCategory;
  userTypes: UserType[];
  timeOfDay?: TimeOfDay[];
  title: string;
  message: string;
  icon?: string;
  priority: number; // 1-10 (10 = أعلى أولوية)
  frequency: 'once' | 'daily' | 'weekly' | 'always'; // معدل العرض
  conditions?: {
    minDaysActive?: number; // الحد الأدنى لأيام النشاط
    maxTimesShown?: number; // الحد الأقصى لعدد مرات العرض
    requiresFeature?: string; // يتطلب ميزة معينة
  };
}

// إعدادات النصائح
interface TipsSettings {
  enabled: boolean;
  showFrequency: 'high' | 'medium' | 'low';
  preferredCategories: TipCategory[];
  lastShownTips: string[];
  tipHistory: { [tipId: string]: { count: number; lastShown: Date } };
}

export class SmartTipsService {
  private static instance: SmartTipsService;
  private tips: SmartTip[] = [];
  private settings: TipsSettings;
  private currentTip: SmartTip | null = null;
  private tipChangeInterval: NodeJS.Timeout | null = null;

  // النصيحة الثابتة التي تظهر في حالة عدم وجود نصائح أخرى
  private readonly DEFAULT_FALLBACK_TIP: SmartTip = {
    id: 'default_fallback_tip',
    category: 'welcome',
    userTypes: ['admin', 'employee', 'new_user'],
    title: 'مرحباً بك في SmartPOS!',
    message: 'أهلاً بك! هنا ستجد نصائح، تنبيهات، وتحذيرات لمساعدتك على تحقيق أقصى استفادة من النظام.',
    icon: 'FiMessageCircle',
    priority: 1,
    frequency: 'always',
  };

  private constructor() {
    this.settings = this.loadSettings();
    this.initializeTips();
    // إزالة التدوير التلقائي لتوفير الموارد
  }

  public static getInstance(): SmartTipsService {
    if (!SmartTipsService.instance) {
      SmartTipsService.instance = new SmartTipsService();
    }
    return SmartTipsService.instance;
  }

  /**
   * تهيئة النصائح الافتراضية
   */
  private initializeTips(): void {
    this.tips = [
      // تم إزالة النصيحة التجريبية، واستبدالها بنصيحة ثابتة
      // نصائح الترحيب
      {
        id: 'welcome_morning_admin',
        category: 'welcome',
        userTypes: ['admin'],
        timeOfDay: ['morning'],
        title: 'صباح الخير!',
        message: 'ابدأ يومك بمراجعة تقرير المبيعات اليومي لتتبع أداء متجرك',
        icon: 'FiSun',
        priority: 8,
        frequency: 'daily'
      },
      {
        id: 'welcome_morning_employee',
        category: 'welcome',
        userTypes: ['employee'],
        timeOfDay: ['morning'],
        title: 'صباح النشاط!',
        message: 'تحقق من المنتجات منخفضة المخزون قبل بدء العمل',
        icon: 'FiPackage',
        priority: 7,
        frequency: 'daily'
      },
      {
        id: 'welcome_evening',
        category: 'welcome',
        userTypes: ['admin', 'employee'],
        timeOfDay: ['evening'],
        title: 'مساء الخير!',
        message: 'لا تنس مراجعة مبيعات اليوم وإغلاق الصندوق',
        icon: 'FiMoon',
        priority: 8,
        frequency: 'daily'
      },

      // نصائح الإنتاجية
      {
        id: 'productivity_shortcuts',
        category: 'shortcuts',
        userTypes: ['admin', 'employee'],
        title: 'اختصارات سريعة',
        message: 'استخدم Ctrl+N لإضافة منتج جديد بسرعة، أو F2 لفتح نقطة البيع',
        icon: 'FiZap',
        priority: 6,
        frequency: 'weekly'
      },
      {
        id: 'productivity_search',
        category: 'features',
        userTypes: ['admin', 'employee'],
        title: 'البحث السريع',
        message: 'يمكنك البحث عن المنتجات بالاسم أو الباركود مباشرة في نقطة البيع',
        icon: 'FiSearch',
        priority: 7,
        frequency: 'weekly'
      },

      // نصائح الأعمال
      {
        id: 'business_reports',
        category: 'business',
        userTypes: ['admin'],
        title: 'تحليل الأداء',
        message: 'راجع تقارير المبيعات الأسبوعية لتحديد المنتجات الأكثر ربحية',
        icon: 'FiBarChart',
        priority: 9,
        frequency: 'weekly'
      },
      {
        id: 'business_inventory',
        category: 'business',
        userTypes: ['admin'],
        title: 'إدارة المخزون',
        message: 'تابع المنتجات منخفضة المخزون لتجنب نفاد الأصناف المهمة',
        icon: 'FiPackage',
        priority: 8,
        frequency: 'daily'
      },

      // نصائح التحفيز
      {
        id: 'motivation_success',
        category: 'motivation',
        userTypes: ['admin', 'employee'],
        title: 'استمر في التميز!',
        message: 'كل عملية بيع ناجحة تقربك من تحقيق أهدافك التجارية',
        icon: 'FiTrendingUp',
        priority: 5,
        frequency: 'daily'
      },
      {
        id: 'motivation_teamwork',
        category: 'motivation',
        userTypes: ['employee'],
        title: 'العمل الجماعي',
        message: 'تعاونك مع الفريق يساهم في نجاح المتجر وتحقيق الأهداف',
        icon: 'FiUsers',
        priority: 6,
        frequency: 'weekly'
      },

      // نصائح إضافية متنوعة
      {
        id: 'welcome_afternoon_admin',
        category: 'welcome',
        userTypes: ['admin'],
        timeOfDay: ['afternoon'],
        title: 'وقت المراجعة',
        message: 'هذا وقت مثالي لمراجعة أداء المبيعات ومتابعة فريق العمل',
        icon: 'FiBarChart',
        priority: 7,
        frequency: 'daily'
      },
      {
        id: 'productivity_backup',
        category: 'features',
        userTypes: ['admin'],
        title: 'النسخ الاحتياطي',
        message: 'لا تنس عمل نسخة احتياطية من بياناتك بانتظام لحماية معلومات مؤسستك',
        icon: 'FiShield',
        priority: 8,
        frequency: 'weekly'
      },
      {
        id: 'business_customer_service',
        category: 'business',
        userTypes: ['admin', 'employee'],
        title: 'خدمة العملاء',
        message: 'العميل السعيد يعود دائماً، اهتم بتجربة العميل في كل عملية شراء',
        icon: 'FiUsers',
        priority: 7,
        frequency: 'daily'
      },
      {
        id: 'shortcuts_pos_quick',
        category: 'shortcuts',
        userTypes: ['admin', 'employee'],
        title: 'نقطة البيع السريعة',
        message: 'اضغط على Enter بعد إدخال الباركود لإضافة المنتج بسرعة',
        icon: 'FiZap',
        priority: 6,
        frequency: 'weekly'
      },
      {
        id: 'motivation_daily_goal',
        category: 'motivation',
        userTypes: ['admin', 'employee'],
        timeOfDay: ['morning'],
        title: 'هدف اليوم',
        message: 'ضع هدفاً واضحاً لمبيعات اليوم واعمل على تحقيقه خطوة بخطوة',
        icon: 'FiTrendingUp',
        priority: 8,
        frequency: 'daily'
      }
    ];
  }

  /**
   * الحصول على نصيحة ذكية مناسبة
   */
  public getSmartTip(): SmartTip | null {
    try {
      const { user } = useAuthStore.getState();

      if (!user || !this.settings.enabled) {
        return null;
      }

      const userType = this.getUserType(user);
      const timeOfDay = this.getTimeOfDay();

      // فلترة النصائح المناسبة
      const suitableTips = this.tips.filter(tip =>
        this.isTipSuitable(tip, userType, timeOfDay)
      );

      if (suitableTips.length === 0) {
        // في حالة عدم وجود نصيحة ذكية، يتم عرض النصيحة الثابتة
        return this.DEFAULT_FALLBACK_TIP;
      }

      // ترتيب النصائح حسب الأولوية والتكرار
      const sortedTips = suitableTips.sort((a, b) => {
        const aScore = this.calculateTipScore(a);
        const bScore = this.calculateTipScore(b);
        return bScore - aScore;
      });

      const selectedTip = sortedTips[0];
      this.recordTipShown(selectedTip);

      return selectedTip;
    } catch (error) {
      console.error('خطأ في الحصول على النصيحة الذكية:', error);
      // في حالة حدوث خطأ، يتم عرض النصيحة الثابتة أيضاً
      return this.DEFAULT_FALLBACK_TIP;
    }
  }

  /**
   * تحديد نوع المستخدم
   */
  private getUserType(user: any): UserType {
    if (!user.role) {
      return 'new_user';
    }

    // التحقق من نوع role - قد يكون object أو string
    const roleName = typeof user.role === 'object' ? user.role.name : user.role;

    switch (roleName?.toLowerCase()) {
      case 'admin':
        return 'admin';
      case 'employee':
      case 'user':
        return 'employee';
      default:
        return 'new_user';
    }
  }

  /**
   * تحديد وقت اليوم
   */
  private getTimeOfDay(): TimeOfDay {
    const hour = new Date().getHours();
    
    if (hour >= 5 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 17) return 'afternoon';
    if (hour >= 17 && hour < 22) return 'evening';
    return 'night';
  }

  /**
   * فحص مناسبة النصيحة
   */
  private isTipSuitable(tip: SmartTip, userType: UserType, timeOfDay: TimeOfDay): boolean {
    // فحص نوع المستخدم
    if (!tip.userTypes.includes(userType)) {
      return false;
    }

    // فحص وقت اليوم (إذا كان محدد)
    if (tip.timeOfDay && !tip.timeOfDay.includes(timeOfDay)) {
      return false;
    }

    // فحص التكرار
    if (!this.checkTipFrequency(tip)) {
      return false;
    }

    // فحص الشروط الإضافية
    if (tip.conditions && !this.checkTipConditions(tip.conditions)) {
      return false;
    }

    return true;
  }

  /**
   * حساب نقاط النصيحة للترتيب
   */
  private calculateTipScore(tip: SmartTip): number {
    let score = tip.priority;
    
    // تقليل النقاط للنصائح المعروضة مؤخراً
    const history = this.settings.tipHistory[tip.id];
    if (history) {
      const daysSinceLastShown = Math.floor(
        (Date.now() - history.lastShown.getTime()) / (1000 * 60 * 60 * 24)
      );
      
      if (daysSinceLastShown < 1) score -= 3;
      else if (daysSinceLastShown < 3) score -= 1;
    }

    return score;
  }

  /**
   * فحص تكرار عرض النصيحة
   */
  private checkTipFrequency(tip: SmartTip): boolean {
    const history = this.settings.tipHistory[tip.id];
    if (!history) return true;

    const now = new Date();
    const lastShown = history.lastShown;
    const daysSinceLastShown = Math.floor(
      (now.getTime() - lastShown.getTime()) / (1000 * 60 * 60 * 24)
    );

    switch (tip.frequency) {
      case 'once':
        return history.count === 0;
      case 'daily':
        return daysSinceLastShown >= 1;
      case 'weekly':
        return daysSinceLastShown >= 7;
      case 'always':
        return true;
      default:
        return true;
    }
  }

  /**
   * فحص الشروط الإضافية للنصيحة
   */
  private checkTipConditions(conditions: SmartTip['conditions']): boolean {
    if (!conditions) return true;

    // فحص الحد الأقصى لعدد مرات العرض
    if (conditions.maxTimesShown) {
      const history = this.settings.tipHistory[this.currentTip?.id || ''];
      if (history && history.count >= conditions.maxTimesShown) {
        return false;
      }
    }

    return true;
  }

  /**
   * تسجيل عرض النصيحة
   */
  private recordTipShown(tip: SmartTip): void {
    if (!this.settings.tipHistory[tip.id]) {
      this.settings.tipHistory[tip.id] = { count: 0, lastShown: new Date() };
    }

    this.settings.tipHistory[tip.id].count++;
    this.settings.tipHistory[tip.id].lastShown = new Date();
    
    // إضافة للنصائح المعروضة مؤخراً
    this.settings.lastShownTips.unshift(tip.id);
    if (this.settings.lastShownTips.length > 10) {
      this.settings.lastShownTips = this.settings.lastShownTips.slice(0, 10);
    }

    this.saveSettings();
  }

  /**
   * الحصول على النصيحة الحالية
   */
  public getCurrentTip(): SmartTip | null {
    if (!this.currentTip) {
      this.currentTip = this.getSmartTip();
    }
    return this.currentTip;
  }

  /**
   * تحديث إعدادات النصائح
   */
  public updateSettings(newSettings: Partial<TipsSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    this.saveSettings();
  }

  /**
   * تحميل الإعدادات من التخزين المحلي
   */
  private loadSettings(): TipsSettings {
    try {
      const saved = localStorage.getItem('smartTipsSettings');
      if (saved) {
        const parsed = JSON.parse(saved);
        // تحويل التواريخ من strings إلى Date objects
        if (parsed.tipHistory) {
          Object.keys(parsed.tipHistory).forEach(key => {
            parsed.tipHistory[key].lastShown = new Date(parsed.tipHistory[key].lastShown);
          });
        }
        return parsed;
      }
    } catch (error) {
      console.warn('فشل في تحميل إعدادات النصائح:', error);
    }

    // الإعدادات الافتراضية
    return {
      enabled: true,
      showFrequency: 'medium',
      preferredCategories: ['welcome', 'productivity', 'business'],
      lastShownTips: [],
      tipHistory: {}
    };
  }

  /**
   * حفظ الإعدادات في التخزين المحلي
   */
  private saveSettings(): void {
    try {
      localStorage.setItem('smartTipsSettings', JSON.stringify(this.settings));
    } catch (error) {
      console.error('فشل في حفظ إعدادات النصائح:', error);
    }
  }

  /**
   * تنظيف الموارد
   */
  public destroy(): void {
    if (this.tipChangeInterval) {
      clearInterval(this.tipChangeInterval);
      this.tipChangeInterval = null;
    }
  }
}

// تصدير المثيل الوحيد
export const smartTipsService = SmartTipsService.getInstance();