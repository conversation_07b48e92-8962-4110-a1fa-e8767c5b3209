/**
 * خدمة تنسيق الأرقام المختصرة
 * تتعامل مع تحويل الأرقام الكبيرة إلى تنسيق مختصر مع دعم العربية والإنجليزية
 * تطبق مبادئ البرمجة الكائنية (OOP) مع نمط Singleton
 */

import { numberFormattingService } from './numberFormattingService';

// أنواع الوحدات المدعومة
export type UnitType = 'arabic' | 'english';

// واجهة إعدادات الاختصار
export interface CompactNumberSettings {
  unitType: UnitType;
  compactThreshold: number;
  decimalPlaces: number;
  showFullNumber: boolean;
}

// واجهة نتيجة التنسيق
export interface CompactNumberResult {
  compact: string;
  full: string;
  isCompacted: boolean;
  originalValue: number;
}

// تعريف الوحدات
interface NumberUnit {
  value: number;
  arabicSymbol: string;
  englishSymbol: string;
  arabicName: string;
  englishName: string;
}

/**
 * خدمة تنسيق الأرقام المختصرة
 * تطبق نمط Singleton لضمان وجود نسخة واحدة فقط
 */
export class CompactNumberService {
  private static instance: CompactNumberService;
  private settings: CompactNumberSettings | null = null;
  private lastFetchTime = 0;
  private readonly CACHE_DURATION = 10 * 60 * 1000; // 10 دقائق

  // الإعدادات الافتراضية
  private readonly DEFAULT_SETTINGS: CompactNumberSettings = {
    unitType: 'english',
    compactThreshold: 1000,
    decimalPlaces: 1,
    showFullNumber: true
  };

  // تعريف الوحدات بالترتيب من الأكبر للأصغر
  private readonly UNITS: NumberUnit[] = [
    {
      value: 1_000_000_000_000, // تريليون
      arabicSymbol: 'ت',
      englishSymbol: 'T',
      arabicName: 'تريليون',
      englishName: 'trillion'
    },
    {
      value: 1_000_000_000, // مليار
      arabicSymbol: 'ب',
      englishSymbol: 'B',
      arabicName: 'مليار',
      englishName: 'billion'
    },
    {
      value: 1_000_000, // مليون
      arabicSymbol: 'م',
      englishSymbol: 'M',
      arabicName: 'مليون',
      englishName: 'million'
    },
    {
      value: 1_000, // ألف
      arabicSymbol: 'أ',
      englishSymbol: 'K',
      arabicName: 'ألف',
      englishName: 'thousand'
    }
  ];

  private constructor() {
    // منع إنشاء نسخ متعددة
  }

  /**
   * الحصول على النسخة الوحيدة من الخدمة
   */
  public static getInstance(): CompactNumberService {
    if (!CompactNumberService.instance) {
      CompactNumberService.instance = new CompactNumberService();
    }
    return CompactNumberService.instance;
  }

  /**
   * جلب إعدادات الاختصار (يمكن توسيعها لاحقاً لجلب من الخادم)
   */
  private async fetchSettings(): Promise<CompactNumberSettings> {
    const now = Date.now();

    // استخدام الكاش إذا كان متاحاً وحديثاً
    if (this.settings && (now - this.lastFetchTime) < this.CACHE_DURATION) {
      return this.settings;
    }

    // حالياً نستخدم الإعدادات الافتراضية، يمكن توسيعها لاحقاً
    this.settings = { ...this.DEFAULT_SETTINGS };
    this.lastFetchTime = now;

    return this.settings;
  }

  /**
   * تحويل رقم إلى تنسيق مختصر
   */
  public async formatCompact(
    amount: number, 
    options?: Partial<CompactNumberSettings>
  ): Promise<CompactNumberResult> {
    const settings = await this.fetchSettings();
    const finalSettings = { ...settings, ...options };

    if (isNaN(amount) || !isFinite(amount)) {
      return {
        compact: '0',
        full: '0',
        isCompacted: false,
        originalValue: 0
      };
    }

    const absAmount = Math.abs(amount);
    const isNegative = amount < 0;

    // إذا كان الرقم أقل من الحد الأدنى، لا نختصر
    if (absAmount < finalSettings.compactThreshold) {
      const fullFormatted = await this.getFullFormattedNumber(amount);
      return {
        compact: fullFormatted,
        full: fullFormatted,
        isCompacted: false,
        originalValue: amount
      };
    }

    // البحث عن الوحدة المناسبة
    const unit = this.findAppropriateUnit(absAmount);
    if (!unit) {
      const fullFormatted = await this.getFullFormattedNumber(amount);
      return {
        compact: fullFormatted,
        full: fullFormatted,
        isCompacted: false,
        originalValue: amount
      };
    }

    // حساب القيمة المختصرة بدقة أكبر للأرقام المالية
    const compactValue = absAmount / unit.value;

    // للأرقام المالية، نحتاج دقة أكبر - استخدام Math.floor بدلاً من Math.round
    // لتجنب التقريب الخاطئ في الأرقام المالية
    let roundedValue: number;
    if (finalSettings.decimalPlaces === 0) {
      roundedValue = Math.floor(compactValue);
    } else {
      // استخدام Math.floor للحصول على دقة أكبر
      const multiplier = Math.pow(10, finalSettings.decimalPlaces);
      roundedValue = Math.floor(compactValue * multiplier) / multiplier;
    }

    // تحديد الرمز حسب نوع الوحدة
    const symbol = finalSettings.unitType === 'arabic' ? unit.arabicSymbol : unit.englishSymbol;

    // تنسيق الرقم المختصر مع إزالة الأصفار غير الضرورية
    let formattedCompact: string;

    if (finalSettings.decimalPlaces > 0) {
      // تنسيق مع الأرقام العشرية
      const fixedValue = roundedValue.toFixed(finalSettings.decimalPlaces);
      // إزالة الأصفار غير الضرورية من النهاية
      formattedCompact = parseFloat(fixedValue).toString();
    } else {
      formattedCompact = Math.floor(roundedValue).toString();
    }

    const compactResult = `${isNegative ? '-' : ''}${formattedCompact}${symbol}`;

    // تنسيق الرقم الكامل
    const fullFormatted = await this.getFullFormattedNumber(amount);

    return {
      compact: compactResult,
      full: fullFormatted,
      isCompacted: true,
      originalValue: amount
    };
  }

  /**
   * تنسيق رقم مع العملة (مختصر) - محسن للأرقام المالية
   */
  public async formatCompactCurrency(
    amount: number,
    options?: Partial<CompactNumberSettings>
  ): Promise<CompactNumberResult> {
    // للأرقام المالية، نستخدم دقة أكبر (decimalPlaces = 1 كحد أدنى)
    const financialOptions = {
      ...options,
      decimalPlaces: Math.max(options?.decimalPlaces || 1, 1)
    };

    const result = await this.formatCompact(amount, financialOptions);

    // الحصول على رمز العملة من خدمة التنسيق
    try {
      const numberSettings = await numberFormattingService.getCurrentSettings();
      const currencySymbol = numberSettings.currencySymbol;
      const symbolPosition = numberSettings.symbolPosition;

      // إضافة رمز العملة للنتيجة المختصرة
      if (symbolPosition === 'before') {
        result.compact = `${currencySymbol} ${result.compact}`;
      } else {
        result.compact = `${result.compact} ${currencySymbol}`;
      }

      // النتيجة الكاملة تأتي منسقة مع العملة من numberFormattingService
      result.full = await numberFormattingService.formatCurrency(amount);

    } catch (error) {
      console.warn('فشل في الحصول على إعدادات العملة، استخدام الافتراضي:', error);
      result.compact = `${result.compact} د.ل`;
      result.full = `${result.full} د.ل`;
    }

    return result;
  }

  /**
   * البحث عن الوحدة المناسبة للرقم
   */
  private findAppropriateUnit(amount: number): NumberUnit | null {
    for (const unit of this.UNITS) {
      if (amount >= unit.value) {
        return unit;
      }
    }
    return null;
  }

  /**
   * الحصول على الرقم الكامل منسق
   */
  private async getFullFormattedNumber(amount: number): Promise<string> {
    try {
      return await numberFormattingService.formatNumber(amount);
    } catch (error) {
      console.warn('فشل في تنسيق الرقم الكامل:', error);
      return amount.toLocaleString('ar-LY');
    }
  }

  /**
   * تحديث الإعدادات
   */
  public updateSettings(newSettings: Partial<CompactNumberSettings>): void {
    this.settings = { ...this.DEFAULT_SETTINGS, ...this.settings, ...newSettings };
    this.lastFetchTime = Date.now();
  }

  /**
   * مسح الكاش
   */
  public clearCache(): void {
    this.settings = null;
    this.lastFetchTime = 0;
  }

  /**
   * الحصول على الإعدادات الحالية
   */
  public async getCurrentSettings(): Promise<CompactNumberSettings> {
    return await this.fetchSettings();
  }

  /**
   * الحصول على قائمة الوحدات المدعومة
   */
  public getSupportedUnits(): NumberUnit[] {
    return [...this.UNITS];
  }
}

// تصدير النسخة الوحيدة من الخدمة
export const compactNumberService = CompactNumberService.getInstance();
