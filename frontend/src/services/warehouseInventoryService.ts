/**
 * خدمة مخزون المستودعات
 * تطبق مبادئ البرمجة الكائنية مع معالجة شاملة للأخطاء
 */

import axios, { AxiosResponse } from 'axios';

// Types
export interface WarehouseInventory {
  id: number;
  warehouse_id: number;
  product_id: number;
  quantity: number;
  reserved_quantity: number;
  min_stock_level: number;
  max_stock_level?: number;
  location_code?: string;
  last_updated?: string;
  available_quantity?: number;
  stock_status?: 'normal' | 'low' | 'out_of_stock';
  product_name?: string;
  product_barcode?: string;
  product_price?: number;
  item_value?: number;
}

export interface ProductInventory {
  warehouse_id: number;
  warehouse_name: string;
  warehouse_code: string;
  is_main_warehouse: boolean;
  quantity: number;
  reserved_quantity: number;
  available_quantity: number;
  min_stock_level: number;
  max_stock_level?: number;
  location_code?: string;
  last_updated?: string;
  stock_status: 'normal' | 'low' | 'out_of_stock';
}

export interface InventoryFilters {
  low_stock_only?: boolean;
  search?: string;
  min_quantity?: number;
  max_quantity?: number;
}

export interface InventoryUpdate {
  quantity?: number;
  reserved_quantity?: number;
  min_stock_level?: number;
  max_stock_level?: number;
  location_code?: string;
}

export interface StockAvailability {
  available: boolean;
  current_quantity: number;
  reserved_quantity: number;
  available_quantity: number;
  requested_quantity: number;
  shortage?: number;
  message: string;
}

export interface LowStockItem {
  product_id: number;
  product_name: string;
  product_barcode: string;
  current_quantity: number;
  reserved_quantity: number;
  available_quantity: number;
  min_stock_level: number;
  shortage: number;
  location_code?: string;
  last_updated?: string;
}

export interface ApiResponse<T> {
  success: boolean;
  error?: string;
  message?: string;
  inventory?: T;
  product?: any;
  warehouse?: any;
  summary?: any;
  low_stock_items?: LowStockItem[];
  total_count?: number;
  available?: boolean;
  current_quantity?: number;
  available_quantity?: number;
  reserved_quantity?: number;
}

/**
 * خدمة مخزون المستودعات
 * تطبق مبادئ البرمجة الكائنية مع نمط Singleton
 */
export class WarehouseInventoryService {
  private static instance: WarehouseInventoryService;
  private readonly baseURL: string;

  private constructor() {
    this.baseURL = '/api/warehouse-inventory';
  }

  /**
   * الحصول على instance وحيد من الخدمة
   */
  public static getInstance(): WarehouseInventoryService {
    if (!WarehouseInventoryService.instance) {
      WarehouseInventoryService.instance = new WarehouseInventoryService();
    }
    return WarehouseInventoryService.instance;
  }

  /**
   * الحصول على مخزون المنتج في جميع المستودعات
   */
  public async getProductInventory(productId: number): Promise<ApiResponse<ProductInventory[]>> {
    try {
      const response: AxiosResponse<ApiResponse<ProductInventory[]>> = await axios.get(
        `${this.baseURL}/product/${productId}`
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في جلب مخزون المنتج:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في جلب مخزون المنتج'
      };
    }
  }

  /**
   * الحصول على مخزون المستودع
   */
  public async getWarehouseInventory(
    warehouseId: number, 
    filters?: InventoryFilters
  ): Promise<ApiResponse<WarehouseInventory[]>> {
    try {
      const params = new URLSearchParams();
      
      if (filters) {
        if (filters.low_stock_only) params.append('low_stock_only', 'true');
        if (filters.search) params.append('search', filters.search);
        if (filters.min_quantity !== undefined) params.append('min_quantity', filters.min_quantity.toString());
        if (filters.max_quantity !== undefined) params.append('max_quantity', filters.max_quantity.toString());
      }

      const url = `${this.baseURL}/warehouse/${warehouseId}${params.toString() ? '?' + params.toString() : ''}`;
      const response: AxiosResponse<ApiResponse<WarehouseInventory[]>> = await axios.get(url);
      return response.data;
    } catch (error: any) {
      console.error('خطأ في جلب مخزون المستودع:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في جلب مخزون المستودع'
      };
    }
  }

  /**
   * تحديث مستويات المخزون
   */
  public async updateStockLevels(
    warehouseId: number,
    productId: number,
    updateData: InventoryUpdate
  ): Promise<ApiResponse<WarehouseInventory>> {
    try {
      const response: AxiosResponse<ApiResponse<WarehouseInventory>> = await axios.put(
        `${this.baseURL}/warehouse/${warehouseId}/product/${productId}`,
        updateData
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في تحديث مستويات المخزون:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في تحديث مستويات المخزون'
      };
    }
  }

  /**
   * التحقق من توفر المخزون
   */
  public async checkStockAvailability(
    warehouseId: number,
    productId: number,
    quantity: number
  ): Promise<ApiResponse<StockAvailability>> {
    try {
      const response: AxiosResponse<ApiResponse<StockAvailability>> = await axios.get(
        `${this.baseURL}/warehouse/${warehouseId}/product/${productId}/availability?quantity=${quantity}`
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في التحقق من توفر المخزون:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في التحقق من توفر المخزون'
      };
    }
  }

  /**
   * الحصول على المنتجات قليلة المخزون
   */
  public async getLowStockItems(warehouseId: number): Promise<ApiResponse<LowStockItem[]>> {
    try {
      const response: AxiosResponse<ApiResponse<LowStockItem[]>> = await axios.get(
        `${this.baseURL}/warehouse/${warehouseId}/low-stock`
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في جلب المنتجات قليلة المخزون:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في جلب المنتجات قليلة المخزون'
      };
    }
  }

  /**
   * حجز المخزون
   */
  public async reserveStock(
    warehouseId: number,
    productId: number,
    quantity: number
  ): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await axios.post(
        `${this.baseURL}/warehouse/${warehouseId}/product/${productId}/reserve?quantity=${quantity}`
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في حجز المخزون:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في حجز المخزون'
      };
    }
  }

  /**
   * إلغاء حجز المخزون
   */
  public async releaseReservedStock(
    warehouseId: number,
    productId: number,
    quantity: number
  ): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await axios.post(
        `${this.baseURL}/warehouse/${warehouseId}/product/${productId}/release?quantity=${quantity}`
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في إلغاء حجز المخزون:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في إلغاء حجز المخزون'
      };
    }
  }

  /**
   * التحقق من صحة بيانات المخزون
   */
  public validateInventoryData(data: InventoryUpdate): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // التحقق من الكمية
    if (data.quantity !== undefined && data.quantity < 0) {
      errors.push('الكمية يجب أن تكون أكبر من أو تساوي صفر');
    }

    // التحقق من الكمية المحجوزة
    if (data.reserved_quantity !== undefined && data.reserved_quantity < 0) {
      errors.push('الكمية المحجوزة يجب أن تكون أكبر من أو تساوي صفر');
    }

    // التحقق من الحد الأدنى
    if (data.min_stock_level !== undefined && data.min_stock_level < 0) {
      errors.push('الحد الأدنى للمخزون يجب أن يكون أكبر من أو يساوي صفر');
    }

    // التحقق من الحد الأقصى
    if (data.max_stock_level !== undefined && data.max_stock_level !== null && data.max_stock_level < 0) {
      errors.push('الحد الأقصى للمخزون يجب أن يكون أكبر من أو يساوي صفر');
    }

    // التحقق من أن الحد الأقصى أكبر من الحد الأدنى
    if (
      data.max_stock_level !== undefined && 
      data.max_stock_level !== null &&
      data.min_stock_level !== undefined &&
      data.max_stock_level < data.min_stock_level
    ) {
      errors.push('الحد الأقصى يجب أن يكون أكبر من الحد الأدنى');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * تنسيق بيانات المخزون للعرض
   */
  public formatInventoryForDisplay(inventory: WarehouseInventory): any {
    const available = (inventory.available_quantity ?? 0);
    const total = inventory.quantity;
    const reserved = inventory.reserved_quantity;

    return {
      ...inventory,
      available_percentage: total > 0 ? Math.round((available / total) * 100) : 0,
      stock_status_text: this.getStockStatusText(inventory.stock_status || 'normal'),
      stock_status_color: this.getStockStatusColor(inventory.stock_status || 'normal'),
      formatted_quantity: `${available} / ${total}`,
      needs_restock: available <= inventory.min_stock_level,
      shortage_amount: Math.max(0, inventory.min_stock_level - available)
    };
  }

  /**
   * الحصول على نص حالة المخزون
   */
  private getStockStatusText(status: string): string {
    switch (status) {
      case 'normal': return 'طبيعي';
      case 'low': return 'قليل';
      case 'out_of_stock': return 'نفد';
      default: return 'غير محدد';
    }
  }

  /**
   * الحصول على لون حالة المخزون
   */
  private getStockStatusColor(status: string): string {
    switch (status) {
      case 'normal': return 'green';
      case 'low': return 'orange';
      case 'out_of_stock': return 'red';
      default: return 'gray';
    }
  }

  /**
   * حساب إحصائيات المخزون
   */
  public calculateInventoryStats(inventory: WarehouseInventory[]): any {
    const total = inventory.length;
    const lowStock = inventory.filter(item => item.stock_status === 'low').length;
    const outOfStock = inventory.filter(item => item.stock_status === 'out_of_stock').length;
    const normal = total - lowStock - outOfStock;
    const totalValue = inventory.reduce((sum, item) => sum + (item.item_value || 0), 0);

    return {
      total_items: total,
      normal_stock: normal,
      low_stock: lowStock,
      out_of_stock: outOfStock,
      total_value: totalValue,
      low_stock_percentage: total > 0 ? Math.round((lowStock / total) * 100) : 0,
      out_of_stock_percentage: total > 0 ? Math.round((outOfStock / total) * 100) : 0
    };
  }
}

// تصدير instance وحيد
export const warehouseInventoryService = WarehouseInventoryService.getInstance();
