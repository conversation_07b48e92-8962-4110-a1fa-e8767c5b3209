/**
 * خدمة إدارة الفروع
 * تطبق مبادئ البرمجة الكائنية مع معالجة شاملة للأخطاء
 */

import api from '../lib/axios';
import { AxiosResponse } from 'axios';

// Types
export interface Branch {
  id: number;
  uuid: string;
  name: string;
  code?: string;
  address?: string;
  phone?: string;
  manager_name?: string;
  email?: string;
  is_main: boolean;
  is_active: boolean;
  city?: string;
  region?: string;
  postal_code?: string;
  max_daily_sales?: number;
  working_hours_start?: string;
  working_hours_end?: string;
  created_at?: string;
  updated_at?: string;
  created_by?: number;
  updated_by?: number;
  warehouses_count?: number;
  active_warehouses_count?: number;
  warehouses?: WarehouseInBranch[];
}

export interface WarehouseInBranch {
  id: number;
  name: string;
  code: string;
  is_primary: boolean;
  priority: number;
  is_active: boolean;
}

export interface BranchCreate {
  uuid?: string;
  name: string;
  code?: string;
  address?: string;
  phone?: string;
  manager_name?: string;
  email?: string;
  is_main?: boolean;
  is_active?: boolean;
  city?: string;
  region?: string;
  postal_code?: string;
  max_daily_sales?: number;
  working_hours_start?: string;
  working_hours_end?: string;
}

export interface BranchUpdate {
  name?: string;
  code?: string;
  address?: string;
  phone?: string;
  manager_name?: string;
  email?: string;
  is_main?: boolean;
  is_active?: boolean;
  city?: string;
  region?: string;
  postal_code?: string;
  max_daily_sales?: number;
  working_hours_start?: string;
  working_hours_end?: string;
}

export interface BranchSearchParams {
  search_term?: string;
  include_inactive?: boolean;
  city?: string;
  region?: string;
  is_main?: boolean;
}

export interface BranchStatistics {
  total_branches: number;
  active_branches: number;
  inactive_branches: number;
  main_branch_id?: number;
  branches_by_region: Record<string, number>;
  branches_by_city: Record<string, number>;
}

interface ApiResponse<T> {
  success: boolean;
  branches?: T[];
  branch?: T;
  total_count?: number;
  statistics?: BranchStatistics;
  message?: string;
  error?: string;
}

/**
 * خدمة إدارة الفروع - تطبق مبادئ البرمجة الكائنية
 */
export class BranchService {
  private static instance: BranchService;
  private readonly baseURL = '/api/branches';

  private constructor() {
    // منع الإنشاء المباشر - استخدم getInstance()
  }

  /**
   * الحصول على مثيل وحيد من الخدمة (Singleton Pattern)
   */
  public static getInstance(): BranchService {
    if (!BranchService.instance) {
      BranchService.instance = new BranchService();
    }
    return BranchService.instance;
  }

  /**
   * إنشاء فرع جديد
   */
  public async createBranch(branchData: BranchCreate): Promise<ApiResponse<Branch>> {
    try {
      console.log('🔄 إنشاء فرع جديد:', branchData.name);
      
      const response: AxiosResponse<ApiResponse<Branch>> = await api.post(
        this.baseURL,
        branchData
      );
      
      if (response.data.success) {
        console.log('✅ تم إنشاء الفرع بنجاح:', response.data.branch?.name);
      }
      
      return response.data;
    } catch (error: any) {
      console.error('❌ خطأ في إنشاء الفرع:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في إنشاء الفرع'
      };
    }
  }

  /**
   * الحصول على جميع الفروع
   */
  public async getAllBranches(includeInactive: boolean = false): Promise<ApiResponse<Branch>> {
    try {
      const response: AxiosResponse<ApiResponse<Branch>> = await api.get(
        `${this.baseURL}?include_inactive=${includeInactive}`
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في جلب الفروع:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في جلب الفروع'
      };
    }
  }

  /**
   * الحصول على فرع بالمعرف
   */
  public async getBranchById(branchId: number): Promise<ApiResponse<Branch>> {
    try {
      const response: AxiosResponse<ApiResponse<Branch>> = await api.get(
        `${this.baseURL}/${branchId}`
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في جلب الفرع:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في جلب الفرع'
      };
    }
  }

  /**
   * الحصول على فرع بالكود
   */
  public async getBranchByCode(branchCode: string): Promise<ApiResponse<Branch>> {
    try {
      const response: AxiosResponse<ApiResponse<Branch>> = await api.get(
        `${this.baseURL}/code/${branchCode}`
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في جلب الفرع بالكود:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في جلب الفرع'
      };
    }
  }

  /**
   * الحصول على فرع بـ UUID
   */
  public async getBranchByUuid(branchUuid: string): Promise<ApiResponse<Branch>> {
    try {
      console.log('🔄 جلب الفرع بـ UUID:', branchUuid);

      const response: AxiosResponse<ApiResponse<Branch>> = await api.get(
        `${this.baseURL}/uuid/${branchUuid}`
      );

      if (response.data.success) {
        console.log('✅ تم جلب الفرع بنجاح');
      }

      return response.data;
    } catch (error: any) {
      console.error('خطأ في جلب الفرع بـ UUID:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في جلب الفرع'
      };
    }
  }

  /**
   * تحديث فرع
   */
  public async updateBranch(branchId: number, branchData: BranchUpdate): Promise<ApiResponse<Branch>> {
    try {
      console.log('🔄 تحديث الفرع:', branchId);
      
      const response: AxiosResponse<ApiResponse<Branch>> = await api.put(
        `${this.baseURL}/${branchId}`,
        branchData
      );
      
      if (response.data.success) {
        console.log('✅ تم تحديث الفرع بنجاح');
      }
      
      return response.data;
    } catch (error: any) {
      console.error('❌ خطأ في تحديث الفرع:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في تحديث الفرع'
      };
    }
  }

  /**
   * حذف فرع
   */
  public async deleteBranch(branchId: number): Promise<ApiResponse<Branch>> {
    try {
      console.log('🔄 حذف الفرع:', branchId);
      
      const response: AxiosResponse<ApiResponse<Branch>> = await api.delete(
        `${this.baseURL}/${branchId}`
      );
      
      if (response.data.success) {
        console.log('✅ تم حذف الفرع بنجاح');
      }
      
      return response.data;
    } catch (error: any) {
      console.error('❌ خطأ في حذف الفرع:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في حذف الفرع'
      };
    }
  }

  /**
   * تعيين فرع كرئيسي
   */
  public async setMainBranch(branchId: number): Promise<ApiResponse<Branch>> {
    try {
      console.log('🔄 تعيين الفرع الرئيسي:', branchId);
      
      const response: AxiosResponse<ApiResponse<Branch>> = await api.post(
        `${this.baseURL}/${branchId}/set-main`
      );
      
      if (response.data.success) {
        console.log('✅ تم تعيين الفرع الرئيسي بنجاح');
      }
      
      return response.data;
    } catch (error: any) {
      console.error('❌ خطأ في تعيين الفرع الرئيسي:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في تعيين الفرع الرئيسي'
      };
    }
  }

  /**
   * تبديل حالة نشاط الفرع
   */
  public async toggleBranchStatus(branchId: number): Promise<ApiResponse<Branch>> {
    try {
      console.log('🔄 تبديل حالة الفرع:', branchId);
      
      const response: AxiosResponse<ApiResponse<Branch>> = await api.post(
        `${this.baseURL}/${branchId}/toggle-status`
      );
      
      if (response.data.success) {
        console.log('✅ تم تبديل حالة الفرع بنجاح');
      }
      
      return response.data;
    } catch (error: any) {
      console.error('❌ خطأ في تبديل حالة الفرع:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في تبديل حالة الفرع'
      };
    }
  }

  /**
   * البحث في الفروع
   */
  public async searchBranches(searchParams: BranchSearchParams): Promise<ApiResponse<Branch>> {
    try {
      const params = new URLSearchParams();
      
      if (searchParams.search_term) params.append('search_term', searchParams.search_term);
      if (searchParams.include_inactive !== undefined) params.append('include_inactive', searchParams.include_inactive.toString());
      if (searchParams.city) params.append('city', searchParams.city);
      if (searchParams.region) params.append('region', searchParams.region);
      if (searchParams.is_main !== undefined) params.append('is_main', searchParams.is_main.toString());
      
      const response: AxiosResponse<ApiResponse<Branch>> = await api.get(
        `${this.baseURL}/search?${params.toString()}`
      );
      
      return response.data;
    } catch (error: any) {
      console.error('خطأ في البحث في الفروع:', error);
      return {
        success: false,
        error: error.response?.data?.detail || 'خطأ في البحث في الفروع'
      };
    }
  }

  /**
   * الحصول على إحصائيات الفروع
   */
  public async getBranchStatistics(): Promise<ApiResponse<Branch>> {
    try {
      // استخدام endpoint منفصل للإحصائيات
      const response: AxiosResponse<ApiResponse<Branch>> = await api.get(
        '/api/branch-statistics'
      );
      return response.data;
    } catch (error: any) {
      console.error('خطأ في جلب إحصائيات الفروع:', error);

      // معالجة أفضل للأخطاء
      let errorMessage = 'خطأ في جلب إحصائيات الفروع';
      if (error?.response?.data?.detail) {
        if (Array.isArray(error.response.data.detail)) {
          errorMessage = error.response.data.detail.map((d: any) => d.msg || d).join(', ');
        } else if (typeof error.response.data.detail === 'string') {
          errorMessage = error.response.data.detail;
        }
      } else if (error?.message) {
        errorMessage = error.message;
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }
}

// تصدير مثيل وحيد من الخدمة
export const branchService = BranchService.getInstance();
export default branchService;
