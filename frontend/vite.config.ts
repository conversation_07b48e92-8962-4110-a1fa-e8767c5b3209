import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'
import { fileURLToPath } from 'url'
import { dirname } from 'path'
import { networkInterfaces } from 'os'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Function to get the local IP address
const getLocalIP = () => {
  const interfaces = networkInterfaces()
  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name] || []) {
      // Skip internal and non-IPv4 addresses
      if (iface.family === 'IPv4' && !iface.internal) {
        return iface.address
      }
    }
  }
  return 'localhost' // fallback
}

// Dynamic backend URL for proxy
const getBackendProxyTarget = () => {
  const localIP = getLocalIP()
  const isDev = process.env.NODE_ENV !== 'production'

  console.log(`🔧 Vite Environment - NODE_ENV: ${process.env.NODE_ENV}, isDev: ${isDev}`)
  console.log(`🔗 Backend proxy target: http://${localIP}:8002`)

  // Always use local IP for network access, even in production builds served locally
  return `http://${localIP}:8002`
}

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react({
      // تحسين Fast Refresh لتجنب إعادة التحميل المتكرر
      include: "**/*.{jsx,tsx}",
      exclude: /node_modules/,
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    },
  },
  build: {
    // تحسين البناء لتجنب مشاكل إعادة التحميل
    rollupOptions: {
      // تحسين Tree Shaking
      treeshake: {
        moduleSideEffects: false,
        propertyReadSideEffects: false,
        tryCatchDeoptimization: false,
      },
      output: {
        manualChunks: {
          // Core React libraries
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],

          // Charts and visualization (تقسيم أكثر تفصيلاً)
          charts: ['apexcharts', 'react-apexcharts'],

          // State management and utilities
          state: ['zustand'],
          utils: ['axios', 'jwt-decode'],

          // UI components and icons
          ui: ['react-hot-toast', 'react-icons'],

          // Large pages split (تحسين التقسيم)
          reports: ['src/pages/Reports.tsx'],
          pos: ['src/pages/POS.tsx'],
          products: ['src/pages/Products.tsx'],
          settings: ['src/pages/Settings.tsx'],

          // تقسيم مكونات كبيرة منفصلة
          'help-center': ['src/pages/HelpCenter.tsx'],
          'date-time': ['src/components/DateTimeSettings.tsx'],

          // تقسيم خدمات كبيرة
          services: [
            'src/services/dateTimeService.ts',
            'src/services/timezoneDetectionService.ts',
            'src/services/chatWebSocketService.ts'
          ],
        },
      },
    },
    // تقليل حجم الملفات
    minify: 'esbuild', // استخدام esbuild بدلاً من terser
    sourcemap: false, // تعطيل source maps في الإنتاج
    target: 'es2015', // تحديد target لتجنب مشاكل CSP

    // تحسين الأداء
    chunkSizeWarningLimit: 1200, // رفع الحد الأقصى للتحذير إلى 1.2MB (لتجنب تحذير Reports فقط)
  },
  server: {
    port: 5175,
    host: '0.0.0.0', // Allow access from network
    hmr: {
      // تقليل حساسية HMR لتجنب إعادة التحميل المتكرر
      overlay: false, // إخفاء overlay الأخطاء
      clientPort: 5175,
    },
    watch: {
      // تقليل مراقبة الملفات لتجنب إعادة التحميل المتكرر
      ignored: ['**/node_modules/**', '**/dist/**'],
      usePolling: false, // تعطيل polling
    },
    headers: {
      // تعطيل CSP في development لتجنب مشاكل eval()
      'Content-Security-Policy': '',
    },
    proxy: {
      '/api': {
        target: getBackendProxyTarget(),
        changeOrigin: true,
        secure: false,
        ws: true,
        // Don't rewrite the path, keep /api prefix
        // rewrite: (path) => path.replace(/^\/api/, ''),
        headers: {
          'X-Proxy-Debug': 'vite-proxy'
        },
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('❌ [PROXY] Connection error:', err.message);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            // تقليل رسائل debug - فقط للطلبات المهمة
            if (req.url?.includes('/api/system/recovery') || req.url?.includes('/api/auth/')) {
              console.log('🔍 [PROXY] Request:', req.method, req.url);
            }

            // تمرير headers البصمة صراحة (بدون طباعة مفصلة)
            const fingerprintHeaders = [
              'x-device-fingerprint',
              'x-device-hardware',
              'x-device-storage',
              'x-device-screen',
              'x-device-system',
              'x-device-timestamp'
            ];

            fingerprintHeaders.forEach(header => {
              const value = req.headers[header];
              if (value) {
                proxyReq.setHeader(header, value);
              }
            });
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            // تقليل رسائل debug - فقط للأخطاء
            if (proxyRes.statusCode && proxyRes.statusCode >= 400) {
              console.log('⚠️ [PROXY] Response:', proxyRes.statusCode, req.url);
            }

            // Add CORS headers to the response - allow any origin in development
            proxyRes.headers['Access-Control-Allow-Origin'] = '*';
            proxyRes.headers['Access-Control-Allow-Methods'] = 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS';
            proxyRes.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Device-Fingerprint, X-Device-Hardware, X-Device-Storage, X-Device-Screen, X-Device-System, X-Device-Timestamp';
            proxyRes.headers['Access-Control-Allow-Credentials'] = 'true';
          });
        },
      },
    },
  },
})
