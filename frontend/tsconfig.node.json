{"compilerOptions": {"composite": true, "skipLibCheck": true, "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "target": "ES2022", "lib": ["ES2023"], "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "emitDeclarationOnly": true, "verbatimModuleSyntax": true, "types": ["node"]}, "include": ["vite.config.ts"]}