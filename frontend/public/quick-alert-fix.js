/**
 * إصلاح سريع للتنبيهات المكررة - للاستخدام الفوري
 * يمكن تشغيله مباشرة في وحدة التحكم
 */

console.log('🚀 بدء الإصلاح السريع للتنبيهات...');

// إصلاح فوري
(function quickFix() {
  try {
    // 1. التحقق من وجود الخدمات
    const alertService = window.alertService;
    const advancedAlertService = window.advancedAlertService;
    
    if (!alertService) {
      console.error('❌ خدمة التنبيهات غير متوفرة');
      return;
    }

    // 2. إحصائيات قبل الإصلاح
    const beforeStats = alertService.getAlertStatistics();
    console.log('📊 إحصائيات قبل الإصلاح:', beforeStats);

    // 3. إزالة التنبيهات المشكوك فيها
    const alerts = alertService.getAlerts();
    const problematicAlerts = alerts.filter(alert => 
      alert.source === 'ALERT_SYSTEM' ||
      alert.source === 'CRITICAL_ERRORS' ||
      (alert.type === 'critical' && alert.title.includes('أخطاء حرجة')) ||
      (alert.type === 'critical' && alert.title.includes('تحذير'))
    );

    problematicAlerts.forEach(alert => {
      alertService.removeAlert(alert.id);
    });

    console.log(`🗑️ تم إزالة ${problematicAlerts.length} تنبيه مشكوك فيه`);

    // 4. إعادة تعيين العداد
    if (alertService.resetCriticalErrorCount) {
      alertService.resetCriticalErrorCount();
      console.log('🔄 تم إعادة تعيين عداد الأخطاء الحرجة');
    }

    // 5. تنظيف النظام المتقدم
    if (advancedAlertService && advancedAlertService.clearAllAlerts) {
      advancedAlertService.clearAllAlerts();
      console.log('🧹 تم تنظيف النظام المتقدم');
    }

    // 6. تنظيف شامل
    if (alertService.performFullCleanup) {
      alertService.performFullCleanup();
      console.log('✨ تم التنظيف الشامل');
    }

    // 7. إحصائيات بعد الإصلاح
    const afterStats = alertService.getAlertStatistics();
    console.log('📊 إحصائيات بعد الإصلاح:', afterStats);

    // 8. إنشاء تنبيه نجاح
    alertService.addAlert({
      type: 'info',
      title: '✅ تم إصلاح النظام',
      message: `تم حل مشكلة التنبيهات المكررة. تم إزالة ${problematicAlerts.length} تنبيه مشكوك فيه.`,
      source: 'SYSTEM_REPAIR'
    });

    console.log('🎉 تم الإصلاح السريع بنجاح!');
    
    return {
      success: true,
      removedAlerts: problematicAlerts.length,
      beforeStats,
      afterStats
    };

  } catch (error) {
    console.error('❌ فشل الإصلاح السريع:', error);
    
    // محاولة إصلاح طارئ
    try {
      if (window.alertService && window.alertService.clearAllAlerts) {
        window.alertService.clearAllAlerts();
        console.log('⚠️ تم مسح جميع التنبيهات كحل طارئ');
      }
    } catch (emergencyError) {
      console.error('❌ فشل الحل الطارئ:', emergencyError);
    }
    
    return {
      success: false,
      error: error.message
    };
  }
})();

// إضافة دالة للوصول السريع
window.quickAlertFix = function() {
  const script = document.createElement('script');
  script.src = '/quick-alert-fix.js';
  document.head.appendChild(script);
};

console.log('💡 للتشغيل مرة أخرى: quickAlertFix()');
