<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد أصوات التنبيه - SmartPOS</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #2563eb;
            transform: translateY(-2px);
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔊 مولد أصوات التنبيه</h1>
        <p>اضغط على الأزرار لتوليد وتحميل أصوات التنبيه</p>
        
        <button onclick="generateNotificationSound()">توليد صوت التنبيه</button>
        <button onclick="testSound()">اختبار الصوت</button>
        
        <div id="status" class="status"></div>
        
        <audio id="testAudio" preload="auto">
            <source src="notification.mp3" type="audio/mpeg">
            <source src="notification.wav" type="audio/wav">
        </audio>
    </div>

    <script>
        let audioContext;
        let generatedAudioBuffer;

        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
            
            setTimeout(() => {
                status.style.display = 'none';
            }, 3000);
        }

        async function generateNotificationSound() {
            try {
                showStatus('جاري توليد صوت التنبيه...', 'info');
                
                // إنشاء AudioContext
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                
                // إعدادات الصوت
                const sampleRate = audioContext.sampleRate;
                const duration = 0.8; // 0.8 ثانية
                const length = sampleRate * duration;
                
                // إنشاء buffer للصوت
                const audioBuffer = audioContext.createBuffer(1, length, sampleRate);
                const channelData = audioBuffer.getChannelData(0);
                
                // توليد صوت تنبيه لطيف (نغمات متعددة)
                for (let i = 0; i < length; i++) {
                    const time = i / sampleRate;
                    
                    // نغمة أساسية (800 Hz)
                    const freq1 = 800;
                    const wave1 = Math.sin(2 * Math.PI * freq1 * time);
                    
                    // نغمة ثانوية (1200 Hz)
                    const freq2 = 1200;
                    const wave2 = Math.sin(2 * Math.PI * freq2 * time);
                    
                    // مزج النغمات مع تأثير fade out
                    const fadeOut = Math.exp(-time * 3);
                    const envelope = Math.sin(Math.PI * time / duration);
                    
                    channelData[i] = (wave1 * 0.6 + wave2 * 0.4) * fadeOut * envelope * 0.3;
                }
                
                generatedAudioBuffer = audioBuffer;
                
                // تحويل إلى WAV وتحميل
                const wavBlob = audioBufferToWav(audioBuffer);
                downloadBlob(wavBlob, 'notification.wav');
                
                showStatus('تم توليد وتحميل صوت التنبيه بنجاح!', 'success');
                
            } catch (error) {
                console.error('خطأ في توليد الصوت:', error);
                showStatus('حدث خطأ في توليد الصوت', 'error');
            }
        }

        function audioBufferToWav(buffer) {
            const length = buffer.length;
            const arrayBuffer = new ArrayBuffer(44 + length * 2);
            const view = new DataView(arrayBuffer);
            const channelData = buffer.getChannelData(0);
            
            // WAV header
            const writeString = (offset, string) => {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            };
            
            writeString(0, 'RIFF');
            view.setUint32(4, 36 + length * 2, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);
            view.setUint16(22, 1, true);
            view.setUint32(24, buffer.sampleRate, true);
            view.setUint32(28, buffer.sampleRate * 2, true);
            view.setUint16(32, 2, true);
            view.setUint16(34, 16, true);
            writeString(36, 'data');
            view.setUint32(40, length * 2, true);
            
            // PCM data
            let offset = 44;
            for (let i = 0; i < length; i++) {
                const sample = Math.max(-1, Math.min(1, channelData[i]));
                view.setInt16(offset, sample * 0x7FFF, true);
                offset += 2;
            }
            
            return new Blob([arrayBuffer], { type: 'audio/wav' });
        }

        function downloadBlob(blob, filename) {
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function testSound() {
            const audio = document.getElementById('testAudio');
            audio.play().catch(error => {
                showStatus('لا يمكن تشغيل الصوت. تأكد من وجود ملفات الصوت.', 'error');
            });
        }
    </script>
</body>
</html>
