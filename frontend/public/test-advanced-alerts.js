/**
 * سكريبت اختبار النظام المتقدم للتنبيهات
 * يمكن تشغيله من وحدة التحكم في المتصفح
 */

(function() {
  'use strict';

  console.log('🚀 اختبار النظام المتقدم للتنبيهات');
  console.log('=====================================');

  // التحقق من وجود النظام المتقدم
  if (typeof window.advancedAlertService === 'undefined') {
    console.error('❌ النظام المتقدم غير متاح. تأكد من تحميل التطبيق بشكل صحيح.');
    return;
  }

  const advancedService = window.advancedAlertService;

  // دالة لاختبار التنبيهات المختلفة
  function testAllAlertTypes() {
    console.log('🧪 اختبار جميع أنواع التنبيهات...');

    // تنبيه نجاح
    advancedService.addAlert({
      type: 'success',
      title: 'تم الحفظ بنجاح',
      message: 'تم حفظ البيانات في قاعدة البيانات بنجاح',
      source: 'TEST_SUCCESS',
      category: 'user_action',
      priority: 3
    });

    // تنبيه معلومات
    advancedService.addAlert({
      type: 'info',
      title: 'معلومة مفيدة',
      message: 'يمكنك استخدام Ctrl+S لحفظ البيانات بسرعة',
      source: 'TEST_INFO',
      category: 'tips',
      priority: 2
    });

    // تنبيه تحذير
    advancedService.addAlert({
      type: 'warning',
      title: 'تحذير مخزون',
      message: 'المنتج "قلم أزرق" وصل إلى الحد الأدنى للمخزون (5 قطع)',
      source: 'TEST_WARNING',
      category: 'inventory',
      priority: 6,
      actions: [
        {
          id: 'view-product',
          label: 'عرض المنتج',
          action: () => console.log('تم النقر على عرض المنتج'),
          style: 'primary'
        }
      ]
    });

    // تنبيه خطأ
    advancedService.addAlert({
      type: 'error',
      title: 'خطأ في الشبكة',
      message: 'فشل في الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت',
      source: 'TEST_ERROR',
      category: 'network',
      priority: 8,
      metadata: {
        errorCode: 'NETWORK_TIMEOUT',
        url: 'https://api.example.com/data',
        timestamp: new Date().toISOString()
      }
    });

    // تنبيه حرج
    advancedService.addAlert({
      type: 'critical',
      title: 'خطأ حرج في النظام',
      message: 'فقدان الاتصال بقاعدة البيانات. النظام قد لا يعمل بشكل صحيح',
      source: 'TEST_CRITICAL',
      category: 'system',
      priority: 10,
      persistent: true,
      actions: [
        {
          id: 'restart-system',
          label: 'إعادة تشغيل',
          action: () => console.log('تم النقر على إعادة التشغيل'),
          style: 'danger'
        },
        {
          id: 'contact-support',
          label: 'اتصل بالدعم',
          action: () => console.log('تم النقر على الدعم'),
          style: 'secondary'
        }
      ]
    });

    console.log('✅ تم إنشاء جميع أنواع التنبيهات');
  }

  // دالة لاختبار منع التكرار
  function testDuplicatePrevention() {
    console.log('🔄 اختبار منع التكرار...');

    // إنشاء نفس التنبيه عدة مرات
    for (let i = 0; i < 5; i++) {
      const result = advancedService.addAlert({
        type: 'info',
        title: 'تنبيه مكرر',
        message: 'هذا تنبيه مكرر للاختبار',
        source: 'DUPLICATE_TEST',
        category: 'test',
        priority: 1
      });

      if (result) {
        console.log(`✅ تم إنشاء التنبيه ${i + 1}`);
      } else {
        console.log(`❌ تم منع التنبيه المكرر ${i + 1}`);
      }
    }
  }

  // دالة لاختبار الفلترة
  function testFiltering() {
    console.log('🔍 اختبار الفلترة...');

    // الحصول على جميع التنبيهات
    const allAlerts = advancedService.getAlerts();
    console.log(`📊 إجمالي التنبيهات: ${allAlerts.length}`);

    // فلترة التنبيهات الحرجة
    const criticalAlerts = advancedService.getAlerts({
      types: ['critical']
    });
    console.log(`🔴 التنبيهات الحرجة: ${criticalAlerts.length}`);

    // فلترة تنبيهات الاختبار
    const testAlerts = advancedService.getAlerts({
      sources: ['TEST_SUCCESS', 'TEST_ERROR', 'TEST_WARNING']
    });
    console.log(`🧪 تنبيهات الاختبار: ${testAlerts.length}`);

    // فلترة التنبيهات عالية الأولوية
    const highPriorityAlerts = advancedService.getAlerts({
      minPriority: 7
    });
    console.log(`⚡ التنبيهات عالية الأولوية: ${highPriorityAlerts.length}`);
  }

  // دالة لاختبار الإحصائيات
  function testStatistics() {
    console.log('📈 اختبار الإحصائيات...');

    const stats = advancedService.getStats();
    console.log('📊 الإحصائيات الشاملة:');
    console.table(stats);

    console.log('📋 تفصيل حسب النوع:');
    console.table(stats.byType);

    console.log('📋 تفصيل حسب المصدر:');
    console.table(stats.bySource);
  }

  // دالة لاختبار الكتم
  function testMuting() {
    console.log('🔇 اختبار الكتم...');

    // كتم مصدر الاختبار
    advancedService.muteSource('MUTE_TEST', 10000); // 10 ثواني

    // محاولة إنشاء تنبيه من مصدر مكتوم
    const result = advancedService.addAlert({
      type: 'info',
      title: 'تنبيه مكتوم',
      message: 'هذا التنبيه يجب أن يكون مكتوماً',
      source: 'MUTE_TEST',
      category: 'test',
      priority: 1
    });

    if (result) {
      console.log('❌ فشل الكتم - تم إنشاء التنبيه');
    } else {
      console.log('✅ نجح الكتم - تم منع التنبيه');
    }

    // إلغاء الكتم
    setTimeout(() => {
      advancedService.unmuteSource('MUTE_TEST');
      console.log('🔊 تم إلغاء كتم المصدر');
    }, 5000);
  }

  // دالة لاختبار التنظيف
  function testCleanup() {
    console.log('🧹 اختبار التنظيف...');

    const beforeCount = advancedService.getAlerts().length;
    console.log(`📊 عدد التنبيهات قبل التنظيف: ${beforeCount}`);

    // تنظيف التنبيهات غير المستمرة
    const nonPersistentAlerts = advancedService.getAlerts().filter(alert => !alert.persistent);
    nonPersistentAlerts.forEach(alert => {
      advancedService.removeAlert(alert.id);
    });

    const afterCount = advancedService.getAlerts().length;
    console.log(`📊 عدد التنبيهات بعد التنظيف: ${afterCount}`);
    console.log(`🗑️ تم حذف ${beforeCount - afterCount} تنبيهات`);
  }

  // دالة لعرض النتائج النهائية
  function showFinalResults() {
    console.log('🎯 النتائج النهائية:');
    console.log('==================');

    const stats = advancedService.getStats();
    const alerts = advancedService.getAlerts();

    console.log(`✅ إجمالي التنبيهات: ${stats.total}`);
    console.log(`🔴 التنبيهات الحرجة: ${stats.critical}`);
    console.log(`📊 التنبيهات الحديثة: ${stats.recent}`);
    console.log(`👁️ التنبيهات غير المقروءة: ${stats.unread}`);

    console.log('\n📋 قائمة التنبيهات الحالية:');
    alerts.forEach((alert, index) => {
      console.log(`${index + 1}. [${alert.type.toUpperCase()}] ${alert.title} (${alert.source})`);
    });
  }

  // تشغيل جميع الاختبارات
  function runAllTests() {
    console.log('🏁 بدء تشغيل جميع الاختبارات...');
    console.log('');

    // مسح التنبيهات الموجودة
    advancedService.clearAllAlerts();
    console.log('🧹 تم مسح التنبيهات الموجودة');
    console.log('');

    // تشغيل الاختبارات بالتسلسل
    testAllAlertTypes();
    console.log('');

    setTimeout(() => {
      testDuplicatePrevention();
      console.log('');
    }, 1000);

    setTimeout(() => {
      testFiltering();
      console.log('');
    }, 2000);

    setTimeout(() => {
      testStatistics();
      console.log('');
    }, 3000);

    setTimeout(() => {
      testMuting();
      console.log('');
    }, 4000);

    setTimeout(() => {
      testCleanup();
      console.log('');
    }, 6000);

    setTimeout(() => {
      showFinalResults();
      console.log('');
      console.log('🎉 تم الانتهاء من جميع الاختبارات!');
    }, 7000);
  }

  // إتاحة الدوال للاستخدام اليدوي
  window.testAdvancedAlerts = {
    runAll: runAllTests,
    testTypes: testAllAlertTypes,
    testDuplicates: testDuplicatePrevention,
    testFiltering: testFiltering,
    testStats: testStatistics,
    testMuting: testMuting,
    testCleanup: testCleanup,
    showResults: showFinalResults
  };

  console.log('🛠️ الدوال المتاحة:');
  console.log('   testAdvancedAlerts.runAll() - تشغيل جميع الاختبارات');
  console.log('   testAdvancedAlerts.testTypes() - اختبار أنواع التنبيهات');
  console.log('   testAdvancedAlerts.testDuplicates() - اختبار منع التكرار');
  console.log('   testAdvancedAlerts.testFiltering() - اختبار الفلترة');
  console.log('   testAdvancedAlerts.testStats() - اختبار الإحصائيات');
  console.log('   testAdvancedAlerts.testMuting() - اختبار الكتم');
  console.log('   testAdvancedAlerts.testCleanup() - اختبار التنظيف');
  console.log('   testAdvancedAlerts.showResults() - عرض النتائج');
  console.log('');
  console.log('💡 لتشغيل جميع الاختبارات: testAdvancedAlerts.runAll()');

})();
