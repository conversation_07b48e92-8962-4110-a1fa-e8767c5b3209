/**
 * اختبار سريع للنظام المتقدم للتنبيهات
 * يمكن تشغيله من وحدة التحكم في المتصفح
 */

(function() {
  'use strict';

  console.log('🚀 اختبار سريع للنظام المتقدم');
  console.log('===============================');

  // التحقق من وجود النظام المتقدم
  if (typeof window.advancedAlertService === 'undefined') {
    console.error('❌ النظام المتقدم غير متاح');
    
    // محاولة تحميل النظام القديم
    if (typeof window.alertService !== 'undefined') {
      console.log('🔄 استخدام النظام القديم...');
      
      // تنظيف النظام القديم
      window.alertService.clearAllAlerts();
      console.log('✅ تم تنظيف النظام القديم');
      
      // إنشاء تنبيه نجاح
      window.alertService.addAlert({
        type: 'info',
        title: 'تم التنظيف',
        message: 'تم تنظيف النظام القديم بنجاح',
        source: 'CLEANUP'
      });
    }
    return;
  }

  const service = window.advancedAlertService;

  // تنظيف النظام
  console.log('🧹 تنظيف النظام...');
  service.clearAllAlerts();

  // اختبار سريع لجميع الأنواع
  console.log('🧪 اختبار أنواع التنبيهات...');

  // تنبيه نجاح
  service.addAlert({
    type: 'success',
    title: 'تم الحفظ',
    message: 'تم حفظ البيانات بنجاح',
    source: 'TEST',
    category: 'user_action',
    priority: 3
  });

  // تنبيه معلومات
  setTimeout(() => {
    service.addAlert({
      type: 'info',
      title: 'معلومة مفيدة',
      message: 'النظام الجديد يمنع التنبيهات المكررة تلقائياً',
      source: 'TEST',
      category: 'info',
      priority: 2
    });
  }, 1000);

  // تنبيه تحذير
  setTimeout(() => {
    service.addAlert({
      type: 'warning',
      title: 'تحذير مخزون',
      message: 'بعض المنتجات وصلت للحد الأدنى',
      source: 'TEST',
      category: 'inventory',
      priority: 6
    });
  }, 2000);

  // تنبيه خطأ
  setTimeout(() => {
    service.addAlert({
      type: 'error',
      title: 'خطأ في الشبكة',
      message: 'فشل في الاتصال بالخادم',
      source: 'TEST',
      category: 'network',
      priority: 8
    });
  }, 3000);

  // تنبيه حرج
  setTimeout(() => {
    service.addAlert({
      type: 'critical',
      title: 'خطأ حرج',
      message: 'مشكلة حرجة في النظام تحتاج انتباه فوري',
      source: 'TEST',
      category: 'system',
      priority: 10,
      persistent: true
    });
  }, 4000);

  // اختبار منع التكرار
  setTimeout(() => {
    console.log('🔄 اختبار منع التكرار...');
    
    // محاولة إنشاء نفس التنبيه 3 مرات
    for (let i = 0; i < 3; i++) {
      const result = service.addAlert({
        type: 'info',
        title: 'تنبيه مكرر',
        message: 'هذا تنبيه للاختبار',
        source: 'DUPLICATE_TEST',
        category: 'test',
        priority: 1
      });
      
      if (result) {
        console.log(`✅ تم إنشاء التنبيه ${i + 1}`);
      } else {
        console.log(`❌ تم منع التنبيه المكرر ${i + 1}`);
      }
    }
  }, 5000);

  // عرض الإحصائيات
  setTimeout(() => {
    console.log('📊 الإحصائيات النهائية:');
    const stats = service.getStats();
    console.table(stats);
    
    console.log('📋 التنبيهات الحالية:');
    const alerts = service.getAlerts();
    alerts.forEach((alert, index) => {
      console.log(`${index + 1}. [${alert.type.toUpperCase()}] ${alert.title}`);
    });
    
    console.log('🎉 انتهى الاختبار السريع!');
  }, 7000);

  // إتاحة دوال سريعة
  window.quickTest = {
    success: (title, message) => service.addAlert({
      type: 'success',
      title: title || 'نجح',
      message: message || 'العملية تمت بنجاح',
      source: 'QUICK_TEST',
      category: 'test'
    }),
    
    error: (title, message) => service.addAlert({
      type: 'error',
      title: title || 'خطأ',
      message: message || 'حدث خطأ في العملية',
      source: 'QUICK_TEST',
      category: 'test'
    }),
    
    warning: (title, message) => service.addAlert({
      type: 'warning',
      title: title || 'تحذير',
      message: message || 'انتبه لهذا الأمر',
      source: 'QUICK_TEST',
      category: 'test'
    }),
    
    info: (title, message) => service.addAlert({
      type: 'info',
      title: title || 'معلومة',
      message: message || 'معلومة مفيدة',
      source: 'QUICK_TEST',
      category: 'test'
    }),
    
    critical: (title, message) => service.addAlert({
      type: 'critical',
      title: title || 'حرج',
      message: message || 'مشكلة حرجة',
      source: 'QUICK_TEST',
      category: 'test',
      persistent: true
    }),
    
    clear: () => {
      service.clearAllAlerts();
      console.log('🧹 تم مسح جميع التنبيهات');
    },
    
    stats: () => {
      const stats = service.getStats();
      console.table(stats);
      return stats;
    },
    
    list: () => {
      const alerts = service.getAlerts();
      console.log('📋 التنبيهات الحالية:');
      alerts.forEach((alert, index) => {
        console.log(`${index + 1}. [${alert.type.toUpperCase()}] ${alert.title} (${alert.source})`);
      });
      return alerts;
    }
  };

  console.log('🛠️ الدوال السريعة المتاحة:');
  console.log('   quickTest.success("عنوان", "رسالة") - تنبيه نجاح');
  console.log('   quickTest.error("عنوان", "رسالة") - تنبيه خطأ');
  console.log('   quickTest.warning("عنوان", "رسالة") - تنبيه تحذير');
  console.log('   quickTest.info("عنوان", "رسالة") - تنبيه معلومات');
  console.log('   quickTest.critical("عنوان", "رسالة") - تنبيه حرج');
  console.log('   quickTest.clear() - مسح جميع التنبيهات');
  console.log('   quickTest.stats() - عرض الإحصائيات');
  console.log('   quickTest.list() - عرض قائمة التنبيهات');
  console.log('');
  console.log('💡 مثال: quickTest.success("تم الحفظ", "تم حفظ البيانات بنجاح")');

})();
