/**
 * اختبار أزرار الإغلاق في نظام التنبيهات
 * يمكن تشغيله من وحدة التحكم في المتصفح
 */

(function() {
  'use strict';

  console.log('🧪 اختبار أزرار الإغلاق في التنبيهات');
  console.log('=====================================');

  // التحقق من وجود النظام المتقدم
  if (typeof window.advancedAlertService === 'undefined') {
    console.error('❌ النظام المتقدم غير متاح');

    // جرب النظام القديم
    if (typeof window.alertService !== 'undefined') {
      console.log('🔄 استخدام النظام القديم...');
      testOldSystem();
    }
    return;
  }

  const service = window.advancedAlertService;

  // تنظيف النظام أولاً
  console.log('🧹 تنظيف النظام...');
  service.clearAllAlerts();

  // إنشاء تنبيهات للاختبار
  console.log('📝 إنشاء تنبيهات للاختبار...');

  // تنبيه نجاح مع زر إغلاق
  const successId = service.addAlert({
    type: 'success',
    title: 'تنبيه نجاح',
    message: 'اضغط على زر X في الزاوية العلوية اليمنى لإغلاق هذا التنبيه',
    source: 'CLOSE_TEST',
    category: 'test',
    priority: 3,
    hideAfter: 15000 // 15 ثانية
  });

  // تنبيه معلومات مع زر إغلاق
  const infoId = service.addAlert({
    type: 'info',
    title: 'تنبيه معلومات',
    message: 'يمكنك إغلاق هذا التنبيه بالضغط على زر الإغلاق (X)',
    source: 'CLOSE_TEST',
    category: 'test',
    priority: 2,
    hideAfter: 20000 // 20 ثانية
  });

  // تنبيه تحذير مع زر إغلاق
  const warningId = service.addAlert({
    type: 'warning',
    title: 'تنبيه تحذير',
    message: 'هذا تنبيه تحذير - جرب الضغط على X لإغلاقه',
    source: 'CLOSE_TEST',
    category: 'test',
    priority: 6,
    hideAfter: 25000 // 25 ثانية
  });

  // تنبيه خطأ مع زر إغلاق
  const errorId = service.addAlert({
    type: 'error',
    title: 'تنبيه خطأ',
    message: 'تنبيه خطأ للاختبار - يمكن إغلاقه بزر X',
    source: 'CLOSE_TEST',
    category: 'test',
    priority: 8,
    hideAfter: 30000 // 30 ثانية
  });

  // تنبيه حرج مع زر إغلاق (دائم)
  const criticalId = service.addAlert({
    type: 'critical',
    title: 'تنبيه حرج',
    message: 'تنبيه حرج - لن يختفي تلقائياً، يجب إغلاقه بزر X',
    source: 'CLOSE_TEST',
    category: 'test',
    priority: 10,
    persistent: true,
    autoHide: false
  });

  console.log('✅ تم إنشاء 5 تنبيهات للاختبار');
  console.log('');
  console.log('🎯 تعليمات الاختبار:');
  console.log('1. ابحث عن التنبيهات في الزاوية العلوية اليمنى');
  console.log('2. ستجد زر X في الزاوية العلوية اليمنى لكل تنبيه');
  console.log('3. اضغط على زر X لإغلاق التنبيه');
  console.log('4. جرب إغلاق تنبيهات مختلفة');
  console.log('5. التنبيه الحرج لن يختفي تلقائياً - يجب إغلاقه يدوياً');
  console.log('');

  // مراقبة التنبيهات المغلقة
  let closedAlerts = [];
  const originalRemoveAlert = service.removeAlert.bind(service);
  
  service.removeAlert = function(alertId) {
    const result = originalRemoveAlert(alertId);
    if (result) {
      closedAlerts.push(alertId);
      console.log(`✅ تم إغلاق التنبيه: ${alertId}`);
      console.log(`📊 عدد التنبيهات المغلقة: ${closedAlerts.length}/5`);
      
      if (closedAlerts.length === 5) {
        console.log('🎉 ممتاز! تم إغلاق جميع التنبيهات بنجاح');
        console.log('✅ أزرار الإغلاق تعمل بشكل صحيح');
        
        // إعادة الدالة الأصلية
        service.removeAlert = originalRemoveAlert;
      }
    }
    return result;
  };

  // إتاحة دوال للاختبار اليدوي
  window.closeButtonTest = {
    // إنشاء تنبيه جديد للاختبار
    createTest: (type = 'info') => {
      return service.addAlert({
        type: type,
        title: `تنبيه ${type} للاختبار`,
        message: 'اضغط على زر X لإغلاق هذا التنبيه',
        source: 'MANUAL_TEST',
        category: 'test',
        priority: 5,
        hideAfter: type === 'critical' ? 0 : 10000
      });
    },

    // إغلاق تنبيه محدد
    close: (alertId) => {
      const result = service.removeAlert(alertId);
      if (result) {
        console.log(`✅ تم إغلاق التنبيه: ${alertId}`);
      } else {
        console.log(`❌ فشل في إغلاق التنبيه: ${alertId}`);
      }
      return result;
    },

    // إغلاق جميع التنبيهات
    closeAll: () => {
      service.clearAllAlerts();
      console.log('🧹 تم إغلاق جميع التنبيهات');
    },

    // عرض التنبيهات الحالية
    list: () => {
      const alerts = service.getAlerts();
      console.log('📋 التنبيهات الحالية:');
      alerts.forEach((alert, index) => {
        console.log(`${index + 1}. [${alert.type.toUpperCase()}] ${alert.title} (ID: ${alert.id})`);
      });
      return alerts;
    },

    // اختبار شامل لجميع الأنواع
    testAll: () => {
      service.clearAllAlerts();
      
      const types = ['success', 'info', 'warning', 'error', 'critical'];
      types.forEach((type, index) => {
        setTimeout(() => {
          window.closeButtonTest.createTest(type);
        }, index * 1000);
      });
      
      console.log('🧪 تم إنشاء تنبيهات من جميع الأنواع للاختبار');
    }
  };

  console.log('🛠️ الدوال المتاحة للاختبار اليدوي:');
  console.log('   closeButtonTest.createTest("success") - إنشاء تنبيه للاختبار');
  console.log('   closeButtonTest.close("alert_id") - إغلاق تنبيه محدد');
  console.log('   closeButtonTest.closeAll() - إغلاق جميع التنبيهات');
  console.log('   closeButtonTest.list() - عرض التنبيهات الحالية');
  console.log('   closeButtonTest.testAll() - اختبار جميع الأنواع');
  console.log('');

  // تذكير بعد 10 ثواني
  setTimeout(() => {
    const remainingAlerts = service.getAlerts().length;
    if (remainingAlerts > 0) {
      console.log(`⏰ تذكير: لا يزال هناك ${remainingAlerts} تنبيهات مفتوحة`);
      console.log('💡 جرب الضغط على زر X لإغلاقها');
    }
  }, 10000);

  // تذكير نهائي بعد 30 ثانية
  setTimeout(() => {
    const remainingAlerts = service.getAlerts().length;
    if (remainingAlerts > 0) {
      console.log(`🔔 تذكير نهائي: ${remainingAlerts} تنبيهات لا تزال مفتوحة`);
      console.log('🎯 التنبيهات الحرجة لا تختفي تلقائياً - يجب إغلاقها يدوياً');
      
      // عرض قائمة التنبيهات المتبقية
      const alerts = service.getAlerts();
      alerts.forEach((alert, index) => {
        console.log(`${index + 1}. [${alert.type.toUpperCase()}] ${alert.title}`);
      });
    } else {
      console.log('🎉 ممتاز! تم إغلاق جميع التنبيهات');
    }
  }, 30000);

  // دالة اختبار النظام القديم
  function testOldSystem() {
    console.log('🔄 اختبار النظام القديم مع أزرار الإغلاق الجديدة...');

    const oldService = window.alertService;

    // تنظيف النظام
    oldService.clearAllAlerts();

    // إنشاء تنبيهات للاختبار
    oldService.addAlert({
      type: 'info',
      title: 'تنبيه معلومات',
      message: 'هذا تنبيه من النظام القديم مع زر إغلاق جديد - اضغط على X لإغلاقه',
      source: 'OLD_SYSTEM_TEST'
    });

    oldService.addAlert({
      type: 'warning',
      title: 'تنبيه تحذير',
      message: 'تنبيه تحذير مع زر إغلاق - جرب الضغط على X',
      source: 'OLD_SYSTEM_TEST'
    });

    oldService.addAlert({
      type: 'error',
      title: 'تنبيه خطأ',
      message: 'تنبيه خطأ مع زر إغلاق - يمكن إغلاقه بزر X',
      source: 'OLD_SYSTEM_TEST'
    });

    oldService.addAlert({
      type: 'critical',
      title: 'تنبيه حرج',
      message: 'تنبيه حرج - لن يختفي تلقائياً، يجب إغلاقه بزر X',
      source: 'OLD_SYSTEM_TEST'
    });

    console.log('✅ تم إنشاء 4 تنبيهات من النظام القديم مع أزرار إغلاق جديدة');
    console.log('🎯 ابحث عن زر X في الزاوية العلوية اليمنى لكل تنبيه');

    // إتاحة دوال للاختبار
    window.oldSystemTest = {
      createAlert: (type = 'info') => {
        oldService.addAlert({
          type: type,
          title: `تنبيه ${type}`,
          message: 'تنبيه للاختبار مع زر إغلاق - اضغط على X',
          source: 'MANUAL_OLD_TEST'
        });
      },

      clearAll: () => {
        oldService.clearAllAlerts();
        console.log('🧹 تم مسح جميع التنبيهات');
      }
    };

    console.log('🛠️ دوال النظام القديم المتاحة:');
    console.log('   oldSystemTest.createAlert("warning") - إنشاء تنبيه');
    console.log('   oldSystemTest.clearAll() - مسح جميع التنبيهات');
  }

})();
