/**
 * إصلاح فوري لمشكلة الحلقة المفرغة في التنبيهات الحرجة
 * يمكن تشغيل هذا السكريبت في وحدة التحكم للإصلاح الفوري
 * الإصدار المحسن - يتعامل مع جميع أنواع التنبيهات المكررة
 */

(function() {
  console.log('🔧 بدء إصلاح مشكلة التنبيهات الحرجة المكررة (الإصدار المحسن)...');

  // التحقق من وجود خدمة التنبيهات
  if (typeof window.alertService === 'undefined') {
    console.error('❌ خدمة التنبيهات غير متوفرة');
    return;
  }

  const alertService = window.alertService;

  // فحص النظام المتقدم أيضاً
  const advancedAlertService = window.advancedAlertService;

  try {
    // 1. إعادة تعيين عداد الأخطاء الحرجة
    if (typeof alertService.resetCriticalErrorCount === 'function') {
      alertService.resetCriticalErrorCount();
      console.log('✅ تم إعادة تعيين عداد الأخطاء الحرجة');
    }

    // 2. إزالة جميع التنبيهات من ALERT_SYSTEM و CRITICAL_ERRORS
    const alerts = alertService.getAlerts();
    const problematicSources = ['ALERT_SYSTEM', 'CRITICAL_ERRORS', 'SYSTEM_LOGS'];
    const problematicAlerts = alerts.filter(alert =>
      problematicSources.includes(alert.source) ||
      (alert.type === 'critical' && alert.title.includes('أخطاء حرجة'))
    );

    problematicAlerts.forEach(alert => {
      alertService.removeAlert(alert.id);
    });

    if (problematicAlerts.length > 0) {
      console.log(`✅ تم إزالة ${problematicAlerts.length} تنبيه مشكوك فيه`);
    }

    // 3. إزالة التنبيهات المكررة الحرجة (تحسين أفضل)
    const criticalAlerts = alerts.filter(alert => alert.type === 'critical');
    const duplicateCriticalAlerts = [];
    const seenFingerprints = new Map();

    criticalAlerts.forEach(alert => {
      const fingerprint = `${alert.title}-${alert.source}-${alert.type}`;
      if (seenFingerprints.has(fingerprint)) {
        // الاحتفاظ بالأحدث
        const existing = seenFingerprints.get(fingerprint);
        if (alert.timestamp > existing.timestamp) {
          duplicateCriticalAlerts.push(existing);
          seenFingerprints.set(fingerprint, alert);
        } else {
          duplicateCriticalAlerts.push(alert);
        }
      } else {
        seenFingerprints.set(fingerprint, alert);
      }
    });

    duplicateCriticalAlerts.forEach(alert => {
      alertService.removeAlert(alert.id);
    });

    if (duplicateCriticalAlerts.length > 0) {
      console.log(`✅ تم إزالة ${duplicateCriticalAlerts.length} تنبيه حرج مكرر`);
    }

    // 4. تنظيف النظام المتقدم إذا كان متوفراً
    if (advancedAlertService && typeof advancedAlertService.clearAllAlerts === 'function') {
      const advancedAlerts = advancedAlertService.getAlerts();
      if (advancedAlerts.length > 0) {
        advancedAlertService.clearAllAlerts();
        console.log(`✅ تم مسح ${advancedAlerts.length} تنبيه من النظام المتقدم`);
      }
    }

    // 5. تنظيف شامل
    if (typeof alertService.performFullCleanup === 'function') {
      alertService.performFullCleanup();
      console.log('✅ تم التنظيف الشامل');
    }

    // 5. إظهار إحصائيات نهائية
    const finalAlerts = alertService.getAlerts();
    const finalStats = {
      total: finalAlerts.length,
      critical: finalAlerts.filter(a => a.type === 'critical').length,
      error: finalAlerts.filter(a => a.type === 'error').length,
      warning: finalAlerts.filter(a => a.type === 'warning').length,
      info: finalAlerts.filter(a => a.type === 'info').length
    };

    console.log('📊 إحصائيات التنبيهات النهائية:', finalStats);
    console.log('🎉 تم إصلاح مشكلة التنبيهات الحرجة بنجاح!');

    // 6. إنشاء تنبيه نجاح
    alertService.addAlert({
      type: 'info',
      title: 'تم إصلاح مشكلة التنبيهات',
      message: 'تم حل مشكلة التنبيهات الحرجة المكررة بنجاح.',
      source: 'SYSTEM_REPAIR'
    });

  } catch (error) {
    console.error('❌ فشل في إصلاح المشكلة:', error);
    
    // محاولة إصلاح بديلة
    try {
      // إزالة جميع التنبيهات والبدء من جديد
      alertService.clearAllAlerts();
      console.log('⚠️ تم مسح جميع التنبيهات كحل بديل');
      
      alertService.addAlert({
        type: 'warning',
        title: 'تم إعادة تعيين النظام',
        message: 'تم مسح جميع التنبيهات لحل مشكلة التكرار.',
        source: 'EMERGENCY_RESET'
      });
      
    } catch (fallbackError) {
      console.error('❌ فشل الحل البديل أيضاً:', fallbackError);
    }
  }
})();

// إضافة دالة مساعدة للوصول السريع
window.fixCriticalAlertLoop = function() {
  const script = document.createElement('script');
  script.src = '/fix-critical-alert-loop.js';
  document.head.appendChild(script);
};

console.log('💡 لتشغيل الإصلاح مرة أخرى، استخدم: fixCriticalAlertLoop()');
