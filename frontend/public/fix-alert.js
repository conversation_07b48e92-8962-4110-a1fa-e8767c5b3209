/**
 * سكريبت إصلاح سريع للتنبيهات
 * يمكن تشغيله من وحدة التحكم في المتصفح
 */

(function() {
  'use strict';

  console.log('🔧 أداة إصلاح التنبيهات السريعة');
  console.log('================================');

  // التحقق من وجود alertService
  if (typeof window.alertService === 'undefined') {
    console.error('❌ alertService غير متاح. تأكد من تحميل التطبيق بشكل صحيح.');
    return;
  }

  const alertService = window.alertService;

  // دالة لحل التنبيه المحدد
  function fixSpecificAlert(alertId) {
    console.log(`🔍 البحث عن التنبيه: ${alertId}`);
    
    const alerts = alertService.getAlerts();
    const targetAlert = alerts.find(alert => alert.id === alertId);
    
    if (!targetAlert) {
      console.log('❌ التنبيه غير موجود في النظام');
      return false;
    }

    console.log('📋 تفاصيل التنبيه:');
    console.log(`   النوع: ${targetAlert.type}`);
    console.log(`   العنوان: ${targetAlert.title}`);
    console.log(`   الرسالة: ${targetAlert.message}`);
    console.log(`   المصدر: ${targetAlert.source}`);
    console.log(`   الوقت: ${targetAlert.timestamp}`);

    // حذف التنبيه
    alertService.removeAlert(alertId);
    console.log('✅ تم حذف التنبيه');

    // إذا كان حرج، إعادة تعيين العداد
    if (targetAlert.type === 'critical') {
      alertService.resetCriticalErrorCount();
      console.log('✅ تم إعادة تعيين عداد الأخطاء الحرجة');
    }

    return true;
  }

  // دالة للتنظيف الشامل
  function performFullCleanup() {
    console.log('🧹 بدء التنظيف الشامل...');
    
    const beforeCount = alertService.getAlerts().length;
    console.log(`📊 عدد التنبيهات قبل التنظيف: ${beforeCount}`);

    // تنظيف شامل
    alertService.performFullCleanup();
    
    const afterCount = alertService.getAlerts().length;
    console.log(`📊 عدد التنبيهات بعد التنظيف: ${afterCount}`);
    console.log(`🗑️ تم حذف ${beforeCount - afterCount} تنبيهات`);
    
    console.log('✅ التنظيف الشامل مكتمل');
  }

  // دالة لعرض الإحصائيات
  function showStats() {
    console.log('📊 إحصائيات التنبيهات:');
    console.log('========================');
    
    const stats = alertService.getAlertStatistics();
    console.table(stats);
    
    const alerts = alertService.getAlerts();
    console.log(`\n📋 التنبيهات الحالية (${alerts.length}):`);
    
    alerts.forEach((alert, index) => {
      const timeAgo = Math.round((new Date() - alert.timestamp) / 1000 / 60);
      console.log(`${index + 1}. [${alert.type.toUpperCase()}] ${alert.title} (منذ ${timeAgo} دقيقة)`);
    });
  }

  // حل المشكلة المحددة
  const problematicAlertId = 'alert_1749663360320_24etws1r4';

  console.log(`🎯 محاولة حل التنبيه المشكل: ${problematicAlertId}`);

  if (fixSpecificAlert(problematicAlertId)) {
    console.log('🎉 تم حل المشكلة بنجاح!');
  } else {
    console.log('ℹ️ التنبيه غير موجود (ربما تم حذفه مسبقاً)');
  }

  // تنظيف شامل إضافي
  performFullCleanup();

  // فحص النظام الجديد إذا كان متاحاً
  if (typeof window.advancedAlertService !== 'undefined') {
    console.log('🔧 تم العثور على النظام المتقدم - تشغيل التنظيف المتقدم...');

    const advancedService = window.advancedAlertService;

    // تنظيف النظام المتقدم
    advancedService.clearAllAlerts();
    console.log('✅ تم مسح جميع التنبيهات من النظام المتقدم');

    // عرض إحصائيات النظام المتقدم
    const stats = advancedService.getStats();
    console.log('📊 إحصائيات النظام المتقدم:', stats);

    // إنشاء تنبيه نجاح
    advancedService.addAlert({
      type: 'success',
      title: 'تم إصلاح النظام',
      message: 'تم حل مشكلة التنبيهات وتنظيف النظام بنجاح',
      source: 'SYSTEM_REPAIR',
      category: 'maintenance',
      priority: 5,
      hideAfter: 5000
    });

    console.log('🎉 تم تطبيق النظام المتقدم بنجاح!');
  }

  // عرض الإحصائيات النهائية
  showStats();

  // إتاحة الدوال للاستخدام اليدوي
  window.fixAlert = fixSpecificAlert;
  window.cleanupAlerts = performFullCleanup;
  window.showAlertStats = showStats;

  console.log('\n🛠️ الدوال المتاحة:');
  console.log('   fixAlert(alertId) - لحذف تنبيه محدد');
  console.log('   cleanupAlerts() - للتنظيف الشامل');
  console.log('   showAlertStats() - لعرض الإحصائيات');

})();
