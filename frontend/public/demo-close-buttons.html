<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض أزرار الإغلاق - SmartPOS</title>
    <style>
        body {
            font-family: 'almarai', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            direction: rtl;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #2d3748;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }
        
        .header p {
            color: #718096;
            font-size: 1.1rem;
        }
        
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f7fafc;
            border-radius: 10px;
            border-right: 4px solid #4299e1;
        }
        
        .demo-section h3 {
            color: #2d3748;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .demo-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .demo-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn-success {
            background: #48bb78;
            color: white;
        }
        
        .btn-info {
            background: #4299e1;
            color: white;
        }
        
        .btn-warning {
            background: #ed8936;
            color: white;
        }
        
        .btn-error {
            background: #f56565;
            color: white;
        }
        
        .btn-critical {
            background: #e53e3e;
            color: white;
        }
        
        .btn-clear {
            background: #718096;
            color: white;
        }
        
        .instructions {
            background: #e6fffa;
            border: 1px solid #81e6d9;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .instructions h4 {
            color: #234e52;
            margin-bottom: 10px;
        }
        
        .instructions ul {
            color: #2c7a7b;
            margin: 0;
            padding-right: 20px;
        }
        
        .code-block {
            background: #1a202c;
            color: #68d391;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            overflow-x: auto;
            margin-top: 15px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
            text-align: center;
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .status {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: #2d3748;
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔔 أزرار الإغلاق الجديدة</h1>
            <p>تجربة تفاعلية لأزرار الإغلاق في نظام التنبيهات المتقدم</p>
        </div>

        <div class="demo-section">
            <h3>🧪 اختبار أزرار الإغلاق</h3>
            <div class="instructions">
                <h4>📋 التعليمات:</h4>
                <ul>
                    <li>اضغط على أي زر لإنشاء تنبيه</li>
                    <li>ابحث عن زر X في الزاوية العلوية اليمنى للتنبيه</li>
                    <li>اضغط على زر X لإغلاق التنبيه فوراً</li>
                    <li>التنبيهات الحرجة لا تختفي تلقائياً</li>
                </ul>
            </div>
            
            <div class="demo-buttons">
                <button class="demo-btn btn-success" onclick="testAlert('success')">
                    ✅ تنبيه نجاح
                </button>
                <button class="demo-btn btn-info" onclick="testAlert('info')">
                    ℹ️ تنبيه معلومات
                </button>
                <button class="demo-btn btn-warning" onclick="testAlert('warning')">
                    ⚠️ تنبيه تحذير
                </button>
                <button class="demo-btn btn-error" onclick="testAlert('error')">
                    ❌ تنبيه خطأ
                </button>
                <button class="demo-btn btn-critical" onclick="testAlert('critical')">
                    🚨 تنبيه حرج
                </button>
                <button class="demo-btn btn-clear" onclick="clearAllAlerts()">
                    🧹 مسح الكل
                </button>
            </div>
        </div>

        <div class="demo-section">
            <h3>🎯 الميزات الجديدة</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">❌</div>
                    <h4>زر إغلاق مرئي</h4>
                    <p>زر X واضح في كل تنبيه</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🎨</div>
                    <h4>تصميم احترافي</h4>
                    <p>ألوان مميزة لكل نوع</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h4>استجابة سريعة</h4>
                    <p>إغلاق فوري عند الضغط</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🌙</div>
                    <h4>الوضع المظلم</h4>
                    <p>دعم كامل للوضع المظلم</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>💻 للمطورين</h3>
            <p>يمكن استخدام النظام الجديد بسهولة:</p>
            <div class="code-block">
// استخدام التنبيهات المخصصة<br/>
import { customToast } from '../components/alerts/CustomToast';<br/><br/>

// تنبيه نجاح مع زر إغلاق<br/>
customToast.success('تم الحفظ', 'تم حفظ البيانات بنجاح');<br/><br/>

// تنبيه خطأ مع زر إغلاق<br/>
customToast.error('خطأ', 'فشل في الحفظ');<br/><br/>

// تنبيه مخصص مع خيارات<br/>
customToast.warning('تحذير', 'انتبه لهذا الأمر', {<br/>
&nbsp;&nbsp;duration: 10000,<br/>
&nbsp;&nbsp;onClose: () => console.log('تم إغلاق التنبيه')<br/>
});
            </div>
        </div>

        <div class="demo-section">
            <h3>🔧 اختبار من وحدة التحكم</h3>
            <p>افتح وحدة التحكم (F12) وجرب هذه الأوامر:</p>
            <div class="code-block">
// تحميل اختبار شامل<br/>
const script = document.createElement('script');<br/>
script.src = '/test-close-buttons.js';<br/>
document.head.appendChild(script);<br/><br/>

// أو جرب مباشرة<br/>
testAlert('success');<br/>
testAlert('error');<br/>
clearAllAlerts();
            </div>
        </div>
    </div>

    <div class="status" id="status">
        جاهز للاختبار ✅
    </div>

    <script>
        let alertCount = 0;
        
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }
        
        function testAlert(type) {
            alertCount++;
            
            const messages = {
                success: {
                    title: 'تم بنجاح!',
                    message: `عملية ناجحة رقم ${alertCount} - اضغط على X لإغلاق هذا التنبيه`
                },
                info: {
                    title: 'معلومة مفيدة',
                    message: `معلومة رقم ${alertCount} - يمكنك إغلاق هذا التنبيه بزر X`
                },
                warning: {
                    title: 'تحذير مهم',
                    message: `تحذير رقم ${alertCount} - انتبه! اضغط X للإغلاق`
                },
                error: {
                    title: 'حدث خطأ',
                    message: `خطأ رقم ${alertCount} - يرجى المحاولة مرة أخرى. اضغط X للإغلاق`
                },
                critical: {
                    title: 'خطأ حرج!',
                    message: `خطأ حرج رقم ${alertCount} - يحتاج انتباه فوري! لن يختفي تلقائياً - اضغط X للإغلاق`
                }
            };
            
            const alert = messages[type];
            
            // محاكاة إنشاء تنبيه
            if (typeof window.customToast !== 'undefined') {
                window.customToast[type](alert.title, alert.message);
            } else if (typeof window.advancedAlertService !== 'undefined') {
                window.advancedAlertService.addAlert({
                    type: type,
                    title: alert.title,
                    message: alert.message,
                    source: 'DEMO',
                    category: 'test',
                    priority: type === 'critical' ? 10 : 5
                });
            } else if (typeof window.alertService !== 'undefined') {
                window.alertService.addAlert({
                    type: type,
                    title: alert.title,
                    message: alert.message,
                    source: 'DEMO'
                });
            } else {
                // fallback للعرض فقط
                alert(`${alert.title}\n\n${alert.message}\n\nملاحظة: هذا مجرد عرض - النظام الحقيقي سيحتوي على زر إغلاق X`);
            }
            
            updateStatus(`تم إنشاء تنبيه ${type} رقم ${alertCount}`);
        }
        
        function clearAllAlerts() {
            if (typeof window.advancedAlertService !== 'undefined') {
                window.advancedAlertService.clearAllAlerts();
            } else if (typeof window.alertService !== 'undefined') {
                window.alertService.clearAllAlerts();
            }
            
            updateStatus('تم مسح جميع التنبيهات');
        }
        
        // رسالة ترحيب
        setTimeout(() => {
            updateStatus('مرحباً! جرب الأزرار أعلاه لاختبار أزرار الإغلاق');
        }, 2000);
    </script>
</body>
</html>
