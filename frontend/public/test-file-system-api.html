<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار File System Access API</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
        }
        .supported {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .not-supported {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .info {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
        .result {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid #dee2e6;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار File System Access API</h1>
        
        <div id="browserInfo" class="info">
            <h3>معلومات المتصفح:</h3>
            <div id="userAgent"></div>
        </div>

        <div id="supportStatus"></div>

        <div class="info">
            <h3>الاختبارات المتاحة:</h3>
            <button id="testDirectoryPicker" onclick="testDirectoryPicker()">
                🗂️ اختبار اختيار المجلد
            </button>
            <button id="testFilePicker" onclick="testFilePicker()">
                📄 اختبار اختيار الملف
            </button>
        </div>

        <div id="results"></div>

        <div class="info">
            <h3>المتصفحات المدعومة:</h3>
            <ul>
                <li>✅ Chrome 86+ (مدعوم بالكامل)</li>
                <li>✅ Edge 86+ (مدعوم بالكامل)</li>
                <li>✅ Opera 72+ (مدعوم بالكامل)</li>
                <li>❌ Firefox (غير مدعوم حالياً)</li>
                <li>❌ Safari (غير مدعوم حالياً)</li>
            </ul>
            <p><strong>ملاحظة:</strong> يتطلب HTTPS أو localhost للعمل</p>
        </div>
    </div>

    <script>
        // عرض معلومات المتصفح
        document.getElementById('userAgent').textContent = navigator.userAgent;

        // فحص الدعم
        function checkSupport() {
            const hasShowDirectoryPicker = 'showDirectoryPicker' in window;
            const hasShowOpenFilePicker = 'showOpenFilePicker' in window;
            const isSecureContext = window.isSecureContext;
            const isFunction = typeof window.showDirectoryPicker === 'function';

            let statusHtml = '';
            let statusClass = '';

            if (hasShowDirectoryPicker && isFunction && isSecureContext) {
                statusHtml = '✅ File System Access API مدعوم بالكامل!';
                statusClass = 'supported';
                document.getElementById('testDirectoryPicker').disabled = false;
                document.getElementById('testFilePicker').disabled = false;
            } else {
                statusHtml = '❌ File System Access API غير مدعوم<br>';
                statusClass = 'not-supported';
                
                if (!hasShowDirectoryPicker) {
                    statusHtml += '• showDirectoryPicker غير موجود<br>';
                }
                if (!isFunction) {
                    statusHtml += '• showDirectoryPicker ليس دالة<br>';
                }
                if (!isSecureContext) {
                    statusHtml += '• السياق غير آمن (يتطلب HTTPS أو localhost)<br>';
                }
                
                document.getElementById('testDirectoryPicker').disabled = true;
                document.getElementById('testFilePicker').disabled = true;
            }

            document.getElementById('supportStatus').innerHTML = statusHtml;
            document.getElementById('supportStatus').className = `status ${statusClass}`;
        }

        // اختبار اختيار المجلد
        async function testDirectoryPicker() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="result">جاري اختبار اختيار المجلد...</div>';

            try {
                const dirHandle = await window.showDirectoryPicker({
                    mode: 'readwrite',
                    startIn: 'documents',
                    id: 'test-folder-picker'
                });

                const result = `✅ نجح اختيار المجلد!
اسم المجلد: ${dirHandle.name}
نوع المقبض: ${dirHandle.kind}
الوقت: ${new Date().toLocaleString('ar')}`;

                resultsDiv.innerHTML = `<div class="result">${result}</div>`;
            } catch (error) {
                let errorMsg = '❌ فشل في اختيار المجلد:\n';
                if (error.name === 'AbortError') {
                    errorMsg += 'تم إلغاء العملية من قبل المستخدم';
                } else {
                    errorMsg += `خطأ: ${error.name}\nالرسالة: ${error.message}`;
                }
                resultsDiv.innerHTML = `<div class="result">${errorMsg}</div>`;
            }
        }

        // اختبار اختيار الملف
        async function testFilePicker() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="result">جاري اختبار اختيار الملف...</div>';

            try {
                const fileHandles = await window.showOpenFilePicker({
                    multiple: false,
                    startIn: 'documents',
                    id: 'test-file-picker'
                });

                const fileHandle = fileHandles[0];
                const file = await fileHandle.getFile();

                const result = `✅ نجح اختيار الملف!
اسم الملف: ${file.name}
الحجم: ${file.size} بايت
النوع: ${file.type || 'غير محدد'}
آخر تعديل: ${new Date(file.lastModified).toLocaleString('ar')}`;

                resultsDiv.innerHTML = `<div class="result">${result}</div>`;
            } catch (error) {
                let errorMsg = '❌ فشل في اختيار الملف:\n';
                if (error.name === 'AbortError') {
                    errorMsg += 'تم إلغاء العملية من قبل المستخدم';
                } else {
                    errorMsg += `خطأ: ${error.name}\nالرسالة: ${error.message}`;
                }
                resultsDiv.innerHTML = `<div class="result">${errorMsg}</div>`;
            }
        }

        // تشغيل الفحص عند تحميل الصفحة
        checkSupport();
    </script>
</body>
</html>
