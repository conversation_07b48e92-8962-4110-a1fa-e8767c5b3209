#!/usr/bin/env python3
"""
فحص بيانات المديونية لتحديد المشكلة
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from database.session import get_db
from models.customer import CustomerDebt, DebtPayment
from datetime import datetime, timedelta
from utils.datetime_utils import get_tripoli_now

def debug_debt_data():
    """فحص بيانات المديونية"""
    
    db = next(get_db())
    
    try:
        current_time = get_tripoli_now()
        
        print("🔍 فحص بيانات المديونية...")
        
        # 1. إجمالي الديون
        total_debts = db.query(func.count(CustomerDebt.id)).scalar() or 0
        total_amount = db.query(func.sum(CustomerDebt.amount)).scalar() or 0.0
        
        print(f"📊 إجمالي الديون: {total_debts} دين بمبلغ {total_amount:.2f} د.ل")
        
        # 2. الديون غير المدفوعة
        unpaid_debts = db.query(func.count(CustomerDebt.id)).filter(CustomerDebt.is_paid == False).scalar() or 0
        unpaid_amount = db.query(func.sum(CustomerDebt.amount)).filter(CustomerDebt.is_paid == False).scalar() or 0.0
        
        print(f"📊 الديون غير المدفوعة: {unpaid_debts} دين بمبلغ {unpaid_amount:.2f} د.ل")
        
        # 3. إجمالي المدفوعات
        total_payments = db.query(func.sum(DebtPayment.amount)).scalar() or 0.0
        
        print(f"📊 إجمالي المدفوعات: {total_payments:.2f} د.ل")
        
        # 4. المبلغ المتبقي المحسوب
        remaining_calculated = unpaid_amount - total_payments
        print(f"📊 المبلغ المتبقي المحسوب (بسيط): {remaining_calculated:.2f} د.ل")
        
        # 5. المبلغ المتبقي الدقيق (نفس منطق الكود)
        remaining_amount_query = db.query(
            func.sum(
                CustomerDebt.amount - func.coalesce(
                    db.query(func.sum(DebtPayment.amount))
                    .filter(DebtPayment.debt_id == CustomerDebt.id)
                    .scalar_subquery(), 0
                )
            )
        ).filter(CustomerDebt.is_paid == False)
        
        remaining_precise = remaining_amount_query.scalar() or 0.0
        print(f"📊 المبلغ المتبقي الدقيق: {remaining_precise:.2f} د.ل")
        
        # 6. فحص أعمار الديون
        print("\n🔍 فحص أعمار الديون...")
        
        aging_ranges = [
            ("0-30 يوم", 0, 30),
            ("31-60 يوم", 31, 60),
            ("61-90 يوم", 61, 90),
            ("أكثر من 90 يوم", 91, None)
        ]
        
        total_aging_amount = 0
        total_aging_count = 0
        
        for range_name, start_days, end_days in aging_ranges:
            if end_days is None:
                start_date = current_time - timedelta(days=start_days)
                end_date = None
            else:
                start_date = current_time - timedelta(days=end_days)
                end_date = current_time - timedelta(days=start_days)
            
            # عدد الديون في هذه الفترة
            count_query = db.query(func.count(CustomerDebt.id)).filter(CustomerDebt.is_paid == False)
            
            if start_date is not None:
                count_query = count_query.filter(CustomerDebt.created_at >= start_date)
            if end_date is not None:
                count_query = count_query.filter(CustomerDebt.created_at <= end_date)
            
            count = count_query.scalar() or 0
            
            # مبلغ الديون في هذه الفترة (المبلغ الأصلي)
            original_amount_query = db.query(func.sum(CustomerDebt.amount)).filter(CustomerDebt.is_paid == False)
            
            if start_date is not None:
                original_amount_query = original_amount_query.filter(CustomerDebt.created_at >= start_date)
            if end_date is not None:
                original_amount_query = original_amount_query.filter(CustomerDebt.created_at <= end_date)
            
            original_amount = original_amount_query.scalar() or 0.0
            
            # المبلغ المتبقي في هذه الفترة
            remaining_amount_query = db.query(
                func.sum(
                    CustomerDebt.amount - func.coalesce(
                        db.query(func.sum(DebtPayment.amount))
                        .filter(DebtPayment.debt_id == CustomerDebt.id)
                        .scalar_subquery(), 0
                    )
                )
            ).filter(CustomerDebt.is_paid == False)
            
            if start_date is not None:
                remaining_amount_query = remaining_amount_query.filter(CustomerDebt.created_at >= start_date)
            if end_date is not None:
                remaining_amount_query = remaining_amount_query.filter(CustomerDebt.created_at <= end_date)
            
            remaining_amount = remaining_amount_query.scalar() or 0.0
            
            print(f"  {range_name}: {count} دين")
            print(f"    المبلغ الأصلي: {original_amount:.2f} د.ل")
            print(f"    المبلغ المتبقي: {remaining_amount:.2f} د.ل")
            
            total_aging_amount += remaining_amount
            total_aging_count += count
        
        print(f"\n📊 مجموع أعمار الديون:")
        print(f"  العدد: {total_aging_count}")
        print(f"  المبلغ المتبقي: {total_aging_amount:.2f} د.ل")
        print(f"  الفرق مع المبلغ المتبقي الدقيق: {abs(remaining_precise - total_aging_amount):.2f} د.ل")
        
        # 7. فحص الديون التي لها مدفوعات
        debts_with_payments = db.query(func.count(func.distinct(DebtPayment.debt_id))).scalar() or 0
        print(f"\n📊 الديون التي لها مدفوعات: {debts_with_payments} دين")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    debug_debt_data()
