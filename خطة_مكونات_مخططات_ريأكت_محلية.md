
# خطة إنشاء مكونات مخططات React تدعم التنسيق المحلي واللغة العربية

## 🎯 الهدف
إنشاء مكونات مخططات قابلة لإعادة الاستخدام في React + Vite + TypeScript  
تدعم:
- تنسيق التاريخ والوقت من الإعدادات (timezone + format)
- اللغة العربية
- أنواع مخططات متعددة (خط، أعمدة، مساحات)
- تكامل مع `useDateTimeFormatters`, `FormattedDate`, `FormattedTime`, `formatDateTime`

---

## 🏗️ هيكلة المشروع

```
/components
  /charts
    BaseChart.tsx             ← مكون عام يهيئ ApexCharts
    LineChart.tsx             ← مخصص للمخططات الخطية
    BarChart.tsx              ← مخصص لمخططات الأعمدة
    AreaChart.tsx             ← للمخططات المساحية
    ChartWrapper.tsx          ← يتكامل مع التنسيقات واللغة
  /formatters
    useDateTimeFormatters.ts  ← موجود لديك
```

---

## ⚙️ BaseChart.tsx (المكون الأساسي العام)

```tsx
import Chart from 'react-apexcharts';

const BaseChart = ({ options, series, height = 300, width = '100%' }) => {
  return (
    <Chart
      options={options}
      series={series}
      type={options.chart?.type || 'line'}
      height={height}
      width={width}
    />
  );
};
```

---

## 🌍 ChartWrapper.tsx (يدعم الإعدادات واللغة العربية)

```tsx
import { useDateTimeFormatters } from '@/formatters/useDateTimeFormatters';
import BaseChart from './BaseChart';

const ChartWrapper = ({ series, chartType, categories, height, width, title }) => {
  const { formatDateTime } = useDateTimeFormatters();

  const options = {
    chart: {
      type: chartType,
      fontFamily: 'almarai, sans-serif',
      locales: [{ name: 'ar', options: { months: [...], days: [...], toolbar: {...} } }],
      defaultLocale: 'ar',
    },
    xaxis: {
      type: 'datetime',
      categories,
      labels: {
        formatter: (value) => formatDateTime(Number(value)),
      },
    },
    tooltip: {
      x: {
        formatter: (value) => formatDateTime(value),
      },
    },
    title: { text: title, align: 'center' },
  };

  return <BaseChart series={series} options={options} height={height} width={width} />;
};
```

---

## 📊 LineChart.tsx (مكون مخصص للمخططات الخطية)

```tsx
import ChartWrapper from './ChartWrapper';

const LineChart = ({ data, categories, title }) => (
  <ChartWrapper
    series={data}
    chartType="line"
    categories={categories}
    title={title}
  />
);
```

---

## 🧪 مثال الاستخدام

```tsx
import LineChart from '@/components/charts/LineChart';

const series = [
  {
    name: 'المبيعات',
    data: [
      { x: new Date("2024-07-01").getTime(), y: 120 },
      { x: new Date("2024-07-02").getTime(), y: 150 },
    ],
  },
];

export default function Dashboard() {
  return <LineChart data={series} title="تقرير المبيعات اليومية" />;
}
```

---

## ✅ الميزات
- دعم اللغة العربية والمحتوى منسق RTL.
- يدعم تنسيق التاريخ المحلي والمنطقة الزمنية.
- قابل للتوسعة لأي نوع مخطط.
- مخصص لمشاريع تعتمد على الإعدادات المركزية للتنسيق.

