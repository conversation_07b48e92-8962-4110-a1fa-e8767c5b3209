# 🚀 SmartPOS - نظام نقاط البيع الذكي

<div align="center">

![SmartPOS](https://img.shields.io/badge/SmartPOS-v3.0.0-blue?style=for-the-badge&logo=react)
![License](https://img.shields.io/badge/License-MIT-green?style=for-the-badge)
![Arabic](https://img.shields.io/badge/Language-Arabic-red?style=for-the-badge)
![Status](https://img.shields.io/badge/Status-Active-brightgreen?style=for-the-badge)

**نظام نقاط البيع الذكي الأكثر تطوراً في ليبيا والعالم العربي**

*مفتوح المصدر • عربي 100% • تقنيات حديثة*

[🚀 البدء السريع](#-البدء-السريع) • [📖 الوثائق](#-الوثائق) • [💬 المحادثة](#-المحادثة-الفورية) • [🔐 الأمان](#-إدارة-الأجهزة) • [📊 التقارير](#-التقارير)

</div>

---

## 📋 جدول المحتويات

- [🌟 نظرة عامة](#-نظرة-عامة)
- [🚀 البدء السريع](#-البدء-السريع)
- [🎯 الميزات الأساسية](#-الميزات-الأساسية)
- [🏗️ هيكلية المشروع](#️-هيكلية-المشروع)
- [⚙️ التقنيات المستخدمة](#️-التقنيات-المستخدمة)
- [📱 لقطات الشاشة](#-لقطات-الشاشة)
- [📖 الوثائق](#-الوثائق)
- [🤝 المساهمة](#-المساهمة)
- [📞 التواصل](#-التواصل)

---

## 🌟 نظرة عامة

**SmartPOS** هو نظام نقاط بيع مفتوح المصدر متطور مصمم خصيصاً للشركات العربية. يجمع بين الأداء العالي والتقنيات الحديثة مع واجهة عربية كاملة.

### ✨ ما يميزنا

| الميزة | الوصف |
|--------|--------|
| 💬 **المحادثة الفورية** | أول نظام محادثة متكامل في أنظمة نقاط البيع العربية |
| 🔐 **الأمان المتقدم** | نظام بصمة الأجهزة وإدارة الوصول الذكي |
| 💾 **النسخ الاحتياطي** | حماية البيانات مع Google Drive والجدولة التلقائية |
| 🌍 **عربي أصيل** | واجهة عربية كاملة مع دعم RTL متقدم |
| ⚡ **أداء فائق** | معالجة ملايين السجلات بسلاسة |



## 🚀 البدء السريع

### ⚡ تشغيل فوري (أقل من دقيقة)

```bash
# 1. استنساخ المشروع
git clone https://github.com/Chiqwa50/SmartPosWeb.git
cd SmartPosWeb

# 2. تشغيل النظام بالكامل
./start_optimized_system.sh
```

### 🌐 الوصول للنظام

| الخدمة | الرابط | الوصف |
|---------|--------|--------|
| 🖥️ **النظام الرئيسي** | http://localhost:5175 | الواجهة الكاملة |
| 🔧 **API** | http://localhost:8002 | الخادم الخلفي |
| 📚 **الوثائق** | http://localhost:8002/docs | وثائق API تفاعلية |

### 🔧 التثبيت اليدوي

<details>
<summary>انقر لعرض خطوات التثبيت التفصيلية</summary>

#### الخادم الخلفي
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Linux/Mac
pip install -r requirements.txt
python main.py
```

#### الواجهة الأمامية
```bash
cd frontend
npm install --include=dev
npm run dev
```

#### 🐘 إعداد PostgreSQL (موصى به)
```bash
# تثبيت PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# إنشاء قاعدة البيانات
sudo -u postgres psql -c "CREATE DATABASE smartpos_db;"

# تحديث متغير البيئة
export DATABASE_URL="postgresql+psycopg2://postgres:password@localhost:5432/smartpos_db"

# تثبيت مكتبة PostgreSQL
cd backend
source venv/bin/activate
pip install psycopg2-binary
```

> 📖 **للمزيد**: راجع [دليل ترقية PostgreSQL](docs/guides/POSTGRESQL_MIGRATION_GUIDE.md)

</details>

## 🎯 الميزات الأساسية

### 🛒 نقطة البيع
- **واجهة سريعة** - إدخال المنتجات وحساب المجموع تلقائياً
- **قارئ الباركود** - مسح سريع للمنتجات
- **طباعة الفواتير** - طباعة حرارية مع تصدير PDF
- **إدارة المدفوعات** - نقد، بطاقة، تقسيط

### 📦 إدارة المخزون
- **إدارة المنتجات** - إضافة وتعديل المنتجات
- **تتبع الكميات** - مراقبة المخزون والتنبيهات
- **التصنيفات** - تنظيم المنتجات في فئات
- **التقارير** - تقارير المخزون والحركة

### 💬 المحادثة الفورية 🆕
- **رسائل فورية** - تواصل مباشر بين الفريق
- **حالات القراءة** - ✓ مرسل، ✓✓ مستلم، ✓✓ مقروء
- **معاينة الروابط** - عرض الروابط مع الصور تلقائياً
- **منتقي الإيموجي** - إضافة الإيموجي للرسائل

### 🔐 إدارة الأجهزة 🆕
- **نظام البصمة** - تعرف فريد على كل جهاز
- **إدارة الوصول** - موافقة أو حظر الأجهزة
- **مراقبة النشاط** - تتبع حالة الأجهزة (متصل/غير متصل)
- **سجل الوصول** - تسجيل جميع محاولات الدخول

### 💾 النسخ الاحتياطي 🆕
- **نسخ محلي** - نسخ تلقائية يومية لقاعدة البيانات
- **Google Drive** - رفع النسخ للسحابة تلقائياً
- **جدولة المهام** - تحديد مواعيد النسخ والتنظيف
- **استرداد البيانات** - استعادة النسخ عند الحاجة

### 🎨 التصميم والواجهة المتطورة
- 🌙 **الوضع المظلم/المضيء المحسن** - تبديل ذكي مع حفظ التفضيلات
- 📱 **تصميم متجاوب متقدم** - تكيف ذكي مع جميع أحجام الشاشات
- 🔄 **واجهة عربية متطورة** - دعم RTL كامل مع تحسينات الخط
- ⚡ **أداء فائق السرعة** - تحميل فوري مع تحسينات متقدمة
- 🎯 **تجربة مستخدم استثنائية** - تصميم بديهي مع إرشادات تفاعلية
- 🔔 **نظام التنبيهات الذكي** - تنبيهات متعددة المستويات مع أولويات
- 📊 **لوحة تحكم تفاعلية متقدمة** - إحصائيات ديناميكية مع تخصيص كامل
- 🎨 **تصميم موحد متطور** - نظام تصميم شامل مع مكونات قابلة للتخصيص
- 🖼️ **واجهة مرئية غنية** - رسوم متحركة وانتقالات سلسة
- 🎛️ **تحكم متقدم** - اختصارات لوحة المفاتيح ووضع اللمس

### 🔧 المميزات التقنية المتطورة
- 🔒 **أمان متعدد المستويات** - تشفير متقدم ومصادقة ثنائية
- 📊 **قاعدة بيانات متقدمة** - PostgreSQL مع تحسينات enterprise-grade
- 🔄 **تحديث فوري متقدم** - WebSocket مع تزامن في الوقت الفعلي
- 📱 **PWA متقدم** - تطبيق ويب تقدمي مع إمكانيات أصلية
- 🌐 **API RESTful شامل** - واجهات برمجية متقدمة مع GraphQL
- 🔍 **مراقبة الأداء الذكية** - نظام مراقبة شامل مع تحليلات متقدمة
- 📧 **إرسال التقارير الذكي** - إرسال تلقائي مع تحليل الأخطاء
- 🛠️ **إصلاح تلقائي متقدم** - حل المشاكل بالذكاء الاصطناعي
- 📈 **تحليلات متطورة** - رسوم بيانية متقدمة مع تنبؤات
- 🔌 **تكامل خارجي** - APIs للأنظمة الخارجية والمحاسبية
- 🚀 **أداء محسن** - تحسينات متقدمة للسرعة والاستجابة

## 🏗️ هيكلية المشروع

<div align="center">

### 📊 نظرة عامة على البنية

```mermaid
graph TD
    A[🚀 SmartPOS] --> B[🐍 Backend]
    A --> C[⚛️ Frontend]
    A --> D[📚 Documentation]

    B --> B1[Models]
    B --> B2[Routes]
    B --> B3[Services]

    C --> C1[Components]
    C --> C2[Pages]
    C --> C3[Stores]

    D --> D1[Features]
    D --> D2[Guides]
    D --> D3[Updates]
```

</div>

### 📁 الهيكلية التفصيلية

```
SmartPOS/
├── 🐍 backend/                    # الخادم الخلفي (FastAPI + Python)
│   ├── models/                   # نماذج البيانات
│   │   ├── user.py              # المستخدمون
│   │   ├── product.py           # المنتجات
│   │   ├── sale.py              # المبيعات
│   │   ├── chat_message.py      # المحادثة 🆕
│   │   └── device_security.py   # أمان الأجهزة 🆕
│   │
│   ├── routers/                 # مسارات API
│   │   ├── auth.py              # المصادقة
│   │   ├── sales.py             # المبيعات
│   │   ├── chat.py              # المحادثة 🆕
│   │   └── device_security.py   # الأجهزة 🆕
│   │
│   ├── services/                # الخدمات
│   │   ├── chat_websocket.py    # WebSocket 🆕
│   │   ├── google_drive.py      # Google Drive 🆕
│   │   └── scheduler.py         # المهام المجدولة 🆕
│   │
│   └── main.py                  # نقطة البداية
│
├── ⚛️ frontend/                   # الواجهة الأمامية (React + TypeScript)
│   ├── src/
│   │   ├── components/          # المكونات
│   │   │   ├── Chat/           # مكونات المحادثة 🆕
│   │   │   ├── DeviceManagement/ # إدارة الأجهزة 🆕
│   │   │   ├── POS/            # نقطة البيع
│   │   │   └── Reports/        # التقارير
│   │   │
│   │   ├── pages/              # الصفحات
│   │   │   ├── Dashboard.tsx   # لوحة التحكم
│   │   │   ├── Sales.tsx       # المبيعات
│   │   │   └── Settings.tsx    # الإعدادات
│   │   │
│   │   ├── services/           # خدمات API
│   │   │   ├── chatService.ts  # خدمة المحادثة 🆕
│   │   │   └── api.ts          # API العام
│   │   │
│   │   └── stores/             # إدارة الحالة (Zustand)
│   │       ├── authStore.ts    # المصادقة
│   │       └── chatStore.ts    # المحادثة 🆕
│   │
│   ├── package.json            # تبعيات المشروع
│   └── vite.config.ts          # إعدادات Vite
│
├── 📚 docs/                      # الوثائق الشاملة
│   ├── features/               # وثائق الميزات
│   │   ├── REAL_TIME_CHAT_SYSTEM.md 🆕
│   │   └── ENHANCED_DEVICE_MANAGEMENT.md 🆕
│   │
│   ├── guides/                 # أدلة الاستخدام
│   │   ├── INSTALLATION_GUIDE.md
│   │   └── TROUBLESHOOTING_GUIDE.md
│   │
│   └── updates/                # تحديثات النظام
│       └── LATEST_UPDATES_SUMMARY.md
│
├── 🔧 scripts/                   # سكريبتات التشغيل
│   ├── start_optimized_system.sh
│   └── test_data_streaming.py
│
├── .gitignore                   # ملفات Git المتجاهلة
├── LICENSE                      # رخصة المشروع
└── README.md                    # هذا الملف
```




## ⚙️ التقنيات المستخدمة

### 🎨 الواجهة الأمامية
- **React 18** - مكتبة واجهة المستخدم الحديثة
- **TypeScript** - لغة البرمجة المطورة والآمنة
- **Vite** - أداة البناء فائقة السرعة
- **TailwindCSS** - إطار عمل CSS مرن
- **Zustand** - إدارة الحالة البسيطة
- **ApexCharts** - الرسوم البيانية التفاعلية

### 🐍 الخادم الخلفي
- **FastAPI** - إطار عمل Python السريع
- **SQLAlchemy** - ORM متقدم لقاعدة البيانات
- **PostgreSQL** - قاعدة البيانات الرئيسية المتقدمة 🆕

- **WebSockets** - الاتصالات الفورية
- **APScheduler** - جدولة المهام مع PostgreSQL job store 🆕
- **Google APIs** - تكامل Google Drive

### 🆕 الميزات الجديدة
- **Socket.io** - المحادثة الفورية
- **emoji-picker-react** - منتقي الإيموجي
- **Google Drive API** - النسخ الاحتياطي السحابي
- **BeautifulSoup4** - معاينة الروابط







## 📱 لقطات الشاشة

> 🚧 **قريباً**: سيتم إضافة لقطات الشاشة للميزات الجديدة

### 🎯 الميزات المتاحة

| الميزة | الحالة | الوصف |
|--------|---------|--------|
| 🛒 **نقطة البيع** | ✅ متاح | واجهة سريعة مع قارئ باركود |
| 📦 **إدارة المخزون** | ✅ متاح | تتبع المنتجات والكميات |
| 💬 **المحادثة الفورية** | 🆕 جديد | رسائل فورية مع حالات القراءة |
| 🔐 **إدارة الأجهزة** | 🆕 جديد | نظام بصمة وأمان متقدم |
| 💾 **النسخ الاحتياطي** | 🆕 جديد | نسخ محلي + Google Drive |
| 📊 **التقارير** | ✅ متاح | رسوم بيانية تفاعلية |

## 📖 الوثائق

### 📚 الوثائق المتاحة
- **[دليل التثبيت](docs/guides/INSTALLATION_GUIDE.md)** - خطوات التثبيت التفصيلية
- **[نظام المحادثة](docs/features/REAL_TIME_CHAT_SYSTEM.md)** - دليل المحادثة الفورية
- **[إدارة الأجهزة](docs/features/ENHANCED_DEVICE_MANAGEMENT_SYSTEM.md)** - نظام الأمان
- **[النسخ الاحتياطي](docs/features/GOOGLE_DRIVE_FEATURE_UPDATE.md)** - Google Drive
- **[استكشاف الأخطاء](docs/guides/TROUBLESHOOTING_GUIDE.md)** - حل المشاكل الشائعة

### 🔧 التكوين السريع

```bash
# إعداد الخادم الخلفي
cd backend
cp .env.example .env
# عدل ملف .env حسب احتياجاتك

# إعداد الواجهة الأمامية
cd frontend
npm install
npm run dev
```

## 🤝 المساهمة

نرحب بمساهماتكم في تطوير SmartPOS!

### 🔧 كيفية المساهمة
1. **Fork** المشروع
2. إنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. تنفيذ التغييرات (`git commit -m 'Add amazing feature'`)
4. رفع التغييرات (`git push origin feature/amazing-feature`)
5. إنشاء **Pull Request**

### 🐛 الإبلاغ عن الأخطاء
- استخدم [GitHub Issues](https://github.com/Chiqwa50/SmartPosWeb/issues)
- قدم وصفاً مفصلاً للمشكلة
- أرفق لقطات شاشة إن أمكن

## 📞 التواصل

### 📧 معلومات الاتصال
- **البريد الإلكتروني**: <EMAIL>
- **GitHub**: [@Chiqwa50](https://github.com/Chiqwa50)
- **LinkedIn**: [Najib Gadamsi](https://linkedin.com/in/najib-gadamsi)
- **المستودع**: [SmartPosWeb](https://github.com/Chiqwa50/SmartPosWeb)

---

## 📊 حالة المشروع

### ✅ حالة التطوير

| المكون | الحالة | التبعيات | الاختبارات |
|--------|---------|-----------|-------------|
| 🐍 **Backend** | ✅ مستقر | 25 تبعية متوافقة | ✅ تمر جميع الاختبارات |
| ⚛️ **Frontend** | ✅ مستقر | 13 رئيسية + 18 تطوير | ✅ بناء ناجح |
| 📦 **Build** | ✅ ناجح | جميع الملفات محسنة | ✅ لا توجد أخطاء |
| 🧹 **النظافة** | ✅ نظيف | خالٍ من الملفات الزائدة | ✅ محسن بالكامل |

### 📈 إحصائيات المشروع

- **📁 حجم المشروع**: 32MB (بدون node_modules/venv)
- **🐍 ملفات Python**: 135 ملف
- **⚛️ ملفات TypeScript**: 72 ملف
- **📱 ملفات TSX**: 132 ملف
- **🔧 ملفات التكوين**: محسنة ومتطابقة

### 🔧 متطلبات النظام المحققة

- ✅ Python 3.8+ مع جميع التبعيات
- ✅ Node.js 16+ مع npm محدث
- ✅ PostgreSQL جاهز للاستخدام
- ✅ جميع الخدمات تعمل بشكل صحيح

---

<div align="center">

## 🎯 خلاصة المشروع

**SmartPOS** - نظام نقاط البيع الذكي الأكثر تطوراً في ليبيا والعالم العربي

### 🌟 ما يميزنا

| الميزة | الوصف |
|--------|--------|
| 💬 **المحادثة الفورية** | أول نظام محادثة متكامل في أنظمة نقاط البيع العربية |
| 🔐 **الأمان المتقدم** | نظام بصمة الأجهزة وإدارة الوصول الذكي |
| 💾 **النسخ الاحتياطي** | حماية البيانات مع Google Drive والجدولة التلقائية |
| 🌍 **عربي أصيل** | واجهة عربية كاملة مع دعم RTL متقدم |
| ⚡ **أداء فائق** | معالجة ملايين السجلات بسلاسة |

### 🚀 ابدأ الآن

[![GitHub](https://img.shields.io/badge/GitHub-SmartPosWeb-black?style=for-the-badge&logo=github)](https://github.com/Chiqwa50/SmartPosWeb)
[![License](https://img.shields.io/badge/License-MIT-green?style=for-the-badge)](LICENSE)
[![Arabic](https://img.shields.io/badge/Language-Arabic-red?style=for-the-badge)](README.md)

**صنع بـ ❤️ للمجتمع الليبي والعربي**

**جميع الحقوق محفوظة © 2025 SmartPOS**

**🌟 انضم إلى ثورة نقاط البيع الذكية! 🚀**

</div>

