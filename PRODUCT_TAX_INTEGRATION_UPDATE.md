# 🔗 تحديث ربط نظام الضرائب في صفحة إنشاء المنتج

## 📋 نظرة عامة

تم ربط مكونات إعدادات الضريبة في صفحة إنشاء المنتج بخدمة الضرائب الموجودة، مع تحديث هيكل الحقول ليكون:
- **الحقل الأول**: اختيار **نوع الضريبة** (مثل ضريبة القيمة المضافة، ضريبة الخدمات)
- **الحقل الثاني**: اختيار **قيمة الضريبة** المرتبطة بالنوع المختار (مثل 15%, 5%)

## 🎯 التحديثات المنجزة

### 1. ربط المخازن (Tax Stores Integration)

تم إضافة استيراد واستخدام المخازن التالية في `PricingInventorySection.tsx`:

```typescript
// Import tax stores
import useTaxTypeStore from '../../../stores/taxTypeStore';
import useTaxRateStore from '../../../stores/taxRateStore';
```

### 2. تحميل البيانات التلقائي

```typescript
// Load tax data on component mount
useEffect(() => {
  const loadTaxData = async () => {
    try {
      await fetchTaxTypes();
      await fetchTaxRates(); // Load all tax rates
    } catch (error) {
      console.error('خطأ في تحميل بيانات الضرائب:', error);
    }
  };

  loadTaxData();
}, [fetchTaxTypes, fetchTaxRates]);
```

### 3. إنتاج خيارات الضريبة الديناميكية المحدثة

تم تحديث هيكل الخيارات ليكون هرمياً:

```typescript
// Tax Type Options (نوع الضريبة)
const taxTypeOptions = taxTypes
  .filter(type => type.is_active)
  .map(type => ({
    value: type.id.toString(),
    label: type.name_ar || type.name
  }));

// Tax Rate Options (قيمة الضريبة) - مفلترة حسب النوع المختار
const taxRateOptions = formData.tax_type_id 
  ? taxRates
      .filter(rate => 
        rate.is_active && 
        rate.tax_type_id === parseInt(formData.tax_type_id) &&
        rate.applies_to !== 'services'
      )
      .map(rate => ({
        value: rate.id.toString(),
        label: `${rate.name} (${rate.rate_value}%)`
      }))
  : [];
```

### 4. النظام الهرمي للاختيار

تم تطبيق نظام اختيار هرمي يعمل كالتالي:

```typescript
// عند اختيار نوع الضريبة، يتم إعادة تعيين قيمة الضريبة
onChange={(value) => {
  updateFormData('tax_type_id', value ? parseInt(value) : null);
  // Reset tax rate when tax type changes
  updateFormData('tax_rate_id', null);
}}
```

### 5. حسابات الضريبة التفاعلية المحدثة

إضافة دوال حساب الضريبة المحدثة:

```typescript
// Calculate tax amount
const calculateTaxAmount = () => {
  if (formData.tax_rate_id && formData.price > 0) {
    const selectedTaxRate = taxRates.find(rate => rate.id === parseInt(formData.tax_rate_id));
    if (selectedTaxRate) {
      const baseAmount = formData.price - (formData.discount_type === 'fixed' 
        ? formData.discount_value 
        : formData.price * formData.discount_value / 100);
      return (baseAmount * selectedTaxRate.rate_value / 100).toFixed(2);
    }
  }
  return '0.00';
};
```

### 5. عرض تفاعلي للحسابات

إضافة عرض مباشر لحسابات الضريبة:

- **مبلغ الضريبة**: يُحسب بناءً على الضريبة المختارة
- **الإجمالي النهائي**: يختلف حسب نوع الضريبة (شاملة/غير شاملة)
- **حالات التحميل**: عرض spinner أثناء تحميل البيانات
- **حالات فارغة**: رسالة تنبيه عندما لا توجد ضرائب متاحة

## 🎨 واجهة المستخدم المحدثة

### إعدادات الضريبة الهرمية
- **نوع الضريبة**: اختيار من أنواع الضرائب المتاحة (ضريبة القيمة المضافة، ضريبة الخدمات، إلخ)
- **قيمة الضريبة**: قائمة مفلترة تعرض قيم الضريبة التابعة للنوع المختار فقط
- **مبلغ الضريبة**: عرض المبلغ المحسوب تلقائياً بناءً على القيمة المختارة
- **الإجمالي النهائي**: عرض السعر النهائي شامل الضريبة

### ميزات التفاعل
- **الاختيار المتسلسل**: عند تغيير نوع الضريبة، يتم مسح قيمة الضريبة وإعادة تحميل الخيارات
- **البحث**: إمكانية البحث في كلا الحقلين
- **التعطيل الذكي**: تعطيل حقل القيمة حتى يتم اختيار النوع أولاً

### حالات التحميل والخطأ المحدثة
- **أثناء التحميل**: عرض spinner مع رسالة "جاري تحميل بيانات الضرائب..."
- **لا توجد أنواع ضرائب**: رسالة صفراء توجه المستخدم لإضافة أنواع الضرائب أولاً
- **لا توجد قيم للنوع المختار**: رسالة زرقاء تخبر المستخدم بعدم وجود قيم للنوع المختار
- **حالات العرض التفاعلية**: 
  - "اختر نوع الضريبة أولاً..." عندما لم يتم اختيار النوع
  - "جاري التحميل..." أثناء تحميل البيانات
  - "لا توجد قيم ضريبية متاحة" عندما لا توجد قيم للنوع المختار

## 🔧 التحديثات التقنية

### تحديث هيكل حقول الضريبة

تم تغيير بنية حقول الضريبة في `CreateProduct.tsx`:

```typescript
// Before (الإصدار السابق)
tax_type: 'inclusive',
tax_id: null,

// After (الإصدار المحدث)
tax_type_id: null,    // معرف نوع الضريبة
tax_rate_id: null,    // معرف قيمة الضريبة
```

### تحسين دوال الحساب

- **calculateProfitMargin**: حساب هامش الربح
- **calculateTaxAmount**: حساب مبلغ الضريبة
- **calculateTotalPrice**: حساب السعر الإجمالي
- **دعم الضريبة الشاملة/غير الشاملة**: حسابات مختلفة حسب النوع

## 📊 الملفات المُحدثة

### 1. `/components/product/ProductForm/PricingInventorySection.tsx`
- إضافة استيراد مخازن الضرائب
- تحديث منطق عرض الخيارات
- إضافة دوال الحساب
- تحسين واجهة المستخدم

### 2. `/pages/CreateProduct.tsx`
- إضافة `tax_id` إلى formData الأولية

## 🎯 الميزات الجديدة

### ✅ ما تم إنجازه:
- [x] ربط مخازن الضرائب
- [x] تحميل البيانات التلقائي
- [x] عرض ديناميكي للضرائب
- [x] حسابات تفاعلية للضريبة
- [x] دعم الضريبة الشاملة/غير الشاملة
- [x] حالات التحميل والخطأ
- [x] واجهة مستخدم محسنة

### 🔄 التحسينات المستقبلية المقترحة:
- [ ] إضافة validation للضريبة المختارة
- [ ] دعم الضرائب المركبة (compound taxes)
- [ ] إضافة تواريخ فعالية الضريبة
- [ ] تكامل مع نظام التقارير لحساب الضرائب

## 🧪 الاختبار

### خطوات الاختبار:
1. انتقل إلى صفحة إنشاء منتج جديد
2. توجه إلى قسم "التسعير والضرائب"
3. تأكد من تحميل الضرائب المتاحة
4. اختر ضريبة وتأكد من حساب المبلغ
5. غير نوع الضريبة وتأكد من تحديث الحسابات

### حالات الاختبار:
- ✅ تحميل البيانات بنجاح
- ✅ عرض حالة التحميل
- ✅ عرض رسالة عدم وجود ضرائب
- ✅ حساب الضريبة الشاملة
- ✅ حساب الضريبة غير الشاملة
- ✅ تحديث الحسابات عند تغيير الضريبة

## 💡 استخدام النظام المحدث

### للمستخدمين:
1. **إعداد الضرائب**: انتقل أولاً إلى "إدارة الفهرس" → "أنواع الضرائب" لإعداد أنواع وقيم الضرائب
2. **إنشاء منتج**: 
   - في قسم "التسعير والضرائب"
   - **الخطوة 1**: اختر نوع الضريبة (مثل: ضريبة القيمة المضافة)
   - **الخطوة 2**: اختر قيمة الضريبة من القائمة المفلترة (مثل: 15%)
3. **مراجعة الحسابات**: شاهد المبالغ المحسوبة تلقائياً (مبلغ الضريبة والإجمالي النهائي)
4. **حفظ المنتج**: تأكد من صحة البيانات قبل الحفظ

### للمطورين:
- المكون يستخدم `useTaxTypeStore` و `useTaxRateStore` للبيانات
- الحسابات تتم محلياً في الواجهة للاستجابة السريعة
- يمكن توسيع النظام لدعم المزيد من أنواع الضرائب

---

**تاريخ التحديث**: ${new Date().toLocaleDateString('ar-SA')}  
**المطور**: Zencoder AI Assistant  
**الإصدار**: v1.0.0  