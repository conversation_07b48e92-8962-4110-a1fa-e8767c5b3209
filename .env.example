# إعدادات البيئة - SmartPOS

# نوع البيئة (development, production)
ENVIRONMENT=development

# إعدادات التطوير
DEBUG=true
NODE_ENV=development

# إعدادات الخادم للتطوير
DEV_BACKEND_HOST=*************
DEV_BACKEND_PORT=8002
DEV_FRONTEND_HOST=*************
DEV_FRONTEND_PORT=5173

# إعدادات الخادم للإنتاج
PROD_BACKEND_HOST=your-domain.com
PROD_BACKEND_PORT=8002
PROD_FRONTEND_HOST=your-domain.com
PROD_FRONTEND_PORT=80
PROD_USE_HTTPS=true

# إعدادات قاعدة البيانات
DATABASE_URL=sqlite:///./smartpos.db

# إعدادات الأمان
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# إعدادات إضافية
ALLOWED_HOSTS=localhost,127.0.0.1,*************
CORS_ORIGINS=http://localhost:5173,http://*************:5173
