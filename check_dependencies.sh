#!/bin/bash

# 🔍 سكريبت فحص التبعيات الشامل
# يتحقق من صحة وتطابق التبعيات في المشروع

echo "🔍 فحص التبعيات الشامل للمشروع..."
echo "=================================="

# فحص Backend Dependencies
echo ""
echo "🐍 فحص تبعيات Backend:"
echo "------------------------"

if [ -f "backend/requirements.txt" ] && [ -f "backend/pyproject.toml" ]; then
    echo "✅ ملفات التبعيات موجودة"
    
    # عد التبعيات
    req_count=$(grep -c "^[^#]" backend/requirements.txt)
    pyproject_count=$(grep -A 50 "dependencies = \[" backend/pyproject.toml | grep -B 50 "^]" | grep -c "\".*>=.*\"")
    
    echo "📦 عدد التبعيات في requirements.txt: $req_count"
    echo "📦 عدد التبعيات في pyproject.toml: $pyproject_count"
    
    if [ "$req_count" -eq "$pyproject_count" ]; then
        echo "✅ عدد التبعيات متطابق"
    else
        echo "⚠️ عدد التبعيات غير متطابق"
    fi
else
    echo "❌ ملفات التبعيات مفقودة"
fi

# فحص البيئة الافتراضية
if [ -d "backend/venv" ]; then
    echo "✅ البيئة الافتراضية موجودة"
    
    # فحص إذا كانت البيئة مفعلة
    if [[ "$VIRTUAL_ENV" == *"backend/venv"* ]]; then
        echo "✅ البيئة الافتراضية مفعلة"
        
        # فحص التبعيات المثبتة
        echo "🔍 فحص التبعيات المثبتة..."
        pip check > /dev/null 2>&1
        if [ $? -eq 0 ]; then
            echo "✅ جميع التبعيات متوافقة"
        else
            echo "⚠️ يوجد تعارض في التبعيات"
            pip check
        fi
    else
        echo "⚠️ البيئة الافتراضية غير مفعلة"
        echo "💡 لتفعيلها: source backend/venv/bin/activate"
    fi
else
    echo "❌ البيئة الافتراضية مفقودة"
    echo "💡 لإنشائها: cd backend && python -m venv venv"
fi

# فحص Frontend Dependencies
echo ""
echo "⚛️ فحص تبعيات Frontend:"
echo "------------------------"

if [ -f "frontend/package.json" ]; then
    echo "✅ package.json موجود"
    
    # عد التبعيات
    deps_count=$(jq '.dependencies | length' frontend/package.json 2>/dev/null || echo "0")
    dev_deps_count=$(jq '.devDependencies | length' frontend/package.json 2>/dev/null || echo "0")
    
    echo "📦 عدد dependencies: $deps_count"
    echo "📦 عدد devDependencies: $dev_deps_count"
    
    # فحص node_modules
    if [ -d "frontend/node_modules" ]; then
        echo "✅ node_modules موجود"
        
        # فحص package-lock.json
        if [ -f "frontend/package-lock.json" ]; then
            echo "✅ package-lock.json موجود"
        else
            echo "⚠️ package-lock.json مفقود"
        fi
    else
        echo "❌ node_modules مفقود"
        echo "💡 لتثبيت التبعيات: cd frontend && npm install"
    fi
else
    echo "❌ package.json مفقود"
fi

# فحص الملفات المكررة
echo ""
echo "🔍 فحص الملفات المكررة:"
echo "------------------------"

# فحص البيئات الافتراضية المكررة
venv_count=$(find . -name "venv" -type d | wc -l)
echo "📁 عدد مجلدات venv: $venv_count"

if [ "$venv_count" -gt 1 ]; then
    echo "⚠️ يوجد بيئات افتراضية مكررة:"
    find . -name "venv" -type d
fi

# فحص ملفات النسخ الاحتياطي
backup_count=$(find . -name "*.backup" -o -name "*.bak" | wc -l)
echo "📁 عدد ملفات النسخ الاحتياطي: $backup_count"

if [ "$backup_count" -gt 0 ]; then
    echo "⚠️ يوجد ملفات نسخ احتياطي:"
    find . -name "*.backup" -o -name "*.bak"
fi

# فحص ملفات __pycache__
pycache_count=$(find . -name "__pycache__" -type d | wc -l)
echo "📁 عدد مجلدات __pycache__: $pycache_count"

# الخلاصة
echo ""
echo "📋 خلاصة الفحص:"
echo "==============="

issues=0

if [ "$req_count" -ne "$pyproject_count" ]; then
    echo "❌ عدم تطابق تبعيات Backend"
    ((issues++))
fi

if [ ! -d "backend/venv" ]; then
    echo "❌ البيئة الافتراضية مفقودة"
    ((issues++))
fi

if [ ! -d "frontend/node_modules" ]; then
    echo "❌ node_modules مفقود"
    ((issues++))
fi

if [ "$venv_count" -gt 1 ]; then
    echo "❌ بيئات افتراضية مكررة"
    ((issues++))
fi

if [ "$backup_count" -gt 0 ]; then
    echo "⚠️ ملفات نسخ احتياطي غير ضرورية"
    ((issues++))
fi

if [ "$pycache_count" -gt 0 ]; then
    echo "⚠️ ملفات Python مترجمة"
    ((issues++))
fi

if [ "$issues" -eq 0 ]; then
    echo "✅ المشروع نظيف ولا يوجد مشاكل"
else
    echo "⚠️ تم العثور على $issues مشكلة"
    echo "💡 استخدم ./cleanup_project.sh للتنظيف"
fi
