#!/bin/bash

# نص إيقاف نظام SmartPOS مع تدفق البيانات
# Stop SmartPOS with Data Streaming System

echo "🛑 إيقاف نظام SmartPOS مع تدفق البيانات الكبيرة"
echo "=============================================="

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دالة طباعة ملونة
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# إيقاف الخادم الخلفي
stop_backend() {
    print_step "إيقاف الخادم الخلفي..."
    
    if [ -f "backend.pid" ]; then
        BACKEND_PID=$(cat backend.pid)
        if kill -0 $BACKEND_PID 2>/dev/null; then
            print_status "إيقاف العملية $BACKEND_PID..."
            kill $BACKEND_PID
            
            # انتظار الإيقاف الطبيعي
            sleep 3
            
            # فحص ما إذا كانت العملية ما زالت تعمل
            if kill -0 $BACKEND_PID 2>/dev/null; then
                print_warning "إيقاف قسري للعملية..."
                kill -9 $BACKEND_PID
            fi
            
            print_status "تم إيقاف الخادم الخلفي"
        else
            print_warning "الخادم الخلفي غير قيد التشغيل"
        fi
        rm backend.pid
    else
        print_warning "ملف PID للخادم الخلفي غير موجود"
    fi
    
    # إيقاف أي عمليات Python متبقية على المنفذ 8002
    PYTHON_PROCESSES=$(lsof -ti:8002 2>/dev/null)
    if [ ! -z "$PYTHON_PROCESSES" ]; then
        print_status "إيقاف العمليات المتبقية على المنفذ 8002..."
        echo $PYTHON_PROCESSES | xargs kill -9 2>/dev/null
    fi
}

# إيقاف الواجهة الأمامية
stop_frontend() {
    print_step "إيقاف الواجهة الأمامية..."
    
    if [ -f "frontend.pid" ]; then
        FRONTEND_PID=$(cat frontend.pid)
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            print_status "إيقاف العملية $FRONTEND_PID..."
            kill $FRONTEND_PID
            
            sleep 2
            
            if kill -0 $FRONTEND_PID 2>/dev/null; then
                print_warning "إيقاف قسري للعملية..."
                kill -9 $FRONTEND_PID
            fi
            
            print_status "تم إيقاف الواجهة الأمامية"
        else
            print_warning "الواجهة الأمامية غير قيد التشغيل"
        fi
        rm frontend.pid
    else
        print_warning "ملف PID للواجهة الأمامية غير موجود"
    fi
    
    # إيقاف أي عمليات Node.js متبقية على المنفذ 5173
    NODE_PROCESSES=$(lsof -ti:5173 2>/dev/null)
    if [ ! -z "$NODE_PROCESSES" ]; then
        print_status "إيقاف العمليات المتبقية على المنفذ 5173..."
        echo $NODE_PROCESSES | xargs kill -9 2>/dev/null
    fi
}

# تنظيف الملفات المؤقتة
cleanup_temp_files() {
    print_step "تنظيف الملفات المؤقتة..."
    
    # تنظيف ملفات التصدير القديمة
    if [ -d "backend/exports_cache" ]; then
        find backend/exports_cache -name "*.json" -mtime +1 -delete 2>/dev/null
        find backend/exports_cache -name "*.csv" -mtime +1 -delete 2>/dev/null
        find backend/exports_cache -name "*.gz" -mtime +1 -delete 2>/dev/null
        print_status "تم تنظيف ملفات التصدير القديمة"
    fi
    
    # تنظيف ملفات السجلات القديمة
    if [ -d "backend/logs" ]; then
        find backend/logs -name "*.log" -mtime +7 -delete 2>/dev/null
        print_status "تم تنظيف ملفات السجلات القديمة"
    fi
    
    # تنظيف ملفات PID
    rm -f *.pid 2>/dev/null
}

# فحص حالة النظام
check_system_status() {
    print_step "فحص حالة النظام..."
    
    # فحص المنفذ 8002
    if lsof -i:8002 >/dev/null 2>&1; then
        print_warning "المنفذ 8002 ما زال مستخدماً"
        lsof -i:8002
    else
        print_status "المنفذ 8002 متاح"
    fi
    
    # فحص المنفذ 5173
    if lsof -i:5173 >/dev/null 2>&1; then
        print_warning "المنفذ 5173 ما زال مستخدماً"
        lsof -i:5173
    else
        print_status "المنفذ 5173 متاح"
    fi
}

# إيقاف قسري لجميع العمليات المرتبطة
force_stop_all() {
    print_step "إيقاف قسري لجميع العمليات المرتبطة..."
    
    # إيقاف جميع عمليات Python التي تحتوي على main.py
    pkill -f "python.*main.py" 2>/dev/null
    
    # إيقاف جميع عمليات npm run dev
    pkill -f "npm run dev" 2>/dev/null
    
    # إيقاف جميع عمليات vite
    pkill -f "vite" 2>/dev/null
    
    print_status "تم الإيقاف القسري"
}

# عرض تقرير الإيقاف
show_stop_report() {
    echo ""
    echo "=============================================="
    print_status "🏁 تم إيقاف نظام SmartPOS بنجاح!"
    echo "=============================================="
    echo ""
    echo "📋 تقرير الإيقاف:"
    echo "   ✅ تم إيقاف الخادم الخلفي"
    echo "   ✅ تم إيقاف الواجهة الأمامية"
    echo "   ✅ تم تنظيف الملفات المؤقتة"
    echo "   ✅ تم تحرير المنافذ"
    echo ""
    echo "🔄 لإعادة التشغيل:"
    echo "   ./start_with_streaming.sh"
    echo ""
}

# الدالة الرئيسية
main() {
    # فحص المعاملات
    if [ "$1" = "--force" ] || [ "$1" = "-f" ]; then
        print_warning "تم تفعيل الإيقاف القسري"
        force_stop_all
    fi
    
    stop_backend
    stop_frontend
    cleanup_temp_files
    check_system_status
    show_stop_report
}

# تشغيل الدالة الرئيسية
main "$@"
