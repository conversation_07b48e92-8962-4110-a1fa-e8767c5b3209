
# تحليل خطأ react-apexcharts في رياكت + فيت + تايب سكربت

## وصف المشكلة
أثناء ربط المخطط بإعدادات تنسيق **التاريخ والوقت** التي تتضمن:
- تنسيقات **عربية**
- **تاريخ هجري**
- **تاريخ إنجليزي**

ظهر الخطأ التالي:

```
Error: <path> attribute d: Expected number, "…6126680000002125C 20.85504840353…"
```

---

## التحليل الفني للخطأ

هذا الخطأ ناتج من مكتبة `react-apexcharts` عند محاولة رسم المخطط باستخدام عناصر SVG.  
خاصية `d` داخل الوسم `<path>` تتطلب أرقامًا منسقة بدقة، وعندما تحتوي على قيمة غير رقمية أو غير متوافقة (مثل تاريخ عربي أو هجري كنص)، فإنها تُفشل عملية الرسم.

---

## السبب المحتمل

القيم المستخدمة في `x` لمحور التواريخ تحتوي على تواريخ **منسقة نصيًا** (عربي أو هجري)،  
بينما يتوقع `ApexCharts` إما:
- رقم timestamp
- أو كائن `Date`
- أو سلسلة بصيغة ISO (مثل `"2024-07-15"`)

---

## كيفية حل المشكلة

### ✅ تأكد أن البيانات بالشكل الصحيح:
```ts
const series = [
  {
    name: "المبيعات",
    data: [
      { x: new Date("2024-07-01").getTime(), y: 120 },
      { x: new Date("2024-07-02").getTime(), y: 150 }
    ]
  }
];
```

### ✅ التنسيق العربي/الهجري فقط في العرض:
```ts
const options = {
  xaxis: {
    type: "datetime",
    labels: {
      formatter: (value: string) => {
        return new Date(parseInt(value)).toLocaleDateString("ar-EG");
      }
    }
  }
};
```

### ⛔ لا تفعل:
- لا تمرر تواريخ كنصوص عربية مثل "١٥ يوليو ٢٠٢٤"
- لا تستخدم تاريخ هجري كقيمة `x` في `series` مباشرة

---

## مكتبات مفيدة
- [`moment-hijri`](https://www.npmjs.com/package/moment-hijri)
- [`@arafato/hijri-date`](https://www.npmjs.com/package/@arafato/hijri-date)

