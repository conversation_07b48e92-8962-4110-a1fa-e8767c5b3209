
# 🏗️ مخطط عمل نظام SmartPOS أونلاين + أوفلاين

هذا المستند يشرح للمطورين الطريقة الكاملة لجعل تطبيق **SmartPOS** يعمل في وضعي **أوفلاين** و **أونلاين** بدون فقدان بيانات.

---

## 🎯 الهدف
- ضمان استمرار عمل نقاط البيع في الفروع حتى عند انقطاع الإنترنت.
- مزامنة البيانات تلقائياً مع الخادم المركزي عند عودة الاتصال.
- تأمين الاتصال ومنع تضارب البيانات.

---

## 1️⃣ مكونات النظام

### 📌 قاعدة بيانات محلية في كل فرع
- نوع: **PostgreSQL** (أو SQLite للأجهزة الضعيفة).
- تخزن بيانات المبيعات والمخزون محلياً.
- تعمل بشكل مستقل تماماً عند انقطاع الإنترنت.

### 📌 الخادم المركزي (Cloud / VPS)
- نوع: **FastAPI + PostgreSQL**.
- يخزن **النسخة الرئيسية** من جميع بيانات الفروع.
- يوفر **API** للمزامنة والتكامل مع الأنظمة الأخرى.

### 📌 خدمة المزامنة (Sync Service)
- مهمة: إرسال واستقبال التحديثات بين الفروع والخادم المركزي.
- مبنية على **APScheduler** أو **Celery**.
- تدعم:
  - **Push**: إرسال العمليات الجديدة من الفرع إلى السيرفر.
  - **Pull**: جلب أي تحديثات من السيرفر إلى الفرع.

---

## 2️⃣ مخطط البنية المعمارية

```mermaid
graph TD
    subgraph فرع1
    POS1[💻 SmartPOS - فرع 1]
    DB1[(🗄️ قاعدة بيانات محلية)]
    POS1 --> DB1
    end

    subgraph فرع2
    POS2[💻 SmartPOS - فرع 2]
    DB2[(🗄️ قاعدة بيانات محلية)]
    POS2 --> DB2
    end

    subgraph Cloud
    API[🌐 خادم مركزي - FastAPI]
    MainDB[(🗄️ قاعدة بيانات مركزية)]
    API --> MainDB
    end

    Sync1[🔄 خدمة مزامنة] --> API
    Sync2[🔄 خدمة مزامنة] --> API

    DB1 <--> Sync1
    DB2 <--> Sync2
```

---

## 3️⃣ تدفق العمل

### 📥 حالة **الأوفلاين**
1. المستخدم يدخل عمليات البيع والمخزون في النظام.
2. البيانات تحفظ في القاعدة المحلية مباشرة.
3. النظام لا يحتاج إنترنت أثناء هذه العملية.

### 📤 حالة **الأونلاين**
1. خدمة المزامنة تتحقق من وجود إنترنت.
2. إذا متصل:
   - ترسل جميع العمليات الجديدة إلى الخادم المركزي (**Push**).
   - تجلب أي تحديثات جديدة من الخادم المركزي (**Pull**).
3. تحديث القاعدة المحلية والنسخة السحابية.

---

## 4️⃣ معالجة تضارب البيانات
- يتم حفظ **Timestamp** و **ID فريد** لكل عملية.
- عند المزامنة:
  - إذا تعارضت البيانات، يتم اعتماد **آخر تعديل** (Last Write Wins).
  - يمكن إضافة واجهة مراجعة للمشرف قبل دمج التعديلات.

---

## 5️⃣ أمان الاتصال
- جميع الاتصالات بين الفروع والسيرفر عبر **HTTPS**.
- تفعيل **رموز JWT** للمصادقة.
- ربط كل جهاز بـ **بصمة فريدة** (Device Fingerprint) لمنع الدخول غير المصرح.

---

## 6️⃣ خطة التنفيذ للمطورين

### 📌 الخطوة 1: إعداد القاعدة المحلية
- إضافة دعم اتصال ثنائي: Local DB + Remote DB.
- عند بدء النظام، يحدد وضع العمل (أوفلاين / أونلاين).

### 📌 الخطوة 2: تطوير API المزامنة
- **Endpoint** لإرسال البيانات من الفرع إلى الخادم المركزي.
- **Endpoint** لجلب التحديثات الجديدة من الخادم المركزي.

### 📌 الخطوة 3: جدولة المزامنة
- استخدام **APScheduler** أو **Celery** للمزامنة كل 1-5 دقائق.
- إذا لا يوجد إنترنت → إعادة المحاولة تلقائياً.

### 📌 الخطوة 4: إضافة سجل المزامنة
- حفظ آخر وقت مزامنة لكل جدول.
- إرسال فقط البيانات التي تغيرت.

### 📌 الخطوة 5: اختبارات التحمل
- محاكاة انقطاع الإنترنت أثناء البيع.
- التأكد من استئناف المزامنة بدون فقد بيانات.

---

## 7️⃣ ملاحظات هامة
- يجب ضغط البيانات قبل الإرسال لتسريع المزامنة.
- دعم **WebSockets** للتحديثات الفورية عندما يكون الإنترنت متاح.
- استخدام **UUID** للمعاملات بدل الأرقام التلقائية لتجنب التعارض بين الفروع.

---

## 📌 مثال لجدول المزامنة
| العملية | المصدر | الوجهة | الحالة |
|---------|--------|--------|--------|
| إضافة فاتورة | فرع 1 | سيرفر مركزي | ✅ تمت |
| تعديل منتج | سيرفر مركزي | فرع 2 | ✅ تمت |
| بيع جديد | فرع 2 | سيرفر مركزي | ⏳ انتظار الإنترنت |

---

## ✅ النتيجة
باستخدام هذه البنية، سيعمل **SmartPOS** بكفاءة في بيئة متقطعة الإنترنت مع ضمان تزامن البيانات عند توفر الاتصال.
