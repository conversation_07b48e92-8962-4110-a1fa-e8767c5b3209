# 📦 خدمة إدارة الصور في منصة نقاط البيع الذكية

**التاريخ:** 2025-07-23  
**الهدف:** تصميم خدمة مستقلة لإدارة صور المنتجات والفئات داخل مشروع نقاط بيع ذكي يعمل أوفلاين باستخدام FastAPI وReact.

---

## ✅ ملخص الخدمة

- **نوع الخدمة:** خدمة إدارة الصور (رفع، حذف، تخزين)
- **مكان الخدمة:** داخل المشروع الرئيسي كخدمة مستقلة (قابلة للفصل لاحقًا)
- **مكان تخزين الصور:** في نظام الملفات المحلي (وليس في قاعدة البيانات)
- **مسار التخزين:** `static/uploads/{products|categories}`
- **مبدأ التخزين:** حفظ الصور محليًا وتخزين المسار في قاعدة البيانات
- **لغة التنفيذ:** Python + FastAPI
- **توسعة مستقبلية:** إمكانية التحويل إلى Google Drive أو S3 لاحقًا
- توليد صور مصغرة (Thumbnails).

---

## 🧱 هيكل المشروع المقترح

```
backend/
├── main.py
├── database/
├── models/
├── routes/
│   ├── __init__.py
│   └── images.py
├── services/
│   ├── __init__.py
│   └── image_service.py
├── static/
│   └── uploads/
│       ├── products/
│       └── categories/
```

---

## ⚙️ 1. خدمة الحفظ والمسح (services/image_service.py)

```python
import os
import uuid
from fastapi import UploadFile
from shutil import copyfileobj

BASE_DIR = "static/uploads"

def save_image(file: UploadFile, folder: str) -> str:
    ext = file.filename.split('.')[-1]
    filename = f"{uuid.uuid4().hex}.{ext}"
    upload_path = os.path.join(BASE_DIR, folder)
    os.makedirs(upload_path, exist_ok=True)
    file_path = os.path.join(upload_path, filename)

    with open(file_path, "wb") as buffer:
        copyfileobj(file.file, buffer)

    return f"{folder}/{filename}"

def delete_image(path: str) -> bool:
    file_path = os.path.join(BASE_DIR, path)
    if os.path.exists(file_path):
        os.remove(file_path)
        return True
    return False
```

---

## 🌐 2. مسارات API (routes/images.py)

```python
from fastapi import APIRouter, UploadFile, File
from services.image_service import save_image, delete_image

router = APIRouter(prefix="/images", tags=["Images"])

@router.post("/upload/{folder}")
async def upload_image(folder: str, file: UploadFile = File(...)):
    path = save_image(file, folder)
    return {"image_path": path}

@router.delete("/")
def remove_image(path: str):
    deleted = delete_image(path)
    return {"deleted": deleted}
```

---

## 🔧 3. تفعيل Static Files (main.py)

```python
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from routes import images

app = FastAPI()

app.mount("/static", StaticFiles(directory="static"), name="static")
app.include_router(images.router)
```

---

## 🖼️ 4. في الواجهة (React)

### رفع صورة:
```tsx
const uploadImage = async (file: File, folder: 'products' | 'categories') => {
  const formData = new FormData();
  formData.append("file", file);

  const res = await fetch(`http://localhost:8000/images/upload/${folder}`, {
    method: "POST",
    body: formData,
  });

  const data = await res.json();
  return data.image_path;
};
```

### عرض الصورة:
```tsx
<img src={`http://localhost:8000/static/${product.image_path}`} alt="Product" />
```

---

## 📌 ملاحظات مهمة

| الميزة | التوضيح |
|--------|---------|
| ✅ أداء عالي | لأن الصور خارج قاعدة البيانات |
| ✅ قابل للتوسع | يمكنك نقل الصور لاحقًا إلى Google Drive أو S3 بسهولة |
| ✅ تنظيم | مجلدات حسب النوع (products, categories) |
| ✅ UUID | لتفادي تكرار الأسماء |
| ✅ حماية | يمكن لاحقًا إضافة تحقق من حجم ونوع الصورة |

---

## 📈 مستقبلًا (توسعة)

- دعم Google Drive أو Amazon S3.
- ضغط الصور قبل التخزين.
- واجهة منفصلة مستقلة (Microservice).
- نظام صلاحيات لتحكم بالوصول للصور.

