
# 🤖 SmartPOS - تكامل وكيل ذكاء اصطناعي داخلي (AI Agent)

هذا الدليل يشرح خطوة بخطوة كيفية بناء وكيل ذكاء اصطناعي محلي ضمن مشروعك SmartPOS باستخدام FastAPI وReact، مع نماذج LLM تعمل عبر Ollama مثل `Mistral` أو `Nous Her<PERSON>`.

---

## 📦 المتطلبات الأساسية

- Python 3.10+
- FastAPI
- React 18 + Vite
- Node.js
- PostgreSQL
- نموذج LLM محلي عبر Ollama

---

## 🧠 النماذج المقترحة للوكيل

| النموذج              | الحجم     | المزايا                                      | مناسب لـ                          |
|---------------------|-----------|---------------------------------------------|-----------------------------------|
| **Mistral 7B**       | 4–6GB     | أداء ممتاز، فهم جيد، سريع                    | تقارير مالية وتحليلية ذكية       |
| **LLaMA 3 (8B/13B)** | كبير      | دقة عالية، فهم عميق                         | مهام تحليل معقدة/متقدمة          |
| **Phi-3 Small**      | صغير      | استجابة سريعة، مثالي للمهام القصيرة         | مساعدات داخلية، أوامر خفيفة     |
| **Nous Hermes 2**    | متوسط     | مخصص للتعليمات، تقارير واضحة                | تقارير جاهزة/محادثات ذكاء       |

---

## ⚙️ 1. تثبيت Ollama وتشغيل النموذج

```bash
# تثبيت Ollama
curl -fsSL https://ollama.com/install.sh | sh

# تشغيل نموذج Mistral
ollama run mistral
```

> 💡 يمكن استبدال `mistral` بـ `llama3`, `phi3`, `nous-hermes` حسب الحاجة.

---

## 🧠 2. إنشاء وحدة الذكاء الاصطناعي في FastAPI

```python
# backend/agent/ai_agent.py

import subprocess

def run_ai_agent(prompt: str) -> str:
    result = subprocess.run(
        ['ollama', 'run', 'mistral'],  # استبدل باسم النموذج إذا لزم
        input=prompt.encode('utf-8'),
        capture_output=True
    )
    return result.stdout.decode('utf-8')
```

---

## 🧾 3. نقطة نهاية API لإنشاء تقرير ذكي

```python
# backend/routers/ai_reports.py

from fastapi import APIRouter
from backend.agent.ai_agent import run_ai_agent
from backend.services.current_period_service import get_sales_data

router = APIRouter()

@router.get("/intelligent-report")
def generate_ai_report():
    data = get_sales_data()
    prompt = (
        "البيانات التالية تمثل المبيعات لهذا الشهر:\n"
        f"{data}\n"
        "الرجاء تحليل الأداء المالي، استخراج نقاط القوة والضعف، وكتابة تقرير شامل كما يفعل المحاسب."
    )
    return {"report": run_ai_agent(prompt)}
```

---

## 💻 4. واجهة React لعرض التقرير الذكي

```tsx
// frontend/src/pages/Reports.tsx

import { useEffect, useState } from 'react';
import { get } from '../services/api';

export default function Reports() {
  const [report, setReport] = useState('');

  useEffect(() => {
    get('/intelligent-report').then(res => setReport(res.data.report));
  }, []);

  return (
    <div className="p-6">
      <h2 className="text-xl font-bold mb-4">🧠 التقرير الذكي</h2>
      <pre className="bg-gray-100 p-4 rounded whitespace-pre-wrap">
        {report}
      </pre>
    </div>
  );
}
```

---

## 🔎 5. وظائف ممكنة للوكيل الذكي

| الوظيفة                         | الوصف                                                              |
|--------------------------------|---------------------------------------------------------------------|
| 📊 تحليل الأداء المالي         | تقييم الأرباح والمصروفات ونقاط القوة/الضعف.                         |
| 🔍 اكتشاف الشذوذ               | تحديد بيانات غير منطقية أو غير اعتيادية.                            |
| 🕒 التوقعات المستقبلية         | التنبؤ بالمبيعات أو الديون باستخدام LLM.                            |
| 💡 تقديم التوصيات              | اقتراح طرق لتحسين الأداء وزيادة الربحية.                            |
| 📄 إنشاء تقارير قابلة للطباعة | يمكن تصديرها عبر PDF (WeasyPrint أو pdfkit).                         |

---

## 📤 تصدير التقرير إلى PDF (اختياري)

```bash
pip install weasyprint
```

```python
# backend/utils/export_pdf.py
from weasyprint import HTML

def export_report_to_pdf(html_content: str, output_path: str):
    HTML(string=html_content).write_pdf(output_path)
```

---

## ✅ الخطوات القادمة

- [ ] ربط تقارير مختلفة (ديون، مبيعات، مخزون)
- [ ] إضافة واجهة دردشة تفاعلية للوكيل
- [ ] دعم تعدد اللغات (عربي / إنجليزي)
- [ ] جدولة التقارير تلقائيًا (CRON أو APScheduler)

---

> 🧩 هل تحتاج أيضًا لتوليد التقرير الأول (مثل تقرير مبيعات حقيقي) باستخدام هذا النظام؟ فقط أخبرني وسأقوم بتوليده فورًا حسب صيغة واقعية.
