---
type: "always_apply"
---

# 📋 **AI Agent Guidelines for SmartPOS System**

>  **⚠️ Mandatory**: The AI agent must strictly follow these guidelines to ensure efficiency and stability in the project.

## 🎯 **Core Tasks for the AI Agent**

### 1. **Codebase Search**:

-  Before providing any advice or making decisions, the agent **must search the codebase** using `codebase-retrieval` to ensure that similar solutions already exist.
-  The search should be conducted in relevant directories such as:
   -  `services/`
   -  `components/`
   -  `utils/`
   -  `models/`
   -  `types/`

   **Search should be the first step** in any development or modification task.

### 2. **Check for Existing Solutions**:

-  If a similar function or service exists, the agent must **improve the current solution** rather than creating a new one.
-  If no similar solution is found, the agent should **suggest** creating a new solution following OOP principles.

### 3. **Object-Oriented Programming (OOP)**:

-  The agent must **create Classes** only, and avoid using **separate Functions**.
-  **Encapsulation**: Hide internal details in objects.
-  Each class should follow the **Single Responsibility Principle**, meaning each class should have only one job or responsibility.

### 4. **Error Handling**:

-  In case an error is discovered in the code or functionality, the agent **must handle errors** using `try-catch` blocks.
-  The agent must **log errors clearly** in the `console` with detailed messages in Arabic.
-  Error messages must be clear and informative for developers, providing sufficient detail about the issue.

### 5. **Code Testing Before Deployment**:

-  The agent **must test the code** in a local environment before deploying it to production.
-  The agent should ensure that all units work as expected and that nothing breaks when integrating new features.

### 6. **Implementing Synchronous Programming**:

-  The AI agent should ensure that any data-processing tasks are **handled synchronously** or with proper concurrency control (async/await) to prevent race conditions.

### 7. **Use of Approved Icons and Colors**:

-  The agent must always **use approved icons** from `react-icons/fi` for UI components.
-  It must also ensure that all design elements (like icons and colors) **adhere to the approved theme** to maintain consistency in the system's UI.

### 8. **Consistent Use of Date & Time Services**:

-  The agent should **always use the existing `dateTimeService`** for handling date and time, avoiding the creation of custom date-time solutions unless absolutely necessary.



## 🚫 **Prohibited Actions for the AI Agent**:

```
❌ Creating new functions without checking for existing ones first.
❌ Modifying `package.json` manually.
❌ Using `eval()` or string evaluation.
❌ Creating duplicate config files in different directories.
❌ Using `localhost` for remote devices.
❌ Ignoring OOP principles.
❌ Creating `backups` folders at the root.
❌ Using Canvas or Print Window for PDF export.
❌ Not implementing a smart cache system for data.
❌ Applying advanced fingerprinting on the main server.
❌ Using colors or icons that differ from the approved design system.
❌ Creating new date/time services instead of using the existing ones.
❌ Not separating services into distinct files.
❌ Ignoring the approved project structure.
❌ Not following the approved color and design scheme.
```

## ✅ **Always Required Actions for the AI Agent**:

```
✅ Using Classes instead of separate Functions.
✅ Comprehensive error handling with `try-catch`.
✅ Checking existing systems before creating new ones.
✅ Using network IPs for remote devices.
✅ Keeping `config` files only in `backend/config`.
✅ Using unified identifiers for devices.
✅ Testing the system after any significant change.
✅ Using `npm install --include=dev` for dev dependencies issues.
✅ Integrating the existing date and time services in all PDF services.
✅ Handling data asynchronously in the background.
✅ Linking existing services instead of creating new solutions.
✅ Following the unified system design for new features.
✅ Using approved icons from `react-icons/fi`.
✅ Using existing date and time services (`dateTimeService`).
✅ Separating each service into its own file under `services/`.
✅ Following the approved project structure.
✅ Applying OOP principles in all services.
✅ Using the approved color scheme throughout the application.
✅ Creating reusable UI components.
✅ Documenting all new services and components.
```

## 📝 **Documentation After Each Task**:

1. **Update** `SYSTEM_MEMORY.md` with new guidelines or rules.
2. **Create detailed documentation** in the `docs/` folder.
3. **Document lessons learned**.
4. **Update usage examples**.
5. **Document any new icons or colors used**.
6. **Document any new services with usage examples**.

**Last Update**: July 2025 - Added unified design rules and new guidelines.

**For detailed documentation**: Refer to the `docs/` folder. **For design and colors**: Refer to `docs/design/`.

>  **🤖 For the AI Agent**: This document must be fully read and followed before executing any tasks in the project.  Adhering to these guidelines is essential for ensuring system consistency and stability.