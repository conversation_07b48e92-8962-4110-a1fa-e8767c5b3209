# مرجع سريع - القائمة الجانبية المحسنة

## التثبيت السريع

```bash
npm install react-custom-scrollbars-2
```

## الاستيراد المطلوب

```typescript
import { Scrollbars } from 'react-custom-scrollbars-2';
```

## الملفات المتأثرة

1. `frontend/src/components/Sidebar.tsx` ✅ محدث
2. `frontend/src/components/Topbar.tsx` ✅ محدث  
3. `package.json` ✅ مكتبة مضافة

## الميزات الجديدة

### ✅ شريط تمرير مخصص
- تصميم متسق مع التطبيق
- دعم الوضع المظلم/الفاتح
- إخفاء تلقائي

### ✅ زر تصغير داخلي
- زر في رأس القائمة للشاشات الكبيرة
- زر في الشريط العلوي للتحكم العام

### ✅ وضع مصغر محسن
- عرض الأيقونات فقط
- tooltips عند المرور
- قوائم فرعية منبثقة

## الاستخدام

### تبديل القائمة
```typescript
const { toggleSidebar } = useSidebarStore();

// في الزر
<button onClick={toggleSidebar}>
  تبديل القائمة
</button>
```

### شريط التمرير المخصص
```typescript
<Scrollbars
  style={{ height: '100%' }}
  renderThumbVertical={renderScrollbarThumb}
  renderTrackVertical={renderScrollbarTrack}
  autoHide
  autoHideTimeout={1000}
  autoHideDuration={200}
  thumbMinSize={30}
  universal={true}
>
  {/* المحتوى */}
</Scrollbars>
```

## CSS Classes المهمة

```css
/* إخفاء شريط التمرير الافتراضي */
.scrollbar-hide

/* انتقالات سلسة */
.transition-all.duration-300.ease-in-out

/* تحكم في العرض */
.lg:w-64  /* مفتوح */
.lg:w-16  /* مصغر */

/* تحكم في الظهور */
.translate-x-0      /* ظاهر */
.translate-x-full   /* مخفي */
```

## حالات الاستخدام

### الشاشات الكبيرة (Desktop)
- القائمة ظاهرة دائماً
- تتبدل بين الوضع المفتوح (64) والمصغر (16)
- شريط تمرير مخصص في الوضع المفتوح

### الشاشات المحمولة (Mobile)
- القائمة مخفية افتراضياً
- تظهر كـ overlay عند الفتح
- زر إغلاق في رأس القائمة

## استكشاف الأخطاء

### القائمة لا تظهر
```typescript
// تأكد من استيراد toggleSidebar
const { toggleSidebar } = useSidebarStore();
```

### شريط التمرير لا يعمل
```typescript
// تأكد من تعيين الارتفاع
style={{ height: '100%' }}
```

### المحتوى مخفي
```typescript
// تأكد من منطق العرض الصحيح
{isOpen ? (
  <Scrollbars>...</Scrollbars>
) : (
  <div>...</div>
)}
```

## الحالة في المتجر

```typescript
interface SidebarState {
  isOpen: boolean;           // حالة القائمة (مفتوح/مصغر)
  isMobileMenuOpen: boolean; // حالة القائمة المحمولة
  // ...
}

// الحالة الافتراضية
isOpen: true,              // مفتوح افتراضياً
isMobileMenuOpen: false,   // مغلق افتراضياً
```

## الأزرار والتحكم

### زر الشريط العلوي
```typescript
// في Topbar.tsx
<button onClick={toggleSidebar}>
  <FiMenu />
</button>
```

### زر رأس القائمة
```typescript
// في Sidebar.tsx
{isOpen && (
  <button onClick={toggleSidebar}>
    <FiChevronRight />
  </button>
)}
```

## نصائح للتطوير

1. **استخدم المتجر**: دائماً استخدم `useSidebarStore()` للحالة
2. **اختبر الأحجام**: اختبر على شاشات مختلفة
3. **تأكد من الاستيراد**: لا تنس استيراد `toggleSidebar`
4. **راقب الأداء**: شريط التمرير محسن لكن راقب الأداء
5. **اتبع النمط**: استخدم نفس النمط للمكونات الجديدة

## الدعم

- ✅ Chrome/Chromium
- ✅ Firefox  
- ✅ Safari
- ✅ Edge
- ✅ Mobile browsers
- ✅ RTL languages
- ✅ Dark/Light mode
