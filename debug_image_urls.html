<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار URLs الصور</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .url-test {
            margin: 10px 0;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 3px;
        }
        .url-link {
            color: #0066cc;
            text-decoration: none;
            font-family: monospace;
            font-size: 14px;
        }
        .url-link:hover {
            text-decoration: underline;
        }
        img {
            max-width: 200px;
            max-height: 200px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status {
            font-weight: bold;
            margin-left: 10px;
        }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار URLs الصور - SmartPOS</h1>
        
        <div class="test-section">
            <h3>📸 الصورة الأصلية</h3>
            <div class="url-test">
                <div>URL: <a href="http://localhost:8002/static/uploads/general/general_20250725_011248_432fee6a_d4722dd0.jpg" 
                           target="_blank" class="url-link">
                    /static/uploads/general/general_20250725_011248_432fee6a_d4722dd0.jpg
                </a></div>
                <img src="http://localhost:8002/static/uploads/general/general_20250725_011248_432fee6a_d4722dd0.jpg" 
                     alt="الصورة الأصلية"
                     onload="this.nextElementSibling.innerHTML='<span class=status success>✅ تم التحميل بنجاح</span>'"
                     onerror="this.nextElementSibling.innerHTML='<span class=status error>❌ فشل التحميل</span>'">
                <div></div>
            </div>
        </div>

        <div class="test-section">
            <h3>🖼️ الصور المصغرة</h3>
            
            <div class="url-test">
                <h4>صغير (150x150)</h4>
                <div>URL: <a href="http://localhost:8002/static/uploads/general/thumbnails/small/general_20250725_011248_432fee6a_d4722dd0.jpg" 
                           target="_blank" class="url-link">
                    /static/uploads/general/thumbnails/small/general_20250725_011248_432fee6a_d4722dd0.jpg
                </a></div>
                <img src="http://localhost:8002/static/uploads/general/thumbnails/small/general_20250725_011248_432fee6a_d4722dd0.jpg" 
                     alt="صورة مصغرة صغيرة"
                     onload="this.nextElementSibling.innerHTML='<span class=status success>✅ تم التحميل بنجاح</span>'"
                     onerror="this.nextElementSibling.innerHTML='<span class=status error>❌ فشل التحميل</span>'">
                <div></div>
            </div>

            <div class="url-test">
                <h4>متوسط (300x300)</h4>
                <div>URL: <a href="http://localhost:8002/static/uploads/general/thumbnails/medium/general_20250725_011248_432fee6a_d4722dd0.jpg" 
                           target="_blank" class="url-link">
                    /static/uploads/general/thumbnails/medium/general_20250725_011248_432fee6a_d4722dd0.jpg
                </a></div>
                <img src="http://localhost:8002/static/uploads/general/thumbnails/medium/general_20250725_011248_432fee6a_d4722dd0.jpg" 
                     alt="صورة مصغرة متوسطة"
                     onload="this.nextElementSibling.innerHTML='<span class=status success>✅ تم التحميل بنجاح</span>'"
                     onerror="this.nextElementSibling.innerHTML='<span class=status error>❌ فشل التحميل</span>'">
                <div></div>
            </div>

            <div class="url-test">
                <h4>كبير (600x600)</h4>
                <div>URL: <a href="http://localhost:8002/static/uploads/general/thumbnails/large/general_20250725_011248_432fee6a_d4722dd0.jpg" 
                           target="_blank" class="url-link">
                    /static/uploads/general/thumbnails/large/general_20250725_011248_432fee6a_d4722dd0.jpg
                </a></div>
                <img src="http://localhost:8002/static/uploads/general/thumbnails/large/general_20250725_011248_432fee6a_d4722dd0.jpg" 
                     alt="صورة مصغرة كبيرة"
                     onload="this.nextElementSibling.innerHTML='<span class=status success>✅ تم التحميل بنجاح</span>'"
                     onerror="this.nextElementSibling.innerHTML='<span class=status error>❌ فشل التحميل</span>'">
                <div></div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 اختبار JavaScript URLs</h3>
            <div id="js-test-results"></div>
            <button onclick="testJavaScriptUrls()">اختبار URLs بـ JavaScript</button>
        </div>
    </div>

    <script>
        function testJavaScriptUrls() {
            const filePath = 'general/general_20250725_011248_432fee6a_d4722dd0.jpg';
            
            // محاكاة دوال الخدمة
            function getImageUrl(filePath) {
                const cleanPath = filePath.startsWith('/') ? filePath.slice(1) : filePath;
                const finalPath = cleanPath.startsWith('uploads/') ? cleanPath : `uploads/${cleanPath}`;
                return `/static/${finalPath}`;
            }
            
            function getThumbnailUrl(filePath, size = 'medium') {
                const cleanPath = filePath.startsWith('/') ? filePath.slice(1) : filePath;
                const pathParts = cleanPath.split('/');
                
                if (pathParts.length < 2) return getImageUrl(filePath);
                
                const folder = pathParts[0];
                const filename = pathParts[pathParts.length - 1];
                
                return `/static/uploads/${folder}/thumbnails/${size}/${filename}`;
            }
            
            const results = document.getElementById('js-test-results');
            results.innerHTML = `
                <div style="background: #f0f0f0; padding: 10px; border-radius: 5px; margin: 10px 0;">
                    <h4>نتائج اختبار JavaScript:</h4>
                    <p><strong>مسار الملف:</strong> ${filePath}</p>
                    <p><strong>URL الصورة الأصلية:</strong> ${getImageUrl(filePath)}</p>
                    <p><strong>URL الصورة المصغرة (متوسط):</strong> ${getThumbnailUrl(filePath, 'medium')}</p>
                    <p><strong>URL الصورة المصغرة (صغير):</strong> ${getThumbnailUrl(filePath, 'small')}</p>
                </div>
            `;
        }
    </script>
</body>
</html>
