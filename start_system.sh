#!/bin/bash

# ملف تشغيل نظام SmartPOS بشكل صحيح
# يضمن تشغيل الخادم الخلفي والواجهة الأمامية معاً

echo "🏪 بدء تشغيل نظام SmartPOS"
echo "================================"

# التحقق من وجود المجلدات المطلوبة
if [ ! -d "backend" ]; then
    echo "❌ مجلد backend غير موجود"
    exit 1
fi

if [ ! -d "frontend" ]; then
    echo "❌ مجلد frontend غير موجود"
    exit 1
fi

# التحقق من البيئة الافتراضية للخادم الخلفي
if [ ! -d "backend/venv" ]; then
    echo "❌ البيئة الافتراضية غير موجودة في backend/venv"
    echo "💡 قم بإنشائها أولاً: cd backend && python3 -m venv venv && source venv/bin/activate && pip install -r requirements.txt"
    exit 1
fi

# التحقق من node_modules للواجهة الأمامية
if [ ! -d "frontend/node_modules" ]; then
    echo "❌ node_modules غير موجود في frontend"
    echo "💡 قم بتثبيت التبعيات أولاً: cd frontend && npm install"
    exit 1
fi

# دالة لإيقاف العمليات عند الخروج
cleanup() {
    echo ""
    echo "🛑 إيقاف النظام..."
    
    # إيقاف الخادم الخلفي
    if [ ! -z "$BACKEND_PID" ]; then
        echo "⏹️ إيقاف الخادم الخلفي (PID: $BACKEND_PID)"
        kill $BACKEND_PID 2>/dev/null
    fi
    
    # إيقاف الواجهة الأمامية
    if [ ! -z "$FRONTEND_PID" ]; then
        echo "⏹️ إيقاف الواجهة الأمامية (PID: $FRONTEND_PID)"
        kill $FRONTEND_PID 2>/dev/null
    fi
    
    echo "✅ تم إيقاف النظام بنجاح"
    exit 0
}

# تسجيل دالة التنظيف للتشغيل عند الخروج
trap cleanup SIGINT SIGTERM

echo "🔧 بدء تشغيل الخادم الخلفي..."

# تشغيل الخادم الخلفي في الخلفية
cd backend
source venv/bin/activate
python main.py &
BACKEND_PID=$!
cd ..

echo "✅ تم بدء الخادم الخلفي (PID: $BACKEND_PID)"

# انتظار قليل للتأكد من بدء الخادم الخلفي
echo "⏳ انتظار بدء الخادم الخلفي..."
sleep 5

# التحقق من أن الخادم الخلفي يعمل
if ! curl -s http://localhost:8002/api/system/health > /dev/null; then
    echo "❌ فشل في بدء الخادم الخلفي"
    cleanup
    exit 1
fi

echo "✅ الخادم الخلفي يعمل بنجاح على http://localhost:8002"

echo "🎨 بدء تشغيل الواجهة الأمامية..."

# تشغيل الواجهة الأمامية في الخلفية
cd frontend
npm run dev &
FRONTEND_PID=$!
cd ..

echo "✅ تم بدء الواجهة الأمامية (PID: $FRONTEND_PID)"

echo ""
echo "🎉 النظام يعمل بنجاح!"
echo "================================"
echo "🔗 الواجهة الأمامية: http://localhost:5175"
echo "🔗 الخادم الخلفي: http://localhost:8002"
echo "🔗 للوصول من الشبكة: http://$(hostname -I | awk '{print $1}'):5175"
echo ""
echo "⚠️ اضغط Ctrl+C لإيقاف النظام"
echo ""

# انتظار إشارة الإيقاف
wait
