#!/bin/bash

# نص تشغيل نظام SmartPOS مع تدفق البيانات الكبيرة
# Start SmartPOS with Data Streaming System

echo "🚀 بدء تشغيل نظام SmartPOS مع تدفق البيانات الكبيرة"
echo "=================================================="

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دالة طباعة ملونة
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# فحص متطلبات النظام
check_requirements() {
    print_step "فحص متطلبات النظام..."
    
    # فحص Python
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
        print_status "Python متوفر: $PYTHON_VERSION"
    else
        print_error "Python 3 غير مثبت"
        exit 1
    fi
    
    # فحص Node.js
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_status "Node.js متوفر: $NODE_VERSION"
    else
        print_warning "Node.js غير مثبت - مطلوب للواجهة الأمامية"
    fi
    
    # فحص pip
    if command -v pip3 &> /dev/null; then
        print_status "pip3 متوفر"
    else
        print_error "pip3 غير مثبت"
        exit 1
    fi
}

# إعداد البيئة الافتراضية للخادم الخلفي
setup_backend() {
    print_step "إعداد الخادم الخلفي..."
    
    cd backend
    
    # إنشاء البيئة الافتراضية إذا لم تكن موجودة
    if [ ! -d "venv" ]; then
        print_status "إنشاء البيئة الافتراضية..."
        python3 -m venv venv
    fi
    
    # تفعيل البيئة الافتراضية
    print_status "تفعيل البيئة الافتراضية..."
    source venv/bin/activate
    
    # تثبيت المتطلبات
    print_status "تثبيت متطلبات Python..."
    pip install -r requirements.txt
    
    # إضافة متطلبات تدفق البيانات إذا لم تكن موجودة
    print_status "فحص متطلبات تدفق البيانات..."
    pip install aiofiles redis gzip-stream
    
    cd ..
}

# إعداد الواجهة الأمامية
setup_frontend() {
    print_step "إعداد الواجهة الأمامية..."
    
    if command -v npm &> /dev/null; then
        cd frontend
        
        # تثبيت المتطلبات إذا لم تكن موجودة
        if [ ! -d "node_modules" ]; then
            print_status "تثبيت متطلبات Node.js..."
            npm install
        fi
        
        cd ..
    else
        print_warning "تم تخطي إعداد الواجهة الأمامية - npm غير متوفر"
    fi
}

# إنشاء مجلدات التخزين المؤقت
create_cache_directories() {
    print_step "إنشاء مجلدات التخزين المؤقت..."
    
    mkdir -p backend/exports_cache
    mkdir -p backend/logs
    mkdir -p static
    
    print_status "تم إنشاء مجلدات التخزين المؤقت"
}

# تهيئة قاعدة البيانات
init_database() {
    print_step "تهيئة قاعدة البيانات..."
    
    cd backend
    source venv/bin/activate
    
    # تشغيل سكريبت تهيئة قاعدة البيانات
    if [ -f "init_db.py" ]; then
        python init_db.py
        print_status "تم تهيئة قاعدة البيانات"
    else
        print_warning "ملف تهيئة قاعدة البيانات غير موجود"
    fi
    
    cd ..
}

# تشغيل الخادم الخلفي
start_backend() {
    print_step "تشغيل الخادم الخلفي..."
    
    cd backend
    source venv/bin/activate
    
    # تعيين متغيرات البيئة لتدفق البيانات
    export STREAMING_CACHE_ENABLED=true
    export STREAMING_COMPRESSION=true
    export STREAMING_MAX_CHUNK_SIZE=5000
    export STREAMING_AUTO_CLEANUP=true
    
    print_status "بدء تشغيل الخادم على المنفذ 8002..."
    python main.py &
    BACKEND_PID=$!
    
    cd ..
    
    # انتظار تشغيل الخادم
    sleep 3
    
    # فحص ما إذا كان الخادم يعمل
    if kill -0 $BACKEND_PID 2>/dev/null; then
        print_status "الخادم الخلفي يعمل بنجاح (PID: $BACKEND_PID)"
        echo $BACKEND_PID > backend.pid
    else
        print_error "فشل في تشغيل الخادم الخلفي"
        exit 1
    fi
}

# تشغيل الواجهة الأمامية
start_frontend() {
    if command -v npm &> /dev/null; then
        print_step "تشغيل الواجهة الأمامية..."
        
        cd frontend
        npm run dev &
        FRONTEND_PID=$!
        
        cd ..
        
        sleep 2
        
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            print_status "الواجهة الأمامية تعمل بنجاح (PID: $FRONTEND_PID)"
            echo $FRONTEND_PID > frontend.pid
        else
            print_warning "فشل في تشغيل الواجهة الأمامية"
        fi
    else
        print_warning "تم تخطي تشغيل الواجهة الأمامية - npm غير متوفر"
    fi
}

# اختبار النظام
test_system() {
    print_step "اختبار النظام..."
    
    # انتظار قليل للتأكد من تشغيل الخادم
    sleep 5
    
    # اختبار الخادم الأساسي
    if curl -s http://localhost:8002/ > /dev/null; then
        print_status "✅ الخادم الأساسي يعمل"
    else
        print_error "❌ الخادم الأساسي لا يستجيب"
        return 1
    fi
    
    # اختبار تدفق البيانات
    if curl -s http://localhost:8002/api/data-streaming/sales/stream > /dev/null; then
        print_status "✅ نظام تدفق البيانات يعمل"
    else
        print_warning "⚠️ نظام تدفق البيانات قد لا يعمل بشكل صحيح"
    fi
    
    # تشغيل الاختبار السريع
    if [ -f "quick_test_streaming.py" ]; then
        print_status "تشغيل الاختبار السريع..."
        python3 quick_test_streaming.py
    fi
}

# عرض معلومات النظام
show_system_info() {
    echo ""
    echo "=================================================="
    print_status "🎉 تم تشغيل نظام SmartPOS بنجاح!"
    echo "=================================================="
    echo ""
    echo "📋 معلومات النظام:"
    echo "   🌐 الخادم الخلفي: http://localhost:8002"
    echo "   🖥️  الواجهة الأمامية: http://localhost:5173"
    echo "   📊 تدفق البيانات: http://localhost:8002/api/data-streaming"
    echo ""
    echo "🔧 أدوات مفيدة:"
    echo "   📈 المقاييس: http://localhost:8002/api/data-streaming/metrics"
    echo "   📁 تصدير المبيعات: http://localhost:8002/api/data-streaming/sales/stream"
    echo "   📦 تصدير المنتجات: http://localhost:8002/api/data-streaming/products/stream"
    echo ""
    echo "🧪 اختبار النظام:"
    echo "   python3 quick_test_streaming.py    # اختبار سريع"
    echo "   python3 test_data_streaming.py     # اختبار شامل"
    echo ""
    echo "⏹️  إيقاف النظام:"
    echo "   ./stop_system.sh                   # إيقاف آمن"
    echo "   Ctrl+C                             # إيقاف فوري"
    echo ""
}

# دالة التنظيف عند الإيقاف
cleanup() {
    print_step "إيقاف النظام..."
    
    if [ -f "backend.pid" ]; then
        BACKEND_PID=$(cat backend.pid)
        if kill -0 $BACKEND_PID 2>/dev/null; then
            kill $BACKEND_PID
            print_status "تم إيقاف الخادم الخلفي"
        fi
        rm backend.pid
    fi
    
    if [ -f "frontend.pid" ]; then
        FRONTEND_PID=$(cat frontend.pid)
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            kill $FRONTEND_PID
            print_status "تم إيقاف الواجهة الأمامية"
        fi
        rm frontend.pid
    fi
    
    print_status "تم إيقاف النظام بأمان"
    exit 0
}

# التعامل مع إشارات الإيقاف
trap cleanup SIGINT SIGTERM

# تشغيل النظام
main() {
    check_requirements
    setup_backend
    setup_frontend
    create_cache_directories
    init_database
    start_backend
    start_frontend
    test_system
    show_system_info
    
    # انتظار إشارة الإيقاف
    print_status "النظام يعمل... اضغط Ctrl+C للإيقاف"
    while true; do
        sleep 1
    done
}

# تشغيل الدالة الرئيسية
main
