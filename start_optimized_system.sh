#!/bin/bash

# 🚀 سكريبت تشغيل النظام المحسن - SmartPOS
# يقوم بتشغيل النظام مع جميع التحسينات الجديدة

echo "🚀 بدء تشغيل SmartPOS المحسن للبيانات الكبيرة..."
echo "=================================================="

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 غير مثبت. يرجى تثبيت Python3 أولاً."
    exit 1
fi

# التحقق من وجود Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً."
    exit 1
fi

# التحقق من وجود npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm غير مثبت. يرجى تثبيت npm أولاً."
    exit 1
fi

echo "✅ جميع المتطلبات متوفرة"

# تشغيل Backend
echo ""
echo "🔧 تشغيل الخادم الخلفي المحسن..."
cd backend

# التحقق من وجود البيئة الافتراضية
if [ ! -d "venv" ]; then
    echo "📦 إنشاء البيئة الافتراضية..."
    python3 -m venv venv
fi

# تفعيل البيئة الافتراضية
echo "🔄 تفعيل البيئة الافتراضية..."
source venv/bin/activate

# تثبيت المتطلبات
echo "📥 تثبيت متطلبات Python..."
pip install -r requirements.txt

# تشغيل الخادم في الخلفية
echo "🚀 تشغيل الخادم الخلفي على المنفذ 8002..."
python main.py &
BACKEND_PID=$!

# انتظار تشغيل الخادم
echo "⏳ انتظار تشغيل الخادم الخلفي..."
sleep 5

# التحقق من تشغيل الخادم
if ps -p $BACKEND_PID > /dev/null; then
    echo "✅ الخادم الخلفي يعمل بنجاح (PID: $BACKEND_PID)"
else
    echo "❌ فشل في تشغيل الخادم الخلفي"
    exit 1
fi

# العودة للمجلد الرئيسي
cd ..

# تشغيل Frontend
echo ""
echo "🎨 تشغيل الواجهة الأمامية المحسنة..."
cd frontend

# تثبيت المتطلبات
echo "📥 تثبيت متطلبات Node.js..."
npm install

# تشغيل الواجهة الأمامية
echo "🚀 تشغيل الواجهة الأمامية على المنفذ 5173..."
npm run dev &
FRONTEND_PID=$!

# انتظار تشغيل الواجهة
echo "⏳ انتظار تشغيل الواجهة الأمامية..."
sleep 3

# التحقق من تشغيل الواجهة
if ps -p $FRONTEND_PID > /dev/null; then
    echo "✅ الواجهة الأمامية تعمل بنجاح (PID: $FRONTEND_PID)"
else
    echo "❌ فشل في تشغيل الواجهة الأمامية"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

echo ""
echo "🎉 تم تشغيل النظام المحسن بنجاح!"
echo "=================================================="
echo "📊 الخادم الخلفي: http://localhost:8002"
echo "🌐 الواجهة الأمامية: http://localhost:5173"
echo "📈 مراقب الأداء: http://localhost:5173/reports?tab=system&subtab=monitor"
echo "🔧 API التحسينات: http://localhost:8002/api/performance/"
echo ""
echo "🚀 الميزات الجديدة المتاحة:"
echo "   ✅ تحسينات قاعدة البيانات (فهارس محسنة + WAL mode)"
echo "   ✅ نظام تخزين مؤقت متقدم"
echo "   ✅ استعلامات محسنة للبيانات الكبيرة"
echo "   ✅ Virtual scrolling للجداول الكبيرة"
echo "   ✅ مراقبة الأداء في الوقت الفعلي"
echo "   ✅ تحسين الذاكرة والمعالج"
echo ""
echo "📋 للإيقاف: اضغط Ctrl+C"
echo "=================================================="

# دالة تنظيف عند الإيقاف
cleanup() {
    echo ""
    echo "🛑 إيقاف النظام..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    echo "✅ تم إيقاف النظام بنجاح"
    exit 0
}

# التقاط إشارة الإيقاف
trap cleanup SIGINT SIGTERM

# انتظار إشارة الإيقاف
wait
