#!/usr/bin/env python3
"""
Script لتشغيل migration جداول الضرائب
يقوم بإنشاء جداول الضرائب وإدراج البيانات الافتراضية
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """تشغيل migration جداول الضرائب"""
    try:
        print("🚀 تشغيل migration جداول الضرائب...")
        print("=" * 60)
        
        # استيراد وتشغيل migration
        from migrations.create_tax_tables import main as run_migration
        
        success = run_migration()
        
        if success:
            print("\n🎉 تم إكمال migration بنجاح!")
            print("\n📋 يمكنك الآن:")
            print("1. تشغيل الخادم: python main.py")
            print("2. الوصول لإدارة الضرائب من صفحة إدارة الفهرس")
            print("3. إضافة أنواع ضرائب وقيم جديدة حسب الحاجة")
            return True
        else:
            print("\n❌ فشل في تشغيل migration")
            return False
            
    except ImportError as e:
        print(f"❌ خطأ في استيراد migration: {str(e)}")
        print("تأكد من وجود ملف migrations/create_tax_tables.py")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
