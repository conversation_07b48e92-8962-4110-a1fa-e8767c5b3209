"""
API endpoints لإدارة بصمات الأجهزة - نسخة مبسطة
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Optional
import logging

from database.session import get_db
from services.unified_fingerprint_service import get_unified_fingerprint_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/device-fingerprints", tags=["Device Fingerprints"])


@router.get("/")
async def get_all_fingerprints(
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """الحصول على جميع بصمات الأجهزة النشطة"""
    try:
        fingerprint_service = get_unified_fingerprint_service(db)
        fingerprints = fingerprint_service.get_all_fingerprints(limit)

        # البيانات جاهزة كقواميس من الخدمة الموحدة
        return {"fingerprints": fingerprints, "count": len(fingerprints)}
    except Exception as e:
        logger.error(f"خطأ في الحصول على بصمات الأجهزة: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ في الحصول على بصمات الأجهزة"
        )


@router.get("/{fingerprint_id}")
async def get_fingerprint(
    fingerprint_id: str,
    db: Session = Depends(get_db)
):
    """الحصول على بصمة جهاز محددة"""
    try:
        fingerprint_service = get_unified_fingerprint_service(db)
        fingerprint = fingerprint_service.get_fingerprint_by_id(fingerprint_id)

        if not fingerprint:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="بصمة الجهاز غير موجودة"
            )

        return {"fingerprint": fingerprint}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في الحصول على بصمة الجهاز: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ في الحصول على بصمة الجهاز"
        )


@router.get("/{fingerprint_id}/history")
async def get_fingerprint_history(
    fingerprint_id: str,
    limit: int = 50,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """الحصول على تاريخ بصمة الجهاز"""
    try:
        # التحقق من صحة المعاملات
        limit = min(max(limit, 1), 100)  # بين 1 و 100
        offset = max(offset, 0)

        # استخدام خدمة التاريخ المحسنة
        from services.device_fingerprint_history_service import get_fingerprint_history_service

        history_service = get_fingerprint_history_service(db)
        history_data = history_service.get_device_history(
            fingerprint_id=fingerprint_id,
            limit=limit,
            offset=offset
        )

        logger.info(f"تم جلب {len(history_data)} سجل تاريخ للجهاز: {fingerprint_id}")

        return {
            "success": True,
            "history": history_data,
            "count": len(history_data),
            "pagination": {
                "limit": limit,
                "offset": offset,
                "has_more": len(history_data) == limit
            },
            "message": f"تم جلب {len(history_data)} سجل بنجاح"
        }
    except Exception as e:
        logger.error(f"خطأ في الحصول على تاريخ البصمة: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ في الحصول على تاريخ البصمة"
        )


@router.delete("/{fingerprint_id}")
async def deactivate_fingerprint(
    fingerprint_id: str,
    reason: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """إلغاء تفعيل بصمة الجهاز"""
    try:
        fingerprint_service = get_unified_fingerprint_service(db)
        success = fingerprint_service.delete_fingerprint(fingerprint_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="بصمة الجهاز غير موجودة"
            )

        return {"message": "تم حذف البصمة بنجاح", "fingerprint_id": fingerprint_id}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في إلغاء تفعيل البصمة: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ في إلغاء تفعيل البصمة"
        )


@router.post("/cleanup")
async def cleanup_old_fingerprints(
    days_old: int = 90,
    db: Session = Depends(get_db)
):
    """تنظيف البصمات القديمة"""
    try:
        # تنظيف البصمات القديمة غير متوفر حالياً
        return {
            "message": "تنظيف البصمات القديمة غير متوفر حالياً",
            "cleaned_count": 0,
            "days_threshold": days_old
        }
    except Exception as e:
        logger.error(f"خطأ في تنظيف البصمات القديمة: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ في تنظيف البصمات القديمة"
        )


@router.get("/stats/summary")
async def get_fingerprint_stats(db: Session = Depends(get_db)):
    """الحصول على إحصائيات بصمات الأجهزة"""
    try:
        fingerprint_service = get_unified_fingerprint_service(db)

        # إحصائيات أساسية
        all_fingerprints = fingerprint_service.get_all_fingerprints(1000)

        # إحصائيات بسيطة
        stats = {
            "total_active_fingerprints": len(all_fingerprints),
            "message": "نظام بصمات الأجهزة يعمل بنجاح"
        }

        return stats
    except Exception as e:
        logger.error(f"خطأ في الحصول على إحصائيات البصمات: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ في الحصول على إحصائيات البصمات"
        )
