#!/usr/bin/env python3
"""
اختبار شامل لنظام إدارة الضرائب
يقوم بفحص جميع المكونات والوظائف الأساسية
"""

import sys
import os
import asyncio
import json
from pathlib import Path
from decimal import Decimal

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """اختبار استيراد جميع المكونات"""
    print("🔄 اختبار استيراد المكونات...")
    
    try:
        # اختبار استيراد النماذج
        from models.tax_type import TaxType
        from models.tax_rate import TaxRate
        print("✅ تم استيراد النماذج بنجاح")
        
        # اختبار استيراد الخدمات
        from services.tax.tax_type_service import TaxTypeService
        from services.tax.tax_rate_service import TaxRateService
        print("✅ تم استيراد الخدمات بنجاح")
        
        # اختبار استيراد Schemas
        from schemas.tax import (
            TaxTypeCreate, TaxTypeUpdate, TaxTypeResponse,
            TaxRateCreate, TaxRateUpdate, TaxRateResponse
        )
        print("✅ تم استيراد Schemas بنجاح")
        
        # اختبار استيراد Routers
        from routers.tax.tax_types_router import router as tax_types_router
        from routers.tax.tax_rates_router import router as tax_rates_router
        print("✅ تم استيراد Routers بنجاح")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("\n🔄 اختبار الاتصال بقاعدة البيانات...")
    
    try:
        from sqlalchemy import create_engine
        from sqlalchemy.orm import sessionmaker
        from database.session import get_database_url
        from models.tax_type import TaxType
        from models.tax_rate import TaxRate
        
        # إنشاء الاتصال
        engine = create_engine(get_database_url())
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        session = SessionLocal()
        
        # اختبار الاستعلام
        tax_types_count = session.query(TaxType).count()
        tax_rates_count = session.query(TaxRate).count()
        
        print(f"✅ الاتصال بقاعدة البيانات ناجح")
        print(f"📊 عدد أنواع الضرائب: {tax_types_count}")
        print(f"📊 عدد القيم الضريبية: {tax_rates_count}")
        
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return False

def test_tax_type_operations():
    """اختبار عمليات أنواع الضرائب"""
    print("\n🔄 اختبار عمليات أنواع الضرائب...")
    
    try:
        from sqlalchemy import create_engine
        from sqlalchemy.orm import sessionmaker
        from database.session import get_database_url
        from services.tax.tax_type_service import TaxTypeService
        from schemas.tax import TaxTypeCreate, TaxTypeUpdate
        from models.user import User
        
        # إنشاء الاتصال
        engine = create_engine(get_database_url())
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        session = SessionLocal()
        
        # الحصول على مستخدم للاختبار (أول مستخدم في قاعدة البيانات)
        user = session.query(User).first()
        if not user:
            print("⚠️ لا يوجد مستخدمون في قاعدة البيانات")
            session.close()
            return False
        
        # إنشاء خدمة أنواع الضرائب
        service = TaxTypeService(session, user)
        
        # اختبار جلب أنواع الضرائب
        tax_types = service.get_tax_types()
        print(f"✅ تم جلب {len(tax_types)} نوع ضريبة")
        
        # اختبار إنشاء نوع ضريبة جديد
        test_tax_type_data = TaxTypeCreate(
            name="Test Tax",
            name_ar="ضريبة اختبار",
            description="نوع ضريبة للاختبار",
            tax_category="standard",
            calculation_method="percentage",
            is_compound=False,
            is_active=True,
            sort_order=999
        )
        
        new_tax_type = service.create_tax_type(test_tax_type_data)
        print(f"✅ تم إنشاء نوع ضريبة جديد: {new_tax_type.name_ar}")
        
        # اختبار تحديث نوع الضريبة
        update_data = TaxTypeUpdate(description="وصف محدث للاختبار")
        updated_tax_type = service.update_tax_type(new_tax_type.id, update_data)
        print(f"✅ تم تحديث نوع الضريبة: {updated_tax_type.description}")
        
        # اختبار حذف نوع الضريبة
        service.delete_tax_type(new_tax_type.id)
        print("✅ تم حذف نوع الضريبة الاختباري")
        
        # اختبار الإحصائيات
        stats = service.get_tax_type_statistics()
        print(f"✅ تم جلب الإحصائيات: {stats['total_types']} نوع ضريبة")
        
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عمليات أنواع الضرائب: {str(e)}")
        if 'session' in locals():
            session.rollback()
            session.close()
        return False

def test_tax_rate_operations():
    """اختبار عمليات القيم الضريبية"""
    print("\n🔄 اختبار عمليات القيم الضريبية...")
    
    try:
        from sqlalchemy import create_engine
        from sqlalchemy.orm import sessionmaker
        from database.session import get_database_url
        from services.tax.tax_rate_service import TaxRateService
        from schemas.tax import TaxRateCreate
        from models.user import User
        from models.tax_type import TaxType
        
        # إنشاء الاتصال
        engine = create_engine(get_database_url())
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        session = SessionLocal()
        
        # الحصول على مستخدم ونوع ضريبة للاختبار
        user = session.query(User).first()
        tax_type = session.query(TaxType).first()
        
        if not user or not tax_type:
            print("⚠️ لا توجد بيانات كافية للاختبار")
            session.close()
            return False
        
        # إنشاء خدمة القيم الضريبية
        service = TaxRateService(session, user)
        
        # اختبار جلب القيم الضريبية
        tax_rates = service.get_tax_rates()
        print(f"✅ تم جلب {len(tax_rates)} قيمة ضريبية")
        
        # اختبار إنشاء قيمة ضريبية جديدة
        test_tax_rate_data = TaxRateCreate(
            tax_type_id=tax_type.id,
            name="قيمة اختبار",
            rate_value=Decimal("10.0000"),
            description="قيمة ضريبية للاختبار",
            is_default=False,
            is_active=True,
            applies_to="all",
            sort_order=999
        )
        
        new_tax_rate = service.create_tax_rate(test_tax_rate_data)
        print(f"✅ تم إنشاء قيمة ضريبية جديدة: {new_tax_rate.name}")
        
        # اختبار حساب الضريبة
        calculation_result = service.calculate_tax(new_tax_rate.id, Decimal("100.00"))
        print(f"✅ تم حساب الضريبة: {calculation_result['tax_amount']} على مبلغ 100")
        
        # اختبار حذف القيمة الضريبية
        service.delete_tax_rate(new_tax_rate.id)
        print("✅ تم حذف القيمة الضريبية الاختبارية")
        
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عمليات القيم الضريبية: {str(e)}")
        if 'session' in locals():
            session.rollback()
            session.close()
        return False

def test_api_endpoints():
    """اختبار API endpoints"""
    print("\n🔄 اختبار API endpoints...")
    
    try:
        from fastapi.testclient import TestClient
        from main import app
        
        client = TestClient(app)
        
        # اختبار endpoint أنواع الضرائب
        response = client.get("/api/tax-types/")
        if response.status_code == 401:
            print("⚠️ يتطلب تسجيل الدخول لاختبار API")
            return True
        elif response.status_code == 200:
            data = response.json()
            print(f"✅ API أنواع الضرائب يعمل: {len(data)} نوع")
        else:
            print(f"⚠️ استجابة غير متوقعة من API: {response.status_code}")
        
        # اختبار endpoint القيم الضريبية
        response = client.get("/api/tax-rates/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API القيم الضريبية يعمل: {len(data)} قيمة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار API endpoints: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 بدء اختبار شامل لنظام إدارة الضرائب")
    print("=" * 60)
    
    tests = [
        ("استيراد المكونات", test_imports),
        ("الاتصال بقاعدة البيانات", test_database_connection),
        ("عمليات أنواع الضرائب", test_tax_type_operations),
        ("عمليات القيم الضريبية", test_tax_rate_operations),
        ("API Endpoints", test_api_endpoints)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 اختبار: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ نجح اختبار: {test_name}")
            else:
                print(f"❌ فشل اختبار: {test_name}")
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        print("\n📋 الخطوات التالية:")
        print("1. تشغيل الخادم: python main.py")
        print("2. فتح المتصفح على: http://localhost:5175")
        print("3. الانتقال إلى إدارة الفهرس ← أنواع الضرائب")
        return True
    else:
        print(f"⚠️ {total - passed} اختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
