"""
تبعيات المصادقة والتفويض
"""

from fastapi import Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from typing import Optional

from database.session import get_db
from models.user import User

# إعداد نظام المصادقة
security = HTTPBearer()

def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """
    الحصول على المستخدم الحالي من التوكن
    """
    try:
        # هنا يجب إضافة منطق التحقق من التوكن
        # للآن سنقوم بإرجاع مستخدم وهمي للاختبار
        
        # البحث عن مستخدم في قاعدة البيانات
        user = db.query(User).filter(User.id == 1).first()
        
        if not user:
            # إنشاء مستخدم وهمي للاختبار
            user = User(
                id=1,
                username="admin",
                email="<EMAIL>",
                is_active=True
            )
        
        return user
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    التحقق من أن المستخدم نشط
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user

def require_permission(permission: str):
    """
    التحقق من صلاحية معينة
    """
    def permission_checker(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        # هنا يجب إضافة منطق التحقق من الصلاحيات
        # للآن سنقوم بإرجاع المستخدم مباشرة
        return current_user
    
    return permission_checker
