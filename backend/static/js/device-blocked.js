document.addEventListener('DOMContentLoaded', function() {
    // عرض عنوان IP مع تأثير الكتابة
    const deviceIpElement = document.getElementById('device-ip');
    const deviceIp = window.location.hostname || 'غير معروف';
    typeWriter(deviceIpElement, deviceIp, 50);

    // عرض وقت الحظر مع تأثير الكتابة (بتوقيت طرابلس)
    const tripoliTime = getCurrentTripoliDateTime();
    const timeElement = document.getElementById('block-time');
    const formattedTime = formatDateTime(tripoliTime, 'datetime');
    setTimeout(() => typeWriter(timeElement, formattedTime, 30), 500);

    // تأثيرات التفاعل
    addButtonInteractions();

    // تأثير اهتزاز للأيقونة كل 5 ثوان
    setInterval(() => {
        const icon = document.querySelector('.shake-animation');
        if (icon) {
            icon.style.animation = 'none';
            setTimeout(() => {
                icon.style.animation = 'shake 0.5s ease-in-out';
            }, 10);
        }
    }, 5000);

    // إضافة زر للفحص اليدوي بدلاً من الفحص التلقائي
    const manualCheckButton = document.getElementById('manual-check-btn');
    if (manualCheckButton) {
        manualCheckButton.addEventListener('click', async () => {
            try {
                manualCheckButton.disabled = true;
                manualCheckButton.textContent = 'جاري الفحص...';

                const response = await fetch('/api/device/status');
                if (response.ok) {
                    const data = await response.json();
                    if (data.status === 'allowed') {
                        // إعادة توجيه إلى الصفحة الرئيسية
                        window.location.href = '/';
                    } else if (data.status === 'pending_approval') {
                        // إعادة توجيه إلى صفحة انتظار الموافقة
                        window.location.href = '/device-pending-approval';
                    } else {
                        // لا تزال محظورة
                        manualCheckButton.textContent = 'فحص الحالة';
                        manualCheckButton.disabled = false;
                    }
                } else {
                    manualCheckButton.textContent = 'فحص الحالة';
                    manualCheckButton.disabled = false;
                }
            } catch (error) {
                console.warn('Failed to check device status:', error);
                manualCheckButton.textContent = 'فحص الحالة';
                manualCheckButton.disabled = false;
            }
        });
    }

    console.log('Device blocked page loaded successfully');
});
