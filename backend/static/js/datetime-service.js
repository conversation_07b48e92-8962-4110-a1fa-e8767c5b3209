// خدمات التواريخ والوقت - محاكاة خدمات المشروع
const TRIPOLI_TIMEZONE = 'Africa/Tripoli'; // UTC+2
const TRIPOLI_TIMEZONE_OFFSET = 2; // ساعات

/**
 * الحصول على التاريخ والوقت الحالي بتوقيت طرابلس
 * مطابق لـ getCurrentTripoliDateTime في dateTimeService.ts
 */
function getCurrentTripoliDateTime() {
    try {
        // الحصول على الوقت الحالي
        const now = new Date();
        // استخدام الوقت الحالي مباشرة (مثل المشروع)
        return now;
    } catch (error) {
        console.error('Error getting current time:', error);
        return new Date();
    }
}

/**
 * تحويل تاريخ إلى تاريخ بتوقيت طرابلس
 * مطابق لـ convertToTripoliTime في dateTimeService.ts
 */
function convertToTripoliTime(date) {
    if (!date) {
        console.warn('Attempted to convert null/undefined date to Tripoli time');
        return new Date();
    }

    try {
        // إذا كان التاريخ سلسلة، قم بتحويله إلى كائن Date
        const dateObj = typeof date === 'string' ? new Date(date) : new Date(date.getTime());

        // بما أن التواريخ مخزنة بالفعل بتوقيت طرابلس في قاعدة البيانات،
        // فلا نحتاج إلى إضافة ساعات إضافية، نستخدم التاريخ كما هو
        console.debug(`Using date as is (already in Tripoli time): ${dateObj.toISOString()}`);
        return dateObj;
    } catch (error) {
        console.error('Error converting to Tripoli time:', error);
        return new Date();
    }
}

/**
 * تنسيق التاريخ والوقت بالصيغة المطلوبة
 * مطابق لـ formatDateTime في dateTimeService.ts
 */
function formatDateTime(date, format = 'datetime') {
    if (!date) {
        console.warn('Attempted to format null/undefined date');
        return '';
    }

    // إذا كان التاريخ سلسلة، قم بتحويله إلى كائن Date
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    // تحويل التاريخ إلى توقيت طرابلس
    const tripoliDate = convertToTripoliTime(dateObj);

    // تنسيق التاريخ حسب الصيغة المطلوبة
    let formatted = '';
    switch (format) {
        case 'date':
            formatted = tripoliDate.toLocaleDateString('ar-LY');
            break;
        case 'time':
            formatted = tripoliDate.toLocaleTimeString('ar-LY');
            break;
        case 'datetime':
            formatted = tripoliDate.toLocaleString('ar-LY');
            break;
        case 'day':
            formatted = `${tripoliDate.getDate().toString().padStart(2, '0')}`;
            break;
        case 'month':
            formatted = `${(tripoliDate.getMonth() + 1).toString().padStart(2, '0')}`;
            break;
        case 'year':
            formatted = `${tripoliDate.getFullYear()}`;
            break;
        default:
            formatted = tripoliDate.toLocaleString('ar-LY');
    }

    console.debug(`Formatted ${tripoliDate.toISOString()} as ${formatted} using format ${format}`);
    return formatted;
}

/**
 * عرض الوقت النسبي (مثل "منذ دقيقتين")
 * مطابق لمنطق المشروع
 */
function getRelativeTime(date) {
    if (!date) return '';

    const now = getCurrentTripoliDateTime();
    const tripoliDate = convertToTripoliTime(date);
    const diffMs = now.getTime() - tripoliDate.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffSeconds < 60) {
        return 'الآن';
    } else if (diffMinutes < 60) {
        return `منذ ${diffMinutes} دقيقة`;
    } else if (diffHours < 24) {
        return `منذ ${diffHours} ساعة`;
    } else if (diffDays < 30) {
        return `منذ ${diffDays} يوم`;
    } else {
        return formatDateTime(tripoliDate, 'date');
    }
}

/**
 * إنشاء معرف طلب واقعي يتضمن التاريخ والوقت
 */
function generateRequestId() {
    const now = getCurrentTripoliDateTime();
    const year = now.getFullYear().toString().substr(-2);
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const hour = now.getHours().toString().padStart(2, '0');
    const minute = now.getMinutes().toString().padStart(2, '0');
    const randomSuffix = Math.random().toString(36).substr(2, 4).toUpperCase();
    return `REQ-${year}${month}${day}${hour}${minute}-${randomSuffix}`;
}

/**
 * تأثير الكتابة
 */
function typeWriter(element, text, speed) {
    element.textContent = '';
    let i = 0;
    function type() {
        if (i < text.length) {
            element.textContent += text.charAt(i);
            i++;
            setTimeout(type, speed);
        }
    }
    type();
}

/**
 * تأثيرات التفاعل للأزرار
 */
function addButtonInteractions() {
    document.querySelectorAll('button').forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.02)';
        });
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}
