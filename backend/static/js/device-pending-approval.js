// ⚠️ هذا الملف معطل بالكامل لأن React تتولى كل شيء الآن
// تم استبداله بـ DevicePendingApproval.tsx React component

console.log('🔍 Legacy device pending approval JS loaded - DISABLED for React');
console.log('⚠️ This legacy JS file is disabled. React component handles everything now.');

// تعطيل جميع الوظائف لمنع التداخل مع React
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚫 Legacy JS disabled - React component is handling the page');

    // لا نفعل أي شيء هنا لتجنب التداخل مع React
    // React component DevicePendingApproval.tsx يتولى كل شيء
});

// تعطيل جميع الوظائف القديمة
/*
// الكود القديم معطل بالكامل...

    // عرض وقت الطلب مع تأثير الكتابة (بتوقيت طرابلس)
    const tripoliTime = getCurrentTripoliDateTime();
    const timeElement = document.getElementById('request-time');
    const formattedTime = window.dateTimeService ?
        window.dateTimeService.formatDateTime(tripoliTime, 'datetime') :
        tripoliTime.toLocaleString('ar-LY');
    if (timeElement) {
        setTimeout(() => typeWriter(timeElement, formattedTime, 30), 500);
    }

    // إنشاء معرف طلب واقعي يتضمن التاريخ والوقت
    const requestId = generateRequestId();
    const requestIdElement = document.getElementById('request-id');
    if (requestIdElement) {
        setTimeout(() => typeWriter(requestIdElement, requestId, 80), 1000);
    }

    // عرض معلومات البصمة إذا كانت متوفرة
    displayDeviceFingerprint();

    // تأثيرات التفاعل
    addButtonInteractions();

    // إزالة العداد التلقائي - سيتم الفحص فقط يدوياً
    // let countdown = 30;
    // const countdownElement = document.getElementById('countdown');
    // const countdownDisplayElement = document.getElementById('countdown-display');

    // function updateCountdown() {
    //     if (countdownElement) countdownElement.textContent = countdown;
    //     if (countdownDisplayElement) countdownDisplayElement.textContent = countdown;

    //     countdown--;

    //     if (countdown < 0) {
    //         // إعادة تعيين العداد بدلاً من إعادة تحميل الصفحة
    //         countdown = 30;
    //     }
    // }

    // تحديث العداد كل ثانية (بدون إعادة تحميل) - معطل
    // const countdownInterval = setInterval(updateCountdown, 1000);

    // تنظيف العداد عند مغادرة الصفحة - معطل
    // window.addEventListener('beforeunload', () => {
    //     clearInterval(countdownInterval);
    // });

    // إضافة زر للفحص اليدوي
    const manualCheckButton = document.getElementById('manual-check-btn');
    if (manualCheckButton) {
        manualCheckButton.addEventListener('click', () => {
            checkDeviceStatus(true); // فحص يدوي
        });
    }

    // تعطيل الفحص الدوري التلقائي لتجنب إزعاج النظام
    // المستخدم يمكنه استخدام زر "فحص الحالة الآن" للفحص اليدوي
    // const statusCheckInterval = setInterval(() => {
    //     console.log('🔄 Automatic status check (legacy JS)...');
    //     checkDeviceStatus(false); // فحص تلقائي
    // }, 60000); // تغيير من 5 ثوان إلى 60 ثانية

    // فحص أولي بعد 10 ثوان (محسن لتوفير الموارد) - معطل لتوفير الموارد
    // setTimeout(() => {
    //     console.log('🔄 Initial status check (legacy JS)...');
    //     checkDeviceStatus(false);
    // }, 10000); // تغيير من 3 ثوان إلى 10 ثوان

    // حفظ interval للتنظيف لاحقاً - معطل
    // window.statusCheckInterval = statusCheckInterval;

    // إضافة تأثيرات بصرية للانتظار
    const pulseElements = document.querySelectorAll('.pulse-animation');
    pulseElements.forEach(element => {
        element.addEventListener('animationiteration', () => {
            // يمكن إضافة تأثيرات إضافية هنا
        });
    });

    // تهيئة الأيقونات
    initializeIcons();

    console.log('Device pending approval page loaded successfully');
});

// فحص حالة الجهاز
async function checkDeviceStatus(isManual = false) {
    const manualCheckButton = document.getElementById('manual-check-btn');

    try {
        if (isManual && manualCheckButton) {
            manualCheckButton.disabled = true;
            manualCheckButton.textContent = 'جاري الفحص...';
        }

        const response = await fetch('/api/device/status');
        if (response.ok) {
            const data = await response.json();
            console.log('📊 Device status response:', data);

            if (data.status === 'allowed') {
                console.log('✅ Device approved! Redirecting...');

                // إيقاف الفحص الدوري
                if (window.statusCheckInterval) {
                    clearInterval(window.statusCheckInterval);
                }

                // تحديد الرابط للتوجيه
                let redirectUrl;
                const urlParams = new URLSearchParams(window.location.search);
                const originalUrl = data.smart_redirect_url || data.original_url || urlParams.get('original_url');

                if (originalUrl && originalUrl !== 'null' && originalUrl !== 'undefined') {
                    try {
                        const parsedUrl = new URL(originalUrl);
                        if (parsedUrl.hostname === window.location.hostname ||
                            parsedUrl.hostname === '*************' ||
                            parsedUrl.hostname === 'localhost') {
                            redirectUrl = originalUrl;
                        } else {
                            throw new Error('رابط غير آمن');
                        }
                    } catch (error) {
                        console.warn('⚠️ رابط أصلي غير صحيح، استخدام الرابط الافتراضي:', error);
                        redirectUrl = `http://${window.location.hostname}:5175/`;
                    }
                } else {
                    redirectUrl = `http://${window.location.hostname}:5175/`;
                }

                console.log('🔄 Redirecting to:', redirectUrl);
                window.location.href = redirectUrl;

            } else if (data.status === 'blocked') {
                console.log('❌ Device blocked! Redirecting to blocked page...');

                // إيقاف الفحص الدوري
                if (window.statusCheckInterval) {
                    clearInterval(window.statusCheckInterval);
                }

                const blockedUrl = `http://${window.location.hostname}:5175/device-blocked`;
                console.log('🔄 Redirecting to blocked page:', blockedUrl);
                window.location.href = blockedUrl;

            } else {
                console.log('⏳ Device still pending approval');
                if (isManual && manualCheckButton) {
                    manualCheckButton.textContent = 'فحص الحالة الآن';
                    manualCheckButton.disabled = false;
                }
            }
        } else {
            console.warn('Failed to check device status:', response.status);
            if (isManual && manualCheckButton) {
                manualCheckButton.textContent = 'فحص الحالة الآن';
                manualCheckButton.disabled = false;
            }
        }
    } catch (error) {
        console.warn('Failed to check device status:', error);
        if (isManual && manualCheckButton) {
            manualCheckButton.textContent = 'فحص الحالة الآن';
            manualCheckButton.disabled = false;
        }
    }
}

// تحسين عرض الأيقونات SVG
function initializeIcons() {
    console.log('تم تهيئة الأيقونات SVG بنجاح');

    // إضافة تأثيرات للأيقونات المتحركة
    const spinningIcons = document.querySelectorAll('.icon-spin');
    spinningIcons.forEach(icon => {
        icon.style.animation = 'spin 1s linear infinite';
    });
}

// الحصول على العنوان العام (الدولي)
async function getPublicIP() {
    try {
        // محاولة الحصول على IP من خدمات خارجية
        const ipServices = [
            'https://api.ipify.org?format=json',
            'https://ipapi.co/json/',
            'https://httpbin.org/ip'
        ];

        for (const service of ipServices) {
            try {
                const response = await fetch(service, { timeout: 5000 });
                if (response.ok) {
                    const data = await response.json();
                    const ip = data.ip || data.origin;
                    if (ip && ip !== '127.0.0.1' && ip !== 'localhost') {
                        console.log(`✅ تم الحصول على IP من ${service}: ${ip}`);
                        return ip;
                    }
                }
            } catch (error) {
                console.warn(`⚠️ فشل في الحصول على IP من ${service}:`, error);
                continue;
            }
        }

        // إذا فشلت الخدمات الخارجية
        console.warn('⚠️ فشل في الحصول على العنوان العام من جميع الخدمات');
        return 'غير متوفر';
    } catch (error) {
        console.error('❌ خطأ في الحصول على العنوان العام:', error);
        return 'غير متوفر';
    }
}

// الحصول على العنوان المحلي من الخلفية
async function getLocalIP() {
    try {
        // الحصول على عنوان الجهاز من الخلفية
        const response = await fetch('/api/device/client-ip', {
            method: 'GET',
            headers: {
                'Accept': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.client_ip && data.client_ip !== '127.0.0.1') {
                console.log(`✅ تم الحصول على العنوان المحلي من الخلفية: ${data.client_ip}`);
                return data.client_ip;
            }
        }

        // fallback: استخدام عنوان الخادم
        const serverIP = window.location.hostname;
        if (serverIP && serverIP !== 'localhost' && serverIP !== '127.0.0.1') {
            console.log(`📍 استخدام عنوان الخادم كـ fallback: ${serverIP}`);
            return serverIP;
        }

        return 'غير متوفر';
    } catch (error) {
        console.error('❌ خطأ في الحصول على العنوان المحلي:', error);
        return 'غير متوفر';
    }
}

// عرض معلومات البصمة
async function displayDeviceFingerprint() {
    try {
        // محاولة الحصول على بصمة الجهاز من localStorage
        const storedFingerprint = localStorage.getItem('smartpos_fingerprint');
        const deviceId = localStorage.getItem('smartpos_device_id');

        if (storedFingerprint) {
            const fingerprint = JSON.parse(storedFingerprint);
            console.log('🔍 Device fingerprint found:', fingerprint);

            // عرض معرف الجهاز إذا كان متوفراً
            const deviceIdElement = document.getElementById('device-fingerprint-id');
            if (deviceIdElement && fingerprint.deviceId) {
                deviceIdElement.textContent = fingerprint.deviceId.slice(0, 12) + '...';
            }
        } else if (deviceId) {
            console.log('🔍 Device ID found:', deviceId);
            const deviceIdElement = document.getElementById('device-fingerprint-id');
            if (deviceIdElement) {
                deviceIdElement.textContent = deviceId.slice(0, 12) + '...';
            }
        }
    } catch (error) {
        console.warn('خطأ في عرض معلومات البصمة:', error);
    }
}

// معالجة fallback للأيقونات
function handleIconFallback() {
    // التحقق من تحميل الأيقونات وإضافة fallback إذا لزم الأمر
    console.log('تم تهيئة fallback للأيقونات');
}

// تأثير الكتابة
function typeWriter(element, text, speed = 50) {
    if (!element || !text) return;

    element.textContent = '';
    let i = 0;

    function type() {
        if (i < text.length) {
            element.textContent += text.charAt(i);
            i++;
            setTimeout(type, speed);
        }
    }

    type();
}

// إضافة تأثيرات التفاعل
function addButtonInteractions() {
    // إضافة تأثيرات hover للعناصر التفاعلية
    const interactiveElements = document.querySelectorAll('.hover-effect');
    interactiveElements.forEach(element => {
        element.addEventListener('mouseenter', () => {
            element.style.transform = 'scale(1.05)';
            element.style.transition = 'transform 0.2s ease';
        });

        element.addEventListener('mouseleave', () => {
            element.style.transform = 'scale(1)';
        });
    });
}

// إنشاء معرف طلب
function generateRequestId() {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 7);
    return `REQ-${timestamp}-${random}`.toUpperCase();
}

// نهاية الكود القديم المعطل
*/
