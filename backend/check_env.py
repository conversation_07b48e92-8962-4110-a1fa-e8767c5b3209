#!/usr/bin/env python3
"""
فحص متغيرات البيئة
"""

import os
from pathlib import Path
from dotenv import load_dotenv

print("=" * 50)
print("🔍 فحص متغيرات البيئة")
print("=" * 50)

# المسار الحالي
current_dir = Path(__file__).parent
print(f"📁 المجلد الحالي: {current_dir}")

# مسار ملف .env
env_path = current_dir / ".env"
print(f"📄 مسار ملف .env: {env_path}")
print(f"📋 ملف .env موجود: {env_path.exists()}")

if env_path.exists():
    print(f"📖 محتوى ملف .env:")
    with open(env_path, 'r', encoding='utf-8') as f:
        content = f.read()
        print(content)

print("\n" + "=" * 50)
print("🔄 تحميل متغيرات البيئة")
print("=" * 50)

# تحميل متغيرات البيئة
load_dotenv(dotenv_path=env_path)

# فحص متغير DATABASE_URL
database_url = os.getenv("DATABASE_URL")
print(f"🗄️ DATABASE_URL: {database_url}")

# فحص متغيرات أخرى
email_user = os.getenv("EMAIL_USER")
print(f"📧 EMAIL_USER: {email_user}")

node_env = os.getenv("NODE_ENV")
print(f"🌐 NODE_ENV: {node_env}")

print("=" * 50)
