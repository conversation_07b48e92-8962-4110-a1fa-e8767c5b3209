#!/usr/bin/env python3
"""
Script to run catalog tables migration
"""

import sys
import os

# Add the backend directory to the Python path
backend_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, backend_dir)

try:
    from migrations.create_catalog_tables import run_migration
    
    print("🚀 بدء تشغيل migration لجداول الفهرس...")
    run_migration()
    print("✅ تم إكمال migration بنجاح!")
    
except Exception as e:
    print(f"❌ فشل في تشغيل migration: {str(e)}")
    sys.exit(1)
