"""
تكوين Google Drive - بيانات الاعتماد الثابتة
"""

# بيانات اعتماد Google Drive
GOOGLE_DRIVE_CREDENTIALS = {
    "web": {
        "client_id": "************-n3f6ei3spe769sg2464omu1vo6mff6al.apps.googleusercontent.com",
        "project_id": "smartposly",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_secret": "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",
        "redirect_uris": ["http://localhost:8002/api/google-drive/oauth-callback"]
    }
}

# نطاقات الصلاحيات المطلوبة
GOOGLE_DRIVE_SCOPES = ['https://www.googleapis.com/auth/drive.file']

# رابط إعادة التوجيه
REDIRECT_URI = "http://localhost:8002/api/google-drive/oauth-callback"

# صفحات إغلاق النافذة
OAUTH_SUCCESS_PAGE = "/api/google-drive/oauth-success"
OAUTH_ERROR_PAGE = "/api/google-drive/oauth-error"
