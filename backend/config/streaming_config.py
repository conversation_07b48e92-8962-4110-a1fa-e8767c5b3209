"""
إعدادات نظام تدفق البيانات الكبيرة
"""

import os
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from pathlib import Path

@dataclass
class StreamingConfig:
    """إعدادات تدفق البيانات"""
    
    # إعدادات عامة
    MAX_CHUNK_SIZE: int = 5000
    DEFAULT_CHUNK_SIZE: int = 1000
    MAX_CONCURRENT_STREAMS: int = 5
    DEFAULT_TIMEOUT_SECONDS: int = 300
    MAX_FILE_SIZE_MB: int = 500
    
    # إعدادات التخزين المؤقت
    CACHE_ENABLED: bool = True
    CACHE_TTL_SECONDS: int = 3600  # ساعة واحدة
    CACHE_MAX_SIZE_MB: int = 1000
    EXPORT_CACHE_DIR: str = "exports_cache"
    
    # إعدادات الضغط
    COMPRESSION_ENABLED: bool = True
    COMPRESSION_LEVEL: int = 6  # مستوى ضغط GZIP (1-9)
    COMPRESSION_THRESHOLD_KB: int = 100  # ضغط الملفات أكبر من 100KB
    
    # إعدادات الأمان
    MAX_REQUESTS_PER_MINUTE: int = 60
    MAX_EXPORT_SIZE_PER_USER_MB: int = 1000
    ALLOWED_EXPORT_FORMATS: Optional[List[str]] = None
    REQUIRE_AUTHENTICATION: bool = True
    
    # إعدادات المراقبة
    MONITORING_ENABLED: bool = True
    LOG_LEVEL: str = "INFO"
    METRICS_RETENTION_DAYS: int = 30
    PERFORMANCE_TRACKING: bool = True
    
    # إعدادات التنظيف التلقائي
    AUTO_CLEANUP_ENABLED: bool = True
    CLEANUP_INTERVAL_HOURS: int = 6
    MAX_FILE_AGE_HOURS: int = 24
    CLEANUP_ON_STARTUP: bool = True
    
    # إعدادات قاعدة البيانات
    DB_QUERY_TIMEOUT: int = 30
    DB_CONNECTION_POOL_SIZE: int = 20
    DB_MAX_OVERFLOW: int = 30
    ENABLE_QUERY_OPTIMIZATION: bool = True
    
    # إعدادات Redis (اختيارية)
    REDIS_ENABLED: bool = False
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    REDIS_TTL_SECONDS: int = 86400  # 24 ساعة
    
    # إعدادات التصدير المتقدمة
    ENABLE_BULK_EXPORT: bool = True
    ENABLE_STREAMING_EXPORT: bool = True
    ENABLE_SCHEDULED_EXPORT: bool = False
    MAX_BULK_TABLES: int = 10
    
    # إعدادات التنسيقات
    SUPPORTED_FORMATS: Optional[List[str]] = None
    DEFAULT_FORMAT: str = "json"
    ENABLE_CSV_EXPORT: bool = True
    ENABLE_JSON_EXPORT: bool = True
    ENABLE_XML_EXPORT: bool = False
    
    # إعدادات الشبكة
    STREAM_BUFFER_SIZE: int = 8192
    CONNECTION_TIMEOUT: int = 30
    READ_TIMEOUT: int = 60
    ENABLE_KEEP_ALIVE: bool = True
    
    def __post_init__(self):
        """تهيئة القيم الافتراضية"""
        if self.ALLOWED_EXPORT_FORMATS is None:
            self.ALLOWED_EXPORT_FORMATS = ["json", "csv"]
        
        if self.SUPPORTED_FORMATS is None:
            self.SUPPORTED_FORMATS = ["json", "csv", "xml"]
        
        # التحقق من صحة الإعدادات
        self._validate_config()
    
    def _validate_config(self):
        """التحقق من صحة الإعدادات"""
        if self.MAX_CHUNK_SIZE < 100:
            raise ValueError("MAX_CHUNK_SIZE يجب أن يكون أكبر من 100")
        
        if self.DEFAULT_CHUNK_SIZE > self.MAX_CHUNK_SIZE:
            raise ValueError("DEFAULT_CHUNK_SIZE يجب أن يكون أصغر من MAX_CHUNK_SIZE")
        
        if self.MAX_FILE_SIZE_MB < 1:
            raise ValueError("MAX_FILE_SIZE_MB يجب أن يكون أكبر من 1")
        
        if self.COMPRESSION_LEVEL < 1 or self.COMPRESSION_LEVEL > 9:
            raise ValueError("COMPRESSION_LEVEL يجب أن يكون بين 1 و 9")

        if self.ALLOWED_EXPORT_FORMATS and self.DEFAULT_FORMAT not in self.ALLOWED_EXPORT_FORMATS:
            raise ValueError(f"DEFAULT_FORMAT ({self.DEFAULT_FORMAT}) غير مدعوم")
    
    @classmethod
    def from_env(cls) -> 'StreamingConfig':
        """إنشاء إعدادات من متغيرات البيئة"""
        return cls(
            MAX_CHUNK_SIZE=int(os.getenv('STREAMING_MAX_CHUNK_SIZE', 5000)),
            DEFAULT_CHUNK_SIZE=int(os.getenv('STREAMING_DEFAULT_CHUNK_SIZE', 1000)),
            MAX_CONCURRENT_STREAMS=int(os.getenv('STREAMING_MAX_CONCURRENT', 5)),
            DEFAULT_TIMEOUT_SECONDS=int(os.getenv('STREAMING_TIMEOUT', 300)),
            MAX_FILE_SIZE_MB=int(os.getenv('STREAMING_MAX_FILE_SIZE_MB', 500)),
            
            CACHE_ENABLED=os.getenv('STREAMING_CACHE_ENABLED', 'true').lower() == 'true',
            CACHE_TTL_SECONDS=int(os.getenv('STREAMING_CACHE_TTL', 3600)),
            EXPORT_CACHE_DIR=os.getenv('STREAMING_CACHE_DIR', 'exports_cache'),
            
            COMPRESSION_ENABLED=os.getenv('STREAMING_COMPRESSION', 'true').lower() == 'true',
            COMPRESSION_LEVEL=int(os.getenv('STREAMING_COMPRESSION_LEVEL', 6)),
            
            MAX_REQUESTS_PER_MINUTE=int(os.getenv('STREAMING_RATE_LIMIT', 60)),
            REQUIRE_AUTHENTICATION=os.getenv('STREAMING_AUTH_REQUIRED', 'true').lower() == 'true',
            
            REDIS_ENABLED=os.getenv('REDIS_ENABLED', 'false').lower() == 'true',
            REDIS_HOST=os.getenv('REDIS_HOST', 'localhost'),
            REDIS_PORT=int(os.getenv('REDIS_PORT', 6379)),
            REDIS_PASSWORD=os.getenv('REDIS_PASSWORD') or None,
            
            LOG_LEVEL=os.getenv('STREAMING_LOG_LEVEL', 'INFO'),
            AUTO_CLEANUP_ENABLED=os.getenv('STREAMING_AUTO_CLEANUP', 'true').lower() == 'true',
            MAX_FILE_AGE_HOURS=int(os.getenv('STREAMING_MAX_FILE_AGE_HOURS', 24))
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل الإعدادات إلى قاموس"""
        return {
            field.name: getattr(self, field.name)
            for field in self.__dataclass_fields__.values()
        }
    
    def get_cache_path(self) -> Path:
        """الحصول على مسار التخزين المؤقت"""
        cache_path = Path(self.EXPORT_CACHE_DIR)
        cache_path.mkdir(exist_ok=True)
        return cache_path
    
    def is_format_allowed(self, format_type: str) -> bool:
        """فحص ما إذا كان التنسيق مسموح"""
        if not self.ALLOWED_EXPORT_FORMATS:
            return True
        return format_type.lower() in [f.lower() for f in self.ALLOWED_EXPORT_FORMATS]
    
    def get_max_file_size_bytes(self) -> int:
        """الحصول على الحد الأقصى لحجم الملف بالبايت"""
        return self.MAX_FILE_SIZE_MB * 1024 * 1024
    
    def should_compress_file(self, file_size_bytes: int) -> bool:
        """تحديد ما إذا كان يجب ضغط الملف"""
        if not self.COMPRESSION_ENABLED:
            return False
        
        threshold_bytes = self.COMPRESSION_THRESHOLD_KB * 1024
        return file_size_bytes > threshold_bytes

# إنشاء مثيل الإعدادات العامة
streaming_config = StreamingConfig.from_env()

# إعدادات خاصة بكل نوع تصدير
EXPORT_TYPE_CONFIGS = {
    'sales_export': {
        'default_chunk_size': 2000,
        'include_items': True,
        'include_customer_info': True,
        'date_range_required': False,
        'max_records': 100000
    },
    'products_export': {
        'default_chunk_size': 1500,
        'include_analytics': True,
        'include_stock_info': True,
        'category_filter': True,
        'max_records': 50000
    },
    'customers_export': {
        'default_chunk_size': 1000,
        'include_debts': False,
        'include_purchase_history': False,
        'active_only': False,
        'max_records': 25000
    },
    'bulk_export': {
        'default_chunk_size': 500,
        'max_tables': 5,
        'compression_required': True,
        'separate_files': False,
        'max_total_records': 200000
    }
}

# إعدادات الأمان والحدود
SECURITY_LIMITS = {
    'admin': {
        'max_concurrent_exports': 10,
        'max_file_size_mb': 1000,
        'max_records_per_export': 1000000,
        'rate_limit_per_minute': 120
    },
    'cashier': {
        'max_concurrent_exports': 3,
        'max_file_size_mb': 100,
        'max_records_per_export': 50000,
        'rate_limit_per_minute': 30
    },
    'default': {
        'max_concurrent_exports': 2,
        'max_file_size_mb': 50,
        'max_records_per_export': 10000,
        'rate_limit_per_minute': 20
    }
}

# رسائل الخطأ المترجمة
ERROR_MESSAGES = {
    'file_too_large': 'حجم الملف كبير جداً. الحد الأقصى المسموح: {max_size}',
    'format_not_supported': 'تنسيق الملف غير مدعوم: {format}',
    'rate_limit_exceeded': 'تم تجاوز الحد المسموح للطلبات. حاول مرة أخرى بعد {retry_after} ثانية',
    'insufficient_permissions': 'ليس لديك صلاحية للوصول لهذه البيانات',
    'export_failed': 'فشل في تصدير البيانات: {error}',
    'task_not_found': 'المهمة غير موجودة أو انتهت صلاحيتها',
    'invalid_parameters': 'معاملات غير صحيحة: {details}',
    'database_error': 'خطأ في قاعدة البيانات. حاول مرة أخرى لاحقاً',
    'compression_failed': 'فشل في ضغط الملف',
    'cleanup_failed': 'فشل في تنظيف الملفات القديمة'
}

def get_user_limits(user_role: str) -> Dict[str, Any]:
    """الحصول على حدود المستخدم حسب دوره"""
    return SECURITY_LIMITS.get(user_role, SECURITY_LIMITS['default'])

def get_export_config(export_type: str) -> Dict[str, Any]:
    """الحصول على إعدادات نوع التصدير"""
    return EXPORT_TYPE_CONFIGS.get(export_type, {})

def validate_export_request(
    export_type: str,
    user_role: str,
    parameters: Dict[str, Any]
) -> tuple[bool, str]:
    """التحقق من صحة طلب التصدير"""
    
    # فحص نوع التصدير
    if export_type not in EXPORT_TYPE_CONFIGS:
        return False, f"نوع التصدير غير مدعوم: {export_type}"
    
    # فحص التنسيق
    format_type = parameters.get('format_type', streaming_config.DEFAULT_FORMAT)
    if not streaming_config.is_format_allowed(format_type):
        return False, ERROR_MESSAGES['format_not_supported'].format(format=format_type)
    
    # فحص حدود المستخدم
    user_limits = get_user_limits(user_role)
    export_config = get_export_config(export_type)

    # فحص حجم الدفعة
    chunk_size = parameters.get('chunk_size', export_config.get('default_chunk_size', streaming_config.DEFAULT_CHUNK_SIZE))
    if chunk_size > streaming_config.MAX_CHUNK_SIZE:
        return False, f"حجم الدفعة كبير جداً. الحد الأقصى: {streaming_config.MAX_CHUNK_SIZE}"

    # فحص حدود المستخدم (يمكن إضافة المزيد من الفحوصات هنا)
    max_file_size = user_limits.get('max_file_size_mb', streaming_config.MAX_FILE_SIZE_MB)
    if max_file_size < streaming_config.MAX_FILE_SIZE_MB:
        # يمكن إضافة فحص إضافي هنا إذا لزم الأمر
        pass
    
    return True, "صحيح"
