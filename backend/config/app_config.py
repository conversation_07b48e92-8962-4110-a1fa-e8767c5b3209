"""
إعدادات التطبيق المركزية - SmartPOS
ملف تكوين شامل لجميع إعدادات النظام
"""

import os
from typing import Dict, Any, List, Optional
from pathlib import Path

class AppConfig:
    """
    كلاس إعدادات التطبيق المركزية
    يحتوي على جميع الإعدادات والثوابت المستخدمة في النظام
    """
    
    # معلومات التطبيق الأساسية
    APP_NAME = "SmartPOS"
    APP_VERSION = "2.0.0"
    APP_DESCRIPTION = "نظام نقاط البيع الذكي"
    
    # مسارات المشروع
    BASE_DIR = Path(__file__).parent.parent
    STATIC_DIR = BASE_DIR / "static"
    TEMPLATES_DIR = BASE_DIR / "templates"
    UPLOADS_DIR = BASE_DIR / "uploads"
    BACKUPS_DIR = BASE_DIR / "backups"
    LOGS_DIR = BASE_DIR / "logs"
    
    # إعدادات قاعدة البيانات
    DATABASE_URL = "postgresql+psycopg2://postgres:password@localhost:5432/smartpos_db"
    DATABASE_ECHO = False  # تفعيل لرؤية استعلامات SQL
    DATABASE_POOL_SIZE = 20  # زيادة حجم التجميع لـ PostgreSQL
    DATABASE_MAX_OVERFLOW = 30  # زيادة الحد الأقصى للاتصالات الإضافية
    
    # إعدادات الخادم
    HOST = "0.0.0.0"
    PORT = 8002
    DEBUG = False
    RELOAD = False
    
    # إعدادات الأمان
    SECRET_KEY = os.getenv("SECRET_KEY", "smartpos-secret-key-2024")
    ACCESS_TOKEN_EXPIRE_MINUTES = 30
    ALGORITHM = "HS256"
    
    # إعدادات CORS
    CORS_ORIGINS = [
        "http://localhost:3000",
        "http://localhost:5173",
        "http://localhost:5175",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5173",
        "http://127.0.0.1:5175",
    ]
    
    # إعدادات الشبكة
    NETWORK_TIMEOUT = 30
    MAX_RETRIES = 3
    RETRY_DELAY = 1
    
    # إعدادات الأمان المتقدمة
    SECURITY_CONFIG = {
        "device_approval_required": True,
        "max_failed_attempts": 5,
        "lockout_duration": 300,  # 5 دقائق
        "session_timeout": 1800,  # 30 دقيقة
        "password_min_length": 8,
        "require_strong_password": True
    }
    
    # إعدادات middleware الأمان
    SECURITY_MIDDLEWARE_CONFIG = {
        "cache_ttl": 300,  # 5 دقائق
        "throttle_seconds": 30,
        "rejection_cooldown": 600,  # 10 دقائق
        "max_rejections_per_hour": 5,
        "suspicious_threshold": 10,
        "excluded_paths": {
            "/static", "/favicon", "/ws/", "/_next", "/assets", 
            "/images", "/css", "/js", "/fonts", "/icons", 
            "/api/system/health", "/api/system/performance", 
            "/docs", "/redoc", "/openapi.json"
        },
        "auth_endpoints": {
            "/api/auth/login", "/api/auth/logout", "/api/auth/verify",
            "/api/settings/update-device-user", "/api/device/approve",
            "/api/settings/", "/api/admin/", "/api/backup/", "/api/restore/"
        }
    }
    
    # إعدادات الأداء
    PERFORMANCE_CONFIG = {
        "max_concurrent_requests": 100,
        "rate_limit_window": 60,
        "max_requests_per_window": 1000,
        "slow_request_threshold": 5.0,
        "memory_threshold": 80,
        "cpu_threshold": 70,
        "cache_size": 1000,
        "cleanup_interval": 3600  # ساعة واحدة
    }
    
    # إعدادات التسجيل
    LOGGING_CONFIG = {
        "level": "INFO",
        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        "max_file_size": 10 * 1024 * 1024,  # 10 MB
        "backup_count": 5,
        "log_to_file": True,
        "log_to_console": True
    }
    
    # إعدادات النسخ الاحتياطية
    BACKUP_CONFIG = {
        "auto_backup": True,
        "backup_interval": 6,  # ساعات
        "max_backups": 10,
        "compress_backups": True,
        "backup_on_startup": False,
        "include_logs": False
    }
    
    # إعدادات Google Drive
    GOOGLE_DRIVE_CONFIG = {
        "enabled": False,
        "credentials_file": "credentials.json",
        "token_file": "token.json",
        "folder_name": "SmartPOS_Backups",
        "auto_upload": False,
        "max_cloud_backups": 5
    }
    
    # إعدادات البريد الإلكتروني
    EMAIL_CONFIG = {
        "enabled": False,
        "smtp_server": "",
        "smtp_port": 587,
        "username": "",
        "password": "",
        "use_tls": True,
        "from_email": "",
        "support_email": "<EMAIL>"
    }
    
    # إعدادات الطباعة
    PRINTING_CONFIG = {
        "default_printer": "",
        "thermal_printer": True,
        "paper_width": 80,  # mm
        "auto_cut": True,
        "print_logo": True,
        "print_qr": True
    }
    
    # إعدادات التقارير
    REPORTS_CONFIG = {
        "default_period": "month",
        "max_export_rows": 10000,
        "cache_reports": True,
        "cache_duration": 300,  # 5 دقائق
        "export_formats": ["pdf", "excel", "csv"]
    }
    
    # إعدادات المهام المجدولة
    SCHEDULER_CONFIG = {
        "enabled": True,
        "max_workers": 4,
        "job_defaults": {
            "coalesce": False,
            "max_instances": 1,
            "misfire_grace_time": 30
        },
        "timezone": "Asia/Riyadh"
    }
    
    @classmethod
    def get_config(cls, section: Optional[str] = None) -> Dict[str, Any]:
        """
        الحصول على إعدادات قسم معين أو جميع الإعدادات
        """
        if section:
            return getattr(cls, f"{section.upper()}_CONFIG", {})
        
        return {
            "app": {
                "name": cls.APP_NAME,
                "version": cls.APP_VERSION,
                "description": cls.APP_DESCRIPTION
            },
            "database": {
                "url": cls.DATABASE_URL,
                "echo": cls.DATABASE_ECHO
            },
            "server": {
                "host": cls.HOST,
                "port": cls.PORT,
                "debug": cls.DEBUG
            },
            "security": cls.SECURITY_CONFIG,
            "performance": cls.PERFORMANCE_CONFIG,
            "logging": cls.LOGGING_CONFIG,
            "backup": cls.BACKUP_CONFIG,
            "google_drive": cls.GOOGLE_DRIVE_CONFIG,
            "email": cls.EMAIL_CONFIG,
            "printing": cls.PRINTING_CONFIG,
            "reports": cls.REPORTS_CONFIG,
            "scheduler": cls.SCHEDULER_CONFIG
        }
    
    def update_config(self, section: str, key: str, value: Any) -> bool:
        """
        تحديث إعداد معين
        """
        try:
            config_attr = f"{section.upper()}_CONFIG"
            if hasattr(self.__class__, config_attr):
                config = getattr(self.__class__, config_attr)
                config[key] = value
                return True
            return False
        except Exception:
            return False
    
    @classmethod
    def create_directories(cls):
        """
        إنشاء المجلدات المطلوبة
        """
        directories = [
            cls.STATIC_DIR,
            cls.UPLOADS_DIR,
            cls.BACKUPS_DIR,
            cls.LOGS_DIR
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def validate_config(cls) -> List[str]:
        """
        التحقق من صحة الإعدادات
        """
        errors = []
        
        # فحص المجلدات المطلوبة
        if not cls.BASE_DIR.exists():
            errors.append("مجلد المشروع الأساسي غير موجود")
        
        # فحص إعدادات قاعدة البيانات
        if not cls.DATABASE_URL:
            errors.append("رابط قاعدة البيانات غير محدد")
        
        # فحص إعدادات الخادم
        if not (1 <= cls.PORT <= 65535):
            errors.append("رقم المنفذ غير صحيح")
        
        return errors


# إنشاء instance عامة للاستخدام
app_config = AppConfig()

# إنشاء المجلدات المطلوبة عند الاستيراد
app_config.create_directories()
