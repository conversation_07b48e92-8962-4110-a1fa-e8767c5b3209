#!/usr/bin/env python3
"""
سكربت ترحيل قاعدة البيانات من SQLite إلى PostgreSQL
يستخدم خدمة DatabaseMigrationService المحسنة
"""

import sys
import os
import logging
from pathlib import Path

# إضافة مسار backend إلى Python path
backend_path = Path(__file__).parent
sys.path.insert(0, str(backend_path))

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('migration.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def main():
    """تنفيذ عملية الترحيل الرئيسية"""
    try:
        print("=" * 60)
        print("🚀 بدء عملية ترحيل قاعدة البيانات من SQLite إلى PostgreSQL")
        print("=" * 60)
        
        # استيراد خدمة الترحيل
        from services.database_migration_service import DatabaseMigrationService
        
        # الحصول على instance من الخدمة
        migration_service = DatabaseMigrationService.getInstance()
        
        # تنفيذ عملية الترحيل
        print("\n📋 بدء عملية الترحيل...")
        result = migration_service.migrate_database()
        
        # عرض النتائج
        print("\n" + "=" * 60)
        if result["success"]:
            print("✅ تم ترحيل قاعدة البيانات بنجاح!")
            print(f"📊 الإحصائيات:")
            stats = result["stats"]
            print(f"   • الجداول المرحلة: {stats['tables_migrated']}")
            print(f"   • إجمالي الصفوف: {stats['total_rows_migrated']}")
            print(f"   • المدة: {stats['duration_seconds']:.2f} ثانية")
            
            if stats["errors"]:
                print(f"\n⚠️ تحذيرات ({len(stats['errors'])}):")
                for error in stats["errors"]:
                    print(f"   • {error}")
        else:
            print("❌ فشل في ترحيل قاعدة البيانات!")
            print(f"💬 الرسالة: {result['message']}")
            
            if result["stats"]["errors"]:
                print(f"\n🔍 الأخطاء:")
                for error in result["stats"]["errors"]:
                    print(f"   • {error}")
        
        print("=" * 60)
        
        # التحقق من صحة الترحيل
        if result["success"]:
            print("\n🔍 التحقق من صحة الترحيل...")
            verification = migration_service.verify_migration()
            
            if verification["success"]:
                print("✅ تم التحقق من صحة الترحيل بنجاح!")
                print(f"📈 نسبة التطابق: {verification['match_percentage']:.1f}%")
            else:
                print("⚠️ تحذير: هناك مشاكل في التحقق من الترحيل")
                print(f"💬 الرسالة: {verification['message']}")
                
                if "details" in verification:
                    print("\n📋 تفاصيل التحقق:")
                    for table, details in verification["details"].items():
                        if not details.get("match", False):
                            print(f"   ❌ {table}: {details}")
        
        return 0 if result["success"] else 1
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الوحدات: {e}")
        print("💡 تأكد من تشغيل السكربت من مجلد backend")
        return 1
        
    except Exception as e:
        logger.error(f"خطأ عام في عملية الترحيل: {e}")
        print(f"❌ خطأ غير متوقع: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
