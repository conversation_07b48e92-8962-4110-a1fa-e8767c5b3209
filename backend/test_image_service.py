#!/usr/bin/env python3
"""
اختبار بسيط لخدمة إدارة الصور
"""

import os
import sys
from pathlib import Path

# إضافة مسار المشروع إلى Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_image_service():
    """اختبار أساسي لخدمة إدارة الصور"""
    
    print("🧪 بدء اختبار خدمة إدارة الصور...")
    
    try:
        # استيراد الخدمة
        from services.image_management_service import ImageManagementService
        
        print("✅ تم استيراد الخدمة بنجاح")
        
        # إنشاء مثيل من الخدمة
        service = ImageManagementService.get_instance()
        print("✅ تم إنشاء مثيل من الخدمة")
        
        # اختبار الحصول على الصيغ المدعومة
        formats = service.get_supported_formats()
        if formats["success"]:
            print(f"✅ الصيغ المدعومة: {formats['supported_extensions']}")
            print(f"✅ الحد الأقصى لحجم الملف: {formats['max_file_size_formatted']}")
        else:
            print(f"❌ فشل في الحصول على الصيغ المدعومة: {formats.get('error')}")
        
        # اختبار إحصائيات التخزين
        stats = service.get_storage_statistics()
        if stats["success"]:
            print(f"✅ إجمالي الملفات: {stats['total_files']}")
            print(f"✅ حجم التخزين: {stats['total_size_formatted']}")
            print(f"✅ عدد المجلدات: {len(stats['folders']) if stats['folders'] else 0}")
        else:
            print(f"❌ فشل في الحصول على إحصائيات التخزين: {stats.get('error')}")
        
        # التحقق من وجود المجلدات
        base_dir = Path(service.BASE_UPLOAD_DIR)
        folders = ['products', 'categories', 'brands', 'users', 'general']
        
        print("\n📁 فحص المجلدات:")
        for folder in folders:
            folder_path = base_dir / folder
            if folder_path.exists():
                print(f"✅ مجلد {folder} موجود")
                
                # فحص مجلدات الصور المصغرة
                for size in service.THUMBNAIL_SIZES.keys():
                    thumbnail_path = folder_path / 'thumbnails' / size
                    if thumbnail_path.exists():
                        print(f"  ✅ مجلد الصور المصغرة {size} موجود")
                    else:
                        print(f"  ⚠️ مجلد الصور المصغرة {size} غير موجود")
            else:
                print(f"❌ مجلد {folder} غير موجود")
        
        # اختبار تنظيف الملفات المهجورة
        print("\n🧹 اختبار تنظيف الملفات المهجورة:")
        for folder in folders:
            cleanup_result = service.cleanup_orphaned_files(folder)
            if cleanup_result["success"]:
                print(f"✅ تنظيف مجلد {folder}: {cleanup_result['total_cleaned']} ملف تم تنظيفه")
            else:
                print(f"❌ فشل في تنظيف مجلد {folder}: {cleanup_result.get('error')}")
        
        print("\n🎉 اكتمل اختبار الخدمة بنجاح!")
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("تأكد من تثبيت جميع المتطلبات (FastAPI, Pillow, SQLAlchemy)")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def create_directory_structure():
    """إنشاء هيكل المجلدات المطلوب"""
    
    print("📁 إنشاء هيكل المجلدات...")
    
    base_dir = Path("static/uploads")
    folders = ['products', 'categories', 'brands', 'users', 'general']
    thumbnail_sizes = ['small', 'medium', 'large']
    
    try:
        for folder in folders:
            folder_path = base_dir / folder
            folder_path.mkdir(parents=True, exist_ok=True)
            print(f"✅ تم إنشاء مجلد: {folder_path}")
            
            # إنشاء مجلدات الصور المصغرة
            for size in thumbnail_sizes:
                thumbnail_path = folder_path / 'thumbnails' / size
                thumbnail_path.mkdir(parents=True, exist_ok=True)
                print(f"  ✅ تم إنشاء مجلد الصور المصغرة: {thumbnail_path}")
        
        print("✅ تم إنشاء جميع المجلدات بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المجلدات: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🖼️ اختبار خدمة إدارة الصور - SmartPOS")
    print("=" * 60)
    
    # إنشاء هيكل المجلدات أولاً
    if create_directory_structure():
        print("\n" + "=" * 60)
        
        # تشغيل اختبار الخدمة
        success = test_image_service()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 جميع الاختبارات نجحت!")
        else:
            print("❌ فشل في بعض الاختبارات")
        print("=" * 60)
    else:
        print("❌ فشل في إنشاء هيكل المجلدات")
