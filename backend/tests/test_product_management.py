"""
اختبارات خدمات إدارة المنتجات الجديدة
"""

import pytest
from sqlalchemy.orm import Session
from datetime import datetime, date

from services.barcode_service import BarcodeService
from services.slug_service import SlugService
from services.product_validation_service import ProductValidationService
from services.product_management_service import ProductManagementService
from models.product import Product
from models.category import Category
from models.brand import Brand
from models.unit import Unit
from models.warehouse import Warehouse


class TestBarcodeService:
    """اختبارات خدمة الباركود"""
    
    def test_generate_sku(self, db_session: Session):
        """اختبار توليد SKU"""
        service = BarcodeService.get_instance(db_session)
        sku = service.generate_sku()
        
        assert sku.startswith('SKU')
        assert len(sku) > 10
        
        # اختبار مع prefix مخصص
        custom_sku = service.generate_sku('PROD')
        assert custom_sku.startswith('PROD')
    
    def test_generate_barcode(self, db_session: Session):
        """اختبار توليد الباركود"""
        service = BarcodeService.get_instance(db_session)
        
        # اختبار Code 128
        barcode = service.generate_barcode('CODE128', 12)
        assert len(barcode) == 12
        
        # اختبار EAN-13
        ean13 = service.generate_barcode('EAN_13')
        assert len(ean13) == 13
        assert ean13.isdigit()
        
        # اختبار UPC-A
        upca = service.generate_barcode('UPC_A')
        assert len(upca) == 12
        assert upca.isdigit()
    
    def test_validate_barcode(self, db_session: Session):
        """اختبار التحقق من صحة الباركود"""
        service = BarcodeService.get_instance(db_session)
        
        # باركود صحيح
        valid_result = service.validate_barcode('123456789012', 'UPC_A')
        assert valid_result['valid'] == True
        
        # باركود غير صحيح - طول خاطئ
        invalid_result = service.validate_barcode('12345', 'UPC_A')
        assert invalid_result['valid'] == False
        
        # باركود غير صحيح - أحرف في رقمي
        invalid_chars = service.validate_barcode('12345678901A', 'UPC_A')
        assert invalid_chars['valid'] == False
    
    def test_get_supported_symbologies(self, db_session: Session):
        """اختبار جلب أنواع الباركود المدعومة"""
        service = BarcodeService.get_instance(db_session)
        symbologies = service.get_supported_symbologies()
        
        assert len(symbologies) > 0
        assert any(s['value'] == 'CODE128' for s in symbologies)
        assert any(s['value'] == 'EAN_13' for s in symbologies)


class TestSlugService:
    """اختبارات خدمة الروابط"""
    
    def test_create_slug_arabic(self, db_session: Session):
        """اختبار إنشاء رابط من النص العربي"""
        service = SlugService.get_instance(db_session)
        
        # نص عربي
        arabic_slug = service.create_slug('منتج جديد رائع')
        assert 'muntaj' in arabic_slug.lower() or 'jadid' in arabic_slug.lower()
        
        # نص مختلط
        mixed_slug = service.create_slug('منتج Product جديد')
        assert len(mixed_slug) > 0
        assert not mixed_slug.startswith('-')
        assert not mixed_slug.endswith('-')
    
    def test_create_slug_english(self, db_session: Session):
        """اختبار إنشاء رابط من النص الإنجليزي"""
        service = SlugService.get_instance(db_session)
        
        slug = service.create_slug('Amazing New Product')
        assert slug == 'amazing-new-product'
        
        # مع أحرف خاصة
        special_slug = service.create_slug('Product & Service (2024)!')
        assert '&' not in special_slug
        assert '(' not in special_slug
        assert ')' not in special_slug
        assert '!' not in special_slug
    
    def test_validate_slug(self, db_session: Session):
        """اختبار التحقق من صحة الرابط"""
        service = SlugService.get_instance(db_session)
        
        # رابط صحيح
        valid_result = service.validate_slug('valid-product-slug')
        assert valid_result['valid'] == True
        
        # رابط قصير جداً
        short_result = service.validate_slug('ab')
        assert short_result['valid'] == False
        
        # رابط يبدأ بشرطة
        dash_start = service.validate_slug('-invalid-slug')
        assert dash_start['valid'] == False
        
        # رابط ينتهي بشرطة
        dash_end = service.validate_slug('invalid-slug-')
        assert dash_end['valid'] == False
    
    def test_suggest_slugs(self, db_session: Session):
        """اختبار اقتراح الروابط"""
        service = SlugService.get_instance(db_session)
        
        suggestions = service.suggest_slugs('منتج رائع', count=3)
        assert len(suggestions) == 3
        assert all(isinstance(s, str) for s in suggestions)


class TestProductValidationService:
    """اختبارات خدمة التحقق من صحة بيانات المنتج"""
    
    def test_validate_text_field(self, db_session: Session):
        """اختبار التحقق من الحقول النصية"""
        service = ProductValidationService.get_instance(db_session)
        
        # نص صحيح
        valid_result = service.validate_text_field('منتج رائع', 'اسم المنتج', {
            'required': True,
            'min_length': 2,
            'max_length': 100
        })
        assert valid_result['valid'] == True
        
        # نص فارغ مع الإجبارية
        empty_result = service.validate_text_field('', 'اسم المنتج', {
            'required': True,
            'min_length': 2
        })
        assert empty_result['valid'] == False
        
        # نص قصير جداً
        short_result = service.validate_text_field('ا', 'اسم المنتج', {
            'min_length': 2
        })
        assert short_result['valid'] == False
    
    def test_validate_numeric_field(self, db_session: Session):
        """اختبار التحقق من الحقول الرقمية"""
        service = ProductValidationService.get_instance(db_session)
        
        # رقم صحيح
        valid_result = service.validate_numeric_field(100.50, 'السعر', {
            'required': True,
            'min_value': 0.01,
            'max_value': 999999.99,
            'decimal_places': 2
        })
        assert valid_result['valid'] == True
        assert valid_result['value'] == 100.50
        
        # رقم سالب
        negative_result = service.validate_numeric_field(-10, 'السعر', {
            'min_value': 0
        })
        assert negative_result['valid'] == False
        
        # رقم كبير جداً
        large_result = service.validate_numeric_field(9999999, 'السعر', {
            'max_value': 999999
        })
        assert large_result['valid'] == False
    
    def test_validate_date_field(self, db_session: Session):
        """اختبار التحقق من حقول التاريخ"""
        service = ProductValidationService.get_instance(db_session)
        
        # تاريخ صحيح
        valid_result = service.validate_date_field('2024-01-15', 'تاريخ التصنيع')
        assert valid_result['valid'] == True
        assert isinstance(valid_result['value'], date)
        
        # تاريخ غير صحيح
        invalid_result = service.validate_date_field('invalid-date', 'تاريخ التصنيع')
        assert invalid_result['valid'] == False


class TestProductManagementService:
    """اختبارات خدمة إدارة المنتجات"""
    
    @pytest.fixture
    def sample_warehouse(self, db_session: Session):
        """إنشاء مستودع للاختبار"""
        warehouse = Warehouse(
            name='مستودع الاختبار',
            location='موقع الاختبار',
            is_active=True
        )
        db_session.add(warehouse)
        db_session.commit()
        return warehouse
    
    @pytest.fixture
    def sample_category(self, db_session: Session):
        """إنشاء فئة للاختبار"""
        category = Category(
            name='فئة الاختبار',
            is_active=True
        )
        db_session.add(category)
        db_session.commit()
        return category
    
    def test_create_product(self, db_session: Session, sample_warehouse, sample_category):
        """اختبار إنشاء منتج جديد"""
        service = ProductManagementService.get_instance(db_session)
        
        product_data = {
            'name': 'منتج اختبار',
            'description': 'وصف منتج الاختبار',
            'price': 100.00,
            'cost_price': 80.00,
            'quantity': 50,
            'min_quantity': 10,
            'warehouse_id': sample_warehouse.id,
            'category_id': sample_category.id,
            'is_active': True
        }
        
        result = service.create_product(product_data, user_id=1)
        
        assert result['success'] == True
        assert result['product'].name == 'منتج اختبار'
        assert result['product'].price == 100.00
        assert result['product'].warehouse_id == sample_warehouse.id
    
    def test_update_product(self, db_session: Session, sample_warehouse):
        """اختبار تحديث منتج موجود"""
        service = ProductManagementService.get_instance(db_session)
        
        # إنشاء منتج أولاً
        product = Product(
            name='منتج للتحديث',
            price=50.00,
            cost_price=40.00,
            quantity=20,
            min_quantity=5,
            warehouse_id=sample_warehouse.id,
            created_by=1
        )
        db_session.add(product)
        db_session.commit()
        
        # تحديث المنتج
        update_data = {
            'name': 'منتج محدث',
            'price': 60.00,
            'quantity': 30
        }
        
        result = service.update_product(product.id, update_data, user_id=1)
        
        assert result['success'] == True
        assert result['product'].name == 'منتج محدث'
        assert result['product'].price == 60.00
        assert result['product'].quantity == 30
    
    def test_delete_product(self, db_session: Session, sample_warehouse):
        """اختبار حذف منتج"""
        service = ProductManagementService.get_instance(db_session)
        
        # إنشاء منتج للحذف
        product = Product(
            name='منتج للحذف',
            price=25.00,
            cost_price=20.00,
            quantity=10,
            min_quantity=2,
            warehouse_id=sample_warehouse.id,
            created_by=1
        )
        db_session.add(product)
        db_session.commit()
        product_id = product.id
        
        # حذف المنتج
        result = service.delete_product(product_id, user_id=1)
        
        assert result['success'] == True
        
        # التأكد من الحذف
        deleted_product = db_session.query(Product).filter(Product.id == product_id).first()
        assert deleted_product is None
    
    def test_search_products(self, db_session: Session, sample_warehouse):
        """اختبار البحث في المنتجات"""
        service = ProductManagementService.get_instance(db_session)
        
        # إنشاء منتجات للبحث
        products = [
            Product(name='منتج البحث الأول', price=100, cost_price=80, quantity=50, warehouse_id=sample_warehouse.id, created_by=1),
            Product(name='منتج البحث الثاني', price=200, cost_price=160, quantity=30, warehouse_id=sample_warehouse.id, created_by=1),
            Product(name='منتج مختلف', price=150, cost_price=120, quantity=20, warehouse_id=sample_warehouse.id, created_by=1)
        ]
        
        for product in products:
            db_session.add(product)
        db_session.commit()
        
        # البحث بالاسم
        search_result = service.search_products({'search': 'البحث'}, page=1, limit=10)
        
        assert search_result['success'] == True
        assert len(search_result['products']) == 2
        assert search_result['pagination']['total_count'] == 2
    
    def test_get_product_statistics(self, db_session: Session, sample_warehouse):
        """اختبار جلب إحصائيات المنتجات"""
        service = ProductManagementService.get_instance(db_session)
        
        # إنشاء منتجات للإحصائيات
        products = [
            Product(name='منتج نشط 1', price=100, cost_price=80, quantity=50, is_active=True, warehouse_id=sample_warehouse.id, created_by=1),
            Product(name='منتج نشط 2', price=200, cost_price=160, quantity=0, is_active=True, warehouse_id=sample_warehouse.id, created_by=1),
            Product(name='منتج غير نشط', price=150, cost_price=120, quantity=20, is_active=False, warehouse_id=sample_warehouse.id, created_by=1)
        ]
        
        for product in products:
            db_session.add(product)
        db_session.commit()
        
        result = service.get_product_statistics()
        
        assert result['success'] == True
        stats = result['statistics']
        assert stats['total_products'] >= 3
        assert stats['active_products'] >= 2
        assert stats['out_of_stock_products'] >= 1
        assert stats['total_stock_value'] > 0
