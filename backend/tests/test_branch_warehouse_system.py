"""
اختبارات شاملة لنظام الفروع والمستودعات
تطبق مبادئ البرمجة الكائنية مع اختبارات متقدمة
"""

import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# إضافة مسار المشروع للاستيراد
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.session import DATABASE_URL
from services.branch_service import get_branch_service
from services.branch_warehouse_service import get_branch_warehouse_service
from services.warehouse_service import get_warehouse_service
from services.smart_distribution_service import get_smart_distribution_service
from services.transfer_request_service import get_transfer_request_service
from services.advanced_reports_service import get_advanced_reports_service
# from utils.datetime_utils import get_tripoli_now  # غير مستخدم حالياً

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BranchWarehouseSystemTester:
    """
    كلاس اختبار نظام الفروع والمستودعات
    """
    
    def __init__(self):
        """تهيئة اختبار النظام"""
        self.engine = create_engine(DATABASE_URL)
        self.SessionLocal = sessionmaker(bind=self.engine)
        self.test_results = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'test_details': []
        }
        
    def run_all_tests(self) -> Dict[str, Any]:
        """
        تشغيل جميع الاختبارات
        
        Returns:
            نتائج الاختبارات
        """
        try:
            logger.info("🧪 بدء اختبارات نظام الفروع والمستودعات")
            
            # 1. اختبار الخدمات الأساسية
            self._test_basic_services()
            
            # 2. اختبار العلاقات
            self._test_relationships()
            
            # 3. اختبار التوزيع الذكي
            self._test_smart_distribution()
            
            # 4. اختبار التحويلات
            self._test_transfers()
            
            # 5. اختبار التقارير
            self._test_reports()
            
            # 6. اختبار سلامة البيانات
            self._test_data_integrity()
            
            # حساب النتائج النهائية
            success_rate = (self.test_results['passed_tests'] / max(1, self.test_results['total_tests'])) * 100
            
            logger.info(f"✅ اكتملت الاختبارات - معدل النجاح: {success_rate:.2f}%")
            
            return {
                'success': True,
                'test_results': self.test_results,
                'success_rate': round(success_rate, 2),
                'summary': f"نجح {self.test_results['passed_tests']} من {self.test_results['total_tests']} اختبار"
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في تشغيل الاختبارات: {e}")
            return {
                'success': False,
                'error': f'خطأ في تشغيل الاختبارات: {str(e)}',
                'test_results': self.test_results
            }
    
    def _test_basic_services(self):
        """اختبار الخدمات الأساسية"""
        logger.info("🔧 اختبار الخدمات الأساسية...")
        
        with self.SessionLocal() as db:
            # اختبار خدمة الفروع
            self._run_test("Branch Service Initialization", lambda: get_branch_service(db))
            
            # اختبار خدمة العلاقات
            self._run_test("Branch-Warehouse Service Initialization", lambda: get_branch_warehouse_service(db))
            
            # اختبار خدمة المستودعات
            self._run_test("Warehouse Service Initialization", lambda: get_warehouse_service(db))
            
            # اختبار خدمة التوزيع الذكي
            self._run_test("Smart Distribution Service Initialization", lambda: get_smart_distribution_service(db))
            
            # اختبار خدمة التحويلات
            self._run_test("Transfer Request Service Initialization", lambda: get_transfer_request_service(db))
            
            # اختبار خدمة التقارير
            self._run_test("Advanced Reports Service Initialization", lambda: get_advanced_reports_service(db))
    
    def _test_relationships(self):
        """اختبار العلاقات بين الفروع والمستودعات"""
        logger.info("🔗 اختبار العلاقات...")
        
        with self.SessionLocal() as db:
            branch_service = get_branch_service(db)
            warehouse_service = get_warehouse_service(db)
            branch_warehouse_service = get_branch_warehouse_service(db)
            
            # إنشاء فرع تجريبي
            test_branch_data = {
                'name': 'فرع اختبار',
                'code': f'TEST-{datetime.now().strftime("%Y%m%d%H%M%S")}',
                'address': 'عنوان اختبار',
                'city': 'طرابلس',
                'is_active': True
            }
            
            def create_test_branch():
                result = branch_service.create_branch(test_branch_data)
                assert result['success'], f"فشل في إنشاء الفرع: {result.get('error')}"
                return result['branch']['id']
            
            branch_id = self._run_test("Create Test Branch", create_test_branch)
            
            if branch_id:
                # اختبار جلب الفروع
                def get_branches():
                    result = branch_service.get_all_branches()
                    assert result['success'], f"فشل في جلب الفروع: {result.get('error')}"
                    assert len(result['branches']) > 0, "لا توجد فروع"
                    return True
                
                self._run_test("Get All Branches", get_branches)
                
                # اختبار جلب المستودعات
                def get_warehouses():
                    result = warehouse_service.get_all_warehouses()
                    assert result['success'], f"فشل في جلب المستودعات: {result.get('error')}"
                    return result['warehouses'][0]['id'] if result['warehouses'] else None
                
                warehouse_id = self._run_test("Get Warehouses", get_warehouses)
                
                if warehouse_id:
                    # اختبار ربط الفرع بالمستودع
                    def link_branch_warehouse():
                        result = branch_warehouse_service.link_branch_to_warehouse(
                            branch_id, warehouse_id, is_primary=True, priority=1
                        )
                        assert result['success'], f"فشل في ربط الفرع بالمستودع: {result.get('error')}"
                        return True
                    
                    self._run_test("Link Branch to Warehouse", link_branch_warehouse)
                    
                    # اختبار جلب مستودعات الفرع
                    def get_branch_warehouses():
                        result = branch_warehouse_service.get_warehouses_for_branch(branch_id)
                        assert result['success'], f"فشل في جلب مستودعات الفرع: {result.get('error')}"
                        assert len(result['warehouses']) > 0, "لا توجد مستودعات مرتبطة"
                        return True
                    
                    self._run_test("Get Branch Warehouses", get_branch_warehouses)
    
    def _test_smart_distribution(self):
        """اختبار التوزيع الذكي"""
        logger.info("🧠 اختبار التوزيع الذكي...")
        
        with self.SessionLocal() as db:
            smart_service = get_smart_distribution_service(db)
            
            # الحصول على فرع ومنتج للاختبار
            branch_result = db.execute(text("SELECT id FROM branches WHERE is_active = true LIMIT 1")).fetchone()
            product_result = db.execute(text("SELECT id FROM products LIMIT 1")).fetchone()
            
            if branch_result and product_result:
                branch_id = branch_result.id
                product_id = product_result.id
                
                def test_optimal_warehouse():
                    result = smart_service.find_optimal_warehouse_for_branch(
                        branch_id, product_id, 5.0
                    )
                    # قد ينجح أو يفشل حسب توفر المخزون
                    return result.get('success', False)
                
                self._run_test("Find Optimal Warehouse", test_optimal_warehouse)
                
                def test_multi_product_optimization():
                    products_requirements = [
                        {'product_id': product_id, 'quantity': 5.0}
                    ]
                    result = smart_service.optimize_multi_product_distribution(
                        branch_id, products_requirements
                    )
                    assert result['success'], f"فشل في تحسين التوزيع: {result.get('error')}"
                    return True
                
                self._run_test("Multi-Product Distribution Optimization", test_multi_product_optimization)
    
    def _test_transfers(self):
        """اختبار التحويلات"""
        logger.info("📦 اختبار التحويلات...")
        
        with self.SessionLocal() as db:
            transfer_service = get_transfer_request_service(db)
            
            # الحصول على فروع للاختبار
            branches_result = db.execute(text("SELECT id FROM branches WHERE is_active = true LIMIT 2")).fetchall()
            
            if len(branches_result) >= 2:
                from_branch_id = branches_result[0].id
                to_branch_id = branches_result[1].id
                
                def test_branch_transfer_suggestions():
                    result = transfer_service.get_transfer_suggestions_for_branch(from_branch_id)
                    assert result['success'], f"فشل في جلب اقتراحات التحويل: {result.get('error')}"
                    return True
                
                self._run_test("Get Transfer Suggestions", test_branch_transfer_suggestions)
                
                def test_branch_transfer_history():
                    result = transfer_service.get_branch_transfer_history(from_branch_id)
                    assert result['success'], f"فشل في جلب تاريخ التحويلات: {result.get('error')}"
                    return True
                
                self._run_test("Get Branch Transfer History", test_branch_transfer_history)
    
    def _test_reports(self):
        """اختبار التقارير"""
        logger.info("📊 اختبار التقارير...")
        
        with self.SessionLocal() as db:
            reports_service = get_advanced_reports_service(db)
            
            def test_branch_performance_report():
                result = reports_service.generate_branch_performance_report()
                assert result['success'], f"فشل في إنشاء تقرير أداء الفروع: {result.get('error')}"
                return True
            
            self._run_test("Branch Performance Report", test_branch_performance_report)
            
            def test_warehouse_utilization_report():
                result = reports_service.generate_warehouse_utilization_report()
                assert result['success'], f"فشل في إنشاء تقرير استخدام المستودعات: {result.get('error')}"
                return True
            
            self._run_test("Warehouse Utilization Report", test_warehouse_utilization_report)
            
            def test_distribution_optimization_report():
                result = reports_service.generate_distribution_optimization_report()
                assert result['success'], f"فشل في إنشاء تقرير تحسين التوزيع: {result.get('error')}"
                return True
            
            self._run_test("Distribution Optimization Report", test_distribution_optimization_report)
    
    def _test_data_integrity(self):
        """اختبار سلامة البيانات"""
        logger.info("🔍 اختبار سلامة البيانات...")
        
        with self.SessionLocal() as db:
            def test_foreign_key_constraints():
                # التحقق من عدم وجود روابط معطلة
                orphaned_links = db.execute(text("""
                    SELECT COUNT(*) FROM branch_warehouses bw
                    LEFT JOIN branches b ON bw.branch_id = b.id
                    LEFT JOIN warehouses w ON bw.warehouse_id = w.id
                    WHERE b.id IS NULL OR w.id IS NULL
                """)).scalar()
                
                assert orphaned_links == 0, f"يوجد {orphaned_links} رابط معطل"
                return True
            
            self._run_test("Foreign Key Constraints", test_foreign_key_constraints)
            
            def test_unique_constraints():
                # التحقق من عدم تكرار أكواد الفروع
                duplicate_codes = db.execute(text("""
                    SELECT code, COUNT(*) as count
                    FROM branches
                    GROUP BY code
                    HAVING COUNT(*) > 1
                """)).fetchall()
                
                assert len(duplicate_codes) == 0, f"يوجد {len(duplicate_codes)} كود مكرر"
                return True
            
            self._run_test("Unique Constraints", test_unique_constraints)
            
            def test_business_rules():
                # التحقق من وجود فرع رئيسي واحد فقط
                main_branches = db.execute(text("""
                    SELECT COUNT(*) FROM branches WHERE is_main = true
                """)).scalar() or 0

                assert main_branches <= 1, f"يوجد {main_branches} فرع رئيسي (يجب أن يكون واحد فقط)"
                return True
            
            self._run_test("Business Rules", test_business_rules)
    
    def _run_test(self, test_name: str, test_function) -> Any:
        """
        تشغيل اختبار واحد
        
        Args:
            test_name: اسم الاختبار
            test_function: دالة الاختبار
            
        Returns:
            نتيجة الاختبار
        """
        self.test_results['total_tests'] += 1
        
        try:
            result = test_function()
            self.test_results['passed_tests'] += 1
            self.test_results['test_details'].append({
                'name': test_name,
                'status': 'PASSED',
                'message': 'نجح الاختبار'
            })
            logger.info(f"✅ {test_name}: نجح")
            return result
            
        except Exception as e:
            self.test_results['failed_tests'] += 1
            self.test_results['test_details'].append({
                'name': test_name,
                'status': 'FAILED',
                'message': str(e)
            })
            logger.error(f"❌ {test_name}: فشل - {e}")
            return None


def main():
    """
    الدالة الرئيسية لتشغيل الاختبارات
    """
    print("🧪 بدء اختبارات نظام الفروع والمستودعات")
    print("=" * 50)
    
    tester = BranchWarehouseSystemTester()
    results = tester.run_all_tests()
    
    print("\n📊 نتائج الاختبارات:")
    print(f"إجمالي الاختبارات: {results['test_results']['total_tests']}")
    print(f"الاختبارات الناجحة: {results['test_results']['passed_tests']}")
    print(f"الاختبارات الفاشلة: {results['test_results']['failed_tests']}")
    print(f"معدل النجاح: {results.get('success_rate', 0):.2f}%")
    
    if results['test_results']['failed_tests'] > 0:
        print("\n❌ الاختبارات الفاشلة:")
        for test in results['test_results']['test_details']:
            if test['status'] == 'FAILED':
                print(f"- {test['name']}: {test['message']}")
    
    if results.get('success_rate', 0) >= 80:
        print("\n✅ النظام يعمل بشكل جيد!")
    else:
        print("\n⚠️ النظام يحتاج إلى مراجعة وإصلاح")


if __name__ == "__main__":
    main()
