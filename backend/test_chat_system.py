#!/usr/bin/env python3
"""
اختبار بسيط لنظام المحادثة الفورية
يتحقق من أن جميع المكونات تعمل بشكل صحيح
"""

import asyncio
import requests
import json
import sys
import os
from pathlib import Path

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

# إعدادات الاختبار
BASE_URL = "http://localhost:8002"
TEST_USER_1 = {"username": "admin", "password": "admin123"}
TEST_USER_2 = {"username": "test_user", "password": "123456"}

class ChatSystemTester:
    def __init__(self):
        self.session = requests.Session()
        self.user1_token = None
        self.user2_token = None
        
    def test_server_health(self):
        """اختبار صحة الخادم"""
        print("🔍 اختبار صحة الخادم...")
        try:
            response = self.session.get(f"{BASE_URL}/api/system/health")
            if response.status_code == 200:
                print("✅ الخادم يعمل بشكل صحيح")
                return True
            else:
                print(f"❌ الخادم لا يعمل بشكل صحيح: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في الاتصال بالخادم: {e}")
            return False
    
    def login_user(self, username, password):
        """تسجيل دخول المستخدم"""
        print(f"🔐 تسجيل دخول المستخدم: {username}")
        try:
            response = self.session.post(f"{BASE_URL}/api/auth/token", data={
                "username": username,
                "password": password
            })
            
            if response.status_code == 200:
                data = response.json()
                token = data.get("access_token")
                print(f"✅ تم تسجيل دخول {username} بنجاح")
                return token
            else:
                print(f"❌ فشل في تسجيل دخول {username}: {response.status_code}")
                print(f"   الاستجابة: {response.text}")
                return None
        except Exception as e:
            print(f"❌ خطأ في تسجيل دخول {username}: {e}")
            return None
    
    def test_chat_endpoints(self, token, username):
        """اختبار endpoints المحادثة"""
        print(f"🧪 اختبار endpoints المحادثة للمستخدم: {username}")
        
        headers = {"Authorization": f"Bearer {token}"}
        
        # اختبار حالة المحادثة
        try:
            response = self.session.get(f"{BASE_URL}/api/chat/status", headers=headers)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ حالة المحادثة: متصل={data.get('is_online', False)}")
            else:
                print(f"❌ فشل في جلب حالة المحادثة: {response.status_code}")
        except Exception as e:
            print(f"❌ خطأ في اختبار حالة المحادثة: {e}")
        
        # اختبار جلب المحادثات
        try:
            response = self.session.get(f"{BASE_URL}/api/chat/conversations", headers=headers)
            if response.status_code == 200:
                conversations = response.json()
                print(f"✅ تم جلب {len(conversations)} محادثة")
            else:
                print(f"❌ فشل في جلب المحادثات: {response.status_code}")
        except Exception as e:
            print(f"❌ خطأ في جلب المحادثات: {e}")
        
        # اختبار عدد الرسائل غير المقروءة
        try:
            response = self.session.get(f"{BASE_URL}/api/chat/unread-count", headers=headers)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ عدد الرسائل غير المقروءة: {data.get('unread_count', 0)}")
            else:
                print(f"❌ فشل في جلب عدد الرسائل غير المقروءة: {response.status_code}")
        except Exception as e:
            print(f"❌ خطأ في جلب عدد الرسائل غير المقروءة: {e}")
    
    def test_send_message(self, sender_token, receiver_id, content):
        """اختبار إرسال رسالة"""
        print(f"📤 اختبار إرسال رسالة...")
        
        headers = {
            "Authorization": f"Bearer {sender_token}",
            "Content-Type": "application/json"
        }
        
        message_data = {
            "receiver_id": receiver_id,
            "content": content,
            "message_type": "text"
        }
        
        try:
            response = self.session.post(
                f"{BASE_URL}/api/chat/send", 
                headers=headers,
                json=message_data
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ تم إرسال الرسالة بنجاح: ID={data.get('id')}")
                return data.get('id')
            else:
                print(f"❌ فشل في إرسال الرسالة: {response.status_code}")
                print(f"   الاستجابة: {response.text}")
                return None
        except Exception as e:
            print(f"❌ خطأ في إرسال الرسالة: {e}")
            return None
    
    def test_get_messages(self, token, other_user_id):
        """اختبار جلب الرسائل"""
        print(f"📥 اختبار جلب الرسائل...")
        
        headers = {"Authorization": f"Bearer {token}"}
        
        try:
            response = self.session.get(
                f"{BASE_URL}/api/chat/messages/{other_user_id}",
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                messages = data.get('messages', [])
                print(f"✅ تم جلب {len(messages)} رسالة")
                return messages
            else:
                print(f"❌ فشل في جلب الرسائل: {response.status_code}")
                return []
        except Exception as e:
            print(f"❌ خطأ في جلب الرسائل: {e}")
            return []
    
    def test_database_tables(self):
        """اختبار وجود جداول قاعدة البيانات"""
        print("🗄️ اختبار جداول قاعدة البيانات...")
        
        import sqlite3
        db_path = Path(__file__).parent / "smartpos.db"
        
        if not db_path.exists():
            print("❌ ملف قاعدة البيانات غير موجود")
            return False
        
        try:
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # فحص الجداول المطلوبة
            required_tables = [
                'chat_messages',
                'chat_rooms', 
                'chat_room_members',
                'user_online_status'
            ]
            
            for table in required_tables:
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                if cursor.fetchone():
                    print(f"✅ جدول {table} موجود")
                else:
                    print(f"❌ جدول {table} غير موجود")
            
            # فحص أعمدة المستخدمين الجديدة
            cursor.execute("PRAGMA table_info(users)")
            columns = [col[1] for col in cursor.fetchall()]
            
            if 'is_online' in columns:
                print("✅ عمود is_online موجود في جدول المستخدمين")
            else:
                print("❌ عمود is_online غير موجود في جدول المستخدمين")
            
            if 'last_seen' in columns:
                print("✅ عمود last_seen موجود في جدول المستخدمين")
            else:
                print("❌ عمود last_seen غير موجود في جدول المستخدمين")
            
            conn.close()
            return True
            
        except Exception as e:
            print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
            return False
    
    def run_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 بدء اختبار نظام المحادثة الفورية")
        print("=" * 50)
        
        # اختبار قاعدة البيانات
        if not self.test_database_tables():
            print("❌ فشل في اختبار قاعدة البيانات")
            return False
        
        print()
        
        # اختبار صحة الخادم
        if not self.test_server_health():
            print("❌ فشل في اختبار صحة الخادم")
            return False
        
        print()
        
        # تسجيل دخول المستخدم الأول
        self.user1_token = self.login_user(TEST_USER_1["username"], TEST_USER_1["password"])
        if not self.user1_token:
            print("❌ فشل في تسجيل دخول المستخدم الأول")
            return False
        
        print()
        
        # اختبار endpoints المحادثة
        self.test_chat_endpoints(self.user1_token, TEST_USER_1["username"])
        
        print()
        print("=" * 50)
        print("🎉 تم إكمال اختبار نظام المحادثة الفورية")
        print("✅ النظام جاهز للاستخدام!")
        
        return True

def main():
    """الدالة الرئيسية"""
    tester = ChatSystemTester()
    success = tester.run_tests()
    
    if success:
        print("\n🎯 نصائح للاستخدام:")
        print("1. تأكد من تشغيل الخادم على المنفذ 8002")
        print("2. يمكنك الآن إضافة زر المحادثة إلى الواجهة الأمامية")
        print("3. اختبر WebSocket من خلال فتح المحادثة في المتصفح")
        sys.exit(0)
    else:
        print("\n❌ فشل في بعض الاختبارات")
        sys.exit(1)

if __name__ == "__main__":
    main()
