#!/bin/bash

# إعداد البيئة الافتراضية لمشروع SmartPOS
# Setup virtual environment for SmartPOS project

echo "🚀 إعداد البيئة الافتراضية لمشروع SmartPOS"
echo "🚀 Setting up virtual environment for SmartPOS project"
echo "=================================================="

# التحقق من وجود Python 3
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 غير مثبت. يرجى تثبيت Python 3 أولاً."
    echo "❌ Python 3 is not installed. Please install Python 3 first."
    exit 1
fi

echo "✅ Python 3 موجود: $(python3 --version)"

# حذف البيئة الافتراضية القديمة إذا كانت موجودة
if [ -d "venv" ]; then
    echo "🗑️  حذف البيئة الافتراضية القديمة..."
    echo "🗑️  Removing old virtual environment..."
    rm -rf venv
fi

# إنشاء بيئة افتراضية جديدة
echo "📦 إنشاء بيئة افتراضية جديدة..."
echo "📦 Creating new virtual environment..."
python3 -m venv venv

if [ $? -ne 0 ]; then
    echo "❌ فشل في إنشاء البيئة الافتراضية"
    echo "❌ Failed to create virtual environment"
    exit 1
fi

# تفعيل البيئة الافتراضية
echo "🔄 تفعيل البيئة الافتراضية..."
echo "🔄 Activating virtual environment..."
source venv/bin/activate

# تحديث pip
echo "⬆️  تحديث pip..."
echo "⬆️  Upgrading pip..."
pip install --upgrade pip

# تثبيت المتطلبات
echo "📥 تثبيت المتطلبات من requirements.txt..."
echo "📥 Installing requirements from requirements.txt..."
pip install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "❌ فشل في تثبيت المتطلبات"
    echo "❌ Failed to install requirements"
    exit 1
fi

echo ""
echo "🎉 تم إعداد البيئة الافتراضية بنجاح!"
echo "🎉 Virtual environment setup completed successfully!"
echo ""

# عرض معلومات البيئة
echo "📋 معلومات البيئة الافتراضية:"
echo "📋 Virtual environment information:"
echo "🐍 Python: $(which python)"
echo "📦 Pip: $(which pip)"
echo ""

# عرض إصدارات المكتبات المهمة
echo "📋 إصدارات المكتبات المثبتة:"
echo "📋 Installed package versions:"
python -c "
import sys
packages = ['fastapi', 'uvicorn', 'sqlalchemy', 'psycopg2']
for package in packages:
    try:
        module = __import__(package)
        if hasattr(module, '__version__'):
            print(f'  {package}: {module.__version__}')
        else:
            print(f'  {package}: مثبت / Installed')
    except ImportError:
        print(f'  {package}: غير مثبت / Not installed')
"

echo ""
echo "✅ البيئة الافتراضية جاهزة للاستخدام!"
echo "✅ Virtual environment is ready to use!"
echo ""
echo "💡 لتفعيل البيئة الافتراضية في المستقبل، استخدم:"
echo "💡 To activate the virtual environment in the future, use:"
echo "   source venv/bin/activate"
echo "   أو / or: ./activate_venv.sh"
echo ""
echo "🚀 لتشغيل الخادم:"
echo "🚀 To run the server:"
echo "   uvicorn main:app --reload --host 0.0.0.0 --port 8000"
