"""
نموذج تتبع مراحل حالات مطالبات الضمان
يحتوي على سجل كامل لجميع تغييرات حالات المطالبات
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from database.base import Base


class ClaimStatusHistory(Base):
    """
    نموذج تتبع مراحل حالات مطالبات الضمان
    يسجل كل تغيير في حالة المطالبة مع التفاصيل الكاملة
    """
    __tablename__ = "claim_status_history"

    id = Column(Integer, primary_key=True, index=True)
    claim_id = Column(Integer, ForeignKey("warranty_claims.id"), nullable=False, index=True)
    
    # معلومات الحالة
    from_status = Column(String(20), nullable=True, index=True)  # الحالة السابقة (null للحالة الأولى)
    to_status = Column(String(20), nullable=False, index=True)   # الحالة الجديدة
    
    # معلومات التغيير
    changed_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    changed_by = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # تفاصيل التغيير
    reason = Column(Text, nullable=True)  # سبب التغيير
    notes = Column(Text, nullable=True)   # ملاحظات إضافية
    
    # معلومات إضافية حسب نوع التغيير
    resolution_details = Column(Text, nullable=True)  # تفاصيل الحل (للحالات المكتملة)
    estimated_cost = Column(String(20), nullable=True)  # التكلفة المقدرة عند التغيير
    actual_cost = Column(String(20), nullable=True)     # التكلفة الفعلية عند التغيير
    
    # معلومات النظام
    system_notes = Column(Text, nullable=True)  # ملاحظات النظام التلقائية
    ip_address = Column(String(45), nullable=True)  # عنوان IP للمستخدم
    user_agent = Column(Text, nullable=True)     # معلومات المتصفح
    
    # العلاقات
    claim = relationship("WarrantyClaim", backref="status_history")
    user = relationship("User", foreign_keys=[changed_by])

    def __repr__(self):
        return f"<ClaimStatusHistory(id={self.id}, claim_id={self.claim_id}, from='{self.from_status}', to='{self.to_status}')>"
    
    def get_status_change_description(self) -> str:
        """
        وصف تغيير الحالة بالعربية
        """
        status_labels = {
            'pending': 'في الانتظار',
            'approved': 'موافق عليه',
            'rejected': 'مرفوض',
            'in_progress': 'قيد التنفيذ',
            'completed': 'مكتمل'
        }
        
        from_label = status_labels.get(str(self.from_status), str(self.from_status)) if getattr(self, 'from_status', None) else 'جديد'
        to_label = status_labels.get(str(self.to_status), str(self.to_status))
        
        return f"تم تغيير الحالة من '{from_label}' إلى '{to_label}'"
    
    def get_time_since_change(self) -> str:
        """
        حساب الوقت المنقضي منذ التغيير
        """
        try:
            from datetime import datetime, timezone

            # استخدام الوقت الحالي مع timezone
            now = datetime.now(timezone.utc)

            # التأكد من أن changed_at له timezone
            changed_at = self.changed_at
            if changed_at.tzinfo is None:
                changed_at = changed_at.replace(tzinfo=timezone.utc)

            # حساب الفرق
            diff = now - changed_at

            # تحويل إلى نص مفهوم
            if diff.days > 0:
                return f"منذ {diff.days} يوم"
            elif diff.seconds > 3600:
                hours = diff.seconds // 3600
                return f"منذ {hours} ساعة"
            elif diff.seconds > 60:
                minutes = diff.seconds // 60
                return f"منذ {minutes} دقيقة"
            else:
                return "منذ لحظات"

        except Exception:
            return "غير محدد"

    def is_recent_change(self, hours: int = 24) -> bool:
        """
        تحديد إذا كان التغيير حديث (خلال عدد ساعات محدد)
        """
        try:
            from datetime import datetime, timezone, timedelta

            now = datetime.now(timezone.utc)
            changed_at = self.changed_at

            if changed_at.tzinfo is None:
                changed_at = changed_at.replace(tzinfo=timezone.utc)

            return (now - changed_at) < timedelta(hours=hours)

        except Exception:
            return False
