from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, func
from sqlalchemy.orm import relationship
from database.base import Base


class VariantAttribute(Base):
    """
    نموذج خصائص المتغيرات - لتخزين أنواع الخصائص مثل الحجم، اللون، المادة، إلخ
    """
    __tablename__ = "variant_attributes"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, unique=True, index=True)
    name_ar = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    attribute_type = Column(String(50), default='text', nullable=False)  # text, color, list, number
    is_required = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    sort_order = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(Integer, ForeignKey("users.id"))
    updated_by = Column(Integer, ForeignKey("users.id"))

    # Relationships
    creator = relationship("User", foreign_keys=[created_by])
    updater = relationship("User", foreign_keys=[updated_by])
    values = relationship("VariantValue", back_populates="attribute", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<VariantAttribute(id={self.id}, name='{self.name}', name_ar='{self.name_ar}')>"
