from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship

from database.base import Base
from utils.datetime_utils import tripoli_timestamp, enhanced_settings_timestamp

class Sale(Base):
    __tablename__ = "sales"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=True)  # Link to customer
    total_amount = Column(Float, nullable=False)  # Amount before tax and discount
    payment_method = Column(String(20), nullable=False)
    tax_amount = Column(Float, nullable=False, default=0.0)  # Tax amount in currency
    discount_amount = Column(Float, nullable=False, default=0.0)  # Discount amount
    discount_type = Column(String(20), nullable=False, default='fixed')  # 'fixed' or 'percentage'
    customer_name = Column(String(100), nullable=True)  # Keep for backward compatibility
    notes = Column(Text, nullable=True)

    # Payment fields
    amount_paid = Column(Float, nullable=False, default=0.0)  # Amount actually paid
    payment_status = Column(String(20), nullable=False, default='paid')  # 'paid', 'partial', 'unpaid'

    created_at = Column(DateTime(timezone=True), server_default=enhanced_settings_timestamp())
    updated_at = Column(DateTime(timezone=True), onupdate=enhanced_settings_timestamp())

    # Relationships
    user = relationship("User", back_populates="sales")
    customer = relationship("Customer", back_populates="sales")
    items = relationship("SaleItem", back_populates="sale", cascade="all, delete-orphan")

class SaleItem(Base):
    __tablename__ = "sale_items"

    id = Column(Integer, primary_key=True, index=True)
    sale_id = Column(Integer, ForeignKey("sales.id"), nullable=False)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    quantity = Column(Integer, nullable=False)
    unit_price = Column(Float, nullable=False)
    subtotal = Column(Float, nullable=False)
    discount = Column(Float, nullable=False, default=0)
    created_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())

    # Relationships
    sale = relationship("Sale", back_populates="items")
    product = relationship("Product")