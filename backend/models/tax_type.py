"""
نموذج أنواع الضرائب - Tax Types Model
يحتوي على تعريفات أنواع الضرائب المختلفة في النظام
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, DECIMAL, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from database.base import Base
from utils.datetime_utils import settings_timestamp as tripoli_timestamp


class TaxType(Base):
    """
    نموذج أنواع الضرائب
    يحتوي على تعريفات أنواع الضرائب المختلفة مثل ضريبة القيمة المضافة، ضريبة الخدمات، إلخ
    """
    __tablename__ = "tax_types"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)  # الاسم بالإنجليزية
    name_ar = Column(String(100), nullable=False, index=True)  # الاسم بالعربية
    description = Column(Text, nullable=True)  # الوصف
    tax_category = Column(String(50), nullable=False, default='standard')  # فئة الضريبة: standard, reduced, zero, exempt
    calculation_method = Column(String(20), nullable=False, default='percentage')  # طريقة الحساب: percentage, fixed
    is_compound = Column(Boolean, default=False, nullable=False)  # ضريبة مركبة (تحسب على الضريبة الأخرى)
    is_active = Column(Boolean, default=True, nullable=False)  # حالة التفعيل
    sort_order = Column(Integer, default=0, nullable=False)  # ترتيب العرض
    
    # معلومات التدقيق
    created_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())
    updated_at = Column(DateTime(timezone=True), onupdate=tripoli_timestamp())
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    updated_by = Column(Integer, ForeignKey("users.id"), nullable=True)

    # العلاقات
    tax_rates = relationship("TaxRate", back_populates="tax_type", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<TaxType(id={self.id}, name_ar='{self.name_ar}', category='{self.tax_category}')>"

    def to_dict(self):
        """تحويل النموذج إلى قاموس"""
        return {
            'id': self.id,
            'name': self.name,
            'name_ar': self.name_ar,
            'description': self.description,
            'tax_category': self.tax_category,
            'calculation_method': self.calculation_method,
            'is_compound': self.is_compound,
            'is_active': self.is_active,
            'sort_order': self.sort_order,
            'created_at': self.created_at.isoformat() if self.created_at is not None else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at is not None else None,
            'created_by': self.created_by,
            'updated_by': self.updated_by,
            'tax_rates_count': len(self.tax_rates) if self.tax_rates is not None else 0
        }

    @property
    def active_rates_count(self):
        """عدد القيم الضريبية النشطة"""
        if self.tax_rates is None:
            return 0
        return len([rate for rate in self.tax_rates if rate.is_active])

    @property
    def default_rate(self):
        """القيمة الضريبية الافتراضية"""
        if self.tax_rates is None:
            return None
        default_rates = [rate for rate in self.tax_rates if rate.is_default and rate.is_active]
        return default_rates[0] if default_rates else None

    @property
    def category_display_name(self):
        """اسم فئة الضريبة للعرض"""
        category_names = {
            'standard': 'معيارية',
            'reduced': 'مخفضة',
            'zero': 'صفر',
            'exempt': 'معفاة'
        }
        return category_names.get(str(self.tax_category), str(self.tax_category))

    @property
    def calculation_method_display_name(self):
        """اسم طريقة الحساب للعرض"""
        method_names = {
            'percentage': 'نسبة مئوية',
            'fixed': 'مبلغ ثابت'
        }
        return method_names.get(str(self.calculation_method), str(self.calculation_method))
