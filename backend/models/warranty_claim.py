"""
نموذج مطالبات الضمان
يحتوي على معلومات المطالبات المقدمة للضمانات
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, Date, Numeric
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from database.base import Base


class WarrantyClaim(Base):
    """
    نموذج مطالبات الضمان
    يحتوي على معلومات المطالبات المقدمة للضمانات
    """
    __tablename__ = "warranty_claims"

    id = Column(Integer, primary_key=True, index=True)
    warranty_id = Column(Integer, ForeignKey("product_warranties.id"), nullable=False, index=True)
    claim_number = Column(String(50), nullable=False, unique=True, index=True)  # رقم المطالبة الفريد
    
    # نوع المطالبة: repair, replacement, refund
    claim_type = Column(String(20), nullable=False, index=True)
    
    # وصف المشكلة والمطالبة
    issue_description = Column(Text, nullable=False)  # وصف المشكلة
    claim_description = Column(Text, nullable=True)  # تفاصيل إضافية للمطالبة
    
    # تواريخ المطالبة
    claim_date = Column(Date, nullable=False, default=func.current_date())  # تاريخ تقديم المطالبة
    expected_resolution_date = Column(Date, nullable=True)  # التاريخ المتوقع للحل
    
    # حالة المطالبة: pending, approved, rejected, in_progress, completed
    status = Column(String(20), nullable=False, default='pending', index=True)
    priority = Column(String(10), nullable=False, default='normal')  # low, normal, high, urgent
    
    # معلومات القرار والحل
    resolution = Column(Text, nullable=True)  # وصف الحل المقدم
    resolution_date = Column(Date, nullable=True)  # تاريخ الحل
    resolved_by = Column(Integer, ForeignKey("users.id"), nullable=True)  # من قام بالحل
    
    # التكلفة المالية
    estimated_cost = Column(Numeric(10, 2), nullable=True)  # التكلفة المقدرة
    actual_cost = Column(Numeric(10, 2), nullable=True)  # التكلفة الفعلية
    cost_covered_by_warranty = Column(Boolean, default=True)  # هل التكلفة مغطاة بالضمان
    
    # معلومات إضافية
    notes = Column(Text, nullable=True)  # ملاحظات إضافية
    internal_notes = Column(Text, nullable=True)  # ملاحظات داخلية (للموظفين فقط)
    
    # معلومات المراجعة والموافقة
    reviewed_by = Column(Integer, ForeignKey("users.id"), nullable=True)  # من راجع المطالبة
    reviewed_at = Column(DateTime(timezone=True), nullable=True)  # تاريخ المراجعة
    approved_by = Column(Integer, ForeignKey("users.id"), nullable=True)  # من وافق على المطالبة
    approved_at = Column(DateTime(timezone=True), nullable=True)  # تاريخ الموافقة
    
    # معلومات الرفض (في حالة الرفض)
    rejection_reason = Column(Text, nullable=True)  # سبب الرفض
    rejected_by = Column(Integer, ForeignKey("users.id"), nullable=True)  # من رفض المطالبة
    rejected_at = Column(DateTime(timezone=True), nullable=True)  # تاريخ الرفض

    # معلومات التتبع
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    updated_by = Column(Integer, ForeignKey("users.id"), nullable=True)

    # العلاقات
    warranty = relationship("ProductWarranty", backref="claims")

    def __repr__(self):
        return f"<WarrantyClaim(id={self.id}, claim_number='{self.claim_number}', status='{self.status}')>"
    
    def get_is_pending(self):
        """تحقق من كون المطالبة في انتظار المراجعة"""
        return str(self.status) == 'pending'

    def get_is_approved(self):
        """تحقق من كون المطالبة موافق عليها"""
        return str(self.status) == 'approved'

    def get_is_completed(self):
        """تحقق من كون المطالبة مكتملة"""
        return str(self.status) == 'completed'

    def get_days_since_claim(self):
        """عدد الأيام منذ تقديم المطالبة"""
        try:
            from utils.datetime_utils import get_current_time_with_settings
            from database.session import get_db

            # محاولة الحصول على جلسة قاعدة البيانات
            try:
                db_gen = get_db()
                db = next(db_gen)
                current_time = get_current_time_with_settings(db)
                current_date = current_time.date()
            except:
                # fallback إلى التاريخ المحلي في حالة عدم توفر جلسة قاعدة البيانات
                from datetime import date
                current_date = date.today()

            return (current_date - self.claim_date).days
        except:
            # fallback نهائي
            from datetime import date
            return (date.today() - self.claim_date).days

    @property
    def product_name(self):
        """اسم المنتج"""
        if self.warranty and self.warranty.product:
            return self.warranty.product.name
        return None

    @property
    def customer_name(self):
        """اسم العميل"""
        if self.warranty and self.warranty.customer:
            return self.warranty.customer.name
        return None

    @property
    def warranty_number(self):
        """رقم الضمان"""
        if self.warranty:
            return self.warranty.warranty_number
        return None
