from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Enum
from database.base import Base
from utils.datetime_utils import tripoli_timestamp
import enum

class TaskType(str, enum.Enum):
    """أنواع المهام المجدولة"""
    DATABASE_BACKUP = "database_backup"
    CLEANUP_OLD_BACKUPS = "cleanup_old_backups"
    GOOGLE_DRIVE_BACKUP = "google_drive_backup"
    GOOGLE_DRIVE_CLEANUP = "google_drive_cleanup"
    SYSTEM_MAINTENANCE = "system_maintenance"
    CLEANUP_INACTIVE_DEVICES = "cleanup_inactive_devices"

class TaskStatus(str, enum.Enum):
    """حالات المهام المجدولة"""
    ACTIVE = "active"
    PAUSED = "paused"
    DISABLED = "disabled"

class ScheduledTask(Base):
    """نموذج المهام المجدولة"""
    __tablename__ = "scheduled_tasks"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    task_type = Column(Enum(TaskType), nullable=False, index=True)
    cron_expression = Column(String(100), nullable=False)
    status = Column(Enum(TaskStatus), nullable=False, default=TaskStatus.ACTIVE, index=True)

    # معاملات المهمة (JSON string)
    task_params = Column(Text, nullable=True)

    # معلومات التنفيذ
    last_run = Column(DateTime(timezone=True), nullable=True)
    next_run = Column(DateTime(timezone=True), nullable=True)
    run_count = Column(Integer, default=0)
    failure_count = Column(Integer, default=0)
    last_error = Column(Text, nullable=True)

    # إعدادات إضافية
    max_retries = Column(Integer, default=3)
    timeout_seconds = Column(Integer, default=300)  # 5 دقائق افتراضي

    # معلومات النظام
    is_system_task = Column(Boolean, default=False)  # المهام الأساسية للنظام
    created_by = Column(Integer, nullable=True)  # معرف المستخدم الذي أنشأ المهمة

    # التواريخ
    created_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())
    updated_at = Column(DateTime(timezone=True), onupdate=tripoli_timestamp())

    def __repr__(self):
        return f"<ScheduledTask(id={self.id}, name='{self.name}', type='{self.task_type}', status='{self.status}')>"
