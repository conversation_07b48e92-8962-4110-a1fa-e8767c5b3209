from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, Text, Float, func
from sqlalchemy.orm import relationship
from database.base import Base

class Unit(Base):
    """Unit model for product measurement units."""
    __tablename__ = "units"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False, unique=True)  # e.g., "قطعة", "كيلو", "لتر"
    symbol = Column(String(10), nullable=True)              # e.g., "kg", "L", "pcs"
    description = Column(Text, nullable=True)
    unit_type = Column(String(50), nullable=True)           # e.g., "weight", "volume", "count"
    base_unit_id = Column(Integer, ForeignKey("units.id"), nullable=True)  # For unit conversions
    conversion_factor = Column(Float, nullable=True)        # Factor to convert to base unit
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(Integer, ForeignKey("users.id"))
    updated_by = Column(Integer, ForeignKey("users.id"))

    # Relationships
    creator = relationship("User", foreign_keys=[created_by])
    updater = relationship("User", foreign_keys=[updated_by])
    base_unit = relationship("Unit", remote_side=[id])  # Self-referential relationship
    products = relationship("Product", back_populates="unit_rel")
