"""
نماذج قاعدة البيانات لنظام المحادثة الفورية
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, Enum
from sqlalchemy.orm import relationship
from database.base import Base
import enum
from utils.datetime_utils import tripoli_timestamp

class MessageStatus(str, enum.Enum):
    """حالات الرسالة"""
    SENT = "sent"           # تم الإرسال
    DELIVERED = "delivered" # تم التسليم
    READ = "read"          # تم القراءة

class MessageType(str, enum.Enum):
    """أنواع الرسائل"""
    TEXT = "text"          # رسالة نصية
    IMAGE = "image"        # صورة
    FILE = "file"          # ملف
    SYSTEM = "system"      # رسالة نظام

class ChatMessage(Base):
    """
    نموذج رسائل المحادثة
    """
    __tablename__ = "chat_messages"

    id = Column(Integer, primary_key=True, index=True)
    
    # معرفات المرسل والمستقبل
    sender_id = Column(Integer, ForeignKey('users.id'), nullable=False, index=True)
    receiver_id = Column(Integer, ForeignKey('users.id'), nullable=False, index=True)
    
    # محتوى الرسالة
    content = Column(Text, nullable=False)
    message_type = Column(Enum(MessageType), default=MessageType.TEXT, nullable=False)
    
    # حالة الرسالة
    status = Column(Enum(MessageStatus), default=MessageStatus.SENT, nullable=False)
    
    # معلومات إضافية
    is_edited = Column(Boolean, default=False)
    edited_at = Column(DateTime(timezone=True), nullable=True)
    
    # أوقات مهمة
    created_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp(), nullable=False)
    delivered_at = Column(DateTime(timezone=True), nullable=True)
    read_at = Column(DateTime(timezone=True), nullable=True)
    
    # العلاقات
    sender = relationship("User", foreign_keys=[sender_id], backref="sent_messages")
    receiver = relationship("User", foreign_keys=[receiver_id], backref="received_messages")

    def __repr__(self):
        return f"<ChatMessage(id={self.id}, sender_id={self.sender_id}, receiver_id={self.receiver_id}, status={self.status})>"



class ChatRoom(Base):
    """
    نموذج غرف المحادثة (للمحادثات الجماعية - مستقبلاً)
    """
    __tablename__ = "chat_rooms"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    
    # معرف المنشئ
    created_by = Column(Integer, ForeignKey('users.id'), nullable=False)
    
    # إعدادات الغرفة
    is_private = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    max_members = Column(Integer, default=50)
    
    # أوقات
    created_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())
    updated_at = Column(DateTime(timezone=True), onupdate=tripoli_timestamp())
    
    # العلاقات
    creator = relationship("User", backref="created_rooms")
    
    def __repr__(self):
        return f"<ChatRoom(id={self.id}, name={self.name}, created_by={self.created_by})>"

class ChatRoomMember(Base):
    """
    نموذج أعضاء غرف المحادثة
    """
    __tablename__ = "chat_room_members"

    id = Column(Integer, primary_key=True, index=True)
    room_id = Column(Integer, ForeignKey('chat_rooms.id'), nullable=False)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    
    # صلاحيات العضو
    is_admin = Column(Boolean, default=False)
    can_send_messages = Column(Boolean, default=True)
    
    # أوقات
    joined_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())
    last_read_at = Column(DateTime(timezone=True), nullable=True)
    
    # العلاقات
    room = relationship("ChatRoom", backref="members")
    user = relationship("User", backref="room_memberships")
    
    def __repr__(self):
        return f"<ChatRoomMember(room_id={self.room_id}, user_id={self.user_id})>"

class UserOnlineStatus(Base):
    """
    نموذج حالة الاتصال للمستخدمين
    """
    __tablename__ = "user_online_status"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.id'), unique=True, nullable=False)
    
    # حالة الاتصال
    is_online = Column(Boolean, default=False)
    last_seen = Column(DateTime(timezone=True), server_default=tripoli_timestamp())
    
    # معلومات الاتصال
    socket_id = Column(String(100), nullable=True)  # معرف WebSocket
    device_info = Column(Text, nullable=True)       # معلومات الجهاز
    
    # أوقات
    created_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())
    updated_at = Column(DateTime(timezone=True), onupdate=tripoli_timestamp())
    
    # العلاقات
    user = relationship("User", backref="online_status")
    
    def __repr__(self):
        return f"<UserOnlineStatus(user_id={self.user_id}, is_online={self.is_online})>"
