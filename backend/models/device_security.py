"""
نماذج قاعدة البيانات لأمان الأجهزة
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text
from database.base import Base
from utils.datetime_utils import tripoli_timestamp

class BlockedDevice(Base):
    """
    نموذج الأجهزة المحظورة
    """
    __tablename__ = "blocked_devices"

    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(String, unique=True, index=True, nullable=False)
    client_ip = Column(String, index=True, nullable=False)
    hostname = Column(String)
    device_type = Column(String)
    system = Column(String)
    platform = Column(String)
    browser = Column(String)  # ✅ إضافة حقل المتصفح
    user_agent = Column(Text)

    # معلومات الحظر
    blocked_by = Column(String, nullable=False)  # المستخدم الذي حظر الجهاز
    blocked_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())
    block_reason = Column(String, default="محظور بواسطة المدير")
    original_table = Column(String)  # الجدول الأصلي للاستعادة (pending_devices أو approved_devices)
    is_permanent = Column(Boolean, default=True)

    # معلومات إضافية
    first_access = Column(DateTime(timezone=True))
    last_access = Column(DateTime(timezone=True))
    access_count = Column(Integer, default=0)

    created_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())
    updated_at = Column(DateTime(timezone=True), onupdate=tripoli_timestamp())

class DeviceSecuritySettings(Base):
    """
    إعدادات أمان الأجهزة
    """
    __tablename__ = "device_security_settings"

    id = Column(Integer, primary_key=True, index=True)
    setting_key = Column(String, unique=True, index=True, nullable=False)
    setting_value = Column(String, nullable=False)
    description = Column(String)

    created_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())
    updated_at = Column(DateTime(timezone=True), onupdate=tripoli_timestamp())

class PendingDevice(Base):
    """
    الأجهزة في انتظار الموافقة
    """
    __tablename__ = "pending_devices"

    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(String, unique=True, index=True, nullable=False)
    client_ip = Column(String, index=True, nullable=False)
    hostname = Column(String)
    device_type = Column(String)
    system = Column(String)
    platform = Column(String)
    browser = Column(String)  # ✅ إضافة حقل المتصفح
    user_agent = Column(Text)
    current_user = Column(String)

    # معلومات الطلب
    requested_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())
    first_access = Column(DateTime(timezone=True))
    last_access = Column(DateTime(timezone=True))
    access_count = Column(Integer, default=1)

    # حالة الطلب
    status = Column(String, default="pending")  # pending, approved, rejected
    reviewed_by = Column(String)
    reviewed_at = Column(DateTime(timezone=True))
    review_notes = Column(String)

    # معلومات البصمة (JSON)
    fingerprint_data = Column(Text)  # تخزين معلومات البصمة كـ JSON

    created_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())
    updated_at = Column(DateTime(timezone=True), onupdate=tripoli_timestamp())


class ApprovedDevice(Base):
    """
    الأجهزة المعتمدة - نموذج منفصل لتحسين الأداء
    """
    __tablename__ = "approved_devices"

    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(String, unique=True, index=True, nullable=False)
    client_ip = Column(String, index=True, nullable=False)
    hostname = Column(String)
    device_type = Column(String)
    system = Column(String)
    platform = Column(String)
    browser = Column(String)  # ✅ إضافة حقل المتصفح
    user_agent = Column(Text)
    current_user = Column(String)

    # معلومات الاعتماد
    approved_by = Column(String, nullable=False)  # المستخدم الذي اعتمد الجهاز
    approved_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())
    approval_notes = Column(String)

    # معلومات إضافية
    first_access = Column(DateTime(timezone=True))
    last_access = Column(DateTime(timezone=True))
    access_count = Column(Integer, default=1)

    created_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())
    updated_at = Column(DateTime(timezone=True), onupdate=tripoli_timestamp())