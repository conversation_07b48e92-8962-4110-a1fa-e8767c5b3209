"""
نموذج سجلات النظام
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Enum
from sqlalchemy.orm import relationship
from database.base import Base
from utils.datetime_utils import tripoli_timestamp
import enum

class LogLevel(str, enum.Enum):
    """مستويات السجلات"""
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class LogSource(str, enum.Enum):
    """مصادر السجلات"""
    FRONTEND = "FRONTEND"
    BACKEND = "BACKEND"
    DATABASE = "DATABASE"
    SYSTEM = "SYSTEM"

class SystemLog(Base):
    """نموذج سجلات النظام"""
    __tablename__ = "system_logs"

    id = Column(Integer, primary_key=True, index=True)
    timestamp = Column(DateTime(timezone=True), server_default=tripoli_timestamp())
    level = Column(Enum(LogLevel), nullable=False)
    source = Column(Enum(LogSource), nullable=False)
    message = Column(Text, nullable=False)
    details = Column(Text, nullable=True)
    stack_trace = Column(Text, nullable=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    session_id = Column(String(255), nullable=True)
    resolved = Column(Boolean, default=False)
    resolution_notes = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())
    updated_at = Column(DateTime(timezone=True), onupdate=tripoli_timestamp())

    # العلاقات
    user = relationship("User", back_populates="system_logs")

    def __repr__(self):
        return f"<SystemLog(id={self.id}, level={self.level}, source={self.source}, message='{self.message[:50]}...')>"
