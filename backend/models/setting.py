from sqlalchemy import Column, Integer, String, DateTime, Text
from sqlalchemy.sql import func

from database.base import Base
from utils.datetime_utils import tripoli_timestamp, enhanced_settings_timestamp

class Setting(Base):
    __tablename__ = "settings"

    id = Column(Integer, primary_key=True, index=True)
    key = Column(String(50), unique=True, index=True, nullable=False)
    value = Column(Text, nullable=False)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=enhanced_settings_timestamp())
    updated_at = Column(DateTime(timezone=True), onupdate=enhanced_settings_timestamp())