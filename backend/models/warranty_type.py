"""
نموذج أنواع الضمانات
يحدد أنواع الضمانات المختلفة المتاحة في النظام
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.sql import func
from database.base import Base


class WarrantyType(Base):
    """
    نموذج أنواع الضمانات
    يحتوي على تعريفات أنواع الضمانات المختلفة
    """
    __tablename__ = "warranty_types"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)  # الاسم بالإنجليزية
    name_ar = Column(String(100), nullable=False, index=True)  # الاسم بالعربية
    description = Column(Text, nullable=True)  # الوصف
    duration_months = Column(Integer, nullable=False)  # مدة الضمان بالأشهر
    coverage_type = Column(String(20), nullable=False, default='full')  # نوع التغطية: full, partial, limited
    terms_conditions = Column(Text, nullable=True)  # الشروط والأحكام
    is_active = Column(Boolean, default=True, nullable=False)  # حالة التفعيل

    # معلومات التتبع
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    updated_by = Column(Integer, ForeignKey("users.id"), nullable=True)

    def __repr__(self):
        return f"<WarrantyType(id={self.id}, name='{self.name}', name_ar='{self.name_ar}')>"
