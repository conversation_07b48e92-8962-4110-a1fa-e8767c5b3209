"""
نموذج بصمات الأجهزة - لحفظ البصمات الفريدة للأجهزة البعيدة
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, Index
from database.base import Base
import json
from typing import Dict, Any, Optional
from datetime import datetime


class DeviceFingerprint(Base):
    """
    جدول بصمات الأجهزة - يحفظ البصمة الفريدة لكل جهاز
    """
    __tablename__ = "device_fingerprints"

    id = Column(Integer, primary_key=True, index=True)

    # معرف البصمة الفريد (fp_xxxxx)
    fingerprint_id = Column(String(64), unique=True, index=True, nullable=False)

    # بصمة الأجهزة (Hardware Fingerprint)
    hardware_fingerprint = Column(String(64), index=True, nullable=False)

    # بصمة التخزين (Storage Fingerprint)
    storage_fingerprint = Column(String(64), index=True, nullable=False)

    # بصمة الشاشة
    screen_fingerprint = Column(String(32), nullable=True)

    # بصمة النظام
    system_fingerprint = Column(String(128), nullable=True)

    # ملاحظة: تم إلغاء network_fingerprint نهائياً حسب متطلبات النظام الجديدة

    # معلومات تفصيلية للبصمة (JSON)
    fingerprint_details = Column(Text, nullable=True)  # تفاصيل البصمة الكاملة

    # معلومات الجهاز (JSON)
    device_info = Column(Text, nullable=True)  # معلومات الجهاز والمتصفح

    # معلومات الشاشة (JSON)
    screen_info = Column(Text, nullable=True)  # تفاصيل الشاشة والدقة

    # معلومات النظام (JSON)
    system_info = Column(Text, nullable=True)  # تفاصيل نظام التشغيل

    # معلومات المتصفح (JSON)
    browser_info = Column(Text, nullable=True)  # تفاصيل المتصفح والإضافات

    # معلومات الشبكة (JSON)
    network_info = Column(Text, nullable=True)  # تفاصيل الشبكة والاتصال

    # معلومات إضافية (JSON)
    additional_info = Column(Text, nullable=True)

    # آخر عنوان IP مُسجل
    last_ip = Column(String(45), nullable=True)

    # آخر User Agent
    last_user_agent = Column(Text, nullable=True)

    # حالة البصمة
    is_active = Column(Boolean, default=True, nullable=False)

    # معتمد تلقائياً
    auto_approved = Column(Boolean, default=True, nullable=False)

    # تواريخ - يتم تحديدها صراحة في الكود
    created_at = Column(DateTime(timezone=True), nullable=False)
    updated_at = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now())
    last_seen_at = Column(DateTime(timezone=True), nullable=False)

    # فهارس للأداء
    __table_args__ = (
        Index('idx_fingerprint_hardware', 'hardware_fingerprint'),
        Index('idx_fingerprint_storage', 'storage_fingerprint'),
        Index('idx_fingerprint_active', 'is_active'),
        Index('idx_fingerprint_last_seen', 'last_seen_at'),
        Index('idx_fingerprint_composite', 'hardware_fingerprint', 'storage_fingerprint'),
    )

    def to_dict(self) -> Dict[str, Any]:
        """تحويل البصمة إلى قاموس"""
        def parse_json_field(field_value):
            if field_value is not None:
                try:
                    return json.loads(str(field_value))
                except:
                    return {}
            return {}

        return {
            'id': self.id,
            'fingerprint_id': self.fingerprint_id,
            'hardware_fingerprint': self.hardware_fingerprint,
            'storage_fingerprint': self.storage_fingerprint,
            'screen_fingerprint': self.screen_fingerprint,
            'system_fingerprint': self.system_fingerprint,
            # تم إزالة network_fingerprint نهائياً حسب متطلبات النظام الجديدة
            'fingerprint_details': parse_json_field(self.fingerprint_details),
            'device_info': parse_json_field(self.device_info),
            'screen_info': parse_json_field(self.screen_info),
            'system_info': parse_json_field(self.system_info),
            'browser_info': parse_json_field(self.browser_info),
            'network_info': parse_json_field(self.network_info),
            'additional_info': parse_json_field(self.additional_info),
            'last_ip': self.last_ip,
            'last_user_agent': self.last_user_agent,
            'is_active': self.is_active,
            'auto_approved': self.auto_approved,
            'created_at': self.created_at.isoformat() if self.created_at is not None else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at is not None else None,
            'last_seen_at': self.last_seen_at.isoformat() if self.last_seen_at is not None else None,
        }

    def update_fingerprint_details(self, details: Dict[str, Any]):
        """تحديث تفاصيل البصمة"""
        try:
            self.fingerprint_details = json.dumps(details, ensure_ascii=False)
        except Exception as e:
            self.fingerprint_details = json.dumps({'error': str(e)}, ensure_ascii=False)

    def update_device_info(self, info: Dict[str, Any]):
        """تحديث معلومات الجهاز"""
        try:
            self.device_info = json.dumps(info, ensure_ascii=False)
        except Exception as e:
            self.device_info = json.dumps({'error': str(e)}, ensure_ascii=False)

    def update_screen_info(self, info: Dict[str, Any]):
        """تحديث معلومات الشاشة"""
        try:
            self.screen_info = json.dumps(info, ensure_ascii=False)
        except Exception as e:
            self.screen_info = json.dumps({'error': str(e)}, ensure_ascii=False)

    def update_system_info(self, info: Dict[str, Any]):
        """تحديث معلومات النظام"""
        try:
            self.system_info = json.dumps(info, ensure_ascii=False)
        except Exception as e:
            self.system_info = json.dumps({'error': str(e)}, ensure_ascii=False)

    def update_browser_info(self, info: Dict[str, Any]):
        """تحديث معلومات المتصفح"""
        try:
            self.browser_info = json.dumps(info, ensure_ascii=False)
        except Exception as e:
            self.browser_info = json.dumps({'error': str(e)}, ensure_ascii=False)

    def update_network_info(self, info: Dict[str, Any]):
        """تحديث معلومات الشبكة"""
        try:
            self.network_info = json.dumps(info, ensure_ascii=False)
        except Exception as e:
            self.network_info = json.dumps({'error': str(e)}, ensure_ascii=False)

    def update_additional_info(self, info: Dict[str, Any]):
        """تحديث المعلومات الإضافية"""
        try:
            existing_info = {}
            if self.additional_info is not None:
                existing_info = json.loads(str(self.additional_info))

            existing_info.update(info)
            self.additional_info = json.dumps(existing_info, ensure_ascii=False)
        except Exception:
            # في حالة الخطأ، احفظ المعلومات الجديدة فقط
            self.additional_info = json.dumps(info, ensure_ascii=False)

    def get_additional_info(self) -> Dict[str, Any]:
        """الحصول على المعلومات الإضافية"""
        if self.additional_info is None:
            return {}

        try:
            return json.loads(str(self.additional_info))
        except:
            return {}

    def is_matching_fingerprint(self, hardware_fp: str, storage_fp: str) -> bool:
        """فحص إذا كانت البصمة تطابق البصمة المحفوظة"""
        return bool(
            self.hardware_fingerprint == hardware_fp and
            self.storage_fingerprint == storage_fp and
            self.is_active
        )

    def update_last_seen(self, ip: Optional[str] = None, user_agent: Optional[str] = None):
        """تحديث آخر مشاهدة"""
        from utils.datetime_utils import get_tripoli_now
        self.last_seen_at = get_tripoli_now()
        if ip:
            self.last_ip = ip
        if user_agent:
            self.last_user_agent = user_agent

    def __repr__(self):
        return f"<DeviceFingerprint(id={self.id}, fingerprint_id='{self.fingerprint_id}', active={self.is_active})>"


class DeviceFingerprintHistory(Base):
    """
    تاريخ بصمات الأجهزة - لتتبع التغييرات والوصول
    """
    __tablename__ = "device_fingerprint_history"

    id = Column(Integer, primary_key=True, index=True)

    # معرف البصمة المرتبطة
    fingerprint_id = Column(String(64), index=True, nullable=False)

    # نوع الحدث
    event_type = Column(String(32), nullable=False)  # 'created', 'accessed', 'updated', 'blocked'

    # عنوان IP
    ip_address = Column(String(45), nullable=True)

    # User Agent
    user_agent = Column(Text, nullable=True)

    # معلومات إضافية عن الحدث
    event_data = Column(Text, nullable=True)

    # تاريخ الحدث - يتم تحديده صراحة في الكود
    created_at = Column(DateTime(timezone=True), nullable=False)

    # فهارس
    __table_args__ = (
        Index('idx_history_fingerprint', 'fingerprint_id'),
        Index('idx_history_event_type', 'event_type'),
        Index('idx_history_created_at', 'created_at'),
        Index('idx_history_ip', 'ip_address'),
    )

    def to_dict(self) -> Dict[str, Any]:
        """تحويل السجل إلى قاموس"""
        event_data = {}
        if self.event_data is not None:
            try:
                event_data = json.loads(str(self.event_data))
            except:
                event_data = {'raw': str(self.event_data)}

        return {
            'id': self.id,
            'fingerprint_id': self.fingerprint_id,
            'event_type': self.event_type,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'event_data': event_data,
            'created_at': self.created_at.isoformat() if self.created_at is not None else None,
        }

    def get_event_data(self) -> Dict[str, Any]:
        """الحصول على بيانات الحدث"""
        if self.event_data is None:
            return {}

        try:
            return json.loads(str(self.event_data))
        except:
            return {'raw': str(self.event_data)}

    def set_event_data(self, data: Dict[str, Any]):
        """تعيين بيانات الحدث"""
        try:
            self.event_data = json.dumps(data, ensure_ascii=False)
        except Exception as e:
            self.event_data = json.dumps({'error': str(e)}, ensure_ascii=False)

    def __repr__(self):
        return f"<DeviceFingerprintHistory(id={self.id}, fingerprint_id='{self.fingerprint_id}', event='{self.event_type}')>"
