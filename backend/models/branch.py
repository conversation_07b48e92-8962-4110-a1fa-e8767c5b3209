from sqlalchemy import Column, Integer, String, DateTime, Bo<PERSON>an, Text, ForeignKey, Table
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from database.base import Base
from utils.datetime_utils import tripoli_timestamp
import uuid


# جدول الربط Many-to-Many بين الفروع والمستودعات
branch_warehouses = Table(
    'branch_warehouses',
    Base.metadata,
    Column('branch_id', Integer, ForeignKey('branches.id'), primary_key=True),
    Column('warehouse_id', Integer, ForeignKey('warehouses.id'), primary_key=True),
    Column('created_at', DateTime(timezone=True), server_default=tripoli_timestamp()),
    Column('is_primary', Boolean, default=False, comment='هل هذا المستودع الأساسي للفرع'),
    Column('priority', Integer, default=1, comment='أولوية المستودع للفرع (1 = أعلى أولوية)')
)


class Branch(Base):
    """
    نموذج الفروع - يمثل فروع الشركة المختلفة
    
    يدعم العلاقة Many-to-Many مع المستودعات حيث:
    - يمكن لأي فرع أن يرتبط بعدة مستودعات
    - يمكن لأي مستودع أن يخدم عدة فروع
    """
    __tablename__ = "branches"

    id = Column(Integer, primary_key=True, index=True)
    uuid = Column(UUID(as_uuid=True), unique=True, nullable=False, index=True, default=uuid.uuid4, comment='المعرف الفريد للفرع (UUID)')
    name = Column(String(100), nullable=False, comment='اسم الفرع')
    code = Column(String(20), unique=True, nullable=True, index=True, comment='كود الفرع (اختياري للعرض)')
    address = Column(Text, nullable=True, comment='عنوان الفرع')
    phone = Column(String(20), nullable=True, comment='رقم هاتف الفرع')
    manager_name = Column(String(100), nullable=True, comment='اسم مدير الفرع')
    email = Column(String(100), nullable=True, comment='البريد الإلكتروني للفرع')
    is_active = Column(Boolean, default=True, nullable=False, comment='حالة نشاط الفرع')
    is_main = Column(Boolean, default=False, nullable=False, comment='هل هذا الفرع الرئيسي')
    
    # معلومات إضافية للفرع
    city = Column(String(50), nullable=True, comment='المدينة')
    region = Column(String(50), nullable=True, comment='المنطقة')
    postal_code = Column(String(20), nullable=True, comment='الرمز البريدي')
    
    # إعدادات الفرع
    max_daily_sales = Column(Integer, nullable=True, comment='الحد الأقصى للمبيعات اليومية')
    working_hours_start = Column(String(10), nullable=True, comment='بداية ساعات العمل')
    working_hours_end = Column(String(10), nullable=True, comment='نهاية ساعات العمل')
    
    # تواريخ النظام
    created_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())
    updated_at = Column(DateTime(timezone=True), onupdate=tripoli_timestamp())
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    updated_by = Column(Integer, ForeignKey("users.id"), nullable=True)

    # العلاقات
    # علاقة Many-to-Many مع المستودعات
    warehouses = relationship(
        "Warehouse", 
        secondary=branch_warehouses, 
        back_populates="branches",
        lazy="select"
    )
    
    # علاقة مع المبيعات (إذا كان هناك حقل branch_id في جدول المبيعات)
    # sales = relationship("Sale", back_populates="branch")
    
    # علاقة مع المستخدمين الذين أنشأوا/حدثوا الفرع
    creator = relationship("User", foreign_keys=[created_by])
    updater = relationship("User", foreign_keys=[updated_by])

    def __repr__(self):
        return f"<Branch(id={self.id}, uuid='{self.uuid}', name='{self.name}', code='{self.code}', is_active={self.is_active})>"

    def __str__(self):
        return f"{self.name} ({self.uuid})"

    @property
    def active_warehouses(self):
        """الحصول على المستودعات النشطة المرتبطة بهذا الفرع"""
        return [w for w in self.warehouses if w.is_active]

    @property
    def primary_warehouse(self):
        """الحصول على المستودع الأساسي للفرع"""
        # سنحتاج لتنفيذ هذا عبر استعلام مخصص لاحقاً
        # لأن معلومات الأولوية موجودة في جدول الربط
        return None

    @property
    def total_warehouses_count(self):
        """عدد المستودعات المرتبطة بالفرع"""
        return len(self.warehouses)

    @property
    def active_warehouses_count(self):
        """عدد المستودعات النشطة المرتبطة بالفرع"""
        return len(self.active_warehouses)
