"""
نموذج ضمانات المنتجات
يحتوي على معلومات الضمانات المرتبطة بالمنتجات
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, Date, Boolean
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from database.base import Base


class ProductWarranty(Base):
    """
    نموذج ضمانات المنتجات
    يحتوي على معلومات الضمان لكل منتج
    """
    __tablename__ = "product_warranties"

    id = Column(Integer, primary_key=True, index=True)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False, index=True)
    warranty_type_id = Column(Integer, ForeignKey("warranty_types.id"), nullable=False, index=True)
    warranty_number = Column(String(50), nullable=False, unique=True, index=True)  # رقم الضمان الفريد
    
    # تواريخ الضمان
    purchase_date = Column(Date, nullable=False)  # تاريخ الشراء
    start_date = Column(Date, nullable=False)  # تاريخ بداية الضمان
    end_date = Column(Date, nullable=False)  # تاريخ انتهاء الضمان
    
    # معلومات العميل (اختيارية)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=True, index=True)
    
    # حالة الضمان: active, expired, voided
    status = Column(String(20), nullable=False, default='active', index=True)
    notes = Column(Text, nullable=True)  # ملاحظات إضافية

    # تتبع المطالبات
    has_claims = Column(Boolean, default=False, nullable=False)  # هل تم المطالبة بهذا الضمان مسبقاً
    
    # معلومات الإلغاء (في حالة الإلغاء)
    void_reason = Column(Text, nullable=True)  # سبب الإلغاء
    voided_at = Column(DateTime(timezone=True), nullable=True)  # تاريخ الإلغاء
    voided_by = Column(Integer, ForeignKey("users.id"), nullable=True)  # من قام بالإلغاء
    
    # معلومات التمديد
    extended_months = Column(Integer, default=0)  # عدد الأشهر المضافة
    extension_reason = Column(Text, nullable=True)  # سبب التمديد
    extended_at = Column(DateTime(timezone=True), nullable=True)  # تاريخ التمديد
    extended_by = Column(Integer, ForeignKey("users.id"), nullable=True)  # من قام بالتمديد

    # معلومات التتبع
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    updated_by = Column(Integer, ForeignKey("users.id"), nullable=True)

    # العلاقات
    warranty_type = relationship("WarrantyType", backref="product_warranties")
    product = relationship("Product", backref="warranties")
    customer = relationship("Customer", backref="warranties")

    def __repr__(self):
        return f"<ProductWarranty(id={self.id}, warranty_number='{self.warranty_number}', status='{self.status}')>"
    
    def get_is_active(self):
        """تحقق من كون الضمان نشطاً"""
        try:
            from utils.datetime_utils import get_current_time_with_settings
            from database.session import get_db

            # محاولة الحصول على جلسة قاعدة البيانات
            try:
                db_gen = get_db()
                db = next(db_gen)
                current_time = get_current_time_with_settings(db)
                current_date = current_time.date()
            except:
                # fallback إلى التاريخ المحلي في حالة عدم توفر جلسة قاعدة البيانات
                from datetime import date
                current_date = date.today()

            return str(self.status) == 'active' and self.end_date >= current_date
        except:
            # fallback نهائي
            from datetime import date
            return str(self.status) == 'active' and self.end_date >= date.today()

    def get_days_remaining(self):
        """عدد الأيام المتبقية في الضمان"""
        try:
            from utils.datetime_utils import get_current_time_with_settings
            from database.session import get_db

            if str(self.status) != 'active':
                return 0

            # محاولة الحصول على جلسة قاعدة البيانات
            try:
                db_gen = get_db()
                db = next(db_gen)
                current_time = get_current_time_with_settings(db)
                current_date = current_time.date()
            except:
                # fallback إلى التاريخ المحلي في حالة عدم توفر جلسة قاعدة البيانات
                from datetime import date
                current_date = date.today()

            remaining = (self.end_date - current_date).days
            return max(0, remaining)
        except:
            # fallback نهائي
            from datetime import date
            if str(self.status) != 'active':
                return 0
            remaining = (self.end_date - date.today()).days
            return max(0, remaining)

    def get_is_expiring_soon(self, days_threshold=30):
        """تحقق من كون الضمان سينتهي قريباً"""
        return self.get_is_active() and self.get_days_remaining() <= days_threshold
