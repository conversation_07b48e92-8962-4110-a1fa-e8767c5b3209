from sqlalchemy import Column, Integer, String, DateTime, Bo<PERSON>an, Text, ForeignKey, Enum, DECIMAL
from sqlalchemy.orm import relationship
import enum

from database.base import Base
from utils.datetime_utils import tripoli_timestamp


class MovementType(str, enum.Enum):
    """أنواع حركات المستودعات"""
    IN = "IN"           # دخول
    OUT = "OUT"         # خروج
    TRANSFER = "TRANSFER"  # تحويل
    ADJUSTMENT = "ADJUSTMENT"  # تعديل


class ReferenceType(str, enum.Enum):
    """أنواع المراجع للحركات"""
    PURCHASE = "PURCHASE"      # مشتريات
    SALE = "SALE"             # مبيعات
    TRANSFER = "TRANSFER"      # تحويل
    ADJUSTMENT = "ADJUSTMENT"  # تعديل
    RETURN = "RETURN"         # مرتجعات


class TransferStatus(str, enum.Enum):
    """حالات طلبات التحويل"""
    PENDING = "PENDING"       # معلق
    APPROVED = "APPROVED"     # موافق عليه
    IN_TRANSIT = "IN_TRANSIT" # في الطريق
    COMPLETED = "COMPLETED"   # مكتمل
    CANCELLED = "CANCELLED"   # ملغي


class Warehouse(Base):
    """نموذج المستودعات"""
    __tablename__ = "warehouses"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    code = Column(String(20), unique=True, nullable=False, index=True)
    address = Column(Text, nullable=True)
    phone = Column(String(20), nullable=True)
    manager_name = Column(String(100), nullable=True)
    email = Column(String(100), nullable=True)
    is_main = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    capacity_limit = Column(DECIMAL(15, 2), nullable=True)
    current_capacity = Column(DECIMAL(15, 2), default=0)
    created_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())
    updated_at = Column(DateTime(timezone=True), onupdate=tripoli_timestamp())

    # العلاقات
    inventory_items = relationship("WarehouseInventory", back_populates="warehouse", cascade="all, delete-orphan")
    movements_from = relationship("WarehouseMovement", foreign_keys="WarehouseMovement.from_warehouse_id", back_populates="from_warehouse")
    movements_to = relationship("WarehouseMovement", foreign_keys="WarehouseMovement.to_warehouse_id", back_populates="to_warehouse")
    transfer_requests_from = relationship("TransferRequest", foreign_keys="TransferRequest.from_warehouse_id", back_populates="from_warehouse")
    transfer_requests_to = relationship("TransferRequest", foreign_keys="TransferRequest.to_warehouse_id", back_populates="to_warehouse")

    # علاقة Many-to-Many مع الفروع
    branches = relationship(
        "Branch",
        secondary="branch_warehouses",
        back_populates="warehouses",
        lazy="select"
    )

    def __repr__(self):
        return f"<Warehouse(id={self.id}, name='{self.name}', code='{self.code}', is_main={self.is_main}, is_active={self.is_active})>"

    def __str__(self):
        return f"{self.name} ({self.code})"

    @property
    def active_branches(self):
        """الحصول على الفروع النشطة المرتبطة بهذا المستودع"""
        return [b for b in self.branches if b.is_active]

    @property
    def total_branches_count(self):
        """عدد الفروع المرتبطة بالمستودع"""
        return len(self.branches)

    @property
    def active_branches_count(self):
        """عدد الفروع النشطة المرتبطة بالمستودع"""
        return len(self.active_branches)

    @property
    def capacity_percentage(self):
        """نسبة استخدام السعة"""
        if self.capacity_limit is not None and float(self.capacity_limit) > 0:
            current = float(self.current_capacity) if self.current_capacity is not None else 0
            return (current / float(self.capacity_limit)) * 100
        return 0

    @property
    def available_capacity(self):
        """السعة المتاحة"""
        if self.capacity_limit is not None:
            current = float(self.current_capacity) if self.current_capacity is not None else 0
            return float(self.capacity_limit) - current
        return None


class WarehouseInventory(Base):
    """نموذج مخزون المستودعات"""
    __tablename__ = "warehouse_inventory"

    id = Column(Integer, primary_key=True, index=True)
    warehouse_id = Column(Integer, ForeignKey("warehouses.id"), nullable=False)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    quantity = Column(DECIMAL(10, 2), nullable=False, default=0)
    reserved_quantity = Column(DECIMAL(10, 2), default=0)
    min_stock_level = Column(DECIMAL(10, 2), default=0)
    max_stock_level = Column(DECIMAL(10, 2), nullable=True)
    location_code = Column(String(50), nullable=True)
    last_updated = Column(DateTime(timezone=True), server_default=tripoli_timestamp(), onupdate=tripoli_timestamp())

    # العلاقات
    warehouse = relationship("Warehouse", back_populates="inventory_items")
    product = relationship("Product")

    # فهرس مركب لضمان عدم تكرار المنتج في نفس المستودع
    __table_args__ = (
        {"schema": None},
    )


class WarehouseMovement(Base):
    """نموذج حركات المستودعات"""
    __tablename__ = "warehouse_movements"

    id = Column(Integer, primary_key=True, index=True)
    movement_type = Column(Enum(MovementType), nullable=False)
    from_warehouse_id = Column(Integer, ForeignKey("warehouses.id"), nullable=True)
    to_warehouse_id = Column(Integer, ForeignKey("warehouses.id"), nullable=True)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    quantity = Column(DECIMAL(10, 2), nullable=False)
    unit_cost = Column(DECIMAL(10, 2), nullable=True)
    total_cost = Column(DECIMAL(15, 2), nullable=True)
    reference_type = Column(Enum(ReferenceType), nullable=True)
    reference_id = Column(Integer, nullable=True)
    notes = Column(Text, nullable=True)
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())

    # العلاقات
    from_warehouse = relationship("Warehouse", foreign_keys=[from_warehouse_id], back_populates="movements_from")
    to_warehouse = relationship("Warehouse", foreign_keys=[to_warehouse_id], back_populates="movements_to")
    product = relationship("Product")
    creator = relationship("User")


class TransferRequest(Base):
    """نموذج طلبات التحويل"""
    __tablename__ = "transfer_requests"

    id = Column(Integer, primary_key=True, index=True)
    request_number = Column(String(50), unique=True, nullable=False, index=True)
    from_warehouse_id = Column(Integer, ForeignKey("warehouses.id"), nullable=False)
    to_warehouse_id = Column(Integer, ForeignKey("warehouses.id"), nullable=False)
    status = Column(Enum(TransferStatus), default=TransferStatus.PENDING)
    requested_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    approved_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    notes = Column(Text, nullable=True)
    requested_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())
    approved_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # العلاقات
    from_warehouse = relationship("Warehouse", foreign_keys=[from_warehouse_id], back_populates="transfer_requests_from")
    to_warehouse = relationship("Warehouse", foreign_keys=[to_warehouse_id], back_populates="transfer_requests_to")
    requester = relationship("User", foreign_keys=[requested_by])
    approver = relationship("User", foreign_keys=[approved_by])
    items = relationship("TransferRequestItem", back_populates="transfer_request", cascade="all, delete-orphan")


class TransferRequestItem(Base):
    """نموذج تفاصيل طلبات التحويل"""
    __tablename__ = "transfer_request_items"

    id = Column(Integer, primary_key=True, index=True)
    transfer_request_id = Column(Integer, ForeignKey("transfer_requests.id"), nullable=False)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    requested_quantity = Column(DECIMAL(10, 2), nullable=False)
    approved_quantity = Column(DECIMAL(10, 2), nullable=True)
    transferred_quantity = Column(DECIMAL(10, 2), default=0)
    unit_cost = Column(DECIMAL(10, 2), nullable=True)
    notes = Column(Text, nullable=True)

    # العلاقات
    transfer_request = relationship("TransferRequest", back_populates="items")
    product = relationship("Product")
