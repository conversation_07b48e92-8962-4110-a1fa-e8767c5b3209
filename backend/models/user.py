from sqlalchemy import Column, Integer, String, <PERSON><PERSON>an, DateTime, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from database.base import Base
import enum
from utils.datetime_utils import tripoli_timestamp, enhanced_settings_timestamp

class UserRole(str, enum.Enum):
    ADMIN = "admin"
    CASHIER = "cashier"

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True, nullable=False)
    email = Column(String, unique=True, index=True)
    full_name = Column(String)
    hashed_password = Column(String, nullable=False)
    role = Column(Enum(UserRole), nullable=False, default=UserRole.CASHIER)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=enhanced_settings_timestamp())
    updated_at = Column(DateTime(timezone=True), onupdate=enhanced_settings_timestamp())

    # حقول إضافية للمحادثة
    is_online = Column(Boolean, default=False)
    last_seen = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    sales = relationship("Sale", back_populates="user")
    system_logs = relationship("SystemLog", back_populates="user")