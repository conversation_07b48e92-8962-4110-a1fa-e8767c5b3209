"""
نموذج قيم الضرائب - Tax Rates Model
يحتوي على القيم والنسب الضريبية المختلفة لكل نوع ضريبة
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, DECIMAL, DateTime, ForeignKey, Date
from sqlalchemy.orm import relationship
from database.base import Base
from utils.datetime_utils import settings_timestamp as tripoli_timestamp
from decimal import Decimal


class TaxRate(Base):
    """
    نموذج قيم الضرائب
    يحتوي على النسب والقيم الضريبية المختلفة لكل نوع ضريبة
    """
    __tablename__ = "tax_rates"

    id = Column(Integer, primary_key=True, index=True)
    tax_type_id = Column(Integer, ForeignKey("tax_types.id"), nullable=False, index=True)
    name = Column(String(100), nullable=False, index=True)  # اسم القيمة الضريبية
    rate_value = Column(DECIMAL(10, 4), nullable=False)  # القيمة (نسبة مئوية أو مبلغ ثابت)
    description = Column(Text, nullable=True)  # الوصف
    
    # تواريخ السريان
    effective_from = Column(Date, nullable=True)  # تاريخ بداية السريان
    effective_to = Column(Date, nullable=True)  # تاريخ نهاية السريان
    
    # إعدادات القيمة
    is_default = Column(Boolean, default=False, nullable=False)  # القيمة الافتراضية لهذا النوع
    is_active = Column(Boolean, default=True, nullable=False)  # حالة التفعيل
    applies_to = Column(String(50), nullable=False, default='all')  # ينطبق على: all, products, services
    
    # حدود التطبيق
    min_amount = Column(DECIMAL(15, 2), nullable=True)  # الحد الأدنى للمبلغ
    max_amount = Column(DECIMAL(15, 2), nullable=True)  # الحد الأقصى للمبلغ
    
    # معلومات إضافية
    tax_code = Column(String(20), nullable=True, index=True)  # رمز الضريبة الحكومي
    sort_order = Column(Integer, default=0, nullable=False)  # ترتيب العرض
    
    # معلومات التدقيق
    created_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())
    updated_at = Column(DateTime(timezone=True), onupdate=tripoli_timestamp())
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    updated_by = Column(Integer, ForeignKey("users.id"), nullable=True)

    # العلاقات
    tax_type = relationship("TaxType", back_populates="tax_rates")
    
    def __repr__(self):
        return f"<TaxRate(id={self.id}, name='{self.name}', rate={self.rate_value}%)>"

    def to_dict(self):
        """تحويل النموذج إلى قاموس"""
        return {
            'id': self.id,
            'tax_type_id': self.tax_type_id,
            'name': self.name,
            'rate_value': float(self.rate_value) if self.rate_value is not None else 0,
            'description': self.description,
            'effective_from': self.effective_from.isoformat() if self.effective_from is not None else None,
            'effective_to': self.effective_to.isoformat() if self.effective_to is not None else None,
            'is_default': self.is_default,
            'is_active': self.is_active,
            'applies_to': self.applies_to,
            'min_amount': float(self.min_amount) if self.min_amount is not None else None,
            'max_amount': float(self.max_amount) if self.max_amount is not None else None,
            'tax_code': self.tax_code,
            'sort_order': self.sort_order,
            'created_at': self.created_at.isoformat() if self.created_at is not None else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at is not None else None,
            'created_by': self.created_by,
            'updated_by': self.updated_by,
            'tax_type_name': self.tax_type.name_ar if self.tax_type is not None else None
        }

    @property
    def display_rate(self):
        """عرض القيمة الضريبية بالتنسيق المناسب"""
        if self.rate_value is None:
            return "0"
        
        if self.tax_type and self.tax_type.calculation_method == 'percentage':
            return f"{float(self.rate_value)}%"
        else:
            return f"{float(self.rate_value)} د.ل"

    @property
    def applies_to_display_name(self):
        """اسم نطاق التطبيق للعرض"""
        applies_names = {
            'all': 'جميع العناصر',
            'products': 'المنتجات فقط',
            'services': 'الخدمات فقط'
        }
        return applies_names.get(str(self.applies_to), str(self.applies_to))

    def is_currently_effective(self):
        """التحقق من كون القيمة الضريبية سارية حالياً"""
        from datetime import date
        today = date.today()
        
        if self.effective_from is not None and today < self.effective_from:
            return False
        
        if self.effective_to is not None and today > self.effective_to:
            return False
        
        return True

    def calculate_tax_amount(self, base_amount: Decimal) -> Decimal:
        """حساب مبلغ الضريبة على أساس المبلغ الأساسي"""
        # تحويل القيم من Column إلى القيم الفعلية
        is_active = bool(self.is_active) if hasattr(self, 'is_active') else True
        if not is_active or not self.is_currently_effective():
            return Decimal('0')
        
        # التحقق من الحدود
        min_amount = Decimal(str(self.min_amount)) if self.min_amount is not None else None
        max_amount = Decimal(str(self.max_amount)) if self.max_amount is not None else None
        
        if min_amount is not None and base_amount < min_amount:
            return Decimal('0')
        
        if max_amount is not None and base_amount > max_amount:
            base_amount = max_amount
        
        rate_value = Decimal(str(self.rate_value)) if self.rate_value is not None else Decimal('0')
        
        if self.tax_type and str(self.tax_type.calculation_method) == 'percentage':
            return base_amount * (rate_value / Decimal('100'))
        else:
            return rate_value
