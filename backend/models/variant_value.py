from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, func
from sqlalchemy.orm import relationship
from database.base import Base


class VariantValue(Base):
    """
    نموذج قيم خصائص المتغيرات - لتخزين القيم المحددة لكل خاصية
    مثل: للحجم (S, M, L, XL) أو للون (أحمر، أزرق، أخضر)
    """
    __tablename__ = "variant_values"

    id = Column(Integer, primary_key=True, index=True)
    attribute_id = Column(Integer, ForeignKey("variant_attributes.id", ondelete="CASCADE"), nullable=False)
    value = Column(String(100), nullable=False, index=True)
    value_ar = Column(String(100), nullable=False, index=True)
    color_code = Column(String(7), nullable=True)  # للألوان - hex color code مثل #FF0000
    is_active = Column(Boolean, default=True)
    sort_order = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    attribute = relationship("VariantAttribute", back_populates="values")

    def __repr__(self):
        return f"<VariantValue(id={self.id}, value='{self.value}', value_ar='{self.value_ar}')>"
