#!/usr/bin/env python3
"""
سكريبت مراقبة سريع للإحداثيات
"""

import time
from database.session import get_db
from models.setting import Setting

def quick_check():
    """فحص سريع للإحداثيات"""
    db = next(get_db())
    
    latitude_setting = db.query(Setting).filter(Setting.key == 'store_latitude').first()
    longitude_setting = db.query(Setting).filter(Setting.key == 'store_longitude').first()
    
    lat = latitude_setting.value if latitude_setting else "غير موجود"
    lng = longitude_setting.value if longitude_setting else "غير موجود"
    
    print(f"📍 store_latitude: {lat}")
    print(f"📍 store_longitude: {lng}")
    print("=" * 40)
    
    db.close()

if __name__ == "__main__":
    print("🔍 مراقبة سريعة للإحداثيات...")
    print("اختر موقعاً جديداً واضغط 'حفظ الموقع'")
    print("=" * 40)
    
    while True:
        try:
            quick_check()
            time.sleep(3)
        except KeyboardInterrupt:
            print("\n🛑 تم إيقاف المراقبة")
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")
            time.sleep(5)
