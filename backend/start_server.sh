#!/bin/bash

# 🚀 ملف تشغيل سريع لخادم SmartPOS على البورت 8002
# 🚀 Quick start script for SmartPOS server on port 8002

echo "============================================================"
echo "🚀 بدء تشغيل خادم SmartPOS على البورت 8002"
echo "🚀 Starting SmartPOS server on port 8002"
echo "============================================================"

# الانتقال إلى مجلد backend
cd "$(dirname "$0")"

# التحقق من وجود البيئة الافتراضية
if [ ! -d "venv" ]; then
    echo "❌ البيئة الافتراضية غير موجودة!"
    echo "❌ Virtual environment not found!"
    echo "💡 يرجى تشغيل: ./setup_venv.sh"
    echo "💡 Please run: ./setup_venv.sh"
    exit 1
fi

# تفعيل البيئة الافتراضية
echo "🔧 تفعيل البيئة الافتراضية..."
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# التحقق من تفعيل البيئة الافتراضية
if [ "$VIRTUAL_ENV" != "" ]; then
    echo "✅ تم تفعيل البيئة الافتراضية: $VIRTUAL_ENV"
    echo "✅ Virtual environment activated: $VIRTUAL_ENV"
else
    echo "❌ فشل في تفعيل البيئة الافتراضية"
    echo "❌ Failed to activate virtual environment"
    exit 1
fi

# فحص وجود uvicorn
if ! command -v uvicorn &> /dev/null; then
    echo "❌ uvicorn غير مثبت"
    echo "❌ uvicorn not installed"
    echo "💡 يرجى تشغيل: pip install -r requirements.txt"
    echo "💡 Please run: pip install -r requirements.txt"
    exit 1
fi

# تشغيل الخادم
echo "------------------------------------------------------------"
echo "🔄 تشغيل الخادم على البورت 8002..."
echo "🔄 Starting server on port 8002..."
echo "🌐 الخادم سيكون متاح على: http://localhost:8002"
echo "🌐 Server will be available at: http://localhost:8002"
echo "⏹️  للإيقاف اضغط Ctrl+C"
echo "⏹️  To stop press Ctrl+C"
echo "------------------------------------------------------------"

# تشغيل uvicorn
uvicorn main:app --reload --host 0.0.0.0 --port 8002 --log-level info
