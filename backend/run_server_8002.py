#!/usr/bin/env python3
"""
ملف تشغيل خادم SmartPOS على البورت 8002 مع تفعيل البيئة الافتراضية تلقائياً
SmartPOS Server Runner on port 8002 with automatic virtual environment activation

الميزات:
- تفعيل البيئة الافتراضية تلقائياً
- فحص المتطلبات والتبعيات
- إعداد متغيرات البيئة
- مراقبة حالة الخادم
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def check_python_version():
    """فحص إصدار Python"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        print("❌ Python 3.8 or newer required")
        return False
    print(f"✅ إصدار Python: {version.major}.{version.minor}.{version.micro}")
    print(f"✅ Python version: {version.major}.{version.minor}.{version.micro}")
    return True

def check_requirements(venv_path):
    """فحص المتطلبات المثبتة"""
    pip_path = venv_path / "bin" / "pip"
    if platform.system() == "Windows":
        pip_path = venv_path / "Scripts" / "pip.exe"

    try:
        # فحص uvicorn
        result = subprocess.run([str(pip_path), "show", "uvicorn"],
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ uvicorn غير مثبت في البيئة الافتراضية")
            print("❌ uvicorn not installed in virtual environment")
            print("💡 يرجى تشغيل: pip install -r requirements.txt")
            print("💡 Please run: pip install -r requirements.txt")
            return False

        # فحص fastapi
        result = subprocess.run([str(pip_path), "show", "fastapi"],
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ fastapi غير مثبت في البيئة الافتراضية")
            print("❌ fastapi not installed in virtual environment")
            return False

        print("✅ جميع المتطلبات الأساسية مثبتة")
        print("✅ All basic requirements installed")
        return True

    except Exception as e:
        print(f"⚠️ خطأ في فحص المتطلبات: {e}")
        print(f"⚠️ Error checking requirements: {e}")
        return True  # نتابع حتى لو فشل الفحص

def setup_environment():
    """إعداد متغيرات البيئة"""
    current_dir = Path(__file__).parent.absolute()
    env_file = current_dir / ".env"

    if env_file.exists():
        print("✅ تم العثور على ملف .env")
        print("✅ .env file found")
    else:
        print("⚠️ ملف .env غير موجود")
        print("⚠️ .env file not found")

    # إعداد متغيرات البيئة الأساسية
    os.environ["PYTHONPATH"] = str(current_dir)
    os.environ["PYTHONUNBUFFERED"] = "1"

def main():
    """تشغيل الخادم على البورت 8002 مع تفعيل البيئة الافتراضية"""

    print("=" * 60)
    print("🚀 بدء تشغيل خادم SmartPOS على البورت 8002")
    print("🚀 Starting SmartPOS server on port 8002")
    print("=" * 60)

    # فحص إصدار Python
    if not check_python_version():
        return 1

    # الحصول على مسار المجلد الحالي
    current_dir = Path(__file__).parent.absolute()
    venv_path = current_dir / "venv"

    # تحديد مسار Python حسب نظام التشغيل
    if platform.system() == "Windows":
        python_venv = venv_path / "Scripts" / "python.exe"
    else:
        python_venv = venv_path / "bin" / "python"

    print(f"📁 مجلد العمل: {current_dir}")
    print(f"📁 Working directory: {current_dir}")
    print(f"💻 نظام التشغيل: {platform.system()}")
    print(f"💻 Operating System: {platform.system()}")

    # التحقق من وجود البيئة الافتراضية
    if not venv_path.exists():
        print("❌ البيئة الافتراضية غير موجودة!")
        print("❌ Virtual environment not found!")
        print("💡 يرجى تشغيل: ./setup_venv.sh أو python -m venv venv")
        print("💡 Please run: ./setup_venv.sh or python -m venv venv")
        return 1

    if not python_venv.exists():
        print("❌ Python في البيئة الافتراضية غير موجود!")
        print("❌ Python in virtual environment not found!")
        print(f"❌ المسار المتوقع: {python_venv}")
        print(f"❌ Expected path: {python_venv}")
        return 1

    print(f"✅ تم العثور على البيئة الافتراضية: {venv_path}")
    print(f"✅ Virtual environment found: {venv_path}")
    print(f"🐍 Python path: {python_venv}")

    # فحص المتطلبات
    if not check_requirements(venv_path):
        return 1

    # إعداد متغيرات البيئة
    setup_environment()

    # تغيير مجلد العمل إلى backend
    os.chdir(current_dir)

    # فحص وجود ملف main.py
    main_file = current_dir / "main.py"
    if not main_file.exists():
        print("❌ ملف main.py غير موجود!")
        print("❌ main.py file not found!")
        return 1

    # تشغيل الخادم باستخدام Python من البيئة الافتراضية
    try:
        print("-" * 60)
        print("🔄 تشغيل الخادم على البورت 8002...")
        print("🔄 Starting server on port 8002...")
        print("🌐 الخادم سيكون متاح على: http://localhost:8002")
        print("🌐 Server will be available at: http://localhost:8002")
        print("🌐 للشبكة المحلية: http://[YOUR_IP]:8002")
        print("🌐 For local network: http://[YOUR_IP]:8002")
        print("⏹️  للإيقاف اضغط Ctrl+C")
        print("⏹️  To stop press Ctrl+C")
        print("-" * 60)

        # تشغيل uvicorn مع Python من البيئة الافتراضية
        cmd = [
            str(python_venv),
            "-m", "uvicorn",
            "main:app",
            "--reload",
            "--host", "0.0.0.0",
            "--port", "8002",
            "--log-level", "info"
        ]

        print(f"🔧 الأمر المنفذ: {' '.join(cmd)}")
        print(f"🔧 Executing command: {' '.join(cmd)}")
        print("-" * 60)

        # تشغيل الخادم
        process = subprocess.run(cmd, cwd=current_dir)
        return process.returncode

    except KeyboardInterrupt:
        print("\n" + "=" * 60)
        print("🛑 تم إيقاف الخادم بواسطة المستخدم")
        print("🛑 Server stopped by user")
        print("=" * 60)
        return 0
    except FileNotFoundError as e:
        print(f"❌ ملف غير موجود: {e}")
        print(f"❌ File not found: {e}")
        print("💡 تأكد من تثبيت uvicorn في البيئة الافتراضية")
        print("💡 Make sure uvicorn is installed in virtual environment")
        return 1
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        print(f"❌ Error starting server: {e}")
        print("💡 تحقق من سجلات الأخطاء أعلاه")
        print("💡 Check error logs above")
        return 1

def show_help():
    """عرض معلومات المساعدة"""
    print("""
🔧 مساعدة تشغيل خادم SmartPOS
🔧 SmartPOS Server Help

الاستخدام / Usage:
    python run_server_8002.py

المتطلبات / Requirements:
    - Python 3.8+
    - البيئة الافتراضية مُفعلة / Virtual environment activated
    - المتطلبات مثبتة / Dependencies installed

استكشاف الأخطاء / Troubleshooting:
    1. تأكد من وجود البيئة الافتراضية / Ensure virtual environment exists
    2. تثبيت المتطلبات / Install requirements:
       pip install -r requirements.txt
    3. فحص ملف .env / Check .env file
    4. فحص البورت 8002 / Check port 8002 availability

للمساعدة / For help:
    python run_server_8002.py --help
    """)

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ["--help", "-h", "help"]:
        show_help()
        sys.exit(0)

    sys.exit(main())
