from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import select, func
from typing import List
from passlib.context import CryptContext

from database.session import get_db
from models.user import User
from models.sale import Sale
from schemas.user import UserCreate, UserUpdate, UserResponse
from utils.auth import get_current_active_user, get_current_admin_user

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

router = APIRouter(prefix="/api/users", tags=["users"])

@router.get("/", response_model=List[UserResponse])
async def get_users(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Get all users with sales count. Only accessible by admin users.
    """
    try:
        # Query users with sales count using subquery
        sales_count_subquery = select(
            Sale.user_id,
            func.count(Sale.id).label('sales_count')
        ).group_by(Sale.user_id).subquery()

        stmt = select(
            User,
            func.coalesce(sales_count_subquery.c.sales_count, 0).label('sales_count')
        ).outerjoin(
            sales_count_subquery, User.id == sales_count_subquery.c.user_id
        ).offset(skip).limit(limit)

        result = db.execute(stmt)
        users_with_counts = result.all()

        # Convert to response format
        users_response = []
        for user, sales_count in users_with_counts:
            user_dict = {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "full_name": user.full_name,
                "role": user.role,
                "is_active": user.is_active,
                "created_at": user.created_at,
                "updated_at": user.updated_at,
                "sales_count": int(sales_count)
            }
            users_response.append(user_dict)

        return users_response
    except Exception as e:
        print(f"Error fetching users: {e}")
        raise

@router.post("/", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def create_user(
    user: UserCreate,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Create a new user. Only accessible by admin users.
    """
    # Check if username already exists
    stmt = select(User).where(User.username == user.username)
    db_user = db.execute(stmt).scalar_one_or_none()
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already exists"
        )

    # Check if email already exists (if email is provided)
    if user.email:
        stmt = select(User).where(User.email == user.email)
        db_user = db.execute(stmt).scalar_one_or_none()
        if db_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )

    # Create user data with hashed password
    user_data = user.model_dump()
    password = user_data.pop("password")  # Remove password from dict

    # Create new user with hashed password
    new_user = User(
        **user_data,
        hashed_password=get_password_hash(password)
    )

    db.add(new_user)
    db.commit()
    db.refresh(new_user)

    # Add sales_count for response
    new_user.sales_count = 0
    return new_user

@router.get("/me", response_model=UserResponse)
async def read_user_me(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get current user's information with sales count.
    """
    # Get sales count for current user
    sales_count = db.execute(
        select(func.count(Sale.id)).where(Sale.user_id == current_user.id)
    ).scalar_one()

    # Add sales_count for response
    current_user.sales_count = int(sales_count)
    return current_user

@router.put("/me", response_model=UserResponse)
async def update_user_me(
    user: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update current user's information.
    """
    update_data = user.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(current_user, key, value)

    db.commit()
    db.refresh(current_user)

    # Add sales_count for response
    sales_count = db.execute(
        select(func.count(Sale.id)).where(Sale.user_id == current_user.id)
    ).scalar_one()
    current_user.sales_count = int(sales_count)

    return current_user

@router.get("/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: int,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific user by ID with sales count. Only accessible by admin users.
    """
    # Get user with sales count
    sales_count = db.execute(
        select(func.count(Sale.id)).where(Sale.user_id == user_id)
    ).scalar_one()

    stmt = select(User).where(User.id == user_id)
    user = db.execute(stmt).scalar_one_or_none()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Add sales_count for response
    user.sales_count = int(sales_count)
    return user

@router.post("/{user_id}/update", response_model=UserResponse)
async def update_user(
    user_id: int,
    user: UserUpdate,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Update a user. Only accessible by admin users.
    """
    stmt = select(User).where(User.id == user_id)
    db_user = db.execute(stmt).scalar_one_or_none()
    if not db_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Check if username already exists (if username is being updated)
    if user.username and user.username != db_user.username:
        stmt = select(User).where(User.username == user.username)
        existing_user = db.execute(stmt).scalar_one_or_none()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already exists"
            )

    # Update user data
    update_data = user.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_user, key, value)

    db.commit()
    db.refresh(db_user)

    # Add sales_count for response
    sales_count = db.execute(
        select(func.count(Sale.id)).where(Sale.user_id == user_id)
    ).scalar_one()
    db_user.sales_count = int(sales_count)

    return db_user

@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(
    user_id: int,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Delete a user. Only accessible by admin users.
    """
    stmt = select(User).where(User.id == user_id)
    user = db.execute(stmt).scalar_one_or_none()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Check if trying to delete own account using scalar comparison
    is_self = db.execute(
        select(User.id == current_user.id).where(User.id == user_id)
    ).scalar_one()

    if is_self:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete your own account"
        )

    # Check if user has any sales records
    sales_count = db.execute(
        select(func.count(Sale.id)).where(Sale.user_id == user_id)
    ).scalar_one()

    if sales_count > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Cannot delete user '{user.full_name}'. This user has {sales_count} sales records in the system. Please transfer or remove these records before deleting the user."
        )

    db.delete(user)
    db.commit()
    return None

@router.post("/{user_id}/reset-password", response_model=UserResponse)
async def reset_user_password(
    user_id: int,
    password_data: dict,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Reset a user's password. Only accessible by admin users.
    """
    stmt = select(User).where(User.id == user_id)
    user = db.execute(stmt).scalar_one_or_none()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Get password from request body
    password = password_data.get("password")
    if not password:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password is required"
        )

    # Update password
    setattr(user, "hashed_password", get_password_hash(password))

    db.commit()
    db.refresh(user)

    # Add sales_count for response
    sales_count = db.execute(
        select(func.count(Sale.id)).where(Sale.user_id == user_id)
    ).scalar_one()
    user.sales_count = int(sales_count)

    return user