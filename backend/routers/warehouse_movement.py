"""
Router لحركات المستودعات
يوفر API endpoints لإدارة حركات المستودعات
"""

import logging
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from datetime import datetime

from database.session import get_db
from models.user import User
from auth.dependencies import get_current_user
from services.warehouse_movement_service import WarehouseMovementService
from schemas.warehouse import (
    WarehouseMovementCreate, StockAdjustmentRequest
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/warehouse-movements", tags=["warehouse-movements"])


@router.post("/", response_model=Dict[str, Any], status_code=status.HTTP_201_CREATED)
async def record_movement(
    movement_data: WarehouseMovementCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """تسجيل حركة مستودع"""
    try:
        movement_service = WarehouseMovementService.get_instance(db)
        
        # إضافة معرف المستخدم الحالي
        movement_dict = movement_data.dict()
        movement_dict['created_by'] = current_user.id
        
        result = movement_service.record_movement(movement_dict)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        logger.info(f"تم تسجيل حركة مستودع بواسطة {current_user.username}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في تسجيل حركة المستودع: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.get("/warehouse/{warehouse_id}", response_model=Dict[str, Any])
async def get_warehouse_movements(
    warehouse_id: int,
    movement_type: Optional[str] = Query(None, description="نوع الحركة"),
    product_id: Optional[int] = Query(None, description="معرف المنتج"),
    date_from: Optional[datetime] = Query(None, description="من تاريخ"),
    date_to: Optional[datetime] = Query(None, description="إلى تاريخ"),
    reference_type: Optional[str] = Query(None, description="نوع المرجع"),
    page: int = Query(1, ge=1, description="رقم الصفحة"),
    per_page: int = Query(50, ge=1, le=100, description="عدد العناصر في الصفحة"),
    db: Session = Depends(get_db),
    _: User = Depends(get_current_user)
):
    """الحصول على حركات المستودع"""
    try:
        movement_service = WarehouseMovementService.get_instance(db)
        
        # تجميع الفلاتر
        filters = {
            'movement_type': movement_type,
            'product_id': product_id,
            'date_from': date_from,
            'date_to': date_to,
            'reference_type': reference_type,
            'page': page,
            'per_page': per_page
        }
        
        # إزالة القيم الفارغة
        filters = {k: v for k, v in filters.items() if v is not None}
        
        result = movement_service.get_warehouse_movements(warehouse_id, filters)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=result['error']
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب حركات المستودع: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.get("/product/{product_id}/history", response_model=Dict[str, Any])
async def get_product_movement_history(
    product_id: int,
    warehouse_id: Optional[int] = Query(None, description="معرف المستودع"),
    movement_type: Optional[str] = Query(None, description="نوع الحركة"),
    date_from: Optional[datetime] = Query(None, description="من تاريخ"),
    date_to: Optional[datetime] = Query(None, description="إلى تاريخ"),
    db: Session = Depends(get_db),
    _: User = Depends(get_current_user)
):
    """الحصول على تاريخ حركات المنتج"""
    try:
        movement_service = WarehouseMovementService.get_instance(db)
        
        # تجميع الفلاتر
        filters = {
            'warehouse_id': warehouse_id,
            'movement_type': movement_type,
            'date_from': date_from,
            'date_to': date_to
        }
        
        # إزالة القيم الفارغة
        filters = {k: v for k, v in filters.items() if v is not None}
        
        result = movement_service.get_product_movement_history(product_id, filters)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=result['error']
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب تاريخ حركات المنتج: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.post("/adjustment", response_model=Dict[str, Any])
async def process_stock_adjustment(
    adjustment_data: StockAdjustmentRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """معالجة تعديل المخزون"""
    try:
        movement_service = WarehouseMovementService.get_instance(db)
        
        # إضافة معرف المستخدم الحالي
        adjustment_dict = adjustment_data.dict()
        adjustment_dict['created_by'] = current_user.id
        
        result = movement_service.process_stock_adjustment(adjustment_dict)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        logger.info(f"تم تعديل المخزون بواسطة {current_user.username}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في تعديل المخزون: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.get("/warehouse/{warehouse_id}/summary", response_model=Dict[str, Any])
async def get_movement_summary(
    warehouse_id: int,
    date_from: Optional[datetime] = Query(None, description="من تاريخ"),
    date_to: Optional[datetime] = Query(None, description="إلى تاريخ"),
    db: Session = Depends(get_db),
    _: User = Depends(get_current_user)
):
    """الحصول على ملخص حركات المستودع"""
    try:
        movement_service = WarehouseMovementService.get_instance(db)
        
        # تجميع نطاق التاريخ
        date_range = None
        if date_from or date_to:
            date_range = {
                'date_from': date_from,
                'date_to': date_to
            }
        
        result = movement_service.get_movement_summary(warehouse_id, date_range)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب ملخص الحركات: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )
