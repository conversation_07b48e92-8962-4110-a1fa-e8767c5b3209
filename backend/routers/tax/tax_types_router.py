"""
API endpoints لإدارة أنواع الضرائب
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from database.session import get_db
from utils.auth import get_current_user, get_current_admin_user
from models.user import User
from services.tax import TaxTypeService
from schemas.tax import (
    TaxTypeResponse,
    TaxTypeCreate,
    TaxTypeUpdate,
    TaxTypeFilters,
    TaxTypeWithRates
)
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/tax-types", tags=["tax-types"])


@router.get("/", response_model=List[TaxTypeResponse])
async def get_tax_types(
    search: Optional[str] = Query(None, description="البحث في الاسم أو الوصف"),
    status_filter: Optional[str] = Query("all", description="الحالة: all, active, inactive"),
    tax_category: Optional[str] = Query("all", description="فئة الضريبة: all, standard, reduced, zero, exempt"),
    calculation_method: Optional[str] = Query("all", description="طريقة الحساب: all, percentage, fixed"),
    is_compound: Optional[bool] = Query(None, description="ضريبة مركبة"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    جلب جميع أنواع الضرائب مع الفلاتر
    """
    try:
        logger.info(f"🔄 [API] جلب أنواع الضرائب - المستخدم: {current_user.username}")

        # إنشاء خدمة أنواع الضرائب
        service = TaxTypeService(db, current_user)

        # إعداد الفلاتر
        filters = TaxTypeFilters(
            search=search,
            status=status_filter,
            tax_category=tax_category,
            calculation_method=calculation_method,
            is_compound=is_compound
        )

        # جلب أنواع الضرائب
        tax_types = service.get_tax_types(filters)

        logger.info(f"✅ [API] تم جلب {len(tax_types)} نوع ضريبة")

        return tax_types

    except Exception as e:
        logger.error(f"❌ [API] خطأ في جلب أنواع الضرائب: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في جلب أنواع الضرائب: {str(e)}"
        )


@router.get("/{tax_type_id}", response_model=TaxTypeWithRates)
async def get_tax_type(
    tax_type_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    جلب نوع ضريبة محدد بالمعرف مع قيمه الضريبية
    """
    try:
        logger.info(f"🔄 [API] جلب نوع الضريبة: {tax_type_id}")

        # إنشاء خدمة أنواع الضرائب
        service = TaxTypeService(db, current_user)

        # جلب نوع الضريبة
        tax_type = service.get_tax_type_by_id(tax_type_id)

        if not tax_type:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"نوع الضريبة غير موجود: {tax_type_id}"
            )

        logger.info(f"✅ [API] تم جلب نوع الضريبة: {tax_type.name_ar}")

        return tax_type

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ [API] خطأ في جلب نوع الضريبة: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في جلب نوع الضريبة: {str(e)}"
        )


@router.post("/", response_model=TaxTypeResponse, status_code=status.HTTP_201_CREATED)
async def create_tax_type(
    tax_type_data: TaxTypeCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    إنشاء نوع ضريبة جديد
    يتطلب صلاحيات المدير
    """
    try:
        logger.info(f"🔄 [API] إنشاء نوع ضريبة جديد: {tax_type_data.name_ar}")

        # إنشاء خدمة أنواع الضرائب
        service = TaxTypeService(db, current_user)

        # إنشاء نوع الضريبة
        tax_type = service.create_tax_type(tax_type_data)

        logger.info(f"✅ [API] تم إنشاء نوع الضريبة: {tax_type.name_ar}")

        return tax_type

    except ValueError as e:
        logger.warning(f"⚠️ [API] خطأ في البيانات: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"❌ [API] خطأ في إنشاء نوع الضريبة: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في إنشاء نوع الضريبة: {str(e)}"
        )


@router.put("/{tax_type_id}", response_model=TaxTypeResponse)
async def update_tax_type(
    tax_type_id: int,
    tax_type_data: TaxTypeUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    تحديث نوع ضريبة موجود
    يتطلب صلاحيات المدير
    """
    try:
        logger.info(f"🔄 [API] تحديث نوع الضريبة: {tax_type_id}")

        # إنشاء خدمة أنواع الضرائب
        service = TaxTypeService(db, current_user)

        # تحديث نوع الضريبة
        tax_type = service.update_tax_type(tax_type_id, tax_type_data)

        logger.info(f"✅ [API] تم تحديث نوع الضريبة: {tax_type.name_ar}")

        return tax_type

    except ValueError as e:
        logger.warning(f"⚠️ [API] خطأ في البيانات: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"❌ [API] خطأ في تحديث نوع الضريبة: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في تحديث نوع الضريبة: {str(e)}"
        )


@router.delete("/{tax_type_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_tax_type(
    tax_type_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    حذف نوع ضريبة
    يتطلب صلاحيات المدير
    """
    try:
        logger.info(f"🔄 [API] حذف نوع الضريبة: {tax_type_id}")

        # إنشاء خدمة أنواع الضرائب
        service = TaxTypeService(db, current_user)

        # حذف نوع الضريبة
        service.delete_tax_type(tax_type_id)

        logger.info(f"✅ [API] تم حذف نوع الضريبة: {tax_type_id}")

    except ValueError as e:
        logger.warning(f"⚠️ [API] خطأ في البيانات: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"❌ [API] خطأ في حذف نوع الضريبة: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في حذف نوع الضريبة: {str(e)}"
        )


@router.get("/{tax_type_id}/statistics")
async def get_tax_type_statistics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    جلب إحصائيات أنواع الضرائب
    """
    try:
        logger.info(f"🔄 [API] جلب إحصائيات أنواع الضرائب")

        # إنشاء خدمة أنواع الضرائب
        service = TaxTypeService(db, current_user)

        # جلب الإحصائيات
        statistics = service.get_tax_type_statistics()

        logger.info(f"✅ [API] تم جلب إحصائيات أنواع الضرائب")

        return statistics

    except Exception as e:
        logger.error(f"❌ [API] خطأ في جلب إحصائيات أنواع الضرائب: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في جلب الإحصائيات: {str(e)}"
        )
