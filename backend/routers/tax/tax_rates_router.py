"""
API endpoints لإدارة قيم الضرائب
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from decimal import Decimal

from database.session import get_db
from utils.auth import get_current_user, get_current_admin_user
from models.user import User
from services.tax import TaxRateService
from schemas.tax import (
    TaxRateResponse,
    TaxRateCreate,
    TaxRateUpdate,
    TaxRateFilters,
    TaxCalculationRequest,
    TaxCalculationResponse
)
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/tax-rates", tags=["tax-rates"])


@router.get("/", response_model=List[TaxRateResponse])
async def get_tax_rates(
    search: Optional[str] = Query(None, description="البحث في الاسم أو الوصف"),
    status_filter: Optional[str] = Query("all", description="الحالة: all, active, inactive"),
    tax_type_id: Optional[int] = Query(None, description="معرف نوع الضريبة"),
    applies_to: Optional[str] = Query("all", description="ينطبق على: all, products, services"),
    is_default: Optional[bool] = Query(None, description="القيمة الافتراضية"),
    effective_only: Optional[bool] = Query(None, description="القيم السارية فقط"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    جلب جميع قيم الضرائب مع الفلاتر
    """
    try:
        logger.info(f"🔄 [API] جلب قيم الضرائب - المستخدم: {current_user.username}")

        # إنشاء خدمة قيم الضرائب
        service = TaxRateService(db, current_user)

        # إعداد الفلاتر
        filters = TaxRateFilters(
            search=search,
            status=status_filter,
            tax_type_id=tax_type_id,
            applies_to=applies_to,
            is_default=is_default,
            effective_only=effective_only
        )

        # جلب قيم الضرائب
        tax_rates = service.get_tax_rates(filters)

        logger.info(f"✅ [API] تم جلب {len(tax_rates)} قيمة ضريبية")

        return tax_rates

    except Exception as e:
        logger.error(f"❌ [API] خطأ في جلب قيم الضرائب: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في جلب قيم الضرائب: {str(e)}"
        )


@router.get("/{tax_rate_id}", response_model=TaxRateResponse)
async def get_tax_rate(
    tax_rate_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    جلب قيمة ضريبية محددة بالمعرف
    """
    try:
        logger.info(f"🔄 [API] جلب القيمة الضريبية: {tax_rate_id}")

        # إنشاء خدمة قيم الضرائب
        service = TaxRateService(db, current_user)

        # جلب القيمة الضريبية
        tax_rate = service.get_tax_rate_by_id(tax_rate_id)

        if not tax_rate:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"القيمة الضريبية غير موجودة: {tax_rate_id}"
            )

        logger.info(f"✅ [API] تم جلب القيمة الضريبية: {tax_rate.name}")

        return tax_rate

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ [API] خطأ في جلب القيمة الضريبية: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في جلب القيمة الضريبية: {str(e)}"
        )


@router.post("/", response_model=TaxRateResponse, status_code=status.HTTP_201_CREATED)
async def create_tax_rate(
    tax_rate_data: TaxRateCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    إنشاء قيمة ضريبية جديدة
    يتطلب صلاحيات المدير
    """
    try:
        logger.info(f"🔄 [API] إنشاء قيمة ضريبية جديدة: {tax_rate_data.name}")

        # إنشاء خدمة قيم الضرائب
        service = TaxRateService(db, current_user)

        # إنشاء القيمة الضريبية
        tax_rate = service.create_tax_rate(tax_rate_data)

        logger.info(f"✅ [API] تم إنشاء القيمة الضريبية: {tax_rate.name}")

        return tax_rate

    except ValueError as e:
        logger.warning(f"⚠️ [API] خطأ في البيانات: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"❌ [API] خطأ في إنشاء القيمة الضريبية: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في إنشاء القيمة الضريبية: {str(e)}"
        )


@router.put("/{tax_rate_id}", response_model=TaxRateResponse)
async def update_tax_rate(
    tax_rate_id: int,
    tax_rate_data: TaxRateUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    تحديث قيمة ضريبية موجودة
    يتطلب صلاحيات المدير
    """
    try:
        logger.info(f"🔄 [API] تحديث القيمة الضريبية: {tax_rate_id}")

        # إنشاء خدمة قيم الضرائب
        service = TaxRateService(db, current_user)

        # تحديث القيمة الضريبية
        tax_rate = service.update_tax_rate(tax_rate_id, tax_rate_data)

        logger.info(f"✅ [API] تم تحديث القيمة الضريبية: {tax_rate.name}")

        return tax_rate

    except ValueError as e:
        logger.warning(f"⚠️ [API] خطأ في البيانات: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"❌ [API] خطأ في تحديث القيمة الضريبية: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في تحديث القيمة الضريبية: {str(e)}"
        )


@router.delete("/{tax_rate_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_tax_rate(
    tax_rate_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    حذف قيمة ضريبية
    يتطلب صلاحيات المدير
    """
    try:
        logger.info(f"🔄 [API] حذف القيمة الضريبية: {tax_rate_id}")

        # إنشاء خدمة قيم الضرائب
        service = TaxRateService(db, current_user)

        # حذف القيمة الضريبية
        service.delete_tax_rate(tax_rate_id)

        logger.info(f"✅ [API] تم حذف القيمة الضريبية: {tax_rate_id}")

    except ValueError as e:
        logger.warning(f"⚠️ [API] خطأ في البيانات: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"❌ [API] خطأ في حذف القيمة الضريبية: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في حذف القيمة الضريبية: {str(e)}"
        )


@router.post("/calculate", response_model=TaxCalculationResponse)
async def calculate_tax(
    calculation_request: TaxCalculationRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    حساب الضريبة على أساس القيمة الضريبية والمبلغ الأساسي
    """
    try:
        logger.info(f"🔄 [API] حساب الضريبة للقيمة: {calculation_request.tax_rate_id}")

        # إنشاء خدمة قيم الضرائب
        service = TaxRateService(db, current_user)

        # حساب الضريبة
        result = service.calculate_tax(
            calculation_request.tax_rate_id,
            calculation_request.base_amount
        )

        logger.info(f"✅ [API] تم حساب الضريبة بنجاح")

        return result

    except ValueError as e:
        logger.warning(f"⚠️ [API] خطأ في البيانات: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"❌ [API] خطأ في حساب الضريبة: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في حساب الضريبة: {str(e)}"
        )
