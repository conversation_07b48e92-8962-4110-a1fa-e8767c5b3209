"""
Router لطلبات التحويل
يوفر API endpoints لإدارة طلبات التحويل بين المستودعات
"""

import logging
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from datetime import datetime

from database.session import get_db
from models.user import User
from auth.dependencies import get_current_user
from services.transfer_request_service import TransferRequestService
from schemas.warehouse import (
    TransferRequestCreate, TransferApprovalRequest
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/transfer-requests", tags=["transfer-requests"])


@router.post("/", response_model=Dict[str, Any], status_code=status.HTTP_201_CREATED)
async def create_transfer_request(
    request_data: TransferRequestCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """إنشاء طلب تحويل جديد"""
    try:
        transfer_service = TransferRequestService.get_instance(db)
        
        # إضافة معرف المستخدم الحالي
        request_dict = request_data.dict()
        request_dict['requested_by'] = current_user.id
        
        result = transfer_service.create_transfer_request(request_dict)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        logger.info(f"تم إنشاء طلب تحويل جديد بواسطة {current_user.username}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في إنشاء طلب التحويل: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.get("/pending", response_model=Dict[str, Any])
async def get_pending_transfers(
    db: Session = Depends(get_db),
    _: User = Depends(get_current_user)
):
    """الحصول على طلبات التحويل المعلقة"""
    try:
        transfer_service = TransferRequestService.get_instance(db)
        result = transfer_service.get_pending_transfers()
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب طلبات التحويل المعلقة: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.get("/history", response_model=Dict[str, Any])
async def get_transfer_history(
    status_filter: Optional[str] = Query(None, description="فلتر الحالة"),
    from_warehouse_id: Optional[int] = Query(None, description="معرف المستودع المصدر"),
    to_warehouse_id: Optional[int] = Query(None, description="معرف المستودع الوجهة"),
    date_from: Optional[datetime] = Query(None, description="من تاريخ"),
    date_to: Optional[datetime] = Query(None, description="إلى تاريخ"),
    page: int = Query(1, ge=1, description="رقم الصفحة"),
    per_page: int = Query(20, ge=1, le=100, description="عدد العناصر في الصفحة"),
    db: Session = Depends(get_db),
    _: User = Depends(get_current_user)
):
    """الحصول على تاريخ طلبات التحويل"""
    try:
        transfer_service = TransferRequestService.get_instance(db)
        
        # تجميع الفلاتر
        filters = {
            'status': status_filter,
            'from_warehouse_id': from_warehouse_id,
            'to_warehouse_id': to_warehouse_id,
            'date_from': date_from,
            'date_to': date_to,
            'page': page,
            'per_page': per_page
        }
        
        # إزالة القيم الفارغة
        filters = {k: v for k, v in filters.items() if v is not None}
        
        result = transfer_service.get_transfer_history(filters)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب تاريخ طلبات التحويل: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.post("/{request_id}/approve", response_model=Dict[str, Any])
async def approve_transfer_request(
    request_id: int,
    approval_data: Optional[TransferApprovalRequest] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الموافقة على طلب التحويل"""
    try:
        transfer_service = TransferRequestService.get_instance(db)
        
        approval_dict = approval_data.dict() if approval_data else None
        result = transfer_service.approve_transfer_request(request_id, int(current_user.id), approval_dict)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        logger.info(f"تم الموافقة على طلب التحويل {request_id} بواسطة {current_user.username}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في الموافقة على طلب التحويل: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.post("/{request_id}/process", response_model=Dict[str, Any])
async def process_transfer(
    request_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """معالجة التحويل (تغيير الحالة إلى في الطريق)"""
    try:
        transfer_service = TransferRequestService.get_instance(db)
        result = transfer_service.process_transfer(request_id)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        logger.info(f"تم بدء معالجة طلب التحويل {request_id} بواسطة {current_user.username}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في معالجة طلب التحويل: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.post("/{request_id}/complete", response_model=Dict[str, Any])
async def complete_transfer(
    request_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """إكمال التحويل"""
    try:
        transfer_service = TransferRequestService.get_instance(db)
        result = transfer_service.complete_transfer(request_id)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        logger.info(f"تم إكمال طلب التحويل {request_id} بواسطة {current_user.username}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في إكمال طلب التحويل: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.post("/{request_id}/cancel", response_model=Dict[str, Any])
async def cancel_transfer_request(
    request_id: int,
    reason: str = Query(..., description="سبب الإلغاء"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """إلغاء طلب التحويل"""
    try:
        transfer_service = TransferRequestService.get_instance(db)
        result = transfer_service.cancel_transfer_request(request_id, reason)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        logger.info(f"تم إلغاء طلب التحويل {request_id} بواسطة {current_user.username}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في إلغاء طلب التحويل: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )
