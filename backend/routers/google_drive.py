"""
API endpoints لخدمة Google Drive
"""

import json
import logging
import secrets
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Request
from fastapi.responses import RedirectResponse, HTMLResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel

from database.session import get_db
from utils.auth import get_current_admin_user
from models.user import User
from services.google_drive_service import GoogleDriveService
from models.setting import Setting
from config.google_drive_config import GOOGLE_DRIVE_CREDENTIALS, GOOGLE_DRIVE_SCOPES, REDIRECT_URI, OAUTH_SUCCESS_PAGE, OAUTH_ERROR_PAGE

# تخزين مؤقت لحالات OAuth
oauth_states = {}

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/google-drive", tags=["Google Drive"])

# Pydantic models
class GoogleDriveStatusResponse(BaseModel):
    available: bool
    configured: bool
    enabled: bool
    user_email: Optional[str] = None
    error: Optional[str] = None

class GoogleDriveTestResponse(BaseModel):
    success: bool
    message: Optional[str] = None
    user_email: Optional[str] = None
    error: Optional[str] = None

class GoogleDriveUploadResponse(BaseModel):
    success: bool
    file_id: Optional[str] = None
    file_name: Optional[str] = None
    file_size: Optional[str] = None
    message: Optional[str] = None
    error: Optional[str] = None

class GoogleDriveFilesResponse(BaseModel):
    success: bool
    files: list = []
    count: int = 0
    message: Optional[str] = None
    error: Optional[str] = None

class GoogleDriveStatsResponse(BaseModel):
    success: bool
    user_email: Optional[str] = None
    display_name: Optional[str] = None
    total_storage: Optional[int] = None
    used_storage: Optional[int] = None
    available_storage: Optional[int] = None
    total_files: Optional[int] = None
    storage_percentage: Optional[float] = None
    error: Optional[str] = None

class CredentialsUploadRequest(BaseModel):
    credentials_json: str

class OAuthStartResponse(BaseModel):
    auth_url: str
    state: str

@router.get("/status", response_model=GoogleDriveStatusResponse)
async def get_google_drive_status(
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    الحصول على حالة خدمة Google Drive
    """
    try:
        drive_service = GoogleDriveService(db_session=db)

        status_data = {
            "available": drive_service.is_available(),
            "configured": drive_service.is_configured(),
            "enabled": drive_service.is_enabled(),
            "user_email": None,
            "error": None
        }

        # إذا كانت الخدمة متاحة ومكونة، اختبر الاتصال
        if status_data["available"] and status_data["configured"]:
            test_result = drive_service.test_connection()
            if test_result.get("success", False):
                status_data["user_email"] = test_result.get("user_email")
            else:
                status_data["error"] = test_result.get("error")

        return GoogleDriveStatusResponse(**status_data)

    except Exception as e:
        logger.error(f"خطأ في جلب حالة Google Drive: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في جلب حالة Google Drive: {str(e)}"
        )

@router.post("/test-connection", response_model=GoogleDriveTestResponse)
async def test_google_drive_connection(
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    اختبار الاتصال مع Google Drive
    """
    try:
        drive_service = GoogleDriveService(db_session=db)

        if not drive_service.is_available():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="مكتبات Google APIs غير متوفرة"
            )

        if not drive_service.is_configured():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="خدمة Google Drive غير مكونة"
            )

        result = drive_service.test_connection()
        return GoogleDriveTestResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في اختبار الاتصال: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في اختبار الاتصال: {str(e)}"
        )

@router.get("/stats", response_model=GoogleDriveStatsResponse)
async def get_google_drive_stats(
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    الحصول على إحصائيات Google Drive
    """
    try:
        drive_service = GoogleDriveService(db_session=db)

        if not drive_service.is_available():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="مكتبات Google APIs غير متوفرة"
            )

        if not drive_service.is_configured():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="خدمة Google Drive غير مكونة"
            )

        result = drive_service.get_storage_stats()
        return GoogleDriveStatsResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب إحصائيات Google Drive: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في جلب إحصائيات Google Drive: {str(e)}"
        )

@router.post("/start-oauth", response_model=OAuthStartResponse)
async def start_oauth_flow(
    current_user: User = Depends(get_current_admin_user)
):
    """
    بدء عملية OAuth مع Google
    """
    try:
        from google_auth_oauthlib.flow import Flow

        # إنشاء OAuth flow باستخدام البيانات الثابتة
        flow = Flow.from_client_config(
            GOOGLE_DRIVE_CREDENTIALS,
            scopes=GOOGLE_DRIVE_SCOPES
        )
        flow.redirect_uri = REDIRECT_URI

        # توليد state عشوائي للأمان
        state = secrets.token_urlsafe(32)

        # حفظ معلومات OAuth مؤقتاً
        oauth_states[state] = {
            "flow": flow,
            "user_id": current_user.id
        }

        # الحصول على رابط التفويض
        auth_url, _ = flow.authorization_url(
            access_type='offline',
            include_granted_scopes='true',
            state=state,
            prompt='consent'  # إجبار Google على طلب الموافقة مرة أخرى
        )

        return OAuthStartResponse(auth_url=auth_url, state=state)

    except Exception as e:
        logger.error(f"خطأ في بدء OAuth flow: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في بدء عملية التفويض: {str(e)}"
        )

@router.get("/oauth-callback")
async def oauth_callback(
    request: Request,
    db: Session = Depends(get_db)
):
    """
    معالجة callback من Google OAuth
    """
    try:
        # الحصول على المعاملات من URL
        code = request.query_params.get("code")
        state = request.query_params.get("state")
        error = request.query_params.get("error")

        if error:
            return RedirectResponse(
                url=f"{OAUTH_ERROR_PAGE}?error={error}",
                status_code=302
            )

        if not code or not state:
            return RedirectResponse(
                url=f"{OAUTH_ERROR_PAGE}?error=missing_parameters",
                status_code=302
            )

        # التحقق من صحة state
        if state not in oauth_states:
            return RedirectResponse(
                url=f"{OAUTH_ERROR_PAGE}?error=invalid_state",
                status_code=302
            )

        oauth_data = oauth_states[state]
        flow = oauth_data["flow"]

        # تبديل authorization code بـ access token
        flow.fetch_token(code=code)

        # حفظ credentials في قاعدة البيانات
        credentials = flow.credentials
        credentials_json = credentials.to_json()

        # حفظ في الإعدادات
        setting = db.query(Setting).filter(Setting.key == "google_drive_credentials").first()
        if setting:
            setting.value = credentials_json
        else:
            setting = Setting(key="google_drive_credentials", value=credentials_json)
            db.add(setting)

        db.commit()

        # تنظيف البيانات المؤقتة
        del oauth_states[state]

        return RedirectResponse(
            url=OAUTH_SUCCESS_PAGE,
            status_code=302
        )

    except Exception as e:
        logger.error(f"خطأ في OAuth callback: {e}")
        return RedirectResponse(
            url=f"{OAUTH_ERROR_PAGE}?error=callback_failed",
            status_code=302
        )

@router.get("/oauth-success")
async def oauth_success_page():
    """
    صفحة نجاح OAuth - تغلق النافذة وترسل رسالة للنافذة الأصلية
    """
    html_content = """
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تم تسجيل الدخول بنجاح</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                margin: 0;
                padding: 20px;
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
                color: white;
            }
            .container {
                text-align: center;
                background: rgba(255, 255, 255, 0.1);
                padding: 40px;
                border-radius: 15px;
                backdrop-filter: blur(10px);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            }
            .success-icon {
                font-size: 4rem;
                margin-bottom: 20px;
                color: #4ade80;
            }
            h1 {
                margin: 0 0 10px 0;
                font-size: 2rem;
            }
            p {
                margin: 10px 0;
                font-size: 1.1rem;
                opacity: 0.9;
            }
            .spinner {
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-top: 3px solid white;
                border-radius: 50%;
                width: 30px;
                height: 30px;
                animation: spin 1s linear infinite;
                margin: 20px auto;
            }
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="success-icon">✅</div>
            <h1>تم تسجيل الدخول بنجاح!</h1>
            <p>تم ربط حساب Google Drive بنجاح</p>
            <p>سيتم إغلاق هذه النافذة تلقائياً...</p>
            <div class="spinner"></div>
        </div>

        <script>
            // إرسال رسالة للنافذة الأصلية
            if (window.opener) {
                window.opener.postMessage({
                    type: 'GOOGLE_OAUTH_SUCCESS',
                    success: true,
                    message: 'تم تسجيل الدخول مع Google Drive بنجاح!'
                }, '*');
            }

            // إغلاق النافذة بعد 2 ثانية
            setTimeout(() => {
                window.close();
            }, 2000);
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@router.get("/oauth-error")
async def oauth_error_page(error: str = "unknown"):
    """
    صفحة خطأ OAuth - تغلق النافذة وترسل رسالة خطأ للنافذة الأصلية
    """
    error_messages = {
        "access_denied": "تم رفض الوصول من قبل المستخدم",
        "invalid_state": "حالة غير صالحة - يرجى المحاولة مرة أخرى",
        "missing_parameters": "معاملات مفقودة في الاستجابة",
        "callback_failed": "فشل في معالجة الاستجابة",
        "unknown": "خطأ غير معروف"
    }

    error_message = error_messages.get(error, error_messages["unknown"])

    html_content = f"""
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>فشل تسجيل الدخول</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
                margin: 0;
                padding: 20px;
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
                color: white;
            }}
            .container {{
                text-align: center;
                background: rgba(255, 255, 255, 0.1);
                padding: 40px;
                border-radius: 15px;
                backdrop-filter: blur(10px);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            }}
            .error-icon {{
                font-size: 4rem;
                margin-bottom: 20px;
                color: #fca5a5;
            }}
            h1 {{
                margin: 0 0 10px 0;
                font-size: 2rem;
            }}
            p {{
                margin: 10px 0;
                font-size: 1.1rem;
                opacity: 0.9;
            }}
            .spinner {{
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-top: 3px solid white;
                border-radius: 50%;
                width: 30px;
                height: 30px;
                animation: spin 1s linear infinite;
                margin: 20px auto;
            }}
            @keyframes spin {{
                0% {{ transform: rotate(0deg); }}
                100% {{ transform: rotate(360deg); }}
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="error-icon">❌</div>
            <h1>فشل تسجيل الدخول</h1>
            <p>{error_message}</p>
            <p>سيتم إغلاق هذه النافذة تلقائياً...</p>
            <div class="spinner"></div>
        </div>

        <script>
            // إرسال رسالة للنافذة الأصلية
            if (window.opener) {{
                window.opener.postMessage({{
                    type: 'GOOGLE_OAUTH_ERROR',
                    success: false,
                    error: '{error}',
                    message: '{error_message}'
                }}, '*');
            }}

            // إغلاق النافذة بعد 3 ثواني
            setTimeout(() => {{
                window.close();
            }}, 3000);
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@router.post("/logout")
async def logout_google_drive(
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    تسجيل الخروج من Google Drive وحذف بيانات الاعتماد
    """
    try:
        # حذف بيانات الاعتماد من قاعدة البيانات
        setting = db.query(Setting).filter(Setting.key == "google_drive_credentials").first()
        if setting:
            db.delete(setting)
            db.commit()

        return {
            "success": True,
            "message": "تم تسجيل الخروج من Google Drive بنجاح"
        }
    except Exception as e:
        logger.error(f"خطأ في تسجيل الخروج من Google Drive: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في تسجيل الخروج: {str(e)}"
        )

@router.post("/reset")
async def reset_google_drive_state(
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    إعادة تعيين حالة Google Drive (حذف جميع البيانات المحفوظة)
    """
    try:
        # حذف جميع إعدادات Google Drive
        settings_to_delete = [
            "google_drive_credentials",
            "google_drive_backup_folder_id",
            "google_drive_enabled"
        ]

        for setting_key in settings_to_delete:
            setting = db.query(Setting).filter(Setting.key == setting_key).first()
            if setting:
                db.delete(setting)

        db.commit()

        return {
            "success": True,
            "message": "تم إعادة تعيين حالة Google Drive بنجاح"
        }
    except Exception as e:
        logger.error(f"خطأ في إعادة تعيين حالة Google Drive: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في إعادة تعيين الحالة: {str(e)}"
        )

@router.post("/upload-credentials")
async def upload_credentials(
    request: CredentialsUploadRequest,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    رفع بيانات اعتماد Google Drive
    """
    try:
        # التحقق من صحة JSON
        try:
            credentials_data = json.loads(request.credentials_json)
        except json.JSONDecodeError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="بيانات الاعتماد غير صالحة (JSON غير صحيح)"
            )

        # التحقق من وجود الحقول المطلوبة
        required_fields = ["client_id", "client_secret", "refresh_token"]
        missing_fields = [field for field in required_fields if field not in credentials_data]

        if missing_fields:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"حقول مطلوبة مفقودة: {', '.join(missing_fields)}"
            )

        # حفظ بيانات الاعتماد
        drive_service = GoogleDriveService(db_session=db)
        success = drive_service._save_setting_value("google_drive_credentials", request.credentials_json)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="فشل في حفظ بيانات الاعتماد"
            )

        # اختبار الاتصال
        test_result = drive_service.test_connection()

        return {
            "success": True,
            "message": "تم حفظ بيانات الاعتماد بنجاح",
            "connection_test": test_result
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في رفع بيانات الاعتماد: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في رفع بيانات الاعتماد: {str(e)}"
        )

@router.post("/upload-backup", response_model=GoogleDriveUploadResponse)
async def upload_backup_to_drive(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    رفع ملف نسخة احتياطية إلى Google Drive
    """
    try:
        drive_service = GoogleDriveService(db_session=db)

        if not drive_service.is_available():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="مكتبات Google APIs غير متوفرة"
            )

        if not drive_service.is_configured():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="خدمة Google Drive غير مكونة"
            )

        # حفظ الملف مؤقتاً
        import tempfile
        import os

        with tempfile.NamedTemporaryFile(delete=False, suffix=".db") as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name

        try:
            # رفع الملف إلى Google Drive
            result = drive_service.upload_backup_file(temp_file_path, file.filename)
            return GoogleDriveUploadResponse(**result)
        finally:
            # حذف الملف المؤقت
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في رفع النسخة الاحتياطية: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في رفع النسخة الاحتياطية: {str(e)}"
        )

@router.get("/files", response_model=GoogleDriveFilesResponse)
async def list_backup_files(
    limit: int = 50,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    جلب قائمة النسخ الاحتياطية من Google Drive
    """
    try:
        drive_service = GoogleDriveService(db_session=db)

        if not drive_service.is_available():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="مكتبات Google APIs غير متوفرة"
            )

        if not drive_service.is_configured():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="خدمة Google Drive غير مكونة"
            )

        result = drive_service.list_backup_files(limit=limit)
        return GoogleDriveFilesResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب قائمة النسخ الاحتياطية: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في جلب قائمة النسخ الاحتياطية: {str(e)}"
        )

@router.get("/download/{file_id}")
async def download_backup_file(
    file_id: str,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    تحميل ملف نسخة احتياطية من Google Drive
    """
    try:
        drive_service = GoogleDriveService(db_session=db)

        if not drive_service.is_available():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="مكتبات Google APIs غير متوفرة"
            )

        if not drive_service.is_configured():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="خدمة Google Drive غير مكونة"
            )

        # تحميل الملف من Google Drive
        result = drive_service.download_backup_file(file_id)

        if not result["success"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["error"]
            )

        # إرجاع الملف كـ StreamingResponse
        from fastapi.responses import StreamingResponse
        import io

        file_content = result["file_content"]
        file_name = result["file_name"]

        # إرجاع الملف كـ StreamingResponse
        return StreamingResponse(
            io.BytesIO(file_content),
            media_type="application/octet-stream",
            headers={
                "Content-Disposition": f"attachment; filename={file_name}",
                "Content-Length": str(len(file_content))
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في تحميل النسخة الاحتياطية: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في تحميل النسخة الاحتياطية: {str(e)}"
        )

@router.delete("/files/{file_id}")
async def delete_backup_file(
    file_id: str,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    حذف ملف نسخة احتياطية محدد من Google Drive
    """
    try:
        drive_service = GoogleDriveService(db_session=db)

        if not drive_service.is_available():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="مكتبات Google APIs غير متوفرة"
            )

        if not drive_service.is_configured():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="خدمة Google Drive غير مكونة"
            )

        result = drive_service.delete_backup_file(file_id)

        if result["success"]:
            return {"success": True, "message": result["message"]}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result["error"]
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في حذف الملف: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في حذف الملف: {str(e)}"
        )

@router.post("/cleanup")
async def cleanup_old_backups(
    keep_count: int = 10,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    تنظيف النسخ الاحتياطية القديمة من Google Drive
    """
    try:
        drive_service = GoogleDriveService(db_session=db)

        if not drive_service.is_available():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="مكتبات Google APIs غير متوفرة"
            )

        if not drive_service.is_configured():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="خدمة Google Drive غير مكونة"
            )

        result = drive_service.delete_old_backups(keep_count=keep_count)

        if result["success"]:
            return {"success": True, "message": result["message"]}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result["error"]
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في تنظيف النسخ الاحتياطية: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في تنظيف النسخ الاحتياطية: {str(e)}"
        )
