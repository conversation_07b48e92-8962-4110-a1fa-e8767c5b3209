"""
Router لمخزون المستودعات
يوفر API endpoints لإدارة مخزون المستودعات
"""

import logging
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from database.session import get_db
from models.user import User
from auth.dependencies import get_current_user
from services.warehouse_inventory_service import WarehouseInventoryService
from schemas.warehouse import (
    WarehouseInventoryUpdate
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/warehouse-inventory", tags=["warehouse-inventory"])


@router.get("/product/{product_id}", response_model=Dict[str, Any])
async def get_product_inventory(
    product_id: int,
    db: Session = Depends(get_db),
    _: User = Depends(get_current_user)
):
    """الحصول على مخزون المنتج في جميع المستودعات"""
    try:
        inventory_service = WarehouseInventoryService.get_instance(db)
        result = inventory_service.get_product_inventory_by_warehouse(product_id)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=result['error']
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب مخزون المنتج: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.get("/warehouse/{warehouse_id}", response_model=Dict[str, Any])
async def get_warehouse_inventory(
    warehouse_id: int,
    low_stock_only: bool = Query(False, description="المنتجات قليلة المخزون فقط"),
    search: Optional[str] = Query(None, description="البحث في اسم المنتج أو الباركود"),
    min_quantity: Optional[float] = Query(None, description="الحد الأدنى للكمية"),
    max_quantity: Optional[float] = Query(None, description="الحد الأقصى للكمية"),
    db: Session = Depends(get_db),
    _: User = Depends(get_current_user)
):
    """الحصول على مخزون المستودع"""
    try:
        inventory_service = WarehouseInventoryService.get_instance(db)
        
        # تجميع الفلاتر
        filters = {
            'low_stock_only': low_stock_only,
            'search': search,
            'min_quantity': min_quantity,
            'max_quantity': max_quantity
        }
        
        # إزالة القيم الفارغة
        filters = {k: v for k, v in filters.items() if v is not None}
        
        result = inventory_service.get_warehouse_inventory(warehouse_id, filters)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=result['error']
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب مخزون المستودع: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.put("/warehouse/{warehouse_id}/product/{product_id}", response_model=Dict[str, Any])
async def update_stock_levels(
    warehouse_id: int,
    product_id: int,
    inventory_data: WarehouseInventoryUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """تحديث مستويات المخزون"""
    try:
        inventory_service = WarehouseInventoryService.get_instance(db)
        
        # تحويل البيانات وإزالة القيم الفارغة
        update_data = {k: v for k, v in inventory_data.dict().items() if v is not None}
        
        result = inventory_service.update_stock_levels(warehouse_id, product_id, update_data)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        logger.info(f"تم تحديث مستويات المخزون للمنتج {product_id} في المستودع {warehouse_id} بواسطة {current_user.username}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في تحديث مستويات المخزون: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.get("/warehouse/{warehouse_id}/product/{product_id}/availability", response_model=Dict[str, Any])
async def check_stock_availability(
    warehouse_id: int,
    product_id: int,
    quantity: float = Query(..., gt=0, description="الكمية المطلوبة"),
    db: Session = Depends(get_db),
    _: User = Depends(get_current_user)
):
    """التحقق من توفر المخزون"""
    try:
        inventory_service = WarehouseInventoryService.get_instance(db)
        result = inventory_service.check_stock_availability(warehouse_id, product_id, quantity)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في التحقق من توفر المخزون: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.get("/warehouse/{warehouse_id}/low-stock", response_model=Dict[str, Any])
async def get_low_stock_items(
    warehouse_id: int,
    db: Session = Depends(get_db),
    _: User = Depends(get_current_user)
):
    """الحصول على المنتجات قليلة المخزون"""
    try:
        inventory_service = WarehouseInventoryService.get_instance(db)
        result = inventory_service.get_low_stock_items(warehouse_id)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب المنتجات قليلة المخزون: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.post("/warehouse/{warehouse_id}/product/{product_id}/reserve", response_model=Dict[str, Any])
async def reserve_stock(
    warehouse_id: int,
    product_id: int,
    quantity: float = Query(..., gt=0, description="الكمية المراد حجزها"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """حجز المخزون"""
    try:
        inventory_service = WarehouseInventoryService.get_instance(db)
        result = inventory_service.reserve_stock(warehouse_id, product_id, quantity)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        logger.info(f"تم حجز {quantity} من المنتج {product_id} في المستودع {warehouse_id} بواسطة {current_user.username}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في حجز المخزون: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.post("/warehouse/{warehouse_id}/product/{product_id}/release", response_model=Dict[str, Any])
async def release_reserved_stock(
    warehouse_id: int,
    product_id: int,
    quantity: float = Query(..., gt=0, description="الكمية المراد إلغاء حجزها"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """إلغاء حجز المخزون"""
    try:
        inventory_service = WarehouseInventoryService.get_instance(db)
        result = inventory_service.release_reserved_stock(warehouse_id, product_id, quantity)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        logger.info(f"تم إلغاء حجز {quantity} من المنتج {product_id} في المستودع {warehouse_id} بواسطة {current_user.username}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في إلغاء حجز المخزون: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )
