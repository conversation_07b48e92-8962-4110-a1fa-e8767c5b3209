from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr
from typing import Dict, Any
import logging

from database.session import get_db
from models.user import User
from utils.auth import get_current_user_optional
from services.email_service import email_service

# إعداد السجلات
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/support", tags=["support"])

class StoreInfo(BaseModel):
    store_name: str
    store_address: str
    store_phone: str
    store_email: str

class SupportEmailRequest(BaseModel):
    subject: str
    message: str
    senderName: str
    senderEmail: EmailStr
    storeInfo: StoreInfo

@router.post("/send-email")
async def send_support_email(
    request: SupportEmailRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user_optional)
):
    """
    إرسال رسالة دعم عبر البريد الإلكتروني
    """
    try:
        # التحقق من صحة البيانات
        if not request.subject.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="موضوع الرسالة مطلوب"
            )
        
        if not request.message.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="محتوى الرسالة مطلوب"
            )
        
        if not request.senderName.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="اسم المرسل مطلوب"
            )

        # إعداد معلومات المستخدم
        user_info = {
            "sender_name": request.senderName,
            "sender_email": str(request.senderEmail),
            "subject": request.subject,
            "message": request.message,
            "username": current_user.username if current_user else "غير مسجل",
            "user_role": current_user.role if current_user else "زائر",
            "user_id": current_user.id if current_user else None
        }

        # إعداد معلومات المؤسسة
        store_info = {
            "store_name": request.storeInfo.store_name,
            "store_address": request.storeInfo.store_address,
            "store_phone": request.storeInfo.store_phone,
            "store_email": request.storeInfo.store_email
        }

        # إرسال البريد الإلكتروني
        email_result = email_service.send_support_email_to_support(
            user_info=user_info,
            store_info=store_info
        )

        if email_result.get('success'):
            logger.info(f"Support email sent successfully from {request.senderEmail}")
            return {
                "success": True,
                "message": "تم إرسال رسالتك بنجاح! سنرد عليك في أقرب وقت ممكن.",
                "support_email": email_result.get("support_email"),
                "message_id": email_result.get("message_id")
            }
        else:
            error_message = email_result.get('error', 'خطأ غير معروف')
            logger.error(f"Failed to send support email: {error_message}")

            # تحديد نوع الخطأ وإرجاع رسالة مناسبة
            if 'connection' in error_message.lower() or 'network' in error_message.lower():
                detail = "فشل في الاتصال بخادم البريد الإلكتروني. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى."
            elif 'authentication' in error_message.lower() or 'login' in error_message.lower():
                detail = "خطأ في إعدادات البريد الإلكتروني. يرجى التواصل مع المدير."
            elif 'timeout' in error_message.lower():
                detail = "انتهت مهلة الاتصال. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى."
            else:
                detail = f"فشل في إرسال الرسالة: {email_result.get('message', 'خطأ في الخادم')}"

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=detail
            )

    except HTTPException:
        raise
    except Exception as e:
        error_str = str(e)
        logger.error(f"Error sending support email: {error_str}")

        # تحديد نوع الخطأ العام
        if 'connection' in error_str.lower() or 'network' in error_str.lower():
            detail = "مشكلة في الاتصال بالشبكة. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى."
        elif 'timeout' in error_str.lower():
            detail = "انتهت مهلة الاتصال. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى."
        else:
            detail = "حدث خطأ أثناء إرسال الرسالة. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى."

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail
        )
