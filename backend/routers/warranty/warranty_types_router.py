"""
API endpoints لإدارة أنواع الضمانات
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from database.session import get_db
from utils.auth import get_current_user, get_current_admin_user
from models.user import User
from services.warranty import WarrantyTypeService
from schemas.warranty import (
    WarrantyTypeResponse,
    WarrantyTypeCreate,
    WarrantyTypeUpdate,
    WarrantyTypeFilters
)
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/warranty-types", tags=["warranty-types"])


@router.get("/", response_model=List[WarrantyTypeResponse])
async def get_warranty_types(
    search: Optional[str] = Query(None, description="البحث في الاسم أو الوصف"),
    status: Optional[str] = Query("all", description="الحالة: all, active, inactive"),
    coverage_type: Optional[str] = Query("all", description="نوع التغطية: all, full, partial, limited"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    جلب جميع أنواع الضمانات مع الفلاتر
    """
    try:
        logger.info(f"🔄 [API] جلب أنواع الضمانات - المستخدم: {current_user.username}")

        # إنشاء خدمة أنواع الضمانات
        service = WarrantyTypeService(db, current_user)

        # إعداد الفلاتر
        filters = WarrantyTypeFilters(
            search=search,
            status=status,
            coverage_type=coverage_type
        )

        # جلب أنواع الضمانات
        warranty_types = service.get_warranty_types(filters)

        logger.info(f"✅ [API] تم جلب {len(warranty_types)} نوع ضمان")

        return warranty_types

    except Exception as e:
        logger.error(f"❌ [API] خطأ في جلب أنواع الضمانات: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في جلب أنواع الضمانات: {str(e)}"
        )


@router.get("/{warranty_type_id}", response_model=WarrantyTypeResponse)
async def get_warranty_type(
    warranty_type_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    جلب نوع ضمان محدد بالمعرف
    """
    try:
        logger.info(f"🔄 [API] جلب نوع الضمان: {warranty_type_id}")

        # إنشاء خدمة أنواع الضمانات
        service = WarrantyTypeService(db, current_user)

        # جلب نوع الضمان
        warranty_type = service.get_warranty_type_by_id(warranty_type_id)

        if not warranty_type:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"نوع الضمان غير موجود: {warranty_type_id}"
            )

        logger.info(f"✅ [API] تم جلب نوع الضمان: {warranty_type.name_ar}")

        return warranty_type

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ [API] خطأ في جلب نوع الضمان: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في جلب نوع الضمان: {str(e)}"
        )


@router.post("/", response_model=WarrantyTypeResponse, status_code=status.HTTP_201_CREATED)
async def create_warranty_type(
    warranty_type_data: WarrantyTypeCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    إنشاء نوع ضمان جديد
    يتطلب صلاحيات المدير
    """
    try:
        logger.info(f"🔄 [API] إنشاء نوع ضمان جديد: {warranty_type_data.name_ar}")

        # إنشاء خدمة أنواع الضمانات
        service = WarrantyTypeService(db, current_user)

        # إنشاء نوع الضمان
        warranty_type = service.create_warranty_type(warranty_type_data)

        logger.info(f"✅ [API] تم إنشاء نوع الضمان: {warranty_type.name_ar}")

        return warranty_type

    except ValueError as e:
        logger.warning(f"⚠️ [API] خطأ في البيانات: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"❌ [API] خطأ في إنشاء نوع الضمان: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في إنشاء نوع الضمان: {str(e)}"
        )


@router.put("/{warranty_type_id}", response_model=WarrantyTypeResponse)
async def update_warranty_type(
    warranty_type_id: int,
    warranty_type_data: WarrantyTypeUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    تحديث نوع ضمان موجود
    يتطلب صلاحيات المدير
    """
    try:
        logger.info(f"🔄 [API] تحديث نوع الضمان: {warranty_type_id}")

        # إنشاء خدمة أنواع الضمانات
        service = WarrantyTypeService(db, current_user)

        # تحديث نوع الضمان
        warranty_type = service.update_warranty_type(warranty_type_id, warranty_type_data)

        logger.info(f"✅ [API] تم تحديث نوع الضمان: {warranty_type.name_ar}")

        return warranty_type

    except ValueError as e:
        logger.warning(f"⚠️ [API] خطأ في البيانات: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"❌ [API] خطأ في تحديث نوع الضمان: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في تحديث نوع الضمان: {str(e)}"
        )


@router.delete("/{warranty_type_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_warranty_type(
    warranty_type_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    حذف نوع ضمان
    يتطلب صلاحيات المدير
    """
    try:
        logger.info(f"🔄 [API] حذف نوع الضمان: {warranty_type_id}")

        # إنشاء خدمة أنواع الضمانات
        service = WarrantyTypeService(db, current_user)

        # حذف نوع الضمان
        service.delete_warranty_type(warranty_type_id)

        logger.info(f"✅ [API] تم حذف نوع الضمان: {warranty_type_id}")

    except ValueError as e:
        logger.warning(f"⚠️ [API] خطأ في البيانات: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"❌ [API] خطأ في حذف نوع الضمان: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في حذف نوع الضمان: {str(e)}"
        )


@router.get("/stats/overview")
async def get_warranty_types_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    جلب إحصائيات أنواع الضمانات
    """
    try:
        logger.info(f"🔄 [API] جلب إحصائيات أنواع الضمانات")

        # إنشاء خدمة أنواع الضمانات
        service = WarrantyTypeService(db, current_user)

        # جلب الإحصائيات
        stats = service.get_warranty_types_stats()

        logger.info(f"✅ [API] تم جلب إحصائيات أنواع الضمانات")

        return stats

    except Exception as e:
        logger.error(f"❌ [API] خطأ في جلب إحصائيات أنواع الضمانات: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في جلب الإحصائيات: {str(e)}"
        )
