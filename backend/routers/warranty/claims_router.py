"""
API endpoints لإدارة مطالبات الضمان
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import date

from database.session import get_db
from utils.auth import get_current_user, get_current_admin_user
from models.user import User
from services.warranty import WarrantyClaimService
from schemas.warranty import (
    WarrantyClaimResponse,
    WarrantyClaimCreate,
    WarrantyClaimUpdate,
    ClaimFilters,
    ProcessClaimData
)
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/warranty-claims", tags=["warranty-claims"])


# وضع المسارات الثابتة أولاً قبل المسارات التي تحتوي على parameters




@router.get("/", response_model=List[dict])
async def get_claims(
    search: Optional[str] = Query(None, description="البحث في رقم المطالبة أو وصف المشكلة"),
    status: Optional[str] = Query("all", description="الحالة: all, pending, approved, rejected, in_progress, completed"),
    claim_type: Optional[str] = Query("all", description="نوع المطالبة: all, repair, replacement, refund"),
    priority: Optional[str] = Query("all", description="الأولوية: all, low, normal, high, urgent"),
    warranty_id: Optional[int] = Query(None, description="معرف الضمان"),
    start_date: Optional[date] = Query(None, description="تاريخ البداية"),
    end_date: Optional[date] = Query(None, description="تاريخ النهاية"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    جلب جميع مطالبات الضمان مع الفلاتر
    """
    try:
        logger.info(f"🔄 [API] جلب مطالبات الضمان - المستخدم: {current_user.username}")

        # إنشاء خدمة مطالبات الضمان
        service = WarrantyClaimService(db, current_user)

        # إعداد الفلاتر
        filters = ClaimFilters(
            search=search,
            status=status,
            claim_type=claim_type,
            priority=priority,
            warranty_id=warranty_id,
            start_date=start_date,
            end_date=end_date
        )

        # جلب المطالبات
        claims = service.get_claims(filters)

        logger.info(f"✅ [API] تم جلب {len(claims)} مطالبة")

        return claims

    except Exception as e:
        logger.error(f"❌ [API] خطأ في جلب مطالبات الضمان: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في جلب مطالبات الضمان: {str(e)}"
        )


@router.get("/statistics", response_model=dict)
async def get_claim_statistics(
    start_date: Optional[date] = Query(None, description="تاريخ البداية"),
    end_date: Optional[date] = Query(None, description="تاريخ النهاية"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    جلب إحصائيات مطالبات الضمان
    """
    try:
        logger.info(f"🔄 [API] جلب إحصائيات مطالبات الضمان")

        # إنشاء خدمة التحليلات
        from services.warranty import WarrantyAnalyticsService
        from schemas.warranty import DateRange

        service = WarrantyAnalyticsService(db, current_user)

        # إعداد نطاق التاريخ
        date_range = None
        if start_date and end_date:
            date_range = DateRange(start_date=start_date, end_date=end_date)

        # جلب الإحصائيات
        stats = service.get_claim_statistics(date_range)

        logger.info(f"✅ [API] تم جلب إحصائيات مطالبات الضمان")

        return stats

    except Exception as e:
        logger.error(f"❌ [API] خطأ في جلب إحصائيات مطالبات الضمان: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في جلب إحصائيات مطالبات الضمان: {str(e)}"
        )


@router.get("/{claim_id}", response_model=WarrantyClaimResponse)
async def get_claim(
    claim_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    جلب مطالبة محددة بالمعرف
    """
    try:
        logger.info(f"🔄 [API] جلب المطالبة: {claim_id}")

        # إنشاء خدمة مطالبات الضمان
        service = WarrantyClaimService(db, current_user)

        # جلب المطالبة
        claim = service.get_claim_by_id(claim_id)

        if not claim:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"المطالبة غير موجودة: {claim_id}"
            )

        logger.info(f"✅ [API] تم جلب المطالبة: {claim.claim_number}")

        return claim

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ [API] خطأ في جلب المطالبة: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في جلب المطالبة: {str(e)}"
        )


@router.post("/", response_model=WarrantyClaimResponse, status_code=status.HTTP_201_CREATED)
async def create_claim(
    claim_data: WarrantyClaimCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    إنشاء مطالبة ضمان جديدة
    """
    try:
        logger.info(f"🔄 [API] إنشاء مطالبة جديدة للضمان: {claim_data.warranty_id}")

        # إنشاء خدمة مطالبات الضمان
        service = WarrantyClaimService(db, current_user)

        # إنشاء المطالبة
        claim = service.create_claim(claim_data)

        logger.info(f"✅ [API] تم إنشاء المطالبة: {claim.claim_number}")

        return claim

    except ValueError as e:
        logger.warning(f"⚠️ [API] خطأ في البيانات: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"❌ [API] خطأ في إنشاء المطالبة: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في إنشاء المطالبة: {str(e)}"
        )


@router.put("/{claim_id}", response_model=WarrantyClaimResponse)
async def update_claim(
    claim_id: int,
    claim_data: WarrantyClaimUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    تحديث مطالبة ضمان موجودة
    """
    try:
        logger.info(f"🔄 [API] تحديث المطالبة: {claim_id}")

        # إنشاء خدمة مطالبات الضمان
        service = WarrantyClaimService(db, current_user)

        # جلب المطالبة أولاً للتحقق من وجودها
        claim = service.get_claim_by_id(claim_id)
        if not claim:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"المطالبة غير موجودة: {claim_id}"
            )

        # تحديث البيانات
        update_data = claim_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(claim, field, value)

        claim.updated_by = current_user.id

        service.db.commit()
        service.db.refresh(claim)

        logger.info(f"✅ [API] تم تحديث المطالبة: {claim.claim_number}")

        return claim

    except HTTPException:
        raise
    except ValueError as e:
        logger.warning(f"⚠️ [API] خطأ في البيانات: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"❌ [API] خطأ في تحديث المطالبة: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في تحديث المطالبة: {str(e)}"
        )


@router.post("/{claim_id}/process", response_model=WarrantyClaimResponse)
async def process_claim(
    claim_id: int,
    process_data: ProcessClaimData,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    معالجة مطالبة الضمان (موافقة/رفض/إكمال)
    يتطلب صلاحيات المدير
    """
    try:
        logger.info(f"🔄 [API] معالجة المطالبة: {claim_id} - الحالة: {process_data.status}")

        # إنشاء خدمة مطالبات الضمان
        service = WarrantyClaimService(db, current_user)

        # معالجة المطالبة
        claim = service.process_claim(claim_id, process_data)

        logger.info(f"✅ [API] تم معالجة المطالبة: {claim.claim_number}")

        return claim

    except ValueError as e:
        logger.warning(f"⚠️ [API] خطأ في البيانات: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"❌ [API] خطأ في معالجة المطالبة: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في معالجة المطالبة: {str(e)}"
        )


@router.get("/{claim_id}/next-statuses")
async def get_available_next_statuses(
    claim_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    جلب الحالات التالية المتاحة للمطالبة
    """
    try:
        logger.info(f"🔄 [API] جلب الحالات التالية المتاحة للمطالبة: {claim_id}")

        # إنشاء خدمة مطالبات الضمان
        service = WarrantyClaimService(db, current_user)

        # جلب الحالات التالية المتاحة
        next_statuses = service.get_available_next_statuses(claim_id)

        logger.info(f"✅ [API] تم جلب {len(next_statuses)} حالة متاحة")

        return {
            "claim_id": claim_id,
            "available_statuses": next_statuses
        }

    except ValueError as e:
        logger.warning(f"⚠️ [API] خطأ في البيانات: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ [API] خطأ في جلب الحالات التالية: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في جلب الحالات التالية: {str(e)}"
        )


@router.get("/{claim_id}/status-history")
async def get_claim_status_history(
    claim_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    جلب تاريخ تغييرات حالة المطالبة
    """
    try:
        logger.info(f"🔄 [API] جلب تاريخ تغييرات الحالة للمطالبة: {claim_id}")

        # إنشاء خدمة مطالبات الضمان
        service = WarrantyClaimService(db, current_user)

        # جلب تاريخ التغييرات
        history = service.get_claim_status_history(claim_id)

        logger.info(f"✅ [API] تم جلب {len(history)} سجل من تاريخ التغييرات")

        return {
            "claim_id": claim_id,
            "status_history": history
        }

    except ValueError as e:
        logger.warning(f"⚠️ [API] خطأ في البيانات: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ [API] خطأ في جلب تاريخ التغييرات: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في جلب تاريخ التغييرات: {str(e)}"
        )


@router.post("/{claim_id}/change-status")
async def change_claim_status(
    claim_id: int,
    status_data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    تغيير حالة المطالبة مع تسجيل التاريخ
    """
    try:
        logger.info(f"🔄 [API] تغيير حالة المطالبة: {claim_id}")

        # إنشاء خدمة مطالبات الضمان
        service = WarrantyClaimService(db, current_user)

        # التحقق من وجود الحالة الجديدة
        new_status = status_data.get("new_status")
        if not new_status:
            raise ValueError("الحالة الجديدة مطلوبة")

        # تغيير حالة المطالبة
        updated_claim = service.change_claim_status(
            claim_id=claim_id,
            new_status=new_status,
            reason=status_data.get("reason"),
            notes=status_data.get("notes"),
            resolution_details=status_data.get("resolution_details"),
            estimated_cost=status_data.get("estimated_cost"),
            actual_cost=status_data.get("actual_cost")
        )

        logger.info(f"✅ [API] تم تغيير حالة المطالبة: {claim_id}")

        return {
            "message": "تم تغيير حالة المطالبة بنجاح",
            "claim": {
                "id": updated_claim.id,
                "claim_number": updated_claim.claim_number,
                "status": str(updated_claim.status),
                "resolution": str(updated_claim.resolution) if getattr(updated_claim, 'resolution', None) else None
            }
        }

    except ValueError as e:
        logger.warning(f"⚠️ [API] خطأ في البيانات: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ [API] خطأ في تغيير حالة المطالبة: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في تغيير حالة المطالبة: {str(e)}"
        )



