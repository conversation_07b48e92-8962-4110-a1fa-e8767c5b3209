"""
API endpoints لإدارة ضمانات المنتجات
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import date

from database.session import get_db
from utils.auth import get_current_user, get_current_admin_user
from models.user import User
from services.warranty import ProductWarrantyService
from schemas.warranty import (
    ProductWarrantyResponse,
    ProductWarrantyCreate,
    WarrantyFilters,
    ExtendWarrantyData,
    VoidWarrantyData
)
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/warranties", tags=["product-warranties"])


@router.get("/", response_model=List[dict])
async def get_warranties(
    search: Optional[str] = Query(None, description="البحث في رقم الضمان أو اسم المنتج"),
    status: Optional[str] = Query("all", description="الحالة: all, active, expired, voided"),
    warranty_type_id: Optional[int] = Query(None, description="معرف نوع الضمان"),
    customer_id: Optional[int] = Query(None, description="معرف العميل"),
    start_date: Optional[date] = Query(None, description="تاريخ البداية"),
    end_date: Optional[date] = Query(None, description="تاريخ النهاية"),
    available_for_claims: Optional[bool] = Query(None, description="الضمانات المتاحة للمطالبة فقط"),
    page: Optional[int] = Query(1, description="رقم الصفحة"),
    limit: Optional[int] = Query(10, description="عدد العناصر في الصفحة"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    جلب جميع ضمانات المنتجات مع الفلاتر
    """
    try:
        logger.info(f"🔄 [API] جلب ضمانات المنتجات - المستخدم: {current_user.username}")

        # إنشاء خدمة ضمانات المنتجات
        service = ProductWarrantyService(db, current_user)

        # إعداد الفلاتر
        filters = WarrantyFilters(
            search=search,
            status=status,
            warranty_type_id=warranty_type_id,
            customer_id=customer_id,
            start_date=start_date,
            end_date=end_date,
            available_for_claims=available_for_claims
        )

        # جلب الضمانات
        warranties = service.get_warranties(filters, page=page or 1, limit=limit or 10)

        logger.info(f"✅ [API] تم جلب {len(warranties)} ضمان")

        return warranties

    except Exception as e:
        logger.error(f"❌ [API] خطأ في جلب ضمانات المنتجات: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في جلب ضمانات المنتجات: {str(e)}"
        )


# وضع المسارات الثابتة أولاً قبل المسارات التي تحتوي على parameters

@router.get("/expiring", response_model=List[dict])
async def get_expiring_warranties(
    days_ahead: int = Query(30, description="عدد الأيام للبحث عن الضمانات المنتهية"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    جلب الضمانات المنتهية قريباً
    """
    try:
        logger.info(f"🔄 [API] جلب الضمانات المنتهية خلال {days_ahead} يوم")

        # إنشاء خدمة التحليلات
        from services.warranty import WarrantyAnalyticsService
        service = WarrantyAnalyticsService(db, current_user)

        # جلب الضمانات المنتهية
        expiring_warranties = service.get_expiring_warranties(days_ahead)

        logger.info(f"✅ [API] تم جلب {len(expiring_warranties)} ضمان منتهي قريباً")

        return expiring_warranties

    except Exception as e:
        logger.error(f"❌ [API] خطأ في جلب الضمانات المنتهية: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في جلب الضمانات المنتهية: {str(e)}"
        )


@router.get("/{warranty_id}", response_model=ProductWarrantyResponse)
async def get_warranty(
    warranty_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    جلب ضمان محدد بالمعرف
    """
    try:
        logger.info(f"🔄 [API] جلب الضمان: {warranty_id}")

        # إنشاء خدمة ضمانات المنتجات
        service = ProductWarrantyService(db, current_user)

        # جلب الضمان
        warranty = service.get_warranty_by_id(warranty_id)

        if not warranty:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"الضمان غير موجود: {warranty_id}"
            )

        logger.info(f"✅ [API] تم جلب الضمان: {warranty.warranty_number}")

        return warranty

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ [API] خطأ في جلب الضمان: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في جلب الضمان: {str(e)}"
        )


@router.post("/", response_model=ProductWarrantyResponse, status_code=status.HTTP_201_CREATED)
async def create_warranty(
    warranty_data: ProductWarrantyCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    إنشاء ضمان جديد
    """
    try:
        logger.info(f"🔄 [API] إنشاء ضمان جديد للمنتج: {warranty_data.product_id}")

        # إنشاء خدمة ضمانات المنتجات
        service = ProductWarrantyService(db, current_user)

        # إنشاء الضمان
        warranty = service.create_warranty(warranty_data)

        logger.info(f"✅ [API] تم إنشاء الضمان: {warranty.warranty_number}")

        return warranty

    except ValueError as e:
        logger.warning(f"⚠️ [API] خطأ في البيانات: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"❌ [API] خطأ في إنشاء الضمان: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في إنشاء الضمان: {str(e)}"
        )


@router.post("/{warranty_id}/extend", response_model=ProductWarrantyResponse)
async def extend_warranty(
    warranty_id: int,
    extend_data: ExtendWarrantyData,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    تمديد فترة الضمان
    يتطلب صلاحيات المدير
    """
    try:
        logger.info(f"🔄 [API] تمديد الضمان: {warranty_id}")

        # إنشاء خدمة ضمانات المنتجات
        service = ProductWarrantyService(db, current_user)

        # تمديد الضمان
        warranty = service.extend_warranty(warranty_id, extend_data)

        logger.info(f"✅ [API] تم تمديد الضمان: {warranty.warranty_number}")

        return warranty

    except ValueError as e:
        logger.warning(f"⚠️ [API] خطأ في البيانات: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"❌ [API] خطأ في تمديد الضمان: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في تمديد الضمان: {str(e)}"
        )


@router.post("/{warranty_id}/void", response_model=ProductWarrantyResponse)
async def void_warranty(
    warranty_id: int,
    void_data: VoidWarrantyData,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    إلغاء الضمان
    يتطلب صلاحيات المدير
    """
    try:
        logger.info(f"🔄 [API] إلغاء الضمان: {warranty_id}")
        logger.info(f"📝 [API] بيانات الإلغاء: {void_data}")

        # التحقق من صحة البيانات
        if not void_data.reason or not void_data.reason.strip():
            logger.warning(f"⚠️ [API] سبب الإلغاء مطلوب")
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="سبب الإلغاء مطلوب ولا يمكن أن يكون فارغاً"
            )

        if len(void_data.reason.strip()) < 3:
            logger.warning(f"⚠️ [API] سبب الإلغاء قصير جداً")
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="سبب الإلغاء يجب أن يكون 3 أحرف على الأقل"
            )

        # إنشاء خدمة ضمانات المنتجات
        service = ProductWarrantyService(db, current_user)

        # إلغاء الضمان
        warranty = service.void_warranty(warranty_id, void_data)

        logger.info(f"✅ [API] تم إلغاء الضمان: {warranty.warranty_number}")

        # إضافة معلومات المطالبات إلى الاستجابة إذا كانت متوفرة (معطل مؤقتاً)
        # if hasattr(warranty, '_claims_info'):
        #     # تحويل الضمان إلى dict وإضافة معلومات المطالبات
        #     warranty_dict = {
        #         "id": warranty.id,
        #         "warranty_number": warranty.warranty_number,
        #         "product_id": warranty.product_id,
        #         "customer_id": warranty.customer_id,
        #         "warranty_type_id": warranty.warranty_type_id,
        #         "purchase_date": warranty.purchase_date,
        #         "start_date": warranty.start_date,
        #         "end_date": warranty.end_date,
        #         "status": warranty.status,
        #         "void_reason": warranty.void_reason,
        #         "voided_at": warranty.voided_at,
        #         "voided_by": warranty.voided_by,
        #         "created_by": warranty.created_by,
        #         "updated_by": warranty.updated_by,
        #         "created_at": warranty.created_at,
        #         "updated_at": warranty.updated_at,
        #         "claims_info": warranty._claims_info
        #     }
        #     from fastapi.responses import JSONResponse
        #     return JSONResponse(content=warranty_dict)

        return warranty

    except HTTPException:
        # إعادة رفع HTTPException كما هي
        raise
    except ValueError as e:
        logger.warning(f"⚠️ [API] خطأ في البيانات: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"❌ [API] خطأ غير متوقع في إلغاء الضمان: {e}")

        # تحديد نوع الخطأ لإرجاع رسالة مناسبة
        error_message = "حدث خطأ أثناء إلغاء الضمان."

        if "transaction" in str(e).lower() or "psycopg2" in str(e).lower():
            error_message = "خطأ في قاعدة البيانات. يرجى المحاولة مرة أخرى."
        elif "not found" in str(e).lower():
            error_message = "الضمان المطلوب غير موجود."
        elif "voided" in str(e).lower():
            error_message = "الضمان ملغي بالفعل."

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_message
        )


@router.delete("/{warranty_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_warranty(
    warranty_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    حذف ضمان المنتج نهائياً
    يتطلب صلاحيات المدير
    """
    try:
        logger.info(f"🔄 [API] حذف الضمان: {warranty_id}")

        # إنشاء خدمة ضمانات المنتجات
        service = ProductWarrantyService(db, current_user)

        # حذف الضمان
        service.delete_warranty(warranty_id)

        logger.info(f"✅ [API] تم حذف الضمان: {warranty_id}")

        return None

    except ValueError as e:
        logger.warning(f"⚠️ [API] خطأ في البيانات: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ [API] خطأ غير متوقع في حذف الضمان: {e}")

        # تحديد نوع الخطأ لإرجاع رسالة مناسبة
        error_message = "حدث خطأ أثناء حذف الضمان."

        if "transaction" in str(e).lower() or "psycopg2" in str(e).lower():
            error_message = "خطأ في قاعدة البيانات. يرجى المحاولة مرة أخرى."
        elif "foreign key" in str(e).lower() or "constraint" in str(e).lower():
            error_message = "لا يمكن حذف هذا الضمان لوجود بيانات مرتبطة به."
        elif "not found" in str(e).lower():
            error_message = "الضمان المطلوب غير موجود."

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_message
        )



