"""
API router لتصدير تقارير الضمانات
يدعم تصدير التقارير بصيغ مختلفة مع فلترة حسب الفترة الزمنية
"""

import logging
from typing import Optional
from datetime import date, datetime
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
import io
import json

from database.session import get_db
from models.user import User
from utils.auth import get_current_user
from services.warranty.warranty_analytics_service import WarrantyAnalyticsService
from schemas.warranty import DateRange

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/warranty-reports", tags=["warranty-reports"])


@router.get("/export")
async def export_warranty_report(
    type: str = Query(..., description="نوع التقرير: warranties, claims, expiring, statistics"),
    start_date: Optional[date] = Query(None, description="تاريخ البداية"),
    end_date: Optional[date] = Query(None, description="تاريخ النهاية"),
    format: str = Query("json", description="صيغة التصدير: json"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    تصدير تقارير الضمانات بصيغة JSON
    """
    try:
        logger.info(f"🔄 [API] تصدير تقرير الضمانات - النوع: {type}")

        # إنشاء خدمة التحليلات
        service = WarrantyAnalyticsService(db, current_user)

        # إعداد نطاق التاريخ
        date_range = None
        if start_date and end_date:
            date_range = DateRange(start_date=start_date, end_date=end_date)

        # جلب البيانات حسب نوع التقرير
        if type == "statistics":
            stats = service.get_warranty_stats(date_range)
            claim_stats = service.get_claim_statistics(date_range)
            trends = service.get_warranty_trends(6)

            data = {
                "type": "statistics",
                "warranty_stats": stats,
                "claim_stats": claim_stats,
                "trends": trends,
                "export_date": datetime.now().isoformat(),
                "date_range": {
                    "start": start_date.isoformat() if start_date else None,
                    "end": end_date.isoformat() if end_date else None
                }
            }
        else:
            raise HTTPException(status_code=400, detail=f"نوع تقرير غير مدعوم: {type}")

        logger.info(f"✅ [API] تم تصدير التقرير بنجاح")

        return data

    except Exception as e:
        logger.error(f"❌ [API] خطأ في تصدير التقرير: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في تصدير التقرير: {str(e)}"
        )


def _get_warranties_data(service: WarrantyAnalyticsService, date_range: Optional[DateRange]):
    """جلب بيانات الضمانات"""
    stats = service.get_warranty_stats(date_range)
    return {
        "type": "warranties",
        "stats": stats,
        "date_range": {
            "start": date_range.start_date.isoformat() if date_range else None,
            "end": date_range.end_date.isoformat() if date_range else None
        }
    }


def _get_claims_data(service: WarrantyAnalyticsService, date_range: Optional[DateRange]):
    """جلب بيانات المطالبات"""
    stats = service.get_claim_statistics(date_range)
    return {
        "type": "claims",
        "stats": stats,
        "date_range": {
            "start": date_range.start_date.isoformat() if date_range else None,
            "end": date_range.end_date.isoformat() if date_range else None
        }
    }


def _get_expiring_warranties_data(service: WarrantyAnalyticsService):
    """جلب بيانات الضمانات المنتهية"""
    expiring = service.get_expiring_warranties(30)
    return {
        "type": "expiring",
        "warranties": expiring,
        "count": len(expiring)
    }


def _get_statistics_data(service: WarrantyAnalyticsService, date_range: Optional[DateRange]):
    """جلب البيانات الإحصائية الشاملة"""
    stats = service.get_warranty_stats(date_range)
    claim_stats = service.get_claim_statistics(date_range)
    trends = service.get_warranty_trends(6)

    return {
        "type": "statistics",
        "warranty_stats": stats,
        "claim_stats": claim_stats,
        "trends": trends,
        "date_range": {
            "start": date_range.start_date.isoformat() if date_range else None,
            "end": date_range.end_date.isoformat() if date_range else None
        }
    }


def _export_to_excel(data: dict, filename: str, report_type: str):
    """تصدير البيانات إلى Excel - مؤقتاً يُرجع JSON"""
    # مؤقتاً: إرجاع JSON حتى يتم تثبيت openpyxl
    return _export_to_json(data, filename)


def _export_to_json(data: dict, filename: str):
    """تصدير البيانات إلى JSON"""
    try:
        json_data = json.dumps(data, ensure_ascii=False, indent=2, default=str)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        full_filename = f"{filename}_{timestamp}.json"

        return StreamingResponse(
            io.StringIO(json_data),
            media_type="application/json",
            headers={"Content-Disposition": f"attachment; filename={full_filename}"}
        )

    except Exception as e:
        logger.error(f"❌ خطأ في تصدير JSON: {e}")
        raise


# وظائف Excel محذوفة مؤقتاً حتى يتم تثبيت openpyxl
