"""
API endpoints لتحليلات وإحصائيات الضمانات
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import Optional
from datetime import date

from database.session import get_db
from utils.auth import get_current_user
from models.user import User
from services.warranty import WarrantyAnalyticsService
from schemas.warranty import DateRange
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/warranty-stats", tags=["warranty-analytics"])


@router.get("/")
async def get_warranty_stats(
    start_date: Optional[date] = Query(None, description="تاريخ البداية"),
    end_date: Optional[date] = Query(None, description="تاريخ النهاية"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    جلب إحصائيات الضمانات الشاملة
    """
    try:
        logger.info(f"🔄 [API] جلب إحصائيات الضمانات - المستخدم: {current_user.username}")

        # إنشاء خدمة التحليلات
        service = WarrantyAnalyticsService(db, current_user)

        # إعداد نطاق التاريخ
        date_range = None
        if start_date and end_date:
            date_range = DateRange(start_date=start_date, end_date=end_date)

        # جلب الإحصائيات
        stats = service.get_warranty_stats(date_range)

        logger.info(f"✅ [API] تم جلب إحصائيات الضمانات")

        return stats

    except Exception as e:
        logger.error(f"❌ [API] خطأ في جلب إحصائيات الضمانات: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في جلب إحصائيات الضمانات: {str(e)}"
        )


@router.get("/claims")
async def get_claim_statistics(
    start_date: Optional[date] = Query(None, description="تاريخ البداية"),
    end_date: Optional[date] = Query(None, description="تاريخ النهاية"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    جلب إحصائيات مطالبات الضمان المفصلة
    """
    try:
        logger.info(f"🔄 [API] جلب إحصائيات مطالبات الضمان")

        # إنشاء خدمة التحليلات
        service = WarrantyAnalyticsService(db, current_user)

        # إعداد نطاق التاريخ
        date_range = None
        if start_date and end_date:
            date_range = DateRange(start_date=start_date, end_date=end_date)

        # جلب الإحصائيات
        stats = service.get_claim_statistics(date_range)

        logger.info(f"✅ [API] تم جلب إحصائيات مطالبات الضمان")

        return stats

    except Exception as e:
        logger.error(f"❌ [API] خطأ في جلب إحصائيات المطالبات: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في جلب إحصائيات المطالبات: {str(e)}"
        )


@router.get("/expiring")
async def get_expiring_warranties(
    days_ahead: int = Query(30, description="عدد الأيام للبحث عن الضمانات المنتهية"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    جلب الضمانات المنتهية قريباً
    """
    try:
        logger.info(f"🔄 [API] جلب الضمانات المنتهية خلال {days_ahead} يوم")

        # إنشاء خدمة التحليلات
        service = WarrantyAnalyticsService(db, current_user)

        # جلب الضمانات المنتهية
        expiring_warranties = service.get_expiring_warranties(days_ahead)

        logger.info(f"✅ [API] تم جلب {len(expiring_warranties)} ضمان منتهي قريباً")

        return expiring_warranties

    except Exception as e:
        logger.error(f"❌ [API] خطأ في جلب الضمانات المنتهية: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في جلب الضمانات المنتهية: {str(e)}"
        )


@router.get("/trends")
async def get_warranty_trends(
    months: int = Query(12, description="عدد الأشهر للتحليل"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    جلب اتجاهات الضمانات خلال فترة زمنية
    """
    try:
        logger.info(f"🔄 [API] بدء جلب اتجاهات الضمانات لآخر {months} شهر")
        logger.info(f"👤 [API] المستخدم: {current_user.username}")

        # إنشاء خدمة التحليلات
        logger.info(f"🏗️ [API] إنشاء خدمة التحليلات...")
        service = WarrantyAnalyticsService(db, current_user)
        logger.info(f"✅ [API] تم إنشاء خدمة التحليلات")

        # جلب الاتجاهات
        logger.info(f"📊 [API] جلب الاتجاهات...")
        trends = service.get_warranty_trends(months)
        logger.info(f"📊 [API] تم جلب الاتجاهات: {trends}")

        logger.info(f"✅ [API] تم جلب اتجاهات الضمانات بنجاح")

        return trends

    except Exception as e:
        logger.error(f"❌ [API] خطأ في جلب اتجاهات الضمانات: {e}")
        import traceback
        logger.error(f"❌ [API] تفاصيل الخطأ: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في جلب اتجاهات الضمانات: {str(e)}"
        )


@router.get("/dashboard")
async def get_warranty_dashboard(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    جلب بيانات لوحة معلومات الضمانات
    """
    try:
        logger.info(f"🔄 [API] جلب بيانات لوحة معلومات الضمانات")

        # إنشاء خدمة التحليلات
        service = WarrantyAnalyticsService(db, current_user)

        # جلب الإحصائيات الأساسية
        stats = service.get_warranty_stats()

        # جلب الضمانات المنتهية قريباً
        expiring_warranties = service.get_expiring_warranties(30)

        # جلب إحصائيات المطالبات
        claim_stats = service.get_claim_statistics()

        # جلب الاتجاهات
        trends = service.get_warranty_trends(6)  # آخر 6 أشهر

        dashboard_data = {
            'stats': stats,
            'expiring_warranties': expiring_warranties[:10],  # أول 10 ضمانات منتهية
            'claim_stats': claim_stats,
            'trends': trends
        }

        logger.info(f"✅ [API] تم جلب بيانات لوحة معلومات الضمانات")

        return dashboard_data

    except Exception as e:
        logger.error(f"❌ [API] خطأ في جلب بيانات لوحة المعلومات: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في جلب بيانات لوحة المعلومات: {str(e)}"
        )
