from fastapi import APIRouter, Depends, HTTPException, status, Response
from sqlalchemy.orm import Session
from sqlalchemy import select, update, and_
from typing import List, Optional
from datetime import datetime, date

from database.session import get_db
from models.sale import Sale, SaleItem
from models.product import Product
from models.user import User, UserRole
from schemas.sale import SaleCreate, SaleResponse, SaleItemCreate, SaleUpdate
from utils.auth import get_current_active_user
from utils.datetime_utils import get_tripoli_now, convert_to_tripoli_time
from services.query_optimizer import QueryOptimizer
from services.cache_service import cached, invalidate_sales_cache, cache_key_for_user

router = APIRouter(prefix="/api/sales", tags=["sales"])

@router.post("/", response_model=SaleResponse, status_code=status.HTTP_201_CREATED)
async def create_sale(
    sale: SaleCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):

    """
    Create a new sale transaction.
    """
    # Get current time in Tripoli timezone
    current_time = get_tripoli_now()
    print(f"Creating sale at Tripoli time: {current_time}")

    # Calculate final amount (total + tax - discount)
    final_amount = sale.total_amount + sale.tax_amount - sale.discount_amount

    # Determine payment method and amount based on payment type
    payment_method = sale.payment_method
    amount_paid = sale.amount_paid

    # For credit/deferred sales, force payment method to "آجل" and amount_paid to 0
    if sale.payment_method == 'آجل' or sale.payment_status == 'unpaid':
        payment_method = 'آجل'
        amount_paid = 0.0
        payment_status = 'unpaid'
    # For partial payments, set payment_method to "جزئي" and ensure customer is specified
    elif sale.payment_status == 'partial':
        if not sale.customer_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Customer is required for partial payments"
            )
        payment_method = 'جزئي'  # Store as "جزئي" in database
        payment_status = 'partial'
    # For full payments (with tolerance for floating point precision)
    elif abs(amount_paid - final_amount) < 0.01 or amount_paid >= final_amount:
        payment_status = 'paid'
    elif amount_paid > 0:
        # If amount is paid but less than final amount, it's partial
        if not sale.customer_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Customer is required for partial payments"
            )
        payment_method = 'جزئي'  # Store as "جزئي" in database
        payment_status = 'partial'
    else:
        payment_status = 'unpaid'

    # Create sale record with explicit timestamp
    new_sale = Sale(
        user_id=current_user.id,  # Use the current user's ID
        customer_id=sale.customer_id,  # Link to customer
        total_amount=sale.total_amount,
        payment_method=payment_method,  # Use corrected payment method
        tax_amount=sale.tax_amount,  # Add tax amount
        discount_amount=sale.discount_amount,  # Add discount amount
        discount_type=sale.discount_type,  # Add discount type
        customer_name=sale.customer_name,  # Keep for backward compatibility
        notes=sale.notes,
        amount_paid=amount_paid,  # Use corrected amount paid
        payment_status=payment_status,  # Payment status
        created_at=current_time,  # Explicitly set the created_at time
        updated_at=current_time   # Explicitly set the updated_at time
    )

    db.add(new_sale)
    db.flush()  # Get the sale ID without committing

    # Process sale items and update inventory
    for item in sale.items:
        stmt = select(Product).where(Product.id == item.product_id)
        product = db.execute(stmt).scalar_one_or_none()
        if not product:
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Product with id {item.product_id} not found"
            )

        # Check stock using scalar comparison
        # Convert SQLAlchemy Column object to Python int for comparison
        product_quantity = getattr(product, 'quantity', 0)
        if isinstance(product_quantity, int) and product_quantity < item.quantity:
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Insufficient stock for product {product.name}"
            )

        # Create sale item with explicit timestamp (no individual discount)
        sale_item = SaleItem(
            sale_id=new_sale.id,
            product_id=item.product_id,
            quantity=item.quantity,
            unit_price=item.unit_price,
            subtotal=item.quantity * item.unit_price,
            discount=0,  # No individual discount - discount is now at sale level
            created_at=current_time  # Explicitly set the created_at time
        )
        db.add(sale_item)

        # Update product stock using update statement
        stmt = update(Product).where(Product.id == item.product_id).values(
            quantity=product.quantity - item.quantity
        )
        db.execute(stmt)

    try:
        db.commit()
        db.refresh(new_sale)

        # Create debt record if there's remaining amount and customer is specified
        # Use tolerance for floating point comparison
        if sale.customer_id and (final_amount - amount_paid) > 0.01:
            from models.customer import CustomerDebt

            remaining_amount = final_amount - amount_paid

            # Create debt description based on payment type
            if payment_method == 'آجل':
                description = f"بيع آجل - فاتورة رقم {new_sale.id}"
            else:
                description = f"دفع جزئي - فاتورة رقم {new_sale.id}"

            debt = CustomerDebt(
                customer_id=sale.customer_id,
                sale_id=new_sale.id,
                amount=remaining_amount,
                remaining_amount=remaining_amount,
                description=description,
                is_paid=False,
                created_at=current_time,
                updated_at=current_time
            )
            db.add(debt)
            db.commit()

        # Get user information
        user_query = select(User).where(User.id == new_sale.user_id)
        user = db.execute(user_query).scalar_one_or_none()

        # Get customer information if customer_id is provided
        customer = None
        if new_sale.customer_id is not None:
            from models.customer import Customer
            customer_query = select(Customer).where(Customer.id == new_sale.customer_id)
            customer = db.execute(customer_query).scalar_one_or_none()

        # Process sale items to include product information
        items_with_products = []
        for item in new_sale.items:
            # Get product information
            product_query = select(Product).where(Product.id == item.product_id)
            product = db.execute(product_query).scalar_one_or_none()

            # Create a copy of the item object
            item_dict = {
                "id": item.id,
                "sale_id": item.sale_id,
                "product_id": item.product_id,
                "quantity": item.quantity,
                "unit_price": item.unit_price,
                "subtotal": item.subtotal,
                "discount": item.discount,
                "created_at": item.created_at
            }

            # Add product information if available
            if product:
                item_dict["product"] = {
                    "id": product.id,
                    "name": product.name,
                    "price": product.price,
                    "barcode": product.barcode
                }
            else:
                item_dict["product"] = {
                    "id": item.product_id,
                    "name": f"منتج #{item.product_id}",
                    "price": item.unit_price,
                    "barcode": ""
                }

            items_with_products.append(item_dict)

        # Create a copy of the sale object
        sale_dict = {
            "id": new_sale.id,
            "user_id": new_sale.user_id,
            "customer_id": new_sale.customer_id,
            "total_amount": new_sale.total_amount,
            "payment_method": new_sale.payment_method,
            "tax_amount": new_sale.tax_amount,
            "discount_amount": new_sale.discount_amount,
            "discount_type": new_sale.discount_type,
            "customer_name": new_sale.customer_name,
            "notes": new_sale.notes,
            "amount_paid": new_sale.amount_paid,
            "payment_status": new_sale.payment_status,
            "created_at": new_sale.created_at,
            "updated_at": new_sale.updated_at,
            "items": items_with_products
        }

        # Add user information if available
        if user:
            sale_dict["user"] = {
                "id": user.id,
                "username": user.username,
                "full_name": user.full_name
            }
        else:
            sale_dict["user"] = {
                "id": new_sale.user_id,
                "username": "unknown",
                "full_name": "مستخدم غير معروف"
            }

        # Add customer information if available
        if customer:
            sale_dict["customer"] = {
                "id": customer.id,
                "name": customer.name,
                "phone": customer.phone,
                "email": customer.email
            }
        else:
            sale_dict["customer"] = None

        return sale_dict
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error creating sale"
        )

@router.get("/", response_model=List[SaleResponse])
async def get_sales(
    response: Response,
    page: int = 1,
    limit: int = 50,  # زيادة الحد الافتراضي للأداء الأفضل
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    payment_method: Optional[str] = None,
    min_amount: Optional[float] = None,
    max_amount: Optional[float] = None,
    user_id: Optional[int] = None,
    customer_id: Optional[int] = None,
    search: Optional[str] = None,
    all: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get all sales with filtering and pagination using optimized queries.
    If 'all' parameter is 'true', returns all sales without pagination.
    """
    # استخدام QueryOptimizer للاستعلامات المحسنة
    optimizer = QueryOptimizer(db)

    # تحويل التواريخ إلى datetime إذا لزم الأمر
    start_datetime = datetime.combine(start_date, datetime.min.time()) if start_date else None
    end_datetime = datetime.combine(end_date, datetime.max.time()) if end_date else None

    # تحديد الحد الأقصى للعناصر
    if all == "true":
        limit = 50000  # حد أقصى أكبر للبيانات الكبيرة مع إمكانية التحكم

    # تطبيق فلتر المستخدم حسب الصلاحيات
    user_role = getattr(current_user.role, 'value', str(current_user.role))
    if user_role != "admin":
        # للكاشيرز، عرض مبيعاتهم فقط
        user_id = getattr(current_user, 'id', None)

    # استخدام الاستعلام المحسن
    sales, total_count = optimizer.get_sales_with_pagination(
        page=page,
        limit=limit,
        start_date=start_datetime,
        end_date=end_datetime,
        user_id=user_id,
        customer_id=customer_id,
        payment_method=payment_method,
        min_amount=min_amount,
        max_amount=max_amount,
        search=search
    )

    # Print debug info
    print(f"Fetched {len(sales)} sales records from database")
    for sale in sales:
        # Convert to Tripoli time for display
        tripoli_time = convert_to_tripoli_time(sale.created_at)
        print(f"Sale ID: {sale.id}, Amount: {sale.total_amount}, UTC Date: {sale.created_at}, Tripoli Date: {tripoli_time}")

    # Add user information to each sale
    sales_with_users = []
    for sale in sales:
        # Get user information
        user_query = select(User).where(User.id == sale.user_id)
        user = db.execute(user_query).scalar_one_or_none()

        # Process sale items to include product information
        items_with_products = []
        for item in sale.items:
            # Get product information
            product_query = select(Product).where(Product.id == item.product_id)
            product = db.execute(product_query).scalar_one_or_none()

            # Create a copy of the item object
            item_dict = {
                "id": item.id,
                "sale_id": item.sale_id,
                "product_id": item.product_id,
                "quantity": item.quantity,
                "unit_price": item.unit_price,
                "subtotal": item.subtotal,
                "discount": item.discount,
                "created_at": item.created_at
            }

            # Add product information if available
            if product:
                item_dict["product"] = {
                    "id": product.id,
                    "name": product.name,
                    "price": product.price,
                    "barcode": product.barcode
                }
            else:
                item_dict["product"] = {
                    "id": item.product_id,
                    "name": f"منتج #{item.product_id}",
                    "price": item.unit_price,
                    "barcode": ""
                }

            items_with_products.append(item_dict)

        # Create a copy of the sale object
        sale_dict = {
            "id": sale.id,
            "user_id": sale.user_id,
            "customer_id": getattr(sale, 'customer_id', None),
            "total_amount": sale.total_amount,
            "payment_method": sale.payment_method,
            "tax_amount": getattr(sale, 'tax_amount', 0.0),
            "discount_amount": getattr(sale, 'discount_amount', 0.0),
            "discount_type": getattr(sale, 'discount_type', 'fixed'),
            "customer_name": sale.customer_name,
            "notes": sale.notes,
            "amount_paid": getattr(sale, 'amount_paid', 0.0),
            "payment_status": getattr(sale, 'payment_status', 'paid'),
            "created_at": sale.created_at,
            "updated_at": sale.updated_at,
            "items": items_with_products
        }

        # Add user information if available
        if user:
            sale_dict["user"] = {
                "id": user.id,
                "username": user.username,
                "full_name": user.full_name
            }
        else:
            sale_dict["user"] = {
                "id": sale.user_id,
                "username": "unknown",
                "full_name": "مستخدم غير معروف"
            }

        # Add customer information if available
        customer = None
        if sale_dict["customer_id"]:
            from models.customer import Customer
            customer_query = select(Customer).where(Customer.id == sale_dict["customer_id"])
            customer = db.execute(customer_query).scalar_one_or_none()

        if customer:
            sale_dict["customer"] = {
                "id": customer.id,
                "name": customer.name,
                "phone": customer.phone,
                "email": customer.email,
                "total_debt": 0.0,  # Will be calculated if needed
                "is_active": customer.is_active
            }
        else:
            sale_dict["customer"] = None

        sales_with_users.append(sale_dict)

    # حساب عدد الصفحات
    total_pages = (total_count + limit - 1) // limit if total_count > 0 else 1

    # Add pagination headers
    response.headers["X-Total-Count"] = str(total_count)
    response.headers["X-Page"] = str(page)
    response.headers["X-Limit"] = str(limit)
    response.headers["X-Pages"] = str(total_pages)

    return sales_with_users

@router.get("/{sale_id}", response_model=SaleResponse)
async def get_sale(
    sale_id: int,
    db: Session = Depends(get_db)
):
    """
    Get a specific sale by ID.
    """
    stmt = select(Sale).where(Sale.id == sale_id)
    sale = db.execute(stmt).scalar_one_or_none()
    if not sale:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Sale not found"
        )

    # Get user information
    user_query = select(User).where(User.id == sale.user_id)
    user = db.execute(user_query).scalar_one_or_none()

    # Process sale items to include product information
    items_with_products = []
    for item in sale.items:
        # Get product information
        product_query = select(Product).where(Product.id == item.product_id)
        product = db.execute(product_query).scalar_one_or_none()

        # Create a copy of the item object
        item_dict = {
            "id": item.id,
            "sale_id": item.sale_id,
            "product_id": item.product_id,
            "quantity": item.quantity,
            "unit_price": item.unit_price,
            "subtotal": item.subtotal,
            "discount": item.discount,
            "created_at": item.created_at
        }

        # Add product information if available
        if product:
            item_dict["product"] = {
                "id": product.id,
                "name": product.name,
                "price": product.price,
                "barcode": product.barcode
            }
        else:
            item_dict["product"] = {
                "id": item.product_id,
                "name": f"منتج #{item.product_id}",
                "price": item.unit_price,
                "barcode": ""
            }

        items_with_products.append(item_dict)

    # Create a copy of the sale object
    sale_dict = {
        "id": sale.id,
        "user_id": sale.user_id,
        "customer_id": getattr(sale, 'customer_id', None),
        "total_amount": sale.total_amount,
        "payment_method": sale.payment_method,
        "tax_amount": getattr(sale, 'tax_amount', 0.0),
        "discount_amount": getattr(sale, 'discount_amount', 0.0),
        "discount_type": getattr(sale, 'discount_type', 'fixed'),
        "customer_name": sale.customer_name,
        "notes": sale.notes,
        "amount_paid": getattr(sale, 'amount_paid', 0.0),
        "payment_status": getattr(sale, 'payment_status', 'paid'),
        "created_at": sale.created_at,
        "updated_at": sale.updated_at,
        "items": items_with_products
    }

    # Add user information if available
    if user:
        sale_dict["user"] = {
            "id": user.id,
            "username": user.username,
            "full_name": user.full_name
        }
    else:
        sale_dict["user"] = {
            "id": sale.user_id,
            "username": "unknown",
            "full_name": "مستخدم غير معروف"
        }

    # Add customer information if available
    customer = None
    if sale_dict["customer_id"]:
        from models.customer import Customer
        customer_query = select(Customer).where(Customer.id == sale_dict["customer_id"])
        customer = db.execute(customer_query).scalar_one_or_none()

    if customer:
        sale_dict["customer"] = {
            "id": customer.id,
            "name": customer.name,
            "phone": customer.phone,
            "email": customer.email,
            "total_debt": 0.0,  # Will be calculated if needed
            "is_active": customer.is_active
        }
    else:
        sale_dict["customer"] = None

    return sale_dict

@router.put("/{sale_id}", response_model=SaleResponse)
async def update_sale(
    sale_id: int,
    sale_update: SaleUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update an existing sale transaction.
    Regular users can only update their own sales.
    Admins can update any sale.
    """
    # Get the existing sale
    stmt = select(Sale).where(Sale.id == sale_id)
    existing_sale = db.execute(stmt).scalar_one_or_none()
    if not existing_sale:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Sale not found"
        )

    # Check permissions: users can only update their own sales, admins can update any
    user_role = getattr(current_user.role, 'value', str(current_user.role))
    sale_user_id = getattr(existing_sale, 'user_id', None)
    current_user_id = getattr(current_user, 'id', None)

    if user_role != "admin" and sale_user_id != current_user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only update your own sales"
        )

    # Get current time in Tripoli timezone
    current_time = get_tripoli_now()

    # If items are being updated, we need to handle inventory changes
    if sale_update.items is not None:
        # First, restore the quantities from the original sale
        for item in existing_sale.items:
            product_stmt = select(Product).where(Product.id == item.product_id)
            product = db.execute(product_stmt).scalar_one_or_none()
            if product:
                # Restore quantity
                stmt = update(Product).where(Product.id == item.product_id).values(
                    quantity=product.quantity + item.quantity
                )
                db.execute(stmt)

        # Delete existing sale items
        for item in existing_sale.items:
            db.delete(item)
        db.flush()

        # Add new sale items and update inventory
        for item_data in sale_update.items:
            stmt = select(Product).where(Product.id == item_data.product_id)
            product = db.execute(stmt).scalar_one_or_none()
            if not product:
                db.rollback()
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Product with id {item_data.product_id} not found"
                )

            # Check stock
            product_quantity = getattr(product, 'quantity', 0)
            if isinstance(product_quantity, int) and product_quantity < item_data.quantity:
                db.rollback()
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Insufficient stock for product {product.name}"
                )

            # Create new sale item (no individual discount)
            sale_item = SaleItem(
                sale_id=sale_id,
                product_id=item_data.product_id,
                quantity=item_data.quantity,
                unit_price=item_data.unit_price,
                subtotal=item_data.quantity * item_data.unit_price,
                discount=0,  # No individual discount - discount is now at sale level
                created_at=current_time
            )
            db.add(sale_item)

            # Update product stock
            stmt = update(Product).where(Product.id == item_data.product_id).values(
                quantity=product.quantity - item_data.quantity
            )
            db.execute(stmt)

    # Update sale fields
    update_data = sale_update.model_dump(exclude_unset=True, exclude={'items'})
    update_data['updated_at'] = current_time

    # Apply payment logic for updates as well
    if 'payment_method' in update_data or 'payment_status' in update_data or 'amount_paid' in update_data:
        payment_method = update_data.get('payment_method', existing_sale.payment_method)
        amount_paid = update_data.get('amount_paid', existing_sale.amount_paid)
        payment_status = update_data.get('payment_status', existing_sale.payment_status)

        # Calculate final amount for the updated sale
        total_amount = update_data.get('total_amount', existing_sale.total_amount)
        tax_amount = update_data.get('tax_amount', existing_sale.tax_amount)
        discount_amount = update_data.get('discount_amount', existing_sale.discount_amount)
        final_amount = total_amount + tax_amount - discount_amount

        # For credit/deferred sales, force payment method to "آجل" and amount_paid to 0
        if payment_method == 'آجل' or payment_status == 'unpaid':
            update_data['payment_method'] = 'آجل'
            update_data['amount_paid'] = 0.0
            update_data['payment_status'] = 'unpaid'
        # For partial payments, set payment_method to "جزئي" and ensure customer is specified
        elif payment_status == 'partial':
            customer_id = update_data.get('customer_id', existing_sale.customer_id)
            if not customer_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Customer is required for partial payments"
                )
            update_data['payment_method'] = 'جزئي'  # Store as "جزئي" in database
            update_data['payment_status'] = 'partial'
        # For full payments
        elif amount_paid >= final_amount:
            update_data['payment_status'] = 'paid'
        elif amount_paid > 0:
            # If amount is paid but less than final amount, it's partial
            customer_id = update_data.get('customer_id', existing_sale.customer_id)
            if not customer_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Customer is required for partial payments"
                )
            update_data['payment_method'] = 'جزئي'  # Store as "جزئي" in database
            update_data['payment_status'] = 'partial'
        else:
            update_data['payment_status'] = 'unpaid'

    # Handle debt management when payment status changes
    if 'payment_method' in update_data or 'payment_status' in update_data or 'amount_paid' in update_data:
        from models.customer import CustomerDebt, DebtPayment

        # Get existing debt for this sale
        debt_stmt = select(CustomerDebt).where(CustomerDebt.sale_id == sale_id)
        existing_debt = db.execute(debt_stmt).scalar_one_or_none()

        payment_status = update_data.get('payment_status', existing_sale.payment_status)
        amount_paid = update_data.get('amount_paid', existing_sale.amount_paid)

        # Calculate final amount for the updated sale
        total_amount = update_data.get('total_amount', existing_sale.total_amount)
        tax_amount = update_data.get('tax_amount', existing_sale.tax_amount)
        discount_amount = update_data.get('discount_amount', existing_sale.discount_amount)
        final_amount = total_amount + tax_amount - discount_amount

        if payment_status == 'paid' and amount_paid >= final_amount:
            # Sale is now fully paid - delete any existing debt
            if existing_debt:
                # Delete all payments for this debt first
                payment_stmt = select(DebtPayment).where(DebtPayment.debt_id == existing_debt.id)
                payments = db.execute(payment_stmt).scalars().all()
                for payment in payments:
                    db.delete(payment)

                # Delete the debt
                db.delete(existing_debt)

        elif payment_status in ['partial', 'unpaid']:
            # Sale has remaining debt
            remaining_amount = final_amount - amount_paid
            customer_id = update_data.get('customer_id', existing_sale.customer_id)

            if customer_id and remaining_amount > 0:
                if existing_debt:
                    # Update existing debt
                    debt_update_stmt = update(CustomerDebt).where(CustomerDebt.id == existing_debt.id).values(
                        amount=remaining_amount,
                        remaining_amount=remaining_amount,
                        is_paid=False,
                        updated_at=current_time
                    )
                    db.execute(debt_update_stmt)
                else:
                    # Create new debt
                    description = f"بيع آجل - فاتورة رقم {sale_id}" if payment_status == 'unpaid' else f"دفع جزئي - فاتورة رقم {sale_id}"
                    new_debt = CustomerDebt(
                        customer_id=customer_id,
                        sale_id=sale_id,
                        amount=remaining_amount,
                        remaining_amount=remaining_amount,
                        description=description,
                        is_paid=False,
                        created_at=current_time,
                        updated_at=current_time
                    )
                    db.add(new_debt)

    # Update the sale using update statement
    stmt = update(Sale).where(Sale.id == sale_id).values(**update_data)
    db.execute(stmt)

    try:
        db.commit()
        db.refresh(existing_sale)

        # Return the updated sale using the same format as get_sale
        return await get_sale(sale_id, db)
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update sale: {str(e)}"
        )

@router.delete("/{sale_id}", status_code=status.HTTP_204_NO_CONTENT)
async def void_sale(
    sale_id: int,
    db: Session = Depends(get_db)
):
    """
    Void a sale and restore product quantities.
    Also delete any associated customer debts.
    """
    stmt = select(Sale).where(Sale.id == sale_id)
    sale = db.execute(stmt).scalar_one_or_none()
    if not sale:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Sale not found"
        )

    # Delete associated customer debts first
    if sale.customer_id is not None:
        from models.customer import CustomerDebt
        debt_stmt = select(CustomerDebt).where(CustomerDebt.sale_id == sale_id)
        debts = db.execute(debt_stmt).scalars().all()

        for debt in debts:
            db.delete(debt)

    # Restore product quantities using update statements
    for item in sale.items:
        # Get current product quantity
        product_stmt = select(Product).where(Product.id == item.product_id)
        product = db.execute(product_stmt).scalar_one_or_none()

        if product:
            # Update quantity
            stmt = update(Product).where(Product.id == item.product_id).values(
                quantity=product.quantity + item.quantity
            )
            db.execute(stmt)

    db.delete(sale)  # This will cascade delete sale items
    db.commit()
    return None