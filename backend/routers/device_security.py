"""
API endpoints لإدارة أمان الأجهزة
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from typing import List, Dict, Any
from pydantic import BaseModel

from database.session import get_db
from utils.auth import get_current_admin_user
from models.user import User
from services.device_security import device_security
from services.redirect_manager import redirect_manager
# from utils.device_detection import device_detector  # تم إزالة هذا الملف
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/device-security", tags=["device-security"])

class SecuritySettingRequest(BaseModel):
    key: str
    value: str

class BlockDeviceRequest(BaseModel):
    device_id: str
    reason: str = "محظور بواسطة المدير"

class SecuritySettingsResponse(BaseModel):
    require_approval: bool
    auto_block_suspicious: bool
    max_devices_per_ip: int
    block_unknown_devices: bool
    notification_enabled: bool

@router.get("/settings", response_model=SecuritySettingsResponse)
async def get_security_settings(
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    الحصول على إعدادات الأمان
    """
    try:
        settings = {
            "require_approval": device_security.get_security_setting("require_approval", db) == "true",
            "auto_block_suspicious": device_security.get_security_setting("auto_block_suspicious", db) == "true",
            "max_devices_per_ip": int(device_security.get_security_setting("max_devices_per_ip", db)),
            "block_unknown_devices": device_security.get_security_setting("block_unknown_devices", db) == "true",
            "notification_enabled": device_security.get_security_setting("notification_enabled", db) == "true"
        }

        return settings
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في الحصول على إعدادات الأمان: {str(e)}"
        )

@router.post("/settings")
async def update_security_setting(
    setting: SecuritySettingRequest,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    تحديث إعداد أمان
    """
    try:
        success = device_security.set_security_setting(setting.key, setting.value, db)

        if success:
            return {"success": True, "message": "تم تحديث الإعداد بنجاح"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="فشل في تحديث الإعداد"
            )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في تحديث الإعداد: {str(e)}"
        )

@router.get("/blocked-devices")
async def get_blocked_devices(
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    الحصول على قائمة الأجهزة المحظورة
    """
    try:
        blocked_devices = device_security.get_blocked_devices(db)
        return {
            "success": True,
            "blocked_devices": blocked_devices,
            "total": len(blocked_devices)
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في الحصول على الأجهزة المحظورة: {str(e)}"
        )

@router.post("/block-device")
async def block_device(
    request: BlockDeviceRequest,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    حظر جهاز
    """
    try:
        # الحصول على بيانات الجهاز من قاعدة البيانات
        from services.device_tracker import device_tracker
        devices_data = await device_tracker.get_devices_from_database()
        device_data = None

        for device in devices_data.get('devices', []):
            if device.get('device_id') == request.device_id:
                device_data = device
                break

        if not device_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="الجهاز غير موجود"
            )

        # التحقق من أن الجهاز ليس الخادم الرئيسي
        if device_security._is_main_server_device(request.device_id):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="لا يمكن حظر الخادم الرئيسي"
            )

        success = device_security.block_device(
            device_data,
            str(current_user.username),
            request.reason,
            db
        )

        if success:
            # ✅ إشعار فوري للجهاز المحظور وتحديث قائمة الأجهزة
            try:
                import asyncio

                # إنشاء مهمة إشعار في الخلفية
                async def notify_block():
                    # تحديث فوري لقائمة الأجهزة عبر WebSocket
                    await device_tracker.broadcast_device_update()
                    logger.info(f"✅ تم إرسال إشعار WebSocket بحظر الجهاز: {request.device_id}")

                # تشغيل الإشعار في الخلفية
                asyncio.create_task(notify_block())
                logger.info(f"تم إرسال إشعار الحظر وتحديث قائمة الأجهزة للجهاز: {request.device_id}")
            except Exception as notify_error:
                logger.warning(f"فشل في إرسال إشعار الحظر: {notify_error}")

            return {"success": True, "message": "تم حظر الجهاز بنجاح"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="فشل في حظر الجهاز"
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في حظر الجهاز {request.device_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في حظر الجهاز: {str(e)}"
        )

@router.post("/unblock-device/{device_id}")
async def unblock_device(
    device_id: str,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    إلغاء حظر جهاز
    """
    try:
        success = device_security.unblock_device(device_id, db)

        if success:
            # ✅ إشعار فوري لإلغاء حظر الجهاز وتحديث قائمة الأجهزة
            try:
                from services.device_tracker import device_tracker
                import asyncio

                # إنشاء مهمة إشعار في الخلفية
                async def notify_unblock():
                    # تحديث فوري لقائمة الأجهزة عبر WebSocket
                    await device_tracker.broadcast_device_update()
                    logger.info(f"✅ تم إرسال إشعار WebSocket بإلغاء حظر الجهاز: {device_id}")

                # تشغيل الإشعار في الخلفية
                asyncio.create_task(notify_unblock())
                logger.info(f"تم إرسال إشعار إلغاء الحظر وتحديث قائمة الأجهزة للجهاز: {device_id}")
            except Exception as notify_error:
                logger.warning(f"فشل في إرسال إشعار إلغاء الحظر: {notify_error}")

            return {"success": True, "message": "تم إلغاء حظر الجهاز بنجاح"}
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="الجهاز غير موجود في قائمة المحظورين"
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في إلغاء حظر الجهاز {device_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في إلغاء حظر الجهاز: {str(e)}"
        )

@router.get("/pending-devices")
async def get_pending_devices(
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    الحصول على الأجهزة في انتظار الموافقة
    """
    try:
        from sqlalchemy import select
        from models.device_security import PendingDevice

        stmt = select(PendingDevice).where(PendingDevice.status == "pending")
        pending_devices = db.execute(stmt).scalars().all()

        devices_list = [
            {
                "id": device.id,
                "device_id": device.device_id,
                "client_ip": device.client_ip,
                "hostname": device.hostname,
                "device_type": device.device_type,
                "system": device.system,
                "platform": device.platform,
                "current_user": device.current_user,
                "requested_at": device.requested_at.isoformat() if device.requested_at else None,
                "first_access": device.first_access.isoformat() if device.first_access else None,
                "last_access": device.last_access.isoformat() if device.last_access else None,
                "access_count": device.access_count,
                "status": device.status
            }
            for device in pending_devices
        ]

        return {
            "success": True,
            "pending_devices": devices_list,
            "total": len(devices_list)
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في الحصول على الأجهزة المعلقة: {str(e)}"
        )

@router.post("/approve-device/{device_id}")
async def approve_device(
    device_id: str,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    الموافقة على جهاز
    """
    try:
        from sqlalchemy import select
        from models.device_security import PendingDevice
        from datetime import datetime

        stmt = select(PendingDevice).where(PendingDevice.device_id == device_id)
        pending_device = db.execute(stmt).scalar_one_or_none()

        if not pending_device:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="الجهاز غير موجود في قائمة الانتظار"
            )

        # استخدام النظام المحسن للاعتماد
        success = device_security.approve_device(
            device_id,
            str(current_user.username),
            "تمت الموافقة بواسطة المدير",
            db
        )

        if success:
            # إشعار فوري للجهاز المعتمد وتحديث قائمة الأجهزة
            try:
                # from utils.device_status_manager import device_status_manager  # تم إزالة هذا الملف
                from services.device_tracker import device_tracker
                import asyncio

                # إنشاء مهمة إشعار في الخلفية
                async def notify_approval():
                    # إشعار تغيير حالة الجهاز (تم إزالة device_status_manager)
                    # await device_status_manager._notify_status_change(device_id, 'approved')

                    # تحديث فوري لقائمة الأجهزة عبر WebSocket
                    await device_tracker.broadcast_device_update()

                # تشغيل الإشعار في الخلفية
                asyncio.create_task(notify_approval())
                logger.info(f"تم إرسال إشعار الموافقة وتحديث قائمة الأجهزة للجهاز: {device_id}")
            except Exception as notify_error:
                logger.warning(f"فشل في إرسال إشعار الموافقة: {notify_error}")

            return {"success": True, "message": "تمت الموافقة على الجهاز بنجاح"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="فشل في اعتماد الجهاز"
            )
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في الموافقة على الجهاز: {str(e)}"
        )

@router.post("/reject-device/{device_id}")
async def reject_device(
    device_id: str,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    رفض جهاز
    """
    try:
        from sqlalchemy import select
        from models.device_security import PendingDevice
        from datetime import datetime

        stmt = select(PendingDevice).where(PendingDevice.device_id == device_id)
        pending_device = db.execute(stmt).scalar_one_or_none()

        if not pending_device:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="الجهاز غير موجود في قائمة الانتظار"
            )

        # تحديث حالة الجهاز وحظره
        pending_device.status = "rejected"
        pending_device.reviewed_by = current_user.username
        pending_device.reviewed_at = datetime.now()
        pending_device.review_notes = "تم رفض الجهاز بواسطة المدير"

        # إضافة الجهاز لقائمة المحظورين
        device_data = {
            "device_id": pending_device.device_id,
            "client_ip": pending_device.client_ip,
            "hostname": pending_device.hostname,
            "device_type": pending_device.device_type,
            "system": pending_device.system,
            "platform": pending_device.platform,
            "user_agent": pending_device.user_agent,
            "first_access": pending_device.first_access,
            "last_access": pending_device.last_access,
            "access_count": pending_device.access_count
        }

        device_security.block_device(
            device_data,
            str(current_user.username),
            "تم رفض الجهاز من قبل المدير",
            db
        )

        db.commit()

        return {"success": True, "message": "تم رفض وحظر الجهاز بنجاح"}
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في رفض الجهاز: {str(e)}"
        )

@router.get("/redirect-info/{device_id}")
async def get_redirect_info(
    device_id: str,
    current_user: User = Depends(get_current_admin_user)
):
    """
    الحصول على معلومات إعادة التوجيه للجهاز
    """
    try:
        original_url = redirect_manager.get_original_url(device_id)
        smart_redirect = redirect_manager.get_smart_redirect_url(device_id)

        return {
            "success": True,
            "device_id": device_id,
            "original_url": original_url,
            "smart_redirect_url": smart_redirect,
            "has_saved_redirect": original_url is not None
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في الحصول على معلومات إعادة التوجيه: {str(e)}"
        )

@router.get("/redirect-stats")
async def get_redirect_stats(
    current_user: User = Depends(get_current_admin_user)
):
    """
    الحصول على إحصائيات إعادة التوجيه
    """
    try:
        stats = redirect_manager.get_redirect_stats()
        return {
            "success": True,
            "stats": stats
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في الحصول على إحصائيات إعادة التوجيه: {str(e)}"
        )

@router.post("/cleanup-expired-redirects")
async def cleanup_expired_redirects(
    current_user: User = Depends(get_current_admin_user)
):
    """
    تنظيف الروابط منتهية الصلاحية
    """
    try:
        cleaned_count = redirect_manager.cleanup_expired_redirects()
        return {
            "success": True,
            "message": f"تم تنظيف {cleaned_count} رابط منتهي الصلاحية",
            "cleaned_count": cleaned_count
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في تنظيف الروابط منتهية الصلاحية: {str(e)}"
        )
