"""
API endpoints للمهام المجدولة
"""

import json
import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException
from fastapi import status as http_status
from sqlalchemy.orm import Session
from sqlalchemy import desc

from database.session import get_db
from models.scheduled_task import ScheduledTask, TaskType, TaskStatus
from models.user import User, UserRole
from schemas.scheduled_task import (
    ScheduledTaskCreate,
    ScheduledTaskUpdate,
    ScheduledTaskResponse,
    ScheduledTaskStats,
    CronExpressionBuilder
)
from utils.auth import get_current_active_user
from services.scheduler_service import scheduler_service

router = APIRouter(prefix="/api/scheduled-tasks", tags=["scheduled-tasks"])
logger = logging.getLogger(__name__)

@router.get("/", response_model=List[ScheduledTaskResponse])
async def get_scheduled_tasks(
    skip: int = 0,
    limit: int = 100,
    task_type: Optional[TaskType] = None,
    status: Optional[TaskStatus] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """جلب قائمة المهام المجدولة"""
    try:
        if current_user.role.value != UserRole.ADMIN.value:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="يجب أن تكون مديراً للوصول إلى المهام المجدولة"
            )

        query = db.query(ScheduledTask)

        if task_type:
            query = query.filter(ScheduledTask.task_type == task_type)

        if status:
            query = query.filter(ScheduledTask.status == status)

        tasks = query.order_by(desc(ScheduledTask.created_at)).offset(skip).limit(limit).all()

        # تحويل البيانات يدوياً لتجنب مشاكل التحويل
        result = []
        for task in tasks:
            try:
                task_dict = {
                    "id": task.id,
                    "name": task.name,
                    "description": task.description,
                    "task_type": task.task_type,
                    "cron_expression": task.cron_expression,
                    "status": task.status,
                    "task_params": json.loads(task.task_params) if task.task_params else None,
                    "max_retries": task.max_retries or 3,
                    "timeout_seconds": task.timeout_seconds or 300,
                    "last_run": task.last_run,
                    "next_run": task.next_run,
                    "run_count": task.run_count or 0,
                    "failure_count": task.failure_count or 0,
                    "last_error": task.last_error,
                    "is_system_task": task.is_system_task or False,
                    "created_by": task.created_by,
                    "created_at": task.created_at,
                    "updated_at": task.updated_at
                }
                result.append(task_dict)
            except Exception as e:
                logger.error(f"خطأ في تحويل المهمة {task.id}: {e}")
                continue

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب المهام المجدولة: {e}")
        import traceback
        logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في جلب المهام المجدولة: {str(e)}"
        )

@router.get("/stats", response_model=ScheduledTaskStats)
async def get_scheduled_tasks_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """جلب إحصائيات المهام المجدولة"""
    if current_user.role.value != UserRole.ADMIN.value:
        raise HTTPException(
            status_code=http_status.HTTP_403_FORBIDDEN,
            detail="يجب أن تكون مديراً للوصول إلى إحصائيات المهام"
        )

    total_tasks = db.query(ScheduledTask).count()
    active_tasks = db.query(ScheduledTask).filter(ScheduledTask.status.value == TaskStatus.ACTIVE.value).count()
    paused_tasks = db.query(ScheduledTask).filter(ScheduledTask.status.value == TaskStatus.PAUSED.value).count()
    disabled_tasks = db.query(ScheduledTask).filter(ScheduledTask.status == TaskStatus.DISABLED).count()
    failed_tasks = db.query(ScheduledTask).filter(ScheduledTask.failure_count > 0).count()

    # إحصائيات اليوم (يمكن تحسينها لاحقاً)
    successful_runs_today = 0
    failed_runs_today = 0

    return ScheduledTaskStats(
        total_tasks=total_tasks,
        active_tasks=active_tasks,
        paused_tasks=paused_tasks,
        disabled_tasks=disabled_tasks,
        failed_tasks=failed_tasks,
        successful_runs_today=successful_runs_today,
        failed_runs_today=failed_runs_today
    )

@router.get("/status")
async def get_scheduler_status(
    current_user: User = Depends(get_current_active_user)
):
    """جلب حالة المجدول"""
    if current_user.role.value != UserRole.ADMIN.value:
        raise HTTPException(
            status_code=http_status.HTTP_403_FORBIDDEN,
            detail="يجب أن تكون مديراً للوصول إلى حالة المجدول"
        )

    try:
        is_running = scheduler_service.scheduler is not None and scheduler_service.scheduler.running
        job_count = len(scheduler_service.scheduler.get_jobs()) if scheduler_service.scheduler else 0

        return {
            "scheduler_running": is_running,
            "job_count": job_count,
            "scheduler_available": scheduler_service.scheduler is not None
        }
    except Exception as e:
        logger.error(f"خطأ في جلب حالة المجدول: {e}")
        return {
            "scheduler_running": False,
            "job_count": 0,
            "scheduler_available": False,
            "error": str(e)
        }

@router.get("/{task_id}", response_model=ScheduledTaskResponse)
async def get_scheduled_task(
    task_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """جلب مهمة مجدولة محددة"""
    if current_user.role.value != UserRole.ADMIN.value:
        raise HTTPException(
            status_code=http_status.HTTP_403_FORBIDDEN,
            detail="يجب أن تكون مديراً للوصول إلى المهام المجدولة"
        )

    task = db.query(ScheduledTask).filter(ScheduledTask.id == task_id).first()
    if not task:
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND,
            detail="المهمة غير موجودة"
        )

    return task

@router.post("/", response_model=ScheduledTaskResponse)
async def create_scheduled_task(
    task_data: ScheduledTaskCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """إنشاء مهمة مجدولة جديدة"""
    if current_user.role.value != UserRole.ADMIN.value:
        raise HTTPException(
            status_code=http_status.HTTP_403_FORBIDDEN,
            detail="يجب أن تكون مديراً لإنشاء مهام مجدولة"
        )

    # التحقق من عدم وجود مهمة بنفس الاسم
    existing_task = db.query(ScheduledTask).filter(ScheduledTask.name == task_data.name).first()
    if existing_task:
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail="يوجد مهمة بنفس الاسم مسبقاً"
        )

    # إنشاء المهمة
    task_params_json = None
    if task_data.task_params:
        task_params_json = json.dumps(task_data.task_params)

    new_task = ScheduledTask(
        name=task_data.name,
        description=task_data.description,
        task_type=task_data.task_type,
        cron_expression=task_data.cron_expression,
        status=task_data.status,
        task_params=task_params_json,
        max_retries=task_data.max_retries,
        timeout_seconds=task_data.timeout_seconds,
        created_by=current_user.id
    )

    db.add(new_task)
    db.commit()
    db.refresh(new_task)

    # إضافة المهمة إلى المجدول إذا كانت نشطة
    if new_task.status.value == TaskStatus.ACTIVE.value:
        try:
            scheduler_service._add_job_to_scheduler(new_task)
        except Exception as e:
            logger.error(f"خطأ في إضافة المهمة إلى المجدول: {e}")
            # لا نرفع خطأ هنا لأن المهمة تم حفظها في قاعدة البيانات

    logger.info(f"تم إنشاء مهمة مجدولة جديدة: {new_task.name}")
    return new_task

@router.put("/{task_id}", response_model=ScheduledTaskResponse)
async def update_scheduled_task(
    task_id: int,
    task_data: ScheduledTaskUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """تحديث مهمة مجدولة"""
    if current_user.role.value != UserRole.ADMIN.value:
        raise HTTPException(
            status_code=http_status.HTTP_403_FORBIDDEN,
            detail="يجب أن تكون مديراً لتحديث المهام المجدولة"
        )

    task = db.query(ScheduledTask).filter(ScheduledTask.id == task_id).first()
    if not task:
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND,
            detail="المهمة غير موجودة"
        )

    # التحقق من عدم وجود مهمة أخرى بنفس الاسم
    if task_data.name and task_data.name != task.name:
        existing_task = db.query(ScheduledTask).filter(
            ScheduledTask.name == task_data.name,
            ScheduledTask.id != task_id
        ).first()
        if existing_task:
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail="يوجد مهمة أخرى بنفس الاسم"
            )

    # تحديث البيانات
    update_data = task_data.model_dump(exclude_unset=True)

    if 'task_params' in update_data and update_data['task_params'] is not None:
        update_data['task_params'] = json.dumps(update_data['task_params'])

    for field, value in update_data.items():
        setattr(task, field, value)

    db.commit()
    db.refresh(task)

    # تحديث المهمة في المجدول
    try:
        # إزالة المهمة القديمة
        if scheduler_service.scheduler and scheduler_service.scheduler.get_job(f"task_{task_id}"):
            scheduler_service.scheduler.remove_job(f"task_{task_id}")

        # إضافة المهمة المحدثة إذا كانت نشطة
        if task.status.value == TaskStatus.ACTIVE.value:
            scheduler_service._add_job_to_scheduler(task)
    except Exception as e:
        logger.error(f"خطأ في تحديث المهمة في المجدول: {e}")

    logger.info(f"تم تحديث المهمة المجدولة: {task.name}")
    return task

@router.delete("/{task_id}")
async def delete_scheduled_task(
    task_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """حذف مهمة مجدولة"""
    if current_user.role.value != UserRole.ADMIN.value:
        raise HTTPException(
            status_code=http_status.HTTP_403_FORBIDDEN,
            detail="يجب أن تكون مديراً لحذف المهام المجدولة"
        )

    task = db.query(ScheduledTask).filter(ScheduledTask.id == task_id).first()
    if not task:
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND,
            detail="المهمة غير موجودة"
        )

    # منع حذف المهام الأساسية للنظام
    if task.is_system_task:
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail="لا يمكن حذف المهام الأساسية للنظام"
        )

    # إزالة المهمة من المجدول
    try:
        if scheduler_service.scheduler and scheduler_service.scheduler.get_job(f"task_{task_id}"):
            scheduler_service.scheduler.remove_job(f"task_{task_id}")
    except Exception as e:
        logger.error(f"خطأ في إزالة المهمة من المجدول: {e}")

    # حذف المهمة من قاعدة البيانات
    db.delete(task)
    db.commit()

    logger.info(f"تم حذف المهمة المجدولة: {task.name}")
    return {"message": "تم حذف المهمة بنجاح"}

@router.post("/{task_id}/toggle")
async def toggle_scheduled_task(
    task_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """تفعيل/إيقاف مهمة مجدولة"""
    if current_user.role.value != UserRole.ADMIN.value:
        raise HTTPException(
            status_code=http_status.HTTP_403_FORBIDDEN,
            detail="يجب أن تكون مديراً للتحكم في المهام المجدولة"
        )

    task = db.query(ScheduledTask).filter(ScheduledTask.id == task_id).first()
    if not task:
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND,
            detail="المهمة غير موجودة"
        )

    # تغيير حالة المهمة
    if task.status.value == TaskStatus.ACTIVE.value:
        task.status = TaskStatus.PAUSED
        # إزالة من المجدول
        try:
            if scheduler_service.scheduler and scheduler_service.scheduler.get_job(f"task_{task_id}"):
                scheduler_service.scheduler.remove_job(f"task_{task_id}")
        except Exception as e:
            logger.error(f"خطأ في إزالة المهمة من المجدول: {e}")
    else:
        task.status = TaskStatus.ACTIVE
        # إضافة إلى المجدول
        try:
            scheduler_service._add_job_to_scheduler(task)
        except Exception as e:
            logger.error(f"خطأ في إضافة المهمة إلى المجدول: {e}")

    db.commit()
    db.refresh(task)

    status_text = "تم تفعيل" if task.status.value == TaskStatus.ACTIVE.value else "تم إيقاف"
    logger.info(f"{status_text} المهمة المجدولة: {task.name}")

    return {
        "message": f"{status_text} المهمة بنجاح",
        "status": task.status
    }

@router.post("/{task_id}/run-now")
async def run_task_now(
    task_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """تشغيل مهمة فوراً"""
    if current_user.role.value != UserRole.ADMIN.value:
        raise HTTPException(
            status_code=http_status.HTTP_403_FORBIDDEN,
            detail="يجب أن تكون مديراً لتشغيل المهام"
        )

    task = db.query(ScheduledTask).filter(ScheduledTask.id == task_id).first()
    if not task:
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND,
            detail="المهمة غير موجودة"
        )

    try:
        # تشغيل المهمة فوراً
        scheduler_service._execute_task(task_id)
        return {"message": "تم تشغيل المهمة بنجاح"}
    except Exception as e:
        logger.error(f"خطأ في تشغيل المهمة {task_id}: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في تشغيل المهمة: {str(e)}"
        )

@router.post("/cron-builder", response_model=str)
async def build_cron_expression(
    cron_data: CronExpressionBuilder,
    current_user: User = Depends(get_current_active_user)
):
    """بناء تعبير Cron من المعاملات"""
    if current_user.role.value != UserRole.ADMIN.value:
        raise HTTPException(
            status_code=http_status.HTTP_403_FORBIDDEN,
            detail="يجب أن تكون مديراً لاستخدام منشئ Cron"
        )

    return cron_data.to_cron_expression()



@router.get("/health")
async def health_check():
    """فحص صحة النظام - بدون مصادقة للاختبار"""
    try:
        return {
            "status": "healthy",
            "scheduler_available": scheduler_service.scheduler is not None,
            "scheduler_running": scheduler_service.scheduler is not None and scheduler_service.scheduler.running,
            "message": "نظام المهام المجدولة يعمل"
        }
    except Exception as e:
        return {
            "status": "error",
            "scheduler_available": False,
            "scheduler_running": False,
            "error": str(e),
            "message": "خطأ في نظام المهام المجدولة"
        }
