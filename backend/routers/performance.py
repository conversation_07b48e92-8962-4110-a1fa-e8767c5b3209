"""
موجه الأداء والتحسينات
يحتوي على endpoints لمراقبة وتحسين أداء النظام
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import text, func, select
# from typing import Dict, Any  # سيتم استخدامها لاحقاً
import psutil
import time
import logging
from datetime import datetime, timedelta

from database.session import get_db, get_database_stats
from models.user import User
from models.sale import Sale, SaleItem
from models.product import Product
from models.customer import Customer
from utils.auth import get_current_active_user
from services.cache_service import cache_service, cached
from services.query_optimizer import QueryOptimizer

router = APIRouter(prefix="/api/performance", tags=["performance"])
logger = logging.getLogger(__name__)

@router.get("/database-stats")
async def get_database_performance_stats(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    الحصول على إحصائيات أداء قاعدة البيانات
    """
    try:
        # إحصائيات قاعدة البيانات الأساسية
        db_stats = get_database_stats()
        
        # إحصائيات الجداول
        table_stats = {}
        tables = ['sales', 'sale_items', 'products', 'customers', 'users']
        
        for table in tables:
            try:
                # عدد الصفوف
                count_result = db.execute(text(f"SELECT COUNT(*) FROM {table}"))
                row_count = count_result.scalar()
                
                # حجم الجدول (تقريبي)
                size_result = db.execute(text(f"SELECT COUNT(*) * 1024 as estimated_size FROM {table}"))
                estimated_size = size_result.scalar() or 0
                
                table_stats[table] = {
                    "row_count": row_count,
                    "estimated_size_bytes": estimated_size,
                    "estimated_size_mb": round(estimated_size / (1024 * 1024), 2)
                }
            except Exception as e:
                logger.warning(f"Error getting stats for table {table}: {e}")
                table_stats[table] = {"error": str(e)}
        
        # إحصائيات الفهارس
        index_result = db.execute(text("""
            SELECT name, tbl_name, sql 
            FROM sqlite_master 
            WHERE type='index' AND name NOT LIKE 'sqlite_%'
        """))
        indexes = [
            {"name": row[0], "table": row[1], "sql": row[2]} 
            for row in index_result.fetchall()
        ]
        
        # إحصائيات الأداء
        performance_stats = {
            "query_cache_hits": 0,  # يمكن تحسينه لاحقاً
            "query_cache_misses": 0,
            "average_query_time": 0,
            "slow_queries_count": 0
        }
        
        return {
            "database": db_stats,
            "tables": table_stats,
            "indexes": indexes,
            "performance": performance_stats,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting database stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving database statistics: {str(e)}"
        )

@router.get("/system-stats")
async def get_system_performance_stats(
    current_user: User = Depends(get_current_active_user)
):
    """
    الحصول على إحصائيات أداء النظام
    """
    try:
        # إحصائيات المعالج
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        
        # إحصائيات الذاكرة
        memory = psutil.virtual_memory()
        memory_stats = {
            "total_gb": round(memory.total / (1024**3), 2),
            "available_gb": round(memory.available / (1024**3), 2),
            "used_gb": round(memory.used / (1024**3), 2),
            "percent": memory.percent
        }
        
        # إحصائيات القرص
        disk = psutil.disk_usage('/')
        disk_stats = {
            "total_gb": round(disk.total / (1024**3), 2),
            "free_gb": round(disk.free / (1024**3), 2),
            "used_gb": round(disk.used / (1024**3), 2),
            "percent": round((disk.used / disk.total) * 100, 2)
        }
        
        # إحصائيات الشبكة
        try:
            network = psutil.net_io_counters()
            if network is not None:
                network_stats = {
                    "bytes_sent": getattr(network, 'bytes_sent', 0),
                    "bytes_recv": getattr(network, 'bytes_recv', 0),
                    "packets_sent": getattr(network, 'packets_sent', 0),
                    "packets_recv": getattr(network, 'packets_recv', 0)
                }
            else:
                # في حالة عدم توفر إحصائيات الشبكة
                network_stats = {
                    "bytes_sent": 0,
                    "bytes_recv": 0,
                    "packets_sent": 0,
                    "packets_recv": 0,
                    "error": "Network statistics not available"
                }
        except Exception as e:
            # في حالة حدوث خطأ في الحصول على إحصائيات الشبكة
            logger.warning(f"Error getting network stats: {e}")
            network_stats = {
                "bytes_sent": 0,
                "bytes_recv": 0,
                "packets_sent": 0,
                "packets_recv": 0,
                "error": f"Network statistics error: {str(e)}"
            }
        
        # معلومات العملية الحالية
        process = psutil.Process()
        process_stats = {
            "cpu_percent": process.cpu_percent(),
            "memory_mb": round(process.memory_info().rss / (1024**2), 2),
            "threads": process.num_threads(),
            "connections": len(process.net_connections())
        }
        
        return {
            "cpu": {
                "percent": cpu_percent,
                "count": cpu_count
            },
            "memory": memory_stats,
            "disk": disk_stats,
            "network": network_stats,
            "process": process_stats,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting system stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving system statistics: {str(e)}"
        )

@router.get("/cache-stats")
async def get_cache_performance_stats(
    current_user: User = Depends(get_current_active_user)
):
    """
    الحصول على إحصائيات التخزين المؤقت
    """
    try:
        cache_stats = cache_service.get_stats()
        
        return {
            "cache": cache_stats,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting cache stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving cache statistics: {str(e)}"
        )

@router.post("/optimize-database")
async def optimize_database_performance(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    تحسين أداء قاعدة البيانات
    """
    # التحقق من صلاحيات المدير
    user_role = getattr(current_user.role, 'value', str(current_user.role))
    if user_role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    
    try:
        optimizer = QueryOptimizer(db)
        result = optimizer.optimize_database()
        
        return {
            "status": "success",
            "message": "Database optimization completed",
            "details": result,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error optimizing database: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error optimizing database: {str(e)}"
        )

@router.post("/clear-cache")
async def clear_application_cache(
    current_user: User = Depends(get_current_active_user)
):
    """
    مسح التخزين المؤقت للتطبيق
    """
    try:
        cache_service.clear()
        
        return {
            "status": "success",
            "message": "Cache cleared successfully",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error clearing cache: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error clearing cache: {str(e)}"
        )

@router.get("/query-performance")
@cached("query_performance", ttl=60)  # تخزين مؤقت لدقيقة واحدة
async def get_query_performance_stats(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    الحصول على إحصائيات أداء الاستعلامات
    """
    try:
        # قياس أداء الاستعلامات الشائعة
        performance_tests = []
        
        # اختبار استعلام المبيعات
        start_time = time.time()
        sales_count = db.execute(select(func.count(Sale.id))).scalar()
        sales_query_time = (time.time() - start_time) * 1000
        
        performance_tests.append({
            "query": "Sales Count",
            "execution_time_ms": round(sales_query_time, 2),
            "result_count": sales_count,
            "status": "fast" if sales_query_time < 100 else "slow" if sales_query_time < 500 else "very_slow"
        })
        
        # اختبار استعلام المنتجات
        start_time = time.time()
        products_count = db.execute(select(func.count(Product.id))).scalar()
        products_query_time = (time.time() - start_time) * 1000
        
        performance_tests.append({
            "query": "Products Count",
            "execution_time_ms": round(products_query_time, 2),
            "result_count": products_count,
            "status": "fast" if products_query_time < 100 else "slow" if products_query_time < 500 else "very_slow"
        })
        
        # اختبار استعلام العملاء
        start_time = time.time()
        customers_count = db.execute(select(func.count(Customer.id))).scalar()
        customers_query_time = (time.time() - start_time) * 1000
        
        performance_tests.append({
            "query": "Customers Count",
            "execution_time_ms": round(customers_query_time, 2),
            "result_count": customers_count,
            "status": "fast" if customers_query_time < 100 else "slow" if customers_query_time < 500 else "very_slow"
        })
        
        # اختبار استعلام معقد (المبيعات مع العناصر)
        start_time = time.time()
        complex_result = db.execute(
            select(func.count(Sale.id))
            .join(SaleItem, Sale.id == SaleItem.sale_id)
            .where(Sale.created_at >= datetime.now() - timedelta(days=30))
        ).scalar()
        complex_query_time = (time.time() - start_time) * 1000
        
        performance_tests.append({
            "query": "Complex Sales Query (30 days)",
            "execution_time_ms": round(complex_query_time, 2),
            "result_count": complex_result,
            "status": "fast" if complex_query_time < 200 else "slow" if complex_query_time < 1000 else "very_slow"
        })
        
        # حساب المتوسط العام
        avg_query_time = sum(test["execution_time_ms"] for test in performance_tests) / len(performance_tests)
        
        return {
            "tests": performance_tests,
            "summary": {
                "average_query_time_ms": round(avg_query_time, 2),
                "total_tests": len(performance_tests),
                "fast_queries": len([t for t in performance_tests if t["status"] == "fast"]),
                "slow_queries": len([t for t in performance_tests if t["status"] in ["slow", "very_slow"]]),
                "overall_status": "good" if avg_query_time < 150 else "warning" if avg_query_time < 500 else "critical"
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting query performance stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving query performance statistics: {str(e)}"
        )
