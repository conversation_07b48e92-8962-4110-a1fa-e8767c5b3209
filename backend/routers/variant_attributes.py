from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from database.session import get_db
from models.user import User
from services.variant_attribute_service import VariantAttributeService
from schemas.variant_attribute import (
    VariantAttributeCreate,
    VariantAttributeUpdate,
    VariantAttributeResponse,
    VariantValueCreate,
    VariantValueUpdate,
    VariantValueResponse,
    AttributeOrderUpdate,
    ValueOrderUpdate
)
from utils.auth import get_current_user

router = APIRouter(prefix="/api/variant-attributes", tags=["variant-attributes"])


@router.get("/", response_model=List[VariantAttributeResponse])
async def get_all_attributes(
    include_inactive: bool = Query(False, description="تضمين الخصائص غير النشطة"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    جلب جميع خصائص المتغيرات مع قيمها
    """
    try:
        service = VariantAttributeService(db)
        attributes = service.get_all_attributes(include_inactive=include_inactive)
        return attributes
    except Exception as error:
        print(f"خطأ في جلب خصائص المتغيرات: {error}")
        raise HTTPException(status_code=500, detail="فشل في جلب خصائص المتغيرات")


@router.get("/{attribute_id}", response_model=VariantAttributeResponse)
async def get_attribute_by_id(
    attribute_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    جلب خاصية محددة بواسطة المعرف
    """
    try:
        service = VariantAttributeService(db)
        attribute = service.get_attribute_by_id(attribute_id)
        
        if not attribute:
            raise HTTPException(status_code=404, detail="الخاصية غير موجودة")
        
        return attribute
    except HTTPException:
        raise
    except Exception as error:
        print(f"خطأ في جلب الخاصية {attribute_id}: {error}")
        raise HTTPException(status_code=500, detail="فشل في جلب الخاصية")


@router.post("/", response_model=VariantAttributeResponse)
async def create_attribute(
    attribute_data: VariantAttributeCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    إنشاء خاصية جديدة مع قيمها
    """
    try:
        service = VariantAttributeService(db)
        attribute = service.create_attribute(attribute_data, int(current_user.id))
        return attribute
    except HTTPException:
        raise
    except Exception as error:
        print(f"خطأ في إنشاء الخاصية: {error}")
        raise HTTPException(status_code=500, detail="فشل في إنشاء الخاصية")


@router.put("/{attribute_id}", response_model=VariantAttributeResponse)
async def update_attribute(
    attribute_id: int,
    attribute_data: VariantAttributeUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    تحديث خاصية موجودة
    """
    try:
        service = VariantAttributeService(db)
        attribute = await service.update_attribute(attribute_id, attribute_data, int(current_user.id))
        return attribute
    except HTTPException:
        raise
    except Exception as error:
        print(f"خطأ في تحديث الخاصية {attribute_id}: {error}")
        raise HTTPException(status_code=500, detail="فشل في تحديث الخاصية")


@router.get("/{attribute_id}/usage")
async def check_attribute_usage(
    attribute_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    فحص استخدام الخاصية في المنتجات
    """
    try:
        service = VariantAttributeService(db)
        usage_info = await service.check_attribute_usage(attribute_id)
        return usage_info

    except HTTPException:
        raise
    except Exception as error:
        print(f"خطأ في فحص استخدام الخاصية {attribute_id}: {error}")
        raise HTTPException(status_code=500, detail="فشل في فحص استخدام الخاصية")

@router.delete("/{attribute_id}")
async def delete_attribute(
    attribute_id: int,
    force_delete: bool = Query(False, description="إجبار الحذف حتى لو كانت مستخدمة"),
    permanent_delete: bool = Query(True, description="حذف فعلي من قاعدة البيانات"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    حذف خاصية مع فحص الاستخدام
    """
    try:
        service = VariantAttributeService(db)
        result = await service.delete_attribute(attribute_id, force_delete, permanent_delete)
        return result

    except HTTPException:
        raise
    except Exception as error:
        print(f"خطأ في حذف الخاصية {attribute_id}: {error}")
        raise HTTPException(status_code=500, detail="فشل في حذف الخاصية")


@router.put("/reorder")
async def reorder_attributes(
    attribute_orders: List[AttributeOrderUpdate],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    إعادة ترتيب الخصائص
    """
    try:
        service = VariantAttributeService(db)
        success = await service.reorder_attributes(attribute_orders)
        
        if success:
            return {"message": "تم إعادة ترتيب الخصائص بنجاح"}
        else:
            raise HTTPException(status_code=500, detail="فشل في إعادة ترتيب الخصائص")
    except HTTPException:
        raise
    except Exception as error:
        print(f"خطأ في إعادة ترتيب الخصائص: {error}")
        raise HTTPException(status_code=500, detail="فشل في إعادة ترتيب الخصائص")


# ==================== مسارات إدارة قيم الخصائص ====================

@router.get("/{attribute_id}/values", response_model=List[VariantValueResponse])
async def get_attribute_values(
    attribute_id: int,
    include_inactive: bool = Query(False, description="تضمين القيم غير النشطة"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    جلب قيم خاصية معينة
    """
    try:
        service = VariantAttributeService(db)
        values = await service.get_attribute_values(attribute_id, include_inactive=include_inactive)
        return values
    except HTTPException:
        raise
    except Exception as error:
        print(f"خطأ في جلب قيم الخاصية {attribute_id}: {error}")
        raise HTTPException(status_code=500, detail="فشل في جلب قيم الخاصية")


@router.post("/{attribute_id}/values", response_model=VariantValueResponse)
async def add_attribute_value(
    attribute_id: int,
    value_data: VariantValueCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    إضافة قيمة جديدة لخاصية
    """
    try:
        service = VariantAttributeService(db)
        value = await service.add_attribute_value(attribute_id, value_data)
        return value
    except HTTPException:
        raise
    except Exception as error:
        print(f"خطأ في إضافة قيمة للخاصية {attribute_id}: {error}")
        raise HTTPException(status_code=500, detail="فشل في إضافة القيمة")


@router.put("/values/{value_id}", response_model=VariantValueResponse)
async def update_attribute_value(
    value_id: int,
    value_data: VariantValueUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    تحديث قيمة خاصية
    """
    try:
        service = VariantAttributeService(db)
        value = await service.update_attribute_value(value_id, value_data)
        return value
    except HTTPException:
        raise
    except Exception as error:
        print(f"خطأ في تحديث القيمة {value_id}: {error}")
        raise HTTPException(status_code=500, detail="فشل في تحديث القيمة")


@router.delete("/values/{value_id}")
async def delete_attribute_value(
    value_id: int,
    permanent_delete: bool = Query(True, description="حذف فعلي من قاعدة البيانات"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    حذف قيمة خاصية
    """
    try:
        service = VariantAttributeService(db)
        success = await service.delete_attribute_value(value_id, permanent_delete)

        if success:
            message = "تم حذف القيمة نهائياً" if permanent_delete else "تم تعطيل القيمة"
            return {"message": message, "permanent": permanent_delete}
        else:
            raise HTTPException(status_code=500, detail="فشل في حذف القيمة")
    except HTTPException:
        raise
    except Exception as error:
        print(f"خطأ في حذف القيمة {value_id}: {error}")
        raise HTTPException(status_code=500, detail="فشل في حذف القيمة")


@router.put("/values/reorder")
async def reorder_attribute_values(
    value_orders: List[ValueOrderUpdate],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    إعادة ترتيب قيم الخصائص
    """
    try:
        service = VariantAttributeService(db)
        success = await service.reorder_attribute_values(value_orders)
        
        if success:
            return {"message": "تم إعادة ترتيب قيم الخصائص بنجاح"}
        else:
            raise HTTPException(status_code=500, detail="فشل في إعادة ترتيب قيم الخصائص")
    except HTTPException:
        raise
    except Exception as error:
        print(f"خطأ في إعادة ترتيب قيم الخصائص: {error}")
        raise HTTPException(status_code=500, detail="فشل في إعادة ترتيب قيم الخصائص")
