"""
PDF Export API endpoints for SmartPOS system.
Provides server-side PDF generation with advanced Arabic text support.
"""

from fastapi import APIRouter, Depends, HTTPException, Query, Response
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import List, Optional, Dict, Any
import logging
import os
import json
import tempfile
from pathlib import Path

from database.session import get_db
from models.user import User
from models.device_fingerprint import DeviceFingerprint, DeviceFingerprintHistory
from routers.auth import get_current_user
from services.advanced_pdf_service import AdvancedPDFService
from utils.datetime_utils import get_tripoli_now
import pytz
from datetime import datetime

# إعداد السجلات
logger = logging.getLogger(__name__)

def safe_datetime_convert(dt):
    """تحويل آمن للتواريخ مع معالجة timezone"""
    if dt is None:
        return get_tripoli_now()

    try:
        # إذا كان نص
        if isinstance(dt, str):
            try:
                dt = datetime.fromisoformat(dt.replace('Z', '+00:00'))
            except:
                return get_tripoli_now()

        # إذا لم يكن له timezone
        if dt.tzinfo is None:
            tripoli_tz = pytz.timezone('Africa/Tripoli')
            dt = tripoli_tz.localize(dt)

        return dt
    except Exception:
        return get_tripoli_now()

router = APIRouter(prefix="/api/pdf", tags=["pdf-export"])


@router.post("/device-access-log/{device_id}")
async def export_device_access_log(
    device_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    تصدير سجل وصول الجهاز إلى PDF من الخلفية
    يدعم النصوص العربية بشكل متقدم
    """
    try:
        logger.info(f"بدء تصدير سجل وصول الجهاز: {device_id}")
        
        # البحث عن الجهاز
        device = db.query(DeviceFingerprint).filter(
            DeviceFingerprint.fingerprint_id == device_id
        ).first()
        
        if not device:
            raise HTTPException(
                status_code=404,
                detail=f"الجهاز غير موجود: {device_id}"
            )
        
        # ✅ جلب سجل الوصول من مصادر متعددة محسنة
        access_history = []

        # 1. جلب من DeviceFingerprintHistory باستخدام fingerprint_id
        try:
            fingerprint_history = db.query(DeviceFingerprintHistory).filter(
                DeviceFingerprintHistory.fingerprint_id == device_id
            ).order_by(DeviceFingerprintHistory.created_at.desc()).limit(100).all()
            access_history.extend(fingerprint_history)
            logger.info(f"تم جلب {len(fingerprint_history)} سجل من DeviceFingerprintHistory")
        except Exception as fp_error:
            logger.warning(f"خطأ في جلب DeviceFingerprintHistory: {fp_error}")

        # 2. ✅ جلب من نظام تتبع الأجهزة المحسن (ApprovedDevice & PendingDevice)
        try:
            from models.device_security import ApprovedDevice, PendingDevice
            from sqlalchemy import select

            # البحث في الأجهزة المعتمدة والمنتظرة
            device_security_stmt = select(ApprovedDevice).where(ApprovedDevice.device_id == device_id)
            approved_device = db.execute(device_security_stmt).scalar_one_or_none()

            if not approved_device:
                pending_device_stmt = select(PendingDevice).where(PendingDevice.device_id == device_id)
                pending_device = db.execute(pending_device_stmt).scalar_one_or_none()
                if pending_device:
                    # إضافة سجل من الجهاز المنتظر
                    access_history.append({
                        'id': f"pending_{pending_device.id}",
                        'event_type': 'device_pending_approval',
                        'ip_address': pending_device.client_ip,
                        'user_agent': pending_device.user_agent or 'غير معروف',
                        'created_at': pending_device.requested_at,
                        'event_data': {
                            'status': 'pending',
                            'current_user': pending_device.current_user,
                            'access_count': pending_device.access_count
                        }
                    })
            else:
                # إضافة سجل من الجهاز المعتمد
                access_history.append({
                    'id': f"approved_{approved_device.id}",
                    'event_type': 'device_approved',
                    'ip_address': approved_device.client_ip,
                    'user_agent': approved_device.user_agent or 'غير معروف',
                    'created_at': approved_device.approved_at,
                    'event_data': {
                        'status': 'approved',
                        'current_user': approved_device.current_user,
                        'approved_by': approved_device.approved_by,
                        'access_count': approved_device.access_count
                    }
                })

            logger.info(f"تم جلب بيانات من نظام تتبع الأجهزة المحسن")
        except Exception as security_error:
            logger.warning(f"خطأ في جلب بيانات نظام تتبع الأجهزة: {security_error}")

        # 3. جلب من device_access_logs إذا كان موجوداً
        try:
            device_logs_query = db.execute(
                text("SELECT * FROM device_access_logs WHERE device_id = :device_id ORDER BY created_at DESC LIMIT 50"),
                {"device_id": device_id}
            ).fetchall()

            # تحويل النتائج إلى قاموس
            for log in device_logs_query:
                access_history.append({
                    'id': log.id if hasattr(log, 'id') else len(access_history) + 1,
                    'event_type': log.event_type if hasattr(log, 'event_type') else 'device_access',
                    'ip_address': log.ip_address if hasattr(log, 'ip_address') else 'غير معروف',
                    'user_agent': log.user_agent if hasattr(log, 'user_agent') else 'غير معروف',
                    'created_at': safe_datetime_convert(log.created_at if hasattr(log, 'created_at') else None),
                    'event_data': log.event_data if hasattr(log, 'event_data') else None
                })
            logger.info(f"تم جلب {len(device_logs_query)} سجل من device_access_logs")
        except Exception as log_error:
            logger.debug(f"لا يمكن الوصول إلى device_access_logs: {log_error}")

        # 4. ✅ جلب من سجل أنشطة المستخدمين الجديد
        try:
            user_activity_logs = db.execute(
                text("""
                    SELECT * FROM user_activity_logs
                    WHERE event_data LIKE :device_pattern
                    ORDER BY created_at DESC LIMIT 30
                """),
                {"device_pattern": f"%{device_id}%"}
            ).fetchall()

            for log in user_activity_logs:
                access_history.append({
                    'id': f"user_activity_{log.id}" if hasattr(log, 'id') else len(access_history) + 1,
                    'event_type': f"user_{log.event_type}" if hasattr(log, 'event_type') else 'user_activity',
                    'ip_address': log.ip_address if hasattr(log, 'ip_address') else 'غير معروف',
                    'user_agent': log.user_agent if hasattr(log, 'user_agent') else 'غير معروف',
                    'created_at': safe_datetime_convert(log.created_at if hasattr(log, 'created_at') else None),
                    'event_data': {
                        'username': log.username if hasattr(log, 'username') else None,
                        'event_type': log.event_type if hasattr(log, 'event_type') else None
                    }
                })
            logger.info(f"تم جلب {len(user_activity_logs)} سجل من user_activity_logs")
        except Exception as activity_error:
            logger.debug(f"لا يمكن الوصول إلى user_activity_logs: {activity_error}")

        # ترتيب حسب التاريخ وأخذ أحدث 100 سجل
        if access_history:
            def safe_get_datetime(item):
                """استخراج التاريخ بشكل آمن من العنصر"""
                try:
                    if hasattr(item, 'created_at'):
                        # إذا كان object
                        dt = item.created_at
                    elif isinstance(item, dict):
                        # إذا كان dictionary
                        dt = item.get('created_at')
                    else:
                        dt = None

                    return safe_datetime_convert(dt)
                except Exception:
                    return get_tripoli_now()

            access_history.sort(key=safe_get_datetime, reverse=True)
            access_history = access_history[:100]
        
        # جلب معلومات إضافية من ملف التكوين
        config_device_info = {}
        try:
            import os
            config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'connected_devices.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    for device_config in config_data.get('devices', []):
                        if device_config.get('fingerprint_id') == device_id:
                            config_device_info = device_config
                            break
                logger.info(f"تم جلب معلومات إضافية من التكوين: {bool(config_device_info)}")
        except Exception as config_error:
            logger.warning(f"لا يمكن قراءة ملف التكوين: {config_error}")

        # استخراج المعلومات من الحقول JSON بطريقة آمنة ومحسنة
        try:
            # محاولة استخراج JSON من قاعدة البيانات
            device_info_json = {}
            system_info_json = {}
            network_info_json = {}

            if hasattr(device, 'device_info') and device.device_info:
                try:
                    device_info_json = json.loads(device.device_info)
                except json.JSONDecodeError:
                    logger.warning(f"خطأ في تحليل device_info JSON للجهاز {device_id}")

            if hasattr(device, 'system_info') and device.system_info:
                try:
                    system_info_json = json.loads(device.system_info)
                except json.JSONDecodeError:
                    logger.warning(f"خطأ في تحليل system_info JSON للجهاز {device_id}")

            if hasattr(device, 'network_info') and device.network_info:
                try:
                    network_info_json = json.loads(device.network_info)
                except json.JSONDecodeError:
                    logger.warning(f"خطأ في تحليل network_info JSON للجهاز {device_id}")

            # استخراج hostname من مصادر متعددة بأولوية
            hostname = 'غير معروف'

            # 1. من ملف التكوين أولاً (أعلى أولوية)
            if config_device_info:
                hostname = config_device_info.get('hostname',
                          config_device_info.get('device_name',
                          config_device_info.get('computer_name', hostname)))

            # 2. من device_info_json
            if hostname == 'غير معروف' and device_info_json:
                hostname = device_info_json.get('hostname',
                          device_info_json.get('computer_name',
                          device_info_json.get('device_name',
                          device_info_json.get('computerName',
                          device_info_json.get('platform', hostname)))))

            # 3. من system_info_json
            if hostname == 'غير معروف' and system_info_json:
                hostname = system_info_json.get('hostname',
                          system_info_json.get('computer_name',
                          system_info_json.get('device_name',
                          system_info_json.get('computerName',
                          system_info_json.get('platform', hostname)))))

            # 4. من browser_info_json
            browser_info_json = {}
            if hasattr(device, 'browser_info') and device.browser_info:
                try:
                    browser_info_json = json.loads(device.browser_info)
                    if hostname == 'غير معروف':
                        hostname = browser_info_json.get('platform',
                                  browser_info_json.get('userAgent', hostname))
                except json.JSONDecodeError:
                    pass

            # 5. من قاعدة البيانات
            if hostname == 'غير معروف' and hasattr(device, 'hostname') and device.hostname:
                hostname = device.hostname

            # 6. من last_user_agent إذا كان متوفراً
            if hostname == 'غير معروف' and hasattr(device, 'last_user_agent') and device.last_user_agent:
                user_agent = device.last_user_agent
                if 'Windows' in user_agent:
                    hostname = 'جهاز Windows'
                elif 'Mac' in user_agent:
                    hostname = 'جهاز Mac'
                elif 'Linux' in user_agent:
                    hostname = 'جهاز Linux'
                elif 'Android' in user_agent:
                    hostname = 'جهاز Android'
                elif 'iPhone' in user_agent or 'iPad' in user_agent:
                    hostname = 'جهاز iOS'

            # استخراج نوع الجهاز مع تفاصيل شاملة
            device_type = 'غير معروف'

            # من ملف التكوين
            if config_device_info:
                device_type = config_device_info.get('device_type',
                             config_device_info.get('platform', device_type))

            # من system_info_json مع تفاصيل أكثر
            if device_type == 'غير معروف' and system_info_json:
                platform = system_info_json.get('platform', '')
                os_name = system_info_json.get('os', system_info_json.get('operatingSystem', ''))
                browser = system_info_json.get('browser', '')
                arch = system_info_json.get('architecture', system_info_json.get('arch', ''))

                if platform and os_name:
                    device_type = f"{platform} - {os_name}"
                    if arch:
                        device_type += f" ({arch})"
                elif platform:
                    device_type = platform
                elif os_name:
                    device_type = os_name
                elif browser and 'Mozilla' in browser:
                    if 'Chrome' in browser:
                        device_type = 'متصفح Chrome'
                    elif 'Firefox' in browser:
                        device_type = 'متصفح Firefox'
                    elif 'Safari' in browser:
                        device_type = 'متصفح Safari'
                    else:
                        device_type = 'متصفح ويب'

            # من browser_info_json
            if device_type == 'غير معروف' and browser_info_json:
                browser_name = browser_info_json.get('name', '')
                platform = browser_info_json.get('platform', '')

                if browser_name and platform:
                    device_type = f"{browser_name} على {platform}"
                elif browser_name:
                    device_type = f"متصفح {browser_name}"
                elif platform:
                    device_type = platform

            # من device_info_json
            if device_type == 'غير معروف' and device_info_json:
                device_type = device_info_json.get('platform',
                             device_info_json.get('os',
                             device_info_json.get('device_type', device_type)))

            # من قاعدة البيانات
            if device_type == 'غير معروف' and hasattr(device, 'device_type') and device.device_type:
                device_type = device.device_type

            # من last_user_agent كحل أخير
            if device_type == 'غير معروف' and hasattr(device, 'last_user_agent') and device.last_user_agent:
                user_agent = device.last_user_agent
                if 'Windows NT' in user_agent:
                    device_type = 'Windows Desktop'
                elif 'Macintosh' in user_agent:
                    device_type = 'Mac Desktop'
                elif 'X11; Linux' in user_agent:
                    device_type = 'Linux Desktop'
                elif 'Android' in user_agent:
                    device_type = 'Android Mobile'
                elif 'iPhone' in user_agent:
                    device_type = 'iPhone'
                elif 'iPad' in user_agent:
                    device_type = 'iPad'
                elif 'Chrome' in user_agent:
                    device_type = 'متصفح Chrome'
                elif 'Firefox' in user_agent:
                    device_type = 'متصفح Firefox'
                elif 'Safari' in user_agent:
                    device_type = 'متصفح Safari'

            # استخراج IP العميل
            client_ip = 'غير معروف'
            if config_device_info:
                client_ip = config_device_info.get('client_ip',
                           config_device_info.get('ip_address', client_ip))
            elif network_info_json:
                client_ip = network_info_json.get('client_ip',
                           network_info_json.get('ip_address',
                           network_info_json.get('ipAddress', client_ip)))
            elif hasattr(device, 'last_ip') and device.last_ip:
                client_ip = device.last_ip

        except Exception as extract_error:
            logger.warning(f"خطأ في استخراج بيانات JSON: {extract_error}")
            hostname = 'غير معروف'
            device_type = 'غير معروف'
            client_ip = 'غير معروف'

        # ✅ جلب معلومات إضافية من نظام تتبع الأجهزة المحسن
        enhanced_device_info = {}
        try:
            from models.device_security import ApprovedDevice, PendingDevice
            from sqlalchemy import select

            # البحث في الأجهزة المعتمدة
            approved_stmt = select(ApprovedDevice).where(ApprovedDevice.device_id == device_id)
            approved_device = db.execute(approved_stmt).scalar_one_or_none()

            if approved_device:
                enhanced_device_info = {
                    'status': 'معتمد',
                    'current_user': approved_device.current_user or 'غير محدد',
                    'approved_by': approved_device.approved_by,
                    'approved_at': safe_datetime_convert(approved_device.approved_at),
                    'approval_notes': approved_device.approval_notes or 'تم اعتماد الجهاز',
                    'access_count': approved_device.access_count or 0,
                    'browser': approved_device.browser or 'غير معروف',
                    'system': approved_device.system or 'غير معروف',
                    'platform': approved_device.platform or 'غير معروف'
                }
            else:
                # البحث في الأجهزة المنتظرة
                pending_stmt = select(PendingDevice).where(PendingDevice.device_id == device_id)
                pending_device = db.execute(pending_stmt).scalar_one_or_none()

                if pending_device:
                    enhanced_device_info = {
                        'status': 'في انتظار الموافقة',
                        'current_user': pending_device.current_user or 'غير محدد',
                        'requested_at': safe_datetime_convert(pending_device.requested_at),
                        'access_count': pending_device.access_count or 0,
                        'browser': pending_device.browser or 'غير معروف',
                        'system': pending_device.system or 'غير معروف',
                        'platform': pending_device.platform or 'غير معروف'
                    }
                else:
                    enhanced_device_info = {
                        'status': 'غير مسجل في النظام',
                        'current_user': 'غير محدد'
                    }
        except Exception as enhanced_error:
            logger.warning(f"خطأ في جلب المعلومات المحسنة: {enhanced_error}")
            enhanced_device_info = {'status': 'غير معروف'}

        # تحويل البيانات إلى قاموس آمن مع المعلومات المحسنة
        device_info = {
            'device_id': getattr(device, 'fingerprint_id', device_id),
            'hostname': hostname,
            'client_ip': client_ip,
            'device_type': device_type,
            'last_access': safe_datetime_convert(getattr(device, 'last_seen_at', None)),
            'created_at': safe_datetime_convert(getattr(device, 'created_at', None)),
            'is_approved': getattr(device, 'is_approved', True),
            'is_blocked': getattr(device, 'is_blocked', False),
            # ✅ إضافة المعلومات المحسنة
            **enhanced_device_info
        }
        
        # تحويل سجل الوصول إلى قاموس آمن مع تحسين user_agent
        history_data = []
        try:
            for entry in access_history:
                # استخراج user_agent محسن
                user_agent = getattr(entry, 'user_agent', 'غير معروف')

                # تحسين عرض user_agent
                if user_agent and user_agent != 'غير معروف':
                    # استخراج معلومات المتصفح ونظام التشغيل
                    if 'Chrome' in user_agent:
                        if 'Windows' in user_agent:
                            user_agent_display = 'Chrome على Windows'
                        elif 'Mac' in user_agent:
                            user_agent_display = 'Chrome على Mac'
                        elif 'Linux' in user_agent:
                            user_agent_display = 'Chrome على Linux'
                        elif 'Android' in user_agent:
                            user_agent_display = 'Chrome على Android'
                        else:
                            user_agent_display = 'متصفح Chrome'
                    elif 'Firefox' in user_agent:
                        if 'Windows' in user_agent:
                            user_agent_display = 'Firefox على Windows'
                        elif 'Mac' in user_agent:
                            user_agent_display = 'Firefox على Mac'
                        elif 'Linux' in user_agent:
                            user_agent_display = 'Firefox على Linux'
                        else:
                            user_agent_display = 'متصفح Firefox'
                    elif 'Safari' in user_agent:
                        if 'iPhone' in user_agent:
                            user_agent_display = 'Safari على iPhone'
                        elif 'iPad' in user_agent:
                            user_agent_display = 'Safari على iPad'
                        elif 'Mac' in user_agent:
                            user_agent_display = 'Safari على Mac'
                        else:
                            user_agent_display = 'متصفح Safari'
                    elif 'Edge' in user_agent:
                        user_agent_display = 'Microsoft Edge'
                    else:
                        # عرض أول 100 حرف من user_agent الأصلي
                        user_agent_display = user_agent[:100] + ('...' if len(user_agent) > 100 else '')
                else:
                    user_agent_display = 'غير معروف'

                history_data.append({
                    'id': getattr(entry, 'id', ''),
                    'event_type': getattr(entry, 'event_type', 'غير معروف'),
                    'ip_address': getattr(entry, 'ip_address', 'غير معروف'),
                    'user_agent': user_agent_display,
                    'created_at': getattr(entry, 'created_at', None),
                    'event_data': getattr(entry, 'event_data', None)
                })
        except Exception as history_error:
            logger.warning(f"خطأ في معالجة سجل الوصول: {history_error}")
            history_data = []
        
        # تسجيل معلومات التشخيص
        logger.info(f"معلومات الجهاز: {device_info}")
        logger.info(f"عدد سجلات الوصول: {len(history_data)}")

        # إنشاء خدمة PDF المتقدمة
        try:
            pdf_service = AdvancedPDFService({
                'company_name': 'نظام SmartPOS المتقدم',
                'theme': 'light',
                'include_header': True,
                'include_footer': True
            })

            logger.info(f"تم إنشاء خدمة PDF بمحرك: {pdf_service.engine}")

            # تصدير PDF
            output_path = pdf_service.export_device_access_log(
                device_info=device_info,
                access_history=history_data
            )

            logger.info(f"تم إنشاء PDF في المسار: {output_path}")

        except Exception as pdf_error:
            logger.error(f"خطأ في إنشاء خدمة PDF: {pdf_error}")
            raise HTTPException(
                status_code=500,
                detail=f"فشل في إنشاء خدمة PDF: {str(pdf_error)}"
            )
        
        # التحقق من وجود الملف
        if not os.path.exists(output_path):
            logger.error(f"الملف غير موجود: {output_path}")
            raise HTTPException(
                status_code=500,
                detail="فشل في إنشاء ملف PDF"
            )

        # التحقق من حجم الملف
        file_size = os.path.getsize(output_path)
        logger.info(f"حجم الملف المُنشأ: {file_size} بايت")

        if file_size == 0:
            logger.error("الملف فارغ")
            raise HTTPException(
                status_code=500,
                detail="الملف المُنشأ فارغ"
            )

        # تحديد اسم الملف للتحميل (بدون أحرف عربية لتجنب مشاكل encoding)
        device_name = device_info.get('hostname', 'unknown_device')
        # تحويل الأحرف العربية إلى نص آمن
        import re
        import unicodedata

        # تحويل النص العربي إلى نص إنجليزي آمن
        if 'جهاز' in device_name:
            if 'Windows' in device_name:
                device_name = 'Windows_Device'
            elif 'Mac' in device_name:
                device_name = 'Mac_Device'
            elif 'Linux' in device_name:
                device_name = 'Linux_Device'
            elif 'Android' in device_name:
                device_name = 'Android_Device'
            elif 'iOS' in device_name:
                device_name = 'iOS_Device'
            else:
                device_name = 'Unknown_Device'
        elif 'متصفح' in device_name:
            if 'Chrome' in device_name:
                device_name = 'Chrome_Browser'
            elif 'Firefox' in device_name:
                device_name = 'Firefox_Browser'
            elif 'Safari' in device_name:
                device_name = 'Safari_Browser'
            else:
                device_name = 'Web_Browser'
        elif device_name == 'غير معروف':
            device_name = 'unknown_device'
        else:
            # إزالة الأحرف غير ASCII وتحويل إلى ASCII آمن
            try:
                # تحويل Unicode إلى ASCII
                device_name = unicodedata.normalize('NFKD', device_name)
                device_name = device_name.encode('ascii', 'ignore').decode('ascii')
                # إزالة الأحرف غير الآمنة
                device_name = re.sub(r'[^\w\-_.]', '_', device_name)
                if not device_name or device_name == '_':
                    device_name = 'unknown_device'
            except:
                device_name = 'unknown_device'

        device_name = device_name.replace(' ', '_').replace('/', '_')
        timestamp = get_tripoli_now().strftime('%Y%m%d_%H%M%S')

        # تحديد نوع الملف بناءً على الامتداد
        file_extension = os.path.splitext(output_path)[1]
        if file_extension == '.json':
            filename = f"device_access_log_{device_name}_{timestamp}.json"
            media_type = 'application/json'
        else:
            filename = f"device_access_log_{device_name}_{timestamp}.pdf"
            media_type = 'application/pdf'

        logger.info(f"تم إنشاء الملف بنجاح: {output_path} ({file_size} بايت)")

        # إرجاع الملف للتحميل
        from urllib.parse import quote

        # تشفير اسم الملف بشكل آمن للـ headers
        safe_filename = quote(filename.encode('utf-8'))

        return FileResponse(
            path=output_path,
            filename=filename,
            media_type=media_type,
            headers={
                "Content-Disposition": f"attachment; filename=\"{filename}\"; filename*=UTF-8''{safe_filename}",
                "Cache-Control": "no-cache",
                "Content-Length": str(file_size)
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في تصدير سجل وصول الجهاز: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"خطأ في تصدير PDF: {str(e)}"
        )


@router.get("/test-pdf-engines")
async def test_pdf_engines(
    current_user: User = Depends(get_current_user)
):
    """
    اختبار محركات PDF المتاحة
    """
    try:
        pdf_service = AdvancedPDFService()
        
        # بيانات تجريبية
        test_device_info = {
            'device_id': 'test_device',
            'hostname': 'جهاز تجريبي',
            'client_ip': '*************',
            'device_type': 'desktop',
            'last_access': get_tripoli_now()
        }
        
        test_history = [
            {
                'id': 1,
                'event_type': 'fingerprint_created',
                'ip_address': '*************',
                'user_agent': 'Mozilla/5.0 Test Browser',
                'created_at': get_tripoli_now()
            },
            {
                'id': 2,
                'event_type': 'device_access',
                'ip_address': '*************',
                'user_agent': 'Mozilla/5.0 Test Browser',
                'created_at': get_tripoli_now()
            }
        ]
        
        # اختبار التصدير
        output_path = pdf_service.export_device_access_log(
            device_info=test_device_info,
            access_history=test_history
        )
        
        return {
            "status": "success",
            "engine": pdf_service.engine,
            "output_path": output_path,
            "file_exists": os.path.exists(output_path),
            "file_size": os.path.getsize(output_path) if os.path.exists(output_path) else 0,
            "message": f"تم اختبار محرك PDF: {pdf_service.engine}"
        }
        
    except Exception as e:
        logger.error(f"خطأ في اختبار محركات PDF: {e}")
        return {
            "status": "error",
            "error": str(e),
            "message": "فشل في اختبار محركات PDF"
        }


@router.get("/available-engines")
async def get_available_engines():
    """
    الحصول على محركات PDF المتاحة
    """
    try:
        from services.advanced_pdf_service import WEASYPRINT_AVAILABLE, REPORTLAB_AVAILABLE
        
        engines = {
            "weasyprint": {
                "available": WEASYPRINT_AVAILABLE,
                "description": "محرك WeasyPrint - الأفضل للنصوص العربية",
                "priority": 1
            },
            "reportlab": {
                "available": REPORTLAB_AVAILABLE,
                "description": "محرك ReportLab - بديل موثوق",
                "priority": 2
            },
            "fallback": {
                "available": True,
                "description": "طريقة احتياطية - JSON export",
                "priority": 3
            }
        }
        
        # تحديد المحرك المفضل
        preferred_engine = "fallback"
        if WEASYPRINT_AVAILABLE:
            preferred_engine = "weasyprint"
        elif REPORTLAB_AVAILABLE:
            preferred_engine = "reportlab"
        
        return {
            "engines": engines,
            "preferred_engine": preferred_engine,
            "total_available": sum(1 for engine in engines.values() if engine["available"])
        }
        
    except Exception as e:
        logger.error(f"خطأ في جلب محركات PDF: {e}")
        return {
            "error": str(e),
            "engines": {},
            "preferred_engine": "fallback"
        }


@router.post("/custom-report")
async def export_custom_report(
    title: str,
    data: List[Dict[str, Any]],
    additional_info: Optional[Dict[str, Any]] = None,
    config: Optional[Dict[str, Any]] = None,
    current_user: User = Depends(get_current_user)
):
    """
    تصدير تقرير مخصص إلى PDF
    """
    try:
        logger.info(f"بدء تصدير تقرير مخصص: {title}")
        
        # إنشاء خدمة PDF
        pdf_service = AdvancedPDFService(config or {})
        
        # إنشاء ملف مؤقت
        timestamp = get_tripoli_now().strftime('%Y%m%d_%H%M%S')
        filename = f"custom_report_{timestamp}.pdf"
        output_path = os.path.join(tempfile.gettempdir(), filename)
        
        # تصدير التقرير (استخدام نفس منطق device access log)
        if pdf_service.engine == 'weasyprint':
            output_path = pdf_service._export_with_weasyprint(title, data, additional_info or {}, output_path)
        elif pdf_service.engine == 'reportlab':
            output_path = pdf_service._export_with_reportlab(title, data, additional_info or {}, output_path)
        else:
            output_path = pdf_service._export_fallback(title, data, additional_info or {}, output_path)
        
        if not os.path.exists(output_path):
            raise HTTPException(
                status_code=500,
                detail="فشل في إنشاء التقرير المخصص"
            )
        
        logger.info(f"تم إنشاء التقرير المخصص: {output_path}")
        
        return FileResponse(
            path=output_path,
            filename=filename,
            media_type='application/pdf' if output_path.endswith('.pdf') else 'application/json',
            headers={
                "Content-Disposition": f"attachment; filename*=UTF-8''{filename}",
                "Cache-Control": "no-cache"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في تصدير التقرير المخصص: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"خطأ في تصدير التقرير: {str(e)}"
        )
