# pyright: reportInvalidTypeForm=false
# pyright: reportOptionalOperand=false
# pyright: reportUnusedVariable=false
# pyright: reportGeneralTypeIssues=false
# Note: SQLAlchemy expressions may trigger type checker warnings for conditional operations
# These warnings are expected and safe in SQLAlchemy context
from fastapi import APIRouter, Depends, HTTPException, status, Query, Response
from sqlalchemy.orm import Session
from sqlalchemy import select, update, and_, func
from typing import List, Optional
from pydantic import BaseModel

from database.session import get_db
from models.customer import Customer, CustomerDebt, DebtPayment
from schemas.customer import (
    DebtCreate, DebtUpdate, DebtResponse,
    PaymentCreate, PaymentUpdate, PaymentResponse
)
from utils.auth import get_current_active_user
# from utils.datetime_utils import get_tripoli_now  # Not used currently
from services.debt_calculation_service import debt_calculation_service

# Schema for multiple debt payment
class MultipleDebtPayment(BaseModel):
    customer_id: int
    total_amount: float
    payment_method: str = 'cash'
    notes: Optional[str] = None

router = APIRouter(prefix="/api/debts", tags=["debts"])

@router.post("/", response_model=DebtResponse, status_code=status.HTTP_201_CREATED)
async def create_debt(
    debt: DebtCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Create a new debt for a customer."""

    # Verify customer exists
    customer = db.execute(
        select(Customer).where(Customer.id == debt.customer_id)
    ).scalar_one_or_none()

    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )

    # Create new debt
    new_debt = CustomerDebt(
        customer_id=debt.customer_id,
        sale_id=debt.sale_id,
        amount=debt.amount,
        remaining_amount=debt.amount,  # Initially, remaining amount equals total amount
        description=debt.description
    )

    db.add(new_debt)
    db.commit()
    db.refresh(new_debt)

    # Get payments for this debt (should be empty for new debt)
    payments = []

    # Get sale information if debt is linked to a sale
    sale_info = None
    if new_debt.sale_id is not None:
        from models.sale import Sale
        sale = db.execute(
            select(Sale).where(Sale.id == new_debt.sale_id)
        ).scalar_one_or_none()
        if sale:
            sale_info = {
                'id': sale.id,
                'total_amount': sale.total_amount,
                'tax_amount': sale.tax_amount,
                'discount_amount': sale.discount_amount,
                'amount_paid': sale.amount_paid,
                'payment_status': sale.payment_status
            }

    # Create clean dict for response
    debt_dict = {
        'id': new_debt.id,
        'customer_id': new_debt.customer_id,
        'sale_id': new_debt.sale_id,
        'amount': new_debt.amount,
        'remaining_amount': new_debt.remaining_amount,
        'description': new_debt.description,
        'is_paid': new_debt.is_paid,
        'created_at': new_debt.created_at,
        'updated_at': new_debt.updated_at,
        'customer': {
            'id': customer.id,
            'name': customer.name,
            'phone': customer.phone,
            'email': customer.email,
            'image_url': customer.image_url
        },
        'sale': sale_info,
        'payments': payments
    }

    return DebtResponse.model_validate(debt_dict)

@router.get("/filter-stats")
async def get_filter_stats(
    customer_id: Optional[int] = Query(None),
    payment_status: Optional[str] = Query(None),
    unpaid_only: bool = Query(False),
    paid_only: bool = Query(False),
    invoice_only: bool = Query(False),
    standalone_only: bool = Query(False),
    min_amount: Optional[float] = Query(None, ge=0),
    max_amount: Optional[float] = Query(None, ge=0),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get statistics for filtered debts."""

    # Build query with same filters as get_debts
    query = select(CustomerDebt)

    # Apply all filters
    if customer_id:
        query = query.where(CustomerDebt.customer_id == customer_id)

    # Payment status filters
    if payment_status:
        query = query.where(CustomerDebt.payment_status == payment_status)
    elif unpaid_only:
        query = query.where(CustomerDebt.is_paid == False)
    elif paid_only:
        query = query.where(CustomerDebt.is_paid == True)

    # Debt type filters
    if invoice_only:
        query = query.where(CustomerDebt.sale_id.isnot(None))
    elif standalone_only:
        query = query.where(CustomerDebt.sale_id.is_(None))

    # Amount range filters
    if min_amount is not None:
        query = query.where(CustomerDebt.remaining_amount >= min_amount)
    if max_amount is not None:
        query = query.where(CustomerDebt.remaining_amount <= max_amount)

    # Date range filters
    if start_date:
        try:
            from datetime import datetime
            start_datetime = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            query = query.where(CustomerDebt.created_at >= start_datetime)
        except ValueError:
            pass

    if end_date:
        try:
            from datetime import datetime, timedelta
            end_datetime = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            end_datetime = end_datetime + timedelta(days=1)
            query = query.where(CustomerDebt.created_at < end_datetime)
        except ValueError:
            pass

    # Search functionality
    if search:
        query = query.join(Customer, CustomerDebt.customer_id == Customer.id)
        search_term = f"%{search}%"
        search_condition = (Customer.name.ilike(search_term)) | (CustomerDebt.description.ilike(search_term))
        query = query.where(search_condition)

    # Get filtered debts
    filtered_debts = db.execute(query).scalars().all()

    # Calculate statistics
    total_filtered = len(filtered_debts)
    unpaid_filtered = len([d for d in filtered_debts if d.payment_status == 'unpaid'])
    partial_filtered = len([d for d in filtered_debts if d.payment_status == 'partial'])
    paid_filtered = len([d for d in filtered_debts if d.payment_status == 'paid'])

    total_amount_filtered = sum(d.amount for d in filtered_debts)
    unpaid_amount_filtered = sum(d.remaining_amount for d in filtered_debts)
    paid_amount_filtered = sum(d.amount - d.remaining_amount for d in filtered_debts)

    return {
        "totalFiltered": total_filtered,
        "unpaidFiltered": unpaid_filtered,
        "partialFiltered": partial_filtered,
        "paidFiltered": paid_filtered,
        "totalAmountFiltered": total_amount_filtered,
        "unpaidAmountFiltered": unpaid_amount_filtered,
        "paidAmountFiltered": paid_amount_filtered
    }

@router.get("/", response_model=List[DebtResponse])
async def get_debts(
    response: Response,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    customer_id: Optional[int] = Query(None),
    payment_status: Optional[str] = Query(None),  # 'unpaid', 'partial', 'paid'
    unpaid_only: bool = Query(False),  # Keep for backward compatibility
    paid_only: bool = Query(False),    # Keep for backward compatibility
    invoice_only: bool = Query(False),
    standalone_only: bool = Query(False),
    min_amount: Optional[float] = Query(None, ge=0),
    max_amount: Optional[float] = Query(None, ge=0),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get all debts with optional filtering."""

    # Build base query for counting
    count_query = select(func.count(CustomerDebt.id))

    # Build main query for data
    query = select(CustomerDebt)

    # Apply filters to both queries
    if customer_id:
        query = query.where(CustomerDebt.customer_id == customer_id)
        count_query = count_query.where(CustomerDebt.customer_id == customer_id)

    # Payment status filters
    if payment_status:
        # Use new payment_status field
        query = query.where(CustomerDebt.payment_status == payment_status)
        count_query = count_query.where(CustomerDebt.payment_status == payment_status)
    elif unpaid_only:
        # Backward compatibility
        query = query.where(CustomerDebt.is_paid == False)
        count_query = count_query.where(CustomerDebt.is_paid == False)
    elif paid_only:
        # Backward compatibility
        query = query.where(CustomerDebt.is_paid == True)
        count_query = count_query.where(CustomerDebt.is_paid == True)

    # Debt type filters (mutually exclusive)
    if invoice_only:
        query = query.where(CustomerDebt.sale_id.isnot(None))
        count_query = count_query.where(CustomerDebt.sale_id.isnot(None))
    elif standalone_only:
        query = query.where(CustomerDebt.sale_id.is_(None))
        count_query = count_query.where(CustomerDebt.sale_id.is_(None))

    # Amount range filters
    if min_amount is not None:
        query = query.where(CustomerDebt.remaining_amount >= min_amount)
        count_query = count_query.where(CustomerDebt.remaining_amount >= min_amount)
    if max_amount is not None:
        query = query.where(CustomerDebt.remaining_amount <= max_amount)
        count_query = count_query.where(CustomerDebt.remaining_amount <= max_amount)

    # Date range filters
    if start_date:
        try:
            from datetime import datetime
            start_datetime = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            query = query.where(CustomerDebt.created_at >= start_datetime)
            count_query = count_query.where(CustomerDebt.created_at >= start_datetime)
        except ValueError:
            pass  # Invalid date format, ignore filter

    if end_date:
        try:
            from datetime import datetime, timedelta
            end_datetime = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            # Add one day to include the entire end date
            end_datetime = end_datetime + timedelta(days=1)
            query = query.where(CustomerDebt.created_at < end_datetime)
            count_query = count_query.where(CustomerDebt.created_at < end_datetime)
        except ValueError:
            pass  # Invalid date format, ignore filter

    # Add search functionality
    if search:
        # Join with Customer table to search by customer name
        query = query.join(Customer, CustomerDebt.customer_id == Customer.id)
        count_query = count_query.join(Customer, CustomerDebt.customer_id == Customer.id)
        search_term = f"%{search}%"
        search_condition = (Customer.name.ilike(search_term)) | (CustomerDebt.description.ilike(search_term))
        query = query.where(search_condition)
        count_query = count_query.where(search_condition)

    # Get total count before pagination
    total_count = db.execute(count_query).scalar() or 0

    # Add pagination to main query
    query = query.offset(skip).limit(limit)

    debts = db.execute(query).scalars().all()

    # Add customer information and sale information to each debt
    result = []
    for debt in debts:
        customer = db.execute(
            select(Customer).where(Customer.id == debt.customer_id)
        ).scalar_one_or_none()

        # Get payments for this debt
        payments = db.execute(
            select(DebtPayment).where(DebtPayment.debt_id == debt.id)
        ).scalars().all()

        # Get sale information if debt is linked to a sale
        sale_info = None
        if debt.sale_id is not None:
            from models.sale import Sale
            sale = db.execute(
                select(Sale).where(Sale.id == debt.sale_id)
            ).scalar_one_or_none()
            if sale:
                sale_info = {
                    'id': sale.id,
                    'total_amount': sale.total_amount,
                    'tax_amount': sale.tax_amount,
                    'discount_amount': sale.discount_amount,
                    'amount_paid': sale.amount_paid,
                    'payment_status': sale.payment_status
                }

        # Create clean dict without SQLAlchemy internal state
        debt_dict = {
            'id': debt.id,
            'customer_id': debt.customer_id,
            'sale_id': debt.sale_id,
            'amount': debt.amount,
            'remaining_amount': debt.remaining_amount,
            'description': debt.description,
            'is_paid': debt.is_paid,
            'payment_status': debt.payment_status,
            'created_at': debt.created_at,
            'updated_at': debt.updated_at,
            'customer': {
                'id': customer.id,
                'name': customer.name,
                'phone': customer.phone,
                'email': customer.email,
                'image_url': customer.image_url
            } if customer else None,
            'sale': sale_info,
            'payments': [{
                'id': payment.id,
                'debt_id': payment.debt_id,
                'amount': payment.amount,
                'payment_method': payment.payment_method,
                'notes': payment.notes,
                'created_at': payment.created_at
            } for payment in payments]
        }
        result.append(DebtResponse.model_validate(debt_dict))

    # Add total count header for pagination
    response.headers["x-total-count"] = str(total_count)

    return result

@router.get("/stats")
async def get_debt_stats(
    customer_id: Optional[int] = Query(None),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get debt statistics, optionally filtered by customer."""

    # Build base conditions for filtering by customer if specified
    if customer_id:
        customer_filter = CustomerDebt.customer_id == customer_id
    else:
        customer_filter = None

    # Total debts count
    if customer_filter is not None:
        total_debts = db.execute(
            select(func.count(CustomerDebt.id))
            .where(customer_filter)
        ).scalar() or 0
    else:
        total_debts = db.execute(
            select(func.count(CustomerDebt.id))
        ).scalar() or 0

    # Unpaid debts count
    if customer_filter is not None:
        unpaid_debts = db.execute(
            select(func.count(CustomerDebt.id))
            .where(and_(CustomerDebt.is_paid == False, customer_filter))
        ).scalar() or 0
    else:
        unpaid_debts = db.execute(
            select(func.count(CustomerDebt.id))
            .where(CustomerDebt.is_paid == False)
        ).scalar() or 0

    # Total amount
    if customer_filter is not None:
        total_amount = db.execute(
            select(func.sum(CustomerDebt.amount))
            .where(customer_filter)
        ).scalar() or 0.0
    else:
        total_amount = db.execute(
            select(func.sum(CustomerDebt.amount))
        ).scalar() or 0.0

    # Unpaid amount
    if customer_filter is not None:
        unpaid_amount = db.execute(
            select(func.sum(CustomerDebt.remaining_amount))
            .where(and_(CustomerDebt.is_paid == False, customer_filter))
        ).scalar() or 0.0
    else:
        unpaid_amount = db.execute(
            select(func.sum(CustomerDebt.remaining_amount))
            .where(CustomerDebt.is_paid == False)
        ).scalar() or 0.0

    # Total paid amount - calculate from sales linked to debts
    from models.sale import Sale
    if customer_filter is not None:
        total_paid_amount = db.execute(
            select(func.coalesce(func.sum(Sale.amount_paid), 0))
            .join(CustomerDebt, CustomerDebt.sale_id == Sale.id)
            .where(and_(CustomerDebt.sale_id.isnot(None), customer_filter))
        ).scalar() or 0.0
    else:
        total_paid_amount = db.execute(
            select(func.coalesce(func.sum(Sale.amount_paid), 0))
            .join(CustomerDebt, CustomerDebt.sale_id == Sale.id)
            .where(CustomerDebt.sale_id.isnot(None))
        ).scalar() or 0.0

    # Total invoice amount - calculate total invoice amounts from sales linked to debts
    if customer_filter is not None:
        total_invoice_amount = db.execute(
            select(func.coalesce(func.sum(Sale.total_amount + Sale.tax_amount - Sale.discount_amount), 0))
            .join(CustomerDebt, CustomerDebt.sale_id == Sale.id)
            .where(and_(CustomerDebt.sale_id.isnot(None), customer_filter))
        ).scalar() or 0.0
    else:
        total_invoice_amount = db.execute(
            select(func.coalesce(func.sum(Sale.total_amount + Sale.tax_amount - Sale.discount_amount), 0))
            .join(CustomerDebt, CustomerDebt.sale_id == Sale.id)
            .where(CustomerDebt.sale_id.isnot(None))
        ).scalar() or 0.0

    return {
        "totalDebts": total_debts,
        "unpaidDebts": unpaid_debts,
        "totalAmount": float(total_amount),
        "totalInvoiceAmount": float(total_invoice_amount),
        "totalPaidAmount": float(total_paid_amount),
        "unpaidAmount": float(unpaid_amount)
    }

@router.get("/{debt_id}", response_model=DebtResponse)
async def get_debt(
    debt_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get a specific debt by ID."""

    debt = db.execute(
        select(CustomerDebt).where(CustomerDebt.id == debt_id)
    ).scalar_one_or_none()

    if not debt:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Debt not found"
        )

    # Get customer information
    customer = db.execute(
        select(Customer).where(Customer.id == debt.customer_id)
    ).scalar_one_or_none()

    # Get payments for this debt
    payments = db.execute(
        select(DebtPayment).where(DebtPayment.debt_id == debt.id)
    ).scalars().all()

    # Get sale information if debt is linked to a sale
    sale_info = None
    if debt.sale_id is not None:
        from models.sale import Sale
        sale = db.execute(
            select(Sale).where(Sale.id == debt.sale_id)
        ).scalar_one_or_none()
        if sale:
            sale_info = {
                'id': sale.id,
                'total_amount': sale.total_amount,
                'tax_amount': sale.tax_amount,
                'discount_amount': sale.discount_amount,
                'amount_paid': sale.amount_paid,
                'payment_status': sale.payment_status
            }

    # Create clean dict without SQLAlchemy internal state
    debt_dict = {
        'id': debt.id,
        'customer_id': debt.customer_id,
        'sale_id': debt.sale_id,
        'amount': debt.amount,
        'remaining_amount': debt.remaining_amount,
        'description': debt.description,
        'is_paid': debt.is_paid,
        'payment_status': debt.payment_status,
        'created_at': debt.created_at,
        'updated_at': debt.updated_at,
        'customer': {
            'id': customer.id,
            'name': customer.name,
            'phone': customer.phone,
            'email': customer.email,
            'image_url': customer.image_url
        } if customer else None,
        'sale': sale_info,
        'payments': [{
            'id': payment.id,
            'debt_id': payment.debt_id,
            'amount': payment.amount,
            'payment_method': payment.payment_method,
            'notes': payment.notes,
            'created_at': payment.created_at
        } for payment in payments]
    }

    return DebtResponse.model_validate(debt_dict)

@router.put("/{debt_id}", response_model=DebtResponse)
async def update_debt(
    debt_id: int,
    debt_update: DebtUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Update a debt."""

    debt = db.execute(
        select(CustomerDebt).where(CustomerDebt.id == debt_id)
    ).scalar_one_or_none()

    if not debt:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Debt not found"
        )

    # Update debt fields
    update_data = debt_update.model_dump(exclude_unset=True)

    # If amount is being updated, we'll handle it separately
    if 'amount' in update_data:
        new_amount = update_data['amount']

        # Get all payments for this debt to calculate total paid
        payments = db.execute(
            select(DebtPayment).where(DebtPayment.debt_id == debt_id)
        ).scalars().all()
        total_paid = sum(p.amount for p in payments)

        # Calculate new remaining amount based on new total and existing payments
        new_remaining = new_amount - total_paid
        is_paid_value = new_remaining <= 0
        final_remaining = 0.0 if is_paid_value else new_remaining

        # Update debt with correct remaining amount
        stmt = update(CustomerDebt).where(CustomerDebt.id == debt_id).values(
            amount=new_amount,
            remaining_amount=final_remaining,
            is_paid=is_paid_value
        )
        db.execute(stmt)

        # If debt is linked to a sale, update the sale accordingly
        if debt.sale_id is not None:
            from models.sale import Sale

            sale = db.execute(
                select(Sale).where(Sale.id == debt.sale_id)
            ).scalar_one_or_none()

            if sale:
                # When debt amount changes, we need to update the sale total as well
                # Calculate the difference between old debt and new debt
                old_debt_amount = debt.amount
                debt_difference = new_amount - old_debt_amount

                # Update sale total amount to reflect the debt change
                new_sale_total = sale.total_amount + debt_difference

                # Calculate new total invoice amount
                total_invoice_amount = new_sale_total + sale.tax_amount - sale.discount_amount

                # The amount paid in the sale should be the total of all payments made
                new_amount_paid = total_paid

                # Update payment status and method based on amount paid
                # type: ignore - SQLAlchemy expression comparison
                if new_amount_paid >= total_invoice_amount:
                    new_payment_status = 'paid'
                    # Determine payment method based on existing payments
                    if payments:
                        payment_methods = [p.payment_method for p in payments]
                        unique_methods = list(set(payment_methods))
                        if len(unique_methods) == 1:
                            method = unique_methods[0]
                            if str(method) == 'cash':
                                new_payment_method = 'نقدي'
                            elif str(method) == 'card':
                                new_payment_method = 'بطاقة'
                            else:
                                new_payment_method = str(method)
                        else:
                            new_payment_method = 'مختلط'
                    else:
                        new_payment_method = 'نقدي'  # Default if no payments

                elif new_amount_paid > 0:
                    new_payment_status = 'partial'
                    new_payment_method = 'جزئي'
                else:
                    new_payment_status = 'unpaid'
                    new_payment_method = 'آجل'

                # Update sale with new total and payment info
                sale_stmt = update(Sale).where(Sale.id == debt.sale_id).values(
                    total_amount=new_sale_total,
                    amount_paid=new_amount_paid,
                    payment_status=new_payment_status,
                    payment_method=new_payment_method
                )
                db.execute(sale_stmt)

        update_data.pop('amount')  # Remove from update_data as we handled it manually

    for field, value in update_data.items():
        setattr(debt, field, value)

    db.commit()
    db.refresh(debt)

    # Get customer information
    customer = db.execute(
        select(Customer).where(Customer.id == debt.customer_id)
    ).scalar_one_or_none()

    # Get payments for this debt
    payments = db.execute(
        select(DebtPayment).where(DebtPayment.debt_id == debt_id)
    ).scalars().all()

    # Get sale information if debt is linked to a sale
    sale_info = None
    if debt.sale_id is not None:
        from models.sale import Sale
        sale = db.execute(
            select(Sale).where(Sale.id == debt.sale_id)
        ).scalar_one_or_none()
        if sale:
            sale_info = {
                'id': sale.id,
                'total_amount': sale.total_amount,
                'tax_amount': sale.tax_amount,
                'discount_amount': sale.discount_amount,
                'amount_paid': sale.amount_paid,
                'payment_status': sale.payment_status
            }

    # Create clean dict for response
    debt_dict = {
        'id': debt.id,
        'customer_id': debt.customer_id,
        'sale_id': debt.sale_id,
        'amount': debt.amount,
        'remaining_amount': debt.remaining_amount,
        'description': debt.description,
        'is_paid': debt.is_paid,
        'created_at': debt.created_at,
        'updated_at': debt.updated_at,
        'customer': {
            'id': customer.id,
            'name': customer.name,
            'phone': customer.phone,
            'email': customer.email,
            'image_url': customer.image_url
        } if customer else None,
        'sale': sale_info,
        'payments': [{
            'id': payment.id,
            'debt_id': payment.debt_id,
            'amount': payment.amount,
            'payment_method': payment.payment_method,
            'notes': payment.notes,
            'created_at': payment.created_at
        } for payment in payments]
    }

    return DebtResponse.model_validate(debt_dict)

@router.post("/{debt_id}/payments", response_model=PaymentResponse, status_code=status.HTTP_201_CREATED)
async def create_payment(
    debt_id: int,
    payment: PaymentCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Create a payment for a debt with accurate calculation."""

    # Verify debt exists
    debt = db.execute(
        select(CustomerDebt).where(CustomerDebt.id == debt_id)
    ).scalar_one_or_none()

    if not debt:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Debt not found"
        )

    # Get current debt calculation
    current_calculation = debt_calculation_service.calculate_debt_amounts(db, debt_id)

    # Validate payment amount
    if payment.amount <= 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Payment amount must be greater than zero"
        )

    if payment.amount > current_calculation.remaining_amount:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Payment amount ({payment.amount}) cannot exceed remaining debt amount ({current_calculation.remaining_amount})"
        )

    # Determine invoice_id if debt is linked to a sale
    invoice_id = debt.sale_id if debt.sale_id else payment.invoice_id

    # Create new payment
    new_payment = DebtPayment(
        debt_id=debt_id,
        invoice_id=invoice_id,
        amount=payment.amount,
        payment_method=payment.payment_method,
        notes=payment.notes
    )

    db.add(new_payment)
    db.flush()  # Get the payment ID

    # Update debt status using the calculation service
    debt_calculation_service.update_debt_status(db, debt_id)

    # If debt is linked to a sale, update the sale's amount_paid
    if debt.sale_id is not None:
        from models.sale import Sale

        # Get current sale
        sale = db.execute(
            select(Sale).where(Sale.id == debt.sale_id)
        ).scalar_one_or_none()

        if sale:
            # Get updated calculation after payment
            updated_calculation = debt_calculation_service.calculate_debt_amounts(db, debt_id)

            # Update sale's amount_paid based on actual payments
            new_amount_paid = sale.amount_paid + payment.amount

            # Update payment status based on debt status
            if updated_calculation.remaining_amount <= 0:
                new_payment_status = 'paid'
                # Determine payment method based on all payments
                all_payments = db.execute(
                    select(DebtPayment).where(DebtPayment.debt_id == debt_id)
                ).scalars().all()

                payment_methods = [p.payment_method for p in all_payments]
                unique_methods = list(set(payment_methods))

                if len(unique_methods) == 1:
                    method = unique_methods[0]
                    new_payment_method = 'نقدي' if method == 'cash' else 'بطاقة' if method == 'card' else str(method)
                else:
                    new_payment_method = 'مختلط'
            elif updated_calculation.paid_amount > 0:
                new_payment_status = 'partial'
                new_payment_method = 'جزئي'
            else:
                new_payment_status = 'pending'
                new_payment_method = 'آجل'

            # Update sale
            sale_stmt = update(Sale).where(Sale.id == debt.sale_id).values(
                amount_paid=new_amount_paid,
                payment_status=new_payment_status,
                payment_method=new_payment_method
            )
            db.execute(sale_stmt)

    # Commit all changes
    db.commit()
    db.refresh(new_payment)

    # Get updated debt information
    updated_debt = db.execute(
        select(CustomerDebt).where(CustomerDebt.id == debt_id)
    ).scalar_one()

    # Create clean dict for response
    payment_dict = {
        'id': new_payment.id,
        'debt_id': new_payment.debt_id,
        'amount': new_payment.amount,
        'payment_method': new_payment.payment_method,
        'notes': new_payment.notes,
        'created_at': new_payment.created_at,
        'debt': {
            'id': updated_debt.id,
            'customer_id': updated_debt.customer_id,
            'sale_id': updated_debt.sale_id,
            'amount': updated_debt.amount,
            'remaining_amount': updated_debt.remaining_amount,
            'is_paid': updated_debt.is_paid
        }
    }

    return PaymentResponse.model_validate(payment_dict)

@router.delete("/{debt_id}/payments/{payment_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_payment(
    debt_id: int,
    payment_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Delete a payment and update debt and sale accordingly."""

    # Verify debt exists
    debt = db.execute(
        select(CustomerDebt).where(CustomerDebt.id == debt_id)
    ).scalar_one_or_none()

    if not debt:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Debt not found"
        )

    # Verify payment exists and belongs to this debt
    payment = db.execute(
        select(DebtPayment).where(
            and_(DebtPayment.id == payment_id, DebtPayment.debt_id == debt_id)
        )
    ).scalar_one_or_none()

    if not payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payment not found"
        )

    # Store payment amount for sale updates
    payment_amount = payment.amount

    # If debt is linked to a sale, update the sale's amount_paid and payment method
    if debt.sale_id is not None:
        from models.sale import Sale

        # Get current sale
        sale = db.execute(
            select(Sale).where(Sale.id == debt.sale_id)
        ).scalar_one_or_none()

        if sale:
            # Update sale's amount_paid by subtracting the payment amount
            new_amount_paid = sale.amount_paid - payment.amount

            # Get remaining payments for this debt (excluding the one being deleted)
            remaining_payments = db.execute(
                select(DebtPayment).where(
                    and_(DebtPayment.debt_id == debt_id, DebtPayment.id != payment_id)
                )
            ).scalars().all()

            # Determine payment method and status based on remaining payments
            if remaining_payments:
                payment_methods = [p.payment_method for p in remaining_payments]
                unique_methods = list(set(payment_methods))
                if len(unique_methods) == 1:
                    method = unique_methods[0]
                    new_payment_method = 'نقدي' if method == 'cash' else 'بطاقة' if method == 'card' else str(method)
                else:
                    new_payment_method = 'مختلط'
            else:
                new_payment_method = 'آجل'

            # Update sale with new payment amount and method
            sale_stmt = update(Sale).where(Sale.id == debt.sale_id).values(
                amount_paid=new_amount_paid,
                payment_method=new_payment_method
            )
            db.execute(sale_stmt)

    # Delete the payment
    db.delete(payment)
    db.flush()  # Ensure payment is deleted before recalculating

    # Update debt status using the calculation service
    debt_calculation_service.update_debt_status(db, debt_id)

    # Update sale payment status if linked
    if debt.sale_id is not None:
        from models.sale import Sale

        # Get updated debt calculation
        updated_calculation = debt_calculation_service.calculate_debt_amounts(db, debt_id)

        # Update sale payment status based on debt status
        if updated_calculation.remaining_amount <= 0:
            new_payment_status = 'paid'
        elif updated_calculation.paid_amount > 0:
            new_payment_status = 'partial'
        else:
            new_payment_status = 'pending'

        # Update sale payment status
        sale_status_stmt = update(Sale).where(Sale.id == debt.sale_id).values(
            payment_status=new_payment_status
        )
        db.execute(sale_status_stmt)

    db.commit()
    return None


@router.put("/{debt_id}/payments/{payment_id}", response_model=PaymentResponse)
async def update_payment(
    debt_id: int,
    payment_id: int,
    payment_update: PaymentUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Update a payment and recalculate debt and sale accordingly."""

    # Verify debt exists
    debt = db.execute(
        select(CustomerDebt).where(CustomerDebt.id == debt_id)
    ).scalar_one_or_none()

    if not debt:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Debt not found"
        )

    # Verify payment exists and belongs to this debt
    payment = db.execute(
        select(DebtPayment).where(
            and_(DebtPayment.id == payment_id, DebtPayment.debt_id == debt_id)
        )
    ).scalar_one_or_none()

    if not payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payment not found"
        )

    # Get update data
    update_data = payment_update.model_dump(exclude_unset=True)

    if not update_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No fields to update"
        )

    # Store old payment amount for calculations
    old_payment_amount = payment.amount

    # Validate new payment amount if being updated
    if 'amount' in update_data:
        new_amount = update_data['amount']
        if new_amount <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Payment amount must be greater than zero"
            )

        # Get current calculation
        current_calculation = debt_calculation_service.calculate_debt_amounts(db, debt_id)

        # Calculate what the remaining would be if we remove old payment and add new one
        old_amount = payment.amount
        remaining_without_old_payment = current_calculation.remaining_amount + old_amount

        if new_amount > remaining_without_old_payment:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Payment amount ({new_amount}) cannot exceed available amount ({remaining_without_old_payment})"
            )

    # Update payment fields
    for field, value in update_data.items():
        setattr(payment, field, value)

    # Calculate payment difference for sale updates
    payment_difference = payment.amount - old_payment_amount

    db.flush()  # Ensure payment is updated before recalculating

    # Update debt status using the calculation service
    debt_calculation_service.update_debt_status(db, debt_id)

    # If debt is linked to a sale, update the sale's amount_paid and payment method
    if debt.sale_id is not None:
        from models.sale import Sale

        # Get current sale
        sale = db.execute(
            select(Sale).where(Sale.id == debt.sale_id)
        ).scalar_one_or_none()

        if sale:
            # Update sale's amount_paid by adding the payment difference
            new_amount_paid = sale.amount_paid + payment_difference

            # Calculate total invoice amount
            total_invoice_amount = sale.total_amount + sale.tax_amount - sale.discount_amount

            # Update payment status and payment method based on amount paid
            if new_amount_paid >= total_invoice_amount:
                new_payment_status = 'paid'
                # For fully paid debts, determine payment method based on all payments
                all_payments = db.execute(
                    select(DebtPayment).where(DebtPayment.debt_id == debt_id)
                ).scalars().all()

                if all_payments:
                    payment_methods = [p.payment_method for p in all_payments]
                    unique_methods = list(set(payment_methods))
                    if len(unique_methods) == 1:
                        # Convert to Arabic if needed
                        method = unique_methods[0]
                        if str(method) == 'cash':
                            new_payment_method = 'نقدي'
                        elif str(method) == 'card':
                            new_payment_method = 'بطاقة'
                        else:
                            new_payment_method = str(method)
                    else:
                        new_payment_method = 'مختلط'
                else:
                    new_payment_method = 'نقدي'  # Default if no payments

            elif new_amount_paid > 0:
                new_payment_status = 'partial'
                new_payment_method = 'جزئي'
            else:
                new_payment_status = 'unpaid'
                new_payment_method = 'آجل'

            # Update sale with new payment status and method
            sale_stmt = update(Sale).where(Sale.id == debt.sale_id).values(
                amount_paid=new_amount_paid,
                payment_status=new_payment_status,
                payment_method=new_payment_method
            )
            db.execute(sale_stmt)

    db.commit()
    db.refresh(payment)

    # Get updated debt information
    updated_debt = db.execute(
        select(CustomerDebt).where(CustomerDebt.id == debt_id)
    ).scalar_one()

    # Create clean dict for response
    payment_dict = {
        'id': payment.id,
        'debt_id': payment.debt_id,
        'amount': payment.amount,
        'payment_method': payment.payment_method,
        'notes': payment.notes,
        'created_at': payment.created_at,
        'debt': {
            'id': updated_debt.id,
            'customer_id': updated_debt.customer_id,
            'sale_id': updated_debt.sale_id,
            'amount': updated_debt.amount,
            'remaining_amount': updated_debt.remaining_amount,
            'is_paid': updated_debt.is_paid
        }
    }

    return PaymentResponse.model_validate(payment_dict)


@router.delete("/{debt_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_debt(
    debt_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Delete a debt and update associated sale if needed."""

    # Verify debt exists
    debt = db.execute(
        select(CustomerDebt).where(CustomerDebt.id == debt_id)
    ).scalar_one_or_none()

    if not debt:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Debt not found"
        )

    # If debt is linked to a sale, update the sale to reflect debt removal
    if debt.sale_id is not None:
        from models.sale import Sale

        # Get current sale
        sale = db.execute(
            select(Sale).where(Sale.id == debt.sale_id)
        ).scalar_one_or_none()

        if sale:
            # Calculate total invoice amount
            total_invoice_amount = sale.total_amount + sale.tax_amount - sale.discount_amount

            # Get all payments for this debt to calculate total paid amount
            payments = db.execute(
                select(DebtPayment).where(DebtPayment.debt_id == debt_id)
            ).scalars().all()

            # Reset sale to fully paid status since debt is being removed
            # This assumes that removing debt means the sale is considered fully paid
            sale_stmt = update(Sale).where(Sale.id == debt.sale_id).values(
                amount_paid=total_invoice_amount,
                payment_status='paid',
                payment_method='نقدي'  # Default to cash when debt is removed
            )
            db.execute(sale_stmt)

    # Delete associated payments first
    payments = db.execute(
        select(DebtPayment).where(DebtPayment.debt_id == debt_id)
    ).scalars().all()

    for payment in payments:
        db.delete(payment)

    # Delete the debt
    db.delete(debt)
    db.commit()

    return None

@router.get("/customer/{customer_id}/summary")
async def get_customer_debt_summary(
    customer_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get debt summary for a specific customer."""

    # Verify customer exists
    customer = db.execute(
        select(Customer).where(Customer.id == customer_id)
    ).scalar_one_or_none()

    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )

    # Calculate debt statistics
    total_debt = db.execute(
        select(func.sum(CustomerDebt.amount))
        .where(CustomerDebt.customer_id == customer_id)
    ).scalar() or 0.0

    remaining_debt = db.execute(
        select(func.sum(CustomerDebt.remaining_amount))
        .where(and_(CustomerDebt.customer_id == customer_id, CustomerDebt.is_paid == False))
    ).scalar() or 0.0

    paid_debt = total_debt - remaining_debt

    debt_count = db.execute(
        select(func.count(CustomerDebt.id))
        .where(CustomerDebt.customer_id == customer_id)
    ).scalar() or 0

    unpaid_debt_count = db.execute(
        select(func.count(CustomerDebt.id))
        .where(and_(CustomerDebt.customer_id == customer_id, CustomerDebt.is_paid == False))
    ).scalar() or 0

    return {
        "customer_id": customer_id,
        "customer_name": customer.name,
        "total_debt": total_debt,
        "remaining_debt": remaining_debt,
        "paid_debt": paid_debt,
        "debt_count": debt_count,
        "unpaid_debt_count": unpaid_debt_count
    }

@router.post("/multiple-payment")
async def create_multiple_debt_payment(
    payment_data: MultipleDebtPayment,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Create payments for multiple debts of a customer with smart distribution."""

    # Verify customer exists
    customer = db.execute(
        select(Customer).where(Customer.id == payment_data.customer_id)
    ).scalar_one_or_none()

    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )

    # Get all unpaid debts for this customer, ordered by creation date (oldest first)
    unpaid_debts = db.execute(
        select(CustomerDebt)
        .where(and_(
            CustomerDebt.customer_id == payment_data.customer_id,
            CustomerDebt.is_paid == False,
            CustomerDebt.remaining_amount > 0
        ))
        .order_by(CustomerDebt.created_at.asc())
    ).scalars().all()

    if not unpaid_debts:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No unpaid debts found for this customer"
        )

    # Calculate total remaining debt
    total_remaining = sum(debt.remaining_amount for debt in unpaid_debts)

    if payment_data.total_amount <= 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Payment amount must be greater than zero"
        )

    if payment_data.total_amount > total_remaining:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Payment amount ({payment_data.total_amount}) exceeds total remaining debt ({total_remaining})"
        )

    # Smart distribution algorithm
    remaining_payment = payment_data.total_amount
    payments_created = []
    debts_updated = []

    for debt in unpaid_debts:
        if remaining_payment <= 0:
            break

        # Calculate payment amount for this debt
        debt_remaining = debt.remaining_amount
        if remaining_payment >= debt_remaining:
            # Pay the full remaining amount for this debt
            payment_amount = debt_remaining
        else:
            # Pay partial amount (this will be the last payment)
            payment_amount = remaining_payment

        # Create payment record
        new_payment = DebtPayment(
            debt_id=debt.id,
            amount=payment_amount,
            payment_method=payment_data.payment_method,
            notes=payment_data.notes
        )
        db.add(new_payment)

        # Update debt
        new_remaining = debt_remaining - payment_amount
        is_paid_value = new_remaining <= 0
        final_remaining = 0.0 if is_paid_value else new_remaining

        stmt = update(CustomerDebt).where(CustomerDebt.id == debt.id).values(
            remaining_amount=final_remaining,
            is_paid=is_paid_value
        )
        db.execute(stmt)

        # Update linked sale if exists
        if debt.sale_id is not None:
            from models.sale import Sale

            sale = db.execute(
                select(Sale).where(Sale.id == debt.sale_id)
            ).scalar_one_or_none()

            if sale:
                # Get all payments for this debt to calculate total paid
                all_payments = db.execute(
                    select(DebtPayment).where(DebtPayment.debt_id == debt.id)
                ).scalars().all()
                total_paid = sum(p.amount for p in all_payments) + payment_amount

                # Calculate total invoice amount
                total_invoice_amount = sale.total_amount + sale.tax_amount - sale.discount_amount

                # Determine payment status and method
                if total_paid >= total_invoice_amount:
                    new_payment_status = 'paid'
                    if payment_data.payment_method == 'cash':
                        new_payment_method = 'نقدي'
                    elif payment_data.payment_method == 'card':
                        new_payment_method = 'بطاقة'
                    else:
                        new_payment_method = payment_data.payment_method
                elif total_paid > 0:
                    new_payment_status = 'partial'
                    new_payment_method = 'جزئي'
                else:
                    new_payment_status = 'unpaid'
                    new_payment_method = 'آجل'

                # Update sale
                sale_stmt = update(Sale).where(Sale.id == debt.sale_id).values(
                    amount_paid=total_paid,
                    payment_status=new_payment_status,
                    payment_method=new_payment_method
                )
                db.execute(sale_stmt)

        # Track created payments and updated debts
        payments_created.append({
            'debt_id': debt.id,
            'amount': payment_amount,
            'remaining_debt': final_remaining,
            'is_paid': is_paid_value
        })

        debts_updated.append(debt.id)
        remaining_payment -= payment_amount

    # Commit all changes
    db.commit()

    return {
        "message": "Multiple debt payment processed successfully",
        "customer_id": payment_data.customer_id,
        "customer_name": customer.name,
        "total_amount_paid": payment_data.total_amount,
        "payment_method": payment_data.payment_method,
        "payments_created": len(payments_created),
        "debts_updated": len(debts_updated),
        "debts_fully_paid": len([p for p in payments_created if p['is_paid']]),
        "payment_details": payments_created
    }
