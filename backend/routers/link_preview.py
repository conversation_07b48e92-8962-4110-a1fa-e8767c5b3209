"""
API لمعاينة الروابط
يستخرج معلومات الروابط مثل العنوان والوصف والصورة
"""

from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import J<PERSON>NResponse
import re
from urllib.parse import urljoin, urlparse
from typing import Optional, Dict, Any
import asyncio
import logging

try:
    import httpx
    import requests
    HTTPX_AVAILABLE = True
except ImportError:
    import requests
    HTTPX_AVAILABLE = False

try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api", tags=["link-preview"])

# تعبير منتظم لاستخراج meta tags
META_PATTERNS = {
    'title': [
        r'<meta\s+property=["\']og:title["\']\s+content=["\']([^"\']*)["\']',
        r'<meta\s+name=["\']twitter:title["\']\s+content=["\']([^"\']*)["\']',
        r'<title[^>]*>([^<]*)</title>'
    ],
    'description': [
        r'<meta\s+property=["\']og:description["\']\s+content=["\']([^"\']*)["\']',
        r'<meta\s+name=["\']twitter:description["\']\s+content=["\']([^"\']*)["\']',
        r'<meta\s+name=["\']description["\']\s+content=["\']([^"\']*)["\']'
    ],
    'image': [
        r'<meta\s+property=["\']og:image["\']\s+content=["\']([^"\']*)["\']',
        r'<meta\s+name=["\']twitter:image["\']\s+content=["\']([^"\']*)["\']'
    ],
    'site_name': [
        r'<meta\s+property=["\']og:site_name["\']\s+content=["\']([^"\']*)["\']'
    ]
}

class LinkPreviewService:
    def __init__(self):
        self.timeout = 10.0
        self.max_content_length = 1024 * 1024  # 1MB
        
    async def get_preview(self, url: str) -> Dict[str, Any]:
        """استخراج معلومات الرابط"""
        try:
            # التحقق من صحة الرابط
            parsed_url = urlparse(url)
            if not parsed_url.scheme or not parsed_url.netloc:
                raise ValueError("رابط غير صحيح")

            # التحقق من الروابط المحلية
            is_local = self._is_local_url(parsed_url.netloc)
            if is_local:
                return await self._handle_local_url(url, parsed_url)

            # التحقق من روابط اليوتيوب
            if self._is_youtube_url(url):
                return await self._handle_youtube_url(url)

            # التحقق من روابط الفيسبوك
            if self._is_facebook_url(url):
                return await self._handle_facebook_url(url)

            # التحقق من روابط الصور المباشرة
            if self._is_direct_image_url(url):
                return self._handle_direct_image_url(url)
            
            # إعداد headers
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
            
            async with httpx.AsyncClient(
                timeout=self.timeout,
                follow_redirects=True,
                headers=headers
            ) as client:
                # طلب HEAD أولاً للتحقق من نوع المحتوى
                try:
                    head_response = await client.head(url)
                    content_type = head_response.headers.get('content-type', '').lower()
                    
                    # التحقق من أن المحتوى HTML
                    if not any(ct in content_type for ct in ['text/html', 'application/xhtml']):
                        return self._create_basic_preview(url)
                        
                except Exception:
                    # إذا فشل HEAD، جرب GET مباشرة
                    pass
                
                # جلب المحتوى
                response = await client.get(url)
                response.raise_for_status()
                
                # التحقق من حجم المحتوى
                content_length = len(response.content)
                if content_length > self.max_content_length:
                    return self._create_basic_preview(url)
                
                # تحليل HTML
                return await self._parse_html(url, response.text)
                
        except httpx.TimeoutException:
            logger.warning(f"انتهت مهلة الطلب للرابط: {url}")
            return self._create_error_preview(url, "انتهت مهلة الطلب")
            
        except httpx.HTTPStatusError as e:
            logger.warning(f"خطأ HTTP {e.response.status_code} للرابط: {url}")
            return self._create_error_preview(url, f"خطأ HTTP {e.response.status_code}")
            
        except Exception as e:
            logger.error(f"خطأ في جلب معاينة الرابط {url}: {str(e)}")
            return self._create_error_preview(url, "فشل في جلب المعاينة")
    
    async def _parse_html(self, url: str, html: str) -> Dict[str, Any]:
        """تحليل HTML لاستخراج المعلومات"""
        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            # استخراج المعلومات
            title = self._extract_title(soup)
            description = self._extract_description(soup)
            image = self._extract_image(soup, url)
            site_name = self._extract_site_name(soup, url)
            favicon = self._extract_favicon(soup, url)
            
            return {
                'url': url,
                'title': title,
                'description': description,
                'image': image,
                'siteName': site_name,
                'favicon': favicon,
                'domain': urlparse(url).netloc.replace('www.', ''),
                'success': True
            }
            
        except Exception as e:
            logger.error(f"خطأ في تحليل HTML للرابط {url}: {str(e)}")
            return self._create_basic_preview(url)
    
    def _extract_title(self, soup: BeautifulSoup) -> Optional[str]:
        """استخراج العنوان"""
        # Open Graph title
        og_title = soup.find('meta', property='og:title')
        if og_title and og_title.get('content'):
            return og_title['content'].strip()
        
        # Twitter title
        twitter_title = soup.find('meta', attrs={'name': 'twitter:title'})
        if twitter_title and twitter_title.get('content'):
            return twitter_title['content'].strip()
        
        # HTML title
        title_tag = soup.find('title')
        if title_tag and title_tag.string:
            return title_tag.string.strip()
        
        return None
    
    def _extract_description(self, soup: BeautifulSoup) -> Optional[str]:
        """استخراج الوصف"""
        # Open Graph description
        og_desc = soup.find('meta', property='og:description')
        if og_desc and og_desc.get('content'):
            return og_desc['content'].strip()
        
        # Twitter description
        twitter_desc = soup.find('meta', attrs={'name': 'twitter:description'})
        if twitter_desc and twitter_desc.get('content'):
            return twitter_desc['content'].strip()
        
        # Meta description
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        if meta_desc and meta_desc.get('content'):
            return meta_desc['content'].strip()
        
        return None
    
    def _extract_image(self, soup: BeautifulSoup, base_url: str) -> Optional[str]:
        """استخراج الصورة"""
        # Open Graph image (الأولوية الأولى)
        og_image = soup.find('meta', property='og:image')
        if og_image and og_image.get('content'):
            image_url = urljoin(base_url, og_image['content'])
            if self._is_valid_image_url(image_url):
                return image_url

        # Open Graph image:secure_url (للفيسبوك)
        og_image_secure = soup.find('meta', property='og:image:secure_url')
        if og_image_secure and og_image_secure.get('content'):
            image_url = urljoin(base_url, og_image_secure['content'])
            if self._is_valid_image_url(image_url):
                return image_url

        # البحث عن صور الفيسبوك بطرق إضافية
        if 'facebook.com' in base_url.lower():
            # البحث عن صور في البيانات المنظمة
            json_ld_scripts = soup.find_all('script', type='application/ld+json')
            for script in json_ld_scripts:
                try:
                    import json
                    data = json.loads(script.string)
                    if isinstance(data, dict) and 'image' in data:
                        image_url = data['image']
                        if isinstance(image_url, list) and image_url:
                            image_url = image_url[0]
                        if isinstance(image_url, str) and self._is_valid_image_url(image_url):
                            return urljoin(base_url, image_url)
                except:
                    continue

        # Twitter image
        twitter_image = soup.find('meta', attrs={'name': 'twitter:image'})
        if twitter_image and twitter_image.get('content'):
            image_url = urljoin(base_url, twitter_image['content'])
            if self._is_valid_image_url(image_url):
                return image_url

        # Twitter image src
        twitter_image_src = soup.find('meta', attrs={'name': 'twitter:image:src'})
        if twitter_image_src and twitter_image_src.get('content'):
            image_url = urljoin(base_url, twitter_image_src['content'])
            if self._is_valid_image_url(image_url):
                return image_url

        # البحث عن أول صورة كبيرة في الصفحة
        images = soup.find_all('img', src=True)
        for img in images:
            src = img.get('src')
            if src:
                image_url = urljoin(base_url, src)
                # تجاهل الصور الصغيرة والأيقونات
                if self._is_valid_content_image(img, image_url):
                    return image_url

        # للفيسبوك: البحث عن صور في data-src أو other attributes
        if 'facebook.com' in base_url.lower():
            fb_images = soup.find_all('img', attrs={'data-src': True})
            for img in fb_images:
                src = img.get('data-src')
                if src:
                    image_url = urljoin(base_url, src)
                    if self._is_valid_image_url(image_url):
                        return image_url

        return None

    def _is_valid_image_url(self, url: str) -> bool:
        """التحقق من صحة رابط الصورة"""
        if not url:
            return False

        # السماح بجميع صور الفيسبوك
        if 'fbcdn.net' in url or 'facebook.com' in url:
            return True

        # تجاهل الصور الصغيرة جداً أو الأيقونات للمواقع الأخرى
        invalid_patterns = [
            'favicon', 'icon', 'logo-small', 'avatar-small',
            '1x1', 'pixel', 'spacer', 'blank'
        ]

        url_lower = url.lower()
        return not any(pattern in url_lower for pattern in invalid_patterns)

    def _is_valid_content_image(self, img_tag, url: str) -> bool:
        """التحقق من كون الصورة مناسبة للمعاينة"""
        if not self._is_valid_image_url(url):
            return False

        # فحص أبعاد الصورة إذا كانت متوفرة
        width = img_tag.get('width')
        height = img_tag.get('height')

        if width and height:
            try:
                w, h = int(width), int(height)
                # تجاهل الصور الصغيرة جداً
                if w < 100 or h < 100:
                    return False
                # تجاهل الصور الطويلة جداً أو العريضة جداً
                if w / h > 5 or h / w > 5:
                    return False
            except ValueError:
                pass

        # فحص الـ alt text أو class للتأكد من أنها ليست أيقونة
        alt = img_tag.get('alt', '').lower()
        class_name = ' '.join(img_tag.get('class', [])).lower()

        invalid_indicators = ['icon', 'logo', 'avatar', 'button', 'arrow', 'bullet']
        if any(indicator in alt or indicator in class_name for indicator in invalid_indicators):
            return False

        return True
    
    def _extract_site_name(self, soup: BeautifulSoup, url: str) -> str:
        """استخراج اسم الموقع"""
        # Open Graph site name
        og_site = soup.find('meta', property='og:site_name')
        if og_site and og_site.get('content'):
            return og_site['content'].strip()
        
        # استخراج من النطاق
        domain = urlparse(url).netloc.replace('www.', '')
        return domain.split('.')[0].capitalize()
    
    def _extract_favicon(self, soup: BeautifulSoup, base_url: str) -> str:
        """استخراج أيقونة الموقع"""
        # البحث عن favicon
        favicon_link = soup.find('link', rel=lambda x: x and 'icon' in x.lower())
        if favicon_link and favicon_link.get('href'):
            return urljoin(base_url, favicon_link['href'])
        
        # استخدام Google favicon service كبديل
        domain = urlparse(base_url).netloc
        return f"https://www.google.com/s2/favicons?domain={domain}"
    
    def _create_basic_preview(self, url: str) -> Dict[str, Any]:
        """إنشاء معاينة أساسية"""
        domain = urlparse(url).netloc.replace('www.', '')
        return {
            'url': url,
            'title': domain.split('.')[0].capitalize(),
            'domain': domain,
            'favicon': f"https://www.google.com/s2/favicons?domain={domain}",
            'success': True
        }
    
    def _create_error_preview(self, url: str, error: str) -> Dict[str, Any]:
        """إنشاء معاينة خطأ"""
        domain = urlparse(url).netloc.replace('www.', '')
        return {
            'url': url,
            'domain': domain,
            'error': error,
            'success': False
        }

    def _is_local_url(self, netloc: str) -> bool:
        """التحقق من كون الرابط محلي (IP address)"""
        import re
        # فحص عناوين IP المحلية
        ip_pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(?:\:[0-9]+)?$'
        return bool(re.match(ip_pattern, netloc))

    async def _handle_local_url(self, url: str, parsed_url) -> Dict[str, Any]:
        """التعامل مع الروابط المحلية"""
        try:
            # محاولة الوصول للرابط المحلي مع timeout قصير
            headers = {
                'User-Agent': 'SmartPOS-LinkPreview/1.0',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'ar,en;q=0.5',
                'Connection': 'keep-alive',
            }

            async with httpx.AsyncClient(
                timeout=5.0,  # timeout أقصر للروابط المحلية
                follow_redirects=True,
                headers=headers
            ) as client:
                response = await client.get(url)
                response.raise_for_status()

                # تحليل HTML إذا كان المحتوى HTML
                content_type = response.headers.get('content-type', '').lower()
                if any(ct in content_type for ct in ['text/html', 'application/xhtml']):
                    return await self._parse_html(url, response.text)
                else:
                    # إنشاء معاينة أساسية للملفات غير HTML
                    return self._create_local_file_preview(url, parsed_url, content_type)

        except Exception as e:
            logger.warning(f"فشل في الوصول للرابط المحلي {url}: {str(e)}")
            return self._create_local_basic_preview(url, parsed_url)

    def _create_local_file_preview(self, url: str, parsed_url, content_type: str) -> Dict[str, Any]:
        """إنشاء معاينة للملفات المحلية"""
        file_types = {
            'application/pdf': 'ملف PDF',
            'image/': 'صورة',
            'video/': 'فيديو',
            'audio/': 'ملف صوتي',
            'application/json': 'ملف JSON',
            'text/': 'ملف نصي'
        }

        file_type = 'ملف'
        for ct, ft in file_types.items():
            if content_type.startswith(ct):
                file_type = ft
                break

        return {
            'url': url,
            'title': f'{file_type} - {parsed_url.netloc}',
            'description': f'ملف محلي من النوع: {content_type}',
            'domain': parsed_url.netloc,
            'siteName': 'خادم محلي',
            'favicon': f"https://www.google.com/s2/favicons?domain={parsed_url.netloc}",
            'success': True
        }

    def _create_local_basic_preview(self, url: str, parsed_url) -> Dict[str, Any]:
        """إنشاء معاينة أساسية للروابط المحلية"""
        return {
            'url': url,
            'title': f'تطبيق محلي - {parsed_url.netloc}',
            'description': 'تطبيق ويب محلي',
            'domain': parsed_url.netloc,
            'siteName': 'خادم محلي',
            'favicon': f"https://www.google.com/s2/favicons?domain={parsed_url.netloc}",
            'success': True
        }

    def _is_youtube_url(self, url: str) -> bool:
        """التحقق من كون الرابط من اليوتيوب"""
        youtube_domains = ['youtube.com', 'www.youtube.com', 'youtu.be', 'm.youtube.com']
        parsed = urlparse(url)
        return parsed.netloc.lower() in youtube_domains

    def _is_facebook_url(self, url: str) -> bool:
        """التحقق من كون الرابط من الفيسبوك"""
        facebook_domains = ['facebook.com', 'www.facebook.com', 'm.facebook.com', 'fb.com']
        parsed = urlparse(url)
        return parsed.netloc.lower() in facebook_domains

    def _is_direct_image_url(self, url: str) -> bool:
        """التحقق من كون الرابط صورة مباشرة"""
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg']
        parsed = urlparse(url)
        path = parsed.path.lower()
        return any(path.endswith(ext) for ext in image_extensions)

    async def _handle_youtube_url(self, url: str) -> Dict[str, Any]:
        """التعامل مع روابط اليوتيوب"""
        try:
            # استخراج معرف الفيديو
            video_id = self._extract_youtube_video_id(url)
            if not video_id:
                return self._create_basic_preview(url)

            # استخدام YouTube oEmbed API
            oembed_url = f"https://www.youtube.com/oembed?url={url}&format=json"

            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(oembed_url)
                response.raise_for_status()
                data = response.json()

                return {
                    'url': url,
                    'title': data.get('title', 'فيديو يوتيوب'),
                    'description': f"فيديو من {data.get('author_name', 'YouTube')}",
                    'image': data.get('thumbnail_url', f"https://img.youtube.com/vi/{video_id}/maxresdefault.jpg"),
                    'siteName': 'YouTube',
                    'favicon': 'https://www.youtube.com/favicon.ico',
                    'domain': 'youtube.com',
                    'videoId': video_id,
                    'embedUrl': f"https://www.youtube.com/embed/{video_id}",
                    'type': 'video',
                    'success': True
                }

        except Exception as e:
            logger.warning(f"فشل في جلب معلومات فيديو اليوتيوب {url}: {str(e)}")
            # إنشاء معاينة أساسية لليوتيوب
            video_id = self._extract_youtube_video_id(url)
            return {
                'url': url,
                'title': 'فيديو يوتيوب',
                'description': 'فيديو من YouTube',
                'image': f"https://img.youtube.com/vi/{video_id}/maxresdefault.jpg" if video_id else None,
                'siteName': 'YouTube',
                'favicon': 'https://www.youtube.com/favicon.ico',
                'domain': 'youtube.com',
                'videoId': video_id,
                'embedUrl': f"https://www.youtube.com/embed/{video_id}" if video_id else None,
                'type': 'video',
                'success': True
            }

    def _extract_youtube_video_id(self, url: str) -> Optional[str]:
        """استخراج معرف فيديو اليوتيوب"""
        import re

        # أنماط مختلفة لروابط اليوتيوب
        patterns = [
            r'(?:youtube\.com/watch\?v=|youtu\.be/|youtube\.com/embed/)([a-zA-Z0-9_-]{11})',
            r'youtube\.com/watch\?.*v=([a-zA-Z0-9_-]{11})',
        ]

        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)

        return None

    async def _handle_facebook_url(self, url: str) -> Dict[str, Any]:
        """التعامل مع روابط الفيسبوك"""
        try:
            # إعداد headers خاصة للفيسبوك - محاكاة Facebook's own crawler
            headers = {
                'User-Agent': 'Mozilla/5.0 (compatible; facebookexternalhit/1.1; +http://www.facebook.com/externalhit_uatext.php)',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
            }

            async with httpx.AsyncClient(
                timeout=15.0,  # timeout أطول للفيسبوك
                follow_redirects=True,
                headers=headers
            ) as client:
                response = await client.get(url)
                response.raise_for_status()

                # تحليل HTML
                return await self._parse_html(url, response.text)

        except Exception as e:
            logger.warning(f"فشل في جلب معلومات منشور الفيسبوك {url}: {str(e)}")
            # إنشاء معاينة أساسية للفيسبوك
            return {
                'url': url,
                'title': 'منشور فيسبوك',
                'description': 'منشور من Facebook',
                'siteName': 'Facebook',
                'favicon': 'https://www.facebook.com/favicon.ico',
                'domain': 'facebook.com',
                'type': 'website',
                'success': True
            }

    def _handle_direct_image_url(self, url: str) -> Dict[str, Any]:
        """التعامل مع روابط الصور المباشرة"""
        parsed = urlparse(url)
        filename = parsed.path.split('/')[-1] if parsed.path else 'صورة'

        return {
            'url': url,
            'title': filename,
            'description': 'صورة',
            'image': url,
            'domain': parsed.netloc.replace('www.', ''),
            'siteName': parsed.netloc.replace('www.', '').split('.')[0].capitalize(),
            'favicon': f"https://www.google.com/s2/favicons?domain={parsed.netloc}",
            'type': 'image',
            'success': True
        }

# إنشاء instance من الخدمة
preview_service = LinkPreviewService()

@router.get("/link-preview")
async def get_link_preview(url: str = Query(..., description="الرابط المراد معاينته")):
    """
    الحصول على معاينة الرابط
    """
    try:
        if not url:
            raise HTTPException(status_code=400, detail="الرابط مطلوب")
        
        # التحقق من صحة الرابط
        if not url.startswith(('http://', 'https://')):
            url = f"https://{url}"
        
        preview = await preview_service.get_preview(url)
        return JSONResponse(content=preview)
        
    except Exception as e:
        logger.error(f"خطأ في API معاينة الروابط: {str(e)}")
        raise HTTPException(status_code=500, detail="خطأ في الخادم")
