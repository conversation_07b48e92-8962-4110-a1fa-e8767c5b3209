"""
تقارير المديونية المحسنة
تستخدم خدمة تحليل المديونية المتقدمة مع مبادئ البرمجة الكائنية
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
import logging

from database.session import get_db
from models.user import User
from routers.auth import get_current_user
from services.debt_analytics_service import DebtAnalyticsService

# إعداد السجلات
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/debts/reports", tags=["debt-reports"])

# إنشاء مثيل من خدمة تحليل المديونية
debt_service = DebtAnalyticsService()

@router.get("/summary")
async def get_debt_summary(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    الحصول على ملخص شامل للمديونية
    """
    try:
        logger.info(f"طلب ملخص المديونية من المستخدم: {current_user.username}")

        # استخدام خدمة تحليل المديونية
        summary = debt_service.get_debt_summary(db)

        # تحويل البيانات إلى تنسيق API
        response = {
            "totalDebts": summary.total_debts,
            "totalAmount": summary.total_amount,
            "paidAmount": summary.paid_amount,
            "remainingAmount": summary.remaining_amount,
            "collectionRate": summary.collection_rate,
            "averageDebtAge": summary.average_debt_age,
            "overdueDebts": summary.overdue_debts,
            "overdueAmount": summary.overdue_amount,
            "uniqueDebtors": summary.unique_debtors,
            "averageDebtAmount": summary.average_debt_amount,
            "maxDebt": summary.max_debt,
            "minDebt": summary.min_debt
        }

        logger.info(f"تم إرجاع ملخص المديونية بنجاح: {summary.remaining_amount:.2f} د.ل متبقي")
        return response

    except Exception as e:
        logger.error(f"خطأ في جلب ملخص المديونية: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ في جلب ملخص المديونية: {str(e)}")

@router.get("/aging")
async def get_debt_aging(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> List[Dict[str, Any]]:
    """
    الحصول على تقرير أعمار الديون
    """
    try:
        logger.info(f"طلب تقرير أعمار الديون من المستخدم: {current_user.username}")

        # استخدام خدمة تحليل المديونية
        aging_data = debt_service.get_debt_aging(db)

        # تحويل البيانات إلى تنسيق API
        response = []
        for aging in aging_data:
            response.append({
                "range": aging.range_name,
                "count": aging.count,
                "amount": aging.amount,
                "percentage": aging.percentage
            })

        logger.info(f"تم إرجاع تقرير أعمار الديون بنجاح: {len(response)} فئة")
        return response

    except Exception as e:
        logger.error(f"خطأ في جلب تقرير أعمار الديون: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ في جلب تقرير أعمار الديون: {str(e)}")

@router.get("/trends")
async def get_debt_trends(
    period: str = Query("month", description="الفترة الزمنية: day, week, month, year"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> List[Dict[str, Any]]:
    """
    الحصول على اتجاهات المديونية
    """
    try:
        logger.info(f"طلب اتجاهات المديونية للفترة {period} من المستخدم: {current_user.username}")

        # التحقق من صحة الفترة
        valid_periods = ["day", "week", "month", "year"]
        if period not in valid_periods:
            raise HTTPException(status_code=400, detail=f"فترة غير صحيحة. الفترات المتاحة: {valid_periods}")

        # استخدام خدمة تحليل المديونية
        trends_data = debt_service.get_debt_trends(db, period)

        # تحويل البيانات إلى تنسيق API مع إصلاح أسماء الحقول
        response = []
        for trend in trends_data:
            response.append({
                "date": trend.period,  # الواجهة تتوقع "date" وليس "period"
                "period": trend.period,
                "newDebts": trend.new_debts,
                "newAmount": trend.new_amount,
                "paidDebts": trend.paid_debts,
                "paidAmount": trend.paid_amount,
                "netChange": trend.net_change
            })

        logger.info(f"تم إرجاع اتجاهات المديونية بنجاح: {len(response)} فترة")
        return response

    except Exception as e:
        logger.error(f"خطأ في جلب اتجاهات المديونية: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ في جلب اتجاهات المديونية: {str(e)}")

@router.get("/collection-efficiency")
async def get_collection_efficiency(
    period: str = Query("month", description="الفترة الزمنية: day, week, month, year"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> List[Dict[str, Any]]:
    """
    الحصول على كفاءة التحصيل
    """
    try:
        logger.info(f"طلب كفاءة التحصيل للفترة {period} من المستخدم: {current_user.username}")

        # التحقق من صحة الفترة
        valid_periods = ["day", "week", "month", "year"]
        if period not in valid_periods:
            raise HTTPException(status_code=400, detail=f"فترة غير صحيحة. الفترات المتاحة: {valid_periods}")

        # استخدام خدمة تحليل المديونية
        efficiency_data = debt_service.get_collection_efficiency(db, period)

        # تحويل البيانات إلى تنسيق API
        response = []
        for efficiency in efficiency_data:
            response.append({
                "period": efficiency.period,
                "targetCollection": efficiency.target_collection,
                "actualCollection": efficiency.actual_collection,
                "efficiency": efficiency.efficiency,
                "variance": efficiency.variance
            })

        logger.info(f"تم إرجاع كفاءة التحصيل بنجاح: {len(response)} فترة")
        return response

    except Exception as e:
        logger.error(f"خطأ في جلب كفاءة التحصيل: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ في جلب كفاءة التحصيل: {str(e)}")

@router.get("/top-debtors")
async def get_top_debtors(
    limit: int = Query(10, description="عدد أكبر المدينين المطلوب"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> List[Dict[str, Any]]:
    """
    الحصول على أكبر المدينين
    """
    try:
        logger.info(f"طلب أكبر {limit} مدينين من المستخدم: {current_user.username}")

        # التحقق من صحة الحد
        if limit < 1 or limit > 100:
            raise HTTPException(status_code=400, detail="الحد يجب أن يكون بين 1 و 100")

        # استخدام خدمة تحليل المديونية
        top_debtors = debt_service.get_top_debtors(db, limit)

        logger.info(f"تم إرجاع أكبر المدينين بنجاح: {len(top_debtors)} عميل")
        return top_debtors

    except Exception as e:
        logger.error(f"خطأ في جلب أكبر المدينين: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ في جلب أكبر المدينين: {str(e)}")