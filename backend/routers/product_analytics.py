from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, select, and_, case, desc, cast, Float
from typing import List, Optional, cast as typing_cast
from datetime import datetime, timedelta
from decimal import Decimal, ROUND_HALF_UP
import logging

from database.session import get_db
from models.product import Product
from models.sale import Sale, SaleItem
from models.user import User
from schemas.product_analytics import (
    ProductAnalyticsResponse,
    ProductAnalyticsSummary,
    BestSellingProduct,
    UnsoldProduct,
    ProductPerformance,
    ExpectedLoss,
    InventoryStatus,
    AvailablePeriod
)
from utils.auth import get_current_user
from utils.datetime_utils import get_tripoli_now

# Setup logging
logger = logging.getLogger(__name__)

def _calculate_days_without_sales(db: Session, product_id: int, current_time: datetime, product_created_at: Optional[datetime] = None) -> int:
    """Calculate accurate days without sales for a product."""
    # Find the last sale date for this product
    last_sale_query = select(func.max(Sale.created_at))\
        .select_from(Sale)\
        .join(SaleItem, Sale.id == SaleItem.sale_id)\
        .where(SaleItem.product_id == product_id)

    last_sale_date = db.execute(last_sale_query).scalar()

    if last_sale_date:
        # Calculate days since last sale
        if last_sale_date.tzinfo is None:
            current_time_naive = current_time.replace(tzinfo=None)
            days_without_sales = (current_time_naive - last_sale_date).days
        else:
            days_without_sales = (current_time - last_sale_date).days
    else:
        # Product never sold - use creation date or a reasonable default
        if product_created_at:
            if product_created_at.tzinfo is None:
                current_time_naive = current_time.replace(tzinfo=None)
                days_without_sales = (current_time_naive - product_created_at).days
            else:
                days_without_sales = (current_time - product_created_at).days
        else:
            # Fallback: assume very old product
            days_without_sales = 365

    return max(0, days_without_sales)  # Ensure non-negative

def _calculate_loss_metrics(days_without_sales: int, cost_value: float, period_days: int) -> tuple:
    """Calculate loss percentage, category, and recommendation based on days without sales and analysis period."""

    # Adjust thresholds based on analysis period for more accurate assessment
    if period_days <= 30:  # Short-term analysis
        if days_without_sales > 90:  # 3+ months without sales in 30-day analysis
            loss_percentage = 0.30
            loss_category = 'high_risk'
            recommendation = 'تصفية عاجلة - منتج راكد'
        elif days_without_sales > 60:  # 2+ months
            loss_percentage = 0.20
            loss_category = 'high_risk'
            recommendation = 'تخفيض السعر بشكل كبير'
        elif days_without_sales > 30:  # 1+ month
            loss_percentage = 0.10
            loss_category = 'medium_risk'
            recommendation = 'عروض ترويجية عاجلة'
        else:
            loss_percentage = 0.05
            loss_category = 'low_risk'
            recommendation = 'مراقبة مستمرة'

    elif period_days <= 90:  # Medium-term analysis
        if days_without_sales > 180:  # 6+ months
            loss_percentage = 0.35
            loss_category = 'high_risk'
            recommendation = 'تصفية فورية بأي سعر'
        elif days_without_sales > 120:  # 4+ months
            loss_percentage = 0.25
            loss_category = 'high_risk'
            recommendation = 'تصفية عاجلة بخصم كبير'
        elif days_without_sales > 60:  # 2+ months
            loss_percentage = 0.15
            loss_category = 'medium_risk'
            recommendation = 'تخفيض السعر أو عروض ترويجية'
        elif days_without_sales > 30:  # 1+ month
            loss_percentage = 0.08
            loss_category = 'medium_risk'
            recommendation = 'مراجعة استراتيجية التسويق'
        else:
            loss_percentage = 0.03
            loss_category = 'low_risk'
            recommendation = 'مراقبة مستمرة'

    else:  # Long-term analysis (90+ days)
        if days_without_sales > 365:  # 1+ year
            loss_percentage = 0.50
            loss_category = 'high_risk'
            recommendation = 'تصفية فورية - منتج ميت'
        elif days_without_sales > 270:  # 9+ months
            loss_percentage = 0.40
            loss_category = 'high_risk'
            recommendation = 'تصفية فورية بأي سعر'
        elif days_without_sales > 180:  # 6+ months
            loss_percentage = 0.30
            loss_category = 'high_risk'
            recommendation = 'تصفية عاجلة بخصم كبير'
        elif days_without_sales > 90:  # 3+ months
            loss_percentage = 0.15
            loss_category = 'medium_risk'
            recommendation = 'تخفيض السعر أو عروض ترويجية'
        elif days_without_sales > 45:  # 1.5+ months
            loss_percentage = 0.08
            loss_category = 'medium_risk'
            recommendation = 'مراجعة استراتيجية التسويق'
        else:
            loss_percentage = 0.03
            loss_category = 'low_risk'
            recommendation = 'مراقبة مستمرة'

    estimated_loss = cost_value * loss_percentage
    return loss_percentage, loss_category, recommendation, estimated_loss

router = APIRouter(prefix="/api/product-analytics", tags=["product-analytics"])

@router.get("/summary", response_model=ProductAnalyticsResponse)
async def get_product_analytics_summary(
    period_days: int = Query(30, description="Number of days to analyze"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get comprehensive product analytics summary with optimized performance."""
    try:
        # Calculate date range
        end_date = get_tripoli_now()
        start_date = end_date - timedelta(days=period_days)

        logger.info(f"Starting analytics summary calculation for period: {period_days} days")

        # Get summary statistics (optimized)
        summary = await _get_analytics_summary_optimized(db, start_date, end_date)

        # Get best selling products (limited to 10 for faster loading)
        best_selling = await _get_best_selling_products_optimized(db, start_date, end_date, limit=10)

        # Get unsold products (limited for initial load)
        unsold_products = await _get_unsold_products_optimized(db, start_date, end_date, limit=20)

        # Get performance analysis (limited for initial load)
        performance_analysis = await _get_performance_analysis_optimized(db, start_date, end_date, limit=20)

        # Get expected losses (limited for initial load)
        expected_losses = await _get_expected_losses_optimized(db, start_date, end_date, limit=15)

        # Get inventory status (limited for initial load)
        inventory_status = await _get_inventory_status_optimized(db, limit=20)

        logger.info("Analytics summary calculation completed successfully")

        return ProductAnalyticsResponse(
            summary=summary,
            best_selling=best_selling,
            unsold_products=unsold_products,
            performance_analysis=performance_analysis,
            expected_losses=expected_losses,
            inventory_status=inventory_status,
            period_days=period_days,
            generated_at=end_date
        )

    except Exception as e:
        logger.error(f"Error generating analytics summary: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating analytics: {str(e)}")

@router.get("/best-selling", response_model=List[BestSellingProduct])
async def get_best_selling_products(
    period_days: int = Query(30, description="Number of days to analyze"),
    limit: int = Query(20, description="Number of products to return"),
    category: Optional[str] = Query(None, description="Filter by category"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get best selling products for the specified period with optimized performance."""
    try:
        end_date = get_tripoli_now()
        start_date = end_date - timedelta(days=period_days)

        logger.info(f"Fetching best selling products: period={period_days}, limit={limit}")
        return await _get_best_selling_products_optimized(db, start_date, end_date, limit, category)

    except Exception as e:
        logger.error(f"Error fetching best selling products: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching best selling products: {str(e)}")

@router.get("/summary-only", response_model=ProductAnalyticsSummary)
async def get_analytics_summary_only(
    period_days: int = Query(30, description="Number of days to analyze"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get only the analytics summary for faster initial loading."""
    try:
        end_date = get_tripoli_now()
        start_date = end_date - timedelta(days=period_days)

        logger.info(f"Fetching analytics summary only: period={period_days}")
        return await _get_analytics_summary_optimized(db, start_date, end_date)

    except Exception as e:
        logger.error(f"Error fetching analytics summary: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching analytics summary: {str(e)}")

@router.get("/performance-analysis", response_model=List[ProductPerformance])
async def get_performance_analysis(
    period_days: int = Query(30, description="Number of days to analyze"),
    limit: int = Query(None, description="Number of products to analyze (None for all products)"),
    category: Optional[str] = Query(None, description="Filter by category"),
    search: Optional[str] = Query(None, description="Search by product name or barcode"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get detailed performance analysis for products."""
    try:
        end_date = get_tripoli_now()
        start_date = end_date - timedelta(days=period_days)

        logger.info(f"Fetching performance analysis: period={period_days}, limit={limit}, category={category}")
        return await _get_performance_analysis_optimized(db, start_date, end_date, limit, category, search)

    except Exception as e:
        logger.error(f"Error fetching performance analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching performance analysis: {str(e)}")

@router.get("/unsold", response_model=List[UnsoldProduct])
async def get_unsold_products(
    period_days: int = Query(30, description="Number of days to check for sales"),
    category: Optional[str] = Query(None, description="Filter by category"),
    search: Optional[str] = Query(None, description="Search by product name or barcode"),
    min_stock: int = Query(0, description="Minimum stock to include"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get products that haven't been sold in the specified period."""
    try:
        end_date = get_tripoli_now()
        start_date = end_date - timedelta(days=period_days)
        
        return await _get_unsold_products(db, start_date, end_date, category, search, min_stock)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching unsold products: {str(e)}")

@router.get("/expected-losses", response_model=List[ExpectedLoss])
async def get_expected_losses(
    period_days: int = Query(30, description="Number of days to analyze"),
    risk_level: Optional[str] = Query(None, description="Filter by risk level: high_risk, medium_risk, low_risk"),
    search: Optional[str] = Query(None, description="Search by product name or barcode"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Calculate expected losses from slow-moving inventory."""
    try:
        end_date = get_tripoli_now()
        start_date = end_date - timedelta(days=period_days)
        
        losses = await _get_expected_losses(db, start_date, end_date, search)
        
        if risk_level:
            losses = [loss for loss in losses if loss.loss_category == risk_level]
            
        return losses

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error calculating expected losses: {str(e)}")

@router.get("/available-periods", response_model=List[AvailablePeriod])
async def get_available_periods(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get available analysis periods based on actual data."""
    try:
        logger.info("Fetching available analysis periods")

        # Define standard periods to check (in days)
        standard_periods = [7, 30, 60, 90, 180, 365]
        available_periods = []

        current_date = get_tripoli_now()

        for period_days in standard_periods:
            start_date = current_date - timedelta(days=period_days)

            logger.debug(f"Calculating period {period_days} days: {start_date} to {current_date}")

            # Count sales in this period (using proper date range)
            sales_query = select(func.count(Sale.id), func.coalesce(func.sum(Sale.amount_paid), 0))\
                .where(and_(Sale.created_at >= start_date, Sale.created_at <= current_date))

            sales_result = db.execute(sales_query).first()
            sales_count = sales_result[0] if sales_result else 0
            total_amount = float(sales_result[1]) if sales_result else 0.0

            # Count products with sales in this period (using proper date range)
            products_query = select(func.count(func.distinct(SaleItem.product_id)))\
                .select_from(SaleItem)\
                .join(Sale, SaleItem.sale_id == Sale.id)\
                .where(and_(Sale.created_at >= start_date, Sale.created_at <= current_date))

            products_result = db.execute(products_query).scalar()
            products_count = products_result if products_result else 0

            logger.debug(f"Period {period_days} days: {sales_count} sales, {products_count} products, {total_amount} total amount")

            # Determine period label
            if period_days == 7:
                period_label = "أسبوع واحد"
            elif period_days == 30:
                period_label = "30 يوم"
            elif period_days == 60:
                period_label = "60 يوم"
            elif period_days == 90:
                period_label = "3 أشهر"
            elif period_days == 180:
                period_label = "6 أشهر"
            elif period_days == 365:
                period_label = "سنة كاملة"
            else:
                period_label = f"{period_days} يوم"

            # Include all periods, but mark which ones have data
            has_data = sales_count > 0 or products_count > 0

            available_periods.append(AvailablePeriod(
                period_days=period_days,
                period_label=period_label,
                sales_count=sales_count,
                products_count=products_count,
                total_amount=total_amount,
                start_date=start_date.strftime('%Y-%m-%d'),
                end_date=current_date.strftime('%Y-%m-%d'),
                has_data=has_data
            ))

        logger.info(f"Found {len(available_periods)} available periods")
        return available_periods

    except Exception as e:
        logger.error(f"Error getting available periods: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching available periods: {str(e)}")

@router.get("/inventory-status", response_model=List[InventoryStatus])
async def get_inventory_status(
    period_days: int = Query(30, description="Number of days to analyze for sales trends"),
    status_filter: Optional[str] = Query(None, description="Filter by status: healthy, low, overstocked, out_of_stock"),
    category: Optional[str] = Query(None, description="Filter by category"),
    search: Optional[str] = Query(None, description="Search by product name or barcode"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get current inventory status with recommendations based on sales trends."""
    try:
        # Calculate date range for sales analysis
        end_date = get_tripoli_now()
        start_date = end_date - timedelta(days=period_days)

        inventory_status = await _get_inventory_status(db, category, search, start_date, end_date)

        if status_filter:
            inventory_status = [item for item in inventory_status if item.stock_status == status_filter]

        return inventory_status
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching inventory status: {str(e)}")

# Optimized Helper functions
async def _get_analytics_summary_optimized(db: Session, start_date: datetime, end_date: datetime) -> ProductAnalyticsSummary:
    """Calculate analytics summary with optimized performance."""
    logger.info(f"Calculating optimized analytics summary from {start_date} to {end_date}")

    try:
        # Single optimized query to get all basic counts
        basic_stats_query = select(
            func.count(Product.id).label('total_products'),
            func.count(case((Product.is_active == True, 1))).label('active_products'),
            func.coalesce(func.sum(case((Product.is_active == True, Product.price * Product.quantity), else_=0)), 0).label('total_stock_value'),
            func.coalesce(func.sum(case((Product.is_active == True, Product.cost_price * Product.quantity), else_=0)), 0).label('total_cost_value')
        ).select_from(Product)

        basic_stats = db.execute(basic_stats_query).first()

        # Optimized query for products with sales
        products_with_sales = db.execute(
            select(func.count(func.distinct(SaleItem.product_id)))
            .select_from(SaleItem)
            .join(Sale, SaleItem.sale_id == Sale.id)
            .where(Sale.created_at.between(start_date, end_date))
        ).scalar() or 0

        # Optimized profit margin calculation
        profit_margin_query = select(
            func.coalesce(func.sum(SaleItem.subtotal), 0).label('total_revenue'),
            func.coalesce(func.sum((SaleItem.unit_price - Product.cost_price) * SaleItem.quantity), 0).label('total_profit')
        ).select_from(SaleItem)\
        .join(Product, SaleItem.product_id == Product.id)\
        .join(Sale, SaleItem.sale_id == Sale.id)\
        .where(Sale.created_at.between(start_date, end_date))

        profit_stats = db.execute(profit_margin_query).first()

        # Calculate average profit margin
        if profit_stats and profit_stats.total_revenue and profit_stats.total_revenue > 0:
            average_profit_margin = float((profit_stats.total_profit / profit_stats.total_revenue) * 100)
        else:
            average_profit_margin = 0.0

        return ProductAnalyticsSummary(
            total_products=basic_stats.total_products if basic_stats else 0,
            active_products=basic_stats.active_products if basic_stats else 0,
            products_with_sales=products_with_sales,
            products_without_sales=(basic_stats.active_products if basic_stats else 0) - products_with_sales,
            total_stock_value=float(basic_stats.total_stock_value if basic_stats else 0),
            total_cost_value=float(basic_stats.total_cost_value if basic_stats else 0),
            average_profit_margin=round(average_profit_margin, 2),
            top_performing_category=None,
            worst_performing_category=None,
            total_potential_losses=0.0
        )

    except Exception as e:
        logger.error(f"Error in optimized analytics summary: {str(e)}")
        raise

# Original helper function (kept for backward compatibility)
async def _get_analytics_summary(db: Session, start_date: datetime, end_date: datetime) -> ProductAnalyticsSummary:
    """Calculate analytics summary with high precision."""
    logger.info(f"Calculating analytics summary from {start_date} to {end_date}")

    # Total products
    total_products = db.execute(select(func.count(Product.id))).scalar() or 0

    # Active products
    active_products = db.execute(
        select(func.count(Product.id)).where(Product.is_active == True)
    ).scalar() or 0

    # Products with sales in period
    products_with_sales = db.execute(
        select(func.count(func.distinct(SaleItem.product_id)))
        .select_from(SaleItem)
        .join(Sale, SaleItem.sale_id == Sale.id)
        .where(and_(Sale.created_at >= start_date, Sale.created_at <= end_date))
    ).scalar() or 0

    products_without_sales = active_products - products_with_sales

    # Stock values with high precision
    stock_value_result = db.execute(
        select(func.sum(cast(Product.price * Product.quantity, Float)))
        .where(Product.is_active == True)
    ).scalar()
    total_stock_value = float(Decimal(str(stock_value_result or 0)).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP))

    cost_value_result = db.execute(
        select(func.sum(cast(Product.cost_price * Product.quantity, Float)))
        .where(Product.is_active == True)
    ).scalar()
    total_cost_value = float(Decimal(str(cost_value_result or 0)).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP))

    # Calculate weighted average profit margin based on actual sales
    # This is more accurate than simple average of all products
    sales_profit_query = select(
        func.sum(cast(SaleItem.subtotal, Float)).label('total_revenue'),
        func.sum(cast((SaleItem.unit_price - Product.cost_price) * SaleItem.quantity, Float)).label('total_profit')
    ).select_from(SaleItem)\
    .join(Product, SaleItem.product_id == Product.id)\
    .join(Sale, SaleItem.sale_id == Sale.id)\
    .where(and_(
        Sale.created_at >= start_date,
        Sale.created_at <= end_date,
        Product.is_active == True
    ))

    sales_result = db.execute(sales_profit_query).first()
    if sales_result and sales_result.total_revenue and sales_result.total_revenue > 0:
        average_profit_margin = float(
            (Decimal(str(sales_result.total_profit)) / Decimal(str(sales_result.total_revenue)) * 100)
            .quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        )
    else:
        # Fallback to simple average if no sales data
        avg_margin_result = db.execute(
            select(func.avg(case(
                (Product.price > 0, (Product.price - Product.cost_price) / Product.price * 100),
                else_=0
            ))).where(Product.is_active == True)
        ).scalar()
        average_profit_margin = float(Decimal(str(avg_margin_result or 0)).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP))

    logger.info(f"Summary calculated: {total_products} total, {active_products} active, {products_with_sales} with sales")
    
    return ProductAnalyticsSummary(
        total_products=total_products,
        active_products=active_products,
        products_with_sales=products_with_sales,
        products_without_sales=products_without_sales,
        total_stock_value=total_stock_value,
        total_cost_value=total_cost_value,
        average_profit_margin=average_profit_margin,
        top_performing_category=None,  # Will be calculated separately
        worst_performing_category=None,  # Will be calculated separately
        total_potential_losses=0.0  # Will be calculated from expected losses
    )

async def _get_best_selling_products_optimized(
    db: Session,
    start_date: datetime,
    end_date: datetime,
    limit: int = 20,
    category: Optional[str] = None
) -> List[BestSellingProduct]:
    """Get best selling products with optimized performance."""
    logger.info(f"Calculating optimized best selling products from {start_date} to {end_date}, limit: {limit}")

    try:
        # Single optimized query with all calculations
        query = select(
            Product.id,
            Product.name,
            Product.barcode,
            Product.category,
            Product.cost_price,
            func.sum(SaleItem.quantity).label('total_sold'),
            func.sum(SaleItem.subtotal).label('total_revenue'),
            func.sum((SaleItem.unit_price - Product.cost_price) * SaleItem.quantity).label('total_profit')
        ).select_from(Product)\
        .join(SaleItem, Product.id == SaleItem.product_id)\
        .join(Sale, SaleItem.sale_id == Sale.id)\
        .where(Sale.created_at.between(start_date, end_date))\
        .group_by(Product.id, Product.name, Product.barcode, Product.category, Product.cost_price)\
        .order_by(func.sum(SaleItem.quantity).desc())\
        .limit(limit)

        if category:
            query = query.where(Product.category == category)

        results = db.execute(query).all()

        # Get total sales for percentage calculation (optimized)
        total_sales = db.execute(
            select(func.sum(SaleItem.quantity))
            .select_from(SaleItem)
            .join(Sale, SaleItem.sale_id == Sale.id)
            .where(Sale.created_at.between(start_date, end_date))
        ).scalar() or 1.0

        best_selling = []
        for rank, row in enumerate(results, 1):
            total_sold = float(row.total_sold or 0)
            total_revenue = float(row.total_revenue or 0)
            total_profit = float(row.total_profit or 0)

            profit_margin = (total_profit / total_revenue * 100) if total_revenue > 0 else 0.0
            percentage = (total_sold / total_sales * 100) if total_sales > 0 else 0.0

            best_selling.append(BestSellingProduct(
                id=row.id,
                name=row.name,
                barcode=row.barcode,
                category=row.category,
                total_sold=int(total_sold),
                total_revenue=round(total_revenue, 2),
                total_profit=round(total_profit, 2),
                profit_margin=round(profit_margin, 2),
                sales_rank=rank,
                percentage_of_total_sales=round(percentage, 2)
            ))

        return best_selling

    except Exception as e:
        logger.error(f"Error in optimized best selling products: {str(e)}")
        raise

async def _get_best_selling_products(
    db: Session,
    start_date: datetime,
    end_date: datetime,
    limit: int = 20,
    category: Optional[str] = None
) -> List[BestSellingProduct]:
    """Get best selling products for the period with high precision calculations."""

    logger.info(f"Calculating best selling products from {start_date} to {end_date}")

    # Build query for sales data with precise calculations
    query = select(
        Product.id,
        Product.name,
        Product.barcode,
        Product.category,
        Product.cost_price,
        func.sum(cast(SaleItem.quantity, Float)).label('total_sold'),
        func.sum(cast(SaleItem.subtotal, Float)).label('total_revenue'),
        func.sum(cast((SaleItem.unit_price - Product.cost_price) * SaleItem.quantity, Float)).label('total_profit')
    ).select_from(Product)\
    .join(SaleItem, Product.id == SaleItem.product_id)\
    .join(Sale, SaleItem.sale_id == Sale.id)\
    .where(and_(
        Sale.created_at >= start_date,
        Sale.created_at <= end_date,
        Product.is_active == True
    ))

    if category:
        query = query.where(Product.category == category)

    query = query.group_by(Product.id, Product.name, Product.barcode, Product.category, Product.cost_price)\
                 .order_by(desc(func.sum(cast(SaleItem.quantity, Float))))\
                 .limit(limit)

    results = db.execute(query).all()
    logger.info(f"Found {len(results)} best selling products")

    # Calculate total sales for percentage calculation with precise query
    total_sales_query = select(func.sum(cast(SaleItem.quantity, Float)))\
        .select_from(SaleItem)\
        .join(Sale, SaleItem.sale_id == Sale.id)\
        .where(and_(Sale.created_at >= start_date, Sale.created_at <= end_date))

    total_sales_result = db.execute(total_sales_query).scalar()
    total_sales = float(total_sales_result) if total_sales_result else 1.0
    logger.info(f"Total sales in period: {total_sales}")

    best_selling = []
    for rank, row in enumerate(results, 1):
        # Use Decimal for high precision calculations
        total_sold = Decimal(str(row.total_sold or 0))
        total_revenue = Decimal(str(row.total_revenue or 0))
        total_profit = Decimal(str(row.total_profit or 0))
        cost_price = Decimal(str(row.cost_price or 0))

        # Calculate profit margin with high precision
        if total_revenue > 0:
            profit_margin = float((total_profit / total_revenue * 100).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP))
        else:
            profit_margin = 0.0

        # Calculate percentage of total sales
        if total_sales > 0:
            percentage = float((total_sold / Decimal(str(total_sales)) * 100).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP))
        else:
            percentage = 0.0

        # Validate calculations
        expected_profit = (total_revenue - (cost_price * total_sold))
        if abs(total_profit - expected_profit) > Decimal('0.01'):
            logger.warning(f"Profit calculation mismatch for product {row.name}: calculated={total_profit}, expected={expected_profit}")

        best_selling.append(BestSellingProduct(
            id=row.id,
            name=row.name,
            barcode=row.barcode,
            category=row.category,
            total_sold=int(total_sold),
            total_revenue=float(total_revenue),
            total_profit=float(total_profit),
            profit_margin=profit_margin,
            sales_rank=rank,
            percentage_of_total_sales=percentage
        ))

        logger.debug(f"Product {row.name}: sold={total_sold}, revenue={total_revenue}, profit={total_profit}, margin={profit_margin}%")

    return best_selling

async def _get_unsold_products_optimized(
    db: Session,
    start_date: datetime,
    end_date: datetime,
    limit: int = 20,
    category: Optional[str] = None,
    min_stock: int = 0
) -> List[UnsoldProduct]:
    """Get unsold products with optimized performance."""
    logger.info(f"Calculating optimized unsold products, limit: {limit}")

    try:
        # Optimized query to get unsold products
        sold_products_subquery = select(func.distinct(SaleItem.product_id))\
            .select_from(SaleItem)\
            .join(Sale, SaleItem.sale_id == Sale.id)\
            .where(Sale.created_at.between(start_date, end_date))

        query = select(Product)\
            .where(and_(
                Product.is_active == True,
                Product.quantity >= min_stock,
                ~Product.id.in_(sold_products_subquery)
            ))\
            .order_by(Product.quantity.desc())\
            .limit(limit)

        if category:
            query = query.where(Product.category == category)

        results = db.execute(query).all()

        unsold_products = []
        current_time = get_tripoli_now()

        for row in results:
            product = row[0]

            # Calculate days in stock
            if product.created_at:
                if product.created_at.tzinfo is None:
                    current_time_naive = current_time.replace(tzinfo=None)
                    days_in_stock = (current_time_naive - product.created_at).days
                else:
                    days_in_stock = (current_time - product.created_at).days
            else:
                days_in_stock = 0

            stock_value = float(product.price * product.quantity)
            cost_value = float(product.cost_price * product.quantity)
            potential_loss = cost_value * 0.1  # 10% depreciation

            unsold_products.append(UnsoldProduct(
                id=product.id,
                name=product.name,
                barcode=product.barcode,
                category=product.category,
                current_stock=product.quantity,
                stock_value=round(stock_value, 2),
                cost_value=round(cost_value, 2),
                days_in_stock=days_in_stock,
                potential_loss=round(potential_loss, 2),
                last_updated=product.updated_at or product.created_at
            ))

        return unsold_products

    except Exception as e:
        logger.error(f"Error in optimized unsold products: {str(e)}")
        raise

async def _get_unsold_products(
    db: Session,
    start_date: datetime,
    end_date: datetime,
    category: Optional[str] = None,
    search: Optional[str] = None,
    min_stock: int = 0
) -> List[UnsoldProduct]:
    """Get products that haven't been sold in the period."""

    # Get products that have no sales in the period
    sold_products_subquery = select(func.distinct(SaleItem.product_id))\
        .select_from(SaleItem)\
        .join(Sale, SaleItem.sale_id == Sale.id)\
        .where(and_(Sale.created_at >= start_date, Sale.created_at <= end_date))

    query = select(Product)\
        .where(and_(
            Product.is_active == True,
            Product.quantity >= min_stock,
            ~Product.id.in_(sold_products_subquery)
        ))

    if category:
        query = query.where(Product.category == category)

    if search:
        query = query.where(
            (Product.name.ilike(f"%{search}%")) |
            (Product.barcode.ilike(f"%{search}%"))
        )

    results = db.execute(query).all()

    unsold_products = []
    for row in results:
        product = row[0]  # Extract product from result row
        # Handle timezone-aware datetime comparison
        if product.created_at:
            if product.created_at.tzinfo is None:
                # If product.created_at is naive, make end_date naive too
                end_date_naive = end_date.replace(tzinfo=None)
                days_in_stock = (end_date_naive - product.created_at).days
            else:
                days_in_stock = (end_date - product.created_at).days
        else:
            days_in_stock = 0
        stock_value = product.price * product.quantity
        cost_value = product.cost_price * product.quantity
        potential_loss = cost_value * 0.1  # Assume 10% depreciation

        unsold_products.append(UnsoldProduct(
            id=product.id,
            name=product.name,
            barcode=product.barcode,
            category=product.category,
            current_stock=product.quantity,
            stock_value=stock_value,
            cost_value=cost_value,
            days_in_stock=days_in_stock,
            potential_loss=potential_loss,
            last_updated=product.updated_at or product.created_at
        ))

    return unsold_products

async def _get_performance_analysis_optimized(
    db: Session,
    start_date: datetime,
    end_date: datetime,
    limit: Optional[int] = None,
    category: Optional[str] = None,
    search: Optional[str] = None
) -> List[ProductPerformance]:
    """Analyze product performance with optimized queries."""
    logger.info(f"Calculating optimized performance analysis, limit: {limit if limit else 'ALL PRODUCTS'}")

    try:
        # Optimized query to get products with sales data
        query = select(
            Product.id,
            Product.name,
            Product.barcode,
            Product.category,
            Product.quantity,
            Product.min_quantity,
            func.coalesce(func.sum(SaleItem.quantity), 0).label('total_sold'),
            func.coalesce(func.sum(SaleItem.subtotal), 0).label('total_revenue'),
            func.coalesce(func.sum((SaleItem.unit_price - Product.cost_price) * SaleItem.quantity), 0).label('total_profit')
        ).select_from(Product)\
        .outerjoin(SaleItem, Product.id == SaleItem.product_id)\
        .outerjoin(Sale, and_(
            SaleItem.sale_id == Sale.id,
            Sale.created_at.between(start_date, end_date)
        ))\
        .where(Product.is_active == True)

        # Add category filter if provided
        if category:
            query = query.where(Product.category == category)

        # Add search filter if provided
        if search:
            query = query.where(
                (Product.name.ilike(f"%{search}%")) |
                (Product.barcode.ilike(f"%{search}%"))
            )

        query = query.group_by(Product.id, Product.name, Product.barcode, Product.category, Product.quantity, Product.min_quantity)\
        .order_by(func.coalesce(func.sum(SaleItem.quantity), 0).desc())

        # Apply limit only if specified
        if limit is not None:
            query = query.limit(limit)

        results = db.execute(query).all()
        period_days = (end_date - start_date).days or 1

        performance_analysis = []
        for row in results:
            total_sold = float(row.total_sold or 0)
            total_revenue = float(row.total_revenue or 0)
            total_profit = float(row.total_profit or 0)

            daily_avg_sales = total_sold / period_days

            # Enhanced trend analysis with actual time-based trend calculation
            if total_sold == 0:
                sales_trend = 'no_sales'
                performance_score = 0.0
            else:
                # Calculate actual trend by analyzing sales distribution over time
                sales_trend, trend_strength = await _calculate_sales_trend(db, row.id, start_date, end_date)

                # Assign performance score based on both volume and trend
                if daily_avg_sales >= 2.0:  # High volume
                    if sales_trend == 'increasing':
                        performance_score = 85.0
                    elif sales_trend == 'stable':
                        performance_score = 80.0
                    else:  # decreasing
                        performance_score = 70.0
                elif daily_avg_sales >= 0.5:  # Medium volume
                    if sales_trend == 'increasing':
                        performance_score = 75.0
                    elif sales_trend == 'stable':
                        performance_score = 65.0
                    else:  # decreasing
                        performance_score = 55.0
                else:  # Low volume (< 0.5 units per day)
                    if sales_trend == 'increasing':
                        performance_score = 50.0
                    elif sales_trend == 'stable':
                        performance_score = 35.0
                    else:  # decreasing
                        performance_score = 25.0

            # Simplified stock status
            if row.quantity == 0:
                stock_status = 'out_of_stock'
            elif row.quantity <= row.min_quantity:
                stock_status = 'low'
            elif daily_avg_sales > 0 and (row.quantity / daily_avg_sales) > 90:
                stock_status = 'overstocked'
            else:
                stock_status = 'healthy'

            # Enhanced recommendations based on trend and volume
            if sales_trend == 'no_sales':
                if row.quantity > row.min_quantity * 3:
                    recommendation = 'تصفية عاجلة - مخزون راكد'
                else:
                    recommendation = 'مراقبة الأداء وتحسين التسويق'
            elif sales_trend == 'increasing':
                if daily_avg_sales >= 2.0:
                    if stock_status == 'low':
                        recommendation = 'يحتاج إعادة تخزين عاجل'
                    else:
                        recommendation = 'أداء ممتاز - حافظ على المستوى'
                elif daily_avg_sales >= 0.5:
                    recommendation = 'أداء جيد - فكر في زيادة التسويق'
                else:
                    recommendation = 'اتجاه إيجابي - عزز التسويق'
            elif sales_trend == 'stable':
                if daily_avg_sales >= 2.0:
                    recommendation = 'أداء ممتاز - حافظ على المستوى'
                elif daily_avg_sales >= 0.5:
                    recommendation = 'أداء جيد - مراقبة مستمرة'
                else:
                    recommendation = 'مراقبة الأداء وتحسين التسويق'
            elif sales_trend == 'decreasing':
                if daily_avg_sales >= 2.0:
                    recommendation = 'انتبه - المبيعات تتراجع رغم الحجم العالي'
                elif daily_avg_sales >= 0.5:
                    recommendation = 'تحسين عاجل - المبيعات في تراجع'
                else:
                    recommendation = 'مراجعة شاملة - أداء ضعيف ومتراجع'
            else:
                recommendation = 'مراقبة الأداء وتحسين التسويق'

            # Override for stock issues
            if stock_status == 'low' and sales_trend in ['increasing', 'stable']:
                recommendation = 'يحتاج إعادة تخزين عاجل'
            elif stock_status == 'overstocked':
                recommendation = 'تقليل الطلبات المستقبلية'

            performance_analysis.append(ProductPerformance(
                id=row.id,
                name=row.name,
                barcode=row.barcode,
                category=row.category,
                sales_trend=sales_trend,
                performance_score=performance_score,
                total_sold=int(total_sold),
                total_revenue=round(total_revenue, 2),
                total_profit=round(total_profit, 2),
                stock_status=stock_status,
                recommendation=recommendation
            ))



        return performance_analysis

    except Exception as e:
        logger.error(f"Error in optimized performance analysis: {str(e)}")
        raise

async def _calculate_sales_trend(db: Session, product_id: int, start_date: datetime, end_date: datetime) -> tuple[str, float]:
    """Calculate actual sales trend by analyzing time-based sales distribution."""
    try:
        # Split the period into segments to analyze trend
        period_days = (end_date - start_date).days
        if period_days < 7:
            # Too short period for trend analysis
            return 'stable', 0.0

        # Divide period into 3 segments for trend analysis
        segment_days = period_days // 3

        # Calculate sales for each segment
        segment1_end = start_date + timedelta(days=segment_days)
        segment2_end = start_date + timedelta(days=segment_days * 2)

        # Segment 1 (earliest)
        segment1_sales = db.execute(
            select(func.coalesce(func.sum(SaleItem.quantity), 0))
            .select_from(SaleItem)
            .join(Sale, SaleItem.sale_id == Sale.id)
            .where(and_(
                SaleItem.product_id == product_id,
                Sale.created_at.between(start_date, segment1_end)
            ))
        ).scalar() or 0

        # Segment 2 (middle)
        segment2_sales = db.execute(
            select(func.coalesce(func.sum(SaleItem.quantity), 0))
            .select_from(SaleItem)
            .join(Sale, SaleItem.sale_id == Sale.id)
            .where(and_(
                SaleItem.product_id == product_id,
                Sale.created_at.between(segment1_end, segment2_end)
            ))
        ).scalar() or 0

        # Segment 3 (latest)
        segment3_sales = db.execute(
            select(func.coalesce(func.sum(SaleItem.quantity), 0))
            .select_from(SaleItem)
            .join(Sale, SaleItem.sale_id == Sale.id)
            .where(and_(
                SaleItem.product_id == product_id,
                Sale.created_at.between(segment2_end, end_date)
            ))
        ).scalar() or 0

        # Calculate trend
        segments = [float(segment1_sales), float(segment2_sales), float(segment3_sales)]

        # Calculate trend strength (slope)
        if sum(segments) == 0:
            return 'no_sales', 0.0

        # Simple linear trend calculation
        trend_slope = (segments[2] - segments[0]) / 2 if segments[0] > 0 else 0

        # Determine trend direction
        if segments[2] > segments[1] > segments[0]:
            return 'increasing', abs(trend_slope)
        elif segments[2] < segments[1] < segments[0]:
            return 'decreasing', abs(trend_slope)
        elif segments[2] > segments[0] * 1.2:  # 20% increase from first to last
            return 'increasing', abs(trend_slope)
        elif segments[2] < segments[0] * 0.8:  # 20% decrease from first to last
            return 'decreasing', abs(trend_slope)
        else:
            return 'stable', abs(trend_slope)

    except Exception as e:
        logger.error(f"Error calculating sales trend for product {product_id}: {str(e)}")
        return 'stable', 0.0

async def _get_performance_analysis(
    db: Session,
    start_date: datetime,
    end_date: datetime
) -> List[ProductPerformance]:
    """Analyze product performance with enhanced metrics."""

    logger.info(f"Analyzing product performance from {start_date} to {end_date}")

    # Get all active products with their sales data using precise calculations
    query = select(
        Product.id,
        Product.name,
        Product.category,
        Product.quantity,
        Product.min_quantity,
        Product.cost_price,
        func.coalesce(func.sum(cast(SaleItem.quantity, Float)), 0).label('total_sold'),
        func.coalesce(func.sum(cast(SaleItem.subtotal, Float)), 0).label('total_revenue'),
        func.coalesce(func.sum(cast((SaleItem.unit_price - Product.cost_price) * SaleItem.quantity, Float)), 0).label('total_profit')
    ).select_from(Product)\
    .outerjoin(SaleItem, Product.id == SaleItem.product_id)\
    .outerjoin(Sale, and_(
        SaleItem.sale_id == Sale.id,
        Sale.created_at >= start_date,
        Sale.created_at <= end_date
    ))\
    .where(Product.is_active == True)\
    .group_by(Product.id, Product.name, Product.category, Product.quantity, Product.min_quantity, Product.cost_price)

    results = db.execute(query).all()
    logger.info(f"Analyzing {len(results)} products")

    # Calculate period length for better trend analysis
    period_days = (end_date - start_date).days

    performance_analysis = []
    for row in results:
        # Use Decimal for precise calculations
        total_sold = Decimal(str(row.total_sold or 0))
        total_revenue = Decimal(str(row.total_revenue or 0))
        total_profit = Decimal(str(row.total_profit or 0))

        # Calculate daily average sales
        daily_avg_sales = float(total_sold / period_days) if period_days > 0 else 0

        # Enhanced sales trend analysis based on daily averages
        if total_sold == 0:
            sales_trend = 'no_sales'
            performance_score = 0.0
        elif daily_avg_sales >= 2.0:  # 2+ units per day
            sales_trend = 'increasing'
            performance_score = 85.0
        elif daily_avg_sales >= 0.5:  # 0.5-2 units per day
            sales_trend = 'stable'
            performance_score = 65.0
        elif daily_avg_sales > 0:  # Less than 0.5 units per day
            sales_trend = 'decreasing'
            performance_score = 35.0
        else:
            sales_trend = 'no_sales'
            performance_score = 0.0

        # Enhanced stock status analysis
        if row.quantity == 0:
            stock_status = 'out_of_stock'
        elif row.quantity <= row.min_quantity:
            stock_status = 'low'
        elif daily_avg_sales > 0:
            # Calculate days of supply
            days_of_supply = row.quantity / daily_avg_sales
            if days_of_supply > 90:  # More than 3 months supply
                stock_status = 'overstocked'
            else:
                stock_status = 'healthy'
        elif row.quantity > row.min_quantity * 5:  # Fallback for products with no sales
            stock_status = 'overstocked'
        else:
            stock_status = 'healthy'

        # Enhanced recommendations based on multiple factors
        if sales_trend == 'no_sales' and row.quantity > 0:
            if row.quantity > row.min_quantity * 3:
                recommendation = 'تصفية عاجلة - مخزون راكد'
            else:
                recommendation = 'فكر في تخفيض السعر أو عروض ترويجية'
        elif stock_status == 'low' and sales_trend in ['increasing', 'stable']:
            recommendation = 'يحتاج إعادة تخزين عاجل'
        elif stock_status == 'overstocked':
            if sales_trend == 'no_sales':
                recommendation = 'إيقاف الطلبات وتصفية المخزون'
            else:
                recommendation = 'تقليل الطلبات المستقبلية'
        elif sales_trend == 'increasing':
            recommendation = 'أداء ممتاز - حافظ على المستوى'
        elif sales_trend == 'stable':
            recommendation = 'أداء جيد - مراقبة مستمرة'
        else:
            recommendation = 'مراقبة الأداء وتحسين التسويق'

        performance_analysis.append(ProductPerformance(
            id=row.id,
            name=row.name,
            barcode=row.barcode,
            category=row.category,
            sales_trend=sales_trend,
            performance_score=performance_score,
            total_sold=int(total_sold),
            total_revenue=float(total_revenue),
            total_profit=float(total_profit),
            stock_status=stock_status,
            recommendation=recommendation
        ))

        logger.debug(f"Product {row.name}: trend={sales_trend}, score={performance_score}, stock={stock_status}")

    return performance_analysis

async def _get_expected_losses_optimized(
    db: Session,
    start_date: datetime,
    end_date: datetime,
    limit: int = 15
) -> List[ExpectedLoss]:
    """Calculate expected losses with optimized performance and accurate days calculation."""
    period_days = (end_date - start_date).days
    logger.info(f"Calculating optimized expected losses for {period_days} days period, limit: {limit}")

    try:
        # Optimized query for products with no recent sales
        sold_products_subquery = select(func.distinct(SaleItem.product_id))\
            .select_from(SaleItem)\
            .join(Sale, SaleItem.sale_id == Sale.id)\
            .where(Sale.created_at >= start_date)

        query = select(Product)\
            .where(and_(
                Product.is_active == True,
                Product.quantity > 0,
                ~Product.id.in_(sold_products_subquery)
            ))\
            .order_by((Product.cost_price * Product.quantity).desc())\
            .limit(limit)

        results = db.execute(query).all()

        expected_losses = []
        current_time = get_tripoli_now()

        for row in results:
            product = row[0]

            # Use the helper function to calculate days without sales accurately
            days_without_sales = _calculate_days_without_sales(
                db, product.id, current_time, product.created_at
            )

            cost_value = float(product.cost_price * product.quantity)

            # Use the helper function to calculate loss metrics based on period
            loss_percentage, loss_category, recommendation, estimated_loss = _calculate_loss_metrics(
                days_without_sales, cost_value, period_days
            )

            expected_losses.append(ExpectedLoss(
                product_id=product.id,
                product_name=product.name,
                product_barcode=product.barcode,
                category=product.category,
                current_stock=product.quantity,
                days_without_sales=days_without_sales,
                estimated_loss_amount=round(estimated_loss, 2),
                loss_category=loss_category,
                recommendation=recommendation
            ))

        return expected_losses

    except Exception as e:
        logger.error(f"Error in optimized expected losses: {str(e)}")
        raise

async def _get_expected_losses(
    db: Session,
    start_date: datetime,
    end_date: datetime,
    search: Optional[str] = None
) -> List[ExpectedLoss]:
    """Calculate expected losses from slow-moving inventory with accurate days calculation."""
    period_days = (end_date - start_date).days
    logger.info(f"Calculating expected losses for {period_days} days period")

    # Get products with no recent sales
    sold_products_subquery = select(func.distinct(SaleItem.product_id))\
        .select_from(SaleItem)\
        .join(Sale, SaleItem.sale_id == Sale.id)\
        .where(Sale.created_at >= start_date)

    query = select(Product)\
        .where(and_(
            Product.is_active == True,
            Product.quantity > 0,
            ~Product.id.in_(sold_products_subquery)
        ))

    # Add search filter if provided
    if search:
        query = query.where(
            (Product.name.ilike(f"%{search}%")) |
            (Product.barcode.ilike(f"%{search}%"))
        )

    results = db.execute(query).all()

    expected_losses = []
    current_time = get_tripoli_now()
    for row in results:
        product = row[0]  # Extract product from result row

        # Use the helper function to calculate days without sales accurately
        days_without_sales = _calculate_days_without_sales(
            db, product.id, current_time, product.created_at
        )

        cost_value = product.cost_price * product.quantity

        # Use the helper function to calculate loss metrics based on period
        loss_percentage, loss_category, recommendation, estimated_loss = _calculate_loss_metrics(
            days_without_sales, float(cost_value), period_days
        )

        expected_losses.append(ExpectedLoss(
            product_id=product.id,
            product_name=product.name,
            product_barcode=product.barcode,
            category=product.category,
            current_stock=product.quantity,
            days_without_sales=days_without_sales,
            estimated_loss_amount=estimated_loss,
            loss_category=loss_category,
            recommendation=recommendation
        ))

    return expected_losses

async def _get_inventory_status_optimized(
    db: Session,
    limit: int = 20,
    category: Optional[str] = None
) -> List[InventoryStatus]:
    """Get inventory status with optimized performance."""
    logger.info(f"Calculating optimized inventory status, limit: {limit}")

    try:
        query = select(Product).where(Product.is_active == True)

        if category:
            query = query.where(Product.category == category)

        query = query.order_by(Product.quantity.desc()).limit(limit)
        results = db.execute(query).all()

        inventory_status = []
        thirty_days_ago = get_tripoli_now() - timedelta(days=30)

        for row in results:
            product = row[0]

            # Simplified sales calculation (last 30 days only)
            sales_30 = db.execute(
                select(func.coalesce(func.sum(SaleItem.quantity), 0))
                .select_from(SaleItem)
                .join(Sale, SaleItem.sale_id == Sale.id)
                .where(and_(
                    SaleItem.product_id == product.id,
                    Sale.created_at >= thirty_days_ago
                ))
            ).scalar() or 0

            avg_daily_sales = float(sales_30) / 30.0

            # Calculate days of supply
            if avg_daily_sales > 0:
                days_of_supply = round(product.quantity / avg_daily_sales, 1)
            else:
                days_of_supply = 999.0 if product.quantity > 0 else 0.0

            # Simplified stock status
            if product.quantity == 0:
                stock_status = 'out_of_stock'
            elif product.quantity <= product.min_quantity:
                stock_status = 'low'
            elif days_of_supply > 90:
                stock_status = 'overstocked'
            else:
                stock_status = 'healthy'

            # Simplified calculations
            reorder_point = max(product.min_quantity, int(avg_daily_sales * 7))
            suggested_order = max(0, int(avg_daily_sales * 30) - product.quantity)
            max_recommended = max(int(avg_daily_sales * 60), product.min_quantity * 2)

            inventory_status.append(InventoryStatus(
                product_id=product.id,
                product_name=product.name,
                product_barcode=product.barcode,
                category=product.category,
                current_stock=product.quantity,
                min_quantity=product.min_quantity,
                max_recommended=max_recommended,
                stock_status=stock_status,
                days_of_supply=days_of_supply,
                reorder_point=reorder_point,
                suggested_order_quantity=suggested_order
            ))

        return inventory_status

    except Exception as e:
        logger.error(f"Error in optimized inventory status: {str(e)}")
        raise

async def _get_inventory_status(
    db: Session,
    category: Optional[str] = None,
    search: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None
) -> List[InventoryStatus]:
    """Get current inventory status with enhanced recommendations based on sales period."""

    # Use provided dates or default to 30 days
    # Create local variables with guaranteed datetime types
    effective_end_date: datetime = end_date if end_date is not None else get_tripoli_now()

    # Handle start_date calculation separately to avoid type issues
    if start_date is not None:
        effective_start_date: datetime = start_date
    else:
        effective_start_date = typing_cast(datetime, effective_end_date - timedelta(days=30))

    period_days = (effective_end_date - effective_start_date).days
    logger.info(f"Calculating inventory status for category: {category or 'all'} over {period_days} days")

    query = select(Product).where(Product.is_active == True)

    if category:
        query = query.where(Product.category == category)

    # Add search filter if provided
    if search:
        query = query.where(
            (Product.name.ilike(f"%{search}%")) |
            (Product.barcode.ilike(f"%{search}%"))
        )

    results = db.execute(query).all()
    logger.info(f"Analyzing inventory for {len(results)} products")

    inventory_status = []
    for row in results:
        product = row[0]  # Extract product from result row

        # Calculate days of supply based on sales in the specified period
        # Also get a longer period for better trend analysis
        longer_start = effective_end_date - timedelta(days=max(60, period_days * 2))

        # Get sales data for the specified period
        sales_period_query = select(
            func.coalesce(func.sum(cast(SaleItem.quantity, Float)), 0)
        ).select_from(SaleItem)\
        .join(Sale, SaleItem.sale_id == Sale.id)\
        .where(and_(
            SaleItem.product_id == product.id,
            Sale.created_at >= effective_start_date,
            Sale.created_at <= effective_end_date
        ))

        # Get sales data for longer period for trend analysis
        sales_longer_query = select(
            func.coalesce(func.sum(cast(SaleItem.quantity, Float)), 0)
        ).select_from(SaleItem)\
        .join(Sale, SaleItem.sale_id == Sale.id)\
        .where(and_(
            SaleItem.product_id == product.id,
            Sale.created_at >= longer_start,
            Sale.created_at <= effective_end_date
        ))

        sales_period = float(db.execute(sales_period_query).scalar() or 0)
        sales_longer = float(db.execute(sales_longer_query).scalar() or 0)

        # Calculate average daily sales for both periods
        avg_daily_period = sales_period / period_days if period_days > 0 else 0.0
        # Use effective dates for calculations
        longer_period_days = (effective_end_date - longer_start).days
        avg_daily_longer = sales_longer / longer_period_days if longer_period_days > 0 else 0.0

        # Use weighted average: more weight to recent period if it has sales
        if sales_period > 0:
            avg_daily_sales = (avg_daily_period * 0.7) + (avg_daily_longer * 0.3)
        elif sales_longer > 0:
            avg_daily_sales = avg_daily_longer
        else:
            avg_daily_sales = 0.0

        # Calculate days of supply with precision
        if avg_daily_sales > 0:
            days_of_supply = float(Decimal(str(product.quantity)) / Decimal(str(avg_daily_sales)))
            days_of_supply = float(Decimal(str(days_of_supply)).quantize(Decimal('0.1'), rounding=ROUND_HALF_UP))
        else:
            days_of_supply = 999.0 if product.quantity > 0 else 0.0

        # Enhanced stock status determination
        if product.quantity == 0:
            stock_status = 'out_of_stock'
        elif product.quantity <= product.min_quantity:
            stock_status = 'low'
        elif avg_daily_sales > 0:
            if days_of_supply > 120:  # More than 4 months supply
                stock_status = 'overstocked'
            elif days_of_supply < 7:  # Less than 1 week supply
                stock_status = 'low'
            else:
                stock_status = 'healthy'
        elif product.quantity > product.min_quantity * 4:  # Fallback for products with no sales
            stock_status = 'overstocked'
        else:
            stock_status = 'healthy'

        # Enhanced reorder calculations
        # Reorder point = (average daily sales * lead time) + safety stock
        lead_time_days = 7  # Assume 1 week lead time
        safety_stock = max(product.min_quantity, int(avg_daily_sales * 3))  # 3 days safety stock
        reorder_point = int((avg_daily_sales * lead_time_days) + safety_stock)

        # Suggested order quantity based on optimal stock level
        order_cycle_days = 30  # Monthly ordering
        optimal_stock = int(avg_daily_sales * (order_cycle_days + lead_time_days))
        suggested_order = max(0, optimal_stock - product.quantity)

        # Calculate max recommended stock (2 months supply or minimum based on min_quantity)
        if avg_daily_sales > 0:
            max_recommended = max(int(avg_daily_sales * 60), product.min_quantity * 2)
        else:
            max_recommended = product.min_quantity * 3

        inventory_status.append(InventoryStatus(
            product_id=product.id,
            product_name=product.name,
            product_barcode=product.barcode,
            category=product.category,
            current_stock=product.quantity,
            min_quantity=product.min_quantity,
            max_recommended=max_recommended,
            stock_status=stock_status,
            days_of_supply=days_of_supply,
            reorder_point=reorder_point,
            suggested_order_quantity=suggested_order
        ))

        logger.debug(f"Product {product.name}: stock={product.quantity}, days_supply={days_of_supply}, status={stock_status}")

    return inventory_status
