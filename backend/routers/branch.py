"""
API endpoints للفروع - Branch Router
يوفر جميع العمليات المطلوبة لإدارة الفروع
"""

import logging
from typing import Dict, Any, List
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from database.session import get_db
from auth.dependencies import get_current_user
from models.user import User
from services.branch_service import get_branch_service
from schemas.branch import (
    BranchCreate, BranchUpdate, BranchResponse, BranchListResponse,
    BranchSearchParams, BranchStatistics
)

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/api/branches",
    tags=["branches"],
    responses={404: {"description": "الفرع غير موجود"}}
)


@router.post("/", response_model=Dict[str, Any], status_code=status.HTTP_201_CREATED)
async def create_branch(
    branch_data: BranchCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    إنشاء فرع جديد
    
    - **name**: اسم الفرع (مطلوب)
    - **code**: كود الفرع الفريد (مطلوب)
    - **address**: عنوان الفرع
    - **phone**: رقم هاتف الفرع
    - **manager_name**: اسم مدير الفرع
    - **email**: البريد الإلكتروني للفرع
    - **is_active**: حالة نشاط الفرع (افتراضي: true)
    - **is_main**: هل هذا الفرع الرئيسي (افتراضي: false)
    """
    try:
        branch_service = get_branch_service(db)
        
        # إضافة معرف المستخدم الذي أنشأ الفرع
        branch_dict = branch_data.dict()
        branch_dict['created_by'] = current_user.id
        
        result = branch_service.create_branch(branch_dict)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        logger.info(f"تم إنشاء فرع جديد بواسطة {current_user.username}: {result['branch']['name']}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في إنشاء الفرع: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.get("/", response_model=Dict[str, Any])
async def get_all_branches(
    include_inactive: bool = Query(False, description="تضمين الفروع غير النشطة"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    الحصول على جميع الفروع
    
    - **include_inactive**: تضمين الفروع غير النشطة (افتراضي: false)
    """
    try:
        branch_service = get_branch_service(db)
        result = branch_service.get_all_branches(include_inactive=include_inactive)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب الفروع: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.get("/{branch_id}", response_model=Dict[str, Any])
async def get_branch_by_id(
    branch_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    الحصول على فرع بالمعرف
    
    - **branch_id**: معرف الفرع
    """
    try:
        branch_service = get_branch_service(db)
        result = branch_service.get_branch_by_id(branch_id)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=result['error']
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب الفرع: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.get("/code/{branch_code}", response_model=Dict[str, Any])
async def get_branch_by_code(
    branch_code: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    الحصول على فرع بالكود
    
    - **branch_code**: كود الفرع
    """
    try:
        branch_service = get_branch_service(db)
        result = branch_service.get_branch_by_code(branch_code)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=result['error']
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب الفرع بالكود: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.get("/uuid/{branch_uuid}", response_model=Dict[str, Any])
async def get_branch_by_uuid(
    branch_uuid: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    الحصول على فرع بـ UUID

    - **branch_uuid**: UUID الفرع
    """
    try:
        branch_service = get_branch_service(db)
        result = branch_service.get_branch_by_uuid(branch_uuid)

        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=result['error']
            )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب الفرع بـ UUID: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.put("/{branch_id}", response_model=Dict[str, Any])
async def update_branch(
    branch_id: int,
    branch_data: BranchUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    تحديث بيانات الفرع
    
    - **branch_id**: معرف الفرع
    - جميع الحقول اختيارية للتحديث
    """
    try:
        branch_service = get_branch_service(db)
        
        # تحويل البيانات وإزالة القيم الفارغة
        update_data = {k: v for k, v in branch_data.dict().items() if v is not None}
        update_data['updated_by'] = current_user.id
        
        result = branch_service.update_branch(branch_id, update_data)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        logger.info(f"تم تحديث الفرع {branch_id} بواسطة {current_user.username}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في تحديث الفرع: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.delete("/{branch_id}", response_model=Dict[str, Any])
async def delete_branch(
    branch_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    حذف الفرع
    
    - **branch_id**: معرف الفرع
    
    ملاحظة: لا يمكن حذف الفرع إذا كان:
    - مرتبط بمستودعات
    - فرع رئيسي
    """
    try:
        branch_service = get_branch_service(db)
        result = branch_service.delete_branch(branch_id)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        logger.info(f"تم حذف الفرع {branch_id} بواسطة {current_user.username}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في حذف الفرع: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.post("/{branch_id}/set-main", response_model=Dict[str, Any])
async def set_main_branch(
    branch_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    تعيين الفرع الرئيسي
    
    - **branch_id**: معرف الفرع
    
    سيتم إلغاء الرئيسية من جميع الفروع الأخرى
    """
    try:
        branch_service = get_branch_service(db)
        result = branch_service.set_main_branch(branch_id)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        logger.info(f"تم تعيين الفرع الرئيسي {branch_id} بواسطة {current_user.username}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في تعيين الفرع الرئيسي: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.post("/{branch_id}/toggle-status", response_model=Dict[str, Any])
async def toggle_branch_status(
    branch_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    تبديل حالة نشاط الفرع
    
    - **branch_id**: معرف الفرع
    
    ملاحظة: لا يمكن إلغاء تفعيل الفرع الرئيسي
    """
    try:
        branch_service = get_branch_service(db)
        result = branch_service.toggle_branch_status(branch_id)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        logger.info(f"تم تبديل حالة الفرع {branch_id} بواسطة {current_user.username}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في تبديل حالة الفرع: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.get("/search/", response_model=Dict[str, Any])
async def search_branches(
    search_term: str = Query(..., description="مصطلح البحث"),
    include_inactive: bool = Query(False, description="تضمين الفروع غير النشطة"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    البحث في الفروع

    - **search_term**: مصطلح البحث (يبحث في الاسم، الكود، العنوان، مدير الفرع، المدينة، المنطقة)
    - **include_inactive**: تضمين الفروع غير النشطة
    """
    try:
        branch_service = get_branch_service(db)
        result = branch_service.search_branches(search_term, include_inactive)

        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في البحث في الفروع: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )
