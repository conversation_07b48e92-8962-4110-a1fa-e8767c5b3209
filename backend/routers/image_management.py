"""
API endpoints لخدمة إدارة الصور
"""

import logging
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Query
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel

from database.session import get_db
from utils.auth import get_current_active_user
from models.user import User
from services.image_management_service import ImageManagementService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/images", tags=["Image Management"])


# Pydantic Models
class ImageUploadResponse(BaseModel):
    success: bool
    filename: Optional[str] = None
    file_path: Optional[str] = None
    file_size: Optional[int] = None
    folder: Optional[str] = None
    upload_time: Optional[str] = None
    thumbnails: Optional[dict] = None
    error: Optional[str] = None
    message: Optional[str] = None


class ImageDeleteResponse(BaseModel):
    success: bool
    deleted_files: Optional[List[str]] = None
    message: Optional[str] = None
    error: Optional[str] = None


class ImageInfoResponse(BaseModel):
    success: bool
    file_path: Optional[str] = None
    filename: Optional[str] = None
    file_size: Optional[int] = None
    created_time: Optional[str] = None
    modified_time: Optional[str] = None
    image_format: Optional[str] = None
    image_mode: Optional[str] = None
    image_size: Optional[tuple] = None
    width: Optional[int] = None
    height: Optional[int] = None
    has_transparency: Optional[bool] = None
    thumbnails: Optional[dict] = None
    error: Optional[str] = None


class ImageListResponse(BaseModel):
    success: bool
    folder: Optional[str] = None
    total_images: Optional[int] = None
    images: Optional[List[dict]] = None
    error: Optional[str] = None


class StorageStatsResponse(BaseModel):
    success: bool
    total_size: Optional[int] = None
    total_files: Optional[int] = None
    total_size_formatted: Optional[str] = None
    folders: Optional[dict] = None
    error: Optional[str] = None


class SupportedFormatsResponse(BaseModel):
    success: bool
    supported_extensions: Optional[List[str]] = None
    max_file_size: Optional[int] = None
    max_file_size_formatted: Optional[str] = None
    thumbnail_sizes: Optional[dict] = None
    upload_directory: Optional[str] = None


@router.post("/upload/{folder}", response_model=ImageUploadResponse)
async def upload_image(
    folder: str,
    file: UploadFile = File(...),
    generate_thumbnails: bool = Query(True, description="إنشاء صور مصغرة"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    رفع صورة جديدة مع إنشاء الصور المصغرة
    """
    try:
        # التحقق من صحة اسم المجلد
        allowed_folders = ['products', 'categories', 'users', 'brands', 'customers', 'general']
        if folder not in allowed_folders:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"مجلد غير مدعوم. المجلدات المدعومة: {', '.join(allowed_folders)}"
            )

        # إنشاء مثيل من خدمة إدارة الصور
        image_service = ImageManagementService.get_instance(db)
        
        # رفع الصورة
        result = image_service.save_image(file, folder, generate_thumbnails)
        
        if result["success"]:
            logger.info(f"تم رفع صورة جديدة بواسطة {current_user.username}: {result.get('file_path')}")
        
        return ImageUploadResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في رفع الصورة: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"فشل في رفع الصورة: {str(e)}"
        )


@router.delete("/delete", response_model=ImageDeleteResponse)
async def delete_image(
    file_path: str = Query(..., description="مسار الملف المراد حذفه"),
    delete_thumbnails: bool = Query(True, description="حذف الصور المصغرة"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    حذف صورة والصور المصغرة المرتبطة بها
    """
    try:
        # إنشاء مثيل من خدمة إدارة الصور
        image_service = ImageManagementService.get_instance(db)
        
        # حذف الصورة
        result = image_service.delete_image(file_path, delete_thumbnails)
        
        if result["success"]:
            logger.info(f"تم حذف صورة بواسطة {current_user.username}: {file_path}")
        
        return ImageDeleteResponse(**result)

    except Exception as e:
        logger.error(f"خطأ في حذف الصورة: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"فشل في حذف الصورة: {str(e)}"
        )


@router.get("/info", response_model=ImageInfoResponse)
async def get_image_info(
    file_path: str = Query(..., description="مسار الملف"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    الحصول على معلومات صورة معينة
    """
    try:
        # إنشاء مثيل من خدمة إدارة الصور
        image_service = ImageManagementService.get_instance(db)
        
        # جلب معلومات الصورة
        result = image_service.get_image_info(file_path)
        
        return ImageInfoResponse(**result)

    except Exception as e:
        logger.error(f"خطأ في جلب معلومات الصورة: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"فشل في جلب معلومات الصورة: {str(e)}"
        )


@router.get("/list/{folder}", response_model=ImageListResponse)
async def list_images(
    folder: str,
    include_thumbnails: bool = Query(False, description="تضمين معلومات الصور المصغرة"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    جلب قائمة الصور في مجلد معين
    """
    try:
        # إنشاء مثيل من خدمة إدارة الصور
        image_service = ImageManagementService.get_instance(db)
        
        # جلب قائمة الصور
        result = image_service.list_images(folder, include_thumbnails)
        
        return ImageListResponse(**result)

    except Exception as e:
        logger.error(f"خطأ في جلب قائمة الصور: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"فشل في جلب قائمة الصور: {str(e)}"
        )


@router.post("/cleanup/{folder}")
async def cleanup_orphaned_files(
    folder: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    تنظيف الملفات المهجورة في مجلد معين
    """
    try:
        # التحقق من صلاحيات الإدارة
        user_role = getattr(current_user.role, 'value', str(current_user.role))
        if user_role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="غير مصرح لك بتنفيذ هذه العملية"
            )

        # إنشاء مثيل من خدمة إدارة الصور
        image_service = ImageManagementService.get_instance(db)
        
        # تنظيف الملفات المهجورة
        result = image_service.cleanup_orphaned_files(folder)
        
        if result["success"]:
            logger.info(f"تم تنظيف الملفات المهجورة بواسطة {current_user.username}: {folder}")
        
        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في تنظيف الملفات المهجورة: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"فشل في تنظيف الملفات المهجورة: {str(e)}"
        )


@router.get("/storage-stats", response_model=StorageStatsResponse)
async def get_storage_statistics(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    الحصول على إحصائيات التخزين
    """
    try:
        # إنشاء مثيل من خدمة إدارة الصور
        image_service = ImageManagementService.get_instance(db)
        
        # جلب إحصائيات التخزين
        result = image_service.get_storage_statistics()
        
        return StorageStatsResponse(**result)

    except Exception as e:
        logger.error(f"خطأ في جلب إحصائيات التخزين: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"فشل في جلب إحصائيات التخزين: {str(e)}"
        )


@router.post("/regenerate-thumbnails")
async def regenerate_thumbnails(
    file_path: str = Query(..., description="مسار الملف"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    إعادة إنشاء الصور المصغرة لصورة معينة
    """
    try:
        # إنشاء مثيل من خدمة إدارة الصور
        image_service = ImageManagementService.get_instance(db)
        
        # إعادة إنشاء الصور المصغرة
        result = image_service.regenerate_thumbnails(file_path)
        
        if result["success"]:
            logger.info(f"تم إعادة إنشاء الصور المصغرة بواسطة {current_user.username}: {file_path}")
        
        return result

    except Exception as e:
        logger.error(f"خطأ في إعادة إنشاء الصور المصغرة: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"فشل في إعادة إنشاء الصور المصغرة: {str(e)}"
        )


@router.get("/serve/{folder}/{filename}")
async def serve_image(
    folder: str,
    filename: str,
    size: str = Query(None, description="حجم الصورة المصغرة: small, medium, large")
):
    """
    تقديم الصور مباشرة مع التحقق من الوجود والتبديل التلقائي
    """
    try:
        from pathlib import Path
        from fastapi.responses import FileResponse
        import mimetypes

        # تحديد المسار الأساسي
        base_path = Path("static/uploads")

        if size and size in ['small', 'medium', 'large']:
            # مسار الصورة المصغرة
            file_path = base_path / folder / 'thumbnails' / size / filename
            logger.info(f"🔍 البحث عن الصورة المصغرة: {file_path}")
        else:
            # مسار الصورة الأصلية
            file_path = base_path / folder / filename
            logger.info(f"🔍 البحث عن الصورة الأصلية: {file_path}")

        # التحقق من وجود الملف
        if not file_path.exists():
            logger.warning(f"⚠️ الملف غير موجود: {file_path}")

            # إذا لم توجد الصورة المصغرة، جرب الصورة الأصلية
            if size:
                original_path = base_path / folder / filename
                logger.info(f"🔄 محاولة الصورة الأصلية: {original_path}")

                if original_path.exists():
                    logger.info(f"✅ تم العثور على الصورة الأصلية")
                    # تحديد نوع الملف
                    mime_type, _ = mimetypes.guess_type(str(original_path))
                    if not mime_type:
                        mime_type = "image/jpeg"

                    return FileResponse(
                        path=str(original_path),
                        media_type=mime_type,
                        headers={
                            "Cache-Control": "public, max-age=3600",
                            "X-Image-Source": "original"
                        }
                    )

            logger.error(f"❌ الصورة غير موجودة نهائياً: {folder}/{filename}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"الصورة غير موجودة: {folder}/{filename}"
            )

        # تحديد نوع الملف
        mime_type, _ = mimetypes.guess_type(str(file_path))
        if not mime_type:
            mime_type = "image/jpeg"

        logger.info(f"✅ تقديم الصورة: {file_path}")

        # تقديم الصورة
        return FileResponse(
            path=str(file_path),
            media_type=mime_type,
            headers={
                "Cache-Control": "public, max-age=3600",
                "X-Image-Source": "thumbnail" if size else "original"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ خطأ في تقديم الصورة: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"فشل في تقديم الصورة: {str(e)}"
        )


@router.get("/check/{folder}/{filename}")
async def check_image_exists(folder: str, filename: str):
    """
    التحقق من وجود الصورة وحالتها
    """
    try:
        from pathlib import Path

        base_path = Path("static/uploads")
        original_path = base_path / folder / filename

        result = {
            "exists": original_path.exists(),
            "original_path": str(original_path),
            "thumbnails": {}
        }

        if result["exists"]:
            # فحص الصور المصغرة
            for size in ['small', 'medium', 'large']:
                thumb_path = base_path / folder / 'thumbnails' / size / filename
                result["thumbnails"][size] = {
                    "exists": thumb_path.exists(),
                    "path": str(thumb_path)
                }

        return result

    except Exception as e:
        logger.error(f"خطأ في فحص الصورة: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"فشل في فحص الصورة: {str(e)}"
        )


@router.get("/supported-formats", response_model=SupportedFormatsResponse)
async def get_supported_formats():
    """
    الحصول على قائمة الصيغ المدعومة
    """
    try:
        # إنشاء مثيل من خدمة إدارة الصور
        image_service = ImageManagementService.get_instance()

        # جلب الصيغ المدعومة
        result = image_service.get_supported_formats()

        return SupportedFormatsResponse(**result)

    except Exception as e:
        logger.error(f"خطأ في جلب الصيغ المدعومة: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"فشل في جلب الصيغ المدعومة: {str(e)}"
        )
