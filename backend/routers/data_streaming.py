"""
نظام تدفق البيانات الكبيرة لتطبيق SmartPOS
يوفر APIs محسنة للتعامل مع البيانات الكبيرة بكفاءة عالية
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from sqlalchemy import select, func, and_, desc
from typing import Dict, Any, Optional, Generator
from datetime import datetime, date
from pathlib import Path
import json
import io
import gzip
import logging

from database.session import get_db
from models.sale import Sale, SaleItem
from models.product import Product
from models.customer import Customer, CustomerDebt
from models.user import User
from utils.auth import get_current_active_user

router = APIRouter(prefix="/api/data-streaming", tags=["data-streaming"])
logger = logging.getLogger(__name__)

def safe_float(value, default=0.0):
    """تحويل آمن للقيم إلى float"""
    try:
        if value is None:
            return default
        return float(value)
    except (ValueError, TypeError):
        return default

def safe_str(value, default=""):
    """تحويل آمن للقيم إلى string"""
    try:
        if value is None:
            return default
        return str(value)
    except (ValueError, TypeError):
        return default

def safe_datetime_iso(value):
    """تحويل آمن للتاريخ إلى ISO format"""
    try:
        if value is None:
            return None
        if hasattr(value, 'isoformat'):
            return value.isoformat()
        return str(value)
    except (ValueError, TypeError, AttributeError):
        return None

def safe_int(value, default=0):
    """تحويل آمن للقيم إلى int"""
    try:
        if value is None:
            return default
        return int(value)
    except (ValueError, TypeError):
        return default

class DataStreamer:
    """
    خدمة تدفق البيانات الكبيرة
    تدعم التدفق المستمر والضغط والتصدير
    """
    
    def __init__(self, db: Session):
        self.db = db
        self.chunk_size = 1000  # حجم الدفعة الواحدة
    
    def stream_sales_data(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        user_id: Optional[int] = None,
        format_type: str = "json"
    ) -> Generator[str, None, None]:
        """
        تدفق بيانات المبيعات بدفعات محسنة
        """
        try:
            # بناء الاستعلام الأساسي
            query = select(Sale).order_by(desc(Sale.created_at))
            
            # تطبيق الفلاتر
            conditions = []
            if start_date:
                conditions.append(Sale.created_at >= start_date)
            if end_date:
                conditions.append(Sale.created_at <= end_date)
            if user_id:
                conditions.append(Sale.user_id == user_id)
            
            if conditions:
                query = query.where(and_(*conditions))
            
            # حساب العدد الإجمالي
            count_query = select(func.count(Sale.id))
            if conditions:
                count_query = count_query.where(and_(*conditions))
            
            total_count = self.db.execute(count_query).scalar() or 0
            
            # إرسال معلومات البداية
            if format_type == "json":
                yield f'{{"total_count": {total_count}, "data": ['
            elif format_type == "csv":
                yield "id,user_id,customer_id,total_amount,payment_method,tax_amount,discount_amount,customer_name,notes,amount_paid,payment_status,created_at\n"
            
            # تدفق البيانات بدفعات
            offset = 0
            first_batch = True
            
            while offset < total_count:
                batch_query = query.offset(offset).limit(self.chunk_size)
                sales = self.db.execute(batch_query).scalars().all()
                
                if not sales:
                    break
                
                for i, sale in enumerate(sales):
                    if format_type == "json":
                        if not first_batch or i > 0:
                            yield ","
                        
                        sale_data = {
                            "id": sale.id,
                            "user_id": sale.user_id,
                            "customer_id": sale.customer_id,
                            "total_amount": safe_float(sale.total_amount),
                            "payment_method": sale.payment_method,
                            "tax_amount": safe_float(sale.tax_amount),
                            "discount_amount": safe_float(sale.discount_amount),
                            "customer_name": sale.customer_name,
                            "notes": sale.notes,
                            "amount_paid": safe_float(sale.amount_paid),
                            "payment_status": sale.payment_status,
                            "created_at": safe_datetime_iso(sale.created_at)
                        }
                        yield json.dumps(sale_data, ensure_ascii=False)
                        
                    elif format_type == "csv":
                        csv_row = f"{sale.id},{sale.user_id},{sale.customer_id or ''},{safe_float(sale.total_amount)},{sale.payment_method},{safe_float(sale.tax_amount)},{safe_float(sale.discount_amount)},\"{sale.customer_name or ''}\",\"{sale.notes or ''}\",{safe_float(sale.amount_paid)},{sale.payment_status},{safe_datetime_iso(sale.created_at) or ''}\n"
                        yield csv_row
                
                offset += self.chunk_size
                first_batch = False
                
                # تحديث التقدم
                progress = min(100, (offset / total_count) * 100)
                logger.info(f"Streaming progress: {progress:.1f}% ({offset}/{total_count})")
            
            # إنهاء التدفق
            if format_type == "json":
                yield "]}"
                
        except Exception as e:
            logger.error(f"Error in stream_sales_data: {e}")
            if format_type == "json":
                yield f'{{"error": "خطأ في تدفق البيانات: {str(e)}"}}'
            else:
                yield f"Error: {str(e)}\n"
    
    def stream_products_data(
        self,
        category: Optional[str] = None,
        low_stock: bool = False,
        format_type: str = "json"
    ) -> Generator[str, None, None]:
        """
        تدفق بيانات المنتجات مع التحليلات
        """
        try:
            # بناء الاستعلام مع التحليلات
            query = select(
                Product.id,
                Product.name,
                Product.barcode,
                Product.category,
                Product.price,
                Product.cost_price,
                Product.quantity,
                Product.min_quantity,
                Product.unit,
                Product.is_active,
                Product.created_at,
                func.coalesce(func.sum(SaleItem.quantity), 0).label('total_sold'),
                func.coalesce(func.sum(SaleItem.subtotal), 0).label('total_revenue')
            ).select_from(Product)\
            .outerjoin(SaleItem, Product.id == SaleItem.product_id)\
            .group_by(Product.id)
            
            # تطبيق الفلاتر
            conditions = []
            if category:
                conditions.append(Product.category == category)
            if low_stock:
                conditions.append(Product.quantity <= Product.min_quantity)
            
            if conditions:
                query = query.where(and_(*conditions))
            
            query = query.order_by(desc(Product.created_at))
            
            # حساب العدد الإجمالي
            count_query = select(func.count(func.distinct(Product.id)))
            if conditions:
                count_query = count_query.where(and_(*conditions))
            
            total_count = self.db.execute(count_query).scalar() or 0
            
            # إرسال معلومات البداية
            if format_type == "json":
                yield f'{{"total_count": {total_count}, "data": ['
            elif format_type == "csv":
                yield "id,name,barcode,category,price,cost_price,quantity,min_quantity,unit,is_active,total_sold,total_revenue,profit_margin,stock_status\n"
            
            # تدفق البيانات بدفعات
            offset = 0
            first_batch = True
            
            while offset < total_count:
                batch_query = query.offset(offset).limit(self.chunk_size)
                products = self.db.execute(batch_query).all()
                
                if not products:
                    break
                
                for i, product in enumerate(products):
                    # حساب هامش الربح
                    profit_margin = 0
                    if product.total_revenue > 0:
                        total_profit = product.total_revenue - (product.cost_price * product.total_sold)
                        profit_margin = (total_profit / product.total_revenue) * 100
                    
                    # تحديد حالة المخزون
                    stock_status = "healthy"
                    if product.quantity == 0:
                        stock_status = "out_of_stock"
                    elif product.quantity <= product.min_quantity:
                        stock_status = "low_stock"
                    elif product.quantity > product.min_quantity * 3:
                        stock_status = "overstocked"
                    
                    if format_type == "json":
                        if not first_batch or i > 0:
                            yield ","
                        
                        product_data = {
                            "id": product.id,
                            "name": product.name,
                            "barcode": product.barcode,
                            "category": product.category,
                            "price": safe_float(product.price),
                            "cost_price": safe_float(product.cost_price),
                            "quantity": product.quantity,
                            "min_quantity": product.min_quantity,
                            "unit": product.unit,
                            "is_active": product.is_active,
                            "total_sold": int(product.total_sold),
                            "total_revenue": float(product.total_revenue),
                            "profit_margin": round(profit_margin, 2),
                            "stock_status": stock_status,
                            "created_at": safe_datetime_iso(product.created_at)
                        }
                        yield json.dumps(product_data, ensure_ascii=False)
                        
                    elif format_type == "csv":
                        csv_row = f"{product.id},\"{product.name}\",{product.barcode or ''},\"{product.category or ''}\",{product.price},{product.cost_price},{product.quantity},{product.min_quantity},{product.unit},{product.is_active},{product.total_sold},{product.total_revenue},{profit_margin:.2f},{stock_status}\n"
                        yield csv_row
                
                offset += self.chunk_size
                first_batch = False
            
            # إنهاء التدفق
            if format_type == "json":
                yield "]}"
                
        except Exception as e:
            logger.error(f"Error in stream_products_data: {e}")
            if format_type == "json":
                yield f'{{"error": "خطأ في تدفق البيانات: {str(e)}"}}'
            else:
                yield f"Error: {str(e)}\n"

@router.get("/sales/stream")
async def stream_sales(
    start_date: Optional[date] = Query(None, description="تاريخ البداية"),
    end_date: Optional[date] = Query(None, description="تاريخ النهاية"),
    user_id: Optional[int] = Query(None, description="معرف المستخدم"),
    format_type: str = Query("json", description="نوع التنسيق: json أو csv"),
    compress: bool = Query(False, description="ضغط البيانات"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    تدفق بيانات المبيعات بشكل مستمر
    يدعم التصفية والتنسيقات المختلفة والضغط
    """
    try:
        # تحويل التواريخ
        start_datetime = datetime.combine(start_date, datetime.min.time()) if start_date else None
        end_datetime = datetime.combine(end_date, datetime.max.time()) if end_date else None
        
        # فحص الصلاحيات
        user_role = getattr(current_user.role, 'value', str(current_user.role))
        if user_role != "admin" and user_id != safe_int(current_user.id):
            user_id = safe_int(current_user.id)  # الكاشيرز يرون بياناتهم فقط
        
        # إنشاء خدمة التدفق
        streamer = DataStreamer(db)
        
        # تحديد نوع المحتوى والاستجابة
        if format_type == "csv":
            media_type = "text/csv"
            filename = f"sales_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        else:
            media_type = "application/json"
            filename = f"sales_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # إنشاء مولد البيانات
        data_generator = streamer.stream_sales_data(
            start_date=start_datetime,
            end_date=end_datetime,
            user_id=user_id,
            format_type=format_type
        )
        
        # إضافة ضغط إذا طُلب
        if compress:
            def compress_generator():
                buffer = io.BytesIO()
                with gzip.GzipFile(fileobj=buffer, mode='wb') as gz_file:
                    for chunk in data_generator:
                        gz_file.write(chunk.encode('utf-8'))
                buffer.seek(0)
                yield buffer.read()
            
            return StreamingResponse(
                compress_generator(),
                media_type="application/gzip",
                headers={
                    "Content-Disposition": f"attachment; filename={filename}.gz",
                    "X-Content-Type": media_type
                }
            )
        else:
            return StreamingResponse(
                (chunk.encode('utf-8') for chunk in data_generator),
                media_type=media_type,
                headers={
                    "Content-Disposition": f"attachment; filename={filename}",
                    "Cache-Control": "no-cache"
                }
            )
            
    except Exception as e:
        logger.error(f"Error in stream_sales: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في تدفق بيانات المبيعات: {str(e)}"
        )

@router.get("/products/stream")
async def stream_products(
    category: Optional[str] = Query(None, description="فئة المنتج"),
    low_stock: bool = Query(False, description="المنتجات قليلة المخزون فقط"),
    format_type: str = Query("json", description="نوع التنسيق: json أو csv"),
    _: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    تدفق بيانات المنتجات مع التحليلات
    """
    try:
        # إنشاء خدمة التدفق
        streamer = DataStreamer(db)
        
        # تحديد نوع المحتوى
        if format_type == "csv":
            media_type = "text/csv"
            filename = f"products_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        else:
            media_type = "application/json"
            filename = f"products_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # إنشاء مولد البيانات
        data_generator = streamer.stream_products_data(
            category=category,
            low_stock=low_stock,
            format_type=format_type
        )
        
        return StreamingResponse(
            (chunk.encode('utf-8') for chunk in data_generator),
            media_type=media_type,
            headers={
                "Content-Disposition": f"attachment; filename={filename}",
                "Cache-Control": "no-cache"
            }
        )
        
    except Exception as e:
        logger.error(f"Error in stream_products: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في تدفق بيانات المنتجات: {str(e)}"
        )

@router.get("/customers/stream")
async def stream_customers(
    with_debts: bool = Query(False, description="تضمين بيانات الديون"),
    format_type: str = Query("json", description="نوع التنسيق: json أو csv"),
    _: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    تدفق بيانات العملاء مع الديون
    """
    try:
        def generate_customers_data():
            # بناء الاستعلام الأساسي
            if with_debts:
                query = select(
                    Customer.id,
                    Customer.name,
                    Customer.phone,
                    Customer.email,
                    Customer.address,
                    Customer.notes,
                    Customer.is_active,
                    Customer.created_at,
                    func.coalesce(func.sum(CustomerDebt.remaining_amount), 0).label('total_debt'),
                    func.count(CustomerDebt.id).label('debt_count')
                ).select_from(Customer)\
                .outerjoin(CustomerDebt, and_(Customer.id == CustomerDebt.customer_id, CustomerDebt.is_paid == False))\
                .group_by(Customer.id)
            else:
                query = select(Customer).order_by(desc(Customer.created_at))

            # حساب العدد الإجمالي
            count_query = select(func.count(Customer.id))
            total_count = db.execute(count_query).scalar() or 0

            # إرسال معلومات البداية
            if format_type == "json":
                yield f'{{"total_count": {total_count}, "data": ['
            elif format_type == "csv":
                if with_debts:
                    yield "id,name,phone,email,address,notes,is_active,total_debt,debt_count,created_at\n"
                else:
                    yield "id,name,phone,email,address,notes,is_active,created_at\n"

            # تدفق البيانات بدفعات
            chunk_size = 1000
            offset = 0
            first_batch = True

            while offset < total_count:
                batch_query = query.offset(offset).limit(chunk_size)
                customers = db.execute(batch_query).all()

                if not customers:
                    break

                for i, customer in enumerate(customers):
                    if format_type == "json":
                        if not first_batch or i > 0:
                            yield ","

                        if with_debts:
                            customer_data = {
                                "id": customer.id,
                                "name": customer.name,
                                "phone": customer.phone,
                                "email": customer.email,
                                "address": customer.address,
                                "notes": customer.notes,
                                "is_active": customer.is_active,
                                "total_debt": float(customer.total_debt or 0),
                                "debt_count": customer.debt_count or 0,
                                "created_at": customer.created_at.isoformat() if customer.created_at else None
                            }
                        else:
                            customer_data = {
                                "id": customer.id,
                                "name": customer.name,
                                "phone": customer.phone,
                                "email": customer.email,
                                "address": customer.address,
                                "notes": customer.notes,
                                "is_active": customer.is_active,
                                "created_at": customer.created_at.isoformat() if customer.created_at else None
                            }
                        yield json.dumps(customer_data, ensure_ascii=False)

                    elif format_type == "csv":
                        if with_debts:
                            csv_row = f"{customer.id},\"{customer.name}\",{customer.phone or ''},\"{customer.email or ''}\",\"{customer.address or ''}\",\"{customer.notes or ''}\",{customer.is_active},{customer.total_debt or 0},{customer.debt_count or 0},{customer.created_at.isoformat() if customer.created_at else ''}\n"
                        else:
                            csv_row = f"{customer.id},\"{customer.name}\",{customer.phone or ''},\"{customer.email or ''}\",\"{customer.address or ''}\",\"{customer.notes or ''}\",{customer.is_active},{customer.created_at.isoformat() if customer.created_at else ''}\n"
                        yield csv_row

                offset += chunk_size
                first_batch = False

            # إنهاء التدفق
            if format_type == "json":
                yield "]}"

        # تحديد نوع المحتوى
        if format_type == "csv":
            media_type = "text/csv"
            filename = f"customers_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        else:
            media_type = "application/json"
            filename = f"customers_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        return StreamingResponse(
            (chunk.encode('utf-8') for chunk in generate_customers_data()),
            media_type=media_type,
            headers={
                "Content-Disposition": f"attachment; filename={filename}",
                "Cache-Control": "no-cache"
            }
        )

    except Exception as e:
        logger.error(f"Error in stream_customers: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في تدفق بيانات العملاء: {str(e)}"
        )

@router.get("/analytics/stream")
async def stream_analytics_data(
    start_date: Optional[date] = Query(None, description="تاريخ البداية"),
    end_date: Optional[date] = Query(None, description="تاريخ النهاية"),
    analysis_type: str = Query("sales", description="نوع التحليل: sales, products, customers"),
    format_type: str = Query("json", description="نوع التنسيق: json أو csv"),
    _: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    تدفق بيانات التحليلات المتقدمة
    """
    try:
        def generate_analytics_data():
            start_datetime = datetime.combine(start_date, datetime.min.time()) if start_date else None
            end_datetime = datetime.combine(end_date, datetime.max.time()) if end_date else None

            query = None

            if analysis_type == "sales":
                # تحليل المبيعات اليومية
                query = select(
                    func.date(Sale.created_at).label('sale_date'),
                    func.count(Sale.id).label('sales_count'),
                    func.sum(Sale.total_amount).label('total_amount'),
                    func.sum(Sale.amount_paid).label('total_paid'),
                    func.avg(Sale.total_amount).label('avg_sale_amount')
                ).group_by(func.date(Sale.created_at))

                # تطبيق فلتر التاريخ
                conditions = []
                if start_datetime:
                    conditions.append(Sale.created_at >= start_datetime)
                if end_datetime:
                    conditions.append(Sale.created_at <= end_datetime)

                if conditions:
                    query = query.where(and_(*conditions))

                query = query.order_by(desc(func.date(Sale.created_at)))

            elif analysis_type == "products":
                # تحليل أداء المنتجات
                query = select(
                    Product.id,
                    Product.name,
                    Product.category,
                    func.coalesce(func.sum(SaleItem.quantity), 0).label('total_sold'),
                    func.coalesce(func.sum(SaleItem.subtotal), 0).label('total_revenue'),
                    func.coalesce(func.count(func.distinct(Sale.id)), 0).label('sales_transactions'),
                    Product.quantity.label('current_stock')
                ).select_from(Product)\
                .outerjoin(SaleItem, Product.id == SaleItem.product_id)\
                .outerjoin(Sale, SaleItem.sale_id == Sale.id)

                # تطبيق فلتر التاريخ على المبيعات
                conditions = []
                if start_datetime:
                    conditions.append(Sale.created_at >= start_datetime)
                if end_datetime:
                    conditions.append(Sale.created_at <= end_datetime)

                if conditions:
                    query = query.where(and_(*conditions))

                query = query.group_by(Product.id).order_by(desc(func.sum(SaleItem.quantity)))
            else:
                # نوع تحليل غير مدعوم
                query = select(Sale.id).limit(0)

            # تنفيذ الاستعلام وتدفق النتائج
            results = db.execute(query).all()

            if format_type == "json":
                yield f'{{"total_count": {len(results)}, "analysis_type": "{analysis_type}", "data": ['
            elif format_type == "csv":
                if analysis_type == "sales":
                    yield "sale_date,sales_count,total_amount,total_paid,avg_sale_amount\n"
                elif analysis_type == "products":
                    yield "product_id,product_name,category,total_sold,total_revenue,sales_transactions,current_stock\n"

            for i, row in enumerate(results):
                if format_type == "json":
                    if i > 0:
                        yield ","

                    data = {}
                    if analysis_type == "sales":
                        data = {
                            "sale_date": str(row.sale_date),
                            "sales_count": row.sales_count,
                            "total_amount": float(row.total_amount or 0),
                            "total_paid": float(row.total_paid or 0),
                            "avg_sale_amount": float(row.avg_sale_amount or 0)
                        }
                    elif analysis_type == "products":
                        data = {
                            "product_id": row.id,
                            "product_name": row.name,
                            "category": row.category,
                            "total_sold": int(row.total_sold),
                            "total_revenue": float(row.total_revenue),
                            "sales_transactions": row.sales_transactions,
                            "current_stock": row.current_stock
                        }

                    yield json.dumps(data, ensure_ascii=False)

                elif format_type == "csv":
                    csv_row = ""
                    if analysis_type == "sales":
                        csv_row = f"{row.sale_date},{row.sales_count},{row.total_amount or 0},{row.total_paid or 0},{row.avg_sale_amount or 0}\n"
                    elif analysis_type == "products":
                        csv_row = f"{row.id},\"{row.name}\",\"{row.category or ''}\",{row.total_sold},{row.total_revenue},{row.sales_transactions},{row.current_stock}\n"

                    yield csv_row

            if format_type == "json":
                yield "]}"

        # تحديد نوع المحتوى
        if format_type == "csv":
            media_type = "text/csv"
            filename = f"{analysis_type}_analytics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        else:
            media_type = "application/json"
            filename = f"{analysis_type}_analytics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        return StreamingResponse(
            (chunk.encode('utf-8') for chunk in generate_analytics_data()),
            media_type=media_type,
            headers={
                "Content-Disposition": f"attachment; filename={filename}",
                "Cache-Control": "no-cache"
            }
        )

    except Exception as e:
        logger.error(f"Error in stream_analytics_data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في تدفق بيانات التحليلات: {str(e)}"
        )

@router.get("/bulk-export")
async def bulk_export_data(
    tables: str = Query(..., description="الجداول المطلوبة مفصولة بفاصلة: sales,products,customers"),
    start_date: Optional[date] = Query(None, description="تاريخ البداية"),
    end_date: Optional[date] = Query(None, description="تاريخ النهاية"),
    format_type: str = Query("json", description="نوع التنسيق: json أو csv"),
    compress: bool = Query(True, description="ضغط البيانات"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    تصدير مجمع للبيانات من عدة جداول
    """
    try:
        def generate_bulk_data():
            requested_tables = [table.strip() for table in tables.split(',')]
            start_datetime = datetime.combine(start_date, datetime.min.time()) if start_date else None
            end_datetime = datetime.combine(end_date, datetime.max.time()) if end_date else None

            if format_type == "json":
                yield '{"export_info": {'
                yield f'"timestamp": "{datetime.now().isoformat()}",'
                yield f'"user_id": {current_user.id},'
                yield f'"tables": {json.dumps(requested_tables)},'
                yield f'"date_range": {{"start": "{start_date}", "end": "{end_date}"}}'
                yield '}, "data": {'

            table_count = 0

            for table_name in requested_tables:
                if table_count > 0 and format_type == "json":
                    yield ","

                if table_name == "sales":
                    if format_type == "json":
                        yield '"sales": ['
                    elif format_type == "csv":
                        yield "=== SALES DATA ===\n"
                        yield "id,user_id,customer_id,total_amount,payment_method,tax_amount,discount_amount,customer_name,notes,amount_paid,payment_status,created_at\n"

                    # استعلام المبيعات
                    sales_query = select(Sale).order_by(desc(Sale.created_at))
                    conditions = []
                    if start_datetime:
                        conditions.append(Sale.created_at >= start_datetime)
                    if end_datetime:
                        conditions.append(Sale.created_at <= end_datetime)

                    # فحص الصلاحيات
                    user_role = getattr(current_user.role, 'value', str(current_user.role))
                    if user_role != "admin":
                        conditions.append(Sale.user_id == current_user.id)

                    if conditions:
                        sales_query = sales_query.where(and_(*conditions))

                    sales = db.execute(sales_query).scalars().all()

                    for i, sale in enumerate(sales):
                        if format_type == "json":
                            if i > 0:
                                yield ","
                            sale_data = {
                                "id": sale.id,
                                "user_id": sale.user_id,
                                "customer_id": sale.customer_id,
                                "total_amount": safe_float(sale.total_amount),
                                "payment_method": sale.payment_method,
                                "tax_amount": safe_float(sale.tax_amount),
                                "discount_amount": safe_float(sale.discount_amount),
                                "customer_name": sale.customer_name,
                                "notes": sale.notes,
                                "amount_paid": safe_float(sale.amount_paid),
                                "payment_status": sale.payment_status,
                                "created_at": safe_datetime_iso(sale.created_at)
                            }
                            yield json.dumps(sale_data, ensure_ascii=False)
                        elif format_type == "csv":
                            csv_row = f"{sale.id},{sale.user_id},{sale.customer_id or ''},{safe_float(sale.total_amount)},{sale.payment_method},{safe_float(sale.tax_amount)},{safe_float(sale.discount_amount)},\"{sale.customer_name or ''}\",\"{sale.notes or ''}\",{safe_float(sale.amount_paid)},{sale.payment_status},{safe_datetime_iso(sale.created_at) or ''}\n"
                            yield csv_row

                    if format_type == "json":
                        yield "]"
                    elif format_type == "csv":
                        yield "\n"

                elif table_name == "products":
                    if format_type == "json":
                        yield '"products": ['
                    elif format_type == "csv":
                        yield "=== PRODUCTS DATA ===\n"
                        yield "id,name,barcode,category,price,cost_price,quantity,min_quantity,unit,is_active,created_at\n"

                    # استعلام المنتجات
                    products_query = select(Product).order_by(desc(Product.created_at))
                    products = db.execute(products_query).scalars().all()

                    for i, product in enumerate(products):
                        if format_type == "json":
                            if i > 0:
                                yield ","
                            product_data = {
                                "id": product.id,
                                "name": product.name,
                                "barcode": product.barcode,
                                "category": product.category,
                                "price": safe_float(product.price),
                                "cost_price": safe_float(product.cost_price),
                                "quantity": product.quantity,
                                "min_quantity": product.min_quantity,
                                "unit": product.unit,
                                "is_active": product.is_active,
                                "created_at": safe_datetime_iso(product.created_at)
                            }
                            yield json.dumps(product_data, ensure_ascii=False)
                        elif format_type == "csv":
                            csv_row = f"{product.id},\"{product.name}\",{product.barcode or ''},\"{product.category or ''}\",{safe_float(product.price)},{safe_float(product.cost_price)},{product.quantity},{product.min_quantity},{product.unit},{product.is_active},{safe_datetime_iso(product.created_at) or ''}\n"
                            yield csv_row

                    if format_type == "json":
                        yield "]"
                    elif format_type == "csv":
                        yield "\n"

                elif table_name == "customers":
                    if format_type == "json":
                        yield '"customers": ['
                    elif format_type == "csv":
                        yield "=== CUSTOMERS DATA ===\n"
                        yield "id,name,phone,email,address,notes,is_active,created_at\n"

                    # استعلام العملاء
                    customers_query = select(Customer).order_by(desc(Customer.created_at))
                    customers = db.execute(customers_query).scalars().all()

                    for i, customer in enumerate(customers):
                        if format_type == "json":
                            if i > 0:
                                yield ","
                            customer_data = {
                                "id": customer.id,
                                "name": customer.name,
                                "phone": customer.phone,
                                "email": customer.email,
                                "address": customer.address,
                                "notes": customer.notes,
                                "is_active": customer.is_active,
                                "created_at": safe_datetime_iso(customer.created_at)
                            }
                            yield json.dumps(customer_data, ensure_ascii=False)
                        elif format_type == "csv":
                            csv_row = f"{customer.id},\"{customer.name}\",{customer.phone or ''},\"{customer.email or ''}\",\"{customer.address or ''}\",\"{customer.notes or ''}\",{customer.is_active},{safe_datetime_iso(customer.created_at) or ''}\n"
                            yield csv_row

                    if format_type == "json":
                        yield "]"
                    elif format_type == "csv":
                        yield "\n"

                table_count += 1

            if format_type == "json":
                yield "}}"

        # تحديد نوع المحتوى والملف
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        if format_type == "csv":
            media_type = "text/csv"
            filename = f"bulk_export_{timestamp}.csv"
        else:
            media_type = "application/json"
            filename = f"bulk_export_{timestamp}.json"

        # إنشاء مولد البيانات
        data_generator = generate_bulk_data()

        # إضافة ضغط إذا طُلب
        if compress:
            def compress_generator():
                buffer = io.BytesIO()
                with gzip.GzipFile(fileobj=buffer, mode='wb') as gz_file:
                    for chunk in data_generator:
                        gz_file.write(chunk.encode('utf-8'))
                buffer.seek(0)
                yield buffer.read()

            return StreamingResponse(
                compress_generator(),
                media_type="application/gzip",
                headers={
                    "Content-Disposition": f"attachment; filename={filename}.gz",
                    "X-Content-Type": media_type
                }
            )
        else:
            return StreamingResponse(
                (chunk.encode('utf-8') for chunk in data_generator),
                media_type=media_type,
                headers={
                    "Content-Disposition": f"attachment; filename={filename}",
                    "Cache-Control": "no-cache"
                }
            )

    except Exception as e:
        logger.error(f"Error in bulk_export_data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في التصدير المجمع: {str(e)}"
        )

@router.post("/tasks/create")
async def create_streaming_task(
    task_type: str = Query(..., description="نوع المهمة: sales_export, products_export, customers_export"),
    parameters: Dict[str, Any] = {},
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    إنشاء مهمة تدفق جديدة
    """
    try:
        from services.data_streaming_service import DataStreamingService

        streaming_service = DataStreamingService(db)

        # فحص الصلاحيات
        user_role = getattr(current_user.role, 'value', str(current_user.role))
        if user_role != "admin" and parameters.get("user_id") != current_user.id:
            parameters["user_id"] = current_user.id

        task_id = await streaming_service.create_streaming_task(
            task_type=task_type,
            parameters=parameters,
            user_id=safe_int(current_user.id)
        )

        return {
            "task_id": task_id,
            "status": "created",
            "message": "تم إنشاء المهمة بنجاح"
        }

    except Exception as e:
        logger.error(f"Error creating streaming task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في إنشاء المهمة: {str(e)}"
        )

@router.get("/tasks/{task_id}/progress")
async def get_task_progress(
    task_id: str,
    _: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    الحصول على تقدم المهمة
    """
    try:
        from services.data_streaming_service import DataStreamingService

        streaming_service = DataStreamingService(db)
        progress = await streaming_service.get_task_progress(task_id)

        if not progress:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="المهمة غير موجودة"
            )

        return progress

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting task progress: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في الحصول على تقدم المهمة: {str(e)}"
        )

@router.get("/tasks/{task_id}/download")
async def download_task_result(
    task_id: str,
    _: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    تحميل نتيجة المهمة
    """
    try:
        from services.data_streaming_service import DataStreamingService

        streaming_service = DataStreamingService(db)
        progress = await streaming_service.get_task_progress(task_id)

        if not progress:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="المهمة غير موجودة"
            )

        if progress.status != "completed":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="المهمة لم تكتمل بعد"
            )

        # الحصول على معلومات الملف
        task_info = streaming_service.active_streams.get(task_id)
        if not task_info or "export_file" not in task_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="ملف التصدير غير موجود"
            )

        export_file = Path(task_info["export_file"])
        if not export_file.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="ملف التصدير غير موجود"
            )

        # تحديد نوع المحتوى
        if export_file.suffix == '.gz':
            media_type = "application/gzip"
        elif export_file.suffix == '.json':
            media_type = "application/json"
        else:
            media_type = "application/octet-stream"

        # إرجاع الملف للتحميل
        def file_generator():
            with open(export_file, 'rb') as f:
                while chunk := f.read(8192):
                    yield chunk

        return StreamingResponse(
            file_generator(),
            media_type=media_type,
            headers={
                "Content-Disposition": f"attachment; filename={export_file.name}",
                "Content-Length": str(export_file.stat().st_size)
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading task result: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في تحميل النتيجة: {str(e)}"
        )

@router.get("/metrics")
async def get_streaming_metrics(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    الحصول على مقاييس أداء التدفق
    """
    try:
        # فحص الصلاحيات - الإدارة فقط
        user_role = getattr(current_user.role, 'value', str(current_user.role))
        if user_role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="غير مصرح لك بالوصول لهذه البيانات"
            )

        from services.data_streaming_service import DataStreamingService

        streaming_service = DataStreamingService(db)
        metrics = streaming_service.get_streaming_metrics()

        return metrics

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting streaming metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في الحصول على المقاييس: {str(e)}"
        )

@router.post("/cleanup")
async def cleanup_old_exports(
    max_age_hours: int = Query(24, description="عمر الملفات بالساعات"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    تنظيف ملفات التصدير القديمة
    """
    try:
        # فحص الصلاحيات - الإدارة فقط
        user_role = getattr(current_user.role, 'value', str(current_user.role))
        if user_role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="غير مصرح لك بتنفيذ هذه العملية"
            )

        from services.data_streaming_service import DataStreamingService

        streaming_service = DataStreamingService(db)
        streaming_service.cleanup_old_exports(max_age_hours)

        return {
            "status": "success",
            "message": f"تم تنظيف الملفات الأقدم من {max_age_hours} ساعة"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cleaning up exports: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في التنظيف: {str(e)}"
        )
