"""
API endpoints لنظام المحادثة الفورية
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime

from database.session import get_db
from models.user import User
from schemas.chat import (
    MessageCreate, MessageResponse, MessagesListResponse,
    ConversationResponse, MarkAsReadRequest,
    UserSearchResponse, UserOnlineStatusResponse
)
from services.chat_message_service import ChatMessageService
from services.chat_websocket_manager import chat_websocket_manager
from utils.auth import get_current_active_user
from sqlalchemy import select, or_, and_

router = APIRouter(prefix="/api/chat", tags=["chat"])

@router.post("/send", response_model=MessageResponse)
async def send_message(
    message_data: MessageCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    إرسال رسالة جديدة
    """
    try:
        # التحقق من أن المستخدم لا يرسل رسالة لنفسه
        user_id = int(current_user.id)
        if message_data.receiver_id == user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="لا يمكن إرسال رسالة لنفسك"
            )

        # إنشاء خدمة الرسائل
        message_service = ChatMessageService(db)

        # إرسال الرسالة
        result = await message_service.send_message(user_id, message_data)
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="فشل في إرسال الرسالة"
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في الخادم: {str(e)}"
        )

@router.get("/messages/{other_user_id}", response_model=MessagesListResponse)
async def get_messages(
    other_user_id: int,
    page: int = Query(1, ge=1, description="رقم الصفحة"),
    limit: int = Query(20, ge=1, le=50, description="عدد الرسائل في الصفحة"),
    before_message_id: Optional[int] = Query(None, description="جلب الرسائل قبل هذه الرسالة"),
    load_direction: str = Query("newer", pattern="^(older|newer)$", description="اتجاه التحميل: older للأقدم، newer للأحدث"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    جلب الرسائل بين المستخدم الحالي ومستخدم آخر
    """
    try:
        # التحقق من وجود المستخدم الآخر
        other_user_stmt = select(User).where(User.id == other_user_id)
        other_user = db.execute(other_user_stmt).scalar_one_or_none()
        
        if not other_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="المستخدم غير موجود"
            )
        
        # إنشاء خدمة الرسائل
        message_service = ChatMessageService(db)
        
        # جلب الرسائل
        user_id = int(current_user.id)
        result = message_service.get_messages_between_users(
            user_id,
            other_user_id,
            page,
            limit,
            before_message_id,
            load_direction
        )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في الخادم: {str(e)}"
        )

@router.post("/mark-as-read")
async def mark_messages_as_read(
    request: MarkAsReadRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    تحديد الرسائل كمقروءة
    """
    try:
        # إنشاء خدمة الرسائل
        message_service = ChatMessageService(db)
        
        # تحديد الرسائل كمقروءة
        user_id = int(current_user.id)
        updated_count = await message_service.mark_messages_as_read(
            user_id,
            request.other_user_id,
            request.message_ids
        )
        
        return {
            "success": True,
            "updated_count": updated_count,
            "message": f"تم تحديد {updated_count} رسالة كمقروءة"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في الخادم: {str(e)}"
        )

@router.get("/conversations")
async def get_conversations(
    page: int = Query(1, ge=1, description="رقم الصفحة"),
    limit: int = Query(20, ge=1, le=1000, description="عدد المحادثات في الصفحة"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    جلب قائمة المحادثات للمستخدم الحالي
    """
    try:
        # إنشاء خدمة الرسائل
        message_service = ChatMessageService(db)
        
        # جلب قائمة المحادثات مع التحميل التدريجي
        user_id = int(current_user.id)
        result = message_service.get_conversations_list(user_id, page, limit)

        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في الخادم: {str(e)}"
        )

@router.get("/unread-count")
async def get_unread_count(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    جلب عدد الرسائل غير المقروءة
    """
    try:
        # إنشاء خدمة الرسائل
        message_service = ChatMessageService(db)
        
        # جلب عدد الرسائل غير المقروءة
        user_id = int(current_user.id)
        unread_count = message_service.get_unread_messages_count(user_id)
        
        return {
            "unread_count": unread_count
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في الخادم: {str(e)}"
        )

@router.get("/search")
async def search_messages(
    query: str = Query(..., min_length=1, max_length=100, description="نص البحث"),
    limit: int = Query(20, ge=1, le=50, description="عدد النتائج"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    البحث في الرسائل
    """
    try:
        # إنشاء خدمة الرسائل
        message_service = ChatMessageService(db)
        
        # البحث في الرسائل
        user_id = int(current_user.id)
        messages = message_service.search_messages(user_id, query, limit)
        
        return {
            "messages": messages,
            "query": query,
            "count": len(messages)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في الخادم: {str(e)}"
        )

@router.get("/users/search", response_model=UserSearchResponse)
async def search_users(
    query: str = Query(..., min_length=1, max_length=100, description="نص البحث"),
    limit: int = Query(10, ge=1, le=50, description="عدد النتائج"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    البحث عن المستخدمين للمحادثة
    """
    try:
        # البحث في المستخدمين
        search_stmt = select(User).where(
            and_(
                User.id != current_user.id,  # استبعاد المستخدم الحالي
                User.is_active == True,
                or_(
                    User.username.contains(query),
                    User.full_name.contains(query)
                )
            )
        ).limit(limit)
        
        users = db.execute(search_stmt).scalars().all()
        
        # تحويل إلى استجابة
        user_responses = []
        for user in users:
            user_responses.append(UserOnlineStatusResponse(
                user_id=user.id,
                username=user.username,
                full_name=user.full_name,
                is_online=user.is_online,
                last_seen=user.last_seen
            ))
        
        return UserSearchResponse(
            users=user_responses,
            total_count=len(user_responses)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في الخادم: {str(e)}"
        )

@router.get("/users/online", response_model=List[UserOnlineStatusResponse])
async def get_online_users(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    جلب قائمة المستخدمين المتصلين
    """
    try:
        # جلب المستخدمين المتصلين من WebSocket Manager
        online_user_ids = chat_websocket_manager.get_online_users()
        
        if not online_user_ids:
            return []
        
        # جلب معلومات المستخدمين المتصلين
        online_users_stmt = select(User).where(
            and_(
                User.id.in_(online_user_ids),
                User.id != current_user.id,  # استبعاد المستخدم الحالي
                User.is_active == True
            )
        )
        
        online_users = db.execute(online_users_stmt).scalars().all()
        
        # تحويل إلى استجابة
        user_responses = []
        for user in online_users:
            user_responses.append(UserOnlineStatusResponse(
                user_id=user.id,
                username=user.username,
                full_name=user.full_name,
                is_online=True,  # نعلم أنهم متصلون
                last_seen=user.last_seen
            ))
        
        return user_responses

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في الخادم: {str(e)}"
        )

@router.get("/users/all", response_model=List[UserOnlineStatusResponse])
async def get_all_users(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    جلب قائمة جميع المستخدمين النشطين في النظام
    """
    try:
        # جلب جميع المستخدمين النشطين
        all_users_stmt = select(User).where(
            and_(
                User.id != current_user.id,  # استبعاد المستخدم الحالي
                User.is_active == True
            )
        ).order_by(User.full_name, User.username)

        all_users = db.execute(all_users_stmt).scalars().all()

        # الحصول على قائمة المستخدمين المتصلين
        online_user_ids = chat_websocket_manager.get_online_users()

        # تحويل إلى استجابة
        user_responses = []
        for user in all_users:
            user_responses.append(UserOnlineStatusResponse(
                user_id=user.id,
                username=user.username,
                full_name=user.full_name,
                is_online=user.id in online_user_ids,
                last_seen=user.last_seen
            ))

        return user_responses

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في الخادم: {str(e)}"
        )

@router.get("/status")
async def get_chat_status(
    current_user: User = Depends(get_current_active_user)
):
    """
    جلب حالة نظام المحادثة
    """
    try:
        user_id = int(current_user.id)
        return {
            "user_id": user_id,
            "username": current_user.username,
            "is_online": chat_websocket_manager.is_user_online(user_id),
            "total_online_users": chat_websocket_manager.get_connection_count(),
            "online_users": chat_websocket_manager.get_online_users()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في الخادم: {str(e)}"
        )

@router.delete("/messages/{message_id}")
async def delete_message(
    message_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    حذف رسالة (للمرسل فقط)
    """
    try:
        from models.chat_message import ChatMessage
        from datetime import datetime

        # البحث عن الرسالة
        message_stmt = select(ChatMessage).where(ChatMessage.id == message_id)
        message = db.execute(message_stmt).scalar_one_or_none()

        if not message:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="الرسالة غير موجودة"
            )

        # التحقق من أن المستخدم هو مرسل الرسالة
        if message.sender_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="غير مصرح لك بحذف هذه الرسالة"
            )

        receiver_id = message.receiver_id

        # حذف الرسالة
        db.delete(message)
        db.commit()

        # إشعار المستقبل بحذف الرسالة
        websocket_message = {
            'type': 'message_deleted',
            'message_id': message_id,
            'sender_id': current_user.id,
            'timestamp': datetime.now().isoformat()
        }

        await chat_websocket_manager.send_message_to_user(
            receiver_id,
            websocket_message
        )

        return {
            "success": True,
            "message": "تم حذف الرسالة بنجاح"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في الخادم: {str(e)}"
        )

@router.delete("/conversations/{user_id}")
async def delete_conversation(
    user_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    حذف المحادثة بالكامل مع مستخدم محدد
    """
    try:
        from models.chat_message import ChatMessage
        from datetime import datetime

        current_user_id = int(current_user.id)

        # التحقق من أن المستخدم لا يحذف محادثة مع نفسه
        if user_id == current_user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="لا يمكن حذف محادثة مع نفسك"
            )

        # البحث عن جميع الرسائل بين المستخدمين
        messages_stmt = select(ChatMessage).where(
            or_(
                and_(ChatMessage.sender_id == current_user_id, ChatMessage.receiver_id == user_id),
                and_(ChatMessage.sender_id == user_id, ChatMessage.receiver_id == current_user_id)
            )
        )
        messages = db.execute(messages_stmt).scalars().all()

        if not messages:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="لا توجد محادثة مع هذا المستخدم"
            )

        # حذف جميع الرسائل
        for message in messages:
            db.delete(message)

        db.commit()

        # إشعار المستخدم الآخر بحذف المحادثة
        websocket_message = {
            'type': 'conversation_deleted',
            'deleted_by_user_id': current_user_id,
            'deleted_by_username': current_user.username,
            'timestamp': datetime.now().isoformat()
        }

        await chat_websocket_manager.send_message_to_user(
            user_id,
            websocket_message
        )

        return {
            "success": True,
            "message": "تم حذف المحادثة بنجاح",
            "deleted_messages_count": len(messages)
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في الخادم: {str(e)}"
        )

@router.put("/messages/{message_id}")
async def edit_message(
    message_id: int,
    new_content: str = Query(..., description="المحتوى الجديد للرسالة"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    تعديل رسالة (للمرسل فقط)
    """
    try:
        from models.chat_message import ChatMessage
        from datetime import datetime

        # التحقق من صحة المحتوى الجديد
        if not new_content or len(new_content.strip()) == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="محتوى الرسالة لا يمكن أن يكون فارغاً"
            )

        if len(new_content) > 5000:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="محتوى الرسالة طويل جداً (الحد الأقصى 5000 حرف)"
            )

        # البحث عن الرسالة
        message_stmt = select(ChatMessage).where(ChatMessage.id == message_id)
        message = db.execute(message_stmt).scalar_one_or_none()

        if not message:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="الرسالة غير موجودة"
            )

        # التحقق من أن المستخدم هو مرسل الرسالة
        if message.sender_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="غير مصرح لك بتعديل هذه الرسالة"
            )

        # تحديث الرسالة
        message.content = new_content.strip()
        message.is_edited = True
        message.edited_at = datetime.now()

        db.commit()
        db.refresh(message)

        # إشعار المستقبل بتعديل الرسالة
        websocket_message = {
            'type': 'message_edited',
            'message_id': message_id,
            'new_content': new_content.strip(),
            'sender_id': current_user.id,
            'edited_at': message.edited_at.isoformat(),
            'timestamp': datetime.now().isoformat()
        }

        await chat_websocket_manager.send_message_to_user(
            message.receiver_id,
            websocket_message
        )

        return {
            "success": True,
            "message": "تم تعديل الرسالة بنجاح",
            "updated_message": {
                "id": message.id,
                "content": message.content,
                "is_edited": message.is_edited,
                "edited_at": message.edited_at.isoformat() if message.edited_at else None
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في الخادم: {str(e)}"
        )
