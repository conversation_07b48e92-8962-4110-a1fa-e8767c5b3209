from fastapi import APIRouter, Depends, HTTPException, Response
from sqlalchemy.orm import Session
from sqlalchemy import and_
from typing import List, Optional
from database.session import get_db
from models.user import User
from models.brand import Brand
from schemas.brand import BrandCreate, BrandUpdate, BrandResponse
from utils.auth import get_current_user

router = APIRouter(prefix="/api/brands", tags=["brands"])

@router.post("/", response_model=BrandResponse)
async def create_brand(
    brand: BrandCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new brand."""
    try:
        # Check if brand name already exists
        existing_brand = db.query(Brand).filter(Brand.name == brand.name).first()
        if existing_brand:
            raise HTTPException(status_code=400, detail="اسم العلامة التجارية موجود بالفعل")

        db_brand = Brand(
            **brand.model_dump(),
            created_by=current_user.id
        )
        db.add(db_brand)
        db.commit()
        db.refresh(db_brand)
        return db_brand
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"فشل في إنشاء العلامة التجارية: {str(e)}")

@router.get("/", response_model=List[BrandResponse])
async def get_brands(
    skip: int = 0,
    limit: int = 100,
    active_only: bool = False,  # تغيير الافتراضي لعرض جميع العلامات التجارية
    search: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get all brands."""
    query = db.query(Brand)
    
    if active_only:
        query = query.filter(Brand.is_active == True)
    
    if search:
        query = query.filter(Brand.name.ilike(f"%{search}%"))
    
    brands = query.offset(skip).limit(limit).all()
    return brands

@router.get("/{brand_id}", response_model=BrandResponse)
async def get_brand(
    brand_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific brand."""
    brand = db.query(Brand).filter(Brand.id == brand_id).first()
    if not brand:
        raise HTTPException(status_code=404, detail="العلامة التجارية غير موجودة")
    return brand

@router.put("/{brand_id}", response_model=BrandResponse)
async def update_brand(
    brand_id: int,
    brand_update: BrandUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update a brand."""
    try:
        db_brand = db.query(Brand).filter(Brand.id == brand_id).first()
        if not db_brand:
            raise HTTPException(status_code=404, detail="العلامة التجارية غير موجودة")

        # Check if new name already exists (if name is being updated)
        if brand_update.name and brand_update.name != db_brand.name:
            existing_brand = db.query(Brand).filter(
                and_(Brand.name == brand_update.name, Brand.id != brand_id)
            ).first()
            if existing_brand:
                raise HTTPException(status_code=400, detail="اسم العلامة التجارية موجود بالفعل")

        # Update fields
        update_data = brand_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_brand, field, value)
        
        db_brand.updated_by = current_user.id
        db.commit()
        db.refresh(db_brand)
        return db_brand
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"فشل في تحديث العلامة التجارية: {str(e)}")

@router.delete("/{brand_id}")
async def delete_brand(
    brand_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete a brand."""
    try:
        db_brand = db.query(Brand).filter(Brand.id == brand_id).first()
        if not db_brand:
            raise HTTPException(status_code=404, detail="العلامة التجارية غير موجودة")

        # Check if brand has products
        if db_brand.products:
            raise HTTPException(
                status_code=400, 
                detail="لا يمكن حذف العلامة التجارية لأنها مرتبطة بمنتجات. قم بإعادة تصنيف المنتجات أولاً"
            )

        db.delete(db_brand)
        db.commit()
        return {"message": "تم حذف العلامة التجارية بنجاح"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"فشل في حذف العلامة التجارية: {str(e)}")

@router.get("/list/names", response_model=List[str])
async def get_brand_names(
    active_only: bool = True,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get list of brand names for dropdowns."""
    query = db.query(Brand.name)
    if active_only:
        query = query.filter(Brand.is_active == True)
    
    brands = query.all()
    return [brand[0] for brand in brands]
