"""
API endpoints لإدارة البصمة الشاملة للأجهزة البعيدة
"""

from fastapi import APIRouter, Depends, HTTPException, Request, Header
from sqlalchemy.orm import Session
from sqlalchemy import select
from typing import Optional, Dict, Any
import json
import logging
from datetime import datetime

from database.session import get_db
from models.device_fingerprint import DeviceFingerprint
from models.device_security import PendingDevice
from services.unified_fingerprint_service import get_unified_fingerprint_service
from services.device_fingerprint_history_service import get_fingerprint_history_service
from utils.datetime_utils import get_tripoli_now

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/comprehensive-fingerprint", tags=["comprehensive-fingerprint"])


@router.post("/store-pending-approval")
async def store_comprehensive_fingerprint_for_pending_approval(
    request: Request,
    db: Session = Depends(get_db),
    x_device_fingerprint: Optional[str] = Header(None),
    x_device_hardware: Optional[str] = Header(None),
    x_device_storage: Optional[str] = Header(None),
    x_device_screen: Optional[str] = Header(None),
    x_device_system: Optional[str] = Header(None),
    x_device_timestamp: Optional[str] = Header(None),
    # ✅ إضافة headers جديدة لمعلومات الجهاز
    x_device_platform: Optional[str] = Header(None),
    x_device_browser: Optional[str] = Header(None),
    x_device_useragent: Optional[str] = Header(None)
):
    """
    تخزين البصمة الشاملة للأجهزة في انتظار الموافقة
    """
    try:
        logger.info("📝 بدء تخزين البصمة الشاملة للجهاز في انتظار الموافقة")
        
        # التحقق من وجود البيانات المطلوبة
        if not x_device_fingerprint:
            raise HTTPException(
                status_code=400,
                detail="معرف البصمة مطلوب في header X-Device-Fingerprint"
            )
        
        if not x_device_hardware or not x_device_storage:
            raise HTTPException(
                status_code=400,
                detail="بصمة الأجهزة والتخزين مطلوبة"
            )
        
        # الحصول على عنوان IP الحقيقي للجهاز من قائمة الانتظار
        # أولاً، البحث عن الجهاز في قائمة الانتظار للحصول على IP الصحيح
        pending_stmt = select(PendingDevice).where(
            PendingDevice.device_id == x_device_fingerprint,
            PendingDevice.status == "pending"
        )
        pending_device = db.execute(pending_stmt).scalar_one_or_none()

        if not pending_device:
            logger.warning(f"⚠️ الجهاز {x_device_fingerprint} غير موجود في قائمة الانتظار")
            return {
                "success": False,
                "error": "الجهاز غير موجود في قائمة الانتظار",
                "fingerprint_id": x_device_fingerprint
            }

        # استخدام IP الجهاز الحقيقي من قائمة الانتظار
        real_device_ip = pending_device.client_ip

        # الحصول على User-Agent (أولوية للمرسل من الواجهة الأمامية)
        user_agent = x_device_useragent or request.headers.get('user-agent', '')

        logger.info(f"📝 معرف البصمة: {x_device_fingerprint}")
        logger.info(f"📝 عنوان IP الحقيقي للجهاز: {real_device_ip}")
        logger.info(f"📝 عنوان IP الطلب: {request.client.host if request.client else 'unknown'}")
        
        # الحصول على خدمة البصمة الموحدة
        fingerprint_service = get_unified_fingerprint_service(db)
        
        # التحقق من وجود بصمة موجودة بنفس المعرف
        existing_fingerprint_stmt = select(DeviceFingerprint).where(
            DeviceFingerprint.fingerprint_id == x_device_fingerprint,
            DeviceFingerprint.is_active == True
        )
        existing_fingerprint = db.execute(existing_fingerprint_stmt).scalar_one_or_none()

        # إذا لم توجد بالمعرف، ابحث بالبصمة الفعلية لمنع التكرار
        if not existing_fingerprint and x_device_hardware and x_device_storage:
            stmt_by_fingerprint = select(DeviceFingerprint).where(
                DeviceFingerprint.hardware_fingerprint == x_device_hardware,
                DeviceFingerprint.storage_fingerprint == x_device_storage,
                DeviceFingerprint.is_active == True
            )
            existing_by_fingerprint = db.execute(stmt_by_fingerprint).scalar_one_or_none()

            if existing_by_fingerprint:
                logger.info(f"🔍 تم العثور على بصمة موجودة بمعرف مختلف: {existing_by_fingerprint.fingerprint_id} -> سيتم تحديث المعرف إلى: {x_device_fingerprint}")

                # حذف البصمة القديمة بالمعرف المختلف لمنع التكرار
                old_fingerprint_id = existing_by_fingerprint.fingerprint_id
                db.delete(existing_by_fingerprint)
                db.flush()

                logger.info(f"🗑️ تم حذف البصمة المكررة: {old_fingerprint_id}")

                # إنشاء بصمة جديدة بالمعرف الصحيح
                existing_fingerprint = None

        if existing_fingerprint:
            # تحديث البصمة الموجودة بدلاً من إنشاء جديدة
            logger.info(f"✅ تم العثور على بصمة موجودة: {x_device_fingerprint}")
            fingerprint = existing_fingerprint
            is_new = False

            # تحديث البيانات الأساسية
            fingerprint.last_ip = real_device_ip
            fingerprint.last_user_agent = user_agent
            fingerprint.last_seen_at = get_tripoli_now()

            # تحديث البصمات إذا تم توفيرها
            if x_device_hardware:
                fingerprint.hardware_fingerprint = x_device_hardware
            if x_device_storage:
                fingerprint.storage_fingerprint = x_device_storage
            if x_device_screen:
                fingerprint.screen_fingerprint = x_device_screen
            if x_device_system:
                fingerprint.system_fingerprint = x_device_system

            # تحديث البيانات التفصيلية إذا كانت فارغة
            if not fingerprint.fingerprint_details:
                fingerprint_details = {
                    "hardware_fingerprint": x_device_hardware,
                    "storage_fingerprint": x_device_storage,
                    "screen_fingerprint": x_device_screen,
                    "system_fingerprint": x_device_system,
                    "update_method": "comprehensive_api",
                    "updated_at": get_tripoli_now().isoformat()
                }
                fingerprint.fingerprint_details = json.dumps(fingerprint_details)

            # تحديث معلومات الجهاز إذا كانت فارغة
            if not fingerprint.device_info:
                device_info = {
                    "user_agent": user_agent,
                    "ip_address": real_device_ip,
                    "request_headers": dict(request.headers) if hasattr(request, 'headers') else {},
                    "detection_method": "advanced_fingerprinting_update"
                }
                fingerprint.device_info = json.dumps(device_info)

            # تحديث معلومات الشبكة إذا كانت فارغة
            if not fingerprint.network_info:
                network_info = {
                    "ip_address": real_device_ip,
                    "request_ip": request.client.host if request.client else "unknown",
                    "connection_type": "remote_device_update"
                }
                fingerprint.network_info = json.dumps(network_info)

            # ملاحظة: تم إلغاء network_fingerprint نهائياً حسب متطلبات النظام الجديدة

        else:
            # إنشاء بصمة جديدة بنفس المعرف الموجود في قائمة الانتظار
            logger.info(f"🆕 إنشاء بصمة جديدة بالمعرف: {x_device_fingerprint}")

            # إعداد البيانات التفصيلية
            fingerprint_details = {
                "hardware_fingerprint": x_device_hardware,
                "storage_fingerprint": x_device_storage,
                "screen_fingerprint": x_device_screen,
                "system_fingerprint": x_device_system,
                "creation_method": "comprehensive_api",
                "timestamp": get_tripoli_now().isoformat()
            }

            # معلومات الجهاز من User Agent
            device_info = {
                "user_agent": user_agent,
                "ip_address": real_device_ip,
                "request_headers": dict(request.headers) if hasattr(request, 'headers') else {},
                "detection_method": "advanced_fingerprinting"
            }

            # معلومات الشاشة
            screen_info = {
                "screen_fingerprint": x_device_screen,
                "screen_data": "extracted_from_fingerprint" if x_device_screen else None
            }

            # معلومات النظام
            system_info = {
                "system_fingerprint": x_device_system,
                "platform_detected": x_device_system if x_device_system else "unknown"
            }

            # معلومات المتصفح
            browser_info = {
                "user_agent": user_agent,
                "browser_fingerprint": "advanced_detection"
            }

            # معلومات الشبكة
            network_info = {
                "ip_address": real_device_ip,
                "request_ip": request.client.host if request.client else "unknown",
                "connection_type": "remote_device"
            }

            fingerprint = DeviceFingerprint(
                fingerprint_id=x_device_fingerprint,  # استخدام نفس المعرف
                hardware_fingerprint=x_device_hardware,
                storage_fingerprint=x_device_storage,
                screen_fingerprint=x_device_screen,
                system_fingerprint=x_device_system,
                # تم إزالة network_fingerprint نهائياً حسب متطلبات النظام الجديدة
                fingerprint_details=json.dumps(fingerprint_details),
                device_info=json.dumps(device_info),
                screen_info=json.dumps(screen_info),
                system_info=json.dumps(system_info),
                browser_info=json.dumps(browser_info),
                network_info=json.dumps(network_info),
                additional_info=json.dumps({
                    "comprehensive_stored": True,
                    "pending_approval": True,
                    "source": "comprehensive_fingerprint_api"
                }),
                last_ip=real_device_ip,
                last_user_agent=user_agent,
                is_active=True,
                auto_approved=False,
                created_at=get_tripoli_now(),
                last_seen_at=get_tripoli_now()
            )

            db.add(fingerprint)
            is_new = True
        
        # ✅ استخدام نفس منطق device_tracker.py لضمان الدقة
        from services.device_tracker import device_tracker

        # استخراج معلومات الجهاز باستخدام نفس الدالة المستخدمة للأجهزة المعتمدة
        device_info = device_tracker._extract_platform_info(user_agent)

        logger.info(f"✅ استخراج معلومات الجهاز باستخدام device_tracker: {device_info}")

        # ✅ تحديث اسم الجهاز باستخدام نفس منطق device_tracker.py
        hostname = device_tracker._generate_device_hostname_from_platform(device_info, real_device_ip)
        pending_device.hostname = hostname

        # تحديث معلومات النظام والمنصة والمتصفح
        pending_device.system = device_info['system']
        pending_device.platform = device_info['platform']
        pending_device.browser = device_info['browser']
        pending_device.device_type = device_info['device_type']
        pending_device.user_agent = user_agent

        # تحديث بيانات البصمة
        pending_device.fingerprint_data = json.dumps({
            "hardware_fingerprint": x_device_hardware,
            "storage_fingerprint": x_device_storage,
            "screen_fingerprint": x_device_screen,
            "system_fingerprint": x_device_system,
            "comprehensive_stored": True,
            "stored_at": get_tripoli_now().isoformat(),
            "unified_fingerprint_id": x_device_fingerprint  # ضمان توحيد المعرف
        })
        pending_device.updated_at = get_tripoli_now()

        logger.info(f"✅ تم تحديث معلومات الجهاز في قائمة الانتظار:")
        logger.info(f"   📱 اسم الجهاز: {pending_device.hostname}")
        logger.info(f"   💻 النظام: {pending_device.system}")
        logger.info(f"   🌐 المنصة: {pending_device.platform}")
        logger.info(f"   🔍 المتصفح: {pending_device.browser}")

        try:
            db.commit()
            logger.info(f"✅ تم تخزين البصمة الشاملة بنجاح: {fingerprint.fingerprint_id}")
            logger.info(f"   - جديدة: {is_new}")
            logger.info(f"   - معرف قاعدة البيانات: {fingerprint.id if hasattr(fingerprint, 'id') else 'N/A'}")
            logger.info(f"   - عنوان IP الصحيح: {real_device_ip}")

            # تسجيل الحدث في تاريخ البصمات باستخدام الخدمة الموحدة
            history_service = get_fingerprint_history_service(db)
            history_service.log_fingerprint_stored(
                fingerprint_id=x_device_fingerprint,
                ip_address=real_device_ip,
                user_agent=user_agent,
                is_new_fingerprint=is_new,
                additional_data={
                    "hardware_fingerprint": x_device_hardware[:8] + "..." if x_device_hardware else None,
                    "storage_fingerprint": x_device_storage[:8] + "..." if x_device_storage else None,
                    "screen_fingerprint": x_device_screen[:8] + "..." if x_device_screen else None,
                    "system_fingerprint": x_device_system[:8] + "..." if x_device_system else None,
                    "request_source": "comprehensive_fingerprint_api",
                    "pending_approval": True
                }
            )

        except Exception as commit_error:
            logger.error(f"❌ خطأ في حفظ البيانات: {commit_error}")
            db.rollback()
            raise

        return {
            "success": True,
            "fingerprint_id": fingerprint.fingerprint_id,
            "is_new_fingerprint": is_new,
            "data": {
                "device_id": x_device_fingerprint,
                "database_id": fingerprint.id if hasattr(fingerprint, 'id') else None,
                "hardware_fingerprint": x_device_hardware[:8] + "..." if x_device_hardware else None,
                "storage_fingerprint": x_device_storage[:8] + "..." if x_device_storage else None,
                "screen_fingerprint": x_device_screen[:8] + "..." if x_device_screen else None,
                "system_fingerprint": x_device_system[:8] + "..." if x_device_system else None,
                "real_device_ip": real_device_ip,
                "request_ip": request.client.host if request.client else "unknown",
                "stored_at": get_tripoli_now().isoformat(),
                "status": "pending_approval"
            },
            "message": "تم تخزين البصمة الشاملة بنجاح مع المعرف الموحد"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ خطأ في تخزين البصمة الشاملة: {e}")
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"خطأ في تخزين البصمة الشاملة: {str(e)}"
        )


@router.get("/{fingerprint_id}/history")
async def get_device_fingerprint_history(
    fingerprint_id: str,
    limit: int = 20,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """
    الحصول على تاريخ وصول جهاز محدد
    """
    try:
        # التحقق من صحة المعاملات
        limit = min(max(limit, 1), 100)  # بين 1 و 100
        offset = max(offset, 0)

        # الحصول على خدمة التاريخ
        history_service = get_fingerprint_history_service(db)

        # جلب تاريخ الجهاز
        history_data = history_service.get_device_history(
            fingerprint_id=fingerprint_id,
            limit=limit,
            offset=offset
        )

        logger.info(f"📋 تم جلب {len(history_data)} سجل تاريخ للجهاز {fingerprint_id}")

        return {
            "success": True,
            "fingerprint_id": fingerprint_id,
            "history": history_data,
            "count": len(history_data),
            "limit": limit,
            "offset": offset,
            "message": f"تم جلب تاريخ الجهاز بنجاح"
        }

    except Exception as e:
        logger.error(f"❌ خطأ في جلب تاريخ الجهاز {fingerprint_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="خطأ في جلب تاريخ الجهاز"
        )


@router.get("/recent-activity")
async def get_recent_device_activity(
    limit: int = 20,
    db: Session = Depends(get_db)
):
    """
    الحصول على النشاط الأخير لجميع الأجهزة
    """
    try:
        # التحقق من صحة المعاملات
        limit = min(max(limit, 1), 50)  # بين 1 و 50

        # الحصول على خدمة التاريخ
        history_service = get_fingerprint_history_service(db)

        # جلب النشاط الأخير
        recent_activity = history_service.get_recent_activity(limit=limit)

        logger.info(f"📊 تم جلب {len(recent_activity)} نشاط أخير للأجهزة")

        return {
            "success": True,
            "recent_activity": recent_activity,
            "count": len(recent_activity),
            "limit": limit,
            "message": "تم جلب النشاط الأخير بنجاح"
        }

    except Exception as e:
        logger.error(f"❌ خطأ في جلب النشاط الأخير: {e}")
        raise HTTPException(
            status_code=500,
            detail="خطأ في جلب النشاط الأخير"
        )


@router.get("/pending/{device_id}")
async def get_pending_device_fingerprint(
    device_id: str,
    db: Session = Depends(get_db)
):
    """
    الحصول على بيانات البصمة للجهاز في انتظار الموافقة
    """
    try:
        # البحث عن الجهاز في قائمة الانتظار
        pending_stmt = select(PendingDevice).where(
            PendingDevice.device_id == device_id,
            PendingDevice.status == "pending"
        )
        pending_device = db.execute(pending_stmt).scalar_one_or_none()
        
        if not pending_device:
            return {
                "success": False,
                "message": "الجهاز غير موجود في قائمة الانتظار",
                "fingerprint": None
            }
        
        # البحث عن البصمة في قاعدة البيانات
        fingerprint_stmt = select(DeviceFingerprint).where(
            DeviceFingerprint.fingerprint_id == device_id,
            DeviceFingerprint.is_active == True
        )
        fingerprint = db.execute(fingerprint_stmt).scalar_one_or_none()
        
        result = {
            "success": True,
            "device_id": device_id,
            "pending_device": {
                "id": pending_device.id,
                "device_id": pending_device.device_id,
                "client_ip": pending_device.client_ip,
                "hostname": pending_device.hostname,
                "device_type": pending_device.device_type,
                "status": pending_device.status,
                "created_at": pending_device.created_at.isoformat() if pending_device.created_at else None,
                "fingerprint_data": json.loads(pending_device.fingerprint_data) if pending_device.fingerprint_data else None
            },
            "comprehensive_fingerprint": fingerprint.to_dict() if fingerprint else None,
            "has_comprehensive_fingerprint": fingerprint is not None
        }
        
        return result
        
    except Exception as e:
        logger.error(f"❌ خطأ في الحصول على بيانات البصمة: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"خطأ في الحصول على بيانات البصمة: {str(e)}"
        )


@router.get("/stats")
async def get_comprehensive_fingerprint_stats(db: Session = Depends(get_db)):
    """
    إحصائيات البصمات الشاملة
    """
    try:
        # عدد البصمات النشطة
        active_fingerprints_stmt = select(DeviceFingerprint).where(
            DeviceFingerprint.is_active == True
        )
        active_fingerprints = db.execute(active_fingerprints_stmt).scalars().all()
        
        # عدد الأجهزة في الانتظار مع بصمات
        pending_with_fingerprints_stmt = select(PendingDevice).where(
            PendingDevice.status == "pending",
            PendingDevice.fingerprint_data.isnot(None)
        )
        pending_with_fingerprints = db.execute(pending_with_fingerprints_stmt).scalars().all()
        
        return {
            "success": True,
            "stats": {
                "total_active_fingerprints": len(active_fingerprints),
                "pending_devices_with_fingerprints": len(pending_with_fingerprints),
                "last_updated": get_tripoli_now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"❌ خطأ في الحصول على إحصائيات البصمات: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"خطأ في الحصول على إحصائيات البصمات: {str(e)}"
        )
