"""
API endpoints لإدارة المنتجات المتقدمة
يستخدم الخدمات الجديدة مع تطبيق مبادئ البرمجة الكائنية
"""

from fastapi import APIRouter, Depends, HTTPException, Query, Body
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

from database.session import get_db
from models.user import User
from routers.auth import get_current_user
from services.product_management_service import ProductManagementService
from services.barcode_service import BarcodeService
from services.slug_service import SlugService
from services.product_validation_service import ProductValidationService
from schemas.product import ProductResponse, ProductCreate, ProductUpdate

router = APIRouter(prefix="/api/products/management", tags=["Product Management"])


# Pydantic models for requests
class BarcodeGenerateRequest(BaseModel):
    symbology: str = "CODE128"
    length: Optional[int] = None

class BarcodeValidateRequest(BaseModel):
    barcode: str
    symbology: str

class SlugGenerateRequest(BaseModel):
    text: str
    max_length: int = 100
    preserve_arabic: bool = False

class BulkUpdateRequest(BaseModel):
    product_ids: List[int]
    update_data: Dict[str, Any]


@router.post("/barcode/generate")
async def generate_barcode(
    request: BarcodeGenerateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """توليد باركود جديد"""
    try:
        barcode_service = BarcodeService.get_instance(db)
        result = barcode_service.generate_unique_barcode(
            symbology=request.symbology,
            max_attempts=10
        )
        
        if not result['success']:
            raise HTTPException(status_code=400, detail=result['error'])
        
        return {
            'success': True,
            'barcode': result['barcode'],
            'symbology': request.symbology,
            'attempts': result['attempts']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في توليد الباركود: {str(e)}")


@router.post("/barcode/validate")
async def validate_barcode(
    request: BarcodeValidateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """التحقق من صحة الباركود"""
    try:
        barcode_service = BarcodeService.get_instance(db)
        
        # التحقق من صحة التنسيق
        validation_result = barcode_service.validate_barcode(
            request.barcode, 
            request.symbology
        )
        
        if not validation_result['valid']:
            return {
                'valid': False,
                'error': validation_result['error']
            }
        
        # التحقق من التفرد
        uniqueness_result = barcode_service.check_barcode_uniqueness(request.barcode)
        
        return {
            'valid': True,
            'unique': uniqueness_result['unique'],
            'symbology': validation_result['symbology'],
            'symbology_name': validation_result['symbology_name'],
            'length': validation_result['length'],
            'uniqueness_info': uniqueness_result
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في التحقق من الباركود: {str(e)}")


@router.get("/barcode/symbologies")
async def get_barcode_symbologies(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على أنواع الباركود المدعومة"""
    try:
        barcode_service = BarcodeService.get_instance(db)
        symbologies = barcode_service.get_supported_symbologies()
        
        return {
            'success': True,
            'symbologies': symbologies
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في جلب أنواع الباركود: {str(e)}")


@router.post("/slug/generate")
async def generate_slug(
    request: SlugGenerateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """توليد رابط من النص"""
    try:
        slug_service = SlugService.get_instance(db)
        result = slug_service.generate_unique_slug(
            text=request.text,
            max_length=request.max_length,
            preserve_arabic=request.preserve_arabic
        )
        
        if not result['success']:
            raise HTTPException(status_code=400, detail=result['error'])
        
        return {
            'success': True,
            'slug': result['slug'],
            'original_text': result['original_text'],
            'attempts': result['attempts'],
            'fallback': result.get('fallback', False)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في توليد الرابط: {str(e)}")


@router.post("/slug/validate")
async def validate_slug(
    slug: str = Body(..., embed=True),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """التحقق من صحة الرابط"""
    try:
        slug_service = SlugService.get_instance(db)
        result = slug_service.validate_slug(slug)
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في التحقق من الرابط: {str(e)}")


@router.post("/slug/suggestions")
async def get_slug_suggestions(
    text: str = Body(..., embed=True),
    count: int = Body(5, embed=True),
    preserve_arabic: bool = Body(False, embed=True),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على اقتراحات للروابط"""
    try:
        slug_service = SlugService.get_instance(db)
        suggestions = slug_service.suggest_slugs(
            text=text,
            count=count,
            preserve_arabic=preserve_arabic
        )
        
        return {
            'success': True,
            'suggestions': suggestions
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في جلب اقتراحات الروابط: {str(e)}")


@router.post("/create")
async def create_product_advanced(
    product_data: Dict[str, Any] = Body(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """إنشاء منتج جديد باستخدام الخدمة المتقدمة"""
    try:
        product_service = ProductManagementService.get_instance(db)
        result = product_service.create_product(product_data, int(current_user.id))
        
        if not result['success']:
            if 'errors' in result:
                raise HTTPException(status_code=400, detail=result['errors'])
            else:
                raise HTTPException(status_code=400, detail=result['error'])
        
        return {
            'success': True,
            'product': result['product'],
            'message': result['message']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في إنشاء المنتج: {str(e)}")


@router.put("/{product_id}/update")
async def update_product_advanced(
    product_id: int,
    product_data: Dict[str, Any] = Body(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """تحديث منتج باستخدام الخدمة المتقدمة"""
    try:
        product_service = ProductManagementService.get_instance(db)
        result = product_service.update_product(product_id, product_data, int(current_user.id))
        
        if not result['success']:
            if 'errors' in result:
                raise HTTPException(status_code=400, detail=result['errors'])
            else:
                raise HTTPException(status_code=400, detail=result['error'])
        
        return {
            'success': True,
            'product': result['product'],
            'message': result['message']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في تحديث المنتج: {str(e)}")


@router.delete("/{product_id}/delete")
async def delete_product_advanced(
    product_id: int,
    force_delete: bool = Query(False, description="حذف قسري حتى لو كان المنتج مستخدم في فواتير"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """حذف منتج باستخدام الخدمة المتقدمة"""
    try:
        product_service = ProductManagementService.get_instance(db)
        result = product_service.delete_product(product_id, int(current_user.id), force_delete)
        
        if not result['success']:
            raise HTTPException(status_code=400, detail=result['error'])
        
        return {
            'success': True,
            'message': result['message']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في حذف المنتج: {str(e)}")


@router.get("/search")
async def search_products_advanced(
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    search: Optional[str] = Query(None),
    category_id: Optional[int] = Query(None),
    subcategory_id: Optional[int] = Query(None),
    brand_id: Optional[int] = Query(None),
    unit_id: Optional[int] = Query(None),
    is_active: Optional[bool] = Query(None),
    low_stock: bool = Query(False),
    zero_stock: bool = Query(False),
    price_min: Optional[float] = Query(None),
    price_max: Optional[float] = Query(None),
    sort_by: str = Query("created_at"),
    sort_order: str = Query("desc"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """البحث المتقدم في المنتجات"""
    try:
        filters = {
            'search': search,
            'category_id': category_id,
            'subcategory_id': subcategory_id,
            'brand_id': brand_id,
            'unit_id': unit_id,
            'is_active': is_active,
            'low_stock': low_stock,
            'zero_stock': zero_stock,
            'price_min': price_min,
            'price_max': price_max,
            'sort_by': sort_by,
            'sort_order': sort_order
        }
        
        product_service = ProductManagementService.get_instance(db)
        result = product_service.search_products(filters, page, limit)
        
        if not result['success']:
            raise HTTPException(status_code=400, detail=result['error'])
        
        return {
            'success': True,
            'products': result['products'],
            'pagination': result['pagination']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في البحث في المنتجات: {str(e)}")


@router.get("/statistics")
async def get_product_statistics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على إحصائيات المنتجات"""
    try:
        product_service = ProductManagementService.get_instance(db)
        result = product_service.get_product_statistics()
        
        if not result['success']:
            raise HTTPException(status_code=400, detail=result['error'])
        
        return {
            'success': True,
            'statistics': result['statistics']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في جلب إحصائيات المنتجات: {str(e)}")


@router.put("/bulk-update")
async def bulk_update_products(
    request: BulkUpdateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """تحديث مجموعة من المنتجات"""
    try:
        product_service = ProductManagementService.get_instance(db)
        result = product_service.bulk_update_products(
            request.product_ids,
            request.update_data,
            int(current_user.id)
        )
        
        if not result['success']:
            raise HTTPException(status_code=400, detail=result['error'])
        
        return {
            'success': True,
            'updated_count': result['updated_count'],
            'errors': result['errors'],
            'message': result['message']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في التحديث المجمع: {str(e)}")
