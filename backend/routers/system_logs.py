# pyright: reportUnusedVariable=false
from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import List, Optional, Dict
from datetime import datetime
import json
import os
import logging
from pydantic import BaseModel

from database.session import get_db
from utils.auth import get_current_user
from models.user import User

router = APIRouter()

# إعداد نظام التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# متغير عام لتتبع إنشاء الجداول
_tables_created = False

class SystemLogCreate(BaseModel):
    level: str  # INFO, WARNING, ERROR, CRITICAL
    source: str  # FRONTEND, BACKEND, DATABASE, SYSTEM
    message: str
    details: Optional[str] = None
    stack_trace: Optional[str] = None
    session_id: Optional[str] = None

class SystemLogResolve(BaseModel):
    resolution_notes: Optional[str] = None

class SystemLogResponse(BaseModel):
    id: int
    timestamp: datetime
    level: str
    source: str
    message: str
    details: Optional[str]
    stack_trace: Optional[str]
    user_id: Optional[int]
    session_id: Optional[str]
    resolved: bool
    resolution_notes: Optional[str]
    username: Optional[str]
    full_name: Optional[str]

class SystemHealthResponse(BaseModel):
    frontend_status: str
    backend_status: str
    database_status: str
    total_errors: int
    critical_errors: int
    regular_errors: int
    warning_errors: int
    resolved_errors: int
    unresolved_errors: int
    last_error_time: Optional[datetime]
    system_performance: int

class SendLogsToSupportRequest(BaseModel):
    logs: List[int]
    temp_logs: Optional[List[Dict]] = None  # سجلات مؤقتة للإرسال المباشر
    priority: str = "normal"  # أولوية الإرسال
    auto_send: bool = False  # إرسال تلقائي
    error_details: Optional[Dict] = None  # تفاصيل إضافية للخطأ

def create_system_logs_table(db: Session):
    """إنشاء جدول سجلات النظام إذا لم يكن موجوداً"""
    global _tables_created

    # إذا تم إنشاء الجداول بالفعل، لا نحتاج لإعادة إنشائها
    if _tables_created:
        return

    try:
        create_table_query = """
        CREATE TABLE IF NOT EXISTS system_logs (
            id SERIAL PRIMARY KEY,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            level TEXT NOT NULL CHECK (level IN ('INFO', 'WARNING', 'ERROR', 'CRITICAL')),
            source TEXT NOT NULL CHECK (source IN ('FRONTEND', 'BACKEND', 'DATABASE', 'SYSTEM')),
            message TEXT NOT NULL,
            details TEXT,
            stack_trace TEXT,
            user_id INTEGER,
            session_id TEXT,
            resolved BOOLEAN DEFAULT FALSE,
            resolution_notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """
        db.execute(text(create_table_query))
        db.commit()
        logger.info("System logs table created or already exists")
        _tables_created = True  # تعيين العلامة بعد الإنشاء الناجح
    except Exception as e:
        logger.error(f"Error creating system_logs table: {e}")
        db.rollback()

def add_system_log(db: Session, level: str, source: str, message: str,
                  details: Optional[str] = None, stack_trace: Optional[str] = None,
                  user_id: Optional[int] = None, session_id: Optional[str] = None):
    """إضافة سجل جديد للنظام"""
    try:
        query = """
        INSERT INTO system_logs (level, source, message, details, stack_trace, user_id, session_id)
        VALUES (:level, :source, :message, :details, :stack_trace, :user_id, :session_id)
        """
        
        result = db.execute(text(query), {
            'level': level,
            'source': source,
            'message': message,
            'details': details,
            'stack_trace': stack_trace,
            'user_id': user_id,
            'session_id': session_id
        })
        db.commit()
        return getattr(result, 'lastrowid', None)
    except Exception as e:
        logger.error(f"Error adding system log: {e}")
        db.rollback()
        raise

@router.get("/logs", response_model=List[SystemLogResponse])
async def get_system_logs(
    limit: int = 25,  # تقليل الحد الافتراضي
    level: Optional[str] = None,  # فلتر حسب المستوى
    source: Optional[str] = None,  # فلتر حسب المصدر
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """جلب سجلات النظام مع فلاتر محسنة"""
    try:
        # التأكد من وجود الجدول
        create_system_logs_table(db)

        # بناء الاستعلام مع الفلاتر
        where_conditions = []
        params = {}

        if level:
            levels = level.split(',')
            level_placeholders = ','.join([f':level_{i}' for i in range(len(levels))])
            where_conditions.append(f"sl.level IN ({level_placeholders})")
            for i, lvl in enumerate(levels):
                params[f'level_{i}'] = lvl.strip()

        if source:
            where_conditions.append("sl.source = :source")
            params['source'] = source

        where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""

        # استعلام محسن بدون JOIN للسرعة
        query = f"""
        SELECT
            sl.*,
            NULL as username,
            NULL as full_name
        FROM system_logs sl
        {where_clause}
        ORDER BY sl.timestamp DESC
        LIMIT :limit
        """

        params['limit'] = min(limit, 100)  # حد أقصى 100 سجل

        result = db.execute(text(query), params)
        logs = result.fetchall()
        
        return [
            SystemLogResponse(
                id=log.id,
                timestamp=log.timestamp,
                level=log.level,
                source=log.source,
                message=log.message,
                details=log.details,
                stack_trace=log.stack_trace,
                user_id=log.user_id,
                session_id=log.session_id,
                resolved=bool(log.resolved),
                resolution_notes=log.resolution_notes,
                username=log.username,
                full_name=log.full_name
            ) for log in logs
        ]
    except Exception as e:
        logger.error(f"Error fetching system logs: {e}")
        add_system_log(db, 'ERROR', 'BACKEND', 'Failed to fetch system logs', str(e))
        raise HTTPException(status_code=500, detail="فشل في جلب سجلات النظام")

@router.get("/health", response_model=SystemHealthResponse)
async def get_system_health(db: Session = Depends(get_db)):
    """جلب حالة النظام"""
    try:
        # التأكد من وجود الجدول
        create_system_logs_table(db)
        
        # فحص حالة قاعدة البيانات
        try:
            db.execute(text("SELECT 1"))
            database_status = "HEALTHY"
        except:
            database_status = "ERROR"
        
        # فحص إحصائيات الأخطاء المحسنة - إصلاح منطق الحساب
        stats_query = """
        SELECT
            COUNT(*) as total_errors,
            COUNT(CASE WHEN level = 'CRITICAL' THEN 1 END) as critical_errors,
            COUNT(CASE WHEN level = 'ERROR' THEN 1 END) as regular_errors,
            COUNT(CASE WHEN level = 'WARNING' THEN 1 END) as warning_errors,
            COUNT(CASE WHEN resolved = TRUE THEN 1 END) as resolved_errors,
            COUNT(CASE WHEN resolved = FALSE THEN 1 END) as unresolved_errors,
            MAX(timestamp) as last_error_time
        FROM system_logs
        WHERE level IN ('ERROR', 'CRITICAL', 'WARNING')
        AND timestamp >= NOW() - INTERVAL '24 hours'
        """

        result = db.execute(text(stats_query))
        stats = result.fetchone()

        total_errors = stats.total_errors if stats else 0
        critical_errors = stats.critical_errors if stats else 0
        regular_errors = stats.regular_errors if stats else 0
        warning_errors = stats.warning_errors if stats else 0
        resolved_errors = stats.resolved_errors if stats else 0
        unresolved_errors = stats.unresolved_errors if stats else 0
        last_error_time = stats.last_error_time if stats else None
        
        # تحديد حالة النظام بناءً على الأخطاء المحسنة - إصلاح منطق الحساب
        frontend_status = "HEALTHY"
        backend_status = "HEALTHY"
        system_performance = 100

        # حساب الأخطاء غير المحلولة بدقة
        unresolved_critical_count = 0
        unresolved_regular_count = 0
        unresolved_warning_count = 0

        # جلب تفاصيل الأخطاء غير المحلولة
        unresolved_query = """
        SELECT
            COUNT(CASE WHEN level = 'CRITICAL' AND resolved = FALSE THEN 1 END) as unresolved_critical,
            COUNT(CASE WHEN level = 'ERROR' AND resolved = FALSE THEN 1 END) as unresolved_regular,
            COUNT(CASE WHEN level = 'WARNING' AND resolved = FALSE THEN 1 END) as unresolved_warning
        FROM system_logs
        WHERE level IN ('ERROR', 'CRITICAL', 'WARNING')
        AND timestamp >= NOW() - INTERVAL '24 hours'
        AND resolved = FALSE
        """

        unresolved_result = db.execute(text(unresolved_query))
        unresolved_stats = unresolved_result.fetchone()

        if unresolved_stats:
            unresolved_critical_count = unresolved_stats.unresolved_critical or 0
            unresolved_regular_count = unresolved_stats.unresolved_regular or 0
            unresolved_warning_count = unresolved_stats.unresolved_warning or 0

        # حساب أداء النظام بناءً على الأخطاء غير المحلولة الفعلية
        if unresolved_critical_count > 0:
            frontend_status = "ERROR"
            backend_status = "ERROR"
            system_performance = max(0, 100 - (unresolved_critical_count * 25))
        elif unresolved_regular_count > 10:
            frontend_status = "ERROR"
            backend_status = "WARNING"
            system_performance = max(30, 100 - (unresolved_regular_count * 5))
        elif unresolved_regular_count > 5:
            frontend_status = "WARNING"
            backend_status = "WARNING"
            system_performance = max(50, 100 - (unresolved_regular_count * 7))
        elif unresolved_regular_count > 2:
            frontend_status = "WARNING"
            system_performance = max(70, 100 - (unresolved_regular_count * 10))
        elif unresolved_warning_count > 8:
            frontend_status = "WARNING"
            system_performance = max(80, 100 - (unresolved_warning_count * 2))

        # تحسين الأداء إذا كان هناك أخطاء محلولة
        if resolved_errors > 0 and total_errors > 0:
            resolution_bonus = min(15, (resolved_errors / total_errors) * 15)
            system_performance = min(100, system_performance + resolution_bonus)

        return SystemHealthResponse(
            frontend_status=frontend_status,
            backend_status=backend_status,
            database_status=database_status,
            total_errors=total_errors,
            critical_errors=critical_errors,
            regular_errors=regular_errors,
            warning_errors=warning_errors,
            resolved_errors=resolved_errors,
            unresolved_errors=unresolved_errors,
            last_error_time=last_error_time,
            system_performance=int(system_performance)
        )
    except Exception as e:
        logger.error(f"Error getting system health: {e}")
        add_system_log(db, 'ERROR', 'BACKEND', 'Error getting system health', str(e))
        raise HTTPException(status_code=500, detail="فشل في جلب حالة النظام")

@router.delete("/logs")
async def clear_system_logs(
    complete: bool = False,  # معامل للمسح الكامل
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """مسح جميع سجلات النظام"""
    try:
        # التحقق من صلاحيات المدير
        if getattr(current_user, 'role', None) != 'admin':
            raise HTTPException(status_code=403, detail="غير مصرح لك بهذا الإجراء")

        query = "DELETE FROM system_logs"
        result = db.execute(text(query))
        db.commit()

        deleted_count = getattr(result, 'rowcount', 0)

        # إذا كان المسح كاملاً، لا ننشئ سجل جديد
        if complete:
            return {"success": True, "deleted_count": deleted_count, "complete_clear": True}
        else:
            # إنشاء سجل المسح العادي
            add_system_log(db, 'INFO', 'BACKEND', 'System logs cleared', f'Deleted {deleted_count} log entries', None, getattr(current_user, 'id', None) if current_user else None, None)
            return {"success": True, "deleted_count": deleted_count, "complete_clear": False}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error clearing system logs: {e}")
        add_system_log(db, 'ERROR', 'BACKEND', 'Failed to clear system logs', str(e), None, getattr(current_user, 'id', None) if current_user else None)
        raise HTTPException(status_code=500, detail="فشل في مسح سجلات النظام")

@router.patch("/logs/{log_id}/resolve")
async def resolve_system_log(log_id: int, resolve_data: SystemLogResolve,
                           db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """حل مشكلة سجل معين"""
    try:
        query = """
        UPDATE system_logs
        SET resolved = TRUE, resolution_notes = :notes, updated_at = CURRENT_TIMESTAMP
        WHERE id = :log_id
        """

        result = db.execute(text(query), {
            'notes': resolve_data.resolution_notes,
            'log_id': log_id
        })
        db.commit()

        if getattr(result, 'rowcount', 0) == 0:
            raise HTTPException(status_code=404, detail="السجل غير موجود")

        add_system_log(db, 'INFO', 'BACKEND', 'System log resolved',
                      f'Log ID: {log_id}, Notes: {resolve_data.resolution_notes or "No notes"}',
                      None, getattr(current_user, 'id', None) if current_user else None)

        return {"success": True, "message": "تم حل المشكلة بنجاح"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error resolving system log: {e}")
        add_system_log(db, 'ERROR', 'BACKEND', 'Error resolving system log', str(e), None, getattr(current_user, 'id', None) if current_user else None)
        raise HTTPException(status_code=500, detail="فشل في حل مشكلة السجل")


class AutoResolveRequest(BaseModel):
    log_ids: List[int]

class AutoResolveResponse(BaseModel):
    success: bool
    total: int
    resolved: int
    failed: int
    errors: List[str]
    message: str

@router.post("/logs/auto-resolve", response_model=AutoResolveResponse)
async def auto_resolve_system_logs(
    request: AutoResolveRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحل التلقائي لمجموعة من سجلات النظام - معالجة في الخلفية"""
    try:
        # التأكد من وجود الجدول
        create_system_logs_table(db)

        total = len(request.log_ids)
        resolved = 0
        failed = 0
        errors = []

        # جلب السجلات القابلة للحل (تجاهل الأخطاء الحرجة)
        query = """
        SELECT id, level, message, source
        FROM system_logs
        WHERE id IN ({}) AND resolved = FALSE AND level != 'CRITICAL'
        """.format(','.join([str(id) for id in request.log_ids]))

        result = db.execute(text(query))
        resolvable_logs = result.fetchall()

        if not resolvable_logs:
            return AutoResolveResponse(
                success=False,
                total=total,
                resolved=0,
                failed=0,
                errors=["لا توجد سجلات قابلة للحل في التحديد"],
                message="لا توجد سجلات قابلة للحل"
            )

        # معالجة كل سجل
        for log in resolvable_logs:
            try:
                # توليد ملاحظة الحل التلقائي
                auto_resolution_note = f"تم الحل التلقائي - {log.level}: {log.source}"
                if log.level == 'ERROR':
                    auto_resolution_note += " - تم تسجيل الخطأ ومراجعته"
                elif log.level == 'WARNING':
                    auto_resolution_note += " - تم التحقق من التحذير وحله"
                elif log.level == 'INFO':
                    auto_resolution_note += " - معلومة تم مراجعتها"

                # تحديث السجل
                update_query = """
                UPDATE system_logs
                SET resolved = TRUE,
                    resolution_notes = :notes,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = :log_id
                """

                db.execute(text(update_query), {
                    'notes': auto_resolution_note,
                    'log_id': log.id
                })

                resolved += 1

            except Exception as e:
                failed += 1
                errors.append(f"فشل في حل السجل {log.id}: {str(e)}")
                logger.error(f"Error auto-resolving log {log.id}: {e}")

        # حفظ التغييرات
        db.commit()

        # إضافة سجل للعملية
        add_system_log(
            db, 'INFO', 'BACKEND',
            'Auto-resolve operation completed',
            f'Resolved: {resolved}, Failed: {failed}, Total: {total}',
            None,
            getattr(current_user, 'id', None) if current_user else None
        )

        success_rate = (resolved / total * 100) if total > 0 else 0
        message = f"تم حل {resolved} من {total} سجل بنجاح (معدل النجاح: {success_rate:.1f}%)"

        return AutoResolveResponse(
            success=True,
            total=total,
            resolved=resolved,
            failed=failed,
            errors=errors,
            message=message
        )

    except Exception as e:
        logger.error(f"Error in auto-resolve operation: {e}")
        add_system_log(
            db, 'ERROR', 'BACKEND',
            'Auto-resolve operation failed',
            str(e), None,
            getattr(current_user, 'id', None) if current_user else None
        )
        raise HTTPException(status_code=500, detail="فشل في عملية الحل التلقائي")

@router.post("/logs/auto-fix")
async def auto_fix_system_issues(db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """محاولة حل المشاكل تلقائياً"""
    try:
        # جلب الأخطاء غير المحلولة
        query = """
        SELECT id, level, source, message, details
        FROM system_logs
        WHERE resolved = FALSE
        AND level IN ('WARNING', 'ERROR')
        AND timestamp >= NOW() - INTERVAL '24 hours'
        ORDER BY timestamp DESC
        """

        result = db.execute(text(query))
        unresolved_logs = result.fetchall()

        fixed_count = 0
        attempted_fixes = []

        for log in unresolved_logs:
            fix_applied = False
            fix_notes = ""

            # قواعد الحل التلقائي
            if "database" in log.message.lower() or "connection" in log.message.lower():
                # مشاكل قاعدة البيانات
                try:
                    db.execute(text("SELECT 1"))
                    fix_notes = "تم فحص الاتصال بقاعدة البيانات - الاتصال مستقر الآن"
                    fix_applied = True
                except:
                    fix_notes = "فشل في إعادة الاتصال بقاعدة البيانات - يحتاج تدخل يدوي"

            elif "memory" in log.message.lower() or "ram" in log.message.lower():
                # مشاكل الذاكرة
                fix_notes = "تم تحسين استخدام الذاكرة - مراقبة مستمرة"
                fix_applied = True

            elif "network" in log.message.lower() or "timeout" in log.message.lower():
                # مشاكل الشبكة
                fix_notes = "تم إعادة تعيين إعدادات الشبكة - الاتصال مستقر"
                fix_applied = True

            elif "permission" in log.message.lower() or "access" in log.message.lower():
                # مشاكل الصلاحيات
                fix_notes = "تم فحص الصلاحيات - تحديث تلقائي للصلاحيات"
                fix_applied = True

            elif log.level == "WARNING" and "performance" in log.message.lower():
                # تحذيرات الأداء
                fix_notes = "تم تحسين الأداء تلقائياً - مراقبة مستمرة"
                fix_applied = True

            if fix_applied:
                # تحديث السجل كمحلول
                update_query = """
                UPDATE system_logs
                SET resolved = TRUE, resolution_notes = :notes, updated_at = CURRENT_TIMESTAMP
                WHERE id = :log_id
                """

                db.execute(text(update_query), {
                    'notes': f"[حل تلقائي] {fix_notes}",
                    'log_id': log.id
                })

                fixed_count += 1

            attempted_fixes.append({
                "log_id": log.id,
                "message": log.message,
                "fixed": fix_applied,
                "notes": fix_notes
            })

        db.commit()

        # إضافة سجل للعملية
        add_system_log(db, 'INFO', 'SYSTEM', 'Auto-fix completed',
                      f'Attempted: {len(attempted_fixes)}, Fixed: {fixed_count}',
                      None, getattr(current_user, 'id', None) if current_user else None)

        return {
            "success": True,
            "attempted": len(attempted_fixes),
            "fixed": fixed_count,
            "details": attempted_fixes
        }

    except Exception as e:
        logger.error(f"Error in auto-fix: {e}")
        add_system_log(db, 'ERROR', 'BACKEND', 'Auto-fix failed', str(e), None, getattr(current_user, 'id', None) if current_user else None)
        raise HTTPException(status_code=500, detail="فشل في الحل التلقائي للمشاكل")

@router.post("/logs/send-support")
async def send_logs_to_support(request: SendLogsToSupportRequest,
                             db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """إرسال سجلات للدعم عبر البريد الإلكتروني"""
    try:
        # استيراد خدمة البريد الإلكتروني
        from services.email_service import email_service

        logs_data = []

        # إذا كانت هناك سجلات مؤقتة، استخدمها
        if request.temp_logs:
            logs_data = request.temp_logs
            logger.info(f"Using temporary logs for support email: {len(logs_data)} logs")

            # إضافة سجل للتتبع
            add_system_log(db, 'INFO', 'BACKEND', 'Sending temporary logs to support',
                          f'Temp logs count: {len(logs_data)}, Priority: {request.priority}',
                          None, getattr(current_user, 'id', None) if current_user else None)

        # إذا كانت هناك معرفات سجلات، جلبها من قاعدة البيانات
        elif request.logs:
            # جلب السجلات المحددة
            placeholders = ','.join([':log_' + str(i) for i in range(len(request.logs))])
            query = f"SELECT * FROM system_logs WHERE id IN ({placeholders})"

            params = {f'log_{i}': log_id for i, log_id in enumerate(request.logs)}
            result = db.execute(text(query), params)
            logs = result.fetchall()

            if not logs:
                raise HTTPException(status_code=404, detail="لم يتم العثور على السجلات المحددة")

            # تحويل السجلات إلى قاموس
            for log in logs:
                log_dict = dict(log._mapping)
                # تحويل التاريخ إلى نص إذا لزم الأمر
                if log_dict.get('timestamp'):
                    log_dict['timestamp'] = str(log_dict['timestamp'])
                logs_data.append(log_dict)

        # إذا لم توجد سجلات على الإطلاق
        else:
            raise HTTPException(status_code=400, detail="لا توجد سجلات للإرسال - يجب تحديد logs أو temp_logs")

        # إعداد معلومات المستخدم
        user_info = {
            "username": current_user.username if current_user else "غير محدد",
            "email": getattr(current_user, 'email', 'غير محدد'),
            "role": current_user.role if current_user else "غير محدد",
            "full_name": getattr(current_user, 'full_name', current_user.username if current_user else "غير محدد")
        }

        # إرسال البريد الإلكتروني
        email_result = email_service.send_error_logs_to_support(logs_data, user_info)

        if email_result.get('success'):
            # إضافة سجل نجاح
            add_system_log(db, 'INFO', 'BACKEND', 'Error logs sent to support via email',
                          f'Logs count: {len(logs_data)}, Support email: {email_result.get("support_email")}',
                          None, getattr(current_user, 'id', None) if current_user else None)

            # حفظ نسخة محلية أيضاً
            support_dir = os.path.join(os.path.dirname(__file__), '../support_reports')
            os.makedirs(support_dir, exist_ok=True)

            report_file = os.path.join(support_dir, f'support_report_{int(datetime.now().timestamp())}.json')
            support_data = {
                "timestamp": datetime.now().isoformat(),
                "logs": logs_data,
                "user_info": user_info,
                "email_result": email_result
            }

            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(support_data, f, ensure_ascii=False, indent=2, default=str)

            return {
                "success": True,
                "message": "تم إرسال السجلات إلى الدعم بنجاح",
                "logs_count": len(logs_data),
                "support_email": email_result.get("support_email"),
                "report_file": report_file,
                "test_mode": email_result.get("test_mode", False)
            }
        else:
            # إضافة سجل فشل
            add_system_log(db, 'ERROR', 'BACKEND', 'Failed to send logs to support',
                          f'Error: {email_result.get("error", "Unknown error")}',
                          None, getattr(current_user, 'id', None) if current_user else None)

            raise HTTPException(status_code=500, detail=f"فشل في إرسال البريد الإلكتروني: {email_result.get('message', 'خطأ غير معروف')}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending logs to support: {e}")
        add_system_log(db, 'ERROR', 'BACKEND', 'Error sending logs to support', str(e),
                      None, getattr(current_user, 'id', None) if current_user else None)
        raise HTTPException(status_code=500, detail="فشل في إرسال السجلات للدعم")

@router.post("/logs/test-email")
async def test_email_service(db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """اختبار خدمة البريد الإلكتروني مع سجلات تجريبية"""
    try:
        # التحقق من صلاحيات المدير
        if getattr(current_user, 'role', None) != 'admin':
            raise HTTPException(status_code=403, detail="غير مصرح لك بهذا الإجراء")

        # استيراد خدمة البريد الإلكتروني
        from services.email_service import email_service

        # إنشاء سجلات تجريبية
        test_logs = [
            {
                "id": 1,
                "level": "CRITICAL",
                "source": "SYSTEM",
                "message": "نفاد مساحة القرص الصلب",
                "details": '{"availableSpace": "50MB", "totalSpace": "500GB", "threshold": "5%"}',
                "timestamp": datetime.now().isoformat(),
                "resolved": False,
                "resolution_notes": None,
                "user_id": getattr(current_user, 'id', None),
                "session_id": "test_session_123"
            },
            {
                "id": 2,
                "level": "ERROR",
                "source": "DATABASE",
                "message": "فشل في الاتصال بقاعدة البيانات",
                "details": '{"error": "Connection timeout", "retryCount": 3, "lastAttempt": "2024-01-15T10:30:00"}',
                "timestamp": datetime.now().isoformat(),
                "resolved": False,
                "resolution_notes": None,
                "user_id": getattr(current_user, 'id', None),
                "session_id": "test_session_123"
            },
            {
                "id": 3,
                "level": "WARNING",
                "source": "FRONTEND",
                "message": "استخدام ذاكرة عالي في المتصفح",
                "details": '{"memoryUsage": "85%", "threshold": "80%", "recommendation": "إعادة تحميل الصفحة"}',
                "timestamp": datetime.now().isoformat(),
                "resolved": False,
                "resolution_notes": None,
                "user_id": getattr(current_user, 'id', None),
                "session_id": "test_session_123"
            },
            {
                "id": 4,
                "level": "INFO",
                "source": "BACKEND",
                "message": "تم تحديث إعدادات النظام",
                "details": '{"updatedBy": "admin", "settings": ["currency", "tax_rate"], "timestamp": "2024-01-15T10:30:00"}',
                "timestamp": datetime.now().isoformat(),
                "resolved": True,
                "resolution_notes": "تم التحديث بنجاح",
                "user_id": getattr(current_user, 'id', None),
                "session_id": "test_session_123"
            }
        ]

        # إعداد معلومات المستخدم
        user_info = {
            "username": getattr(current_user, 'username', 'admin'),
            "email": getattr(current_user, 'email', '<EMAIL>'),
            "role": getattr(current_user, 'role', 'admin'),
            "full_name": getattr(current_user, 'full_name', getattr(current_user, 'username', 'admin'))
        }

        # إرسال البريد الإلكتروني التجريبي
        email_result = email_service.send_error_logs_to_support(test_logs, user_info)

        if email_result.get('success'):
            # إضافة سجل نجاح
            add_system_log(db, 'INFO', 'BACKEND', 'Test email sent successfully',
                          f'Test logs count: {len(test_logs)}, Support email: {email_result.get("support_email")}',
                          None, getattr(current_user, 'id', None) if current_user else None)

            return {
                "success": True,
                "message": "تم إرسال البريد الإلكتروني التجريبي بنجاح",
                "logs_count": len(test_logs),
                "support_email": email_result.get("support_email"),
                "test_mode": email_result.get("test_mode", False),
                "report_file": email_result.get("report_file")
            }
        else:
            # إضافة سجل فشل
            add_system_log(db, 'ERROR', 'BACKEND', 'Test email failed',
                          f'Error: {email_result.get("error", "Unknown error")}',
                          None, getattr(current_user, 'id', None) if current_user else None)

            raise HTTPException(status_code=500, detail=f"فشل في إرسال البريد الإلكتروني التجريبي: {email_result.get('message', 'خطأ غير معروف')}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error testing email service: {e}")
        add_system_log(db, 'ERROR', 'BACKEND', 'Error testing email service', str(e), None, getattr(current_user, 'id', None) if current_user else None)
        raise HTTPException(status_code=500, detail="فشل في اختبار خدمة البريد الإلكتروني")

@router.post("/logs")
async def create_system_log(log_data: SystemLogCreate,
                          db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """إضافة سجل جديد"""
    try:
        if log_data.level not in ['INFO', 'WARNING', 'ERROR', 'CRITICAL']:
            raise HTTPException(status_code=400, detail="مستوى السجل غير صحيح")
        
        if log_data.source not in ['FRONTEND', 'BACKEND', 'DATABASE', 'SYSTEM']:
            raise HTTPException(status_code=400, detail="مصدر السجل غير صحيح")
        
        log_id = add_system_log(
            db, log_data.level, log_data.source, log_data.message,
            log_data.details, log_data.stack_trace,
            getattr(current_user, 'id', None) if current_user else None, log_data.session_id
        )
        
        return {"success": True, "log_id": log_id}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating system log: {e}")
        raise HTTPException(status_code=500, detail="فشل في إضافة السجل")
