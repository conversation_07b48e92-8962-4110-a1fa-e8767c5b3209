"""
API endpoints لإدارة العلاقات بين الفروع والمستودعات - Branch Warehouse Router
يوفر جميع العمليات المطلوبة لإدارة العلاقات Many-to-Many
"""

import logging
from typing import Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from database.session import get_db
from auth.dependencies import get_current_user
from models.user import User
from services.branch_warehouse_service import get_branch_warehouse_service
from schemas.branch import (
    BranchWarehouseLinkCreate, BranchWarehouseLinkUpdate, BranchWarehouseLinkResponse
)

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/api/branch-warehouses",
    tags=["branch-warehouses"],
    responses={404: {"description": "الربط غير موجود"}}
)


@router.post("/link", response_model=Dict[str, Any], status_code=status.HTTP_201_CREATED)
async def link_branch_to_warehouse(
    link_data: BranchWarehouseLinkCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    ربط فرع بمستودع
    
    - **branch_id**: معرف الفرع (مطلوب)
    - **warehouse_id**: معرف المستودع (مطلوب)
    - **is_primary**: هل هذا المستودع الأساسي للفرع (افتراضي: false)
    - **priority**: أولوية المستودع للفرع (1 = أعلى أولوية، افتراضي: 1)
    """
    try:
        service = get_branch_warehouse_service(db)
        result = service.link_branch_to_warehouse(
            branch_id=link_data.branch_id,
            warehouse_id=link_data.warehouse_id,
            is_primary=link_data.is_primary,
            priority=link_data.priority
        )
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        logger.info(f"تم ربط الفرع {link_data.branch_id} بالمستودع {link_data.warehouse_id} بواسطة {current_user.username}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في ربط الفرع بالمستودع: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.delete("/unlink", response_model=Dict[str, Any])
async def unlink_branch_from_warehouse(
    branch_id: int = Query(..., description="معرف الفرع"),
    warehouse_id: int = Query(..., description="معرف المستودع"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    إلغاء ربط فرع من مستودع
    
    - **branch_id**: معرف الفرع
    - **warehouse_id**: معرف المستودع
    """
    try:
        service = get_branch_warehouse_service(db)
        result = service.unlink_branch_from_warehouse(branch_id, warehouse_id)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        logger.info(f"تم إلغاء ربط الفرع {branch_id} من المستودع {warehouse_id} بواسطة {current_user.username}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في إلغاء ربط الفرع من المستودع: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.post("/set-primary", response_model=Dict[str, Any])
async def set_primary_warehouse_for_branch(
    branch_id: int = Query(..., description="معرف الفرع"),
    warehouse_id: int = Query(..., description="معرف المستودع"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    تعيين مستودع أساسي للفرع
    
    - **branch_id**: معرف الفرع
    - **warehouse_id**: معرف المستودع
    
    ملاحظة: سيتم إلغاء الأساسية من جميع المستودعات الأخرى للفرع
    """
    try:
        service = get_branch_warehouse_service(db)
        result = service.set_primary_warehouse_for_branch(branch_id, warehouse_id)

        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )

        logger.info(f"تم تعيين المستودع {warehouse_id} كأساسي للفرع {branch_id} بواسطة {current_user.username}")
        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في تعيين المستودع الأساسي: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.put("/update-priority", response_model=Dict[str, Any])
async def update_warehouse_priority_for_branch(
    branch_id: int = Query(..., description="معرف الفرع"),
    warehouse_id: int = Query(..., description="معرف المستودع"),
    priority: int = Query(..., ge=1, le=100, description="الأولوية الجديدة (1-100)"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    تحديث أولوية مستودع للفرع

    - **branch_id**: معرف الفرع
    - **warehouse_id**: معرف المستودع
    - **priority**: الأولوية الجديدة (1 = أعلى أولوية، 100 = أقل أولوية)
    """
    try:
        service = get_branch_warehouse_service(db)
        result = service.update_warehouse_priority_for_branch(branch_id, warehouse_id, priority)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        logger.info(f"تم تحديث أولوية المستودع {warehouse_id} للفرع {branch_id} إلى {priority} بواسطة {current_user.username}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في تحديث أولوية المستودع: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.get("/branch/{branch_id}/warehouses", response_model=Dict[str, Any])
async def get_warehouses_for_branch(
    branch_id: int,
    include_inactive: bool = Query(False, description="تضمين المستودعات غير النشطة"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    الحصول على المستودعات المرتبطة بالفرع
    
    - **branch_id**: معرف الفرع
    - **include_inactive**: تضمين المستودعات غير النشطة (افتراضي: false)
    """
    try:
        service = get_branch_warehouse_service(db)
        result = service.get_warehouses_for_branch(branch_id, include_inactive)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب مستودعات الفرع: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.get("/warehouse/{warehouse_id}/branches", response_model=Dict[str, Any])
async def get_branches_for_warehouse(
    warehouse_id: int,
    include_inactive: bool = Query(False, description="تضمين الفروع غير النشطة"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    الحصول على الفروع المرتبطة بالمستودع
    
    - **warehouse_id**: معرف المستودع
    - **include_inactive**: تضمين الفروع غير النشطة (افتراضي: false)
    """
    try:
        service = get_branch_warehouse_service(db)
        result = service.get_branches_for_warehouse(warehouse_id, include_inactive)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب فروع المستودع: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.get("/branch/{branch_id}/primary-warehouse", response_model=Dict[str, Any])
async def get_primary_warehouse_for_branch(
    branch_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    الحصول على المستودع الأساسي للفرع
    
    - **branch_id**: معرف الفرع
    """
    try:
        service = get_branch_warehouse_service(db)
        result = service.get_primary_warehouse_for_branch(branch_id)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=result['error']
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب المستودع الأساسي: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.get("/branch/{branch_id}/available-warehouses", response_model=Dict[str, Any])
async def get_available_warehouses_for_branch(
    branch_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    الحصول على المستودعات المتاحة للربط بالفرع (غير مرتبطة بعد)

    - **branch_id**: معرف الفرع
    """
    try:
        service = get_branch_warehouse_service(db)
        result = service.get_available_warehouses_for_branch(branch_id)

        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب المستودعات المتاحة: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.get("/warehouse/{warehouse_id}/available-branches", response_model=Dict[str, Any])
async def get_available_branches_for_warehouse(
    warehouse_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    الحصول على الفروع المتاحة للربط بالمستودع (غير مرتبطة بعد)

    - **warehouse_id**: معرف المستودع
    """
    try:
        service = get_branch_warehouse_service(db)
        result = service.get_available_branches_for_warehouse(warehouse_id)

        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب الفروع المتاحة: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )
