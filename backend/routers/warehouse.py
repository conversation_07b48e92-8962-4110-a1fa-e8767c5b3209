"""
Router للمستودعات
يوفر API endpoints لإدارة المستودعات
"""

import logging
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from database.session import get_db
from models.user import User
from auth.dependencies import get_current_user
from services.warehouse_service import WarehouseService
from schemas.warehouse import (
    WarehouseCreate, WarehouseUpdate, WarehouseResponse
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/warehouses", tags=["warehouses"])


@router.post("/", response_model=Dict[str, Any], status_code=status.HTTP_201_CREATED)
async def create_warehouse(
    warehouse_data: WarehouseCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """إنشاء مستودع جديد"""
    try:
        warehouse_service = WarehouseService.get_instance(db)
        result = warehouse_service.create_warehouse(warehouse_data.dict())
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        logger.info(f"تم إنشاء مستودع جديد بواسطة {current_user.username}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في إنشاء المستودع: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.get("/", response_model=Dict[str, Any])
async def get_warehouses(
    include_inactive: bool = Query(False, description="تضمين المستودعات غير النشطة"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على جميع المستودعات"""
    try:
        warehouse_service = WarehouseService.get_instance(db)
        result = warehouse_service.get_all_warehouses(include_inactive)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب المستودعات: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.get("/{warehouse_id}", response_model=Dict[str, Any])
async def get_warehouse(
    warehouse_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على مستودع بالمعرف"""
    try:
        warehouse_service = WarehouseService.get_instance(db)
        result = warehouse_service.get_warehouse_by_id(warehouse_id)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=result['error']
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب المستودع: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.put("/{warehouse_id}", response_model=Dict[str, Any])
async def update_warehouse(
    warehouse_id: int,
    warehouse_data: WarehouseUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """تحديث بيانات المستودع"""
    try:
        warehouse_service = WarehouseService.get_instance(db)
        
        # تحويل البيانات وإزالة القيم الفارغة
        update_data = {k: v for k, v in warehouse_data.dict().items() if v is not None}
        
        result = warehouse_service.update_warehouse(warehouse_id, update_data)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        logger.info(f"تم تحديث المستودع {warehouse_id} بواسطة {current_user.username}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في تحديث المستودع: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.delete("/{warehouse_id}", response_model=Dict[str, Any])
async def delete_warehouse(
    warehouse_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """حذف المستودع"""
    try:
        warehouse_service = WarehouseService.get_instance(db)
        result = warehouse_service.delete_warehouse(warehouse_id)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        logger.info(f"تم حذف المستودع {warehouse_id} بواسطة {current_user.username}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في حذف المستودع: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.post("/{warehouse_id}/set-main", response_model=Dict[str, Any])
async def set_main_warehouse(
    warehouse_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """تعيين المستودع الرئيسي"""
    try:
        warehouse_service = WarehouseService.get_instance(db)
        result = warehouse_service.set_main_warehouse(warehouse_id)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
        logger.info(f"تم تعيين المستودع {warehouse_id} كرئيسي بواسطة {current_user.username}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في تعيين المستودع الرئيسي: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.get("/{warehouse_id}/capacity", response_model=Dict[str, Any])
async def get_warehouse_capacity(
    warehouse_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على حالة سعة المستودع"""
    try:
        warehouse_service = WarehouseService.get_instance(db)
        result = warehouse_service.get_warehouse_capacity_status(warehouse_id)
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=result['error']
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب حالة السعة: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.get("/summary/overview", response_model=Dict[str, Any])
async def get_warehouses_summary(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على ملخص المستودعات"""
    try:
        warehouse_service = WarehouseService.get_instance(db)
        result = warehouse_service.get_warehouse_summary()

        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب ملخص المستودعات: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )


@router.get("/with-inventory-status", response_model=Dict[str, Any])
async def get_warehouses_with_inventory_status(
    include_inactive: bool = Query(False, description="تضمين المستودعات غير النشطة"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على المستودعات مع معلومات حالة المخزون"""
    try:
        warehouse_service = WarehouseService.get_instance(db)
        result = warehouse_service.get_warehouses_with_inventory_status(include_inactive)

        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب المستودعات مع معلومات المخزون: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ داخلي في الخادم"
        )
