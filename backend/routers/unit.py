from fastapi import APIRouter, Depends, HTTPException, Response
from sqlalchemy.orm import Session
from sqlalchemy import and_
from typing import List, Optional
from database.session import get_db
from models.user import User
from models.unit import Unit
from schemas.unit import UnitCreate, UnitUpdate, UnitResponse
from utils.auth import get_current_user

router = APIRouter(prefix="/api/units", tags=["units"])

@router.post("/", response_model=UnitResponse)
async def create_unit(
    unit: UnitCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new unit."""
    try:
        # Check if unit name already exists
        existing_unit = db.query(Unit).filter(Unit.name == unit.name).first()
        if existing_unit:
            raise HTTPException(status_code=400, detail="اسم الوحدة موجود بالفعل")

        # Check if symbol already exists (if provided)
        if unit.symbol:
            existing_symbol = db.query(Unit).filter(Unit.symbol == unit.symbol).first()
            if existing_symbol:
                raise HTTPException(status_code=400, detail="رمز الوحدة موجود بالفعل")

        # Validate base unit if provided
        if unit.base_unit_id:
            base_unit = db.query(Unit).filter(Unit.id == unit.base_unit_id).first()
            if not base_unit:
                raise HTTPException(status_code=400, detail="الوحدة الأساسية غير موجودة")

        db_unit = Unit(
            **unit.model_dump(),
            created_by=current_user.id
        )
        db.add(db_unit)
        db.commit()
        db.refresh(db_unit)
        return db_unit
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"فشل في إنشاء الوحدة: {str(e)}")

@router.get("/", response_model=List[UnitResponse])
async def get_units(
    skip: int = 0,
    limit: int = 100,
    active_only: bool = False,  # تغيير الافتراضي لعرض جميع الوحدات
    unit_type: Optional[str] = None,
    search: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get all units."""
    query = db.query(Unit)
    
    if active_only:
        query = query.filter(Unit.is_active == True)
    
    if unit_type:
        query = query.filter(Unit.unit_type == unit_type)
    
    if search:
        query = query.filter(Unit.name.ilike(f"%{search}%"))
    
    units = query.offset(skip).limit(limit).all()
    return units

@router.get("/types", response_model=List[str])
async def get_unit_types(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get list of unique unit types."""
    types = db.query(Unit.unit_type).distinct().filter(
        Unit.unit_type.isnot(None),
        Unit.is_active == True
    ).all()
    return [type_[0] for type_ in types if type_[0]]

@router.get("/{unit_id}", response_model=UnitResponse)
async def get_unit(
    unit_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific unit."""
    unit = db.query(Unit).filter(Unit.id == unit_id).first()
    if not unit:
        raise HTTPException(status_code=404, detail="الوحدة غير موجودة")
    return unit

@router.put("/{unit_id}", response_model=UnitResponse)
async def update_unit(
    unit_id: int,
    unit_update: UnitUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update a unit."""
    try:
        db_unit = db.query(Unit).filter(Unit.id == unit_id).first()
        if not db_unit:
            raise HTTPException(status_code=404, detail="الوحدة غير موجودة")

        # Check if new name already exists (if name is being updated)
        if unit_update.name and unit_update.name != db_unit.name:
            existing_unit = db.query(Unit).filter(
                and_(Unit.name == unit_update.name, Unit.id != unit_id)
            ).first()
            if existing_unit:
                raise HTTPException(status_code=400, detail="اسم الوحدة موجود بالفعل")

        # Check if new symbol already exists (if symbol is being updated)
        if unit_update.symbol and unit_update.symbol != db_unit.symbol:
            existing_symbol = db.query(Unit).filter(
                and_(Unit.symbol == unit_update.symbol, Unit.id != unit_id)
            ).first()
            if existing_symbol:
                raise HTTPException(status_code=400, detail="رمز الوحدة موجود بالفعل")

        # Validate base unit if being updated
        if unit_update.base_unit_id and unit_update.base_unit_id != db_unit.base_unit_id:
            if unit_update.base_unit_id == unit_id:
                raise HTTPException(status_code=400, detail="لا يمكن أن تكون الوحدة أساسية لنفسها")
            
            base_unit = db.query(Unit).filter(Unit.id == unit_update.base_unit_id).first()
            if not base_unit:
                raise HTTPException(status_code=400, detail="الوحدة الأساسية غير موجودة")

        # Update fields
        update_data = unit_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_unit, field, value)
        
        db_unit.updated_by = current_user.id
        db.commit()
        db.refresh(db_unit)
        return db_unit
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"فشل في تحديث الوحدة: {str(e)}")

@router.delete("/{unit_id}")
async def delete_unit(
    unit_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete a unit."""
    try:
        db_unit = db.query(Unit).filter(Unit.id == unit_id).first()
        if not db_unit:
            raise HTTPException(status_code=404, detail="الوحدة غير موجودة")

        # Check if unit has products
        if db_unit.products:
            raise HTTPException(
                status_code=400, 
                detail="لا يمكن حذف الوحدة لأنها مرتبطة بمنتجات. قم بإعادة تصنيف المنتجات أولاً"
            )

        # Check if unit is used as base unit for other units
        dependent_units = db.query(Unit).filter(Unit.base_unit_id == unit_id).all()
        if dependent_units:
            raise HTTPException(
                status_code=400, 
                detail="لا يمكن حذف الوحدة لأنها مستخدمة كوحدة أساسية لوحدات أخرى"
            )

        db.delete(db_unit)
        db.commit()
        return {"message": "تم حذف الوحدة بنجاح"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"فشل في حذف الوحدة: {str(e)}")

@router.get("/list/names", response_model=List[str])
async def get_unit_names(
    active_only: bool = True,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get list of unit names for dropdowns."""
    query = db.query(Unit.name)
    if active_only:
        query = query.filter(Unit.is_active == True)
    
    units = query.all()
    return [unit[0] for unit in units]
