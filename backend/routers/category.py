from fastapi import APIRouter, Depends, HTTPException, Response
from sqlalchemy.orm import Session
from sqlalchemy import and_
from typing import List, Optional
from database.session import get_db
from models.user import User
from models.category import Category, Subcategory
from schemas.category import (
    CategoryCreate, CategoryUpdate, CategoryResponse, CategoryWithSubcategories,
    SubcategoryCreate, SubcategoryUpdate, SubcategoryResponse
)
from utils.auth import get_current_user

router = APIRouter(prefix="/api/categories", tags=["categories"])

# Category endpoints
@router.post("/", response_model=CategoryResponse)
async def create_category(
    category: CategoryCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new category."""
    try:
        # Check if category name already exists
        existing_category = db.query(Category).filter(Category.name == category.name).first()
        if existing_category:
            raise HTTPException(status_code=400, detail="اسم الفئة موجود بالفعل")

        db_category = Category(
            **category.model_dump(),
            created_by=current_user.id
        )
        db.add(db_category)
        db.commit()
        db.refresh(db_category)
        return db_category
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"فشل في إنشاء الفئة: {str(e)}")

@router.get("/", response_model=List[CategoryResponse])
async def get_categories(
    skip: int = 0,
    limit: int = 100,
    active_only: bool = True,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get all categories."""
    query = db.query(Category)
    if active_only:
        query = query.filter(Category.is_active == True)
    
    categories = query.offset(skip).limit(limit).all()
    return categories

@router.get("/with-subcategories", response_model=List[CategoryWithSubcategories])
async def get_categories_with_subcategories(
    skip: int = 0,
    limit: int = 100,
    active_only: bool = False,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get all categories with their subcategories."""
    query = db.query(Category)
    if active_only:
        query = query.filter(Category.is_active == True)

    categories = query.offset(skip).limit(limit).all()
    return categories

@router.get("/{category_id}", response_model=CategoryResponse)
async def get_category(
    category_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific category."""
    category = db.query(Category).filter(Category.id == category_id).first()
    if not category:
        raise HTTPException(status_code=404, detail="الفئة غير موجودة")
    return category

@router.put("/{category_id}", response_model=CategoryResponse)
async def update_category(
    category_id: int,
    category_update: CategoryUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update a category."""
    try:
        db_category = db.query(Category).filter(Category.id == category_id).first()
        if not db_category:
            raise HTTPException(status_code=404, detail="الفئة غير موجودة")

        # Check if new name already exists (if name is being updated)
        if category_update.name and category_update.name != db_category.name:
            existing_category = db.query(Category).filter(
                and_(Category.name == category_update.name, Category.id != category_id)
            ).first()
            if existing_category:
                raise HTTPException(status_code=400, detail="اسم الفئة موجود بالفعل")

        # Update fields
        update_data = category_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_category, field, value)
        
        db_category.updated_by = current_user.id
        db.commit()
        db.refresh(db_category)
        return db_category
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"فشل في تحديث الفئة: {str(e)}")

@router.delete("/{category_id}")
async def delete_category(
    category_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete a category."""
    try:
        db_category = db.query(Category).filter(Category.id == category_id).first()
        if not db_category:
            raise HTTPException(status_code=404, detail="الفئة غير موجودة")

        # Check if category has products
        if db_category.products:
            raise HTTPException(
                status_code=400, 
                detail="لا يمكن حذف الفئة لأنها تحتوي على منتجات. قم بإعادة تصنيف المنتجات أولاً"
            )

        db.delete(db_category)
        db.commit()
        return {"message": "تم حذف الفئة بنجاح"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"فشل في حذف الفئة: {str(e)}")

# Subcategory endpoints
@router.post("/{category_id}/subcategories", response_model=SubcategoryResponse)
async def create_subcategory(
    category_id: int,
    subcategory: SubcategoryCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new subcategory."""
    try:
        # Verify category exists
        category = db.query(Category).filter(Category.id == category_id).first()
        if not category:
            raise HTTPException(status_code=404, detail="الفئة الأساسية غير موجودة")

        # Check if subcategory name already exists in this category
        existing_subcategory = db.query(Subcategory).filter(
            and_(Subcategory.name == subcategory.name, Subcategory.category_id == category_id)
        ).first()
        if existing_subcategory:
            raise HTTPException(status_code=400, detail="اسم الفئة الفرعية موجود بالفعل في هذه الفئة")

        subcategory_data = subcategory.model_dump()
        subcategory_data['category_id'] = category_id
        
        db_subcategory = Subcategory(
            **subcategory_data,
            created_by=current_user.id
        )
        db.add(db_subcategory)
        db.commit()
        db.refresh(db_subcategory)
        return db_subcategory
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"فشل في إنشاء الفئة الفرعية: {str(e)}")

@router.get("/{category_id}/subcategories", response_model=List[SubcategoryResponse])
async def get_subcategories(
    category_id: int,
    active_only: bool = True,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get all subcategories for a category."""
    query = db.query(Subcategory).filter(Subcategory.category_id == category_id)
    if active_only:
        query = query.filter(Subcategory.is_active == True)
    
    subcategories = query.all()
    return subcategories

@router.put("/subcategories/{subcategory_id}", response_model=SubcategoryResponse)
async def update_subcategory(
    subcategory_id: int,
    subcategory_update: SubcategoryUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update a subcategory."""
    try:
        db_subcategory = db.query(Subcategory).filter(Subcategory.id == subcategory_id).first()
        if not db_subcategory:
            raise HTTPException(status_code=404, detail="الفئة الفرعية غير موجودة")

        # Check if new name already exists in the same category (if name is being updated)
        if subcategory_update.name and subcategory_update.name != db_subcategory.name:
            existing_subcategory = db.query(Subcategory).filter(
                and_(
                    Subcategory.name == subcategory_update.name,
                    Subcategory.category_id == db_subcategory.category_id,
                    Subcategory.id != subcategory_id
                )
            ).first()
            if existing_subcategory:
                raise HTTPException(status_code=400, detail="اسم الفئة الفرعية موجود بالفعل في هذه الفئة")

        # Update fields
        update_data = subcategory_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_subcategory, field, value)
        
        db_subcategory.updated_by = current_user.id
        db.commit()
        db.refresh(db_subcategory)
        return db_subcategory
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"فشل في تحديث الفئة الفرعية: {str(e)}")

@router.delete("/subcategories/{subcategory_id}")
async def delete_subcategory(
    subcategory_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete a subcategory."""
    try:
        db_subcategory = db.query(Subcategory).filter(Subcategory.id == subcategory_id).first()
        if not db_subcategory:
            raise HTTPException(status_code=404, detail="الفئة الفرعية غير موجودة")

        # Check if subcategory has products
        if db_subcategory.products:
            raise HTTPException(
                status_code=400, 
                detail="لا يمكن حذف الفئة الفرعية لأنها تحتوي على منتجات. قم بإعادة تصنيف المنتجات أولاً"
            )

        db.delete(db_subcategory)
        db.commit()
        return {"message": "تم حذف الفئة الفرعية بنجاح"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"فشل في حذف الفئة الفرعية: {str(e)}")
