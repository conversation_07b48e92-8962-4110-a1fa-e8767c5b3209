from fastapi import APIRouter, Depends, HTTPException, status, Header, Request
from fastapi.security import OAuth2P<PERSON>word<PERSON>earer, OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from sqlalchemy import select
from datetime import datetime, timedelta, timezone
from typing import Optional
from jose import JWTError, jwt
from passlib.context import CryptContext
import os
from dotenv import load_dotenv

from database.session import get_db
from models.user import User, UserRole
from schemas.user import UserCreate, Token, TokenData, UserResponse

load_dotenv()

router = APIRouter(prefix="/api/auth", tags=["auth"])

# Security configuration
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-here")
ALGORITHM = os.getenv("ALGORITHM", "HS256")
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "60"))  # Increased to 60 minutes
REFRESH_TOKEN_EXPIRE_DAYS = int(os.getenv("REFRESH_TOKEN_EXPIRE_DAYS", "7"))

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/auth/token")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=15)
    to_encode.update({"exp": expire, "type": "access"})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def create_refresh_token(data: dict) -> str:
    to_encode = data.copy()
    expire = datetime.now(timezone.utc) + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode.update({"exp": expire, "type": "refresh"})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
) -> User:
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username = payload.get("sub")
        token_type = payload.get("type")

        if username is None or token_type != "access":
            raise credentials_exception

        username = str(username)  # Ensure username is str

        role_value = payload.get("role")
        role = UserRole(role_value) if role_value is not None else None
        token_data = TokenData(username=username, role=role)  # type: ignore
    except JWTError:
        raise credentials_exception

    stmt = select(User).where(User.username == token_data.username)
    user = db.execute(stmt).scalar_one_or_none()
    if user is None:
        raise credentials_exception
    return user

async def get_current_active_user(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> User:
    stmt = select(User.is_active).where(User.id == current_user.id)
    is_active = db.scalar(stmt)
    if not bool(is_active):  # Explicitly convert to bool
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

@router.post("/token", response_model=Token)
async def login_for_access_token(
    request: Request,
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    # First get the user with all fields
    stmt = select(User).where(User.username == form_data.username)
    user = db.execute(stmt).scalar_one_or_none()

    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Then get the hashed password as a string
    stmt = select(User.hashed_password).where(User.id == user.id)
    result = db.execute(stmt).scalar()
    hashed_password = str(result) if result is not None else None

    if hashed_password is None or not verify_password(form_data.password, hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Get the role value safely
    stmt = select(User.role).where(User.id == user.id)
    role_result = db.execute(stmt).scalar()
    role_value = None
    if isinstance(role_result, UserRole):
        role_value = role_result.value

    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username, "role": role_value},
        expires_delta=access_token_expires
    )

    refresh_token = create_refresh_token(
        data={"sub": user.username, "role": role_value}
    )

    # ✅ تسجيل حدث تسجيل الدخول في سجل تاريخ الأجهزة
    try:
        from services.user_activity_logger import user_activity_logger
        import asyncio

        # تسجيل حدث تسجيل الدخول بشكل غير متزامن
        asyncio.create_task(
            user_activity_logger.log_user_login(
                request=request,
                username=user.username,
                additional_data={
                    "role": role_value,
                    "login_method": "password",
                    "success": True
                }
            )
        )

    except Exception as e:
        # لا نريد أن يفشل تسجيل الدخول بسبب خطأ في التسجيل
        print(f"⚠️ خطأ في تسجيل حدث تسجيل الدخول: {e}")

    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer"
    }

@router.post("/refresh", response_model=Token)
async def refresh_access_token(
    authorization: str = Header(...),
    db: Session = Depends(get_db)
):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        # Extract token from Authorization header
        scheme, token = authorization.split()
        if scheme.lower() != 'bearer':
            print(f"Invalid scheme: {scheme}")
            raise credentials_exception

        # Decode and validate refresh token
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username = payload.get("sub")
        token_type = payload.get("type")
        role = payload.get("role")

        print(f"Refresh token payload: username={username}, type={token_type}, role={role}")

        if username is None or token_type != "refresh":
            print(f"Invalid token type or missing username: {token_type}")
            raise credentials_exception

        # Verify user still exists
        stmt = select(User).where(User.username == username)
        user = db.execute(stmt).scalar_one_or_none()
        if user is None:
            print(f"User not found: {username}")
            raise credentials_exception

        print(f"User found: {user.username}, role: {user.role}")

    except (JWTError, ValueError) as e:
        print(f"JWT error or value error: {e}")
        raise credentials_exception
    except Exception as e:
        print(f"Unexpected error in refresh_access_token: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )

    # Create new access token
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": username, "role": role},
        expires_delta=access_token_expires
    )

    # Create new refresh token
    refresh_token = create_refresh_token(
        data={"sub": username, "role": role}
    )

    print(f"New tokens created for user: {username}")

    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer"
    }

@router.post("/register", response_model=UserResponse)
async def register_user(
    user: UserCreate,
    db: Session = Depends(get_db)
):
    # Check if username already exists
    stmt = select(User).where(User.username == user.username)
    db_user = db.execute(stmt).scalar_one_or_none()
    if db_user is not None:
        raise HTTPException(
            status_code=400,
            detail="Username already registered"
        )

    # Create new user
    hashed_password = get_password_hash(user.password)
    db_user = User(
        username=user.username,
        email=user.email,
        full_name=user.full_name,
        hashed_password=hashed_password,
        role=user.role
    )

    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

@router.post("/logout")
async def logout_user(
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """
    تسجيل خروج المستخدم مع تسجيل الحدث في سجل تاريخ الأجهزة
    """
    try:
        # ✅ تسجيل حدث تسجيل الخروج في سجل تاريخ الأجهزة
        from services.user_activity_logger import user_activity_logger
        import asyncio

        # الحصول على اسم المستخدم والدور بشكل آمن من قاعدة البيانات
        from sqlalchemy import select
        from database.session import get_db

        db = next(get_db())
        try:
            # الحصول على اسم المستخدم
            username_stmt = select(User.username).where(User.id == current_user.id)
            username = db.execute(username_stmt).scalar()
            username = str(username) if username else "unknown"

            # الحصول على الدور
            role_stmt = select(User.role).where(User.id == current_user.id)
            role_result = db.execute(role_stmt).scalar()
            user_role = role_result.value if isinstance(role_result, UserRole) else None

        except Exception as e:
            username = "unknown"
            user_role = None
            print(f"خطأ في الحصول على معلومات المستخدم: {e}")
        finally:
            db.close()

        # تسجيل حدث تسجيل الخروج بشكل غير متزامن
        asyncio.create_task(
            user_activity_logger.log_user_logout(
                request=request,
                username=username,
                additional_data={
                    "role": user_role,
                    "logout_method": "manual",
                    "success": True
                }
            )
        )

        return {
            "success": True,
            "message": "تم تسجيل الخروج بنجاح",
            "username": username
        }

    except Exception as e:
        print(f"⚠️ خطأ في تسجيل حدث تسجيل الخروج: {e}")
        return {
            "success": True,  # نعيد success=True لأن تسجيل الخروج نجح، فقط التسجيل فشل
            "message": "تم تسجيل الخروج بنجاح",
            "username": current_user.username,
            "warning": "فشل في تسجيل الحدث"
        }

@router.get("/me", response_model=UserResponse)
async def read_users_me(
    current_user: User = Depends(get_current_active_user)
):
    return current_user