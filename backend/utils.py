import os
import subprocess
import platform
from pathlib import Path
from dotenv import load_dotenv

def check_requirements(venv_path):
    """فحص المتطلبات المثبتة"""
    pip_path = venv_path / "bin" / "pip"
    if platform.system() == "Windows":
        pip_path = venv_path / "Scripts" / "pip.exe"

    try:
        # فحص uvicorn
        result = subprocess.run([str(pip_path), "show", "uvicorn"],
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ uvicorn غير مثبت في البيئة الافتراضية")
            print("❌ uvicorn not installed in virtual environment")
            print("💡 يرجى تشغيل: pip install -r requirements.txt")
            print("💡 Please run: pip install -r requirements.txt")
            return False

        # فحص fastapi
        result = subprocess.run([str(pip_path), "show", "fastapi"],
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ fastapi غير مثبت في البيئة الافتراضية")
            print("❌ fastapi not installed in virtual environment")
            return False

        print("✅ جميع المتطلبات الأساسية مثبتة")
        print("✅ All basic requirements installed")
        return True

    except Exception as e:
        print(f"⚠️ خطأ في فحص المتطلبات: {e}")
        print(f"⚠️ Error checking requirements: {e}")
        return True  # نتابع حتى لو فشل الفحص

def setup_environment():
    """إعداد متغيرات البيئة"""
    current_dir = Path(__file__).parent.absolute()
    load_dotenv(dotenv_path=current_dir / ".env")

    # إعداد متغيرات البيئة الأساسية
    os.environ["PYTHONPATH"] = str(current_dir)
    os.environ["PYTHONUNBUFFERED"] = "1"

    print("✅ تم إعداد متغيرات البيئة")
