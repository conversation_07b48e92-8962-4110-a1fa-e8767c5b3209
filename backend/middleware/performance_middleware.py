"""
Middleware لتحسين الأداء ومنع التجميد - محسن
يستخدم نظام مراقبة الأداء الموحد
"""

import time
import asyncio
import logging
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from fastapi import Request, Response
from fastapi.responses import JSONResponse
import psutil
import threading
from collections import defaultdict, deque

# استيراد النظام الموحد
from core.performance_manager import performance_manager

logger = logging.getLogger(__name__)

class PerformanceMiddleware:
    def __init__(self):
        self.request_counts = defaultdict(int)
        self.request_times = defaultdict(deque)
        self.active_requests = 0
        self.max_concurrent_requests = 100  # زيادة الحد الأقصى للطلبات المتزامنة أكثر
        self.rate_limit_window = 60  # ثانية
        self.max_requests_per_window = 1000  # زيادة الحد الأقصى للطلبات في الدقيقة أكثر
        self.slow_request_threshold = 5.0  # ثانية
        self.memory_threshold = 80  # نسبة مئوية
        self.cpu_threshold = 70  # نسبة مئوية

        # إحصائيات الأداء
        self.performance_stats = {
            'total_requests': 0,
            'slow_requests': 0,
            'failed_requests': 0,
            'average_response_time': 0.0,
            'peak_concurrent_requests': 0,
            'last_reset': datetime.now()
        }

        # بدء مراقبة الموارد
        self.start_resource_monitoring()

    def start_resource_monitoring(self):
        """بدء مراقبة موارد النظام"""
        def monitor_resources():
            while True:
                try:
                    # مراقبة استخدام المعالج والذاكرة
                    cpu_percent = psutil.cpu_percent(interval=1)
                    memory_percent = psutil.virtual_memory().percent

                    if isinstance(cpu_percent, (int, float)) and cpu_percent > self.cpu_threshold:
                        logger.warning(f"High CPU usage detected: {cpu_percent}%")

                    if isinstance(memory_percent, (int, float)) and memory_percent > self.memory_threshold:
                        logger.warning(f"High memory usage detected: {memory_percent}%")

                    time.sleep(10)  # فحص كل 10 ثوانٍ
                except Exception as e:
                    logger.error(f"Error in resource monitoring: {e}")
                    time.sleep(30)

        monitor_thread = threading.Thread(target=monitor_resources, daemon=True)
        monitor_thread.start()

    async def __call__(self, request: Request, call_next):
        start_time = time.time()
        client_ip = request.client.host if request.client else "unknown"

        try:
            # استثناء health checks وطلبات الخادم الرئيسي من Rate Limiting
            is_health_check = (
                request.url.path in ["/api/system/health", "/api/system/database/health", "/api/settings/public"] or
                request.url.path.startswith("/api/system/")
            )

            # استثناء الخادم الرئيسي من Rate Limiting الصارم
            is_main_server = client_ip in ["127.0.0.1", "localhost"] or client_ip == "*************"

            # فحص الحد الأقصى للطلبات المتزامنة (إلا للـ health checks والخادم الرئيسي)
            if not is_health_check and not is_main_server and self.active_requests >= self.max_concurrent_requests:
                logger.warning(f"Too many concurrent requests: {self.active_requests}")
                return JSONResponse(
                    status_code=429,
                    content={
                        "error": "Too many concurrent requests",
                        "message": "الخادم مشغول، يرجى المحاولة لاحقاً",
                        "retry_after": 5
                    }
                )

            # فحص معدل الطلبات (إلا للـ health checks والخادم الرئيسي)
            if not is_health_check and not is_main_server and not self.check_rate_limit(client_ip):
                logger.warning(f"Rate limit exceeded for IP: {client_ip}")
                return JSONResponse(
                    status_code=429,
                    content={
                        "error": "Rate limit exceeded",
                        "message": "تم تجاوز الحد المسموح من الطلبات",
                        "retry_after": 60
                    }
                )

            # زيادة عداد الطلبات النشطة
            self.active_requests += 1
            self.performance_stats['peak_concurrent_requests'] = max(
                self.performance_stats['peak_concurrent_requests'],
                self.active_requests
            )

            # معالجة الطلب
            response = await call_next(request)

            # حساب وقت الاستجابة
            process_time = time.time() - start_time

            # تحديث الإحصائيات
            self.update_performance_stats(process_time, response.status_code)

            # إضافة headers الأداء
            response.headers["X-Process-Time"] = str(round(process_time, 3))
            response.headers["X-Active-Requests"] = str(self.active_requests)

            # تسجيل الطلبات البطيئة
            if process_time > self.slow_request_threshold:
                logger.warning(
                    f"Slow request detected: {request.method} {request.url.path} "
                    f"took {process_time:.2f}s from {client_ip}"
                )

            return response

        except Exception as e:
            logger.error(f"Error in performance middleware: {e}")
            self.performance_stats['failed_requests'] += 1

            # إرجاع استجابة خطأ مناسبة
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Internal server error",
                    "message": "حدث خطأ في الخادم"
                }
            )
        finally:
            # تقليل عداد الطلبات النشطة
            self.active_requests = max(0, self.active_requests - 1)

    def check_rate_limit(self, client_ip: str) -> bool:
        """فحص معدل الطلبات للعميل"""
        now = time.time()

        # تنظيف الطلبات القديمة
        while (self.request_times[client_ip] and
               now - self.request_times[client_ip][0] > self.rate_limit_window):
            self.request_times[client_ip].popleft()

        # فحص عدد الطلبات في النافزة الزمنية
        if len(self.request_times[client_ip]) >= self.max_requests_per_window:
            return False

        # إضافة الطلب الحالي
        self.request_times[client_ip].append(now)
        return True

    def update_performance_stats(self, process_time: float, status_code: int):
        """تحديث إحصائيات الأداء - يستخدم النظام الموحد"""
        # تحديث الإحصائيات المحلية
        self.performance_stats['total_requests'] += 1

        if process_time > self.slow_request_threshold:
            self.performance_stats['slow_requests'] += 1

        if status_code >= 400:
            self.performance_stats['failed_requests'] += 1

        # حساب متوسط وقت الاستجابة
        total_requests = self.performance_stats['total_requests']
        current_avg = self.performance_stats['average_response_time']
        self.performance_stats['average_response_time'] = (
            (current_avg * (total_requests - 1) + process_time) / total_requests
        )

        # تحديث النظام الموحد
        try:
            performance_manager.update_request_stats(process_time, status_code)
        except Exception as e:
            logger.warning(f"فشل في تحديث إحصائيات النظام الموحد: {e}")

    def get_performance_stats(self) -> Dict:
        """الحصول على إحصائيات الأداء"""
        stats = self.performance_stats.copy()
        stats.update({
            'active_requests': self.active_requests,
            'uptime_seconds': (datetime.now() - stats['last_reset']).total_seconds(),
            'system_resources': {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_percent': psutil.disk_usage('/').percent
            }
        })
        return stats

    def reset_stats(self):
        """إعادة تعيين الإحصائيات"""
        self.performance_stats = {
            'total_requests': 0,
            'slow_requests': 0,
            'failed_requests': 0,
            'average_response_time': 0.0,
            'peak_concurrent_requests': 0,
            'last_reset': datetime.now()
        }
        self.request_counts.clear()
        self.request_times.clear()
        logger.info("Performance stats reset")

# إنشاء instance عام
performance_middleware = PerformanceMiddleware()

async def add_performance_middleware(request: Request, call_next):
    """دالة middleware للاستخدام مع FastAPI"""
    return await performance_middleware(request, call_next)

def get_performance_stats():
    """الحصول على إحصائيات الأداء"""
    return performance_middleware.get_performance_stats()

def reset_performance_stats():
    """إعادة تعيين إحصائيات الأداء"""
    performance_middleware.reset_stats()
