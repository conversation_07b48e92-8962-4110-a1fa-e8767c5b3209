"""
Middleware الأمان الموحدة - SmartPOS
middleware شاملة ومحسنة لإدارة أمان الأجهزة والتحكم في الوصول
"""

import time
import asyncio
import logging
from typing import Dict, Set, Optional, Any
from fastapi import Request, Response
from fastapi.responses import JSONResponse, RedirectResponse
from starlette.middleware.base import BaseHTTPMiddleware
from sqlalchemy.orm import Session

from database.session import get_db
from services.device_security import DeviceSecurityService
from services.unified_fingerprint_service import get_unified_fingerprint_service
# from utils.device_detection import device_detector  # تم إزالة هذا الملف
from services.device_tracker import device_tracker

logger = logging.getLogger(__name__)


class UnifiedSecurityMiddleware(BaseHTTPMiddleware):
    """
    Middleware الأمان الموحدة - تجمع جميع وظائف الأمان في مكان واحد
    """

    def __init__(self, app):
        super().__init__(app)
        self.device_security = DeviceSecurityService()

        # نظام cache محسن للأداء
        self.device_cache: Dict[str, Dict] = {}
        self.cache_ttl = 300  # 5 دقائق

        # نظام throttling ذكي
        self.device_last_seen: Dict[str, float] = {}
        self.throttle_seconds = 30

        # نظام rate limiting للأجهزة المرفوضة
        self.rejected_devices: Dict[str, Dict] = {}
        self.rejection_cooldown = 600  # 10 دقائق
        self.max_rejections_per_hour = 5

        # المسارات المستثناة من فحص الأمان
        self.excluded_paths: Set[str] = {
            "/static", "/favicon", "/ws/", "/_next", "/assets", "/images", 
            "/css", "/js", "/fonts", "/icons", "/api/system/health",
            "/api/system/performance", "/docs", "/redoc", "/openapi.json"
        }

        # المسارات المستثناة من التتبع
        self.ignored_tracking_paths: Set[str] = {
            "/static", "/favicon", "/ws/", "/_next", "/api/settings/connected-devices",
            "/assets", "/images", "/css", "/js", "/fonts", "/icons"
        }

        # endpoints المصادقة الحساسة
        self.auth_endpoints: Set[str] = {
            "/api/auth/login", "/api/auth/logout", "/api/auth/verify",
            "/api/settings/update-device-user", "/api/device/approve",
            "/api/settings/", "/api/admin/", "/api/backup/", "/api/restore/"
        }

        # نظام مراقبة الأنشطة المشبوهة
        self.suspicious_activity: Dict[str, Dict] = {}
        self.suspicious_threshold = 10  # عدد الطلبات المشبوهة قبل الحظر المؤقت

        # نظام الحماية من الهجمات
        self.attack_patterns: Set[str] = {
            "sql", "script", "union", "select", "drop", "delete", "insert",
            "update", "exec", "eval", "javascript:", "vbscript:", "onload",
            "onerror", "onclick", "../", "..\\", "etc/passwd", "cmd.exe"
        }

    async def dispatch(self, request: Request, call_next):
        """
        معالج الطلبات الرئيسي - فحص شامل للأمان
        """

        # السماح لطلبات OPTIONS (CORS preflight) بالمرور مباشرة
        if request.method == "OPTIONS":
            return await call_next(request)

        # تخطي فحص الأمان للمسارات المستثناة
        if self._should_skip_security_check(request):
            return await call_next(request)

        # معالجة خاصة لـ /api/device/status
        if request.url.path == "/api/device/status":
            return await call_next(request)

        try:
            # الحصول على معلومات الجهاز
            client_ip = self._get_client_ip(request)
            user_agent = request.headers.get("user-agent", "")
            normalized_ip = client_ip.strip()

            # تحديد نوع الجهاز
            is_main_server = normalized_ip in ['*************', '127.0.0.1', 'localhost']
            is_remote = not is_main_server or self._is_external_access(request)

            # الحصول على معرف الجهاز باستخدام الخدمة الموحدة
            device_id, _ = await self._get_device_identity(
                client_ip, user_agent, request, normalized_ip
            )

            # استثناء خاص للخادم الرئيسي - تجاوز فحص الموافقة
            if self._is_main_server_request(client_ip, user_agent, request) or self._is_main_server_device(device_id, client_ip):
                logger.info(f"✅ [UNIFIED-SEC] Main server access granted: {device_id}")
                # السماح للطلب بالمرور دون تدخل
                response = await call_next(request)
                return response

            logger.debug(f"🔍 [UNIFIED-SEC] Device: {device_id}, Remote: {is_remote}, Path: {request.url.path}")

            # فحص الأنشطة المشبوهة والهجمات أولاً
            suspicious_result = await self._check_suspicious_activity(device_id, request)
            if suspicious_result:
                return suspicious_result

            # فحص الأمان للأجهزة البعيدة فقط
            if is_remote:
                # فحص خاص لـ endpoints المصادقة
                if self._is_auth_endpoint(request):
                    auth_result = await self._check_auth_endpoint_access(device_id, normalized_ip, request)
                    if auth_result:
                        return auth_result
                else:
                    # فحص الأمان العادي
                    security_result = await self._check_device_security(device_id, normalized_ip, request)
                    if security_result:
                        self._log_redirect(device_id, request)
                        return security_result

            # تتبع نشاط الجهاز (مع throttling)
            await self._track_device_activity(
                normalized_ip, user_agent, request, device_id, is_main_server
            )

            # متابعة الطلب
            return await call_next(request)

        except Exception as e:
            logger.error(f"❌ [UNIFIED-SEC] Middleware error: {e}")
            # السماح بالطلب في حالة الخطأ لتجنب كسر النظام
            return await call_next(request)

    async def _check_suspicious_activity(self, device_id: str, request: Request) -> Optional[Response]:
        """فحص الأنشطة المشبوهة والهجمات"""
        try:
            current_time = time.time()
            path = request.url.path.lower()
            query_string = str(request.url.query).lower()
            user_agent = request.headers.get("user-agent", "").lower()

            # فحص أنماط الهجمات في المسار والاستعلام
            suspicious_content = f"{path} {query_string} {user_agent}"

            # عدد الأنماط المشبوهة المكتشفة
            suspicious_count = 0
            detected_patterns = []

            for pattern in self.attack_patterns:
                if pattern in suspicious_content:
                    suspicious_count += 1
                    detected_patterns.append(pattern)

            # إذا تم اكتشاف أنماط مشبوهة
            if suspicious_count > 0:
                # تسجيل النشاط المشبوه
                if device_id not in self.suspicious_activity:
                    self.suspicious_activity[device_id] = {
                        'count': 0,
                        'patterns': set(),
                        'first_seen': current_time,
                        'last_seen': current_time
                    }

                activity = self.suspicious_activity[device_id]
                activity['count'] += suspicious_count
                activity['patterns'].update(detected_patterns)
                activity['last_seen'] = current_time

                logger.warning(f"🚨 [UNIFIED-SEC] Suspicious activity detected: {device_id}, patterns: {detected_patterns}")

                # إذا تجاوز العتبة، حظر مؤقت
                if activity['count'] >= self.suspicious_threshold:
                    logger.error(f"🚫 [UNIFIED-SEC] Blocking device due to suspicious activity: {device_id}")
                    return JSONResponse(
                        status_code=403,
                        content={
                            "detail": "Suspicious activity detected",
                            "blocked": True,
                            "reason": "security_violation"
                        }
                    )

                # تحذير للأنشطة المشبوهة الأقل
                elif suspicious_count >= 3:
                    return JSONResponse(
                        status_code=400,
                        content={
                            "detail": "Invalid request detected",
                            "warning": True
                        }
                    )

            # تنظيف البيانات القديمة (أكثر من ساعة)
            if len(self.suspicious_activity) > 100:
                old_devices = [
                    dev_id for dev_id, data in self.suspicious_activity.items()
                    if current_time - data['last_seen'] > 3600
                ]
                for dev_id in old_devices:
                    del self.suspicious_activity[dev_id]

            return None

        except Exception as e:
            logger.error(f"❌ [UNIFIED-SEC] Error checking suspicious activity: {e}")
            return None

    async def _get_device_identity(
        self,
        client_ip: str,
        user_agent: str,
        request: Request,
        _: str  # normalized_ip غير مستخدم حالياً
    ) -> tuple[str, dict]:
        """
        الحصول على هوية الجهاز باستخدام الخدمة الموحدة
        """
        try:
            request_headers = dict(request.headers)
            
            # فحص البصمة المتقدمة
            device_fingerprint = request_headers.get('x-device-fingerprint', '')
            hardware_fp = request_headers.get('x-device-hardware', '')
            storage_fp = request_headers.get('x-device-storage', '')
            screen_fp = request_headers.get('x-device-screen', '')
            system_fp = request_headers.get('x-device-system', '')

            if device_fingerprint and device_fingerprint.startswith('fp_') and hardware_fp and storage_fp:
                # استخدام البصمة المتقدمة وتخزينها في قاعدة البيانات
                logger.debug(f"🔐 [UNIFIED-SEC] Using advanced fingerprint: {device_fingerprint}")

                # ملاحظة: تم تعطيل إنشاء البصمة هنا لمنع التكرار
                # البصمة ستُنشأ في comprehensive_fingerprint.py فقط
                logger.debug(f"🔐 [UNIFIED-SEC] البصمة المتقدمة موجودة، ستُعالج في comprehensive_fingerprint: {device_fingerprint}")

                return device_fingerprint, {
                    'device_id': device_fingerprint,
                    'client_ip': client_ip,
                    'detection_method': 'advanced_fingerprint',
                    'hardware_fingerprint': hardware_fp,
                    'storage_fingerprint': storage_fp,
                    'screen_fingerprint': screen_fp,
                    'system_fingerprint': system_fp
                }
            else:
                # استخدام الخدمة الموحدة
                db = next(get_db())
                unified_service = get_unified_fingerprint_service(db)
                device_data = unified_service.generate_unified_device_id(
                    client_ip, user_agent, request_headers
                )
                return device_data['device_id'], device_data

        except Exception as e:
            logger.error(f"❌ [UNIFIED-SEC] Error getting device identity: {e}")
            # رفض الجهاز - البصمة المتقدمة مطلوبة
            rejected_id = f"REJECTED_ERROR_{hash(f'{client_ip}_{user_agent}')}"
            return rejected_id, {'device_id': rejected_id, 'client_ip': client_ip, 'status': 'rejected'}

    async def _check_auth_endpoint_access(
        self, 
        device_id: str, 
        client_ip: str, 
        request: Request
    ) -> Optional[Response]:
        """
        فحص خاص للوصول إلى endpoints المصادقة
        """
        try:
            db = next(get_db())
            try:
                # فحص إذا كان الجهاز معتمد
                if self._is_device_approved(device_id, db):
                    logger.info(f"✅ [UNIFIED-SEC] Approved device accessing auth endpoint: {device_id}")
                    return None

                # فحص إذا كان محظور
                if self.device_security.is_device_blocked(device_id, client_ip, db):
                    logger.warning(f"🚫 [UNIFIED-SEC] Blocked device trying auth endpoint: {device_id}")
                    return JSONResponse(
                        status_code=403,
                        content={"detail": "Device is blocked"}
                    )

                # للأجهزة غير المعتمدة: السماح بتسجيل الدخول فقط (ليس endpoints أخرى حساسة)
                if request.url.path == "/api/auth/token" or request.url.path == "/api/auth/login":
                    logger.info(f"🔓 [UNIFIED-SEC] Allowing login attempt for unapproved device: {device_id}")
                    return None  # السماح بمحاولة تسجيل الدخول

                # جهاز غير معتمد يحاول الوصول لـ endpoint حساس آخر
                logger.warning(f"⚠️ [UNIFIED-SEC] Unapproved device accessing sensitive endpoint: {device_id}")
                return self._handle_pending_approval(request, device_id, client_ip)

            finally:
                db.close()

        except Exception as e:
            logger.error(f"❌ [UNIFIED-SEC] Error checking auth endpoint access: {e}")
            return None

    async def _check_device_security(
        self, 
        device_id: str, 
        client_ip: str, 
        request: Request
    ) -> Optional[Response]:
        """
        فحص أمان الجهاز الشامل
        """
        try:
            # فحص rate limiting أولاً
            if self._is_device_rate_limited(device_id, client_ip):
                return self._handle_rate_limited_device(request, device_id)

            # فحص cache
            cache_key = f"security_{device_id}"
            current_time = time.time()

            if cache_key in self.device_cache:
                cache_entry = self.device_cache[cache_key]
                if current_time - cache_entry['timestamp'] < self.cache_ttl:
                    if cache_entry['blocked']:
                        return self._handle_blocked_device(request, device_id)
                    elif cache_entry['pending']:
                        return self._handle_pending_approval(request, device_id, client_ip)
                    else:
                        return None  # مسموح

            # فحص قاعدة البيانات
            db = next(get_db())
            try:
                # فحص الحظر
                if self.device_security.is_device_blocked(device_id, client_ip, db):
                    self._update_cache(cache_key, blocked=True, current_time=current_time)
                    logger.warning(f"🚫 [UNIFIED-SEC] Blocked device request: {device_id}")
                    return self._handle_blocked_device(request, device_id)

                # فحص طلب الموافقة
                if self.device_security.requires_approval(db):
                    if self._is_device_approved(device_id, db):
                        self._update_cache(cache_key, approved=True, current_time=current_time)
                        logger.info(f"✅ [UNIFIED-SEC] Approved device access: {device_id}")
                        return None

                    # جهاز في انتظار الموافقة أو جديد
                    if self._is_device_pending_approval(device_id, db):
                        # ✅ تحديث معلومات الجهاز المنتظر إذا كانت ناقصة
                        self._update_pending_device_info(device_id, client_ip, request.headers.get("user-agent", ""), db)
                        self._update_cache(cache_key, pending=True, current_time=current_time)
                        logger.info(f"⏳ [UNIFIED-SEC] Pending device request: {device_id}")
                    else:
                        logger.info(f"🆕 [UNIFIED-SEC] New device needs approval: {device_id}")
                        self._add_to_pending_approval(device_id, client_ip, request.headers.get("user-agent", ""), db)
                        self._update_cache(cache_key, pending=True, current_time=current_time)

                    return self._handle_pending_approval(request, device_id, client_ip)

                # الجهاز مسموح له
                self._update_cache(cache_key, approved=True, current_time=current_time)
                return None

            finally:
                db.close()

        except Exception as e:
            logger.error(f"❌ [UNIFIED-SEC] Error checking device security: {e}")
            return None

    async def _track_device_activity(
        self, 
        client_ip: str, 
        user_agent: str, 
        request: Request, 
        device_id: str, 
        is_main_server: bool
    ):
        """
        تتبع نشاط الجهاز مع نظام throttling ذكي
        """
        try:
            path = request.url.path

            # فلترة الطلبات غير المهمة
            if not self._should_track_request(path, request):
                return

            # نظام throttling ذكي
            current_time = time.time()
            throttle_key = f"track_{device_id}"
            last_seen = self.device_last_seen.get(throttle_key, 0)

            # الطلبات المهمة لا تخضع للـ throttling
            important_paths = ['/api/auth/login', '/api/auth/logout', '/api/settings/update-device-user']
            is_important = any(path.startswith(imp_path) for imp_path in important_paths)

            # تخطي التسجيل للطلبات المتكررة العادية
            if not is_main_server and not is_important and current_time - last_seen < self.throttle_seconds:
                return

            # تحديث وقت آخر تسجيل
            self.device_last_seen[throttle_key] = current_time

            # الحصول على معلومات المستخدم
            current_user = await self._extract_current_user(request)
            request_headers = dict(request.headers)

            # تسجيل النشاط فوري
            try:
                await device_tracker.register_device_activity(
                    client_ip=client_ip,
                    user_agent=user_agent,
                    current_user=current_user,
                    request_headers=request_headers
                )
                logger.debug(f"📝 [UNIFIED-SEC] Device activity logged: {device_id}")
            except Exception as e:
                logger.warning(f"⚠️ [UNIFIED-SEC] Failed to log device activity: {e}")

            # تسجيل إضافي عبر device_tracker
            if is_main_server:
                asyncio.create_task(
                    device_tracker.register_device_activity(client_ip, user_agent, current_user)
                )
            else:
                asyncio.create_task(
                    device_tracker.register_remote_device_activity(
                        client_ip, user_agent, current_user, request_headers
                    )
                )

            # تنظيف الذاكرة
            self._cleanup_memory(current_time)

        except Exception as e:
            logger.debug(f"⚠️ [UNIFIED-SEC] Error tracking device activity: {e}")

    def _should_skip_security_check(self, request: Request) -> bool:
        """فحص ما إذا كان يجب تخطي فحص الأمان"""
        path = request.url.path
        return any(path.startswith(excluded) for excluded in self.excluded_paths)

    def _should_track_request(self, path: str, request: Request) -> bool:
        """فحص ما إذا كان يجب تتبع الطلب"""
        return (
            not any(path.startswith(ignored) for ignored in self.ignored_tracking_paths) and
            request.method in ["GET", "POST"] and
            not path.endswith((".css", ".js", ".png", ".jpg", ".ico", ".svg", ".woff", ".woff2"))
        )

    def _is_auth_endpoint(self, request: Request) -> bool:
        """فحص ما إذا كان endpoint مصادقة حساس"""
        return any(request.url.path.startswith(auth_path) for auth_path in self.auth_endpoints)

    def _is_external_access(self, request: Request) -> bool:
        """فحص ما إذا كان الوصول خارجي"""
        host = request.headers.get("host", "")
        return not (host.startswith("localhost") or host.startswith("127.0.0.1"))

    def _get_client_ip(self, request: Request) -> str:
        """الحصول على IP العميل"""
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip.strip()

        return request.client.host if request.client else "unknown"

    def _is_main_server_request(self, client_ip: str, user_agent: str, request: Request) -> bool:
        """
        التحقق من أن الطلب من الخادم الرئيسي
        """
        try:
            # استخدام url_manager للتحقق من IP الخادم الرئيسي
            from utils.url_manager import url_manager

            if not url_manager.is_main_server_ip(client_ip):
                return False

            # فحص User Agent للتأكد من أنه متصفح وليس bot
            is_browser = any(browser in user_agent.lower() for browser in ['chrome', 'firefox', 'safari', 'edge'])

            return is_browser

        except Exception as e:
            logger.error(f"❌ [UNIFIED-SEC] Error checking main server request: {e}")
            return False

    def _is_main_server_device(self, device_id: str, client_ip: str) -> bool:
        """
        التحقق من أن الجهاز هو الخادم الرئيسي بناءً على معرف الجهاز أو IP
        """
        try:
            # قائمة معرفات الخادم الرئيسي المعروفة
            main_server_ids = [
                'main_server_primary',
                'main_server_6b74625ff918',
                'fp_3tae9f'
            ]

            # فحص معرف الجهاز
            if device_id in main_server_ids:
                return True

            # استخدام url_manager للتحقق من IP الخادم الرئيسي
            from utils.url_manager import url_manager
            return url_manager.is_main_server_ip(client_ip)

        except Exception as e:
            logger.error(f"❌ [UNIFIED-SEC] Error checking main server device: {e}")
            return False

    def _update_cache(self, cache_key: str, blocked: bool = False, pending: bool = False, approved: bool = False, current_time: Optional[float] = None):
        """تحديث cache الأمان"""
        if current_time is None:
            current_time = time.time()
            
        self.device_cache[cache_key] = {
            'blocked': blocked,
            'pending': pending,
            'approved': approved,
            'timestamp': current_time
        }

    def _cleanup_memory(self, current_time: float):
        """تنظيف الذاكرة من البيانات القديمة"""
        if len(self.device_last_seen) > 1000:
            old_entries = [k for k, v in self.device_last_seen.items() if current_time - v > 3600]
            for old_entry in old_entries:
                del self.device_last_seen[old_entry]

    def _log_redirect(self, device_id: str, request: Request):
        """تسجيل إعادة التوجيه مع تقليل التكرار"""
        redirect_log_key = f"redirect_{device_id}_{request.url.path}"
        current_time = time.time()
        if redirect_log_key not in self.device_last_seen or current_time - self.device_last_seen[redirect_log_key] > 300:
            logger.info(f"🔄 [UNIFIED-SEC] Redirecting device: {device_id} - {request.url.path}")
            self.device_last_seen[redirect_log_key] = current_time

    # باقي الدوال المساعدة سيتم إضافتها في الجزء التالي...
    
    def _is_device_approved(self, device_id: str, db: Session) -> bool:
        """فحص ما إذا كان الجهاز معتمد"""
        try:
            return self.device_security.is_device_approved(device_id, db)
        except Exception as e:
            logger.error(f"❌ [UNIFIED-SEC] Error checking device approval: {e}")
            return False

    def _is_device_pending_approval(self, device_id: str, db: Session) -> bool:
        """فحص ما إذا كان الجهاز في انتظار الموافقة"""
        try:
            # فحص إذا كان الجهاز في قائمة انتظار الموافقة
            from models.device_security import PendingDevice
            from sqlalchemy import select

            stmt = select(PendingDevice).where(PendingDevice.device_id == device_id)
            pending_device = db.execute(stmt).scalar_one_or_none()
            return pending_device is not None
        except Exception as e:
            logger.error(f"❌ [UNIFIED-SEC] Error checking pending approval: {e}")
            return False

    def _add_to_pending_approval(self, device_id: str, client_ip: str, user_agent: str, db: Session):
        """إضافة الجهاز لقائمة انتظار الموافقة مع معلومات كاملة"""
        try:
            from models.device_security import PendingDevice
            from datetime import datetime
            from utils.datetime_utils import get_tripoli_now

            # ✅ استخراج معلومات الجهاز من user_agent
            platform_info = self._extract_platform_info_from_user_agent(user_agent or "")

            # ✅ تحديد اسم الجهاز بناءً على النظام
            hostname = self._generate_device_hostname(platform_info, client_ip)

            # تسجيل المعلومات المستخرجة للتأكد
            logger.info(f"🆕 إضافة جهاز جديد للانتظار: {device_id}")
            logger.info(f"   📱 اسم الجهاز: {hostname}")
            logger.info(f"   💻 النظام: {platform_info.get('system', 'غير معروف')}")
            logger.info(f"   🌐 المنصة: {platform_info.get('platform', 'غير معروف')}")
            logger.info(f"   🔍 المتصفح: {platform_info.get('browser', 'غير معروف')}")
            logger.info(f"   📍 IP: {client_ip}")

            # إنشاء جهاز جديد في انتظار الموافقة مع معلومات كاملة
            pending_device = PendingDevice(
                device_id=device_id,
                client_ip=client_ip,
                hostname=hostname,  # ✅ اسم الجهاز
                device_type=platform_info.get('device_type', 'غير محدد'),  # ✅ نوع الجهاز
                system=platform_info.get('system', 'غير معروف'),  # ✅ نظام التشغيل
                platform=platform_info.get('platform', 'غير معروف'),  # ✅ المنصة
                browser=platform_info.get('browser', 'غير معروف'),  # ✅ المتصفح
                user_agent=user_agent,
                first_access=get_tripoli_now(),
                last_access=get_tripoli_now(),
                access_count=1,
                status="pending",
                created_at=get_tripoli_now()
            )

            db.add(pending_device)
            db.commit()
            logger.info(f"✅ [UNIFIED-SEC] Added device to pending approval with full info: {device_id} ({hostname})")
        except Exception as e:
            logger.error(f"❌ [UNIFIED-SEC] Error adding to pending approval: {e}")
            db.rollback()

    def _update_pending_device_info(self, device_id: str, client_ip: str, user_agent: str, db: Session):
        """تحديث معلومات الجهاز المنتظر إذا كانت ناقصة"""
        try:
            from models.device_security import PendingDevice
            from sqlalchemy import select
            from utils.datetime_utils import get_tripoli_now

            # البحث عن الجهاز المنتظر
            stmt = select(PendingDevice).where(PendingDevice.device_id == device_id)
            pending_device = db.execute(stmt).scalar_one_or_none()

            if not pending_device:
                return

            # ✅ فحص ما إذا كانت المعلومات ناقصة (تحديث دائماً لضمان الدقة)
            needs_update = True  # تحديث دائماً لضمان أحدث المعلومات

            # تسجيل المعلومات الحالية للمقارنة
            logger.debug(f"🔍 معلومات الجهاز الحالية - hostname: {pending_device.hostname}, system: {pending_device.system}, platform: {pending_device.platform}, browser: {getattr(pending_device, 'browser', None)}")

            # ✅ استخراج معلومات الجهاز من user_agent
            platform_info = self._extract_platform_info_from_user_agent(user_agent or "")

            # تسجيل المعلومات المستخرجة
            logger.debug(f"🔍 معلومات مستخرجة من user_agent: {platform_info}")

            # ✅ تحديث المعلومات (تحديث دائماً لضمان الدقة)
            new_hostname = self._generate_device_hostname(platform_info, client_ip)
            new_system = platform_info.get('system', 'غير معروف')
            new_platform = platform_info.get('platform', 'غير معروف')
            new_browser = platform_info.get('browser', 'غير معروف')
            new_device_type = platform_info.get('device_type', 'غير محدد')

            # تطبيق التحديثات
            pending_device.hostname = new_hostname
            pending_device.system = new_system
            pending_device.platform = new_platform
            pending_device.browser = new_browser
            pending_device.device_type = new_device_type

            logger.info(f"✅ تحديث معلومات الجهاز المنتظر: {device_id} -> {new_hostname} ({new_system}, {new_platform}, {new_browser})")

            # تحديث user_agent إذا كان مختلفاً
            if user_agent and pending_device.user_agent != user_agent:
                pending_device.user_agent = user_agent

            # تحديث آخر وصول
            pending_device.last_access = get_tripoli_now()
            pending_device.access_count = (pending_device.access_count or 0) + 1
            pending_device.updated_at = get_tripoli_now()

            db.commit()
            logger.info(f"✅ [UNIFIED-SEC] Updated pending device info: {device_id} ({pending_device.hostname})")

        except Exception as e:
            logger.error(f"❌ [UNIFIED-SEC] Error updating pending device info: {e}")
            db.rollback()

    def _extract_platform_info_from_user_agent(self, user_agent: str) -> dict:
        """
        استخراج معلومات المنصة والمتصفح من user_agent
        """
        try:
            if not user_agent:
                return {
                    'system': 'غير معروف',
                    'platform': 'غير معروف',
                    'browser': 'غير معروف',
                    'device_type': 'غير محدد'
                }

            user_agent_lower = user_agent.lower()

            # تحديد نظام التشغيل (ترتيب مهم: Android قبل Linux)
            system = 'غير معروف'
            if 'android' in user_agent_lower:
                system = 'Android'
            elif 'iphone' in user_agent_lower or 'ipad' in user_agent_lower:
                system = 'iOS'
            elif 'windows' in user_agent_lower:
                if 'windows nt 10' in user_agent_lower:
                    system = 'Windows 10/11'
                elif 'windows nt 6.3' in user_agent_lower:
                    system = 'Windows 8.1'
                elif 'windows nt 6.1' in user_agent_lower:
                    system = 'Windows 7'
                else:
                    system = 'Windows'
            elif 'mac os x' in user_agent_lower or 'macos' in user_agent_lower:
                system = 'macOS'
            elif 'linux' in user_agent_lower:
                if 'ubuntu' in user_agent_lower:
                    system = 'Ubuntu Linux'
                else:
                    system = 'Linux'

            # تحديد المنصة
            platform = 'غير معروف'
            if 'android' in user_agent_lower:
                platform = 'Android'
            elif 'iphone' in user_agent_lower or 'ipad' in user_agent_lower:
                platform = 'iOS'
            elif 'windows' in user_agent_lower:
                platform = 'Windows'
            elif 'mac os x' in user_agent_lower or 'macos' in user_agent_lower:
                platform = 'macOS'
            elif 'linux' in user_agent_lower:
                if 'ubuntu' in user_agent_lower:
                    platform = 'Ubuntu'
                else:
                    platform = 'Linux'

            # تحديد المتصفح
            browser = 'غير معروف'
            if 'chrome' in user_agent_lower and 'edg' not in user_agent_lower and 'opr' not in user_agent_lower:
                browser = 'Google Chrome'
            elif 'firefox' in user_agent_lower:
                browser = 'Mozilla Firefox'
            elif 'safari' in user_agent_lower and 'chrome' not in user_agent_lower:
                browser = 'Safari'
            elif 'edg' in user_agent_lower:
                browser = 'Microsoft Edge'
            elif 'opr' in user_agent_lower or 'opera' in user_agent_lower:
                browser = 'Opera'
            elif 'samsung' in user_agent_lower:
                browser = 'Samsung Browser'

            # تحديد نوع الجهاز
            device_type = 'غير محدد'
            if 'android' in user_agent_lower:
                if 'mobile' in user_agent_lower:
                    device_type = 'هاتف ذكي'
                else:
                    device_type = 'جهاز لوحي'
            elif 'iphone' in user_agent_lower:
                device_type = 'هاتف ذكي'
            elif 'ipad' in user_agent_lower:
                device_type = 'جهاز لوحي'
            elif 'windows' in user_agent_lower or 'mac os x' in user_agent_lower or 'linux' in user_agent_lower:
                device_type = 'جهاز بعيد'

            return {
                'system': system,
                'platform': platform,
                'browser': browser,
                'device_type': device_type
            }

        except Exception as e:
            logger.debug(f"خطأ في استخراج معلومات المنصة: {e}")
            return {
                'system': 'غير معروف',
                'platform': 'غير معروف',
                'browser': 'غير معروف',
                'device_type': 'غير محدد'
            }

    def _generate_device_hostname(self, platform_info: dict, client_ip: str) -> str:
        """
        إنشاء اسم مناسب للجهاز بناءً على معلومات المنصة
        """
        try:
            system = platform_info.get('system', 'غير معروف')
            device_type = platform_info.get('device_type', 'غير محدد')

            if system == 'Android':
                if device_type == 'هاتف ذكي':
                    return 'هاتف Android'
                else:
                    return 'جهاز Android'
            elif system == 'iOS':
                if device_type == 'هاتف ذكي':
                    return 'iPhone'
                elif device_type == 'جهاز لوحي':
                    return 'iPad'
                else:
                    return 'جهاز iOS'
            elif 'Windows' in system:
                return 'جهاز Windows'
            elif system == 'macOS':
                return 'جهاز Mac'
            elif 'Linux' in system:
                return 'جهاز Linux'
            else:
                # استخدام IP كاسم احتياطي
                return f'جهاز {client_ip}' if client_ip else 'جهاز غير معروف'

        except Exception as e:
            logger.debug(f"خطأ في إنشاء اسم الجهاز: {e}")
            return f'جهاز {client_ip}' if client_ip else 'جهاز غير معروف'

    async def _extract_current_user(self, _: Request) -> Optional[str]:
        """استخراج المستخدم الحالي من الطلب"""
        try:
            # محاولة استخراج المستخدم من session أو token
            # هذا يعتمد على نظام المصادقة المستخدم
            return None  # placeholder
        except Exception:
            return None

    def _is_device_rate_limited(self, device_id: str, _: str) -> bool:
        """فحص ما إذا كان الجهاز محدود بـ rate limiting"""
        try:
            current_time = time.time()

            if device_id in self.rejected_devices:
                rejection_data = self.rejected_devices[device_id]

                # فحص cooldown
                if current_time - rejection_data.get('last_rejection', 0) < self.rejection_cooldown:
                    # فحص عدد الرفض في الساعة الماضية
                    recent_rejections = [
                        t for t in rejection_data.get('rejection_times', [])
                        if current_time - t < 3600  # ساعة واحدة
                    ]

                    if len(recent_rejections) >= self.max_rejections_per_hour:
                        return True

            return False
        except Exception as e:
            logger.error(f"❌ [UNIFIED-SEC] Error checking rate limit: {e}")
            return False

    def _record_device_rejection(self, device_id: str, client_ip: str):
        """تسجيل رفض الجهاز لحساب rate limiting"""
        try:
            current_time = time.time()

            if device_id not in self.rejected_devices:
                self.rejected_devices[device_id] = {
                    'rejection_times': [],
                    'last_rejection': current_time,
                    'client_ip': client_ip
                }

            rejection_data = self.rejected_devices[device_id]
            rejection_data['rejection_times'].append(current_time)
            rejection_data['last_rejection'] = current_time

            # تنظيف البيانات القديمة (أكثر من ساعة)
            rejection_data['rejection_times'] = [
                t for t in rejection_data['rejection_times']
                if current_time - t < 3600
            ]

        except Exception as e:
            logger.error(f"❌ [UNIFIED-SEC] Error recording device rejection: {e}")

    def _handle_rate_limited_device(self, request: Request, device_id: str) -> Response:
        """التعامل مع الجهاز المحدود بـ rate limiting"""
        logger.warning(f"🚫 [UNIFIED-SEC] Rate limited device: {device_id}")

        accept_header = request.headers.get("accept", "")
        if "application/json" in accept_header or request.url.path.startswith("/api/"):
            return JSONResponse(
                status_code=429,
                content={
                    "detail": "Too many requests. Please try again later.",
                    "retry_after": self.rejection_cooldown
                }
            )
        else:
            return JSONResponse(
                status_code=429,
                content={"detail": "Too many requests"}
            )

    def _handle_blocked_device(self, request: Request, device_id: str) -> Response:
        """التعامل مع الجهاز المحظور"""
        logger.warning(f"🚫 [UNIFIED-SEC] Blocked device access: {device_id}")

        accept_header = request.headers.get("accept", "")
        if "application/json" in accept_header or request.url.path.startswith("/api/"):
            return JSONResponse(
                status_code=403,
                content={"detail": "Device is blocked"}
            )
        else:
            return JSONResponse(
                status_code=403,
                content={"detail": "Access denied"}
            )

    def _handle_pending_approval(self, request: Request, device_id: str, client_ip: Optional[str] = None) -> Optional[Response]:
        """التعامل مع الجهاز في انتظار الموافقة"""
        path = request.url.path

        # استثناءات للـ endpoints المسموحة للأجهزة في قائمة الانتظار
        allowed_pending_paths = [
            "/api/comprehensive-fingerprint/store-pending-approval",
            "/api/comprehensive-fingerprint/recent-activity",
            "/api/device-fingerprints/",  # السماح لجميع endpoints البصمات
            "/api/system/health",
            "/api/device/status"
        ]

        # السماح لـ endpoints التاريخ (تحتوي على /history)
        if "/history" in path and "/api/comprehensive-fingerprint/" in path:
            logger.info(f"🔄 [UNIFIED-SEC] Allowing history access: {device_id} - {path}")
            return None

        # السماح لـ endpoints البصمات (تحتوي على /device-fingerprints/)
        if "/api/device-fingerprints/" in path:
            logger.info(f"🔄 [UNIFIED-SEC] Allowing fingerprint API access: {device_id} - {path}")
            return None

        # السماح للـ endpoints المحددة
        if any(path.startswith(allowed_path) for allowed_path in allowed_pending_paths):
            logger.info(f"🔄 [UNIFIED-SEC] Redirecting device: {device_id} - {path}")
            return None  # السماح بالمرور

        # تسجيل الرفض لحساب rate limiting
        if client_ip:
            self._record_device_rejection(device_id, client_ip)

        accept_header = request.headers.get("accept", "")

        # تسجيل معلومات الطلب (مع تقليل التكرار)
        current_time = time.time()
        log_key = f"pending_log_{device_id}_{path}"
        if log_key not in self.device_last_seen or current_time - self.device_last_seen[log_key] > 600:
            logger.info(f"⏳ [UNIFIED-SEC] Pending approval request: path={path}, method={request.method}")
            self.device_last_seen[log_key] = current_time

        # طلبات API
        if "application/json" in accept_header or path.startswith("/api/"):
            return JSONResponse(
                status_code=403,
                content={
                    "detail": "Device pending approval",
                    "device_id": device_id,
                    "status": "pending_approval"
                }
            )

        # طلبات المتصفح - إعادة توجيه لصفحة انتظار الموافقة
        try:
            original_url = str(request.url)
            client_host = request.headers.get("host", "localhost")

            # إنشاء URL مع معامل الرابط الأصلي
            try:
                from utils.url_manager import url_manager
                backend_url = url_manager.get_pending_approval_url(original_url, device_id)
            except Exception:
                backend_url = f"http://{client_host}:8002/device-pending-approval?original_url={original_url}&device_id={device_id}"

            return RedirectResponse(url=backend_url, status_code=302)

        except Exception as e:
            logger.error(f"❌ [UNIFIED-SEC] Error handling pending approval: {e}")
            return JSONResponse(
                status_code=403,
                content={"detail": "Device pending approval"}
            )


# دالة مساعدة للتحقق من حظر الجهاز (للاستخدام الخارجي)
def is_device_blocked(client_ip: str, user_agent: str) -> bool:
    """
    دالة مساعدة للتحقق من حظر الجهاز باستخدام الخدمة الموحدة
    """
    try:
        device_security = DeviceSecurityService()

        # استخدام الخدمة الموحدة لإنشاء معرف الجهاز
        db = next(get_db())
        try:
            unified_service = get_unified_fingerprint_service(db)
            device_data = unified_service.generate_unified_device_id(client_ip, user_agent)
            device_id = device_data['device_id']
            normalized_ip = device_data.get('ip_address', client_ip)

            return device_security.is_device_blocked(device_id, normalized_ip, db)
        finally:
            db.close()

    except Exception as e:
        logger.error(f"❌ [UNIFIED-SEC] Error checking device block status: {e}")
        return False


# دوال إضافية للإدارة والإحصائيات
def get_security_middleware_stats() -> Dict[str, Any]:
    """الحصول على إحصائيات middleware الأمان"""
    try:
        # هذه دالة مساعدة للحصول على الإحصائيات من خارج الكلاس
        return {
            'status': 'active',
            'middleware_type': 'unified_security',
            'features': [
                'device_fingerprinting',
                'rate_limiting',
                'suspicious_activity_detection',
                'device_approval_system',
                'cache_optimization',
                'attack_pattern_detection'
            ]
        }
    except Exception as e:
        logger.error(f"❌ [UNIFIED-SEC] Error getting middleware stats: {e}")
        return {'error': str(e)}
