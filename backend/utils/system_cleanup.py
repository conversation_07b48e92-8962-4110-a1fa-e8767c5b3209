#!/usr/bin/env python3
"""
أداة تنظيف شاملة للنظام
تقوم بإزالة جميع أنواع التكرارات وتحسين الأداء
"""

import json
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import select, delete

logger = logging.getLogger(__name__)

class SystemCleaner:
    """أداة تنظيف شاملة للنظام"""
    
    def __init__(self):
        # تحديد المسار بناءً على موقع الملف الحالي
        current_file = Path(__file__).resolve()
        project_root = current_file.parent.parent  # من utils إلى backend
        self.config_dir = project_root / "config"
        self.connected_devices_file = self.config_dir / "connected_devices.json"
        
    def full_system_cleanup(self) -> Dict[str, Any]:
        """
        تنظيف شامل للنظام
        """
        try:
            results = {
                'success': True,
                'operations': [],
                'total_removed': 0,
                'errors': []
            }
            
            # 1. تنظيف ملف الأجهزة المتصلة
            file_result = self._cleanup_connected_devices_file()
            results['operations'].append(file_result)
            results['total_removed'] += file_result.get('removed_count', 0)
            
            # 2. تنظيف قاعدة البيانات
            db_result = self._cleanup_database()
            results['operations'].append(db_result)
            results['total_removed'] += db_result.get('removed_count', 0)
            
            # 3. تنظيف الذاكرة المؤقتة
            cache_result = self._cleanup_cache()
            results['operations'].append(cache_result)
            
            results['message'] = f'تم تنظيف النظام بنجاح - إزالة {results["total_removed"]} عنصر مكرر'
            
            return results
            
        except Exception as e:
            logger.error(f"خطأ في التنظيف الشامل: {e}")
            return {
                'success': False,
                'message': f'فشل في التنظيف الشامل: {str(e)}',
                'error': str(e)
            }
    
    def _cleanup_connected_devices_file(self) -> Dict[str, Any]:
        """
        تنظيف ملف الأجهزة المتصلة
        """
        try:
            if not self.connected_devices_file.exists():
                return {
                    'operation': 'cleanup_devices_file',
                    'success': True,
                    'message': 'لا يوجد ملف أجهزة للتنظيف',
                    'removed_count': 0
                }
            
            # قراءة البيانات
            with open(self.connected_devices_file, 'r', encoding='utf-8') as f:
                devices = json.load(f)
            
            original_count = len(devices)
            
            # إزالة التكرارات بناءً على IP
            unique_devices = {}
            for device in devices:
                client_ip = device.get('client_ip', '')
                if not client_ip:
                    continue
                
                # احتفظ بالجهاز الأحدث
                if client_ip in unique_devices:
                    existing = unique_devices[client_ip]
                    existing_time = existing.get('last_access', '')
                    current_time = device.get('last_access', '')
                    
                    if current_time > existing_time:
                        unique_devices[client_ip] = device
                else:
                    unique_devices[client_ip] = device
            
            # تحديث حالة الأجهزة
            current_time = datetime.now()
            cleaned_devices = []
            
            for device in unique_devices.values():
                # تحديث حالة الجهاز
                try:
                    last_access_str = device.get('last_access', '')
                    if last_access_str:
                        if last_access_str.endswith('Z'):
                            last_access_str = last_access_str[:-1] + '+00:00'
                        
                        last_access = datetime.fromisoformat(last_access_str)
                        time_diff = current_time - last_access
                        total_seconds = time_diff.total_seconds()
                        
                        if total_seconds < 300:  # 5 دقائق
                            device['status'] = 'online'
                        elif total_seconds < 3600:  # ساعة واحدة
                            device['status'] = 'recently_active'
                        else:
                            device['status'] = 'offline'
                    else:
                        device['status'] = 'offline'
                except:
                    device['status'] = 'offline'
                
                cleaned_devices.append(device)
            
            # حفظ البيانات المنظفة
            with open(self.connected_devices_file, 'w', encoding='utf-8') as f:
                json.dump(cleaned_devices, f, indent=2, ensure_ascii=False)
            
            removed_count = original_count - len(cleaned_devices)
            
            return {
                'operation': 'cleanup_devices_file',
                'success': True,
                'message': f'تم تنظيف ملف الأجهزة: {original_count} -> {len(cleaned_devices)}',
                'original_count': original_count,
                'final_count': len(cleaned_devices),
                'removed_count': removed_count
            }
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف ملف الأجهزة: {e}")
            return {
                'operation': 'cleanup_devices_file',
                'success': False,
                'message': f'فشل في تنظيف ملف الأجهزة: {str(e)}',
                'error': str(e),
                'removed_count': 0
            }
    
    def _cleanup_database(self) -> Dict[str, Any]:
        """
        تنظيف قاعدة البيانات من التكرارات
        """
        try:
            from database.session import get_db
            from models.device_security import PendingDevice, BlockedDevice
            
            db = next(get_db())
            total_removed = 0
            
            try:
                # تنظيف الأجهزة المعلقة المكررة
                pending_devices = db.execute(select(PendingDevice)).scalars().all()
                ip_map = {}
                to_delete = []
                
                for device in pending_devices:
                    if device.client_ip in ip_map:
                        # احتفظ بالأحدث
                        existing = ip_map[device.client_ip]
                        if device.last_access > existing.last_access:
                            to_delete.append(existing)
                            ip_map[device.client_ip] = device
                        else:
                            to_delete.append(device)
                    else:
                        ip_map[device.client_ip] = device
                
                # حذف المكررات
                for device in to_delete:
                    db.delete(device)
                    total_removed += 1
                
                # تنظيف الأجهزة المحظورة المكررة
                blocked_devices = db.execute(select(BlockedDevice)).scalars().all()
                ip_map = {}
                to_delete = []
                
                for device in blocked_devices:
                    if device.client_ip in ip_map:
                        # احتفظ بالأحدث
                        existing = ip_map[device.client_ip]
                        if device.blocked_at > existing.blocked_at:
                            to_delete.append(existing)
                            ip_map[device.client_ip] = device
                        else:
                            to_delete.append(device)
                    else:
                        ip_map[device.client_ip] = device
                
                # حذف المكررات
                for device in to_delete:
                    db.delete(device)
                    total_removed += 1
                
                db.commit()
                
                return {
                    'operation': 'cleanup_database',
                    'success': True,
                    'message': f'تم تنظيف قاعدة البيانات - إزالة {total_removed} عنصر مكرر',
                    'removed_count': total_removed
                }
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"خطأ في تنظيف قاعدة البيانات: {e}")
            return {
                'operation': 'cleanup_database',
                'success': False,
                'message': f'فشل في تنظيف قاعدة البيانات: {str(e)}',
                'error': str(e),
                'removed_count': 0
            }
    
    def _cleanup_cache(self) -> Dict[str, Any]:
        """
        تنظيف الذاكرة المؤقتة
        """
        try:
            # تنظيف cache الأمان
            from services.device_security import device_security
            if hasattr(device_security, '_approval_cache'):
                device_security._approval_cache = None
                device_security._approval_cache_time = 0
            
            # تنظيف cache أخرى إذا وجدت
            import gc
            gc.collect()
            
            return {
                'operation': 'cleanup_cache',
                'success': True,
                'message': 'تم تنظيف الذاكرة المؤقتة'
            }
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف الذاكرة المؤقتة: {e}")
            return {
                'operation': 'cleanup_cache',
                'success': False,
                'message': f'فشل في تنظيف الذاكرة المؤقتة: {str(e)}',
                'error': str(e)
            }
    
    def get_system_status(self) -> Dict[str, Any]:
        """
        الحصول على حالة النظام
        """
        try:
            from database.session import get_db
            from models.device_security import PendingDevice, BlockedDevice
            
            # فحص ملف الأجهزة
            devices_file_status = {
                'exists': self.connected_devices_file.exists(),
                'size': 0,
                'devices_count': 0,
                'duplicates_count': 0
            }
            
            if self.connected_devices_file.exists():
                devices_file_status['size'] = self.connected_devices_file.stat().st_size
                
                try:
                    with open(self.connected_devices_file, 'r', encoding='utf-8') as f:
                        devices = json.load(f)
                    
                    devices_file_status['devices_count'] = len(devices)
                    
                    # فحص التكرارات
                    ips = [d.get('client_ip') for d in devices if d.get('client_ip')]
                    unique_ips = set(ips)
                    devices_file_status['duplicates_count'] = len(ips) - len(unique_ips)
                    
                except:
                    pass
            
            # فحص قاعدة البيانات
            db_status = {
                'pending_devices': 0,
                'blocked_devices': 0,
                'pending_duplicates': 0,
                'blocked_duplicates': 0
            }
            
            try:
                db = next(get_db())
                try:
                    # عدد الأجهزة المعلقة
                    pending_count = db.execute(select(PendingDevice)).scalars().all()
                    db_status['pending_devices'] = len(pending_count)
                    
                    # فحص تكرارات الأجهزة المعلقة
                    pending_ips = [d.client_ip for d in pending_count]
                    unique_pending_ips = set(pending_ips)
                    db_status['pending_duplicates'] = len(pending_ips) - len(unique_pending_ips)
                    
                    # عدد الأجهزة المحظورة
                    blocked_count = db.execute(select(BlockedDevice)).scalars().all()
                    db_status['blocked_devices'] = len(blocked_count)
                    
                    # فحص تكرارات الأجهزة المحظورة
                    blocked_ips = [d.client_ip for d in blocked_count]
                    unique_blocked_ips = set(blocked_ips)
                    db_status['blocked_duplicates'] = len(blocked_ips) - len(unique_blocked_ips)
                    
                finally:
                    db.close()
            except:
                pass
            
            return {
                'success': True,
                'devices_file': devices_file_status,
                'database': db_status,
                'needs_cleanup': (
                    devices_file_status['duplicates_count'] > 0 or
                    db_status['pending_duplicates'] > 0 or
                    db_status['blocked_duplicates'] > 0
                )
            }
            
        except Exception as e:
            logger.error(f"خطأ في فحص حالة النظام: {e}")
            return {
                'success': False,
                'error': str(e)
            }

# إنشاء instance عام
system_cleaner = SystemCleaner()

def full_system_cleanup():
    """دالة مساعدة للتنظيف الشامل"""
    return system_cleaner.full_system_cleanup()

def get_system_status():
    """دالة مساعدة لفحص حالة النظام"""
    return system_cleaner.get_system_status()
