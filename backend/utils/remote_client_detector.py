"""
نظام محسن لتمييز الأجهزة البعيدة
يستخدم معلومات إضافية من المتصفح لتحديد الأجهزة البعيدة بدقة
"""

import hashlib
import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import Request

logger = logging.getLogger(__name__)

class RemoteClientDetector:
    """
    كاشف الأجهزة البعيدة المحسن
    """
    
    def __init__(self):
        self.remote_clients_cache = {}  # cache للأجهزة البعيدة المكتشفة
    
    def detect_remote_client(self, request: Request) -> Dict[str, Any]:
        """
        كشف الجهاز البعيد بناءً على معلومات المتصفح والشبكة
        """
        try:
            # جمع معلومات شاملة من الطلب
            client_info = self._extract_client_info(request)
            
            # تحديد نوع الوصول
            access_type = self._determine_access_type(request, client_info)
            
            # إنشاء معرف فريد للعميل البعيد
            if access_type == "remote":
                client_id = self._generate_remote_client_id(client_info)
                client_ip = self._generate_remote_client_ip(client_info)
            else:
                client_id = "local_access"
                client_ip = request.client.host if request.client else "127.0.0.1"
            
            result = {
                "client_id": client_id,
                "client_ip": client_ip,
                "access_type": access_type,
                "is_remote": access_type == "remote",
                "client_info": client_info,
                "detection_method": "enhanced_browser_fingerprint",
                "timestamp": datetime.now().isoformat()
            }
            
            # حفظ في cache إذا كان جهاز بعيد
            if access_type == "remote":
                self.remote_clients_cache[client_id] = result
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في كشف الجهاز البعيد: {e}")
            return {
                "client_id": "unknown",
                "client_ip": request.client.host if request.client else "unknown",
                "access_type": "unknown",
                "is_remote": False,
                "client_info": {"error": str(e)},
                "detection_method": "error_recovery",
                "timestamp": datetime.now().isoformat()
            }
    
    def _extract_client_info(self, request: Request) -> Dict[str, Any]:
        """
        استخراج معلومات شاملة من الطلب
        """
        headers = dict(request.headers)
        
        return {
            "user_agent": headers.get("user-agent", ""),
            "accept_language": headers.get("accept-language", ""),
            "accept_encoding": headers.get("accept-encoding", ""),
            "accept": headers.get("accept", ""),
            "host": headers.get("host", ""),
            "referer": headers.get("referer", ""),
            "origin": headers.get("origin", ""),
            "connection": headers.get("connection", ""),
            "cache_control": headers.get("cache-control", ""),
            "upgrade_insecure_requests": headers.get("upgrade-insecure-requests", ""),
            "sec_fetch_site": headers.get("sec-fetch-site", ""),
            "sec_fetch_mode": headers.get("sec-fetch-mode", ""),
            "sec_fetch_dest": headers.get("sec-fetch-dest", ""),
            "sec_ch_ua": headers.get("sec-ch-ua", ""),
            "sec_ch_ua_mobile": headers.get("sec-ch-ua-mobile", ""),
            "sec_ch_ua_platform": headers.get("sec-ch-ua-platform", ""),
            "dnt": headers.get("dnt", ""),
            "x_forwarded_for": headers.get("x-forwarded-for", ""),
            "x_real_ip": headers.get("x-real-ip", ""),
            "cf_connecting_ip": headers.get("cf-connecting-ip", ""),
            "x_client_ip": headers.get("x-client-ip", ""),
            "forwarded": headers.get("forwarded", ""),
            "via": headers.get("via", ""),
            "x_forwarded_proto": headers.get("x-forwarded-proto", ""),
            "x_forwarded_host": headers.get("x-forwarded-host", ""),
            "x_forwarded_port": headers.get("x-forwarded-port", ""),
            "direct_ip": request.client.host if request.client else "unknown",
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "query": str(request.url.query) if request.url.query else "",
        }
    
    def _determine_access_type(self, request: Request, client_info: Dict[str, Any]) -> str:
        """
        تحديد نوع الوصول (محلي أم بعيد)
        """
        try:
            # فحص IP المباشر
            direct_ip = client_info.get("direct_ip", "")
            
            # فحص headers الخاصة بـ proxy
            proxy_ips = [
                client_info.get("x_forwarded_for", ""),
                client_info.get("x_real_ip", ""),
                client_info.get("cf_connecting_ip", ""),
                client_info.get("x_client_ip", "")
            ]
            
            # إذا وجدت proxy headers، فهو وصول بعيد
            for proxy_ip in proxy_ips:
                if proxy_ip and not self._is_local_ip(proxy_ip.split(",")[0].strip()):
                    return "remote"
            
            # فحص Host header
            host = client_info.get("host", "")
            if host:
                host_ip = host.split(":")[0]
                # إذا كان Host يحتوي على IP خارجي، فهو وصول بعيد
                if self._is_valid_ip(host_ip) and not self._is_local_ip(host_ip):
                    return "remote"
            
            # فحص Origin و Referer
            origin = client_info.get("origin", "")
            referer = client_info.get("referer", "")
            
            for url in [origin, referer]:
                if url and ("http://" in url or "https://" in url):
                    try:
                        from urllib.parse import urlparse
                        parsed = urlparse(url)
                        if parsed.hostname and not self._is_local_ip(parsed.hostname):
                            return "remote"
                    except:
                        pass
            
            # فحص User-Agent للمؤشرات على الوصول البعيد
            user_agent = client_info.get("user_agent", "").lower()
            remote_indicators = [
                "mobile", "android", "iphone", "ipad", "tablet",
                "chrome os", "chromebook", "edge", "safari"
            ]
            
            # إذا كان User-Agent يحتوي على مؤشرات جهاز محمول، فهو غالباً بعيد
            if any(indicator in user_agent for indicator in remote_indicators):
                # فحص إضافي: إذا كان IP محلي لكن User-Agent يشير لجهاز محمول
                if self._is_local_ip(direct_ip):
                    # قد يكون جهاز محمول في نفس الشبكة
                    return "remote_local_network"
                else:
                    return "remote"
            
            # إذا كان IP محلي ولا توجد مؤشرات على الوصول البعيد
            if self._is_local_ip(direct_ip):
                return "local"
            
            # افتراضي: إذا لم نتمكن من التحديد، اعتبره بعيد للأمان
            return "remote"
            
        except Exception as e:
            logger.warning(f"خطأ في تحديد نوع الوصول: {e}")
            return "unknown"
    
    def _is_local_ip(self, ip: str) -> bool:
        """
        التحقق من أن IP محلي
        """
        if not ip or ip == "unknown":
            return True

        # التحقق من أن IP ينتمي للخادم الرئيسي
        try:
            from utils.url_manager import url_manager
            if url_manager.is_main_server_ip(ip):
                return True
        except:
            pass

        local_patterns = [
            "127.", "localhost", "::1", "0.0.0.0",
            "192.168.", "10.", "172.16.", "172.17.", "172.18.", "172.19.",
            "172.20.", "172.21.", "172.22.", "172.23.", "172.24.", "172.25.",
            "172.26.", "172.27.", "172.28.", "172.29.", "172.30.", "172.31."
        ]

        return any(ip.startswith(pattern) for pattern in local_patterns)
    
    def _is_valid_ip(self, ip: str) -> bool:
        """
        التحقق من صحة تنسيق IP
        """
        try:
            import ipaddress
            ipaddress.ip_address(ip)
            return True
        except:
            return False
    
    def _generate_remote_client_id(self, client_info: Dict[str, Any]) -> Optional[str]:
        """
        إنشاء معرف فريد للعميل البعيد
        """
        try:
            # استخدام معلومات مميزة لإنشاء معرف فريد
            signature_parts = [
                client_info.get("user_agent", ""),
                client_info.get("accept_language", ""),
                client_info.get("accept_encoding", ""),
                client_info.get("sec_ch_ua", ""),
                client_info.get("sec_ch_ua_platform", ""),
                client_info.get("host", ""),
                # إضافة IP إذا كان متاح
                client_info.get("x_forwarded_for", "").split(",")[0].strip() if client_info.get("x_forwarded_for") else "",
                client_info.get("x_real_ip", ""),
                client_info.get("direct_ip", "")
            ]
            
            # إزالة القيم الفارغة
            signature_parts = [part for part in signature_parts if part]
            
            # إنشاء hash
            signature = "_".join(signature_parts)
            hash_obj = hashlib.sha256(signature.encode())
            hash_hex = hash_obj.hexdigest()
            
            return f"remote_client_{hash_hex[:16]}"
            
        except Exception as e:
            logger.warning(f"فشل في إنشاء معرف العميل البعيد: {e}")
            # رفض العميل - البصمة المتقدمة مطلوبة
            return None
    
    def _generate_remote_client_ip(self, client_info: Dict[str, Any]) -> str:
        """
        إنشاء IP فريد للعميل البعيد
        """
        try:
            # محاولة استخدام IP حقيقي أولاً
            real_ips = [
                client_info.get("x_forwarded_for", "").split(",")[0].strip() if client_info.get("x_forwarded_for") else "",
                client_info.get("x_real_ip", ""),
                client_info.get("cf_connecting_ip", ""),
                client_info.get("x_client_ip", "")
            ]
            
            for ip in real_ips:
                if ip and not self._is_local_ip(ip) and self._is_valid_ip(ip):
                    return ip
            
            # إذا لم نجد IP حقيقي، أنشئ IP فريد
            client_id = self._generate_remote_client_id(client_info)
            if client_id is None:
                # رفض العميل - البصمة المتقدمة مطلوبة
                return "REJECTED_NO_ADVANCED_FINGERPRINT"
            hash_obj = hashlib.md5(client_id.encode())
            hash_hex = hash_obj.hexdigest()
            
            # تحويل hash إلى IP في نطاق خاص
            ip_suffix = int(hash_hex[:4], 16) % 65536
            third_octet = ip_suffix // 256
            fourth_octet = ip_suffix % 256
            
            return f"172.19.{third_octet}.{fourth_octet}"
            
        except Exception as e:
            logger.warning(f"فشل في إنشاء IP للعميل البعيد: {e}")
            return "**********"
    
    def get_cached_remote_clients(self) -> Dict[str, Any]:
        """
        الحصول على الأجهزة البعيدة المحفوظة في cache
        """
        return self.remote_clients_cache.copy()
    
    def clear_cache(self):
        """
        مسح cache الأجهزة البعيدة
        """
        self.remote_clients_cache.clear()

# إنشاء instance مشترك
remote_client_detector = RemoteClientDetector()

# دالة مساعدة للاستخدام السريع
def detect_remote_client(request: Request) -> Dict[str, Any]:
    """
    دالة مساعدة لكشف الجهاز البعيد
    """
    return remote_client_detector.detect_remote_client(request)
