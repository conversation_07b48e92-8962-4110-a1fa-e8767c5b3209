#!/usr/bin/env python3
"""
أداة إزالة تكرار الأجهزة المتصلة
تقوم بتنظيف الأجهزة المكررة بنفس IP وتحسين دقة البيانات
"""

import json
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Set
from collections import defaultdict

logger = logging.getLogger(__name__)

class DeviceDeduplicator:
    """فئة إزالة تكرار الأجهزة"""
    
    def __init__(self):
        # تحديد المسار بناءً على موقع الملف الحالي
        current_file = Path(__file__).resolve()
        project_root = current_file.parent.parent  # من utils إلى backend
        self.config_dir = project_root / "config"
        self.connected_devices_file = self.config_dir / "connected_devices.json"
        
    def deduplicate_devices(self) -> Dict[str, Any]:
        """
        إزالة تكرار الأجهزة وإرجاع تقرير بالعملية
        """
        try:
            # قراءة البيانات الحالية
            devices = self._load_devices()
            original_count = len(devices)
            
            if original_count == 0:
                return {
                    'success': True,
                    'message': 'لا توجد أجهزة للتنظيف',
                    'original_count': 0,
                    'final_count': 0,
                    'removed_count': 0,
                    'duplicates_removed': 0
                }
            
            logger.info(f"بدء إزالة تكرار {original_count} جهاز...")
            
            # إزالة التكرارات
            deduplicated_devices = self._remove_ip_duplicates(devices)
            
            # تنظيف إضافي
            cleaned_devices = self._additional_cleanup(deduplicated_devices)
            
            # حفظ البيانات المنظفة
            self._save_devices(cleaned_devices)
            
            final_count = len(cleaned_devices)
            removed_count = original_count - final_count
            
            result = {
                'success': True,
                'message': f'تم تنظيف الأجهزة بنجاح: إزالة {removed_count} جهاز مكرر',
                'original_count': original_count,
                'final_count': final_count,
                'removed_count': removed_count,
                'duplicates_removed': removed_count
            }
            
            logger.info(f"تم الانتهاء من إزالة التكرار: {original_count} -> {final_count}")
            return result
            
        except Exception as e:
            logger.error(f"خطأ في إزالة تكرار الأجهزة: {e}")
            return {
                'success': False,
                'message': f'فشل في إزالة تكرار الأجهزة: {str(e)}',
                'error': str(e)
            }
    
    def _load_devices(self) -> List[Dict[str, Any]]:
        """تحميل قائمة الأجهزة"""
        try:
            if not self.connected_devices_file.exists():
                return []
            
            with open(self.connected_devices_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"خطأ في تحميل الأجهزة: {e}")
            return []
    
    def _save_devices(self, devices: List[Dict[str, Any]]) -> None:
        """حفظ قائمة الأجهزة"""
        try:
            self.config_dir.mkdir(exist_ok=True)
            with open(self.connected_devices_file, 'w', encoding='utf-8') as f:
                json.dump(devices, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"خطأ في حفظ الأجهزة: {e}")
    
    def _remove_ip_duplicates(self, devices: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        إزالة الأجهزة المكررة بنفس IP
        يحتفظ بالجهاز الأحدث أو الأكثر نشاطاً
        """
        ip_groups = defaultdict(list)
        
        # تجميع الأجهزة حسب IP
        for device in devices:
            client_ip = device.get('client_ip', '')
            if client_ip:
                ip_groups[client_ip].append(device)
        
        deduplicated_devices = []
        
        for ip, ip_devices in ip_groups.items():
            if len(ip_devices) == 1:
                # لا يوجد تكرار
                deduplicated_devices.append(ip_devices[0])
            else:
                # يوجد تكرار - اختيار الأفضل
                best_device = self._select_best_device(ip_devices)
                deduplicated_devices.append(best_device)
                
                logger.debug(f"إزالة {len(ip_devices) - 1} جهاز مكرر لـ IP: {ip}")
        
        return deduplicated_devices
    
    def _select_best_device(self, devices: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        اختيار أفضل جهاز من مجموعة أجهزة بنفس IP
        """
        # أولوية للخادم الرئيسي
        main_servers = [d for d in devices if d.get('is_main_server', False)]
        if main_servers:
            return self._get_most_recent_device(main_servers)
        
        # أولوية للأجهزة المحلية
        local_devices = [d for d in devices if d.get('is_local_access', False)]
        if local_devices:
            return self._get_most_recent_device(local_devices)
        
        # اختيار الأحدث من الأجهزة البعيدة
        return self._get_most_recent_device(devices)
    
    def _get_most_recent_device(self, devices: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        الحصول على الجهاز الأحدث من قائمة
        """
        if not devices:
            return {}
        
        if len(devices) == 1:
            return devices[0]
        
        # ترتيب حسب آخر وصول
        try:
            sorted_devices = sorted(devices, key=lambda d: d.get('last_access', ''), reverse=True)
            return sorted_devices[0]
        except Exception:
            # في حالة الخطأ، إرجاع الأول
            return devices[0]
    
    def _additional_cleanup(self, devices: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        تنظيف إضافي للأجهزة
        """
        cleaned_devices = []
        
        for device in devices:
            # تجاهل الأجهزة بدون IP صحيح
            client_ip = device.get('client_ip', '')
            if not client_ip or client_ip == 'unknown':
                continue
            
            # تجاهل الأجهزة التجريبية
            device_id = device.get('device_id', '')
            hostname = device.get('hostname', '')
            
            if (device_id.startswith('test_') or 
                hostname.startswith('TestDevice') or
                'test' in hostname.lower()):
                continue
            
            # تحديث حالة الجهاز
            device = self._update_device_status(device)
            
            cleaned_devices.append(device)
        
        return cleaned_devices
    
    def _update_device_status(self, device: Dict[str, Any]) -> Dict[str, Any]:
        """
        تحديث حالة الجهاز بناءً على آخر وصول
        """
        try:
            last_access_str = device.get('last_access', '')
            if not last_access_str:
                device['status'] = 'offline'
                return device
            
            # معالجة تنسيق التاريخ
            if last_access_str.endswith('Z'):
                last_access_str = last_access_str[:-1] + '+00:00'
            
            last_access = datetime.fromisoformat(last_access_str)
            current_time = datetime.now()
            time_diff = current_time - last_access
            total_seconds = time_diff.total_seconds()
            
            if total_seconds < 300:  # 5 دقائق
                device['status'] = 'online'
            elif total_seconds < 3600:  # ساعة واحدة
                device['status'] = 'recently_active'
            else:
                device['status'] = 'offline'
                
        except Exception as e:
            logger.warning(f"خطأ في تحديث حالة الجهاز: {e}")
            device['status'] = 'offline'
        
        return device
    
    def get_duplicate_report(self) -> Dict[str, Any]:
        """
        إنشاء تقرير عن الأجهزة المكررة
        """
        try:
            devices = self._load_devices()
            
            ip_groups = defaultdict(list)
            for device in devices:
                client_ip = device.get('client_ip', '')
                if client_ip:
                    ip_groups[client_ip].append(device)
            
            duplicates = {ip: devices for ip, devices in ip_groups.items() if len(devices) > 1}
            
            return {
                'success': True,
                'total_devices': len(devices),
                'unique_ips': len(ip_groups),
                'duplicate_ips': len(duplicates),
                'duplicates': duplicates,
                'duplicate_count': sum(len(devices) - 1 for devices in duplicates.values())
            }
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير التكرار: {e}")
            return {
                'success': False,
                'error': str(e)
            }

# إنشاء instance عام
device_deduplicator = DeviceDeduplicator()

def deduplicate_devices():
    """دالة مساعدة لإزالة تكرار الأجهزة"""
    return device_deduplicator.deduplicate_devices()

def get_duplicate_report():
    """دالة مساعدة للحصول على تقرير التكرار"""
    return device_deduplicator.get_duplicate_report()
