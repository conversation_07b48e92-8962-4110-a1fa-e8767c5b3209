"""
أداة فحص سلامة البيانات لنظام الفروع والمستودعات
تطبق مبادئ البرمجة الكائنية للتحقق من سلامة البيانات
"""

import logging
import sys
import os
from typing import Dict, Any, List
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# إضافة مسار المشروع للاستيراد
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.session import DATABASE_URL
from utils.datetime_utils import get_tripoli_now, get_current_time_with_settings

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DataIntegrityChecker:
    """
    كلاس فحص سلامة البيانات
    """
    
    def __init__(self):
        """تهيئة فاحص سلامة البيانات"""
        self.engine = create_engine(DATABASE_URL)
        self.SessionLocal = sessionmaker(bind=self.engine)
        self.issues_found = []
        
    def run_full_integrity_check(self) -> Dict[str, Any]:
        """
        تشغيل فحص شامل لسلامة البيانات
        
        Returns:
            نتائج الفحص
        """
        try:
            logger.info("🔍 بدء فحص سلامة البيانات الشامل")
            
            with self.SessionLocal() as db:
                # 1. فحص القيود الأساسية
                self._check_basic_constraints(db)
                
                # 2. فحص العلاقات
                self._check_relationships(db)
                
                # 3. فحص القواعد التجارية
                self._check_business_rules(db)
                
                # 4. فحص البيانات المنطقية
                self._check_logical_data(db)
                
                # 5. فحص الأداء
                self._check_performance_issues(db)
            
            # تحليل النتائج
            critical_issues = [issue for issue in self.issues_found if issue['severity'] == 'critical']
            warning_issues = [issue for issue in self.issues_found if issue['severity'] == 'warning']
            info_issues = [issue for issue in self.issues_found if issue['severity'] == 'info']
            
            logger.info(f"✅ اكتمل فحص سلامة البيانات")
            logger.info(f"المشاكل الحرجة: {len(critical_issues)}")
            logger.info(f"التحذيرات: {len(warning_issues)}")
            logger.info(f"المعلومات: {len(info_issues)}")
            
            return {
                'success': True,
                'summary': {
                    'total_issues': len(self.issues_found),
                    'critical_issues': len(critical_issues),
                    'warning_issues': len(warning_issues),
                    'info_issues': len(info_issues)
                },
                'issues': self.issues_found,
                'recommendations': self._generate_recommendations()
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في فحص سلامة البيانات: {e}")
            return {
                'success': False,
                'error': f'خطأ في فحص سلامة البيانات: {str(e)}'
            }
    
    def _check_basic_constraints(self, db):
        """فحص القيود الأساسية"""
        logger.info("🔧 فحص القيود الأساسية...")
        
        # فحص القيود الفريدة للفروع
        duplicate_branch_codes = db.execute(text("""
            SELECT code, COUNT(*) as count
            FROM branches
            GROUP BY code
            HAVING COUNT(*) > 1
        """)).fetchall()
        
        for row in duplicate_branch_codes:
            self._add_issue(
                'critical',
                'Duplicate Branch Code',
                f'كود الفرع "{row.code}" مكرر {row.count} مرة',
                f'UPDATE branches SET code = code || \'_\' || id WHERE code = \'{row.code}\' AND id != (SELECT MIN(id) FROM branches WHERE code = \'{row.code}\')'
            )
        
        # فحص القيود الفريدة للمستودعات
        duplicate_warehouse_codes = db.execute(text("""
            SELECT code, COUNT(*) as count
            FROM warehouses
            GROUP BY code
            HAVING COUNT(*) > 1
        """)).fetchall()
        
        for row in duplicate_warehouse_codes:
            self._add_issue(
                'critical',
                'Duplicate Warehouse Code',
                f'كود المستودع "{row.code}" مكرر {row.count} مرة',
                f'UPDATE warehouses SET code = code || \'_\' || id WHERE code = \'{row.code}\' AND id != (SELECT MIN(id) FROM warehouses WHERE code = \'{row.code}\')'
            )
        
        # فحص القيم الفارغة المطلوبة
        null_branch_names = db.execute(text("""
            SELECT COUNT(*) FROM branches WHERE name IS NULL OR name = ''
        """)).scalar()
        
        if null_branch_names > 0:
            self._add_issue(
                'critical',
                'Null Branch Names',
                f'{null_branch_names} فرع بدون اسم',
                'UPDATE branches SET name = \'فرع بدون اسم\' WHERE name IS NULL OR name = \'\''
            )
    
    def _check_relationships(self, db):
        """فحص العلاقات"""
        logger.info("🔗 فحص العلاقات...")
        
        # فحص الروابط المعطلة في branch_warehouses
        orphaned_branch_links = db.execute(text("""
            SELECT bw.branch_id, bw.warehouse_id
            FROM branch_warehouses bw
            LEFT JOIN branches b ON bw.branch_id = b.id
            WHERE b.id IS NULL
        """)).fetchall()
        
        for row in orphaned_branch_links:
            self._add_issue(
                'critical',
                'Orphaned Branch Link',
                f'رابط معطل: فرع {row.branch_id} غير موجود في جدول branch_warehouses',
                f'DELETE FROM branch_warehouses WHERE branch_id = {row.branch_id} AND warehouse_id = {row.warehouse_id}'
            )
        
        orphaned_warehouse_links = db.execute(text("""
            SELECT bw.branch_id, bw.warehouse_id
            FROM branch_warehouses bw
            LEFT JOIN warehouses w ON bw.warehouse_id = w.id
            WHERE w.id IS NULL
        """)).fetchall()
        
        for row in orphaned_warehouse_links:
            self._add_issue(
                'critical',
                'Orphaned Warehouse Link',
                f'رابط معطل: مستودع {row.warehouse_id} غير موجود في جدول branch_warehouses',
                f'DELETE FROM branch_warehouses WHERE branch_id = {row.branch_id} AND warehouse_id = {row.warehouse_id}'
            )
        
        # فحص الفروع بدون مستودعات
        branches_without_warehouses = db.execute(text("""
            SELECT b.id, b.name
            FROM branches b
            LEFT JOIN branch_warehouses bw ON b.id = bw.branch_id
            WHERE b.is_active = true AND bw.branch_id IS NULL
        """)).fetchall()
        
        for row in branches_without_warehouses:
            self._add_issue(
                'warning',
                'Branch Without Warehouses',
                f'الفرع "{row.name}" (ID: {row.id}) غير مرتبط بأي مستودع',
                f'-- يجب ربط الفرع بمستودع مناسب يدوياً'
            )
    
    def _check_business_rules(self, db):
        """فحص القواعد التجارية"""
        logger.info("📋 فحص القواعد التجارية...")
        
        # فحص عدد الفروع الرئيسية
        main_branches_count = db.execute(text("""
            SELECT COUNT(*) FROM branches WHERE is_main = true
        """)).scalar()
        
        if main_branches_count == 0:
            self._add_issue(
                'warning',
                'No Main Branch',
                'لا يوجد فرع رئيسي محدد',
                'UPDATE branches SET is_main = true WHERE id = (SELECT id FROM branches WHERE is_active = true ORDER BY created_at LIMIT 1)'
            )
        elif main_branches_count > 1:
            self._add_issue(
                'warning',
                'Multiple Main Branches',
                f'يوجد {main_branches_count} فرع رئيسي (يجب أن يكون واحد فقط)',
                'UPDATE branches SET is_main = false WHERE id != (SELECT id FROM branches WHERE is_main = true ORDER BY created_at LIMIT 1)'
            )
        
        # فحص الفروع بمستودعات أساسية متعددة
        branches_multiple_primary = db.execute(text("""
            SELECT bw.branch_id, COUNT(*) as primary_count
            FROM branch_warehouses bw
            WHERE bw.is_primary = true
            GROUP BY bw.branch_id
            HAVING COUNT(*) > 1
        """)).fetchall()
        
        for row in branches_multiple_primary:
            self._add_issue(
                'warning',
                'Multiple Primary Warehouses',
                f'الفرع {row.branch_id} له {row.primary_count} مستودع أساسي (يجب أن يكون واحد فقط)',
                f'UPDATE branch_warehouses SET is_primary = false WHERE branch_id = {row.branch_id} AND warehouse_id != (SELECT warehouse_id FROM branch_warehouses WHERE branch_id = {row.branch_id} AND is_primary = true ORDER BY created_at LIMIT 1)'
            )
    
    def _check_logical_data(self, db):
        """فحص البيانات المنطقية"""
        logger.info("🧠 فحص البيانات المنطقية...")
        
        # فحص السعات السالبة
        negative_capacities = db.execute(text("""
            SELECT id, name, current_capacity, capacity_limit
            FROM warehouses
            WHERE current_capacity < 0 OR capacity_limit < 0
        """)).fetchall()
        
        for row in negative_capacities:
            self._add_issue(
                'warning',
                'Negative Capacity',
                f'المستودع "{row.name}" له سعة سالبة: الحالية={row.current_capacity}, الحد الأقصى={row.capacity_limit}',
                f'UPDATE warehouses SET current_capacity = 0 WHERE id = {row.id} AND current_capacity < 0'
            )
        
        # فحص السعة الحالية أكبر من الحد الأقصى
        over_capacity = db.execute(text("""
            SELECT id, name, current_capacity, capacity_limit
            FROM warehouses
            WHERE capacity_limit IS NOT NULL 
            AND current_capacity > capacity_limit
        """)).fetchall()
        
        for row in over_capacity:
            self._add_issue(
                'warning',
                'Over Capacity',
                f'المستودع "{row.name}" يتجاوز السعة القصوى: {row.current_capacity} > {row.capacity_limit}',
                f'-- يجب مراجعة البيانات يدوياً'
            )
        
        # فحص الأولويات غير المنطقية
        invalid_priorities = db.execute(text("""
            SELECT branch_id, warehouse_id, priority
            FROM branch_warehouses
            WHERE priority <= 0 OR priority > 100
        """)).fetchall()
        
        for row in invalid_priorities:
            self._add_issue(
                'info',
                'Invalid Priority',
                f'أولوية غير منطقية: فرع {row.branch_id}, مستودع {row.warehouse_id}, أولوية {row.priority}',
                f'UPDATE branch_warehouses SET priority = 1 WHERE branch_id = {row.branch_id} AND warehouse_id = {row.warehouse_id}'
            )
    
    def _check_performance_issues(self, db):
        """فحص مشاكل الأداء"""
        logger.info("⚡ فحص مشاكل الأداء...")
        
        # فحص الجداول الكبيرة بدون فهارس
        large_tables = db.execute(text("""
            SELECT 
                schemaname,
                tablename,
                attname,
                n_distinct,
                correlation
            FROM pg_stats 
            WHERE schemaname = 'public' 
            AND tablename IN ('branches', 'warehouses', 'branch_warehouses')
            AND n_distinct > 100
        """)).fetchall()
        
        if large_tables:
            self._add_issue(
                'info',
                'Performance Optimization',
                f'يمكن تحسين الأداء بإضافة فهارس للجداول الكبيرة',
                '-- راجع الفهارس الموجودة وأضف فهارس جديدة حسب الحاجة'
            )
    
    def _add_issue(self, severity: str, issue_type: str, description: str, solution: str):
        """إضافة مشكلة للقائمة"""
        self.issues_found.append({
            'severity': severity,
            'type': issue_type,
            'description': description,
            'solution': solution,
            'timestamp': get_tripoli_now().isoformat()
        })
    
    def _generate_recommendations(self) -> List[str]:
        """إنشاء توصيات عامة"""
        recommendations = []
        
        critical_count = len([i for i in self.issues_found if i['severity'] == 'critical'])
        warning_count = len([i for i in self.issues_found if i['severity'] == 'warning'])
        
        if critical_count == 0 and warning_count == 0:
            recommendations.append("✅ البيانات سليمة ولا توجد مشاكل")
        else:
            if critical_count > 0:
                recommendations.append(f"🚨 يجب إصلاح {critical_count} مشكلة حرجة فوراً")
            
            if warning_count > 0:
                recommendations.append(f"⚠️ يُنصح بمراجعة {warning_count} تحذير")
            
            recommendations.append("📝 استخدم الحلول المقترحة لإصلاح المشاكل")
            recommendations.append("🔄 قم بتشغيل الفحص مرة أخرى بعد الإصلاح")
        
        return recommendations


def main():
    """
    الدالة الرئيسية لتشغيل فحص سلامة البيانات
    """
    print("🔍 بدء فحص سلامة البيانات")
    print("=" * 50)
    
    checker = DataIntegrityChecker()
    results = checker.run_full_integrity_check()
    
    if results['success']:
        summary = results['summary']
        print(f"\n📊 ملخص النتائج:")
        print(f"إجمالي المشاكل: {summary['total_issues']}")
        print(f"المشاكل الحرجة: {summary['critical_issues']}")
        print(f"التحذيرات: {summary['warning_issues']}")
        print(f"المعلومات: {summary['info_issues']}")
        
        if summary['total_issues'] > 0:
            print(f"\n📋 تفاصيل المشاكل:")
            for issue in results['issues']:
                severity_icon = {'critical': '🚨', 'warning': '⚠️', 'info': 'ℹ️'}
                print(f"{severity_icon[issue['severity']]} {issue['type']}: {issue['description']}")
                if issue['solution']:
                    print(f"   💡 الحل: {issue['solution']}")
        
        print(f"\n💡 التوصيات:")
        for rec in results['recommendations']:
            print(f"- {rec}")
    else:
        print(f"❌ خطأ: {results['error']}")


if __name__ == "__main__":
    main()
