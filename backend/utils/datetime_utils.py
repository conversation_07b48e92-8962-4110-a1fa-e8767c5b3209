"""
Utility functions for handling dates and times with proper timezone support.
Provides consistent handling of Tripoli timezone (UTC+2) across the application.

This is the central module for all datetime operations in the application.
All datetime operations should use functions from this module to ensure consistency.
"""

import logging
import pytz
from datetime import datetime, timedelta, timezone, date
from sqlalchemy.sql import expression
from sqlalchemy.ext.compiler import compiles
from sqlalchemy.types import DateTime
from sqlalchemy.orm import Session
from typing import Optional

# Set up logging
logger = logging.getLogger(__name__)

# Tripoli, Libya is UTC+2
TRIPOLI_TIMEZONE_OFFSET = 2  # hours
TRIPOLI_TIMEZONE_NAME = 'Africa/Tripoli'
TRIPOLI_TIMEZONE = pytz.timezone(TRIPOLI_TIMEZONE_NAME)

class TripoliTimestampFunction(expression.FunctionElement):
    """
    Custom SQL function to get current timestamp in the configured timezone.
    Now supports settings-based timezone instead of hardcoded Tripoli.
    """
    type = DateTime()
    name = 'settings_timestamp'

@compiles(TripoliTimestampFunction, 'sqlite')
def sqlite_tripoli_timestamp(element, compiler, **kw):  # noqa: ARG001
    """
    Compile the Tripoli timestamp function for SQLite.
    SQLite doesn't have timezone support, so we add 2 hours for Tripoli timezone.

    Note: We're storing the Tripoli time directly in the database.
    """
    # Add 2 hours to UTC time for Tripoli timezone (UTC+2)
    return "datetime('now', '+2 hours')"

@compiles(TripoliTimestampFunction, 'postgresql')
def postgresql_tripoli_timestamp(element, compiler, **kw):  # noqa: ARG001
    """
    Compile the Tripoli timestamp function for PostgreSQL.
    PostgreSQL has proper timezone support, so we use timezone conversion.
    Note: This uses a simple NOW() function for DEFAULT values to avoid subquery issues.
    """
    # Use simple NOW() function for DEFAULT values to avoid PostgreSQL subquery limitation
    return "NOW()"

def get_timezone_from_settings(db: Optional[Session] = None) -> pytz.BaseTzInfo:
    """
    Get timezone from database settings.

    Args:
        db: Database session (optional)

    Returns:
        pytz.BaseTzInfo: Timezone object from settings or default Tripoli timezone
    """
    if db is None:
        logger.debug("No database session provided, using default Tripoli timezone")
        return TRIPOLI_TIMEZONE

    try:
        # Import here to avoid circular imports
        from models.setting import Setting

        # Get timezone setting from database
        timezone_setting = db.query(Setting).filter(Setting.key == "timezone").first()

        if timezone_setting and timezone_setting.value:
            timezone_name = timezone_setting.value
            logger.debug(f"Found timezone setting: {timezone_name}")

            # Validate and return the timezone
            try:
                return pytz.timezone(timezone_name)
            except pytz.UnknownTimeZoneError:
                logger.warning(f"Unknown timezone '{timezone_name}', using default Tripoli timezone")
                return TRIPOLI_TIMEZONE
        else:
            logger.debug("No timezone setting found, using default Tripoli timezone")
            return TRIPOLI_TIMEZONE

    except Exception as e:
        logger.error(f"Error getting timezone from settings: {e}")
        return TRIPOLI_TIMEZONE

def get_current_time_with_settings(db: Optional[Session] = None):
    """
    Get current datetime in the timezone specified in settings.

    Args:
        db: Database session (optional)

    Returns:
        datetime: Current datetime in the configured timezone with tzinfo
    """
    # Get current UTC time
    utc_now = datetime.now(timezone.utc)

    # Get timezone from settings
    target_timezone = get_timezone_from_settings(db)

    # Convert to target timezone
    current_time = utc_now.astimezone(target_timezone)

    logger.debug(f"Current time in {target_timezone}: {current_time}, tzinfo: {current_time.tzinfo}")
    return current_time

def get_tripoli_now():
    """
    Get current datetime in Tripoli timezone (UTC+2).

    Returns:
        datetime: Current datetime in Tripoli timezone with tzinfo
    """
    # Get current UTC time
    utc_now = datetime.now(timezone.utc)

    # Convert to Tripoli timezone using pytz
    tripoli_now = utc_now.astimezone(TRIPOLI_TIMEZONE)

    logger.debug(f"Current Tripoli time: {tripoli_now}, tzinfo: {tripoli_now.tzinfo}")
    return tripoli_now

def tripoli_timestamp():
    """
    Return a SQLAlchemy function element for Tripoli timestamp.
    Note: This is kept for backward compatibility, but new code should use settings_timestamp().

    Returns:
        TripoliTimestampFunction: SQLAlchemy function element for Tripoli timestamp
    """
    return TripoliTimestampFunction()

def settings_timestamp():
    """
    Return a SQLAlchemy function element for timestamp using settings timezone.
    This is the preferred method for new code.

    Returns:
        TripoliTimestampFunction: SQLAlchemy function element for settings-based timestamp
    """
    return TripoliTimestampFunction()

class SettingsTimestampFunction(expression.FunctionElement):
    """
    Enhanced SQL function to get current timestamp in the timezone from settings.
    This is a more explicit implementation for settings-based timestamps.
    """
    type = DateTime()
    name = 'enhanced_settings_timestamp'

@compiles(SettingsTimestampFunction, 'postgresql')
def postgresql_settings_timestamp(element, compiler, **kw):  # noqa: ARG001
    """
    Compile the enhanced settings timestamp function for PostgreSQL.
    Uses the timezone from settings table with fallback to Africa/Tripoli.
    """
    return "TIMEZONE(COALESCE((SELECT value FROM settings WHERE key = 'timezone'), 'Africa/Tripoli'), NOW())"

def enhanced_settings_timestamp():
    """
    Return a SQLAlchemy function element for enhanced settings-based timestamp.
    This is the most robust method for timezone-aware timestamps.

    Returns:
        SettingsTimestampFunction: SQLAlchemy function element for enhanced settings-based timestamp
    """
    return SettingsTimestampFunction()

def convert_to_settings_timezone(dt, db: Optional[Session] = None):
    """
    Convert a datetime object to the timezone specified in settings.
    If the datetime is naive (no timezone info), assume it's in UTC.

    Args:
        dt (datetime or str or date): Datetime object, string, or date to convert
        db: Database session (optional)

    Returns:
        datetime: Datetime object in the configured timezone with tzinfo
    """
    if dt is None:
        logger.warning("Attempted to convert None to settings timezone")
        return None

    # If dt is a date object (not datetime), convert it to datetime
    if isinstance(dt, date) and not isinstance(dt, datetime):
        dt = datetime.combine(dt, datetime.min.time())
        logger.debug(f"Converted date to datetime: {dt}")

    # If dt is a string, try to parse it
    if isinstance(dt, str):
        try:
            # Try parsing common formats
            for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M:%S.%f', '%Y-%m-%dT%H:%M:%S', '%Y-%m-%dT%H:%M:%S.%f']:
                try:
                    dt = datetime.strptime(dt, fmt)
                    logger.debug(f"Parsed string to datetime: {dt}")
                    break
                except ValueError:
                    continue
            else:
                # If none of the formats work, try the default parser
                dt = datetime.fromisoformat(dt.replace('Z', '+00:00'))
                logger.debug(f"Parsed ISO string to datetime: {dt}")
        except (ValueError, TypeError) as e:
            logger.error(f"Failed to parse datetime string {dt}: {e}")
            return None

    # At this point, dt should be a datetime object
    if not isinstance(dt, datetime):
        logger.error(f"Failed to convert {dt} to datetime object")
        return None

    # Get target timezone from settings
    target_timezone = get_timezone_from_settings(db)

    if dt.tzinfo is None:
        # If the datetime is naive, assume it's already in the target timezone
        # Just add the timezone info
        dt = dt.replace(tzinfo=target_timezone)
        logger.debug(f"Added {target_timezone} timezone info to naive datetime: {dt}")
        return dt

    # If the datetime already has timezone info, convert it to target timezone
    converted_time = dt.astimezone(target_timezone)
    logger.info(f"Converted {dt} to {target_timezone} time: {converted_time}, tzinfo: {converted_time.tzinfo}")
    return converted_time

def create_timestamp_with_settings(db: Optional[Session] = None):
    """
    Create a timestamp in the timezone specified in settings.
    This is the preferred method for creating timestamps in new code.

    Args:
        db: Database session (optional)

    Returns:
        datetime: Current datetime in settings timezone with tzinfo
    """
    return get_current_time_with_settings(db)

def convert_to_tripoli_time(dt):
    """
    Convert a datetime object to Tripoli timezone (UTC+2).
    If the datetime is naive (no timezone info), assume it's in UTC.

    Args:
        dt (datetime or str or date): Datetime object, string, or date to convert

    Returns:
        datetime: Datetime object in Tripoli timezone with tzinfo
    """
    if dt is None:
        logger.warning("Attempted to convert None to Tripoli time")
        return None

    # If dt is a date object (not datetime), convert it to datetime
    if isinstance(dt, date) and not isinstance(dt, datetime):
        dt = datetime.combine(dt, datetime.min.time())
        logger.debug(f"Converted date to datetime: {dt}")

    # If dt is a string, try to parse it
    if isinstance(dt, str):
        dt_str = dt  # Store the string value
        try:
            # Try to parse as ISO format first
            dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
            logger.debug(f"Parsed string datetime: {dt}")
        except ValueError:
            try:
                # Try to parse as SQLite format (YYYY-MM-DD HH:MM:SS)
                dt = datetime.strptime(dt_str, '%Y-%m-%d %H:%M:%S')
                logger.debug(f"Parsed SQLite datetime: {dt}")
            except ValueError:
                try:
                    # Try to parse as date only (YYYY-MM-DD)
                    dt = datetime.strptime(dt_str, '%Y-%m-%d')
                    logger.debug(f"Parsed date only: {dt}")
                except ValueError:
                    logger.error(f"Failed to parse datetime string: {dt_str}")
                    return None

    # At this point, dt should be a datetime object
    if not isinstance(dt, datetime):
        logger.error(f"Failed to convert {dt} to datetime object")
        return None

    if dt.tzinfo is None:
        # If the datetime is naive, assume it's already in Tripoli time
        # Just add the Tripoli timezone info
        dt = dt.replace(tzinfo=TRIPOLI_TIMEZONE)
        logger.debug(f"Added Tripoli timezone info to naive datetime: {dt}")
        return dt

    # If the datetime already has timezone info, convert it to Tripoli timezone
    tripoli_time = dt.astimezone(TRIPOLI_TIMEZONE)
    logger.info(f"Converted {dt} to Tripoli time: {tripoli_time}, tzinfo: {tripoli_time.tzinfo}")
    return tripoli_time

def format_date(dt, format_str='%Y-%m-%d'):
    """
    Format a datetime object as a string using the specified format.
    Ensures the datetime is in Tripoli timezone before formatting.
    For dates after midnight but before 2 AM, they should be shown as the previous day.

    Args:
        dt (datetime): Datetime object to format
        format_str (str): Format string to use

    Returns:
        str: Formatted date string
    """
    if dt is None:
        logger.warning("Attempted to format None date")
        return ""

    # Convert to Tripoli time
    tripoli_dt = convert_to_tripoli_time(dt)

    # Check if conversion returned None
    if tripoli_dt is None:
        logger.warning("Failed to convert datetime to Tripoli time")
        return ""

    # Get the current date for reference
    reference_date = get_tripoli_now().date()

    # If it's between midnight and 2 AM of the next day, show it as the current day
    if tripoli_dt.date() > reference_date and tripoli_dt.hour < 2:
        # Create a new datetime with the current date but keep the time
        adjusted_dt = tripoli_dt.replace(
            year=reference_date.year,
            month=reference_date.month,
            day=reference_date.day
        )
        logger.info(f"Date after midnight: Showing {tripoli_dt} as {adjusted_dt} (original: {dt})")
        tripoli_dt = adjusted_dt

    # Format the datetime
    formatted = tripoli_dt.strftime(format_str)
    logger.debug(f"Formatted {tripoli_dt} as {formatted} using format {format_str}")

    return formatted

def get_hour_from_datetime_with_settings(dt, db: Optional[Session] = None):
    """
    Get the hour from a datetime object in the timezone specified in settings.

    Args:
        dt (datetime): Datetime object
        db: Database session (optional)

    Returns:
        str: Hour in format "HH:00"
    """
    if dt is None:
        logger.warning("Attempted to get hour from None datetime")
        return "00:00"

    # Convert to settings timezone
    converted_time = convert_to_settings_timezone(dt, db)

    # Check if conversion returned None
    if converted_time is None:
        logger.warning("Failed to convert datetime to settings timezone")
        return "00:00"

    # Format the hour
    hour = converted_time.hour
    formatted = f"{hour:02d}:00"

    logger.info(f"Extracted hour {formatted} from {converted_time} (original: {dt})")
    return formatted

def get_hour_from_datetime(dt):
    """
    Get the hour from a datetime object in Tripoli timezone.

    Args:
        dt (datetime): Datetime object

    Returns:
        str: Hour in format "HH:00"
    """
    if dt is None:
        logger.warning("Attempted to get hour from None datetime")
        return "00:00"

    # Convert to Tripoli time
    tripoli_time = convert_to_tripoli_time(dt)

    # Check if conversion returned None
    if tripoli_time is None:
        logger.warning("Failed to convert datetime to Tripoli time")
        return "00:00"

    # Format the hour
    hour = tripoli_time.hour
    formatted = f"{hour:02d}:00"

    logger.info(f"Extracted hour {formatted} from {tripoli_time} (original: {dt})")
    return formatted

def get_date_components(dt):
    """
    Get year, month, and day components from a datetime object in Tripoli timezone.

    Args:
        dt (datetime): Datetime object

    Returns:
        tuple: (year, month, day)
    """
    if dt is None:
        logger.warning("Attempted to get date components from None datetime")
        return (0, 0, 0)

    # Convert to Tripoli time
    tripoli_time = convert_to_tripoli_time(dt)

    # Check if conversion returned None
    if tripoli_time is None:
        logger.warning("Failed to convert datetime to Tripoli time")
        return (0, 0, 0)

    components = (tripoli_time.year, tripoli_time.month, tripoli_time.day)
    logger.debug(f"Extracted date components {components} from {tripoli_time}")

    return components

def get_previous_days(days_count):
    """
    Get a list of previous days in YYYY-MM-DD format.
    Returns days in chronological order, from oldest to newest.

    Args:
        days_count (int): Number of previous days to get

    Returns:
        list: List of date strings in YYYY-MM-DD format
    """
    result = []
    # Get current date in Tripoli timezone
    today = get_tripoli_now().date()

    logger.debug(f"Generating {days_count} previous days from {today}")

    # Generate days in chronological order (oldest to newest)
    for i in range(days_count):
        # Calculate the date: days_count-i-1 days ago
        day = today - timedelta(days=days_count-i-1)
        formatted = day.strftime('%Y-%m-%d')
        result.append(formatted)
        logger.debug(f"Added day {i+1}/{days_count}: {formatted}")

    return result

def get_previous_months(months_count):
    """
    Get a list of previous months in YYYY-MM format.
    Returns months in chronological order, from oldest to newest.

    Args:
        months_count (int): Number of previous months to get

    Returns:
        list: List of month strings in YYYY-MM format
    """
    result = []
    # Get current date in Tripoli timezone
    today = get_tripoli_now()

    logger.debug(f"Generating {months_count} previous months from {today.year}-{today.month:02d}")

    # Generate months in chronological order (oldest to newest)
    for i in range(months_count):
        # Calculate the month: months_count-i-1 months ago
        month_offset = months_count - i - 1

        # Create a new date by subtracting months
        # This handles month/year rollover correctly
        year = today.year
        month = today.month - month_offset

        # Adjust year if month is negative or zero
        while month <= 0:
            year -= 1
            month += 12

        formatted = f"{year}-{month:02d}"
        result.append(formatted)
        logger.info(f"Added month {i+1}/{months_count}: {formatted}")

    return result


def parse_date_string(date_str, format_str='%Y-%m-%d'):
    """
    Parse a date string into a datetime object in Tripoli timezone.

    Args:
        date_str (str): Date string to parse
        format_str (str): Format string to use for parsing

    Returns:
        datetime: Datetime object in Tripoli timezone with tzinfo
    """
    if not date_str:
        logger.warning("Attempted to parse empty date string")
        return None

    try:
        # Parse the date string into a naive datetime
        naive_dt = datetime.strptime(date_str, format_str)

        # Assume the date is in Tripoli timezone
        # First make it UTC by subtracting the offset
        utc_dt = naive_dt - timedelta(hours=TRIPOLI_TIMEZONE_OFFSET)

        # Then add UTC timezone info
        utc_dt = utc_dt.replace(tzinfo=timezone.utc)

        # Finally convert to Tripoli timezone
        tripoli_dt = utc_dt.astimezone(TRIPOLI_TIMEZONE)

        logger.debug(f"Parsed {date_str} as {tripoli_dt} using format {format_str}")
        return tripoli_dt
    except ValueError as e:
        logger.error(f"Failed to parse date string {date_str} with format {format_str}: {e}")
        return None


def get_day_start_end(dt=None):
    """
    Get the start and end of a day in Tripoli timezone.

    Args:
        dt (datetime, optional): Datetime object. If None, uses current Tripoli time.

    Returns:
        tuple: (start_of_day, end_of_day) both with Tripoli timezone info
    """
    if dt is None:
        dt = get_tripoli_now()
    else:
        dt = convert_to_tripoli_time(dt)

    if dt is None:
        logger.warning("Failed to get day start/end: invalid datetime")
        return None, None

    # Start of day: midnight (00:00:00)
    start = dt.replace(hour=0, minute=0, second=0, microsecond=0)

    # End of day: just before midnight (23:59:59.999999)
    end = dt.replace(hour=23, minute=59, second=59, microsecond=999999)

    logger.debug(f"Day start: {start}, end: {end} for {dt}")
    return start, end


def get_month_start_end(dt=None):
    """
    Get the start and end of a month in Tripoli timezone.

    Args:
        dt (datetime, optional): Datetime object. If None, uses current Tripoli time.

    Returns:
        tuple: (start_of_month, end_of_month) both with Tripoli timezone info
    """
    if dt is None:
        dt = get_tripoli_now()
    else:
        dt = convert_to_tripoli_time(dt)

    if dt is None:
        logger.warning("Failed to get month start/end: invalid datetime")
        return None, None

    # Start of month: first day at midnight
    start = dt.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

    # End of month: determine the last day of the month
    if dt.month == 12:
        # December: last day is 31st
        end = dt.replace(year=dt.year+1, month=1, day=1, hour=0, minute=0, second=0, microsecond=0) - timedelta(microseconds=1)
    else:
        # Other months: last day is the day before the 1st of next month
        end = dt.replace(month=dt.month+1, day=1, hour=0, minute=0, second=0, microsecond=0) - timedelta(microseconds=1)

    logger.debug(f"Month start: {start}, end: {end} for {dt}")
    return start, end