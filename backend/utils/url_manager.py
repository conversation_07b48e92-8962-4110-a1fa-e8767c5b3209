"""
مدير الروابط الديناميكي - يتكيف مع بيئة التطوير والإنتاج
"""

import os
import logging
from pathlib import Path
from typing import Dict, Optional, List
from urllib.parse import urlparse, urlunparse

logger = logging.getLogger(__name__)


class URLManager:
    """
    مدير الروابط الديناميكي - يحدد الروابط المناسبة حسب البيئة
    """

    def __init__(self):
        self.environment = self._detect_environment()
        self.config = self._load_url_config()
        logger.info(f"تم تهيئة مدير الروابط للبيئة: {self.environment}")

    def _detect_environment(self) -> str:
        """
        اكتشاف البيئة الحالية (تطوير أو إنتاج)
        """
        # محاولة قراءة ملف .env
        try:
            env_file = Path('.env')
            if env_file.exists():
                with open(env_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line.startswith('ENVIRONMENT='):
                            env_value = line.split('=', 1)[1].strip().lower()
                            if env_value in ['production', 'prod']:
                                return 'production'
                            elif env_value in ['development', 'dev']:
                                return 'development'
        except Exception as e:
            logger.debug(f"خطأ في قراءة ملف .env: {e}")

        # فحص متغيرات البيئة
        env = os.getenv('ENVIRONMENT', '').lower()
        if env in ['production', 'prod']:
            return 'production'
        elif env in ['development', 'dev']:
            return 'development'

        # فحص متغيرات أخرى
        if os.getenv('DEBUG', '').lower() in ['true', '1']:
            return 'development'

        if os.getenv('NODE_ENV', '').lower() == 'production':
            return 'production'

        # فحص وجود ملفات التطوير
        dev_indicators = [
            'package.json',
            'requirements.txt',
            '.env.development',
            'vite.config.ts',
            'frontend/package.json'
        ]

        for indicator in dev_indicators:
            if os.path.exists(indicator):
                return 'development'

        # افتراضي: بيئة التطوير
        return 'development'

    def _get_server_ip(self) -> str:
        """
        الكشف التلقائي عن IP الخادم مع إعطاء الأولوية للـ IP الفعلي
        """
        try:
            import socket

            # الطريقة الأولى: الاتصال بخادم خارجي (الأكثر دقة)
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            server_ip = s.getsockname()[0]
            s.close()

            # التحقق من أن IP ليس محلي
            if not server_ip.startswith("127.") and server_ip != "0.0.0.0":
                logger.info(f"🌐 تم اكتشاف IP الخادم الفعلي: {server_ip}")
                return server_ip

        except Exception as e:
            logger.warning(f"فشل في اكتشاف IP الخادم عبر الاتصال الخارجي: {e}")

        # الطريقة الثانية: فحص واجهات الشبكة باستخدام socket
        try:
            import subprocess
            import re

            # محاولة استخدام ip command على Linux
            try:
                result = subprocess.run(['ip', 'route', 'get', '*******'],
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    # البحث عن IP في النتيجة
                    match = re.search(r'src (\d+\.\d+\.\d+\.\d+)', result.stdout)
                    if match:
                        ip = match.group(1)
                        if ip.startswith('192.168.') and not ip.startswith('127.'):
                            logger.info(f"🌐 تم اكتشاف IP الخادم من ip route: {ip}")
                            return ip
            except (subprocess.TimeoutExpired, FileNotFoundError):
                pass

            # محاولة استخدام ifconfig كبديل
            try:
                result = subprocess.run(['ifconfig'], capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    # البحث عن IPs في نطاق 192.168.x.x
                    ips = re.findall(r'inet (\d+\.\d+\.\d+\.\d+)', result.stdout)
                    for ip in ips:
                        if ip.startswith('192.168.') and not ip.startswith('192.168.122.'):
                            logger.info(f"🌐 تم اكتشاف IP الخادم من ifconfig: {ip}")
                            return ip
            except (subprocess.TimeoutExpired, FileNotFoundError):
                pass

        except Exception as e:
            logger.warning(f"فشل في فحص واجهات الشبكة: {e}")

        # الطريقة الثالثة: استخدام hostname (مع تجنب 127.x.x.x)
        try:
            hostname = socket.gethostname()
            server_ip = socket.gethostbyname(hostname)

            if not server_ip.startswith("127.") and server_ip != "0.0.0.0":
                logger.info(f"🌐 تم اكتشاف IP الخادم من hostname: {server_ip}")
                return server_ip
        except Exception as e2:
            logger.warning(f"فشل في اكتشاف IP من hostname: {e2}")

        # الطريقة الرابعة: IP افتراضي للتطوير
        default_ip = "*************"
        logger.warning(f"استخدام IP افتراضي للتطوير: {default_ip}")
        return default_ip

    def get_main_server_ips(self) -> List[str]:
        """
        الحصول على جميع عناوين IP المحتملة للخادم الرئيسي
        """
        main_server_ips = [
            '127.0.0.1',
            'localhost',
            '::1',
            self.config.get('backend_host', '*************')  # IP الخادم الحالي
        ]

        # إضافة IP الفعلي المكتشف
        try:
            detected_ip = self._get_server_ip()
            if detected_ip not in main_server_ips:
                main_server_ips.append(detected_ip)
        except Exception:
            pass

        # إضافة hostname IP إذا كان مختلف
        try:
            import socket
            hostname = socket.gethostname()
            hostname_ip = socket.gethostbyname(hostname)
            if hostname_ip not in main_server_ips:
                main_server_ips.append(hostname_ip)
        except Exception:
            pass

        return main_server_ips

    def is_main_server_ip(self, ip: str) -> bool:
        """
        التحقق من أن IP ينتمي للخادم الرئيسي
        """
        if not ip:
            return False

        # تطبيع IP
        normalized_ip = ip.strip()
        if ':' in normalized_ip and not normalized_ip.startswith('['):
            normalized_ip = normalized_ip.split(':')[0]

        return normalized_ip in self.get_main_server_ips()

    def _load_url_config(self) -> Dict:
        """
        تحميل إعدادات الروابط حسب البيئة
        """
        if self.environment == 'production':
            return {
                'backend_host': '0.0.0.0',
                'backend_port': 8002,
                'frontend_host': '0.0.0.0',
                'frontend_port': 5173,
                'allowed_hosts': ['localhost', '127.0.0.1'],
                'allowed_ports': [5173, 8002, 80, 443],
                'use_https': False,  # يمكن تغييره للإنتاج
                'default_scheme': 'http'
            }
        else:  # development
            # الكشف التلقائي عن IP الخادم
            server_ip = self._get_server_ip()

            return {
                'backend_host': server_ip,
                'backend_port': 8002,
                'frontend_host': server_ip,
                'frontend_port': 5175,  # تصحيح المنفذ الصحيح للواجهة الأمامية
                'allowed_hosts': [server_ip, 'localhost', '127.0.0.1'],
                'allowed_ports': [5173, 5175, 8002, 3000, 8000],
                'use_https': False,
                'default_scheme': 'http'
            }

    def get_backend_url(self, path: str = '') -> str:
        """
        الحصول على رابط الخادم الخلفي
        """
        scheme = 'https' if self.config['use_https'] else 'http'
        host = self.config['backend_host']
        port = self.config['backend_port']

        if port in [80, 443]:
            url = f"{scheme}://{host}"
        else:
            url = f"{scheme}://{host}:{port}"

        if path and not path.startswith('/'):
            path = '/' + path

        return url + path

    def get_frontend_url(self, path: str = '') -> str:
        """
        الحصول على رابط الواجهة الأمامية
        """
        scheme = 'https' if self.config['use_https'] else 'http'
        host = self.config['frontend_host']
        port = self.config['frontend_port']

        if port in [80, 443]:
            url = f"{scheme}://{host}"
        else:
            url = f"{scheme}://{host}:{port}"

        if path and not path.startswith('/'):
            path = '/' + path

        return url + path

    def get_pending_approval_url(self, original_url: Optional[str] = None, device_id: Optional[str] = None) -> str:
        """
        الحصول على رابط صفحة انتظار الموافقة مع المعاملات
        """
        base_url = self.get_backend_url('/device-pending-approval')

        params = []
        if original_url:
            params.append(f"original_url={original_url}")
        if device_id:
            params.append(f"device_id={device_id}")

        if params:
            return f"{base_url}?{'&'.join(params)}"

        return base_url

    def get_default_redirect_url(self) -> str:
        """
        الحصول على الرابط الافتراضي لإعادة التوجيه
        """
        return self.get_frontend_url('/')

    def is_valid_redirect_url(self, url: str) -> bool:
        """
        التحقق من صحة رابط إعادة التوجيه
        """
        try:
            parsed = urlparse(url)

            # التحقق من وجود scheme و netloc
            if not parsed.scheme or not parsed.netloc:
                return False

            # استخراج المضيف والمنفذ
            host = parsed.hostname
            port = parsed.port

            # التحقق من وجود المضيف
            if not host:
                return False

            # التحقق من المضيف
            if host not in self.config['allowed_hosts']:
                # في بيئة الإنتاج، السماح بالمضيفات المحلية فقط
                if self.environment == 'production':
                    return False
                # في بيئة التطوير، السماح بمضيفات الشبكة المحلية
                elif not self._is_local_network_host(host):
                    return False

            # التحقق من المنفذ
            if port and port not in self.config['allowed_ports']:
                return False

            # التحقق من البروتوكول
            if parsed.scheme not in ['http', 'https']:
                return False

            return True

        except Exception as e:
            logger.error(f"خطأ في التحقق من صحة الرابط: {e}")
            return False

    def _is_local_network_host(self, host: str) -> bool:
        """
        التحقق من أن المضيف في الشبكة المحلية
        """
        if not host:
            return False

        # المضيفات المحلية المعروفة
        local_hosts = ['localhost', '127.0.0.1', '::1']
        if host in local_hosts:
            return True

        # شبكات محلية شائعة
        local_networks = [
            '192.168.',  # ***********/16
            '10.',       # 10.0.0.0/8
            '172.16.',   # **********/12 (جزء منها)
            '172.17.',
            '172.18.',
            '172.19.',
            '172.20.',
            '172.21.',
            '172.22.',
            '172.23.',
            '172.24.',
            '172.25.',
            '172.26.',
            '172.27.',
            '172.28.',
            '172.29.',
            '172.30.',
            '172.31.'
        ]

        for network in local_networks:
            if host.startswith(network):
                return True

        return False

    def normalize_url(self, url: str, request_host: Optional[str] = None) -> str:
        """
        تطبيع الرابط ليتناسب مع البيئة الحالية
        """
        try:
            parsed = urlparse(url)

            # إذا لم يكن هناك مضيف، استخدم المضيف من الطلب أو الافتراضي
            if not parsed.netloc:
                if request_host:
                    host = request_host
                else:
                    host = self.config['frontend_host']

                # إعادة بناء الرابط
                scheme = self.config['default_scheme']
                port = self.config['frontend_port']

                if port in [80, 443]:
                    netloc = host
                else:
                    netloc = f"{host}:{port}"

                return urlunparse((scheme, netloc, parsed.path, parsed.params, parsed.query, parsed.fragment))

            return url

        except Exception as e:
            logger.error(f"خطأ في تطبيع الرابط: {e}")
            return url

    def get_environment_info(self) -> Dict:
        """
        الحصول على معلومات البيئة الحالية
        """
        return {
            'environment': self.environment,
            'backend_url': self.get_backend_url(),
            'frontend_url': self.get_frontend_url(),
            'config': self.config,
            'allowed_hosts': self.config['allowed_hosts'],
            'allowed_ports': self.config['allowed_ports']
        }

    def update_config_for_production(self,
                                   domain: str,
                                   use_https: bool = True,
                                   backend_port: int = 8002,
                                   frontend_port: int = 80) -> None:
        """
        تحديث الإعدادات لبيئة الإنتاج
        """
        self.environment = 'production'
        self.config.update({
            'backend_host': domain,
            'backend_port': backend_port,
            'frontend_host': domain,
            'frontend_port': frontend_port,
            'allowed_hosts': [domain, 'localhost', '127.0.0.1'],
            'allowed_ports': [frontend_port, backend_port, 80, 443],
            'use_https': use_https,
            'default_scheme': 'https' if use_https else 'http'
        })

        logger.info(f"تم تحديث الإعدادات لبيئة الإنتاج: {domain}")


# إنشاء instance عام للاستخدام
url_manager = URLManager()
