"""
خدمة التنظيف التلقائي المحسنة للأجهزة غير النشطة

قواعد التنظيف المحسنة:
1. الاحتفاظ بالخادم الرئيسي دائماً
2. الاحتفاظ بالأجهزة المعتمدة دائماً (حتى لو كانت غير نشطة)
3. إزالة الأجهزة غير المعتمدة وغير النشطة فقط

هذا يضمن عدم اختفاء الأجهزة المعتمدة من القائمة
"""

import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List
import threading
import time

logger = logging.getLogger(__name__)

class AutoDeviceCleanup:
    """خدمة التنظيف التلقائي للأجهزة"""
    
    def __init__(self, cleanup_interval_minutes: int = 30, inactive_threshold_hours: int = 1):
        """
        تهيئة خدمة التنظيف التلقائي
        
        Args:
            cleanup_interval_minutes: فترة التنظيف بالدقائق (افتراضي: 30 دقيقة)
            inactive_threshold_hours: عدد الساعات لاعتبار الجهاز غير نشط (افتراضي: 1 ساعة)
        """
        self.cleanup_interval = cleanup_interval_minutes * 60  # تحويل إلى ثوان
        self.inactive_threshold = timedelta(hours=inactive_threshold_hours)
        # تحديد المسار بناءً على موقع الملف الحالي
        current_file = Path(__file__).resolve()
        project_root = current_file.parent.parent  # من utils إلى backend
        config_dir = project_root / "config"
        self.devices_file = config_dir / "connected_devices.json"
        self.is_running = False
        self.cleanup_thread = None
        self.last_cleanup = None
        
    def start(self):
        """بدء خدمة التنظيف التلقائي"""
        if self.is_running:
            logger.warning("خدمة التنظيف التلقائي تعمل بالفعل")
            return
            
        self.is_running = True
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleanup_thread.start()
        logger.info(f"تم بدء خدمة التنظيف التلقائي - فترة التنظيف: {self.cleanup_interval/60} دقيقة")
        
    def stop(self):
        """إيقاف خدمة التنظيف التلقائي"""
        self.is_running = False
        if self.cleanup_thread and self.cleanup_thread.is_alive():
            self.cleanup_thread.join(timeout=5)
        logger.info("تم إيقاف خدمة التنظيف التلقائي")
        
    def _cleanup_loop(self):
        """حلقة التنظيف الرئيسية"""
        while self.is_running:
            try:
                # تنفيذ عملية التنظيف
                result = self._perform_cleanup()
                self.last_cleanup = datetime.now()
                
                if result['removed_count'] > 0:
                    logger.info(f"التنظيف التلقائي المحسن: تم إزالة {result['removed_count']} جهاز غير معتمد وغير نشط")
                    logger.info(f"الأجهزة المحفوظة: {result.get('approved_devices_count', 0)} معتمد + {result.get('main_server_count', 0)} خادم رئيسي")
                else:
                    logger.debug(f"التنظيف التلقائي: لا توجد أجهزة للإزالة - محفوظ: {result.get('approved_devices_count', 0)} معتمد + {result.get('main_server_count', 0)} خادم")
                    
            except Exception as e:
                logger.error(f"خطأ في التنظيف التلقائي: {e}")
                
            # انتظار الفترة المحددة
            time.sleep(self.cleanup_interval)
            
    def _perform_cleanup(self) -> Dict[str, Any]:
        """
        تنفيذ عملية التنظيف المحسنة

        قواعد التنظيف:
        1. الاحتفاظ بالخادم الرئيسي دائماً
        2. الاحتفاظ بالأجهزة المعتمدة دائماً (حتى لو كانت غير نشطة)
        3. إزالة الأجهزة غير المعتمدة وغير النشطة فقط
        """
        try:
            # تحميل الأجهزة
            devices = self._load_devices()
            if not devices:
                return {
                    'success': True,
                    'original_count': 0,
                    'final_count': 0,
                    'removed_count': 0,
                    'approved_devices_count': 0,
                    'main_server_count': 0
                }
                
            original_count = len(devices)
            current_time = datetime.now()

            active_devices = []
            removed_count = 0
            approved_devices_count = 0
            main_server_count = 0
            
            for device in devices:
                try:
                    # الاحتفاظ بالخادم الرئيسي دائماً
                    if device.get('is_main_server', False):
                        active_devices.append(device)
                        main_server_count += 1
                        continue

                    # الاحتفاظ بالأجهزة المعتمدة دائماً (حتى لو كانت غير نشطة)
                    # الأجهزة المعتمدة هي التي تحتوي على approved_by أو approved_at
                    is_approved_device = (
                        device.get('approved_by') or
                        device.get('approved_at') or
                        device.get('requires_approval') == False
                    )

                    if is_approved_device:
                        active_devices.append(device)
                        approved_devices_count += 1
                        logger.debug(f"الاحتفاظ بالجهاز المعتمد: {device.get('hostname', 'unknown')} - معتمد من: {device.get('approved_by', 'النظام')}")
                        continue

                    last_access_str = device.get('last_access', '')
                    if last_access_str:
                        last_access = datetime.fromisoformat(last_access_str.replace('Z', '+00:00'))
                        time_diff = current_time - last_access

                        # فحص إذا كان الجهاز نشط (للأجهزة غير المعتمدة فقط)
                        if time_diff <= self.inactive_threshold:
                            active_devices.append(device)
                        else:
                            removed_count += 1
                            logger.debug(f"إزالة جهاز غير معتمد وغير نشط: {device.get('hostname', 'unknown')} - آخر وصول منذ {time_diff}")
                    else:
                        # إزالة الأجهزة غير المعتمدة بدون تاريخ وصول فقط
                        removed_count += 1
                        logger.debug(f"إزالة جهاز غير معتمد بدون تاريخ وصول: {device.get('hostname', 'unknown')}")

                except Exception as e:
                    logger.warning(f"خطأ في فحص الجهاز: {e}")
                    # في حالة الخطأ، احتفظ بالخادم الرئيسي والأجهزة المعتمدة
                    if device.get('is_main_server', False) or device.get('approved_by') or device.get('approved_at'):
                        active_devices.append(device)
            
            # حفظ البيانات المنظفة إذا تم إزالة أجهزة
            if removed_count > 0:
                self._save_devices(active_devices)
                
            return {
                'success': True,
                'original_count': original_count,
                'final_count': len(active_devices),
                'removed_count': removed_count,
                'approved_devices_count': approved_devices_count,
                'main_server_count': main_server_count,
                'cleanup_policy': 'محسن - الاحتفاظ بالأجهزة المعتمدة'
            }
            
        except Exception as e:
            logger.error(f"خطأ في عملية التنظيف: {e}")
            return {
                'success': False,
                'error': str(e),
                'removed_count': 0
            }
            
    def _load_devices(self) -> List[Dict[str, Any]]:
        """تحميل قائمة الأجهزة المتصلة"""
        if not self.devices_file.exists():
            return []
            
        try:
            with open(self.devices_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"خطأ في تحميل ملف الأجهزة: {e}")
            return []
            
    def _save_devices(self, devices: List[Dict[str, Any]]):
        """حفظ قائمة الأجهزة المتصلة"""
        try:
            # إنشاء نسخة احتياطية
            if self.devices_file.exists():
                backup_file = self.devices_file.with_suffix('.json.auto_backup')
                import shutil
                shutil.copy2(self.devices_file, backup_file)
                
            # حفظ البيانات الجديدة
            with open(self.devices_file, 'w', encoding='utf-8') as f:
                json.dump(devices, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"خطأ في حفظ ملف الأجهزة: {e}")
            raise
            
    def get_status(self) -> Dict[str, Any]:
        """الحصول على حالة خدمة التنظيف"""
        return {
            'is_running': self.is_running,
            'cleanup_interval_minutes': self.cleanup_interval / 60,
            'inactive_threshold_hours': self.inactive_threshold.total_seconds() / 3600,
            'last_cleanup': self.last_cleanup.isoformat() if self.last_cleanup else None,
            'next_cleanup': (self.last_cleanup + timedelta(seconds=self.cleanup_interval)).isoformat() if self.last_cleanup else None
        }
        
    def force_cleanup(self) -> Dict[str, Any]:
        """تنفيذ تنظيف فوري"""
        logger.info("تنفيذ تنظيف فوري للأجهزة غير النشطة")
        result = self._perform_cleanup()
        self.last_cleanup = datetime.now()
        return result

# إنشاء instance عام للخدمة
auto_cleanup_service = AutoDeviceCleanup(
    cleanup_interval_minutes=30,  # تنظيف كل 30 دقيقة
    inactive_threshold_hours=1    # إزالة الأجهزة غير النشطة لأكثر من ساعة
)

def start_auto_cleanup():
    """بدء خدمة التنظيف التلقائي"""
    auto_cleanup_service.start()

def stop_auto_cleanup():
    """إيقاف خدمة التنظيف التلقائي"""
    auto_cleanup_service.stop()

def get_cleanup_status():
    """الحصول على حالة خدمة التنظيف"""
    return auto_cleanup_service.get_status()

def force_cleanup():
    """تنفيذ تنظيف فوري"""
    return auto_cleanup_service.force_cleanup()

# بدء الخدمة تلقائياً عند استيراد الوحدة
if __name__ != "__main__":
    try:
        start_auto_cleanup()
        logger.info("تم بدء خدمة التنظيف التلقائي للأجهزة")
    except Exception as e:
        logger.error(f"فشل في بدء خدمة التنظيف التلقائي: {e}")

if __name__ == "__main__":
    # اختبار الخدمة
    print("🧹 اختبار خدمة التنظيف التلقائي للأجهزة")
    print("=" * 50)
    
    # عرض الحالة
    status = get_cleanup_status()
    print(f"حالة الخدمة: {'تعمل' if status['is_running'] else 'متوقفة'}")
    print(f"فترة التنظيف: {status['cleanup_interval_minutes']} دقيقة")
    print(f"عتبة عدم النشاط: {status['inactive_threshold_hours']} ساعة")
    
    # تنفيذ تنظيف فوري
    print("\n🚀 تنفيذ تنظيف فوري...")
    result = force_cleanup()
    
    if result['success']:
        print(f"✅ تم التنظيف بنجاح:")
        print(f"   - الأجهزة الأصلية: {result['original_count']}")
        print(f"   - الأجهزة النهائية: {result['final_count']}")
        print(f"   - الأجهزة المحذوفة: {result['removed_count']}")
    else:
        print(f"❌ فشل في التنظيف: {result.get('error', 'خطأ غير معروف')}")
    
    print("=" * 50)
