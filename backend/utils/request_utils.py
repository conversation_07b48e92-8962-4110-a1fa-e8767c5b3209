"""
أدوات مساعدة لاستخراج معلومات الطلبات
"""

import logging
from typing import Dict, Any, Optional
from fastapi import Request
from datetime import datetime

logger = logging.getLogger(__name__)

def get_client_ip(request: Request) -> str:
    """
    استخراج عنوان IP الحقيقي للعميل
    """
    # التحقق من headers المختلفة للحصول على IP الحقيقي
    forwarded_for = request.headers.get('X-Forwarded-For')
    if forwarded_for:
        # أخذ أول IP في القائمة (IP الأصلي)
        return forwarded_for.split(',')[0].strip()

    real_ip = request.headers.get('X-Real-IP')
    if real_ip:
        return real_ip.strip()

    cf_connecting_ip = request.headers.get('CF-Connecting-IP')
    if cf_connecting_ip:
        return cf_connecting_ip.strip()

    # استخدام IP المباشر كحل أخير
    if request.client and hasattr(request.client, 'host') and request.client.host:
        return request.client.host

    return 'unknown'

def extract_request_info(request: Request) -> Dict[str, Any]:
    """
    استخراج معلومات شاملة من الطلب
    """
    client_ip = get_client_ip(request)
    headers = dict(request.headers)

    # معلومات أساسية
    request_info = {
        'client_ip': client_ip,
        'method': request.method,
        'url': str(request.url),
        'path': request.url.path,
        'query_params': dict(request.query_params),
        'timestamp': datetime.now().isoformat(),
        'user_agent': headers.get('user-agent', ''),
        'referer': headers.get('referer', ''),
        'accept_language': headers.get('accept-language', ''),
        'accept_encoding': headers.get('accept-encoding', ''),
        'connection': headers.get('connection', ''),
        'content_type': headers.get('content-type', ''),
        'content_length': headers.get('content-length', '0')
    }

    # معلومات الشبكة
    network_info = {
        'forwarded_for': headers.get('x-forwarded-for', ''),
        'real_ip': headers.get('x-real-ip', ''),
        'cf_connecting_ip': headers.get('cf-connecting-ip', ''),
        'cf_ray': headers.get('cf-ray', ''),
        'cf_country': headers.get('cf-ipcountry', ''),
        'via': headers.get('via', ''),
        'proxy_detected': bool(headers.get('x-forwarded-for') or headers.get('x-real-ip'))
    }

    # معلومات الأمان
    security_info = {
        'https': request.url.scheme == 'https',
        'upgrade_insecure_requests': headers.get('upgrade-insecure-requests') == '1',
        'dnt': headers.get('dnt') == '1',  # Do Not Track
        'sec_fetch_site': headers.get('sec-fetch-site', ''),
        'sec_fetch_mode': headers.get('sec-fetch-mode', ''),
        'sec_fetch_dest': headers.get('sec-fetch-dest', ''),
        'sec_ch_ua': headers.get('sec-ch-ua', ''),
        'sec_ch_ua_mobile': headers.get('sec-ch-ua-mobile', ''),
        'sec_ch_ua_platform': headers.get('sec-ch-ua-platform', '')
    }

    # معلومات البصمة (إذا كانت موجودة)
    fingerprint_headers = {
        'device_fingerprint': headers.get('x-device-fingerprint', ''),
        'hardware_fingerprint': headers.get('x-device-hardware', ''),
        'storage_fingerprint': headers.get('x-device-storage', ''),
        'screen_fingerprint': headers.get('x-device-screen', ''),
        'system_fingerprint': headers.get('x-device-system', ''),
        'timestamp': headers.get('x-device-timestamp', '')
    }

    # دمج جميع المعلومات
    request_info.update({
        'network_info': network_info,
        'security_info': security_info,
        'fingerprint_headers': fingerprint_headers,
        'headers_count': len(headers),
        'has_fingerprint': bool(fingerprint_headers['device_fingerprint'])
    })

    return request_info

def analyze_user_agent(user_agent: str) -> Dict[str, Any]:
    """
    تحليل User Agent لاستخراج معلومات المتصفح والنظام
    """
    if not user_agent:
        return {
            'browser': 'Unknown',
            'browser_version': 'Unknown',
            'platform': 'Unknown',
            'is_mobile': False,
            'is_tablet': False,
            'is_desktop': True,
            'is_bot': False
        }

    ua_lower = user_agent.lower()

    # تحديد المتصفح
    browser = 'Unknown'
    browser_version = 'Unknown'

    if 'chrome' in ua_lower and 'edg' not in ua_lower and 'opr' not in ua_lower:
        browser = 'Chrome'
        if 'chrome/' in ua_lower:
            try:
                browser_version = ua_lower.split('chrome/')[1].split(' ')[0]
            except:
                pass
    elif 'firefox' in ua_lower:
        browser = 'Firefox'
        if 'firefox/' in ua_lower:
            try:
                browser_version = ua_lower.split('firefox/')[1].split(' ')[0]
            except:
                pass
    elif 'safari' in ua_lower and 'chrome' not in ua_lower:
        browser = 'Safari'
        if 'version/' in ua_lower:
            try:
                browser_version = ua_lower.split('version/')[1].split(' ')[0]
            except:
                pass
    elif 'edg' in ua_lower:
        browser = 'Edge'
        if 'edg/' in ua_lower:
            try:
                browser_version = ua_lower.split('edg/')[1].split(' ')[0]
            except:
                pass
    elif 'opr' in ua_lower or 'opera' in ua_lower:
        browser = 'Opera'
        if 'opr/' in ua_lower:
            try:
                browser_version = ua_lower.split('opr/')[1].split(' ')[0]
            except:
                pass

    # تحديد النظام
    platform = 'Unknown'
    if 'windows nt' in ua_lower:
        platform = 'Windows'
        if 'windows nt 10' in ua_lower:
            platform = 'Windows 10/11'
        elif 'windows nt 6.3' in ua_lower:
            platform = 'Windows 8.1'
        elif 'windows nt 6.1' in ua_lower:
            platform = 'Windows 7'
    elif 'mac os x' in ua_lower or 'macos' in ua_lower:
        platform = 'macOS'
    elif 'linux' in ua_lower and 'android' not in ua_lower:
        platform = 'Linux'
    elif 'android' in ua_lower:
        platform = 'Android'
        if 'android ' in ua_lower:
            try:
                android_version = ua_lower.split('android ')[1].split(';')[0]
                platform = f'Android {android_version}'
            except:
                pass
    elif 'ios' in ua_lower or 'iphone os' in ua_lower:
        platform = 'iOS'
        if 'os ' in ua_lower:
            try:
                ios_version = ua_lower.split('os ')[1].split(' ')[0].replace('_', '.')
                platform = f'iOS {ios_version}'
            except:
                pass
    elif 'iphone' in ua_lower:
        platform = 'iOS (iPhone)'
    elif 'ipad' in ua_lower:
        platform = 'iOS (iPad)'

    # تحديد نوع الجهاز
    is_mobile = any(keyword in ua_lower for keyword in [
        'mobile', 'android', 'iphone', 'ipod', 'blackberry', 'windows phone'
    ])
    is_tablet = any(keyword in ua_lower for keyword in [
        'tablet', 'ipad', 'kindle', 'silk', 'playbook'
    ])
    is_desktop = not (is_mobile or is_tablet)

    # كشف البوتات
    is_bot = any(keyword in ua_lower for keyword in [
        'bot', 'crawler', 'spider', 'scraper', 'curl', 'wget', 'python-requests',
        'googlebot', 'bingbot', 'slurp', 'duckduckbot', 'baiduspider', 'yandexbot'
    ])

    return {
        'browser': browser,
        'browser_version': browser_version,
        'platform': platform,
        'is_mobile': is_mobile,
        'is_tablet': is_tablet,
        'is_desktop': is_desktop,
        'is_bot': is_bot,
        'raw_user_agent': user_agent
    }

def detect_suspicious_patterns(request_info: Dict[str, Any]) -> Dict[str, Any]:
    """
    كشف الأنماط المشبوهة في الطلب
    """
    suspicious_indicators = []
    risk_score = 0

    user_agent = request_info.get('user_agent', '')
    headers = request_info.get('fingerprint_headers', {})

    # فحص User Agent
    ua_analysis = analyze_user_agent(user_agent)
    if ua_analysis['is_bot']:
        suspicious_indicators.append('bot_detected')
        risk_score += 30

    if not user_agent or len(user_agent) < 10:
        suspicious_indicators.append('missing_or_short_user_agent')
        risk_score += 20

    # فحص البصمة
    if not headers.get('device_fingerprint'):
        suspicious_indicators.append('missing_device_fingerprint')
        risk_score += 15

    if not headers.get('hardware_fingerprint'):
        suspicious_indicators.append('missing_hardware_fingerprint')
        risk_score += 10

    # فحص الشبكة
    network_info = request_info.get('network_info', {})
    if network_info.get('proxy_detected'):
        suspicious_indicators.append('proxy_detected')
        risk_score += 5

    # فحص headers الأمنية
    security_info = request_info.get('security_info', {})
    if not security_info.get('https'):
        suspicious_indicators.append('insecure_connection')
        risk_score += 10

    # تحديد مستوى المخاطر
    if risk_score >= 50:
        risk_level = 'high'
    elif risk_score >= 25:
        risk_level = 'medium'
    elif risk_score >= 10:
        risk_level = 'low'
    else:
        risk_level = 'minimal'

    return {
        'suspicious_indicators': suspicious_indicators,
        'risk_score': risk_score,
        'risk_level': risk_level,
        'is_suspicious': risk_score >= 25,
        'recommendation': _get_security_recommendation(risk_level, suspicious_indicators)
    }

def _get_security_recommendation(risk_level: str, indicators: list) -> str:
    """
    الحصول على توصية أمنية بناءً على مستوى المخاطر
    """
    if risk_level == 'high':
        return 'يُنصح بحظر هذا الطلب أو طلب تحقق إضافي'
    elif risk_level == 'medium':
        return 'يُنصح بمراقبة هذا الجهاز عن كثب'
    elif risk_level == 'low':
        return 'مراقبة عادية مع تسجيل النشاط'
    else:
        return 'الطلب يبدو طبيعياً'
