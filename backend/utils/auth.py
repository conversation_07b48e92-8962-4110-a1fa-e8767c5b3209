from fastapi import Depends, HTTPException, status
from fastapi.security import OA<PERSON>2<PERSON><PERSON><PERSON><PERSON>earer, OAuth2PasswordRequestForm
from jose import JW<PERSON>rror, jwt
from sqlalchemy.orm import Session
from datetime import datetime, timedelta, timezone
from typing import Optional, cast, Any, Union
from sqlalchemy import select
import os
from dotenv import load_dotenv
from typing import TYPE_CHECKING

from database.session import get_db

# Define UserRole enum here to avoid circular imports
import enum
class UserRole(str, enum.Enum):
    ADMIN = "admin"
    CASHIER = "cashier"

# Use TYPE_CHECKING for type hints to avoid circular imports
if TYPE_CHECKING:
    from models.user import User

load_dotenv()

# Configuration
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-here")
ALGORITHM = os.getenv("ALGORITHM", "HS256")
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/auth/token", auto_error=True)
optional_oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/auth/token", auto_error=False)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Create a JWT access token.
    """
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=15)
    to_encode.update({"exp": expire, "type": "access"})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def decode_access_token(token: str) -> Optional[dict]:
    """
    Decode a JWT access token and return the payload.
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        token_type = payload.get("type")

        if token_type != "access":
            return None

        return payload
    except JWTError:
        return None

async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    # Import User here to avoid circular imports
    from models.user import User
    """
    Get the current authenticated user.
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        print(f"Decoding token: {token[:10]}...")
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username = payload.get("sub")
        token_type = payload.get("type")
        role = payload.get("role")

        print(f"Token payload: username={username}, type={token_type}, role={role}")

        if username is None or token_type != "access":
            print(f"Invalid token type or missing username: {token_type}")
            raise credentials_exception

        if not isinstance(username, str):
            print(f"Username is not a string: {type(username)}")
            raise credentials_exception

        print(f"Looking up user: {username}")
        stmt = select(User).where(User.username == username)
        user = db.execute(stmt).scalar_one_or_none()

        if user is None:
            print(f"User not found: {username}")
            raise credentials_exception

        print(f"User authenticated: {user.username}, role: {user.role}")
        return user
    except JWTError as e:
        print(f"JWT error: {e}")
        raise credentials_exception
    except Exception as e:
        print(f"Unexpected error in get_current_user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )

async def get_optional_user(
    token: Optional[str] = Depends(optional_oauth2_scheme),
    db: Session = Depends(get_db)
):
    # Import User here to avoid circular imports
    from models.user import User
    """
    Get the current user if authenticated, otherwise return None.
    This dependency can be used for endpoints that work with or without authentication.
    """
    if not token:
        return None

    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username = payload.get("sub")
        token_type = payload.get("type")

        if username is None or token_type != "access" or not isinstance(username, str):
            return None

        stmt = select(User).where(User.username == username)
        user = db.execute(stmt).scalar_one_or_none()
        return user
    except JWTError:
        return None

async def get_current_active_user(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Import User here to avoid circular imports
    from models.user import User
    """
    Get the current active user.
    """
    try:
        print(f"Checking if user is active: {current_user.username}")
        stmt = select(User.is_active).where(User.id == current_user.id)
        is_active = db.execute(stmt).scalar_one()

        if not is_active:
            print(f"User is inactive: {current_user.username}")
            raise HTTPException(status_code=400, detail="Inactive user")

        print(f"User is active: {current_user.username}")
        return current_user
    except Exception as e:
        print(f"Error in get_current_active_user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )

async def get_current_admin_user(
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    # Import User here to avoid circular imports
    from models.user import User
    """
    Get the current admin user.
    """
    try:
        stmt = select(User.role).where(User.id == current_user.id)
        role = db.execute(stmt).scalar_one()
        if role != UserRole.ADMIN:
            print(f"User {current_user.username} with role {role} tried to access admin-only endpoint")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enough privileges"
            )
        print(f"Admin user {current_user.username} authenticated successfully")
        return current_user
    except Exception as e:
        print(f"Error in get_current_admin_user: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed"
        )

# Alias for backward compatibility
get_current_user_optional = get_optional_user

def get_user_by_id(db: Session, user_id: int):
    """
    جلب المستخدم بواسطة المعرف
    """
    try:
        from models.user import User
        stmt = select(User).where(User.id == user_id)
        user = db.execute(stmt).scalar_one_or_none()
        return user
    except Exception as e:
        print(f"Error in get_user_by_id: {e}")
        return None