"""
نظام النسخ الاحتياطي لقاعدة البيانات
"""

import os
import shutil
from datetime import datetime
from typing import Optional, List, Dict, Any
import logging
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

class DatabaseBackup:
    """فئة إدارة النسخ الاحتياطية لقاعدة البيانات"""

    def __init__(self, db_path: Optional[str] = None, backup_dir: str = "backend/backups", db_session: Optional[Session] = None):
        self.db_path = db_path  # None للـ PostgreSQL
        self.backup_dir = self._get_backup_path(backup_dir, db_session)
        self.is_postgresql = db_path is None  # تحديد نوع قاعدة البيانات

        # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)

    def _get_backup_path(self, default_path: str, db_session: Optional[Session] = None) -> str:
        """الحصول على مسار النسخ الاحتياطية من الإعدادات"""
        # تحديد المسار الأساسي بناءً على موقع الملف الحالي
        from pathlib import Path
        current_file = Path(__file__).resolve()
        project_root = current_file.parent.parent  # من utils إلى backend (جذر المشروع)

        if db_session is None:
            # تحويل المسار النسبي إلى مطلق
            if not os.path.isabs(default_path):
                return str(project_root / default_path)
            return default_path

        try:
            from models.setting import Setting
            backup_path_setting = db_session.query(Setting).filter(Setting.key == "backup_path").first()
            if backup_path_setting and backup_path_setting.value:
                custom_path = backup_path_setting.value.strip()
                if custom_path:
                    # التأكد من أن المسار صالح
                    if os.path.isabs(custom_path):
                        # مسار مطلق
                        return custom_path
                    else:
                        # مسار نسبي - تحويله إلى مطلق
                        return str(project_root / custom_path)

            # استخدام المسار الافتراضي
            if not os.path.isabs(default_path):
                return str(project_root / default_path)
            return default_path

        except Exception as e:
            logger.warning(f"فشل في جلب مسار النسخ الاحتياطية من الإعدادات: {e}")
            # استخدام المسار الافتراضي
            if not os.path.isabs(default_path):
                return str(project_root / default_path)
            return default_path

    def create_backup(self, backup_name: Optional[str] = None) -> Dict[str, Any]:
        """
        إنشاء نسخة احتياطية من قاعدة البيانات

        Args:
            backup_name: اسم النسخة الاحتياطية (اختياري)

        Returns:
            dict: معلومات النسخة الاحتياطية المنشأة
        """
        try:
            if self.is_postgresql:
                return self._create_postgresql_backup(backup_name)
            else:
                return self._create_sqlite_backup(backup_name)

        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "فشل في إنشاء النسخة الاحتياطية"
            }

    def _create_postgresql_backup(self, backup_name: Optional[str] = None) -> Dict[str, Any]:
        """إنشاء نسخة احتياطية PostgreSQL باستخدام pg_dump"""
        try:
            import subprocess

            # إنشاء اسم النسخة الاحتياطية
            if not backup_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"smartpos_backup_{timestamp}.sql"

            backup_path = os.path.join(self.backup_dir, backup_name)

            # استخدام pg_dump لإنشاء النسخة الاحتياطية
            cmd = [
                "pg_dump",
                "--host=localhost",
                "--port=5432",
                "--username=postgres",
                "--dbname=smartpos_db",
                "--file=" + backup_path,
                "--verbose",
                "--clean",
                "--create"
            ]

            # تعيين كلمة المرور عبر متغير البيئة
            env = os.environ.copy()
            env['PGPASSWORD'] = 'password'

            # تنفيذ الأمر
            result = subprocess.run(cmd, env=env, capture_output=True, text=True)

            if result.returncode == 0:
                # الحصول على معلومات النسخة الاحتياطية
                backup_info = self.get_backup_info(backup_path)

                logger.info(f"تم إنشاء النسخة الاحتياطية PostgreSQL بنجاح: {backup_path}")

                return {
                    "success": True,
                    "backup_path": backup_path,
                    "backup_name": backup_name,
                    "size": backup_info["size"],
                    "created_at": backup_info["created_at"],
                    "message": "تم إنشاء النسخة الاحتياطية PostgreSQL بنجاح"
                }
            else:
                raise Exception(f"pg_dump failed: {result.stderr}")

        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية PostgreSQL: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "فشل في إنشاء النسخة الاحتياطية PostgreSQL"
            }

    def _create_sqlite_backup(self, backup_name: Optional[str] = None) -> Dict[str, Any]:
        """إنشاء نسخة احتياطية SQLite (للتوافق مع النسخ القديمة)"""
        try:
            # التحقق من وجود قاعدة البيانات
            if not os.path.exists(self.db_path):
                raise FileNotFoundError(f"قاعدة البيانات غير موجودة: {self.db_path}")

            # إنشاء اسم النسخة الاحتياطية
            if not backup_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"smartpos_backup_{timestamp}.db"

            backup_path = os.path.join(self.backup_dir, backup_name)

            # إنشاء النسخة الاحتياطية باستخدام نسخ الملف البسيط
            shutil.copy2(self.db_path, backup_path)

            # الحصول على معلومات النسخة الاحتياطية
            backup_info = self.get_backup_info(backup_path)

            logger.info(f"تم إنشاء النسخة الاحتياطية SQLite بنجاح: {backup_path}")

            return {
                "success": True,
                "backup_path": backup_path,
                "backup_name": backup_name,
                "size": backup_info["size"],
                "created_at": backup_info["created_at"],
                "message": "تم إنشاء النسخة الاحتياطية SQLite بنجاح"
            }

        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية SQLite: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "فشل في إنشاء النسخة الاحتياطية SQLite"
            }

    def get_backup_info(self, backup_path: str) -> Dict[str, Any]:
        """الحصول على معلومات النسخة الاحتياطية"""
        try:
            stat = os.stat(backup_path)
            size_bytes = stat.st_size

            # تحويل الحجم إلى وحدة مناسبة
            if size_bytes < 1024:
                size_str = f"{size_bytes} بايت"
            elif size_bytes < 1024 * 1024:
                size_str = f"{round(size_bytes / 1024, 1)} KB"
            else:
                size_str = f"{round(size_bytes / (1024 * 1024), 2)} MB"

            created_at = datetime.fromtimestamp(stat.st_ctime).strftime("%Y-%m-%d %H:%M:%S")

            return {
                "size": size_str,
                "created_at": created_at,
                "size_bytes": size_bytes
            }
        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات النسخة الاحتياطية: {str(e)}")
            return {
                "size": "غير معروف",
                "created_at": "غير معروف",
                "size_bytes": 0
            }

    def list_backups(self) -> List[Dict[str, Any]]:
        """قائمة بجميع النسخ الاحتياطية المتاحة"""
        try:
            backups = []

            if not os.path.exists(self.backup_dir):
                return backups

            for filename in os.listdir(self.backup_dir):
                if filename.endswith('.db'):
                    backup_path = os.path.join(self.backup_dir, filename)
                    backup_info = self.get_backup_info(backup_path)

                    backups.append({
                        "name": filename,
                        "path": backup_path,
                        "size": backup_info["size"],
                        "created_at": backup_info["created_at"],
                        "size_bytes": backup_info["size_bytes"]
                    })

            # ترتيب النسخ الاحتياطية حسب تاريخ الإنشاء (الأحدث أولاً)
            backups.sort(key=lambda x: x["created_at"], reverse=True)

            return backups

        except Exception as e:
            logger.error(f"خطأ في جلب قائمة النسخ الاحتياطية: {str(e)}")
            return []

    def get_latest_backup(self) -> Optional[Dict[str, Any]]:
        """الحصول على آخر نسخة احتياطية"""
        backups = self.list_backups()
        return backups[0] if backups else None

    def delete_backup(self, backup_name: str) -> Dict[str, Any]:
        """حذف نسخة احتياطية"""
        try:
            backup_path = os.path.join(self.backup_dir, backup_name)

            if not os.path.exists(backup_path):
                return {
                    "success": False,
                    "message": "النسخة الاحتياطية غير موجودة"
                }

            os.remove(backup_path)

            logger.info(f"تم حذف النسخة الاحتياطية: {backup_path}")

            return {
                "success": True,
                "message": "تم حذف النسخة الاحتياطية بنجاح"
            }

        except Exception as e:
            logger.error(f"خطأ في حذف النسخة الاحتياطية: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "فشل في حذف النسخة الاحتياطية"
            }

    def cleanup_old_backups(self, keep_count: int = 10) -> Dict[str, Any]:
        """تنظيف النسخ الاحتياطية القديمة والاحتفاظ بعدد محدد"""
        try:
            backups = self.list_backups()

            if len(backups) <= keep_count:
                return {
                    "success": True,
                    "message": f"لا توجد نسخ احتياطية قديمة للحذف. العدد الحالي: {len(backups)}"
                }

            # حذف النسخ الاحتياطية الزائدة
            deleted_count = 0
            for backup in backups[keep_count:]:
                result = self.delete_backup(backup["name"])
                if result["success"]:
                    deleted_count += 1

            return {
                "success": True,
                "message": f"تم حذف {deleted_count} نسخة احتياطية قديمة"
            }

        except Exception as e:
            logger.error(f"خطأ في تنظيف النسخ الاحتياطية القديمة: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "فشل في تنظيف النسخ الاحتياطية القديمة"
            }

# إنشاء مثيل عام للنسخ الاحتياطي
backup_manager = DatabaseBackup()
