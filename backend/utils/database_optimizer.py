"""
أدوات تحسين قاعدة البيانات لتحسين الأداء
"""

import logging
import asyncio
from typing import List, Dict, Any, Optional
from sqlalchemy import text, inspect
from sqlalchemy.orm import Session
from database.session import engine, get_db
import time
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class DatabaseOptimizer:
    def __init__(self):
        self.optimization_history = []
        self.last_optimization = None

    async def optimize_database(self) -> Dict[str, Any]:
        """تحسين شامل لقاعدة البيانات"""
        start_time = time.time()
        results = {
            "started_at": datetime.now().isoformat(),
            "operations": [],
            "total_time": 0,
            "status": "success"
        }

        try:
            db = next(get_db())

            # 1. تحليل الجداول
            analyze_result = await self.analyze_tables(db)
            results["operations"].append({
                "operation": "analyze_tables",
                "result": analyze_result,
                "time": analyze_result.get("time", 0)
            })

            # 2. تنظيف البيانات القديمة
            cleanup_result = await self.cleanup_old_data(db)
            results["operations"].append({
                "operation": "cleanup_old_data",
                "result": cleanup_result,
                "time": cleanup_result.get("time", 0)
            })

            # 3. إعادة بناء الفهارس
            reindex_result = await self.reindex_tables(db)
            results["operations"].append({
                "operation": "reindex_tables",
                "result": reindex_result,
                "time": reindex_result.get("time", 0)
            })

            # 4. تحديث إحصائيات الجداول
            stats_result = await self.update_table_statistics(db)
            results["operations"].append({
                "operation": "update_statistics",
                "result": stats_result,
                "time": stats_result.get("time", 0)
            })

            db.close()

        except Exception as e:
            logger.error(f"خطأ في تحسين قاعدة البيانات: {e}")
            results["status"] = "error"
            results["error"] = str(e)

        results["total_time"] = time.time() - start_time
        results["completed_at"] = datetime.now().isoformat()

        self.optimization_history.append(results)
        self.last_optimization = datetime.now()

        # الاحتفاظ بآخر 10 عمليات تحسين فقط
        if len(self.optimization_history) > 10:
            self.optimization_history.pop(0)

        return results

    async def analyze_tables(self, db: Session) -> Dict[str, Any]:
        """تحليل الجداول لتحسين الأداء"""
        start_time = time.time()
        result = {
            "analyzed_tables": [],
            "recommendations": [],
            "time": 0
        }

        try:
            # الحصول على قائمة الجداول
            inspector = inspect(engine)
            tables = inspector.get_table_names()

            for table_name in tables:
                try:
                    # تحليل الجدول
                    db.execute(text(f"ANALYZE {table_name}"))
                    result["analyzed_tables"].append(table_name)

                    # فحص حجم الجدول (SQLite)
                    size_query = text(f"SELECT COUNT(*) as row_count FROM {table_name}")
                    size_result = db.execute(size_query).fetchone()

                    if size_result and size_result[0] > 10000:
                        result["recommendations"].append(
                            f"الجدول {table_name} يحتوي على {size_result[0]} صف - قد يحتاج تنظيف"
                        )

                except Exception as e:
                    logger.warning(f"فشل في تحليل الجدول {table_name}: {e}")

            db.commit()

        except Exception as e:
            logger.error(f"خطأ في تحليل الجداول: {e}")
            result["error"] = str(e)

        result["time"] = time.time() - start_time
        return result

    async def cleanup_old_data(self, db: Session) -> Dict[str, Any]:
        """تنظيف البيانات القديمة"""
        start_time = time.time()
        result = {
            "cleaned_tables": [],
            "deleted_rows": 0,
            "time": 0
        }

        try:
            # تنظيف آمن للبيانات - فقط سجلات النظام والأداء (ليس بيانات العملاء)
            cutoff_date = datetime.now() - timedelta(days=30)

            # ⚠️ هام: هذه الجداول آمنة للتنظيف ولا تحتوي على بيانات العملاء المهمة
            safe_cleanup_queries = [
                {
                    "table": "system_logs",
                    "description": "سجلات النظام القديمة",
                    "query": f"DELETE FROM system_logs WHERE created_at < '{cutoff_date}' AND log_level IN ('DEBUG', 'INFO')"
                },
                {
                    "table": "device_activity_logs",
                    "description": "سجلات نشاط الأجهزة القديمة",
                    "query": f"DELETE FROM device_activity_logs WHERE created_at < '{cutoff_date}' AND activity_type NOT IN ('LOGIN', 'LOGOUT')"
                },
                {
                    "table": "performance_logs",
                    "description": "سجلات الأداء القديمة",
                    "query": f"DELETE FROM performance_logs WHERE created_at < '{cutoff_date}'"
                },
                {
                    "table": "session_logs",
                    "description": "سجلات الجلسات المنتهية",
                    "query": f"DELETE FROM session_logs WHERE created_at < '{cutoff_date}' AND status = 'EXPIRED'"
                }
            ]

            for cleanup in safe_cleanup_queries:
                try:
                    result_proxy = db.execute(text(cleanup["query"]))
                    # استخدام getattr للحصول على rowcount بشكل آمن
                    deleted_count = getattr(result_proxy, 'rowcount', 0)

                    if deleted_count and deleted_count > 0:
                        result["cleaned_tables"].append({
                            "table": cleanup["table"],
                            "deleted_rows": deleted_count
                        })
                        result["deleted_rows"] += deleted_count

                except Exception as e:
                    logger.warning(f"فشل في تنظيف الجدول {cleanup['table']}: {e}")

            db.commit()

        except Exception as e:
            logger.error(f"خطأ في تنظيف البيانات القديمة: {e}")
            result["error"] = str(e)

        result["time"] = time.time() - start_time
        return result

    async def reindex_tables(self, db: Session) -> Dict[str, Any]:
        """إعادة بناء الفهارس"""
        start_time = time.time()
        result = {
            "reindexed_tables": [],
            "time": 0
        }

        try:
            # الجداول الرئيسية التي تحتاج إعادة فهرسة
            important_tables = [
                "sales", "products", "users", "customers",
                "device_security", "system_logs"
            ]

            for table_name in important_tables:
                try:
                    # إعادة بناء فهارس الجدول
                    db.execute(text(f"REINDEX TABLE {table_name}"))
                    result["reindexed_tables"].append(table_name)

                except Exception as e:
                    logger.warning(f"فشل في إعادة فهرسة الجدول {table_name}: {e}")

            db.commit()

        except Exception as e:
            logger.error(f"خطأ في إعادة بناء الفهارس: {e}")
            result["error"] = str(e)

        result["time"] = time.time() - start_time
        return result

    async def update_table_statistics(self, db: Session) -> Dict[str, Any]:
        """تحديث إحصائيات الجداول"""
        start_time = time.time()
        result = {
            "updated_tables": [],
            "time": 0
        }

        try:
            # تحديث إحصائيات جميع الجداول
            db.execute(text("ANALYZE"))
            result["updated_tables"].append("all_tables")
            db.commit()

        except Exception as e:
            logger.error(f"خطأ في تحديث إحصائيات الجداول: {e}")
            result["error"] = str(e)

        result["time"] = time.time() - start_time
        return result

    def get_optimization_history(self) -> List[Dict[str, Any]]:
        """الحصول على تاريخ عمليات التحسين"""
        return self.optimization_history

    def should_optimize(self) -> bool:
        """فحص ما إذا كان يجب تشغيل التحسين"""
        if not self.last_optimization:
            return True

        # تشغيل التحسين كل 24 ساعة
        return datetime.now() - self.last_optimization > timedelta(hours=24)

    async def get_database_health(self) -> Dict[str, Any]:
        """فحص صحة قاعدة البيانات"""
        try:
            db = next(get_db())

            # فحص الاتصال
            db.execute(text("SELECT 1"))

            # الحصول على إحصائيات قاعدة البيانات (PostgreSQL)
            stats_query = text("""
                SELECT
                    t.table_name,
                    COALESCE(i.index_count, 0) as index_count
                FROM information_schema.tables t
                LEFT JOIN (
                    SELECT
                        schemaname||'.'||tablename as table_name,
                        COUNT(*) as index_count
                    FROM pg_indexes
                    WHERE schemaname = 'public'
                    GROUP BY schemaname, tablename
                ) i ON 'public.'||t.table_name = i.table_name
                WHERE t.table_schema = 'public'
                AND t.table_type = 'BASE TABLE'
                ORDER BY t.table_name
                LIMIT 10
            """)

            stats_result = db.execute(stats_query).fetchall()

            # فحص حجم قاعدة البيانات PostgreSQL
            size_query = text("SELECT pg_database_size(current_database())")
            size_result = db.execute(size_query).scalar()

            # فحص إعدادات PostgreSQL
            pragma_checks = {
                "version": db.execute(text("SELECT version()")).scalar(),
                "max_connections": db.execute(text("SHOW max_connections")).scalar(),
                "shared_buffers": db.execute(text("SHOW shared_buffers")).scalar(),
            }

            db.close()

            return {
                "status": "healthy",
                "connection": "ok",
                "database_size_bytes": size_result or 0,
                "database_size_mb": round((size_result or 0) / (1024 * 1024), 2),
                "table_stats": [
                    {
                        "table": row[0],
                        "index_count": row[1]
                    }
                    for row in stats_result
                ],
                "pragma_settings": pragma_checks,
                "last_optimization": self.last_optimization.isoformat() if self.last_optimization else None,
                "needs_optimization": self.should_optimize()
            }

        except Exception as e:
            logger.error(f"خطأ في فحص صحة قاعدة البيانات: {e}")
            return {
                "status": "error",
                "error": str(e),
                "connection": "failed"
            }

# إنشاء instance عام
database_optimizer = DatabaseOptimizer()
