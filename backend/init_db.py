from sqlalchemy.orm import Session
from passlib.context import CryptContext
from models.user import User, UserRole
from database.session import get_db, engine
from database.base import Base

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def init_db():
    # Create tables
    Base.metadata.create_all(bind=engine)
    
    # Get DB session
    db = next(get_db())
    
    # Check if admin user exists
    admin = db.query(User).filter(User.username == "admin").first()
    
    if not admin:
        print("Creating admin user...")
        # Create admin user
        admin_user = User(
            username="admin",
            full_name="Admin User",
            email="<EMAIL>",
            hashed_password=get_password_hash("admin123"),
            role=UserRole.ADMIN,
            is_active=True
        )
        
        db.add(admin_user)
        db.commit()
        print("Admin user created successfully!")
    else:
        print("Admin user already exists.")

if __name__ == "__main__":
    init_db()
