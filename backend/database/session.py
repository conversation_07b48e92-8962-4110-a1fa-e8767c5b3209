from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from typing import Generator
from pathlib import Path
import logging

from database.base import Base

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# PostgreSQL لا يحتاج مسار ملف محلي

# إعدادات قاعدة البيانات المحسنة للبيانات الكبيرة
DATABASE_CONFIG = {
    # إعدادات الأداء
    "pool_size": 20,  # حجم تجميع الاتصالات
    "max_overflow": 30,  # الحد الأقصى للاتصالات الإضافية
    "pool_timeout": 30,  # مهلة انتظار الاتصال
    "pool_recycle": 3600,  # إعادة تدوير الاتصالات كل ساعة
    "pool_pre_ping": True,  # فحص الاتصالات قبل الاستخدام

    # PostgreSQL يدير هذه الإعدادات تلقائياً
}

# قراءة إعدادات قاعدة البيانات من متغيرات البيئة
import os
from dotenv import load_dotenv

# تحديد مسار ملف .env بشكل صحيح
env_path = Path(__file__).parent.parent / ".env"
load_dotenv(dotenv_path=env_path)

# الحصول على URL قاعدة البيانات من متغيرات البيئة
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql+psycopg2://postgres:password@localhost:5432/smartpos_db")

logger.info(f"Database type: PostgreSQL")
logger.info(f"Database URL: {DATABASE_URL.replace('password', '***') if 'password' in DATABASE_URL else DATABASE_URL}")

# إنشاء محرك قاعدة البيانات PostgreSQL مع التحسينات
engine = create_engine(
    DATABASE_URL,
    pool_size=DATABASE_CONFIG["pool_size"],
    max_overflow=DATABASE_CONFIG["max_overflow"],
    pool_timeout=DATABASE_CONFIG["pool_timeout"],
    pool_recycle=DATABASE_CONFIG["pool_recycle"],
    pool_pre_ping=DATABASE_CONFIG["pool_pre_ping"],
    echo=False,  # تعطيل سجلات SQL في الإنتاج
)

# PostgreSQL لا يحتاج إعدادات pragma خاصة

# إنشاء الفهارس المحسنة
def create_performance_indexes():
    """إنشاء فهارس محسنة للأداء مع البيانات الكبيرة"""
    with engine.connect() as conn:
        try:
            # فهارس PostgreSQL المحسنة
            index_commands = [
                # فهارس للمبيعات
                "CREATE INDEX IF NOT EXISTS idx_sales_created_at ON sales(created_at)",
                "CREATE INDEX IF NOT EXISTS idx_sales_user_date ON sales(user_id, created_at)",
                "CREATE INDEX IF NOT EXISTS idx_sales_customer_date ON sales(customer_id, created_at)",
                "CREATE INDEX IF NOT EXISTS idx_sales_payment_status ON sales(payment_status)",
                "CREATE INDEX IF NOT EXISTS idx_sales_total_amount ON sales(total_amount)",

                # فهارس لعناصر المبيعات
                "CREATE INDEX IF NOT EXISTS idx_sale_items_sale_id ON sale_items(sale_id)",
                "CREATE INDEX IF NOT EXISTS idx_sale_items_product_id ON sale_items(product_id)",
                "CREATE INDEX IF NOT EXISTS idx_sale_items_created_at ON sale_items(created_at)",

                # فهارس للمنتجات
                "CREATE INDEX IF NOT EXISTS idx_products_category ON products(category)",
                "CREATE INDEX IF NOT EXISTS idx_products_is_active ON products(is_active)",
                "CREATE INDEX IF NOT EXISTS idx_products_quantity ON products(quantity)",
                "CREATE INDEX IF NOT EXISTS idx_products_name_search ON products(name)",

                # فهارس للعملاء
                "CREATE INDEX IF NOT EXISTS idx_customers_is_active ON customers(is_active)",
                "CREATE INDEX IF NOT EXISTS idx_customers_name_search ON customers(name)",

                # فهارس للديون
                "CREATE INDEX IF NOT EXISTS idx_customer_debts_customer_paid ON customer_debts(customer_id, is_paid)",
                "CREATE INDEX IF NOT EXISTS idx_customer_debts_created_at ON customer_debts(created_at)",
            ]

            # تنفيذ أوامر إنشاء الفهارس
            for command in index_commands:
                conn.execute(text(command))

            # فهارس مركبة للاستعلامات المعقدة
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_sales_composite_analytics ON sales(created_at, user_id, total_amount, payment_status)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_products_composite_inventory ON products(is_active, category, quantity, min_quantity)"))

            conn.commit()
            logger.info("Performance indexes created successfully")

        except Exception as e:
            logger.error(f"Error creating performance indexes: {e}")
            conn.rollback()

# Create all tables
Base.metadata.create_all(bind=engine)

# إنشاء الفهارس المحسنة
create_performance_indexes()

# SessionLocal class for database sessions with optimizations
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    expire_on_commit=False  # تحسين للأداء
)

def get_db() -> Generator[Session, None, None]:
    """
    Dependency function to get a database session with performance optimizations.
    """
    db = SessionLocal()
    try:
        # PostgreSQL لا يحتاج تحسينات إضافية في الجلسة
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()

# دالة لمراقبة أداء قاعدة البيانات
def get_database_stats():
    """الحصول على إحصائيات أداء قاعدة البيانات"""
    with engine.connect() as conn:
        try:
            # إحصائيات PostgreSQL
            # حجم قاعدة البيانات
            size_result = conn.execute(text("SELECT pg_database_size(current_database())"))
            db_size = size_result.scalar()

            # إحصائيات الفهارس
            index_stats = conn.execute(text("""
                SELECT indexname, tablename
                FROM pg_indexes
                WHERE schemaname = 'public'
            """))
            indexes = index_stats.fetchall()

            # إحصائيات الجداول
            table_stats = conn.execute(text("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
            """))
            tables = table_stats.fetchall()

            return {
                "database_type": "PostgreSQL",
                "database_size_bytes": db_size,
                "database_size_mb": round(db_size / (1024 * 1024), 2) if db_size else 0,
                "total_indexes": len(indexes),
                "total_tables": len(tables),
                "indexes": [{"name": idx[0], "table": idx[1]} for idx in indexes],
                "tables": [table[0] for table in tables]
            }
        except Exception as e:
            logger.error(f"Error getting database stats: {e}")
            return {"error": str(e)}