-- Migration: تحديث أوصاف خصائص المتغيرات
-- التاريخ: 2025-01-27
-- الوصف: إضافة أوصاف مفصلة لجميع خصائص المتغيرات الموجودة

-- تحديث أوصاف الخصائص الموجودة
UPDATE variant_attributes SET description = 'خاصية تحديد حجم المنتج مثل الملابس والأحذية (صغير، متوسط، كبير)' WHERE name = 'Size';

UPDATE variant_attributes SET description = 'خاصية تحديد لون المنتج مع إمكانية عرض كود اللون الفعلي' WHERE name = 'Color';

UPDATE variant_attributes SET description = 'خاصية تحديد المادة المصنوع منها المنتج (قطن، جلد، صناعي، إلخ)' WHERE name = 'Material';

UPDATE variant_attributes SET description = 'خاصية تحديد وزن المنتج أو فئة الوزن (خفيف، متوسط، ثقيل)' WHERE name = 'Weight';

UPDATE variant_attributes SET description = 'خاصية تحديد نمط أو طراز المنتج (كاجوال، رسمي، رياضي)' WHERE name = 'Style';

UPDATE variant_attributes SET description = 'خاصية تحديد نقشة أو تصميم المنتج (سادة، مخطط، منقوش)' WHERE name = 'Pattern';

UPDATE variant_attributes SET description = 'خاصية تحديد سعة الذاكرة للأجهزة الإلكترونية (8GB، 16GB، إلخ)' WHERE name = 'Memory';

UPDATE variant_attributes SET description = 'خاصية تحديد سعة التخزين للأجهزة الإلكترونية (128GB، 256GB، إلخ)' WHERE name = 'Storage';

UPDATE variant_attributes SET description = 'خاصية تحديد طول المنتج أو فئة الطول (قصير، متوسط، طويل)' WHERE name = 'Length';

UPDATE variant_attributes SET description = 'خاصية تحديد سعة أو حجم المنتج (صغير، متوسط، كبير)' WHERE name = 'Capacity';

-- إضافة خصائص إضافية مفيدة مع أوصافها وقيم افتراضية
INSERT INTO variant_attributes (name, name_ar, description, attribute_type, sort_order, created_by) VALUES
('Brand', 'العلامة التجارية', 'خاصية تحديد العلامة التجارية للمنتج', 'list', 11, 1),
('Model', 'الموديل', 'خاصية تحديد موديل أو رقم المنتج', 'text', 12, 1),
('Condition', 'الحالة', 'خاصية تحديد حالة المنتج (جديد، مستعمل، مجدد)', 'list', 13, 1),
('Gender', 'الجنس', 'خاصية تحديد الجنس المستهدف للمنتج (رجالي، نسائي، أطفال)', 'list', 14, 1),
('Age_Group', 'الفئة العمرية', 'خاصية تحديد الفئة العمرية المستهدفة للمنتج', 'list', 15, 1),
('Season', 'الموسم', 'خاصية تحديد الموسم المناسب للمنتج (صيف، شتاء، ربيع، خريف)', 'list', 16, 1),
('Warranty', 'الضمان', 'خاصية تحديد فترة الضمان للمنتج', 'list', 17, 1),
('Origin', 'بلد المنشأ', 'خاصية تحديد بلد صنع أو منشأ المنتج', 'list', 18, 1),
('Fragrance', 'العطر', 'خاصية تحديد نوع العطر أو الرائحة للمنتجات العطرية', 'list', 19, 1),
('Flavor', 'النكهة', 'خاصية تحديد نكهة المنتجات الغذائية والمشروبات', 'list', 20, 1)
ON CONFLICT (name) DO NOTHING;

-- إضافة قيم افتراضية للخصائص الجديدة

-- قيم العلامة التجارية
INSERT INTO variant_values (attribute_id, value, value_ar, sort_order) VALUES
((SELECT id FROM variant_attributes WHERE name = 'Brand'), 'Samsung', 'سامسونج', 1),
((SELECT id FROM variant_attributes WHERE name = 'Brand'), 'Apple', 'آبل', 2),
((SELECT id FROM variant_attributes WHERE name = 'Brand'), 'Nike', 'نايك', 3),
((SELECT id FROM variant_attributes WHERE name = 'Brand'), 'Adidas', 'أديداس', 4),
((SELECT id FROM variant_attributes WHERE name = 'Brand'), 'Generic', 'عام', 5);

-- قيم الحالة
INSERT INTO variant_values (attribute_id, value, value_ar, sort_order) VALUES
((SELECT id FROM variant_attributes WHERE name = 'Condition'), 'New', 'جديد', 1),
((SELECT id FROM variant_attributes WHERE name = 'Condition'), 'Used', 'مستعمل', 2),
((SELECT id FROM variant_attributes WHERE name = 'Condition'), 'Refurbished', 'مجدد', 3);

-- قيم الجنس
INSERT INTO variant_values (attribute_id, value, value_ar, sort_order) VALUES
((SELECT id FROM variant_attributes WHERE name = 'Gender'), 'Male', 'رجالي', 1),
((SELECT id FROM variant_attributes WHERE name = 'Gender'), 'Female', 'نسائي', 2),
((SELECT id FROM variant_attributes WHERE name = 'Gender'), 'Kids', 'أطفال', 3),
((SELECT id FROM variant_attributes WHERE name = 'Gender'), 'Unisex', 'للجنسين', 4);

-- قيم الفئة العمرية
INSERT INTO variant_values (attribute_id, value, value_ar, sort_order) VALUES
((SELECT id FROM variant_attributes WHERE name = 'Age_Group'), 'Baby', 'رضع (0-2 سنة)', 1),
((SELECT id FROM variant_attributes WHERE name = 'Age_Group'), 'Toddler', 'أطفال صغار (2-5 سنوات)', 2),
((SELECT id FROM variant_attributes WHERE name = 'Age_Group'), 'Kids', 'أطفال (5-12 سنة)', 3),
((SELECT id FROM variant_attributes WHERE name = 'Age_Group'), 'Teen', 'مراهقين (13-17 سنة)', 4),
((SELECT id FROM variant_attributes WHERE name = 'Age_Group'), 'Adult', 'بالغين (18+ سنة)', 5),
((SELECT id FROM variant_attributes WHERE name = 'Age_Group'), 'Senior', 'كبار السن (65+ سنة)', 6);

-- قيم الموسم
INSERT INTO variant_values (attribute_id, value, value_ar, sort_order) VALUES
((SELECT id FROM variant_attributes WHERE name = 'Season'), 'Spring', 'ربيع', 1),
((SELECT id FROM variant_attributes WHERE name = 'Season'), 'Summer', 'صيف', 2),
((SELECT id FROM variant_attributes WHERE name = 'Season'), 'Autumn', 'خريف', 3),
((SELECT id FROM variant_attributes WHERE name = 'Season'), 'Winter', 'شتاء', 4),
((SELECT id FROM variant_attributes WHERE name = 'Season'), 'All_Season', 'جميع المواسم', 5);

-- قيم الضمان
INSERT INTO variant_values (attribute_id, value, value_ar, sort_order) VALUES
((SELECT id FROM variant_attributes WHERE name = 'Warranty'), '1_Month', 'شهر واحد', 1),
((SELECT id FROM variant_attributes WHERE name = 'Warranty'), '3_Months', '3 أشهر', 2),
((SELECT id FROM variant_attributes WHERE name = 'Warranty'), '6_Months', '6 أشهر', 3),
((SELECT id FROM variant_attributes WHERE name = 'Warranty'), '1_Year', 'سنة واحدة', 4),
((SELECT id FROM variant_attributes WHERE name = 'Warranty'), '2_Years', 'سنتان', 5),
((SELECT id FROM variant_attributes WHERE name = 'Warranty'), '3_Years', '3 سنوات', 6),
((SELECT id FROM variant_attributes WHERE name = 'Warranty'), 'Lifetime', 'مدى الحياة', 7);

-- قيم بلد المنشأ
INSERT INTO variant_values (attribute_id, value, value_ar, sort_order) VALUES
((SELECT id FROM variant_attributes WHERE name = 'Origin'), 'Saudi_Arabia', 'السعودية', 1),
((SELECT id FROM variant_attributes WHERE name = 'Origin'), 'UAE', 'الإمارات', 2),
((SELECT id FROM variant_attributes WHERE name = 'Origin'), 'Egypt', 'مصر', 3),
((SELECT id FROM variant_attributes WHERE name = 'Origin'), 'Turkey', 'تركيا', 4),
((SELECT id FROM variant_attributes WHERE name = 'Origin'), 'China', 'الصين', 5),
((SELECT id FROM variant_attributes WHERE name = 'Origin'), 'USA', 'أمريكا', 6),
((SELECT id FROM variant_attributes WHERE name = 'Origin'), 'Germany', 'ألمانيا', 7),
((SELECT id FROM variant_attributes WHERE name = 'Origin'), 'Japan', 'اليابان', 8),
((SELECT id FROM variant_attributes WHERE name = 'Origin'), 'South_Korea', 'كوريا الجنوبية', 9),
((SELECT id FROM variant_attributes WHERE name = 'Origin'), 'Italy', 'إيطاليا', 10);

-- قيم العطر
INSERT INTO variant_values (attribute_id, value, value_ar, sort_order) VALUES
((SELECT id FROM variant_attributes WHERE name = 'Fragrance'), 'Floral', 'زهري', 1),
((SELECT id FROM variant_attributes WHERE name = 'Fragrance'), 'Woody', 'خشبي', 2),
((SELECT id FROM variant_attributes WHERE name = 'Fragrance'), 'Fresh', 'منعش', 3),
((SELECT id FROM variant_attributes WHERE name = 'Fragrance'), 'Oriental', 'شرقي', 4),
((SELECT id FROM variant_attributes WHERE name = 'Fragrance'), 'Citrus', 'حمضي', 5),
((SELECT id FROM variant_attributes WHERE name = 'Fragrance'), 'Vanilla', 'فانيليا', 6),
((SELECT id FROM variant_attributes WHERE name = 'Fragrance'), 'Musk', 'مسك', 7);

-- قيم النكهة
INSERT INTO variant_values (attribute_id, value, value_ar, sort_order) VALUES
((SELECT id FROM variant_attributes WHERE name = 'Flavor'), 'Vanilla', 'فانيليا', 1),
((SELECT id FROM variant_attributes WHERE name = 'Flavor'), 'Chocolate', 'شوكولاتة', 2),
((SELECT id FROM variant_attributes WHERE name = 'Flavor'), 'Strawberry', 'فراولة', 3),
((SELECT id FROM variant_attributes WHERE name = 'Flavor'), 'Mint', 'نعناع', 4),
((SELECT id FROM variant_attributes WHERE name = 'Flavor'), 'Orange', 'برتقال', 5),
((SELECT id FROM variant_attributes WHERE name = 'Flavor'), 'Lemon', 'ليمون', 6),
((SELECT id FROM variant_attributes WHERE name = 'Flavor'), 'Apple', 'تفاح', 7),
((SELECT id FROM variant_attributes WHERE name = 'Flavor'), 'Grape', 'عنب', 8),
((SELECT id FROM variant_attributes WHERE name = 'Flavor'), 'Coffee', 'قهوة', 9),
((SELECT id FROM variant_attributes WHERE name = 'Flavor'), 'Caramel', 'كراميل', 10);

-- تحديث updated_at للخصائص المحدثة
UPDATE variant_attributes SET updated_at = CURRENT_TIMESTAMP WHERE description IS NOT NULL;

-- إضافة تعليق على التحديث
COMMENT ON TABLE variant_attributes IS 'جدول خصائص المتغيرات - محدث بأوصاف شاملة وخصائص إضافية مفيدة';

-- عرض ملخص التحديثات
SELECT 
    'تم تحديث أوصاف الخصائص الموجودة وإضافة خصائص جديدة' as status,
    COUNT(*) as total_attributes
FROM variant_attributes;

SELECT 
    'إجمالي القيم المضافة' as status,
    COUNT(*) as total_values
FROM variant_values;
