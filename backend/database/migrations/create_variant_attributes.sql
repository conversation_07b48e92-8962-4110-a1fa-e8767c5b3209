-- Migration: إنشاء جداول خصائص المتغيرات
-- التاريخ: 2025-01-27
-- الوصف: إنشاء جداول variant_attributes و variant_values لإدارة خصائص المنتجات

-- إ<PERSON><PERSON><PERSON>ء جدول خصائص المتغيرات
CREATE TABLE IF NOT EXISTS variant_attributes (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    name_ar VARCHAR(100) NOT NULL,
    description TEXT,
    attribute_type VARCHAR(50) DEFAULT 'text' NOT NULL,
    is_required BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id)
);

-- إن<PERSON>اء فهارس للجدول الأول
CREATE INDEX IF NOT EXISTS idx_variant_attributes_name ON variant_attributes(name);
CREATE INDEX IF NOT EXISTS idx_variant_attributes_name_ar ON variant_attributes(name_ar);
CREATE INDEX IF NOT EXISTS idx_variant_attributes_active ON variant_attributes(is_active);
CREATE INDEX IF NOT EXISTS idx_variant_attributes_sort ON variant_attributes(sort_order);

-- إنشاء جدول قيم خصائص المتغيرات
CREATE TABLE IF NOT EXISTS variant_values (
    id SERIAL PRIMARY KEY,
    attribute_id INTEGER NOT NULL REFERENCES variant_attributes(id) ON DELETE CASCADE,
    value VARCHAR(100) NOT NULL,
    value_ar VARCHAR(100) NOT NULL,
    color_code VARCHAR(7), -- للألوان - hex color code
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء فهارس للجدول الثاني
CREATE INDEX IF NOT EXISTS idx_variant_values_attribute_id ON variant_values(attribute_id);
CREATE INDEX IF NOT EXISTS idx_variant_values_value ON variant_values(value);
CREATE INDEX IF NOT EXISTS idx_variant_values_value_ar ON variant_values(value_ar);
CREATE INDEX IF NOT EXISTS idx_variant_values_active ON variant_values(is_active);
CREATE INDEX IF NOT EXISTS idx_variant_values_sort ON variant_values(sort_order);

-- إدراج البيانات الافتراضية مع الأوصاف
INSERT INTO variant_attributes (name, name_ar, description, attribute_type, sort_order, created_by) VALUES
('Size', 'الحجم', 'خاصية تحديد حجم المنتج مثل الملابس والأحذية (صغير، متوسط، كبير)', 'list', 1, 1),
('Color', 'اللون', 'خاصية تحديد لون المنتج مع إمكانية عرض كود اللون الفعلي', 'color', 2, 1),
('Material', 'المادة', 'خاصية تحديد المادة المصنوع منها المنتج (قطن، جلد، صناعي، إلخ)', 'list', 3, 1),
('Weight', 'الوزن', 'خاصية تحديد وزن المنتج أو فئة الوزن (خفيف، متوسط، ثقيل)', 'list', 4, 1),
('Style', 'النمط', 'خاصية تحديد نمط أو طراز المنتج (كاجوال، رسمي، رياضي)', 'list', 5, 1),
('Pattern', 'النقشة', 'خاصية تحديد نقشة أو تصميم المنتج (سادة، مخطط، منقوش)', 'list', 6, 1),
('Memory', 'الذاكرة', 'خاصية تحديد سعة الذاكرة للأجهزة الإلكترونية (8GB، 16GB، إلخ)', 'list', 7, 1),
('Storage', 'التخزين', 'خاصية تحديد سعة التخزين للأجهزة الإلكترونية (128GB، 256GB، إلخ)', 'list', 8, 1),
('Length', 'الطول', 'خاصية تحديد طول المنتج أو فئة الطول (قصير، متوسط، طويل)', 'list', 9, 1),
('Capacity', 'السعة', 'خاصية تحديد سعة أو حجم المنتج (صغير، متوسط، كبير)', 'list', 10, 1)
ON CONFLICT (name) DO NOTHING;

-- إدراج القيم الافتراضية للحجم
INSERT INTO variant_values (attribute_id, value, value_ar, sort_order) VALUES
((SELECT id FROM variant_attributes WHERE name = 'Size'), 'XS', 'صغير جداً', 1),
((SELECT id FROM variant_attributes WHERE name = 'Size'), 'S', 'صغير', 2),
((SELECT id FROM variant_attributes WHERE name = 'Size'), 'M', 'متوسط', 3),
((SELECT id FROM variant_attributes WHERE name = 'Size'), 'L', 'كبير', 4),
((SELECT id FROM variant_attributes WHERE name = 'Size'), 'XL', 'كبير جداً', 5),
((SELECT id FROM variant_attributes WHERE name = 'Size'), 'XXL', 'كبير جداً جداً', 6);

-- إدراج القيم الافتراضية للون
INSERT INTO variant_values (attribute_id, value, value_ar, color_code, sort_order) VALUES
((SELECT id FROM variant_attributes WHERE name = 'Color'), 'Red', 'أحمر', '#FF0000', 1),
((SELECT id FROM variant_attributes WHERE name = 'Color'), 'Blue', 'أزرق', '#0000FF', 2),
((SELECT id FROM variant_attributes WHERE name = 'Color'), 'Green', 'أخضر', '#00FF00', 3),
((SELECT id FROM variant_attributes WHERE name = 'Color'), 'Black', 'أسود', '#000000', 4),
((SELECT id FROM variant_attributes WHERE name = 'Color'), 'White', 'أبيض', '#FFFFFF', 5);

-- إدراج القيم الافتراضية للمادة
INSERT INTO variant_values (attribute_id, value, value_ar, sort_order) VALUES
((SELECT id FROM variant_attributes WHERE name = 'Material'), 'Cotton', 'قطن', 1),
((SELECT id FROM variant_attributes WHERE name = 'Material'), 'Leather', 'جلد', 2),
((SELECT id FROM variant_attributes WHERE name = 'Material'), 'Synthetic', 'صناعي', 3),
((SELECT id FROM variant_attributes WHERE name = 'Material'), 'Silk', 'حرير', 4);

-- إدراج القيم الافتراضية للوزن
INSERT INTO variant_values (attribute_id, value, value_ar, sort_order) VALUES
((SELECT id FROM variant_attributes WHERE name = 'Weight'), 'Light', 'خفيف', 1),
((SELECT id FROM variant_attributes WHERE name = 'Weight'), 'Medium', 'متوسط', 2),
((SELECT id FROM variant_attributes WHERE name = 'Weight'), 'Heavy', 'ثقيل', 3);

-- إدراج القيم الافتراضية للنمط
INSERT INTO variant_values (attribute_id, value, value_ar, sort_order) VALUES
((SELECT id FROM variant_attributes WHERE name = 'Style'), 'Casual', 'كاجوال', 1),
((SELECT id FROM variant_attributes WHERE name = 'Style'), 'Formal', 'رسمي', 2),
((SELECT id FROM variant_attributes WHERE name = 'Style'), 'Sport', 'رياضي', 3);

-- إدراج القيم الافتراضية للنقشة
INSERT INTO variant_values (attribute_id, value, value_ar, sort_order) VALUES
((SELECT id FROM variant_attributes WHERE name = 'Pattern'), 'Solid', 'سادة', 1),
((SELECT id FROM variant_attributes WHERE name = 'Pattern'), 'Striped', 'مخطط', 2),
((SELECT id FROM variant_attributes WHERE name = 'Pattern'), 'Dotted', 'منقوش', 3);

-- إدراج القيم الافتراضية للذاكرة
INSERT INTO variant_values (attribute_id, value, value_ar, sort_order) VALUES
((SELECT id FROM variant_attributes WHERE name = 'Memory'), '8GB', '8 جيجابايت', 1),
((SELECT id FROM variant_attributes WHERE name = 'Memory'), '16GB', '16 جيجابايت', 2),
((SELECT id FROM variant_attributes WHERE name = 'Memory'), '32GB', '32 جيجابايت', 3),
((SELECT id FROM variant_attributes WHERE name = 'Memory'), '64GB', '64 جيجابايت', 4);

-- إدراج القيم الافتراضية للتخزين
INSERT INTO variant_values (attribute_id, value, value_ar, sort_order) VALUES
((SELECT id FROM variant_attributes WHERE name = 'Storage'), '128GB', '128 جيجابايت', 1),
((SELECT id FROM variant_attributes WHERE name = 'Storage'), '256GB', '256 جيجابايت', 2),
((SELECT id FROM variant_attributes WHERE name = 'Storage'), '512GB', '512 جيجابايت', 3),
((SELECT id FROM variant_attributes WHERE name = 'Storage'), '1TB', '1 تيرابايت', 4);

-- إدراج القيم الافتراضية للطول
INSERT INTO variant_values (attribute_id, value, value_ar, sort_order) VALUES
((SELECT id FROM variant_attributes WHERE name = 'Length'), 'Short', 'قصير', 1),
((SELECT id FROM variant_attributes WHERE name = 'Length'), 'Medium', 'متوسط', 2),
((SELECT id FROM variant_attributes WHERE name = 'Length'), 'Long', 'طويل', 3);

-- إدراج القيم الافتراضية للسعة
INSERT INTO variant_values (attribute_id, value, value_ar, sort_order) VALUES
((SELECT id FROM variant_attributes WHERE name = 'Capacity'), 'Small', 'صغير', 1),
((SELECT id FROM variant_attributes WHERE name = 'Capacity'), 'Medium', 'متوسط', 2),
((SELECT id FROM variant_attributes WHERE name = 'Capacity'), 'Large', 'كبير', 3);

-- إنشاء trigger لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_variant_attributes_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_variant_attributes_updated_at
    BEFORE UPDATE ON variant_attributes
    FOR EACH ROW
    EXECUTE FUNCTION update_variant_attributes_updated_at();

-- تعليق على الجداول
COMMENT ON TABLE variant_attributes IS 'جدول خصائص المتغيرات - يحتوي على أنواع الخصائص مثل الحجم واللون';
COMMENT ON TABLE variant_values IS 'جدول قيم خصائص المتغيرات - يحتوي على القيم المحددة لكل خاصية';
