from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import registry

# Create the SQLAlchemy declarative base
Base = declarative_base()

# Create a registry for models
mapper_registry = registry()

# This can be used as a decorator for models
registry_base = mapper_registry.generate_base()

# استيراد جميع النماذج لضمان إنشاء الجداول
def import_all_models():
    """استيراد جميع النماذج لضمان إنشاء الجداول"""
    try:
        # النماذج الأساسية
        from models.user import User  # noqa: F401
        from models.product import Product  # noqa: F401
        from models.customer import Customer, CustomerDebt, DebtPayment  # noqa: F401
        from models.sale import Sale, SaleItem  # noqa: F401
        from models.setting import Setting  # noqa: F401
        from models.scheduled_task import ScheduledTask  # noqa: F401

        # نماذج بصمات الأجهزة الجديدة
        from models.device_fingerprint import DeviceFingerprint, DeviceFingerprintHistory  # noqa: F401

        # نماذج أمان الأجهزة
        from models.device_security import ApprovedDevice, BlockedDevice, PendingDevice, DeviceSecuritySettings  # noqa: F401

        # نماذج نظام المحادثة الجديدة
        from models.chat_message import ChatMessage, ChatRoom, ChatRoomMember, UserOnlineStatus  # noqa: F401

        # نموذج سجلات النظام
        from models.system_log import SystemLog  # noqa: F401

        # نماذج الفئات والعلامات التجارية والوحدات
        from models.category import Category, Subcategory  # noqa: F401
        from models.brand import Brand  # noqa: F401
        from models.unit import Unit  # noqa: F401

        # نماذج المستودعات والفروع
        from models.warehouse import (  # noqa: F401
            Warehouse, WarehouseInventory, WarehouseMovement,
            TransferRequest, TransferRequestItem
        )
        from models.branch import Branch, branch_warehouses  # noqa: F401

        print("✅ تم استيراد جميع النماذج بنجاح")
    except ImportError as e:
        print(f"⚠️ تحذير: فشل في استيراد بعض النماذج: {e}")

# استدعاء الدالة عند استيراد الوحدة
import_all_models()