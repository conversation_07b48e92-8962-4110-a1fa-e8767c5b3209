{"timestamp": "2025-07-08T04:15:17.271547", "logs": [{"id": 2890, "timestamp": "2025-07-07 21:49:42", "level": "CRITICAL", "source": "FRONTEND", "message": "React Component Error", "details": "{\"message\":\"conversations.find is not a function\",\"stack\":\"TypeError: conversations.find is not a function\\n    at getCurrentConversationUser (http://192.168.1.110:5175/src/components/Chat/ChatWindow.tsx?t=1751924981527:197:44)\\n    at ChatWindow (http://192.168.1.110:5175/src/components/Chat/ChatWindow.tsx?t=1751924981527:212:23)\\n    at renderWithHooks (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-WRD5HZVH.js?v=aa8da6b9:11548:26)\\n    at updateFunctionComponent (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-WRD5HZVH.js?v=aa8da6b9:1458...\",\"componentStack\":\"\\n    at ChatWindow (http://192.168.1.110:5175/src/components/Chat/ChatWindow.tsx?t=1751924981527:42:23)\\n    at ChatHeaderButton (http://192.168.1.110:5175/src/components/Chat/ChatHeaderButton.tsx?t=1751924981527:23:29)\\n    at div\\n    at div\\n    at div\\n    at header\\n    at div\\n    at Layout (http://192.168.1.110:5175/src/components/Layout.tsx:41:19)\\n    at ProtectedRoute (http://192.168.1.110:5175/src/App.tsx:62:27)\\n    at RenderedRoute (http://192.168.1.110:5175/node_modules/.vite/deps/react-rou...\",\"errorBoundary\":true}", "stack_trace": "TypeError: conversations.find is not a function\n    at getCurrentConversationUser (http://192.168.1.110:5175/src/components/Chat/ChatWindow.tsx?t=1751924981527:197:44)\n    at ChatWindow (http://192.168.1.110:5175/src/components/Chat/ChatWindow.tsx?t=1751924981527:212:23)\n    at renderWithHooks (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-WRD5HZVH.js?v=aa8da6b9:11548:26)\n    at updateFunctionComponent (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-WRD5HZVH.js?v=aa8da6b9:14582:28)\n    at beginWork (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-WRD5HZVH.js?v=aa8da6b9:15924:22)\n    at beginWork$1 (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-WRD5HZVH.js?v=aa8da6b9:19753:22)\n    at performUnitOfWork (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-WRD5HZVH.js?v=aa8da6b9:19198:20)\n    at workLoopSync (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-WRD5HZVH.js?v=aa8da6b9:19137:13)\n    at renderRootSync (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-WRD5HZVH.js?v=aa8da6b9:19116:15)\n    at recoverFromConcurrentError (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-WRD5HZVH.js?v=aa8da6b9:18736:28)", "user_id": 3, "session_id": "session_1751921762244_y7fqhela3", "resolved": 0, "resolution_notes": null, "created_at": "2025-07-07 21:49:42", "updated_at": "2025-07-07 21:49:42"}], "user_info": {"username": "chiqwa", "email": "<EMAIL>", "role": "admin", "full_name": "نجيب الغدامسي"}, "email_result": {"success": true, "message": "تم إرسال 1 سجل إلى الدعم بنجاح عبر البريد الإلكتروني", "logs_count": 1, "support_email": "<EMAIL>", "sender_email": "<EMAIL>", "real_email": true}}