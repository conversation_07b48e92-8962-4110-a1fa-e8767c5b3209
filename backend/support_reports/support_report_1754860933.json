{"timestamp": "2025-08-10T23:22:13.298499", "logs": [{"id": 1273, "timestamp": "2025-08-07 11:29:03.828210", "level": "CRITICAL", "source": "FRONTEND", "message": "React Component Error", "details": "{\"message\":\"claimsPeriod is not defined\",\"stack\":\"ReferenceError: claimsPeriod is not defined\\n    at WarrantyReportsTab (http://192.168.1.110:5175/src/components/warranty/WarrantyReportsTab.tsx?t=1754558943243:919:31)\\n    at renderWithHooks (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:11596:26)\\n    at mountIndeterminateComponent (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:14974:21)\\n    at beginWork (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7...\",\"componentStack\":\"\\n    at WarrantyReportsTab (http://192.168.1.110:5175/src/components/warranty/WarrantyReportsTab.tsx?t=1754558943243:41:31)\\n    at div\\n    at WarrantyManagement (http://192.168.1.110:5175/src/pages/WarrantyManagement.tsx?t=1754555160509:43:20)\\n    at RenderedRoute (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=29d1b7c2:4088:5)\\n    at Routes (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=29d1b7c2:4558:5)\\n    at Suspense\\n    at AppContent\\n    at ...\",\"errorBoundary\":true}", "stack_trace": "ReferenceError: claimsPeriod is not defined\n    at WarrantyReportsTab (http://192.168.1.110:5175/src/components/warranty/WarrantyReportsTab.tsx?t=1754558943243:919:31)\n    at renderWithHooks (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:11596:26)\n    at mountIndeterminateComponent (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:14974:21)\n    at beginWork (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:15962:22)\n    at beginWork$1 (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19806:22)\n    at performUnitOfWork (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19251:20)\n    at workLoopSync (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19190:13)\n    at renderRootSync (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19169:15)\n    at recoverFromConcurrentError (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:18786:28)\n    at performSyncWorkOnRoot (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:18932:28)", "user_id": 2, "session_id": "session_1754555245533_z4c20g0mt", "resolved": false, "resolution_notes": null, "created_at": "2025-08-07 11:29:03.828210", "updated_at": "2025-08-07 11:29:03.828210"}, {"id": 1280, "timestamp": "2025-08-07 15:27:27.820306", "level": "CRITICAL", "source": "FRONTEND", "message": "React Component Error", "details": "{\"message\":\"The requested module '/node_modules/.vite/deps/react-icons_fi.js?v=29d1b7c2' does not provide an export named 'FiArrowRightLeft'\",\"stack\":\"SyntaxError: The requested module '/node_modules/.vite/deps/react-icons_fi.js?v=29d1b7c2' does not provide an export named 'FiArrowRightLeft'\",\"componentStack\":\"\\n    at Lazy\\n    at ProtectedRoute (http://192.168.1.110:5175/src/App.tsx?t=1754567038331:43:27)\\n    at RenderedRoute (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=29d1b7c2:4088:5)\\n    at Routes (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=29d1b7c2:4558:5)\\n    at Suspense\\n    at Router (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=29d1b7c2:4501:15)\\n    at BrowserRouter (http://192.168.1.110:5175/node_modules/.vite/...\",\"errorBoundary\":true}", "stack_trace": "SyntaxError: The requested module '/node_modules/.vite/deps/react-icons_fi.js?v=29d1b7c2' does not provide an export named 'FiArrowRightLeft'", "user_id": 2, "session_id": "session_1754573247493_xvit6oh2k", "resolved": false, "resolution_notes": null, "created_at": "2025-08-07 15:27:27.820306", "updated_at": "2025-08-07 15:27:27.820306"}, {"id": 1281, "timestamp": "2025-08-07 15:27:27.822722", "level": "CRITICAL", "source": "FRONTEND", "message": "React Component Error", "details": "{\"message\":\"The requested module '/node_modules/.vite/deps/react-icons_fi.js?v=29d1b7c2' does not provide an export named 'FiArrowRightLeft'\",\"stack\":\"SyntaxError: The requested module '/node_modules/.vite/deps/react-icons_fi.js?v=29d1b7c2' does not provide an export named 'FiArrowRightLeft'\",\"componentStack\":\"\\n    at Lazy\\n    at ProtectedRoute (http://192.168.1.110:5175/src/App.tsx?t=1754567038331:43:27)\\n    at RenderedRoute (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=29d1b7c2:4088:5)\\n    at Routes (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=29d1b7c2:4558:5)\\n    at Suspense\\n    at Router (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=29d1b7c2:4501:15)\\n    at BrowserRouter (http://192.168.1.110:5175/node_modules/.vite/...\",\"errorBoundary\":true}", "stack_trace": "SyntaxError: The requested module '/node_modules/.vite/deps/react-icons_fi.js?v=29d1b7c2' does not provide an export named 'FiArrowRightLeft'", "user_id": 2, "session_id": "session_1754569489382_6z2bzbrym", "resolved": false, "resolution_notes": null, "created_at": "2025-08-07 15:27:27.822722", "updated_at": "2025-08-07 15:27:27.822722"}, {"id": 1285, "timestamp": "2025-08-07 15:27:28.684025", "level": "CRITICAL", "source": "FRONTEND", "message": "React Component Error", "details": "{\"message\":\"The requested module '/node_modules/.vite/deps/react-icons_fi.js?v=29d1b7c2' does not provide an export named 'FiArrowRightLeft'\",\"stack\":\"SyntaxError: The requested module '/node_modules/.vite/deps/react-icons_fi.js?v=29d1b7c2' does not provide an export named 'FiArrowRightLeft'\",\"componentStack\":\"\\n    at Lazy\\n    at ProtectedRoute (http://192.168.1.110:5175/src/App.tsx?t=1754567038331:43:27)\\n    at RenderedRoute (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=29d1b7c2:4088:5)\\n    at Routes (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=29d1b7c2:4558:5)\\n    at Suspense\\n    at Router (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=29d1b7c2:4501:15)\\n    at BrowserRouter (http://192.168.1.110:5175/node_modules/.vite/...\",\"errorBoundary\":true}", "stack_trace": "SyntaxError: The requested module '/node_modules/.vite/deps/react-icons_fi.js?v=29d1b7c2' does not provide an export named 'FiArrowRightLeft'", "user_id": 2, "session_id": "session_1754573248492_95fcl12xj", "resolved": false, "resolution_notes": null, "created_at": "2025-08-07 15:27:28.684025", "updated_at": "2025-08-07 15:27:28.684025"}, {"id": 1289, "timestamp": "2025-08-07 15:28:03.037319", "level": "CRITICAL", "source": "FRONTEND", "message": "React Component Error", "details": "{\"message\":\"The requested module '/node_modules/.vite/deps/react-icons_fi.js?v=29d1b7c2' does not provide an export named 'FiArrowRightLeft'\",\"stack\":\"SyntaxError: The requested module '/node_modules/.vite/deps/react-icons_fi.js?v=29d1b7c2' does not provide an export named 'FiArrowRightLeft'\",\"componentStack\":\"\\n    at Lazy\\n    at ProtectedRoute (http://192.168.1.110:5175/src/App.tsx?t=1754567038331:43:27)\\n    at RenderedRoute (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=29d1b7c2:4088:5)\\n    at Routes (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=29d1b7c2:4558:5)\\n    at Suspense\\n    at Router (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=29d1b7c2:4501:15)\\n    at BrowserRouter (http://192.168.1.110:5175/node_modules/.vite/...\",\"errorBoundary\":true}", "stack_trace": "SyntaxError: The requested module '/node_modules/.vite/deps/react-icons_fi.js?v=29d1b7c2' does not provide an export named 'FiArrowRightLeft'", "user_id": 2, "session_id": "session_1754573282828_kmj82tw2y", "resolved": false, "resolution_notes": null, "created_at": "2025-08-07 15:28:03.037319", "updated_at": "2025-08-07 15:28:03.037319"}, {"id": 1293, "timestamp": "2025-08-07 16:00:31.889809", "level": "CRITICAL", "source": "FRONTEND", "message": "React Component Error", "details": "{\"message\":\"WarehouseAPITest is not defined\",\"stack\":\"ReferenceError: WarehouseAPITest is not defined\\n    at WarehousesPage (http://192.168.1.110:5175/src/pages/WarehousesPage.tsx?t=1754575229874:137:89)\\n    at renderWithHooks (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:11596:26)\\n    at updateFunctionComponent (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:14630:28)\\n    at mountLazyComponent (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:14881:23)\\n...\",\"componentStack\":\"\\n    at WarehousesPage (http://192.168.1.110:5175/src/pages/WarehousesPage.tsx?t=1754575229874:46:7)\\n    at RenderedRoute (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=29d1b7c2:4088:5)\\n    at Routes (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=29d1b7c2:4558:5)\\n    at Suspense\\n    at AppContent\\n    at div\\n    at div\\n    at main\\n    at div\\n    at div\\n    at PersistentLayout (http://192.168.1.110:5175/src/components/PersistentLayout.tsx?t=17545...\",\"errorBoundary\":true}", "stack_trace": "ReferenceError: WarehouseAPITest is not defined\n    at WarehousesPage (http://192.168.1.110:5175/src/pages/WarehousesPage.tsx?t=1754575229874:137:89)\n    at renderWithHooks (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:11596:26)\n    at updateFunctionComponent (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:14630:28)\n    at mountLazyComponent (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:14881:23)\n    at beginWork (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:15966:22)\n    at beginWork$1 (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19806:22)\n    at performUnitOfWork (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19251:20)\n    at workLoopSync (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19190:13)\n    at renderRootSync (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19169:15)\n    at recoverFromConcurrentError (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:18786:28)", "user_id": 2, "session_id": "session_1754575114478_lx68gs4s4", "resolved": false, "resolution_notes": null, "created_at": "2025-08-07 16:00:31.889809", "updated_at": "2025-08-07 16:00:31.889809"}, {"id": 1297, "timestamp": "2025-08-07 16:00:33.815498", "level": "CRITICAL", "source": "FRONTEND", "message": "React Component Error", "details": "{\"message\":\"WarehouseAPITest is not defined\",\"stack\":\"ReferenceError: WarehouseAPITest is not defined\\n    at WarehousesPage (http://192.168.1.110:5175/src/pages/WarehousesPage.tsx?t=1754575229874:137:89)\\n    at renderWithHooks (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:11596:26)\\n    at updateFunctionComponent (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:14630:28)\\n    at mountLazyComponent (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:14881:23)\\n...\",\"componentStack\":\"\\n    at WarehousesPage (http://192.168.1.110:5175/src/pages/WarehousesPage.tsx?t=1754575229874:46:7)\\n    at RenderedRoute (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=29d1b7c2:4088:5)\\n    at Routes (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=29d1b7c2:4558:5)\\n    at Suspense\\n    at AppContent\\n    at div\\n    at div\\n    at main\\n    at div\\n    at div\\n    at PersistentLayout (http://192.168.1.110:5175/src/components/PersistentLayout.tsx?t=17545...\",\"errorBoundary\":true}", "stack_trace": "ReferenceError: WarehouseAPITest is not defined\n    at WarehousesPage (http://192.168.1.110:5175/src/pages/WarehousesPage.tsx?t=1754575229874:137:89)\n    at renderWithHooks (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:11596:26)\n    at updateFunctionComponent (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:14630:28)\n    at mountLazyComponent (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:14881:23)\n    at beginWork (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:15966:22)\n    at beginWork$1 (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19806:22)\n    at performUnitOfWork (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19251:20)\n    at workLoopSync (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19190:13)\n    at renderRootSync (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19169:15)\n    at recoverFromConcurrentError (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:18786:28)", "user_id": 2, "session_id": "session_1754575233271_efz04tu0o", "resolved": false, "resolution_notes": null, "created_at": "2025-08-07 16:00:33.815498", "updated_at": "2025-08-07 16:00:33.815498"}, {"id": 1301, "timestamp": "2025-08-07 16:07:19.367788", "level": "CRITICAL", "source": "FRONTEND", "message": "React Component Error", "details": "{\"message\":\"AuthGuard is not defined\",\"stack\":\"ReferenceError: AuthGuard is not defined\\n    at WarehousesPage (http://192.168.1.110:5175/src/pages/WarehousesPage.tsx?t=1754575638942:94:33)\\n    at renderWithHooks (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:11596:26)\\n    at updateFunctionComponent (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:14630:28)\\n    at beginWork (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:15972:22)\\n    at beginWork$...\",\"componentStack\":\"\\n    at WarehousesPage (http://192.168.1.110:5175/src/pages/WarehousesPage.tsx?t=1754575638942:36:46)\\n    at RenderedRoute (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=29d1b7c2:4088:5)\\n    at Routes (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=29d1b7c2:4558:5)\\n    at Suspense\\n    at AppContent\\n    at div\\n    at div\\n    at main\\n    at div\\n    at div\\n    at PersistentLayout (http://192.168.1.110:5175/src/components/PersistentLayout.tsx?t=1754...\",\"errorBoundary\":true}", "stack_trace": "ReferenceError: AuthGuard is not defined\n    at WarehousesPage (http://192.168.1.110:5175/src/pages/WarehousesPage.tsx?t=1754575638942:94:33)\n    at renderWithHooks (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:11596:26)\n    at updateFunctionComponent (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:14630:28)\n    at beginWork (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:15972:22)\n    at beginWork$1 (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19806:22)\n    at performUnitOfWork (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19251:20)\n    at workLoopSync (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19190:13)\n    at renderRootSync (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19169:15)\n    at recoverFromConcurrentError (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:18786:28)\n    at performSyncWorkOnRoot (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:18932:28)", "user_id": 2, "session_id": "session_1754575600387_zq63183z2", "resolved": false, "resolution_notes": null, "created_at": "2025-08-07 16:07:19.367788", "updated_at": "2025-08-07 16:07:19.367788"}, {"id": 1305, "timestamp": "2025-08-07 18:14:24.891905", "level": "CRITICAL", "source": "FRONTEND", "message": "React Component Error", "details": "{\"message\":\"warehouseMovementStore.fetchTransferRequests is not a function\",\"stack\":\"TypeError: warehouseMovementStore.fetchTransferRequests is not a function\\n    at http://192.168.1.110:5175/src/components/warehouse/TransferRequestsTab.tsx:42:28\\n    at commitHookEffectListMount (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:16963:34)\\n    at commitPassiveMountOnFiber (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:18206:19)\\n    at commitPassiveMountEffects_complete (http://192.168.1.110:5175/node_modules/.vite/deps...\",\"componentStack\":\"\\n    at TransferRequestsTab (http://192.168.1.110:5175/src/components/warehouse/TransferRequestsTab.tsx:32:32)\\n    at div\\n    at WarehouseManagement (http://192.168.1.110:5175/src/pages/WarehouseManagement.tsx?t=1754577679119:41:20)\\n    at RenderedRoute (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=29d1b7c2:4088:5)\\n    at Routes (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=29d1b7c2:4558:5)\\n    at Suspense\\n    at AppContent\\n    at div\\n    at ...\",\"errorBoundary\":true}", "stack_trace": "TypeError: warehouseMovementStore.fetchTransferRequests is not a function\n    at http://192.168.1.110:5175/src/components/warehouse/TransferRequestsTab.tsx:42:28\n    at commitHookEffectListMount (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:16963:34)\n    at commitPassiveMountOnFiber (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:18206:19)\n    at commitPassiveMountEffects_complete (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:18179:17)\n    at commitPassiveMountEffects_begin (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:18169:15)\n    at commitPassiveMountEffects (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:18159:11)\n    at flushPassiveEffectsImpl (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19543:11)\n    at flushPassiveEffects (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19500:22)\n    at commitRootImpl (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19469:13)\n    at commitRoot (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19330:13)", "user_id": 2, "session_id": "session_1754575898986_o2f32h43k", "resolved": false, "resolution_notes": null, "created_at": "2025-08-07 18:14:24.891905", "updated_at": "2025-08-07 18:14:24.891905"}, {"id": 1310, "timestamp": "2025-08-10 12:50:06.360396", "level": "CRITICAL", "source": "FRONTEND", "message": "React Component Error", "details": "{\"message\":\"getNoDataMessage is not defined\",\"stack\":\"ReferenceError: getNoDataMessage is not defined\\n    at ComparisonIndicator (http://192.168.1.110:5175/src/components/ComparisonIndicator.tsx?t=1754823005964:107:120)\\n    at renderWithHooks (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:11596:26)\\n    at updateFunctionComponent (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:14630:28)\\n    at beginWork (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:159...\",\"componentStack\":\"\\n    at ComparisonIndicator (http://192.168.1.110:5175/src/components/ComparisonIndicator.tsx?t=1754823005964:19:3)\\n    at div\\n    at _c4 (http://192.168.1.110:5175/src/components/CompactNumberDisplay.tsx:219:3)\\n    at div\\n    at div\\n    at Dashboard (http://192.168.1.110:5175/src/pages/Dashboard.tsx:1036:7)\\n    at RenderedRoute (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=29d1b7c2:4088:5)\\n    at Routes (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-d...\",\"errorBoundary\":true}", "stack_trace": "ReferenceError: getNoDataMessage is not defined\n    at ComparisonIndicator (http://192.168.1.110:5175/src/components/ComparisonIndicator.tsx?t=1754823005964:107:120)\n    at renderWithHooks (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:11596:26)\n    at updateFunctionComponent (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:14630:28)\n    at beginWork (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:15972:22)\n    at beginWork$1 (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19806:22)\n    at performUnitOfWork (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19251:20)\n    at workLoopSync (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19190:13)\n    at renderRootSync (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19169:15)\n    at recoverFromConcurrentError (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:18786:28)\n    at performSyncWorkOnRoot (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:18932:28)", "user_id": 2, "session_id": "session_1754822180655_vkmbv56yk", "resolved": false, "resolution_notes": null, "created_at": "2025-08-10 12:50:06.360396", "updated_at": "2025-08-10 12:50:06.360396"}, {"id": 1314, "timestamp": "2025-08-10 18:38:51.669052", "level": "CRITICAL", "source": "FRONTEND", "message": "React Component Error", "details": "{\"message\":\"value.trim is not a function\",\"stack\":\"TypeError: value.trim is not a function\\n    at http://192.168.1.110:5175/src/components/inputs/NumberInput.tsx:126:35\\n    at renderWithHooks (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:11596:26)\\n    at updateForwardRef (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:14373:28)\\n    at beginWork (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:15994:22)\\n    at beginWork$1 (http://192.168.1.110:5175/no...\",\"componentStack\":\"\\n    at http://192.168.1.110:5175/src/components/inputs/NumberInput.tsx:22:3\\n    at div\\n    at form\\n    at div\\n    at div\\n    at div\\n    at div\\n    at Modal (http://192.168.1.110:5175/src/components/Modal.tsx:18:18)\\n    at EditWarehouseModal (http://192.168.1.110:5175/src/components/warehouse/EditWarehouseModal.tsx:25:3)\\n    at div\\n    at WarehousesTab (http://192.168.1.110:5175/src/components/warehouse/WarehousesTab.tsx?t=1754843841399:35:26)\\n    at div\\n    at WarehouseManagement (http://192.16...\",\"errorBoundary\":true}", "stack_trace": "TypeError: value.trim is not a function\n    at http://192.168.1.110:5175/src/components/inputs/NumberInput.tsx:126:35\n    at renderWithHooks (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:11596:26)\n    at updateForwardRef (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:14373:28)\n    at beginWork (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:15994:22)\n    at beginWork$1 (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19806:22)\n    at performUnitOfWork (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19251:20)\n    at workLoopSync (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19190:13)\n    at renderRootSync (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19169:15)\n    at recoverFromConcurrentError (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:18786:28)\n    at performConcurrentWorkOnRoot (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:18734:30)", "user_id": 2, "session_id": "session_1754822180655_vkmbv56yk", "resolved": false, "resolution_notes": null, "created_at": "2025-08-10 18:38:51.669052", "updated_at": "2025-08-10 18:38:51.669052"}, {"id": 1319, "timestamp": "2025-08-10 18:44:13.359923", "level": "CRITICAL", "source": "FRONTEND", "message": "React Component Error", "details": "{\"message\":\"formatDate is not defined\",\"stack\":\"ReferenceError: formatDate is not defined\\n    at WarehouseDetailsModal (http://192.168.1.110:5175/src/components/warehouse/WarehouseDetailsModal.tsx?t=1754844252974:396:112)\\n    at renderWithHooks (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:11596:26)\\n    at updateFunctionComponent (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:14630:28)\\n    at beginWork (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1...\",\"componentStack\":\"\\n    at WarehouseDetailsModal (http://192.168.1.110:5175/src/components/warehouse/WarehouseDetailsModal.tsx?t=1754844252974:30:3)\\n    at div\\n    at WarehousesTab (http://192.168.1.110:5175/src/components/warehouse/WarehousesTab.tsx?t=1754844119393:33:26)\\n    at div\\n    at WarehouseManagement (http://192.168.1.110:5175/src/pages/WarehouseManagement.tsx?t=1754843841399:43:20)\\n    at RenderedRoute (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=29d1b7c2:4088:5)\\n    at Route...\",\"errorBoundary\":true}", "stack_trace": "ReferenceError: formatDate is not defined\n    at WarehouseDetailsModal (http://192.168.1.110:5175/src/components/warehouse/WarehouseDetailsModal.tsx?t=1754844252974:396:112)\n    at renderWithHooks (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:11596:26)\n    at updateFunctionComponent (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:14630:28)\n    at beginWork (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:15972:22)\n    at beginWork$1 (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19806:22)\n    at performUnitOfWork (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19251:20)\n    at workLoopSync (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19190:13)\n    at renderRootSync (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:19169:15)\n    at recoverFromConcurrentError (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:18786:28)\n    at performSyncWorkOnRoot (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-YQ5BCTVV.js?v=29d1b7c2:18932:28)", "user_id": 2, "session_id": "session_1754843969789_qfdjar<PERSON>f", "resolved": false, "resolution_notes": null, "created_at": "2025-08-10 18:44:13.359923", "updated_at": "2025-08-10 18:44:13.359923"}, {"id": 1322, "timestamp": "2025-08-10 23:22:02.882869", "level": "INFO", "source": "BACKEND", "message": "Auto-resolve operation completed", "details": "Resolved: 38, Failed: 0, Total: 38", "stack_trace": null, "user_id": 2, "session_id": null, "resolved": false, "resolution_notes": null, "created_at": "2025-08-10 23:22:02.882869", "updated_at": "2025-08-10 23:22:02.882869"}], "user_info": {"username": "chiqwa", "email": "<EMAIL>", "role": "admin", "full_name": "نجيب الغدامسي"}, "email_result": {"success": true, "message": "تم إرسال 13 سجل إلى الدعم بنجاح عبر البريد الإلكتروني", "logs_count": 13, "support_email": "<EMAIL>", "sender_email": "<EMAIL>", "real_email": true}}