{"timestamp": "2025-07-11T02:39:05.345617", "logs": [{"id": 69, "timestamp": "2025-07-10 23:10:11.319335", "level": "CRITICAL", "source": "FRONTEND", "message": "React Component Error", "details": "{\"message\":\"Fa<PERSON>lock is not defined\",\"stack\":\"ReferenceError: FaClock is not defined\\n    at http://192.168.1.110:5175/src/pages/Debts.tsx?t=1752181810532:1525:40\\n    at Array.map (<anonymous>)\\n    at Debts (http://192.168.1.110:5175/src/pages/Debts.tsx?t=1752181810532:1320:153)\\n    at renderWithHooks (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-WRD5HZVH.js?v=5aa1d809:11548:26)\\n    at updateFunctionComponent (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-WRD5HZVH.js?v=5aa1d809:14582:28)\\n    at beginWork (http://192.168....\",\"componentStack\":\"\\n    at Debts (http://192.168.1.110:5175/src/pages/Debts.tsx?t=1752181810532:60:20)\\n    at main\\n    at div\\n    at Layout (http://192.168.1.110:5175/src/components/Layout.tsx:42:19)\\n    at ProtectedRoute (http://192.168.1.110:5175/src/App.tsx?t=1752178475754:62:27)\\n    at RenderedRoute (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=5aa1d809:4088:5)\\n    at Routes (http://192.168.1.110:5175/node_modules/.vite/deps/react-router-dom.js?v=5aa1d809:4558:5)\\n    at Suspense\\n    ...\",\"errorBoundary\":true}", "stack_trace": "ReferenceError: FaClock is not defined\n    at http://192.168.1.110:5175/src/pages/Debts.tsx?t=1752181810532:1525:40\n    at Array.map (<anonymous>)\n    at Debts (http://192.168.1.110:5175/src/pages/Debts.tsx?t=1752181810532:1320:153)\n    at renderWithHooks (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-WRD5HZVH.js?v=5aa1d809:11548:26)\n    at updateFunctionComponent (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-WRD5HZVH.js?v=5aa1d809:14582:28)\n    at beginWork (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-WRD5HZVH.js?v=5aa1d809:15924:22)\n    at beginWork$1 (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-WRD5HZVH.js?v=5aa1d809:19753:22)\n    at performUnitOfWork (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-WRD5HZVH.js?v=5aa1d809:19198:20)\n    at workLoopSync (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-WRD5HZVH.js?v=5aa1d809:19137:13)\n    at renderRootSync (http://192.168.1.110:5175/node_modules/.vite/deps/chunk-WRD5HZVH.js?v=5aa1d809:19116:15)", "user_id": 2, "session_id": "session_1752179243204_h5cr7gfzk", "resolved": false, "resolution_notes": null, "created_at": "2025-07-10 23:10:11.319335", "updated_at": "2025-07-10 23:10:11.319335"}], "user_info": {"username": "chiqwa", "email": "<EMAIL>", "role": "admin", "full_name": "نجيب الغدامسي"}, "email_result": {"success": true, "message": "تم إرسال 1 سجل إلى الدعم بنجاح عبر البريد الإلكتروني", "logs_count": 1, "support_email": "<EMAIL>", "sender_email": "<EMAIL>", "real_email": true}}