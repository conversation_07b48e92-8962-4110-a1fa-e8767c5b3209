"""
Migration script to add discount fields to sales table
"""
import sqlite3
import os

def migrate_database():
    """Add discount_amount and discount_type columns to sales table"""

    # Get the database path (in the root directory)
    db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'smartpos.db')

    if not os.path.exists(db_path):
        print(f"Database file not found at {db_path}")
        return False

    conn = None
    try:
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Check if columns already exist
        cursor.execute("PRAGMA table_info(sales)")
        columns = [column[1] for column in cursor.fetchall()]

        # Add discount_amount column if it doesn't exist
        if 'discount_amount' not in columns:
            cursor.execute("""
                ALTER TABLE sales
                ADD COLUMN discount_amount REAL NOT NULL DEFAULT 0.0
            """)
            print("Added discount_amount column to sales table")
        else:
            print("discount_amount column already exists")

        # Add discount_type column if it doesn't exist
        if 'discount_type' not in columns:
            cursor.execute("""
                ALTER TABLE sales
                ADD COLUMN discount_type VARCHAR(20) NOT NULL DEFAULT 'fixed'
            """)
            print("Added discount_type column to sales table")
        else:
            print("discount_type column already exists")

        # Commit the changes
        conn.commit()
        print("Migration completed successfully")
        return True

    except sqlite3.Error as e:
        print(f"Database error: {e}")
        return False
    except Exception as e:
        print(f"Error: {e}")
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    migrate_database()
