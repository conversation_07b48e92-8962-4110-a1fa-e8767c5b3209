"""
Migration: إضافة UUID للفروع
التاريخ: 2025-01-14
الوصف: إضافة حقل uuid إلى جدول branches وتوليد UUID لجميع الفروع الموجودة
"""

import uuid
import logging
from sqlalchemy import text
from database.session import engine

logger = logging.getLogger(__name__)

def add_uuid_to_branches():
    """
    إضافة حقل UUID إلى جدول الفروع وتوليد UUID لجميع الفروع الموجودة
    """
    try:
        logger.info("🔄 بدء migration: إضافة UUID للفروع")
        
        with engine.connect() as conn:
            # التحقق من وجود العمود uuid
            check_column_query = text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'branches' 
                AND column_name = 'uuid'
            """)
            
            result = conn.execute(check_column_query)
            column_exists = result.fetchone() is not None
            
            if not column_exists:
                logger.info("📝 إضافة عمود uuid إلى جدول branches")
                
                # إضافة عمود UUID
                add_column_query = text("""
                    ALTER TABLE branches 
                    ADD COLUMN uuid UUID UNIQUE
                """)
                conn.execute(add_column_query)
                
                # تحديث الفروع الموجودة بـ UUID جديد
                logger.info("🔄 توليد UUID لجميع الفروع الموجودة")
                
                # الحصول على جميع الفروع بدون UUID
                get_branches_query = text("""
                    SELECT id FROM branches WHERE uuid IS NULL
                """)
                
                branches_result = conn.execute(get_branches_query)
                branches = branches_result.fetchall()
                
                for branch in branches:
                    branch_uuid = str(uuid.uuid4())
                    update_query = text("""
                        UPDATE branches 
                        SET uuid = :uuid 
                        WHERE id = :branch_id
                    """)
                    
                    conn.execute(update_query, {
                        'uuid': branch_uuid,
                        'branch_id': branch.id
                    })
                    
                    logger.info(f"✅ تم تحديث الفرع {branch.id} بـ UUID: {branch_uuid}")
                
                # جعل العمود مطلوب بعد تحديث جميع الصفوف
                logger.info("📝 جعل عمود uuid مطلوب")
                make_not_null_query = text("""
                    ALTER TABLE branches 
                    ALTER COLUMN uuid SET NOT NULL
                """)
                conn.execute(make_not_null_query)
                
                # إضافة فهرس للعمود
                logger.info("📝 إضافة فهرس لعمود uuid")
                add_index_query = text("""
                    CREATE INDEX IF NOT EXISTS idx_branches_uuid 
                    ON branches(uuid)
                """)
                conn.execute(add_index_query)
                
                # تأكيد التغييرات
                conn.commit()
                
                logger.info("✅ تم إكمال migration بنجاح: إضافة UUID للفروع")
                
            else:
                logger.info("ℹ️ عمود uuid موجود بالفعل في جدول branches")
                
                # التحقق من وجود فروع بدون UUID وتحديثها
                check_null_uuid_query = text("""
                    SELECT id FROM branches WHERE uuid IS NULL
                """)
                
                null_uuid_result = conn.execute(check_null_uuid_query)
                branches_without_uuid = null_uuid_result.fetchall()
                
                if branches_without_uuid:
                    logger.info(f"🔄 توليد UUID لـ {len(branches_without_uuid)} فرع بدون UUID")
                    
                    for branch in branches_without_uuid:
                        branch_uuid = str(uuid.uuid4())
                        update_query = text("""
                            UPDATE branches 
                            SET uuid = :uuid 
                            WHERE id = :branch_id
                        """)
                        
                        conn.execute(update_query, {
                            'uuid': branch_uuid,
                            'branch_id': branch.id
                        })
                        
                        logger.info(f"✅ تم تحديث الفرع {branch.id} بـ UUID: {branch_uuid}")
                    
                    conn.commit()
                    logger.info("✅ تم تحديث جميع الفروع بـ UUID")
                else:
                    logger.info("ℹ️ جميع الفروع لديها UUID بالفعل")
            
            # التحقق من النتائج
            verify_query = text("""
                SELECT COUNT(*) as total_branches,
                       COUNT(uuid) as branches_with_uuid
                FROM branches
            """)
            
            verify_result = conn.execute(verify_query)
            stats = verify_result.fetchone()

            if stats:
                logger.info(f"📊 إحصائيات النهائية:")
                logger.info(f"   - إجمالي الفروع: {stats.total_branches}")
                logger.info(f"   - الفروع مع UUID: {stats.branches_with_uuid}")

                if stats.total_branches == stats.branches_with_uuid:
                    logger.info("✅ جميع الفروع لديها UUID بنجاح")
                    return True
                else:
                    logger.error("❌ بعض الفروع لا تحتوي على UUID")
                    return False
            else:
                logger.error("❌ لا يمكن الحصول على إحصائيات الفروع")
                return False
                
    except Exception as e:
        logger.error(f"❌ خطأ في migration: {e}")
        return False

def rollback_uuid_from_branches():
    """
    التراجع عن إضافة UUID (للطوارئ فقط)
    """
    try:
        logger.info("🔄 بدء rollback: إزالة UUID من الفروع")
        
        with engine.connect() as conn:
            # التحقق من وجود العمود
            check_column_query = text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'branches' 
                AND column_name = 'uuid'
            """)
            
            result = conn.execute(check_column_query)
            column_exists = result.fetchone() is not None
            
            if column_exists:
                # إزالة الفهرس أولاً
                drop_index_query = text("""
                    DROP INDEX IF EXISTS idx_branches_uuid
                """)
                conn.execute(drop_index_query)
                
                # إزالة العمود
                drop_column_query = text("""
                    ALTER TABLE branches 
                    DROP COLUMN IF EXISTS uuid
                """)
                conn.execute(drop_column_query)
                
                conn.commit()
                logger.info("✅ تم إزالة عمود uuid من جدول branches")
                return True
            else:
                logger.info("ℹ️ عمود uuid غير موجود في جدول branches")
                return True
                
    except Exception as e:
        logger.error(f"❌ خطأ في rollback: {e}")
        return False

if __name__ == "__main__":
    # تشغيل Migration
    success = add_uuid_to_branches()
    if success:
        print("✅ Migration completed successfully")
    else:
        print("❌ Migration failed")
