#!/usr/bin/env python3
"""
إضافة المفاتيح الخارجية المفقودة لجدول products
Add missing foreign keys to products table

هذا السكريبت يضيف الأعمدة التالية لجدول products:
- category_id (مرجع لجدول categories)
- subcategory_id (مرجع لجدول subcategories)  
- brand_id (مرجع لجدول brands)
- unit_id (مرجع لجدول units)

تاريخ الإنشاء: 23 يوليو 2025
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text, inspect
from database.session import engine, get_db
from models.product import Product
from models.category import Category, Subcategory
from models.brand import Brand
from models.unit import Unit

class ProductForeignKeyMigration:
    """خدمة ترحيل المفاتيح الخارجية لجدول المنتجات"""
    
    def __init__(self):
        self.engine = engine
        
    def check_column_exists(self, table_name: str, column_name: str) -> bool:
        """فحص وجود عمود في جدول"""
        try:
            inspector = inspect(self.engine)
            columns = inspector.get_columns(table_name)
            return any(col['name'] == column_name for col in columns)
        except Exception as e:
            print(f"❌ خطأ في فحص العمود {column_name}: {e}")
            return False
    
    def add_foreign_key_columns(self):
        """إضافة أعمدة المفاتيح الخارجية"""
        print("🔄 بدء إضافة أعمدة المفاتيح الخارجية...")
        
        # قائمة الأعمدة المطلوب إضافتها
        columns_to_add = [
            ('category_id', 'INTEGER'),
            ('subcategory_id', 'INTEGER'),
            ('brand_id', 'INTEGER'),
            ('unit_id', 'INTEGER')
        ]
        
        with self.engine.connect() as conn:
            for column_name, column_type in columns_to_add:
                if not self.check_column_exists('products', column_name):
                    try:
                        print(f"➕ إضافة عمود {column_name}...")
                        sql = f"ALTER TABLE products ADD COLUMN {column_name} {column_type}"
                        conn.execute(text(sql))
                        conn.commit()
                        print(f"✅ تم إضافة عمود {column_name} بنجاح")
                    except Exception as e:
                        print(f"❌ خطأ في إضافة عمود {column_name}: {e}")
                        conn.rollback()
                        raise
                else:
                    print(f"ℹ️ عمود {column_name} موجود بالفعل")
    
    def add_foreign_key_constraints(self):
        """إضافة قيود المفاتيح الخارجية"""
        print("🔄 بدء إضافة قيود المفاتيح الخارجية...")
        
        # قائمة القيود المطلوب إضافتها
        constraints = [
            ('fk_products_category', 'category_id', 'categories(id)'),
            ('fk_products_subcategory', 'subcategory_id', 'subcategories(id)'),
            ('fk_products_brand', 'brand_id', 'brands(id)'),
            ('fk_products_unit', 'unit_id', 'units(id)')
        ]
        
        with self.engine.connect() as conn:
            for constraint_name, column_name, reference in constraints:
                try:
                    # فحص وجود القيد
                    check_sql = f"""
                    SELECT constraint_name 
                    FROM information_schema.table_constraints 
                    WHERE table_name = 'products' 
                    AND constraint_name = '{constraint_name}'
                    """
                    result = conn.execute(text(check_sql)).fetchone()
                    
                    if not result:
                        print(f"➕ إضافة قيد {constraint_name}...")
                        sql = f"""
                        ALTER TABLE products 
                        ADD CONSTRAINT {constraint_name} 
                        FOREIGN KEY ({column_name}) 
                        REFERENCES {reference}
                        """
                        conn.execute(text(sql))
                        conn.commit()
                        print(f"✅ تم إضافة قيد {constraint_name} بنجاح")
                    else:
                        print(f"ℹ️ قيد {constraint_name} موجود بالفعل")
                        
                except Exception as e:
                    print(f"❌ خطأ في إضافة قيد {constraint_name}: {e}")
                    conn.rollback()
                    # لا نرفع الخطأ هنا لأن القيود اختيارية
    
    def verify_migration(self):
        """التحقق من نجاح الترحيل"""
        print("🔍 التحقق من نجاح الترحيل...")
        
        required_columns = ['category_id', 'subcategory_id', 'brand_id', 'unit_id']
        
        inspector = inspect(self.engine)
        columns = inspector.get_columns('products')
        existing_columns = [col['name'] for col in columns]
        
        missing_columns = [col for col in required_columns if col not in existing_columns]
        
        if missing_columns:
            print(f"❌ أعمدة مفقودة: {missing_columns}")
            return False
        else:
            print("✅ جميع الأعمدة المطلوبة موجودة")
            print(f"📊 إجمالي أعمدة جدول products: {len(existing_columns)}")
            return True
    
    def run_migration(self):
        """تشغيل الترحيل الكامل"""
        try:
            print("🚀 بدء ترحيل المفاتيح الخارجية لجدول products...")
            print("=" * 60)
            
            # إضافة الأعمدة
            self.add_foreign_key_columns()
            print()
            
            # إضافة القيود (اختياري)
            self.add_foreign_key_constraints()
            print()
            
            # التحقق من النتائج
            success = self.verify_migration()
            print()
            
            if success:
                print("🎉 تم الترحيل بنجاح!")
                print("✅ يمكن الآن استخدام المفاتيح الخارجية في جدول products")
            else:
                print("❌ فشل الترحيل!")
                return False
                
            return True
            
        except Exception as e:
            print(f"❌ خطأ عام في الترحيل: {e}")
            return False

def main():
    """الدالة الرئيسية"""
    print("🔧 سكريبت ترحيل المفاتيح الخارجية لجدول products")
    print("=" * 60)
    
    migration = ProductForeignKeyMigration()
    success = migration.run_migration()
    
    if success:
        print("\n🎯 الترحيل مكتمل بنجاح!")
        print("💡 يمكن الآن إعادة تشغيل الخادم لاختبار التحديثات")
    else:
        print("\n💥 فشل الترحيل!")
        sys.exit(1)

if __name__ == "__main__":
    main()
