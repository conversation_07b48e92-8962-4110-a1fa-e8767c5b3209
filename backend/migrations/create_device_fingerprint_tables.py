"""
Migration script لإنشاء جداول بصمات الأجهزة
"""

import sys
import os
from pathlib import Path

# إضافة مسار backend للـ Python path
backend_path = Path(__file__).parent.parent
sys.path.insert(0, str(backend_path))

from sqlalchemy import create_engine, text
from database.session import engine, get_db
from models.device_fingerprint import DeviceFingerprint, DeviceFingerprintHistory
from database.base import Base
import logging

logger = logging.getLogger(__name__)


def create_device_fingerprint_tables():
    """إنشاء جداول بصمات الأجهزة"""
    try:
        logger.info("بدء إنشاء جداول بصمات الأجهزة...")
        
        # إنشاء الجداول
        Base.metadata.create_all(bind=engine, tables=[
            DeviceFingerprint.__table__,
            DeviceFingerprintHistory.__table__
        ])
        
        logger.info("✅ تم إنشاء جداول بصمات الأجهزة بنجاح")
        
        # إنشاء فهارس إضافية للأداء
        create_additional_indexes()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء جداول بصمات الأجهزة: {e}")
        return False


def create_additional_indexes():
    """إنشاء فهارس إضافية للأداء"""
    try:
        with engine.connect() as conn:
            # فهارس لجدول device_fingerprints
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_device_fingerprints_composite_lookup ON device_fingerprints(hardware_fingerprint, storage_fingerprint, is_active)",
                "CREATE INDEX IF NOT EXISTS idx_device_fingerprints_last_seen_active ON device_fingerprints(last_seen_at, is_active)",
                "CREATE INDEX IF NOT EXISTS idx_device_fingerprints_ip_active ON device_fingerprints(last_ip, is_active)",
                "CREATE INDEX IF NOT EXISTS idx_device_fingerprints_auto_approved ON device_fingerprints(auto_approved, is_active)",
                
                # فهارس لجدول device_fingerprint_history
                "CREATE INDEX IF NOT EXISTS idx_device_fingerprint_history_composite ON device_fingerprint_history(fingerprint_id, event_type, created_at)",
                "CREATE INDEX IF NOT EXISTS idx_device_fingerprint_history_ip_event ON device_fingerprint_history(ip_address, event_type)",
                "CREATE INDEX IF NOT EXISTS idx_device_fingerprint_history_recent ON device_fingerprint_history(created_at DESC)",
            ]
            
            for index_sql in indexes:
                try:
                    conn.execute(text(index_sql))
                    logger.debug(f"تم إنشاء فهرس: {index_sql.split('ON')[1].split('(')[0].strip()}")
                except Exception as e:
                    logger.warning(f"فشل في إنشاء فهرس: {e}")
            
            conn.commit()
            logger.info("✅ تم إنشاء الفهارس الإضافية بنجاح")
            
    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء الفهارس الإضافية: {e}")


def verify_tables():
    """التحقق من وجود الجداول"""
    try:
        with engine.connect() as conn:
            # فحص جدول device_fingerprints
            result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='device_fingerprints'"))
            fingerprints_table_exists = result.fetchone() is not None
            
            # فحص جدول device_fingerprint_history
            result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='device_fingerprint_history'"))
            history_table_exists = result.fetchone() is not None
            
            if fingerprints_table_exists and history_table_exists:
                logger.info("✅ جميع جداول بصمات الأجهزة موجودة")
                
                # فحص عدد الأعمدة
                result = conn.execute(text("PRAGMA table_info(device_fingerprints)"))
                fingerprints_columns = result.fetchall()
                
                result = conn.execute(text("PRAGMA table_info(device_fingerprint_history)"))
                history_columns = result.fetchall()
                
                logger.info(f"جدول device_fingerprints: {len(fingerprints_columns)} عمود")
                logger.info(f"جدول device_fingerprint_history: {len(history_columns)} عمود")
                
                return True
            else:
                logger.error("❌ بعض جداول بصمات الأجهزة مفقودة")
                logger.error(f"device_fingerprints: {'موجود' if fingerprints_table_exists else 'مفقود'}")
                logger.error(f"device_fingerprint_history: {'موجود' if history_table_exists else 'مفقود'}")
                return False
                
    except Exception as e:
        logger.error(f"❌ خطأ في التحقق من الجداول: {e}")
        return False


def get_table_stats():
    """الحصول على إحصائيات الجداول"""
    try:
        with engine.connect() as conn:
            stats = {}
            
            # إحصائيات device_fingerprints
            try:
                result = conn.execute(text("SELECT COUNT(*) FROM device_fingerprints"))
                stats['total_fingerprints'] = result.scalar()
                
                result = conn.execute(text("SELECT COUNT(*) FROM device_fingerprints WHERE is_active = 1"))
                stats['active_fingerprints'] = result.scalar()
                
                result = conn.execute(text("SELECT COUNT(*) FROM device_fingerprints WHERE auto_approved = 1"))
                stats['auto_approved_fingerprints'] = result.scalar()
            except:
                stats['fingerprints_error'] = "جدول غير موجود"
            
            # إحصائيات device_fingerprint_history
            try:
                result = conn.execute(text("SELECT COUNT(*) FROM device_fingerprint_history"))
                stats['total_history_records'] = result.scalar()
                
                result = conn.execute(text("SELECT COUNT(DISTINCT fingerprint_id) FROM device_fingerprint_history"))
                stats['unique_fingerprints_in_history'] = result.scalar()
            except:
                stats['history_error'] = "جدول غير موجود"
            
            return stats
            
    except Exception as e:
        logger.error(f"خطأ في الحصول على إحصائيات الجداول: {e}")
        return {"error": str(e)}


def main():
    """الدالة الرئيسية للـ migration"""
    print("🚀 بدء migration لجداول بصمات الأجهزة...")
    
    # إعداد نظام السجلات
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        # إنشاء الجداول
        success = create_device_fingerprint_tables()
        if not success:
            print("❌ فشل في إنشاء الجداول")
            return False
        
        # التحقق من الجداول
        verification_success = verify_tables()
        if not verification_success:
            print("❌ فشل في التحقق من الجداول")
            return False
        
        # عرض الإحصائيات
        stats = get_table_stats()
        print("\n📊 إحصائيات الجداول:")
        for key, value in stats.items():
            print(f"  - {key}: {value}")
        
        print("\n✅ تم إكمال migration بنجاح!")
        print("\n🎯 الآن يمكن للنظام:")
        print("  - حفظ بصمات الأجهزة في قاعدة البيانات")
        print("  - التعرف على الأجهزة تلقائياً")
        print("  - تتبع تاريخ الوصول")
        print("  - إدارة الأجهزة المعتمدة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في migration: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
