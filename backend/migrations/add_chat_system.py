"""
إضافة نظام المحادثة الفورية
يضيف الجداول والأعمدة المطلوبة لنظام المحادثة
"""

import sqlite3
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

def run_migration():
    """تشغيل migration لإضافة نظام المحادثة"""
    
    # مسار قاعدة البيانات
    db_path = Path(__file__).parent.parent / "smartpos.db"
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        print("🔄 بدء migration لإضافة نظام المحادثة...")
        
        # 1. إضافة أعمدة المحادثة لجدول المستخدمين
        try:
            cursor.execute("ALTER TABLE users ADD COLUMN is_online BOOLEAN DEFAULT 0")
            print("✅ تم إضافة عمود is_online لجدول المستخدمين")
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e):
                print("⚠️ عمود is_online موجود بالفعل")
            else:
                raise
        
        try:
            cursor.execute("ALTER TABLE users ADD COLUMN last_seen DATETIME")
            print("✅ تم إضافة عمود last_seen لجدول المستخدمين")
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e):
                print("⚠️ عمود last_seen موجود بالفعل")
            else:
                raise
        
        # 2. إنشاء جدول رسائل المحادثة
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS chat_messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sender_id INTEGER NOT NULL,
                receiver_id INTEGER NOT NULL,
                content TEXT NOT NULL,
                message_type VARCHAR(20) DEFAULT 'text',
                status VARCHAR(20) DEFAULT 'sent',
                is_edited BOOLEAN DEFAULT 0,
                edited_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                delivered_at DATETIME,
                read_at DATETIME,
                FOREIGN KEY (sender_id) REFERENCES users (id),
                FOREIGN KEY (receiver_id) REFERENCES users (id)
            )
        """)
        print("✅ تم إنشاء جدول chat_messages")
        
        # 3. إنشاء جدول غرف المحادثة (للمستقبل)
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS chat_rooms (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                created_by INTEGER NOT NULL,
                is_private BOOLEAN DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                max_members INTEGER DEFAULT 50,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        """)
        print("✅ تم إنشاء جدول chat_rooms")
        
        # 4. إنشاء جدول أعضاء غرف المحادثة
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS chat_room_members (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                room_id INTEGER NOT NULL,
                user_id INTEGER NOT NULL,
                is_admin BOOLEAN DEFAULT 0,
                can_send_messages BOOLEAN DEFAULT 1,
                joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_read_at DATETIME,
                FOREIGN KEY (room_id) REFERENCES chat_rooms (id),
                FOREIGN KEY (user_id) REFERENCES users (id),
                UNIQUE(room_id, user_id)
            )
        """)
        print("✅ تم إنشاء جدول chat_room_members")
        
        # 5. إنشاء جدول حالة اتصال المستخدمين
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_online_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER UNIQUE NOT NULL,
                is_online BOOLEAN DEFAULT 0,
                last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
                socket_id VARCHAR(100),
                device_info TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        print("✅ تم إنشاء جدول user_online_status")
        
        # 6. إنشاء فهارس لتحسين الأداء
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_chat_messages_sender ON chat_messages(sender_id)",
            "CREATE INDEX IF NOT EXISTS idx_chat_messages_receiver ON chat_messages(receiver_id)",
            "CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON chat_messages(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_chat_messages_status ON chat_messages(status)",
            "CREATE INDEX IF NOT EXISTS idx_chat_messages_conversation ON chat_messages(sender_id, receiver_id, created_at)",
            "CREATE INDEX IF NOT EXISTS idx_user_online_status_user ON user_online_status(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_chat_room_members_room ON chat_room_members(room_id)",
            "CREATE INDEX IF NOT EXISTS idx_chat_room_members_user ON chat_room_members(user_id)",
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        print("✅ تم إنشاء الفهارس لتحسين الأداء")
        
        # 7. إنشاء triggers لتحديث updated_at تلقائياً
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS update_chat_rooms_updated_at
            AFTER UPDATE ON chat_rooms
            BEGIN
                UPDATE chat_rooms SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
            END
        """)
        
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS update_user_online_status_updated_at
            AFTER UPDATE ON user_online_status
            BEGIN
                UPDATE user_online_status SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
            END
        """)
        
        print("✅ تم إنشاء triggers للتحديث التلقائي")
        
        # حفظ التغييرات
        conn.commit()
        print("🎉 تم إكمال migration نظام المحادثة بنجاح!")
        
        return True
        
    except Exception as e:
        logger.error(f"خطأ في migration نظام المحادثة: {e}")
        print(f"❌ خطأ في migration: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
        
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    success = run_migration()
    if success:
        print("✅ Migration completed successfully!")
    else:
        print("❌ Migration failed!")
        exit(1)
