"""
إنشاء جداول أمان الأجهزة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from database.session import engine

def create_device_security_tables():
    """
    إنشاء جداول أمان الأجهزة
    """

    # جدول الأجهزة المحظورة
    blocked_devices_sql = """
    CREATE TABLE IF NOT EXISTS blocked_devices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        device_id VARCHAR NOT NULL UNIQUE,
        client_ip VARCHAR NOT NULL,
        hostname VARCHAR,
        device_type VARCHAR,
        system VARCHAR,
        platform VARCHAR,
        user_agent TEXT,
        blocked_by VARCHAR NOT NULL,
        blocked_at TIMESTAMP DEFAULT (datetime('now', '+3 hours')),
        block_reason VARCHAR DEFAULT 'محظور بواسطة المدير',
        is_permanent BOOLEAN DEFAULT 1,
        first_access TIMESTAMP,
        last_access TIMESTAMP,
        access_count INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT (datetime('now', '+3 hours')),
        updated_at TIMESTAMP DEFAULT (datetime('now', '+3 hours'))
    );
    """

    # جدول إعدادات أمان الأجهزة
    device_security_settings_sql = """
    CREATE TABLE IF NOT EXISTS device_security_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        setting_key VARCHAR NOT NULL UNIQUE,
        setting_value VARCHAR NOT NULL,
        description VARCHAR,
        created_at TIMESTAMP DEFAULT (datetime('now', '+3 hours')),
        updated_at TIMESTAMP DEFAULT (datetime('now', '+3 hours'))
    );
    """

    # جدول الأجهزة في انتظار الموافقة
    pending_devices_sql = """
    CREATE TABLE IF NOT EXISTS pending_devices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        device_id VARCHAR NOT NULL UNIQUE,
        client_ip VARCHAR NOT NULL,
        hostname VARCHAR,
        device_type VARCHAR,
        system VARCHAR,
        platform VARCHAR,
        user_agent TEXT,
        current_user VARCHAR,
        requested_at TIMESTAMP DEFAULT (datetime('now', '+3 hours')),
        first_access TIMESTAMP,
        last_access TIMESTAMP,
        access_count INTEGER DEFAULT 1,
        status VARCHAR DEFAULT 'pending',
        reviewed_by VARCHAR,
        reviewed_at TIMESTAMP,
        review_notes VARCHAR,
        created_at TIMESTAMP DEFAULT (datetime('now', '+3 hours')),
        updated_at TIMESTAMP DEFAULT (datetime('now', '+3 hours'))
    );
    """

    # إدراج الإعدادات الافتراضية
    default_settings_sql = """
    INSERT OR IGNORE INTO device_security_settings (setting_key, setting_value, description) VALUES
    ('require_approval', 'false', 'يتطلب موافقة للأجهزة الجديدة'),
    ('auto_block_suspicious', 'false', 'حظر تلقائي للأجهزة المشبوهة'),
    ('max_devices_per_ip', '5', 'الحد الأقصى للأجهزة لكل IP'),
    ('block_unknown_devices', 'false', 'حظر الأجهزة غير المعروفة'),
    ('notification_enabled', 'true', 'تفعيل الإشعارات');
    """

    try:
        with engine.connect() as connection:
            # إنشاء الجداول
            connection.execute(text(blocked_devices_sql))
            connection.execute(text(device_security_settings_sql))
            connection.execute(text(pending_devices_sql))

            # إدراج الإعدادات الافتراضية
            connection.execute(text(default_settings_sql))

            connection.commit()
            print("✅ تم إنشاء جداول أمان الأجهزة بنجاح")

    except Exception as e:
        print(f"❌ خطأ في إنشاء جداول أمان الأجهزة: {e}")
        raise

if __name__ == "__main__":
    create_device_security_tables()
