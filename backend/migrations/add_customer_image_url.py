"""
Migration: إضافة حقل image_url لجدول العملاء
التاريخ: 2025-07-28
الهدف: إضافة دعم الصور لملفات العملاء
"""

import logging
import sys
import os

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.dirname(current_dir)
sys.path.append(backend_dir)

from sqlalchemy import text
from database.session import engine

logger = logging.getLogger(__name__)

def add_customer_image_url():
    """إضافة حقل image_url لجدول العملاء"""
    try:
        logger.info("🔄 بدء إضافة حقل image_url لجدول العملاء...")
        
        with engine.connect() as conn:
            # التحقق من وجود العمود أولاً
            check_column_query = text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'customers' 
                AND column_name = 'image_url'
            """)
            
            result = conn.execute(check_column_query)
            column_exists = result.fetchone()
            
            if column_exists:
                logger.info("✅ حقل image_url موجود بالفعل في جدول العملاء")
                return True
            
            # إضافة العمود الجديد
            add_column_query = text("""
                ALTER TABLE customers 
                ADD COLUMN image_url VARCHAR(255) NULL
            """)
            
            conn.execute(add_column_query)
            conn.commit()
            
            logger.info("✅ تم إضافة حقل image_url لجدول العملاء بنجاح")
            return True
                
    except Exception as e:
        logger.error(f"❌ خطأ في إضافة حقل image_url: {e}")
        return False

def main():
    """تشغيل migration إضافة حقل image_url للعملاء"""
    try:
        logger.info("🚀 بدء migration إضافة حقل image_url للعملاء...")
        
        success = add_customer_image_url()
        
        if success:
            logger.info("🎉 تم إنجاز migration بنجاح!")
            print("✅ تم إضافة حقل image_url لجدول العملاء بنجاح")
        else:
            logger.error("❌ فشل في تنفيذ migration")
            print("❌ فشل في إضافة حقل image_url لجدول العملاء")
            
    except Exception as e:
        logger.error(f"❌ خطأ عام في migration: {e}")
        print(f"❌ خطأ في تنفيذ migration: {e}")

if __name__ == "__main__":
    # إعداد التسجيل
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    main()
