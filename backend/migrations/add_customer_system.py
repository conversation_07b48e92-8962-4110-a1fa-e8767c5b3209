"""
Migration script to add customer management system tables
"""

import sqlite3
from pathlib import Path

def run_migration():
    """Add customer management system tables to the database."""
    
    # Get database path
    db_path = Path(__file__).parent.parent.parent / "smartpos.db"
    
    # Connect to database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("Starting customer system migration...")
        
        # Create customers table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                phone VARCHAR(20),
                email VARCHAR(100),
                address TEXT,
                notes TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✓ Created customers table")
        
        # Create customer_debts table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS customer_debts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER NOT NULL,
                sale_id INTEGER,
                amount REAL NOT NULL,
                remaining_amount REAL NOT NULL,
                description TEXT,
                is_paid BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (sale_id) REFERENCES sales (id)
            )
        """)
        print("✓ Created customer_debts table")
        
        # Create debt_payments table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS debt_payments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                debt_id INTEGER NOT NULL,
                amount REAL NOT NULL,
                payment_method VARCHAR(20) DEFAULT 'cash',
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (debt_id) REFERENCES customer_debts (id)
            )
        """)
        print("✓ Created debt_payments table")
        
        # Add customer_id column to sales table if it doesn't exist
        try:
            cursor.execute("ALTER TABLE sales ADD COLUMN customer_id INTEGER")
            print("✓ Added customer_id column to sales table")
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e).lower():
                print("✓ customer_id column already exists in sales table")
            else:
                raise e
        
        # Create indexes for better performance
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_customer_debts_customer_id ON customer_debts(customer_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_customer_debts_is_paid ON customer_debts(is_paid)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_debt_payments_debt_id ON debt_payments(debt_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_sales_customer_id ON sales(customer_id)")
        print("✓ Created indexes")
        
        # Commit changes
        conn.commit()
        print("✅ Customer system migration completed successfully!")
        
    except Exception as e:
        conn.rollback()
        print(f"❌ Migration failed: {e}")
        raise e
    finally:
        conn.close()

if __name__ == "__main__":
    run_migration()
