"""
Migration script لإنشاء جداول الضرائب
يقوم بإنشاء جداول tax_types و tax_rates مع البيانات الافتراضية
"""

import sys
import os
from datetime import datetime, date
from decimal import Decimal

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from database.session import DATABASE_URL
from models.tax_type import TaxType
from models.tax_rate import TaxRate
from database.base import Base

def create_tax_tables():
    """إنشاء جداول الضرائب"""
    try:
        print("🔄 بدء إنشاء جداول الضرائب...")
        
        # إنشاء الاتصال بقاعدة البيانات
        engine = create_engine(DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        # إنشاء الجداول
        print("📋 إنشاء جداول tax_types و tax_rates...")
        Base.metadata.create_all(bind=engine, tables=[TaxType.__table__, TaxRate.__table__])
        
        print("✅ تم إنشاء جداول الضرائب بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جداول الضرائب: {str(e)}")
        return False

def insert_default_tax_data():
    """إدراج البيانات الافتراضية للضرائب"""
    try:
        print("🔄 بدء إدراج البيانات الافتراضية للضرائب...")
        
        # إنشاء الاتصال بقاعدة البيانات
        engine = create_engine(DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        session = SessionLocal()
        
        # التحقق من وجود بيانات مسبقة
        existing_count = session.query(TaxType).count()
        if existing_count > 0:
            print(f"⚠️ توجد بالفعل {existing_count} أنواع ضرائب في قاعدة البيانات")
            session.close()
            return True
        
        # إنشاء أنواع الضرائب الافتراضية
        tax_types_data = [
            {
                'name': 'VAT',
                'name_ar': 'ضريبة القيمة المضافة',
                'description': 'ضريبة القيمة المضافة المطبقة على معظم السلع والخدمات',
                'tax_category': 'standard',
                'calculation_method': 'percentage',
                'is_compound': False,
                'is_active': True,
                'sort_order': 1
            },
            {
                'name': 'Service Tax',
                'name_ar': 'ضريبة الخدمات',
                'description': 'ضريبة مطبقة على الخدمات المقدمة',
                'tax_category': 'standard',
                'calculation_method': 'percentage',
                'is_compound': False,
                'is_active': True,
                'sort_order': 2
            },
            {
                'name': 'Reduced VAT',
                'name_ar': 'ضريبة القيمة المضافة المخفضة',
                'description': 'ضريبة قيمة مضافة بنسبة مخفضة للسلع الأساسية',
                'tax_category': 'reduced',
                'calculation_method': 'percentage',
                'is_compound': False,
                'is_active': True,
                'sort_order': 3
            },
            {
                'name': 'Zero Rate',
                'name_ar': 'معدل صفر',
                'description': 'ضريبة بمعدل صفر للسلع المعفاة',
                'tax_category': 'zero',
                'calculation_method': 'percentage',
                'is_compound': False,
                'is_active': True,
                'sort_order': 4
            },
            {
                'name': 'Tax Exempt',
                'name_ar': 'معفى من الضريبة',
                'description': 'السلع والخدمات المعفاة من الضريبة',
                'tax_category': 'exempt',
                'calculation_method': 'percentage',
                'is_compound': False,
                'is_active': True,
                'sort_order': 5
            }
        ]
        
        created_tax_types = []
        for tax_type_data in tax_types_data:
            tax_type = TaxType(**tax_type_data)
            session.add(tax_type)
            session.flush()  # للحصول على ID
            created_tax_types.append(tax_type)
            print(f"✅ تم إنشاء نوع الضريبة: {tax_type.name_ar}")
        
        # إنشاء القيم الضريبية الافتراضية
        tax_rates_data = [
            # ضريبة القيمة المضافة - 15%
            {
                'tax_type_id': created_tax_types[0].id,
                'name': 'ضريبة القيمة المضافة 15%',
                'rate_value': Decimal('15.0000'),
                'description': 'المعدل الأساسي لضريبة القيمة المضافة',
                'is_default': True,
                'is_active': True,
                'applies_to': 'all',
                'sort_order': 1
            },
            # ضريبة الخدمات - 5%
            {
                'tax_type_id': created_tax_types[1].id,
                'name': 'ضريبة الخدمات 5%',
                'rate_value': Decimal('5.0000'),
                'description': 'ضريبة على الخدمات المقدمة',
                'is_default': True,
                'is_active': True,
                'applies_to': 'services',
                'sort_order': 1
            },
            # ضريبة القيمة المضافة المخفضة - 5%
            {
                'tax_type_id': created_tax_types[2].id,
                'name': 'ضريبة مخفضة 5%',
                'rate_value': Decimal('5.0000'),
                'description': 'ضريبة مخفضة للسلع الأساسية',
                'is_default': True,
                'is_active': True,
                'applies_to': 'products',
                'sort_order': 1
            },
            # معدل صفر - 0%
            {
                'tax_type_id': created_tax_types[3].id,
                'name': 'معدل صفر 0%',
                'rate_value': Decimal('0.0000'),
                'description': 'ضريبة بمعدل صفر',
                'is_default': True,
                'is_active': True,
                'applies_to': 'all',
                'sort_order': 1
            },
            # معفى من الضريبة - 0%
            {
                'tax_type_id': created_tax_types[4].id,
                'name': 'معفى من الضريبة',
                'rate_value': Decimal('0.0000'),
                'description': 'السلع والخدمات المعفاة من الضريبة',
                'is_default': True,
                'is_active': True,
                'applies_to': 'all',
                'sort_order': 1
            }
        ]
        
        for tax_rate_data in tax_rates_data:
            tax_rate = TaxRate(**tax_rate_data)
            session.add(tax_rate)
            print(f"✅ تم إنشاء القيمة الضريبية: {tax_rate.name}")
        
        # حفظ التغييرات
        session.commit()
        session.close()
        
        print(f"✅ تم إدراج {len(tax_types_data)} أنواع ضرائب و {len(tax_rates_data)} قيم ضريبية")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إدراج البيانات الافتراضية: {str(e)}")
        if 'session' in locals():
            session.rollback()
            session.close()
        return False

def main():
    """الدالة الرئيسية لتشغيل Migration"""
    print("🚀 بدء migration جداول الضرائب...")
    print("=" * 50)
    
    # إنشاء الجداول
    if not create_tax_tables():
        print("❌ فشل في إنشاء جداول الضرائب")
        return False
    
    # إدراج البيانات الافتراضية
    if not insert_default_tax_data():
        print("❌ فشل في إدراج البيانات الافتراضية")
        return False
    
    print("=" * 50)
    print("✅ تم إكمال migration جداول الضرائب بنجاح!")
    print("\n📊 ملخص العملية:")
    print("- تم إنشاء جدول tax_types")
    print("- تم إنشاء جدول tax_rates")
    print("- تم إدراج 5 أنواع ضرائب افتراضية")
    print("- تم إدراج 5 قيم ضريبية افتراضية")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
