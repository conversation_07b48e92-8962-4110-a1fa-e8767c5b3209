"""
إضافة حقول البصمة الشاملة إلى جدول device_fingerprint
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers
revision = 'add_comprehensive_fingerprint_fields'
down_revision = None  # يجب تحديد هذا بناءً على آخر migration
branch_labels = None
depends_on = None

def upgrade():
    """إضافة الحقول الجديدة للبصمة الشاملة"""
    
    # إضافة الحقول الجديدة إلى جدول device_fingerprint
    op.add_column('device_fingerprint', 
                  sa.Column('fingerprint_details', sa.Text(), nullable=True, 
                           comment='تفاصيل البصمة الكاملة (JSON)'))
    
    op.add_column('device_fingerprint', 
                  sa.Column('device_info', sa.Text(), nullable=True, 
                           comment='معلومات الجهاز والمتصفح (JSON)'))
    
    op.add_column('device_fingerprint', 
                  sa.Column('screen_info', sa.Text(), nullable=True, 
                           comment='تفاصيل الشاشة والدقة (JSON)'))
    
    op.add_column('device_fingerprint', 
                  sa.Column('system_info', sa.Text(), nullable=True, 
                           comment='تفاصيل نظام التشغيل (JSON)'))
    
    op.add_column('device_fingerprint', 
                  sa.Column('browser_info', sa.Text(), nullable=True, 
                           comment='تفاصيل المتصفح والإضافات (JSON)'))
    
    op.add_column('device_fingerprint', 
                  sa.Column('network_info', sa.Text(), nullable=True, 
                           comment='تفاصيل الشبكة والاتصال (JSON)'))
    
    # إضافة فهارس للأداء
    op.create_index('idx_fingerprint_device_info', 'device_fingerprint', ['device_info'], mysql_length={'device_info': 255})
    op.create_index('idx_fingerprint_browser_info', 'device_fingerprint', ['browser_info'], mysql_length={'browser_info': 255})

def downgrade():
    """إزالة الحقول المضافة"""
    
    # إزالة الفهارس
    op.drop_index('idx_fingerprint_browser_info', table_name='device_fingerprint')
    op.drop_index('idx_fingerprint_device_info', table_name='device_fingerprint')
    
    # إزالة الحقول
    op.drop_column('device_fingerprint', 'network_info')
    op.drop_column('device_fingerprint', 'browser_info')
    op.drop_column('device_fingerprint', 'system_info')
    op.drop_column('device_fingerprint', 'screen_info')
    op.drop_column('device_fingerprint', 'device_info')
    op.drop_column('device_fingerprint', 'fingerprint_details')
