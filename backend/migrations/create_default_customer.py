#!/usr/bin/env python3
"""
Migration script to create default customer "عميل مباشر"
"""

import os
import sys
import sqlite3
from datetime import datetime

def create_default_customer():
    """Create the default customer 'عميل مباشر' if it doesn't exist."""
    
    # Get database path - database is in the root directory
    db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'smartpos.db')
    
    print(f"Using database: {db_path}")
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return False
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if default customer already exists
        cursor.execute("SELECT id FROM customers WHERE name = ?", ('عميل مباشر',))
        existing_customer = cursor.fetchone()
        
        if existing_customer:
            print("✅ Default customer 'عميل مباشر' already exists")
            conn.close()
            return True
        
        # Create default customer
        current_time = datetime.now().isoformat()
        
        cursor.execute("""
            INSERT INTO customers (
                name, 
                phone, 
                email, 
                address, 
                is_active, 
                created_at, 
                updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            'عميل مباشر',
            None,
            None,
            'للمبيعات النقدية المباشرة',
            True,
            current_time,
            current_time
        ))
        
        # Commit changes
        conn.commit()
        
        # Get the created customer ID
        customer_id = cursor.lastrowid
        
        print(f"✅ Default customer 'عميل مباشر' created successfully with ID: {customer_id}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error creating default customer: {e}")
        return False

if __name__ == "__main__":
    print("Creating default customer...")
    success = create_default_customer()
    
    if success:
        print("✅ Migration completed successfully!")
        sys.exit(0)
    else:
        print("❌ Migration failed!")
        sys.exit(1)
