"""
إضافة حقل has_claims إلى جدول ضمانات المنتجات
سكريبت SQL لإضافة حقل تتبع المطالبات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from database.session import get_db


def add_has_claims_column():
    """إضافة حقل has_claims إلى جدول product_warranties"""

    db = next(get_db())

    try:
        print("🔄 بدء إضافة حقل has_claims...")

        # التحقق من وجود العمود
        check_column_query = text("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'product_warranties'
            AND column_name = 'has_claims'
        """)

        result = db.execute(check_column_query).fetchone()

        if result:
            print("✅ حقل has_claims موجود بالفعل")
            return

        # إضافة العمود الجديد
        add_column_query = text("""
            ALTER TABLE product_warranties
            ADD COLUMN has_claims BOOLEAN NOT NULL DEFAULT FALSE
        """)

        db.execute(add_column_query)
        print("✅ تم إضافة حقل has_claims")

        # تحديث القيم الموجودة بناءً على وجود مطالبات
        update_query = text("""
            UPDATE product_warranties
            SET has_claims = TRUE
            WHERE id IN (
                SELECT DISTINCT warranty_id
                FROM warranty_claims
                WHERE warranty_id IS NOT NULL
            )
        """)

        result = db.execute(update_query)
        db.commit()

        # التحقق من عدد الضمانات المحدثة
        count_query = text("""
            SELECT COUNT(*) FROM product_warranties WHERE has_claims = TRUE
        """)
        count_result = db.execute(count_query).scalar()

        print(f"✅ تم تحديث {count_result} ضمان بعلامة المطالبة")
        print("🎉 تم إضافة حقل has_claims بنجاح!")

    except Exception as e:
        db.rollback()
        print(f"❌ خطأ في إضافة حقل has_claims: {e}")
        raise
    finally:
        db.close()


if __name__ == "__main__":
    add_has_claims_column()
