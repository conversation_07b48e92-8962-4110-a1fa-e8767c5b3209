#!/usr/bin/env python3
"""
Migration script to create catalog tables (categories, brands, units)
and update products table with foreign key relationships.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from database.session import DATABASE_URL
from models.category import Category, Subcategory
from models.brand import Brand
from models.unit import Unit
from models.product import Product
from database.base import Base
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_migration():
    """Run the catalog tables migration."""
    try:
        # Use database URL from session
        engine = create_engine(DATABASE_URL)
        
        logger.info("🚀 بدء إنشاء جداول الفهرس...")
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        logger.info("✅ تم إنشاء جداول الفهرس بنجاح")
        
        # Create session
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        try:
            # Create default categories
            logger.info("📝 إنشاء الفئات الافتراضية...")
            default_categories = [
                {"name": "مواد غذائية", "description": "المواد الغذائية والمشروبات"},
                {"name": "منظفات", "description": "مواد التنظيف والعناية"},
                {"name": "أدوات منزلية", "description": "الأدوات والمعدات المنزلية"},
                {"name": "مستحضرات تجميل", "description": "مستحضرات التجميل والعناية الشخصية"},
                {"name": "قرطاسية", "description": "الأدوات المكتبية والقرطاسية"},
            ]
            
            for cat_data in default_categories:
                existing = db.query(Category).filter(Category.name == cat_data["name"]).first()
                if not existing:
                    category = Category(
                        name=cat_data["name"],
                        description=cat_data["description"],
                        created_by=1  # Assuming admin user ID is 1
                    )
                    db.add(category)
            
            # Create default units
            logger.info("📏 إنشاء الوحدات الافتراضية...")
            default_units = [
                {"name": "قطعة", "symbol": "pcs", "unit_type": "count", "description": "وحدة العد"},
                {"name": "كيلوغرام", "symbol": "kg", "unit_type": "weight", "description": "وحدة الوزن"},
                {"name": "غرام", "symbol": "g", "unit_type": "weight", "description": "وحدة الوزن الصغيرة"},
                {"name": "لتر", "symbol": "L", "unit_type": "volume", "description": "وحدة الحجم"},
                {"name": "مليلتر", "symbol": "ml", "unit_type": "volume", "description": "وحدة الحجم الصغيرة"},
                {"name": "متر", "symbol": "m", "unit_type": "length", "description": "وحدة الطول"},
                {"name": "سنتيمتر", "symbol": "cm", "unit_type": "length", "description": "وحدة الطول الصغيرة"},
                {"name": "علبة", "symbol": "box", "unit_type": "count", "description": "وحدة التعبئة"},
                {"name": "كرتونة", "symbol": "carton", "unit_type": "count", "description": "وحدة التعبئة الكبيرة"},
                {"name": "زجاجة", "symbol": "bottle", "unit_type": "count", "description": "وحدة التعبئة السائلة"},
            ]
            
            for unit_data in default_units:
                existing = db.query(Unit).filter(Unit.name == unit_data["name"]).first()
                if not existing:
                    unit = Unit(
                        name=unit_data["name"],
                        symbol=unit_data["symbol"],
                        unit_type=unit_data["unit_type"],
                        description=unit_data["description"],
                        created_by=1  # Assuming admin user ID is 1
                    )
                    db.add(unit)
            
            # Create default brands
            logger.info("🏷️ إنشاء العلامات التجارية الافتراضية...")
            default_brands = [
                {"name": "بدون علامة تجارية", "description": "منتجات بدون علامة تجارية محددة"},
                {"name": "محلي", "description": "منتجات محلية الصنع"},
                {"name": "مستورد", "description": "منتجات مستوردة"},
            ]
            
            for brand_data in default_brands:
                existing = db.query(Brand).filter(Brand.name == brand_data["name"]).first()
                if not existing:
                    brand = Brand(
                        name=brand_data["name"],
                        description=brand_data["description"],
                        created_by=1  # Assuming admin user ID is 1
                    )
                    db.add(brand)
            
            # Set up unit conversions
            logger.info("🔄 إعداد تحويلات الوحدات...")
            db.commit()  # Commit to get IDs
            
            # Get base units
            kg_unit = db.query(Unit).filter(Unit.name == "كيلوغرام").first()
            liter_unit = db.query(Unit).filter(Unit.name == "لتر").first()
            meter_unit = db.query(Unit).filter(Unit.name == "متر").first()
            
            # Set up conversions
            if kg_unit:
                gram_unit = db.query(Unit).filter(Unit.name == "غرام").first()
                if gram_unit:
                    gram_unit.base_unit_id = kg_unit.id
                    gram_unit.conversion_factor = 0.001
            
            if liter_unit:
                ml_unit = db.query(Unit).filter(Unit.name == "مليلتر").first()
                if ml_unit:
                    ml_unit.base_unit_id = liter_unit.id
                    ml_unit.conversion_factor = 0.001
            
            if meter_unit:
                cm_unit = db.query(Unit).filter(Unit.name == "سنتيمتر").first()
                if cm_unit:
                    cm_unit.base_unit_id = meter_unit.id
                    cm_unit.conversion_factor = 0.01
            
            db.commit()
            logger.info("✅ تم إنشاء البيانات الافتراضية بنجاح")
            
        except Exception as e:
            db.rollback()
            logger.error(f"❌ خطأ في إنشاء البيانات الافتراضية: {str(e)}")
            raise
        finally:
            db.close()
        
        logger.info("🎉 تم إكمال migration بنجاح!")
        
    except Exception as e:
        logger.error(f"❌ فشل في تشغيل migration: {str(e)}")
        raise

if __name__ == "__main__":
    run_migration()
