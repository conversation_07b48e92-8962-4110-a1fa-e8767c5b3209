#!/usr/bin/env python3
"""
Migration script لإنشاء جداول المستودعات
يدعم PostgreSQL مع معالجة شاملة للأخطاء
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
from sqlalchemy import text, inspect
from sqlalchemy.exc import SQLAlchemyError

from database.session import get_db, engine
from database.base import Base
from models.warehouse import (
    Warehouse, WarehouseInventory, WarehouseMovement,
    TransferRequest, TransferRequestItem
)

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def check_table_exists(table_name: str) -> bool:
    """التحقق من وجود الجدول"""
    try:
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        return table_name in tables
    except Exception as e:
        logger.error(f"خطأ في التحقق من وجود الجدول {table_name}: {e}")
        return False


def create_warehouse_tables():
    """إنشاء جداول المستودعات"""
    try:
        logger.info("🚀 بدء إنشاء جداول المستودعات...")
        
        # التحقق من الاتصال بقاعدة البيانات
        with engine.connect() as conn:
            _ = conn.execute(text("SELECT 1"))
            logger.info("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # إنشاء الجداول
        Base.metadata.create_all(bind=engine, tables=[
            Warehouse.__table__,
            WarehouseInventory.__table__,
            WarehouseMovement.__table__,
            TransferRequest.__table__,
            TransferRequestItem.__table__
        ])
        
        logger.info("✅ تم إنشاء جداول المستودعات بنجاح")
        return True
        
    except SQLAlchemyError as e:
        logger.error(f"❌ خطأ في قاعدة البيانات: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ خطأ عام في إنشاء الجداول: {e}")
        return False


def create_indexes():
    """إنشاء الفهارس المحسنة للأداء"""
    try:
        logger.info("📊 إنشاء الفهارس المحسنة...")
        
        indexes = [
            # فهارس جدول المستودعات
            "CREATE INDEX IF NOT EXISTS idx_warehouses_code ON warehouses(code);",
            "CREATE INDEX IF NOT EXISTS idx_warehouses_is_main ON warehouses(is_main);",
            "CREATE INDEX IF NOT EXISTS idx_warehouses_is_active ON warehouses(is_active);",
            
            # فهارس جدول مخزون المستودعات
            "CREATE INDEX IF NOT EXISTS idx_warehouse_inventory_warehouse_id ON warehouse_inventory(warehouse_id);",
            "CREATE INDEX IF NOT EXISTS idx_warehouse_inventory_product_id ON warehouse_inventory(product_id);",
            "CREATE UNIQUE INDEX IF NOT EXISTS idx_warehouse_inventory_unique ON warehouse_inventory(warehouse_id, product_id);",
            "CREATE INDEX IF NOT EXISTS idx_warehouse_inventory_quantity ON warehouse_inventory(quantity);",
            "CREATE INDEX IF NOT EXISTS idx_warehouse_inventory_low_stock ON warehouse_inventory(warehouse_id, product_id) WHERE quantity <= min_stock_level;",
            
            # فهارس جدول حركات المستودعات
            "CREATE INDEX IF NOT EXISTS idx_warehouse_movements_from_warehouse ON warehouse_movements(from_warehouse_id);",
            "CREATE INDEX IF NOT EXISTS idx_warehouse_movements_to_warehouse ON warehouse_movements(to_warehouse_id);",
            "CREATE INDEX IF NOT EXISTS idx_warehouse_movements_product_id ON warehouse_movements(product_id);",
            "CREATE INDEX IF NOT EXISTS idx_warehouse_movements_type ON warehouse_movements(movement_type);",
            "CREATE INDEX IF NOT EXISTS idx_warehouse_movements_created_at ON warehouse_movements(created_at);",
            "CREATE INDEX IF NOT EXISTS idx_warehouse_movements_reference ON warehouse_movements(reference_type, reference_id);",
            
            # فهارس جدول طلبات التحويل
            "CREATE INDEX IF NOT EXISTS idx_transfer_requests_number ON transfer_requests(request_number);",
            "CREATE INDEX IF NOT EXISTS idx_transfer_requests_from_warehouse ON transfer_requests(from_warehouse_id);",
            "CREATE INDEX IF NOT EXISTS idx_transfer_requests_to_warehouse ON transfer_requests(to_warehouse_id);",
            "CREATE INDEX IF NOT EXISTS idx_transfer_requests_status ON transfer_requests(status);",
            "CREATE INDEX IF NOT EXISTS idx_transfer_requests_requested_by ON transfer_requests(requested_by);",
            "CREATE INDEX IF NOT EXISTS idx_transfer_requests_requested_at ON transfer_requests(requested_at);",
            
            # فهارس جدول تفاصيل طلبات التحويل
            "CREATE INDEX IF NOT EXISTS idx_transfer_request_items_request_id ON transfer_request_items(transfer_request_id);",
            "CREATE INDEX IF NOT EXISTS idx_transfer_request_items_product_id ON transfer_request_items(product_id);",
        ]
        
        with engine.connect() as conn:
            for index_sql in indexes:
                try:
                    conn.execute(text(index_sql))
                    conn.commit()
                except Exception as e:
                    logger.warning(f"تحذير في إنشاء فهرس: {e}")
        
        logger.info("✅ تم إنشاء الفهارس بنجاح")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء الفهارس: {e}")
        return False


def create_default_warehouse():
    """إنشاء مستودع افتراضي"""
    try:
        logger.info("🏪 إنشاء المستودع الافتراضي...")
        
        db_gen = get_db()
        db = next(db_gen)
        
        try:
            # التحقق من وجود مستودع افتراضي
            existing_warehouse = db.query(Warehouse).filter(
                Warehouse.is_main == True
            ).first()
            
            if existing_warehouse:
                logger.info(f"✅ يوجد مستودع رئيسي بالفعل: {existing_warehouse.name}")
                return True
            
            # إنشاء المستودع الافتراضي
            default_warehouse = Warehouse(
                name="المستودع الرئيسي",
                code="MAIN-001",
                address="المقر الرئيسي",
                manager_name="مدير المستودع",
                is_main=True,
                is_active=True,
                capacity_limit=10000,
                current_capacity=0
            )
            
            db.add(default_warehouse)
            db.commit()
            db.refresh(default_warehouse)
            
            logger.info(f"✅ تم إنشاء المستودع الافتراضي: {default_warehouse.name}")
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء المستودع الافتراضي: {e}")
        return False


def verify_tables():
    """التحقق من إنشاء الجداول بنجاح"""
    try:
        logger.info("🔍 التحقق من الجداول...")
        
        required_tables = [
            'warehouses',
            'warehouse_inventory',
            'warehouse_movements',
            'transfer_requests',
            'transfer_request_items'
        ]
        
        missing_tables = []
        for table_name in required_tables:
            if not check_table_exists(table_name):
                missing_tables.append(table_name)
        
        if missing_tables:
            logger.error(f"❌ الجداول المفقودة: {missing_tables}")
            return False
        
        logger.info("✅ جميع الجداول موجودة")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في التحقق من الجداول: {e}")
        return False


def get_table_stats():
    """الحصول على إحصائيات الجداول"""
    try:
        stats = {}
        
        with engine.connect() as conn:
            # إحصائيات المستودعات
            result = conn.execute(text("SELECT COUNT(*) FROM warehouses"))
            stats['warehouses_count'] = result.scalar()
            
            # إحصائيات مخزون المستودعات
            result = conn.execute(text("SELECT COUNT(*) FROM warehouse_inventory"))
            stats['inventory_records'] = result.scalar()
            
            # إحصائيات حركات المستودعات
            result = conn.execute(text("SELECT COUNT(*) FROM warehouse_movements"))
            stats['movements_count'] = result.scalar()
            
            # إحصائيات طلبات التحويل
            result = conn.execute(text("SELECT COUNT(*) FROM transfer_requests"))
            stats['transfer_requests_count'] = result.scalar()
            
            # إحصائيات تفاصيل طلبات التحويل
            result = conn.execute(text("SELECT COUNT(*) FROM transfer_request_items"))
            stats['transfer_items_count'] = result.scalar()
        
        return stats
        
    except Exception as e:
        logger.error(f"خطأ في جلب الإحصائيات: {e}")
        return {}


def main():
    """الدالة الرئيسية"""
    try:
        logger.info("🚀 بدء migration جداول المستودعات...")
        
        # إنشاء الجداول
        if not create_warehouse_tables():
            logger.error("❌ فشل في إنشاء الجداول")
            return False
        
        # إنشاء الفهارس
        if not create_indexes():
            logger.error("❌ فشل في إنشاء الفهارس")
            return False
        
        # التحقق من الجداول
        if not verify_tables():
            logger.error("❌ فشل في التحقق من الجداول")
            return False
        
        # إنشاء المستودع الافتراضي
        if not create_default_warehouse():
            logger.error("❌ فشل في إنشاء المستودع الافتراضي")
            return False
        
        # عرض الإحصائيات
        stats = get_table_stats()
        logger.info("\n📊 إحصائيات الجداول:")
        for key, value in stats.items():
            logger.info(f"  - {key}: {value}")
        
        logger.info("\n✅ تم إكمال migration بنجاح!")
        logger.info("\n🎯 الآن يمكن للنظام:")
        logger.info("  - إدارة المستودعات المتعددة")
        logger.info("  - تتبع مخزون كل مستودع")
        logger.info("  - تسجيل حركات المخزون")
        logger.info("  - إدارة طلبات التحويل")
        logger.info("  - تحليل أداء المستودعات")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في migration: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
