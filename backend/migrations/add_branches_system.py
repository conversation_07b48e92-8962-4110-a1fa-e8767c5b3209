"""
Migration Script: إضافة نظام الفروع والعلاقات مع المستودعات
تاريخ الإنشاء: 2025-01-11
الوصف: إضافة جدول الفروع وجدول الربط Many-to-Many مع المستودعات
"""

import logging
import sys
import os

# إضافة مسار المشروع للـ Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text, inspect
from database.session import engine, SessionLocal

logger = logging.getLogger(__name__)


class BranchSystemMigration:
    """
    فئة Migration لإضافة نظام الفروع
    تطبق مبادئ OOP وتضمن سلامة البيانات
    """
    
    def __init__(self):
        self.db_session = SessionLocal()
        self.inspector = inspect(engine)
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):  # type: ignore
        if exc_type:
            self.db_session.rollback()
        self.db_session.close()
    
    def check_table_exists(self, table_name: str) -> bool:
        """التحقق من وجود الجدول"""
        try:
            tables = self.inspector.get_table_names()
            return table_name in tables
        except Exception as e:
            logger.error(f"خطأ في فحص وجود الجدول {table_name}: {e}")
            return False
    
    def create_branches_table(self) -> bool:
        """إنشاء جدول الفروع"""
        try:
            if self.check_table_exists('branches'):
                logger.info("✅ جدول الفروع موجود مسبقاً")
                return True
            
            logger.info("🏗️ إنشاء جدول الفروع...")
            
            create_table_sql = text("""
                CREATE TABLE branches (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    code VARCHAR(20) UNIQUE NOT NULL,
                    address TEXT,
                    phone VARCHAR(20),
                    manager_name VARCHAR(100),
                    email VARCHAR(100),
                    is_active BOOLEAN NOT NULL DEFAULT true,
                    is_main BOOLEAN NOT NULL DEFAULT false,
                    city VARCHAR(50),
                    region VARCHAR(50),
                    postal_code VARCHAR(20),
                    max_daily_sales INTEGER,
                    working_hours_start VARCHAR(10),
                    working_hours_end VARCHAR(10),
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE,
                    created_by INTEGER REFERENCES users(id),
                    updated_by INTEGER REFERENCES users(id)
                );
                
                -- إنشاء الفهارس
                CREATE INDEX idx_branches_code ON branches(code);
                CREATE INDEX idx_branches_name ON branches(name);
                CREATE INDEX idx_branches_is_active ON branches(is_active);
                CREATE INDEX idx_branches_is_main ON branches(is_main);
                CREATE INDEX idx_branches_city ON branches(city);
                CREATE INDEX idx_branches_region ON branches(region);
                
                -- إضافة تعليقات
                COMMENT ON TABLE branches IS 'جدول الفروع - يحتوي على معلومات فروع الشركة';
                COMMENT ON COLUMN branches.name IS 'اسم الفرع';
                COMMENT ON COLUMN branches.code IS 'كود الفرع الفريد';
                COMMENT ON COLUMN branches.address IS 'عنوان الفرع';
                COMMENT ON COLUMN branches.phone IS 'رقم هاتف الفرع';
                COMMENT ON COLUMN branches.manager_name IS 'اسم مدير الفرع';
                COMMENT ON COLUMN branches.email IS 'البريد الإلكتروني للفرع';
                COMMENT ON COLUMN branches.is_active IS 'حالة نشاط الفرع';
                COMMENT ON COLUMN branches.is_main IS 'هل هذا الفرع الرئيسي';
                COMMENT ON COLUMN branches.city IS 'المدينة';
                COMMENT ON COLUMN branches.region IS 'المنطقة';
                COMMENT ON COLUMN branches.postal_code IS 'الرمز البريدي';
                COMMENT ON COLUMN branches.max_daily_sales IS 'الحد الأقصى للمبيعات اليومية';
                COMMENT ON COLUMN branches.working_hours_start IS 'بداية ساعات العمل';
                COMMENT ON COLUMN branches.working_hours_end IS 'نهاية ساعات العمل';
            """)
            
            self.db_session.execute(create_table_sql)
            self.db_session.commit()
            
            logger.info("✅ تم إنشاء جدول الفروع بنجاح")
            return True
            
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"❌ خطأ في إنشاء جدول الفروع: {e}")
            return False
    
    def create_branch_warehouses_table(self) -> bool:
        """إنشاء جدول الربط بين الفروع والمستودعات"""
        try:
            if self.check_table_exists('branch_warehouses'):
                logger.info("✅ جدول الربط branch_warehouses موجود مسبقاً")
                return True
            
            logger.info("🏗️ إنشاء جدول الربط branch_warehouses...")
            
            create_table_sql = text("""
                CREATE TABLE branch_warehouses (
                    branch_id INTEGER NOT NULL REFERENCES branches(id) ON DELETE CASCADE,
                    warehouse_id INTEGER NOT NULL REFERENCES warehouses(id) ON DELETE CASCADE,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    is_primary BOOLEAN DEFAULT false,
                    priority INTEGER DEFAULT 1,
                    PRIMARY KEY (branch_id, warehouse_id)
                );
                
                -- إنشاء الفهارس
                CREATE INDEX idx_branch_warehouses_branch_id ON branch_warehouses(branch_id);
                CREATE INDEX idx_branch_warehouses_warehouse_id ON branch_warehouses(warehouse_id);
                CREATE INDEX idx_branch_warehouses_is_primary ON branch_warehouses(is_primary);
                CREATE INDEX idx_branch_warehouses_priority ON branch_warehouses(priority);
                
                -- إضافة تعليقات
                COMMENT ON TABLE branch_warehouses IS 'جدول الربط Many-to-Many بين الفروع والمستودعات';
                COMMENT ON COLUMN branch_warehouses.branch_id IS 'معرف الفرع';
                COMMENT ON COLUMN branch_warehouses.warehouse_id IS 'معرف المستودع';
                COMMENT ON COLUMN branch_warehouses.is_primary IS 'هل هذا المستودع الأساسي للفرع';
                COMMENT ON COLUMN branch_warehouses.priority IS 'أولوية المستودع للفرع (1 = أعلى أولوية)';
                COMMENT ON COLUMN branch_warehouses.created_at IS 'تاريخ إنشاء الربط';
            """)
            
            self.db_session.execute(create_table_sql)
            self.db_session.commit()
            
            logger.info("✅ تم إنشاء جدول الربط branch_warehouses بنجاح")
            return True
            
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"❌ خطأ في إنشاء جدول الربط: {e}")
            return False
    
    def create_default_branch(self) -> bool:
        """إنشاء فرع افتراضي رئيسي"""
        try:
            logger.info("🏢 إنشاء الفرع الافتراضي...")
            
            # التحقق من عدم وجود فروع
            count_result = self.db_session.execute(text("SELECT COUNT(*) FROM branches")).scalar()
            
            if count_result and count_result > 0:
                logger.info("✅ يوجد فروع مسبقاً، لن يتم إنشاء فرع افتراضي")
                return True
            
            # إنشاء الفرع الافتراضي
            insert_sql = text("""
                INSERT INTO branches (
                    name, code, address, is_active, is_main, 
                    city, region, created_at, updated_at
                ) VALUES (
                    'الفرع الرئيسي', 'MAIN', 'العنوان الرئيسي', 
                    true, true, 'طرابلس', 'طرابلس', 
                    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
                )
            """)
            
            self.db_session.execute(insert_sql)
            self.db_session.commit()
            
            logger.info("✅ تم إنشاء الفرع الافتراضي بنجاح")
            return True
            
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"❌ خطأ في إنشاء الفرع الافتراضي: {e}")
            return False
    
    def link_existing_warehouses_to_main_branch(self) -> bool:
        """ربط المستودعات الموجودة بالفرع الرئيسي"""
        try:
            logger.info("🔗 ربط المستودعات الموجودة بالفرع الرئيسي...")
            
            # الحصول على معرف الفرع الرئيسي
            main_branch_result = self.db_session.execute(
                text("SELECT id FROM branches WHERE is_main = true LIMIT 1")
            ).first()
            
            if not main_branch_result:
                logger.warning("⚠️ لا يوجد فرع رئيسي، سيتم تخطي ربط المستودعات")
                return True
            
            main_branch_id = main_branch_result[0]
            
            # الحصول على جميع المستودعات النشطة
            warehouses_result = self.db_session.execute(
                text("SELECT id FROM warehouses WHERE is_active = true")
            ).fetchall()
            
            if not warehouses_result:
                logger.info("ℹ️ لا توجد مستودعات نشطة للربط")
                return True
            
            # ربط كل مستودع بالفرع الرئيسي
            for warehouse_row in warehouses_result:
                warehouse_id = warehouse_row[0]
                
                # التحقق من عدم وجود الربط مسبقاً
                existing_link = self.db_session.execute(
                    text("SELECT 1 FROM branch_warehouses WHERE branch_id = :branch_id AND warehouse_id = :warehouse_id"),
                    {"branch_id": main_branch_id, "warehouse_id": warehouse_id}
                ).first()
                
                if not existing_link:
                    # إنشاء الربط
                    self.db_session.execute(
                        text("""
                            INSERT INTO branch_warehouses (branch_id, warehouse_id, is_primary, priority, created_at)
                            VALUES (:branch_id, :warehouse_id, true, 1, CURRENT_TIMESTAMP)
                        """),
                        {"branch_id": main_branch_id, "warehouse_id": warehouse_id}
                    )
                    
                    logger.info(f"✅ تم ربط المستودع {warehouse_id} بالفرع الرئيسي")
            
            self.db_session.commit()
            logger.info("✅ تم ربط جميع المستودعات بالفرع الرئيسي بنجاح")
            return True
            
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"❌ خطأ في ربط المستودعات بالفرع الرئيسي: {e}")
            return False
    
    def run_migration(self) -> bool:
        """تشغيل Migration كاملة"""
        try:
            logger.info("🚀 بدء تشغيل Migration نظام الفروع...")
            
            # 1. إنشاء جدول الفروع
            if not self.create_branches_table():
                return False
            
            # 2. إنشاء جدول الربط
            if not self.create_branch_warehouses_table():
                return False
            
            # 3. إنشاء الفرع الافتراضي
            if not self.create_default_branch():
                return False
            
            # 4. ربط المستودعات الموجودة بالفرع الرئيسي
            if not self.link_existing_warehouses_to_main_branch():
                return False
            
            logger.info("🎉 تم تشغيل Migration نظام الفروع بنجاح!")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ عام في Migration: {e}")
            return False


def run_branches_migration():
    """تشغيل Migration نظام الفروع"""
    try:
        with BranchSystemMigration() as migration:
            success = migration.run_migration()
            
            if success:
                print("✅ تم تشغيل Migration نظام الفروع بنجاح!")
                return True
            else:
                print("❌ فشل في تشغيل Migration نظام الفروع!")
                return False
                
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل Migration: {e}")
        print(f"❌ خطأ في تشغيل Migration: {e}")
        return False


if __name__ == "__main__":
    # تشغيل Migration
    success = run_branches_migration()
    exit(0 if success else 1)
