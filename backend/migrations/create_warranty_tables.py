"""
إنشاء جداول نظام إدارة الضمانات
يتوافق مع PostgreSQL ويطبق أفضل الممارسات
"""

import logging
import sys
import os

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from database.session import engine

logger = logging.getLogger(__name__)


def create_warranty_tables():
    """
    إنشاء جداول نظام إدارة الضمانات في PostgreSQL
    """
    try:
        logger.info("🔄 بدء إنشاء جداول نظام إدارة الضمانات...")

        with engine.connect() as conn:
            # إنشاء جدول أنواع الضمانات
            warranty_types_sql = """
            CREATE TABLE IF NOT EXISTS warranty_types (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                name_ar VARCHAR(100) NOT NULL,
                description TEXT,
                duration_months INTEGER NOT NULL CHECK (duration_months > 0),
                coverage_type VARCHAR(20) NOT NULL DEFAULT 'full' CHECK (coverage_type IN ('full', 'partial', 'limited')),
                terms_conditions TEXT,
                is_active BOOLEAN NOT NULL DEFAULT true,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE,
                created_by INTEGER NOT NULL REFERENCES users(id),
                updated_by INTEGER REFERENCES users(id),
                
                -- فهارس للأداء
                CONSTRAINT warranty_types_name_unique UNIQUE (name),
                CONSTRAINT warranty_types_name_ar_unique UNIQUE (name_ar)
            );
            
            -- إنشاء فهارس للبحث السريع
            CREATE INDEX IF NOT EXISTS idx_warranty_types_name ON warranty_types(name);
            CREATE INDEX IF NOT EXISTS idx_warranty_types_name_ar ON warranty_types(name_ar);
            CREATE INDEX IF NOT EXISTS idx_warranty_types_is_active ON warranty_types(is_active);
            CREATE INDEX IF NOT EXISTS idx_warranty_types_coverage_type ON warranty_types(coverage_type);
            """

            # إنشاء جدول ضمانات المنتجات
            product_warranties_sql = """
            CREATE TABLE IF NOT EXISTS product_warranties (
                id SERIAL PRIMARY KEY,
                product_id INTEGER NOT NULL REFERENCES products(id),
                warranty_type_id INTEGER NOT NULL REFERENCES warranty_types(id),
                warranty_number VARCHAR(50) NOT NULL UNIQUE,
                purchase_date DATE NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                customer_id INTEGER REFERENCES customers(id),
                status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'expired', 'voided', 'claimed')),
                notes TEXT,
                
                -- معلومات الإلغاء
                void_reason TEXT,
                voided_at TIMESTAMP WITH TIME ZONE,
                voided_by INTEGER REFERENCES users(id),
                
                -- معلومات التمديد
                extended_months INTEGER DEFAULT 0,
                extension_reason TEXT,
                extended_at TIMESTAMP WITH TIME ZONE,
                extended_by INTEGER REFERENCES users(id),
                
                -- معلومات التتبع
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE,
                created_by INTEGER NOT NULL REFERENCES users(id),
                updated_by INTEGER REFERENCES users(id),
                
                -- قيود التحقق
                CONSTRAINT product_warranties_dates_check CHECK (start_date <= end_date),
                CONSTRAINT product_warranties_extended_months_check CHECK (extended_months >= 0)
            );
            
            -- إنشاء فهارس للأداء
            CREATE INDEX IF NOT EXISTS idx_product_warranties_warranty_number ON product_warranties(warranty_number);
            CREATE INDEX IF NOT EXISTS idx_product_warranties_product_id ON product_warranties(product_id);
            CREATE INDEX IF NOT EXISTS idx_product_warranties_warranty_type_id ON product_warranties(warranty_type_id);
            CREATE INDEX IF NOT EXISTS idx_product_warranties_customer_id ON product_warranties(customer_id);
            CREATE INDEX IF NOT EXISTS idx_product_warranties_status ON product_warranties(status);
            CREATE INDEX IF NOT EXISTS idx_product_warranties_end_date ON product_warranties(end_date);
            CREATE INDEX IF NOT EXISTS idx_product_warranties_created_at ON product_warranties(created_at);
            """

            # إنشاء جدول مطالبات الضمان
            warranty_claims_sql = """
            CREATE TABLE IF NOT EXISTS warranty_claims (
                id SERIAL PRIMARY KEY,
                warranty_id INTEGER NOT NULL REFERENCES product_warranties(id),
                claim_number VARCHAR(50) NOT NULL UNIQUE,
                claim_type VARCHAR(20) NOT NULL CHECK (claim_type IN ('repair', 'replacement', 'refund')),
                issue_description TEXT NOT NULL,
                claim_description TEXT,
                claim_date DATE NOT NULL DEFAULT CURRENT_DATE,
                expected_resolution_date DATE,
                status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'in_progress', 'completed')),
                priority VARCHAR(10) NOT NULL DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
                
                -- معلومات القرار والحل
                resolution TEXT,
                resolution_date DATE,
                resolved_by INTEGER REFERENCES users(id),
                
                -- التكلفة المالية
                estimated_cost DECIMAL(10, 2) CHECK (estimated_cost >= 0),
                actual_cost DECIMAL(10, 2) CHECK (actual_cost >= 0),
                cost_covered_by_warranty BOOLEAN DEFAULT true,
                
                -- معلومات إضافية
                notes TEXT,
                internal_notes TEXT,
                
                -- معلومات المراجعة والموافقة
                reviewed_by INTEGER REFERENCES users(id),
                reviewed_at TIMESTAMP WITH TIME ZONE,
                approved_by INTEGER REFERENCES users(id),
                approved_at TIMESTAMP WITH TIME ZONE,
                
                -- معلومات الرفض
                rejection_reason TEXT,
                rejected_by INTEGER REFERENCES users(id),
                rejected_at TIMESTAMP WITH TIME ZONE,
                
                -- معلومات التتبع
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE,
                created_by INTEGER NOT NULL REFERENCES users(id),
                updated_by INTEGER REFERENCES users(id),
                
                -- قيود التحقق
                CONSTRAINT warranty_claims_resolution_date_check CHECK (
                    (status = 'completed' AND resolution_date IS NOT NULL) OR 
                    (status != 'completed')
                )
            );
            
            -- إنشاء فهارس للأداء
            CREATE INDEX IF NOT EXISTS idx_warranty_claims_claim_number ON warranty_claims(claim_number);
            CREATE INDEX IF NOT EXISTS idx_warranty_claims_warranty_id ON warranty_claims(warranty_id);
            CREATE INDEX IF NOT EXISTS idx_warranty_claims_status ON warranty_claims(status);
            CREATE INDEX IF NOT EXISTS idx_warranty_claims_claim_type ON warranty_claims(claim_type);
            CREATE INDEX IF NOT EXISTS idx_warranty_claims_priority ON warranty_claims(priority);
            CREATE INDEX IF NOT EXISTS idx_warranty_claims_claim_date ON warranty_claims(claim_date);
            CREATE INDEX IF NOT EXISTS idx_warranty_claims_created_at ON warranty_claims(created_at);
            """

            # تنفيذ الاستعلامات
            logger.info("📋 إنشاء جدول أنواع الضمانات...")
            conn.execute(text(warranty_types_sql))
            
            logger.info("📋 إنشاء جدول ضمانات المنتجات...")
            conn.execute(text(product_warranties_sql))
            
            logger.info("📋 إنشاء جدول مطالبات الضمان...")
            conn.execute(text(warranty_claims_sql))

            # إنشاء trigger لتحديث updated_at تلقائياً
            trigger_sql = """
            -- دالة تحديث updated_at
            CREATE OR REPLACE FUNCTION update_updated_at_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = NOW();
                RETURN NEW;
            END;
            $$ language 'plpgsql';

            -- إنشاء triggers للجداول
            DROP TRIGGER IF EXISTS update_warranty_types_updated_at ON warranty_types;
            CREATE TRIGGER update_warranty_types_updated_at
                BEFORE UPDATE ON warranty_types
                FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

            DROP TRIGGER IF EXISTS update_product_warranties_updated_at ON product_warranties;
            CREATE TRIGGER update_product_warranties_updated_at
                BEFORE UPDATE ON product_warranties
                FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

            DROP TRIGGER IF EXISTS update_warranty_claims_updated_at ON warranty_claims;
            CREATE TRIGGER update_warranty_claims_updated_at
                BEFORE UPDATE ON warranty_claims
                FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
            """

            logger.info("⚙️ إنشاء triggers لتحديث التواريخ...")
            conn.execute(text(trigger_sql))

            # إدراج بيانات تجريبية لأنواع الضمانات
            sample_data_sql = """
            INSERT INTO warranty_types (name, name_ar, description, duration_months, coverage_type, is_active, created_by)
            SELECT 'Standard Warranty', 'ضمان قياسي', 'ضمان قياسي يغطي العيوب الصناعية', 12, 'full', true, 1
            WHERE NOT EXISTS (SELECT 1 FROM warranty_types WHERE name = 'Standard Warranty')
            UNION ALL
            SELECT 'Extended Warranty', 'ضمان ممتد', 'ضمان ممتد لفترة أطول مع تغطية شاملة', 24, 'full', true, 1
            WHERE NOT EXISTS (SELECT 1 FROM warranty_types WHERE name = 'Extended Warranty')
            UNION ALL
            SELECT 'Limited Warranty', 'ضمان محدود', 'ضمان محدود يغطي أجزاء معينة فقط', 6, 'limited', true, 1
            WHERE NOT EXISTS (SELECT 1 FROM warranty_types WHERE name = 'Limited Warranty');
            """

            logger.info("📊 إدراج بيانات تجريبية...")
            conn.execute(text(sample_data_sql))

            # تأكيد التغييرات
            conn.commit()

            logger.info("✅ تم إنشاء جداول نظام إدارة الضمانات بنجاح!")

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء جداول الضمانات: {e}")
        raise


if __name__ == "__main__":
    # إعداد نظام السجلات
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # تنفيذ إنشاء الجداول
    create_warranty_tables()
