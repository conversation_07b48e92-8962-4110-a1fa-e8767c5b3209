"""
Migration script to add payment fields to sales table and create default customer
"""

import sqlite3
import os
from datetime import datetime

def run_migration():
    """Run the migration to add payment fields to sales table"""

    # Get database path - database is in the root directory
    db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'smartpos.db')

    conn = None
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        print("Starting migration: Adding payment fields to sales table...")

        # Check if columns already exist
        cursor.execute("PRAGMA table_info(sales)")
        columns = [column[1] for column in cursor.fetchall()]

        # Add amount_paid column if it doesn't exist
        if 'amount_paid' not in columns:
            cursor.execute("""
                ALTER TABLE sales
                ADD COLUMN amount_paid REAL NOT NULL DEFAULT 0.0
            """)
            print("✓ Added amount_paid column to sales table")
        else:
            print("✓ amount_paid column already exists")

        # Add payment_status column if it doesn't exist
        if 'payment_status' not in columns:
            cursor.execute("""
                ALTER TABLE sales
                ADD COLUMN payment_status TEXT NOT NULL DEFAULT 'paid'
            """)
            print("✓ Added payment_status column to sales table")
        else:
            print("✓ payment_status column already exists")

        # Update existing sales to have correct payment amounts
        # For existing sales, set amount_paid = total_amount + tax_amount - discount_amount
        cursor.execute("""
            UPDATE sales
            SET amount_paid = total_amount + tax_amount - discount_amount,
                payment_status = 'paid'
            WHERE amount_paid = 0.0
        """)
        updated_rows = cursor.rowcount
        if updated_rows > 0:
            print(f"✓ Updated {updated_rows} existing sales with payment amounts")

        # Create default customer "عميل مباشر" if it doesn't exist
        cursor.execute("""
            SELECT id FROM customers WHERE name = 'عميل مباشر'
        """)
        default_customer = cursor.fetchone()

        if not default_customer:
            cursor.execute("""
                INSERT INTO customers (name, phone, email, address, notes, is_active, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                'عميل مباشر',
                None,
                None,
                None,
                'العميل الافتراضي للمبيعات المباشرة',
                True,
                datetime.now().isoformat(),
                datetime.now().isoformat()
            ))
            print("✓ Created default customer 'عميل مباشر'")
        else:
            print("✓ Default customer 'عميل مباشر' already exists")

        # Commit changes
        conn.commit()
        print("✅ Migration completed successfully!")

    except Exception as e:
        print(f"❌ Migration failed: {e}")
        if conn:
            conn.rollback()
        raise
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    run_migration()
