"""
Migration Script للنظام الجديد للفروع والمستودعات
يقوم بإنشاء الجداول الجديدة وتحديث البيانات الموجودة بشكل آمن
"""

import logging
import sys
import os
from typing import Dict, Any, List
from sqlalchemy import create_engine, text, MetaData, Table, inspect
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import SQLAlchemyError

# إضافة مسار المشروع للاستيراد
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.session import DATABASE_URL
from utils.datetime_utils import get_tripoli_now, create_timestamp_with_settings

# إعداد التسجيل
current_time = get_tripoli_now()
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'migration_{current_time.strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class BranchWarehouseMigration:
    """
    كلاس إدارة migration للنظام الجديد
    """
    
    def __init__(self):
        """تهيئة migration"""
        self.engine = create_engine(DATABASE_URL)
        self.SessionLocal = sessionmaker(bind=self.engine)
        self.metadata = MetaData()
        self.inspector = inspect(self.engine)
        
    def run_migration(self) -> Dict[str, Any]:
        """
        تشغيل migration كامل
        
        Returns:
            نتيجة العملية
        """
        try:
            logger.info("🚀 بدء migration نظام الفروع والمستودعات")
            
            # 1. فحص النظام الحالي
            system_check = self._check_current_system()
            if not system_check['success']:
                return system_check
            
            # 2. إنشاء backup
            backup_result = self._create_backup()
            if not backup_result['success']:
                return backup_result
            
            # 3. إنشاء الجداول الجديدة
            tables_result = self._create_new_tables()
            if not tables_result['success']:
                return tables_result
            
            # 4. ترحيل البيانات الموجودة
            migration_result = self._migrate_existing_data()
            if not migration_result['success']:
                return migration_result
            
            # 5. إنشاء الفهارس
            indexes_result = self._create_indexes()
            if not indexes_result['success']:
                return indexes_result
            
            # 6. التحقق من سلامة البيانات
            validation_result = self._validate_migration()
            if not validation_result['success']:
                return validation_result
            
            logger.info("✅ تم إكمال migration بنجاح")
            
            return {
                'success': True,
                'message': 'تم إكمال migration نظام الفروع والمستودعات بنجاح',
                'details': {
                    'backup_created': backup_result.get('backup_file'),
                    'tables_created': tables_result.get('tables_created', []),
                    'data_migrated': migration_result.get('records_migrated', 0),
                    'indexes_created': indexes_result.get('indexes_created', [])
                }
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في migration: {e}")
            return {
                'success': False,
                'error': f'خطأ في migration: {str(e)}'
            }
    
    def _check_current_system(self) -> Dict[str, Any]:
        """
        فحص النظام الحالي
        
        Returns:
            نتيجة الفحص
        """
        try:
            logger.info("🔍 فحص النظام الحالي...")
            
            # فحص وجود جدول المستودعات
            if not self.inspector.has_table('warehouses'):
                return {
                    'success': False,
                    'error': 'جدول المستودعات غير موجود'
                }
            
            # فحص وجود جدول المستخدمين
            if not self.inspector.has_table('users'):
                return {
                    'success': False,
                    'error': 'جدول المستخدمين غير موجود'
                }
            
            # فحص إذا كان migration تم من قبل
            if self.inspector.has_table('branches'):
                logger.warning("⚠️ جدول الفروع موجود مسبقاً - قد يكون migration تم من قبل")
            
            if self.inspector.has_table('branch_warehouses'):
                logger.warning("⚠️ جدول ربط الفروع والمستودعات موجود مسبقاً")
            
            logger.info("✅ فحص النظام مكتمل")
            
            return {
                'success': True,
                'message': 'النظام جاهز للـ migration'
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في فحص النظام: {e}")
            return {
                'success': False,
                'error': f'خطأ في فحص النظام: {str(e)}'
            }
    
    def _create_backup(self) -> Dict[str, Any]:
        """
        إنشاء backup للبيانات الحالية
        
        Returns:
            نتيجة إنشاء backup
        """
        try:
            logger.info("💾 إنشاء backup للبيانات...")

            current_time = get_tripoli_now()
            timestamp = current_time.strftime("%Y%m%d_%H%M%S")
            backup_file = f"backup_before_branch_migration_{timestamp}.sql"
            
            # في بيئة الإنتاج، يجب استخدام pg_dump أو أدوات backup مناسبة
            # هنا نقوم بحفظ معلومات أساسية فقط
            
            with self.SessionLocal() as session:
                # حفظ عدد المستودعات الحالية
                warehouses_count = session.execute(text("SELECT COUNT(*) FROM warehouses")).scalar()
                
                # حفظ عدد المستخدمين
                users_count = session.execute(text("SELECT COUNT(*) FROM users")).scalar()
                
                backup_info = {
                    'timestamp': timestamp,
                    'warehouses_count': warehouses_count,
                    'users_count': users_count,
                    'backup_file': backup_file
                }
                
                # كتابة معلومات backup
                with open(backup_file, 'w', encoding='utf-8') as f:
                    f.write(f"-- Backup created at {timestamp}\n")
                    f.write(f"-- Warehouses count: {warehouses_count}\n")
                    f.write(f"-- Users count: {users_count}\n")
                    f.write("-- This is a basic backup info file\n")
                    f.write("-- For full backup, use pg_dump or similar tools\n")
            
            logger.info(f"✅ تم إنشاء backup: {backup_file}")
            
            return {
                'success': True,
                'backup_file': backup_file,
                'backup_info': backup_info
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء backup: {e}")
            return {
                'success': False,
                'error': f'خطأ في إنشاء backup: {str(e)}'
            }
    
    def _create_new_tables(self) -> Dict[str, Any]:
        """
        إنشاء الجداول الجديدة
        
        Returns:
            نتيجة إنشاء الجداول
        """
        try:
            logger.info("🏗️ إنشاء الجداول الجديدة...")
            
            tables_created = []
            
            with self.SessionLocal() as session:
                # إنشاء جدول الفروع
                if not self.inspector.has_table('branches'):
                    create_branches_table = text("""
                        CREATE TABLE branches (
                            id SERIAL PRIMARY KEY,
                            name VARCHAR(100) NOT NULL,
                            code VARCHAR(20) UNIQUE NOT NULL,
                            address TEXT,
                            phone VARCHAR(20),
                            manager_name VARCHAR(100),
                            email VARCHAR(100),
                            is_active BOOLEAN NOT NULL DEFAULT true,
                            is_main BOOLEAN NOT NULL DEFAULT false,
                            city VARCHAR(50),
                            region VARCHAR(50),
                            postal_code VARCHAR(20),
                            max_daily_sales INTEGER,
                            working_hours_start VARCHAR(10),
                            working_hours_end VARCHAR(10),
                            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP WITH TIME ZONE,
                            created_by INTEGER REFERENCES users(id),
                            updated_by INTEGER REFERENCES users(id)
                        )
                    """)
                    
                    session.execute(create_branches_table)
                    tables_created.append('branches')
                    logger.info("✅ تم إنشاء جدول الفروع")
                
                # إنشاء جدول الربط
                if not self.inspector.has_table('branch_warehouses'):
                    create_branch_warehouses_table = text("""
                        CREATE TABLE branch_warehouses (
                            branch_id INTEGER NOT NULL REFERENCES branches(id) ON DELETE CASCADE,
                            warehouse_id INTEGER NOT NULL REFERENCES warehouses(id) ON DELETE CASCADE,
                            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                            is_primary BOOLEAN DEFAULT false,
                            priority INTEGER DEFAULT 1,
                            PRIMARY KEY (branch_id, warehouse_id)
                        )
                    """)
                    
                    session.execute(create_branch_warehouses_table)
                    tables_created.append('branch_warehouses')
                    logger.info("✅ تم إنشاء جدول ربط الفروع والمستودعات")
                
                session.commit()
            
            logger.info(f"✅ تم إنشاء {len(tables_created)} جدول جديد")
            
            return {
                'success': True,
                'tables_created': tables_created
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء الجداول: {e}")
            return {
                'success': False,
                'error': f'خطأ في إنشاء الجداول: {str(e)}'
            }
    
    def _migrate_existing_data(self) -> Dict[str, Any]:
        """
        ترحيل البيانات الموجودة
        
        Returns:
            نتيجة ترحيل البيانات
        """
        try:
            logger.info("📦 ترحيل البيانات الموجودة...")
            
            records_migrated = 0
            
            with self.SessionLocal() as session:
                # إنشاء فرع افتراضي إذا لم يوجد
                existing_branches = session.execute(text("SELECT COUNT(*) FROM branches")).scalar()
                
                if existing_branches == 0:
                    # إنشاء فرع رئيسي افتراضي
                    insert_main_branch = text("""
                        INSERT INTO branches (
                            name, code, address, is_active, is_main, 
                            city, region, created_at
                        ) VALUES (
                            'الفرع الرئيسي', 'MAIN-001', 'المقر الرئيسي', 
                            true, true, 'طرابلس', 'طرابلس', :created_at
                        )
                    """)
                    
                    session.execute(insert_main_branch, {"created_at": get_tripoli_now()})
                    records_migrated += 1
                    logger.info("✅ تم إنشاء الفرع الرئيسي الافتراضي")
                    
                    # ربط جميع المستودعات بالفرع الرئيسي
                    link_warehouses = text("""
                        INSERT INTO branch_warehouses (branch_id, warehouse_id, is_primary, priority, created_at)
                        SELECT 
                            (SELECT id FROM branches WHERE code = 'MAIN-001'),
                            w.id,
                            w.is_main,
                            CASE WHEN w.is_main THEN 1 ELSE 2 END,
                            :created_at
                        FROM warehouses w
                        WHERE w.is_active = true
                    """)
                    
                    result = session.execute(link_warehouses, {"created_at": get_tripoli_now()})
                    warehouses_linked = getattr(result, 'rowcount', 0)
                    records_migrated += warehouses_linked
                    
                    logger.info(f"✅ تم ربط {warehouses_linked} مستودع بالفرع الرئيسي")
                
                session.commit()
            
            logger.info(f"✅ تم ترحيل {records_migrated} سجل")
            
            return {
                'success': True,
                'records_migrated': records_migrated
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في ترحيل البيانات: {e}")
            return {
                'success': False,
                'error': f'خطأ في ترحيل البيانات: {str(e)}'
            }
    
    def _create_indexes(self) -> Dict[str, Any]:
        """
        إنشاء الفهارس
        
        Returns:
            نتيجة إنشاء الفهارس
        """
        try:
            logger.info("📇 إنشاء الفهارس...")
            
            indexes_created = []
            
            with self.SessionLocal() as session:
                # فهارس جدول الفروع
                branch_indexes = [
                    "CREATE INDEX IF NOT EXISTS idx_branches_code ON branches(code)",
                    "CREATE INDEX IF NOT EXISTS idx_branches_name ON branches(name)",
                    "CREATE INDEX IF NOT EXISTS idx_branches_is_active ON branches(is_active)",
                    "CREATE INDEX IF NOT EXISTS idx_branches_is_main ON branches(is_main)",
                    "CREATE INDEX IF NOT EXISTS idx_branches_city ON branches(city)",
                    "CREATE INDEX IF NOT EXISTS idx_branches_region ON branches(region)"
                ]
                
                for index_sql in branch_indexes:
                    session.execute(text(index_sql))
                    indexes_created.append(index_sql.split()[-1])
                
                # فهارس جدول الربط
                link_indexes = [
                    "CREATE INDEX IF NOT EXISTS idx_branch_warehouses_branch_id ON branch_warehouses(branch_id)",
                    "CREATE INDEX IF NOT EXISTS idx_branch_warehouses_warehouse_id ON branch_warehouses(warehouse_id)",
                    "CREATE INDEX IF NOT EXISTS idx_branch_warehouses_is_primary ON branch_warehouses(is_primary)",
                    "CREATE INDEX IF NOT EXISTS idx_branch_warehouses_priority ON branch_warehouses(priority)"
                ]
                
                for index_sql in link_indexes:
                    session.execute(text(index_sql))
                    indexes_created.append(index_sql.split()[-1])
                
                session.commit()
            
            logger.info(f"✅ تم إنشاء {len(indexes_created)} فهرس")
            
            return {
                'success': True,
                'indexes_created': indexes_created
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء الفهارس: {e}")
            return {
                'success': False,
                'error': f'خطأ في إنشاء الفهارس: {str(e)}'
            }

    def _validate_migration(self) -> Dict[str, Any]:
        """
        التحقق من سلامة migration

        Returns:
            نتيجة التحقق
        """
        try:
            logger.info("🔍 التحقق من سلامة migration...")

            validation_results = {}

            with self.SessionLocal() as session:
                # التحقق من وجود الجداول
                tables_check = {
                    'branches': self.inspector.has_table('branches'),
                    'branch_warehouses': self.inspector.has_table('branch_warehouses')
                }
                validation_results['tables_exist'] = tables_check

                if not all(tables_check.values()):
                    return {
                        'success': False,
                        'error': 'بعض الجداول المطلوبة غير موجودة',
                        'details': tables_check
                    }

                # التحقق من البيانات
                branches_count = session.execute(text("SELECT COUNT(*) FROM branches")).scalar()
                warehouses_count = session.execute(text("SELECT COUNT(*) FROM warehouses")).scalar()
                links_count = session.execute(text("SELECT COUNT(*) FROM branch_warehouses")).scalar()

                validation_results['data_counts'] = {
                    'branches': branches_count,
                    'warehouses': warehouses_count,
                    'links': links_count
                }

                # التحقق من وجود فرع رئيسي
                main_branches = session.execute(text("SELECT COUNT(*) FROM branches WHERE is_main = true")).scalar() or 0
                validation_results['main_branches_count'] = main_branches

                if main_branches == 0:
                    logger.warning("⚠️ لا يوجد فرع رئيسي محدد")
                elif main_branches > 1:
                    logger.warning("⚠️ يوجد أكثر من فرع رئيسي")

                # التحقق من سلامة الروابط
                orphaned_links = session.execute(text("""
                    SELECT COUNT(*) FROM branch_warehouses bw
                    LEFT JOIN branches b ON bw.branch_id = b.id
                    LEFT JOIN warehouses w ON bw.warehouse_id = w.id
                    WHERE b.id IS NULL OR w.id IS NULL
                """)).scalar() or 0

                validation_results['orphaned_links'] = orphaned_links

                if orphaned_links > 0:
                    return {
                        'success': False,
                        'error': f'يوجد {orphaned_links} رابط معطل في جدول branch_warehouses'
                    }

            logger.info("✅ التحقق من سلامة migration مكتمل")

            return {
                'success': True,
                'validation_results': validation_results
            }

        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من سلامة migration: {e}")
            return {
                'success': False,
                'error': f'خطأ في التحقق: {str(e)}'
            }

    def rollback_migration(self) -> Dict[str, Any]:
        """
        التراجع عن migration (في حالة الحاجة)

        Returns:
            نتيجة التراجع
        """
        try:
            logger.info("🔄 بدء التراجع عن migration...")

            with self.SessionLocal() as session:
                # حذف الجداول الجديدة بالترتيب الصحيح
                session.execute(text("DROP TABLE IF EXISTS branch_warehouses CASCADE"))
                session.execute(text("DROP TABLE IF EXISTS branches CASCADE"))

                session.commit()

            logger.info("✅ تم التراجع عن migration بنجاح")

            return {
                'success': True,
                'message': 'تم التراجع عن migration بنجاح'
            }

        except Exception as e:
            logger.error(f"❌ خطأ في التراجع عن migration: {e}")
            return {
                'success': False,
                'error': f'خطأ في التراجع: {str(e)}'
            }


def main():
    """
    الدالة الرئيسية لتشغيل migration
    """
    print("🚀 بدء migration نظام الفروع والمستودعات")
    print("=" * 50)

    migration = BranchWarehouseMigration()

    # عرض خيارات للمستخدم
    print("اختر العملية المطلوبة:")
    print("1. تشغيل migration كامل")
    print("2. التحقق من النظام فقط")
    print("3. التراجع عن migration")
    print("4. إنهاء")

    choice = input("أدخل اختيارك (1-4): ").strip()

    if choice == "1":
        # تأكيد من المستخدم
        confirm = input("هل أنت متأكد من تشغيل migration؟ (yes/no): ").strip().lower()
        if confirm in ['yes', 'y', 'نعم']:
            result = migration.run_migration()
            if result['success']:
                print("\n✅ تم إكمال migration بنجاح!")
                print(f"التفاصيل: {result.get('details', {})}")
            else:
                print(f"\n❌ فشل migration: {result.get('error')}")
        else:
            print("تم إلغاء العملية")

    elif choice == "2":
        result = migration._check_current_system()
        if result['success']:
            print(f"\n✅ {result['message']}")
        else:
            print(f"\n❌ {result['error']}")

    elif choice == "3":
        confirm = input("هل أنت متأكد من التراجع عن migration؟ (yes/no): ").strip().lower()
        if confirm in ['yes', 'y', 'نعم']:
            result = migration.rollback_migration()
            if result['success']:
                print(f"\n✅ {result['message']}")
            else:
                print(f"\n❌ {result['error']}")
        else:
            print("تم إلغاء العملية")

    elif choice == "4":
        print("تم إنهاء البرنامج")

    else:
        print("اختيار غير صحيح")


if __name__ == "__main__":
    main()
