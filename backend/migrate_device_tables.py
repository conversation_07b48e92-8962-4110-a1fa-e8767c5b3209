#!/usr/bin/env python3
"""
سكربت ترحيل جداول الأجهزة المفقودة من SQLite إلى PostgreSQL
"""

import sys
import os
import logging
from pathlib import Path

# إضافة مسار backend إلى Python path
backend_path = Path(__file__).parent
sys.path.insert(0, str(backend_path))

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('device_tables_migration.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def main():
    """ترحيل جداول الأجهزة المفقودة"""
    try:
        print("=" * 60)
        print("🔄 ترحيل جداول الأجهزة من SQLite إلى PostgreSQL")
        print("=" * 60)
        
        # استيراد خدمة الترحيل
        from services.database_migration_service import DatabaseMigrationService
        
        # الحصول على instance من الخدمة
        migration_service = DatabaseMigrationService.getInstance()
        
        # إعداد الاتصالات
        if not migration_service._setup_database_connections():
            print("❌ فشل في إعداد اتصالات قواعد البيانات")
            return 1
        
        # قائمة جداول الأجهزة للترحيل
        device_tables = [
            "approved_devices",
            "blocked_devices", 
            "pending_devices",
            "device_security_settings"
        ]
        
        total_rows = 0
        migrated_tables = 0
        
        for table_name in device_tables:
            print(f"\n📊 ترحيل بيانات {table_name}...")
            success, row_count = migration_service._migrate_table_data(table_name)
            if success:
                print(f"✅ تم ترحيل {row_count} صف من جدول {table_name}")
                total_rows += row_count
                migrated_tables += 1
            else:
                print(f"⚠️ لم يتم العثور على بيانات في جدول {table_name} أو حدث خطأ")
        
        # تحديث sequences للجداول الجديدة
        print("\n🔄 تحديث sequences...")
        migration_service._update_sequences()
        
        # تنظيف الاتصالات
        migration_service._cleanup_connections()
        
        print("\n" + "=" * 60)
        print(f"✅ تم إكمال ترحيل جداول الأجهزة بنجاح!")
        print(f"📊 الإحصائيات:")
        print(f"   • الجداول المرحلة: {migrated_tables}")
        print(f"   • إجمالي الصفوف: {total_rows}")
        print("=" * 60)
        
        return 0
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الوحدات: {e}")
        print("💡 تأكد من تشغيل السكربت من مجلد backend")
        return 1
        
    except Exception as e:
        logger.error(f"خطأ عام في ترحيل جداول الأجهزة: {e}")
        print(f"❌ خطأ غير متوقع: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
