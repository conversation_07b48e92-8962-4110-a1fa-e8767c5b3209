#!/bin/bash

# تفعيل البيئة الافتراضية لمشروع SmartPOS
# Activate virtual environment for SmartPOS project

echo "🔄 تفعيل البيئة الافتراضية..."
echo "🔄 Activating virtual environment..."

# التأكد من وجود البيئة الافتراضية
if [ ! -d "venv" ]; then
    echo "❌ البيئة الافتراضية غير موجودة. يرجى إنشاؤها أولاً."
    echo "❌ Virtual environment not found. Please create it first."
    echo "💡 استخدم: python3 -m venv venv"
    echo "💡 Use: python3 -m venv venv"
    exit 1
fi

# تفعيل البيئة الافتراضية
source venv/bin/activate

echo "✅ تم تفعيل البيئة الافتراضية بنجاح!"
echo "✅ Virtual environment activated successfully!"
echo "🐍 Python path: $(which python)"
echo "📦 Pip path: $(which pip)"

# عرض إصدارات المكتبات المهمة
echo ""
echo "📋 إصدارات المكتبات المثبتة:"
echo "📋 Installed package versions:"
python -c "
try:
    import fastapi
    print(f'  FastAPI: {fastapi.__version__}')
except ImportError:
    print('  FastAPI: غير مثبت / Not installed')

try:
    import uvicorn
    print(f'  Uvicorn: {uvicorn.__version__}')
except ImportError:
    print('  Uvicorn: غير مثبت / Not installed')

try:
    import sqlalchemy
    print(f'  SQLAlchemy: {sqlalchemy.__version__}')
except ImportError:
    print('  SQLAlchemy: غير مثبت / Not installed')

try:
    import psycopg2
    print('  psycopg2: مثبت / Installed')
except ImportError:
    print('  psycopg2: غير مثبت / Not installed')
"

echo ""
echo "🚀 يمكنك الآن تشغيل الخادم باستخدام:"
echo "🚀 You can now run the server using:"
echo "   uvicorn main:app --reload --host 0.0.0.0 --port 8000"
