#!/usr/bin/env python3
"""
سكربت ترحيل الجداول المفقودة من SQLite إلى PostgreSQL
"""

import sys
import os
import logging
from pathlib import Path

# إضافة مسار backend إلى Python path
backend_path = Path(__file__).parent
sys.path.insert(0, str(backend_path))

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('missing_tables_migration.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def main():
    """ترحيل الجداول المفقودة"""
    try:
        print("=" * 60)
        print("🔄 ترحيل الجداول المفقودة من SQLite إلى PostgreSQL")
        print("=" * 60)
        
        # استيراد خدمة الترحيل
        from services.database_migration_service import DatabaseMigrationService
        
        # الحصول على instance من الخدمة
        migration_service = DatabaseMigrationService.getInstance()
        
        # إعداد الاتصالات
        if not migration_service._setup_database_connections():
            print("❌ فشل في إعداد اتصالات قواعد البيانات")
            return 1
        
        # إنشاء مخطط PostgreSQL (سيضيف الجداول المفقودة)
        print("\n📋 إنشاء الجداول المفقودة في PostgreSQL...")
        if not migration_service._create_postgres_schema():
            print("❌ فشل في إنشاء مخطط PostgreSQL")
            return 1
        
        # ترحيل بيانات system_logs إذا كانت موجودة
        print("\n📊 ترحيل بيانات system_logs...")
        success, row_count = migration_service._migrate_table_data("system_logs")
        if success:
            print(f"✅ تم ترحيل {row_count} صف من جدول system_logs")
        else:
            print("⚠️ لم يتم العثور على بيانات في جدول system_logs أو حدث خطأ")
        
        # تحديث sequences للجداول الجديدة
        print("\n🔄 تحديث sequences...")
        migration_service._update_sequences()
        
        # تنظيف الاتصالات
        migration_service._cleanup_connections()
        
        print("\n" + "=" * 60)
        print("✅ تم إكمال ترحيل الجداول المفقودة بنجاح!")
        print("=" * 60)
        
        return 0
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الوحدات: {e}")
        print("💡 تأكد من تشغيل السكربت من مجلد backend")
        return 1
        
    except Exception as e:
        logger.error(f"خطأ عام في ترحيل الجداول المفقودة: {e}")
        print(f"❌ خطأ غير متوقع: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
