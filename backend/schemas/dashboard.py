from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime

class SalesTrend(BaseModel):
    date: str
    amount: float

class TopProduct(BaseModel):
    id: int
    name: str
    quantity: int
    total: float

class RecentSale(BaseModel):
    id: int
    total: float
    createdAt: str
    items: int
    payment_method: str
    amount_paid: float
    total_amount: float
    payment_status: str

class DashboardStats(BaseModel):
    totalSales: int
    totalRevenue: float
    todaySales: float
    todayProfits: float
    totalDebts: float
    unpaidDebts: float
    lowStockCount: int
    recentSales: List[RecentSale]
    topProducts: List[TopProduct]