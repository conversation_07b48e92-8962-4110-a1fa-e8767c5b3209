from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, ConfigDict

class UnitBase(BaseModel):
    """Base schema for unit data."""
    name: str = Field(..., min_length=1, max_length=50)
    symbol: Optional[str] = Field(None, max_length=10)
    description: Optional[str] = None
    unit_type: Optional[str] = Field(None, max_length=50)  # weight, volume, count, etc.
    base_unit_id: Optional[int] = None
    conversion_factor: Optional[float] = Field(None, gt=0)
    is_active: bool = True

class UnitCreate(UnitBase):
    """Schema for creating a new unit."""
    pass

class UnitUpdate(BaseModel):
    """Schema for updating an existing unit."""
    name: Optional[str] = Field(None, min_length=1, max_length=50)
    symbol: Optional[str] = Field(None, max_length=10)
    description: Optional[str] = None
    unit_type: Optional[str] = Field(None, max_length=50)
    base_unit_id: Optional[int] = None
    conversion_factor: Optional[float] = Field(None, gt=0)
    is_active: Optional[bool] = None

class UnitInDB(UnitBase):
    """Schema for unit data as stored in database."""
    id: int
    created_at: datetime
    updated_at: Optional[datetime]
    created_by: int
    updated_by: Optional[int]

    model_config = ConfigDict(from_attributes=True)

class UnitResponse(UnitInDB):
    """Schema for unit response."""
    base_unit: Optional['UnitResponse'] = None

# Update forward references
UnitResponse.model_rebuild()
