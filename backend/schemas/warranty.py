"""
Pydantic schemas لنظام إدارة الضمانات
"""

from pydantic import BaseModel, Field, ConfigDict, field_validator
from typing import Optional
from datetime import datetime, date
from decimal import Decimal


# ===== أنواع الضمانات =====

class WarrantyTypeBase(BaseModel):
    """Base schema for warranty type data."""
    name: str = Field(..., min_length=1, max_length=100)
    name_ar: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    duration_months: int = Field(..., gt=0, le=120)  # من 1 إلى 120 شهر
    coverage_type: str = Field(..., pattern="^(full|partial|limited)$")
    terms_conditions: Optional[str] = None
    is_active: bool = True


class WarrantyTypeCreate(WarrantyTypeBase):
    """Schema for creating a new warranty type."""
    pass


class WarrantyTypeUpdate(BaseModel):
    """Schema for updating an existing warranty type."""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    name_ar: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    duration_months: Optional[int] = Field(None, gt=0, le=120)
    coverage_type: Optional[str] = Field(None, pattern="^(full|partial|limited)$")
    terms_conditions: Optional[str] = None
    is_active: Optional[bool] = None


class WarrantyTypeResponse(WarrantyTypeBase):
    """Schema for warranty type response."""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    created_by: int
    updated_by: Optional[int] = None

    model_config = ConfigDict(from_attributes=True)


# ===== ضمانات المنتجات =====

class ProductWarrantyBase(BaseModel):
    """Base schema for product warranty data."""
    product_id: int
    warranty_type_id: int
    purchase_date: date
    start_date: date
    end_date: date
    customer_id: Optional[int] = None
    status: str = Field(default="active", pattern="^(active|expired|voided|claimed)$")
    notes: Optional[str] = None


class ProductWarrantyCreate(ProductWarrantyBase):
    """Schema for creating a new product warranty."""
    pass


class ProductWarrantyUpdate(BaseModel):
    """Schema for updating an existing product warranty."""
    warranty_type_id: Optional[int] = None
    purchase_date: Optional[date] = None
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    customer_id: Optional[int] = None
    status: Optional[str] = Field(None, pattern="^(active|expired|voided)$")
    notes: Optional[str] = None


class ExtendWarrantyData(BaseModel):
    """Schema for extending warranty."""
    months: int = Field(..., gt=0, le=60)  # من 1 إلى 60 شهر
    reason: str = Field(..., min_length=1, max_length=500)


class VoidWarrantyData(BaseModel):
    """Schema for voiding warranty."""
    reason: str = Field(..., min_length=1, max_length=500)


class ProductWarrantyResponse(ProductWarrantyBase):
    """Schema for product warranty response."""
    id: int
    warranty_number: str
    product_name: Optional[str] = None
    product_sku: Optional[str] = None
    warranty_type_name: Optional[str] = None
    customer_name: Optional[str] = None
    extended_months: int = 0
    extension_reason: Optional[str] = None
    extended_at: Optional[datetime] = None
    extended_by: Optional[int] = None
    void_reason: Optional[str] = None
    voided_at: Optional[datetime] = None
    voided_by: Optional[int] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    created_by: int
    updated_by: Optional[int] = None
    
    # خصائص محسوبة
    is_active: Optional[bool] = None
    days_remaining: Optional[int] = None
    is_expiring_soon: Optional[bool] = None

    model_config = ConfigDict(from_attributes=True)


# ===== مطالبات الضمان =====

class WarrantyClaimBase(BaseModel):
    """Base schema for warranty claim data."""
    warranty_id: int
    claim_type: str = Field(..., pattern="^(repair|replacement|refund)$")
    issue_description: str = Field(..., min_length=1, max_length=2000)
    claim_description: Optional[str] = Field(None, max_length=2000)
    expected_resolution_date: Optional[date] = None
    priority: str = Field(default="normal", pattern="^(low|normal|high|urgent)$")
    estimated_cost: Optional[Decimal] = Field(None, ge=0)
    cost_covered_by_warranty: bool = True
    notes: Optional[str] = None


class WarrantyClaimCreate(WarrantyClaimBase):
    """Schema for creating a new warranty claim."""
    pass


class WarrantyClaimUpdate(BaseModel):
    """Schema for updating an existing warranty claim."""
    claim_type: Optional[str] = Field(None, pattern="^(repair|replacement|refund)$")
    issue_description: Optional[str] = Field(None, min_length=1, max_length=2000)
    claim_description: Optional[str] = Field(None, max_length=2000)
    expected_resolution_date: Optional[date] = None
    priority: Optional[str] = Field(None, pattern="^(low|normal|high|urgent)$")
    estimated_cost: Optional[Decimal] = Field(None, ge=0)
    actual_cost: Optional[Decimal] = Field(None, ge=0)
    cost_covered_by_warranty: Optional[bool] = None
    notes: Optional[str] = None
    internal_notes: Optional[str] = None


class ProcessClaimData(BaseModel):
    """Schema for processing warranty claim."""
    status: str = Field(..., pattern="^(approved|rejected|in_progress|completed)$")
    resolution: Optional[str] = Field(None, max_length=2000)
    actual_cost: Optional[Decimal] = Field(None, ge=0)
    notes: Optional[str] = None
    internal_notes: Optional[str] = None
    rejection_reason: Optional[str] = Field(None, max_length=1000)


class WarrantyClaimResponse(WarrantyClaimBase):
    """Schema for warranty claim response."""
    id: int
    claim_number: str
    claim_date: date
    status: str
    warranty_number: Optional[str] = None
    product_name: Optional[str] = None
    customer_name: Optional[str] = None
    resolution: Optional[str] = None
    resolution_date: Optional[date] = None
    resolved_by: Optional[int] = None
    actual_cost: Optional[Decimal] = None
    reviewed_by: Optional[int] = None
    reviewed_at: Optional[datetime] = None
    approved_by: Optional[int] = None
    approved_at: Optional[datetime] = None
    rejection_reason: Optional[str] = None
    rejected_by: Optional[int] = None
    rejected_at: Optional[datetime] = None
    internal_notes: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    created_by: int
    updated_by: Optional[int] = None
    
    # خصائص محسوبة
    is_pending: Optional[bool] = None
    is_approved: Optional[bool] = None
    is_completed: Optional[bool] = None
    days_since_claim: Optional[int] = None

    model_config = ConfigDict(from_attributes=True)


# ===== الفلاتر والبحث =====

class WarrantyTypeFilters(BaseModel):
    """Filters for warranty types."""
    search: Optional[str] = None
    status: Optional[str] = Field(None, pattern="^(all|active|inactive)$")
    coverage_type: Optional[str] = Field(None, pattern="^(all|full|partial|limited)$")


class WarrantyFilters(BaseModel):
    """Filters for product warranties."""
    search: Optional[str] = None
    status: Optional[str] = Field(None, pattern="^(all|active|expired|voided|claimed)$")
    warranty_type_id: Optional[int] = None
    customer_id: Optional[int] = None
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    available_for_claims: Optional[bool] = None  # فلتر للضمانات المتاحة للمطالبة


class ClaimFilters(BaseModel):
    """Filters for warranty claims."""
    search: Optional[str] = None
    status: Optional[str] = Field(None, pattern="^(all|pending|approved|rejected|in_progress|completed)$")
    claim_type: Optional[str] = Field(None, pattern="^(all|repair|replacement|refund)$")
    priority: Optional[str] = Field(None, pattern="^(all|low|normal|high|urgent)$")
    warranty_id: Optional[int] = None
    start_date: Optional[date] = None
    end_date: Optional[date] = None


# ===== الإحصائيات والتقارير =====

class WarrantyStats(BaseModel):
    """Warranty statistics."""
    total_warranties: int = 0
    active_warranties: int = 0
    expired_warranties: int = 0
    voided_warranties: int = 0
    claimed_warranties: int = 0
    expiring_soon: int = 0  # خلال 30 يوم
    total_claims: int = 0
    pending_claims: int = 0
    approved_claims: int = 0
    rejected_claims: int = 0
    completed_claims: int = 0
    average_resolution_days: Optional[float] = None
    total_claim_cost: Optional[Decimal] = None


class ClaimStatistics(BaseModel):
    """Claim statistics."""
    total_claims: int = 0
    pending_claims: int = 0
    approved_claims: int = 0
    rejected_claims: int = 0
    in_progress_claims: int = 0
    completed_claims: int = 0
    repair_claims: int = 0
    replacement_claims: int = 0
    refund_claims: int = 0
    average_resolution_days: Optional[float] = None
    total_estimated_cost: Optional[Decimal] = None
    total_actual_cost: Optional[Decimal] = None
    cost_savings: Optional[Decimal] = None


class ExpiringWarranty(BaseModel):
    """Expiring warranty information."""
    id: int
    warranty_number: str
    product_name: str
    product_sku: str
    customer_name: Optional[str] = None
    end_date: date
    days_remaining: int
    warranty_type_name: str

    model_config = ConfigDict(from_attributes=True)


class DateRange(BaseModel):
    """Date range for reports."""
    start_date: date
    end_date: date

    @field_validator('end_date')
    def validate_end_date(cls, v, info):
        if 'start_date' in info.data and v < info.data['start_date']:
            raise ValueError('End date must be after start date')
        return v


class ReportType(BaseModel):
    """Report type for exports."""
    type: str = Field(..., pattern="^(warranties|claims|statistics|expiring)$")
    format: str = Field(default="excel", pattern="^(excel|csv|pdf)$")
