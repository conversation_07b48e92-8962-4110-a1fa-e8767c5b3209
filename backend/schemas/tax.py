"""
Schemas للضرائب - Tax Schemas
يحتوي على جميع schemas المطلوبة للتحقق من صحة البيانات للضرائب
"""

from pydantic import BaseModel, Field, field_validator
from typing import Optional, List
from datetime import datetime, date
from decimal import Decimal


# ===== Tax Type Schemas =====

class TaxTypeBase(BaseModel):
    """Schema أساسي لأنواع الضرائب"""
    name: str = Field(..., min_length=1, max_length=100, description="اسم نوع الضريبة بالإنجليزية")
    name_ar: str = Field(..., min_length=1, max_length=100, description="اسم نوع الضريبة بالعربية")
    description: Optional[str] = Field(None, description="وصف نوع الضريبة")
    tax_category: str = Field(default='standard', description="فئة الضريبة")
    calculation_method: str = Field(default='percentage', description="طريقة الحساب")
    is_compound: bool = Field(default=False, description="ضريبة مركبة")
    is_active: bool = Field(default=True, description="حالة التفعيل")
    sort_order: int = Field(default=0, description="ترتيب العرض")

    @field_validator('tax_category')
    def validate_tax_category(cls, v):
        allowed_categories = ['standard', 'reduced', 'zero', 'exempt']
        if v not in allowed_categories:
            raise ValueError(f'tax_category must be one of: {", ".join(allowed_categories)}')
        return v

    @field_validator('calculation_method')
    def validate_calculation_method(cls, v):
        allowed_methods = ['percentage', 'fixed']
        if v not in allowed_methods:
            raise ValueError(f'calculation_method must be one of: {", ".join(allowed_methods)}')
        return v


class TaxTypeCreate(TaxTypeBase):
    """Schema لإنشاء نوع ضريبة جديد"""
    pass


class TaxTypeUpdate(BaseModel):
    """Schema لتحديث نوع ضريبة موجود"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    name_ar: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    tax_category: Optional[str] = None
    calculation_method: Optional[str] = None
    is_compound: Optional[bool] = None
    is_active: Optional[bool] = None
    sort_order: Optional[int] = None

    @field_validator('tax_category')
    def validate_tax_category(cls, v):
        if v is not None:
            allowed_categories = ['standard', 'reduced', 'zero', 'exempt']
            if v not in allowed_categories:
                raise ValueError(f'tax_category must be one of: {", ".join(allowed_categories)}')
        return v

    @field_validator('calculation_method')
    def validate_calculation_method(cls, v):
        if v is not None:
            allowed_methods = ['percentage', 'fixed']
            if v not in allowed_methods:
                raise ValueError(f'calculation_method must be one of: {", ".join(allowed_methods)}')
        return v


class TaxTypeResponse(TaxTypeBase):
    """Schema لاستجابة نوع الضريبة"""
    id: int
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    created_by: Optional[int] = None
    updated_by: Optional[int] = None
    tax_rates_count: int = Field(default=0, description="عدد القيم الضريبية")
    active_rates_count: int = Field(default=0, description="عدد القيم الضريبية النشطة")
    category_display_name: str = Field(description="اسم الفئة للعرض")
    calculation_method_display_name: str = Field(description="اسم طريقة الحساب للعرض")

    class Config:
        from_attributes = True


# ===== Tax Rate Schemas =====

class TaxRateBase(BaseModel):
    """Schema أساسي لقيم الضرائب"""
    tax_type_id: int = Field(..., gt=0, description="معرف نوع الضريبة")
    name: str = Field(..., min_length=1, max_length=100, description="اسم القيمة الضريبية")
    rate_value: Decimal = Field(..., ge=0, description="القيمة الضريبية")
    description: Optional[str] = Field(None, description="وصف القيمة الضريبية")
    effective_from: Optional[date] = Field(None, description="تاريخ بداية السريان")
    effective_to: Optional[date] = Field(None, description="تاريخ نهاية السريان")
    is_default: bool = Field(default=False, description="القيمة الافتراضية")
    is_active: bool = Field(default=True, description="حالة التفعيل")
    applies_to: str = Field(default='all', description="ينطبق على")
    min_amount: Optional[Decimal] = Field(None, ge=0, description="الحد الأدنى للمبلغ")
    max_amount: Optional[Decimal] = Field(None, ge=0, description="الحد الأقصى للمبلغ")
    tax_code: Optional[str] = Field(None, max_length=20, description="رمز الضريبة الحكومي")
    sort_order: int = Field(default=0, description="ترتيب العرض")

    @field_validator('applies_to')
    def validate_applies_to(cls, v):
        allowed_values = ['all', 'products', 'services']
        if v not in allowed_values:
            raise ValueError(f'applies_to must be one of: {", ".join(allowed_values)}')
        return v

    @field_validator('max_amount')
    def validate_max_amount(cls, v, info):
        if v is not None and 'min_amount' in info.data and info.data['min_amount'] is not None:
            if v < info.data['min_amount']:
                raise ValueError('max_amount must be greater than or equal to min_amount')
        return v


class TaxRateCreate(TaxRateBase):
    """Schema لإنشاء قيمة ضريبية جديدة"""
    pass


class TaxRateUpdate(BaseModel):
    """Schema لتحديث قيمة ضريبية موجودة"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    rate_value: Optional[Decimal] = Field(None, ge=0)
    description: Optional[str] = None
    effective_from: Optional[date] = None
    effective_to: Optional[date] = None
    is_default: Optional[bool] = None
    is_active: Optional[bool] = None
    applies_to: Optional[str] = None
    min_amount: Optional[Decimal] = Field(None, ge=0)
    max_amount: Optional[Decimal] = Field(None, ge=0)
    tax_code: Optional[str] = Field(None, max_length=20)
    sort_order: Optional[int] = None

    @field_validator('applies_to')
    def validate_applies_to(cls, v):
        if v is not None:
            allowed_values = ['all', 'products', 'services']
            if v not in allowed_values:
                raise ValueError(f'applies_to must be one of: {", ".join(allowed_values)}')
        return v


class TaxRateResponse(TaxRateBase):
    """Schema لاستجابة القيمة الضريبية"""
    id: int
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    created_by: Optional[int] = None
    updated_by: Optional[int] = None
    tax_type_name: Optional[str] = Field(None, description="اسم نوع الضريبة")
    display_rate: str = Field(description="القيمة الضريبية للعرض")
    applies_to_display_name: str = Field(description="اسم نطاق التطبيق للعرض")

    class Config:
        from_attributes = True


# ===== Filter Schemas =====

class TaxTypeFilters(BaseModel):
    """Schema لفلاتر أنواع الضرائب"""
    search: Optional[str] = Field(None, description="البحث في الاسم أو الوصف")
    status: Optional[str] = Field("all", description="الحالة: all, active, inactive")
    tax_category: Optional[str] = Field("all", description="فئة الضريبة")
    calculation_method: Optional[str] = Field("all", description="طريقة الحساب")
    is_compound: Optional[bool] = Field(None, description="ضريبة مركبة")


class TaxRateFilters(BaseModel):
    """Schema لفلاتر قيم الضرائب"""
    search: Optional[str] = Field(None, description="البحث في الاسم أو الوصف")
    status: Optional[str] = Field("all", description="الحالة: all, active, inactive")
    tax_type_id: Optional[int] = Field(None, description="معرف نوع الضريبة")
    applies_to: Optional[str] = Field("all", description="ينطبق على")
    is_default: Optional[bool] = Field(None, description="القيمة الافتراضية")
    effective_only: Optional[bool] = Field(None, description="القيم السارية فقط")


# ===== Combined Response Schemas =====

class TaxTypeWithRates(TaxTypeResponse):
    """Schema لنوع الضريبة مع قيمها"""
    tax_rates: List[TaxRateResponse] = Field(default_factory=list, description="قيم الضريبة")


class TaxCalculationRequest(BaseModel):
    """Schema لطلب حساب الضريبة"""
    tax_rate_id: int = Field(..., gt=0, description="معرف القيمة الضريبية")
    base_amount: Decimal = Field(..., gt=0, description="المبلغ الأساسي")


class TaxCalculationResponse(BaseModel):
    """Schema لاستجابة حساب الضريبة"""
    base_amount: Decimal = Field(description="المبلغ الأساسي")
    tax_amount: Decimal = Field(description="مبلغ الضريبة")
    total_amount: Decimal = Field(description="المبلغ الإجمالي")
    tax_rate: TaxRateResponse = Field(description="القيمة الضريبية المستخدمة")
