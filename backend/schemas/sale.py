from pydantic import BaseModel, Field, ConfigDict, field_validator
from typing import Optional, List
from datetime import datetime

class SaleItemBase(BaseModel):
    product_id: int
    quantity: int = Field(..., gt=0)
    unit_price: float = Field(..., gt=0)
    discount: float = Field(default=0, ge=0)

class SaleItemCreate(SaleItemBase):
    pass

class SaleItemResponse(SaleItemBase):
    id: int
    sale_id: int
    subtotal: float
    created_at: datetime
    product: Optional[dict] = None  # Add product information

    model_config = ConfigDict(from_attributes=True)

class SaleBase(BaseModel):
    total_amount: float = Field(..., gt=0)  # Amount before tax and discount
    payment_method: str = Field(..., min_length=1, max_length=20)
    tax_amount: float = Field(default=0.0, ge=0)  # Tax amount in currency
    discount_amount: float = Field(default=0.0, ge=0)  # Discount amount
    discount_type: str = Field(default='fixed')  # 'fixed' or 'percentage'

    customer_id: Optional[int] = None  # Link to customer
    customer_name: Optional[str] = Field(None, max_length=100)  # Keep for backward compatibility
    notes: Optional[str] = None

    # Payment fields
    amount_paid: float = Field(default=0.0, ge=0)  # Amount actually paid
    payment_status: str = Field(default='paid')  # 'paid', 'partial', 'unpaid'

    @field_validator('discount_type')
    def validate_discount_type(cls, v):
        if v not in ['fixed', 'percentage']:
            raise ValueError('discount_type must be either "fixed" or "percentage"')
        return v

    @field_validator('payment_status')
    def validate_payment_status(cls, v):
        if v not in ['paid', 'partial', 'unpaid']:
            raise ValueError('payment_status must be either "paid", "partial", or "unpaid"')
        return v

class SaleCreate(SaleBase):
    items: List[SaleItemCreate]

class SaleUpdate(BaseModel):
    total_amount: Optional[float] = Field(None, gt=0)
    payment_method: Optional[str] = Field(None, min_length=1, max_length=20)
    tax_amount: Optional[float] = Field(None, ge=0)
    discount_amount: Optional[float] = Field(None, ge=0)
    discount_type: Optional[str] = Field(None)
    customer_id: Optional[int] = None
    customer_name: Optional[str] = Field(None, max_length=100)
    notes: Optional[str] = None
    amount_paid: Optional[float] = Field(None, ge=0)
    payment_status: Optional[str] = None
    items: Optional[List[SaleItemCreate]] = None

    @field_validator('discount_type')
    def validate_discount_type(cls, v):
        if v is not None and v not in ['fixed', 'percentage']:
            raise ValueError('discount_type must be either "fixed" or "percentage"')
        return v

    @field_validator('payment_status')
    def validate_payment_status(cls, v):
        if v is not None and v not in ['paid', 'partial', 'unpaid']:
            raise ValueError('payment_status must be either "paid", "partial", or "unpaid"')
        return v

class SaleResponse(SaleBase):
    id: int
    user_id: int
    items: List[SaleItemResponse]
    created_at: datetime
    updated_at: Optional[datetime] = None
    user: Optional[dict] = None  # Add user information
    customer: Optional[dict] = None  # Add customer information

    model_config = ConfigDict(from_attributes=True)