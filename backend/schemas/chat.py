"""
Pydantic schemas لنظام المحادثة الفورية
"""

from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from models.chat_message import MessageStatus, MessageType

class MessageCreate(BaseModel):
    """Schema لإنشاء رسالة جديدة"""
    receiver_id: int = Field(..., description="معرف المستقبل")
    content: str = Field(..., min_length=1, max_length=5000, description="محتوى الرسالة")
    message_type: MessageType = Field(default=MessageType.TEXT, description="نوع الرسالة")

class MessageUpdate(BaseModel):
    """Schema لتحديث الرسالة"""
    content: Optional[str] = Field(None, min_length=1, max_length=5000, description="محتوى الرسالة الجديد")
    status: Optional[MessageStatus] = Field(None, description="حالة الرسالة")

class MessageResponse(BaseModel):
    """Schema لاستجابة الرسالة"""
    id: int
    sender_id: int
    receiver_id: int
    content: str
    message_type: MessageType
    status: MessageStatus
    is_edited: bool
    created_at: datetime
    delivered_at: Optional[datetime] = None
    read_at: Optional[datetime] = None
    sender_username: Optional[str] = None
    sender_full_name: Optional[str] = None

    class Config:
        from_attributes = True

class ConversationResponse(BaseModel):
    """Schema لاستجابة المحادثة"""
    user_id: int
    username: str
    full_name: Optional[str] = None
    is_online: bool
    last_seen: Optional[datetime] = None
    last_message: Optional[MessageResponse] = None
    unread_count: int = 0

    class Config:
        from_attributes = True

class ChatRoomCreate(BaseModel):
    """Schema لإنشاء غرفة محادثة"""
    name: str = Field(..., min_length=1, max_length=100, description="اسم الغرفة")
    description: Optional[str] = Field(None, max_length=500, description="وصف الغرفة")
    is_private: bool = Field(default=False, description="هل الغرفة خاصة")
    max_members: int = Field(default=50, ge=2, le=100, description="الحد الأقصى للأعضاء")

class ChatRoomResponse(BaseModel):
    """Schema لاستجابة غرفة المحادثة"""
    id: int
    name: str
    description: Optional[str] = None
    is_private: bool
    is_active: bool
    max_members: int
    created_by: int
    created_at: datetime
    member_count: int = 0

    class Config:
        from_attributes = True

class UserOnlineStatusResponse(BaseModel):
    """Schema لحالة اتصال المستخدم"""
    user_id: int
    username: str
    full_name: Optional[str] = None
    is_online: bool
    last_seen: Optional[datetime] = None

    class Config:
        from_attributes = True

class WebSocketMessage(BaseModel):
    """Schema لرسائل WebSocket"""
    type: str = Field(..., description="نوع الرسالة")
    data: dict = Field(default_factory=dict, description="بيانات الرسالة")
    timestamp: datetime = Field(default_factory=datetime.now, description="وقت الرسالة")

class ChatNotification(BaseModel):
    """Schema لإشعارات المحادثة"""
    type: str  # new_message, user_online, user_offline, message_read
    user_id: int
    message: Optional[str] = None
    data: dict = Field(default_factory=dict)
    timestamp: datetime = Field(default_factory=datetime.now)

class MessagesListRequest(BaseModel):
    """Schema لطلب قائمة الرسائل"""
    other_user_id: int = Field(..., description="معرف المستخدم الآخر")
    page: int = Field(default=1, ge=1, description="رقم الصفحة")
    limit: int = Field(default=50, ge=1, le=100, description="عدد الرسائل في الصفحة")
    before_message_id: Optional[int] = Field(None, description="جلب الرسائل قبل هذه الرسالة")
    load_direction: str = Field(default="newer", pattern="^(older|newer)$", description="اتجاه التحميل")

class MessagesListResponse(BaseModel):
    """Schema لاستجابة قائمة الرسائل"""
    messages: List[MessageResponse]
    total_count: int
    has_more: bool
    page: int
    limit: int

class ConversationsListResponse(BaseModel):
    """Schema لاستجابة قائمة المحادثات"""
    conversations: List[ConversationResponse]
    total_count: int

class MarkAsReadRequest(BaseModel):
    """Schema لتحديد الرسائل كمقروءة"""
    other_user_id: int = Field(..., description="معرف المستخدم الآخر")
    message_ids: Optional[List[int]] = Field(None, description="معرفات الرسائل المحددة (اختياري)")

class UserSearchRequest(BaseModel):
    """Schema للبحث عن المستخدمين"""
    query: str = Field(..., min_length=1, max_length=100, description="نص البحث")
    limit: int = Field(default=10, ge=1, le=50, description="عدد النتائج")

class UserSearchResponse(BaseModel):
    """Schema لنتائج البحث عن المستخدمين"""
    users: List[UserOnlineStatusResponse]
    total_count: int
