"""
Schemas للمستودعات
تطبق التحقق من صحة البيانات وتسلسلها
"""

from pydantic import BaseModel, Field, field_validator, model_validator
from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from enum import Enum


class MovementTypeEnum(str, Enum):
    """أنواع حركات المستودعات"""
    IN = "IN"
    OUT = "OUT"
    TRANSFER = "TRANSFER"
    ADJUSTMENT = "ADJUSTMENT"


class ReferenceTypeEnum(str, Enum):
    """أنواع المراجع للحركات"""
    PURCHASE = "PURCHASE"
    SALE = "SALE"
    TRANSFER = "TRANSFER"
    ADJUSTMENT = "ADJUSTMENT"
    RETURN = "RETURN"


class TransferStatusEnum(str, Enum):
    """حالات طلبات التحويل"""
    PENDING = "PENDING"
    APPROVED = "APPROVED"
    IN_TRANSIT = "IN_TRANSIT"
    COMPLETED = "COMPLETED"
    CANCELLED = "CANCELLED"


# Warehouse Schemas
class WarehouseBase(BaseModel):
    """Schema أساسي للمستودع"""
    name: str = Field(..., min_length=1, max_length=100, description="اسم المستودع")
    code: str = Field(..., min_length=1, max_length=20, description="كود المستودع")
    address: Optional[str] = Field(None, description="عنوان المستودع")
    phone: Optional[str] = Field(None, max_length=20, description="هاتف المستودع")
    manager_name: Optional[str] = Field(None, max_length=100, description="اسم مدير المستودع")
    email: Optional[str] = Field(None, max_length=100, description="بريد إلكتروني")
    is_main: bool = Field(False, description="هل هو المستودع الرئيسي")
    is_active: bool = Field(True, description="هل المستودع نشط")
    capacity_limit: Optional[Decimal] = Field(None, ge=0, description="الحد الأقصى للسعة")
    current_capacity: Decimal = Field(Decimal('0'), ge=0, description="السعة الحالية")

    @field_validator('email')
    def validate_email(cls, v):
        if v and '@' not in v:
            raise ValueError('البريد الإلكتروني غير صحيح')
        return v

    @field_validator('code')
    def validate_code(cls, v):
        if not v.replace('-', '').replace('_', '').isalnum():
            raise ValueError('كود المستودع يجب أن يحتوي على أحرف وأرقام فقط')
        return v.upper()


class WarehouseCreate(WarehouseBase):
    """Schema لإنشاء مستودع جديد"""
    pass


class WarehouseUpdate(BaseModel):
    """Schema لتحديث المستودع"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    code: Optional[str] = Field(None, min_length=1, max_length=20)
    address: Optional[str] = None
    phone: Optional[str] = Field(None, max_length=20)
    manager_name: Optional[str] = Field(None, max_length=100)
    email: Optional[str] = Field(None, max_length=100)
    is_main: Optional[bool] = None
    is_active: Optional[bool] = None
    capacity_limit: Optional[Decimal] = Field(None, ge=0)
    current_capacity: Optional[Decimal] = Field(None, ge=0)

    @field_validator('email')
    def validate_email(cls, v):
        if v and '@' not in v:
            raise ValueError('البريد الإلكتروني غير صحيح')
        return v

    @field_validator('code')
    def validate_code(cls, v):
        if v and not v.replace('-', '').replace('_', '').isalnum():
            raise ValueError('كود المستودع يجب أن يحتوي على أحرف وأرقام فقط')
        return v.upper() if v else v


class WarehouseResponse(WarehouseBase):
    """Schema لاستجابة المستودع"""
    id: int
    created_at: Optional[datetime]
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


# Warehouse Inventory Schemas
class WarehouseInventoryBase(BaseModel):
    """Schema أساسي لمخزون المستودع"""
    warehouse_id: int = Field(..., gt=0, description="معرف المستودع")
    product_id: int = Field(..., gt=0, description="معرف المنتج")
    quantity: Decimal = Field(Decimal('0'), ge=0, description="الكمية")
    reserved_quantity: Decimal = Field(Decimal('0'), ge=0, description="الكمية المحجوزة")
    min_stock_level: Decimal = Field(Decimal('0'), ge=0, description="الحد الأدنى للمخزون")
    max_stock_level: Optional[Decimal] = Field(None, ge=0, description="الحد الأقصى للمخزون")
    location_code: Optional[str] = Field(None, max_length=50, description="كود الموقع")

    @model_validator(mode='after')
    def validate_stock_levels(self):
        if (self.max_stock_level is not None and
            self.min_stock_level is not None and
            self.max_stock_level < self.min_stock_level):
            raise ValueError('الحد الأقصى يجب أن يكون أكبر من الحد الأدنى')
        return self


class WarehouseInventoryCreate(WarehouseInventoryBase):
    """Schema لإنشاء مخزون مستودع جديد"""
    pass


class WarehouseInventoryUpdate(BaseModel):
    """Schema لتحديث مخزون المستودع"""
    quantity: Optional[Decimal] = Field(None, ge=0)
    reserved_quantity: Optional[Decimal] = Field(None, ge=0)
    min_stock_level: Optional[Decimal] = Field(None, ge=0)
    max_stock_level: Optional[Decimal] = Field(None, ge=0)
    location_code: Optional[str] = Field(None, max_length=50)


class WarehouseInventoryResponse(WarehouseInventoryBase):
    """Schema لاستجابة مخزون المستودع"""
    id: int
    last_updated: Optional[datetime]
    available_quantity: Optional[Decimal] = None
    stock_status: Optional[str] = None

    class Config:
        from_attributes = True


# Warehouse Movement Schemas
class WarehouseMovementBase(BaseModel):
    """Schema أساسي لحركة المستودع"""
    movement_type: MovementTypeEnum = Field(..., description="نوع الحركة")
    from_warehouse_id: Optional[int] = Field(None, gt=0, description="معرف المستودع المصدر")
    to_warehouse_id: Optional[int] = Field(None, gt=0, description="معرف المستودع الوجهة")
    product_id: int = Field(..., gt=0, description="معرف المنتج")
    quantity: Decimal = Field(..., gt=0, description="الكمية")
    unit_cost: Optional[Decimal] = Field(None, ge=0, description="تكلفة الوحدة")
    total_cost: Optional[Decimal] = Field(None, ge=0, description="التكلفة الإجمالية")
    reference_type: Optional[ReferenceTypeEnum] = Field(None, description="نوع المرجع")
    reference_id: Optional[int] = Field(None, gt=0, description="معرف المرجع")
    notes: Optional[str] = Field(None, description="ملاحظات")
    created_by: Optional[int] = Field(None, gt=0, description="معرف المنشئ")

    @model_validator(mode='after')
    def validate_warehouses(self):
        movement_type = self.movement_type

        if movement_type == MovementTypeEnum.IN and not self.to_warehouse_id:
            raise ValueError('مستودع الوجهة مطلوب لحركة الدخول')
        elif movement_type == MovementTypeEnum.OUT and not self.from_warehouse_id:
            raise ValueError('مستودع المصدر مطلوب لحركة الخروج')
        elif movement_type == MovementTypeEnum.TRANSFER:
            if not self.from_warehouse_id:
                raise ValueError('مستودع المصدر مطلوب لحركة التحويل')
            elif not self.to_warehouse_id:
                raise ValueError('مستودع الوجهة مطلوب لحركة التحويل')
            elif self.from_warehouse_id == self.to_warehouse_id:
                raise ValueError('لا يمكن التحويل من وإلى نفس المستودع')

        return self


class WarehouseMovementCreate(WarehouseMovementBase):
    """Schema لإنشاء حركة مستودع جديدة"""
    pass


class WarehouseMovementResponse(WarehouseMovementBase):
    """Schema لاستجابة حركة المستودع"""
    id: int
    created_at: Optional[datetime]
    direction: Optional[str] = None  # 'in' or 'out' بالنسبة للمستودع
    product_name: Optional[str] = None
    product_barcode: Optional[str] = None
    created_by_username: Optional[str] = None

    class Config:
        from_attributes = True


# Transfer Request Schemas
class TransferRequestItemBase(BaseModel):
    """Schema أساسي لعنصر طلب التحويل"""
    product_id: int = Field(..., gt=0, description="معرف المنتج")
    requested_quantity: Decimal = Field(..., gt=0, description="الكمية المطلوبة")
    unit_cost: Optional[Decimal] = Field(None, ge=0, description="تكلفة الوحدة")
    notes: Optional[str] = Field(None, description="ملاحظات")


class TransferRequestItemCreate(TransferRequestItemBase):
    """Schema لإنشاء عنصر طلب تحويل جديد"""
    pass


class TransferRequestItemResponse(TransferRequestItemBase):
    """Schema لاستجابة عنصر طلب التحويل"""
    id: int
    transfer_request_id: int
    approved_quantity: Optional[Decimal] = None
    transferred_quantity: Decimal = Decimal('0')
    product_name: Optional[str] = None
    product_barcode: Optional[str] = None

    class Config:
        from_attributes = True


class TransferRequestBase(BaseModel):
    """Schema أساسي لطلب التحويل"""
    from_warehouse_id: int = Field(..., gt=0, description="معرف المستودع المصدر")
    to_warehouse_id: int = Field(..., gt=0, description="معرف المستودع الوجهة")
    requested_by: int = Field(..., gt=0, description="معرف الطالب")
    notes: Optional[str] = Field(None, description="ملاحظات")

    @model_validator(mode='after')
    def validate_different_warehouses(self):
        if self.from_warehouse_id == self.to_warehouse_id:
            raise ValueError('لا يمكن التحويل من وإلى نفس المستودع')
        return self


class TransferRequestCreate(TransferRequestBase):
    """Schema لإنشاء طلب تحويل جديد"""
    items: List[TransferRequestItemCreate] = Field(..., min_items=1, description="عناصر الطلب")


class TransferRequestUpdate(BaseModel):
    """Schema لتحديث طلب التحويل"""
    notes: Optional[str] = None


class TransferRequestResponse(TransferRequestBase):
    """Schema لاستجابة طلب التحويل"""
    id: int
    request_number: str
    status: TransferStatusEnum
    approved_by: Optional[int] = None
    requested_at: Optional[datetime]
    approved_at: Optional[datetime]
    completed_at: Optional[datetime]
    items: Optional[List[TransferRequestItemResponse]] = None
    from_warehouse_name: Optional[str] = None
    from_warehouse_code: Optional[str] = None
    to_warehouse_name: Optional[str] = None
    to_warehouse_code: Optional[str] = None
    requested_by_username: Optional[str] = None
    approved_by_username: Optional[str] = None
    items_count: Optional[int] = None

    class Config:
        from_attributes = True


# Filter Schemas
class WarehouseInventoryFilters(BaseModel):
    """فلاتر مخزون المستودع"""
    low_stock_only: Optional[bool] = False
    search: Optional[str] = None
    min_quantity: Optional[Decimal] = None
    max_quantity: Optional[Decimal] = None


class WarehouseMovementFilters(BaseModel):
    """فلاتر حركات المستودع"""
    movement_type: Optional[MovementTypeEnum] = None
    product_id: Optional[int] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    reference_type: Optional[ReferenceTypeEnum] = None
    page: int = Field(1, ge=1)
    per_page: int = Field(50, ge=1, le=100)


class TransferRequestFilters(BaseModel):
    """فلاتر طلبات التحويل"""
    status: Optional[TransferStatusEnum] = None
    from_warehouse_id: Optional[int] = None
    to_warehouse_id: Optional[int] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    page: int = Field(1, ge=1)
    per_page: int = Field(20, ge=1, le=100)


# Stock Adjustment Schema
class StockAdjustmentRequest(BaseModel):
    """Schema لطلب تعديل المخزون"""
    warehouse_id: int = Field(..., gt=0, description="معرف المستودع")
    product_id: int = Field(..., gt=0, description="معرف المنتج")
    new_quantity: Decimal = Field(..., ge=0, description="الكمية الجديدة")
    reason: str = Field(..., min_length=1, description="سبب التعديل")
    created_by: Optional[int] = Field(None, gt=0, description="معرف المنشئ")


# Approval Schema
class TransferApprovalRequest(BaseModel):
    """Schema لطلب الموافقة على التحويل"""
    items: Optional[List[dict]] = None  # قائمة بالعناصر والكميات المعتمدة
