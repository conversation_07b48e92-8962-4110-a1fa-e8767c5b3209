from pydantic import BaseModel, Field, ConfigDict, field_validator
from typing import Optional, List
from datetime import datetime

class CustomerBase(BaseModel):
    """Base schema for customer data."""
    name: str = Field(..., min_length=1, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    email: Optional[str] = Field(None, max_length=100)
    address: Optional[str] = None
    notes: Optional[str] = None
    image_url: Optional[str] = Field(None, max_length=255)  # For customer profile image
    is_active: bool = True

    @field_validator('email')
    def validate_email(cls, v):
        if v and '@' not in v:
            raise ValueError('Invalid email format')
        return v

class CustomerCreate(CustomerBase):
    """Schema for creating a new customer."""
    pass

class CustomerUpdate(BaseModel):
    """Schema for updating an existing customer."""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    email: Optional[str] = Field(None, max_length=100)
    address: Optional[str] = None
    notes: Optional[str] = None
    image_url: Optional[str] = Field(None, max_length=255)  # For customer profile image
    is_active: Optional[bool] = None

    @field_validator('email')
    def validate_email(cls, v):
        if v and '@' not in v:
            raise ValueError('Invalid email format')
        return v

class CustomerResponse(CustomerBase):
    """Schema for customer response."""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    total_debt: float = 0.0
    total_sales: float = 0.0

    model_config = ConfigDict(from_attributes=True)

# Debt Schemas
class DebtBase(BaseModel):
    """Base schema for debt data."""
    customer_id: int
    amount: float = Field(..., gt=0)
    description: Optional[str] = None

class DebtCreate(DebtBase):
    """Schema for creating a new debt."""
    sale_id: Optional[int] = None

class DebtUpdate(BaseModel):
    """Schema for updating an existing debt."""
    amount: Optional[float] = Field(None, gt=0)
    description: Optional[str] = None
    is_paid: Optional[bool] = None
    payment_status: Optional[str] = Field(None, pattern="^(unpaid|partial|paid)$")

# Customer schema for debt response
class CustomerInDebt(BaseModel):
    """Schema for customer data in debt response."""
    id: int
    name: str
    phone: Optional[str] = None
    email: Optional[str] = None
    image_url: Optional[str] = None

class DebtResponse(DebtBase):
    """Schema for debt response."""
    id: int
    sale_id: Optional[int] = None
    remaining_amount: float
    is_paid: bool
    payment_status: str = 'unpaid'
    created_at: datetime
    updated_at: Optional[datetime] = None
    customer: Optional[CustomerInDebt] = None
    sale: Optional[dict] = None
    payments: List[dict] = []

    model_config = ConfigDict(from_attributes=True)

# Payment Schemas
class PaymentBase(BaseModel):
    """Base schema for payment data."""
    debt_id: int
    invoice_id: Optional[int] = None  # Link to specific invoice
    amount: float = Field(..., gt=0)
    payment_method: str = Field(default='cash', max_length=20)
    notes: Optional[str] = None

class PaymentCreate(PaymentBase):
    """Schema for creating a new payment."""
    pass

class PaymentUpdate(BaseModel):
    """Schema for updating an existing payment."""
    invoice_id: Optional[int] = None
    amount: Optional[float] = Field(None, gt=0)
    payment_method: Optional[str] = Field(None, max_length=20)
    notes: Optional[str] = None

class PaymentResponse(PaymentBase):
    """Schema for payment response."""
    id: int
    created_at: datetime
    debt: Optional[dict] = None

    model_config = ConfigDict(from_attributes=True)
