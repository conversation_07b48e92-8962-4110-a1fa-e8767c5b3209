from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, ConfigDict


class VariantValueBase(BaseModel):
    """Base schema for variant value data."""
    value: str = Field(..., min_length=1, max_length=100)
    value_ar: str = Field(..., min_length=1, max_length=100)
    color_code: Optional[str] = Field(None, max_length=7, pattern=r'^#[0-9A-Fa-f]{6}$')
    is_active: bool = True
    sort_order: int = Field(default=0, ge=0)


class VariantValueCreate(VariantValueBase):
    """Schema for creating a new variant value."""
    pass


class VariantValueUpdate(BaseModel):
    """Schema for updating an existing variant value."""
    value: Optional[str] = Field(None, min_length=1, max_length=100)
    value_ar: Optional[str] = Field(None, min_length=1, max_length=100)
    color_code: Optional[str] = Field(None, max_length=7, pattern=r'^#[0-9A-Fa-f]{6}$')
    is_active: Optional[bool] = None
    sort_order: Optional[int] = Field(None, ge=0)


class VariantValueInDB(VariantValueBase):
    """Schema for variant value data as stored in database."""
    id: int
    attribute_id: int
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)


class VariantValueResponse(VariantValueInDB):
    """Schema for variant value response."""
    pass


class VariantAttributeBase(BaseModel):
    """Base schema for variant attribute data."""
    name: str = Field(..., min_length=1, max_length=100)
    name_ar: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    attribute_type: str = Field(default='text', pattern=r'^(text|color|list|number)$')
    is_required: bool = False
    is_active: bool = True
    sort_order: int = Field(default=0, ge=0)


class VariantAttributeCreate(VariantAttributeBase):
    """Schema for creating a new variant attribute."""
    values: Optional[List[VariantValueCreate]] = []


class VariantAttributeUpdate(BaseModel):
    """Schema for updating an existing variant attribute."""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    name_ar: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    attribute_type: Optional[str] = Field(None, pattern=r'^(text|color|list|number)$')
    is_required: Optional[bool] = None
    is_active: Optional[bool] = None
    sort_order: Optional[int] = Field(None, ge=0)


class VariantAttributeInDB(VariantAttributeBase):
    """Schema for variant attribute data as stored in database."""
    id: int
    created_at: datetime
    updated_at: Optional[datetime]
    created_by: int
    updated_by: Optional[int]

    model_config = ConfigDict(from_attributes=True)


class VariantAttributeResponse(VariantAttributeInDB):
    """Schema for variant attribute response including values."""
    values: List[VariantValueResponse] = []


class AttributeOrderUpdate(BaseModel):
    """Schema for updating attribute order."""
    id: int
    sort_order: int = Field(..., ge=0)


class ValueOrderUpdate(BaseModel):
    """Schema for updating value order."""
    id: int
    sort_order: int = Field(..., ge=0)
