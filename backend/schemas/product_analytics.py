from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime

class ProductSalesAnalytics(BaseModel):
    """Schema for product sales analytics."""
    id: int
    name: str
    category: Optional[str]
    total_sold: int
    total_revenue: float
    total_profit: float
    profit_margin: float
    last_sale_date: Optional[datetime]
    days_since_last_sale: Optional[int]
    average_daily_sales: float
    stock_turnover_rate: float
    current_stock: int
    stock_value: float

class BestSellingProduct(BaseModel):
    """Schema for best selling products."""
    id: int
    name: str
    barcode: Optional[str]
    category: Optional[str]
    total_sold: int
    total_revenue: float
    total_profit: float
    profit_margin: float
    sales_rank: int
    percentage_of_total_sales: float

class UnsoldProduct(BaseModel):
    """Schema for unsold products."""
    id: int
    name: str
    barcode: Optional[str]
    category: Optional[str]
    current_stock: int
    stock_value: float
    cost_value: float
    days_in_stock: int
    potential_loss: float
    last_updated: datetime

class ProductPerformance(BaseModel):
    """Schema for product performance analysis."""
    id: int
    name: str
    barcode: Optional[str]
    category: Optional[str]
    sales_trend: str  # 'increasing', 'decreasing', 'stable', 'no_sales'
    performance_score: float
    total_sold: int
    total_revenue: float
    total_profit: float
    stock_status: str  # 'healthy', 'low', 'overstocked', 'out_of_stock'
    recommendation: str

class ExpectedLoss(BaseModel):
    """Schema for expected losses calculation."""
    product_id: int
    product_name: str
    product_barcode: Optional[str]
    category: Optional[str]
    current_stock: int
    days_without_sales: int
    estimated_loss_amount: float
    loss_category: str  # 'high_risk', 'medium_risk', 'low_risk'
    recommendation: str

class InventoryStatus(BaseModel):
    """Schema for inventory status."""
    product_id: int
    product_name: str
    product_barcode: Optional[str]
    category: Optional[str]
    current_stock: int
    min_quantity: int
    max_recommended: int
    stock_status: str
    days_of_supply: float
    reorder_point: int
    suggested_order_quantity: int

class ProductAnalyticsSummary(BaseModel):
    """Schema for product analytics summary."""
    total_products: int
    active_products: int
    products_with_sales: int
    products_without_sales: int
    total_stock_value: float
    total_cost_value: float
    average_profit_margin: float
    top_performing_category: Optional[str]
    worst_performing_category: Optional[str]
    total_potential_losses: float

class AvailablePeriod(BaseModel):
    """Schema for available analysis periods."""
    period_days: int
    period_label: str
    sales_count: int
    products_count: int
    total_amount: float
    start_date: str
    end_date: str
    has_data: bool

class ProductAnalyticsResponse(BaseModel):
    """Complete product analytics response."""
    summary: ProductAnalyticsSummary
    best_selling: List[BestSellingProduct]
    unsold_products: List[UnsoldProduct]
    performance_analysis: List[ProductPerformance]
    expected_losses: List[ExpectedLoss]
    inventory_status: List[InventoryStatus]
    period_days: int
    generated_at: datetime

class ProductTrendData(BaseModel):
    """Schema for product trend data."""
    date: str
    sales_count: int
    revenue: float
    profit: float

class ProductDetailedAnalytics(BaseModel):
    """Schema for detailed product analytics."""
    product: ProductSalesAnalytics
    sales_trend: List[ProductTrendData]
    monthly_performance: List[ProductTrendData]
    category_comparison: List[dict]
    recommendations: List[str]
