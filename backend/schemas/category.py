from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, ConfigDict

# Category Schemas
class CategoryBase(BaseModel):
    """Base schema for category data."""
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    image_url: Optional[str] = Field(None, max_length=255)
    is_active: bool = True

class CategoryCreate(CategoryBase):
    """Schema for creating a new category."""
    pass

class CategoryUpdate(BaseModel):
    """Schema for updating an existing category."""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    image_url: Optional[str] = Field(None, max_length=255)
    is_active: Optional[bool] = None

class CategoryInDB(CategoryBase):
    """Schema for category data as stored in database."""
    id: int
    created_at: datetime
    updated_at: Optional[datetime]
    created_by: int
    updated_by: Optional[int]

    model_config = ConfigDict(from_attributes=True)

class CategoryResponse(CategoryInDB):
    """Schema for category response."""
    pass

# Subcategory Schemas
class SubcategoryBase(BaseModel):
    """Base schema for subcategory data."""
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    category_id: int
    is_active: bool = True

class SubcategoryCreate(BaseModel):
    """Schema for creating a new subcategory."""
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    is_active: bool = True

class SubcategoryUpdate(BaseModel):
    """Schema for updating an existing subcategory."""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    category_id: Optional[int] = None
    is_active: Optional[bool] = None

class SubcategoryInDB(SubcategoryBase):
    """Schema for subcategory data as stored in database."""
    id: int
    created_at: datetime
    updated_at: Optional[datetime]
    created_by: int
    updated_by: Optional[int]

    model_config = ConfigDict(from_attributes=True)

class SubcategoryResponse(SubcategoryInDB):
    """Schema for subcategory response."""
    category: Optional[CategoryResponse] = None

# Category with subcategories
class CategoryWithSubcategories(CategoryResponse):
    """Schema for category response with subcategories."""
    subcategories: List[SubcategoryResponse] = []
