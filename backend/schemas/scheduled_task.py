from pydantic import BaseModel, Field, ConfigDict, validator
from typing import Optional, Dict, Any
from datetime import datetime
from models.scheduled_task import TaskType, TaskStatus
import json

class ScheduledTaskBase(BaseModel):
    """النموذج الأساسي للمهام المجدولة"""
    name: str = Field(..., min_length=1, max_length=100, description="اسم المهمة")
    description: Optional[str] = Field(None, description="وصف المهمة")
    task_type: TaskType = Field(..., description="نوع المهمة")
    cron_expression: str = Field(..., min_length=1, max_length=100, description="تعبير Cron للجدولة")
    status: TaskStatus = Field(default=TaskStatus.ACTIVE, description="حالة المهمة")
    task_params: Optional[Dict[str, Any]] = Field(None, description="معاملات المهمة")
    max_retries: int = Field(default=3, ge=0, le=10, description="عدد المحاولات القصوى")
    timeout_seconds: int = Field(default=300, ge=30, le=3600, description="مهلة التنفيذ بالثواني")

    @validator('cron_expression')
    def validate_cron_expression(cls, v):
        """التحقق من صحة تعبير Cron"""
        if not v or not v.strip():
            raise ValueError('تعبير Cron مطلوب')

        # التحقق الأساسي من تعبير Cron (5 أو 6 أجزاء)
        parts = v.strip().split()
        if len(parts) not in [5, 6]:
            raise ValueError('تعبير Cron يجب أن يحتوي على 5 أو 6 أجزاء')

        return v.strip()

class ScheduledTaskCreate(ScheduledTaskBase):
    """نموذج إنشاء مهمة مجدولة"""
    pass

class ScheduledTaskUpdate(BaseModel):
    """نموذج تحديث مهمة مجدولة"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    cron_expression: Optional[str] = Field(None, min_length=1, max_length=100)
    status: Optional[TaskStatus] = None
    task_params: Optional[Dict[str, Any]] = None
    max_retries: Optional[int] = Field(None, ge=0, le=10)
    timeout_seconds: Optional[int] = Field(None, ge=30, le=3600)

    @validator('cron_expression')
    def validate_cron_expression(cls, v):
        """التحقق من صحة تعبير Cron"""
        if v is not None:
            if not v or not v.strip():
                raise ValueError('تعبير Cron مطلوب')

            parts = v.strip().split()
            if len(parts) not in [5, 6]:
                raise ValueError('تعبير Cron يجب أن يحتوي على 5 أو 6 أجزاء')

            return v.strip()
        return v

class ScheduledTaskResponse(ScheduledTaskBase):
    """نموذج استجابة المهمة المجدولة"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    last_run: Optional[datetime] = None
    next_run: Optional[datetime] = None
    run_count: int = 0
    failure_count: int = 0
    last_error: Optional[str] = None
    is_system_task: bool = False
    created_by: Optional[int] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    @validator('task_params', pre=True)
    def parse_task_params(cls, v):
        """تحويل JSON string إلى dictionary"""
        if v is None:
            return None
        if isinstance(v, str):
            try:
                return json.loads(v)
            except (json.JSONDecodeError, ValueError):
                return None
        return v

class TaskExecutionResult(BaseModel):
    """نتيجة تنفيذ المهمة"""
    success: bool
    message: str
    execution_time: float
    error: Optional[str] = None
    details: Optional[Dict[str, Any]] = None

class CronExpressionBuilder(BaseModel):
    """مساعد لبناء تعبيرات Cron"""
    minute: str = Field(default="0", description="الدقيقة (0-59)")
    hour: str = Field(default="*", description="الساعة (0-23)")
    day: str = Field(default="*", description="اليوم من الشهر (1-31)")
    month: str = Field(default="*", description="الشهر (1-12)")
    day_of_week: str = Field(default="*", description="يوم الأسبوع (0-6)")

    def to_cron_expression(self) -> str:
        """تحويل إلى تعبير Cron"""
        return f"{self.minute} {self.hour} {self.day} {self.month} {self.day_of_week}"

class ScheduledTaskStats(BaseModel):
    """إحصائيات المهام المجدولة"""
    total_tasks: int
    active_tasks: int
    paused_tasks: int
    disabled_tasks: int
    failed_tasks: int
    successful_runs_today: int
    failed_runs_today: int
