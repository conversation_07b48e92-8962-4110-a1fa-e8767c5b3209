from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, ConfigDict, HttpUrl

class BrandBase(BaseModel):
    """Base schema for brand data."""
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    logo_url: Optional[str] = Field(None, max_length=255)
    website: Optional[str] = Field(None, max_length=255)
    contact_info: Optional[str] = None
    is_active: bool = True

class BrandCreate(BrandBase):
    """Schema for creating a new brand."""
    pass

class BrandUpdate(BaseModel):
    """Schema for updating an existing brand."""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    logo_url: Optional[str] = Field(None, max_length=255)
    website: Optional[str] = Field(None, max_length=255)
    contact_info: Optional[str] = None
    is_active: Optional[bool] = None

class BrandInDB(BrandBase):
    """Schema for brand data as stored in database."""
    id: int
    created_at: datetime
    updated_at: Optional[datetime]
    created_by: int
    updated_by: Optional[int]

    model_config = ConfigDict(from_attributes=True)

class BrandResponse(BrandInDB):
    """Schema for brand response."""
    pass
