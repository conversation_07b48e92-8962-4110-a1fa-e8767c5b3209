"""
مخططات Pydantic للفروع - Branch Schemas
تحدد هيكل البيانات للفروع في API
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime
from uuid import UUID


class BranchBase(BaseModel):
    """المخطط الأساسي للفرع"""
    name: str = Field(..., min_length=1, max_length=100, description="اسم الفرع")
    code: Optional[str] = Field(None, max_length=20, description="كود الفرع (اختياري للعرض)")
    address: Optional[str] = Field(None, max_length=500, description="عنوان الفرع")
    phone: Optional[str] = Field(None, max_length=20, description="رقم هاتف الفرع")
    manager_name: Optional[str] = Field(None, max_length=100, description="اسم مدير الفرع")
    email: Optional[str] = Field(None, max_length=100, description="البريد الإلكتروني للفرع")
    is_active: bool = Field(True, description="حالة نشاط الفرع")
    is_main: bool = Field(False, description="هل هذا الفرع الرئيسي")
    
    # معلومات إضافية
    city: Optional[str] = Field(None, max_length=50, description="المدينة")
    region: Optional[str] = Field(None, max_length=50, description="المنطقة")
    postal_code: Optional[str] = Field(None, max_length=20, description="الرمز البريدي")
    
    # إعدادات الفرع
    max_daily_sales: Optional[int] = Field(None, ge=0, description="الحد الأقصى للمبيعات اليومية")
    working_hours_start: Optional[str] = Field(None, max_length=10, description="بداية ساعات العمل (HH:MM)")
    working_hours_end: Optional[str] = Field(None, max_length=10, description="نهاية ساعات العمل (HH:MM)")

    @validator('email')
    def validate_email(cls, v):
        if v and '@' not in v:
            raise ValueError('البريد الإلكتروني غير صحيح')
        return v

    @validator('working_hours_start', 'working_hours_end')
    def validate_time_format(cls, v):
        if v:
            try:
                # التحقق من صيغة الوقت HH:MM
                parts = v.split(':')
                if len(parts) != 2:
                    raise ValueError('صيغة الوقت يجب أن تكون HH:MM')
                
                hour, minute = int(parts[0]), int(parts[1])
                if not (0 <= hour <= 23) or not (0 <= minute <= 59):
                    raise ValueError('الوقت غير صحيح')
            except (ValueError, IndexError):
                raise ValueError('صيغة الوقت يجب أن تكون HH:MM')
        return v


class BranchCreate(BranchBase):
    """مخطط إنشاء فرع جديد"""
    uuid: Optional[UUID] = Field(None, description="المعرف الفريد للفرع (سيتم توليده تلقائياً إذا لم يتم تمريره)")


class BranchUpdate(BaseModel):
    """مخطط تحديث الفرع"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="اسم الفرع")
    code: Optional[str] = Field(None, max_length=20, description="كود الفرع (اختياري للعرض)")
    address: Optional[str] = Field(None, max_length=500, description="عنوان الفرع")
    phone: Optional[str] = Field(None, max_length=20, description="رقم هاتف الفرع")
    manager_name: Optional[str] = Field(None, max_length=100, description="اسم مدير الفرع")
    email: Optional[str] = Field(None, max_length=100, description="البريد الإلكتروني للفرع")
    is_active: Optional[bool] = Field(None, description="حالة نشاط الفرع")
    is_main: Optional[bool] = Field(None, description="هل هذا الفرع الرئيسي")
    
    # معلومات إضافية
    city: Optional[str] = Field(None, max_length=50, description="المدينة")
    region: Optional[str] = Field(None, max_length=50, description="المنطقة")
    postal_code: Optional[str] = Field(None, max_length=20, description="الرمز البريدي")
    
    # إعدادات الفرع
    max_daily_sales: Optional[int] = Field(None, ge=0, description="الحد الأقصى للمبيعات اليومية")
    working_hours_start: Optional[str] = Field(None, max_length=10, description="بداية ساعات العمل (HH:MM)")
    working_hours_end: Optional[str] = Field(None, max_length=10, description="نهاية ساعات العمل (HH:MM)")

    @validator('email')
    def validate_email(cls, v):
        if v and '@' not in v:
            raise ValueError('البريد الإلكتروني غير صحيح')
        return v

    @validator('working_hours_start', 'working_hours_end')
    def validate_time_format(cls, v):
        if v:
            try:
                # التحقق من صيغة الوقت HH:MM
                parts = v.split(':')
                if len(parts) != 2:
                    raise ValueError('صيغة الوقت يجب أن تكون HH:MM')
                
                hour, minute = int(parts[0]), int(parts[1])
                if not (0 <= hour <= 23) or not (0 <= minute <= 59):
                    raise ValueError('الوقت غير صحيح')
            except (ValueError, IndexError):
                raise ValueError('صيغة الوقت يجب أن تكون HH:MM')
        return v


class WarehouseInBranch(BaseModel):
    """مخطط المستودع في الفرع"""
    id: int
    name: str
    code: str
    is_main: bool
    is_active: bool
    is_primary: Optional[bool] = Field(None, description="هل هذا المستودع الأساسي للفرع")
    priority: Optional[int] = Field(None, description="أولوية المستودع للفرع")
    capacity_limit: Optional[float] = None
    current_capacity: Optional[float] = None
    capacity_percentage: Optional[float] = None
    link_created_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class BranchResponse(BranchBase):
    """مخطط استجابة الفرع"""
    id: int
    uuid: UUID
    warehouses_count: Optional[int] = Field(0, description="عدد المستودعات المرتبطة")
    active_warehouses_count: Optional[int] = Field(0, description="عدد المستودعات النشطة المرتبطة")
    warehouses: Optional[List[WarehouseInBranch]] = Field([], description="قائمة المستودعات المرتبطة")
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    created_by: Optional[int] = None
    updated_by: Optional[int] = None

    class Config:
        from_attributes = True


class BranchListResponse(BaseModel):
    """مخطط استجابة قائمة الفروع"""
    id: int
    name: str
    code: str
    address: Optional[str] = None
    phone: Optional[str] = None
    manager_name: Optional[str] = None
    email: Optional[str] = None
    is_active: bool
    is_main: bool
    city: Optional[str] = None
    region: Optional[str] = None
    warehouses_count: Optional[int] = 0
    active_warehouses_count: Optional[int] = 0
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# مخططات العلاقات بين الفروع والمستودعات

class BranchWarehouseLinkCreate(BaseModel):
    """مخطط ربط فرع بمستودع"""
    branch_id: int = Field(..., description="معرف الفرع")
    warehouse_id: int = Field(..., description="معرف المستودع")
    is_primary: bool = Field(False, description="هل هذا المستودع الأساسي للفرع")
    priority: int = Field(1, ge=1, le=100, description="أولوية المستودع (1 = أعلى أولوية)")


class BranchWarehouseLinkUpdate(BaseModel):
    """مخطط تحديث ربط فرع بمستودع"""
    is_primary: Optional[bool] = Field(None, description="هل هذا المستودع الأساسي للفرع")
    priority: Optional[int] = Field(None, ge=1, le=100, description="أولوية المستودع (1 = أعلى أولوية)")


class BranchWarehouseLinkResponse(BaseModel):
    """مخطط استجابة ربط فرع بمستودع"""
    branch_id: int
    warehouse_id: int
    is_primary: bool
    priority: int
    link_created_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class BranchInWarehouse(BaseModel):
    """مخطط الفرع في المستودع"""
    id: int
    name: str
    code: str
    address: Optional[str] = None
    manager_name: Optional[str] = None
    is_main: bool
    is_active: bool
    city: Optional[str] = None
    region: Optional[str] = None
    is_primary_warehouse: Optional[bool] = Field(None, description="هل هذا المستودع أساسي للفرع")
    warehouse_priority: Optional[int] = Field(None, description="أولوية المستودع للفرع")
    link_created_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# مخططات البحث والفلترة

class BranchSearchParams(BaseModel):
    """مخطط معاملات البحث في الفروع"""
    search_term: Optional[str] = Field(None, description="مصطلح البحث")
    include_inactive: bool = Field(False, description="تضمين الفروع غير النشطة")
    city: Optional[str] = Field(None, description="فلترة حسب المدينة")
    region: Optional[str] = Field(None, description="فلترة حسب المنطقة")
    is_main: Optional[bool] = Field(None, description="فلترة حسب الفرع الرئيسي")


class BranchStatistics(BaseModel):
    """مخطط إحصائيات الفرع"""
    total_warehouses: int = 0
    active_warehouses: int = 0
    total_sales: Optional[int] = 0
    total_sales_amount: Optional[float] = 0.0
    primary_warehouse_id: Optional[int] = None
    primary_warehouse_name: Optional[str] = None

    class Config:
        from_attributes = True
