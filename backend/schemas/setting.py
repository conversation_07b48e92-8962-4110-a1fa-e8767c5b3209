from pydantic import BaseModel, Field, ConfigDict
from typing import Optional
from datetime import datetime

class SettingBase(BaseModel):
    key: str = Field(..., min_length=1, max_length=50)
    value: str
    description: Optional[str] = None

class SettingCreate(SettingBase):
    pass

class SettingUpdate(BaseModel):
    value: str
    description: Optional[str] = None

class SettingResponse(SettingBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True) 