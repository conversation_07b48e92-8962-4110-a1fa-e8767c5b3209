"""
مخططات البيانات لنظام تدفق البيانات الكبيرة
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Union
from datetime import datetime, date
from enum import Enum

class StreamFormat(str, Enum):
    """أنواع تنسيقات التدفق المدعومة"""
    JSON = "json"
    CSV = "csv"
    XML = "xml"

class CompressionType(str, Enum):
    """أنواع الضغط المدعومة"""
    NONE = "none"
    GZIP = "gzip"
    ZIP = "zip"

class DataTable(str, Enum):
    """الجداول المتاحة للتصدير"""
    SALES = "sales"
    PRODUCTS = "products"
    CUSTOMERS = "customers"
    USERS = "users"
    DEBTS = "debts"

class AnalysisType(str, Enum):
    """أنواع التحليلات المتاحة"""
    SALES = "sales"
    PRODUCTS = "products"
    CUSTOMERS = "customers"
    INVENTORY = "inventory"
    FINANCIAL = "financial"

class StreamingRequest(BaseModel):
    """طلب تدفق البيانات الأساسي"""
    format_type: StreamFormat = Field(default=StreamFormat.JSON, description="نوع التنسيق")
    compress: bool = Field(default=False, description="ضغط البيانات")
    compression_type: CompressionType = Field(default=CompressionType.GZIP, description="نوع الضغط")
    chunk_size: int = Field(default=1000, ge=100, le=10000, description="حجم الدفعة")
    start_date: Optional[date] = Field(None, description="تاريخ البداية")
    end_date: Optional[date] = Field(None, description="تاريخ النهاية")

class SalesStreamRequest(StreamingRequest):
    """طلب تدفق بيانات المبيعات"""
    user_id: Optional[int] = Field(None, description="معرف المستخدم")
    customer_id: Optional[int] = Field(None, description="معرف العميل")
    payment_method: Optional[str] = Field(None, description="طريقة الدفع")
    min_amount: Optional[float] = Field(None, ge=0, description="الحد الأدنى للمبلغ")
    max_amount: Optional[float] = Field(None, ge=0, description="الحد الأقصى للمبلغ")
    include_items: bool = Field(default=True, description="تضمين عناصر البيع")

class ProductsStreamRequest(StreamingRequest):
    """طلب تدفق بيانات المنتجات"""
    category: Optional[str] = Field(None, description="فئة المنتج")
    low_stock: bool = Field(default=False, description="المنتجات قليلة المخزون فقط")
    zero_stock: bool = Field(default=False, description="المنتجات نافدة المخزون فقط")
    is_active: Optional[bool] = Field(None, description="حالة النشاط")
    include_analytics: bool = Field(default=True, description="تضمين التحليلات")

class CustomersStreamRequest(StreamingRequest):
    """طلب تدفق بيانات العملاء"""
    with_debts: bool = Field(default=False, description="تضمين بيانات الديون")
    active_only: bool = Field(default=False, description="العملاء النشطين فقط")
    has_purchases: bool = Field(default=False, description="العملاء الذين لديهم مشتريات فقط")

class AnalyticsStreamRequest(StreamingRequest):
    """طلب تدفق بيانات التحليلات"""
    analysis_type: AnalysisType = Field(default=AnalysisType.SALES, description="نوع التحليل")
    group_by: Optional[str] = Field(None, description="تجميع البيانات حسب")
    include_trends: bool = Field(default=False, description="تضمين الاتجاهات")

class BulkExportRequest(StreamingRequest):
    """طلب التصدير المجمع"""
    tables: List[DataTable] = Field(..., description="الجداول المطلوبة")
    include_relationships: bool = Field(default=True, description="تضمين العلاقات")
    separate_files: bool = Field(default=False, description="ملفات منفصلة لكل جدول")

class StreamingResponse(BaseModel):
    """استجابة تدفق البيانات"""
    status: str = Field(..., description="حالة العملية")
    message: str = Field(..., description="رسالة الحالة")
    total_records: Optional[int] = Field(None, description="إجمالي السجلات")
    processed_records: Optional[int] = Field(None, description="السجلات المعالجة")
    file_size: Optional[int] = Field(None, description="حجم الملف بالبايت")
    download_url: Optional[str] = Field(None, description="رابط التحميل")
    expires_at: Optional[datetime] = Field(None, description="تاريخ انتهاء الرابط")

class StreamingProgress(BaseModel):
    """تقدم عملية التدفق"""
    task_id: str = Field(..., description="معرف المهمة")
    status: str = Field(..., description="حالة المهمة")
    progress_percentage: float = Field(..., ge=0, le=100, description="نسبة التقدم")
    current_record: int = Field(..., description="السجل الحالي")
    total_records: int = Field(..., description="إجمالي السجلات")
    estimated_time_remaining: Optional[int] = Field(None, description="الوقت المتبقي بالثواني")
    error_message: Optional[str] = Field(None, description="رسالة الخطأ")

class DataQualityReport(BaseModel):
    """تقرير جودة البيانات"""
    table_name: str = Field(..., description="اسم الجدول")
    total_records: int = Field(..., description="إجمالي السجلات")
    valid_records: int = Field(..., description="السجلات الصحيحة")
    invalid_records: int = Field(..., description="السجلات غير الصحيحة")
    missing_fields: Dict[str, int] = Field(default_factory=dict, description="الحقول المفقودة")
    data_types_issues: Dict[str, int] = Field(default_factory=dict, description="مشاكل أنواع البيانات")
    quality_score: float = Field(..., ge=0, le=100, description="نقاط الجودة")

class ExportStatistics(BaseModel):
    """إحصائيات التصدير"""
    export_id: str = Field(..., description="معرف التصدير")
    user_id: int = Field(..., description="معرف المستخدم")
    tables_exported: List[str] = Field(..., description="الجداول المصدرة")
    total_records: int = Field(..., description="إجمالي السجلات")
    file_size_bytes: int = Field(..., description="حجم الملف")
    compression_ratio: Optional[float] = Field(None, description="نسبة الضغط")
    export_duration_seconds: float = Field(..., description="مدة التصدير")
    created_at: datetime = Field(..., description="تاريخ الإنشاء")
    format_type: StreamFormat = Field(..., description="نوع التنسيق")

class StreamingError(BaseModel):
    """خطأ في التدفق"""
    error_code: str = Field(..., description="رمز الخطأ")
    error_message: str = Field(..., description="رسالة الخطأ")
    error_details: Optional[Dict[str, Any]] = Field(None, description="تفاصيل الخطأ")
    timestamp: datetime = Field(default_factory=datetime.now, description="وقت الخطأ")
    affected_records: Optional[int] = Field(None, description="السجلات المتأثرة")

class StreamingConfiguration(BaseModel):
    """إعدادات التدفق"""
    max_chunk_size: int = Field(default=5000, description="الحد الأقصى لحجم الدفعة")
    max_concurrent_streams: int = Field(default=5, description="الحد الأقصى للتدفقات المتزامنة")
    default_timeout_seconds: int = Field(default=300, description="المهلة الافتراضية")
    compression_enabled: bool = Field(default=True, description="تفعيل الضغط")
    cache_results: bool = Field(default=True, description="تخزين النتائج مؤقتاً")
    max_file_size_mb: int = Field(default=500, description="الحد الأقصى لحجم الملف")

class StreamingMetrics(BaseModel):
    """مقاييس الأداء للتدفق"""
    total_exports: int = Field(..., description="إجمالي التصديرات")
    successful_exports: int = Field(..., description="التصديرات الناجحة")
    failed_exports: int = Field(..., description="التصديرات الفاشلة")
    average_export_time: float = Field(..., description="متوسط وقت التصدير")
    total_data_exported_mb: float = Field(..., description="إجمالي البيانات المصدرة")
    most_exported_table: str = Field(..., description="الجدول الأكثر تصديراً")
    peak_usage_hour: int = Field(..., description="ساعة الذروة")

# Response models for API endpoints
class StreamSalesResponse(BaseModel):
    """استجابة تدفق المبيعات"""
    stream_url: str = Field(..., description="رابط التدفق")
    total_sales: int = Field(..., description="إجمالي المبيعات")
    date_range: Dict[str, Optional[str]] = Field(..., description="نطاق التاريخ")
    format_info: Dict[str, str] = Field(..., description="معلومات التنسيق")

class StreamProductsResponse(BaseModel):
    """استجابة تدفق المنتجات"""
    stream_url: str = Field(..., description="رابط التدفق")
    total_products: int = Field(..., description="إجمالي المنتجات")
    filters_applied: Dict[str, Any] = Field(..., description="الفلاتر المطبقة")
    analytics_included: bool = Field(..., description="التحليلات مضمنة")

class StreamCustomersResponse(BaseModel):
    """استجابة تدفق العملاء"""
    stream_url: str = Field(..., description="رابط التدفق")
    total_customers: int = Field(..., description="إجمالي العملاء")
    debts_included: bool = Field(..., description="الديون مضمنة")
    active_only: bool = Field(..., description="النشطين فقط")

class BulkExportResponse(BaseModel):
    """استجابة التصدير المجمع"""
    export_id: str = Field(..., description="معرف التصدير")
    download_url: str = Field(..., description="رابط التحميل")
    tables_included: List[str] = Field(..., description="الجداول المضمنة")
    total_size_mb: float = Field(..., description="الحجم الإجمالي")
    estimated_download_time: int = Field(..., description="الوقت المقدر للتحميل")
    expires_at: datetime = Field(..., description="تاريخ انتهاء الرابط")
