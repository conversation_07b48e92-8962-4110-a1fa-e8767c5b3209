#!/usr/bin/env python3
"""
مراقب الأداء أثناء إضافة البيانات الكبيرة
Performance Monitor for Large Data Operations
"""
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

import time
import psutil
import os
from datetime import datetime
from database.session import SessionLocal
from models.user import User
from models.product import Product
from models.customer import Customer
from models.sale import Sale, SaleItem
from models.customer import CustomerDebt
from sqlalchemy import func

class PerformanceMonitor:
    def __init__(self):
        self.db = SessionLocal()
        self.start_time = None
        self.process = psutil.Process(os.getpid())
        
    def get_database_size(self):
        """الحصول على حجم قاعدة البيانات"""
        try:
            db_path = "smartpos.db"
            if os.path.exists(db_path):
                size_bytes = os.path.getsize(db_path)
                size_mb = size_bytes / (1024 * 1024)
                return size_mb
            return 0
        except Exception:
            return 0
    
    def get_memory_usage(self):
        """الحصول على استخدام الذاكرة"""
        try:
            memory_info = self.process.memory_info()
            memory_mb = memory_info.rss / (1024 * 1024)
            return memory_mb
        except Exception:
            return 0
    
    def get_cpu_usage(self):
        """الحصول على استخدام المعالج"""
        try:
            return self.process.cpu_percent()
        except Exception:
            return 0
    
    def get_table_counts(self):
        """الحصول على عدد السجلات في كل جدول"""
        try:
            counts = {
                'users': self.db.query(User).count(),
                'products': self.db.query(Product).count(),
                'customers': self.db.query(Customer).count(),
                'sales': self.db.query(Sale).count(),
                'sale_items': self.db.query(SaleItem).count(),
                'customer_debts': self.db.query(CustomerDebt).count()
            }
            return counts
        except Exception as e:
            print(f"خطأ في الحصول على عدد السجلات: {e}")
            return {}
    
    def get_database_stats(self):
        """الحصول على إحصائيات قاعدة البيانات"""
        try:
            # إحصائيات مالية
            total_sales = self.db.query(func.sum(Sale.total_amount)).scalar() or 0
            total_paid = self.db.query(func.sum(Sale.amount_paid)).scalar() or 0
            total_debts = self.db.query(func.sum(CustomerDebt.remaining_amount)).scalar() or 0
            
            # إحصائيات المنتجات
            active_products = self.db.query(Product).filter(Product.is_active == True).count()
            low_stock_products = self.db.query(Product).filter(
                Product.quantity <= Product.min_quantity
            ).count()
            
            # إحصائيات العملاء
            active_customers = self.db.query(Customer).filter(Customer.is_active == True).count()
            customers_with_debts = self.db.query(CustomerDebt).filter(
                CustomerDebt.is_paid == False
            ).distinct(CustomerDebt.customer_id).count()
            
            return {
                'financial': {
                    'total_sales': total_sales,
                    'total_paid': total_paid,
                    'total_debts': total_debts
                },
                'products': {
                    'active_products': active_products,
                    'low_stock_products': low_stock_products
                },
                'customers': {
                    'active_customers': active_customers,
                    'customers_with_debts': customers_with_debts
                }
            }
        except Exception as e:
            print(f"خطأ في الحصول على إحصائيات قاعدة البيانات: {e}")
            return {}
    
    def test_query_performance(self):
        """اختبار أداء الاستعلامات المختلفة"""
        print("\n🔍 اختبار أداء الاستعلامات:")
        print("-" * 40)
        
        queries = [
            {
                'name': 'عدد المنتجات النشطة',
                'query': lambda: self.db.query(Product).filter(Product.is_active == True).count()
            },
            {
                'name': 'إجمالي المبيعات اليوم',
                'query': lambda: self.db.query(func.sum(Sale.total_amount)).filter(
                    func.date(Sale.created_at) == func.date('now')
                ).scalar() or 0
            },
            {
                'name': 'أفضل 10 منتجات مبيعاً',
                'query': lambda: self.db.query(
                    Product.name,
                    func.sum(SaleItem.quantity).label('total_sold')
                ).join(SaleItem).group_by(Product.id).order_by(
                    func.sum(SaleItem.quantity).desc()
                ).limit(10).all()
            },
            {
                'name': 'العملاء مع الديون',
                'query': lambda: self.db.query(Customer).join(CustomerDebt).filter(
                    CustomerDebt.is_paid == False
                ).distinct().count()
            },
            {
                'name': 'المبيعات الشهرية',
                'query': lambda: self.db.query(
                    func.strftime('%Y-%m', Sale.created_at).label('month'),
                    func.sum(Sale.total_amount).label('total')
                ).group_by(func.strftime('%Y-%m', Sale.created_at)).all()
            }
        ]
        
        for query_info in queries:
            start_time = time.time()
            try:
                result = query_info['query']()
                end_time = time.time()
                duration = (end_time - start_time) * 1000  # بالميلي ثانية
                
                if isinstance(result, list):
                    result_count = len(result)
                    print(f"  ✅ {query_info['name']}: {duration:.2f}ms ({result_count} نتيجة)")
                else:
                    print(f"  ✅ {query_info['name']}: {duration:.2f}ms (النتيجة: {result})")
                    
            except Exception as e:
                print(f"  ❌ {query_info['name']}: خطأ - {e}")
    
    def run_performance_test(self):
        """تشغيل اختبار الأداء الشامل"""
        print("🚀 بدء اختبار الأداء الشامل...")
        print("=" * 50)
        
        # معلومات النظام
        print(f"📅 التاريخ والوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"💻 نظام التشغيل: {psutil.LINUX if hasattr(psutil, 'LINUX') else 'Linux'}")
        print(f"🧠 الذاكرة الإجمالية: {psutil.virtual_memory().total / (1024**3):.2f} GB")
        print(f"⚡ عدد المعالجات: {psutil.cpu_count()}")
        
        # حجم قاعدة البيانات
        db_size = self.get_database_size()
        print(f"💾 حجم قاعدة البيانات: {db_size:.2f} MB")
        
        # استخدام الموارد
        memory_usage = self.get_memory_usage()
        cpu_usage = self.get_cpu_usage()
        print(f"🧠 استخدام الذاكرة: {memory_usage:.2f} MB")
        print(f"⚡ استخدام المعالج: {cpu_usage:.1f}%")
        
        # عدد السجلات
        print("\n📊 عدد السجلات في الجداول:")
        table_counts = self.get_table_counts()
        for table, count in table_counts.items():
            print(f"  📋 {table}: {count:,}")
        
        # إحصائيات قاعدة البيانات
        print("\n💰 الإحصائيات المالية:")
        db_stats = self.get_database_stats()
        if 'financial' in db_stats:
            financial = db_stats['financial']
            print(f"  💵 إجمالي المبيعات: {financial['total_sales']:,.2f} د.ل")
            print(f"  💰 إجمالي المدفوع: {financial['total_paid']:,.2f} د.ل")
            print(f"  💳 إجمالي الديون: {financial['total_debts']:,.2f} د.ل")
        
        if 'products' in db_stats:
            products = db_stats['products']
            print(f"\n📦 إحصائيات المنتجات:")
            print(f"  ✅ المنتجات النشطة: {products['active_products']:,}")
            print(f"  ⚠️ منتجات قليلة المخزون: {products['low_stock_products']:,}")
        
        if 'customers' in db_stats:
            customers = db_stats['customers']
            print(f"\n👥 إحصائيات العملاء:")
            print(f"  ✅ العملاء النشطون: {customers['active_customers']:,}")
            print(f"  💳 عملاء لديهم ديون: {customers['customers_with_debts']:,}")
        
        # اختبار أداء الاستعلامات
        self.test_query_performance()
        
        print("\n✅ انتهى اختبار الأداء!")
        
    def monitor_during_data_insertion(self, check_interval=30):
        """مراقبة الأداء أثناء إدراج البيانات"""
        print(f"👁️ بدء مراقبة الأداء (فحص كل {check_interval} ثانية)...")
        print("اضغط Ctrl+C للتوقف")
        
        try:
            while True:
                print(f"\n⏰ {datetime.now().strftime('%H:%M:%S')}")
                
                # استخدام الموارد
                memory_usage = self.get_memory_usage()
                cpu_usage = self.get_cpu_usage()
                db_size = self.get_database_size()
                
                print(f"🧠 الذاكرة: {memory_usage:.1f} MB | ⚡ المعالج: {cpu_usage:.1f}% | 💾 قاعدة البيانات: {db_size:.1f} MB")
                
                # عدد السجلات
                table_counts = self.get_table_counts()
                total_records = sum(table_counts.values())
                print(f"📊 إجمالي السجلات: {total_records:,}")
                
                time.sleep(check_interval)
                
        except KeyboardInterrupt:
            print("\n🛑 تم إيقاف المراقبة.")
    
    def __del__(self):
        """تنظيف الموارد"""
        if hasattr(self, 'db'):
            self.db.close()

def main():
    """الدالة الرئيسية"""
    monitor = PerformanceMonitor()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "monitor":
            # مراقبة مستمرة
            interval = int(sys.argv[2]) if len(sys.argv) > 2 else 30
            monitor.monitor_during_data_insertion(interval)
        elif sys.argv[1] == "test":
            # اختبار أداء شامل
            monitor.run_performance_test()
    else:
        # اختبار أداء افتراضي
        monitor.run_performance_test()

if __name__ == "__main__":
    main()
