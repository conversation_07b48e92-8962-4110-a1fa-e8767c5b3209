#!/usr/bin/env python3
"""
سكريبت اختبار شامل لنظام المنطقة الزمنية المحدث
يختبر جميع جوانب النظام للتأكد من دقة تطبيق المنطقة الزمنية
"""

import sys
import os
import logging
from datetime import datetime, timezone
from pathlib import Path

# إضافة مسار المشروع إلى Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('timezone_test.log')
    ]
)
logger = logging.getLogger(__name__)

def test_timezone_functions():
    """اختبار دوال المنطقة الزمنية الأساسية"""
    logger.info("🔍 اختبار دوال المنطقة الزمنية الأساسية...")
    
    try:
        from utils.datetime_utils import (
            get_timezone_from_settings,
            get_current_time_with_settings,
            convert_to_settings_timezone,
            create_timestamp_with_settings,
            TRIPOLI_TIMEZONE
        )
        
        # اختبار جلب المنطقة الزمنية من الإعدادات
        timezone_obj = get_timezone_from_settings()
        logger.info(f"✅ المنطقة الزمنية من الإعدادات: {timezone_obj}")
        
        # اختبار الحصول على الوقت الحالي
        current_time = get_current_time_with_settings()
        logger.info(f"✅ الوقت الحالي بالمنطقة الزمنية المحددة: {current_time}")
        
        # اختبار تحويل التاريخ
        utc_now = datetime.now(timezone.utc)
        converted_time = convert_to_settings_timezone(utc_now)
        logger.info(f"✅ تحويل من UTC: {utc_now} → {converted_time}")
        
        # اختبار إنشاء timestamp
        timestamp = create_timestamp_with_settings()
        logger.info(f"✅ إنشاء timestamp: {timestamp}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار دوال المنطقة الزمنية: {e}")
        return False

def test_database_models():
    """اختبار نماذج قاعدة البيانات"""
    logger.info("🔍 اختبار نماذج قاعدة البيانات...")
    
    try:
        from database.session import get_db
        from models.setting import Setting
        from models.sale import Sale
        from models.user import User
        
        # اختبار الاتصال بقاعدة البيانات
        db = next(get_db())
        
        # اختبار جلب إعدادات المنطقة الزمنية
        timezone_setting = db.query(Setting).filter(Setting.key == "timezone").first()
        if timezone_setting:
            logger.info(f"✅ إعداد المنطقة الزمنية في قاعدة البيانات: {timezone_setting.value}")
        else:
            logger.warning("⚠️ لم يتم العثور على إعداد المنطقة الزمنية في قاعدة البيانات")
        
        # اختبار النماذج المحدثة
        logger.info("✅ تم تحديث النماذج لاستخدام enhanced_settings_timestamp")
        
        db.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار نماذج قاعدة البيانات: {e}")
        return False

def test_sales_service():
    """اختبار خدمة المبيعات"""
    logger.info("🔍 اختبار خدمة المبيعات...")
    
    try:
        from database.session import get_db
        from services.current_period_service import CurrentPeriodService
        from models.user import User
        
        # الحصول على مستخدم للاختبار
        db = next(get_db())
        user = db.query(User).first()
        
        if user:
            # اختبار خدمة الفترة الحالية
            service = CurrentPeriodService(db, user)
            
            # اختبار جلب بيانات اليوم الحالي
            daily_sales = service.get_current_day_sales()
            logger.info(f"✅ تم جلب بيانات {len(daily_sales)} ساعة لليوم الحالي")
            
            # اختبار جلب بيانات الأسبوع الحالي
            weekly_sales = service.get_current_week_sales()
            logger.info(f"✅ تم جلب بيانات {len(weekly_sales)} يوم للأسبوع الحالي")
            
        else:
            logger.warning("⚠️ لم يتم العثور على مستخدمين في قاعدة البيانات")
        
        db.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار خدمة المبيعات: {e}")
        return False

def test_timezone_consistency():
    """اختبار اتساق المنطقة الزمنية"""
    logger.info("🔍 اختبار اتساق المنطقة الزمنية...")
    
    try:
        from utils.datetime_utils import (
            get_current_time_with_settings,
            get_tripoli_now,
            get_timezone_from_settings
        )
        from database.session import get_db
        
        db = next(get_db())
        
        # الحصول على الأوقات من مصادر مختلفة
        settings_time = get_current_time_with_settings(db)
        tripoli_time = get_tripoli_now()
        settings_timezone = get_timezone_from_settings(db)
        
        logger.info(f"✅ الوقت من الإعدادات: {settings_time} ({settings_time.tzinfo})")
        logger.info(f"✅ وقت طرابلس: {tripoli_time} ({tripoli_time.tzinfo})")
        logger.info(f"✅ المنطقة الزمنية من الإعدادات: {settings_timezone}")
        
        # التحقق من الاتساق
        if str(settings_timezone) == 'Africa/Tripoli':
            time_diff = abs((settings_time - tripoli_time).total_seconds())
            if time_diff < 60:  # أقل من دقيقة
                logger.info("✅ الأوقات متسقة (الفرق أقل من دقيقة)")
            else:
                logger.warning(f"⚠️ فرق في الأوقات: {time_diff} ثانية")
        else:
            logger.info(f"✅ المنطقة الزمنية مختلفة عن طرابلس: {settings_timezone}")
        
        db.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار اتساق المنطقة الزمنية: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    logger.info("🚀 بدء اختبار نظام المنطقة الزمنية المحدث")
    logger.info("=" * 60)
    
    tests = [
        ("دوال المنطقة الزمنية الأساسية", test_timezone_functions),
        ("نماذج قاعدة البيانات", test_database_models),
        ("خدمة المبيعات", test_sales_service),
        ("اتساق المنطقة الزمنية", test_timezone_consistency),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 اختبار: {test_name}")
        logger.info("-" * 40)
        
        if test_func():
            passed += 1
            logger.info(f"✅ نجح اختبار: {test_name}")
        else:
            logger.error(f"❌ فشل اختبار: {test_name}")
    
    logger.info("\n" + "=" * 60)
    logger.info(f"📊 نتائج الاختبار: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        logger.info("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        return 0
    else:
        logger.error("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
