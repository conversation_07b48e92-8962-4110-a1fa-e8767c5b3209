#!/bin/bash

# سكريپت تشغيل إصلاح الإحداثيات
# يقوم بتشغيل سكريپت SQL لحل مشكلة عدم حفظ store_latitude

echo "🔧 بدء إصلاح مشكلة الإحداثيات..."
echo "=================================="

# البحث عن ملف قاعدة البيانات
DB_FILE=""

# البحث في المجلدات المحتملة
if [ -f "../database.db" ]; then
    DB_FILE="../database.db"
elif [ -f "../backend/database.db" ]; then
    DB_FILE="../backend/database.db"
elif [ -f "./database.db" ]; then
    DB_FILE="./database.db"
elif [ -f "../smartpos.db" ]; then
    DB_FILE="../smartpos.db"
elif [ -f "./smartpos.db" ]; then
    DB_FILE="./smartpos.db"
else
    echo "❌ لم يتم العثور على ملف قاعدة البيانات"
    echo "يرجى تحديد مسار ملف قاعدة البيانات يدوياً:"
    echo "sqlite3 /path/to/database.db < fix_coordinates.sql"
    exit 1
fi

echo "📁 تم العثور على قاعدة البيانات: $DB_FILE"

# التحقق من وجود sqlite3
if ! command -v sqlite3 &> /dev/null; then
    echo "❌ sqlite3 غير مثبت"
    echo "يرجى تثبيت sqlite3 أولاً"
    exit 1
fi

# تشغيل سكريپت الإصلاح
echo "🔄 تشغيل سكريپت الإصلاح..."
sqlite3 "$DB_FILE" < fix_coordinates.sql

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ تم تشغيل سكريپت الإصلاح بنجاح!"
    echo ""
    echo "🧪 للتحقق من النتيجة، يمكنك تشغيل:"
    echo "sqlite3 $DB_FILE \"SELECT key, value FROM settings WHERE key IN ('store_latitude', 'store_longitude');\""
    echo ""
    echo "🎯 الخطوات التالية:"
    echo "1. افتح الإعدادات في الواجهة"
    echo "2. اختبر اختيار عنوان من الخريطة"
    echo "3. تأكد من حفظ الإحداثيات بشكل صحيح"
else
    echo "❌ فشل في تشغيل سكريپت الإصلاح"
    exit 1
fi
