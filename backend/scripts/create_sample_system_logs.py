#!/usr/bin/env python3
"""
إنشاء بيانات تجريبية لسجلات النظام
"""

import sys
import os
from datetime import datetime, timedelta
import random

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.session import get_db
from sqlalchemy import text

def create_sample_system_logs():
    """إنشاء بيانات تجريبية لسجلات النظام"""
    
    db = next(get_db())
    
    try:
        # إنشاء جدول سجلات النظام إذا لم يكن موجوداً
        create_table_query = """
        CREATE TABLE IF NOT EXISTS system_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            level TEXT NOT NULL CHECK (level IN ('INFO', 'WARNING', 'ERROR', 'CRITICAL')),
            source TEXT NOT NULL CHECK (source IN ('FRONTEND', 'BACKEND', 'DATABASE', 'SYSTEM')),
            message TEXT NOT NULL,
            details TEXT,
            stack_trace TEXT,
            user_id INTEGER,
            session_id TEXT,
            resolved BOOLEAN DEFAULT FALSE,
            resolution_notes TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """
        db.execute(text(create_table_query))
        db.commit()
        print("✓ تم إنشاء جدول سجلات النظام")
        
        # مسح البيانات الموجودة
        db.execute(text("DELETE FROM system_logs"))
        db.commit()
        print("✓ تم مسح البيانات الموجودة")
        
        # بيانات تجريبية للسجلات
        sample_logs = [
            # سجلات معلوماتية
            {
                'level': 'INFO',
                'source': 'FRONTEND',
                'message': 'User logged in successfully',
                'details': '{"username": "admin", "ip": "*************", "browser": "Chrome"}',
                'timestamp': datetime.now() - timedelta(hours=2)
            },
            {
                'level': 'INFO',
                'source': 'BACKEND',
                'message': 'Database backup completed',
                'details': '{"backup_size": "15.2MB", "duration": "3.5s", "location": "/backups/"}',
                'timestamp': datetime.now() - timedelta(hours=1)
            },
            {
                'level': 'INFO',
                'source': 'SYSTEM',
                'message': 'System health check passed',
                'details': '{"cpu_usage": "45%", "memory_usage": "62%", "disk_space": "78%"}',
                'timestamp': datetime.now() - timedelta(minutes=30)
            },
            
            # سجلات تحذيرية
            {
                'level': 'WARNING',
                'source': 'FRONTEND',
                'message': 'Slow API response detected',
                'details': '{"endpoint": "/api/products", "response_time": "3.2s", "threshold": "2s"}',
                'timestamp': datetime.now() - timedelta(hours=3)
            },
            {
                'level': 'WARNING',
                'source': 'DATABASE',
                'message': 'High memory usage detected',
                'details': '{"memory_usage": "85%", "threshold": "80%", "available": "2.1GB"}',
                'timestamp': datetime.now() - timedelta(hours=1, minutes=15)
            },
            {
                'level': 'WARNING',
                'source': 'BACKEND',
                'message': 'Multiple failed login attempts',
                'details': '{"username": "test_user", "attempts": 5, "ip": "*************", "time_window": "10min"}',
                'timestamp': datetime.now() - timedelta(minutes=45)
            },
            
            # سجلات أخطاء
            {
                'level': 'ERROR',
                'source': 'FRONTEND',
                'message': 'Failed to load product data',
                'details': '{"error": "Network timeout", "endpoint": "/api/products", "retry_count": 3}',
                'stack_trace': 'Error: Network timeout\n    at fetch (/src/api.js:45:12)\n    at ProductService.getProducts (/src/services/products.js:23:8)',
                'timestamp': datetime.now() - timedelta(hours=4)
            },
            {
                'level': 'ERROR',
                'source': 'BACKEND',
                'message': 'Database connection failed',
                'details': '{"database": "smartpos.db", "error": "Connection timeout", "retry_attempts": 3}',
                'stack_trace': 'sqlite3.OperationalError: database is locked\n    at connect() line 156\n    at get_db() line 23',
                'timestamp': datetime.now() - timedelta(hours=2, minutes=30)
            },
            {
                'level': 'ERROR',
                'source': 'DATABASE',
                'message': 'Query execution failed',
                'details': '{"query": "SELECT * FROM sales", "error": "Table locked", "duration": "5.2s"}',
                'stack_trace': 'sqlite3.OperationalError: database table is locked: sales',
                'timestamp': datetime.now() - timedelta(hours=1, minutes=20)
            },
            
            # سجلات حرجة
            {
                'level': 'CRITICAL',
                'source': 'SYSTEM',
                'message': 'Disk space critically low',
                'details': '{"available_space": "150MB", "total_space": "50GB", "usage": "99.7%", "critical_threshold": "95%"}',
                'timestamp': datetime.now() - timedelta(hours=6)
            },
            {
                'level': 'CRITICAL',
                'source': 'BACKEND',
                'message': 'Application crash detected',
                'details': '{"error": "Segmentation fault", "process_id": 1234, "memory_usage": "2.1GB", "uptime": "72h"}',
                'stack_trace': 'Segmentation fault (core dumped)\n    at main() line 45\n    at startup() line 12',
                'timestamp': datetime.now() - timedelta(hours=8),
                'resolved': True,
                'resolution_notes': 'تم إعادة تشغيل الخدمة وإصلاح مشكلة تسريب الذاكرة'
            },
            
            # سجلات إضافية متنوعة
            {
                'level': 'INFO',
                'source': 'FRONTEND',
                'message': 'User performed bulk product update',
                'details': '{"user": "admin", "products_updated": 150, "duration": "2.3s"}',
                'timestamp': datetime.now() - timedelta(minutes=15)
            },
            {
                'level': 'WARNING',
                'source': 'SYSTEM',
                'message': 'CPU usage spike detected',
                'details': '{"cpu_usage": "92%", "duration": "30s", "process": "python3"}',
                'timestamp': datetime.now() - timedelta(minutes=25)
            },
            {
                'level': 'ERROR',
                'source': 'FRONTEND',
                'message': 'React component render error',
                'details': '{"component": "ProductList", "error": "Cannot read property of undefined", "props": "{}"}',
                'stack_trace': 'TypeError: Cannot read property \'map\' of undefined\n    at ProductList.render (/src/components/ProductList.tsx:45:12)',
                'timestamp': datetime.now() - timedelta(minutes=10)
            },
            {
                'level': 'INFO',
                'source': 'BACKEND',
                'message': 'Scheduled maintenance completed',
                'details': '{"task": "database_optimization", "duration": "15min", "tables_optimized": 8}',
                'timestamp': datetime.now() - timedelta(hours=12)
            }
        ]
        
        # إدراج البيانات التجريبية
        for log_data in sample_logs:
            query = """
            INSERT INTO system_logs (level, source, message, details, stack_trace, timestamp, resolved, resolution_notes, session_id)
            VALUES (:level, :source, :message, :details, :stack_trace, :timestamp, :resolved, :resolution_notes, :session_id)
            """
            
            params = {
                'level': log_data['level'],
                'source': log_data['source'],
                'message': log_data['message'],
                'details': log_data.get('details'),
                'stack_trace': log_data.get('stack_trace'),
                'timestamp': log_data['timestamp'],
                'resolved': log_data.get('resolved', False),
                'resolution_notes': log_data.get('resolution_notes'),
                'session_id': f"session_{random.randint(1000, 9999)}"
            }
            
            db.execute(text(query), params)
        
        db.commit()
        print(f"✓ تم إنشاء {len(sample_logs)} سجل تجريبي للنظام")
        
        # عرض إحصائيات
        stats_query = """
        SELECT 
            level,
            COUNT(*) as count
        FROM system_logs 
        GROUP BY level
        ORDER BY 
            CASE level 
                WHEN 'CRITICAL' THEN 1 
                WHEN 'ERROR' THEN 2 
                WHEN 'WARNING' THEN 3 
                WHEN 'INFO' THEN 4 
            END
        """
        
        result = db.execute(text(stats_query))
        stats = result.fetchall()
        
        print("\n📊 إحصائيات السجلات:")
        for stat in stats:
            print(f"   {stat.level}: {stat.count}")
        
        print("\n✅ تم إنشاء بيانات سجلات النظام التجريبية بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء بيانات سجلات النظام: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    create_sample_system_logs()
