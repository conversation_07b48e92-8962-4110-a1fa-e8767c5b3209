#!/usr/bin/env python3
"""
Migration script to add tax_amount column to sales table
"""

import sqlite3
import os
import sys

# Add the parent directory to the path so we can import from backend
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def add_tax_amount_column():
    """Add tax_amount column to sales table if it doesn't exist"""
    
    # Database path
    db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'smartpos.db')
    
    if not os.path.exists(db_path):
        print(f"Database not found at {db_path}")
        return False
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if tax_amount column already exists
        cursor.execute("PRAGMA table_info(sales)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'tax_amount' in columns:
            print("tax_amount column already exists in sales table")
            conn.close()
            return True
        
        # Add tax_amount column
        print("Adding tax_amount column to sales table...")
        cursor.execute("ALTER TABLE sales ADD COLUMN tax_amount REAL NOT NULL DEFAULT 0.0")
        
        # Commit changes
        conn.commit()
        print("Successfully added tax_amount column to sales table")
        
        # Verify the column was added
        cursor.execute("PRAGMA table_info(sales)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'tax_amount' in columns:
            print("Verification: tax_amount column exists in sales table")
        else:
            print("Error: tax_amount column was not added successfully")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"Error adding tax_amount column: {e}")
        return False

if __name__ == "__main__":
    success = add_tax_amount_column()
    if success:
        print("Migration completed successfully!")
    else:
        print("Migration failed!")
        sys.exit(1)
