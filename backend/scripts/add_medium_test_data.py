#!/usr/bin/env python3
"""
إضافة بيانات متوسطة لاختبار سريع
Medium Test Data Generator for Quick Testing
"""
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from add_large_test_data import LargeDataGenerator

class MediumDataGenerator(LargeDataGenerator):
    """مولد بيانات متوسطة للاختبار السريع"""
    
    def run_medium_test(self):
        """تشغيل اختبار البيانات المتوسطة"""
        try:
            print("🚀 بدء إنشاء البيانات المتوسطة للاختبار السريع...")
            print("=" * 60)
            
            if not self.get_admin_user():
                return
            
            # إنشاء بيانات متوسطة (أقل من الكبيرة)
            print("📦 إنشاء 1,000 منتج...")
            self.generate_products(1000)
            
            print("👥 إنشاء 500 عميل...")
            self.generate_customers(500)
            
            print("💰 إنشاء 2,000 عملية بيع...")
            self.generate_sales_and_items(2000)
            
            print("💳 إنشاء 300 دين...")
            self.generate_debts(300)
            
            # طباعة الإحصائيات النهائية
            self.print_statistics()
            
            print("\n🎉 تم إنشاء البيانات المتوسطة بنجاح!")
            print("⚡ النظام جاهز للاختبار السريع!")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء البيانات: {e}")
            self.db.rollback()
        finally:
            self.db.close()

def main():
    """الدالة الرئيسية"""
    print("⚡ اختبار البيانات المتوسطة - للاختبار السريع")
    print("📊 سيتم إنشاء:")
    print("  - 1,000 منتج")
    print("  - 500 عميل") 
    print("  - 2,000 عملية بيع")
    print("  - 300 دين")
    print()
    
    response = input("هل تريد المتابعة؟ (y/n): ")
    if response.lower() not in ['y', 'yes', 'نعم']:
        print("❌ تم إلغاء العملية.")
        return
    
    generator = MediumDataGenerator()
    generator.run_medium_test()

if __name__ == "__main__":
    main()
