#!/usr/bin/env python3
"""
إنشاء مهام Google Drive الافتراضية
"""

import sys
import os
import json

# إضافة مسار المشروع إلى Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.session import get_db
from models.scheduled_task import ScheduledTask, TaskType, TaskStatus

def create_google_drive_tasks():
    """إنشاء مهام Google Drive الافتراضية"""
    
    # إنشاء جلسة قاعدة بيانات
    db_gen = get_db()
    db = next(db_gen)
    
    try:
        # قائمة المهام الافتراضية
        default_tasks = [
            {
                "name": "نسخ احتياطي يومي إلى Google Drive",
                "description": "رفع النسخ الاحتياطية تلقائياً إلى Google Drive يومياً",
                "task_type": TaskType.GOOGLE_DRIVE_BACKUP,
                "cron_expression": "0 4 * * *",  # يومياً في الساعة 4:00 صباحاً
                "status": TaskStatus.PAUSED,  # متوقف افتراضياً حتى يتم تكوين Google Drive
                "task_params": json.dumps({
                    "delete_local_after_upload": False
                }),
                "is_system_task": True
            },
            {
                "name": "تنظيف Google Drive أسبوعياً",
                "description": "حذف النسخ الاحتياطية القديمة من Google Drive أسبوعياً",
                "task_type": TaskType.GOOGLE_DRIVE_CLEANUP,
                "cron_expression": "0 5 * * 0",  # أسبوعياً يوم الأحد في الساعة 5:00 صباحاً
                "status": TaskStatus.PAUSED,  # متوقف افتراضياً حتى يتم تكوين Google Drive
                "task_params": json.dumps({
                    "keep_count": 10
                }),
                "is_system_task": True
            }
        ]
        
        created_count = 0
        existing_count = 0
        
        for task_data in default_tasks:
            # التحقق من وجود المهمة مسبقاً
            existing_task = db.query(ScheduledTask).filter(
                ScheduledTask.name == task_data["name"]
            ).first()
            
            if existing_task:
                print(f"⚠️  Task '{task_data['name']}' already exists.")
                print(f"   Status: {existing_task.status}")
                print(f"   Schedule: {existing_task.cron_expression}")
                existing_count += 1
            else:
                # إنشاء المهمة الجديدة
                new_task = ScheduledTask(
                    name=task_data["name"],
                    description=task_data["description"],
                    task_type=task_data["task_type"],
                    cron_expression=task_data["cron_expression"],
                    status=task_data["status"],
                    task_params=task_data["task_params"],
                    is_system_task=task_data["is_system_task"]
                )
                
                db.add(new_task)
                created_count += 1
                print(f"✅ Created task '{task_data['name']}'")
                print(f"   Type: {task_data['task_type']}")
                print(f"   Schedule: {task_data['cron_expression']}")
                print(f"   Status: {task_data['status']} (will be activated after Google Drive setup)")
        
        # حفظ التغييرات
        db.commit()
        
        print(f"\n🎉 Successfully processed Google Drive tasks:")
        print(f"   - Created: {created_count} new tasks")
        print(f"   - Existing: {existing_count} tasks")
        
        if created_count > 0:
            print(f"\n📝 Next steps:")
            print(f"   1. إعداد Google Drive (راجع docs/GOOGLE_DRIVE_SETUP.md)")
            print(f"   2. تفعيل Google Drive في الإعدادات")
            print(f"   3. تفعيل المهام من واجهة المهام المجدولة")
            print(f"   4. اختبار المهام للتأكد من عملها")
        
    except Exception as e:
        print(f"❌ Error creating Google Drive tasks: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_google_drive_tasks()
