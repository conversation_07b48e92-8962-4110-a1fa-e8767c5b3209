#!/usr/bin/env python3
"""
<PERSON>ript to add sample debt data for testing debt reports.
"""

import sys
import os
from datetime import datetime, timedelta

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from database.session import get_db
from models.customer import Customer, CustomerDebt, DebtPayment
from models.user import User
from utils.datetime_utils import get_tripoli_now

def add_sample_debts():
    """Add sample debt data for testing."""
    
    # Get database session
    db = next(get_db())
    
    try:
        # Get or create test customers
        customers = []
        customer_names = [
            "أحمد محمد",
            "فاطمة علي", 
            "محمد أحمد",
            "عائشة سالم",
            "عبدالله حسن"
        ]
        
        for name in customer_names:
            customer = db.query(Customer).filter(Customer.name == name).first()
            if not customer:
                customer = Customer(
                    name=name,
                    phone=f"091-{len(customers)+1234567}",
                    email=f"customer{len(customers)+1}@example.com"
                )
                db.add(customer)
                db.flush()
            customers.append(customer)
        
        # Get admin user
        admin_user = db.query(User).filter(User.username == "chiqwa").first()
        if not admin_user:
            print("❌ Admin user not found")
            return
        
        current_time = get_tripoli_now()
        
        # Create sample debts with different ages
        sample_debts = [
            # Recent debts (0-30 days)
            {
                "customer": customers[0],
                "amount": 150.0,
                "remaining_amount": 150.0,
                "description": "فاتورة مبيعات - منتجات متنوعة",
                "days_ago": 5,
                "is_paid": False
            },
            {
                "customer": customers[1],
                "amount": 200.0,
                "remaining_amount": 100.0,
                "description": "فاتورة مبيعات - أجهزة إلكترونية",
                "days_ago": 15,
                "is_paid": False
            },
            
            # Medium age debts (31-60 days)
            {
                "customer": customers[2],
                "amount": 300.0,
                "remaining_amount": 300.0,
                "description": "فاتورة مبيعات - مواد غذائية",
                "days_ago": 45,
                "is_paid": False
            },
            {
                "customer": customers[3],
                "amount": 250.0,
                "remaining_amount": 150.0,
                "description": "فاتورة مبيعات - ملابس",
                "days_ago": 50,
                "is_paid": False
            },
            
            # Old debts (61-90 days)
            {
                "customer": customers[4],
                "amount": 400.0,
                "remaining_amount": 400.0,
                "description": "فاتورة مبيعات - أثاث",
                "days_ago": 75,
                "is_paid": False
            },
            
            # Very old debts (90+ days)
            {
                "customer": customers[0],
                "amount": 500.0,
                "remaining_amount": 500.0,
                "description": "فاتورة مبيعات قديمة",
                "days_ago": 120,
                "is_paid": False
            },
            
            # Paid debts for collection rate calculation
            {
                "customer": customers[1],
                "amount": 100.0,
                "remaining_amount": 0.0,
                "description": "فاتورة مدفوعة",
                "days_ago": 10,
                "is_paid": True
            },
            {
                "customer": customers[2],
                "amount": 180.0,
                "remaining_amount": 0.0,
                "description": "فاتورة مدفوعة",
                "days_ago": 25,
                "is_paid": True
            }
        ]
        
        created_debts = []
        
        for debt_data in sample_debts:
            # Check if similar debt already exists
            existing_debt = db.query(CustomerDebt).filter(
                CustomerDebt.customer_id == debt_data["customer"].id,
                CustomerDebt.amount == debt_data["amount"],
                CustomerDebt.description == debt_data["description"]
            ).first()
            
            if existing_debt:
                print(f"✅ Debt already exists for {debt_data['customer'].name}: {debt_data['description']}")
                continue
            
            # Create debt with specific date
            debt_date = current_time - timedelta(days=debt_data["days_ago"])
            
            debt = CustomerDebt(
                customer_id=debt_data["customer"].id,
                amount=debt_data["amount"],
                remaining_amount=debt_data["remaining_amount"],
                description=debt_data["description"],
                is_paid=debt_data["is_paid"],
                created_at=debt_date,
                updated_at=debt_date
            )
            
            db.add(debt)
            db.flush()
            created_debts.append(debt)
            
            # Add payment if debt is paid
            if debt_data["is_paid"]:
                payment_date = debt_date + timedelta(days=5)  # Paid 5 days after creation
                payment = DebtPayment(
                    debt_id=debt.id,
                    amount=debt_data["amount"],
                    payment_method="cash",
                    notes="دفع كامل",
                    created_at=payment_date
                )
                db.add(payment)
            
            # Add partial payment for some debts
            elif debt_data["remaining_amount"] < debt_data["amount"]:
                paid_amount = debt_data["amount"] - debt_data["remaining_amount"]
                payment_date = debt_date + timedelta(days=3)
                payment = DebtPayment(
                    debt_id=debt.id,
                    amount=paid_amount,
                    payment_method="cash",
                    notes="دفع جزئي",
                    created_at=payment_date
                )
                db.add(payment)
            
            print(f"✅ Created debt for {debt_data['customer'].name}: {debt_data['amount']} د.ل")
        
        db.commit()
        
        print(f"\n✅ Successfully added {len(created_debts)} sample debts")
        print("📊 Debt summary:")
        print(f"   - Total customers: {len(customers)}")
        print(f"   - Total debts created: {len(created_debts)}")
        print(f"   - Recent debts (0-30 days): 2")
        print(f"   - Medium age debts (31-60 days): 2") 
        print(f"   - Old debts (61-90 days): 1")
        print(f"   - Very old debts (90+ days): 1")
        print(f"   - Paid debts: 2")
        
    except Exception as e:
        print(f"❌ Error adding sample debts: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    print("💰 Adding sample debt data...")
    add_sample_debts()
    print("✨ Done!")
