#!/usr/bin/env python3
"""
إضافة إعدادات Google Drive للنسخ الاحتياطية
"""

import sys
import os

# إضافة مسار المشروع إلى Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.session import get_db
from models.setting import Setting

def add_google_drive_settings():
    """إضافة إعدادات Google Drive"""
    
    # إنشاء جلسة قاعدة بيانات
    db_gen = get_db()
    db = next(db_gen)
    
    try:
        # قائمة الإعدادات الجديدة
        new_settings = [
            {
                "key": "google_drive_enabled",
                "value": "false",
                "description": "تفعيل/إلغاء تفعيل النسخ الاحتياطي إلى Google Drive"
            },
            {
                "key": "google_drive_credentials",
                "value": "",
                "description": "بيانات اعتماد Google Drive (JSON)"
            },
            {
                "key": "google_drive_backup_folder_id",
                "value": "",
                "description": "معرف مجلد النسخ الاحتياطية في Google Drive"
            },
            {
                "key": "google_drive_auto_cleanup",
                "value": "true",
                "description": "تنظيف النسخ الاحتياطية القديمة تلقائياً من Google Drive"
            },
            {
                "key": "google_drive_keep_count",
                "value": "10",
                "description": "عدد النسخ الاحتياطية للاحتفاظ بها في Google Drive"
            },
            {
                "key": "google_drive_delete_local_after_upload",
                "value": "false",
                "description": "حذف النسخة المحلية بعد رفعها إلى Google Drive"
            }
        ]
        
        added_count = 0
        updated_count = 0
        
        for setting_data in new_settings:
            # التحقق من وجود الإعداد مسبقاً
            existing_setting = db.query(Setting).filter(Setting.key == setting_data["key"]).first()
            
            if existing_setting:
                print(f"⚠️  Setting '{setting_data['key']}' already exists.")
                print(f"   Current value: {existing_setting.value}")
                
                # تحديث الوصف إذا كان مختلفاً
                if existing_setting.description != setting_data["description"]:
                    existing_setting.description = setting_data["description"]
                    updated_count += 1
                    print(f"   ✅ Updated description")
            else:
                # إنشاء الإعداد الجديد
                new_setting = Setting(
                    key=setting_data["key"],
                    value=setting_data["value"],
                    description=setting_data["description"]
                )
                
                db.add(new_setting)
                added_count += 1
                print(f"✅ Added setting '{setting_data['key']}'")
                print(f"   Default value: {setting_data['value']}")
        
        # حفظ التغييرات
        db.commit()
        
        print(f"\n🎉 Successfully processed Google Drive settings:")
        print(f"   - Added: {added_count} new settings")
        print(f"   - Updated: {updated_count} existing settings")
        
        if added_count > 0 or updated_count > 0:
            print(f"\n📝 Next steps:")
            print(f"   1. إعداد مشروع Google Cloud Console")
            print(f"   2. تحميل ملف اعتماد OAuth 2.0")
            print(f"   3. تفعيل Google Drive API")
            print(f"   4. تكوين الإعدادات من واجهة الإعدادات")
        
    except Exception as e:
        print(f"❌ Error adding Google Drive settings: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    add_google_drive_settings()
