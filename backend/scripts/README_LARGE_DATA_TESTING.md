# دليل اختبار البيانات الكبيرة
# Large Data Testing Guide

## نظرة عامة
هذا الدليل يوضح كيفية استخدام سكريبتات اختبار الأداء مع البيانات الكبيرة في نظام SmartPOS.

## السكريبتات المتاحة

### 1. فحص حالة قاعدة البيانات
```bash
python3 scripts/check_database_status.py
```
- يعرض إحصائيات شاملة عن قاعدة البيانات الحالية
- يظهر عدد السجلات في كل جدول
- يعرض الإحصائيات المالية والتشغيلية

### 2. إضافة البيانات الكبيرة
```bash
python3 scripts/add_large_test_data.py
```
- ينشئ بيانات كبيرة لاختبار الأداء:
  - 10,000 منتج متنوع
  - 5,000 عميل
  - 50,000 عملية بيع
  - 150,000 عنصر مبيعات
  - 5,000 دين

### 3. مراقبة الأداء
```bash
python3 scripts/performance_monitor.py
```
- يراقب أداء النظام والذاكرة
- يختبر سرعة الاستعلامات المختلفة
- يعرض إحصائيات مفصلة عن الأداء

#### خيارات مراقبة الأداء:
```bash
# اختبار أداء شامل (افتراضي)
python3 scripts/performance_monitor.py

# مراقبة مستمرة كل 30 ثانية
python3 scripts/performance_monitor.py monitor 30

# اختبار أداء مفصل
python3 scripts/performance_monitor.py test
```

### 4. تشغيل الاختبار الشامل
```bash
python3 scripts/run_large_data_test.py
```
- يجمع بين جميع السكريبتات
- يراقب الأداء أثناء إضافة البيانات
- يعرض تقارير شاملة قبل وبعد

## خطوات الاختبار الموصى بها

### الخطوة 1: فحص الحالة الحالية
```bash
cd backend
python3 scripts/check_database_status.py
```

### الخطوة 2: تشغيل الاختبار الشامل
```bash
python3 scripts/run_large_data_test.py
```

### الخطوة 3: مراقبة الأداء المستمرة (اختياري)
```bash
# في terminal منفصل
python3 scripts/performance_monitor.py monitor 15
```

## البيانات المُنشأة

### المنتجات (10,000)
- أسماء متنوعة باللغة العربية
- 15 فئة مختلفة
- أسعار واقعية حسب الفئة
- كميات ومخزون متنوع
- باركود فريد لكل منتج

### العملاء (5,000)
- أسماء عربية متنوعة
- أرقام هواتف ليبية
- عناوين في مدن مختلفة
- حالات نشاط متنوعة

### المبيعات (50,000)
- تواريخ موزعة على السنة الماضية
- طرق دفع متنوعة (نقدي، بطاقة، آجل، جزئي)
- ضرائب وخصومات واقعية
- ربط بالعملاء والمنتجات

### عناصر المبيعات (150,000)
- متوسط 3 منتجات لكل عملية بيع
- كميات وأسعار متنوعة
- خصومات على مستوى المنتج

### الديون (5,000)
- مرتبطة بالمبيعات غير المدفوعة
- مبالغ واقعية
- حالات دفع متنوعة

## مؤشرات الأداء المراقبة

### استخدام الموارد
- استخدام الذاكرة (RAM)
- استخدام المعالج (CPU)
- حجم قاعدة البيانات

### أداء الاستعلامات
- عدد المنتجات النشطة
- إجمالي المبيعات اليومية
- أفضل المنتجات مبيعاً
- العملاء مع الديون
- المبيعات الشهرية

## نصائح للاختبار

### 1. قبل البدء
- تأكد من وجود مساحة كافية على القرص (على الأقل 1 GB)
- أغلق التطبيقات غير الضرورية
- تأكد من أن الخادم الخلفي والواجهة الأمامية يعملان

### 2. أثناء الاختبار
- راقب استخدام الموارد
- لا تقم بعمليات أخرى كثيفة على النظام
- اتركه يكمل العملية دون مقاطعة

### 3. بعد الاختبار
- اختبر وظائف التطبيق المختلفة
- راقب سرعة التحميل والاستجابة
- اختبر التقارير والإحصائيات

## استكشاف الأخطاء

### مشكلة نفاد الذاكرة
```bash
# قلل حجم البيانات في السكريبت
# أو قم بتشغيل العملية على دفعات أصغر
```

### مشكلة بطء الأداء
```bash
# تأكد من وجود فهارس قاعدة البيانات
# راقب استخدام المعالج والذاكرة
```

### مشكلة في قاعدة البيانات
```bash
# تحقق من صحة ملف قاعدة البيانات
# تأكد من وجود صلاحيات الكتابة
```

## تنظيف البيانات التجريبية

⚠️ **تحذير**: هذه العملية ستحذف جميع البيانات!

```bash
# إنشاء نسخة احتياطية أولاً
cp smartpos.db smartpos_backup.db

# حذف البيانات التجريبية (يتطلب سكريبت منفصل)
# python3 scripts/cleanup_test_data.py
```

## الدعم والمساعدة

إذا واجهت أي مشاكل:
1. تحقق من ملفات السجلات
2. راجع رسائل الخطأ
3. تأكد من إعدادات قاعدة البيانات
4. اتصل بفريق الدعم الفني

---

**ملاحظة**: هذه السكريبتات مصممة لاختبار الأداء فقط ولا يجب استخدامها في بيئة الإنتاج.
