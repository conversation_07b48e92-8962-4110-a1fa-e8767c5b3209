#!/usr/bin/env python3
"""
إضافة إعداد مسار النسخ الاحتياطية إلى قاعدة البيانات
"""

import sys
import os

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.session import get_db
from models.setting import Setting

def add_backup_path_setting():
    """إضافة إعداد مسار النسخ الاحتياطية"""
    
    # إنشاء جلسة قاعدة بيانات
    db_gen = get_db()
    db = next(db_gen)
    
    try:
        # التحقق من وجود الإعداد مسبقاً
        existing_setting = db.query(Setting).filter(Setting.key == "backup_path").first()
        
        if existing_setting:
            print("✅ Setting 'backup_path' already exists.")
            print(f"   Current value: {existing_setting.value}")
            return
        
        # إنشاء الإعداد الجديد
        new_setting = Setting(
            key="backup_path",
            value="backend/backups",  # القيمة الافتراضية
            description="مسار حفظ النسخ الاحتياطية من قاعدة البيانات"
        )
        
        db.add(new_setting)
        db.commit()
        
        print("✅ Successfully added 'backup_path' setting to the database.")
        print("   Default value: backend/backups")
        print("   Description: مسار حفظ النسخ الاحتياطية من قاعدة البيانات")
        
    except Exception as e:
        print(f"❌ Error adding backup_path setting: {str(e)}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    add_backup_path_setting()
