#!/usr/bin/env python3
"""
سكريبت لإنشاء جدول المهام المجدولة وإضافة مهام افتراضية
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.session import get_db, engine
from database.base import Base
from models.scheduled_task import ScheduledTask, TaskType, TaskStatus
import json

def create_tables():
    """إنشاء جداول قاعدة البيانات"""
    try:
        Base.metadata.create_all(bind=engine)
        print("✅ تم إنشاء جداول قاعدة البيانات بنجاح")
    except Exception as e:
        print(f"❌ خطأ في إنشاء جداول قاعدة البيانات: {e}")
        return False
    return True

def create_default_tasks():
    """إنشاء المهام الافتراضية"""
    db_gen = get_db()
    db = next(db_gen)
    
    try:
        # التحقق من وجود المهام مسبقاً
        existing_tasks = db.query(ScheduledTask).filter(ScheduledTask.is_system_task == True).all()
        if existing_tasks:
            print("✅ المهام الافتراضية موجودة مسبقاً")
            return True
        
        # مهمة النسخ الاحتياطي اليومي
        daily_backup_task = ScheduledTask(
            name="نسخ احتياطي يومي",
            description="إنشاء نسخة احتياطية من قاعدة البيانات يومياً في الساعة 2:00 صباحاً",
            task_type=TaskType.DATABASE_BACKUP,
            cron_expression="0 2 * * *",  # كل يوم في الساعة 2:00 صباحاً
            status=TaskStatus.ACTIVE,
            task_params=json.dumps({
                "backup_type": "daily",
                "compress": True
            }),
            max_retries=3,
            timeout_seconds=600,  # 10 دقائق
            is_system_task=True
        )
        
        # مهمة تنظيف النسخ الاحتياطية القديمة
        cleanup_task = ScheduledTask(
            name="تنظيف النسخ الاحتياطية القديمة",
            description="حذف النسخ الاحتياطية القديمة والاحتفاظ بآخر 30 نسخة",
            task_type=TaskType.CLEANUP_OLD_BACKUPS,
            cron_expression="0 3 * * 0",  # كل أسبوع يوم الأحد في الساعة 3:00 صباحاً
            status=TaskStatus.ACTIVE,
            task_params=json.dumps({
                "keep_count": 30
            }),
            max_retries=2,
            timeout_seconds=300,  # 5 دقائق
            is_system_task=True
        )
        
        # إضافة المهام إلى قاعدة البيانات
        db.add(daily_backup_task)
        db.add(cleanup_task)
        db.commit()
        
        print("✅ تم إنشاء المهام الافتراضية بنجاح:")
        print(f"   - {daily_backup_task.name}")
        print(f"   - {cleanup_task.name}")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المهام الافتراضية: {e}")
        db.rollback()
        return False
    finally:
        db.close()
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إعداد نظام المهام المجدولة...")
    
    # إنشاء الجداول
    if not create_tables():
        return
    
    # إنشاء المهام الافتراضية
    if not create_default_tasks():
        return
    
    print("✅ تم إعداد نظام المهام المجدولة بنجاح!")
    print("\n📋 المهام المتاحة:")
    print("   - نسخ احتياطي يومي (2:00 صباحاً)")
    print("   - تنظيف النسخ القديمة (أسبوعياً)")
    print("\n💡 يمكنك إدارة المهام من صفحة الإعدادات في التطبيق")

if __name__ == "__main__":
    main()
