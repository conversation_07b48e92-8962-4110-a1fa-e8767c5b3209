#!/usr/bin/env python3
"""
سكريبت لحذف الجهاز المنتظر بمعلومات ناقصة وإعادة إنشائه بمعلومات كاملة
"""

import sys
import os

# إضافة مسار المشروع
backend_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(backend_dir)
sys.path.insert(0, project_root)
sys.path.insert(0, backend_dir)

from database.session import get_db
from models.device_security import PendingDevice
from sqlalchemy import select

def fix_pending_device():
    """
    حذف الجهاز المنتظر بمعلومات ناقصة
    """
    try:
        db = next(get_db())
        
        # البحث عن الجهاز المحدد
        device_id = "fp_bwqu89"
        stmt = select(PendingDevice).where(PendingDevice.device_id == device_id)
        pending_device = db.execute(stmt).scalar_one_or_none()
        
        if pending_device:
            print(f"🔍 تم العثور على الجهاز: {device_id}")
            print(f"   📱 اسم الجهاز الحالي: {pending_device.hostname}")
            print(f"   💻 النظام الحالي: {pending_device.system}")
            print(f"   🌐 المنصة الحالية: {pending_device.platform}")
            print(f"   🔍 المتصفح الحالي: {getattr(pending_device, 'browser', 'غير موجود')}")
            
            # حذف الجهاز
            db.delete(pending_device)
            db.commit()
            print(f"✅ تم حذف الجهاز بنجاح: {device_id}")
            print("🔄 الآن عند دخول الجهاز مرة أخرى سيتم تسجيله بمعلومات كاملة")
        else:
            print(f"❌ لم يتم العثور على الجهاز: {device_id}")
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح الجهاز المنتظر: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    print("🚀 بدء إصلاح الجهاز المنتظر...")
    fix_pending_device()
    print("🎉 انتهى الإصلاح!")
