#!/usr/bin/env python3
"""
إضافة بيانات كبيرة لاختبار أداء النظام
Large Test Data Generator for Performance Testing
"""
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

import random
from datetime import datetime, timedelta
from database.session import SessionLocal
from models.user import User
from models.product import Product
from models.customer import Customer, CustomerDebt
from models.sale import Sale, SaleItem
from sqlalchemy import func

class LargeDataGenerator:
    def __init__(self):
        self.db = SessionLocal()
        self.admin_user = None
        self.categories = [
            'مشروبات', 'حلويات', 'وجبات', 'مخبوزات', 'منتجات ألبان',
            'خضروات', 'فواكه', 'لحوم', 'أسماك', 'بقوليات',
            'توابل', 'معلبات', 'مجمدات', 'منظفات', 'أدوات منزلية'
        ]
        self.payment_methods = ['cash', 'card', 'جزئي', 'آجل']
        self.payment_statuses = ['paid', 'partial', 'unpaid']

        # قوائم الأسماء والكلمات
        self.product_words = [
            'ممتاز', 'فاخر', 'طازج', 'عالي الجودة', 'مميز', 'طبيعي', 'صحي',
            'لذيذ', 'أصلي', 'مستورد', 'محلي', 'عضوي', 'خاص', 'جديد'
        ]
        self.company_names = [
            'الأمل', 'النجاح', 'الجودة', 'الأصالة', 'التميز', 'الإبداع',
            'الرائد', 'المتقدم', 'الحديث', 'الذهبي', 'الفضي', 'الماسي'
        ]
        self.customer_names = [
            'أحمد محمد', 'سارة علي', 'محمد أحمد', 'فاطمة حسن', 'علي محمود',
            'نور الدين', 'عائشة سالم', 'يوسف إبراهيم', 'زينب عبدالله', 'حسام الدين',
            'مريم أحمد', 'عبدالرحمن علي', 'خديجة محمد', 'طارق سعد', 'هدى عبدالله',
            'كريم محمود', 'سلمى حسن', 'عمر يوسف', 'ليلى إبراهيم', 'سامي عبدالله'
        ]
        
    def get_admin_user(self):
        """الحصول على المستخدم الإداري"""
        self.admin_user = self.db.query(User).filter(User.role == 'admin').first()
        if not self.admin_user:
            print("❌ لم يتم العثور على مستخدم إداري. يرجى إنشاء مستخدم إداري أولاً.")
            return False
        return True

    def generate_products(self, count=10000):
        """إنشاء منتجات كبيرة"""
        print(f"🏭 إنشاء {count:,} منتج...")
        
        products = []
        for i in range(count):
            category = random.choice(self.categories)
            
            # أسماء منتجات متنوعة
            product_names = [
                f"{random.choice(self.product_words)} {category}",
                f"{category} {random.choice(self.product_words)}",
                f"{random.choice(self.company_names)} {category}",
                f"{category} مميز {i+1}",
                f"منتج {category} رقم {i+1}"
            ]
            
            name = random.choice(product_names)
            barcode = f"BAR{str(i+1).zfill(10)}"
            
            # أسعار متنوعة حسب الفئة
            if category in ['مشروبات', 'حلويات']:
                price = round(random.uniform(5, 50), 2)
                cost_price = round(price * random.uniform(0.4, 0.7), 2)
            elif category in ['وجبات', 'لحوم']:
                price = round(random.uniform(20, 150), 2)
                cost_price = round(price * random.uniform(0.5, 0.8), 2)
            else:
                price = round(random.uniform(10, 100), 2)
                cost_price = round(price * random.uniform(0.3, 0.6), 2)
            
            quantity = random.randint(0, 500)
            min_quantity = random.randint(5, 50)
            
            product = Product(
                name=name,
                barcode=barcode,
                description=f"وصف {name} - منتج عالي الجودة",
                price=price,
                cost_price=cost_price,
                quantity=quantity,
                min_quantity=min_quantity,
                category=category,
                unit=random.choice(['piece', 'kg', 'liter', 'box']),
                is_active=random.choice([True, True, True, False]),  # 75% نشط
                created_by=self.admin_user.id if self.admin_user else 1
            )
            products.append(product)
            
            # إدراج دفعي كل 1000 منتج
            if len(products) >= 1000:
                self.db.add_all(products)
                self.db.commit()
                print(f"  ✅ تم إدراج {len(products)} منتج...")
                products = []
        
        # إدراج المنتجات المتبقية
        if products:
            self.db.add_all(products)
            self.db.commit()
            print(f"  ✅ تم إدراج {len(products)} منتج أخير...")
        
        print(f"✅ تم إنشاء {count:,} منتج بنجاح!")

    def generate_customers(self, count=5000):
        """إنشاء عملاء كبار"""
        print(f"👥 إنشاء {count:,} عميل...")
        
        customers = []
        cities = ['طرابلس', 'بنغازي', 'مصراتة', 'سبها', 'الزاوية', 'زليتن', 'أجدابيا', 'درنة']
        
        for i in range(count):
            name = random.choice(self.customer_names) + f" {i+1}"
            phone = f"091{random.randint(1000000, 9999999)}"
            email = f"customer{i+1}@email.com" if random.choice([True, False]) else None
            address = random.choice(cities)
            
            customer = Customer(
                name=name,
                phone=phone,
                email=email,
                address=address,
                notes=f"عميل رقم {i+1} - {random.choice(['عميل مميز', 'عميل جديد', 'عميل قديم', 'عميل عادي'])}",
                is_active=random.choice([True, True, True, False])  # 75% نشط
            )
            customers.append(customer)
            
            # إدراج دفعي كل 1000 عميل
            if len(customers) >= 1000:
                self.db.add_all(customers)
                self.db.commit()
                print(f"  ✅ تم إدراج {len(customers)} عميل...")
                customers = []
        
        # إدراج العملاء المتبقين
        if customers:
            self.db.add_all(customers)
            self.db.commit()
            print(f"  ✅ تم إدراج {len(customers)} عميل أخير...")
        
        print(f"✅ تم إنشاء {count:,} عميل بنجاح!")

    def generate_sales_and_items(self, sales_count=50000):
        """إنشاء مبيعات وعناصر المبيعات"""
        print(f"💰 إنشاء {sales_count:,} عملية بيع...")
        
        # الحصول على قوائم المنتجات والعملاء
        products = self.db.query(Product).filter(Product.is_active == True).all()
        customers = self.db.query(Customer).filter(Customer.is_active == True).all()
        
        if not products or not customers:
            print("❌ لا توجد منتجات أو عملاء نشطين!")
            return
        
        print(f"📦 المنتجات المتاحة: {len(products)}")
        print(f"👥 العملاء المتاحون: {len(customers)}")
        
        sales = []
        
        # تواريخ متنوعة للمبيعات (آخر سنة)
        start_date = datetime.now() - timedelta(days=365)
        
        for i in range(sales_count):
            # اختيار عميل عشوائي (80% مع عميل، 20% بدون)
            customer = random.choice(customers) if random.random() < 0.8 else None
            
            # تاريخ عشوائي
            sale_date = start_date + timedelta(days=random.randint(0, 365))
            
            # اختيار طريقة الدفع وحالة الدفع
            payment_method = random.choice(self.payment_methods)
            if payment_method == 'آجل':
                payment_status = 'unpaid'
                amount_paid = 0.0
            elif payment_method == 'جزئي':
                payment_status = 'partial'
            else:
                payment_status = 'paid'
            
            # إنشاء عناصر البيع (1-5 منتجات لكل عملية بيع)
            num_items = random.randint(1, 5)
            selected_products = random.sample(products, min(num_items, len(products)))

            total_amount = 0.0
            current_sale_items = []

            for product in selected_products:
                quantity = random.randint(1, 10)
                # تحويل السعر إلى float بشكل آمن
                try:
                    # استخدام getattr للحصول على القيمة الفعلية
                    price_value = getattr(product, 'price', 10.0)
                    unit_price = float(price_value) if price_value is not None else 10.0
                except (ValueError, TypeError, AttributeError):
                    unit_price = 10.0  # سعر افتراضي

                discount = round(random.uniform(0, unit_price * 0.2), 2)  # خصم حتى 20%
                subtotal = (unit_price * quantity) - discount
                total_amount += subtotal

                sale_item = {
                    'product_id': product.id,
                    'quantity': quantity,
                    'unit_price': unit_price,
                    'subtotal': subtotal,
                    'discount': discount
                }
                current_sale_items.append(sale_item)

            # حساب الضريبة والخصم الإجمالي
            # تأكد من أن total_amount هو float
            total_amount = float(total_amount)
            tax_amount = round(total_amount * random.uniform(0, 0.15), 2)  # ضريبة حتى 15%
            discount_amount = round(total_amount * random.uniform(0, 0.1), 2)  # خصم حتى 10%
            final_amount = total_amount + tax_amount - discount_amount
            
            # حساب المبلغ المدفوع
            if payment_status == 'paid':
                amount_paid = final_amount
            elif payment_status == 'partial':
                amount_paid = round(final_amount * random.uniform(0.3, 0.8), 2)
            else:
                amount_paid = 0.0
            
            sale = Sale(
                user_id=self.admin_user.id if self.admin_user else 1,
                customer_id=customer.id if customer else None,
                total_amount=final_amount,
                payment_method=payment_method,
                tax_amount=tax_amount,
                discount_amount=discount_amount,
                discount_type='fixed',
                amount_paid=amount_paid,
                payment_status=payment_status,
                notes=f"عملية بيع رقم {i+1}",
                created_at=sale_date,
                updated_at=sale_date
            )
            
            sales.append((sale, current_sale_items))
            
            # إدراج دفعي كل 1000 عملية بيع
            if len(sales) >= 1000:
                self._insert_sales_batch(sales)
                print(f"  ✅ تم إدراج {len(sales)} عملية بيع...")
                sales = []
        
        # إدراج المبيعات المتبقية
        if sales:
            self._insert_sales_batch(sales)
            print(f"  ✅ تم إدراج {len(sales)} عملية بيع أخيرة...")
        
        print(f"✅ تم إنشاء {sales_count:,} عملية بيع بنجاح!")

    def _insert_sales_batch(self, sales_batch):
        """إدراج دفعة من المبيعات وعناصرها"""
        try:
            for sale, sale_items in sales_batch:
                self.db.add(sale)
                self.db.flush()  # للحصول على sale.id
                
                # إضافة عناصر البيع
                for item_data in sale_items:
                    sale_item = SaleItem(
                        sale_id=sale.id,
                        **item_data
                    )
                    self.db.add(sale_item)
            
            self.db.commit()
        except Exception as e:
            print(f"❌ خطأ في إدراج المبيعات: {e}")
            self.db.rollback()

    def generate_debts(self, count=5000):
        """إنشاء ديون للعملاء"""
        print(f"💳 إنشاء {count:,} دين...")

        # الحصول على المبيعات غير المدفوعة أو المدفوعة جزئياً مع عملاء
        unpaid_sales = self.db.query(Sale).filter(
            Sale.payment_status.in_(['unpaid', 'partial']),
            Sale.customer_id.isnot(None)  # التأكد من وجود customer_id
        ).all()

        if not unpaid_sales:
            print("❌ لا توجد مبيعات غير مدفوعة مع عملاء لإنشاء ديون!")
            return

        debts = []
        for sale in unpaid_sales[:count]:
            # التأكد من وجود customer_id
            if sale.customer_id is None:
                continue

            remaining_amount = sale.total_amount - sale.amount_paid

            debt = CustomerDebt(
                customer_id=sale.customer_id,
                sale_id=sale.id,
                amount=remaining_amount,
                remaining_amount=remaining_amount,
                description=f"دين من فاتورة رقم {sale.id}",
                is_paid=False
            )
            debts.append(debt)
            
            # إدراج دفعي كل 1000 دين
            if len(debts) >= 1000:
                self.db.add_all(debts)
                self.db.commit()
                print(f"  ✅ تم إدراج {len(debts)} دين...")
                debts = []
        
        # إدراج الديون المتبقية
        if debts:
            self.db.add_all(debts)
            self.db.commit()
            print(f"  ✅ تم إدراج {len(debts)} دين أخير...")
        
        print(f"✅ تم إنشاء الديون بنجاح!")

    def print_statistics(self):
        """طباعة إحصائيات قاعدة البيانات"""
        print("\n📊 إحصائيات قاعدة البيانات النهائية:")
        print("=" * 50)
        
        users_count = self.db.query(User).count()
        products_count = self.db.query(Product).count()
        customers_count = self.db.query(Customer).count()
        sales_count = self.db.query(Sale).count()
        sale_items_count = self.db.query(SaleItem).count()
        debts_count = self.db.query(CustomerDebt).count()
        
        print(f"👥 إجمالي المستخدمين: {users_count:,}")
        print(f"📦 إجمالي المنتجات: {products_count:,}")
        print(f"👤 إجمالي العملاء: {customers_count:,}")
        print(f"💰 إجمالي المبيعات: {sales_count:,}")
        print(f"📋 إجمالي عناصر المبيعات: {sale_items_count:,}")
        print(f"💳 إجمالي الديون: {debts_count:,}")
        
        # إحصائيات مالية
        total_sales_amount = self.db.query(func.sum(Sale.total_amount)).scalar() or 0
        total_paid_amount = self.db.query(func.sum(Sale.amount_paid)).scalar() or 0
        total_debts_amount = self.db.query(func.sum(CustomerDebt.remaining_amount)).scalar() or 0
        
        print(f"\n💰 إجمالي قيمة المبيعات: {total_sales_amount:,.2f} د.ل")
        print(f"💵 إجمالي المبلغ المدفوع: {total_paid_amount:,.2f} د.ل")
        print(f"💳 إجمالي الديون المتبقية: {total_debts_amount:,.2f} د.ل")

    def run(self):
        """تشغيل مولد البيانات الكبيرة"""
        try:
            print("🚀 بدء إنشاء البيانات الكبيرة لاختبار الأداء...")
            print("=" * 60)
            
            if not self.get_admin_user():
                return
            
            # إنشاء البيانات بالتسلسل
            self.generate_products(10000)
            self.generate_customers(5000)
            self.generate_sales_and_items(50000)
            self.generate_debts(5000)
            
            # طباعة الإحصائيات النهائية
            self.print_statistics()
            
            print("\n🎉 تم إنشاء جميع البيانات الكبيرة بنجاح!")
            print("🔥 النظام جاهز الآن لاختبار الأداء مع البيانات الكبيرة!")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء البيانات: {e}")
            self.db.rollback()
        finally:
            self.db.close()

if __name__ == "__main__":
    generator = LargeDataGenerator()
    generator.run()
