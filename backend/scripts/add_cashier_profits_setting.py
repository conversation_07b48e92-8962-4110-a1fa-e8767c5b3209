#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add the show_cashier_profits setting to the database.
This setting controls whether cashiers can see today's profits in the dashboard.
"""

import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from database.session import get_db
from models.setting import Setting

def add_cashier_profits_setting():
    """Add the show_cashier_profits setting if it doesn't exist."""
    
    # Get database session
    db = next(get_db())
    
    try:
        # Check if the setting already exists
        existing_setting = db.query(Setting).filter(Setting.key == "show_cashier_profits").first()
        
        if existing_setting:
            print("✅ Setting 'show_cashier_profits' already exists.")
            print(f"   Current value: {existing_setting.value}")
            return
        
        # Create the new setting
        new_setting = Setting(
            key="show_cashier_profits",
            value="true",  # Default to true (enabled)
            description="السماح للمستخدمين العاديين (الكاشير) برؤية أرباح اليوم في لوحة التحكم"
        )
        
        db.add(new_setting)
        db.commit()
        
        print("✅ Successfully added 'show_cashier_profits' setting to the database.")
        print("   Default value: true (enabled)")
        print("   Description: السماح للمستخدمين العاديين (الكاشير) برؤية أرباح اليوم في لوحة التحكم")
        
    except Exception as e:
        print(f"❌ Error adding setting: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    print("🔧 Adding show_cashier_profits setting...")
    add_cashier_profits_setting()
    print("✨ Done!")
