#!/usr/bin/env python3
"""
Script لترحيل نظام أمان الأجهزة وإنشاء الجداول الجديدة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.session import engine, get_db
from models.device_security import ApprovedDevice, PendingDevice
from sqlalchemy import text
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_approved_devices_table():
    """
    إنشاء جدول الأجهزة المعتمدة
    """
    try:
        # إنشاء الجدول
        ApprovedDevice.metadata.create_all(bind=engine)
        logger.info("تم إنشاء جدول approved_devices بنجاح")
        
        # نقل الأجهزة المعتمدة من pending_devices
        db = next(get_db())
        try:
            # البحث عن الأجهزة المعتمدة في جدول pending_devices
            result = db.execute(text("""
                SELECT * FROM pending_devices 
                WHERE status = 'approved'
            """))
            
            approved_devices = result.fetchall()
            logger.info(f"تم العثور على {len(approved_devices)} جهاز معتمد للنقل")
            
            # نقل كل جهاز معتمد
            for device in approved_devices:
                # التحقق من عدم وجود الجهاز مسبقاً
                existing = db.execute(text("""
                    SELECT id FROM approved_devices 
                    WHERE device_id = :device_id
                """), {"device_id": device.device_id}).fetchone()
                
                if not existing:
                    # إدراج الجهاز في جدول approved_devices
                    db.execute(text("""
                        INSERT INTO approved_devices (
                            device_id, client_ip, hostname, device_type, system, platform,
                            user_agent, current_user, approved_by, approved_at, approval_notes,
                            first_access, last_access, access_count, created_at, updated_at
                        ) VALUES (
                            :device_id, :client_ip, :hostname, :device_type, :system, :platform,
                            :user_agent, :current_user, :reviewed_by, :reviewed_at, :review_notes,
                            :first_access, :last_access, :access_count, :created_at, :updated_at
                        )
                    """), {
                        "device_id": device.device_id,
                        "client_ip": device.client_ip,
                        "hostname": device.hostname,
                        "device_type": device.device_type,
                        "system": device.system,
                        "platform": device.platform,
                        "user_agent": device.user_agent,
                        "current_user": device.current_user,
                        "reviewed_by": device.reviewed_by,
                        "reviewed_at": device.reviewed_at,
                        "review_notes": device.review_notes,
                        "first_access": device.first_access,
                        "last_access": device.last_access,
                        "access_count": device.access_count,
                        "created_at": device.created_at,
                        "updated_at": device.updated_at
                    })
                    logger.info(f"تم نقل الجهاز المعتمد: {device.device_id}")
            
            db.commit()
            logger.info("تم نقل جميع الأجهزة المعتمدة بنجاح")
            
        except Exception as e:
            db.rollback()
            logger.error(f"خطأ في نقل الأجهزة المعتمدة: {e}")
            raise
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"خطأ في إنشاء جدول الأجهزة المعتمدة: {e}")
        raise

def verify_migration():
    """
    التحقق من نجاح الترحيل
    """
    try:
        db = next(get_db())
        try:
            # عدد الأجهزة المعتمدة في الجدول الجديد
            approved_count = db.execute(text("SELECT COUNT(*) FROM approved_devices")).scalar()
            
            # عدد الأجهزة المعتمدة في الجدول القديم
            pending_approved_count = db.execute(text("""
                SELECT COUNT(*) FROM pending_devices WHERE status = 'approved'
            """)).scalar()
            
            logger.info(f"عدد الأجهزة المعتمدة في الجدول الجديد: {approved_count}")
            logger.info(f"عدد الأجهزة المعتمدة في الجدول القديم: {pending_approved_count}")
            
            if approved_count == pending_approved_count:
                logger.info("✅ تم الترحيل بنجاح - الأعداد متطابقة")
                return True
            else:
                logger.warning("⚠️ الأعداد غير متطابقة - قد تحتاج لمراجعة الترحيل")
                return False
                
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"خطأ في التحقق من الترحيل: {e}")
        return False

def main():
    """
    تشغيل عملية الترحيل
    """
    logger.info("بدء ترحيل نظام أمان الأجهزة...")
    
    try:
        # إنشاء جدول الأجهزة المعتمدة ونقل البيانات
        create_approved_devices_table()
        
        # التحقق من نجاح الترحيل
        if verify_migration():
            logger.info("🎉 تم إكمال الترحيل بنجاح!")
        else:
            logger.warning("⚠️ تم الترحيل مع تحذيرات")
            
    except Exception as e:
        logger.error(f"❌ فشل في الترحيل: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
