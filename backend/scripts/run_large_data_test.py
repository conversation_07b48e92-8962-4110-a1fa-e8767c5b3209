#!/usr/bin/env python3
"""
تشغيل اختبار البيانات الكبيرة مع مراقبة الأداء
Run Large Data Test with Performance Monitoring
"""
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

import time
import threading
from add_large_test_data import LargeDataGenerator
from performance_monitor import PerformanceMonitor

def run_performance_monitoring():
    """تشغيل مراقبة الأداء في خيط منفصل"""
    monitor = PerformanceMonitor()
    try:
        print("🔍 بدء مراقبة الأداء...")
        monitor.monitor_during_data_insertion(check_interval=10)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف مراقبة الأداء.")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار البيانات الكبيرة مع مراقبة الأداء")
    print("=" * 60)
    
    # فحص حالة قاعدة البيانات قبل البدء
    print("📊 فحص حالة قاعدة البيانات الحالية...")
    monitor = PerformanceMonitor()
    monitor.run_performance_test()
    
    # السؤال عن المتابعة
    print("\n" + "="*60)
    response = input("هل تريد المتابعة مع إضافة البيانات الكبيرة؟ (y/n): ")
    
    if response.lower() not in ['y', 'yes', 'نعم']:
        print("❌ تم إلغاء العملية.")
        return
    
    # بدء مراقبة الأداء في خيط منفصل
    monitor_thread = threading.Thread(target=run_performance_monitoring, daemon=True)
    monitor_thread.start()
    
    # تشغيل مولد البيانات الكبيرة
    try:
        generator = LargeDataGenerator()
        
        print("\n🏭 بدء إنشاء البيانات الكبيرة...")
        print("⚠️ هذه العملية قد تستغرق عدة دقائق...")
        print("💡 يمكنك مراقبة الأداء في الخلفية")
        
        start_time = time.time()
        generator.run()
        end_time = time.time()
        
        duration = end_time - start_time
        print(f"\n⏱️ إجمالي وقت التنفيذ: {duration:.2f} ثانية ({duration/60:.2f} دقيقة)")
        
        # فحص حالة قاعدة البيانات بعد الانتهاء
        print("\n📊 فحص حالة قاعدة البيانات بعد إضافة البيانات...")
        final_monitor = PerformanceMonitor()
        final_monitor.run_performance_test()
        
        print("\n🎉 تم الانتهاء من اختبار البيانات الكبيرة بنجاح!")
        print("🔥 النظام جاهز الآن لاختبار الأداء!")
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف العملية بواسطة المستخدم.")
    except Exception as e:
        print(f"\n❌ حدث خطأ أثناء إنشاء البيانات: {e}")

if __name__ == "__main__":
    main()
