#!/usr/bin/env python3
"""
Script to fix debt data accuracy issues.
This script recalculates and updates the remaining_amount field for all debts
to ensure data accuracy.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import sqlite3
from datetime import datetime

def fix_debt_data_accuracy():
    """
    Fix debt data accuracy by recalculating remaining amounts.
    """
    db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'smartpos.db')
    
    if not os.path.exists(db_path):
        print("قاعدة البيانات غير موجودة")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("=== بدء إصلاح دقة بيانات المديونية ===")
        
        # جلب جميع الديون
        cursor.execute("SELECT id, amount FROM customer_debts")
        debts = cursor.fetchall()
        
        fixed_count = 0
        total_difference = 0
        
        for debt_id, original_amount in debts:
            # حساب إجمالي المدفوعات لهذا الدين
            cursor.execute(
                "SELECT COALESCE(SUM(amount), 0) FROM debt_payments WHERE debt_id = ?",
                (debt_id,)
            )
            paid_amount = cursor.fetchone()[0]
            
            # حساب المبلغ المتبقي الصحيح
            correct_remaining = max(0, original_amount - paid_amount)
            
            # جلب المبلغ المتبقي المخزن حالياً
            cursor.execute(
                "SELECT remaining_amount FROM customer_debts WHERE id = ?",
                (debt_id,)
            )
            current_remaining = cursor.fetchone()[0]
            
            # إذا كان هناك فرق، قم بالتحديث
            if abs(correct_remaining - current_remaining) > 0.01:  # تجاهل الفروق الصغيرة جداً
                cursor.execute(
                    "UPDATE customer_debts SET remaining_amount = ? WHERE id = ?",
                    (correct_remaining, debt_id)
                )
                
                difference = abs(correct_remaining - current_remaining)
                total_difference += difference
                fixed_count += 1
                
                print(f"تم إصلاح الدين {debt_id}: {current_remaining} -> {correct_remaining} (فرق: {difference})")
                
                # تحديث حالة الدفع
                if correct_remaining <= 0:
                    cursor.execute(
                        "UPDATE customer_debts SET is_paid = 1 WHERE id = ?",
                        (debt_id,)
                    )
                    print(f"  تم تحديث حالة الدين {debt_id} إلى مدفوع")
                else:
                    cursor.execute(
                        "UPDATE customer_debts SET is_paid = 0 WHERE id = ?",
                        (debt_id,)
                    )
        
        # حفظ التغييرات
        conn.commit()
        
        print(f"\n=== تم الانتهاء من الإصلاح ===")
        print(f"عدد الديون المُصلحة: {fixed_count}")
        print(f"إجمالي الفرق المُصحح: {total_difference}")
        
        # التحقق من النتائج
        print("\n=== التحقق من النتائج ===")
        
        # إجمالي المبالغ
        cursor.execute("SELECT SUM(amount) FROM customer_debts")
        total_amount = cursor.fetchone()[0] or 0
        
        cursor.execute("SELECT SUM(amount) FROM debt_payments")
        paid_amount = cursor.fetchone()[0] or 0
        
        cursor.execute("SELECT SUM(remaining_amount) FROM customer_debts")
        remaining_amount = cursor.fetchone()[0] or 0
        
        calculated_remaining = total_amount - paid_amount
        
        print(f"إجمالي مبلغ الديون: {total_amount}")
        print(f"إجمالي المبلغ المدفوع: {paid_amount}")
        print(f"المبلغ المتبقي المحسوب: {calculated_remaining}")
        print(f"المبلغ المتبقي المخزن: {remaining_amount}")
        print(f"الفرق النهائي: {abs(calculated_remaining - remaining_amount)}")
        
        if abs(calculated_remaining - remaining_amount) < 0.01:
            print("✅ تم إصلاح دقة البيانات بنجاح!")
        else:
            print("⚠️ لا تزال هناك فروق في البيانات")
            
    except Exception as e:
        print(f"خطأ أثناء إصلاح البيانات: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    fix_debt_data_accuracy()
