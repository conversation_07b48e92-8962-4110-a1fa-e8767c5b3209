import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from database.session import SessionLocal
from models.product import Product
from models.user import User

def add_test_data():
    db = SessionLocal()
    try:
        # Check if we already have an admin user
        admin = db.query(User).filter(User.email == "<EMAIL>").first()
        if not admin:
            print("Error: Admin user not found. Please create an admin user first.")
            return

        # Sample products
        products = [
            {
                "name": "قهوة عربية",
                "barcode": "1234567890",
                "description": "قهوة عربية فاخرة",
                "price": 25.0,
                "cost_price": 15.0,
                "quantity": 100,
                "min_quantity": 20,
                "category": "مشروبات",
                "unit": "piece",
                "created_by": admin.id
            },
            {
                "name": "شاي أخضر",
                "barcode": "1234567891",
                "description": "شاي أخضر صيني",
                "price": 15.0,
                "cost_price": 8.0,
                "quantity": 150,
                "min_quantity": 30,
                "category": "مشروبات",
                "unit": "piece",
                "created_by": admin.id
            },
            {
                "name": "كيك شوكولاتة",
                "barcode": "1234567892",
                "description": "كيك شوكولاتة طازج",
                "price": 35.0,
                "cost_price": 20.0,
                "quantity": 50,
                "min_quantity": 10,
                "category": "حلويات",
                "unit": "piece",
                "created_by": admin.id
            },
            {
                "name": "عصير برتقال",
                "barcode": "1234567893",
                "description": "عصير برتقال طبيعي",
                "price": 12.0,
                "cost_price": 6.0,
                "quantity": 80,
                "min_quantity": 15,
                "category": "مشروبات",
                "unit": "piece",
                "created_by": admin.id
            },
            {
                "name": "سندويش دجاج",
                "barcode": "1234567894",
                "description": "سندويش دجاج مشوي",
                "price": 28.0,
                "cost_price": 18.0,
                "quantity": 40,
                "min_quantity": 8,
                "category": "وجبات",
                "unit": "piece",
                "created_by": admin.id
            }
        ]

        # Add products if they don't exist
        for product_data in products:
            existing_product = db.query(Product).filter(
                Product.barcode == product_data["barcode"]
            ).first()

            if not existing_product:
                product = Product(**product_data)
                db.add(product)
                print(f"Added product: {product_data['name']}")

        db.commit()
        print("Test data added successfully!")

    except Exception as e:
        print(f"Error adding test data: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    add_test_data() 