import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from database.session import <PERSON><PERSON>ocal
from models.user import User, UserRole
from passlib.context import CryptContext

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def create_admin():
    db = SessionLocal()
    try:
        # Check if admin already exists
        admin = db.query(User).filter(User.email == "<EMAIL>").first()
        if admin:
            print("Admin user already exists!")
            return

        # Create admin user
        admin = User(
            username="admin",
            full_name="مدير النظام",
            email="<EMAIL>",
            hashed_password=get_password_hash("admin123"),
            role=UserRole.ADMIN,
            is_active=True
        )
        db.add(admin)
        db.commit()
        print("Admin user created successfully!")

    except Exception as e:
        print(f"Error creating admin user: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_admin() 