#!/usr/bin/env python3
"""
فحص حالة قاعدة البيانات الحالية
Check Current Database Status
"""
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

import os
from database.session import SessionLocal
from models.user import User
from models.product import Product
from models.customer import Customer
from models.sale import Sale, SaleItem
from models.customer import CustomerDebt
from sqlalchemy import func

def check_database_status():
    """فحص حالة قاعدة البيانات"""
    print("🔍 فحص حالة قاعدة البيانات...")
    print("=" * 50)
    
    # فحص وجود ملف قاعدة البيانات
    db_path = "smartpos.db"
    if os.path.exists(db_path):
        size_bytes = os.path.getsize(db_path)
        size_mb = size_bytes / (1024 * 1024)
        print(f"✅ ملف قاعدة البيانات موجود: {db_path}")
        print(f"📏 حجم قاعدة البيانات: {size_mb:.2f} MB")
    else:
        print(f"❌ ملف قاعدة البيانات غير موجود: {db_path}")
        return
    
    db = None
    try:
        db = SessionLocal()
        
        print(f"\n📊 إحصائيات الجداول:")
        print("-" * 30)
        
        # عدد السجلات في كل جدول
        tables_info = [
            ("المستخدمون", User),
            ("المنتجات", Product),
            ("العملاء", Customer),
            ("المبيعات", Sale),
            ("عناصر المبيعات", SaleItem),
            ("الديون", CustomerDebt)
        ]
        
        total_records = 0
        for table_name, model in tables_info:
            try:
                count = db.query(model).count()
                total_records += count
                print(f"  📋 {table_name}: {count:,}")
            except Exception as e:
                print(f"  ❌ {table_name}: خطأ - {e}")
        
        print(f"\n📊 إجمالي السجلات: {total_records:,}")
        
        # إحصائيات مفصلة
        print(f"\n💰 الإحصائيات المالية:")
        print("-" * 30)
        
        try:
            total_sales = db.query(func.sum(Sale.total_amount)).scalar() or 0
            total_paid = db.query(func.sum(Sale.amount_paid)).scalar() or 0
            total_debts = db.query(func.sum(CustomerDebt.remaining_amount)).filter(
                CustomerDebt.is_paid == False
            ).scalar() or 0
            
            print(f"  💵 إجمالي قيمة المبيعات: {total_sales:,.2f} د.ل")
            print(f"  💰 إجمالي المبلغ المدفوع: {total_paid:,.2f} د.ل")
            print(f"  💳 إجمالي الديون المتبقية: {total_debts:,.2f} د.ل")
            
        except Exception as e:
            print(f"  ❌ خطأ في الإحصائيات المالية: {e}")
        
        # إحصائيات المنتجات
        print(f"\n📦 إحصائيات المنتجات:")
        print("-" * 30)
        
        try:
            active_products = db.query(Product).filter(Product.is_active == True).count()
            inactive_products = db.query(Product).filter(Product.is_active == False).count()
            low_stock = db.query(Product).filter(
                Product.quantity <= Product.min_quantity,
                Product.is_active == True
            ).count()
            
            print(f"  ✅ المنتجات النشطة: {active_products:,}")
            print(f"  ❌ المنتجات غير النشطة: {inactive_products:,}")
            print(f"  ⚠️ منتجات قليلة المخزون: {low_stock:,}")
            
            # الفئات
            categories = db.query(Product.category, func.count(Product.id)).group_by(
                Product.category
            ).all()
            
            if categories:
                print(f"  📂 الفئات:")
                for category, count in categories:
                    category_name = category or "بدون فئة"
                    print(f"    - {category_name}: {count:,}")
            
        except Exception as e:
            print(f"  ❌ خطأ في إحصائيات المنتجات: {e}")
        
        # إحصائيات العملاء
        print(f"\n👥 إحصائيات العملاء:")
        print("-" * 30)
        
        try:
            active_customers = db.query(Customer).filter(Customer.is_active == True).count()
            inactive_customers = db.query(Customer).filter(Customer.is_active == False).count()
            customers_with_debts = db.query(CustomerDebt.customer_id).filter(
                CustomerDebt.is_paid == False
            ).distinct().count()
            
            print(f"  ✅ العملاء النشطون: {active_customers:,}")
            print(f"  ❌ العملاء غير النشطين: {inactive_customers:,}")
            print(f"  💳 عملاء لديهم ديون: {customers_with_debts:,}")
            
        except Exception as e:
            print(f"  ❌ خطأ في إحصائيات العملاء: {e}")
        
        # إحصائيات المبيعات
        print(f"\n💰 إحصائيات المبيعات:")
        print("-" * 30)
        
        try:
            paid_sales = db.query(Sale).filter(Sale.payment_status == 'paid').count()
            partial_sales = db.query(Sale).filter(Sale.payment_status == 'partial').count()
            unpaid_sales = db.query(Sale).filter(Sale.payment_status == 'unpaid').count()
            
            print(f"  ✅ مبيعات مدفوعة: {paid_sales:,}")
            print(f"  🔄 مبيعات مدفوعة جزئياً: {partial_sales:,}")
            print(f"  ❌ مبيعات غير مدفوعة: {unpaid_sales:,}")
            
            # طرق الدفع
            payment_methods = db.query(
                Sale.payment_method, 
                func.count(Sale.id)
            ).group_by(Sale.payment_method).all()
            
            if payment_methods:
                print(f"  💳 طرق الدفع:")
                for method, count in payment_methods:
                    print(f"    - {method}: {count:,}")
            
        except Exception as e:
            print(f"  ❌ خطأ في إحصائيات المبيعات: {e}")
        
        # آخر العمليات
        print(f"\n🕒 آخر العمليات:")
        print("-" * 30)
        
        try:
            last_sale = db.query(Sale).order_by(Sale.created_at.desc()).first()
            if last_sale:
                print(f"  💰 آخر عملية بيع: {last_sale.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
            
            last_product = db.query(Product).order_by(Product.created_at.desc()).first()
            if last_product:
                print(f"  📦 آخر منتج مضاف: {last_product.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
            
            last_customer = db.query(Customer).order_by(Customer.created_at.desc()).first()
            if last_customer:
                print(f"  👤 آخر عميل مضاف: {last_customer.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
            
        except Exception as e:
            print(f"  ❌ خطأ في آخر العمليات: {e}")
        
        print(f"\n✅ انتهى فحص قاعدة البيانات!")
        
        # تقييم حجم البيانات
        if total_records < 1000:
            print(f"\n📊 تقييم: قاعدة بيانات صغيرة ({total_records:,} سجل)")
            print("💡 يمكن إضافة بيانات كبيرة لاختبار الأداء")
        elif total_records < 10000:
            print(f"\n📊 تقييم: قاعدة بيانات متوسطة ({total_records:,} سجل)")
            print("💡 يمكن إضافة المزيد من البيانات لاختبار أداء أفضل")
        else:
            print(f"\n📊 تقييم: قاعدة بيانات كبيرة ({total_records:,} سجل)")
            print("🔥 مناسبة لاختبار الأداء!")
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
    finally:
        if db is not None:
            db.close()

if __name__ == "__main__":
    check_database_status()
