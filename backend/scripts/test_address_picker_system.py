#!/usr/bin/env python3
"""
سكريبت اختبار نظام اختيار العنوان من الخريطة
يتحقق من صحة عمل النظام من قاعدة البيانات إلى الواجهة الأمامية
"""

import sys
import os
import asyncio
import json
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.session import get_db
from models.setting import Setting
from sqlalchemy.orm import Session

class AddressPickerSystemTester:
    """فئة اختبار نظام اختيار العنوان من الخريطة"""
    
    def __init__(self):
        self.test_results = []
        self.db = next(get_db())
    
    def log_test(self, test_name: str, success: bool, message: str):
        """تسجيل نتيجة اختبار"""
        status = "✅ نجح" if success else "❌ فشل"
        result = {
            "test": test_name,
            "success": success,
            "message": message,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        print(f"{status} {test_name}: {message}")
    
    def test_database_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            # محاولة جلب إعداد واحد
            setting = self.db.query(Setting).first()
            self.log_test(
                "اتصال قاعدة البيانات",
                True,
                f"تم الاتصال بنجاح. عدد الإعدادات: {self.db.query(Setting).count()}"
            )
            return True
        except Exception as e:
            self.log_test(
                "اتصال قاعدة البيانات",
                False,
                f"فشل الاتصال: {str(e)}"
            )
            return False
    
    def test_store_address_setting(self):
        """اختبار وجود إعداد عنوان المؤسسة"""
        try:
            store_address = self.db.query(Setting).filter(
                Setting.key == 'store_address'
            ).first()
            
            if store_address:
                self.log_test(
                    "إعداد عنوان المؤسسة",
                    True,
                    f"الإعداد موجود. القيمة: '{store_address.value[:50]}...'"
                )
                return True, store_address.value
            else:
                # إنشاء إعداد افتراضي
                default_address = "طرابلس، ليبيا"
                new_setting = Setting(
                    key='store_address',
                    value=default_address,
                    description='عنوان المؤسسة'
                )
                self.db.add(new_setting)
                self.db.commit()
                
                self.log_test(
                    "إعداد عنوان المؤسسة",
                    True,
                    f"تم إنشاء إعداد افتراضي: {default_address}"
                )
                return True, default_address
                
        except Exception as e:
            self.log_test(
                "إعداد عنوان المؤسسة",
                False,
                f"خطأ في الوصول للإعداد: {str(e)}"
            )
            return False, None
    
    def test_address_update(self):
        """اختبار تحديث عنوان المؤسسة"""
        try:
            test_address = "شارع الجمهورية، طرابلس، ليبيا - تم التحديث من الخريطة"
            
            # البحث عن الإعداد
            setting = self.db.query(Setting).filter(
                Setting.key == 'store_address'
            ).first()
            
            if setting:
                old_value = setting.value
                setting.value = test_address
                self.db.commit()
                
                # التحقق من التحديث
                updated_setting = self.db.query(Setting).filter(
                    Setting.key == 'store_address'
                ).first()
                
                if updated_setting.value == test_address:
                    self.log_test(
                        "تحديث عنوان المؤسسة",
                        True,
                        f"تم التحديث من '{old_value[:30]}...' إلى '{test_address[:30]}...'"
                    )
                    return True
                else:
                    self.log_test(
                        "تحديث عنوان المؤسسة",
                        False,
                        "فشل في حفظ التحديث"
                    )
                    return False
            else:
                self.log_test(
                    "تحديث عنوان المؤسسة",
                    False,
                    "الإعداد غير موجود"
                )
                return False
                
        except Exception as e:
            self.log_test(
                "تحديث عنوان المؤسسة",
                False,
                f"خطأ في التحديث: {str(e)}"
            )
            return False
    
    def test_settings_retrieval(self):
        """اختبار جلب جميع الإعدادات"""
        try:
            settings = self.db.query(Setting).all()
            settings_dict = {s.key: s.value for s in settings}
            
            required_settings = [
                'store_name', 'store_address', 'store_phone', 'store_email'
            ]
            
            missing_settings = []
            for req_setting in required_settings:
                if req_setting not in settings_dict:
                    missing_settings.append(req_setting)
            
            if not missing_settings:
                self.log_test(
                    "جلب الإعدادات",
                    True,
                    f"تم جلب {len(settings)} إعداد بنجاح"
                )
                return True, settings_dict
            else:
                self.log_test(
                    "جلب الإعدادات",
                    False,
                    f"إعدادات مفقودة: {', '.join(missing_settings)}"
                )
                return False, None
                
        except Exception as e:
            self.log_test(
                "جلب الإعدادات",
                False,
                f"خطأ في جلب الإعدادات: {str(e)}"
            )
            return False, None
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🧪 بدء اختبار نظام اختيار العنوان من الخريطة")
        print("=" * 60)
        
        # اختبار الاتصال بقاعدة البيانات
        if not self.test_database_connection():
            print("❌ فشل في الاتصال بقاعدة البيانات. توقف الاختبار.")
            return False
        
        # اختبار إعداد عنوان المؤسسة
        success, address = self.test_store_address_setting()
        if not success:
            print("❌ فشل في اختبار إعداد عنوان المؤسسة.")
            return False
        
        # اختبار تحديث العنوان
        if not self.test_address_update():
            print("❌ فشل في اختبار تحديث العنوان.")
            return False
        
        # اختبار جلب الإعدادات
        success, settings = self.test_settings_retrieval()
        if not success:
            print("❌ فشل في اختبار جلب الإعدادات.")
            return False
        
        print("\n" + "=" * 60)
        print("📊 ملخص النتائج:")
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"إجمالي الاختبارات: {total_tests}")
        print(f"نجح: {passed_tests}")
        print(f"فشل: {failed_tests}")
        print(f"معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests == 0:
            print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
            return True
        else:
            print(f"\n⚠️ فشل {failed_tests} اختبار. يرجى مراجعة المشاكل أعلاه.")
            return False
    
    def __del__(self):
        """إغلاق اتصال قاعدة البيانات"""
        if hasattr(self, 'db'):
            self.db.close()

def main():
    """الدالة الرئيسية"""
    tester = AddressPickerSystemTester()
    success = tester.run_all_tests()
    
    # حفظ النتائج في ملف
    results_file = "address_picker_test_results.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(tester.test_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 تم حفظ النتائج في: {results_file}")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
