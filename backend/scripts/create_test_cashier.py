#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create a test cashier user for testing the profits visibility feature.
"""

import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from passlib.context import CryptContext
from database.session import get_db
from models.user import User, UserRole

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def create_test_cashier():
    """Create a test cashier user."""
    
    # Get database session
    db = next(get_db())
    
    try:
        # Check if the cashier already exists
        existing_cashier = db.query(User).filter(User.username == "cashier").first()
        
        if existing_cashier:
            print("✅ Test cashier user already exists.")
            print(f"   Username: {existing_cashier.username}")
            print(f"   Role: {existing_cashier.role}")
            return
        
        # Create the test cashier
        cashier_user = User(
            username="cashier",
            full_name="Test Cashier",
            email="<EMAIL>",
            hashed_password=get_password_hash("cashier123"),
            role=UserRole.CASHIER,
            is_active=True
        )
        
        db.add(cashier_user)
        db.commit()
        
        print("✅ Successfully created test cashier user.")
        print("   Username: cashier")
        print("   Password: cashier123")
        print("   Role: CASHIER")
        print("   Email: <EMAIL>")
        
    except Exception as e:
        print(f"❌ Error creating cashier: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    print("👤 Creating test cashier user...")
    create_test_cashier()
    print("✨ Done!")
