#!/usr/bin/env python3
"""
إصلاح توافق PostgreSQL - إنشاء جدول system_logs بالصيغة الصحيحة
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv
from sqlalchemy import create_engine, text
import logging

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# تحديد مسار ملف .env
env_path = Path(__file__).parent / ".env"
load_dotenv(dotenv_path=env_path)

# الحصول على URL قاعدة البيانات
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql+psycopg2://postgres:password@localhost:5432/smartpos_db")

def fix_system_logs_table():
    """إصلاح جدول system_logs ليكون متوافق مع PostgreSQL"""
    
    print("=" * 60)
    print("🔧 إصلاح توافق PostgreSQL - جدول system_logs")
    print("=" * 60)
    
    try:
        # إنشاء اتصال بقاعدة البيانات
        engine = create_engine(DATABASE_URL)
        
        with engine.connect() as conn:
            # حذف الجدول القديم إذا كان موجوداً
            print("🗑️ حذف جدول system_logs القديم...")
            conn.execute(text("DROP TABLE IF EXISTS system_logs CASCADE"))
            conn.commit()
            
            # إنشاء جدول system_logs جديد متوافق مع PostgreSQL
            print("🆕 إنشاء جدول system_logs جديد...")
            create_table_sql = """
            CREATE TABLE system_logs (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                level TEXT NOT NULL CHECK (level IN ('INFO', 'WARNING', 'ERROR', 'CRITICAL')),
                source TEXT NOT NULL CHECK (source IN ('FRONTEND', 'BACKEND', 'DATABASE', 'SYSTEM')),
                message TEXT NOT NULL,
                details TEXT,
                stack_trace TEXT,
                user_id INTEGER,
                session_id TEXT,
                resolved BOOLEAN DEFAULT FALSE,
                resolution_notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
            """
            
            conn.execute(text(create_table_sql))
            conn.commit()
            
            # إنشاء فهارس محسنة
            print("📊 إنشاء فهارس محسنة...")
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_system_logs_timestamp ON system_logs(timestamp)",
                "CREATE INDEX IF NOT EXISTS idx_system_logs_level ON system_logs(level)",
                "CREATE INDEX IF NOT EXISTS idx_system_logs_source ON system_logs(source)",
                "CREATE INDEX IF NOT EXISTS idx_system_logs_resolved ON system_logs(resolved)",
                "CREATE INDEX IF NOT EXISTS idx_system_logs_user_id ON system_logs(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_system_logs_level_timestamp ON system_logs(level, timestamp)",
            ]
            
            for index_sql in indexes:
                conn.execute(text(index_sql))
            
            conn.commit()
            
            # التحقق من إنشاء الجدول
            print("✅ التحقق من إنشاء الجدول...")
            result = conn.execute(text("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = 'system_logs' 
                ORDER BY ordinal_position
            """))
            
            columns = result.fetchall()
            print(f"📋 تم إنشاء الجدول بنجاح مع {len(columns)} عمود:")
            for col in columns:
                print(f"   - {col[0]}: {col[1]} ({'NULL' if col[2] == 'YES' else 'NOT NULL'})")
            
            # إدراج سجل اختبار
            print("🧪 إدراج سجل اختبار...")
            conn.execute(text("""
                INSERT INTO system_logs (level, source, message, details)
                VALUES ('INFO', 'SYSTEM', 'تم إصلاح جدول system_logs بنجاح', 'PostgreSQL compatibility fix applied')
            """))
            conn.commit()
            
            # التحقق من السجل
            result = conn.execute(text("SELECT COUNT(*) FROM system_logs"))
            count = result.scalar()
            print(f"✅ تم إدراج {count} سجل اختبار")
            
        print("=" * 60)
        print("✅ تم إصلاح جدول system_logs بنجاح!")
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح جدول system_logs: {e}")
        return False

def fix_apscheduler_jobs_table():
    """التأكد من وجود جدول apscheduler_jobs"""
    
    print("🔧 التحقق من جدول apscheduler_jobs...")
    
    try:
        engine = create_engine(DATABASE_URL)
        
        with engine.connect() as conn:
            # التحقق من وجود الجدول
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'apscheduler_jobs'
                )
            """))
            
            exists = result.scalar()
            
            if exists:
                print("✅ جدول apscheduler_jobs موجود")
                # عرض عدد المهام
                result = conn.execute(text("SELECT COUNT(*) FROM apscheduler_jobs"))
                count = result.scalar()
                print(f"📊 عدد المهام المجدولة: {count}")
            else:
                print("ℹ️ جدول apscheduler_jobs سيتم إنشاؤه تلقائياً بواسطة APScheduler")
                
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص جدول apscheduler_jobs: {e}")
        return False

def test_websocket_compatibility():
    """اختبار توافق WebSocket مع PostgreSQL"""

    print("🔌 اختبار توافق WebSocket مع PostgreSQL...")

    try:
        engine = create_engine(DATABASE_URL)

        with engine.connect() as conn:
            # اختبار جداول المحادثة
            tables_to_check = [
                'chat_rooms',
                'chat_room_members',
                'chat_messages',
                'user_online_status'
            ]

            for table in tables_to_check:
                result = conn.execute(text(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = 'public'
                        AND table_name = '{table}'
                    )
                """))

                exists = result.scalar()
                if exists:
                    # عرض عدد الصفوف
                    count_result = conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
                    count = count_result.scalar()
                    print(f"✅ جدول {table}: موجود ({count} صف)")
                else:
                    print(f"❌ جدول {table}: غير موجود")
                    return False

            # اختبار إدراج رسالة تجريبية
            print("🧪 اختبار إدراج رسالة تجريبية...")
            conn.execute(text("""
                INSERT INTO chat_messages (sender_id, receiver_id, content, message_type, status, created_at)
                VALUES (1, 2, 'اختبار توافق PostgreSQL مع WebSocket', 'TEXT', 'SENT', NOW())
                ON CONFLICT DO NOTHING
            """))
            conn.commit()

            # التحقق من الرسالة
            result = conn.execute(text("""
                SELECT COUNT(*) FROM chat_messages
                WHERE content LIKE '%اختبار توافق PostgreSQL%'
            """))
            count = result.scalar()

            if count > 0:
                print(f"✅ تم إدراج واسترجاع الرسالة بنجاح ({count} رسالة)")
            else:
                print("❌ فشل في إدراج أو استرجاع الرسالة")
                return False

        print("✅ WebSocket متوافق مع PostgreSQL بنجاح!")
        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار WebSocket: {e}")
        return False

def main():
    """الدالة الرئيسية"""

    print("🐘 بدء إصلاح توافق PostgreSQL...")

    # التحقق من الاتصال بقاعدة البيانات
    try:
        engine = create_engine(DATABASE_URL)
        with engine.connect() as conn:
            result = conn.execute(text("SELECT version()"))
            version = result.scalar()
            print(f"✅ متصل بـ PostgreSQL: {version}")
    except Exception as e:
        print(f"❌ فشل الاتصال بقاعدة البيانات: {e}")
        return False

    # إصلاح الجداول
    success = True
    success &= fix_system_logs_table()
    success &= fix_apscheduler_jobs_table()
    success &= test_websocket_compatibility()

    if success:
        print("\n🎉 تم إصلاح جميع مشاكل التوافق بنجاح!")
        print("✅ النظام جاهز للعمل مع PostgreSQL")
        print("✅ WebSocket يعمل بشكل صحيح مع PostgreSQL")
    else:
        print("\n❌ فشل في إصلاح بعض المشاكل")
        return False

    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
