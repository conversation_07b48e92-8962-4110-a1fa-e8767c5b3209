# 🚀 دليل تشغيل خادم SmartPOS

## 📋 طرق تشغيل الخادم

### 1. 🐍 الطريقة المتقدمة (<PERSON> Script)
```bash
python3 run_server_8002.py
```

**الميزات:**
- ✅ فحص شامل للبيئة والمتطلبات
- ✅ رسائل تفصيلية باللغتين العربية والإنجليزية
- ✅ فحص إصدار Python ونظام التشغيل
- ✅ التحقق من وجود جميع الملفات المطلوبة
- ✅ إعداد متغيرات البيئة تلقائياً
- ✅ دعم نظامي Linux و Windows

### 2. 🔧 الطريقة السريعة (Bash Script)
```bash
./start_server.sh
```

**الميزات:**
- ⚡ تشغيل سريع ومباشر
- ✅ تفعيل البيئة الافتراضية تلقائياً
- ✅ فحص أساسي للمتطلبات
- 🐧 مخصص لأنظمة Linux/Unix

### 3. 🛠️ الطريقة اليدوية
```bash
# تفعيل البيئة الافتراضية
source venv/bin/activate

# تشغيل الخادم
uvicorn main:app --reload --host 0.0.0.0 --port 8002
```

## 🔧 المتطلبات

### 📋 المتطلبات الأساسية:
- **🐍 Python:** 3.8 أو أحدث (مُثبت: 3.12.3)
- **📦 البيئة الافتراضية:** مُفعلة في `venv/`
- **🗄️ قاعدة البيانات:** PostgreSQL (مُكونة)
- **📄 ملف البيئة:** `.env` (موجود)

### 📦 الحزم المطلوبة:
```
fastapi>=0.68.0
uvicorn>=0.15.0
sqlalchemy>=1.4.0
psycopg2-binary>=2.9.0
... (25 حزمة إضافية)
```

## 🌐 معلومات الخادم

### 📍 عناوين الوصول:
- **محلي:** http://localhost:8002
- **الشبكة المحلية:** http://*************:8002
- **API Documentation:** http://localhost:8002/docs

### ⚙️ إعدادات الخادم:
- **البورت:** 8002
- **المضيف:** 0.0.0.0 (جميع الواجهات)
- **إعادة التحميل:** مُفعل (--reload)
- **مستوى السجلات:** info

## 🛠️ استكشاف الأخطاء

### ❌ مشاكل شائعة وحلولها:

#### 1. البيئة الافتراضية غير موجودة
```bash
# الحل
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

#### 2. خطأ في الصلاحيات
```bash
# الحل
chmod +x start_server.sh
chmod +x run_server_8002.py
```

#### 3. البورت 8002 مُستخدم
```bash
# فحص البورت
sudo netstat -tlnp | grep :8002

# إيقاف العملية
sudo kill -9 [PID]
```

#### 4. مشاكل قاعدة البيانات
```bash
# فحص حالة PostgreSQL
sudo systemctl status postgresql

# إعادة تشغيل PostgreSQL
sudo systemctl restart postgresql
```

## 📊 مراقبة الخادم

### 📈 معلومات الأداء:
- **استهلاك الذاكرة:** مُحسن للبيانات الكبيرة
- **تجميع الاتصالات:** 20 اتصال أساسي + 30 إضافي
- **Cache:** Redis مع تنظيف تلقائي
- **المهام المجدولة:** 4 مهام نشطة

### 🔍 السجلات:
- **مستوى السجلات:** INFO
- **التنسيق:** مُحسن للقراءة
- **اللغة:** عربي/إنجليزي

## 🎯 نصائح للاستخدام

### ✅ أفضل الممارسات:
1. **استخدم `run_server_8002.py`** للتطوير والاختبار
2. **استخدم `start_server.sh`** للتشغيل السريع
3. **راقب السجلات** لمتابعة حالة النظام
4. **تأكد من تحديث المتطلبات** بانتظام

### 🔄 إعادة التشغيل:
```bash
# إيقاف الخادم
Ctrl+C

# إعادة التشغيل
python3 run_server_8002.py
```

## 📞 الدعم

### 🆘 في حالة المشاكل:
1. تحقق من السجلات أعلاه
2. راجع ملف `SYSTEM_MEMORY.md`
3. استخدم `python3 run_server_8002.py --help`
4. تأكد من إعدادات `.env`

---

**✅ النظام جاهز للاستخدام الإنتاجي!**
