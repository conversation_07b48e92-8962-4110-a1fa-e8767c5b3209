#!/usr/bin/env python3
"""
ملف تشغيل خادم SmartPOS مع تفعيل البيئة الافتراضية تلقائياً
SmartPOS Server Runner with automatic virtual environment activation
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """تشغيل الخادم مع تفعيل البيئة الافتراضية"""
    
    # الحصول على مسار المجلد الحالي
    current_dir = Path(__file__).parent.absolute()
    venv_path = current_dir / "venv"
    python_venv = venv_path / "bin" / "python"
    
    print("🚀 بدء تشغيل خادم SmartPOS...")
    print("🚀 Starting SmartPOS server...")
    print(f"📁 مجلد العمل: {current_dir}")
    print(f"📁 Working directory: {current_dir}")
    
    # التحقق من وجود البيئة الافتراضية
    if not venv_path.exists():
        print("❌ البيئة الافتراضية غير موجودة!")
        print("❌ Virtual environment not found!")
        print("💡 يرجى تشغيل: ./setup_venv.sh")
        print("💡 Please run: ./setup_venv.sh")
        return 1
    
    if not python_venv.exists():
        print("❌ Python في البيئة الافتراضية غير موجود!")
        print("❌ Python in virtual environment not found!")
        return 1
    
    print(f"✅ تم العثور على البيئة الافتراضية: {venv_path}")
    print(f"✅ Virtual environment found: {venv_path}")
    print(f"🐍 Python path: {python_venv}")
    
    # تغيير مجلد العمل إلى backend
    os.chdir(current_dir)
    
    # تشغيل الخادم باستخدام Python من البيئة الافتراضية
    try:
        print("🔄 تشغيل الخادم على البورت 8000...")
        print("🔄 Starting server on port 8000...")
        print("🌐 الخادم سيكون متاح على: http://localhost:8000")
        print("🌐 Server will be available at: http://localhost:8000")
        print("⏹️  للإيقاف اضغط Ctrl+C")
        print("⏹️  To stop press Ctrl+C")
        print("-" * 50)
        
        # تشغيل uvicorn مع Python من البيئة الافتراضية
        cmd = [
            str(python_venv), 
            "-m", "uvicorn", 
            "main:app", 
            "--reload", 
            "--host", "0.0.0.0", 
            "--port", "8000"
        ]
        
        subprocess.run(cmd, cwd=current_dir)
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
        print("\n🛑 Server stopped by user")
        return 0
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        print(f"❌ Error starting server: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
