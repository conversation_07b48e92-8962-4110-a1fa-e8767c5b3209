#!/usr/bin/env python3
"""
اختبار سلامة النظام - SmartPOS
فحص شامل لجميع مكونات النظام للتأكد من عملها بشكل صحيح
"""

import sys
import os
import importlib
import traceback
from pathlib import Path

# إضافة مجلد المشروع إلى المسار
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """اختبار استيراد جميع الوحدات الأساسية"""
    print("🔍 اختبار الاستيرادات...")
    
    modules_to_test = [
        "config.app_config",
        "database.session",
        "models.user",
        "models.product", 
        "models.sale",
        "models.customer",
        "services.unified_fingerprint_service",
        "middleware.unified_security_middleware",
        "middleware.performance_middleware",
        "core.performance_manager",
        "core.service_manager",
        "core.error_manager"
    ]
    
    success_count = 0
    failed_modules = []
    
    for module_name in modules_to_test:
        try:
            importlib.import_module(module_name)
            print(f"  ✅ {module_name}")
            success_count += 1
        except Exception as e:
            print(f"  ❌ {module_name}: {str(e)}")
            failed_modules.append((module_name, str(e)))
    
    print(f"\n📊 نتائج الاستيراد: {success_count}/{len(modules_to_test)} نجح")
    
    if failed_modules:
        print("\n❌ الوحدات الفاشلة:")
        for module, error in failed_modules:
            print(f"  - {module}: {error}")
    
    return len(failed_modules) == 0

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("\n🗄️ اختبار قاعدة البيانات...")
    
    try:
        from database.session import get_db, engine
        from sqlalchemy import text
        
        # اختبار الاتصال
        db = next(get_db())
        result = db.execute(text("SELECT 1")).fetchone()
        db.close()
        
        if result:
            print("  ✅ الاتصال بقاعدة البيانات يعمل")
            return True
        else:
            print("  ❌ فشل في الاتصال بقاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"  ❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_models():
    """اختبار النماذج"""
    print("\n📋 اختبار النماذج...")
    
    try:
        from models.user import User
        from models.product import Product
        from models.sale import Sale
        from models.customer import Customer
        
        # اختبار إنشاء instances
        user = User(username="test", email="<EMAIL>")
        product = Product(name="Test Product", price=10.0)
        customer = Customer(name="Test Customer")
        
        print("  ✅ جميع النماذج تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في النماذج: {e}")
        return False

def test_services():
    """اختبار الخدمات"""
    print("\n🔧 اختبار الخدمات...")
    
    try:
        from database.session import get_db
        from services.unified_fingerprint_service import get_unified_fingerprint_service
        
        # اختبار خدمة البصمة الموحدة
        db = next(get_db())
        fingerprint_service = get_unified_fingerprint_service(db)
        
        # اختبار إنشاء معرف جهاز
        device_data = fingerprint_service.generate_unified_device_id(
            "192.168.1.100", 
            "Mozilla/5.0 Test Browser"
        )
        
        db.close()
        
        if device_data and 'device_id' in device_data:
            print("  ✅ خدمة البصمة الموحدة تعمل")
            return True
        else:
            print("  ❌ خدمة البصمة الموحدة لا تعمل")
            return False
            
    except Exception as e:
        print(f"  ❌ خطأ في الخدمات: {e}")
        return False

def test_middleware():
    """اختبار middleware"""
    print("\n🛡️ اختبار Middleware...")
    
    try:
        from middleware.unified_security_middleware import UnifiedSecurityMiddleware
        from middleware.performance_middleware import PerformanceMiddleware
        
        # اختبار إنشاء instances
        security_middleware = UnifiedSecurityMiddleware(None)
        performance_middleware = PerformanceMiddleware()
        
        print("  ✅ جميع Middleware تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في Middleware: {e}")
        return False

def test_performance_manager():
    """اختبار مدير الأداء"""
    print("\n📊 اختبار مدير الأداء...")
    
    try:
        from core.performance_manager import PerformanceManager
        
        # اختبار إنشاء instance
        perf_manager = PerformanceManager()
        
        # اختبار التهيئة
        if perf_manager.initialize():
            print("  ✅ مدير الأداء يعمل بشكل صحيح")
            
            # تنظيف
            perf_manager.cleanup()
            return True
        else:
            print("  ❌ فشل في تهيئة مدير الأداء")
            return False
            
    except Exception as e:
        print(f"  ❌ خطأ في مدير الأداء: {e}")
        return False

def test_error_manager():
    """اختبار مدير الأخطاء"""
    print("\n🚨 اختبار مدير الأخطاء...")
    
    try:
        from core.error_manager import ErrorManager, ErrorCategory
        
        # اختبار إنشاء instance
        error_manager = ErrorManager()
        
        # اختبار معالجة خطأ تجريبي
        test_error = ValueError("Test error")
        error_info = error_manager.handle_error(test_error, ErrorCategory.SYSTEM)
        
        if error_info and error_info.message == "Test error":
            print("  ✅ مدير الأخطاء يعمل بشكل صحيح")
            return True
        else:
            print("  ❌ مدير الأخطاء لا يعمل بشكل صحيح")
            return False
            
    except Exception as e:
        print(f"  ❌ خطأ في مدير الأخطاء: {e}")
        return False

def test_file_structure():
    """اختبار بنية الملفات"""
    print("\n📁 اختبار بنية الملفات...")
    
    required_files = [
        "main.py",
        "config/app_config.py",
        "database/session.py",
        "models/__init__.py",
        "services/__init__.py",
        "middleware/__init__.py",
        "core/__init__.py",
        "routers/__init__.py"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"  ✅ {file_path}")
    
    if missing_files:
        print(f"\n❌ ملفات مفقودة:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    else:
        print("  ✅ جميع الملفات المطلوبة موجودة")
        return True

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار سلامة النظام - SmartPOS")
    print("=" * 60)
    
    tests = [
        ("بنية الملفات", test_file_structure),
        ("الاستيرادات", test_imports),
        ("قاعدة البيانات", test_database_connection),
        ("النماذج", test_models),
        ("الخدمات", test_services),
        ("Middleware", test_middleware),
        ("مدير الأداء", test_performance_manager),
        ("مدير الأخطاء", test_error_manager)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
        except Exception as e:
            print(f"\n❌ خطأ في اختبار {test_name}: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار النهائية: {passed_tests}/{total_tests} اختبار نجح")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للعمل.")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
