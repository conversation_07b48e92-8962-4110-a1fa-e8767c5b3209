"""
خدمة إدارة المنتجات المتقدمة
تطبق مبادئ البرمجة الكائنية مع نمط Singleton
"""

from typing import Dict, Any, Optional, List, Union
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func

from models.product import Product
from models.category import Category
from models.category import Subcategory
from models.brand import Brand
from models.unit import Unit
from models.warehouse import Warehouse
from services.barcode_service import BarcodeService
from services.slug_service import SlugService
from services.product_validation_service import ProductValidationService
from utils.datetime_utils import get_tripoli_now
import logging

logger = logging.getLogger(__name__)


class ProductManagementService:
    """
    خدمة إدارة المنتجات المتقدمة
    تطبق مبادئ البرمجة الكائنية مع نمط Singleton
    """
    
    _instance: Optional['ProductManagementService'] = None
    
    def __init__(self, db_session: Optional[Session] = None):
        """تهيئة خدمة إدارة المنتجات"""
        self.db_session = db_session
        self.barcode_service = BarcodeService.get_instance(db_session)
        self.slug_service = SlugService.get_instance(db_session)
        self.validation_service = ProductValidationService.get_instance(db_session)
        logger.info("تم تهيئة خدمة إدارة المنتجات بنجاح")
    
    @classmethod
    def get_instance(cls, db_session: Optional[Session] = None) -> 'ProductManagementService':
        """الحصول على مثيل وحيد من الخدمة"""
        if cls._instance is None:
            cls._instance = cls(db_session)
        elif db_session and cls._instance.db_session != db_session:
            cls._instance.db_session = db_session
            cls._instance.barcode_service = BarcodeService.get_instance(db_session)
            cls._instance.slug_service = SlugService.get_instance(db_session)
            cls._instance.validation_service = ProductValidationService.get_instance(db_session)
        return cls._instance
    
    def create_product(self, product_data: Dict[str, Any], user_id: int) -> Dict[str, Any]:
        """إنشاء منتج جديد"""
        try:
            if not self.db_session:
                return {
                    'success': False,
                    'error': 'جلسة قاعدة البيانات غير متوفرة'
                }
            
            # التحقق من صحة البيانات
            validation_result = self.validation_service.validate_product_data(product_data)
            
            if not validation_result['valid']:
                return {
                    'success': False,
                    'errors': validation_result['errors']
                }
            
            validated_data = validation_result['validated_data']
            
            # توليد SKU إذا لم يتم توفيره
            if not validated_data.get('sku'):
                validated_data['sku'] = self.barcode_service.generate_sku()
            
            # توليد باركود إذا لم يتم توفيره
            if not validated_data.get('barcode'):
                barcode_result = self.barcode_service.generate_unique_barcode(
                    product_data.get('barcode_symbology', 'CODE128')
                )
                if barcode_result['success']:
                    validated_data['barcode'] = barcode_result['barcode']
            
            # إنشاء slug من الاسم
            slug_result = self.slug_service.generate_unique_slug(validated_data['name'])
            if slug_result['success']:
                validated_data['slug'] = slug_result['slug']
            
            # إنشاء المنتج
            new_product = Product(
                name=validated_data['name'],
                description=validated_data.get('description'),
                price=validated_data['price'],
                cost_price=validated_data['cost_price'],
                quantity=validated_data['quantity'],
                min_quantity=validated_data.get('min_quantity', 0),
                barcode=validated_data.get('barcode'),
                category_id=validated_data.get('category_id'),
                subcategory_id=validated_data.get('subcategory_id'),
                brand_id=validated_data.get('brand_id'),
                unit_id=validated_data.get('unit_id'),
                is_active=product_data.get('is_active', True),
                created_by=user_id,
                created_at=get_tripoli_now()
            )
            
            # إضافة الحقول الإضافية إذا كانت موجودة في النموذج
            if hasattr(Product, 'sku') and validated_data.get('sku'):
                new_product.sku = validated_data['sku']
            
            if hasattr(Product, 'slug') and validated_data.get('slug'):
                new_product.slug = validated_data['slug']
            
            if hasattr(Product, 'warranty_type') and product_data.get('warranty_type'):
                new_product.warranty_type = product_data['warranty_type']
            
            if hasattr(Product, 'manufacturer') and product_data.get('manufacturer'):
                new_product.manufacturer = product_data['manufacturer']
            
            if hasattr(Product, 'manufactured_date') and validated_data.get('manufactured_date'):
                new_product.manufactured_date = validated_data['manufactured_date']
            
            if hasattr(Product, 'expiry_date') and validated_data.get('expiry_date'):
                new_product.expiry_date = validated_data['expiry_date']
            
            self.db_session.add(new_product)
            self.db_session.commit()
            self.db_session.refresh(new_product)
            
            logger.info(f"تم إنشاء منتج جديد: {new_product.name} (ID: {new_product.id})")
            
            return {
                'success': True,
                'product': new_product,
                'message': 'تم إنشاء المنتج بنجاح'
            }
            
        except Exception as e:
            if self.db_session:
                self.db_session.rollback()
            logger.error(f"خطأ في إنشاء المنتج: {e}")
            return {
                'success': False,
                'error': f'خطأ في إنشاء المنتج: {str(e)}'
            }
    
    def update_product(self, product_id: int, product_data: Dict[str, Any], user_id: int) -> Dict[str, Any]:
        """تحديث منتج موجود"""
        try:
            if not self.db_session:
                return {
                    'success': False,
                    'error': 'جلسة قاعدة البيانات غير متوفرة'
                }
            
            # البحث عن المنتج
            product = self.db_session.query(Product).filter(Product.id == product_id).first()
            
            if not product:
                return {
                    'success': False,
                    'error': 'المنتج غير موجود'
                }
            
            # التحقق من صحة البيانات
            validation_result = self.validation_service.validate_product_data(
                product_data, 
                exclude_product_id=product_id
            )
            
            if not validation_result['valid']:
                return {
                    'success': False,
                    'errors': validation_result['errors']
                }
            
            validated_data = validation_result['validated_data']
            
            # تحديث البيانات
            for field, value in validated_data.items():
                if hasattr(product, field):
                    setattr(product, field, value)
            
            # تحديث الحقول الإضافية
            if product_data.get('warranty_type') and hasattr(product, 'warranty_type'):
                product.warranty_type = product_data['warranty_type']
            
            if product_data.get('manufacturer') and hasattr(product, 'manufacturer'):
                product.manufacturer = product_data['manufacturer']
            
            if product_data.get('is_active') is not None:
                product.is_active = product_data['is_active']
            
            # تحديث معلومات التعديل
            product.updated_by = user_id
            product.updated_at = get_tripoli_now()
            
            self.db_session.commit()
            self.db_session.refresh(product)
            
            logger.info(f"تم تحديث المنتج: {product.name} (ID: {product.id})")
            
            return {
                'success': True,
                'product': product,
                'message': 'تم تحديث المنتج بنجاح'
            }
            
        except Exception as e:
            if self.db_session:
                self.db_session.rollback()
            logger.error(f"خطأ في تحديث المنتج: {e}")
            return {
                'success': False,
                'error': f'خطأ في تحديث المنتج: {str(e)}'
            }
    
    def delete_product(self, product_id: int, user_id: int, force_delete: bool = False) -> Dict[str, Any]:
        """حذف منتج"""
        try:
            if not self.db_session:
                return {
                    'success': False,
                    'error': 'جلسة قاعدة البيانات غير متوفرة'
                }
            
            # البحث عن المنتج
            product = self.db_session.query(Product).filter(Product.id == product_id).first()
            
            if not product:
                return {
                    'success': False,
                    'error': 'المنتج غير موجود'
                }
            
            # التحقق من وجود المنتج في فواتير إذا لم يكن حذف قسري
            if not force_delete:
                # يمكن إضافة التحقق من الفواتير هنا
                pass
            
            # حذف المنتج
            self.db_session.delete(product)
            self.db_session.commit()
            
            logger.info(f"تم حذف المنتج: {product.name} (ID: {product.id}) بواسطة المستخدم {user_id}")
            
            return {
                'success': True,
                'message': 'تم حذف المنتج بنجاح'
            }
            
        except Exception as e:
            if self.db_session:
                self.db_session.rollback()
            logger.error(f"خطأ في حذف المنتج: {e}")
            return {
                'success': False,
                'error': f'خطأ في حذف المنتج: {str(e)}'
            }
    
    def get_product_by_id(self, product_id: int) -> Dict[str, Any]:
        """الحصول على منتج بالمعرف"""
        try:
            if not self.db_session:
                return {
                    'success': False,
                    'error': 'جلسة قاعدة البيانات غير متوفرة'
                }

            product = self.db_session.query(Product).filter(Product.id == product_id).first()

            if not product:
                return {
                    'success': False,
                    'error': 'المنتج غير موجود'
                }

            return {
                'success': True,
                'product': product
            }

        except Exception as e:
            logger.error(f"خطأ في جلب المنتج: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب المنتج: {str(e)}'
            }

    def search_products(self, filters: Dict[str, Any], page: int = 1, limit: int = 10) -> Dict[str, Any]:
        """البحث في المنتجات مع الفلترة والترقيم"""
        try:
            if not self.db_session:
                return {
                    'success': False,
                    'error': 'جلسة قاعدة البيانات غير متوفرة'
                }

            # بناء الاستعلام الأساسي
            query = self.db_session.query(Product)

            # تطبيق الفلاتر
            if filters.get('search'):
                search_term = f"%{filters['search']}%"
                query = query.filter(
                    or_(
                        Product.name.ilike(search_term),
                        Product.description.ilike(search_term),
                        Product.barcode.ilike(search_term)
                    )
                )

            if filters.get('category_id'):
                query = query.filter(Product.category_id == filters['category_id'])

            if filters.get('subcategory_id'):
                query = query.filter(Product.subcategory_id == filters['subcategory_id'])

            if filters.get('brand_id'):
                query = query.filter(Product.brand_id == filters['brand_id'])

            if filters.get('unit_id'):
                query = query.filter(Product.unit_id == filters['unit_id'])

            if filters.get('is_active') is not None:
                query = query.filter(Product.is_active == filters['is_active'])

            if filters.get('low_stock'):
                query = query.filter(Product.quantity <= Product.min_quantity)

            if filters.get('zero_stock'):
                query = query.filter(Product.quantity == 0)

            if filters.get('price_min'):
                query = query.filter(Product.price >= filters['price_min'])

            if filters.get('price_max'):
                query = query.filter(Product.price <= filters['price_max'])

            # الترتيب
            sort_by = filters.get('sort_by', 'created_at')
            sort_order = filters.get('sort_order', 'desc')

            if hasattr(Product, sort_by):
                if sort_order.lower() == 'asc':
                    query = query.order_by(asc(getattr(Product, sort_by)))
                else:
                    query = query.order_by(desc(getattr(Product, sort_by)))

            # حساب العدد الإجمالي
            total_count = query.count()

            # تطبيق الترقيم
            skip = (page - 1) * limit
            products = query.offset(skip).limit(limit).all()

            # حساب معلومات الترقيم
            total_pages = (total_count + limit - 1) // limit
            has_next = page < total_pages
            has_prev = page > 1

            return {
                'success': True,
                'products': products,
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'total_count': total_count,
                    'total_pages': total_pages,
                    'has_next': has_next,
                    'has_prev': has_prev
                }
            }

        except Exception as e:
            logger.error(f"خطأ في البحث في المنتجات: {e}")
            return {
                'success': False,
                'error': f'خطأ في البحث في المنتجات: {str(e)}'
            }

    def get_product_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات المنتجات"""
        try:
            if not self.db_session:
                return {
                    'success': False,
                    'error': 'جلسة قاعدة البيانات غير متوفرة'
                }

            # إحصائيات أساسية
            total_products = self.db_session.query(Product).count()
            active_products = self.db_session.query(Product).filter(Product.is_active == True).count()
            inactive_products = total_products - active_products

            # منتجات منخفضة المخزون
            low_stock_products = self.db_session.query(Product).filter(
                Product.quantity <= Product.min_quantity
            ).count()

            # منتجات نفد مخزونها
            out_of_stock_products = self.db_session.query(Product).filter(
                Product.quantity == 0
            ).count()

            # قيمة المخزون الإجمالية
            total_stock_value = self.db_session.query(
                func.sum(Product.quantity * Product.cost_price)
            ).scalar() or 0

            # متوسط السعر
            average_price = self.db_session.query(
                func.avg(Product.price)
            ).scalar() or 0

            # إحصائيات حسب الفئة
            category_stats = self.db_session.query(
                Category.name,
                func.count(Product.id).label('product_count')
            ).join(Product, Product.category_id == Category.id).group_by(Category.name).all()

            # إحصائيات حسب العلامة التجارية
            brand_stats = self.db_session.query(
                Brand.name,
                func.count(Product.id).label('product_count')
            ).join(Product, Product.brand_id == Brand.id).group_by(Brand.name).all()

            return {
                'success': True,
                'statistics': {
                    'total_products': total_products,
                    'active_products': active_products,
                    'inactive_products': inactive_products,
                    'low_stock_products': low_stock_products,
                    'out_of_stock_products': out_of_stock_products,
                    'total_stock_value': float(total_stock_value),
                    'average_price': float(average_price),
                    'category_distribution': [
                        {'name': stat[0], 'count': stat[1]} for stat in category_stats
                    ],
                    'brand_distribution': [
                        {'name': stat[0], 'count': stat[1]} for stat in brand_stats
                    ]
                }
            }

        except Exception as e:
            logger.error(f"خطأ في جلب إحصائيات المنتجات: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب إحصائيات المنتجات: {str(e)}'
            }

    def bulk_update_products(self, product_ids: List[int], update_data: Dict[str, Any], user_id: int) -> Dict[str, Any]:
        """تحديث مجموعة من المنتجات"""
        try:
            if not self.db_session:
                return {
                    'success': False,
                    'error': 'جلسة قاعدة البيانات غير متوفرة'
                }

            if not product_ids:
                return {
                    'success': False,
                    'error': 'لا توجد منتجات للتحديث'
                }

            # البحث عن المنتجات
            products = self.db_session.query(Product).filter(Product.id.in_(product_ids)).all()

            if not products:
                return {
                    'success': False,
                    'error': 'لم يتم العثور على منتجات للتحديث'
                }

            updated_count = 0
            errors = []

            for product in products:
                try:
                    # تحديث الحقول المسموحة فقط
                    allowed_fields = ['price', 'cost_price', 'quantity', 'min_quantity', 'is_active']

                    for field, value in update_data.items():
                        if field in allowed_fields and hasattr(product, field):
                            setattr(product, field, value)

                    # تحديث معلومات التعديل
                    product.updated_by = user_id
                    product.updated_at = get_tripoli_now()

                    updated_count += 1

                except Exception as e:
                    errors.append(f"خطأ في تحديث المنتج {product.name}: {str(e)}")

            self.db_session.commit()

            logger.info(f"تم تحديث {updated_count} منتج بواسطة المستخدم {user_id}")

            return {
                'success': True,
                'updated_count': updated_count,
                'errors': errors,
                'message': f'تم تحديث {updated_count} منتج بنجاح'
            }

        except Exception as e:
            if self.db_session:
                self.db_session.rollback()
            logger.error(f"خطأ في التحديث المجمع للمنتجات: {e}")
            return {
                'success': False,
                'error': f'خطأ في التحديث المجمع للمنتجات: {str(e)}'
            }
