"""
خدمة جلب بيانات الفترة السابقة للمبيعات
هذا الملف مختص بجلب وتنظيم بيانات المبيعات للفترة السابقة فقط
يتعامل مع التاريخ والوقت بدقة عالية لضمان عدم تعارض البيانات
"""

import logging
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import select, and_, func, cast
from sqlalchemy.sql.sqltypes import Numeric
from decimal import Decimal

from models.sale import Sale
from models.user import User
from utils.datetime_utils import (
    get_tripoli_now, convert_to_tripoli_time,
    get_hour_from_datetime,
    get_previous_days, get_previous_months
)

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

class PreviousPeriodService:
    """
    خدمة جلب بيانات الفترة السابقة
    تتعامل مع جلب وتنظيم بيانات المبيعات للفترة السابقة بدقة
    """

    def __init__(self, db: Session, current_user: User):
        """
        تهيئة خدمة الفترة السابقة

        Args:
            db: جلسة قاعدة البيانات
            current_user: المستخدم الحالي
        """
        self.db = db
        self.current_user = current_user
        self.is_admin = current_user.role.name == "ADMIN"

        logger.info(f"تم تهيئة خدمة الفترة السابقة للمستخدم: {current_user.username}")
        logger.info(f"صلاحيات المستخدم: {'مدير' if self.is_admin else 'مستخدم عادي'}")

    def get_previous_day_sales(self) -> List[Dict[str, Any]]:
        """
        جلب بيانات مبيعات اليوم السابق (أمس - 24 ساعة كاملة)

        Returns:
            قائمة بيانات المبيعات لكل ساعة في اليوم السابق (أمس كاملاً)
        """
        try:
            logger.info("بدء جلب بيانات مبيعات اليوم السابق (أمس - 24 ساعة كاملة)")

            # الحصول على الوقت الحالي بتوقيت طرابلس
            tripoli_now = get_tripoli_now()
            yesterday = tripoli_now.date() - timedelta(days=1)

            logger.info(f"التاريخ السابق بتوقيت طرابلس: {yesterday}")

            # استخدام فلتر التاريخ بدلاً من النطاق الزمني لتجنب مشاكل التوقيت
            yesterday_str = yesterday.strftime('%Y-%m-%d')

            logger.info(f"فلتر التاريخ السابق: {yesterday_str}")

            # بناء الاستعلام باستخدام فلتر التاريخ - استخدام المبلغ المدفوع فعلياً
            query = select(
                Sale.created_at,
                cast(
                    Sale.amount_paid,
                    Numeric
                ).label("total")
            ).where(
                func.date(Sale.created_at) == yesterday_str
            )

            # تطبيق فلتر المستخدم إذا لم يكن مديراً
            if not self.is_admin:
                query = query.where(Sale.user_id == self.current_user.id)
                logger.info("تم تطبيق فلتر المستخدم العادي")
            else:
                logger.info("عرض بيانات جميع المستخدمين (مدير)")

            # تنفيذ الاستعلام
            raw_sales = self.db.execute(query).all()
            logger.info(f"تم جلب {len(raw_sales)} عملية بيع")

            # تنظيم البيانات حسب الساعات
            hour_totals = {}

            # تهيئة جميع الساعات بقيمة صفر
            for hour in range(24):
                hour_key = f"{hour:02d}:00"
                hour_totals[hour_key] = 0.0

            # معالجة كل عملية بيع
            for sale in raw_sales:
                # تحويل وقت البيع إلى توقيت طرابلس
                sale_time_tripoli = convert_to_tripoli_time(sale.created_at)

                if sale_time_tripoli:
                    # استخراج الساعة
                    hour_key = get_hour_from_datetime(sale_time_tripoli)

                    # إضافة المبلغ إلى الساعة المناسبة
                    if hour_key in hour_totals:
                        hour_totals[hour_key] += float(Decimal(str(sale.total)))
                        logger.debug(f"أضيف {sale.total} إلى الساعة {hour_key}")

            # تحويل إلى قائمة مرتبة
            result = []
            for hour in range(24):
                hour_key = f"{hour:02d}:00"
                result.append({
                    "date": hour_key,
                    "amount": hour_totals[hour_key]
                })

            logger.info(f"تم تنظيم البيانات لـ {len(result)} ساعة")
            return result

        except Exception as e:
            logger.error(f"خطأ في جلب بيانات اليوم السابق: {str(e)}")
            # إرجاع بيانات فارغة في حالة الخطأ
            return [{"date": f"{hour:02d}:00", "amount": 0.0} for hour in range(24)]

    def get_previous_week_sales(self) -> List[Dict[str, Any]]:
        """
        جلب بيانات مبيعات الأسبوع السابق (7 أيام قبل الأسبوع الحالي)

        Returns:
            قائمة بيانات المبيعات لكل يوم في الأسبوع السابق
        """
        try:
            logger.info("بدء جلب بيانات مبيعات الأسبوع السابق")

            # الحصول على الأسبوع السابق (من 14 يوم إلى 8 أيام مضت)
            days_list_str = get_previous_days(14)  # آخر 14 يوم
            # أخذ الأيام من 7 إلى 13 (الأسبوع السابق)
            previous_week_days = days_list_str[:7]

            logger.info(f"أيام الأسبوع السابق: {previous_week_days}")

            # تحويل السلاسل النصية إلى كائنات تاريخ
            days_list = []
            for day_str in previous_week_days:
                day_obj = datetime.strptime(day_str, '%Y-%m-%d').date()
                days_list.append(day_obj)

            # تحديد نطاق الأسبوع السابق
            start_date = days_list[0]  # أقدم يوم
            end_date = days_list[-1]  # أحدث يوم

            logger.info(f"نطاق الأسبوع السابق: من {start_date} إلى {end_date}")

            # بناء استعلام محسن باستخدام SQL aggregation
            # استخدام DATE لتجميع البيانات حسب اليوم مباشرة في قاعدة البيانات
            query = select(
                func.to_char(Sale.created_at, 'YYYY-MM-DD').label('day'),
                func.sum(cast(Sale.amount_paid, Numeric)).label('total_amount')
            ).where(
                and_(
                    Sale.created_at >= start_date.strftime('%Y-%m-%d 00:00:00'),
                    Sale.created_at <= end_date.strftime('%Y-%m-%d 23:59:59')
                )
            ).group_by(
                func.to_char(Sale.created_at, 'YYYY-MM-DD')
            )

            # تطبيق فلتر المستخدم إذا لم يكن مديراً
            if not self.is_admin:
                query = query.where(Sale.user_id == self.current_user.id)
                logger.info("تم تطبيق فلتر المستخدم العادي")
            else:
                logger.info("عرض بيانات جميع المستخدمين (مدير)")

            # تنفيذ الاستعلام المحسن
            raw_results = self.db.execute(query).all()
            logger.info(f"تم جلب {len(raw_results)} يوم مجمع (بدلاً من معالجة آلاف العمليات)")

            # تنظيم البيانات
            day_totals = {}

            # تهيئة جميع الأيام بقيمة صفر
            for day_str in previous_week_days:
                day_totals[day_str] = 0.0

            # معالجة النتائج المجمعة (أسرع بكثير من معالجة كل عملية منفردة)
            for result in raw_results:
                day_key = result.day
                total_amount = float(result.total_amount) if result.total_amount else 0.0

                # إضافة المبلغ إلى اليوم المناسب
                if day_key in day_totals:
                    day_totals[day_key] = total_amount
                    logger.debug(f"يوم {day_key}: {total_amount}")

            # تحويل إلى قائمة مرتبة
            result = []
            for day_str in previous_week_days:
                result.append({
                    "date": day_str,
                    "amount": day_totals[day_str]
                })

            logger.info(f"تم تنظيم البيانات لـ {len(result)} يوم")
            return result

        except Exception as e:
            logger.error(f"خطأ في جلب بيانات الأسبوع السابق: {str(e)}")
            # إرجاع بيانات فارغة في حالة الخطأ
            days_list_str = get_previous_days(14)
            previous_week_days = days_list_str[:7]
            return [{"date": day_str, "amount": 0.0} for day_str in previous_week_days]

    def get_previous_month_sales(self) -> List[Dict[str, Any]]:
        """
        جلب بيانات مبيعات الشهر السابق (30 يوم قبل الشهر الحالي)

        Returns:
            قائمة بيانات المبيعات لكل يوم في الشهر السابق
        """
        try:
            logger.info("بدء جلب بيانات مبيعات الشهر السابق")

            # الحصول على الشهر السابق (من 60 يوم إلى 31 يوم مضى)
            days_list_str = get_previous_days(60)  # آخر 60 يوم
            # أخذ الأيام من 30 إلى 59 (الشهر السابق)
            previous_month_days = days_list_str[:30]

            logger.info(f"نطاق الشهر السابق: من {previous_month_days[0]} إلى {previous_month_days[-1]}")

            # تحويل السلاسل النصية إلى كائنات تاريخ
            days_list = []
            for day_str in previous_month_days:
                day_obj = datetime.strptime(day_str, '%Y-%m-%d').date()
                days_list.append(day_obj)

            # تحديد نطاق الشهر السابق
            start_date = days_list[0]  # أقدم يوم
            end_date = days_list[-1]  # أحدث يوم

            # بناء استعلام محسن باستخدام SQL aggregation
            # استخدام DATE لتجميع البيانات حسب اليوم مباشرة في قاعدة البيانات
            query = select(
                func.to_char(Sale.created_at, 'YYYY-MM-DD').label('day'),
                func.sum(cast(Sale.amount_paid, Numeric)).label('total_amount')
            ).where(
                and_(
                    Sale.created_at >= start_date.strftime('%Y-%m-%d 00:00:00'),
                    Sale.created_at <= end_date.strftime('%Y-%m-%d 23:59:59')
                )
            ).group_by(
                func.to_char(Sale.created_at, 'YYYY-MM-DD')
            )

            # تطبيق فلتر المستخدم إذا لم يكن مديراً
            if not self.is_admin:
                query = query.where(Sale.user_id == self.current_user.id)
                logger.info("تم تطبيق فلتر المستخدم العادي")
            else:
                logger.info("عرض بيانات جميع المستخدمين (مدير)")

            # تنفيذ الاستعلام المحسن
            raw_results = self.db.execute(query).all()
            logger.info(f"تم جلب {len(raw_results)} يوم مجمع (بدلاً من معالجة آلاف العمليات)")

            # تنظيم البيانات
            day_totals = {}

            # تهيئة جميع الأيام بقيمة صفر
            for day_str in previous_month_days:
                day_totals[day_str] = 0.0

            # معالجة النتائج المجمعة (أسرع بكثير من معالجة كل عملية منفردة)
            for result in raw_results:
                day_key = result.day
                total_amount = float(result.total_amount) if result.total_amount else 0.0

                # إضافة المبلغ إلى اليوم المناسب
                if day_key in day_totals:
                    day_totals[day_key] = total_amount
                    logger.debug(f"يوم {day_key}: {total_amount}")

            # تحويل إلى قائمة مرتبة
            result = []
            for day_str in previous_month_days:
                result.append({
                    "date": day_str,
                    "amount": day_totals[day_str]
                })

            logger.info(f"تم تنظيم البيانات لـ {len(result)} يوم")
            return result

        except Exception as e:
            logger.error(f"خطأ في جلب بيانات الشهر السابق: {str(e)}")
            # إرجاع بيانات فارغة في حالة الخطأ
            days_list_str = get_previous_days(60)
            previous_month_days = days_list_str[:30]
            return [{"date": day_str, "amount": 0.0} for day_str in previous_month_days]

    def get_previous_year_sales(self) -> List[Dict[str, Any]]:
        """
        جلب بيانات مبيعات السنة السابقة (12 شهر قبل السنة الحالية)

        Returns:
            قائمة بيانات المبيعات لكل شهر في السنة السابقة
        """
        try:
            logger.info("بدء جلب بيانات مبيعات السنة السابقة")

            # الحصول على السنة السابقة (من 24 شهر إلى 13 شهر مضى)
            months_list_str = get_previous_months(24)  # آخر 24 شهر
            # أخذ الأشهر من 12 إلى 23 (السنة السابقة)
            previous_year_months = months_list_str[:12]

            logger.info(f"أشهر السنة السابقة: {previous_year_months}")

            # تحويل السلاسل النصية إلى كائنات تاريخ
            months_list = []
            for month_str in previous_year_months:
                month_obj = datetime.strptime(month_str + '-01', '%Y-%m-%d').date()
                months_list.append(month_obj)

            # تحديد نطاق السنة السابقة
            start_date = months_list[0]  # أقدم شهر

            # بناء استعلام محسن باستخدام SQL aggregation
            # استخدام DATE_TRUNC لتجميع البيانات حسب الشهر مباشرة في قاعدة البيانات
            query = select(
                func.to_char(Sale.created_at, 'YYYY-MM').label('month'),
                func.sum(cast(Sale.amount_paid, Numeric)).label('total_amount')
            ).where(
                Sale.created_at >= start_date.strftime('%Y-%m-%d 00:00:00')
            ).group_by(
                func.to_char(Sale.created_at, 'YYYY-MM')
            )

            # تطبيق فلتر المستخدم إذا لم يكن مديراً
            if not self.is_admin:
                query = query.where(Sale.user_id == self.current_user.id)
                logger.info("تم تطبيق فلتر المستخدم العادي")
            else:
                logger.info("عرض بيانات جميع المستخدمين (مدير)")

            # تنفيذ الاستعلام المحسن
            raw_results = self.db.execute(query).all()
            logger.info(f"تم جلب {len(raw_results)} شهر مجمع (بدلاً من معالجة آلاف العمليات)")

            # تنظيم البيانات
            month_totals = {}

            # تهيئة جميع الأشهر بقيمة صفر
            for month_str in previous_year_months:
                month_totals[month_str] = 0.0

            # معالجة النتائج المجمعة (أسرع بكثير من معالجة كل عملية منفردة)
            for result in raw_results:
                month_key = result.month
                total_amount = float(result.total_amount) if result.total_amount else 0.0

                # إضافة المبلغ إلى الشهر المناسب
                if month_key in month_totals:
                    month_totals[month_key] = total_amount
                    logger.debug(f"شهر {month_key}: {total_amount}")

            # تحويل إلى قائمة مرتبة
            result = []
            for month_str in previous_year_months:
                result.append({
                    "date": month_str,
                    "amount": month_totals[month_str]
                })

            logger.info(f"تم تنظيم البيانات لـ {len(result)} شهر")
            return result

        except Exception as e:
            logger.error(f"خطأ في جلب بيانات السنة السابقة: {str(e)}")
            # إرجاع بيانات فارغة في حالة الخطأ
            months_list_str = get_previous_months(24)
            previous_year_months = months_list_str[:12]
            return [{"date": month_str, "amount": 0.0} for month_str in previous_year_months]

    def get_sales_by_period(self, period: str) -> List[Dict[str, Any]]:
        """
        جلب بيانات المبيعات حسب الفترة المحددة

        Args:
            period: نوع الفترة (day, week, month, year)

        Returns:
            قائمة بيانات المبيعات للفترة السابقة المحددة
        """
        logger.info(f"جلب بيانات الفترة السابقة: {period}")

        if period == "day":
            return self.get_previous_day_sales()
        elif period == "week":
            return self.get_previous_week_sales()
        elif period == "month":
            return self.get_previous_month_sales()
        elif period == "year":
            return self.get_previous_year_sales()
        else:
            logger.error(f"نوع فترة غير مدعوم: {period}")
            return []

    def get_previous_period_total(self, period: str) -> float:
        """
        جلب إجمالي المبيعات للفترة السابقة

        Args:
            period: نوع الفترة (day, week, month, year)

        Returns:
            إجمالي المبيعات للفترة السابقة
        """
        logger.info(f"جلب إجمالي الفترة السابقة: {period}")

        sales_data = self.get_sales_by_period(period)
        total = sum(item["amount"] for item in sales_data)

        logger.info(f"إجمالي الفترة السابقة {period}: {total}")
        return total
