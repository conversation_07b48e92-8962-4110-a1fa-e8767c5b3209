"""
خدمة تسجيل تاريخ بصمات الأجهزة - SmartPOS
خدمة موحدة لتسجيل جميع أحداث البصمات وفقاً لمبادئ البرمجة الكائنية
"""

from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import select, desc
from models.device_fingerprint import DeviceFingerprintHistory
from utils.datetime_utils import get_tripoli_now
import logging
import json

logger = logging.getLogger(__name__)


class DeviceFingerprintHistoryService:
    """
    خدمة موحدة لإدارة تاريخ بصمات الأجهزة
    تطبق مبادئ البرمجة الكائنية:
    - Single Responsibility: مسؤولية واحدة فقط (تسجيل أحداث البصمات)
    - Encapsulation: تغليف منطق التسجيل
    - Reusability: إعادة الاستخدام عبر النظام
    """
    
    def __init__(self, db: Session):
        """
        تهيئة الخدمة
        
        Args:
            db: جلسة قاعدة البيانات
        """
        self.db = db
        self._validate_database_connection()
    
    def _validate_database_connection(self) -> None:
        """التحقق من صحة اتصال قاعدة البيانات"""
        if not self.db:
            raise ValueError("جلسة قاعدة البيانات مطلوبة")
    
    def log_device_access(
        self,
        fingerprint_id: str,
        ip_address: Optional[str],
        user_agent: Optional[str] = None,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        تسجيل حدث وصول الجهاز
        
        Args:
            fingerprint_id: معرف البصمة
            ip_address: عنوان IP
            user_agent: معلومات المتصفح
            additional_data: بيانات إضافية
            
        Returns:
            bool: نجح التسجيل أم لا
        """
        return self._log_event(
            fingerprint_id=fingerprint_id,
            event_type="device_access",
            ip_address=ip_address,
            user_agent=user_agent,
            additional_data=additional_data or {}
        )
    
    def log_fingerprint_created(
        self,
        fingerprint_id: str,
        ip_address: Optional[str],
        user_agent: Optional[str] = None,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        تسجيل حدث إنشاء البصمة الأولى

        Args:
            fingerprint_id: معرف البصمة
            ip_address: عنوان IP
            user_agent: معلومات المتصفح
            additional_data: بيانات إضافية

        Returns:
            bool: نجح التسجيل أم لا
        """
        event_data = {
            "is_first_creation": True,
            "status": "pending_approval",
            "creation_method": "advanced_fingerprint",
            **(additional_data or {})
        }

        return self._log_event(
            fingerprint_id=fingerprint_id,
            event_type="fingerprint_created",
            ip_address=ip_address,
            user_agent=user_agent,
            additional_data=event_data
        )

    def log_fingerprint_stored(
        self,
        fingerprint_id: str,
        ip_address: Optional[str],
        user_agent: Optional[str] = None,
        is_new_fingerprint: bool = True,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        تسجيل حدث تخزين البصمة

        Args:
            fingerprint_id: معرف البصمة
            ip_address: عنوان IP
            user_agent: معلومات المتصفح
            is_new_fingerprint: هل البصمة جديدة
            additional_data: بيانات إضافية

        Returns:
            bool: نجح التسجيل أم لا
        """
        event_data = {
            "is_new_fingerprint": is_new_fingerprint,
            "status": "pending_approval",
            **(additional_data or {})
        }

        event_type = "fingerprint_created" if is_new_fingerprint else "fingerprint_updated"

        return self._log_event(
            fingerprint_id=fingerprint_id,
            event_type=event_type,
            ip_address=ip_address,
            user_agent=user_agent,
            additional_data=event_data
        )
    
    def log_device_approved(
        self,
        fingerprint_id: str,
        approved_by: str,
        ip_address: Optional[str] = None,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        تسجيل حدث الموافقة على الجهاز (مع منع التكرار)

        Args:
            fingerprint_id: معرف البصمة
            approved_by: المستخدم الذي وافق
            ip_address: عنوان IP
            additional_data: بيانات إضافية

        Returns:
            bool: نجح التسجيل أم لا
        """
        try:
            from sqlalchemy import select, and_
            from datetime import timedelta

            current_time = get_tripoli_now()

            # ✅ فحص التكرار: منع تسجيل نفس الموافقة خلال 5 دقائق
            five_minutes_ago = current_time - timedelta(minutes=5)

            existing_stmt = select(DeviceFingerprintHistory).where(
                and_(
                    DeviceFingerprintHistory.fingerprint_id == fingerprint_id,
                    DeviceFingerprintHistory.event_type == 'device_approved',
                    DeviceFingerprintHistory.created_at >= five_minutes_ago
                )
            )

            existing_record = self.db.execute(existing_stmt).scalar_one_or_none()

            if existing_record:
                logger.debug(f"⏭️ تخطي تسجيل الموافقة المكررة للجهاز: {fingerprint_id}")
                return True  # إرجاع True لأن العملية "نجحت" (موجودة بالفعل)

            # تسجيل الموافقة الجديدة
            event_data = {
                "approved_by": approved_by,
                "approval_time": current_time.isoformat(),
                **(additional_data or {})
            }

            return self._log_event(
                fingerprint_id=fingerprint_id,
                event_type="device_approved",
                ip_address=ip_address,
                user_agent=None,
                additional_data=event_data
            )

        except Exception as e:
            logger.error(f"خطأ في تسجيل الموافقة: {e}")
            return False
    
    def log_device_blocked(
        self,
        fingerprint_id: str,
        blocked_by: str,
        reason: str,
        ip_address: Optional[str] = None,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        تسجيل حدث حظر الجهاز

        Args:
            fingerprint_id: معرف البصمة
            blocked_by: المستخدم الذي حظر
            reason: سبب الحظر
            ip_address: عنوان IP
            additional_data: بيانات إضافية

        Returns:
            bool: نجح التسجيل أم لا
        """
        event_data = {
            "blocked_by": blocked_by,
            "block_reason": reason,
            "block_time": get_tripoli_now().isoformat(),
            **(additional_data or {})
        }

        return self._log_event(
            fingerprint_id=fingerprint_id,
            event_type="device_blocked",
            ip_address=ip_address,
            user_agent=None,
            additional_data=event_data
        )

    def log_user_login(
        self,
        fingerprint_id: str,
        username: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        تسجيل حدث تسجيل دخول المستخدم

        Args:
            fingerprint_id: معرف البصمة
            username: اسم المستخدم الذي سجل الدخول
            ip_address: عنوان IP
            user_agent: معلومات المتصفح
            additional_data: بيانات إضافية

        Returns:
            bool: نجح التسجيل أم لا
        """
        event_data = {
            "username": username,
            "login_time": get_tripoli_now().isoformat(),
            "action": "login",
            **(additional_data or {})
        }

        return self._log_event(
            fingerprint_id=fingerprint_id,
            event_type="user_login",
            ip_address=ip_address,
            user_agent=user_agent,
            additional_data=event_data
        )

    def log_user_logout(
        self,
        fingerprint_id: str,
        username: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        تسجيل حدث تسجيل خروج المستخدم

        Args:
            fingerprint_id: معرف البصمة
            username: اسم المستخدم الذي سجل الخروج
            ip_address: عنوان IP
            user_agent: معلومات المتصفح
            additional_data: بيانات إضافية

        Returns:
            bool: نجح التسجيل أم لا
        """
        event_data = {
            "username": username,
            "logout_time": get_tripoli_now().isoformat(),
            "action": "logout",
            **(additional_data or {})
        }

        return self._log_event(
            fingerprint_id=fingerprint_id,
            event_type="user_logout",
            ip_address=ip_address,
            user_agent=user_agent,
            additional_data=event_data
        )
    
    def _log_event(
        self,
        fingerprint_id: str,
        event_type: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        الدالة الأساسية لتسجيل الأحداث
        
        Args:
            fingerprint_id: معرف البصمة
            event_type: نوع الحدث
            ip_address: عنوان IP
            user_agent: معلومات المتصفح
            additional_data: بيانات إضافية
            
        Returns:
            bool: نجح التسجيل أم لا
        """
        try:
            # التحقق من صحة البيانات
            if not fingerprint_id or not event_type:
                logger.warning("معرف البصمة ونوع الحدث مطلوبان")
                return False
            
            # إنشاء سجل التاريخ مع الوقت الصحيح لطرابلس
            history_entry = DeviceFingerprintHistory(
                fingerprint_id=fingerprint_id,
                event_type=event_type,
                ip_address=ip_address,
                user_agent=user_agent,
                event_data=json.dumps(additional_data or {}) if additional_data else None,
                created_at=get_tripoli_now()  # استخدام الوقت الصحيح لطرابلس
            )
            
            # حفظ في قاعدة البيانات
            self.db.add(history_entry)
            self.db.commit()
            
            logger.info(f"✅ تم تسجيل حدث البصمة: {event_type} للجهاز {fingerprint_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل حدث البصمة: {e}")
            self.db.rollback()
            return False
    
    def get_device_history(
        self,
        fingerprint_id: str,
        limit: int = 50,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        الحصول على تاريخ جهاز محدد
        
        Args:
            fingerprint_id: معرف البصمة
            limit: عدد السجلات المطلوبة
            offset: الإزاحة
            
        Returns:
            List[Dict]: قائمة بسجلات التاريخ
        """
        try:
            # تحديد الحد الأقصى للنتائج
            limit = min(limit, 100)
            
            # جلب السجلات
            stmt = select(DeviceFingerprintHistory).where(
                DeviceFingerprintHistory.fingerprint_id == fingerprint_id
            ).order_by(desc(DeviceFingerprintHistory.created_at)).offset(offset).limit(limit)
            
            history_records = self.db.execute(stmt).scalars().all()
            
            # تحويل إلى قاموس
            history_data = []
            for record in history_records:
                history_data.append(record.to_dict())
            
            logger.debug(f"تم جلب {len(history_data)} سجل تاريخ للجهاز {fingerprint_id}")
            return history_data
            
        except Exception as e:
            logger.error(f"خطأ في جلب تاريخ الجهاز {fingerprint_id}: {e}")
            return []
    
    def get_recent_activity(self, limit: int = 20) -> List[Dict[str, Any]]:
        """
        الحصول على النشاط الأخير لجميع الأجهزة
        
        Args:
            limit: عدد السجلات المطلوبة
            
        Returns:
            List[Dict]: قائمة بالنشاط الأخير
        """
        try:
            limit = min(limit, 100)
            
            stmt = select(DeviceFingerprintHistory).order_by(
                desc(DeviceFingerprintHistory.created_at)
            ).limit(limit)
            
            recent_records = self.db.execute(stmt).scalars().all()
            
            activity_data = []
            for record in recent_records:
                activity_data.append(record.to_dict())
            
            return activity_data
            
        except Exception as e:
            logger.error(f"خطأ في جلب النشاط الأخير: {e}")
            return []


def get_fingerprint_history_service(db: Session) -> DeviceFingerprintHistoryService:
    """
    Factory function للحصول على خدمة تاريخ البصمات
    
    Args:
        db: جلسة قاعدة البيانات
        
    Returns:
        DeviceFingerprintHistoryService: مثيل الخدمة
    """
    return DeviceFingerprintHistoryService(db)
