"""
خدمة تسجيل أنشطة المستخدم في سجل تاريخ الأجهزة
"""

import logging
from typing import Optional, Dict, Any
from fastapi import Request
from database.session import get_db
from services.device_fingerprint_history_service import DeviceFingerprintHistoryService
# تم إزالة import غير مستخدم

logger = logging.getLogger(__name__)

class UserActivityLogger:
    """
    خدمة تسجيل أنشطة المستخدم (تسجيل الدخول والخروج)
    """

    def __init__(self):
        pass  # سنحصل على fingerprint_service عند الحاجة
    
    def _get_real_client_ip(self, request: Request) -> str:
        """
        الحصول على عنوان IP الحقيقي للعميل
        """
        # التحقق من headers المختلفة للحصول على IP الحقيقي
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # أخذ أول IP في القائمة (IP الأصلي)
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip.strip()
        
        # استخدام IP العميل المباشر كحل أخير
        return str(request.client.host) if request.client else "unknown"
    
    async def log_user_login(
        self, 
        request: Request, 
        username: str,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        تسجيل حدث تسجيل دخول المستخدم
        
        Args:
            request: طلب HTTP
            username: اسم المستخدم
            additional_data: بيانات إضافية
            
        Returns:
            bool: نجح التسجيل أم لا
        """
        try:
            # الحصول على معلومات الجهاز
            client_ip = self._get_real_client_ip(request)
            user_agent = request.headers.get("user-agent", "")
            
            # الحصول على معرف البصمة للجهاز
            device_id = await self._get_device_fingerprint_id(client_ip, user_agent)
            
            if not device_id:
                logger.warning(f"لم يتم العثور على معرف البصمة للجهاز: {client_ip}")
                return False
            
            # تسجيل حدث تسجيل الدخول
            db = next(get_db())
            history_service = DeviceFingerprintHistoryService(db)
            
            success = history_service.log_user_login(
                fingerprint_id=device_id,
                username=username,
                ip_address=client_ip,
                user_agent=user_agent,
                additional_data=additional_data or {}
            )
            
            if success:
                logger.info(f"✅ تم تسجيل دخول المستخدم: {username} من الجهاز {device_id}")
            else:
                logger.warning(f"⚠️ فشل في تسجيل دخول المستخدم: {username}")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل دخول المستخدم: {e}")
            return False
        finally:
            if 'db' in locals():
                db.close()
    
    async def log_user_logout(
        self, 
        request: Request, 
        username: str,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        تسجيل حدث تسجيل خروج المستخدم
        
        Args:
            request: طلب HTTP
            username: اسم المستخدم
            additional_data: بيانات إضافية
            
        Returns:
            bool: نجح التسجيل أم لا
        """
        try:
            # الحصول على معلومات الجهاز
            client_ip = self._get_real_client_ip(request)
            user_agent = request.headers.get("user-agent", "")
            
            # الحصول على معرف البصمة للجهاز
            device_id = await self._get_device_fingerprint_id(client_ip, user_agent)
            
            if not device_id:
                logger.warning(f"لم يتم العثور على معرف البصمة للجهاز: {client_ip}")
                return False
            
            # تسجيل حدث تسجيل الخروج
            db = next(get_db())
            history_service = DeviceFingerprintHistoryService(db)
            
            success = history_service.log_user_logout(
                fingerprint_id=device_id,
                username=username,
                ip_address=client_ip,
                user_agent=user_agent,
                additional_data=additional_data or {}
            )
            
            if success:
                logger.info(f"✅ تم تسجيل خروج المستخدم: {username} من الجهاز {device_id}")
            else:
                logger.warning(f"⚠️ فشل في تسجيل خروج المستخدم: {username}")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل خروج المستخدم: {e}")
            return False
        finally:
            if 'db' in locals():
                db.close()
    
    async def _get_device_fingerprint_id(self, client_ip: str, user_agent: str) -> Optional[str]:
        """
        الحصول على معرف البصمة للجهاز من قاعدة البيانات

        Args:
            client_ip: عنوان IP
            user_agent: معلومات المتصفح

        Returns:
            معرف البصمة أو None
        """
        try:
            from models.device_security import ApprovedDevice, PendingDevice
            from sqlalchemy import select

            db = next(get_db())

            # البحث في الأجهزة المعتمدة أولاً
            approved_stmt = select(ApprovedDevice.device_id).where(ApprovedDevice.client_ip == client_ip)
            approved_device_id = db.execute(approved_stmt).scalar_one_or_none()

            if approved_device_id:
                db.close()
                return approved_device_id

            # البحث في الأجهزة المنتظرة
            pending_stmt = select(PendingDevice.device_id).where(PendingDevice.client_ip == client_ip)
            pending_device_id = db.execute(pending_stmt).scalar_one_or_none()

            db.close()
            return pending_device_id

        except Exception as e:
            logger.debug(f"خطأ في الحصول على معرف البصمة: {e}")
            if 'db' in locals():
                db.close()
            return None

# إنشاء مثيل عام للخدمة
user_activity_logger = UserActivityLogger()
