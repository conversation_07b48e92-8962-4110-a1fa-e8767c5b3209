"""
مدير حالات مطالبات الضمان الديناميكي
يدير انتقالات الحالات وتحديد الحالات التالية المتاحة
تطبق مبادئ البرمجة الكائنية مع معالجة شاملة للأخطاء
"""

import logging
from typing import List, Dict, Optional, Tuple, Any
from enum import Enum
from sqlalchemy.orm import Session

from models.warranty_claim import WarrantyClaim
from models.claim_status_history import ClaimStatusHistory
from models.user import User

logger = logging.getLogger(__name__)


class ClaimStatus(Enum):
    """
    تعداد حالات مطالبات الضمان
    """
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"


class ClaimStatusManager:
    """
    مدير حالات مطالبات الضمان الديناميكي
    يدير انتقالات الحالات ويحدد الحالات التالية المتاحة
    """
    
    def __init__(self, db: Session, current_user: User):
        self.db = db
        self.current_user = current_user

        # تعريف انتقالات الحالات المسموحة حسب نوع المطالبة
        # الانتقالات الأساسية (مشتركة لجميع الأنواع)
        self.base_status_transitions = {
            ClaimStatus.PENDING: [
                ClaimStatus.APPROVED,
                ClaimStatus.REJECTED
            ],
            ClaimStatus.APPROVED: [
                ClaimStatus.IN_PROGRESS,
                ClaimStatus.COMPLETED,
                ClaimStatus.REJECTED
            ],
            ClaimStatus.REJECTED: [
                # المطالبات المرفوضة لا يمكن تغيير حالتها (نهائية)
            ],
            ClaimStatus.IN_PROGRESS: [
                ClaimStatus.COMPLETED,
                ClaimStatus.REJECTED  # يمكن رفض المطالبة أثناء التنفيذ
            ],
            ClaimStatus.COMPLETED: [
                # المطالبات المكتملة لا يمكن تغيير حالتها (نهائية)
            ]
        }

        # انتقالات خاصة حسب نوع المطالبة
        self.claim_type_transitions = {
            # مطالبات الإصلاح - تحتاج وقت للتنفيذ
            "repair": {
                ClaimStatus.APPROVED: [ClaimStatus.IN_PROGRESS, ClaimStatus.REJECTED]
            },
            # مطالبات الاستبدال - تحتاج وقت للتنفيذ
            "replacement": {
                ClaimStatus.APPROVED: [ClaimStatus.IN_PROGRESS, ClaimStatus.REJECTED]
            },
            # مطالبات الاسترداد - يمكن إكمالها مباشرة
            "refund": {
                ClaimStatus.APPROVED: [ClaimStatus.COMPLETED, ClaimStatus.REJECTED]
            }
        }
        
        # تسميات الحالات بالعربية
        self.status_labels = {
            ClaimStatus.PENDING: "في الانتظار",
            ClaimStatus.APPROVED: "موافق عليه",
            ClaimStatus.REJECTED: "مرفوض",
            ClaimStatus.IN_PROGRESS: "قيد التنفيذ",
            ClaimStatus.COMPLETED: "مكتمل"
        }
        
        # أوصاف الحالات الأساسية
        self.status_descriptions = {
            ClaimStatus.PENDING: "المطالبة في انتظار المراجعة والموافقة",
            ClaimStatus.APPROVED: "تم الموافقة على المطالبة ويمكن البدء في التنفيذ",
            ClaimStatus.REJECTED: "تم رفض المطالبة ولا يمكن المتابعة",
            ClaimStatus.IN_PROGRESS: "المطالبة قيد التنفيذ حالياً",
            ClaimStatus.COMPLETED: "تم إكمال المطالبة بنجاح"
        }

        # أوصاف خاصة حسب نوع المطالبة - محدثة حسب السيناريوهات الجديدة
        self.claim_type_descriptions = {
            "repair": {
                ClaimStatus.PENDING: "مطالبة إصلاح في انتظار المراجعة",
                ClaimStatus.APPROVED: "تمت الموافقة - سيتم البدء في الإصلاح",
                ClaimStatus.IN_PROGRESS: "جاري إصلاح المنتج",
                ClaimStatus.COMPLETED: "تم إكمال الإصلاح بنجاح",
                ClaimStatus.REJECTED: "تم رفض مطالبة الإصلاح"
            },
            "replacement": {
                ClaimStatus.PENDING: "مطالبة استبدال في انتظار المراجعة",
                ClaimStatus.APPROVED: "تمت الموافقة - سيتم تحضير البديل",
                ClaimStatus.IN_PROGRESS: "جاري تحضير المنتج البديل",
                ClaimStatus.COMPLETED: "تم تسليم المنتج البديل",
                ClaimStatus.REJECTED: "تم رفض مطالبة الاستبدال"
            },
            "refund": {
                ClaimStatus.PENDING: "مطالبة استرداد في انتظار المراجعة",
                ClaimStatus.APPROVED: "تمت الموافقة - سيتم الاسترداد",
                ClaimStatus.COMPLETED: "تم إكمال عملية الاسترداد",
                ClaimStatus.REJECTED: "تم رفض مطالبة الاسترداد"
            }
        }

        # رسائل إرشادية للمستخدم حسب نوع المطالبة
        self.user_guidance_messages = {
            "refund": {
                ClaimStatus.APPROVED: "💡 مطالبة الاسترداد: يمكن الإكمال المباشر"
            },
            "repair": {
                ClaimStatus.APPROVED: "⏱️ مطالبة الإصلاح: تحتاج وقت للتنفيذ"
            },
            "replacement": {
                ClaimStatus.APPROVED: "⏱️ مطالبة الاستبدال: تحتاج وقت للتنفيذ"
            }
        }
        
        # الحقول المطلوبة لكل حالة
        self.required_fields = {
            ClaimStatus.APPROVED: ["resolution"],
            ClaimStatus.REJECTED: ["reason"],
            ClaimStatus.IN_PROGRESS: ["resolution"],
            ClaimStatus.COMPLETED: ["resolution", "actual_cost"]
        }

    def get_available_next_statuses(self, current_status: str, claim_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        جلب الحالات التالية المتاحة للحالة الحالية حسب نوع المطالبة

        Args:
            current_status: الحالة الحالية
            claim_type: نوع المطالبة (repair, replacement, refund)

        Returns:
            قائمة بالحالات التالية المتاحة مع التفاصيل
        """
        try:
            logger.info(f"🔄 جلب الحالات التالية المتاحة للحالة: {current_status}, نوع المطالبة: {claim_type}")

            # تحويل النص إلى enum مع معالجة محسنة للأخطاء
            try:
                current_enum = ClaimStatus(current_status)
            except ValueError:
                logger.warning(f"⚠️ حالة غير معروفة: {current_status}")
                # إرجاع جميع الحالات الأساسية إذا كانت الحالة غير معروفة
                return [
                    {
                        "value": "approved",
                        "label": "موافق عليه",
                        "description": "الموافقة على المطالبة",
                        "required_fields": ["resolution"],
                        "is_final": False
                    },
                    {
                        "value": "rejected",
                        "label": "مرفوض",
                        "description": "رفض المطالبة",
                        "required_fields": ["reason"],
                        "is_final": True
                    }
                ]

            # جلب الحالات التالية المسموحة
            # أولاً: الحالات الأساسية
            next_statuses = self.base_status_transitions.get(current_enum, [])

            # ثانياً: إذا كان هناك نوع مطالبة محدد، استخدم الانتقالات الخاصة
            if claim_type and claim_type in self.claim_type_transitions:
                type_specific_transitions = self.claim_type_transitions[claim_type].get(current_enum, [])
                if type_specific_transitions:
                    next_statuses = type_specific_transitions
                    logger.info(f"✅ استخدام انتقالات خاصة لنوع المطالبة: {claim_type}")

            # تحويل إلى قائمة مع التفاصيل
            available_statuses = []
            for status in next_statuses:
                try:
                    # استخدام الوصف الخاص بنوع المطالبة إذا كان متوفراً
                    description = self.status_descriptions.get(status, f"تغيير الحالة إلى {status.value}")
                    if claim_type and claim_type in self.claim_type_descriptions:
                        type_specific_desc = self.claim_type_descriptions[claim_type].get(status)
                        if type_specific_desc:
                            description = type_specific_desc

                    # إضافة رسالة إرشادية إذا كانت متوفرة
                    guidance_message = None
                    if claim_type and claim_type in self.user_guidance_messages:
                        guidance_message = self.user_guidance_messages[claim_type].get(status)

                    status_info = {
                        "value": status.value,
                        "label": self.status_labels.get(status, status.value),
                        "description": description,
                        "required_fields": self.required_fields.get(status, []),
                        "is_final": status in [ClaimStatus.REJECTED, ClaimStatus.COMPLETED],
                        "claim_type": claim_type,  # إضافة نوع المطالبة للمرجع
                        "guidance_message": guidance_message  # رسالة إرشادية للمستخدم
                    }
                    available_statuses.append(status_info)
                except Exception as status_error:
                    logger.warning(f"⚠️ خطأ في معالجة الحالة {status}: {status_error}")
                    continue
            
            logger.info(f"✅ تم جلب {len(available_statuses)} حالة متاحة")
            return available_statuses
            
        except Exception as e:
            logger.error(f"❌ خطأ في جلب الحالات التالية: {e}")
            return []

    def get_logical_next_status(self, claim_type: str, current_status: str) -> Optional[str]:
        """
        تحديد الحالة التالية المنطقية حسب نوع المطالبة

        Args:
            claim_type: نوع المطالبة
            current_status: الحالة الحالية

        Returns:
            الحالة التالية المنطقية أو None
        """
        try:
            if current_status == "approved":
                if claim_type == "refund":
                    return "completed"  # الاسترداد يمكن إكماله مباشرة
                elif claim_type in ["repair", "replacement"]:
                    return "in_progress"  # الإصلاح والاستبدال يحتاجان وقت

            elif current_status == "in_progress":
                return "completed"  # من قيد التنفيذ إلى مكتمل

            return None

        except Exception as e:
            logger.error(f"❌ خطأ في تحديد الحالة التالية المنطقية: {e}")
            return None

    def get_suggested_next_status(self, current_status: str, claim_type: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        اقتراح الحالة التالية المنطقية مع علامة (مقترح)

        Args:
            current_status: الحالة الحالية
            claim_type: نوع المطالبة

        Returns:
            الحالة المقترحة مع التفاصيل أو None
        """
        try:
            logger.info(f"🎯 اقتراح الحالة التالية للحالة: {current_status}, نوع المطالبة: {claim_type}")

            # تحديد الحالة المقترحة حسب المنطق
            suggested_status = None

            if current_status == "pending":
                # للمطالبات في الانتظار، الاقتراح الافتراضي هو الموافقة
                suggested_status = "approved"
            elif current_status == "approved":
                # حسب نوع المطالبة
                if claim_type == "refund":
                    suggested_status = "completed"  # الاسترداد يمكن إكماله مباشرة
                elif claim_type in ["repair", "replacement"]:
                    suggested_status = "in_progress"  # الإصلاح والاستبدال يحتاجان وقت
            elif current_status == "in_progress":
                suggested_status = "completed"  # من قيد التنفيذ إلى مكتمل

            if not suggested_status:
                return None

            # التحقق من أن الحالة المقترحة متاحة فعلاً
            available_statuses = self.get_available_next_statuses(current_status, claim_type)
            suggested_status_info = None

            for status in available_statuses:
                if status["value"] == suggested_status:
                    suggested_status_info = status.copy()
                    break

            if suggested_status_info:
                # إضافة علامة المقترح
                suggested_status_info["is_suggested"] = True
                suggested_status_info["label"] = f"{suggested_status_info['label']} (مقترح)"
                suggested_status_info["suggestion_reason"] = self._get_suggestion_reason(current_status, suggested_status, claim_type)

                logger.info(f"✅ تم اقتراح الحالة: {suggested_status}")
                return suggested_status_info

            return None

        except Exception as e:
            logger.error(f"❌ خطأ في اقتراح الحالة التالية: {e}")
            return None

    def _get_suggestion_reason(self, current_status: str, suggested_status: str, claim_type: Optional[str] = None) -> str:
        """
        الحصول على سبب الاقتراح

        Args:
            current_status: الحالة الحالية
            suggested_status: الحالة المقترحة
            claim_type: نوع المطالبة

        Returns:
            سبب الاقتراح
        """
        try:
            if current_status == "pending" and suggested_status == "approved":
                return "الخطوة التالية المعتادة بعد مراجعة المطالبة"
            elif current_status == "approved" and suggested_status == "completed" and claim_type == "refund":
                return "مطالبات الاسترداد يمكن إكمالها مباشرة"
            elif current_status == "approved" and suggested_status == "in_progress" and claim_type in ["repair", "replacement"]:
                return f"مطالبات {claim_type} تحتاج وقت للتنفيذ"
            elif current_status == "in_progress" and suggested_status == "completed":
                return "إكمال العمل المطلوب"
            else:
                return "الحالة التالية المنطقية"

        except Exception:
            return "اقتراح تلقائي"

    def get_status_workflow_info(self, claim_type: str, current_status: str) -> Dict[str, Any]:
        """
        الحصول على معلومات سير العمل للحالة الحالية

        Args:
            claim_type: نوع المطالبة
            current_status: الحالة الحالية

        Returns:
            معلومات سير العمل والخطوات التالية
        """
        try:
            logger.info(f"🔄 جلب معلومات سير العمل لـ {claim_type} في الحالة {current_status}")

            # تحديد السيناريو المتوقع
            workflow_info = {
                "current_status": current_status,
                "claim_type": claim_type,
                "scenario": self._get_scenario_type(claim_type),
                "expected_flow": self._get_expected_flow(claim_type),
                "next_steps": [],
                "estimated_completion": self._get_estimated_completion(claim_type, current_status),
                "user_actions_required": self._get_required_user_actions(claim_type, current_status)
            }

            # الحصول على الحالات التالية المتاحة
            available_statuses = self.get_available_next_statuses(current_status, claim_type)
            workflow_info["available_next_statuses"] = available_statuses

            # الحصول على الحالة المقترحة
            suggested_status = self.get_suggested_next_status(current_status, claim_type)
            if suggested_status:
                workflow_info["suggested_next_status"] = suggested_status

            return workflow_info

        except Exception as e:
            logger.error(f"❌ خطأ في جلب معلومات سير العمل: {e}")
            return {}

    def _get_scenario_type(self, claim_type: str) -> str:
        """تحديد نوع السيناريو"""
        scenario_map = {
            "refund": "سيناريو 1: مطالبة استرداد",
            "repair": "سيناريو 2: مطالبة إصلاح",
            "replacement": "سيناريو 3: مطالبة استبدال"
        }
        return scenario_map.get(claim_type, "سيناريو عام")

    def _get_expected_flow(self, claim_type: str) -> List[str]:
        """تحديد التدفق المتوقع للحالات"""
        flow_map = {
            "refund": ["في الانتظار", "موافق عليه", "مكتمل"],
            "repair": ["في الانتظار", "موافق عليه", "قيد التنفيذ", "مكتمل"],
            "replacement": ["في الانتظار", "موافق عليه", "قيد التنفيذ", "مكتمل"]
        }
        return flow_map.get(claim_type, ["في الانتظار", "موافق عليه", "مكتمل"])

    def _get_estimated_completion(self, claim_type: str, current_status: str) -> str:
        """تقدير وقت الإكمال"""
        if current_status == "completed":
            return "مكتمل"
        elif claim_type == "refund" and current_status == "approved":
            return "يمكن الإكمال فوراً"
        elif claim_type in ["repair", "replacement"] and current_status == "approved":
            return "يحتاج 3-7 أيام عمل"
        elif current_status == "in_progress":
            return "قريباً"
        else:
            return "يعتمد على المراجعة"

    def _get_required_user_actions(self, claim_type: str, current_status: str) -> List[str]:
        """تحديد الإجراءات المطلوبة من المستخدم"""
        actions = []

        if current_status == "pending":
            actions.append("انتظار مراجعة الطلب")
        elif current_status == "approved":
            if claim_type == "refund":
                actions.append("تأكيد معلومات الاسترداد")
            elif claim_type in ["repair", "replacement"]:
                actions.append("تسليم المنتج للفحص/الإصلاح")
        elif current_status == "in_progress":
            actions.append("انتظار إكمال العمل")

        return actions

    def can_transition_to(self, current_status: str, target_status: str, claim_type: Optional[str] = None) -> Tuple[bool, str]:
        """
        التحقق من إمكانية الانتقال من حالة إلى أخرى

        Args:
            current_status: الحالة الحالية
            target_status: الحالة المستهدفة
            claim_type: نوع المطالبة (اختياري)

        Returns:
            tuple: (هل الانتقال مسموح, رسالة الخطأ إن وجدت)
        """
        try:
            # تحويل النصوص إلى enums
            try:
                current_enum = ClaimStatus(current_status)
                target_enum = ClaimStatus(target_status)
            except ValueError as e:
                return False, f"حالة غير معروفة: {e}"

            # التحقق من الانتقال - أولاً الانتقالات الأساسية
            allowed_transitions = self.base_status_transitions.get(current_enum, [])

            # إذا كان هناك نوع مطالبة محدد، استخدم الانتقالات الخاصة
            if claim_type and claim_type in self.claim_type_transitions:
                type_specific_transitions = self.claim_type_transitions[claim_type].get(current_enum, [])
                if type_specific_transitions:
                    allowed_transitions = type_specific_transitions
                    logger.info(f"✅ استخدام انتقالات خاصة لنوع المطالبة: {claim_type}")

            if target_enum in allowed_transitions:
                return True, ""
            else:
                return False, f"لا يمكن الانتقال من '{self.status_labels[current_enum]}' إلى '{self.status_labels[target_enum]}'"

        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من الانتقال: {e}")
            return False, f"خطأ في التحقق من الانتقال: {str(e)}"

    def record_status_change(
        self, 
        claim: WarrantyClaim, 
        new_status: str, 
        reason: Optional[str] = None,
        notes: Optional[str] = None,
        resolution_details: Optional[str] = None,
        estimated_cost: Optional[str] = None,
        actual_cost: Optional[str] = None
    ) -> ClaimStatusHistory:
        """
        تسجيل تغيير حالة المطالبة في التاريخ
        
        Args:
            claim: المطالبة
            new_status: الحالة الجديدة
            reason: سبب التغيير
            notes: ملاحظات إضافية
            resolution_details: تفاصيل الحل
            estimated_cost: التكلفة المقدرة
            actual_cost: التكلفة الفعلية
            
        Returns:
            سجل تغيير الحالة المنشأ
        """
        try:
            logger.info(f"🔄 تسجيل تغيير حالة المطالبة {claim.id} من {claim.status} إلى {new_status}")
            
            # إنشاء سجل تغيير الحالة
            status_history = ClaimStatusHistory(
                claim_id=claim.id,
                from_status=claim.status,
                to_status=new_status,
                changed_by=self.current_user.id,
                reason=reason,
                notes=notes,
                resolution_details=resolution_details,
                estimated_cost=estimated_cost,
                actual_cost=actual_cost,
                system_notes=f"تم تغيير الحالة بواسطة {self.current_user.username}"
            )
            
            self.db.add(status_history)
            self.db.flush()  # للحصول على ID بدون commit
            
            logger.info(f"✅ تم تسجيل تغيير الحالة: {status_history.id}")
            return status_history
            
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل تغيير الحالة: {e}")
            raise

    def get_status_history(self, claim_id: int) -> List[Dict[str, Any]]:
        """
        جلب تاريخ تغييرات حالة المطالبة
        
        Args:
            claim_id: معرف المطالبة
            
        Returns:
            قائمة بتاريخ تغييرات الحالة
        """
        try:
            logger.info(f"🔄 جلب تاريخ تغييرات الحالة للمطالبة: {claim_id}")
            
            # التحقق من وجود الجدول أولاً
            try:
                history = self.db.query(ClaimStatusHistory).filter(
                    ClaimStatusHistory.claim_id == claim_id
                ).order_by(ClaimStatusHistory.changed_at.desc()).all()
            except Exception as table_error:
                logger.warning(f"⚠️ جدول تاريخ الحالات غير موجود أو لا يمكن الوصول إليه: {table_error}")
                return []

            # تحويل إلى قائمة مع التفاصيل
            history_list = []
            for record in history:
                try:
                    # معالجة آمنة للحالات
                    from_status_label = "جديد"
                    to_status_label = record.to_status

                    if record.from_status:
                        try:
                            from_status_enum = ClaimStatus(record.from_status)
                            from_status_label = self.status_labels.get(from_status_enum, record.from_status)
                        except ValueError:
                            from_status_label = record.from_status

                    try:
                        to_status_enum = ClaimStatus(record.to_status)
                        to_status_label = self.status_labels.get(to_status_enum, record.to_status)
                    except ValueError:
                        to_status_label = record.to_status

                    history_item = {
                        "id": record.id,
                        "from_status": record.from_status,
                        "to_status": record.to_status,
                        "from_status_label": from_status_label,
                        "to_status_label": to_status_label,
                        "changed_at": record.changed_at.isoformat() if record.changed_at else None,
                        "changed_by": record.changed_by,
                        "reason": record.reason,
                        "notes": record.notes,
                        "resolution_details": record.resolution_details,
                        "estimated_cost": record.estimated_cost,
                        "actual_cost": record.actual_cost,
                        "system_notes": record.system_notes,
                        "description": record.get_status_change_description(),
                        "time_since": record.get_time_since_change(),
                        "is_recent": record.is_recent_change()
                    }
                    history_list.append(history_item)
                except Exception as record_error:
                    logger.warning(f"⚠️ خطأ في معالجة سجل التاريخ {record.id}: {record_error}")
                    continue
            
            logger.info(f"✅ تم جلب {len(history_list)} سجل من تاريخ التغييرات")
            return history_list
            
        except Exception as e:
            logger.error(f"❌ خطأ في جلب تاريخ التغييرات: {e}")
            return []

    def get_status_statistics(self) -> Dict[str, Any]:
        """
        جلب إحصائيات حالات المطالبات
        
        Returns:
            إحصائيات شاملة عن حالات المطالبات
        """
        try:
            logger.info("🔄 جلب إحصائيات حالات المطالبات")
            
            # إحصائيات أساسية
            total_claims = self.db.query(WarrantyClaim).count()
            
            statistics = {
                "total_claims": total_claims,
                "status_counts": {},
                "transition_counts": {},
                "recent_changes": 0
            }
            
            # عدد المطالبات لكل حالة
            for status in ClaimStatus:
                count = self.db.query(WarrantyClaim).filter(
                    WarrantyClaim.status == status.value
                ).count()
                statistics["status_counts"][status.value] = {
                    "count": count,
                    "label": self.status_labels[status],
                    "percentage": round((count / total_claims * 100) if total_claims > 0 else 0, 2)
                }
            
            # عدد التغييرات الحديثة (آخر 24 ساعة)
            from datetime import datetime, timezone, timedelta
            recent_threshold = datetime.now(timezone.utc) - timedelta(hours=24)
            
            statistics["recent_changes"] = self.db.query(ClaimStatusHistory).filter(
                ClaimStatusHistory.changed_at >= recent_threshold
            ).count()
            
            logger.info("✅ تم جلب إحصائيات حالات المطالبات")
            return statistics
            
        except Exception as e:
            logger.error(f"❌ خطأ في جلب الإحصائيات: {e}")
            return {
                "total_claims": 0,
                "status_counts": {},
                "transition_counts": {},
                "recent_changes": 0
            }
