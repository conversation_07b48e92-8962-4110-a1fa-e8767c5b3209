"""
خدمة تحليلات وإحصائيات الضمانات
تطبق مبادئ البرمجة الكائنية مع معالجة شاملة للأخطاء
تستخدم خدمة التاريخ والوقت الموحدة للنظام
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import timedelta
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, func
from decimal import Decimal

from models.warranty_claim import WarrantyClaim
from models.product_warranty import ProductWarranty
from models.user import User
from schemas.warranty import DateRange

# استيراد خدمة التاريخ والوقت الموحدة
from utils.datetime_utils import get_current_time_with_settings

logger = logging.getLogger(__name__)


class WarrantyAnalyticsService:
    """
    خدمة تحليلات وإحصائيات الضمانات
    تتعامل مع جميع العمليات المتعلقة بالتقارير والإحصائيات
    """

    def __init__(self, db: Session, current_user: User):
        """
        تهيئة خدمة تحليلات الضمانات

        Args:
            db: جلسة قاعدة البيانات
            current_user: المستخدم الحالي
        """
        self.db = db
        self.current_user = current_user
        self.is_admin = current_user.role.name == "ADMIN"

        logger.info(f"تم تهيئة خدمة تحليلات الضمانات للمستخدم: {current_user.username}")

    def get_warranty_stats(self, date_range: Optional[DateRange] = None) -> Dict[str, Any]:
        """
        جلب إحصائيات الضمانات الشاملة

        Args:
            date_range: نطاق التاريخ للإحصائيات

        Returns:
            إحصائيات الضمانات
        """
        try:
            logger.info("🔄 جلب إحصائيات الضمانات...")

            # بناء الاستعلام الأساسي
            warranty_query = self.db.query(ProductWarranty)
            claim_query = self.db.query(WarrantyClaim)

            # تطبيق نطاق التاريخ إذا تم تحديده
            if date_range:
                warranty_query = warranty_query.filter(
                    ProductWarranty.created_at >= date_range.start_date,
                    ProductWarranty.created_at <= date_range.end_date
                )
                claim_query = claim_query.filter(
                    WarrantyClaim.created_at >= date_range.start_date,
                    WarrantyClaim.created_at <= date_range.end_date
                )

            # إحصائيات الضمانات
            total_warranties = warranty_query.count()
            active_warranties = warranty_query.filter(ProductWarranty.status == 'active').count()
            expired_warranties = warranty_query.filter(ProductWarranty.status == 'expired').count()
            voided_warranties = warranty_query.filter(ProductWarranty.status == 'voided').count()
            claimed_warranties = warranty_query.filter(ProductWarranty.status == 'claimed').count()

            # الحصول على التاريخ الحالي من خدمة التاريخ الموحدة
            current_time = get_current_time_with_settings(self.db)
            current_date = current_time.date()

            # الضمانات المنتهية قريباً (خلال 30 يوم)
            expiring_date = current_date + timedelta(days=30)
            expiring_soon = warranty_query.filter(
                and_(
                    ProductWarranty.status == 'active',
                    ProductWarranty.end_date <= expiring_date,
                    ProductWarranty.end_date >= current_date
                )
            ).count()

            # إحصائيات المطالبات
            total_claims = claim_query.count()
            pending_claims = claim_query.filter(WarrantyClaim.status == 'pending').count()
            approved_claims = claim_query.filter(WarrantyClaim.status == 'approved').count()
            rejected_claims = claim_query.filter(WarrantyClaim.status == 'rejected').count()
            completed_claims = claim_query.filter(WarrantyClaim.status == 'completed').count()

            # متوسط أيام الحل للمطالبات المكتملة
            completed_claims_with_dates = claim_query.filter(
                and_(
                    WarrantyClaim.status == 'completed',
                    WarrantyClaim.resolution_date.isnot(None)
                )
            ).all()

            average_resolution_days = None
            if completed_claims_with_dates:
                total_days = sum([
                    (claim.resolution_date - claim.claim_date).days 
                    for claim in completed_claims_with_dates
                ])
                average_resolution_days = total_days / len(completed_claims_with_dates)

            # إجمالي تكلفة المطالبات
            total_claim_cost = claim_query.filter(
                WarrantyClaim.actual_cost.isnot(None)
            ).with_entities(func.sum(WarrantyClaim.actual_cost)).scalar() or Decimal('0')

            # حساب متوسط تكلفة المطالبات
            average_claim_cost = 0.0
            if total_claims > 0 and total_claim_cost > 0:
                average_claim_cost = float(total_claim_cost) / total_claims

            # حساب معدل المطالبات (نسبة المطالبات إلى إجمالي الضمانات)
            claim_rate_percentage = 0.0
            if total_warranties > 0:
                claim_rate_percentage = (total_claims / total_warranties) * 100

            stats = {
                'total_warranties': total_warranties,
                'active_warranties': active_warranties,
                'expired_warranties': expired_warranties,
                'voided_warranties': voided_warranties,
                'claimed_warranties': claimed_warranties,
                'expiring_soon': expiring_soon,
                'total_claims': total_claims,
                'pending_claims': pending_claims,
                'approved_claims': approved_claims,
                'rejected_claims': rejected_claims,
                'completed_claims': completed_claims,
                'average_resolution_days': average_resolution_days,
                'total_claim_cost': float(total_claim_cost),
                'average_claim_cost': average_claim_cost,
                'claim_rate_percentage': claim_rate_percentage
            }

            logger.info(f"✅ تم جلب إحصائيات الضمانات: {stats}")

            return stats

        except Exception as e:
            logger.error(f"❌ خطأ في جلب إحصائيات الضمانات: {e}")
            raise

    def get_claim_statistics(self, date_range: Optional[DateRange] = None) -> Dict[str, Any]:
        """
        جلب إحصائيات مطالبات الضمان المفصلة

        Args:
            date_range: نطاق التاريخ للإحصائيات

        Returns:
            إحصائيات المطالبات المفصلة
        """
        try:
            logger.info("🔄 جلب إحصائيات مطالبات الضمان...")

            # بناء الاستعلام الأساسي
            query = self.db.query(WarrantyClaim)

            # تطبيق نطاق التاريخ إذا تم تحديده
            if date_range:
                query = query.filter(
                    WarrantyClaim.created_at >= date_range.start_date,
                    WarrantyClaim.created_at <= date_range.end_date
                )

            # إحصائيات حسب الحالة
            total_claims = query.count()
            pending_claims = query.filter(WarrantyClaim.status == 'pending').count()
            approved_claims = query.filter(WarrantyClaim.status == 'approved').count()
            rejected_claims = query.filter(WarrantyClaim.status == 'rejected').count()
            in_progress_claims = query.filter(WarrantyClaim.status == 'in_progress').count()
            completed_claims = query.filter(WarrantyClaim.status == 'completed').count()

            # إحصائيات حسب نوع المطالبة
            repair_claims = query.filter(WarrantyClaim.claim_type == 'repair').count()
            replacement_claims = query.filter(WarrantyClaim.claim_type == 'replacement').count()
            refund_claims = query.filter(WarrantyClaim.claim_type == 'refund').count()

            # متوسط أيام الحل
            completed_claims_with_dates = query.filter(
                and_(
                    WarrantyClaim.status == 'completed',
                    WarrantyClaim.resolution_date.isnot(None)
                )
            ).all()

            average_resolution_days = None
            if completed_claims_with_dates:
                total_days = sum([
                    (claim.resolution_date - claim.claim_date).days 
                    for claim in completed_claims_with_dates
                ])
                average_resolution_days = total_days / len(completed_claims_with_dates)

            # إحصائيات التكلفة
            total_estimated_cost = query.filter(
                WarrantyClaim.estimated_cost.isnot(None)
            ).with_entities(func.sum(WarrantyClaim.estimated_cost)).scalar() or Decimal('0')

            total_actual_cost = query.filter(
                WarrantyClaim.actual_cost.isnot(None)
            ).with_entities(func.sum(WarrantyClaim.actual_cost)).scalar() or Decimal('0')

            cost_savings = total_estimated_cost - total_actual_cost

            stats = {
                'total_claims': total_claims,
                'pending_claims': pending_claims,
                'approved_claims': approved_claims,
                'rejected_claims': rejected_claims,
                'in_progress_claims': in_progress_claims,
                'completed_claims': completed_claims,
                'repair_claims': repair_claims,
                'replacement_claims': replacement_claims,
                'refund_claims': refund_claims,
                'average_resolution_days': average_resolution_days,
                'total_estimated_cost': float(total_estimated_cost),
                'total_actual_cost': float(total_actual_cost),
                'cost_savings': float(cost_savings)
            }

            logger.info(f"✅ تم جلب إحصائيات المطالبات: {stats}")

            return stats

        except Exception as e:
            logger.error(f"❌ خطأ في جلب إحصائيات المطالبات: {e}")
            raise

    def get_expiring_warranties(self, days_ahead: int = 30) -> List[Dict[str, Any]]:
        """
        جلب الضمانات المنتهية قريباً

        Args:
            days_ahead: عدد الأيام للبحث عن الضمانات المنتهية

        Returns:
            قائمة بالضمانات المنتهية قريباً
        """
        try:
            logger.info(f"🔄 جلب الضمانات المنتهية خلال {days_ahead} يوم...")

            # الحصول على التاريخ الحالي من خدمة التاريخ الموحدة
            current_time = get_current_time_with_settings(self.db)
            current_date = current_time.date()
            expiring_date = current_date + timedelta(days=days_ahead)

            warranties = self.db.query(ProductWarranty).options(
                joinedload(ProductWarranty.product),
                joinedload(ProductWarranty.warranty_type),
                joinedload(ProductWarranty.customer)
            ).filter(
                and_(
                    ProductWarranty.status == 'active',
                    ProductWarranty.end_date <= expiring_date,
                    ProductWarranty.end_date >= current_date
                )
            ).order_by(ProductWarranty.end_date).all()

            result = []
            for warranty in warranties:
                days_remaining = (warranty.end_date - current_date).days
                warranty_data = {
                    'id': warranty.id,
                    'warranty_number': warranty.warranty_number,
                    'product_name': warranty.product.name if warranty.product else None,
                    'product_barcode': warranty.product.barcode if warranty.product else None,
                    'customer_name': warranty.customer.name if warranty.customer else None,
                    'end_date': warranty.end_date,
                    'days_remaining': days_remaining,
                    'warranty_type_name': warranty.warranty_type.name_ar if warranty.warranty_type else None
                }
                result.append(warranty_data)

            logger.info(f"✅ تم جلب {len(result)} ضمان منتهي قريباً")

            return result

        except Exception as e:
            logger.error(f"❌ خطأ في جلب الضمانات المنتهية: {e}")
            raise

    def get_warranty_trends(self, months: int = 12) -> Dict[str, Any]:
        """
        جلب اتجاهات الضمانات خلال فترة زمنية

        Args:
            months: عدد الأشهر للتحليل

        Returns:
            بيانات الاتجاهات
        """
        try:
            logger.info(f"🔄 جلب اتجاهات الضمانات لآخر {months} شهر...")

            # استخدام التاريخ الحالي مباشرة لتجنب مشاكل خدمة التاريخ
            from datetime import datetime
            current_date = datetime.now().date()
            start_date = current_date - timedelta(days=months * 30)

            logger.info(f"📅 نطاق التاريخ: من {start_date} إلى {current_date}")

            # اتجاهات الضمانات الجديدة - استخدام DATE_PART بدلاً من date_trunc
            try:
                warranty_trends = self.db.query(
                    func.extract('year', ProductWarranty.created_at).label('year'),
                    func.extract('month', ProductWarranty.created_at).label('month'),
                    func.count(ProductWarranty.id).label('count')
                ).filter(
                    ProductWarranty.created_at >= start_date
                ).group_by(
                    func.extract('year', ProductWarranty.created_at),
                    func.extract('month', ProductWarranty.created_at)
                ).order_by('year', 'month').all()

                logger.info(f"📊 تم جلب {len(warranty_trends)} اتجاه ضمان")
            except Exception as e:
                logger.error(f"❌ خطأ في جلب اتجاهات الضمانات: {e}")
                warranty_trends = []

            # اتجاهات المطالبات - استخدام DATE_PART بدلاً من date_trunc
            try:
                claim_trends = self.db.query(
                    func.extract('year', WarrantyClaim.created_at).label('year'),
                    func.extract('month', WarrantyClaim.created_at).label('month'),
                    func.count(WarrantyClaim.id).label('count')
                ).filter(
                    WarrantyClaim.created_at >= start_date
                ).group_by(
                    func.extract('year', WarrantyClaim.created_at),
                    func.extract('month', WarrantyClaim.created_at)
                ).order_by('year', 'month').all()

                logger.info(f"📊 تم جلب {len(claim_trends)} اتجاه مطالبة")
            except Exception as e:
                logger.error(f"❌ خطأ في جلب اتجاهات المطالبات: {e}")
                claim_trends = []

            # تنسيق البيانات
            warranty_trends_formatted = []
            for trend in warranty_trends:
                month_str = f"{int(trend.year)}-{int(trend.month):02d}"
                warranty_trends_formatted.append({
                    'month': month_str,
                    'count': trend.count
                })

            claim_trends_formatted = []
            for trend in claim_trends:
                month_str = f"{int(trend.year)}-{int(trend.month):02d}"
                claim_trends_formatted.append({
                    'month': month_str,
                    'count': trend.count
                })

            trends = {
                'warranty_trends': warranty_trends_formatted,
                'claim_trends': claim_trends_formatted
            }

            logger.info(f"✅ تم جلب اتجاهات الضمانات: {len(warranty_trends_formatted)} ضمان، {len(claim_trends_formatted)} مطالبة")

            return trends

        except Exception as e:
            logger.error(f"❌ خطأ في جلب اتجاهات الضمانات: {e}")
            raise
