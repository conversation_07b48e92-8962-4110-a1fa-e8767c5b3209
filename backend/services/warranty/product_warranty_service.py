"""
خدمة إدارة ضمانات المنتجات
تطبق مبادئ البرمجة الكائنية مع معالجة شاملة للأخطاء
تستخدم خدمة التاريخ والوقت الموحدة للنظام
"""

import logging
import uuid
from typing import List, Optional, Dict, Any, TYPE_CHECKING
from datetime import timedelta
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import or_

from models.product_warranty import ProductWarranty
from models.warranty_type import WarrantyType
from models.product import Product
from models.customer import Customer
from models.user import User
from schemas.warranty import (
    ProductWarrantyCreate,
    WarrantyFilters,
    ExtendWarrantyData,
    VoidWarrantyData
)

# استيراد خدمة التاريخ والوقت الموحدة
from utils.datetime_utils import get_current_time_with_settings

# استيراد شرطي لتجنب مشاكل الاستيراد الدائري
if TYPE_CHECKING:
    from models.warranty_claim import WarrantyClaim

logger = logging.getLogger(__name__)

# استيراد النماذج المطلوبة للمطالبات (خارج الدوال لتجنب مشاكل الاستيراد)
try:
    from models.warranty_claim import WarrantyClaim
    from models.claim_status_history import ClaimStatusHistory
    CLAIMS_MODELS_AVAILABLE = True
    logger.info("✅ تم تحميل نماذج المطالبات بنجاح")
except ImportError as e:
    CLAIMS_MODELS_AVAILABLE = False
    logger.warning(f"⚠️ نماذج المطالبات غير متوفرة: {e}")


class ProductWarrantyService:
    """
    خدمة إدارة ضمانات المنتجات
    تتعامل مع جميع العمليات المتعلقة بضمانات المنتجات
    """

    def __init__(self, db: Session, current_user: User):
        """
        تهيئة خدمة ضمانات المنتجات

        Args:
            db: جلسة قاعدة البيانات
            current_user: المستخدم الحالي
        """
        self.db = db
        self.current_user = current_user
        self.is_admin = current_user.role.name == "ADMIN"

        logger.info(f"تم تهيئة خدمة ضمانات المنتجات للمستخدم: {current_user.username}")

    def generate_warranty_number(self) -> str:
        """
        توليد رقم ضمان فريد

        Returns:
            رقم الضمان الفريد
        """
        try:
            # الحصول على التاريخ الحالي من خدمة التاريخ الموحدة
            current_time = get_current_time_with_settings(self.db)
            year = current_time.year

            # تنسيق: WAR-YYYY-XXXXXX
            unique_id = str(uuid.uuid4())[:6].upper()
            warranty_number = f"WAR-{year}-{unique_id}"

            # التحقق من عدم وجود رقم مشابه
            existing = self.db.query(ProductWarranty).filter(
                ProductWarranty.warranty_number == warranty_number
            ).first()

            if existing:
                # إعادة المحاولة مع معرف جديد
                unique_id = str(uuid.uuid4())[:8].upper()
                warranty_number = f"WAR-{year}-{unique_id}"

            logger.info(f"تم توليد رقم ضمان جديد: {warranty_number}")
            return warranty_number

        except Exception as e:
            logger.error(f"❌ خطأ في توليد رقم الضمان: {e}")
            raise

    def get_warranties(self, filters: Optional[WarrantyFilters] = None, page: int = 1, limit: int = 10) -> List[Dict[str, Any]]:
        """
        جلب ضمانات المنتجات مع الفلاتر

        Args:
            filters: فلاتر البحث والتصفية

        Returns:
            قائمة بضمانات المنتجات مع البيانات المرتبطة
        """
        try:
            logger.info("🔄 جلب ضمانات المنتجات...")

            # إذا كان المطلوب الضمانات المتاحة للمطالبة، استخدم الخدمة المتخصصة
            if filters and filters.available_for_claims:
                from .warranty_availability_service import WarrantyAvailabilityService
                availability_service = WarrantyAvailabilityService(self.db, self.current_user)
                return availability_service.get_available_warranties_for_claims(filters, page, limit)

            query = self.db.query(ProductWarranty).options(
                joinedload(ProductWarranty.warranty_type),
                joinedload(ProductWarranty.product),
                joinedload(ProductWarranty.customer)
            )

            # تطبيق الفلاتر
            if filters:
                if filters.search:
                    search_term = f"%{filters.search}%"
                    query = query.join(Product).join(WarrantyType).outerjoin(Customer).filter(
                        or_(
                            ProductWarranty.warranty_number.ilike(search_term),
                            Product.name.ilike(search_term),
                            Product.barcode.ilike(search_term),
                            WarrantyType.name_ar.ilike(search_term),
                            Customer.name.ilike(search_term)
                        )
                    )

                if filters.status and filters.status != 'all':
                    query = query.filter(ProductWarranty.status == filters.status)

                if filters.warranty_type_id:
                    query = query.filter(ProductWarranty.warranty_type_id == filters.warranty_type_id)

                if filters.customer_id:
                    query = query.filter(ProductWarranty.customer_id == filters.customer_id)

                if filters.start_date:
                    query = query.filter(ProductWarranty.start_date >= filters.start_date)

                if filters.end_date:
                    query = query.filter(ProductWarranty.end_date <= filters.end_date)

            # ترتيب النتائج
            query = query.order_by(ProductWarranty.created_at.desc())

            # تطبيق الترقيم
            offset = (page - 1) * limit
            warranties = query.offset(offset).limit(limit).all()

            # تحويل النتائج مع البيانات المحسوبة
            result = []
            for warranty in warranties:
                warranty_data = {
                    'id': warranty.id,
                    'warranty_number': warranty.warranty_number,
                    'product_id': warranty.product_id,
                    'product_name': warranty.product.name if warranty.product else None,
                    'product_barcode': warranty.product.barcode if warranty.product else None,
                    'warranty_type_id': warranty.warranty_type_id,
                    'warranty_type_name': warranty.warranty_type.name_ar if warranty.warranty_type else None,
                    'purchase_date': warranty.purchase_date,
                    'start_date': warranty.start_date,
                    'end_date': warranty.end_date,
                    'customer_id': warranty.customer_id,
                    'customer_name': warranty.customer.name if warranty.customer else None,
                    'status': warranty.status,
                    'has_claims': warranty.has_claims,  # حقل تتبع المطالبات
                    'notes': warranty.notes,
                    'extended_months': warranty.extended_months,
                    'extension_reason': warranty.extension_reason,
                    'extended_at': warranty.extended_at,
                    'extended_by': warranty.extended_by,
                    'void_reason': warranty.void_reason,
                    'voided_at': warranty.voided_at,
                    'voided_by': warranty.voided_by,
                    'created_at': warranty.created_at,
                    'updated_at': warranty.updated_at,
                    'created_by': warranty.created_by,
                    'updated_by': warranty.updated_by,
                    # خصائص محسوبة
                    'is_active': warranty.get_is_active(),
                    'days_remaining': warranty.get_days_remaining(),
                    'is_expiring_soon': warranty.get_is_expiring_soon()
                }
                result.append(warranty_data)

            logger.info(f"✅ تم جلب {len(result)} ضمان")

            return result

        except Exception as e:
            logger.error(f"❌ خطأ في جلب ضمانات المنتجات: {e}")
            raise

    def get_warranty_by_id(self, warranty_id: int) -> Optional[ProductWarranty]:
        """
        جلب ضمان بالمعرف

        Args:
            warranty_id: معرف الضمان

        Returns:
            الضمان أو None
        """
        try:
            logger.info(f"🔄 جلب الضمان بالمعرف: {warranty_id}")

            warranty = self.db.query(ProductWarranty).options(
                joinedload(ProductWarranty.warranty_type),
                joinedload(ProductWarranty.product),
                joinedload(ProductWarranty.customer)
            ).filter(ProductWarranty.id == warranty_id).first()

            if warranty:
                logger.info(f"✅ تم العثور على الضمان: {warranty.warranty_number}")
            else:
                logger.warning(f"⚠️ لم يتم العثور على الضمان بالمعرف: {warranty_id}")

            return warranty

        except Exception as e:
            logger.error(f"❌ خطأ في جلب الضمان: {e}")
            raise

    def create_warranty(self, warranty_data: ProductWarrantyCreate) -> ProductWarranty:
        """
        إنشاء ضمان جديد

        Args:
            warranty_data: بيانات الضمان الجديد

        Returns:
            الضمان المنشأ
        """
        try:
            logger.info(f"🔄 إنشاء ضمان جديد للمنتج: {warranty_data.product_id}")

            # التحقق من وجود المنتج
            product = self.db.query(Product).filter(Product.id == warranty_data.product_id).first()
            if not product:
                raise ValueError(f"المنتج غير موجود: {warranty_data.product_id}")

            # التحقق من وجود نوع الضمان
            warranty_type = self.db.query(WarrantyType).filter(
                WarrantyType.id == warranty_data.warranty_type_id
            ).first()
            if not warranty_type:
                raise ValueError(f"نوع الضمان غير موجود: {warranty_data.warranty_type_id}")

            # التحقق من وجود العميل (إذا تم تحديده)
            if warranty_data.customer_id:
                customer = self.db.query(Customer).filter(
                    Customer.id == warranty_data.customer_id
                ).first()
                if not customer:
                    raise ValueError(f"العميل غير موجود: {warranty_data.customer_id}")

            # توليد رقم ضمان فريد
            warranty_number = self.generate_warranty_number()

            # إنشاء الضمان الجديد
            warranty = ProductWarranty(
                **warranty_data.model_dump(),
                warranty_number=warranty_number,
                created_by=self.current_user.id
            )

            self.db.add(warranty)
            self.db.commit()
            self.db.refresh(warranty)

            logger.info(f"✅ تم إنشاء الضمان بنجاح: {warranty.warranty_number} (ID: {warranty.id})")

            return warranty

        except Exception as e:
            self.db.rollback()
            logger.error(f"❌ خطأ في إنشاء الضمان: {e}")
            raise

    def extend_warranty(self, warranty_id: int, extend_data: ExtendWarrantyData) -> ProductWarranty:
        """
        تمديد فترة الضمان

        Args:
            warranty_id: معرف الضمان
            extend_data: بيانات التمديد

        Returns:
            الضمان المحدث
        """
        try:
            logger.info(f"🔄 تمديد الضمان: {warranty_id} لمدة {extend_data.months} شهر")

            # جلب الضمان
            warranty = self.get_warranty_by_id(warranty_id)
            if not warranty:
                raise ValueError(f"الضمان غير موجود: {warranty_id}")

            if str(warranty.status) != 'active':
                raise ValueError("لا يمكن تمديد ضمان غير نشط")

            # الحصول على التاريخ والوقت الحالي من خدمة التاريخ الموحدة
            current_time = get_current_time_with_settings(self.db)

            # تحديث تاريخ الانتهاء
            new_end_date = warranty.end_date + timedelta(days=extend_data.months * 30)

            setattr(warranty, 'end_date', new_end_date)
            setattr(warranty, 'extended_months', warranty.extended_months + extend_data.months)
            setattr(warranty, 'extension_reason', extend_data.reason)
            setattr(warranty, 'extended_at', current_time)
            setattr(warranty, 'extended_by', self.current_user.id)
            setattr(warranty, 'updated_by', self.current_user.id)

            self.db.commit()
            self.db.refresh(warranty)

            logger.info(f"✅ تم تمديد الضمان بنجاح: {warranty.warranty_number}")

            return warranty

        except Exception as e:
            self.db.rollback()
            logger.error(f"❌ خطأ في تمديد الضمان: {e}")
            raise

    def void_warranty(self, warranty_id: int, void_data: VoidWarrantyData) -> ProductWarranty:
        """
        إلغاء الضمان مع التعامل المنطقي مع المطالبات المرتبطة

        Args:
            warranty_id: معرف الضمان
            void_data: بيانات الإلغاء

        Returns:
            الضمان المحدث
        """
        try:
            logger.info(f"🔄 إلغاء الضمان: {warranty_id}")

            # جلب الضمان
            warranty = self.get_warranty_by_id(warranty_id)
            if not warranty:
                raise ValueError(f"الضمان غير موجود: {warranty_id}")

            if str(warranty.status) == 'voided':
                raise ValueError("الضمان ملغي بالفعل")

            # فحص المطالبات المرتبطة والتعامل معها (مؤقتاً معطل لحل المشكلة)
            claims_info = {
                'total_claims': 0,
                'processed_claims': 0,
                'rejected_claims': 0,
                'completed_claims': 0,
                'summary': []
            }

            # محاولة معالجة المطالبات إذا كانت النماذج متوفرة
            try:
                if CLAIMS_MODELS_AVAILABLE:
                    logger.info("🔄 بدء معالجة المطالبات المرتبطة...")
                    claims_info = self._handle_warranty_claims_on_void(warranty_id, void_data.reason)
                    logger.info(f"✅ تم معالجة {claims_info.get('processed_claims', 0)} مطالبة")
                else:
                    logger.info("📝 تخطي معالجة المطالبات - النماذج غير متوفرة")
            except Exception as claims_error:
                logger.error(f"❌ خطأ في معالجة المطالبات: {claims_error}")
                # نكمل إلغاء الضمان حتى لو فشلت معالجة المطالبات
                logger.info("🔄 سيتم إكمال إلغاء الضمان بدون معالجة المطالبات")

            # الحصول على التاريخ والوقت الحالي من خدمة التاريخ الموحدة
            current_time = get_current_time_with_settings(self.db)

            # إلغاء الضمان
            setattr(warranty, 'status', 'voided')
            setattr(warranty, 'void_reason', void_data.reason)
            setattr(warranty, 'voided_at', current_time)
            setattr(warranty, 'voided_by', self.current_user.id)
            setattr(warranty, 'updated_by', self.current_user.id)

            self.db.commit()
            self.db.refresh(warranty)

            # تسجيل النتيجة مع معلومات المطالبات
            if claims_info['total_claims'] > 0:
                logger.info(f"✅ تم إلغاء الضمان مع معالجة {claims_info['total_claims']} مطالبة: {warranty.warranty_number}")
                logger.info(f"📋 تفاصيل المطالبات: {claims_info['summary']}")
            else:
                logger.info(f"✅ تم إلغاء الضمان بنجاح: {warranty.warranty_number}")

            # إضافة معلومات المطالبات إلى الضمان للإرجاع (معطل مؤقتاً)
            # setattr(warranty, '_claims_info', claims_info)

            return warranty

        except Exception as e:
            self.db.rollback()
            logger.error(f"❌ خطأ في إلغاء الضمان: {e}")
            raise

    def _handle_warranty_claims_on_void(self, warranty_id: int, void_reason: str) -> Dict[str, Any]:
        """
        التعامل مع المطالبات المرتبطة عند إلغاء الضمان

        Args:
            warranty_id: معرف الضمان
            void_reason: سبب إلغاء الضمان

        Returns:
            معلومات عن المطالبات التي تم التعامل معها
        """
        try:
            # التحقق من توفر نماذج المطالبات
            if not CLAIMS_MODELS_AVAILABLE:
                logger.warning("⚠️ نماذج المطالبات غير متوفرة، تخطي معالجة المطالبات")
                return {
                    'total_claims': 0,
                    'processed_claims': 0,
                    'rejected_claims': 0,
                    'completed_claims': 0,
                    'summary': ['نماذج المطالبات غير متوفرة']
                }

            logger.info(f"🔍 فحص المطالبات المرتبطة بالضمان: {warranty_id}")

            # جلب جميع المطالبات المرتبطة بالضمان بطريقة آمنة
            try:
                claims = self.db.query(WarrantyClaim).filter(
                    WarrantyClaim.warranty_id == warranty_id
                ).all()
                logger.info(f"📊 تم العثور على {len(claims)} مطالبة مرتبطة")
            except Exception as query_error:
                logger.error(f"❌ خطأ في جلب المطالبات: {query_error}")
                return {
                    'total_claims': 0,
                    'processed_claims': 0,
                    'rejected_claims': 0,
                    'completed_claims': 0,
                    'summary': [f'خطأ في جلب المطالبات: {str(query_error)}']
                }

            claims_info = {
                'total_claims': len(claims),
                'processed_claims': 0,
                'rejected_claims': 0,
                'completed_claims': 0,
                'summary': []
            }

            if not claims:
                logger.info("📝 لا توجد مطالبات مرتبطة بهذا الضمان")
                return claims_info

            # الحصول على التاريخ والوقت الحالي
            current_time = get_current_time_with_settings(self.db)

            # التعامل مع كل مطالبة حسب حالتها
            for claim in claims:
                try:
                    old_status = str(claim.status)
                    logger.info(f"🔄 معالجة المطالبة {getattr(claim, 'claim_number', claim.id)} - الحالة: {old_status}")

                    action_taken = self._process_claim_on_warranty_void(claim, void_reason, current_time)

                    if action_taken:
                        claims_info['processed_claims'] += 1
                        new_status = str(claim.status)

                        # تسجيل تغيير الحالة في التاريخ
                        try:
                            self._record_claim_status_change(
                                claim, old_status, new_status,
                                f"تم تغيير الحالة بسبب إلغاء الضمان: {void_reason}"
                            )
                        except Exception as history_error:
                            logger.warning(f"⚠️ خطأ في تسجيل تاريخ الحالة: {history_error}")

                        # إحصائيات حسب الحالة الجديدة
                        if new_status == 'rejected':
                            claims_info['rejected_claims'] += 1
                        elif new_status == 'completed':
                            claims_info['completed_claims'] += 1

                        claim_number = getattr(claim, 'claim_number', f'ID-{claim.id}')
                        claims_info['summary'].append(
                            f"المطالبة {claim_number}: {old_status} → {new_status}"
                        )

                        logger.info(f"✅ تم تحديث المطالبة {claim_number}: {old_status} → {new_status}")
                    else:
                        logger.info(f"📝 لم تحتج المطالبة {getattr(claim, 'claim_number', claim.id)} لتغيير")

                except Exception as claim_error:
                    logger.error(f"❌ خطأ في معالجة المطالبة {getattr(claim, 'id', 'unknown')}: {claim_error}")
                    continue

            # إنشاء ملخص نهائي
            if claims_info['processed_claims'] > 0:
                summary_text = f"تم معالجة {claims_info['processed_claims']} من {claims_info['total_claims']} مطالبة"
                if claims_info['rejected_claims'] > 0:
                    summary_text += f" (رُفض {claims_info['rejected_claims']})"
                if claims_info['completed_claims'] > 0:
                    summary_text += f" (اكتمل {claims_info['completed_claims']})"
                claims_info['summary'].insert(0, summary_text)

            logger.info(f"✅ تم التعامل مع {claims_info['processed_claims']} مطالبة من أصل {claims_info['total_claims']}")
            return claims_info

        except Exception as e:
            logger.error(f"❌ خطأ في التعامل مع المطالبات: {e}")
            return {
                'total_claims': 0,
                'processed_claims': 0,
                'rejected_claims': 0,
                'completed_claims': 0,
                'summary': [f"خطأ في معالجة المطالبات: {str(e)}"]
            }

    def _process_claim_on_warranty_void(self, claim: 'WarrantyClaim', void_reason: str, current_time) -> bool:
        """
        معالجة مطالبة واحدة عند إلغاء الضمان

        Args:
            claim: المطالبة
            void_reason: سبب إلغاء الضمان
            current_time: الوقت الحالي

        Returns:
            True إذا تم تغيير حالة المطالبة، False إذا لم تتغير
        """
        try:
            current_status = str(claim.status)
            claim_number = getattr(claim, 'claim_number', f'ID-{claim.id}')

            logger.info(f"🔍 معالجة المطالبة {claim_number} - الحالة الحالية: {current_status}")

            # منطق التعامل مع المطالبات حسب حالتها الحالية
            if current_status == 'pending':
                # المطالبات في الانتظار: يتم رفضها تلقائياً
                try:
                    setattr(claim, 'status', 'rejected')
                    if hasattr(claim, 'rejection_reason'):
                        setattr(claim, 'rejection_reason', f"تم إلغاء الضمان: {void_reason}")
                    if hasattr(claim, 'rejected_by'):
                        setattr(claim, 'rejected_by', self.current_user.id)
                    if hasattr(claim, 'rejected_at'):
                        setattr(claim, 'rejected_at', current_time)
                    setattr(claim, 'updated_by', self.current_user.id)
                    logger.info(f"✅ تم رفض المطالبة المعلقة: {claim_number}")
                    return True
                except Exception as update_error:
                    logger.error(f"❌ خطأ في تحديث المطالبة المعلقة: {update_error}")
                    return False

            elif current_status == 'approved':
                # المطالبات الموافق عليها: يتم رفضها مع تفسير
                try:
                    setattr(claim, 'status', 'rejected')
                    if hasattr(claim, 'rejection_reason'):
                        setattr(claim, 'rejection_reason', f"تم إلغاء الضمان بعد الموافقة: {void_reason}")
                    if hasattr(claim, 'rejected_by'):
                        setattr(claim, 'rejected_by', self.current_user.id)
                    if hasattr(claim, 'rejected_at'):
                        setattr(claim, 'rejected_at', current_time)
                    setattr(claim, 'updated_by', self.current_user.id)
                    logger.info(f"✅ تم رفض المطالبة الموافق عليها: {claim_number}")
                    return True
                except Exception as update_error:
                    logger.error(f"❌ خطأ في تحديث المطالبة الموافق عليها: {update_error}")
                    return False

            elif current_status == 'in_progress':
                # المطالبات قيد التنفيذ: يتم إكمالها بشكل جزئي أو رفضها حسب النوع
                claim_type = getattr(claim, 'claim_type', 'unknown')

                if str(claim_type) == 'refund':
                    # مطالبات الاسترداد: يتم إكمالها
                    try:
                        setattr(claim, 'status', 'completed')
                        if hasattr(claim, 'resolution'):
                            setattr(claim, 'resolution', f"تم الاسترداد قبل إلغاء الضمان: {void_reason}")
                        if hasattr(claim, 'resolution_date'):
                            setattr(claim, 'resolution_date', current_time.date())
                        if hasattr(claim, 'resolved_by'):
                            setattr(claim, 'resolved_by', self.current_user.id)
                        setattr(claim, 'updated_by', self.current_user.id)
                        logger.info(f"✅ تم إكمال مطالبة الاسترداد: {claim_number}")
                        return True
                    except Exception as update_error:
                        logger.error(f"❌ خطأ في إكمال مطالبة الاسترداد: {update_error}")
                        return False
                else:
                    # مطالبات الإصلاح والاستبدال: يتم رفضها
                    try:
                        setattr(claim, 'status', 'rejected')
                        if hasattr(claim, 'rejection_reason'):
                            setattr(claim, 'rejection_reason', f"تم إلغاء الضمان أثناء التنفيذ: {void_reason}")
                        if hasattr(claim, 'rejected_by'):
                            setattr(claim, 'rejected_by', self.current_user.id)
                        if hasattr(claim, 'rejected_at'):
                            setattr(claim, 'rejected_at', current_time)
                        setattr(claim, 'updated_by', self.current_user.id)
                        logger.info(f"✅ تم رفض المطالبة قيد التنفيذ ({claim_type}): {claim_number}")
                        return True
                    except Exception as update_error:
                        logger.error(f"❌ خطأ في رفض المطالبة قيد التنفيذ: {update_error}")
                        return False

            elif current_status in ['completed', 'rejected']:
                # المطالبات المكتملة أو المرفوضة: لا تحتاج تغيير
                logger.info(f"📝 المطالبة {claim_number} في حالة نهائية: {current_status}")
                return False

            else:
                # حالات غير معروفة: يتم رفضها احتياطياً
                try:
                    setattr(claim, 'status', 'rejected')
                    if hasattr(claim, 'rejection_reason'):
                        setattr(claim, 'rejection_reason', f"حالة غير معروفة عند إلغاء الضمان: {void_reason}")
                    if hasattr(claim, 'rejected_by'):
                        setattr(claim, 'rejected_by', self.current_user.id)
                    if hasattr(claim, 'rejected_at'):
                        setattr(claim, 'rejected_at', current_time)
                    setattr(claim, 'updated_by', self.current_user.id)
                    logger.warning(f"⚠️ تم رفض المطالبة ذات الحالة غير المعروفة: {claim_number}")
                    return True
                except Exception as update_error:
                    logger.error(f"❌ خطأ في رفض المطالبة ذات الحالة غير المعروفة: {update_error}")
                    return False

        except Exception as e:
            logger.error(f"❌ خطأ في معالجة المطالبة {getattr(claim, 'id', 'unknown')}: {e}")
            return False

    def _record_claim_status_change(self, claim: 'WarrantyClaim', old_status: str, new_status: str, reason: str):
        """
        تسجيل تغيير حالة المطالبة في التاريخ

        Args:
            claim: المطالبة
            old_status: الحالة القديمة
            new_status: الحالة الجديدة
            reason: سبب التغيير
        """
        try:
            # التحقق من توفر نماذج المطالبات
            if not CLAIMS_MODELS_AVAILABLE:
                logger.warning("⚠️ نماذج المطالبات غير متوفرة، تخطي تسجيل تاريخ الحالة")
                return

            # إنشاء سجل تغيير الحالة
            status_history = ClaimStatusHistory(
                claim_id=claim.id,
                from_status=old_status,
                to_status=new_status,
                changed_by=self.current_user.id,
                reason=reason,
                system_notes=f"تم تغيير الحالة تلقائياً بسبب إلغاء الضمان بواسطة {self.current_user.username}"
            )

            self.db.add(status_history)
            logger.info(f"📝 تم تسجيل تغيير حالة المطالبة {claim.claim_number}: {old_status} → {new_status}")

        except Exception as e:
            logger.warning(f"⚠️ خطأ في تسجيل تاريخ تغيير الحالة: {e}")

    def delete_warranty(self, warranty_id: int) -> None:
        """
        حذف ضمان المنتج نهائياً مع جميع البيانات المرتبطة

        Args:
            warranty_id: معرف الضمان

        Raises:
            ValueError: إذا كان الضمان غير موجود أو لا يمكن حذفه
        """
        try:
            logger.info(f"🔄 حذف الضمان: {warranty_id}")

            # جلب الضمان
            warranty = self.get_warranty_by_id(warranty_id)
            if not warranty:
                raise ValueError(f"الضمان غير موجود: {warranty_id}")

            # حفظ معلومات الضمان للسجل
            warranty_number = warranty.warranty_number
            product_name = warranty.product.name if warranty.product else "غير محدد"

            # البحث عن البيانات المرتبطة وحذفها
            deleted_items = []

            # 1. حذف مطالبات الضمان المرتبطة وسجلات تاريخ الحالات
            try:
                # التحقق من توفر نماذج المطالبات
                if not CLAIMS_MODELS_AVAILABLE:
                    logger.info("📝 نماذج المطالبات غير متوفرة، تخطي حذف المطالبات")
                else:
                    # جلب جميع مطالبات الضمان المرتبطة
                    claims = self.db.query(WarrantyClaim).filter(
                        WarrantyClaim.warranty_id == warranty_id
                    ).all()

                    if claims:
                        claims_count = len(claims)
                        history_count = 0

                        # حذف سجلات تاريخ الحالات لكل مطالبة أولاً
                        for claim in claims:
                            try:
                                # حذف سجلات تاريخ الحالات المرتبطة بهذه المطالبة
                                claim_history = self.db.query(ClaimStatusHistory).filter(
                                    ClaimStatusHistory.claim_id == claim.id
                                ).all()

                                for history_record in claim_history:
                                    self.db.delete(history_record)
                                    history_count += 1

                            except Exception as history_error:
                                logger.warning(f"⚠️ خطأ في حذف تاريخ الحالات للمطالبة {claim.id}: {history_error}")
                                # لا نتوقف، نكمل مع باقي السجلات

                        # الآن حذف المطالبات نفسها
                        for claim in claims:
                            self.db.delete(claim)

                        deleted_items.append(f"{claims_count} مطالبة ضمان")
                        if history_count > 0:
                            deleted_items.append(f"{history_count} سجل تاريخ حالات")

                        logger.info(f"🗑️ تم حذف {claims_count} مطالبة ضمان و {history_count} سجل تاريخ حالات")

            except ImportError as import_error:
                logger.info(f"📝 نموذج غير متوفر: {import_error}")
            except Exception as e:
                logger.error(f"❌ خطأ في حذف مطالبات الضمان: {e}")
                # في حالة خطأ، نعيد تدوير المعاملة ونرفع الخطأ
                self.db.rollback()
                raise ValueError(f"فشل في حذف مطالبات الضمان المرتبطة: {str(e)}")

            # 2. البحث عن أي بيانات أخرى مرتبطة بالضمان
            try:
                # فحص الجداول الأخرى التي قد تحتوي على مراجع للضمان
                from sqlalchemy import text

                # البحث عن أي جداول تحتوي على warranty_id (باستثناء الجداول التي تم التعامل معها)
                check_query = text("""
                    SELECT table_name, column_name
                    FROM information_schema.columns
                    WHERE column_name = 'warranty_id'
                    AND table_schema = 'public'
                    AND table_name NOT IN ('product_warranties', 'warranty_claims', 'claim_status_history')
                """)

                related_tables = self.db.execute(check_query).fetchall()

                for table_name, column_name in related_tables:
                    try:
                        # حذف السجلات المرتبطة من كل جدول
                        delete_query = text(f"DELETE FROM {table_name} WHERE {column_name} = :warranty_id")
                        result = self.db.execute(delete_query, {"warranty_id": warranty_id})

                        # التحقق من عدد الصفوف المحذوفة
                        rows_affected = getattr(result, 'rowcount', 0)
                        if rows_affected > 0:
                            deleted_items.append(f"{rows_affected} سجل من جدول {table_name}")
                            logger.info(f"🗑️ تم حذف {rows_affected} سجل من جدول {table_name}")

                    except Exception as table_error:
                        logger.warning(f"⚠️ خطأ في حذف البيانات من جدول {table_name}: {table_error}")

            except Exception as e:
                logger.warning(f"⚠️ خطأ في فحص الجداول المرتبطة: {e}")

            # 3. حذف أي ملفات مرتبطة بالضمان (إن وجدت)
            try:
                import os
                warranty_files_dir = f"uploads/warranties/{warranty_id}"
                if os.path.exists(warranty_files_dir):
                    import shutil
                    shutil.rmtree(warranty_files_dir)
                    deleted_items.append("ملفات الضمان")
                    logger.info(f"🗑️ تم حذف ملفات الضمان من: {warranty_files_dir}")
            except Exception as e:
                logger.warning(f"⚠️ خطأ في حذف ملفات الضمان: {e}")
                # لا نتوقف، نكمل العملية

            # 4. حذف الضمان نفسه
            try:
                self.db.delete(warranty)
                logger.info(f"🗑️ تم تحضير حذف الضمان الأساسي: {warranty_number}")
            except Exception as e:
                logger.error(f"❌ خطأ في حذف الضمان الأساسي: {e}")
                self.db.rollback()
                raise ValueError(f"فشل في حذف الضمان الأساسي: {str(e)}")

            # 5. تأكيد جميع التغييرات
            try:
                self.db.commit()
                logger.info(f"✅ تم تأكيد جميع عمليات الحذف بنجاح")
            except Exception as commit_error:
                logger.error(f"❌ خطأ في تأكيد المعاملة: {commit_error}")
                self.db.rollback()
                raise ValueError(f"فشل في تأكيد عملية الحذف: {str(commit_error)}")

            # تسجيل العملية
            deleted_summary = ", ".join(deleted_items) if deleted_items else "لا توجد بيانات مرتبطة"
            logger.info(f"✅ تم حذف الضمان بنجاح: {warranty_number} (المنتج: {product_name})")
            logger.info(f"📋 البيانات المحذوفة: {deleted_summary}")

        except ValueError as ve:
            # أخطاء منطقية معروفة
            logger.error(f"❌ خطأ منطقي في حذف الضمان: {ve}")
            raise
        except Exception as e:
            # أخطاء غير متوقعة
            try:
                self.db.rollback()
                logger.info("🔄 تم التراجع عن المعاملة")
            except Exception as rollback_error:
                logger.error(f"❌ خطأ في التراجع عن المعاملة: {rollback_error}")

            logger.error(f"❌ خطأ غير متوقع في حذف الضمان: {e}")
            raise ValueError(f"حدث خطأ أثناء حذف الضمان. يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني. تفاصيل الخطأ: {str(e)}")
