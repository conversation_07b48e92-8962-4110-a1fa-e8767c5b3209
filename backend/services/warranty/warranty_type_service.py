"""
خدمة إدارة أنواع الضمانات
تطبق مبادئ البرمجة الكائنية مع معالجة شاملة للأخطاء
تستخدم خدمة التاريخ والوقت الموحدة للنظام
"""

import logging
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from models.warranty_type import WarrantyType
from models.user import User
from schemas.warranty import (
    WarrantyTypeCreate,
    WarrantyTypeUpdate,
    WarrantyTypeFilters
)

# استيراد خدمة التاريخ والوقت الموحدة
from utils.datetime_utils import get_current_time_with_settings

logger = logging.getLogger(__name__)


class WarrantyTypeService:
    """
    خدمة إدارة أنواع الضمانات
    تتعامل مع جميع العمليات المتعلقة بأنواع الضمانات
    """

    def __init__(self, db: Session, current_user: User):
        """
        تهيئة خدمة أنواع الضمانات

        Args:
            db: جلسة قاعدة البيانات
            current_user: المستخدم الحالي
        """
        self.db = db
        self.current_user = current_user
        self.is_admin = current_user.role.name == "ADMIN"

        logger.info(f"تم تهيئة خدمة أنواع الضمانات للمستخدم: {current_user.username}")

    def get_warranty_types(self, filters: Optional[WarrantyTypeFilters] = None) -> List[WarrantyType]:
        """
        جلب أنواع الضمانات مع الفلاتر

        Args:
            filters: فلاتر البحث والتصفية

        Returns:
            قائمة بأنواع الضمانات
        """
        try:
            logger.info("🔄 جلب أنواع الضمانات...")

            query = self.db.query(WarrantyType)

            # تطبيق الفلاتر
            if filters:
                if filters.search:
                    search_term = f"%{filters.search}%"
                    query = query.filter(
                        or_(
                            WarrantyType.name.ilike(search_term),
                            WarrantyType.name_ar.ilike(search_term),
                            WarrantyType.description.ilike(search_term)
                        )
                    )

                if filters.status and filters.status != 'all':
                    is_active = filters.status == 'active'
                    query = query.filter(WarrantyType.is_active == is_active)

                if filters.coverage_type and filters.coverage_type != 'all':
                    query = query.filter(WarrantyType.coverage_type == filters.coverage_type)

            # ترتيب النتائج
            query = query.order_by(WarrantyType.name_ar)

            warranty_types = query.all()
            logger.info(f"✅ تم جلب {len(warranty_types)} نوع ضمان")

            return warranty_types

        except Exception as e:
            logger.error(f"❌ خطأ في جلب أنواع الضمانات: {e}")
            raise

    def get_warranty_type_by_id(self, warranty_type_id: int) -> Optional[WarrantyType]:
        """
        جلب نوع ضمان بالمعرف

        Args:
            warranty_type_id: معرف نوع الضمان

        Returns:
            نوع الضمان أو None
        """
        try:
            logger.info(f"🔄 جلب نوع الضمان بالمعرف: {warranty_type_id}")

            warranty_type = self.db.query(WarrantyType).filter(
                WarrantyType.id == warranty_type_id
            ).first()

            if warranty_type:
                logger.info(f"✅ تم العثور على نوع الضمان: {warranty_type.name_ar}")
            else:
                logger.warning(f"⚠️ لم يتم العثور على نوع الضمان بالمعرف: {warranty_type_id}")

            return warranty_type

        except Exception as e:
            logger.error(f"❌ خطأ في جلب نوع الضمان: {e}")
            raise

    def create_warranty_type(self, warranty_type_data: WarrantyTypeCreate) -> WarrantyType:
        """
        إنشاء نوع ضمان جديد

        Args:
            warranty_type_data: بيانات نوع الضمان الجديد

        Returns:
            نوع الضمان المنشأ
        """
        try:
            logger.info(f"🔄 إنشاء نوع ضمان جديد: {warranty_type_data.name_ar}")

            # التحقق من عدم وجود نوع ضمان بنفس الاسم
            existing_type = self.db.query(WarrantyType).filter(
                or_(
                    WarrantyType.name == warranty_type_data.name,
                    WarrantyType.name_ar == warranty_type_data.name_ar
                )
            ).first()

            if existing_type:
                raise ValueError(f"نوع ضمان بنفس الاسم موجود بالفعل: {warranty_type_data.name_ar}")

            # إنشاء نوع الضمان الجديد
            warranty_type = WarrantyType(
                **warranty_type_data.model_dump(),
                created_by=self.current_user.id
            )

            self.db.add(warranty_type)
            self.db.commit()
            self.db.refresh(warranty_type)

            logger.info(f"✅ تم إنشاء نوع الضمان بنجاح: {warranty_type.name_ar} (ID: {warranty_type.id})")

            return warranty_type

        except Exception as e:
            self.db.rollback()
            logger.error(f"❌ خطأ في إنشاء نوع الضمان: {e}")
            raise

    def update_warranty_type(self, warranty_type_id: int, warranty_type_data: WarrantyTypeUpdate) -> WarrantyType:
        """
        تحديث نوع ضمان موجود

        Args:
            warranty_type_id: معرف نوع الضمان
            warranty_type_data: البيانات المحدثة

        Returns:
            نوع الضمان المحدث
        """
        try:
            logger.info(f"🔄 تحديث نوع الضمان: {warranty_type_id}")

            # جلب نوع الضمان
            warranty_type = self.get_warranty_type_by_id(warranty_type_id)
            if not warranty_type:
                raise ValueError(f"نوع الضمان غير موجود: {warranty_type_id}")

            # التحقق من عدم تضارب الأسماء
            update_data = warranty_type_data.model_dump(exclude_unset=True)
            if 'name' in update_data or 'name_ar' in update_data:
                existing_type = self.db.query(WarrantyType).filter(
                    and_(
                        WarrantyType.id != warranty_type_id,
                        or_(
                            WarrantyType.name == update_data.get('name', warranty_type.name),
                            WarrantyType.name_ar == update_data.get('name_ar', warranty_type.name_ar)
                        )
                    )
                ).first()

                if existing_type:
                    raise ValueError("نوع ضمان بنفس الاسم موجود بالفعل")

            # تحديث البيانات
            for field, value in update_data.items():
                setattr(warranty_type, field, value)

            warranty_type.updated_by = self.current_user.id

            self.db.commit()
            self.db.refresh(warranty_type)

            logger.info(f"✅ تم تحديث نوع الضمان بنجاح: {warranty_type.name_ar}")

            return warranty_type

        except Exception as e:
            self.db.rollback()
            logger.error(f"❌ خطأ في تحديث نوع الضمان: {e}")
            raise

    def delete_warranty_type(self, warranty_type_id: int) -> bool:
        """
        حذف نوع ضمان

        Args:
            warranty_type_id: معرف نوع الضمان

        Returns:
            True إذا تم الحذف بنجاح
        """
        try:
            logger.info(f"🔄 حذف نوع الضمان: {warranty_type_id}")

            # جلب نوع الضمان
            warranty_type = self.get_warranty_type_by_id(warranty_type_id)
            if not warranty_type:
                raise ValueError(f"نوع الضمان غير موجود: {warranty_type_id}")

            # التحقق من عدم وجود ضمانات مرتبطة
            from models.product_warranty import ProductWarranty
            linked_warranties = self.db.query(ProductWarranty).filter(
                ProductWarranty.warranty_type_id == warranty_type_id
            ).count()

            if linked_warranties > 0:
                raise ValueError(f"لا يمكن حذف نوع الضمان لوجود {linked_warranties} ضمان مرتبط به")

            # حذف نوع الضمان
            self.db.delete(warranty_type)
            self.db.commit()

            logger.info(f"✅ تم حذف نوع الضمان بنجاح: {warranty_type.name_ar}")

            return True

        except Exception as e:
            self.db.rollback()
            logger.error(f"❌ خطأ في حذف نوع الضمان: {e}")
            raise

    def get_warranty_types_stats(self) -> Dict[str, Any]:
        """
        جلب إحصائيات أنواع الضمانات

        Returns:
            إحصائيات أنواع الضمانات
        """
        try:
            logger.info("🔄 جلب إحصائيات أنواع الضمانات...")

            total_types = self.db.query(WarrantyType).count()
            active_types = self.db.query(WarrantyType).filter(WarrantyType.is_active == True).count()
            inactive_types = total_types - active_types

            # إحصائيات حسب نوع التغطية
            coverage_stats = self.db.query(
                WarrantyType.coverage_type,
                func.count(WarrantyType.id).label('count')
            ).group_by(WarrantyType.coverage_type).all()

            stats = {
                'total_types': total_types,
                'active_types': active_types,
                'inactive_types': inactive_types,
                'coverage_stats': {stat.coverage_type: stat.count for stat in coverage_stats}
            }

            logger.info(f"✅ تم جلب إحصائيات أنواع الضمانات: {stats}")

            return stats

        except Exception as e:
            logger.error(f"❌ خطأ في جلب إحصائيات أنواع الضمانات: {e}")
            raise
