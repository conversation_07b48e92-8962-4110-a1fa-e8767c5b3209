"""
خدمة إدارة توفر الضمانات للمطالبة
تطبق مبادئ البرمجة الكائنية مع معالجة شاملة للأخطاء
تحدد الضمانات القابلة للمطالبة حسب حالة المطالبات الموجودة
"""

import logging
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, not_, exists

from models.product_warranty import ProductWarranty
from models.warranty_claim import WarrantyClaim
from models.product import Product
from models.customer import Customer
from models.user import User
from schemas.warranty import WarrantyFilters
from utils.datetime_utils import get_current_time_with_settings

# إعداد التسجيل
logger = logging.getLogger(__name__)


class WarrantyAvailabilityService:
    """
    خدمة إدارة توفر الضمانات للمطالبة
    تطبق مبادئ البرمجة الكائنية مع نمط Singleton
    """

    def __init__(self, db: Session, current_user: User):
        """
        تهيئة خدمة توفر الضمانات

        Args:
            db: جلسة قاعدة البيانات
            current_user: المستخدم الحالي
        """
        self.db = db
        self.current_user = current_user

    def get_available_warranties_for_claims(
        self, 
        filters: Optional[WarrantyFilters] = None,
        page: int = 1,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        جلب الضمانات المتاحة للمطالبة

        المنطق:
        - الضمان يجب أن يكون نشطاً (active)
        - الضمان يجب أن يكون ساري المفعول (لم ينته بعد)
        - إذا كان للضمان مطالبات:
          * يظهر فقط إذا كانت آخر مطالبة مكتملة (completed)
          * لا يظهر إذا كانت هناك مطالبة غير مكتملة (pending, approved, in_progress, rejected)

        Args:
            filters: فلاتر البحث والتصفية
            page: رقم الصفحة
            limit: عدد العناصر في الصفحة

        Returns:
            قائمة بالضمانات المتاحة للمطالبة
        """
        try:
            logger.info("🔄 جلب الضمانات المتاحة للمطالبة...")

            # بناء الاستعلام الأساسي
            query = self.db.query(ProductWarranty).options(
                joinedload(ProductWarranty.warranty_type),
                joinedload(ProductWarranty.product),
                joinedload(ProductWarranty.customer)
            )

            # فلترة الضمانات النشطة فقط
            query = query.filter(ProductWarranty.status == 'active')

            # فلترة الضمانات سارية المفعول
            current_time = get_current_time_with_settings(self.db)
            current_date = current_time.date()
            query = query.filter(ProductWarranty.end_date >= current_date)

            # تطبيق منطق المطالبات
            query = self._apply_claims_availability_logic(query)

            # تطبيق الفلاتر الإضافية
            if filters:
                query = self._apply_additional_filters(query, filters)

            # تطبيق الترقيم
            offset = (page - 1) * limit
            warranties = query.offset(offset).limit(limit).all()

            # تحويل النتائج إلى قاموس
            result = []
            for warranty in warranties:
                warranty_data = {
                    'id': warranty.id,
                    'warranty_number': warranty.warranty_number,
                    'product_id': warranty.product_id,
                    'product_name': warranty.product.name if warranty.product else None,
                    'product_barcode': warranty.product.barcode if warranty.product else None,
                    'warranty_type_id': warranty.warranty_type_id,
                    'warranty_type_name': warranty.warranty_type.name if warranty.warranty_type else None,
                    'customer_id': warranty.customer_id,
                    'customer_name': warranty.customer.name if warranty.customer else None,
                    'purchase_date': warranty.purchase_date,
                    'start_date': warranty.start_date,
                    'end_date': warranty.end_date,
                    'status': warranty.status,
                    'notes': warranty.notes,
                    'created_at': warranty.created_at,
                    'updated_at': warranty.updated_at,
                    # خصائص محسوبة
                    'is_active': warranty.get_is_active(),
                    'days_remaining': warranty.get_days_remaining(),
                    'is_expiring_soon': warranty.get_is_expiring_soon(),
                    # معلومات المطالبات
                    'has_claims': warranty.has_claims,
                    'can_create_claim': True  # جميع الضمانات المرجعة قابلة للمطالبة
                }
                result.append(warranty_data)

            logger.info(f"✅ تم جلب {len(result)} ضمان متاح للمطالبة")
            return result

        except Exception as e:
            logger.error(f"❌ خطأ في جلب الضمانات المتاحة للمطالبة: {e}")
            raise

    def _apply_claims_availability_logic(self, query):
        """
        تطبيق منطق توفر الضمانات للمطالبة

        Args:
            query: استعلام SQLAlchemy

        Returns:
            الاستعلام المحدث
        """
        try:
            # الضمانات التي ليس لها مطالبات (متاحة دائماً)
            no_claims_condition = not_(exists().where(
                WarrantyClaim.warranty_id == ProductWarranty.id
            ))

            # الضمانات التي آخر مطالبة لها مكتملة
            # استخدام subquery للحصول على آخر مطالبة لكل ضمان
            from sqlalchemy import func

            latest_claim_subquery = self.db.query(
                WarrantyClaim.warranty_id,
                func.max(WarrantyClaim.created_at).label('latest_created_at')
            ).group_by(WarrantyClaim.warranty_id).subquery()

            latest_claim_completed = exists().where(
                and_(
                    WarrantyClaim.warranty_id == ProductWarranty.id,
                    WarrantyClaim.status == 'completed',
                    WarrantyClaim.created_at == latest_claim_subquery.c.latest_created_at,
                    WarrantyClaim.warranty_id == latest_claim_subquery.c.warranty_id
                )
            )

            # دمج الشروط: إما لا توجد مطالبات أو آخر مطالبة مكتملة
            availability_condition = or_(
                no_claims_condition,
                latest_claim_completed
            )

            return query.filter(availability_condition)

        except Exception as e:
            logger.error(f"❌ خطأ في تطبيق منطق توفر المطالبات: {e}")
            raise

    def _apply_additional_filters(self, query, filters: WarrantyFilters):
        """
        تطبيق الفلاتر الإضافية

        Args:
            query: استعلام SQLAlchemy
            filters: فلاتر البحث

        Returns:
            الاستعلام المحدث
        """
        try:
            # فلتر البحث
            if filters.search:
                search_term = f"%{filters.search}%"
                query = query.filter(
                    or_(
                        ProductWarranty.warranty_number.ilike(search_term),
                        Product.name.ilike(search_term),
                        Product.barcode.ilike(search_term),
                        Customer.name.ilike(search_term)
                    )
                ).join(Product, ProductWarranty.product_id == Product.id, isouter=True)\
                 .join(Customer, ProductWarranty.customer_id == Customer.id, isouter=True)

            # فلتر نوع الضمان
            if filters.warranty_type_id:
                query = query.filter(ProductWarranty.warranty_type_id == filters.warranty_type_id)

            # فلتر العميل
            if filters.customer_id:
                query = query.filter(ProductWarranty.customer_id == filters.customer_id)

            # فلتر التاريخ
            if filters.start_date:
                query = query.filter(ProductWarranty.start_date >= filters.start_date)

            if filters.end_date:
                query = query.filter(ProductWarranty.end_date <= filters.end_date)

            return query

        except Exception as e:
            logger.error(f"❌ خطأ في تطبيق الفلاتر الإضافية: {e}")
            raise

    def check_warranty_availability_for_claim(self, warranty_id: int) -> Dict[str, Any]:
        """
        فحص توفر ضمان محدد للمطالبة

        Args:
            warranty_id: معرف الضمان

        Returns:
            معلومات توفر الضمان للمطالبة
        """
        try:
            logger.info(f"🔄 فحص توفر الضمان {warranty_id} للمطالبة")

            # جلب الضمان
            warranty = self.db.query(ProductWarranty).filter(
                ProductWarranty.id == warranty_id
            ).first()

            if not warranty:
                return {
                    "available": False,
                    "reason": "الضمان غير موجود",
                    "warranty_id": warranty_id
                }

            # فحص حالة الضمان
            if warranty.status != 'active':
                return {
                    "available": False,
                    "reason": f"الضمان غير نشط (الحالة: {warranty.status})",
                    "warranty_id": warranty_id,
                    "warranty_number": warranty.warranty_number
                }

            # فحص صلاحية الضمان
            if not warranty.get_is_active():
                return {
                    "available": False,
                    "reason": "الضمان منتهي الصلاحية",
                    "warranty_id": warranty_id,
                    "warranty_number": warranty.warranty_number
                }

            # فحص المطالبات الموجودة
            latest_claim = self.db.query(WarrantyClaim).filter(
                WarrantyClaim.warranty_id == warranty_id
            ).order_by(WarrantyClaim.created_at.desc()).first()

            if latest_claim:
                if latest_claim.status != 'completed':
                    return {
                        "available": False,
                        "reason": f"يوجد مطالبة غير مكتملة (الحالة: {latest_claim.status})",
                        "warranty_id": warranty_id,
                        "warranty_number": warranty.warranty_number,
                        "latest_claim_id": latest_claim.id,
                        "latest_claim_status": latest_claim.status
                    }

            # الضمان متاح للمطالبة
            return {
                "available": True,
                "reason": "الضمان متاح للمطالبة",
                "warranty_id": warranty_id,
                "warranty_number": warranty.warranty_number,
                "has_previous_claims": latest_claim is not None,
                "latest_claim_status": latest_claim.status if latest_claim else None
            }

        except Exception as e:
            logger.error(f"❌ خطأ في فحص توفر الضمان للمطالبة: {e}")
            raise
