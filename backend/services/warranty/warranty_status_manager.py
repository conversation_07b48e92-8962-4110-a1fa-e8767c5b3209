"""
مدير حالات الضمان الديناميكي
يدير تحديث حالة الضمان تلقائياً عند إنشاء المطالبة وعند تغيير حالة المطالبة
تطبق مبادئ البرمجة الكائنية مع معالجة شاملة للأخطاء
"""

import logging
from typing import Dict, Any
from sqlalchemy.orm import Session

from models.product_warranty import ProductWarranty
from models.warranty_claim import WarrantyClaim
from models.user import User
from utils.datetime_utils import get_current_time_with_settings
from utils.datetime_utils import get_current_time_with_settings

logger = logging.getLogger(__name__)


class WarrantyStatusManager:
    """
    مدير حالات الضمان الديناميكي
    يدير تحديث حالة الضمان تلقائياً حسب حالة المطالبات
    """

    def __init__(self, db: Session, current_user: User):
        """
        تهيئة مدير حالات الضمان

        Args:
            db: جلسة قاعدة البيانات
            current_user: المستخدم الحالي
        """
        self.db = db
        self.current_user = current_user

    def update_warranty_status_on_claim_creation(self, warranty_id: int) -> Dict[str, Any]:
        """
        تحديث علامة المطالبة عند إنشاء مطالبة جديدة
        تحديث has_claims إلى True (الحالة تبقى كما هي)

        Args:
            warranty_id: معرف الضمان

        Returns:
            معلومات التحديث
        """
        try:
            logger.info(f"🔄 تحديث علامة المطالبة للضمان {warranty_id} عند إنشاء المطالبة")

            # جلب الضمان
            warranty = self.db.query(ProductWarranty).filter(
                ProductWarranty.id == warranty_id
            ).first()

            if not warranty:
                raise ValueError(f"الضمان غير موجود: {warranty_id}")

            # تحديث علامة المطالبة
            old_has_claims = warranty.has_claims
            setattr(warranty, 'has_claims', True)
            setattr(warranty, 'updated_by', self.current_user.id)

            # حفظ التغييرات
            self.db.commit()
            self.db.refresh(warranty)

            logger.info(f"✅ تم تحديث علامة المطالبة للضمان {warranty.warranty_number}")

            return {
                "updated": True,
                "reason": "تم إنشاء مطالبة جديدة للضمان",
                "old_has_claims": old_has_claims,
                "new_has_claims": True,
                "warranty_number": warranty.warranty_number,
                "warranty_status": warranty.status
            }

        except Exception as e:
            self.db.rollback()
            logger.error(f"❌ خطأ في تحديث علامة المطالبة عند إنشاء المطالبة: {e}")
            raise

    def update_warranty_status_on_claim_completion(
        self, 
        claim: WarrantyClaim, 
        new_claim_status: str
    ) -> Dict[str, Any]:
        """
        تحديث حالة الضمان عند اكتمال المطالبة
        - للاسترداد والاستبدال: إلغاء الضمان
        - للإصلاح: إبقاء الضمان نشطاً

        Args:
            claim: المطالبة
            new_claim_status: حالة المطالبة الجديدة

        Returns:
            معلومات التحديث
        """
        try:
            logger.info(f"🔄 تحديث حالة الضمان عند تغيير حالة المطالبة إلى {new_claim_status}")

            # التحقق من أن المطالبة مكتملة
            if new_claim_status != 'completed':
                return {
                    "updated": False,
                    "reason": f"المطالبة ليست مكتملة (الحالة: {new_claim_status})",
                    "action": "none"
                }

            # جلب الضمان المرتبط
            warranty = claim.warranty
            if not warranty:
                raise ValueError(f"الضمان غير موجود للمطالبة: {claim.id}")

            # تحديد الإجراء حسب نوع المطالبة
            if claim.claim_type in ['refund', 'replacement']:
                # للاسترداد والاستبدال: إلغاء الضمان
                return self._void_warranty_on_claim_completion(warranty, claim)
            
            elif str(claim.claim_type) == 'repair':
                # للإصلاح: إبقاء الضمان نشطاً (إذا لم يكن منتهي الصلاحية)
                return self._restore_warranty_on_repair_completion(warranty, claim)
            
            else:
                logger.warning(f"⚠️ نوع مطالبة غير معروف: {claim.claim_type}")
                return {
                    "updated": False,
                    "reason": f"نوع مطالبة غير معروف: {claim.claim_type}",
                    "action": "none"
                }

        except Exception as e:
            self.db.rollback()
            logger.error(f"❌ خطأ في تحديث حالة الضمان عند اكتمال المطالبة: {e}")
            raise

    def _void_warranty_on_claim_completion(
        self, 
        warranty: ProductWarranty, 
        claim: WarrantyClaim
    ) -> Dict[str, Any]:
        """
        إلغاء الضمان عند اكتمال مطالبة الاسترداد أو الاستبدال

        Args:
            warranty: الضمان
            claim: المطالبة

        Returns:
            معلومات التحديث
        """
        try:
            old_status = warranty.status
            current_time = get_current_time_with_settings(self.db)

            # إلغاء الضمان
            setattr(warranty, 'status', 'voided')
            setattr(warranty, 'void_reason', f"تم إلغاء الضمان بسبب اكتمال مطالبة {claim.claim_type} (رقم المطالبة: {claim.claim_number})")
            setattr(warranty, 'voided_at', current_time)
            setattr(warranty, 'voided_by', self.current_user.id)
            setattr(warranty, 'updated_by', self.current_user.id)

            # حفظ التغييرات
            self.db.commit()
            self.db.refresh(warranty)

            logger.info(f"✅ تم إلغاء الضمان {warranty.warranty_number} بسبب اكتمال مطالبة {claim.claim_type}")

            return {
                "updated": True,
                "reason": f"اكتمال مطالبة {claim.claim_type}",
                "old_status": old_status,
                "new_status": "voided",
                "action": "voided",
                "warranty_number": warranty.warranty_number,
                "claim_number": claim.claim_number,
                "claim_type": claim.claim_type
            }

        except Exception as e:
            logger.error(f"❌ خطأ في إلغاء الضمان: {e}")
            raise

    def _restore_warranty_on_repair_completion(
        self, 
        warranty: ProductWarranty, 
        claim: WarrantyClaim
    ) -> Dict[str, Any]:
        """
        استعادة الضمان للحالة النشطة عند اكتمال مطالبة الإصلاح

        Args:
            warranty: الضمان
            claim: المطالبة

        Returns:
            معلومات التحديث
        """
        try:
            old_status = warranty.status
            current_time = get_current_time_with_settings(self.db)
            current_date = current_time.date()

            # التحقق من صلاحية الضمان
            if warranty.end_date < current_date:
                # الضمان منتهي الصلاحية
                setattr(warranty, 'status', 'expired')
                new_status = 'expired'
                reason = "انتهت صلاحية الضمان"
            else:
                # الضمان لا يزال ساري المفعول
                setattr(warranty, 'status', 'active')
                new_status = 'active'
                reason = "تم إصلاح المنتج والضمان لا يزال ساري المفعول"

            setattr(warranty, 'updated_by', self.current_user.id)

            # حفظ التغييرات
            self.db.commit()
            self.db.refresh(warranty)

            logger.info(f"✅ تم تحديث حالة الضمان {warranty.warranty_number} إلى {new_status} بعد اكتمال الإصلاح")

            return {
                "updated": True,
                "reason": reason,
                "old_status": old_status,
                "new_status": new_status,
                "action": "restored" if new_status == 'active' else "expired",
                "warranty_number": warranty.warranty_number,
                "claim_number": claim.claim_number,
                "claim_type": claim.claim_type
            }

        except Exception as e:
            logger.error(f"❌ خطأ في استعادة الضمان: {e}")
            raise

    def get_warranty_status_info(self, warranty_id: int) -> Dict[str, Any]:
        """
        الحصول على معلومات حالة الضمان والمطالبات المرتبطة

        Args:
            warranty_id: معرف الضمان

        Returns:
            معلومات حالة الضمان
        """
        try:
            logger.info(f"🔄 جلب معلومات حالة الضمان {warranty_id}")

            # جلب الضمان مع المطالبات
            warranty = self.db.query(ProductWarranty).filter(
                ProductWarranty.id == warranty_id
            ).first()

            if not warranty:
                raise ValueError(f"الضمان غير موجود: {warranty_id}")

            # جلب المطالبات المرتبطة
            claims = self.db.query(WarrantyClaim).filter(
                WarrantyClaim.warranty_id == warranty_id
            ).all()

            # تحليل حالة الضمان
            status_info = {
                "warranty_id": warranty_id,
                "warranty_number": warranty.warranty_number,
                "current_status": warranty.status,
                "is_active": warranty.get_is_active(),
                "total_claims": len(claims),
                "claims_by_status": {},
                "claims_by_type": {},
                "can_create_claim": warranty.status == 'active' and warranty.get_is_active(),
                "status_history": []
            }

            # تحليل المطالبات
            for claim in claims:
                # حسب الحالة
                if claim.status not in status_info["claims_by_status"]:
                    status_info["claims_by_status"][claim.status] = 0
                status_info["claims_by_status"][claim.status] += 1

                # حسب النوع
                if claim.claim_type not in status_info["claims_by_type"]:
                    status_info["claims_by_type"][claim.claim_type] = 0
                status_info["claims_by_type"][claim.claim_type] += 1

            logger.info(f"✅ تم جلب معلومات حالة الضمان {warranty_id}")
            return status_info

        except Exception as e:
            logger.error(f"❌ خطأ في جلب معلومات حالة الضمان: {e}")
            raise
