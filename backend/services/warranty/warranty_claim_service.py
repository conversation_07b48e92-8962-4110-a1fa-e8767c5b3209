"""
خدمة إدارة مطالبات الضمان
تطبق مبادئ البرمجة الكائنية مع معالجة شاملة للأخطاء
تستخدم خدمة التاريخ والوقت الموحدة للنظام
"""

import logging
import uuid
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import or_

from models.warranty_claim import WarrantyClaim
from models.product_warranty import ProductWarranty
from models.claim_status_history import ClaimStatusHistory
from models.product import Product
from models.customer import Customer
from models.user import User
from schemas.warranty import (
    WarrantyClaimCreate,
    ClaimFilters,
    ProcessClaimData
)

# استيراد خدمة التاريخ والوقت الموحدة
from utils.datetime_utils import get_current_time_with_settings

# استيراد مدير الحالات الديناميكي
from services.warranty.claim_status_manager import ClaimStatusManager

# استيراد مدير حالات الضمان الديناميكي
from services.warranty.warranty_status_manager import WarrantyStatusManager

logger = logging.getLogger(__name__)


class WarrantyClaimService:
    """
    خدمة إدارة مطالبات الضمان
    تتعامل مع جميع العمليات المتعلقة بمطالبات الضمان
    """

    def __init__(self, db: Session, current_user: User):
        """
        تهيئة خدمة مطالبات الضمان

        Args:
            db: جلسة قاعدة البيانات
            current_user: المستخدم الحالي
        """
        self.db = db
        self.current_user = current_user
        self.is_admin = current_user.role.name == "ADMIN"
        self.status_manager = ClaimStatusManager(db, current_user)
        self.warranty_status_manager = WarrantyStatusManager(db, current_user)

        logger.info(f"تم تهيئة خدمة مطالبات الضمان للمستخدم: {current_user.username}")

    def generate_claim_number(self) -> str:
        """
        توليد رقم مطالبة فريد باستخدام خدمة التاريخ والوقت الموحدة

        Returns:
            رقم المطالبة الفريد
        """
        try:
            # الحصول على التاريخ الحالي من خدمة التاريخ الموحدة
            current_time = get_current_time_with_settings(self.db)
            year = current_time.year

            # تنسيق: CLM-YYYY-XXXXXX
            unique_id = str(uuid.uuid4())[:6].upper()
            claim_number = f"CLM-{year}-{unique_id}"

            # التحقق من عدم وجود رقم مشابه
            existing = self.db.query(WarrantyClaim).filter(
                WarrantyClaim.claim_number == claim_number
            ).first()

            if existing:
                # إعادة المحاولة مع معرف جديد
                unique_id = str(uuid.uuid4())[:8].upper()
                claim_number = f"CLM-{year}-{unique_id}"

            logger.info(f"تم توليد رقم مطالبة جديد: {claim_number}")
            return claim_number

        except Exception as e:
            logger.error(f"❌ خطأ في توليد رقم المطالبة: {e}")
            raise

    def get_claims(self, filters: Optional[ClaimFilters] = None) -> List[Dict[str, Any]]:
        """
        جلب مطالبات الضمان مع الفلاتر

        Args:
            filters: فلاتر البحث والتصفية

        Returns:
            قائمة بمطالبات الضمان مع البيانات المرتبطة
        """
        try:
            logger.info("🔄 جلب مطالبات الضمان...")

            query = self.db.query(WarrantyClaim).options(
                joinedload(WarrantyClaim.warranty).joinedload(ProductWarranty.product),
                joinedload(WarrantyClaim.warranty).joinedload(ProductWarranty.warranty_type),
                joinedload(WarrantyClaim.warranty).joinedload(ProductWarranty.customer)
            )

            # تطبيق الفلاتر
            if filters:
                if filters.search:
                    search_term = f"%{filters.search}%"
                    query = query.join(ProductWarranty).join(Product).outerjoin(Customer).filter(
                        or_(
                            WarrantyClaim.claim_number.ilike(search_term),
                            ProductWarranty.warranty_number.ilike(search_term),
                            Product.name.ilike(search_term),
                            Customer.name.ilike(search_term),
                            WarrantyClaim.issue_description.ilike(search_term)
                        )
                    )

                if filters.status and filters.status != 'all':
                    query = query.filter(WarrantyClaim.status == filters.status)

                if filters.claim_type and filters.claim_type != 'all':
                    query = query.filter(WarrantyClaim.claim_type == filters.claim_type)

                if filters.priority and filters.priority != 'all':
                    query = query.filter(WarrantyClaim.priority == filters.priority)

                if filters.warranty_id:
                    query = query.filter(WarrantyClaim.warranty_id == filters.warranty_id)

                if filters.start_date:
                    query = query.filter(WarrantyClaim.claim_date >= filters.start_date)

                if filters.end_date:
                    query = query.filter(WarrantyClaim.claim_date <= filters.end_date)

            # ترتيب النتائج
            query = query.order_by(WarrantyClaim.created_at.desc())

            claims = query.all()

            # تحويل النتائج مع البيانات المحسوبة
            result = []
            for claim in claims:
                claim_data = {
                    'id': claim.id,
                    'claim_number': claim.claim_number,
                    'warranty_id': claim.warranty_id,
                    'warranty_number': claim.warranty.warranty_number if claim.warranty else None,
                    'product_name': claim.warranty.product.name if claim.warranty and claim.warranty.product else None,
                    'customer_name': claim.warranty.customer.name if claim.warranty and claim.warranty.customer else None,
                    'claim_type': claim.claim_type,
                    'issue_description': claim.issue_description,
                    'claim_description': claim.claim_description,
                    'claim_date': claim.claim_date,
                    'expected_resolution_date': claim.expected_resolution_date,
                    'status': claim.status,
                    'priority': claim.priority,
                    'resolution': claim.resolution,
                    'resolution_date': claim.resolution_date,
                    'resolved_by': claim.resolved_by,
                    'estimated_cost': claim.estimated_cost,
                    'actual_cost': claim.actual_cost,
                    'cost_covered_by_warranty': claim.cost_covered_by_warranty,
                    'notes': claim.notes,
                    'internal_notes': claim.internal_notes,
                    'reviewed_by': claim.reviewed_by,
                    'reviewed_at': claim.reviewed_at,
                    'approved_by': claim.approved_by,
                    'approved_at': claim.approved_at,
                    'rejection_reason': claim.rejection_reason,
                    'rejected_by': claim.rejected_by,
                    'rejected_at': claim.rejected_at,
                    'created_at': claim.created_at,
                    'updated_at': claim.updated_at,
                    'created_by': claim.created_by,
                    'updated_by': claim.updated_by,
                    # خصائص محسوبة
                    'is_pending': claim.get_is_pending(),
                    'is_approved': claim.get_is_approved(),
                    'is_completed': claim.get_is_completed(),
                    'days_since_claim': claim.get_days_since_claim()
                }
                result.append(claim_data)

            logger.info(f"✅ تم جلب {len(result)} مطالبة")

            return result

        except Exception as e:
            logger.error(f"❌ خطأ في جلب مطالبات الضمان: {e}")
            raise

    def get_claim_by_id(self, claim_id: int) -> Optional[WarrantyClaim]:
        """
        جلب مطالبة بالمعرف

        Args:
            claim_id: معرف المطالبة

        Returns:
            المطالبة أو None
        """
        try:
            logger.info(f"🔄 جلب المطالبة بالمعرف: {claim_id}")

            claim = self.db.query(WarrantyClaim).options(
                joinedload(WarrantyClaim.warranty).joinedload(ProductWarranty.product),
                joinedload(WarrantyClaim.warranty).joinedload(ProductWarranty.warranty_type),
                joinedload(WarrantyClaim.warranty).joinedload(ProductWarranty.customer)
            ).filter(WarrantyClaim.id == claim_id).first()

            if claim:
                logger.info(f"✅ تم العثور على المطالبة: {claim.claim_number}")
            else:
                logger.warning(f"⚠️ لم يتم العثور على المطالبة بالمعرف: {claim_id}")

            return claim

        except Exception as e:
            logger.error(f"❌ خطأ في جلب المطالبة: {e}")
            raise

    def create_claim(self, claim_data: WarrantyClaimCreate) -> WarrantyClaim:
        """
        إنشاء مطالبة جديدة

        Args:
            claim_data: بيانات المطالبة الجديدة

        Returns:
            المطالبة المنشأة
        """
        try:
            logger.info(f"🔄 إنشاء مطالبة جديدة للضمان: {claim_data.warranty_id}")

            # التحقق من وجود الضمان
            warranty = self.db.query(ProductWarranty).filter(
                ProductWarranty.id == claim_data.warranty_id
            ).first()
            if not warranty:
                raise ValueError(f"الضمان غير موجود: {claim_data.warranty_id}")

            # التحقق من صلاحية الضمان للمطالبة
            if warranty.status != 'active':
                raise ValueError("لا يمكن تقديم مطالبة لضمان غير نشط")

            if not warranty.get_is_active():
                raise ValueError("الضمان منتهي الصلاحية")

            # توليد رقم مطالبة فريد
            claim_number = self.generate_claim_number()

            # إنشاء المطالبة الجديدة
            claim = WarrantyClaim(
                **claim_data.model_dump(),
                claim_number=claim_number,
                created_by=self.current_user.id
            )

            self.db.add(claim)
            self.db.commit()
            self.db.refresh(claim)

            # تحديث حالة الضمان إلى 'claimed' عند إنشاء المطالبة
            try:
                warranty_update_result = self.warranty_status_manager.update_warranty_status_on_claim_creation(
                    claim_data.warranty_id
                )
                if warranty_update_result["updated"]:
                    logger.info(f"✅ تم تحديث حالة الضمان: {warranty_update_result['warranty_number']} من {warranty_update_result['old_status']} إلى {warranty_update_result['new_status']}")
                else:
                    logger.warning(f"⚠️ لم يتم تحديث حالة الضمان: {warranty_update_result['reason']}")
            except Exception as warranty_error:
                logger.error(f"❌ خطأ في تحديث حالة الضمان: {warranty_error}")
                # لا نرفع الخطأ هنا لأن المطالبة تم إنشاؤها بنجاح

            logger.info(f"✅ تم إنشاء المطالبة بنجاح: {claim.claim_number} (ID: {claim.id})")

            return claim

        except Exception as e:
            self.db.rollback()
            logger.error(f"❌ خطأ في إنشاء المطالبة: {e}")
            raise

    def process_claim(self, claim_id: int, process_data: ProcessClaimData) -> WarrantyClaim:
        """
        معالجة مطالبة الضمان

        Args:
            claim_id: معرف المطالبة
            process_data: بيانات المعالجة

        Returns:
            المطالبة المحدثة
        """
        try:
            logger.info(f"🔄 معالجة المطالبة: {claim_id} - الحالة الجديدة: {process_data.status}")

            # جلب المطالبة
            claim = self.get_claim_by_id(claim_id)
            if not claim:
                raise ValueError(f"المطالبة غير موجودة: {claim_id}")

            # تسجيل تغيير الحالة في التاريخ قبل التحديث
            try:
                self.status_manager.record_status_change(
                    claim=claim,
                    new_status=process_data.status,
                    reason=f"معالجة المطالبة: {process_data.status}",
                    notes=process_data.notes,
                    resolution_details=process_data.resolution
                )
            except Exception as history_error:
                logger.warning(f"⚠️ فشل في تسجيل تاريخ التغيير: {history_error}")

            # تحديث حالة المطالبة
            setattr(claim, 'status', process_data.status)
            setattr(claim, 'updated_by', self.current_user.id)

            # الحصول على التاريخ والوقت الحالي من خدمة التاريخ الموحدة
            current_time = get_current_time_with_settings(self.db)
            current_date = current_time.date()

            # معالجة حسب الحالة الجديدة
            if process_data.status == 'approved':
                setattr(claim, 'approved_by', self.current_user.id)
                setattr(claim, 'approved_at', current_time)
                if process_data.resolution:
                    setattr(claim, 'resolution', process_data.resolution)

            elif process_data.status == 'rejected':
                setattr(claim, 'rejected_by', self.current_user.id)
                setattr(claim, 'rejected_at', current_time)
                if process_data.rejection_reason:
                    setattr(claim, 'rejection_reason', process_data.rejection_reason)

            elif process_data.status == 'completed':
                setattr(claim, 'resolution_date', current_date)
                setattr(claim, 'resolved_by', self.current_user.id)
                if process_data.resolution:
                    setattr(claim, 'resolution', process_data.resolution)

            # تحديث التكلفة الفعلية
            if process_data.actual_cost is not None:
                setattr(claim, 'actual_cost', process_data.actual_cost)

            # تحديث الملاحظات
            if process_data.notes:
                setattr(claim, 'notes', process_data.notes)

            if process_data.internal_notes:
                setattr(claim, 'internal_notes', process_data.internal_notes)

            self.db.commit()
            self.db.refresh(claim)

            # تحديث حالة الضمان عند تغيير حالة المطالبة
            try:
                warranty_update_result = self.warranty_status_manager.update_warranty_status_on_claim_completion(
                    claim, process_data.status
                )
                if warranty_update_result["updated"]:
                    logger.info(f"✅ تم تحديث حالة الضمان: {warranty_update_result['warranty_number']} - {warranty_update_result['action']} - {warranty_update_result['reason']}")
                else:
                    logger.info(f"ℹ️ لم يتم تحديث حالة الضمان: {warranty_update_result['reason']}")
            except Exception as warranty_error:
                logger.error(f"❌ خطأ في تحديث حالة الضمان: {warranty_error}")
                # لا نرفع الخطأ هنا لأن المطالبة تم معالجتها بنجاح

            # إعادة جلب المطالبة مع البيانات المرجعية المحدثة
            updated_claim = self.db.query(WarrantyClaim).options(
                joinedload(WarrantyClaim.warranty).joinedload(ProductWarranty.product),
                joinedload(WarrantyClaim.warranty).joinedload(ProductWarranty.warranty_type),
                joinedload(WarrantyClaim.warranty).joinedload(ProductWarranty.customer)
            ).filter(WarrantyClaim.id == claim_id).first()

            logger.info(f"✅ تم معالجة المطالبة بنجاح: {claim.claim_number}")

            return updated_claim

        except Exception as e:
            self.db.rollback()
            logger.error(f"❌ خطأ في معالجة المطالبة: {e}")
            raise

    def get_available_next_statuses(self, claim_id: int) -> List[Dict[str, Any]]:
        """
        جلب الحالات التالية المتاحة للمطالبة

        Args:
            claim_id: معرف المطالبة

        Returns:
            قائمة بالحالات التالية المتاحة
        """
        try:
            logger.info(f"🔄 جلب الحالات التالية المتاحة للمطالبة: {claim_id}")

            # جلب المطالبة
            claim = self.get_claim_by_id(claim_id)
            if not claim:
                raise ValueError(f"المطالبة غير موجودة: {claim_id}")

            # جلب الحالات التالية المتاحة مع نوع المطالبة
            next_statuses = self.status_manager.get_available_next_statuses(
                str(claim.status),
                str(claim.claim_type)
            )

            logger.info(f"✅ تم جلب {len(next_statuses)} حالة متاحة")
            return next_statuses

        except Exception as e:
            logger.error(f"❌ خطأ في جلب الحالات التالية: {e}")
            raise

    def get_claim_status_history(self, claim_id: int) -> List[Dict[str, Any]]:
        """
        جلب تاريخ تغييرات حالة المطالبة

        Args:
            claim_id: معرف المطالبة

        Returns:
            قائمة بتاريخ تغييرات الحالة
        """
        try:
            logger.info(f"🔄 جلب تاريخ تغييرات الحالة للمطالبة: {claim_id}")

            # التحقق من وجود المطالبة
            claim = self.get_claim_by_id(claim_id)
            if not claim:
                raise ValueError(f"المطالبة غير موجودة: {claim_id}")

            # جلب تاريخ التغييرات
            history = self.status_manager.get_status_history(claim_id)

            logger.info(f"✅ تم جلب {len(history)} سجل من تاريخ التغييرات")
            return history

        except Exception as e:
            logger.error(f"❌ خطأ في جلب تاريخ التغييرات: {e}")
            raise

    def change_claim_status(
        self,
        claim_id: int,
        new_status: str,
        reason: Optional[str] = None,
        notes: Optional[str] = None,
        resolution_details: Optional[str] = None,
        estimated_cost: Optional[str] = None,
        actual_cost: Optional[str] = None
    ) -> WarrantyClaim:
        """
        تغيير حالة المطالبة مع تسجيل التاريخ

        Args:
            claim_id: معرف المطالبة
            new_status: الحالة الجديدة
            reason: سبب التغيير
            notes: ملاحظات إضافية
            resolution_details: تفاصيل الحل
            estimated_cost: التكلفة المقدرة
            actual_cost: التكلفة الفعلية

        Returns:
            المطالبة المحدثة
        """
        try:
            logger.info(f"🔄 تغيير حالة المطالبة {claim_id} إلى {new_status}")

            # جلب المطالبة
            claim = self.get_claim_by_id(claim_id)
            if not claim:
                raise ValueError(f"المطالبة غير موجودة: {claim_id}")

            # التحقق من إمكانية الانتقال
            can_transition, error_message = self.status_manager.can_transition_to(
                str(claim.status), new_status, str(claim.claim_type)
            )

            if not can_transition:
                logger.warning(f"⚠️ انتقال غير مسموح من {claim.status} إلى {new_status}: {error_message}")
                # السماح بالانتقال في حالة التطوير أو إذا كانت الحالة الحالية غير معروفة
                if claim.status not in ['pending', 'approved', 'rejected', 'in_progress', 'completed']:
                    logger.info(f"🔄 السماح بالانتقال من حالة غير معروفة: {claim.status}")
                else:
                    raise ValueError(error_message)

            # تسجيل تغيير الحالة في التاريخ (إذا كان الجدول موجود)
            try:
                self.status_manager.record_status_change(
                    claim=claim,
                    new_status=new_status,
                    reason=reason,
                    notes=notes,
                    resolution_details=resolution_details,
                    estimated_cost=estimated_cost,
                    actual_cost=actual_cost
                )
            except Exception as history_error:
                logger.warning(f"⚠️ فشل في تسجيل تاريخ التغيير: {history_error}")
                # المتابعة بدون تسجيل التاريخ

            # تحديث حالة المطالبة
            old_status = str(claim.status)
            setattr(claim, 'status', new_status)

            # تحديث الحقول الإضافية حسب الحالة
            if new_status == "approved" and resolution_details:
                setattr(claim, 'resolution', resolution_details)
            elif new_status == "rejected" and reason:
                setattr(claim, 'resolution', reason)
            elif new_status == "in_progress" and resolution_details:
                setattr(claim, 'resolution', resolution_details)
            elif new_status == "completed":
                if resolution_details:
                    setattr(claim, 'resolution', resolution_details)
                if actual_cost:
                    setattr(claim, 'actual_cost', actual_cost)

            # حفظ التغييرات
            self.db.commit()

            # تحديث حالة الضمان عند تغيير حالة المطالبة
            try:
                warranty_update_result = self.warranty_status_manager.update_warranty_status_on_claim_completion(
                    claim, new_status
                )
                if warranty_update_result["updated"]:
                    logger.info(f"✅ تم تحديث حالة الضمان: {warranty_update_result['warranty_number']} - {warranty_update_result['action']} - {warranty_update_result['reason']}")
                else:
                    logger.info(f"ℹ️ لم يتم تحديث حالة الضمان: {warranty_update_result['reason']}")
            except Exception as warranty_error:
                logger.error(f"❌ خطأ في تحديث حالة الضمان: {warranty_error}")
                # لا نرفع الخطأ هنا لأن المطالبة تم تحديثها بنجاح

            logger.info(f"✅ تم تغيير حالة المطالبة من {old_status} إلى {new_status}")
            return claim

        except Exception as e:
            self.db.rollback()
            logger.error(f"❌ خطأ في تغيير حالة المطالبة: {e}")
            raise
