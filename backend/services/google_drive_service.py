"""
خدمة Google Drive للنسخ الاحتياطية
"""

import os
import json
import logging
from typing import Optional, Dict, Any, List, TYPE_CHECKING
from datetime import datetime
import tempfile
import io

if TYPE_CHECKING:
    from google.oauth2.credentials import Credentials
    from google.auth.transport.requests import Request
    from google_auth_oauthlib.flow import Flow
    from googleapiclient.http import MediaFileUpload, MediaIoBaseUpload
    from googleapiclient.errors import HttpError

try:
    from google.oauth2.credentials import Credentials
    from google.auth.transport.requests import Request
    from google_auth_oauthlib.flow import Flow
    from googleapiclient.discovery import build
    from googleapiclient.http import MediaFileUpload, MediaIoBaseUpload
    from googleapiclient.errors import HttpError
    GOOGLE_APIS_AVAILABLE = True
except ImportError:
    GOOGLE_APIS_AVAILABLE = False

from sqlalchemy.orm import Session
from models.setting import Setting

logger = logging.getLogger(__name__)

class GoogleDriveService:
    """خدمة التعامل مع Google Drive"""

    def __init__(self, db_session: Optional[Session] = None):
        self.db_session = db_session
        self.service = None
        self.credentials = None
        self.scopes = ['https://www.googleapis.com/auth/drive.file']

    def _get_setting_value(self, key: str, default: str = "") -> str:
        """الحصول على قيمة إعداد من قاعدة البيانات"""
        if not self.db_session:
            return default

        try:
            setting = self.db_session.query(Setting).filter(Setting.key == key).first()
            return setting.value if setting else default
        except Exception as e:
            logger.error(f"خطأ في جلب الإعداد {key}: {e}")
            return default

    def _save_setting_value(self, key: str, value: str) -> bool:
        """حفظ قيمة إعداد في قاعدة البيانات"""
        if not self.db_session:
            return False

        try:
            setting = self.db_session.query(Setting).filter(Setting.key == key).first()
            if setting:
                setting.value = value
            else:
                setting = Setting(key=key, value=value)
                self.db_session.add(setting)

            self.db_session.commit()
            return True
        except Exception as e:
            logger.error(f"خطأ في حفظ الإعداد {key}: {e}")
            self.db_session.rollback()
            return False

    def is_available(self) -> bool:
        """التحقق من توفر مكتبات Google APIs"""
        return GOOGLE_APIS_AVAILABLE

    def is_configured(self) -> bool:
        """التحقق من تكوين Google Drive"""
        if not self.is_available():
            return False

        credentials_json = self._get_setting_value("google_drive_credentials")
        return bool(credentials_json.strip())

    def is_enabled(self) -> bool:
        """التحقق من تفعيل Google Drive"""
        enabled = self._get_setting_value("google_drive_enabled", "false")
        return enabled.lower() == "true"

    def get_credentials_from_settings(self) -> Optional[Credentials]:
        """الحصول على بيانات الاعتماد من الإعدادات"""
        if not GOOGLE_APIS_AVAILABLE:
            return None

        try:
            credentials_json = self._get_setting_value("google_drive_credentials")
            if not credentials_json:
                return None

            creds_data = json.loads(credentials_json)
            credentials = Credentials.from_authorized_user_info(creds_data, self.scopes)

            # تحديث التوكن إذا انتهت صلاحيته
            if credentials.expired and credentials.refresh_token:
                try:
                    credentials.refresh(Request())
                    # حفظ التوكن المحدث
                    self._save_setting_value("google_drive_credentials", credentials.to_json())
                    logger.info("تم تحديث access token بنجاح")
                except Exception as refresh_error:
                    logger.error(f"فشل في تحديث access token: {refresh_error}")
                    # حذف بيانات الاعتماد المعطلة
                    self._save_setting_value("google_drive_credentials", "")
                    return None
            elif credentials.expired and not credentials.refresh_token:
                logger.warning("انتهت صلاحية التوكن ولا يوجد refresh token")
                # حذف بيانات الاعتماد المعطلة
                self._save_setting_value("google_drive_credentials", "")
                return None

            return credentials
        except Exception as e:
            logger.error(f"خطأ في جلب بيانات الاعتماد: {e}")
            # حذف بيانات الاعتماد المعطلة في حالة الخطأ
            try:
                self._save_setting_value("google_drive_credentials", "")
            except:
                pass
            return None

    def initialize_service(self) -> bool:
        """تهيئة خدمة Google Drive"""
        try:
            if not self.is_available():
                logger.error("مكتبات Google APIs غير متوفرة")
                return False

            self.credentials = self.get_credentials_from_settings()
            if not self.credentials:
                logger.warning("بيانات الاعتماد غير متوفرة")
                return False

            self.service = build('drive', 'v3', credentials=self.credentials)
            logger.info("تم تهيئة خدمة Google Drive بنجاح")
            return True
        except Exception as e:
            logger.error(f"خطأ في تهيئة خدمة Google Drive: {e}")
            return False

    def test_connection(self) -> Dict[str, Any]:
        """اختبار الاتصال مع Google Drive"""
        try:
            if not self.initialize_service():
                return {
                    "success": False,
                    "error": "فشل في تهيئة خدمة Google Drive"
                }

            if not self.service:
                return {
                    "success": False,
                    "error": "خدمة Google Drive غير متاحة"
                }

            # اختبار بسيط: جلب معلومات المستخدم
            about = self.service.about().get(fields="user").execute()
            user_email = about.get('user', {}).get('emailAddress', 'غير معروف')

            return {
                "success": True,
                "message": f"تم الاتصال بنجاح مع Google Drive",
                "user_email": user_email
            }
        except HttpError as e:
            logger.error(f"خطأ HTTP في اختبار الاتصال: {e}")
            if e.resp.status == 401:
                # خطأ في التفويض - حذف بيانات الاعتماد المعطلة
                try:
                    self._save_setting_value("google_drive_credentials", "")
                except:
                    pass
                return {
                    "success": False,
                    "error": "انتهت صلاحية التفويض. يرجى تسجيل الدخول مرة أخرى"
                }
            return {
                "success": False,
                "error": f"خطأ في الاتصال: {str(e)}"
            }
        except Exception as e:
            logger.error(f"خطأ في اختبار الاتصال: {e}")
            # في حالة خطأ غير متوقع، قد تكون بيانات الاعتماد معطلة
            if "invalid_grant" in str(e).lower() or "unauthorized" in str(e).lower():
                try:
                    self._save_setting_value("google_drive_credentials", "")
                except:
                    pass
                return {
                    "success": False,
                    "error": "انتهت صلاحية التفويض. يرجى تسجيل الدخول مرة أخرى"
                }
            return {
                "success": False,
                "error": f"فشل في الاتصال: {str(e)}"
            }

    def get_storage_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التخزين في Google Drive"""
        try:
            if not self.service:
                if not self.initialize_service():
                    return {
                        "success": False,
                        "error": "فشل في تهيئة خدمة Google Drive"
                    }

            if not self.service:
                return {
                    "success": False,
                    "error": "خدمة Google Drive غير متاحة"
                }

            # جلب معلومات التخزين والمستخدم
            about = self.service.about().get(
                fields="user,storageQuota"
            ).execute()

            user_info = about.get('user', {})
            storage_quota = about.get('storageQuota', {})

            # حساب الإحصائيات
            total_bytes = int(storage_quota.get('limit', 0))
            used_bytes = int(storage_quota.get('usage', 0))
            available_bytes = total_bytes - used_bytes if total_bytes > 0 else 0

            # جلب عدد الملفات الإجمالي
            files_result = self.service.files().list(
                q="trashed=false",
                fields="files(id)",
                pageSize=1000  # للحصول على عدد تقريبي
            ).execute()

            total_files = len(files_result.get('files', []))

            # إذا كان هناك المزيد من الملفات، نحصل على العدد التقريبي
            if len(files_result.get('files', [])) == 1000:
                # استخدام طريقة تقريبية للعدد الكبير
                page_token = files_result.get('nextPageToken')
                file_count = 1000
                pages_checked = 1

                # فحص صفحات إضافية (حد أقصى 10 صفحات لتجنب البطء)
                while page_token and pages_checked < 10:
                    next_result = self.service.files().list(
                        q="trashed=false",
                        fields="files(id)",
                        pageSize=1000,
                        pageToken=page_token
                    ).execute()

                    file_count += len(next_result.get('files', []))
                    page_token = next_result.get('nextPageToken')
                    pages_checked += 1

                total_files = file_count
                if page_token:  # لا يزال هناك المزيد
                    total_files = f"{file_count}+"

            return {
                "success": True,
                "user_email": user_info.get('emailAddress', 'غير معروف'),
                "display_name": user_info.get('displayName', 'غير معروف'),
                "total_storage": total_bytes,
                "used_storage": used_bytes,
                "available_storage": available_bytes,
                "total_files": total_files,
                "storage_percentage": round((used_bytes / total_bytes * 100), 2) if total_bytes > 0 else 0
            }

        except Exception as e:
            logger.error(f"خطأ في جلب إحصائيات التخزين: {e}")
            return {
                "success": False,
                "error": f"فشل في جلب إحصائيات التخزين: {str(e)}"
            }

    def get_or_create_backup_folder(self) -> Optional[str]:
        """الحصول على أو إنشاء مجلد النسخ الاحتياطية"""
        try:
            if not self.service:
                if not self.initialize_service():
                    return None

            if not self.service:
                return None

            folder_name = "SmartPOS Backups"

            # البحث عن المجلد الموجود
            query = f"name='{folder_name}' and mimeType='application/vnd.google-apps.folder' and trashed=false"
            results = self.service.files().list(q=query, fields="files(id, name)").execute()
            folders = results.get('files', [])

            if folders:
                folder_id = folders[0]['id']
                logger.info(f"تم العثور على مجلد النسخ الاحتياطية: {folder_id}")
            else:
                # إنشاء مجلد جديد
                folder_metadata = {
                    'name': folder_name,
                    'mimeType': 'application/vnd.google-apps.folder'
                }
                folder = self.service.files().create(body=folder_metadata, fields='id').execute()
                folder_id = folder.get('id')
                logger.info(f"تم إنشاء مجلد النسخ الاحتياطية: {folder_id}")

            # حفظ معرف المجلد في الإعدادات
            self._save_setting_value("google_drive_backup_folder_id", folder_id)
            return folder_id

        except Exception as e:
            logger.error(f"خطأ في إنشاء مجلد النسخ الاحتياطية: {e}")
            return None

    def upload_backup_file(self, file_path: str, backup_name: Optional[str] = None) -> Dict[str, Any]:
        """رفع ملف النسخة الاحتياطية إلى Google Drive"""
        try:
            if not os.path.exists(file_path):
                return {
                    "success": False,
                    "error": "ملف النسخة الاحتياطية غير موجود"
                }

            if not self.service:
                if not self.initialize_service():
                    return {
                        "success": False,
                        "error": "فشل في تهيئة خدمة Google Drive"
                    }

            if not self.service:
                return {
                    "success": False,
                    "error": "خدمة Google Drive غير متاحة"
                }

            # الحصول على مجلد النسخ الاحتياطية
            folder_id = self.get_or_create_backup_folder()
            if not folder_id:
                return {
                    "success": False,
                    "error": "فشل في إنشاء مجلد النسخ الاحتياطية"
                }

            # تحديد اسم الملف
            if not backup_name:
                backup_name = os.path.basename(file_path)

            # إعداد بيانات الملف
            file_metadata = {
                'name': backup_name,
                'parents': [folder_id]
            }

            # رفع الملف
            media = MediaFileUpload(file_path, resumable=True)
            file = self.service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id,name,size,createdTime'
            ).execute()

            logger.info(f"تم رفع النسخة الاحتياطية إلى Google Drive: {file.get('name')}")

            return {
                "success": True,
                "file_id": file.get('id'),
                "file_name": file.get('name'),
                "file_size": file.get('size'),
                "created_time": file.get('createdTime'),
                "message": "تم رفع النسخة الاحتياطية إلى Google Drive بنجاح"
            }

        except Exception as e:
            logger.error(f"خطأ في رفع النسخة الاحتياطية: {e}")
            return {
                "success": False,
                "error": f"فشل في رفع النسخة الاحتياطية: {str(e)}"
            }

    def list_backup_files(self, limit: int = 50) -> Dict[str, Any]:
        """جلب قائمة النسخ الاحتياطية من Google Drive"""
        try:
            if not self.service:
                if not self.initialize_service():
                    return {
                        "success": False,
                        "error": "فشل في تهيئة خدمة Google Drive"
                    }

            if not self.service:
                return {
                    "success": False,
                    "error": "خدمة Google Drive غير متاحة"
                }

            # الحصول على أو إنشاء مجلد النسخ الاحتياطية
            folder_id = self.get_or_create_backup_folder()
            if not folder_id:
                return {
                    "success": False,
                    "error": "فشل في إنشاء مجلد النسخ الاحتياطية"
                }

            # جلب الملفات من المجلد
            query = f"'{folder_id}' in parents and trashed=false"
            results = self.service.files().list(
                q=query,
                pageSize=limit,
                orderBy='createdTime desc',
                fields="files(id,name,size,createdTime,modifiedTime)"
            ).execute()

            files = results.get('files', [])

            # إذا كان المجلد فارغاً، إرجاع رسالة واضحة
            if len(files) == 0:
                return {
                    "success": True,
                    "files": [],
                    "count": 0,
                    "message": "لا توجد نسخ احتياطية في Google Drive بعد. سيتم إنشاء النسخ الاحتياطية تلقائياً حسب الجدولة المحددة."
                }

            return {
                "success": True,
                "files": files,
                "count": len(files)
            }

        except Exception as e:
            logger.error(f"خطأ في جلب قائمة النسخ الاحتياطية: {e}")
            return {
                "success": False,
                "error": f"فشل في جلب قائمة النسخ الاحتياطية: {str(e)}"
            }

    def download_backup_file(self, file_id: str) -> Dict[str, Any]:
        """تحميل ملف نسخة احتياطية من Google Drive"""
        try:
            if not self.service:
                if not self.initialize_service():
                    return {
                        "success": False,
                        "error": "فشل في تهيئة خدمة Google Drive"
                    }

            if not self.service:
                return {
                    "success": False,
                    "error": "خدمة Google Drive غير متاحة"
                }

            # التحقق من وجود الملف أولاً
            try:
                file_info = self.service.files().get(fileId=file_id, fields='id,name,parents,size').execute()
                file_name = file_info.get('name', 'ملف غير معروف')

                # التحقق من أن الملف في مجلد النسخ الاحتياطية
                folder_id = self.get_or_create_backup_folder()
                if folder_id and folder_id not in file_info.get('parents', []):
                    return {
                        "success": False,
                        "error": "الملف ليس في مجلد النسخ الاحتياطية"
                    }

            except Exception as e:
                logger.error(f"خطأ في التحقق من الملف {file_id}: {e}")
                return {
                    "success": False,
                    "error": "الملف غير موجود أو لا يمكن الوصول إليه"
                }

            # تحميل محتوى الملف
            try:
                import io
                request = self.service.files().get_media(fileId=file_id)
                file_content = io.BytesIO()

                from googleapiclient.http import MediaIoBaseDownload
                downloader = MediaIoBaseDownload(file_content, request)

                done = False
                while done is False:
                    status, done = downloader.next_chunk()

                file_content.seek(0)
                content_bytes = file_content.read()

                logger.info(f"تم تحميل النسخة الاحتياطية: {file_name}")

                return {
                    "success": True,
                    "file_content": content_bytes,
                    "file_name": file_name,
                    "file_size": len(content_bytes),
                    "message": f"تم تحميل الملف '{file_name}' بنجاح من Google Drive"
                }

            except Exception as e:
                logger.error(f"خطأ في تحميل الملف {file_name}: {e}")
                return {
                    "success": False,
                    "error": f"فشل في تحميل الملف: {str(e)}"
                }

        except Exception as e:
            logger.error(f"خطأ في تحميل الملف {file_id}: {e}")
            return {
                "success": False,
                "error": f"فشل في تحميل الملف: {str(e)}"
            }

    def delete_backup_file(self, file_id: str) -> Dict[str, Any]:
        """حذف ملف نسخة احتياطية محدد من Google Drive"""
        try:
            if not self.service:
                if not self.initialize_service():
                    return {
                        "success": False,
                        "error": "فشل في تهيئة خدمة Google Drive"
                    }

            if not self.service:
                return {
                    "success": False,
                    "error": "خدمة Google Drive غير متاحة"
                }

            # التحقق من وجود الملف أولاً
            try:
                file_info = self.service.files().get(fileId=file_id, fields='id,name,parents').execute()
                file_name = file_info.get('name', 'ملف غير معروف')

                # التحقق من أن الملف في مجلد النسخ الاحتياطية
                folder_id = self.get_or_create_backup_folder()
                if folder_id and folder_id not in file_info.get('parents', []):
                    return {
                        "success": False,
                        "error": "الملف ليس في مجلد النسخ الاحتياطية"
                    }

            except Exception as e:
                logger.error(f"خطأ في التحقق من الملف {file_id}: {e}")
                return {
                    "success": False,
                    "error": "الملف غير موجود أو لا يمكن الوصول إليه"
                }

            # حذف الملف
            try:
                self.service.files().delete(fileId=file_id).execute()
                logger.info(f"تم حذف النسخة الاحتياطية: {file_name}")

                return {
                    "success": True,
                    "message": f"تم حذف الملف '{file_name}' بنجاح من Google Drive"
                }

            except Exception as e:
                logger.error(f"خطأ في حذف الملف {file_name}: {e}")
                return {
                    "success": False,
                    "error": f"فشل في حذف الملف: {str(e)}"
                }

        except Exception as e:
            logger.error(f"خطأ في حذف الملف {file_id}: {e}")
            return {
                "success": False,
                "error": f"فشل في حذف الملف: {str(e)}"
            }

    def delete_old_backups(self, keep_count: int = 10) -> Dict[str, Any]:
        """حذف النسخ الاحتياطية القديمة من Google Drive"""
        try:
            # جلب قائمة النسخ الاحتياطية
            result = self.list_backup_files(limit=100)
            if not result["success"]:
                return result

            files = result["files"]

            # إذا لم توجد ملفات، إرجاع رسالة واضحة
            if len(files) == 0:
                return {
                    "success": True,
                    "message": "لا توجد نسخ احتياطية في Google Drive للتنظيف. سيتم إنشاء النسخ الاحتياطية تلقائياً حسب الجدولة المحددة."
                }

            if len(files) <= keep_count:
                return {
                    "success": True,
                    "message": f"لا توجد نسخ احتياطية قديمة للحذف. العدد الحالي: {len(files)}"
                }

            # التحقق من توفر الخدمة
            if not self.service:
                return {
                    "success": False,
                    "error": "خدمة Google Drive غير متاحة"
                }

            # حذف النسخ الزائدة
            files_to_delete = files[keep_count:]
            deleted_count = 0

            for file in files_to_delete:
                try:
                    self.service.files().delete(fileId=file['id']).execute()
                    deleted_count += 1
                    logger.info(f"تم حذف النسخة الاحتياطية: {file['name']}")
                except Exception as e:
                    logger.error(f"خطأ في حذف الملف {file['name']}: {e}")

            return {
                "success": True,
                "message": f"تم حذف {deleted_count} نسخة احتياطية قديمة من Google Drive"
            }

        except Exception as e:
            logger.error(f"خطأ في حذف النسخ الاحتياطية القديمة: {e}")
            return {
                "success": False,
                "error": f"فشل في حذف النسخ الاحتياطية القديمة: {str(e)}"
            }
