"""
خدمة إدارة العلاقات بين الفروع والمستودعات - Branch Warehouse Service
تطبق مبادئ البرمجة الكائنية (OOP) لإدارة العلاقات Many-to-Many
"""

import logging
from typing import Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import text

from models.branch import Branch
from models.warehouse import Warehouse
from utils.datetime_utils import get_current_time_with_settings

logger = logging.getLogger(__name__)


def get_branch_warehouse_service(db_session: Session) -> 'BranchWarehouseService':
    """إنشاء مثيل جديد من خدمة العلاقات بين الفروع والمستودعات"""
    return BranchWarehouseService(db_session)


class BranchWarehouseService:
    """
    خدمة إدارة العلاقات بين الفروع والمستودعات - تطبق مبادئ OOP

    تتعامل مع جميع عمليات العلاقات:
    - ربط فرع بمستودع
    - إلغاء ربط فرع من مستودع
    - تعيين مستودع أساسي للفرع
    - تحديد أولوية المستودعات للفرع
    - استعلام العلاقات
    """

    def __init__(self, db_session: Session):
        self.db_session = db_session

    # تم نقل get_instance إلى دالة خارجية لتجنب مشاكل Type Checking
    
    def link_branch_to_warehouse(self, branch_id: int, warehouse_id: int, 
                                is_primary: bool = False, priority: int = 1) -> Dict[str, Any]:
        """
        ربط فرع بمستودع
        
        Args:
            branch_id: معرف الفرع
            warehouse_id: معرف المستودع
            is_primary: هل هذا المستودع الأساسي للفرع
            priority: أولوية المستودع (1 = أعلى أولوية)
            
        Returns:
            نتيجة العملية
        """
        try:
            logger.info(f"🔗 ربط الفرع {branch_id} بالمستودع {warehouse_id}")
            
            # التحقق من وجود الفرع
            branch = self.db_session.query(Branch).filter(Branch.id == branch_id).first()
            if not branch:
                return {
                    'success': False,
                    'error': 'الفرع غير موجود'
                }
            
            # التحقق من وجود المستودع
            warehouse = self.db_session.query(Warehouse).filter(Warehouse.id == warehouse_id).first()
            if not warehouse:
                return {
                    'success': False,
                    'error': 'المستودع غير موجود'
                }
            
            # التحقق من عدم وجود الربط مسبقاً
            existing_link = self.db_session.execute(
                text("SELECT 1 FROM branch_warehouses WHERE branch_id = :branch_id AND warehouse_id = :warehouse_id"),
                {"branch_id": branch_id, "warehouse_id": warehouse_id}
            ).first()
            
            if existing_link:
                return {
                    'success': False,
                    'error': 'الفرع مرتبط بالمستودع مسبقاً'
                }
            
            # إذا كان المستودع سيصبح أساسياً، إلغاء الأساسية من المستودعات الأخرى للفرع
            if is_primary:
                self.db_session.execute(
                    text("UPDATE branch_warehouses SET is_primary = false WHERE branch_id = :branch_id"),
                    {"branch_id": branch_id}
                )
            
            # إنشاء الربط
            self.db_session.execute(
                text("""
                    INSERT INTO branch_warehouses (branch_id, warehouse_id, is_primary, priority, created_at)
                    VALUES (:branch_id, :warehouse_id, :is_primary, :priority, :created_at)
                """),
                {
                    "branch_id": branch_id,
                    "warehouse_id": warehouse_id,
                    "is_primary": is_primary,
                    "priority": priority,
                    "created_at": get_current_time_with_settings(self.db_session)
                }
            )
            
            self.db_session.commit()
            
            logger.info(f"✅ تم ربط الفرع {branch.name} بالمستودع {warehouse.name}")
            
            return {
                'success': True,
                'message': f'تم ربط الفرع {branch.name} بالمستودع {warehouse.name}',
                'link': {
                    'branch_id': branch_id,
                    'warehouse_id': warehouse_id,
                    'is_primary': is_primary,
                    'priority': priority
                }
            }
            
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"❌ خطأ في ربط الفرع بالمستودع: {e}")
            return {
                'success': False,
                'error': f'خطأ في ربط الفرع بالمستودع: {str(e)}'
            }
    
    def unlink_branch_from_warehouse(self, branch_id: int, warehouse_id: int) -> Dict[str, Any]:
        """
        إلغاء ربط فرع من مستودع
        
        Args:
            branch_id: معرف الفرع
            warehouse_id: معرف المستودع
            
        Returns:
            نتيجة العملية
        """
        try:
            logger.info(f"🔓 إلغاء ربط الفرع {branch_id} من المستودع {warehouse_id}")
            
            # التحقق من وجود الربط
            existing_link = self.db_session.execute(
                text("SELECT 1 FROM branch_warehouses WHERE branch_id = :branch_id AND warehouse_id = :warehouse_id"),
                {"branch_id": branch_id, "warehouse_id": warehouse_id}
            ).first()
            
            if not existing_link:
                return {
                    'success': False,
                    'error': 'الربط غير موجود'
                }
            
            # حذف الربط
            self.db_session.execute(
                text("DELETE FROM branch_warehouses WHERE branch_id = :branch_id AND warehouse_id = :warehouse_id"),
                {"branch_id": branch_id, "warehouse_id": warehouse_id}
            )
            
            self.db_session.commit()
            
            # الحصول على أسماء الفرع والمستودع للرسالة
            branch = self.db_session.query(Branch).filter(Branch.id == branch_id).first()
            warehouse = self.db_session.query(Warehouse).filter(Warehouse.id == warehouse_id).first()
            
            branch_name = branch.name if branch else f"الفرع {branch_id}"
            warehouse_name = warehouse.name if warehouse else f"المستودع {warehouse_id}"
            
            logger.info(f"✅ تم إلغاء ربط {branch_name} من {warehouse_name}")
            
            return {
                'success': True,
                'message': f'تم إلغاء ربط {branch_name} من {warehouse_name}'
            }
            
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"❌ خطأ في إلغاء ربط الفرع من المستودع: {e}")
            return {
                'success': False,
                'error': f'خطأ في إلغاء الربط: {str(e)}'
            }
    
    def set_primary_warehouse_for_branch(self, branch_id: int, warehouse_id: int) -> Dict[str, Any]:
        """
        تعيين مستودع أساسي للفرع
        
        Args:
            branch_id: معرف الفرع
            warehouse_id: معرف المستودع
            
        Returns:
            نتيجة العملية
        """
        try:
            logger.info(f"⭐ تعيين المستودع {warehouse_id} كأساسي للفرع {branch_id}")
            
            # التحقق من وجود الربط
            existing_link = self.db_session.execute(
                text("SELECT 1 FROM branch_warehouses WHERE branch_id = :branch_id AND warehouse_id = :warehouse_id"),
                {"branch_id": branch_id, "warehouse_id": warehouse_id}
            ).first()
            
            if not existing_link:
                return {
                    'success': False,
                    'error': 'الفرع غير مرتبط بهذا المستودع'
                }
            
            # إلغاء الأساسية من جميع المستودعات للفرع
            self.db_session.execute(
                text("UPDATE branch_warehouses SET is_primary = false WHERE branch_id = :branch_id"),
                {"branch_id": branch_id}
            )
            
            # تعيين المستودع الجديد كأساسي
            self.db_session.execute(
                text("UPDATE branch_warehouses SET is_primary = true WHERE branch_id = :branch_id AND warehouse_id = :warehouse_id"),
                {"branch_id": branch_id, "warehouse_id": warehouse_id}
            )
            
            self.db_session.commit()
            
            # الحصول على أسماء الفرع والمستودع للرسالة
            branch = self.db_session.query(Branch).filter(Branch.id == branch_id).first()
            warehouse = self.db_session.query(Warehouse).filter(Warehouse.id == warehouse_id).first()
            
            branch_name = branch.name if branch else f"الفرع {branch_id}"
            warehouse_name = warehouse.name if warehouse else f"المستودع {warehouse_id}"
            
            logger.info(f"✅ تم تعيين {warehouse_name} كمستودع أساسي لـ {branch_name}")
            
            return {
                'success': True,
                'message': f'تم تعيين {warehouse_name} كمستودع أساسي لـ {branch_name}'
            }
            
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"❌ خطأ في تعيين المستودع الأساسي: {e}")
            return {
                'success': False,
                'error': f'خطأ في تعيين المستودع الأساسي: {str(e)}'
            }

    def update_warehouse_priority_for_branch(self, branch_id: int, warehouse_id: int, priority: int) -> Dict[str, Any]:
        """
        تحديث أولوية مستودع للفرع

        Args:
            branch_id: معرف الفرع
            warehouse_id: معرف المستودع
            priority: الأولوية الجديدة

        Returns:
            نتيجة العملية
        """
        try:
            logger.info(f"🔄 تحديث أولوية المستودع {warehouse_id} للفرع {branch_id} إلى {priority}")

            # التحقق من وجود الربط
            existing_link = self.db_session.execute(
                text("SELECT 1 FROM branch_warehouses WHERE branch_id = :branch_id AND warehouse_id = :warehouse_id"),
                {"branch_id": branch_id, "warehouse_id": warehouse_id}
            ).first()

            if not existing_link:
                return {
                    'success': False,
                    'error': 'الفرع غير مرتبط بهذا المستودع'
                }

            # تحديث الأولوية
            self.db_session.execute(
                text("UPDATE branch_warehouses SET priority = :priority WHERE branch_id = :branch_id AND warehouse_id = :warehouse_id"),
                {"priority": priority, "branch_id": branch_id, "warehouse_id": warehouse_id}
            )

            self.db_session.commit()

            logger.info(f"✅ تم تحديث أولوية المستودع بنجاح")

            return {
                'success': True,
                'message': 'تم تحديث أولوية المستودع بنجاح'
            }

        except Exception as e:
            self.db_session.rollback()
            logger.error(f"❌ خطأ في تحديث أولوية المستودع: {e}")
            return {
                'success': False,
                'error': f'خطأ في تحديث الأولوية: {str(e)}'
            }

    def get_warehouses_for_branch(self, branch_id: int, include_inactive: bool = False) -> Dict[str, Any]:
        """
        الحصول على المستودعات المرتبطة بالفرع

        Args:
            branch_id: معرف الفرع
            include_inactive: هل تشمل المستودعات غير النشطة

        Returns:
            قائمة المستودعات مع تفاصيل الربط
        """
        try:
            # استعلام للحصول على المستودعات مع تفاصيل الربط
            query = text("""
                SELECT
                    w.id, w.name, w.code, w.address, w.is_main, w.is_active,
                    w.capacity_limit, w.current_capacity,
                    bw.is_primary, bw.priority, bw.created_at as link_created_at
                FROM warehouses w
                INNER JOIN branch_warehouses bw ON w.id = bw.warehouse_id
                WHERE bw.branch_id = :branch_id
                {}
                ORDER BY bw.is_primary DESC, bw.priority ASC, w.name
            """.format("AND w.is_active = true" if not include_inactive else ""))

            result = self.db_session.execute(query, {"branch_id": branch_id}).fetchall()

            warehouses_data = []
            for row in result:
                warehouses_data.append({
                    'id': row.id,
                    'name': row.name,
                    'code': row.code,
                    'address': row.address,
                    'is_main': row.is_main,
                    'is_active': row.is_active,
                    'capacity_limit': float(row.capacity_limit) if row.capacity_limit is not None else None,
                    'current_capacity': float(row.current_capacity) if row.current_capacity is not None else 0,
                    'capacity_percentage': (float(row.current_capacity) / float(row.capacity_limit) * 100) if row.capacity_limit is not None and float(row.capacity_limit) > 0 else 0,
                    'is_primary': row.is_primary,
                    'priority': row.priority,
                    'link_created_at': row.link_created_at.isoformat() if row.link_created_at is not None else None
                })

            return {
                'success': True,
                'warehouses': warehouses_data,
                'total_count': len(warehouses_data)
            }

        except Exception as e:
            logger.error(f"❌ خطأ في جلب مستودعات الفرع: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب المستودعات: {str(e)}'
            }

    def get_branches_for_warehouse(self, warehouse_id: int, include_inactive: bool = False) -> Dict[str, Any]:
        """
        الحصول على الفروع المرتبطة بالمستودع

        Args:
            warehouse_id: معرف المستودع
            include_inactive: هل تشمل الفروع غير النشطة

        Returns:
            قائمة الفروع مع تفاصيل الربط
        """
        try:
            # استعلام للحصول على الفروع مع تفاصيل الربط
            query = text("""
                SELECT
                    b.id, b.name, b.code, b.address, b.manager_name, b.is_main, b.is_active,
                    b.city, b.region,
                    bw.is_primary, bw.priority, bw.created_at as link_created_at
                FROM branches b
                INNER JOIN branch_warehouses bw ON b.id = bw.branch_id
                WHERE bw.warehouse_id = :warehouse_id
                {}
                ORDER BY bw.is_primary DESC, bw.priority ASC, b.name
            """.format("AND b.is_active = true" if not include_inactive else ""))

            result = self.db_session.execute(query, {"warehouse_id": warehouse_id}).fetchall()

            branches_data = []
            for row in result:
                branches_data.append({
                    'id': row.id,
                    'name': row.name,
                    'code': row.code,
                    'address': row.address,
                    'manager_name': row.manager_name,
                    'is_main': row.is_main,
                    'is_active': row.is_active,
                    'city': row.city,
                    'region': row.region,
                    'is_primary_warehouse': row.is_primary,
                    'warehouse_priority': row.priority,
                    'link_created_at': row.link_created_at.isoformat() if row.link_created_at is not None else None
                })

            return {
                'success': True,
                'branches': branches_data,
                'total_count': len(branches_data)
            }

        except Exception as e:
            logger.error(f"❌ خطأ في جلب فروع المستودع: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب الفروع: {str(e)}'
            }

    def get_primary_warehouse_for_branch(self, branch_id: int) -> Dict[str, Any]:
        """
        الحصول على المستودع الأساسي للفرع

        Args:
            branch_id: معرف الفرع

        Returns:
            بيانات المستودع الأساسي
        """
        try:
            query = text("""
                SELECT
                    w.id, w.name, w.code, w.address, w.is_main, w.is_active,
                    w.capacity_limit, w.current_capacity,
                    bw.priority, bw.created_at as link_created_at
                FROM warehouses w
                INNER JOIN branch_warehouses bw ON w.id = bw.warehouse_id
                WHERE bw.branch_id = :branch_id AND bw.is_primary = true
                LIMIT 1
            """)

            result = self.db_session.execute(query, {"branch_id": branch_id}).first()

            if not result:
                return {
                    'success': False,
                    'error': 'لا يوجد مستودع أساسي محدد للفرع'
                }

            return {
                'success': True,
                'warehouse': {
                    'id': result.id,
                    'name': result.name,
                    'code': result.code,
                    'address': result.address,
                    'is_main': result.is_main,
                    'is_active': result.is_active,
                    'capacity_limit': float(result.capacity_limit) if result.capacity_limit is not None else None,
                    'current_capacity': float(result.current_capacity) if result.current_capacity is not None else 0,
                    'capacity_percentage': (float(result.current_capacity) / float(result.capacity_limit) * 100) if result.capacity_limit is not None and float(result.capacity_limit) > 0 else 0,
                    'priority': result.priority,
                    'link_created_at': result.link_created_at.isoformat() if result.link_created_at is not None else None
                }
            }

        except Exception as e:
            logger.error(f"❌ خطأ في جلب المستودع الأساسي: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب المستودع الأساسي: {str(e)}'
            }

    def get_available_warehouses_for_branch(self, branch_id: int) -> Dict[str, Any]:
        """
        الحصول على المستودعات المتاحة للربط بالفرع (غير مرتبطة بعد)

        Args:
            branch_id: معرف الفرع

        Returns:
            قائمة المستودعات المتاحة
        """
        try:
            query = text("""
                SELECT
                    w.id, w.name, w.code, w.address, w.is_main, w.is_active,
                    w.capacity_limit, w.current_capacity
                FROM warehouses w
                WHERE w.is_active = true
                AND w.id NOT IN (
                    SELECT warehouse_id
                    FROM branch_warehouses
                    WHERE branch_id = :branch_id
                )
                ORDER BY w.is_main DESC, w.name
            """)

            result = self.db_session.execute(query, {"branch_id": branch_id}).fetchall()

            warehouses_data = []
            for row in result:
                warehouses_data.append({
                    'id': row.id,
                    'name': row.name,
                    'code': row.code,
                    'address': row.address,
                    'is_main': row.is_main,
                    'is_active': row.is_active,
                    'capacity_limit': float(row.capacity_limit) if row.capacity_limit is not None else None,
                    'current_capacity': float(row.current_capacity) if row.current_capacity is not None else 0,
                    'capacity_percentage': (float(row.current_capacity) / float(row.capacity_limit) * 100) if row.capacity_limit is not None and float(row.capacity_limit) > 0 else 0
                })

            return {
                'success': True,
                'warehouses': warehouses_data,
                'total_count': len(warehouses_data)
            }

        except Exception as e:
            logger.error(f"❌ خطأ في جلب المستودعات المتاحة: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب المستودعات المتاحة: {str(e)}'
            }

    def get_available_branches_for_warehouse(self, warehouse_id: int) -> Dict[str, Any]:
        """
        الحصول على الفروع المتاحة للربط بالمستودع (غير مرتبطة بعد)

        Args:
            warehouse_id: معرف المستودع

        Returns:
            قائمة الفروع المتاحة
        """
        try:
            query = text("""
                SELECT
                    b.id, b.name, b.code, b.address, b.manager_name, b.is_main, b.is_active,
                    b.city, b.region
                FROM branches b
                WHERE b.is_active = true
                AND b.id NOT IN (
                    SELECT branch_id
                    FROM branch_warehouses
                    WHERE warehouse_id = :warehouse_id
                )
                ORDER BY b.is_main DESC, b.name
            """)

            result = self.db_session.execute(query, {"warehouse_id": warehouse_id}).fetchall()

            branches_data = []
            for row in result:
                branches_data.append({
                    'id': row.id,
                    'name': row.name,
                    'code': row.code,
                    'address': row.address,
                    'manager_name': row.manager_name,
                    'is_main': row.is_main,
                    'is_active': row.is_active,
                    'city': row.city,
                    'region': row.region
                })

            return {
                'success': True,
                'branches': branches_data,
                'total_count': len(branches_data)
            }

        except Exception as e:
            logger.error(f"❌ خطأ في جلب الفروع المتاحة: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب الفروع المتاحة: {str(e)}'
            }
