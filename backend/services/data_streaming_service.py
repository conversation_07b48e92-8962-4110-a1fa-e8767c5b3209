"""
خدمة تدفق البيانات الكبيرة المتقدمة
تدعم التدفق المستمر، الضغط، التخزين المؤقت، ومراقبة الأداء
"""

import asyncio
import gzip
import json
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, Optional, Any
from pathlib import Path
import redis
from sqlalchemy.orm import Session
from sqlalchemy import select, func, and_

from models.sale import Sale, SaleItem
from models.product import Product
from models.customer import Customer, CustomerDebt
from schemas.data_streaming import (
    StreamingProgress, StreamingConfiguration, StreamingMetrics
)

logger = logging.getLogger(__name__)

class DataStreamingService:
    """
    خدمة تدفق البيانات الكبيرة المتقدمة
    """
    
    def __init__(self, db: Session, redis_client: Optional[redis.Redis] = None):
        self.db = db
        self.redis_client = redis_client
        self.config = StreamingConfiguration()
        self.active_streams: Dict[str, Dict] = {}
        self.export_cache_dir = Path("exports_cache")
        self.export_cache_dir.mkdir(exist_ok=True)
    
    async def create_streaming_task(
        self,
        task_type: str,
        parameters: Dict[str, Any],
        user_id: int
    ) -> str:
        """
        إنشاء مهمة تدفق جديدة
        """
        task_id = str(uuid.uuid4())
        
        task_info = {
            "task_id": task_id,
            "task_type": task_type,
            "parameters": parameters,
            "user_id": user_id,
            "status": "pending",
            "created_at": datetime.now(),
            "progress": 0.0,
            "total_records": 0,
            "processed_records": 0
        }
        
        self.active_streams[task_id] = task_info
        
        # حفظ في Redis إذا كان متاحاً
        if self.redis_client:
            await self._save_task_to_redis(task_id, task_info)
        
        # بدء المهمة في الخلفية
        asyncio.create_task(self._execute_streaming_task(task_id))
        
        return task_id
    
    async def get_task_progress(self, task_id: str) -> Optional[StreamingProgress]:
        """
        الحصول على تقدم المهمة
        """
        task_info = self.active_streams.get(task_id)
        
        if not task_info and self.redis_client:
            task_info = await self._get_task_from_redis(task_id)
        
        if not task_info:
            return None
        
        return StreamingProgress(
            task_id=task_id,
            status=task_info["status"],
            progress_percentage=task_info["progress"],
            current_record=task_info["processed_records"],
            total_records=task_info["total_records"],
            estimated_time_remaining=task_info.get("estimated_time_remaining"),
            error_message=task_info.get("error_message")
        )
    
    async def _execute_streaming_task(self, task_id: str):
        """
        تنفيذ مهمة التدفق
        """
        try:
            task_info = self.active_streams[task_id]
            task_info["status"] = "running"
            task_info["started_at"] = datetime.now()
            
            task_type = task_info["task_type"]
            parameters = task_info["parameters"]
            
            if task_type == "sales_export":
                await self._export_sales_data(task_id, parameters)
            elif task_type == "products_export":
                await self._export_products_data(task_id, parameters)
            elif task_type == "customers_export":
                await self._export_customers_data(task_id, parameters)
            elif task_type == "bulk_export":
                await self._export_bulk_data(task_id, parameters)
            else:
                raise ValueError(f"نوع مهمة غير مدعوم: {task_type}")
            
            task_info["status"] = "completed"
            task_info["completed_at"] = datetime.now()
            
        except Exception as e:
            logger.error(f"خطأ في تنفيذ المهمة {task_id}: {e}")
            task_info["status"] = "failed"
            task_info["error_message"] = str(e)
            task_info["failed_at"] = datetime.now()
        
        finally:
            # تحديث في Redis
            if self.redis_client:
                await self._save_task_to_redis(task_id, task_info)
    
    async def _export_sales_data(self, task_id: str, parameters: Dict[str, Any]):
        """
        تصدير بيانات المبيعات
        """
        task_info = self.active_streams[task_id]
        
        # بناء الاستعلام
        query = select(Sale).order_by(Sale.created_at.desc())
        
        # تطبيق الفلاتر
        conditions = []
        if parameters.get("start_date"):
            conditions.append(Sale.created_at >= parameters["start_date"])
        if parameters.get("end_date"):
            conditions.append(Sale.created_at <= parameters["end_date"])
        if parameters.get("user_id"):
            conditions.append(Sale.user_id == parameters["user_id"])
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # حساب العدد الإجمالي
        count_query = select(func.count(Sale.id))
        if conditions:
            count_query = count_query.where(and_(*conditions))
        
        total_count = self.db.execute(count_query).scalar() or 0
        task_info["total_records"] = total_count
        
        # إنشاء ملف التصدير
        export_file = self.export_cache_dir / f"{task_id}_sales.json"
        
        chunk_size = parameters.get("chunk_size", self.config.max_chunk_size)
        processed = 0
        
        with open(export_file, 'w', encoding='utf-8') as f:
            f.write('{"sales": [')
            
            first_record = True
            offset = 0
            
            while offset < total_count:
                batch_query = query.offset(offset).limit(chunk_size)
                sales = self.db.execute(batch_query).scalars().all()
                
                if not sales:
                    break
                
                for sale in sales:
                    if not first_record:
                        f.write(',')
                    
                    sale_data = {
                        "id": sale.id,
                        "user_id": sale.user_id,
                        "customer_id": sale.customer_id,
                        "total_amount": float(sale.total_amount or 0),
                        "payment_method": sale.payment_method,
                        "tax_amount": float(sale.tax_amount or 0),
                        "discount_amount": float(sale.discount_amount or 0),
                        "customer_name": sale.customer_name,
                        "notes": sale.notes,
                        "amount_paid": float(sale.amount_paid or 0),
                        "payment_status": sale.payment_status,
                        "created_at": sale.created_at.isoformat() if sale.created_at else None
                    }
                    
                    # إضافة عناصر البيع إذا طُلب
                    if parameters.get("include_items", True):
                        items = []
                        for item in sale.items:
                            items.append({
                                "id": item.id,
                                "product_id": item.product_id,
                                "quantity": item.quantity,
                                "unit_price": float(item.unit_price or 0),
                                "subtotal": float(item.subtotal or 0),
                                "discount": float(item.discount or 0)
                            })
                        sale_data["items"] = items
                    
                    f.write(json.dumps(sale_data, ensure_ascii=False))
                    first_record = False
                    processed += 1
                    
                    # تحديث التقدم
                    task_info["processed_records"] = processed
                    task_info["progress"] = (processed / total_count) * 100
                
                offset += chunk_size
                
                # إيقاف مؤقت لتجنب إرهاق النظام
                await asyncio.sleep(0.01)
            
            f.write(']}')
        
        # ضغط الملف إذا طُلب
        if parameters.get("compress", False):
            compressed_file = export_file.with_suffix('.json.gz')
            with open(export_file, 'rb') as f_in:
                with gzip.open(compressed_file, 'wb') as f_out:
                    f_out.writelines(f_in)
            
            # حذف الملف غير المضغوط
            export_file.unlink()
            task_info["export_file"] = str(compressed_file)
        else:
            task_info["export_file"] = str(export_file)
        
        # حساب حجم الملف
        final_file = Path(task_info["export_file"])
        task_info["file_size"] = final_file.stat().st_size
    
    async def _export_products_data(self, task_id: str, parameters: Dict[str, Any]):
        """
        تصدير بيانات المنتجات مع التحليلات
        """
        task_info = self.active_streams[task_id]
        
        # بناء استعلام محسن للمنتجات مع التحليلات
        if parameters.get("include_analytics", True):
            query = select(
                Product.id,
                Product.name,
                Product.barcode,
                Product.category,
                Product.price,
                Product.cost_price,
                Product.quantity,
                Product.min_quantity,
                Product.unit,
                Product.is_active,
                Product.created_at,
                func.coalesce(func.sum(SaleItem.quantity), 0).label('total_sold'),
                func.coalesce(func.sum(SaleItem.subtotal), 0).label('total_revenue'),
                func.count(Sale.id).label('sales_count')
            ).select_from(Product)\
            .outerjoin(SaleItem, Product.id == SaleItem.product_id)\
            .outerjoin(Sale, SaleItem.sale_id == Sale.id)\
            .group_by(Product.id)
        else:
            query = select(Product)
        
        # تطبيق الفلاتر
        conditions = []
        if parameters.get("category"):
            conditions.append(Product.category == parameters["category"])
        if parameters.get("low_stock"):
            conditions.append(Product.quantity <= Product.min_quantity)
        if parameters.get("is_active") is not None:
            conditions.append(Product.is_active == parameters["is_active"])
        
        if conditions:
            query = query.where(and_(*conditions))
        
        query = query.order_by(Product.created_at.desc())
        
        # حساب العدد الإجمالي
        count_query = select(func.count(func.distinct(Product.id)))
        if conditions:
            count_query = count_query.where(and_(*conditions))
        
        total_count = self.db.execute(count_query).scalar() or 0
        task_info["total_records"] = total_count
        
        # إنشاء ملف التصدير
        export_file = self.export_cache_dir / f"{task_id}_products.json"
        
        chunk_size = parameters.get("chunk_size", self.config.max_chunk_size)
        processed = 0
        
        with open(export_file, 'w', encoding='utf-8') as f:
            f.write('{"products": [')
            
            first_record = True
            offset = 0
            
            while offset < total_count:
                batch_query = query.offset(offset).limit(chunk_size)
                products = self.db.execute(batch_query).all()
                
                if not products:
                    break
                
                for product in products:
                    if not first_record:
                        f.write(',')
                    
                    if parameters.get("include_analytics", True):
                        # حساب هامش الربح
                        profit_margin = 0
                        if product.total_revenue > 0:
                            total_profit = product.total_revenue - (product.cost_price * product.total_sold)
                            profit_margin = (total_profit / product.total_revenue) * 100
                        
                        # تحديد حالة المخزون
                        stock_status = "healthy"
                        if product.quantity == 0:
                            stock_status = "out_of_stock"
                        elif product.quantity <= product.min_quantity:
                            stock_status = "low_stock"
                        elif product.quantity > product.min_quantity * 3:
                            stock_status = "overstocked"
                        
                        product_data = {
                            "id": product.id,
                            "name": product.name,
                            "barcode": product.barcode,
                            "category": product.category,
                            "price": float(product.price or 0),
                            "cost_price": float(product.cost_price or 0),
                            "quantity": product.quantity,
                            "min_quantity": product.min_quantity,
                            "unit": product.unit,
                            "is_active": product.is_active,
                            "total_sold": int(product.total_sold),
                            "total_revenue": float(product.total_revenue),
                            "profit_margin": round(profit_margin, 2),
                            "stock_status": stock_status,
                            "sales_count": product.sales_count,
                            "created_at": product.created_at.isoformat() if product.created_at else None
                        }
                    else:
                        product_data = {
                            "id": product.id,
                            "name": product.name,
                            "barcode": product.barcode,
                            "category": product.category,
                            "price": float(product.price or 0),
                            "cost_price": float(product.cost_price or 0),
                            "quantity": product.quantity,
                            "min_quantity": product.min_quantity,
                            "unit": product.unit,
                            "is_active": product.is_active,
                            "created_at": product.created_at.isoformat() if product.created_at else None
                        }
                    
                    f.write(json.dumps(product_data, ensure_ascii=False))
                    first_record = False
                    processed += 1
                    
                    # تحديث التقدم
                    task_info["processed_records"] = processed
                    task_info["progress"] = (processed / total_count) * 100
                
                offset += chunk_size
                await asyncio.sleep(0.01)
            
            f.write(']}')
        
        # ضغط الملف إذا طُلب
        if parameters.get("compress", False):
            compressed_file = export_file.with_suffix('.json.gz')
            with open(export_file, 'rb') as f_in:
                with gzip.open(compressed_file, 'wb') as f_out:
                    f_out.writelines(f_in)
            
            export_file.unlink()
            task_info["export_file"] = str(compressed_file)
        else:
            task_info["export_file"] = str(export_file)
        
        final_file = Path(task_info["export_file"])
        task_info["file_size"] = final_file.stat().st_size

    async def _export_customers_data(self, task_id: str, parameters: Dict[str, Any]):
        """
        تصدير بيانات العملاء مع الديون
        """
        task_info = self.active_streams[task_id]

        # بناء استعلام العملاء
        if parameters.get("include_debts", True):
            query = select(
                Customer.id,
                Customer.name,
                Customer.phone,
                Customer.email,
                Customer.address,
                Customer.is_active,
                Customer.created_at,
                func.coalesce(func.sum(CustomerDebt.amount), 0).label('total_debt'),
                func.count(CustomerDebt.id).label('debt_count')
            ).select_from(Customer)\
            .outerjoin(CustomerDebt, Customer.id == CustomerDebt.customer_id)\
            .group_by(Customer.id)
        else:
            query = select(Customer)

        # تطبيق الفلاتر
        conditions = []
        if parameters.get("is_active") is not None:
            conditions.append(Customer.is_active == parameters["is_active"])
        if parameters.get("has_debt"):
            conditions.append(CustomerDebt.amount > 0)

        if conditions:
            query = query.where(and_(*conditions))

        query = query.order_by(Customer.created_at.desc())

        # حساب العدد الإجمالي
        count_query = select(func.count(func.distinct(Customer.id)))
        if conditions:
            count_query = count_query.where(and_(*conditions))

        total_count = self.db.execute(count_query).scalar() or 0
        task_info["total_records"] = total_count

        # إنشاء ملف التصدير
        export_file = self.export_cache_dir / f"{task_id}_customers.json"

        chunk_size = parameters.get("chunk_size", self.config.max_chunk_size)
        processed = 0

        with open(export_file, 'w', encoding='utf-8') as f:
            f.write('{"customers": [')

            first_record = True
            offset = 0

            while offset < total_count:
                batch_query = query.offset(offset).limit(chunk_size)
                customers = self.db.execute(batch_query).all()

                if not customers:
                    break

                for customer in customers:
                    if not first_record:
                        f.write(',')

                    if parameters.get("include_debts", True):
                        customer_data = {
                            "id": customer.id,
                            "name": customer.name,
                            "phone": customer.phone,
                            "email": customer.email,
                            "address": customer.address,
                            "is_active": customer.is_active,
                            "total_debt": float(customer.total_debt or 0),
                            "debt_count": customer.debt_count,
                            "created_at": customer.created_at.isoformat() if customer.created_at else None
                        }
                    else:
                        customer_data = {
                            "id": customer.id,
                            "name": customer.name,
                            "phone": customer.phone,
                            "email": customer.email,
                            "address": customer.address,
                            "is_active": customer.is_active,
                            "created_at": customer.created_at.isoformat() if customer.created_at else None
                        }

                    f.write(json.dumps(customer_data, ensure_ascii=False))
                    first_record = False
                    processed += 1

                    # تحديث التقدم
                    task_info["processed_records"] = processed
                    task_info["progress"] = (processed / total_count) * 100

                offset += chunk_size
                await asyncio.sleep(0.01)

            f.write(']}')

        # ضغط الملف إذا طُلب
        if parameters.get("compress", False):
            compressed_file = export_file.with_suffix('.json.gz')
            with open(export_file, 'rb') as f_in:
                with gzip.open(compressed_file, 'wb') as f_out:
                    f_out.writelines(f_in)

            export_file.unlink()
            task_info["export_file"] = str(compressed_file)
        else:
            task_info["export_file"] = str(export_file)

        final_file = Path(task_info["export_file"])
        task_info["file_size"] = final_file.stat().st_size

    async def _export_bulk_data(self, task_id: str, parameters: Dict[str, Any]):
        """
        تصدير جميع البيانات (مبيعات، منتجات، عملاء)
        """
        task_info = self.active_streams[task_id]

        # حساب العدد الإجمالي لجميع الجداول
        sales_count = self.db.execute(select(func.count(Sale.id))).scalar() or 0
        products_count = self.db.execute(select(func.count(Product.id))).scalar() or 0
        customers_count = self.db.execute(select(func.count(Customer.id))).scalar() or 0

        total_count = sales_count + products_count + customers_count
        task_info["total_records"] = total_count

        # إنشاء ملف التصدير
        export_file = self.export_cache_dir / f"{task_id}_bulk_data.json"
        processed = 0

        with open(export_file, 'w', encoding='utf-8') as f:
            f.write('{"bulk_data": {')

            # تصدير المبيعات
            f.write('"sales": [')
            sales = self.db.execute(select(Sale).order_by(Sale.created_at.desc())).scalars().all()

            for i, sale in enumerate(sales):
                if i > 0:
                    f.write(',')

                sale_data = {
                    "id": sale.id,
                    "user_id": sale.user_id,
                    "customer_id": sale.customer_id,
                    "total_amount": float(sale.total_amount or 0),
                    "payment_method": sale.payment_method,
                    "created_at": sale.created_at.isoformat() if sale.created_at else None
                }

                f.write(json.dumps(sale_data, ensure_ascii=False))
                processed += 1
                task_info["processed_records"] = processed
                task_info["progress"] = (processed / total_count) * 100

                if processed % 100 == 0:  # تحديث كل 100 سجل
                    await asyncio.sleep(0.01)

            f.write('], "products": [')

            # تصدير المنتجات
            products = self.db.execute(select(Product).order_by(Product.created_at.desc())).scalars().all()

            for i, product in enumerate(products):
                if i > 0:
                    f.write(',')

                product_data = {
                    "id": product.id,
                    "name": product.name,
                    "barcode": product.barcode,
                    "category": product.category,
                    "price": float(product.price or 0),
                    "quantity": product.quantity,
                    "is_active": product.is_active,
                    "created_at": product.created_at.isoformat() if product.created_at else None
                }

                f.write(json.dumps(product_data, ensure_ascii=False))
                processed += 1
                task_info["processed_records"] = processed
                task_info["progress"] = (processed / total_count) * 100

                if processed % 100 == 0:
                    await asyncio.sleep(0.01)

            f.write('], "customers": [')

            # تصدير العملاء
            customers = self.db.execute(select(Customer).order_by(Customer.created_at.desc())).scalars().all()

            for i, customer in enumerate(customers):
                if i > 0:
                    f.write(',')

                customer_data = {
                    "id": customer.id,
                    "name": customer.name,
                    "phone": customer.phone,
                    "email": customer.email,
                    "is_active": customer.is_active,
                    "created_at": customer.created_at.isoformat() if customer.created_at else None
                }

                f.write(json.dumps(customer_data, ensure_ascii=False))
                processed += 1
                task_info["processed_records"] = processed
                task_info["progress"] = (processed / total_count) * 100

                if processed % 100 == 0:
                    await asyncio.sleep(0.01)

            f.write(']}}')

        # ضغط الملف إذا طُلب
        if parameters.get("compress", True):  # افتراضياً مضغوط للبيانات الكبيرة
            compressed_file = export_file.with_suffix('.json.gz')
            with open(export_file, 'rb') as f_in:
                with gzip.open(compressed_file, 'wb') as f_out:
                    f_out.writelines(f_in)

            export_file.unlink()
            task_info["export_file"] = str(compressed_file)
        else:
            task_info["export_file"] = str(export_file)

        final_file = Path(task_info["export_file"])
        task_info["file_size"] = final_file.stat().st_size

    async def _save_task_to_redis(self, task_id: str, task_info: Dict):
        """حفظ معلومات المهمة في Redis"""
        if self.redis_client:
            try:
                # تحويل datetime إلى string للتسلسل
                serializable_info = task_info.copy()
                for key, value in serializable_info.items():
                    if isinstance(value, datetime):
                        serializable_info[key] = value.isoformat()
                
                self.redis_client.setex(
                    f"streaming_task:{task_id}",
                    timedelta(hours=24),
                    json.dumps(serializable_info, ensure_ascii=False)
                )
            except Exception as e:
                logger.warning(f"فشل في حفظ المهمة في Redis: {e}")
    
    async def _get_task_from_redis(self, task_id: str) -> Optional[Dict]:
        """استرجاع معلومات المهمة من Redis"""
        if self.redis_client:
            try:
                data = self.redis_client.get(f"streaming_task:{task_id}")
                if data:
                    # تحويل bytes إلى string إذا لزم الأمر
                    if isinstance(data, bytes):
                        data_str = data.decode('utf-8')
                    else:
                        data_str = str(data)
                    return json.loads(data_str)
            except Exception as e:
                logger.warning(f"فشل في استرجاع المهمة من Redis: {e}")
        return None
    
    def cleanup_old_exports(self, max_age_hours: int = 24):
        """
        تنظيف ملفات التصدير القديمة
        """
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        for file_path in self.export_cache_dir.glob("*"):
            if file_path.is_file():
                file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_time < cutoff_time:
                    try:
                        file_path.unlink()
                        logger.info(f"تم حذف الملف القديم: {file_path}")
                    except Exception as e:
                        logger.error(f"فشل في حذف الملف {file_path}: {e}")
    
    def get_streaming_metrics(self) -> StreamingMetrics:
        """
        الحصول على مقاييس الأداء
        """
        # هذه دالة مبسطة - يمكن تطويرها لتشمل إحصائيات حقيقية من قاعدة البيانات
        return StreamingMetrics(
            total_exports=len(self.active_streams),
            successful_exports=len([t for t in self.active_streams.values() if t["status"] == "completed"]),
            failed_exports=len([t for t in self.active_streams.values() if t["status"] == "failed"]),
            average_export_time=0.0,
            total_data_exported_mb=0.0,
            most_exported_table="sales",
            peak_usage_hour=14
        )
