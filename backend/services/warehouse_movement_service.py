"""
خدمة حركات المستودعات
تطبق مبادئ البرمجة الكائنية مع معالجة شاملة للأخطاء
"""

import logging
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc
from datetime import datetime, timedelta
from decimal import Decimal

from models.warehouse import (
    Warehouse, WarehouseInventory, WarehouseMovement,
    MovementType, ReferenceType
)
from models.product import Product
from models.user import User
from utils.datetime_utils import get_tripoli_now

logger = logging.getLogger(__name__)


class WarehouseMovementService:
    """
    خدمة حركات المستودعات
    تطبق مبادئ البرمجة الكائنية مع نمط Singleton
    """
    
    _instance: Optional['WarehouseMovementService'] = None
    
    def __init__(self, db_session: Session):
        """تهيئة خدمة حركات المستودعات"""
        self.db_session = db_session
        logger.info("تم تهيئة خدمة حركات المستودعات بنجاح")
    
    @classmethod  # type: ignore
    def get_instance(cls, db_session: Session) -> 'WarehouseMovementService':
        """الحصول على instance وحيد من الخدمة"""
        if cls._instance is None:
            cls._instance = cls(db_session)
        elif db_session and cls._instance.db_session != db_session:
            cls._instance.db_session = db_session
        return cls._instance
    
    def record_movement(self, movement_data: Dict[str, Any]) -> Dict[str, Any]:
        """تسجيل حركة مستودع"""
        try:
            # التحقق من صحة البيانات
            validation_result = self._validate_movement_data(movement_data)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': validation_result['error']
                }
            
            # إنشاء سجل الحركة
            movement = WarehouseMovement(
                movement_type=MovementType(movement_data['movement_type']),
                from_warehouse_id=movement_data.get('from_warehouse_id'),
                to_warehouse_id=movement_data.get('to_warehouse_id'),
                product_id=movement_data['product_id'],
                quantity=Decimal(str(movement_data['quantity'])),
                unit_cost=Decimal(str(movement_data.get('unit_cost', 0))),
                total_cost=Decimal(str(movement_data.get('total_cost', 0))),
                reference_type=ReferenceType(movement_data.get('reference_type')) if movement_data.get('reference_type') else None,
                reference_id=movement_data.get('reference_id'),
                notes=movement_data.get('notes'),
                created_by=movement_data.get('created_by')
            )
            
            self.db_session.add(movement)
            
            # تحديث المخزون حسب نوع الحركة
            inventory_result = self._update_inventory_for_movement(movement_data)
            if not inventory_result['success']:
                self.db_session.rollback()
                return inventory_result
            
            self.db_session.commit()
            self.db_session.refresh(movement)
            
            logger.info(f"تم تسجيل حركة مستودع: {movement.movement_type} للمنتج {movement.product_id}")
            
            return {
                'success': True,
                'movement': {
                    'id': movement.id,
                    'movement_type': movement.movement_type.value,
                    'from_warehouse_id': movement.from_warehouse_id,
                    'to_warehouse_id': movement.to_warehouse_id,
                    'product_id': movement.product_id,
                    'quantity': float(movement.quantity),
                    'unit_cost': float(movement.unit_cost) if movement.unit_cost is not None else None,
                    'total_cost': float(movement.total_cost) if movement.total_cost is not None else None,
                    'reference_type': movement.reference_type.value if movement.reference_type is not None else None,
                    'reference_id': movement.reference_id,
                    'notes': movement.notes,
                    'created_by': movement.created_by,
                    'created_at': movement.created_at.isoformat() if movement.created_at is not None else None
                }
            }
            
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"خطأ في تسجيل حركة المستودع: {e}")
            return {
                'success': False,
                'error': f'خطأ في تسجيل حركة المستودع: {str(e)}'
            }
    
    def get_warehouse_movements(self, warehouse_id: int, filters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """الحصول على حركات المستودع"""
        try:
            # التحقق من وجود المستودع
            warehouse = self.db_session.query(Warehouse).filter(
                Warehouse.id == warehouse_id
            ).first()
            
            if not warehouse:
                return {
                    'success': False,
                    'error': 'المستودع غير موجود'
                }
            
            # بناء الاستعلام
            query = self.db_session.query(
                WarehouseMovement,
                Product.name.label('product_name'),
                Product.barcode.label('product_barcode'),
                User.username.label('created_by_username')
            ).join(
                Product, WarehouseMovement.product_id == Product.id
            ).outerjoin(
                User, WarehouseMovement.created_by == User.id
            ).filter(
                or_(
                    WarehouseMovement.from_warehouse_id == warehouse_id,
                    WarehouseMovement.to_warehouse_id == warehouse_id
                )
            )
            
            # تطبيق الفلاتر
            if filters:
                if filters.get('movement_type'):
                    query = query.filter(
                        WarehouseMovement.movement_type == MovementType(filters['movement_type'])
                    )
                
                if filters.get('product_id'):
                    query = query.filter(
                        WarehouseMovement.product_id == filters['product_id']
                    )
                
                if filters.get('date_from'):
                    query = query.filter(
                        WarehouseMovement.created_at >= filters['date_from']
                    )
                
                if filters.get('date_to'):
                    query = query.filter(
                        WarehouseMovement.created_at <= filters['date_to']
                    )
                
                if filters.get('reference_type'):
                    query = query.filter(
                        WarehouseMovement.reference_type == ReferenceType(filters['reference_type'])
                    )
            
            # ترتيب النتائج
            query = query.order_by(desc(WarehouseMovement.created_at))
            
            # تطبيق التصفح
            page = filters.get('page', 1) if filters else 1
            per_page = filters.get('per_page', 50) if filters else 50
            offset = (page - 1) * per_page
            
            total_count = query.count()
            movements_query = query.offset(offset).limit(per_page)
            
            movements = []
            for movement, product_name, product_barcode, created_by_username in movements_query:
                # تحديد اتجاه الحركة بالنسبة للمستودع
                direction = 'in' if movement.to_warehouse_id == warehouse_id else 'out'
                
                movements.append({
                    'id': movement.id,
                    'movement_type': movement.movement_type.value,
                    'direction': direction,
                    'from_warehouse_id': movement.from_warehouse_id,
                    'to_warehouse_id': movement.to_warehouse_id,
                    'product_id': movement.product_id,
                    'product_name': product_name,
                    'product_barcode': product_barcode,
                    'quantity': float(movement.quantity),
                    'unit_cost': float(movement.unit_cost) if movement.unit_cost else None,
                    'total_cost': float(movement.total_cost) if movement.total_cost else None,
                    'reference_type': movement.reference_type.value if movement.reference_type else None,
                    'reference_id': movement.reference_id,
                    'notes': movement.notes,
                    'created_by': movement.created_by,
                    'created_by_username': created_by_username,
                    'created_at': movement.created_at.isoformat() if movement.created_at else None
                })
            
            return {
                'success': True,
                'warehouse': {
                    'id': warehouse.id,
                    'name': warehouse.name,
                    'code': warehouse.code
                },
                'movements': movements,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total_count': total_count,
                    'total_pages': (total_count + per_page - 1) // per_page
                }
            }
            
        except Exception as e:
            logger.error(f"خطأ في جلب حركات المستودع: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب حركات المستودع: {str(e)}'
            }
    
    def get_product_movement_history(self, product_id: int, filters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """الحصول على تاريخ حركات المنتج"""
        try:
            # التحقق من وجود المنتج
            product = self.db_session.query(Product).filter(
                Product.id == product_id
            ).first()
            
            if not product:
                return {
                    'success': False,
                    'error': 'المنتج غير موجود'
                }
            
            # بناء الاستعلام
            query = self.db_session.query(
                WarehouseMovement,
                Warehouse.name.label('from_warehouse_name'),
                Warehouse.code.label('from_warehouse_code'),
                User.username.label('created_by_username')
            ).outerjoin(
                Warehouse, WarehouseMovement.from_warehouse_id == Warehouse.id
            ).outerjoin(
                User, WarehouseMovement.created_by == User.id
            ).filter(
                WarehouseMovement.product_id == product_id
            )
            
            # تطبيق الفلاتر
            if filters:
                if filters.get('warehouse_id'):
                    query = query.filter(
                        or_(
                            WarehouseMovement.from_warehouse_id == filters['warehouse_id'],
                            WarehouseMovement.to_warehouse_id == filters['warehouse_id']
                        )
                    )
                
                if filters.get('movement_type'):
                    query = query.filter(
                        WarehouseMovement.movement_type == MovementType(filters['movement_type'])
                    )
                
                if filters.get('date_from'):
                    query = query.filter(
                        WarehouseMovement.created_at >= filters['date_from']
                    )
                
                if filters.get('date_to'):
                    query = query.filter(
                        WarehouseMovement.created_at <= filters['date_to']
                    )
            
            # ترتيب النتائج
            query = query.order_by(desc(WarehouseMovement.created_at))
            
            movements = []
            for movement, from_warehouse_name, from_warehouse_code, created_by_username in query:
                movements.append({
                    'id': movement.id,
                    'movement_type': movement.movement_type.value,
                    'from_warehouse_id': movement.from_warehouse_id,
                    'from_warehouse_name': from_warehouse_name,
                    'from_warehouse_code': from_warehouse_code,
                    'to_warehouse_id': movement.to_warehouse_id,
                    'quantity': float(movement.quantity),
                    'unit_cost': float(movement.unit_cost) if movement.unit_cost else None,
                    'total_cost': float(movement.total_cost) if movement.total_cost else None,
                    'reference_type': movement.reference_type.value if movement.reference_type else None,
                    'reference_id': movement.reference_id,
                    'notes': movement.notes,
                    'created_by': movement.created_by,
                    'created_by_username': created_by_username,
                    'created_at': movement.created_at.isoformat() if movement.created_at else None
                })
            
            return {
                'success': True,
                'product': {
                    'id': product.id,
                    'name': product.name,
                    'barcode': product.barcode
                },
                'movements': movements,
                'total_count': len(movements)
            }
            
        except Exception as e:
            logger.error(f"خطأ في جلب تاريخ حركات المنتج: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب تاريخ حركات المنتج: {str(e)}'
            }

    def process_stock_adjustment(self, adjustment_data: Dict[str, Any]) -> Dict[str, Any]:
        """معالجة تعديل المخزون"""
        try:
            warehouse_id = adjustment_data['warehouse_id']
            product_id = adjustment_data['product_id']
            new_quantity = Decimal(str(adjustment_data['new_quantity']))
            reason = adjustment_data.get('reason', 'تعديل مخزون')
            created_by = adjustment_data.get('created_by')

            # الحصول على المخزون الحالي
            inventory = self.db_session.query(WarehouseInventory).filter(
                and_(
                    WarehouseInventory.warehouse_id == warehouse_id,
                    WarehouseInventory.product_id == product_id
                )
            ).first()

            if not inventory:
                return {
                    'success': False,
                    'error': 'المنتج غير موجود في هذا المستودع'
                }

            current_quantity = inventory.quantity
            adjustment_quantity = new_quantity - current_quantity

            if adjustment_quantity == 0:
                return {
                    'success': True,
                    'message': 'لا يوجد تغيير في الكمية'
                }

            # تسجيل حركة التعديل
            movement_data = {
                'movement_type': MovementType.ADJUSTMENT.value,
                'to_warehouse_id': warehouse_id if adjustment_quantity > 0 else None,
                'from_warehouse_id': warehouse_id if adjustment_quantity < 0 else None,
                'product_id': product_id,
                'quantity': abs(float(adjustment_quantity)),
                'reference_type': ReferenceType.ADJUSTMENT.value,
                'notes': f'{reason} - من {float(current_quantity)} إلى {float(new_quantity)}',
                'created_by': created_by
            }

            movement_result = self.record_movement(movement_data)
            if not movement_result['success']:
                return movement_result

            logger.info(f"تم تعديل مخزون المنتج {product_id} في المستودع {warehouse_id}")

            return {
                'success': True,
                'adjustment': {
                    'warehouse_id': warehouse_id,
                    'product_id': product_id,
                    'old_quantity': float(current_quantity),
                    'new_quantity': float(new_quantity),
                    'adjustment_quantity': float(adjustment_quantity),
                    'reason': reason,
                    'movement_id': movement_result['movement']['id']
                }
            }

        except Exception as e:
            self.db_session.rollback()
            logger.error(f"خطأ في معالجة تعديل المخزون: {e}")
            return {
                'success': False,
                'error': f'خطأ في معالجة تعديل المخزون: {str(e)}'
            }

    def get_movement_summary(self, warehouse_id: int, date_range: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """الحصول على ملخص حركات المستودع"""
        try:
            # تحديد نطاق التاريخ
            if date_range:
                date_from = date_range.get('date_from')
                date_to = date_range.get('date_to')
            else:
                # آخر 30 يوم افتراضياً
                date_to = get_tripoli_now()
                date_from = date_to - timedelta(days=30)

            # استعلام الحركات
            query = self.db_session.query(WarehouseMovement).filter(
                or_(
                    WarehouseMovement.from_warehouse_id == warehouse_id,
                    WarehouseMovement.to_warehouse_id == warehouse_id
                ),
                WarehouseMovement.created_at >= date_from,
                WarehouseMovement.created_at <= date_to
            )

            # إحصائيات الحركات
            movements_in = query.filter(
                WarehouseMovement.to_warehouse_id == warehouse_id
            ).all()

            movements_out = query.filter(
                WarehouseMovement.from_warehouse_id == warehouse_id
            ).all()

            # حساب الإحصائيات
            total_in = sum(float(m.quantity) for m in movements_in)
            total_out = sum(float(m.quantity) for m in movements_out)
            total_value_in = sum(float(m.total_cost or 0) for m in movements_in)
            total_value_out = sum(float(m.total_cost or 0) for m in movements_out)

            # إحصائيات حسب نوع الحركة
            movement_types_summary = {}
            for movement_type in MovementType:
                type_movements = [m for m in query.all() if m.movement_type == movement_type]
                movement_types_summary[movement_type.value] = {
                    'count': len(type_movements),
                    'total_quantity': sum(float(m.quantity) for m in type_movements),
                    'total_value': sum(float(m.total_cost or 0) for m in type_movements)
                }

            return {
                'success': True,
                'summary': {
                    'warehouse_id': warehouse_id,
                    'date_range': {
                        'from': date_from.isoformat() if date_from else None,
                        'to': date_to.isoformat() if date_to else None
                    },
                    'movements_in': {
                        'count': len(movements_in),
                        'total_quantity': total_in,
                        'total_value': total_value_in
                    },
                    'movements_out': {
                        'count': len(movements_out),
                        'total_quantity': total_out,
                        'total_value': total_value_out
                    },
                    'net_movement': {
                        'quantity': total_in - total_out,
                        'value': total_value_in - total_value_out
                    },
                    'by_movement_type': movement_types_summary
                }
            }

        except Exception as e:
            logger.error(f"خطأ في جلب ملخص الحركات: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب ملخص الحركات: {str(e)}'
            }

    def _validate_movement_data(self, movement_data: Dict[str, Any]) -> Dict[str, Any]:
        """التحقق من صحة بيانات الحركة"""
        try:
            # التحقق من الحقول المطلوبة
            required_fields = ['movement_type', 'product_id', 'quantity']
            for field in required_fields:
                if field not in movement_data:
                    return {
                        'valid': False,
                        'error': f'الحقل {field} مطلوب'
                    }

            # التحقق من نوع الحركة
            try:
                movement_type = MovementType(movement_data['movement_type'])
            except ValueError:
                return {
                    'valid': False,
                    'error': f'نوع الحركة غير صحيح: {movement_data["movement_type"]}'
                }

            # التحقق من الكمية
            try:
                quantity = float(movement_data['quantity'])
                if quantity <= 0:
                    return {
                        'valid': False,
                        'error': 'الكمية يجب أن تكون أكبر من صفر'
                    }
            except (ValueError, TypeError):
                return {
                    'valid': False,
                    'error': 'الكمية يجب أن تكون رقم صحيح'
                }

            # التحقق من المستودعات حسب نوع الحركة
            if movement_type == MovementType.IN:
                if not movement_data.get('to_warehouse_id'):
                    return {
                        'valid': False,
                        'error': 'مستودع الوجهة مطلوب لحركة الدخول'
                    }
            elif movement_type == MovementType.OUT:
                if not movement_data.get('from_warehouse_id'):
                    return {
                        'valid': False,
                        'error': 'مستودع المصدر مطلوب لحركة الخروج'
                    }
            elif movement_type == MovementType.TRANSFER:
                if not movement_data.get('from_warehouse_id') or not movement_data.get('to_warehouse_id'):
                    return {
                        'valid': False,
                        'error': 'مستودع المصدر والوجهة مطلوبان لحركة التحويل'
                    }
                if movement_data['from_warehouse_id'] == movement_data['to_warehouse_id']:
                    return {
                        'valid': False,
                        'error': 'لا يمكن التحويل من وإلى نفس المستودع'
                    }

            return {
                'valid': True
            }

        except Exception as e:
            return {
                'valid': False,
                'error': f'خطأ في التحقق من البيانات: {str(e)}'
            }

    def _update_inventory_for_movement(self, movement_data: Dict[str, Any]) -> Dict[str, Any]:
        """تحديث المخزون حسب الحركة"""
        try:
            movement_type = MovementType(movement_data['movement_type'])
            product_id = movement_data['product_id']
            quantity = Decimal(str(movement_data['quantity']))

            # تحديث المخزون حسب نوع الحركة
            if movement_type == MovementType.IN:
                return self._increase_inventory(movement_data['to_warehouse_id'], product_id, quantity)
            elif movement_type == MovementType.OUT:
                return self._decrease_inventory(movement_data['from_warehouse_id'], product_id, quantity)
            elif movement_type == MovementType.TRANSFER:
                # تقليل من المستودع المصدر
                decrease_result = self._decrease_inventory(movement_data['from_warehouse_id'], product_id, quantity)
                if not decrease_result['success']:
                    return decrease_result
                # زيادة في المستودع الوجهة
                return self._increase_inventory(movement_data['to_warehouse_id'], product_id, quantity)
            elif movement_type == MovementType.ADJUSTMENT:
                # التعديل يتم في دالة منفصلة
                return {'success': True}

            return {'success': True}

        except Exception as e:
            return {
                'success': False,
                'error': f'خطأ في تحديث المخزون: {str(e)}'
            }

    def _increase_inventory(self, warehouse_id: int, product_id: int, quantity: Decimal) -> Dict[str, Any]:
        """زيادة المخزون"""
        try:
            inventory = self.db_session.query(WarehouseInventory).filter(
                and_(
                    WarehouseInventory.warehouse_id == warehouse_id,
                    WarehouseInventory.product_id == product_id
                )
            ).first()

            if not inventory:
                # إنشاء سجل مخزون جديد
                inventory = WarehouseInventory(
                    warehouse_id=warehouse_id,
                    product_id=product_id,
                    quantity=quantity
                )
                self.db_session.add(inventory)
            else:
                # زيادة الكمية الموجودة
                inventory.quantity += quantity
                inventory.last_updated = get_tripoli_now()

            return {'success': True}

        except Exception as e:
            return {
                'success': False,
                'error': f'خطأ في زيادة المخزون: {str(e)}'
            }

    def _decrease_inventory(self, warehouse_id: int, product_id: int, quantity: Decimal) -> Dict[str, Any]:
        """تقليل المخزون"""
        try:
            inventory = self.db_session.query(WarehouseInventory).filter(
                and_(
                    WarehouseInventory.warehouse_id == warehouse_id,
                    WarehouseInventory.product_id == product_id
                )
            ).first()

            if not inventory:
                return {
                    'success': False,
                    'error': 'المنتج غير موجود في هذا المستودع'
                }

            available_quantity = inventory.quantity - inventory.reserved_quantity
            if available_quantity < quantity:
                return {
                    'success': False,
                    'error': f'الكمية المتاحة ({available_quantity}) أقل من المطلوب ({quantity})'
                }

            inventory.quantity -= quantity
            inventory.last_updated = get_tripoli_now()

            return {'success': True}

        except Exception as e:
            return {
                'success': False,
                'error': f'خطأ في تقليل المخزون: {str(e)}'
            }
