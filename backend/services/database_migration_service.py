"""
خدمة ترحيل قاعدة البيانات من SQLite إلى PostgreSQL
تطبق مبادئ البرمجة الكائنية وتتبع قواعد النظام
"""

import os
import logging
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
from datetime import datetime
from sqlalchemy import create_engine, MetaData, Table, text, inspect
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

from database.base import Base
from utils.datetime_utils import tripoli_timestamp

logger = logging.getLogger(__name__)

class DatabaseMigrationService:
    """
    خدمة ترحيل قاعدة البيانات من SQLite إلى PostgreSQL
    تطبق مبادئ البرمجة الكائنية مع نمط Singleton
    """
    
    _instance: Optional['DatabaseMigrationService'] = None
    
    def __init__(self):
        """تهيئة خدمة الترحيل"""
        if DatabaseMigrationService._instance is not None:
            raise Exception("استخدم getInstance() للحصول على instance")
        
        self.base_dir = Path(__file__).parent.parent
        self.sqlite_db_path = self.base_dir / "smartpos.db"
        self.backup_dir = self.base_dir / "backups" / "postgres_migration"
        
        # إعدادات قواعد البيانات
        self.sqlite_url = f"sqlite:///{self.sqlite_db_path.absolute()}"
        self.postgres_url = "postgresql+psycopg2://postgres:password@localhost:5432/smartpos_db"
        
        # محركات قواعد البيانات
        self.sqlite_engine = None
        self.postgres_engine = None
        
        # جلسات قواعد البيانات
        self.sqlite_session = None
        self.postgres_session = None
        
        # إحصائيات الترحيل
        self.migration_stats = {
            "start_time": None,
            "end_time": None,
            "tables_migrated": 0,
            "total_rows_migrated": 0,
            "errors": [],
            "success": False
        }
    
    @classmethod
    def getInstance(cls) -> 'DatabaseMigrationService':
        """الحصول على instance وحيد من الخدمة"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def _setup_database_connections(self) -> bool:
        """إعداد اتصالات قواعد البيانات"""
        try:
            # إعداد اتصال SQLite
            self.sqlite_engine = create_engine(
                self.sqlite_url,
                connect_args={"check_same_thread": False}
            )
            
            # إعداد اتصال PostgreSQL
            self.postgres_engine = create_engine(
                self.postgres_url,
                pool_size=20,
                max_overflow=30,
                pool_timeout=30,
                pool_recycle=3600,
                pool_pre_ping=True
            )
            
            # إنشاء جلسات
            SQLiteSession = sessionmaker(bind=self.sqlite_engine)
            PostgresSession = sessionmaker(bind=self.postgres_engine)
            
            self.sqlite_session = SQLiteSession()
            self.postgres_session = PostgresSession()
            
            logger.info("✅ تم إعداد اتصالات قواعد البيانات بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في إعداد اتصالات قواعد البيانات: {e}")
            self.migration_stats["errors"].append(f"Database connection error: {str(e)}")
            return False
    
    def _verify_sqlite_database(self) -> bool:
        """التحقق من وجود وصحة قاعدة بيانات SQLite"""
        try:
            if not self.sqlite_db_path.exists():
                raise FileNotFoundError(f"قاعدة بيانات SQLite غير موجودة: {self.sqlite_db_path}")
            
            # اختبار الاتصال
            with self.sqlite_engine.connect() as conn:
                result = conn.execute(text("SELECT COUNT(*) FROM sqlite_master WHERE type='table'"))
                table_count = result.scalar()
                
            logger.info(f"✅ تم العثور على {table_count} جدول في قاعدة بيانات SQLite")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من قاعدة بيانات SQLite: {e}")
            self.migration_stats["errors"].append(f"SQLite verification error: {str(e)}")
            return False
    
    def _verify_postgres_database(self) -> bool:
        """التحقق من وجود وصحة قاعدة بيانات PostgreSQL"""
        try:
            # اختبار الاتصال
            with self.postgres_engine.connect() as conn:
                result = conn.execute(text("SELECT version()"))
                version = result.scalar()
                
            logger.info(f"✅ تم الاتصال بـ PostgreSQL بنجاح: {version}")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من قاعدة بيانات PostgreSQL: {e}")
            self.migration_stats["errors"].append(f"PostgreSQL verification error: {str(e)}")
            return False
    
    def _create_postgres_schema(self) -> bool:
        """إنشاء مخطط الجداول في PostgreSQL"""
        try:
            # استيراد جميع النماذج لضمان إنشاء الجداول
            from database.base import import_all_models
            import_all_models()
            
            # إنشاء الجداول في PostgreSQL
            Base.metadata.create_all(bind=self.postgres_engine)
            
            logger.info("✅ تم إنشاء مخطط الجداول في PostgreSQL بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء مخطط PostgreSQL: {e}")
            self.migration_stats["errors"].append(f"Schema creation error: {str(e)}")
            return False
    
    def _get_table_migration_order(self) -> List[str]:
        """تحديد ترتيب ترحيل الجداول بناءً على العلاقات"""
        # ترتيب الجداول بناءً على التبعيات (الجداول المرجعية أولاً)
        return [
            "users",
            "settings",
            "customers",
            "products",
            "sales",
            "sale_items",
            "customer_debts",
            "debt_payments",
            "scheduled_tasks",
            "device_fingerprints",
            "device_fingerprint_history",
            "approved_devices",
            "blocked_devices",
            "pending_devices",
            "device_security_settings",
            "chat_rooms",
            "chat_room_members",
            "chat_messages",
            "user_online_status",
            "system_logs"
        ]
    
    def _migrate_table_data(self, table_name: str) -> Tuple[bool, int]:
        """ترحيل بيانات جدول واحد"""
        try:
            # الحصول على metadata للجدول
            sqlite_metadata = MetaData()
            sqlite_metadata.reflect(bind=self.sqlite_engine)
            
            if table_name not in sqlite_metadata.tables:
                logger.warning(f"⚠️ الجدول {table_name} غير موجود في SQLite")
                return True, 0
            
            table = sqlite_metadata.tables[table_name]
            
            # جلب البيانات من SQLite
            with self.sqlite_engine.connect() as sqlite_conn:
                result = sqlite_conn.execute(table.select())
                rows = result.fetchall()
            
            if not rows:
                logger.info(f"📋 الجدول {table_name} فارغ")
                return True, 0
            
            # تحويل البيانات إلى قاموس
            rows_data = [dict(row._mapping) for row in rows]
            
            # إدراج البيانات في PostgreSQL
            with self.postgres_engine.connect() as postgres_conn:
                postgres_conn.execute(table.insert(), rows_data)
                postgres_conn.commit()
            
            logger.info(f"✅ تم ترحيل {len(rows_data)} صف من جدول {table_name}")
            return True, len(rows_data)
            
        except Exception as e:
            logger.error(f"❌ خطأ في ترحيل جدول {table_name}: {e}")
            self.migration_stats["errors"].append(f"Table {table_name} migration error: {str(e)}")
            return False, 0
    
    def _update_sequences(self) -> bool:
        """تحديث sequences في PostgreSQL"""
        try:
            with self.postgres_engine.connect() as conn:
                # الحصول على جميع الجداول التي تحتوي على primary key
                tables_with_pk = [
                    "users", "products", "customers", "sales", "sale_items",
                    "customer_debts", "debt_payments", "settings", "scheduled_tasks",
                    "device_fingerprints", "device_fingerprint_history",
                    "chat_rooms", "chat_room_members", "chat_messages", "user_online_status"
                ]
                
                for table_name in tables_with_pk:
                    try:
                        # الحصول على أقصى ID في الجدول
                        result = conn.execute(text(f"SELECT MAX(id) FROM {table_name}"))
                        max_id = result.scalar()
                        
                        if max_id:
                            # تحديث sequence
                            conn.execute(text(f"SELECT setval('{table_name}_id_seq', {max_id})"))
                            logger.info(f"✅ تم تحديث sequence للجدول {table_name} إلى {max_id}")
                    
                    except Exception as table_error:
                        logger.warning(f"⚠️ تعذر تحديث sequence للجدول {table_name}: {table_error}")
                
                conn.commit()
            
            logger.info("✅ تم تحديث جميع sequences بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث sequences: {e}")
            self.migration_stats["errors"].append(f"Sequences update error: {str(e)}")
            return False

    def migrate_database(self) -> Dict[str, Any]:
        """
        تنفيذ عملية الترحيل الكاملة من SQLite إلى PostgreSQL
        """
        self.migration_stats["start_time"] = datetime.now()
        logger.info("🚀 بدء عملية ترحيل قاعدة البيانات من SQLite إلى PostgreSQL")

        try:
            # 1. إعداد الاتصالات
            if not self._setup_database_connections():
                return self._get_migration_result(False, "فشل في إعداد اتصالات قواعد البيانات")

            # 2. التحقق من قواعد البيانات
            if not self._verify_sqlite_database():
                return self._get_migration_result(False, "فشل في التحقق من قاعدة بيانات SQLite")

            if not self._verify_postgres_database():
                return self._get_migration_result(False, "فشل في التحقق من قاعدة بيانات PostgreSQL")

            # 3. إنشاء مخطط PostgreSQL
            if not self._create_postgres_schema():
                return self._get_migration_result(False, "فشل في إنشاء مخطط PostgreSQL")

            # 4. ترحيل البيانات
            tables_to_migrate = self._get_table_migration_order()
            total_rows = 0

            for table_name in tables_to_migrate:
                success, row_count = self._migrate_table_data(table_name)
                if success:
                    self.migration_stats["tables_migrated"] += 1
                    total_rows += row_count
                else:
                    logger.error(f"❌ فشل في ترحيل جدول {table_name}")

            self.migration_stats["total_rows_migrated"] = total_rows

            # 5. تحديث sequences
            if not self._update_sequences():
                logger.warning("⚠️ تحذير: فشل في تحديث بعض sequences")

            # 6. إنهاء العملية
            self.migration_stats["success"] = True
            self.migration_stats["end_time"] = datetime.now()

            return self._get_migration_result(True, "تم ترحيل قاعدة البيانات بنجاح")

        except Exception as e:
            logger.error(f"❌ خطأ عام في عملية الترحيل: {e}")
            self.migration_stats["errors"].append(f"General migration error: {str(e)}")
            return self._get_migration_result(False, f"فشل في عملية الترحيل: {str(e)}")

        finally:
            self._cleanup_connections()

    def _get_migration_result(self, success: bool, message: str) -> Dict[str, Any]:
        """إنشاء نتيجة عملية الترحيل"""
        duration = None
        if self.migration_stats["start_time"]:
            end_time = self.migration_stats["end_time"] or datetime.now()
            duration = (end_time - self.migration_stats["start_time"]).total_seconds()

        return {
            "success": success,
            "message": message,
            "stats": {
                "tables_migrated": self.migration_stats["tables_migrated"],
                "total_rows_migrated": self.migration_stats["total_rows_migrated"],
                "duration_seconds": duration,
                "start_time": self.migration_stats["start_time"].isoformat() if self.migration_stats["start_time"] else None,
                "end_time": self.migration_stats["end_time"].isoformat() if self.migration_stats["end_time"] else None,
                "errors": self.migration_stats["errors"]
            }
        }

    def _cleanup_connections(self):
        """تنظيف اتصالات قواعد البيانات"""
        try:
            if self.sqlite_session:
                self.sqlite_session.close()
            if self.postgres_session:
                self.postgres_session.close()
            if self.sqlite_engine:
                self.sqlite_engine.dispose()
            if self.postgres_engine:
                self.postgres_engine.dispose()

            logger.info("✅ تم تنظيف اتصالات قواعد البيانات")

        except Exception as e:
            logger.error(f"⚠️ خطأ في تنظيف الاتصالات: {e}")

    def verify_migration(self) -> Dict[str, Any]:
        """التحقق من نجاح عملية الترحيل"""
        try:
            if not self._setup_database_connections():
                return {"success": False, "message": "فشل في إعداد الاتصالات للتحقق"}

            verification_results = {}
            tables_to_check = self._get_table_migration_order()

            for table_name in tables_to_check:
                try:
                    # عدد الصفوف في SQLite
                    with self.sqlite_engine.connect() as sqlite_conn:
                        sqlite_result = sqlite_conn.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                        sqlite_count = sqlite_result.scalar() or 0

                    # عدد الصفوف في PostgreSQL
                    with self.postgres_engine.connect() as postgres_conn:
                        postgres_result = postgres_conn.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                        postgres_count = postgres_result.scalar() or 0

                    verification_results[table_name] = {
                        "sqlite_count": sqlite_count,
                        "postgres_count": postgres_count,
                        "match": sqlite_count == postgres_count
                    }

                except Exception as table_error:
                    verification_results[table_name] = {
                        "error": str(table_error),
                        "match": False
                    }

            # حساب النتيجة الإجمالية
            total_matches = sum(1 for result in verification_results.values()
                              if result.get("match", False))
            total_tables = len(verification_results)

            return {
                "success": total_matches == total_tables,
                "message": f"تطابق {total_matches} من {total_tables} جدول",
                "details": verification_results,
                "match_percentage": (total_matches / total_tables * 100) if total_tables > 0 else 0
            }

        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من الترحيل: {e}")
            return {
                "success": False,
                "message": f"فشل في التحقق: {str(e)}"
            }

        finally:
            self._cleanup_connections()
