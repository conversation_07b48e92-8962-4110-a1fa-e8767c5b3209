from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func
from fastapi import HTTPException

from models.variant_attribute import VariantAttribute
from models.variant_value import VariantValue
from schemas.variant_attribute import (
    VariantAttributeCreate,
    VariantAttributeUpdate,
    VariantValueCreate,
    VariantValueUpdate,
    AttributeOrderUpdate,
    ValueOrderUpdate
)


class VariantAttributeService:
    """
    خدمة إدارة خصائص المتغيرات
    تطبق مبادئ البرمجة الكائنية مع معالجة شاملة للأخطاء
    """

    def __init__(self, db_session: Session):
        self.db = db_session

    def get_all_attributes(self, include_inactive: bool = False) -> List[VariantAttribute]:
        """
        جلب جميع الخصائص مع قيمها
        
        Args:
            include_inactive: هل تشمل الخصائص غير النشطة
            
        Returns:
            قائمة بجميع الخصائص مع قيمها
        """
        try:
            query = self.db.query(VariantAttribute).options(
                joinedload(VariantAttribute.values)
            )
            
            if not include_inactive:
                query = query.filter(VariantAttribute.is_active == True)
            
            attributes = query.order_by(VariantAttribute.sort_order, VariantAttribute.name).all()
            
            # ترتيب القيم داخل كل خاصية
            for attribute in attributes:
                if not include_inactive:
                    attribute.values = [v for v in attribute.values if v.is_active]
                attribute.values.sort(key=lambda x: (x.sort_order, x.value))
            
            return attributes
            
        except Exception as error:
            print(f"خطأ في جلب الخصائص: {error}")
            raise HTTPException(status_code=500, detail="فشل في جلب خصائص المتغيرات")

    def get_attribute_by_id(self, attribute_id: int) -> Optional[VariantAttribute]:
        """
        جلب خاصية محددة بواسطة المعرف
        
        Args:
            attribute_id: معرف الخاصية
            
        Returns:
            الخاصية المطلوبة أو None
        """
        try:
            attribute = self.db.query(VariantAttribute).options(
                joinedload(VariantAttribute.values)
            ).filter(VariantAttribute.id == attribute_id).first()
            
            if attribute:
                # ترتيب القيم
                attribute.values.sort(key=lambda x: (x.sort_order, x.value))
            
            return attribute
            
        except Exception as error:
            print(f"خطأ في جلب الخاصية {attribute_id}: {error}")
            raise HTTPException(status_code=500, detail="فشل في جلب الخاصية")

    def create_attribute(self, attribute_data: VariantAttributeCreate, user_id: int) -> VariantAttribute:
        """
        إنشاء خاصية جديدة مع قيمها
        
        Args:
            attribute_data: بيانات الخاصية الجديدة
            user_id: معرف المستخدم المنشئ
            
        Returns:
            الخاصية المنشأة
        """
        try:
            # التحقق من عدم وجود خاصية بنفس الاسم
            existing = self.db.query(VariantAttribute).filter(
                or_(
                    VariantAttribute.name == attribute_data.name,
                    VariantAttribute.name_ar == attribute_data.name_ar
                )
            ).first()
            
            if existing:
                raise HTTPException(
                    status_code=400, 
                    detail="يوجد خاصية بنفس الاسم مسبقاً"
                )
            
            # إنشاء الخاصية
            db_attribute = VariantAttribute(
                name=attribute_data.name,
                name_ar=attribute_data.name_ar,
                description=attribute_data.description,
                attribute_type=attribute_data.attribute_type,
                is_required=attribute_data.is_required,
                is_active=attribute_data.is_active,
                sort_order=attribute_data.sort_order,
                created_by=user_id
            )
            
            self.db.add(db_attribute)
            self.db.flush()  # للحصول على ID
            
            # إضافة القيم إذا كانت موجودة
            if attribute_data.values:
                for value_data in attribute_data.values:
                    db_value = VariantValue(
                        attribute_id=db_attribute.id,
                        value=value_data.value,
                        value_ar=value_data.value_ar,
                        color_code=value_data.color_code,
                        is_active=value_data.is_active,
                        sort_order=value_data.sort_order
                    )
                    self.db.add(db_value)
            
            self.db.commit()
            self.db.refresh(db_attribute)
            
            return db_attribute
            
        except HTTPException:
            self.db.rollback()
            raise
        except Exception as error:
            self.db.rollback()
            print(f"خطأ في إنشاء الخاصية: {error}")
            raise HTTPException(status_code=500, detail="فشل في إنشاء الخاصية")

    async def update_attribute(self, attribute_id: int, attribute_data: VariantAttributeUpdate, user_id: int) -> VariantAttribute:
        """
        تحديث خاصية موجودة
        
        Args:
            attribute_id: معرف الخاصية
            attribute_data: البيانات المحدثة
            user_id: معرف المستخدم المحدث
            
        Returns:
            الخاصية المحدثة
        """
        try:
            db_attribute = self.db.query(VariantAttribute).filter(
                VariantAttribute.id == attribute_id
            ).first()
            
            if not db_attribute:
                raise HTTPException(status_code=404, detail="الخاصية غير موجودة")
            
            # التحقق من عدم تكرار الاسم
            if attribute_data.name or attribute_data.name_ar:
                existing = self.db.query(VariantAttribute).filter(
                    and_(
                        VariantAttribute.id != attribute_id,
                        or_(
                            VariantAttribute.name == (attribute_data.name or db_attribute.name),
                            VariantAttribute.name_ar == (attribute_data.name_ar or db_attribute.name_ar)
                        )
                    )
                ).first()
                
                if existing:
                    raise HTTPException(
                        status_code=400, 
                        detail="يوجد خاصية بنفس الاسم مسبقاً"
                    )
            
            # تحديث البيانات
            update_data = attribute_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(db_attribute, field, value)
            
            db_attribute.updated_by = user_id
            
            self.db.commit()
            self.db.refresh(db_attribute)
            
            return db_attribute
            
        except HTTPException:
            self.db.rollback()
            raise
        except Exception as error:
            self.db.rollback()
            print(f"خطأ في تحديث الخاصية {attribute_id}: {error}")
            raise HTTPException(status_code=500, detail="فشل في تحديث الخاصية")

    async def check_attribute_usage(self, attribute_id: int) -> dict:
        """
        فحص استخدام الخاصية في المنتجات

        Args:
            attribute_id: معرف الخاصية

        Returns:
            معلومات عن استخدام الخاصية
        """
        try:
            # TODO: عندما يتم إنشاء جدول product_variants، سيتم تحديث هذا الفحص
            # حالياً سنفحص إذا كانت الخاصية مستخدمة في أي مكان آخر

            # فحص عدد القيم المرتبطة بالخاصية
            values_count = self.db.query(VariantValue).filter(
                VariantValue.attribute_id == attribute_id,
                VariantValue.is_active == True
            ).count()

            # في المستقبل، سيتم إضافة فحص للمنتجات:
            # products_using_attribute = self.db.query(ProductVariant).filter(
            #     ProductVariant.attribute_id == attribute_id
            # ).count()

            return {
                "is_used": False,  # سيتم تحديثه عندما يتم إنشاء جدول المنتجات
                "values_count": values_count,
                "products_count": 0,  # مؤقت
                "can_delete": True,   # مؤقت
                "warning_message": None
            }

        except Exception as error:
            print(f"خطأ في فحص استخدام الخاصية {attribute_id}: {error}")
            raise HTTPException(status_code=500, detail="فشل في فحص استخدام الخاصية")

    async def delete_attribute(self, attribute_id: int, force_delete: bool = False, permanent_delete: bool = True) -> dict:
        """
        حذف خاصية مع فحص الاستخدام

        Args:
            attribute_id: معرف الخاصية
            force_delete: إجبار الحذف حتى لو كانت مستخدمة
            permanent_delete: حذف فعلي من قاعدة البيانات (افتراضي: True)

        Returns:
            نتيجة عملية الحذف
        """
        try:
            db_attribute = self.db.query(VariantAttribute).filter(
                VariantAttribute.id == attribute_id
            ).first()

            if not db_attribute:
                raise HTTPException(status_code=404, detail="الخاصية غير موجودة")

            # فحص استخدام الخاصية
            usage_info = await self.check_attribute_usage(attribute_id)

            if usage_info["is_used"] and not force_delete:
                raise HTTPException(
                    status_code=400,
                    detail=f"لا يمكن حذف الخاصية لأنها مستخدمة في {usage_info['products_count']} منتج"
                )

            if permanent_delete:
                # حذف فعلي - حذف جميع القيم المرتبطة أولاً
                deleted_values = self.db.query(VariantValue).filter(
                    VariantValue.attribute_id == attribute_id
                ).delete()

                # حذف الخاصية نفسها
                self.db.delete(db_attribute)

                message = "تم حذف الخاصية نهائياً من قاعدة البيانات"
            else:
                # Soft delete - تعطيل الخاصية بدلاً من حذفها
                db_attribute.is_active = False

                # تعطيل جميع القيم المرتبطة
                deleted_values = self.db.query(VariantValue).filter(
                    VariantValue.attribute_id == attribute_id
                ).update({"is_active": False})

                message = "تم تعطيل الخاصية"

            self.db.commit()

            return {
                "success": True,
                "message": message,
                "deleted_values_count": deleted_values,
                "was_forced": force_delete and usage_info["is_used"],
                "permanent": permanent_delete
            }

        except HTTPException:
            self.db.rollback()
            raise
        except Exception as error:
            self.db.rollback()
            print(f"خطأ في حذف الخاصية {attribute_id}: {error}")
            raise HTTPException(status_code=500, detail="فشل في حذف الخاصية")

    async def reorder_attributes(self, attribute_orders: List[AttributeOrderUpdate]) -> bool:
        """
        إعادة ترتيب الخصائص
        
        Args:
            attribute_orders: قائمة بترتيب الخصائص الجديد
            
        Returns:
            True إذا تم التحديث بنجاح
        """
        try:
            for order_update in attribute_orders:
                self.db.query(VariantAttribute).filter(
                    VariantAttribute.id == order_update.id
                ).update({"sort_order": order_update.sort_order})
            
            self.db.commit()
            
            return True

        except Exception as error:
            self.db.rollback()
            print(f"خطأ في إعادة ترتيب الخصائص: {error}")
            raise HTTPException(status_code=500, detail="فشل في إعادة ترتيب الخصائص")

    # ==================== إدارة قيم الخصائص ====================

    async def get_attribute_values(self, attribute_id: int, include_inactive: bool = False) -> List[VariantValue]:
        """
        جلب قيم خاصية معينة

        Args:
            attribute_id: معرف الخاصية
            include_inactive: هل تشمل القيم غير النشطة

        Returns:
            قائمة بقيم الخاصية
        """
        try:
            query = self.db.query(VariantValue).filter(
                VariantValue.attribute_id == attribute_id
            )

            if not include_inactive:
                query = query.filter(VariantValue.is_active == True)

            values = query.order_by(VariantValue.sort_order, VariantValue.value).all()

            return values

        except Exception as error:
            print(f"خطأ في جلب قيم الخاصية {attribute_id}: {error}")
            raise HTTPException(status_code=500, detail="فشل في جلب قيم الخاصية")

    async def add_attribute_value(self, attribute_id: int, value_data: VariantValueCreate) -> VariantValue:
        """
        إضافة قيمة جديدة لخاصية

        Args:
            attribute_id: معرف الخاصية
            value_data: بيانات القيمة الجديدة

        Returns:
            القيمة المنشأة
        """
        try:
            # التحقق من وجود الخاصية
            attribute = self.db.query(VariantAttribute).filter(
                VariantAttribute.id == attribute_id
            ).first()

            if not attribute:
                raise HTTPException(status_code=404, detail="الخاصية غير موجودة")

            # التحقق من عدم تكرار القيمة
            existing = self.db.query(VariantValue).filter(
                and_(
                    VariantValue.attribute_id == attribute_id,
                    or_(
                        VariantValue.value == value_data.value,
                        VariantValue.value_ar == value_data.value_ar
                    )
                )
            ).first()

            if existing:
                raise HTTPException(
                    status_code=400,
                    detail="يوجد قيمة بنفس الاسم في هذه الخاصية مسبقاً"
                )

            # إنشاء القيمة الجديدة
            db_value = VariantValue(
                attribute_id=attribute_id,
                value=value_data.value,
                value_ar=value_data.value_ar,
                color_code=value_data.color_code,
                is_active=value_data.is_active,
                sort_order=value_data.sort_order
            )

            self.db.add(db_value)
            self.db.commit()
            self.db.refresh(db_value)

            return db_value

        except HTTPException:
            self.db.rollback()
            raise
        except Exception as error:
            self.db.rollback()
            print(f"خطأ في إضافة قيمة للخاصية {attribute_id}: {error}")
            raise HTTPException(status_code=500, detail="فشل في إضافة القيمة")

    async def update_attribute_value(self, value_id: int, value_data: VariantValueUpdate) -> VariantValue:
        """
        تحديث قيمة خاصية

        Args:
            value_id: معرف القيمة
            value_data: البيانات المحدثة

        Returns:
            القيمة المحدثة
        """
        try:
            db_value = self.db.query(VariantValue).filter(
                VariantValue.id == value_id
            ).first()

            if not db_value:
                raise HTTPException(status_code=404, detail="القيمة غير موجودة")

            # التحقق من عدم تكرار القيمة في نفس الخاصية
            if value_data.value or value_data.value_ar:
                existing = self.db.query(VariantValue).filter(
                    and_(
                        VariantValue.id != value_id,
                        VariantValue.attribute_id == db_value.attribute_id,
                        or_(
                            VariantValue.value == (value_data.value or db_value.value),
                            VariantValue.value_ar == (value_data.value_ar or db_value.value_ar)
                        )
                    )
                ).first()

                if existing:
                    raise HTTPException(
                        status_code=400,
                        detail="يوجد قيمة بنفس الاسم في هذه الخاصية مسبقاً"
                    )

            # تحديث البيانات
            update_data = value_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(db_value, field, value)

            self.db.commit()
            self.db.refresh(db_value)

            return db_value

        except HTTPException:
            self.db.rollback()
            raise
        except Exception as error:
            self.db.rollback()
            print(f"خطأ في تحديث القيمة {value_id}: {error}")
            raise HTTPException(status_code=500, detail="فشل في تحديث القيمة")

    async def delete_attribute_value(self, value_id: int, permanent_delete: bool = True) -> bool:
        """
        حذف قيمة خاصية

        Args:
            value_id: معرف القيمة
            permanent_delete: حذف فعلي من قاعدة البيانات (افتراضي: True)

        Returns:
            True إذا تم الحذف بنجاح
        """
        try:
            db_value = self.db.query(VariantValue).filter(
                VariantValue.id == value_id
            ).first()

            if not db_value:
                raise HTTPException(status_code=404, detail="القيمة غير موجودة")

            if permanent_delete:
                # حذف فعلي من قاعدة البيانات
                self.db.delete(db_value)
            else:
                # Soft delete - تعطيل القيمة بدلاً من حذفها
                db_value.is_active = False

            self.db.commit()

            return True

        except HTTPException:
            self.db.rollback()
            raise
        except Exception as error:
            self.db.rollback()
            print(f"خطأ في حذف القيمة {value_id}: {error}")
            raise HTTPException(status_code=500, detail="فشل في حذف القيمة")

    async def reorder_attribute_values(self, value_orders: List[ValueOrderUpdate]) -> bool:
        """
        إعادة ترتيب قيم الخصائص

        Args:
            value_orders: قائمة بترتيب القيم الجديد

        Returns:
            True إذا تم التحديث بنجاح
        """
        try:
            for order_update in value_orders:
                self.db.query(VariantValue).filter(
                    VariantValue.id == order_update.id
                ).update({"sort_order": order_update.sort_order})

            self.db.commit()

            return True

        except Exception as error:
            self.db.rollback()
            print(f"خطأ في إعادة ترتيب قيم الخصائص: {error}")
            raise HTTPException(status_code=500, detail="فشل في إعادة ترتيب قيم الخصائص")
