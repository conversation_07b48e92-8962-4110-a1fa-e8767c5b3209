"""
خدمة إدارة إعادة التوجيه الذكية - لحفظ واسترجاع الروابط الأصلية
"""

import json
import logging
from pathlib import Path
from typing import Dict, Optional, Any
from datetime import datetime, timedelta
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse
import threading

logger = logging.getLogger(__name__)


class RedirectManager:
    """
    مدير إعادة التوجيه الذكي - يحفظ الروابط الأصلية ويعيد توجيه المستخدمين إليها
    """

    def __init__(self):
        self.redirect_file = Path("backend/config/pending_redirects.json")
        self.lock = threading.Lock()
        self._ensure_file_exists()

    def _ensure_file_exists(self):
        """التأكد من وجود ملف إعادة التوجيه"""
        try:
            self.redirect_file.parent.mkdir(parents=True, exist_ok=True)
            if not self.redirect_file.exists():
                with open(self.redirect_file, 'w', encoding='utf-8') as f:
                    json.dump({}, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"خطأ في إنشاء ملف إعادة التوجيه: {e}")

    def save_original_url(self, device_id: str, original_url: str, client_ip: Optional[str] = None) -> bool:
        """
        حفظ الرابط الأصلي للجهاز
        """
        try:
            with self.lock:
                # قراءة البيانات الحالية
                redirects = self._load_redirects()

                # تنظيف الرابط الأصلي
                cleaned_url = self._clean_url(original_url)

                # حفظ بيانات إعادة التوجيه
                redirects[device_id] = {
                    "original_url": cleaned_url,
                    "client_ip": client_ip,
                    "saved_at": datetime.now().isoformat(),
                    "expires_at": (datetime.now() + timedelta(hours=24)).isoformat()
                }

                # حفظ البيانات
                with open(self.redirect_file, 'w', encoding='utf-8') as f:
                    json.dump(redirects, f, ensure_ascii=False, indent=2)

                logger.info(f"تم حفظ الرابط الأصلي للجهاز {device_id}: {cleaned_url}")
                return True

        except Exception as e:
            logger.error(f"خطأ في حفظ الرابط الأصلي: {e}")
            return False

    def get_original_url(self, device_id: str) -> Optional[str]:
        """
        استرجاع الرابط الأصلي للجهاز
        """
        try:
            with self.lock:
                redirects = self._load_redirects()

                if device_id not in redirects:
                    logger.debug(f"لا يوجد رابط محفوظ للجهاز {device_id}")
                    return None

                redirect_data = redirects[device_id]

                # فحص انتهاء الصلاحية
                expires_at = datetime.fromisoformat(redirect_data["expires_at"])
                if datetime.now() > expires_at:
                    logger.info(f"انتهت صلاحية الرابط المحفوظ للجهاز {device_id}")
                    del redirects[device_id]
                    self._save_redirects(redirects)
                    return None

                original_url = redirect_data["original_url"]
                logger.info(f"تم استرجاع الرابط الأصلي للجهاز {device_id}: {original_url}")
                return original_url

        except Exception as e:
            logger.error(f"خطأ في استرجاع الرابط الأصلي: {e}")
            return None

    def remove_redirect(self, device_id: str) -> bool:
        """
        إزالة بيانات إعادة التوجيه للجهاز
        """
        try:
            with self.lock:
                redirects = self._load_redirects()

                if device_id in redirects:
                    del redirects[device_id]
                    self._save_redirects(redirects)
                    logger.info(f"تم حذف بيانات إعادة التوجيه للجهاز {device_id}")
                    return True

                return False

        except Exception as e:
            logger.error(f"خطأ في حذف بيانات إعادة التوجيه: {e}")
            return False

    def cleanup_expired_redirects(self) -> int:
        """
        تنظيف الروابط منتهية الصلاحية
        """
        try:
            with self.lock:
                redirects = self._load_redirects()
                current_time = datetime.now()
                expired_count = 0

                # البحث عن الروابط منتهية الصلاحية
                expired_devices = []
                for device_id, redirect_data in redirects.items():
                    expires_at = datetime.fromisoformat(redirect_data["expires_at"])
                    if current_time > expires_at:
                        expired_devices.append(device_id)

                # حذف الروابط منتهية الصلاحية
                for device_id in expired_devices:
                    del redirects[device_id]
                    expired_count += 1

                if expired_count > 0:
                    self._save_redirects(redirects)
                    logger.info(f"تم تنظيف {expired_count} رابط منتهي الصلاحية")

                return expired_count

        except Exception as e:
            logger.error(f"خطأ في تنظيف الروابط منتهية الصلاحية: {e}")
            return 0

    def get_smart_redirect_url(self, device_id: str, fallback_url: Optional[str] = None) -> str:
        """
        الحصول على رابط إعادة التوجيه الذكي
        """
        try:
            # محاولة استرجاع الرابط الأصلي
            original_url = self.get_original_url(device_id)

            if original_url:
                # التحقق من صحة الرابط
                if self._is_valid_redirect_url(original_url):
                    return original_url

            # استخدام الرابط الاحتياطي أو الافتراضي
            if fallback_url and self._is_valid_redirect_url(fallback_url):
                return fallback_url

            # الرابط الافتراضي باستخدام مدير الروابط الديناميكي
            try:
                from utils.url_manager import url_manager
                return url_manager.get_default_redirect_url()
            except Exception as url_error:
                logger.error(f"خطأ في الحصول على الرابط الافتراضي: {url_error}")
                return "http://192.168.1.110:5175/"

        except Exception as e:
            logger.error(f"خطأ في الحصول على رابط إعادة التوجيه الذكي: {e}")
            try:
                from utils.url_manager import url_manager
                return url_manager.get_default_redirect_url()
            except Exception as url_error:
                logger.error(f"خطأ في الحصول على الرابط الافتراضي: {url_error}")
                return "http://192.168.1.110:5175/"

    def _load_redirects(self) -> Dict[str, Any]:
        """قراءة بيانات إعادة التوجيه"""
        try:
            if self.redirect_file.exists():
                with open(self.redirect_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"خطأ في قراءة ملف إعادة التوجيه: {e}")
            return {}

    def _save_redirects(self, redirects: Dict[str, Any]):
        """حفظ بيانات إعادة التوجيه"""
        try:
            with open(self.redirect_file, 'w', encoding='utf-8') as f:
                json.dump(redirects, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"خطأ في حفظ ملف إعادة التوجيه: {e}")

    def _clean_url(self, url: str) -> str:
        """تنظيف وتحسين الرابط"""
        try:
            parsed = urlparse(url)

            # إزالة معاملات غير مرغوب فيها
            query_params = parse_qs(parsed.query)

            # قائمة المعاملات المسموحة
            allowed_params = ['page', 'tab', 'id', 'view', 'filter', 'search']

            # تنظيف المعاملات
            cleaned_params = {}
            for key, values in query_params.items():
                if key in allowed_params and values:
                    cleaned_params[key] = values[0]

            # إعادة بناء الرابط
            cleaned_query = urlencode(cleaned_params)
            cleaned_url = urlunparse((
                parsed.scheme,
                parsed.netloc,
                parsed.path,
                parsed.params,
                cleaned_query,
                ''  # إزالة fragment
            ))

            return cleaned_url

        except Exception as e:
            logger.error(f"خطأ في تنظيف الرابط: {e}")
            return url

    def _is_valid_redirect_url(self, url: str) -> bool:
        """التحقق من صحة رابط إعادة التوجيه باستخدام مدير الروابط الديناميكي"""
        try:
            from utils.url_manager import url_manager
            return url_manager.is_valid_redirect_url(url)

        except Exception as e:
            logger.error(f"خطأ في التحقق من صحة الرابط: {e}")
            # fallback للتحقق الأساسي
            try:
                parsed = urlparse(url)
                return bool(parsed.scheme and parsed.netloc)
            except:
                return False

    def get_redirect_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات إعادة التوجيه"""
        try:
            redirects = self._load_redirects()
            current_time = datetime.now()

            total_redirects = len(redirects)
            active_redirects = 0
            expired_redirects = 0

            for redirect_data in redirects.values():
                expires_at = datetime.fromisoformat(redirect_data["expires_at"])
                if current_time <= expires_at:
                    active_redirects += 1
                else:
                    expired_redirects += 1

            return {
                "total_redirects": total_redirects,
                "active_redirects": active_redirects,
                "expired_redirects": expired_redirects,
                "file_path": str(self.redirect_file)
            }

        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات إعادة التوجيه: {e}")
            return {"error": str(e)}


# إنشاء instance عام للاستخدام
redirect_manager = RedirectManager()
