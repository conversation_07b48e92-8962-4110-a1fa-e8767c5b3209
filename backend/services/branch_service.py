"""
خدمة إدارة الفروع - Branch Service
تطبق مبادئ البرمجة الكائنية (OOP) لإدارة جميع عمليات الفروع
"""

import logging
from typing import Dict, Any
from uuid import UUID, uuid4
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from sqlalchemy import and_, or_

from models.branch import Branch
from utils.datetime_utils import get_tripoli_now, get_current_time_with_settings, create_timestamp_with_settings

logger = logging.getLogger(__name__)


def get_branch_service(db_session: Session) -> 'BranchService':
    """إنشاء مثيل جديد من خدمة الفروع"""
    return BranchService(db_session)


class BranchService:
    """
    خدمة إدارة الفروع - تطبق مبادئ OOP

    تتعامل مع جميع عمليات الفروع:
    - إنشاء فرع جديد
    - تحديث بيانات الفرع
    - حذف الفرع
    - استعلام الفروع
    - إدارة حالة الفرع الرئيسي
    """

    def __init__(self, db_session: Session):
        self.db_session = db_session

    # تم نقل get_instance إلى دالة خارجية لتجنب مشاكل Type Checking
    
    def create_branch(self, branch_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        إنشاء فرع جديد
        
        Args:
            branch_data: بيانات الفرع الجديد
            
        Returns:
            نتيجة العملية مع بيانات الفرع المنشأ
        """
        try:
            logger.info(f"🏢 إنشاء فرع جديد: {branch_data.get('name')}")
            
            # توليد UUID إذا لم يتم تمريره
            branch_uuid = branch_data.get('uuid')
            if not branch_uuid:
                branch_uuid = uuid4()

            # التحقق من عدم وجود فرع بنفس UUID
            existing_uuid = self.db_session.query(Branch).filter(
                Branch.uuid == branch_uuid
            ).first()

            if existing_uuid:
                return {
                    'success': False,
                    'error': f'يوجد فرع بنفس المعرف الفريد'
                }

            # التحقق من عدم وجود فرع بنفس الكود (إذا تم تمريره)
            if branch_data.get('code'):
                existing_branch = self.db_session.query(Branch).filter(
                    Branch.code == branch_data.get('code')
                ).first()

                if existing_branch:
                    return {
                        'success': False,
                        'error': f'يوجد فرع بنفس الكود: {branch_data.get("code")}'
                    }
            
            # التحقق من عدم وجود فرع بنفس الاسم
            existing_name = self.db_session.query(Branch).filter(
                Branch.name == branch_data.get('name')
            ).first()
            
            if existing_name:
                return {
                    'success': False,
                    'error': f'يوجد فرع بنفس الاسم: {branch_data.get("name")}'
                }
            
            # إنشاء الفرع الجديد
            branch = Branch(
                uuid=branch_uuid,
                name=branch_data.get('name'),
                code=branch_data.get('code'),
                address=branch_data.get('address'),
                phone=branch_data.get('phone'),
                manager_name=branch_data.get('manager_name'),
                email=branch_data.get('email'),
                is_active=branch_data.get('is_active', True),
                is_main=branch_data.get('is_main', False),
                city=branch_data.get('city'),
                region=branch_data.get('region'),
                postal_code=branch_data.get('postal_code'),
                max_daily_sales=branch_data.get('max_daily_sales'),
                working_hours_start=branch_data.get('working_hours_start'),
                working_hours_end=branch_data.get('working_hours_end'),
                created_at=get_current_time_with_settings(self.db_session),
                updated_at=get_current_time_with_settings(self.db_session),
                created_by=branch_data.get('created_by')
            )
            
            # إذا كان الفرع الرئيسي، تأكد من عدم وجود فرع رئيسي آخر
            if branch_data.get('is_main'):
                self.db_session.query(Branch).filter(
                    Branch.is_main == True
                ).update({'is_main': False})
            
            self.db_session.add(branch)
            self.db_session.commit()
            self.db_session.refresh(branch)
            
            logger.info(f"✅ تم إنشاء الفرع بنجاح: {branch.name} (ID: {branch.id})")
            
            return {
                'success': True,
                'message': 'تم إنشاء الفرع بنجاح',
                'branch': {
                    'id': branch.id,
                    'uuid': str(branch.uuid),
                    'name': branch.name,
                    'code': branch.code,
                    'address': branch.address,
                    'phone': branch.phone,
                    'manager_name': branch.manager_name,
                    'email': branch.email,
                    'is_active': branch.is_active,
                    'is_main': branch.is_main,
                    'city': branch.city,
                    'region': branch.region,
                    'postal_code': branch.postal_code,
                    'max_daily_sales': branch.max_daily_sales,
                    'working_hours_start': branch.working_hours_start,
                    'working_hours_end': branch.working_hours_end,
                    'created_at': branch.created_at.isoformat() if branch.created_at is not None else None,
                    'updated_at': branch.updated_at.isoformat() if branch.updated_at is not None else None
                }
            }
            
        except IntegrityError as e:
            self.db_session.rollback()
            logger.error(f"❌ خطأ في تكامل البيانات عند إنشاء الفرع: {e}")
            return {
                'success': False,
                'error': 'خطأ في تكامل البيانات - تأكد من صحة البيانات المدخلة'
            }
        except SQLAlchemyError as e:
            self.db_session.rollback()
            logger.error(f"❌ خطأ في قاعدة البيانات عند إنشاء الفرع: {e}")
            return {
                'success': False,
                'error': f'خطأ في قاعدة البيانات: {str(e)}'
            }
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"❌ خطأ عام عند إنشاء الفرع: {e}")
            return {
                'success': False,
                'error': f'خطأ عام في إنشاء الفرع: {str(e)}'
            }
    
    def update_branch(self, branch_id: int, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        تحديث بيانات الفرع
        
        Args:
            branch_id: معرف الفرع
            update_data: البيانات المحدثة
            
        Returns:
            نتيجة العملية مع بيانات الفرع المحدث
        """
        try:
            logger.info(f"🔄 تحديث الفرع: {branch_id}")
            
            branch = self.db_session.query(Branch).filter(
                Branch.id == branch_id
            ).first()
            
            if not branch:
                return {
                    'success': False,
                    'error': 'الفرع غير موجود'
                }
            
            # التحقق من عدم تكرار الكود إذا تم تحديثه
            if 'code' in update_data and update_data['code'] != branch.code:
                existing_code = self.db_session.query(Branch).filter(
                    and_(Branch.code == update_data['code'], Branch.id != branch_id)
                ).first()
                
                if existing_code:
                    return {
                        'success': False,
                        'error': f'يوجد فرع آخر بنفس الكود: {update_data["code"]}'
                    }
            
            # التحقق من عدم تكرار الاسم إذا تم تحديثه
            if 'name' in update_data and update_data['name'] != branch.name:
                existing_name = self.db_session.query(Branch).filter(
                    and_(Branch.name == update_data['name'], Branch.id != branch_id)
                ).first()
                
                if existing_name:
                    return {
                        'success': False,
                        'error': f'يوجد فرع آخر بنفس الاسم: {update_data["name"]}'
                    }
            
            # إذا كان الفرع سيصبح رئيسياً، إلغاء الرئيسية من الفروع الأخرى
            if update_data.get('is_main') and not branch.is_main:
                self.db_session.query(Branch).filter(
                    Branch.is_main == True
                ).update({'is_main': False})
            
            # تحديث البيانات
            for key, value in update_data.items():
                if hasattr(branch, key):
                    setattr(branch, key, value)
            
            branch.updated_at = get_current_time_with_settings(self.db_session)
            if 'updated_by' in update_data:
                branch.updated_by = update_data['updated_by']
            
            self.db_session.commit()
            self.db_session.refresh(branch)
            
            logger.info(f"✅ تم تحديث الفرع بنجاح: {branch.name}")
            
            return {
                'success': True,
                'message': 'تم تحديث الفرع بنجاح',
                'branch': {
                    'id': branch.id,
                    'name': branch.name,
                    'code': branch.code,
                    'address': branch.address,
                    'phone': branch.phone,
                    'manager_name': branch.manager_name,
                    'email': branch.email,
                    'is_active': branch.is_active,
                    'is_main': branch.is_main,
                    'city': branch.city,
                    'region': branch.region,
                    'postal_code': branch.postal_code,
                    'max_daily_sales': branch.max_daily_sales,
                    'working_hours_start': branch.working_hours_start,
                    'working_hours_end': branch.working_hours_end,
                    'created_at': branch.created_at.isoformat() if branch.created_at is not None else None,
                    'updated_at': branch.updated_at.isoformat() if branch.updated_at is not None else None
                }
            }
            
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"❌ خطأ في تحديث الفرع: {e}")
            return {
                'success': False,
                'error': f'خطأ في تحديث الفرع: {str(e)}'
            }

    def delete_branch(self, branch_id: int) -> Dict[str, Any]:
        """
        حذف الفرع

        Args:
            branch_id: معرف الفرع

        Returns:
            نتيجة العملية
        """
        try:
            logger.info(f"🗑️ حذف الفرع: {branch_id}")

            branch = self.db_session.query(Branch).filter(
                Branch.id == branch_id
            ).first()

            if not branch:
                return {
                    'success': False,
                    'error': 'الفرع غير موجود'
                }

            # التحقق من عدم وجود مستودعات مرتبطة بالفرع
            if branch.warehouses:
                return {
                    'success': False,
                    'error': f'لا يمكن حذف الفرع لأنه مرتبط بـ {len(branch.warehouses)} مستودع. يرجى إلغاء الربط أولاً.'
                }

            # التحقق من عدم كون الفرع رئيسياً
            if branch.is_main:
                return {
                    'success': False,
                    'error': 'لا يمكن حذف الفرع الرئيسي'
                }

            branch_name = branch.name

            # حذف الفرع
            self.db_session.delete(branch)
            self.db_session.commit()

            logger.info(f"✅ تم حذف الفرع بنجاح: {branch_name}")

            return {
                'success': True,
                'message': 'تم حذف الفرع بنجاح'
            }

        except Exception as e:
            self.db_session.rollback()
            logger.error(f"❌ خطأ في حذف الفرع: {e}")
            return {
                'success': False,
                'error': f'خطأ في حذف الفرع: {str(e)}'
            }

    def get_all_branches(self, include_inactive: bool = False) -> Dict[str, Any]:
        """
        الحصول على جميع الفروع

        Args:
            include_inactive: هل تشمل الفروع غير النشطة

        Returns:
            قائمة الفروع
        """
        try:
            query = self.db_session.query(Branch)

            if not include_inactive:
                query = query.filter(Branch.is_active == True)

            branches = query.order_by(Branch.is_main.desc(), Branch.name).all()

            branches_data = []
            for branch in branches:
                branches_data.append({
                    'id': branch.id,
                    'uuid': str(branch.uuid),
                    'name': branch.name,
                    'code': branch.code,
                    'address': branch.address,
                    'phone': branch.phone,
                    'manager_name': branch.manager_name,
                    'email': branch.email,
                    'is_active': branch.is_active,
                    'is_main': branch.is_main,
                    'city': branch.city,
                    'region': branch.region,
                    'postal_code': branch.postal_code,
                    'max_daily_sales': branch.max_daily_sales,
                    'working_hours_start': branch.working_hours_start,
                    'working_hours_end': branch.working_hours_end,
                    'warehouses_count': branch.total_warehouses_count,
                    'active_warehouses_count': branch.active_warehouses_count,
                    'created_at': branch.created_at.isoformat() if branch.created_at is not None else None,
                    'updated_at': branch.updated_at.isoformat() if branch.updated_at is not None else None
                })

            return {
                'success': True,
                'branches': branches_data,
                'total_count': len(branches_data)
            }

        except Exception as e:
            logger.error(f"❌ خطأ في جلب الفروع: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب الفروع: {str(e)}'
            }

    def get_branch_by_id(self, branch_id: int) -> Dict[str, Any]:
        """
        الحصول على فرع بالمعرف

        Args:
            branch_id: معرف الفرع

        Returns:
            بيانات الفرع
        """
        try:
            branch = self.db_session.query(Branch).filter(
                Branch.id == branch_id
            ).first()

            if not branch:
                return {
                    'success': False,
                    'error': 'الفرع غير موجود'
                }

            return {
                'success': True,
                'branch': {
                    'id': branch.id,
                    'uuid': str(branch.uuid),
                    'name': branch.name,
                    'code': branch.code,
                    'address': branch.address,
                    'phone': branch.phone,
                    'manager_name': branch.manager_name,
                    'email': branch.email,
                    'is_active': branch.is_active,
                    'is_main': branch.is_main,
                    'city': branch.city,
                    'region': branch.region,
                    'postal_code': branch.postal_code,
                    'max_daily_sales': branch.max_daily_sales,
                    'working_hours_start': branch.working_hours_start,
                    'working_hours_end': branch.working_hours_end,
                    'warehouses_count': branch.total_warehouses_count,
                    'active_warehouses_count': branch.active_warehouses_count,
                    'warehouses': [
                        {
                            'id': w.id,
                            'name': w.name,
                            'code': w.code,
                            'is_main': w.is_main,
                            'is_active': w.is_active
                        } for w in branch.warehouses
                    ],
                    'created_at': branch.created_at.isoformat() if branch.created_at is not None else None,
                    'updated_at': branch.updated_at.isoformat() if branch.updated_at is not None else None
                }
            }

        except Exception as e:
            logger.error(f"❌ خطأ في جلب الفرع: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب الفرع: {str(e)}'
            }

    def get_branch_by_code(self, branch_code: str) -> Dict[str, Any]:
        """
        الحصول على فرع بالكود

        Args:
            branch_code: كود الفرع

        Returns:
            بيانات الفرع
        """
        try:
            branch = self.db_session.query(Branch).filter(
                Branch.code == branch_code
            ).first()

            if not branch:
                return {
                    'success': False,
                    'error': 'الفرع غير موجود'
                }

            return self.get_branch_by_id(branch.id)

        except Exception as e:
            logger.error(f"❌ خطأ في جلب الفرع بالكود: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب الفرع: {str(e)}'
            }

    def get_branch_by_uuid(self, branch_uuid: UUID) -> Dict[str, Any]:
        """
        الحصول على فرع بـ UUID

        Args:
            branch_uuid: UUID الفرع

        Returns:
            بيانات الفرع
        """
        try:
            branch = self.db_session.query(Branch).filter(
                Branch.uuid == branch_uuid
            ).first()

            if not branch:
                return {
                    'success': False,
                    'error': 'الفرع غير موجود'
                }

            return self.get_branch_by_id(branch.id)

        except Exception as e:
            logger.error(f"❌ خطأ في جلب الفرع بـ UUID: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب الفرع: {str(e)}'
            }

    def set_main_branch(self, branch_id: int) -> Dict[str, Any]:
        """
        تعيين الفرع الرئيسي

        Args:
            branch_id: معرف الفرع

        Returns:
            نتيجة العملية
        """
        try:
            branch = self.db_session.query(Branch).filter(
                Branch.id == branch_id
            ).first()

            if not branch:
                return {
                    'success': False,
                    'error': 'الفرع غير موجود'
                }

            if not branch.is_active:
                return {
                    'success': False,
                    'error': 'لا يمكن تعيين فرع غير نشط كرئيسي'
                }

            # إلغاء الرئيسية من جميع الفروع
            self.db_session.query(Branch).filter(
                Branch.is_main == True
            ).update({'is_main': False})

            # تعيين الفرع الجديد كرئيسي
            branch.is_main = True
            branch.updated_at = get_current_time_with_settings(self.db_session)

            self.db_session.commit()

            logger.info(f"✅ تم تعيين الفرع الرئيسي: {branch.name}")

            return {
                'success': True,
                'message': f'تم تعيين {branch.name} كفرع رئيسي'
            }

        except Exception as e:
            self.db_session.rollback()
            logger.error(f"❌ خطأ في تعيين الفرع الرئيسي: {e}")
            return {
                'success': False,
                'error': f'خطأ في تعيين الفرع الرئيسي: {str(e)}'
            }

    def toggle_branch_status(self, branch_id: int) -> Dict[str, Any]:
        """
        تبديل حالة نشاط الفرع

        Args:
            branch_id: معرف الفرع

        Returns:
            نتيجة العملية
        """
        try:
            branch = self.db_session.query(Branch).filter(
                Branch.id == branch_id
            ).first()

            if not branch:
                return {
                    'success': False,
                    'error': 'الفرع غير موجود'
                }

            # لا يمكن إلغاء تفعيل الفرع الرئيسي
            if branch.is_main and branch.is_active:
                return {
                    'success': False,
                    'error': 'لا يمكن إلغاء تفعيل الفرع الرئيسي'
                }

            # تبديل الحالة
            branch.is_active = not branch.is_active
            branch.updated_at = get_current_time_with_settings(self.db_session)

            self.db_session.commit()

            status = "تم تفعيل" if branch.is_active else "تم إلغاء تفعيل"
            logger.info(f"✅ {status} الفرع: {branch.name}")

            return {
                'success': True,
                'message': f'{status} الفرع بنجاح',
                'is_active': branch.is_active
            }

        except Exception as e:
            self.db_session.rollback()
            logger.error(f"❌ خطأ في تبديل حالة الفرع: {e}")
            return {
                'success': False,
                'error': f'خطأ في تبديل حالة الفرع: {str(e)}'
            }

    def search_branches(self, search_term: str, include_inactive: bool = False) -> Dict[str, Any]:
        """
        البحث في الفروع

        Args:
            search_term: مصطلح البحث
            include_inactive: هل تشمل الفروع غير النشطة

        Returns:
            نتائج البحث
        """
        try:
            query = self.db_session.query(Branch)

            if not include_inactive:
                query = query.filter(Branch.is_active == True)

            # البحث في الاسم والكود والعنوان ومدير الفرع
            search_filter = or_(
                Branch.name.ilike(f'%{search_term}%'),
                Branch.code.ilike(f'%{search_term}%'),
                Branch.address.ilike(f'%{search_term}%'),
                Branch.manager_name.ilike(f'%{search_term}%'),
                Branch.city.ilike(f'%{search_term}%'),
                Branch.region.ilike(f'%{search_term}%')
            )

            branches = query.filter(search_filter).order_by(Branch.name).all()

            branches_data = []
            for branch in branches:
                branches_data.append({
                    'id': branch.id,
                    'uuid': str(branch.uuid),
                    'name': branch.name,
                    'code': branch.code,
                    'address': branch.address,
                    'phone': branch.phone,
                    'manager_name': branch.manager_name,
                    'email': branch.email,
                    'is_active': branch.is_active,
                    'is_main': branch.is_main,
                    'city': branch.city,
                    'region': branch.region,
                    'warehouses_count': branch.total_warehouses_count,
                    'active_warehouses_count': branch.active_warehouses_count
                })

            return {
                'success': True,
                'branches': branches_data,
                'total_count': len(branches_data),
                'search_term': search_term
            }

        except Exception as e:
            logger.error(f"❌ خطأ في البحث في الفروع: {e}")
            return {
                'success': False,
                'error': f'خطأ في البحث: {str(e)}'
            }
"""
خدمة إدارة الفروع - Branch Service
تطبق مبادئ البرمجة الكائنية (OOP) لإدارة جميع عمليات الفروع
"""
