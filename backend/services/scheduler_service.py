"""
خدمة جدولة المهام باستخدام APScheduler
"""

import json
import logging
from datetime import timedelta
from typing import Optional, Dict, Any, List
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from apscheduler.executors.pool import ThreadPoolExecutor
from sqlalchemy.orm import Session

from database.session import get_db
from models.scheduled_task import ScheduledTask, TaskType, TaskStatus
from utils.backup import DatabaseBackup
from utils.datetime_utils import get_tripoli_now
from services.google_drive_service import GoogleDriveService

logger = logging.getLogger(__name__)

class SchedulerService:
    """خدمة جدولة المهام"""

    def __init__(self):
        self.scheduler = None
        self._setup_scheduler()

    def _setup_scheduler(self):
        """إعداد المجدول"""
        try:
            logger.info("بدء إعداد مجدول المهام...")

            # إعداد job store لحفظ المهام في قاعدة البيانات
            from database.session import DATABASE_URL

            # إعداد job store
            jobstores = {
                'default': SQLAlchemyJobStore(url=DATABASE_URL)
            }

            # إعداد executors
            executors = {
                'default': ThreadPoolExecutor(20),
            }

            # إعداد job defaults
            job_defaults = {
                'coalesce': False,
                'max_instances': 3
            }

            # إنشاء المجدول مع الإعدادات المحسنة
            logger.info("إنشاء BackgroundScheduler مع SQLAlchemy job store...")
            self.scheduler = BackgroundScheduler(
                jobstores=jobstores,
                executors=executors,
                job_defaults=job_defaults,
                timezone='Africa/Tripoli'
            )

            logger.info(f"تم إعداد مجدول المهام بنجاح: {type(self.scheduler)}")

        except Exception as e:
            import traceback
            logger.error(f"خطأ في إعداد مجدول المهام: {e}")
            logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
            # في حالة فشل الإعداد المتقدم، استخدم الإعداد البسيط
            try:
                logger.info("محاولة إعداد مجدول بسيط...")
                self.scheduler = BackgroundScheduler()
                logger.info("تم إعداد مجدول بسيط بنجاح")
            except Exception as e2:
                logger.error(f"فشل في إعداد المجدول البسيط أيضاً: {e2}")
                self.scheduler = None

    def start(self):
        """بدء المجدول"""
        try:
            if self.scheduler is None:
                logger.error("المجدول غير مُعد بشكل صحيح")
                return

            if not self.scheduler.running:
                self.scheduler.start()
                logger.info("تم بدء مجدول المهام")

                # تحميل المهام الموجودة من قاعدة البيانات
                self._load_tasks_from_database()

        except Exception as e:
            logger.error(f"خطأ في بدء مجدول المهام: {e}")
            # لا نرفع الخطأ لتجنب توقف التطبيق

    def stop(self):
        """إيقاف المجدول"""
        try:
            if self.scheduler and self.scheduler.running:
                self.scheduler.shutdown(wait=False)
                logger.info("تم إيقاف مجدول المهام")
        except Exception as e:
            logger.error(f"خطأ في إيقاف مجدول المهام: {e}")

    def _load_tasks_from_database(self):
        """تحميل المهام من قاعدة البيانات"""
        try:
            db_gen = get_db()
            db = next(db_gen)

            try:
                # جلب المهام النشطة
                active_tasks = db.query(ScheduledTask).filter(
                    ScheduledTask.status == TaskStatus.ACTIVE
                ).all()

                for task in active_tasks:
                    self._add_job_to_scheduler(task)

                logger.info(f"تم تحميل {len(active_tasks)} مهمة من قاعدة البيانات")

            finally:
                db.close()

        except Exception as e:
            logger.error(f"خطأ في تحميل المهام من قاعدة البيانات: {e}")

    def _add_job_to_scheduler(self, task: ScheduledTask):
        """إضافة مهمة إلى المجدول"""
        try:
            if self.scheduler is None:
                logger.error("المجدول غير متاح لإضافة المهمة")
                return

            # إنشاء trigger من تعبير Cron
            trigger = CronTrigger.from_crontab(task.cron_expression, timezone='Africa/Tripoli')

            # إضافة المهمة إلى المجدول
            self.scheduler.add_job(
                func=self._execute_task,
                trigger=trigger,
                args=[task.id],
                id=f"task_{task.id}",
                name=task.name,
                replace_existing=True,
                max_instances=1
            )

            # تحديث وقت التنفيذ التالي
            task_id = getattr(task, 'id', 0)
            self._update_next_run_time(task_id)

            logger.info(f"تم إضافة المهمة '{task.name}' إلى المجدول")

        except Exception as e:
            logger.error(f"خطأ في إضافة المهمة '{task.name}' إلى المجدول: {e}")

    def _execute_task(self, task_id: int):
        """تنفيذ مهمة"""
        start_time = get_tripoli_now()
        db_gen = get_db()
        db = next(db_gen)

        try:
            # جلب المهمة من قاعدة البيانات
            task = db.query(ScheduledTask).filter(ScheduledTask.id == task_id).first()
            if not task:
                logger.error(f"المهمة {task_id} غير موجودة")
                return

            logger.info(f"بدء تنفيذ المهمة: {task.name}")

            # تنفيذ المهمة حسب نوعها
            result = self._execute_task_by_type(task, db)

            # حساب وقت التنفيذ
            execution_time = (get_tripoli_now() - start_time).total_seconds()

            # تحديث معلومات المهمة
            task.last_run = get_tripoli_now()
            task.run_count += 1

            if result['success']:
                task.last_error = None
                logger.info(f"تم تنفيذ المهمة '{task.name}' بنجاح في {execution_time:.2f} ثانية")
            else:
                task.failure_count += 1
                task.last_error = result.get('error', 'خطأ غير معروف')
                logger.error(f"فشل في تنفيذ المهمة '{task.name}': {task.last_error}")

            # تحديث وقت التنفيذ التالي
            self._update_next_run_time(task_id)

            db.commit()

        except Exception as e:
            logger.error(f"خطأ في تنفيذ المهمة {task_id}: {e}")
            if task:
                task.failure_count += 1
                task.last_error = str(e)
                db.commit()
        finally:
            db.close()

    def _execute_task_by_type(self, task: ScheduledTask, db: Session) -> Dict[str, Any]:
        """تنفيذ المهمة حسب نوعها"""
        try:
            # الحصول على قيمة نوع المهمة الصحيحة
            task_type = task.task_type

            logger.info(f"تنفيذ مهمة من نوع: {task_type}")

            # مقارنة مع enum values مباشرة
            if task_type.value == TaskType.DATABASE_BACKUP.value:
                return self._execute_database_backup(task, db)
            elif task_type.value == TaskType.CLEANUP_OLD_BACKUPS.value:
                return self._execute_cleanup_old_backups(task, db)
            elif task_type.value == TaskType.GOOGLE_DRIVE_BACKUP.value:
                return self._execute_google_drive_backup(task, db)
            elif task_type.value == TaskType.GOOGLE_DRIVE_CLEANUP.value:
                return self._execute_google_drive_cleanup(task, db)
            elif task_type.value == TaskType.CLEANUP_INACTIVE_DEVICES.value:
                return self._execute_cleanup_inactive_devices(task, db)
            else:
                return {
                    'success': False,
                    'error': f'نوع المهمة غير مدعوم: {task_type}'
                }
        except Exception as e:
            logger.error(f"خطأ في تنفيذ المهمة: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _execute_database_backup(self, task: ScheduledTask, db: Session) -> Dict[str, Any]:
        """تنفيذ نسخ احتياطي لقاعدة البيانات"""
        try:
            # جلب معاملات المهمة
            params = {}
            task_params_str = getattr(task, 'task_params', None)
            if task_params_str:
                params = json.loads(task_params_str)

            # PostgreSQL لا يحتاج مسار ملف محلي للنسخ الاحتياطي
            import os

            logger.info("إنشاء نسخة احتياطية PostgreSQL")

            # إنشاء نسخة احتياطية PostgreSQL
            backup_service = DatabaseBackup(db_path=None, db_session=db)
            result = backup_service.create_backup()

            return {
                'success': result['success'],
                'message': result.get('message', ''),
                'error': result.get('error') if not result['success'] else None,
                'details': {
                    'backup_path': result.get('backup_path'),
                    'backup_size': result.get('size')
                }
            }

        except Exception as e:
            logger.error(f"خطأ في النسخ الاحتياطي: {e}")
            return {
                'success': False,
                'error': f'خطأ في النسخ الاحتياطي: {str(e)}'
            }

    def _execute_cleanup_old_backups(self, task: ScheduledTask, db: Session) -> Dict[str, Any]:
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            # جلب معاملات المهمة
            params = {}
            task_params_str = getattr(task, 'task_params', None)
            if task_params_str:
                params = json.loads(task_params_str)

            keep_count = params.get('keep_count', 30)  # الاحتفاظ بـ 30 نسخة افتراضياً

            # PostgreSQL لا يحتاج مسار ملف محلي للتنظيف
            import os

            logger.info("تنظيف النسخ الاحتياطية PostgreSQL القديمة")

            # تنظيف النسخ القديمة
            backup_service = DatabaseBackup(db_path=None, db_session=db)
            result = backup_service.cleanup_old_backups(keep_count=keep_count)

            return {
                'success': result['success'],
                'message': result.get('message', ''),
                'error': result.get('error') if not result['success'] else None
            }

        except Exception as e:
            logger.error(f"خطأ في تنظيف النسخ القديمة: {e}")
            return {
                'success': False,
                'error': f'خطأ في تنظيف النسخ القديمة: {str(e)}'
            }

    def _execute_google_drive_backup(self, task: ScheduledTask, db: Session) -> Dict[str, Any]:
        """تنفيذ نسخ احتياطي إلى Google Drive"""
        try:
            # جلب معاملات المهمة
            params = {}
            task_params_str = getattr(task, 'task_params', None)
            if task_params_str:
                params = json.loads(task_params_str)

            # إنشاء خدمة Google Drive
            drive_service = GoogleDriveService(db_session=db)

            # التحقق من التكوين والتفعيل
            if not drive_service.is_available():
                return {
                    'success': False,
                    'error': 'مكتبات Google APIs غير متوفرة'
                }

            if not drive_service.is_enabled():
                return {
                    'success': False,
                    'error': 'خدمة Google Drive غير مفعلة'
                }

            if not drive_service.is_configured():
                return {
                    'success': False,
                    'error': 'خدمة Google Drive غير مكونة'
                }

            # إنشاء نسخة احتياطية محلية أولاً
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            backend_dir = os.path.dirname(current_dir)
            db_path = os.path.join(backend_dir, "smartpos.db")

            backup_service = DatabaseBackup(db_path=db_path, db_session=db)
            backup_result = backup_service.create_backup()

            if not backup_result['success']:
                return {
                    'success': False,
                    'error': f'فشل في إنشاء النسخة الاحتياطية المحلية: {backup_result.get("error")}'
                }

            # رفع النسخة الاحتياطية إلى Google Drive
            backup_path = backup_result['backup_path']
            upload_result = drive_service.upload_backup_file(backup_path)

            if upload_result['success']:
                # حذف النسخة المحلية إذا تم الرفع بنجاح (اختياري)
                delete_local = params.get('delete_local_after_upload', False)
                if delete_local:
                    try:
                        os.remove(backup_path)
                        logger.info(f"تم حذف النسخة المحلية: {backup_path}")
                    except Exception as e:
                        logger.warning(f"فشل في حذف النسخة المحلية: {e}")

            return {
                'success': upload_result['success'],
                'message': upload_result.get('message', ''),
                'error': upload_result.get('error') if not upload_result['success'] else None,
                'details': {
                    'local_backup_path': backup_path,
                    'drive_file_id': upload_result.get('file_id'),
                    'drive_file_name': upload_result.get('file_name')
                }
            }

        except Exception as e:
            logger.error(f"خطأ في النسخ الاحتياطي إلى Google Drive: {e}")
            return {
                'success': False,
                'error': f'خطأ في النسخ الاحتياطي إلى Google Drive: {str(e)}'
            }

    def _execute_google_drive_cleanup(self, task: ScheduledTask, db: Session) -> Dict[str, Any]:
        """تنظيف النسخ الاحتياطية القديمة من Google Drive"""
        try:
            # جلب معاملات المهمة
            params = {}
            task_params_str = getattr(task, 'task_params', None)
            if task_params_str:
                params = json.loads(task_params_str)

            keep_count = params.get('keep_count', 10)  # الاحتفاظ بـ 10 نسخ افتراضياً

            # إنشاء خدمة Google Drive
            drive_service = GoogleDriveService(db_session=db)

            # التحقق من التكوين والتفعيل
            if not drive_service.is_available():
                return {
                    'success': False,
                    'error': 'مكتبات Google APIs غير متوفرة'
                }

            if not drive_service.is_enabled():
                return {
                    'success': False,
                    'error': 'خدمة Google Drive غير مفعلة'
                }

            if not drive_service.is_configured():
                return {
                    'success': False,
                    'error': 'خدمة Google Drive غير مكونة'
                }

            # تنظيف النسخ القديمة
            result = drive_service.delete_old_backups(keep_count=keep_count)

            return {
                'success': result['success'],
                'message': result.get('message', ''),
                'error': result.get('error') if not result['success'] else None
            }

        except Exception as e:
            logger.error(f"خطأ في تنظيف النسخ الاحتياطية من Google Drive: {e}")
            return {
                'success': False,
                'error': f'خطأ في تنظيف النسخ الاحتياطية من Google Drive: {str(e)}'
            }

    def _execute_cleanup_inactive_devices(self, task: ScheduledTask, db: Session) -> Dict[str, Any]:
        """تنظيف الأجهزة غير النشطة"""
        try:
            # جلب معاملات المهمة
            params = {}
            task_params_str = getattr(task, 'task_params', None)
            if task_params_str:
                params = json.loads(task_params_str)

            # استخدام خدمة تنظيف الأجهزة
            from utils.device_cleanup import DeviceCleanupManager

            cleanup_service = DeviceCleanupManager()
            result = cleanup_service.cleanup_inactive_devices()

            return {
                'success': result['success'],
                'message': result.get('message', ''),
                'error': result.get('error') if not result['success'] else None,
                'details': {
                    'original_count': result.get('original_count', 0),
                    'final_count': result.get('final_count', 0),
                    'removed_count': result.get('removed_count', 0)
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تنظيف الأجهزة غير النشطة: {e}")
            return {
                'success': False,
                'error': f'خطأ في تنظيف الأجهزة غير النشطة: {str(e)}'
            }

    def _update_next_run_time(self, task_id: int):
        """تحديث وقت التنفيذ التالي للمهمة"""
        try:
            if self.scheduler is None:
                return

            job = self.scheduler.get_job(f"task_{task_id}")
            if job and job.next_run_time:
                db_gen = get_db()
                db = next(db_gen)
                try:
                    task = db.query(ScheduledTask).filter(ScheduledTask.id == task_id).first()
                    if task:
                        task.next_run = job.next_run_time
                        db.commit()
                finally:
                    db.close()
        except Exception as e:
            logger.error(f"خطأ في تحديث وقت التنفيذ التالي للمهمة {task_id}: {e}")

# إنشاء instance واحد من الخدمة
scheduler_service = SchedulerService()
