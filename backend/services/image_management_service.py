"""
خدمة إدارة الصور المتقدمة لنظام نقاط البيع الذكية
تطبق مبادئ البرمجة الكائنية مع دعم متعدد الاستخدامات
"""

import re
import logging
from typing import Optional, Dict, Any
from datetime import datetime
from pathlib import Path
from PIL import Image, ImageOps
from fastapi import UploadFile, HTTPException
from sqlalchemy.orm import Session

from utils.datetime_utils import get_tripoli_now

logger = logging.getLogger(__name__)


class ImageManagementService:
    """
    خدمة إدارة الصور المتقدمة
    تطبق مبادئ البرمجة الكائنية مع نمط Singleton
    """
    
    _instance: Optional['ImageManagementService'] = None
    
    # إعدادات الخدمة
    BASE_UPLOAD_DIR = "static/uploads"
    ALLOWED_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'}
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10 MB
    THUMBNAIL_SIZES = {
        'small': (150, 150),
        'medium': (300, 300),
        'large': (600, 600)
    }
    
    def __init__(self, db_session: Optional[Session] = None):
        """تهيئة خدمة إدارة الصور"""
        self.db_session = db_session
        self._ensure_upload_directories()
        logger.info("تم تهيئة خدمة إدارة الصور بنجاح")
    
    @classmethod
    def get_instance(cls, db_session: Optional[Session] = None) -> 'ImageManagementService':
        """الحصول على مثيل وحيد من الخدمة (Singleton Pattern)"""
        if cls._instance is None:
            cls._instance = cls(db_session)
        return cls._instance
    
    def _ensure_upload_directories(self) -> None:
        """التأكد من وجود مجلدات الرفع"""
        try:
            base_path = Path(self.BASE_UPLOAD_DIR)
            base_path.mkdir(parents=True, exist_ok=True)
            
            # إنشاء المجلدات الفرعية
            subdirs = ['products', 'categories', 'users', 'brands', 'customers', 'general']
            for subdir in subdirs:
                subdir_path = base_path / subdir
                subdir_path.mkdir(exist_ok=True)
                
                # إنشاء مجلدات الصور المصغرة
                for size in self.THUMBNAIL_SIZES.keys():
                    thumbnail_path = subdir_path / 'thumbnails' / size
                    thumbnail_path.mkdir(parents=True, exist_ok=True)
            
            logger.info("تم التأكد من وجود جميع مجلدات الرفع")
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء مجلدات الرفع: {e}")
            raise HTTPException(status_code=500, detail="فشل في تهيئة مجلدات الرفع")
    
    def validate_image_file(self, file: UploadFile) -> Dict[str, Any]:
        """التحقق من صحة ملف الصورة"""
        try:
            # التحقق من وجود الملف
            if not file or not file.filename:
                return {
                    "valid": False,
                    "error": "لم يتم تحديد ملف"
                }
            
            # التحقق من امتداد الملف
            file_extension = Path(file.filename).suffix.lower()
            if file_extension not in self.ALLOWED_EXTENSIONS:
                return {
                    "valid": False,
                    "error": f"امتداد الملف غير مدعوم. الامتدادات المدعومة: {', '.join(self.ALLOWED_EXTENSIONS)}"
                }
            
            # التحقق من حجم الملف
            file.file.seek(0, 2)  # الانتقال إلى نهاية الملف
            file_size = file.file.tell()
            file.file.seek(0)  # العودة إلى بداية الملف
            
            if file_size > self.MAX_FILE_SIZE:
                return {
                    "valid": False,
                    "error": f"حجم الملف كبير جداً. الحد الأقصى: {self.MAX_FILE_SIZE // (1024*1024)} MB"
                }
            
            if file_size == 0:
                return {
                    "valid": False,
                    "error": "الملف فارغ"
                }
            
            # التحقق من صحة الصورة
            try:
                file.file.seek(0)
                with Image.open(file.file) as img:
                    img.verify()
                file.file.seek(0)
                
                return {
                    "valid": True,
                    "file_size": file_size,
                    "file_extension": file_extension,
                    "message": "الملف صالح للرفع"
                }
                
            except Exception as img_error:
                return {
                    "valid": False,
                    "error": f"الملف ليس صورة صالحة: {str(img_error)}"
                }
                
        except Exception as e:
            logger.error(f"خطأ في التحقق من صحة الملف: {e}")
            return {
                "valid": False,
                "error": f"خطأ في التحقق من الملف: {str(e)}"
            }
    
    def _get_next_sequence_number(self, folder: str) -> int:
        """الحصول على الرقم التسلسلي التالي للمجلد (بدون إعادة استخدام)"""
        try:
            folder_path = Path(self.BASE_UPLOAD_DIR) / folder
            if not folder_path.exists():
                logger.info(f"📁 مجلد {folder} غير موجود، سيبدأ من 1")
                return 1

            # البحث عن أعلى رقم تسلسلي موجود (بما في ذلك المحذوفة)
            max_number = 0
            pattern = re.compile(rf"^{folder}_(\d+)\.")

            # فحص الملفات الموجودة
            for file_path in folder_path.iterdir():
                if file_path.is_file():
                    match = pattern.match(file_path.name)
                    if match:
                        number = int(match.group(1))
                        max_number = max(max_number, number)

            # فحص سجل الملفات المحذوفة (إذا وجد)
            deleted_log_path = folder_path / '.deleted_files.log'
            if deleted_log_path.exists():
                try:
                    with open(deleted_log_path, 'r', encoding='utf-8') as f:
                        for line in f:
                            line = line.strip()
                            if line:
                                match = pattern.match(line)
                                if match:
                                    number = int(match.group(1))
                                    max_number = max(max_number, number)
                except Exception as e:
                    logger.warning(f"خطأ في قراءة سجل الملفات المحذوفة: {e}")

            next_number = max_number + 1
            logger.info(f"📊 أعلى رقم مستخدم: {max_number}, الرقم التالي: {next_number}")

            return next_number

        except Exception as e:
            logger.error(f"خطأ في الحصول على الرقم التسلسلي: {e}")
            return 1

    def _log_deleted_file(self, folder: str, filename: str) -> None:
        """تسجيل الملف المحذوف لتجنب إعادة استخدام الرقم"""
        try:
            folder_path = Path(self.BASE_UPLOAD_DIR) / folder
            folder_path.mkdir(parents=True, exist_ok=True)

            deleted_log_path = folder_path / '.deleted_files.log'

            # إضافة اسم الملف المحذوف إلى السجل
            with open(deleted_log_path, 'a', encoding='utf-8') as f:
                f.write(f"{filename}\n")

            logger.info(f"📝 تم تسجيل الملف المحذوف: {filename}")

        except Exception as e:
            logger.error(f"خطأ في تسجيل الملف المحذوف: {e}")

    def generate_unique_filename(self, original_filename: str, folder: str) -> str:
        """توليد اسم ملف مختصر مع رقم تسلسلي"""
        try:
            file_extension = Path(original_filename).suffix.lower()

            # الحصول على الرقم التسلسلي التالي
            sequence_number = self._get_next_sequence_number(folder)

            # تكوين اسم الملف الجديد (مختصر)
            new_filename = f"{folder}_{sequence_number:03d}{file_extension}"

            # التأكد من عدم وجود الملف (في حالة نادرة)
            folder_path = Path(self.BASE_UPLOAD_DIR) / folder
            while (folder_path / new_filename).exists():
                sequence_number += 1
                new_filename = f"{folder}_{sequence_number:03d}{file_extension}"

            logger.info(f"✅ تم توليد اسم ملف جديد: {new_filename}")
            return new_filename

        except Exception as e:
            logger.error(f"خطأ في توليد اسم الملف: {e}")
            # اسم احتياطي آمن
            timestamp = get_tripoli_now().strftime("%Y%m%d%H%M%S")
            return f"{folder}_{timestamp}.jpg"
    
    def save_image(self, file: UploadFile, folder: str, generate_thumbnails: bool = True) -> Dict[str, Any]:
        """حفظ الصورة مع إنشاء الصور المصغرة"""
        try:
            # التحقق من صحة الملف
            validation_result = self.validate_image_file(file)
            if not validation_result["valid"]:
                return {
                    "success": False,
                    "error": validation_result["error"]
                }
            
            # توليد اسم ملف فريد
            filename = self.generate_unique_filename(file.filename or "unknown", folder)
            
            # تحديد مسار الحفظ
            folder_path = Path(self.BASE_UPLOAD_DIR) / folder
            file_path = folder_path / filename
            
            # حفظ الملف الأصلي
            file.file.seek(0)
            with open(file_path, "wb") as buffer:
                content = file.file.read()
                buffer.write(content)
            
            logger.info(f"تم حفظ الصورة: {file_path}")
            
            # معلومات الصورة الأساسية
            result = {
                "success": True,
                "filename": filename,
                "original_filename": file.filename,
                "file_path": f"{folder}/{filename}",
                "file_size": validation_result["file_size"],
                "folder": folder,
                "upload_time": get_tripoli_now().isoformat(),
                "thumbnails": {}
            }
            
            # إنشاء الصور المصغرة إذا كان مطلوباً
            if generate_thumbnails:
                thumbnails_result = self.generate_thumbnails(file_path, folder, filename)
                result["thumbnails"] = thumbnails_result.get("thumbnails", {})
                
                if not thumbnails_result.get("success", False):
                    logger.warning(f"فشل في إنشاء الصور المصغرة: {thumbnails_result.get('error', 'خطأ غير معروف')}")
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في حفظ الصورة: {e}")
            return {
                "success": False,
                "error": f"فشل في حفظ الصورة: {str(e)}"
            }
    
    def generate_thumbnails(self, image_path: Path, folder: str, filename: str) -> Dict[str, Any]:
        """إنشاء الصور المصغرة بأحجام مختلفة"""
        try:
            thumbnails = {}
            
            with Image.open(image_path) as img:
                # تحويل إلى RGB إذا كانت الصورة RGBA
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
                
                # إنشاء صور مصغرة بأحجام مختلفة
                for size_name, size_dimensions in self.THUMBNAIL_SIZES.items():
                    try:
                        # إنشاء نسخة مصغرة
                        thumbnail = img.copy()
                        thumbnail = ImageOps.fit(thumbnail, size_dimensions, Image.Resampling.LANCZOS)
                        
                        # تحديد مسار الحفظ
                        thumbnail_dir = Path(self.BASE_UPLOAD_DIR) / folder / 'thumbnails' / size_name
                        thumbnail_path = thumbnail_dir / filename
                        
                        # حفظ الصورة المصغرة
                        thumbnail.save(thumbnail_path, 'JPEG', quality=85, optimize=True)
                        
                        thumbnails[size_name] = {
                            "path": f"{folder}/thumbnails/{size_name}/{filename}",
                            "size": size_dimensions,
                            "file_size": thumbnail_path.stat().st_size
                        }
                        
                        logger.debug(f"تم إنشاء صورة مصغرة {size_name}: {thumbnail_path}")
                        
                    except Exception as thumb_error:
                        logger.error(f"خطأ في إنشاء الصورة المصغرة {size_name}: {thumb_error}")
                        continue
            
            return {
                "success": True,
                "thumbnails": thumbnails,
                "message": f"تم إنشاء {len(thumbnails)} صورة مصغرة"
            }
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء الصور المصغرة: {e}")
            return {
                "success": False,
                "error": f"فشل في إنشاء الصور المصغرة: {str(e)}",
                "thumbnails": {}
            }

    def delete_image(self, file_path: str, delete_thumbnails: bool = True) -> Dict[str, Any]:
        """حذف الصورة والصور المصغرة المرتبطة بها"""
        try:
            # تحديد المسار الكامل للملف
            full_path = Path(self.BASE_UPLOAD_DIR) / file_path

            if not full_path.exists():
                return {
                    "success": False,
                    "error": "الملف غير موجود"
                }

            # تسجيل الملف المحذوف قبل الحذف
            folder = Path(file_path).parent.name
            filename = Path(file_path).name
            self._log_deleted_file(folder, filename)

            # حذف الملف الأصلي
            full_path.unlink()
            logger.info(f"تم حذف الصورة: {full_path}")

            deleted_files = [str(full_path)]

            # حذف الصور المصغرة إذا كان مطلوباً
            if delete_thumbnails:
                folder = Path(file_path).parent
                filename = Path(file_path).name

                for size_name in self.THUMBNAIL_SIZES.keys():
                    thumbnail_path = Path(self.BASE_UPLOAD_DIR) / folder / 'thumbnails' / size_name / filename
                    if thumbnail_path.exists():
                        thumbnail_path.unlink()
                        deleted_files.append(str(thumbnail_path))
                        logger.debug(f"تم حذف الصورة المصغرة: {thumbnail_path}")

            return {
                "success": True,
                "deleted_files": deleted_files,
                "message": f"تم حذف {len(deleted_files)} ملف"
            }

        except Exception as e:
            logger.error(f"خطأ في حذف الصورة: {e}")
            return {
                "success": False,
                "error": f"فشل في حذف الصورة: {str(e)}"
            }

    def get_image_info(self, file_path: str) -> Dict[str, Any]:
        """الحصول على معلومات الصورة"""
        try:
            full_path = Path(self.BASE_UPLOAD_DIR) / file_path

            if not full_path.exists():
                return {
                    "success": False,
                    "error": "الملف غير موجود"
                }

            # معلومات الملف الأساسية
            file_stats = full_path.stat()

            # معلومات الصورة
            with Image.open(full_path) as img:
                image_info = {
                    "success": True,
                    "file_path": file_path,
                    "filename": full_path.name,
                    "file_size": file_stats.st_size,
                    "created_time": datetime.fromtimestamp(file_stats.st_ctime).isoformat(),
                    "modified_time": datetime.fromtimestamp(file_stats.st_mtime).isoformat(),
                    "image_format": img.format,
                    "image_mode": img.mode,
                    "image_size": img.size,
                    "width": img.width,
                    "height": img.height,
                    "has_transparency": img.mode in ('RGBA', 'LA', 'P'),
                    "thumbnails": {}
                }

            # البحث عن الصور المصغرة
            folder = Path(file_path).parent
            filename = Path(file_path).name

            for size_name in self.THUMBNAIL_SIZES.keys():
                thumbnail_path = Path(self.BASE_UPLOAD_DIR) / folder / 'thumbnails' / size_name / filename
                if thumbnail_path.exists():
                    thumb_stats = thumbnail_path.stat()
                    image_info["thumbnails"][size_name] = {
                        "path": f"{folder}/thumbnails/{size_name}/{filename}",
                        "file_size": thumb_stats.st_size,
                        "size": self.THUMBNAIL_SIZES[size_name]
                    }

            return image_info

        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات الصورة: {e}")
            return {
                "success": False,
                "error": f"فشل في الحصول على معلومات الصورة: {str(e)}"
            }

    def list_images(self, folder: str, include_thumbnails: bool = False) -> Dict[str, Any]:
        """جلب قائمة الصور في مجلد معين"""
        try:
            folder_path = Path(self.BASE_UPLOAD_DIR) / folder

            if not folder_path.exists():
                return {
                    "success": False,
                    "error": "المجلد غير موجود"
                }

            images = []

            # البحث عن جميع الصور في المجلد
            for file_path in folder_path.iterdir():
                if file_path.is_file() and file_path.suffix.lower() in self.ALLOWED_EXTENSIONS:
                    try:
                        # معلومات أساسية عن الصورة
                        file_stats = file_path.stat()
                        relative_path = f"{folder}/{file_path.name}"

                        image_data = {
                            "filename": file_path.name,
                            "file_path": relative_path,
                            "file_size": file_stats.st_size,
                            "created_time": datetime.fromtimestamp(file_stats.st_ctime).isoformat(),
                            "modified_time": datetime.fromtimestamp(file_stats.st_mtime).isoformat()
                        }

                        # إضافة معلومات الصور المصغرة إذا كان مطلوباً
                        if include_thumbnails:
                            image_data["thumbnails"] = {}
                            for size_name in self.THUMBNAIL_SIZES.keys():
                                thumbnail_path = folder_path / 'thumbnails' / size_name / file_path.name
                                if thumbnail_path.exists():
                                    thumb_stats = thumbnail_path.stat()
                                    image_data["thumbnails"][size_name] = {
                                        "path": f"{folder}/thumbnails/{size_name}/{file_path.name}",
                                        "file_size": thumb_stats.st_size,
                                        "size": self.THUMBNAIL_SIZES[size_name]
                                    }

                        images.append(image_data)

                    except Exception as file_error:
                        logger.warning(f"خطأ في معالجة الملف {file_path}: {file_error}")
                        continue

            # ترتيب الصور حسب تاريخ الإنشاء (الأحدث أولاً)
            images.sort(key=lambda x: x["created_time"], reverse=True)

            return {
                "success": True,
                "folder": folder,
                "total_images": len(images),
                "images": images
            }

        except Exception as e:
            logger.error(f"خطأ في جلب قائمة الصور: {e}")
            return {
                "success": False,
                "error": f"فشل في جلب قائمة الصور: {str(e)}"
            }

    def cleanup_orphaned_files(self, folder: str) -> Dict[str, Any]:
        """تنظيف الملفات المهجورة والصور المصغرة غير المستخدمة"""
        try:
            folder_path = Path(self.BASE_UPLOAD_DIR) / folder

            if not folder_path.exists():
                return {
                    "success": False,
                    "error": "المجلد غير موجود"
                }

            cleaned_files = []

            # جلب قائمة الصور الأصلية
            original_images = set()
            for file_path in folder_path.iterdir():
                if file_path.is_file() and file_path.suffix.lower() in self.ALLOWED_EXTENSIONS:
                    original_images.add(file_path.name)

            # فحص الصور المصغرة وحذف المهجورة
            thumbnails_dir = folder_path / 'thumbnails'
            if thumbnails_dir.exists():
                for size_dir in thumbnails_dir.iterdir():
                    if size_dir.is_dir() and size_dir.name in self.THUMBNAIL_SIZES:
                        for thumb_file in size_dir.iterdir():
                            if thumb_file.is_file():
                                # التحقق من وجود الصورة الأصلية
                                if thumb_file.name not in original_images:
                                    thumb_file.unlink()
                                    cleaned_files.append(str(thumb_file))
                                    logger.debug(f"تم حذف الصورة المصغرة المهجورة: {thumb_file}")

            return {
                "success": True,
                "cleaned_files": cleaned_files,
                "total_cleaned": len(cleaned_files),
                "message": f"تم تنظيف {len(cleaned_files)} ملف مهجور"
            }

        except Exception as e:
            logger.error(f"خطأ في تنظيف الملفات المهجورة: {e}")
            return {
                "success": False,
                "error": f"فشل في تنظيف الملفات المهجورة: {str(e)}"
            }

    def get_storage_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التخزين"""
        try:
            base_path = Path(self.BASE_UPLOAD_DIR)

            if not base_path.exists():
                return {
                    "success": False,
                    "error": "مجلد الرفع غير موجود"
                }

            statistics = {
                "success": True,
                "total_size": 0,
                "total_files": 0,
                "total_images": 0,  # عدد الصور الأصلية فقط
                "total_thumbnails": 0,  # عدد الصور المصغرة
                "folders": {}
            }

            # فحص كل مجلد فرعي
            for folder_path in base_path.iterdir():
                if folder_path.is_dir():
                    folder_stats = {
                        "total_size": 0,
                        "total_files": 0,
                        "images": 0,  # الصور الأصلية فقط
                        "thumbnails": 0,
                        "thumbnails_size": 0  # حجم الصور المصغرة منفصل
                    }

                    # فحص الملفات الأصلية في المجلد الرئيسي فقط
                    for file_path in folder_path.iterdir():
                        if file_path.is_file() and file_path.suffix.lower() in self.ALLOWED_EXTENSIONS:
                            file_size = file_path.stat().st_size
                            folder_stats["total_size"] += file_size
                            folder_stats["total_files"] += 1
                            folder_stats["images"] += 1

                    # فحص الصور المصغرة (للإحصائيات فقط، لا تُحسب في total_files)
                    thumbnails_dir = folder_path / 'thumbnails'
                    if thumbnails_dir.exists():
                        for thumbnail_file in thumbnails_dir.rglob('*'):
                            if thumbnail_file.is_file():
                                file_size = thumbnail_file.stat().st_size
                                folder_stats["total_size"] += file_size
                                folder_stats["thumbnails"] += 1
                                folder_stats["thumbnails_size"] += file_size

                    statistics["folders"][folder_path.name] = folder_stats
                    statistics["total_size"] += folder_stats["total_size"]
                    # حساب الصور الأصلية فقط في إجمالي الملفات
                    statistics["total_files"] += folder_stats["images"]
                    statistics["total_images"] += folder_stats["images"]
                    statistics["total_thumbnails"] += folder_stats["thumbnails"]

            # تحويل الأحجام إلى وحدات قابلة للقراءة
            statistics["total_size_formatted"] = self._format_file_size(statistics["total_size"])

            for _, folder_stats in statistics["folders"].items():
                folder_stats["total_size_formatted"] = self._format_file_size(folder_stats["total_size"])
                folder_stats["thumbnails_size_formatted"] = self._format_file_size(folder_stats["thumbnails_size"])

            return statistics

        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات التخزين: {e}")
            return {
                "success": False,
                "error": f"فشل في الحصول على إحصائيات التخزين: {str(e)}"
            }

    def _format_file_size(self, size_bytes: int) -> str:
        """تحويل حجم الملف إلى وحدة قابلة للقراءة"""
        try:
            if size_bytes == 0:
                return "0 B"

            size_names = ["B", "KB", "MB", "GB", "TB"]
            i = 0
            size_float = float(size_bytes)
            while size_float >= 1024 and i < len(size_names) - 1:
                size_float /= 1024.0
                i += 1

            return f"{size_float:.2f} {size_names[i]}"

        except Exception:
            return f"{size_bytes} B"

    def regenerate_thumbnails(self, file_path: str) -> Dict[str, Any]:
        """إعادة إنشاء الصور المصغرة لصورة معينة"""
        try:
            full_path = Path(self.BASE_UPLOAD_DIR) / file_path

            if not full_path.exists():
                return {
                    "success": False,
                    "error": "الملف غير موجود"
                }

            folder = Path(file_path).parent
            filename = Path(file_path).name

            # حذف الصور المصغرة الموجودة
            for size_name in self.THUMBNAIL_SIZES.keys():
                thumbnail_path = Path(self.BASE_UPLOAD_DIR) / folder / 'thumbnails' / size_name / filename
                if thumbnail_path.exists():
                    thumbnail_path.unlink()

            # إنشاء صور مصغرة جديدة
            thumbnails_result = self.generate_thumbnails(full_path, str(folder), filename)

            return {
                "success": thumbnails_result.get("success", False),
                "thumbnails": thumbnails_result.get("thumbnails", {}),
                "message": thumbnails_result.get("message", "تم إعادة إنشاء الصور المصغرة"),
                "error": thumbnails_result.get("error")
            }

        except Exception as e:
            logger.error(f"خطأ في إعادة إنشاء الصور المصغرة: {e}")
            return {
                "success": False,
                "error": f"فشل في إعادة إنشاء الصور المصغرة: {str(e)}"
            }

    def get_supported_formats(self) -> Dict[str, Any]:
        """الحصول على قائمة الصيغ المدعومة"""
        return {
            "success": True,
            "supported_extensions": list(self.ALLOWED_EXTENSIONS),
            "max_file_size": self.MAX_FILE_SIZE,
            "max_file_size_formatted": self._format_file_size(self.MAX_FILE_SIZE),
            "thumbnail_sizes": self.THUMBNAIL_SIZES,
            "upload_directory": self.BASE_UPLOAD_DIR
        }
