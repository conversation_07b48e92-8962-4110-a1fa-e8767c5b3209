"""
خدمة إدارة المستودعات
تطبق مبادئ البرمجة الكائنية مع معالجة شاملة للأخطاء
تدعم النظام الجديد للعلاقات مع الفروع
"""

import logging
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import and_, func, text, or_

from models.warehouse import Warehouse, WarehouseInventory
from models.branch import Branch, branch_warehouses
from utils.datetime_utils import get_tripoli_now

logger = logging.getLogger(__name__)


class WarehouseService:
    """
    خدمة إدارة المستودعات
    تطبق مبادئ البرمجة الكائنية مع نمط Singleton
    """
    
    _instance: Optional['WarehouseService'] = None
    
    def __init__(self, db_session: Session):
        """تهيئة خدمة إدارة المستودعات"""
        self.db_session = db_session
        logger.info("تم تهيئة خدمة إدارة المستودعات بنجاح")
    
    @classmethod  # type: ignore
    def get_instance(cls, db_session: Session) -> 'WarehouseService':
        """الحصول على instance وحيد من الخدمة"""
        if cls._instance is None:
            cls._instance = cls(db_session)
        elif db_session and cls._instance.db_session != db_session:
            cls._instance.db_session = db_session
        return cls._instance
    
    def create_warehouse(self, warehouse_data: Dict[str, Any]) -> Dict[str, Any]:
        """إنشاء مستودع جديد"""
        try:
            # التحقق من عدم وجود مستودع بنفس الكود
            existing_warehouse = self.db_session.query(Warehouse).filter(
                Warehouse.code == warehouse_data.get('code')
            ).first()
            
            if existing_warehouse:
                return {
                    'success': False,
                    'error': f'يوجد مستودع بنفس الكود: {warehouse_data.get("code")}'
                }
            
            # إنشاء المستودع الجديد
            warehouse = Warehouse(
                name=warehouse_data.get('name'),
                code=warehouse_data.get('code'),
                address=warehouse_data.get('address'),
                phone=warehouse_data.get('phone'),
                manager_name=warehouse_data.get('manager_name'),
                email=warehouse_data.get('email'),
                is_main=warehouse_data.get('is_main', False),
                is_active=warehouse_data.get('is_active', True),
                capacity_limit=warehouse_data.get('capacity_limit'),
                current_capacity=warehouse_data.get('current_capacity', 0),
                created_at=get_tripoli_now(),
                updated_at=get_tripoli_now()
            )
            
            # إذا كان المستودع الرئيسي، تأكد من عدم وجود مستودع رئيسي آخر
            if warehouse_data.get('is_main'):
                self.db_session.query(Warehouse).filter(
                    Warehouse.is_main == True
                ).update({'is_main': False})
            
            self.db_session.add(warehouse)
            self.db_session.commit()
            self.db_session.refresh(warehouse)
            
            logger.info(f"تم إنشاء مستودع جديد: {warehouse.name} ({warehouse.code})")
            
            return {
                'success': True,
                'warehouse': {
                    'id': warehouse.id,
                    'name': warehouse.name,
                    'code': warehouse.code,
                    'address': warehouse.address,
                    'phone': warehouse.phone,
                    'manager_name': warehouse.manager_name,
                    'email': warehouse.email,
                    'is_main': warehouse.is_main,
                    'is_active': warehouse.is_active,
                    'capacity_limit': float(warehouse.capacity_limit) if warehouse.capacity_limit is not None else None,
                    'current_capacity': float(warehouse.current_capacity) if warehouse.current_capacity is not None else 0.0,
                    'created_at': warehouse.created_at.isoformat() if warehouse.created_at is not None else None
                }
            }
            
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"خطأ في إنشاء المستودع: {e}")
            return {
                'success': False,
                'error': f'خطأ في إنشاء المستودع: {str(e)}'
            }
    
    def update_warehouse(self, warehouse_id: int, data: Dict[str, Any]) -> Dict[str, Any]:
        """تحديث بيانات المستودع"""
        try:
            warehouse = self.db_session.query(Warehouse).filter(
                Warehouse.id == warehouse_id
            ).first()
            
            if not warehouse:
                return {
                    'success': False,
                    'error': 'المستودع غير موجود'
                }
            
            # التحقق من عدم تكرار الكود إذا تم تغييره
            if 'code' in data and data['code'] != warehouse.code:
                existing_warehouse = self.db_session.query(Warehouse).filter(
                    and_(Warehouse.code == data['code'], Warehouse.id != warehouse_id)
                ).first()
                
                if existing_warehouse:
                    return {
                        'success': False,
                        'error': f'يوجد مستودع آخر بنفس الكود: {data["code"]}'
                    }
            
            # تحديث البيانات
            for key, value in data.items():
                if hasattr(warehouse, key):
                    setattr(warehouse, key, value)
            
            # إذا تم تعيين المستودع كرئيسي، إلغاء الرئيسية من الآخرين
            if data.get('is_main'):
                self.db_session.query(Warehouse).filter(
                    and_(Warehouse.is_main == True, Warehouse.id != warehouse_id)
                ).update({'is_main': False})
            
            warehouse.updated_at = get_tripoli_now()
            self.db_session.commit()
            self.db_session.refresh(warehouse)
            
            logger.info(f"تم تحديث المستودع: {warehouse.name}")
            
            return {
                'success': True,
                'warehouse': {
                    'id': warehouse.id,
                    'name': warehouse.name,
                    'code': warehouse.code,
                    'address': warehouse.address,
                    'phone': warehouse.phone,
                    'manager_name': warehouse.manager_name,
                    'email': warehouse.email,
                    'is_main': warehouse.is_main,
                    'is_active': warehouse.is_active,
                    'capacity_limit': float(warehouse.capacity_limit) if warehouse.capacity_limit is not None else None,
                    'current_capacity': float(warehouse.current_capacity) if warehouse.current_capacity is not None else 0.0,
                    'updated_at': warehouse.updated_at.isoformat() if warehouse.updated_at is not None else None
                }
            }
            
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"خطأ في تحديث المستودع: {e}")
            return {
                'success': False,
                'error': f'خطأ في تحديث المستودع: {str(e)}'
            }
    
    def delete_warehouse(self, warehouse_id: int) -> Dict[str, Any]:
        """حذف المستودع"""
        try:
            warehouse = self.db_session.query(Warehouse).filter(
                Warehouse.id == warehouse_id
            ).first()
            
            if not warehouse:
                return {
                    'success': False,
                    'error': 'المستودع غير موجود'
                }
            
            # التحقق من عدم حذف المستودع الرئيسي
            if warehouse.is_main:
                return {
                    'success': False,
                    'error': 'لا يمكن حذف المستودع الرئيسي'
                }
            
            # التحقق من عدم وجود مخزون في المستودع
            from models.warehouse import WarehouseInventory
            inventory_count = self.db_session.query(WarehouseInventory).filter(
                and_(
                    WarehouseInventory.warehouse_id == warehouse_id,
                    WarehouseInventory.quantity > 0
                )
            ).count()
            
            if inventory_count > 0:
                return {
                    'success': False,
                    'error': 'لا يمكن حذف المستودع لوجود مخزون به'
                }
            
            # حذف المستودع
            self.db_session.delete(warehouse)
            self.db_session.commit()
            
            logger.info(f"تم حذف المستودع: {warehouse.name}")
            
            return {
                'success': True,
                'message': 'تم حذف المستودع بنجاح'
            }
            
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"خطأ في حذف المستودع: {e}")
            return {
                'success': False,
                'error': f'خطأ في حذف المستودع: {str(e)}'
            }
    
    def get_all_warehouses(self, include_inactive: bool = False) -> Dict[str, Any]:
        """الحصول على جميع المستودعات"""
        try:
            query = self.db_session.query(Warehouse)
            
            if not include_inactive:
                query = query.filter(Warehouse.is_active == True)
            
            warehouses = query.order_by(Warehouse.is_main.desc(), Warehouse.name).all()
            
            warehouses_list = []
            for warehouse in warehouses:
                warehouses_list.append({
                    'id': warehouse.id,
                    'name': warehouse.name,
                    'code': warehouse.code,
                    'address': warehouse.address,
                    'phone': warehouse.phone,
                    'manager_name': warehouse.manager_name,
                    'email': warehouse.email,
                    'is_main': warehouse.is_main,
                    'is_active': warehouse.is_active,
                    'capacity_limit': float(warehouse.capacity_limit) if warehouse.capacity_limit is not None else None,
                    'current_capacity': float(warehouse.current_capacity) if warehouse.current_capacity is not None else 0.0,
                    'created_at': warehouse.created_at.isoformat() if warehouse.created_at is not None else None,
                    'updated_at': warehouse.updated_at.isoformat() if warehouse.updated_at is not None else None
                })
            
            return {
                'success': True,
                'warehouses': warehouses_list,
                'total': len(warehouses_list)
            }
            
        except Exception as e:
            logger.error(f"خطأ في جلب المستودعات: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب المستودعات: {str(e)}'
            }

    def get_warehouse_by_id(self, warehouse_id: int) -> Dict[str, Any]:
        """الحصول على مستودع بالمعرف"""
        try:
            warehouse = self.db_session.query(Warehouse).filter(
                Warehouse.id == warehouse_id
            ).first()

            if not warehouse:
                return {
                    'success': False,
                    'error': 'المستودع غير موجود'
                }

            return {
                'success': True,
                'warehouse': {
                    'id': warehouse.id,
                    'name': warehouse.name,
                    'code': warehouse.code,
                    'address': warehouse.address,
                    'phone': warehouse.phone,
                    'manager_name': warehouse.manager_name,
                    'email': warehouse.email,
                    'is_main': warehouse.is_main,
                    'is_active': warehouse.is_active,
                    'capacity_limit': float(warehouse.capacity_limit) if warehouse.capacity_limit is not None else None,
                    'current_capacity': float(warehouse.current_capacity) if warehouse.current_capacity is not None else 0.0,
                    'created_at': warehouse.created_at.isoformat() if warehouse.created_at is not None else None,
                    'updated_at': warehouse.updated_at.isoformat() if warehouse.updated_at is not None else None
                }
            }

        except Exception as e:
            logger.error(f"خطأ في جلب المستودع: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب المستودع: {str(e)}'
            }

    def set_main_warehouse(self, warehouse_id: int) -> Dict[str, Any]:
        """تعيين المستودع الرئيسي"""
        try:
            warehouse = self.db_session.query(Warehouse).filter(
                Warehouse.id == warehouse_id
            ).first()

            if not warehouse:
                return {
                    'success': False,
                    'error': 'المستودع غير موجود'
                }

            if not warehouse.is_active:
                return {
                    'success': False,
                    'error': 'لا يمكن تعيين مستودع غير نشط كرئيسي'
                }

            # إلغاء الرئيسية من جميع المستودعات
            self.db_session.query(Warehouse).filter(
                Warehouse.is_main == True
            ).update({'is_main': False})

            # تعيين المستودع الحالي كرئيسي
            warehouse.is_main = True
            warehouse.updated_at = get_tripoli_now()

            self.db_session.commit()

            logger.info(f"تم تعيين المستودع الرئيسي: {warehouse.name}")

            return {
                'success': True,
                'message': 'تم تعيين المستودع الرئيسي بنجاح'
            }

        except Exception as e:
            self.db_session.rollback()
            logger.error(f"خطأ في تعيين المستودع الرئيسي: {e}")
            return {
                'success': False,
                'error': f'خطأ في تعيين المستودع الرئيسي: {str(e)}'
            }



    def get_warehouse_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص المستودعات"""
        try:
            # إحصائيات عامة
            total_warehouses = self.db_session.query(Warehouse).count()
            active_warehouses = self.db_session.query(Warehouse).filter(
                Warehouse.is_active == True
            ).count()
            main_warehouse = self.db_session.query(Warehouse).filter(
                Warehouse.is_main == True
            ).first()

            # إحصائيات السعة
            total_capacity = self.db_session.query(
                func.sum(Warehouse.capacity_limit)
            ).filter(
                Warehouse.is_active == True
            ).scalar() or 0

            used_capacity = self.db_session.query(
                func.sum(Warehouse.current_capacity)
            ).filter(
                Warehouse.is_active == True
            ).scalar() or 0

            # حساب النسبة المئوية
            usage_percentage = 0
            if total_capacity > 0:
                usage_percentage = round((float(used_capacity) / float(total_capacity)) * 100, 2)

            return {
                'success': True,
                'summary': {
                    'total_warehouses': total_warehouses,
                    'active_warehouses': active_warehouses,
                    'inactive_warehouses': total_warehouses - active_warehouses,
                    'main_warehouse': {
                        'id': main_warehouse.id if main_warehouse else None,
                        'name': main_warehouse.name if main_warehouse else None,
                        'code': main_warehouse.code if main_warehouse else None
                    } if main_warehouse else None,
                    'capacity': {
                        'total_capacity': float(total_capacity),
                        'used_capacity': float(used_capacity),
                        'available_capacity': float(total_capacity) - float(used_capacity),
                        'usage_percentage': usage_percentage
                    }
                }
            }

        except Exception as e:
            logger.error(f"خطأ في جلب ملخص المستودعات: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب ملخص المستودعات: {str(e)}'
            }

    def get_warehouses_with_inventory_status(self, include_inactive: bool = False) -> Dict[str, Any]:
        """الحصول على المستودعات مع معلومات حالة المخزون"""
        try:
            logger.info(f"🔍 بدء جلب المستودعات مع معلومات المخزون - include_inactive: {include_inactive}")

            # استعلام أساسي للمستودعات
            query = self.db_session.query(Warehouse)

            if not include_inactive:
                query = query.filter(Warehouse.is_active == True)

            warehouses = query.order_by(Warehouse.is_main.desc(), Warehouse.name).all()

            warehouses_data = []

            for warehouse in warehouses:
                # حساب إجمالي المخزون في المستودع
                total_inventory = self.db_session.query(
                    func.coalesce(func.sum(WarehouseInventory.quantity), 0)
                ).filter(
                    WarehouseInventory.warehouse_id == warehouse.id
                ).scalar() or 0

                # عدد المنتجات المختلفة في المستودع
                products_count = self.db_session.query(
                    func.count(WarehouseInventory.id)
                ).filter(
                    WarehouseInventory.warehouse_id == warehouse.id,
                    WarehouseInventory.quantity > 0
                ).scalar() or 0

                # تحديد حالة المخزون
                has_inventory = float(total_inventory) > 0

                warehouse_data = {
                    'id': warehouse.id,
                    'name': warehouse.name,
                    'code': warehouse.code,
                    'address': warehouse.address,
                    'phone': warehouse.phone,
                    'manager_name': warehouse.manager_name,
                    'email': warehouse.email,
                    'is_main': warehouse.is_main,
                    'is_active': warehouse.is_active,
                    'capacity_limit': float(warehouse.capacity_limit) if warehouse.capacity_limit else None,
                    'current_capacity': float(warehouse.current_capacity) if warehouse.current_capacity else 0,
                    'created_at': warehouse.created_at.isoformat() if warehouse.created_at else None,
                    'updated_at': warehouse.updated_at.isoformat() if warehouse.updated_at else None,
                    'inventory_status': {
                        'has_inventory': has_inventory,
                        'total_quantity': float(total_inventory),
                        'products_count': products_count
                    }
                }

                warehouses_data.append(warehouse_data)

            logger.info(f"✅ تم جلب {len(warehouses_data)} مستودع مع معلومات المخزون بنجاح")

            return {
                'success': True,
                'warehouses': warehouses_data,
                'total_count': len(warehouses_data)
            }

        except Exception as e:
            logger.error(f"❌ خطأ في جلب المستودعات مع معلومات المخزون: {e}")
            logger.error(f"❌ تفاصيل الخطأ: {type(e).__name__}: {str(e)}")
            import traceback
            logger.error(f"❌ Stack trace: {traceback.format_exc()}")
            return {
                'success': False,
                'error': f'خطأ في جلب المستودعات مع معلومات المخزون: {str(e)}'
            }

    # ==================== طرق دعم النظام الجديد للفروع ====================

    def get_warehouses_for_branch(self, branch_id: int, include_inactive: bool = False) -> Dict[str, Any]:
        """
        الحصول على المستودعات المرتبطة بفرع معين

        Args:
            branch_id: معرف الفرع
            include_inactive: هل تشمل المستودعات غير النشطة

        Returns:
            قائمة المستودعات مع تفاصيل الربط
        """
        try:
            # استعلام للحصول على المستودعات مع تفاصيل الربط
            query = text("""
                SELECT
                    w.id, w.name, w.code, w.address, w.phone, w.manager_name,
                    w.email, w.is_main, w.is_active, w.capacity_limit, w.current_capacity,
                    w.created_at, w.updated_at,
                    bw.is_primary, bw.priority, bw.created_at as link_created_at
                FROM warehouses w
                INNER JOIN branch_warehouses bw ON w.id = bw.warehouse_id
                WHERE bw.branch_id = :branch_id
                {}
                ORDER BY bw.is_primary DESC, bw.priority ASC, w.name
            """.format("AND w.is_active = true" if not include_inactive else ""))

            result = self.db_session.execute(query, {"branch_id": branch_id}).fetchall()

            warehouses = []
            for row in result:
                warehouse_data = {
                    'id': row.id,
                    'name': row.name,
                    'code': row.code,
                    'address': row.address,
                    'phone': row.phone,
                    'manager_name': row.manager_name,
                    'email': row.email,
                    'is_main': row.is_main,
                    'is_active': row.is_active,
                    'capacity_limit': float(row.capacity_limit) if row.capacity_limit is not None else None,
                    'current_capacity': float(row.current_capacity) if row.current_capacity is not None else 0.0,
                    'created_at': row.created_at.isoformat() if row.created_at else None,
                    'updated_at': row.updated_at.isoformat() if row.updated_at else None,
                    # تفاصيل الربط
                    'is_primary': row.is_primary,
                    'priority': row.priority,
                    'link_created_at': row.link_created_at.isoformat() if row.link_created_at else None
                }
                warehouses.append(warehouse_data)

            logger.info(f"✅ تم جلب {len(warehouses)} مستودع للفرع {branch_id}")

            return {
                'success': True,
                'warehouses': warehouses,
                'total_count': len(warehouses)
            }

        except Exception as e:
            logger.error(f"❌ خطأ في جلب مستودعات الفرع: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب مستودعات الفرع: {str(e)}'
            }

    def get_branches_for_warehouse(self, warehouse_id: int, include_inactive: bool = False) -> Dict[str, Any]:
        """
        الحصول على الفروع المرتبطة بمستودع معين

        Args:
            warehouse_id: معرف المستودع
            include_inactive: هل تشمل الفروع غير النشطة

        Returns:
            قائمة الفروع مع تفاصيل الربط
        """
        try:
            # استعلام للحصول على الفروع مع تفاصيل الربط
            query = text("""
                SELECT
                    b.id, b.name, b.code, b.address, b.phone, b.manager_name,
                    b.email, b.is_main, b.is_active, b.city, b.region,
                    b.created_at, b.updated_at,
                    bw.is_primary, bw.priority, bw.created_at as link_created_at
                FROM branches b
                INNER JOIN branch_warehouses bw ON b.id = bw.branch_id
                WHERE bw.warehouse_id = :warehouse_id
                {}
                ORDER BY bw.is_primary DESC, bw.priority ASC, b.name
            """.format("AND b.is_active = true" if not include_inactive else ""))

            result = self.db_session.execute(query, {"warehouse_id": warehouse_id}).fetchall()

            branches = []
            for row in result:
                branch_data = {
                    'id': row.id,
                    'name': row.name,
                    'code': row.code,
                    'address': row.address,
                    'phone': row.phone,
                    'manager_name': row.manager_name,
                    'email': row.email,
                    'is_main': row.is_main,
                    'is_active': row.is_active,
                    'city': row.city,
                    'region': row.region,
                    'created_at': row.created_at.isoformat() if row.created_at else None,
                    'updated_at': row.updated_at.isoformat() if row.updated_at else None,
                    # تفاصيل الربط
                    'is_primary': row.is_primary,
                    'priority': row.priority,
                    'link_created_at': row.link_created_at.isoformat() if row.link_created_at else None
                }
                branches.append(branch_data)

            logger.info(f"✅ تم جلب {len(branches)} فرع للمستودع {warehouse_id}")

            return {
                'success': True,
                'branches': branches,
                'total_count': len(branches)
            }

        except Exception as e:
            logger.error(f"❌ خطأ في جلب فروع المستودع: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب فروع المستودع: {str(e)}'
            }

    def get_optimal_warehouse_for_branch(self, branch_id: int, product_id: int,
                                       required_quantity: float) -> Dict[str, Any]:
        """
        الحصول على المستودع الأمثل للفرع لمنتج معين

        Args:
            branch_id: معرف الفرع
            product_id: معرف المنتج
            required_quantity: الكمية المطلوبة

        Returns:
            المستودع الأمثل مع تفاصيل التوفر
        """
        try:
            # استعلام للحصول على المستودعات المرتبطة بالفرع مع مخزون المنتج
            query = text("""
                SELECT
                    w.id, w.name, w.code, w.is_main, w.is_active,
                    w.capacity_limit, w.current_capacity,
                    bw.is_primary, bw.priority,
                    COALESCE(wi.quantity, 0) as available_quantity,
                    COALESCE(wi.reserved_quantity, 0) as reserved_quantity,
                    (COALESCE(wi.quantity, 0) - COALESCE(wi.reserved_quantity, 0)) as free_quantity
                FROM warehouses w
                INNER JOIN branch_warehouses bw ON w.id = bw.warehouse_id
                LEFT JOIN warehouse_inventory wi ON w.id = wi.warehouse_id AND wi.product_id = :product_id
                WHERE bw.branch_id = :branch_id
                AND w.is_active = true
                AND (COALESCE(wi.quantity, 0) - COALESCE(wi.reserved_quantity, 0)) >= :required_quantity
                ORDER BY
                    bw.is_primary DESC,
                    bw.priority ASC,
                    (COALESCE(wi.quantity, 0) - COALESCE(wi.reserved_quantity, 0)) DESC
                LIMIT 1
            """)

            result = self.db_session.execute(query, {
                "branch_id": branch_id,
                "product_id": product_id,
                "required_quantity": required_quantity
            }).fetchone()

            if not result:
                # البحث عن أي مستودع متاح (حتى لو لم يكن مرتبط بالفرع)
                fallback_query = text("""
                    SELECT
                        w.id, w.name, w.code, w.is_main, w.is_active,
                        w.capacity_limit, w.current_capacity,
                        false as is_primary, 999 as priority,
                        COALESCE(wi.quantity, 0) as available_quantity,
                        COALESCE(wi.reserved_quantity, 0) as reserved_quantity,
                        (COALESCE(wi.quantity, 0) - COALESCE(wi.reserved_quantity, 0)) as free_quantity
                    FROM warehouses w
                    LEFT JOIN warehouse_inventory wi ON w.id = wi.warehouse_id AND wi.product_id = :product_id
                    WHERE w.is_active = true
                    AND (COALESCE(wi.quantity, 0) - COALESCE(wi.reserved_quantity, 0)) >= :required_quantity
                    ORDER BY
                        w.is_main DESC,
                        (COALESCE(wi.quantity, 0) - COALESCE(wi.reserved_quantity, 0)) DESC
                    LIMIT 1
                """)

                result = self.db_session.execute(fallback_query, {
                    "product_id": product_id,
                    "required_quantity": required_quantity
                }).fetchone()

                if not result:
                    return {
                        'success': False,
                        'error': 'لا يوجد مستودع متاح بالكمية المطلوبة',
                        'required_quantity': required_quantity
                    }

            warehouse_data = {
                'id': result.id,
                'name': result.name,
                'code': result.code,
                'is_main': result.is_main,
                'is_active': result.is_active,
                'capacity_limit': float(result.capacity_limit) if result.capacity_limit is not None else None,
                'current_capacity': float(result.current_capacity) if result.current_capacity is not None else 0.0,
                'is_primary': result.is_primary,
                'priority': result.priority,
                'available_quantity': float(result.available_quantity),
                'reserved_quantity': float(result.reserved_quantity),
                'free_quantity': float(result.free_quantity),
                'can_fulfill': float(result.free_quantity) >= required_quantity
            }

            logger.info(f"✅ تم العثور على المستودع الأمثل {result.name} للفرع {branch_id}")

            return {
                'success': True,
                'warehouse': warehouse_data,
                'required_quantity': required_quantity
            }

        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن المستودع الأمثل: {e}")
            return {
                'success': False,
                'error': f'خطأ في البحث عن المستودع الأمثل: {str(e)}'
            }

    def get_warehouse_capacity_status(self, warehouse_id: int) -> Dict[str, Any]:
        """
        الحصول على حالة سعة المستودع

        Args:
            warehouse_id: معرف المستودع

        Returns:
            تفاصيل حالة السعة
        """
        try:
            warehouse = self.db_session.query(Warehouse).filter(
                Warehouse.id == warehouse_id
            ).first()

            if not warehouse:
                return {
                    'success': False,
                    'error': 'المستودع غير موجود'
                }

            # حساب إجمالي المخزون
            total_inventory = self.db_session.query(
                func.sum(WarehouseInventory.quantity)
            ).filter(
                WarehouseInventory.warehouse_id == warehouse_id
            ).scalar() or 0

            # حساب المخزون المحجوز
            total_reserved = self.db_session.query(
                func.sum(WarehouseInventory.reserved_quantity)
            ).filter(
                WarehouseInventory.warehouse_id == warehouse_id
            ).scalar() or 0

            capacity_limit = float(warehouse.capacity_limit) if warehouse.capacity_limit else None
            current_capacity = float(warehouse.current_capacity) if warehouse.current_capacity else 0.0
            total_inventory = float(total_inventory)
            total_reserved = float(total_reserved)

            # حساب النسب
            usage_percentage = 0.0
            available_capacity = None
            status = 'normal'

            if capacity_limit and capacity_limit > 0:
                usage_percentage = (current_capacity / capacity_limit) * 100
                available_capacity = capacity_limit - current_capacity

                if usage_percentage >= 90:
                    status = 'critical'
                elif usage_percentage >= 75:
                    status = 'warning'

            capacity_status = {
                'warehouse_id': warehouse.id,
                'warehouse_name': warehouse.name,
                'warehouse_code': warehouse.code,
                'capacity_limit': capacity_limit,
                'current_capacity': current_capacity,
                'available_capacity': available_capacity,
                'total_inventory': total_inventory,
                'total_reserved': total_reserved,
                'free_inventory': total_inventory - total_reserved,
                'usage_percentage': round(usage_percentage, 2),
                'status': status,
                'is_active': warehouse.is_active
            }

            logger.info(f"✅ تم جلب حالة سعة المستودع {warehouse.name}")

            return {
                'success': True,
                'capacity_status': capacity_status
            }

        except Exception as e:
            logger.error(f"❌ خطأ في جلب حالة سعة المستودع: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب حالة سعة المستودع: {str(e)}'
            }


def get_warehouse_service(db_session: Session) -> WarehouseService:
    """
    دالة مساعدة للحصول على instance من خدمة المستودعات

    Args:
        db_session: جلسة قاعدة البيانات

    Returns:
        instance من WarehouseService
    """
    return WarehouseService.get_instance(db_session)
