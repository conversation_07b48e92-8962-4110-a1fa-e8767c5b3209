"""
خدمة التقارير المتقدمة للفروع والمستودعات
تطبق مبادئ البرمجة الكائنية لإنشاء تقارير شاملة ومتقدمة
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, func, text, or_, case
from decimal import Decimal

from models.warehouse import Warehouse, WarehouseInventory, TransferRequest, TransferRequestItem
from models.branch import Branch, branch_warehouses
from models.product import Product
from utils.datetime_utils import get_tripoli_now, get_current_time_with_settings

logger = logging.getLogger(__name__)


class AdvancedReportsService:
    """
    خدمة التقارير المتقدمة
    تطبق مبادئ البرمجة الكائنية مع نمط Singleton
    """
    
    _instance: Optional['AdvancedReportsService'] = None
    
    def __init__(self, db_session: Session):
        """تهيئة خدمة التقارير المتقدمة"""
        self.db_session = db_session
        logger.info("تم تهيئة خدمة التقارير المتقدمة بنجاح")
    
    @classmethod  # type: ignore
    def get_instance(cls, db_session: Session) -> 'AdvancedReportsService':
        """الحصول على instance وحيد من الخدمة"""
        if cls._instance is None:
            cls._instance = cls(db_session)
        elif db_session and cls._instance.db_session != db_session:
            cls._instance.db_session = db_session
        return cls._instance
    
    def generate_branch_performance_report(self, branch_id: Optional[int] = None,
                                         date_from: Optional[str] = None,
                                         date_to: Optional[str] = None) -> Dict[str, Any]:
        """
        إنشاء تقرير أداء الفروع
        
        Args:
            branch_id: معرف فرع محدد (اختياري)
            date_from: تاريخ البداية
            date_to: تاريخ النهاية
            
        Returns:
            تقرير أداء الفروع
        """
        try:
            # تحديد نطاق التاريخ الافتراضي (آخر 30 يوم)
            if not date_from:
                date_from = (get_tripoli_now() - timedelta(days=30)).isoformat()
            if not date_to:
                date_to = get_tripoli_now().isoformat()

            # بناء الاستعلام الأساسي
            base_query = """
                SELECT 
                    b.id as branch_id,
                    b.name as branch_name,
                    b.code as branch_code,
                    b.city,
                    b.region,
                    b.is_main,
                    b.is_active,
                    COUNT(DISTINCT bw.warehouse_id) as linked_warehouses_count,
                    COUNT(CASE WHEN bw.is_primary THEN 1 END) as primary_warehouses_count,
                    COUNT(DISTINCT tr_in.id) as incoming_transfers,
                    COUNT(DISTINCT tr_out.id) as outgoing_transfers,
                    COUNT(CASE WHEN tr_in.status = 'completed' THEN 1 END) as completed_incoming,
                    COUNT(CASE WHEN tr_out.status = 'completed' THEN 1 END) as completed_outgoing,
                    COALESCE(SUM(CASE WHEN tr_in.status = 'completed' THEN tri_in.transferred_quantity * tri_in.unit_cost END), 0) as incoming_value,
                    COALESCE(SUM(CASE WHEN tr_out.status = 'completed' THEN tri_out.transferred_quantity * tri_out.unit_cost END), 0) as outgoing_value
                FROM branches b
                LEFT JOIN branch_warehouses bw ON b.id = bw.branch_id
                LEFT JOIN warehouses w_in ON bw.warehouse_id = w_in.id
                LEFT JOIN warehouses w_out ON bw.warehouse_id = w_out.id
                LEFT JOIN transfer_requests tr_in ON w_in.id = tr_in.to_warehouse_id
                LEFT JOIN transfer_requests tr_out ON w_out.id = tr_out.from_warehouse_id
                LEFT JOIN transfer_request_items tri_in ON tr_in.id = tri_in.transfer_request_id
                LEFT JOIN transfer_request_items tri_out ON tr_out.id = tri_out.transfer_request_id
                WHERE b.is_active = true
                AND (tr_in.requested_at IS NULL OR tr_in.requested_at BETWEEN :date_from AND :date_to)
                AND (tr_out.requested_at IS NULL OR tr_out.requested_at BETWEEN :date_from AND :date_to)
            """

            params = {
                "date_from": date_from,
                "date_to": date_to
            }

            # إضافة فلتر الفرع إذا كان محدد
            if branch_id:
                base_query += " AND b.id = :branch_id"
                params["branch_id"] = branch_id

            base_query += """
                GROUP BY b.id, b.name, b.code, b.city, b.region, b.is_main, b.is_active
                ORDER BY b.is_main DESC, b.name
            """

            result = self.db_session.execute(text(base_query), params).fetchall()

            branches_performance = []
            total_stats = {
                'total_branches': 0,
                'total_warehouses': 0,
                'total_transfers': 0,
                'total_value': 0.0
            }

            for row in result:
                # حساب معدلات الأداء
                total_transfers = (row.incoming_transfers or 0) + (row.outgoing_transfers or 0)
                completed_transfers = (row.completed_incoming or 0) + (row.completed_outgoing or 0)
                completion_rate = (completed_transfers / max(1, total_transfers)) * 100

                total_value = float(row.incoming_value or 0) + float(row.outgoing_value or 0)
                
                # تقييم الأداء
                performance_score = self._calculate_branch_performance_score(
                    completion_rate, total_transfers, row.linked_warehouses_count or 0
                )

                branch_data = {
                    'branch_id': row.branch_id,
                    'branch_name': row.branch_name,
                    'branch_code': row.branch_code,
                    'city': row.city,
                    'region': row.region,
                    'is_main': row.is_main,
                    'is_active': row.is_active,
                    'warehouses_stats': {
                        'linked_warehouses': row.linked_warehouses_count or 0,
                        'primary_warehouses': row.primary_warehouses_count or 0
                    },
                    'transfer_stats': {
                        'incoming_transfers': row.incoming_transfers or 0,
                        'outgoing_transfers': row.outgoing_transfers or 0,
                        'total_transfers': total_transfers,
                        'completed_incoming': row.completed_incoming or 0,
                        'completed_outgoing': row.completed_outgoing or 0,
                        'completed_total': completed_transfers,
                        'completion_rate': round(completion_rate, 2)
                    },
                    'financial_stats': {
                        'incoming_value': float(row.incoming_value or 0),
                        'outgoing_value': float(row.outgoing_value or 0),
                        'total_value': total_value,
                        'net_value': float(row.incoming_value or 0) - float(row.outgoing_value or 0)
                    },
                    'performance': performance_score
                }

                branches_performance.append(branch_data)

                # تحديث الإحصائيات الإجمالية
                total_stats['total_branches'] += 1
                total_stats['total_warehouses'] += row.linked_warehouses_count or 0
                total_stats['total_transfers'] += total_transfers
                total_stats['total_value'] += total_value

            logger.info(f"✅ تم إنشاء تقرير أداء {len(branches_performance)} فرع")

            return {
                'success': True,
                'report': {
                    'report_type': 'branch_performance',
                    'date_range': {
                        'from': date_from,
                        'to': date_to
                    },
                    'total_stats': total_stats,
                    'branches_performance': branches_performance,
                    'generated_at': get_tripoli_now().isoformat()
                }
            }

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء تقرير أداء الفروع: {e}")
            return {
                'success': False,
                'error': f'خطأ في إنشاء تقرير أداء الفروع: {str(e)}'
            }

    def generate_warehouse_utilization_report(self, warehouse_id: Optional[int] = None) -> Dict[str, Any]:
        """
        إنشاء تقرير استخدام المستودعات
        
        Args:
            warehouse_id: معرف مستودع محدد (اختياري)
            
        Returns:
            تقرير استخدام المستودعات
        """
        try:
            # استعلام لجلب بيانات المستودعات مع الاستخدام
            base_query = """
                SELECT 
                    w.id as warehouse_id,
                    w.name as warehouse_name,
                    w.code as warehouse_code,
                    w.address,
                    w.is_main,
                    w.is_active,
                    w.capacity_limit,
                    w.current_capacity,
                    COUNT(DISTINCT bw.branch_id) as linked_branches_count,
                    COUNT(CASE WHEN bw.is_primary THEN 1 END) as primary_for_branches,
                    COUNT(DISTINCT wi.product_id) as unique_products,
                    COALESCE(SUM(wi.quantity), 0) as total_inventory,
                    COALESCE(SUM(wi.reserved_quantity), 0) as total_reserved,
                    COALESCE(SUM(wi.quantity * wi.unit_cost), 0) as inventory_value,
                    COUNT(DISTINCT tr_in.id) as incoming_transfers_30d,
                    COUNT(DISTINCT tr_out.id) as outgoing_transfers_30d
                FROM warehouses w
                LEFT JOIN branch_warehouses bw ON w.id = bw.warehouse_id
                LEFT JOIN warehouse_inventory wi ON w.id = wi.warehouse_id
                LEFT JOIN transfer_requests tr_in ON w.id = tr_in.to_warehouse_id 
                    AND tr_in.requested_at >= :date_30_days_ago
                LEFT JOIN transfer_requests tr_out ON w.id = tr_out.from_warehouse_id 
                    AND tr_out.requested_at >= :date_30_days_ago
                WHERE w.is_active = true
            """

            params = {
                "date_30_days_ago": get_tripoli_now() - timedelta(days=30)
            }

            if warehouse_id:
                base_query += " AND w.id = :warehouse_id"
                params["warehouse_id"] = warehouse_id

            base_query += """
                GROUP BY w.id, w.name, w.code, w.address, w.is_main, w.is_active, 
                         w.capacity_limit, w.current_capacity
                ORDER BY w.is_main DESC, w.name
            """

            result = self.db_session.execute(text(base_query), params).fetchall()

            warehouses_utilization = []
            total_stats = {
                'total_warehouses': 0,
                'total_capacity': 0.0,
                'total_used': 0.0,
                'total_inventory_value': 0.0
            }

            for row in result:
                # حساب معدلات الاستخدام
                capacity_limit = float(row.capacity_limit or 0)
                current_capacity = float(row.current_capacity or 0)
                total_inventory = float(row.total_inventory or 0)
                total_reserved = float(row.total_reserved or 0)
                
                usage_percentage = 0.0
                if capacity_limit > 0:
                    usage_percentage = (current_capacity / capacity_limit) * 100

                # تحديد حالة الاستخدام
                if usage_percentage >= 90:
                    usage_status = 'critical'
                elif usage_percentage >= 75:
                    usage_status = 'warning'
                elif usage_percentage >= 50:
                    usage_status = 'normal'
                else:
                    usage_status = 'low'

                # حساب نقاط الكفاءة
                efficiency_score = self._calculate_warehouse_efficiency_score(
                    usage_percentage, row.unique_products or 0, 
                    (row.incoming_transfers_30d or 0) + (row.outgoing_transfers_30d or 0)
                )

                warehouse_data = {
                    'warehouse_id': row.warehouse_id,
                    'warehouse_name': row.warehouse_name,
                    'warehouse_code': row.warehouse_code,
                    'address': row.address,
                    'is_main': row.is_main,
                    'is_active': row.is_active,
                    'capacity_info': {
                        'capacity_limit': capacity_limit,
                        'current_capacity': current_capacity,
                        'available_capacity': max(0, capacity_limit - current_capacity),
                        'usage_percentage': round(usage_percentage, 2),
                        'usage_status': usage_status
                    },
                    'branch_connections': {
                        'linked_branches': row.linked_branches_count or 0,
                        'primary_for_branches': row.primary_for_branches or 0
                    },
                    'inventory_stats': {
                        'unique_products': row.unique_products or 0,
                        'total_inventory': total_inventory,
                        'total_reserved': total_reserved,
                        'available_inventory': total_inventory - total_reserved,
                        'inventory_value': float(row.inventory_value or 0)
                    },
                    'activity_stats': {
                        'incoming_transfers_30d': row.incoming_transfers_30d or 0,
                        'outgoing_transfers_30d': row.outgoing_transfers_30d or 0,
                        'total_transfers_30d': (row.incoming_transfers_30d or 0) + (row.outgoing_transfers_30d or 0)
                    },
                    'efficiency': efficiency_score
                }

                warehouses_utilization.append(warehouse_data)

                # تحديث الإحصائيات الإجمالية
                total_stats['total_warehouses'] += 1
                total_stats['total_capacity'] += capacity_limit
                total_stats['total_used'] += current_capacity
                total_stats['total_inventory_value'] += float(row.inventory_value or 0)

            # حساب المعدلات الإجمالية
            overall_usage = 0.0
            if total_stats['total_capacity'] > 0:
                overall_usage = (total_stats['total_used'] / total_stats['total_capacity']) * 100

            total_stats['overall_usage_percentage'] = round(overall_usage, 2)

            logger.info(f"✅ تم إنشاء تقرير استخدام {len(warehouses_utilization)} مستودع")

            return {
                'success': True,
                'report': {
                    'report_type': 'warehouse_utilization',
                    'total_stats': total_stats,
                    'warehouses_utilization': warehouses_utilization,
                    'generated_at': get_tripoli_now().isoformat()
                }
            }

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء تقرير استخدام المستودعات: {e}")
            return {
                'success': False,
                'error': f'خطأ في إنشاء تقرير استخدام المستودعات: {str(e)}'
            }

    def generate_distribution_optimization_report(self, branch_id: Optional[int] = None) -> Dict[str, Any]:
        """
        إنشاء تقرير تحسين التوزيع

        Args:
            branch_id: معرف فرع محدد (اختياري)

        Returns:
            تقرير تحسين التوزيع
        """
        try:
            # استعلام لتحليل كفاءة التوزيع
            distribution_query = """
                SELECT
                    b.id as branch_id,
                    b.name as branch_name,
                    b.code as branch_code,
                    b.city,
                    b.region,
                    COUNT(DISTINCT bw.warehouse_id) as linked_warehouses,
                    COUNT(CASE WHEN bw.is_primary THEN 1 END) as primary_warehouses,
                    AVG(bw.priority) as avg_priority,
                    COUNT(DISTINCT wi.product_id) as available_products,
                    COALESCE(SUM(wi.quantity - wi.reserved_quantity), 0) as total_available_inventory,
                    COUNT(CASE WHEN (wi.quantity - wi.reserved_quantity) <= 10 THEN 1 END) as low_stock_products,
                    COUNT(DISTINCT tr.id) as transfer_requests_30d,
                    AVG(CASE
                        WHEN tr.completed_at IS NOT NULL AND tr.requested_at IS NOT NULL
                        THEN EXTRACT(EPOCH FROM (tr.completed_at - tr.requested_at))/3600
                    END) as avg_fulfillment_hours
                FROM branches b
                LEFT JOIN branch_warehouses bw ON b.id = bw.branch_id
                LEFT JOIN warehouses w ON bw.warehouse_id = w.id AND w.is_active = true
                LEFT JOIN warehouse_inventory wi ON w.id = wi.warehouse_id
                LEFT JOIN transfer_requests tr ON w.id = tr.to_warehouse_id
                    AND tr.requested_at >= :date_30_days_ago
                WHERE b.is_active = true
            """

            params = {
                "date_30_days_ago": get_tripoli_now() - timedelta(days=30)
            }

            if branch_id:
                distribution_query += " AND b.id = :branch_id"
                params["branch_id"] = branch_id

            distribution_query += """
                GROUP BY b.id, b.name, b.code, b.city, b.region
                ORDER BY b.name
            """

            result = self.db_session.execute(text(distribution_query), params).fetchall()

            optimization_analysis = []

            for row in result:
                # تحليل كفاءة التوزيع
                linked_warehouses = row.linked_warehouses or 0
                primary_warehouses = row.primary_warehouses or 0
                available_products = row.available_products or 0
                low_stock_products = row.low_stock_products or 0
                avg_fulfillment_hours = row.avg_fulfillment_hours or 0

                # حساب نقاط التحسين
                optimization_score = self._calculate_optimization_score(
                    linked_warehouses, primary_warehouses,
                    low_stock_products, available_products, avg_fulfillment_hours
                )

                # إنشاء التوصيات
                recommendations = self._generate_optimization_recommendations(
                    linked_warehouses, primary_warehouses,
                    low_stock_products, available_products, avg_fulfillment_hours
                )

                branch_analysis = {
                    'branch_id': row.branch_id,
                    'branch_name': row.branch_name,
                    'branch_code': row.branch_code,
                    'location': {
                        'city': row.city,
                        'region': row.region
                    },
                    'current_setup': {
                        'linked_warehouses': linked_warehouses,
                        'primary_warehouses': primary_warehouses,
                        'avg_priority': round(row.avg_priority or 0, 2)
                    },
                    'inventory_analysis': {
                        'available_products': available_products,
                        'total_available_inventory': float(row.total_available_inventory or 0),
                        'low_stock_products': low_stock_products,
                        'stock_health_percentage': round(
                            ((available_products - low_stock_products) / max(1, available_products)) * 100, 2
                        )
                    },
                    'performance_metrics': {
                        'transfer_requests_30d': row.transfer_requests_30d or 0,
                        'avg_fulfillment_hours': round(avg_fulfillment_hours, 2)
                    },
                    'optimization': {
                        'score': optimization_score,
                        'recommendations': recommendations
                    }
                }

                optimization_analysis.append(branch_analysis)

            logger.info(f"✅ تم إنشاء تقرير تحسين التوزيع لـ {len(optimization_analysis)} فرع")

            return {
                'success': True,
                'report': {
                    'report_type': 'distribution_optimization',
                    'optimization_analysis': optimization_analysis,
                    'generated_at': get_tripoli_now().isoformat()
                }
            }

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء تقرير تحسين التوزيع: {e}")
            return {
                'success': False,
                'error': f'خطأ في إنشاء تقرير تحسين التوزيع: {str(e)}'
            }

    def _calculate_branch_performance_score(self, completion_rate: float,
                                          total_transfers: int,
                                          linked_warehouses: int) -> Dict[str, Any]:
        """حساب نقاط أداء الفرع"""
        score = 0.0

        # نقاط معدل الإكمال (50 نقطة)
        completion_score = min(50, completion_rate * 0.5)
        score += completion_score

        # نقاط النشاط (30 نقطة)
        if total_transfers >= 20:
            activity_score = 30
        elif total_transfers >= 10:
            activity_score = 20
        elif total_transfers >= 5:
            activity_score = 10
        else:
            activity_score = 5
        score += activity_score

        # نقاط تنظيم المستودعات (20 نقطة)
        if linked_warehouses >= 3:
            organization_score = 20
        elif linked_warehouses >= 2:
            organization_score = 15
        elif linked_warehouses >= 1:
            organization_score = 10
        else:
            organization_score = 0
        score += organization_score

        # تحديد مستوى الأداء
        if score >= 80:
            performance_level = 'ممتاز'
        elif score >= 60:
            performance_level = 'جيد'
        elif score >= 40:
            performance_level = 'متوسط'
        else:
            performance_level = 'يحتاج تحسين'

        return {
            'total_score': round(score, 2),
            'performance_level': performance_level,
            'details': {
                'completion_score': round(completion_score, 2),
                'activity_score': activity_score,
                'organization_score': organization_score
            }
        }

    def _calculate_warehouse_efficiency_score(self, usage_percentage: float,
                                            unique_products: int,
                                            total_transfers: int) -> Dict[str, Any]:
        """حساب نقاط كفاءة المستودع"""
        score = 0.0

        # نقاط الاستخدام الأمثل (40 نقطة)
        if 60 <= usage_percentage <= 85:
            usage_score = 40  # الاستخدام الأمثل
        elif 50 <= usage_percentage < 60 or 85 < usage_percentage <= 90:
            usage_score = 30
        elif 40 <= usage_percentage < 50 or 90 < usage_percentage <= 95:
            usage_score = 20
        else:
            usage_score = 10
        score += usage_score

        # نقاط تنوع المنتجات (30 نقطة)
        if unique_products >= 100:
            diversity_score = 30
        elif unique_products >= 50:
            diversity_score = 25
        elif unique_products >= 20:
            diversity_score = 20
        elif unique_products >= 10:
            diversity_score = 15
        else:
            diversity_score = 10
        score += diversity_score

        # نقاط النشاط (30 نقطة)
        if total_transfers >= 15:
            activity_score = 30
        elif total_transfers >= 10:
            activity_score = 25
        elif total_transfers >= 5:
            activity_score = 20
        else:
            activity_score = 10
        score += activity_score

        # تحديد مستوى الكفاءة
        if score >= 80:
            efficiency_level = 'عالي'
        elif score >= 60:
            efficiency_level = 'جيد'
        elif score >= 40:
            efficiency_level = 'متوسط'
        else:
            efficiency_level = 'منخفض'

        return {
            'total_score': round(score, 2),
            'efficiency_level': efficiency_level,
            'details': {
                'usage_score': usage_score,
                'diversity_score': diversity_score,
                'activity_score': activity_score
            }
        }

    def _calculate_optimization_score(self, linked_warehouses: int, primary_warehouses: int,
                                    low_stock_products: int, available_products: int,
                                    avg_fulfillment_hours: float) -> Dict[str, Any]:
        """حساب نقاط التحسين"""
        score = 0.0

        # نقاط تنظيم المستودعات (30 نقطة)
        if primary_warehouses >= 1 and linked_warehouses <= 5:
            warehouse_score = 30
        elif primary_warehouses >= 1:
            warehouse_score = 20
        elif linked_warehouses >= 1:
            warehouse_score = 15
        else:
            warehouse_score = 0
        score += warehouse_score

        # نقاط صحة المخزون (40 نقطة)
        if available_products > 0:
            stock_health = ((available_products - low_stock_products) / available_products) * 100
            if stock_health >= 90:
                stock_score = 40
            elif stock_health >= 75:
                stock_score = 30
            elif stock_health >= 60:
                stock_score = 20
            else:
                stock_score = 10
        else:
            stock_score = 0
        score += stock_score

        # نقاط سرعة التنفيذ (30 نقطة)
        if avg_fulfillment_hours <= 24:
            speed_score = 30
        elif avg_fulfillment_hours <= 48:
            speed_score = 20
        elif avg_fulfillment_hours <= 72:
            speed_score = 10
        else:
            speed_score = 5
        score += speed_score

        # تحديد مستوى التحسين
        if score >= 80:
            optimization_level = 'محسن بشكل ممتاز'
        elif score >= 60:
            optimization_level = 'محسن بشكل جيد'
        elif score >= 40:
            optimization_level = 'يحتاج تحسين متوسط'
        else:
            optimization_level = 'يحتاج تحسين كبير'

        return {
            'total_score': round(score, 2),
            'optimization_level': optimization_level,
            'details': {
                'warehouse_score': warehouse_score,
                'stock_score': stock_score,
                'speed_score': speed_score
            }
        }

    def _generate_optimization_recommendations(self, linked_warehouses: int, primary_warehouses: int,
                                             low_stock_products: int, available_products: int,
                                             avg_fulfillment_hours: float) -> List[str]:
        """إنشاء توصيات التحسين"""
        recommendations = []

        # توصيات المستودعات
        if primary_warehouses == 0:
            recommendations.append("🔧 تحديد مستودع أساسي للفرع لتحسين كفاءة التوزيع")

        if linked_warehouses == 0:
            recommendations.append("🔗 ربط الفرع بمستودع واحد على الأقل")
        elif linked_warehouses > 5:
            recommendations.append("📉 تقليل عدد المستودعات المرتبطة لتبسيط التوزيع")

        # توصيات المخزون
        if available_products > 0:
            stock_health = ((available_products - low_stock_products) / available_products) * 100
            if stock_health < 75:
                recommendations.append(f"📦 تحسين مستويات المخزون - {low_stock_products} منتج بمخزون منخفض")

        # توصيات السرعة
        if avg_fulfillment_hours > 48:
            recommendations.append("⚡ تسريع عملية تنفيذ طلبات التحويل")

        # توصيات عامة
        if len(recommendations) == 0:
            recommendations.append("✅ النظام محسن بشكل جيد - استمر في المراقبة")

        return recommendations


def get_advanced_reports_service(db_session: Session) -> AdvancedReportsService:
    """
    دالة مساعدة للحصول على instance من خدمة التقارير المتقدمة

    Args:
        db_session: جلسة قاعدة البيانات

    Returns:
        instance من AdvancedReportsService
    """
    return AdvancedReportsService.get_instance(db_session)
