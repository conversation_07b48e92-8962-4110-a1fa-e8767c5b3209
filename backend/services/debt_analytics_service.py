"""
خدمة تحليل المديونية المتقدمة
تطبق مبادئ البرمجة الكائنية وتضمن الدقة المطلقة في الحسابات
"""

from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, case, text
import logging

from models.customer import Customer, CustomerDebt, DebtPayment
from utils.datetime_utils import get_tripoli_now

logger = logging.getLogger(__name__)

@dataclass
class DebtSummary:
    """ملخص المديونية"""
    total_debts: int
    total_amount: float
    paid_amount: float
    remaining_amount: float
    collection_rate: float
    average_debt_age: float
    overdue_debts: int
    overdue_amount: float
    unique_debtors: int
    average_debt_amount: float
    max_debt: float
    min_debt: float

@dataclass
class DebtAging:
    """أعمار الديون"""
    range_name: str
    count: int
    amount: float
    percentage: float
    start_days: Optional[int]
    end_days: Optional[int]

@dataclass
class DebtTrend:
    """اتجاهات المديونية"""
    period: str
    new_debts: int
    new_amount: float
    paid_debts: int
    paid_amount: float
    net_change: float

@dataclass
class CollectionEfficiency:
    """كفاءة التحصيل"""
    period: str
    target_collection: float
    actual_collection: float
    efficiency: float
    variance: float

class DebtAnalyticsService:
    """
    خدمة تحليل المديونية المتقدمة
    تطبق مبادئ البرمجة الكائنية مع نمط Singleton
    """
    
    _instance: Optional['DebtAnalyticsService'] = None
    
    def __new__(cls) -> 'DebtAnalyticsService':
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized'):
            self._initialized = True
            logger.info("تم تهيئة خدمة تحليل المديونية")
    
    def get_debt_summary(self, db: Session) -> DebtSummary:
        """
        الحصول على ملخص شامل للمديونية
        """
        try:
            logger.info("بدء حساب ملخص المديونية...")
            
            # 1. الإحصائيات الأساسية
            basic_stats = self._get_basic_debt_stats(db)
            
            # 2. حساب المبالغ المدفوعة والمتبقية
            payment_stats = self._get_payment_stats(db)
            
            # 3. إحصائيات العملاء
            customer_stats = self._get_customer_debt_stats(db)
            
            # 4. الديون المتأخرة
            overdue_stats = self._get_overdue_debt_stats(db)
            
            # 5. متوسط عمر الديون
            avg_age = self._get_average_debt_age(db)
            
            # 6. معدل التحصيل
            collection_rate = (payment_stats['paid_amount'] / basic_stats['total_amount'] * 100) if basic_stats['total_amount'] > 0 else 0
            
            summary = DebtSummary(
                total_debts=basic_stats['total_debts'],
                total_amount=basic_stats['total_amount'],
                paid_amount=payment_stats['paid_amount'],
                remaining_amount=payment_stats['remaining_amount'],
                collection_rate=round(collection_rate, 2),
                average_debt_age=round(avg_age, 1),
                overdue_debts=overdue_stats['count'],
                overdue_amount=overdue_stats['amount'],
                unique_debtors=customer_stats['unique_debtors'],
                average_debt_amount=customer_stats['average_amount'],
                max_debt=customer_stats['max_debt'],
                min_debt=customer_stats['min_debt']
            )
            
            logger.info(f"تم حساب ملخص المديونية بنجاح: {summary.remaining_amount:.2f} د.ل متبقي")
            return summary
            
        except Exception as e:
            logger.error(f"خطأ في حساب ملخص المديونية: {e}")
            raise
    
    def _get_basic_debt_stats(self, db: Session) -> Dict[str, Any]:
        """حساب الإحصائيات الأساسية للديون"""
        total_debts = db.query(func.count(CustomerDebt.id)).scalar() or 0
        total_amount = db.query(func.sum(CustomerDebt.amount)).scalar() or 0.0
        
        return {
            'total_debts': total_debts,
            'total_amount': float(total_amount)
        }
    
    def _get_payment_stats(self, db: Session) -> Dict[str, float]:
        """حساب إحصائيات المدفوعات والمبالغ المتبقية"""
        
        # إجمالي المدفوعات
        paid_amount = db.query(func.sum(DebtPayment.amount)).scalar() or 0.0
        
        # المبلغ المتبقي الدقيق - فقط الديون غير المدفوعة
        remaining_query = db.query(
            func.sum(
                CustomerDebt.amount - func.coalesce(
                    db.query(func.sum(DebtPayment.amount))
                    .filter(DebtPayment.debt_id == CustomerDebt.id)
                    .scalar_subquery(), 0
                )
            )
        ).filter(CustomerDebt.is_paid == False)
        
        remaining_amount = max(0.0, remaining_query.scalar() or 0.0)
        
        return {
            'paid_amount': float(paid_amount),
            'remaining_amount': float(remaining_amount)
        }
    
    def _get_customer_debt_stats(self, db: Session) -> Dict[str, Any]:
        """حساب إحصائيات العملاء المدينين"""
        
        # حساب المبلغ المتبقي لكل عميل
        customer_debts = db.query(
            CustomerDebt.customer_id,
            func.sum(
                CustomerDebt.amount - func.coalesce(
                    db.query(func.sum(DebtPayment.amount))
                    .filter(DebtPayment.debt_id == CustomerDebt.id)
                    .scalar_subquery(), 0
                )
            ).label('remaining_debt')
        ).filter(
            CustomerDebt.is_paid == False
        ).group_by(CustomerDebt.customer_id).all()
        
        # فلترة العملاء الذين لديهم ديون متبقية فعلية
        active_debts = [float(debt.remaining_debt) for debt in customer_debts if debt.remaining_debt > 0]
        
        if active_debts:
            return {
                'unique_debtors': len(active_debts),
                'average_amount': sum(active_debts) / len(active_debts),
                'max_debt': max(active_debts),
                'min_debt': min(active_debts)
            }
        else:
            return {
                'unique_debtors': 0,
                'average_amount': 0.0,
                'max_debt': 0.0,
                'min_debt': 0.0
            }
    
    def _get_overdue_debt_stats(self, db: Session) -> Dict[str, Any]:
        """حساب إحصائيات الديون المتأخرة (أكثر من 30 يوم)"""
        # استخدام خدمة التاريخ والوقت الموحدة للمشروع
        current_time = get_tripoli_now()
        thirty_days_ago = current_time - timedelta(days=30)
        
        # عدد الديون المتأخرة
        overdue_count = db.query(func.count(CustomerDebt.id)).filter(
            and_(
                CustomerDebt.is_paid == False,
                CustomerDebt.created_at < thirty_days_ago
            )
        ).scalar() or 0
        
        # مبلغ الديون المتأخرة
        overdue_amount_query = db.query(
            func.sum(
                CustomerDebt.amount - func.coalesce(
                    db.query(func.sum(DebtPayment.amount))
                    .filter(DebtPayment.debt_id == CustomerDebt.id)
                    .scalar_subquery(), 0
                )
            )
        ).filter(
            and_(
                CustomerDebt.is_paid == False,
                CustomerDebt.created_at < thirty_days_ago
            )
        )
        
        overdue_amount = max(0.0, overdue_amount_query.scalar() or 0.0)
        
        return {
            'count': overdue_count,
            'amount': float(overdue_amount)
        }
    
    def _get_average_debt_age(self, db: Session) -> float:
        """حساب متوسط عمر الديون بالأيام"""
        # استخدام خدمة التاريخ والوقت الموحدة للمشروع
        current_time = get_tripoli_now()

        avg_age_result = db.query(
            func.avg(
                func.extract('epoch', current_time - CustomerDebt.created_at) / 86400.0
            )
        ).filter(CustomerDebt.is_paid == False).scalar()

        return float(avg_age_result or 0)

    def get_debt_aging(self, db: Session) -> List[DebtAging]:
        """
        الحصول على تقرير أعمار الديون
        """
        try:
            logger.info("بدء حساب تقرير أعمار الديون...")

            # استخدام خدمة التاريخ والوقت الموحدة للمشروع
            current_time = get_tripoli_now()

            aging_ranges = [
                ("0-30 يوم", 0, 30),
                ("31-60 يوم", 31, 60),
                ("61-90 يوم", 61, 90),
                ("أكثر من 90 يوم", 91, None)
            ]

            # حساب إجمالي المبلغ المتبقي للنسب المئوية
            total_remaining = self._get_payment_stats(db)['remaining_amount']

            aging_data = []

            for range_name, start_days, end_days in aging_ranges:
                # حساب التواريخ بطريقة صحيحة لتجنب التداخل
                if end_days is None:
                    # أكثر من 90 يوم - الديون الأقدم من start_days
                    end_date = current_time - timedelta(days=start_days)
                    start_date = None  # لا حد أدنى
                else:
                    # الديون بين start_days و end_days (حصرياً)
                    end_date = current_time - timedelta(days=start_days)
                    start_date = current_time - timedelta(days=end_days + 1)  # +1 لتجنب التداخل

                # حساب البيانات لهذه الفترة
                period_data = self._get_aging_period_data(db, start_date, end_date)

                # حساب النسبة المئوية
                percentage = (period_data['amount'] / total_remaining * 100) if total_remaining > 0 else 0

                aging_item = DebtAging(
                    range_name=range_name,
                    count=period_data['count'],
                    amount=period_data['amount'],
                    percentage=round(percentage, 2),
                    start_days=start_days,
                    end_days=end_days
                )

                aging_data.append(aging_item)

                logger.info(f"فترة {range_name}: {aging_item.count} دين، {aging_item.amount:.2f} د.ل ({aging_item.percentage:.2f}%)")

            # التحقق من دقة البيانات
            total_aging_amount = sum(item.amount for item in aging_data)
            total_aging_count = sum(item.count for item in aging_data)

            logger.info(f"مجموع أعمار الديون: {total_aging_amount:.2f} د.ل، {total_aging_count} دين")
            logger.info(f"إجمالي المبلغ المتبقي: {total_remaining:.2f} د.ل")

            if abs(total_aging_amount - total_remaining) > 1:
                logger.warning(f"تحذير: فرق في المبالغ {abs(total_aging_amount - total_remaining):.2f} د.ل")

            return aging_data

        except Exception as e:
            logger.error(f"خطأ في حساب تقرير أعمار الديون: {e}")
            raise

    def _get_aging_period_data(self, db: Session, start_date: datetime, end_date: Optional[datetime]) -> Dict[str, Any]:
        """حساب بيانات فترة عمرية محددة بطريقة دقيقة"""

        # استعلام واحد دقيق للديون في هذه الفترة مع المبلغ المتبقي
        aging_query = db.query(
            CustomerDebt.id,
            CustomerDebt.amount,
            func.coalesce(
                db.query(func.sum(DebtPayment.amount))
                .filter(DebtPayment.debt_id == CustomerDebt.id)
                .scalar_subquery(), 0
            ).label('paid_amount')
        ).filter(CustomerDebt.is_paid == False)

        # تطبيق فلاتر التاريخ
        if start_date is not None:
            aging_query = aging_query.filter(CustomerDebt.created_at > start_date)  # > بدلاً من >=
        if end_date is not None:
            aging_query = aging_query.filter(CustomerDebt.created_at <= end_date)

        # جلب النتائج
        results = aging_query.all()

        # حساب العدد والمبلغ المتبقي
        count = 0
        total_amount = 0.0

        for result in results:
            remaining = max(0.0, result.amount - result.paid_amount)
            if remaining > 0:  # فقط الديون التي لها مبلغ متبقي فعلي
                count += 1
                total_amount += remaining

        return {
            'count': count,
            'amount': float(total_amount)
        }

    def get_debt_trends(self, db: Session, period: str = "month") -> List[DebtTrend]:
        """
        الحصول على اتجاهات المديونية
        """
        try:
            logger.info(f"بدء حساب اتجاهات المديونية للفترة: {period}")

            # استخدام خدمة التاريخ والوقت الموحدة للمشروع
            current_time = get_tripoli_now()

            # البيانات في قاعدة البيانات محفوظة مع معلومات المنطقة الزمنية
            # لذا نستخدم current_time مباشرة للمقارنة

            # تحديد الفترة الزمنية مع ضمان وجود بيانات كافية
            if period == "day":
                start_date = current_time - timedelta(days=30)  # آخر 30 يوم
            elif period == "week":
                start_date = current_time - timedelta(weeks=12)  # آخر 12 أسبوع
            elif period == "month":
                start_date = current_time - timedelta(days=180)  # آخر 6 أشهر
            else:  # year
                start_date = current_time - timedelta(days=730)  # آخر سنتين

            # جلب الديون الجديدة
            new_debts_data = self._get_new_debts_trends(db, start_date, period)

            # جلب المدفوعات
            payments_data = self._get_payments_trends(db, start_date, period)

            # إنشاء فترات كاملة لضمان استمرارية البيانات
            complete_periods = self._generate_complete_periods(start_date, current_time, period)

            # دمج البيانات مع الفترات الكاملة
            trends = []

            for period_key in complete_periods:
                new_debt_info = new_debts_data.get(period_key, {'count': 0, 'amount': 0.0})
                payment_info = payments_data.get(period_key, {'count': 0, 'amount': 0.0})

                net_change = new_debt_info['amount'] - payment_info['amount']

                trend = DebtTrend(
                    period=period_key,
                    new_debts=new_debt_info['count'],
                    new_amount=new_debt_info['amount'],
                    paid_debts=payment_info['count'],
                    paid_amount=payment_info['amount'],
                    net_change=net_change
                )

                trends.append(trend)

            logger.info(f"تم حساب {len(trends)} فترة في اتجاهات المديونية")
            return trends

        except Exception as e:
            logger.error(f"خطأ في حساب اتجاهات المديونية: {e}")
            raise

    def _get_new_debts_trends(self, db: Session, start_date: datetime, period: str) -> Dict[str, Dict[str, Any]]:
        """جلب اتجاهات الديون الجديدة"""
        postgres_format = self._get_postgres_date_format(period)

        new_debts_query = db.query(
            func.to_char(CustomerDebt.created_at, postgres_format).label('period'),
            func.count(CustomerDebt.id).label('new_debts'),
            func.sum(CustomerDebt.amount).label('new_amount')
        ).filter(
            CustomerDebt.created_at >= start_date
        ).group_by(
            func.to_char(CustomerDebt.created_at, postgres_format)
        ).all()

        return {
            row.period: {
                'count': row.new_debts,
                'amount': float(row.new_amount or 0)
            }
            for row in new_debts_query
        }

    def _get_payments_trends(self, db: Session, start_date: datetime, period: str) -> Dict[str, Dict[str, Any]]:
        """جلب اتجاهات المدفوعات"""
        postgres_format = self._get_postgres_date_format(period)

        payments_query = db.query(
            func.to_char(DebtPayment.created_at, postgres_format).label('period'),
            func.count(DebtPayment.id).label('paid_debts'),
            func.sum(DebtPayment.amount).label('paid_amount')
        ).filter(
            DebtPayment.created_at >= start_date
        ).group_by(
            func.to_char(DebtPayment.created_at, postgres_format)
        ).all()

        return {
            row.period: {
                'count': row.paid_debts,
                'amount': float(row.paid_amount or 0)
            }
            for row in payments_query
        }

    def _get_postgres_date_format(self, period: str) -> str:
        """الحصول على تنسيق PostgreSQL المناسب للفترة"""
        format_mapping = {
            "day": "YYYY-MM-DD",
            "week": "YYYY-\"W\"IW",  # IW للأسبوع ISO
            "month": "YYYY-MM",
            "year": "YYYY"
        }
        return format_mapping.get(period, "YYYY-MM-DD")

    def _generate_complete_periods(self, start_date: datetime, end_date: datetime, period: str) -> List[str]:
        """إنشاء قائمة كاملة من الفترات لضمان استمرارية البيانات"""

        periods = []
        current = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)

        if period == "day":
            while current <= end:
                periods.append(current.strftime("%Y-%m-%d"))
                current += timedelta(days=1)

        elif period == "week":
            # البدء من بداية الأسبوع (الاثنين)
            current = current - timedelta(days=current.weekday())
            while current <= end:
                # استخدام ISO week number
                year, week_num, _ = current.isocalendar()
                periods.append(f"{year}-W{week_num:02d}")
                current += timedelta(weeks=1)

        elif period == "month":
            # البدء من بداية الشهر
            current = current.replace(day=1)
            while current <= end:
                periods.append(current.strftime("%Y-%m"))
                # الانتقال للشهر التالي بطريقة آمنة
                if current.month == 12:
                    current = current.replace(year=current.year + 1, month=1)
                else:
                    current = current.replace(month=current.month + 1)

        else:  # year
            # البدء من بداية السنة
            current = current.replace(month=1, day=1)
            while current <= end:
                periods.append(current.strftime("%Y"))
                current = current.replace(year=current.year + 1)

        return sorted(list(set(periods)))  # إزالة التكرارات

    def _generate_efficiency_periods(self, current_time: datetime, period: str) -> List[tuple]:
        """
        إنشاء فترات دقيقة لحساب كفاءة التحصيل
        يعيد قائمة من tuples: (period_start, period_end, period_name)
        """
        periods = []

        if period == "day":
            # آخر 7 أيام
            for i in range(7):
                date = current_time - timedelta(days=i)
                period_start = date.replace(hour=0, minute=0, second=0, microsecond=0)
                period_end = date.replace(hour=23, minute=59, second=59, microsecond=999999)
                period_name = date.strftime("%Y-%m-%d")
                periods.append((period_start, period_end, period_name))

        elif period == "week":
            # آخر 4 أسابيع
            for i in range(4):
                # حساب بداية الأسبوع (الاثنين)
                days_since_monday = current_time.weekday()
                week_start = current_time - timedelta(days=days_since_monday + (7 * i))
                week_start = week_start.replace(hour=0, minute=0, second=0, microsecond=0)

                # نهاية الأسبوع (الأحد)
                week_end = week_start + timedelta(days=6, hours=23, minutes=59, seconds=59, microseconds=999999)

                # تنسيق اسم الأسبوع بطريقة ISO
                year, week_num, _ = week_start.isocalendar()
                period_name = f"{year}-W{week_num:02d}"

                periods.append((week_start, week_end, period_name))

        elif period == "month":
            # آخر 6 شهور
            for i in range(6):
                # حساب الشهر الصحيح
                year = current_time.year
                month = current_time.month - i

                # تعديل السنة إذا كان الشهر سالب
                while month <= 0:
                    year -= 1
                    month += 12

                # بداية الشهر
                month_start = datetime(year, month, 1, tzinfo=current_time.tzinfo)

                # نهاية الشهر
                if month == 12:
                    next_month_start = datetime(year + 1, 1, 1, tzinfo=current_time.tzinfo)
                else:
                    next_month_start = datetime(year, month + 1, 1, tzinfo=current_time.tzinfo)

                month_end = next_month_start - timedelta(microseconds=1)

                period_name = f"{year}-{month:02d}"
                periods.append((month_start, month_end, period_name))

        else:  # year
            # آخر 3 سنوات
            for i in range(3):
                year = current_time.year - i
                year_start = datetime(year, 1, 1, tzinfo=current_time.tzinfo)
                year_end = datetime(year, 12, 31, 23, 59, 59, 999999, tzinfo=current_time.tzinfo)
                period_name = str(year)
                periods.append((year_start, year_end, period_name))

        return periods

    def get_collection_efficiency(self, db: Session, period: str = "month") -> List[CollectionEfficiency]:
        """
        الحصول على كفاءة التحصيل
        """
        try:
            logger.info(f"بدء حساب كفاءة التحصيل للفترة: {period}")

            # استخدام خدمة التاريخ والوقت الموحدة للمشروع
            current_time = get_tripoli_now()

            # تحديد الفترات بطريقة دقيقة تتطابق مع PostgreSQL
            periods = self._generate_efficiency_periods(current_time, period)

            efficiency_data = []

            # ترتيب الفترات من الأقدم للأحدث
            periods.reverse()

            for period_start, period_end, period_name in periods:
                # الديون المستحقة في هذه الفترة (الديون المنشأة)
                target_collection = db.query(func.sum(CustomerDebt.amount)).filter(
                    and_(
                        CustomerDebt.created_at >= period_start,
                        CustomerDebt.created_at < period_end
                    )
                ).scalar() or 0.0

                # المبلغ المحصل فعلياً في نفس الفترة
                actual_collection = db.query(func.sum(DebtPayment.amount)).filter(
                    and_(
                        DebtPayment.created_at >= period_start,
                        DebtPayment.created_at < period_end
                    )
                ).scalar() or 0.0

                # حساب الكفاءة والانحراف
                efficiency = (actual_collection / target_collection * 100) if target_collection > 0 else 0
                variance = actual_collection - target_collection

                efficiency_item = CollectionEfficiency(
                    period=period_name,
                    target_collection=float(target_collection),
                    actual_collection=float(actual_collection),
                    efficiency=round(efficiency, 2),
                    variance=float(variance)
                )

                efficiency_data.append(efficiency_item)

                logger.info(f"كفاءة {period_name}: {efficiency:.2f}% (مستهدف: {target_collection:.2f}, فعلي: {actual_collection:.2f})")

            return efficiency_data

        except Exception as e:
            logger.error(f"خطأ في حساب كفاءة التحصيل: {e}")
            raise

    def get_top_debtors(self, db: Session, limit: int = 10) -> List[Dict[str, Any]]:
        """
        الحصول على أكبر المدينين
        """
        try:
            logger.info(f"بدء جلب أكبر {limit} مدينين...")

            # جلب العملاء مع ديونهم
            top_debtors_query = db.query(
                Customer.id,
                Customer.name,
                func.sum(CustomerDebt.amount).label('total_debt'),
                func.count(CustomerDebt.id).label('debt_count'),
                func.min(CustomerDebt.created_at).label('oldest_debt_date')
            ).join(
                CustomerDebt, Customer.id == CustomerDebt.customer_id
            ).filter(
                CustomerDebt.is_paid == False
            ).group_by(
                Customer.id, Customer.name
            ).all()

            # حساب المبلغ المتبقي لكل عميل وترتيبهم
            customer_debts = []
            # استخدام خدمة التاريخ والوقت الموحدة للمشروع
            current_time = get_tripoli_now()

            for result in top_debtors_query:
                # حساب المبلغ المتبقي الدقيق
                remaining_debt = self._get_customer_remaining_debt(db, result.id)

                if remaining_debt > 0:  # فقط العملاء الذين لديهم ديون متبقية
                    # تحديد مستوى المخاطر بناءً على المبلغ المتبقي وفترة التأخير
                    days_old = (current_time - result.oldest_debt_date).days
                    risk_level = self._calculate_risk_level(remaining_debt, days_old)

                    # حساب معدل السداد
                    payment_rate = ((result.total_debt - remaining_debt) / result.total_debt * 100) if result.total_debt > 0 else 0

                    # تحديد وصف مستوى المخاطر
                    risk_descriptions = {
                        "low": "منخفضة",
                        "medium": "متوسطة",
                        "high": "عالية",
                        "critical": "حرجة"
                    }

                    customer_debts.append({
                        'id': result.id,
                        'name': result.name,
                        'totalDebt': float(result.total_debt),
                        'remainingDebt': remaining_debt,
                        'debtCount': result.debt_count,
                        'oldestDebtDate': result.oldest_debt_date.isoformat(),
                        'daysPastDue': days_old,
                        'paymentRate': round(payment_rate, 2),
                        'riskLevel': risk_level,
                        'riskDescription': risk_descriptions.get(risk_level, "غير محدد")
                    })

            # ترتيب حسب المبلغ المتبقي
            customer_debts.sort(key=lambda x: x['remainingDebt'], reverse=True)

            # إرجاع أكبر المدينين
            top_debtors = customer_debts[:limit]

            logger.info(f"تم جلب {len(top_debtors)} من أكبر المدينين")
            return top_debtors

        except Exception as e:
            logger.error(f"خطأ في جلب أكبر المدينين: {e}")
            raise

    def _get_customer_remaining_debt(self, db: Session, customer_id: int) -> float:
        """حساب المبلغ المتبقي لعميل محدد"""
        remaining_debt_query = db.query(
            func.sum(
                CustomerDebt.amount - func.coalesce(
                    db.query(func.sum(DebtPayment.amount))
                    .filter(DebtPayment.debt_id == CustomerDebt.id)
                    .scalar_subquery(), 0
                )
            )
        ).filter(
            and_(
                CustomerDebt.customer_id == customer_id,
                CustomerDebt.is_paid == False
            )
        )

        return max(0.0, remaining_debt_query.scalar() or 0.0)

    def _calculate_risk_level(self, remaining_debt: float, days_old: int) -> str:
        """
        حساب مستوى المخاطر بناءً على المبلغ المتبقي وفترة التأخير

        Args:
            remaining_debt: المبلغ المتبقي
            days_old: عدد أيام التأخير

        Returns:
            مستوى المخاطر: low, medium, high, critical
        """

        # تحديد نقاط المخاطر بناءً على المبلغ
        amount_risk_score = 0
        if remaining_debt >= 5000:  # مبلغ كبير جداً
            amount_risk_score = 4
        elif remaining_debt >= 2000:  # مبلغ كبير
            amount_risk_score = 3
        elif remaining_debt >= 500:   # مبلغ متوسط
            amount_risk_score = 2
        elif remaining_debt >= 100:   # مبلغ صغير
            amount_risk_score = 1
        else:  # مبلغ صغير جداً
            amount_risk_score = 0

        # تحديد نقاط المخاطر بناءً على فترة التأخير
        time_risk_score = 0
        if days_old >= 180:    # أكثر من 6 أشهر
            time_risk_score = 4
        elif days_old >= 90:   # 3-6 أشهر
            time_risk_score = 3
        elif days_old >= 60:   # 2-3 أشهر
            time_risk_score = 2
        elif days_old >= 30:   # شهر - شهرين
            time_risk_score = 1
        else:  # أقل من شهر
            time_risk_score = 0

        # حساب النقاط الإجمالية (وزن أكبر للمبلغ)
        total_risk_score = (amount_risk_score * 1.5) + time_risk_score

        # تحديد مستوى المخاطر النهائي
        if total_risk_score >= 7:      # مخاطر حرجة
            return "critical"
        elif total_risk_score >= 5:    # مخاطر عالية
            return "high"
        elif total_risk_score >= 3:    # مخاطر متوسطة
            return "medium"
        else:                          # مخاطر منخفضة
            return "low"
