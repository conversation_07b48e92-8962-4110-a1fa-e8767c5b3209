"""
خدمة حساب المديونية - DebtCalculationService
خدمة متخصصة لحساب المبالغ والحالات في نظام المديونية بدقة عالية
"""

from typing import Dict, List, Optional
from dataclasses import dataclass
from enum import Enum
from sqlalchemy.orm import Session
from sqlalchemy import select, func
import logging

from models.customer import CustomerDebt, DebtPayment

# إعداد التسجيل
logger = logging.getLogger(__name__)

class DebtStatus(Enum):
    """حالات الدين"""
    UNPAID = "unpaid"           # غير مدفوع
    PARTIALLY_PAID = "partial"  # مدفوع جزئياً
    FULLY_PAID = "paid"         # مدفوع بالكامل

@dataclass
class DebtCalculationResult:
    """نتيجة حساب الدين"""
    debt_id: int
    total_amount: float
    paid_amount: float
    remaining_amount: float
    status: DebtStatus
    is_paid: bool
    payment_count: int
    last_payment_date: Optional[str] = None

@dataclass
class PaymentBreakdown:
    """تفصيل الدفعات"""
    direct_payments: float      # الدفعات المباشرة على الدين
    invoice_payments: float     # الدفعات من خلال الفاتورة
    total_payments: float       # إجمالي الدفعات

class DebtCalculationService:
    """
    خدمة حساب المديونية
    تطبق مبادئ البرمجة الكائنية لحساب المبالغ والحالات بدقة
    """
    
    _instance = None
    
    def __new__(cls):
        """تطبيق نمط Singleton"""
        if cls._instance is None:
            cls._instance = super(DebtCalculationService, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """تهيئة الخدمة"""
        if not hasattr(self, 'initialized'):
            self.initialized = True
            logger.info("تم تهيئة خدمة حساب المديونية")
    
    def calculate_debt_amounts(self, db: Session, debt_id: int) -> DebtCalculationResult:
        """
        حساب مبالغ الدين بدقة
        
        Args:
            db: جلسة قاعدة البيانات
            debt_id: معرف الدين
            
        Returns:
            DebtCalculationResult: نتيجة الحساب
        """
        try:
            # الحصول على بيانات الدين
            debt = db.execute(
                select(CustomerDebt).where(CustomerDebt.id == debt_id)
            ).scalar_one_or_none()
            
            if not debt:
                raise ValueError(f"الدين غير موجود: {debt_id}")
            
            # حساب إجمالي الدفعات
            payment_breakdown = self._calculate_payment_breakdown(db, debt_id)
            
            # حساب المبلغ المتبقي
            remaining_amount = max(0.0, debt.amount - payment_breakdown.total_payments)

            # حساب المبلغ المدفوع الفعلي (لا يتجاوز المبلغ الأصلي)
            actual_paid_amount = min(payment_breakdown.total_payments, debt.amount)

            # تحديد حالة الدين
            status = self._determine_debt_status(debt.amount, actual_paid_amount)
            
            # الحصول على تاريخ آخر دفعة
            last_payment_date = self._get_last_payment_date(db, debt_id)
            
            # عدد الدفعات
            payment_count = self._get_payment_count(db, debt_id)
            
            result = DebtCalculationResult(
                debt_id=debt_id,
                total_amount=debt.amount,
                paid_amount=actual_paid_amount,
                remaining_amount=remaining_amount,
                status=status,
                is_paid=(status == DebtStatus.FULLY_PAID),
                payment_count=payment_count,
                last_payment_date=last_payment_date
            )
            
            logger.debug(f"تم حساب الدين {debt_id}: {result}")
            return result
            
        except Exception as e:
            logger.error(f"خطأ في حساب الدين {debt_id}: {e}")
            raise
    
    def _calculate_payment_breakdown(self, db: Session, debt_id: int) -> PaymentBreakdown:
        """حساب تفصيل الدفعات"""
        try:
            # الحصول على جميع دفعات الدين
            payments = db.execute(
                select(DebtPayment).where(DebtPayment.debt_id == debt_id)
            ).scalars().all()
            
            direct_payments = 0.0
            invoice_payments = 0.0
            
            for payment in payments:
                if payment.invoice_id:
                    invoice_payments += payment.amount
                else:
                    direct_payments += payment.amount
            
            total_payments = direct_payments + invoice_payments
            
            return PaymentBreakdown(
                direct_payments=direct_payments,
                invoice_payments=invoice_payments,
                total_payments=total_payments
            )
            
        except Exception as e:
            logger.error(f"خطأ في حساب تفصيل الدفعات للدين {debt_id}: {e}")
            raise
    
    def _determine_debt_status(self, total_amount: float, paid_amount: float) -> DebtStatus:
        """تحديد حالة الدين"""
        if paid_amount <= 0:
            return DebtStatus.UNPAID
        elif paid_amount >= total_amount:
            return DebtStatus.FULLY_PAID
        else:
            return DebtStatus.PARTIALLY_PAID
    
    def _get_last_payment_date(self, db: Session, debt_id: int) -> Optional[str]:
        """الحصول على تاريخ آخر دفعة"""
        try:
            last_payment = db.execute(
                select(DebtPayment.created_at)
                .where(DebtPayment.debt_id == debt_id)
                .order_by(DebtPayment.created_at.desc())
                .limit(1)
            ).scalar_one_or_none()
            
            return last_payment.isoformat() if last_payment else None
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على تاريخ آخر دفعة للدين {debt_id}: {e}")
            return None
    
    def _get_payment_count(self, db: Session, debt_id: int) -> int:
        """الحصول على عدد الدفعات"""
        try:
            count = db.execute(
                select(func.count(DebtPayment.id))
                .where(DebtPayment.debt_id == debt_id)
            ).scalar() or 0
            
            return count
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على عدد الدفعات للدين {debt_id}: {e}")
            return 0
    
    def calculate_multiple_debts(self, db: Session, debt_ids: List[int]) -> Dict[int, DebtCalculationResult]:
        """حساب عدة ديون في مرة واحدة"""
        results = {}
        
        for debt_id in debt_ids:
            try:
                results[debt_id] = self.calculate_debt_amounts(db, debt_id)
            except Exception as e:
                logger.error(f"خطأ في حساب الدين {debt_id}: {e}")
                continue
        
        return results
    
    def update_debt_status(self, db: Session, debt_id: int) -> bool:
        """تحديث حالة الدين في قاعدة البيانات"""
        try:
            # حساب الحالة الجديدة
            calculation = self.calculate_debt_amounts(db, debt_id)

            # تحديث الدين
            debt = db.execute(
                select(CustomerDebt).where(CustomerDebt.id == debt_id)
            ).scalar_one_or_none()

            if not debt:
                return False

            debt.remaining_amount = calculation.remaining_amount
            debt.is_paid = calculation.is_paid
            debt.payment_status = calculation.status.value

            db.commit()

            logger.info(f"تم تحديث حالة الدين {debt_id}: {calculation.status.value}")
            return True

        except Exception as e:
            logger.error(f"خطأ في تحديث حالة الدين {debt_id}: {e}")
            db.rollback()
            return False
    
    def get_debt_status_summary(self, db: Session, customer_id: Optional[int] = None) -> Dict[str, int]:
        """الحصول على ملخص حالات الديون"""
        try:
            query = select(CustomerDebt)
            
            if customer_id:
                query = query.where(CustomerDebt.customer_id == customer_id)
            
            debts = db.execute(query).scalars().all()
            
            summary = {
                "unpaid": 0,
                "partial": 0,
                "paid": 0,
                "total": len(debts)
            }
            
            for debt in debts:
                calculation = self.calculate_debt_amounts(db, int(debt.id))
                summary[calculation.status.value] += 1
            
            return summary
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على ملخص حالات الديون: {e}")
            return {"unpaid": 0, "partial": 0, "paid": 0, "total": 0}

# إنشاء مثيل واحد من الخدمة
debt_calculation_service = DebtCalculationService()
