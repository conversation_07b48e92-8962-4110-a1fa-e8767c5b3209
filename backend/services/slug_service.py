"""
خدمة إنشاء الروابط (Slugs)
تطبق مبادئ البرمجة الكائنية مع نمط Singleton
"""

import re
import unicodedata
from typing import Dict, Any, Optional, List
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import text

from models.product import Product
from utils.datetime_utils import get_tripoli_now
import logging

logger = logging.getLogger(__name__)


class SlugService:
    """
    خدمة إنشاء الروابط المتقدمة
    تطبق مبادئ البرمجة الكائنية مع نمط Singleton
    """
    
    _instance: Optional['SlugService'] = None
    
    # قاموس ترجمة الأحرف العربية إلى اللاتينية
    ARABIC_TO_LATIN = {
        'ا': 'a', 'أ': 'a', 'إ': 'i', 'آ': 'aa',
        'ب': 'b', 'ت': 't', 'ث': 'th', 'ج': 'j',
        'ح': 'h', 'خ': 'kh', 'د': 'd', 'ذ': 'dh',
        'ر': 'r', 'ز': 'z', 'س': 's', 'ش': 'sh',
        'ص': 's', 'ض': 'd', 'ط': 't', 'ظ': 'z',
        'ع': 'a', 'غ': 'gh', 'ف': 'f', 'ق': 'q',
        'ك': 'k', 'ل': 'l', 'م': 'm', 'ن': 'n',
        'ه': 'h', 'و': 'w', 'ي': 'y', 'ى': 'a',
        'ة': 'h', 'ء': 'a'
    }
    
    # كلمات شائعة يجب تجاهلها
    STOP_WORDS = {
        'ar': ['في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'ذلك', 'تلك', 'التي', 'الذي'],
        'en': ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']
    }
    
    def __init__(self, db_session: Optional[Session] = None):
        """تهيئة خدمة الروابط"""
        self.db_session = db_session
        logger.info("تم تهيئة خدمة الروابط بنجاح")
    
    @classmethod
    def get_instance(cls, db_session: Optional[Session] = None) -> 'SlugService':
        """الحصول على مثيل وحيد من الخدمة"""
        if cls._instance is None:
            cls._instance = cls(db_session)
        elif db_session and cls._instance.db_session != db_session:
            cls._instance.db_session = db_session
        return cls._instance
    
    def transliterate_arabic(self, text: str) -> str:
        """ترجمة النص العربي إلى أحرف لاتينية"""
        try:
            result = ""
            for char in text:
                if char in self.ARABIC_TO_LATIN:
                    result += self.ARABIC_TO_LATIN[char]
                elif char.isalnum() or char in ['-', '_', ' ']:
                    result += char
                else:
                    result += '-'
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في ترجمة النص العربي: {e}")
            return text
    
    def normalize_text(self, text: str) -> str:
        """تطبيع النص وإزالة التشكيل"""
        try:
            # إزالة التشكيل العربي
            text = re.sub(r'[\u064B-\u0652\u0670\u0640]', '', text)
            
            # تطبيع Unicode
            text = unicodedata.normalize('NFKD', text)
            
            # تحويل إلى أحرف صغيرة
            text = text.lower()
            
            return text
            
        except Exception as e:
            logger.error(f"خطأ في تطبيع النص: {e}")
            return text.lower()
    
    def remove_stop_words(self, text: str, language: str = 'mixed') -> str:
        """إزالة الكلمات الشائعة"""
        try:
            words = text.split()
            filtered_words = []
            
            for word in words:
                word = word.strip()
                if not word:
                    continue
                
                # التحقق من الكلمات العربية
                if language in ['ar', 'mixed'] and word in self.STOP_WORDS['ar']:
                    continue
                
                # التحقق من الكلمات الإنجليزية
                if language in ['en', 'mixed'] and word in self.STOP_WORDS['en']:
                    continue
                
                filtered_words.append(word)
            
            return ' '.join(filtered_words)
            
        except Exception as e:
            logger.error(f"خطأ في إزالة الكلمات الشائعة: {e}")
            return text
    
    def create_slug(self, text: str, max_length: int = 100, preserve_arabic: bool = False) -> str:
        """إنشاء رابط من النص"""
        try:
            if not text or not text.strip():
                return f"product-{int(datetime.now().timestamp())}"
            
            # تطبيع النص
            normalized_text = self.normalize_text(text)
            
            # إزالة الكلمات الشائعة
            filtered_text = self.remove_stop_words(normalized_text)
            
            if not preserve_arabic:
                # ترجمة النص العربي إلى لاتيني
                filtered_text = self.transliterate_arabic(filtered_text)
            
            # تنظيف النص
            # إزالة الأحرف الخاصة والاحتفاظ بالأحرف والأرقام والمسافات والشرطات
            if preserve_arabic:
                # الاحتفاظ بالأحرف العربية
                cleaned_text = re.sub(r'[^\u0600-\u06FF\w\s\-]', '', filtered_text)
            else:
                # أحرف لاتينية فقط
                cleaned_text = re.sub(r'[^\w\s\-]', '', filtered_text)
            
            # استبدال المسافات بشرطات
            slug = re.sub(r'\s+', '-', cleaned_text.strip())
            
            # إزالة الشرطات المتتالية
            slug = re.sub(r'-+', '-', slug)
            
            # إزالة الشرطات من البداية والنهاية
            slug = slug.strip('-')
            
            # التحقق من الطول
            if len(slug) > max_length:
                slug = slug[:max_length].rstrip('-')
            
            # التأكد من وجود محتوى
            if not slug:
                slug = f"product-{int(datetime.now().timestamp())}"
            
            logger.info(f"تم إنشاء رابط: {slug} من النص: {text}")
            return slug
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء الرابط: {e}")
            return f"product-{int(datetime.now().timestamp())}"
    
    def check_slug_uniqueness(self, slug: str, exclude_product_id: Optional[int] = None) -> Dict[str, Any]:
        """التحقق من تفرد الرابط"""
        try:
            if not self.db_session:
                return {
                    'unique': False,
                    'error': 'جلسة قاعدة البيانات غير متوفرة'
                }
            
            # البحث في جدول المنتجات (إذا كان يحتوي على حقل slug)
            # نظراً لأن النموذج الحالي لا يحتوي على حقل slug، سنفترض أنه سيتم إضافته
            # أو يمكن استخدام حقل آخر مثل name للتحقق
            
            # للآن، سنتحقق من تفرد الاسم كبديل
            query = self.db_session.query(Product).filter(Product.name.ilike(f"%{slug}%"))
            
            if exclude_product_id:
                query = query.filter(Product.id != exclude_product_id)
            
            similar_products = query.limit(5).all()
            
            if similar_products:
                return {
                    'unique': False,
                    'similar_count': len(similar_products),
                    'similar_products': [
                        {
                            'id': product.id,
                            'name': product.name
                        } for product in similar_products
                    ],
                    'suggestion': f"{slug}-{len(similar_products) + 1}"
                }
            
            return {
                'unique': True,
                'message': 'الرابط متاح للاستخدام'
            }
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من تفرد الرابط: {e}")
            return {
                'unique': False,
                'error': f'خطأ في التحقق من تفرد الرابط: {str(e)}'
            }
    
    def generate_unique_slug(self, text: str, max_length: int = 100, preserve_arabic: bool = False, max_attempts: int = 10) -> Dict[str, Any]:
        """إنشاء رابط فريد مع التحقق من التفرد"""
        try:
            base_slug = self.create_slug(text, max_length - 10, preserve_arabic)  # ترك مساحة للرقم
            
            for attempt in range(max_attempts):
                if attempt == 0:
                    candidate_slug = base_slug
                else:
                    candidate_slug = f"{base_slug}-{attempt}"
                
                uniqueness_check = self.check_slug_uniqueness(candidate_slug)
                
                if uniqueness_check.get('unique', False):
                    return {
                        'success': True,
                        'slug': candidate_slug,
                        'original_text': text,
                        'attempts': attempt + 1
                    }
            
            # إذا فشلت جميع المحاولات، أضف timestamp
            timestamp_slug = f"{base_slug}-{int(datetime.now().timestamp())}"
            
            return {
                'success': True,
                'slug': timestamp_slug,
                'original_text': text,
                'attempts': max_attempts,
                'fallback': True
            }
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء رابط فريد: {e}")
            return {
                'success': False,
                'error': f'خطأ في إنشاء رابط فريد: {str(e)}'
            }
    
    def validate_slug(self, slug: str) -> Dict[str, Any]:
        """التحقق من صحة الرابط"""
        try:
            if not slug or not slug.strip():
                return {
                    'valid': False,
                    'error': 'الرابط لا يمكن أن يكون فارغاً'
                }
            
            # التحقق من الطول
            if len(slug) > 100:
                return {
                    'valid': False,
                    'error': 'الرابط طويل جداً (الحد الأقصى 100 حرف)'
                }
            
            if len(slug) < 3:
                return {
                    'valid': False,
                    'error': 'الرابط قصير جداً (الحد الأدنى 3 أحرف)'
                }
            
            # التحقق من الأحرف المسموحة
            if not re.match(r'^[\u0600-\u06FF\w\-]+$', slug):
                return {
                    'valid': False,
                    'error': 'الرابط يحتوي على أحرف غير مسموحة'
                }
            
            # التحقق من عدم البدء أو الانتهاء بشرطة
            if slug.startswith('-') or slug.endswith('-'):
                return {
                    'valid': False,
                    'error': 'الرابط لا يمكن أن يبدأ أو ينتهي بشرطة'
                }
            
            # التحقق من عدم وجود شرطات متتالية
            if '--' in slug:
                return {
                    'valid': False,
                    'error': 'الرابط لا يمكن أن يحتوي على شرطات متتالية'
                }
            
            return {
                'valid': True,
                'slug': slug,
                'length': len(slug)
            }
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من صحة الرابط: {e}")
            return {
                'valid': False,
                'error': f'خطأ في التحقق من صحة الرابط: {str(e)}'
            }
    
    def suggest_slugs(self, text: str, count: int = 5, preserve_arabic: bool = False) -> List[str]:
        """اقتراح عدة روابط بديلة"""
        try:
            suggestions = []
            base_slug = self.create_slug(text, preserve_arabic=preserve_arabic)
            
            # الرابط الأساسي
            suggestions.append(base_slug)
            
            # إضافة أرقام
            for i in range(1, count):
                suggestions.append(f"{base_slug}-{i}")
            
            # إضافة كلمات وصفية
            descriptive_words = ['new', 'pro', 'plus', 'premium', 'special']
            for word in descriptive_words[:count-len(suggestions)]:
                suggestions.append(f"{base_slug}-{word}")
            
            # إضافة تاريخ إذا لم نصل للعدد المطلوب
            if len(suggestions) < count:
                today = datetime.now().strftime("%Y%m%d")
                suggestions.append(f"{base_slug}-{today}")
            
            return suggestions[:count]
            
        except Exception as e:
            logger.error(f"خطأ في اقتراح الروابط: {e}")
            return [f"product-{int(datetime.now().timestamp())}"]
