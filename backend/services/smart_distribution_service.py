"""
خدمة التوزيع الذكي
تطبق مبادئ البرمجة الكائنية لاختيار المستودع الأمثل للتوريد
تدعم التوزيع الذكي بناءً على المسافة وتوفر المخزون والأولوية
"""

import logging
import math
from typing import Dict, Any, Optional, List, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, func, text, or_
from decimal import Decimal

from models.warehouse import Warehouse, WarehouseInventory
from models.branch import Branch, branch_warehouses
from models.product import Product
from utils.datetime_utils import get_tripoli_now, get_current_time_with_settings

logger = logging.getLogger(__name__)


class SmartDistributionService:
    """
    خدمة التوزيع الذكي
    تطبق مبادئ البرمجة الكائنية مع نمط Singleton
    """
    
    _instance: Optional['SmartDistributionService'] = None
    
    def __init__(self, db_session: Session):
        """تهيئة خدمة التوزيع الذكي"""
        self.db_session = db_session
        logger.info("تم تهيئة خدمة التوزيع الذكي بنجاح")

    def _get_current_time(self):
        """الحصول على الوقت الحالي بإعدادات المنطقة الزمنية"""
        return get_current_time_with_settings(self.db_session)
    
    @classmethod  # type: ignore
    def get_instance(cls, db_session: Session) -> 'SmartDistributionService':
        """الحصول على instance وحيد من الخدمة"""
        if cls._instance is None:
            cls._instance = cls(db_session)
        elif db_session and cls._instance.db_session != db_session:
            cls._instance.db_session = db_session
        return cls._instance
    
    def find_optimal_warehouse_for_branch(self, branch_id: int, product_id: int, 
                                        required_quantity: float,
                                        consider_distance: bool = True,
                                        max_distance_km: Optional[float] = None) -> Dict[str, Any]:
        """
        العثور على المستودع الأمثل للفرع لمنتج معين
        
        Args:
            branch_id: معرف الفرع
            product_id: معرف المنتج
            required_quantity: الكمية المطلوبة
            consider_distance: هل يتم اعتبار المسافة في الحساب
            max_distance_km: الحد الأقصى للمسافة بالكيلومتر
            
        Returns:
            المستودع الأمثل مع تفاصيل التقييم
        """
        try:
            # الحصول على معلومات الفرع
            branch = self.db_session.query(Branch).filter(
                Branch.id == branch_id, Branch.is_active == True
            ).first()
            
            if not branch:
                return {
                    'success': False,
                    'error': 'الفرع غير موجود أو غير نشط'
                }

            # الحصول على جميع المستودعات المتاحة مع المخزون
            warehouses_query = text("""
                SELECT 
                    w.id, w.name, w.code, w.address, w.is_main, w.is_active,
                    w.capacity_limit, w.current_capacity,
                    COALESCE(wi.quantity, 0) as available_quantity,
                    COALESCE(wi.reserved_quantity, 0) as reserved_quantity,
                    (COALESCE(wi.quantity, 0) - COALESCE(wi.reserved_quantity, 0)) as free_quantity,
                    CASE 
                        WHEN bw.branch_id IS NOT NULL THEN bw.is_primary
                        ELSE false
                    END as is_primary_for_branch,
                    CASE 
                        WHEN bw.branch_id IS NOT NULL THEN bw.priority
                        ELSE 999
                    END as priority_for_branch,
                    CASE 
                        WHEN bw.branch_id IS NOT NULL THEN true
                        ELSE false
                    END as is_linked_to_branch
                FROM warehouses w
                LEFT JOIN warehouse_inventory wi ON w.id = wi.warehouse_id AND wi.product_id = :product_id
                LEFT JOIN branch_warehouses bw ON w.id = bw.warehouse_id AND bw.branch_id = :branch_id
                WHERE w.is_active = true
                AND (COALESCE(wi.quantity, 0) - COALESCE(wi.reserved_quantity, 0)) >= :required_quantity
                ORDER BY 
                    is_linked_to_branch DESC,
                    is_primary_for_branch DESC,
                    priority_for_branch ASC,
                    free_quantity DESC
            """)

            warehouses_result = self.db_session.execute(warehouses_query, {
                "product_id": product_id,
                "branch_id": branch_id,
                "required_quantity": required_quantity
            }).fetchall()

            if not warehouses_result:
                return {
                    'success': False,
                    'error': 'لا يوجد مستودع متاح بالكمية المطلوبة',
                    'required_quantity': required_quantity
                }

            # تقييم المستودعات وترتيبها
            evaluated_warehouses = []
            
            for warehouse in warehouses_result:
                evaluation = self._evaluate_warehouse_for_branch(
                    warehouse, branch, required_quantity, consider_distance
                )
                evaluated_warehouses.append(evaluation)

            # ترتيب المستودعات حسب النقاط
            evaluated_warehouses.sort(key=lambda x: x['total_score'], reverse=True)

            # فلترة حسب المسافة إذا كان محدد
            if max_distance_km and consider_distance:
                evaluated_warehouses = [
                    w for w in evaluated_warehouses 
                    if w.get('estimated_distance_km', 0) <= max_distance_km
                ]

            if not evaluated_warehouses:
                return {
                    'success': False,
                    'error': f'لا يوجد مستودع ضمن المسافة المحددة ({max_distance_km} كم)'
                }

            optimal_warehouse = evaluated_warehouses[0]

            logger.info(f"✅ تم العثور على المستودع الأمثل {optimal_warehouse['name']} للفرع {branch.name}")

            return {
                'success': True,
                'optimal_warehouse': optimal_warehouse,
                'alternatives': evaluated_warehouses[1:5],  # أفضل 4 بدائل
                'evaluation_criteria': {
                    'consider_distance': consider_distance,
                    'max_distance_km': max_distance_km,
                    'required_quantity': required_quantity
                }
            }

        except Exception as e:
            logger.error(f"❌ خطأ في العثور على المستودع الأمثل: {e}")
            return {
                'success': False,
                'error': f'خطأ في العثور على المستودع الأمثل: {str(e)}'
            }

    def _evaluate_warehouse_for_branch(self, warehouse: Any, branch: Branch, 
                                     required_quantity: float, 
                                     consider_distance: bool = True) -> Dict[str, Any]:
        """
        تقييم مستودع معين للفرع
        
        Args:
            warehouse: بيانات المستودع
            branch: بيانات الفرع
            required_quantity: الكمية المطلوبة
            consider_distance: هل يتم اعتبار المسافة
            
        Returns:
            تقييم المستودع مع النقاط
        """
        try:
            score = 0.0
            evaluation_details = {}

            # 1. نقاط الربط بالفرع (40 نقطة)
            if warehouse.is_linked_to_branch:
                score += 40
                evaluation_details['branch_link_score'] = 40
                
                # نقاط إضافية للمستودع الأساسي
                if warehouse.is_primary_for_branch:
                    score += 20
                    evaluation_details['primary_bonus'] = 20
                
                # نقاط الأولوية (كلما قل الرقم، زادت النقاط)
                priority_score = max(0, 20 - warehouse.priority_for_branch)
                score += priority_score
                evaluation_details['priority_score'] = priority_score
            else:
                evaluation_details['branch_link_score'] = 0
                evaluation_details['primary_bonus'] = 0
                evaluation_details['priority_score'] = 0

            # 2. نقاط توفر المخزون (30 نقطة)
            free_quantity = float(warehouse.free_quantity)
            if free_quantity >= required_quantity:
                # نقاط أساسية للتوفر
                availability_score = 15
                
                # نقاط إضافية للكمية الزائدة
                excess_ratio = (free_quantity - required_quantity) / required_quantity
                excess_score = min(15, excess_ratio * 10)  # حد أقصى 15 نقطة
                
                availability_score += excess_score
                score += availability_score
                evaluation_details['availability_score'] = availability_score
            else:
                evaluation_details['availability_score'] = 0

            # 3. نقاط المستودع الرئيسي (10 نقاط)
            if warehouse.is_main:
                score += 10
                evaluation_details['main_warehouse_bonus'] = 10
            else:
                evaluation_details['main_warehouse_bonus'] = 0

            # 4. نقاط المسافة (20 نقطة) - إذا كان مطلوب
            distance_score = 0
            estimated_distance = 0
            
            if consider_distance:
                estimated_distance = self._estimate_distance(branch, warehouse)
                if estimated_distance <= 10:  # أقل من 10 كم
                    distance_score = 20
                elif estimated_distance <= 25:  # أقل من 25 كم
                    distance_score = 15
                elif estimated_distance <= 50:  # أقل من 50 كم
                    distance_score = 10
                elif estimated_distance <= 100:  # أقل من 100 كم
                    distance_score = 5
                # أكثر من 100 كم = 0 نقاط
                
                score += distance_score
            
            evaluation_details['distance_score'] = distance_score
            evaluation_details['estimated_distance_km'] = estimated_distance

            # إنشاء النتيجة النهائية
            warehouse_evaluation = {
                'id': warehouse.id,
                'name': warehouse.name,
                'code': warehouse.code,
                'address': warehouse.address,
                'is_main': warehouse.is_main,
                'is_linked_to_branch': warehouse.is_linked_to_branch,
                'is_primary_for_branch': warehouse.is_primary_for_branch,
                'priority_for_branch': warehouse.priority_for_branch,
                'available_quantity': float(warehouse.available_quantity),
                'reserved_quantity': float(warehouse.reserved_quantity),
                'free_quantity': free_quantity,
                'can_fulfill': free_quantity >= required_quantity,
                'estimated_distance_km': estimated_distance,
                'total_score': round(score, 2),
                'evaluation_details': evaluation_details
            }

            return warehouse_evaluation

        except Exception as e:
            logger.error(f"❌ خطأ في تقييم المستودع: {e}")
            return {
                'id': warehouse.id,
                'name': warehouse.name,
                'total_score': 0,
                'error': f'خطأ في التقييم: {str(e)}'
            }

    def _estimate_distance(self, branch: Branch, warehouse: Any) -> float:
        """
        تقدير المسافة بين الفرع والمستودع
        
        Args:
            branch: بيانات الفرع
            warehouse: بيانات المستودع
            
        Returns:
            المسافة المقدرة بالكيلومتر
        """
        try:
            # في الوقت الحالي، نستخدم تقدير بسيط بناءً على المدينة/المنطقة
            # يمكن تطوير هذا لاحقاً لاستخدام GPS أو خدمات الخرائط
            
            branch_city = (branch.city or '').lower().strip()
            branch_region = (branch.region or '').lower().strip()
            
            # استخراج المدينة من عنوان المستودع (تقدير بسيط)
            warehouse_address = (warehouse.address or '').lower()
            
            # إذا كانت نفس المدينة
            if branch_city and branch_city in warehouse_address:
                return 5.0  # 5 كم داخل نفس المدينة
            
            # إذا كانت نفس المنطقة
            if branch_region and branch_region in warehouse_address:
                return 25.0  # 25 كم داخل نفس المنطقة
            
            # مدن رئيسية في ليبيا - تقديرات تقريبية
            city_distances = {
                'طرابلس': {'بنغازي': 1000, 'مصراتة': 200, 'الزاوية': 50, 'صبراتة': 70},
                'بنغازي': {'طرابلس': 1000, 'مصراتة': 800, 'درنة': 300, 'البيضاء': 200},
                'مصراتة': {'طرابلس': 200, 'بنغازي': 800, 'سرت': 250},
                'الزاوية': {'طرابلس': 50, 'صبراتة': 30},
                'صبراتة': {'طرابلس': 70, 'الزاوية': 30}
            }
            
            # البحث عن المسافة في الجدول
            if branch_city in city_distances:
                for city, distance in city_distances[branch_city].items():
                    if city in warehouse_address:
                        return float(distance)
            
            # إذا لم نجد تطابق، نعطي تقدير افتراضي
            return 100.0  # 100 كم كتقدير افتراضي

        except Exception as e:
            logger.error(f"❌ خطأ في تقدير المسافة: {e}")
            return 100.0  # تقدير افتراضي في حالة الخطأ


    def optimize_multi_product_distribution(self, branch_id: int,
                                          products_requirements: List[Dict[str, Any]],
                                          consider_distance: bool = True) -> Dict[str, Any]:
        """
        تحسين التوزيع لعدة منتجات في نفس الوقت

        Args:
            branch_id: معرف الفرع
            products_requirements: قائمة المنتجات والكميات المطلوبة
            consider_distance: هل يتم اعتبار المسافة

        Returns:
            خطة التوزيع المحسنة
        """
        try:
            distribution_plan = {
                'branch_id': branch_id,
                'total_products': len(products_requirements),
                'warehouses_needed': {},
                'optimization_summary': {
                    'total_warehouses': 0,
                    'primary_warehouse_coverage': 0,
                    'linked_warehouses_coverage': 0,
                    'external_warehouses_needed': 0
                },
                'products_distribution': [],
                'recommendations': []
            }

            for product_req in products_requirements:
                product_id = product_req.get('product_id')
                required_quantity = product_req.get('quantity', 0)

                # التحقق من صحة البيانات
                if not product_id or not isinstance(product_id, int):
                    continue

                # العثور على المستودع الأمثل لهذا المنتج
                optimal_result = self.find_optimal_warehouse_for_branch(
                    branch_id, product_id, required_quantity, consider_distance
                )

                if optimal_result['success']:
                    optimal_warehouse = optimal_result['optimal_warehouse']
                    warehouse_id = optimal_warehouse['id']

                    # إضافة المنتج لخطة التوزيع
                    product_distribution = {
                        'product_id': product_id,
                        'required_quantity': required_quantity,
                        'assigned_warehouse': optimal_warehouse,
                        'alternatives': optimal_result.get('alternatives', [])
                    }
                    distribution_plan['products_distribution'].append(product_distribution)

                    # تجميع المستودعات المطلوبة
                    if warehouse_id not in distribution_plan['warehouses_needed']:
                        distribution_plan['warehouses_needed'][warehouse_id] = {
                            'warehouse_info': optimal_warehouse,
                            'products': [],
                            'total_items': 0
                        }

                    distribution_plan['warehouses_needed'][warehouse_id]['products'].append({
                        'product_id': product_id,
                        'quantity': required_quantity
                    })
                    distribution_plan['warehouses_needed'][warehouse_id]['total_items'] += 1

                    # تحديث الإحصائيات
                    if optimal_warehouse['is_primary_for_branch']:
                        distribution_plan['optimization_summary']['primary_warehouse_coverage'] += 1
                    elif optimal_warehouse['is_linked_to_branch']:
                        distribution_plan['optimization_summary']['linked_warehouses_coverage'] += 1
                    else:
                        distribution_plan['optimization_summary']['external_warehouses_needed'] += 1

                else:
                    # إضافة المنتج كغير متوفر
                    product_distribution = {
                        'product_id': product_id,
                        'required_quantity': required_quantity,
                        'assigned_warehouse': None,
                        'error': optimal_result.get('error', 'غير متوفر')
                    }
                    distribution_plan['products_distribution'].append(product_distribution)

            # تحديث الإحصائيات النهائية
            distribution_plan['optimization_summary']['total_warehouses'] = len(distribution_plan['warehouses_needed'])

            # إنشاء التوصيات
            distribution_plan['recommendations'] = self._generate_distribution_recommendations(distribution_plan)

            logger.info(f"✅ تم تحسين التوزيع لـ {len(products_requirements)} منتج للفرع {branch_id}")

            return {
                'success': True,
                'distribution_plan': distribution_plan
            }

        except Exception as e:
            logger.error(f"❌ خطأ في تحسين التوزيع متعدد المنتجات: {e}")
            return {
                'success': False,
                'error': f'خطأ في تحسين التوزيع: {str(e)}'
            }

    def _generate_distribution_recommendations(self, distribution_plan: Dict[str, Any]) -> List[str]:
        """
        إنشاء توصيات لتحسين التوزيع

        Args:
            distribution_plan: خطة التوزيع

        Returns:
            قائمة التوصيات
        """
        recommendations = []

        try:
            summary = distribution_plan['optimization_summary']
            total_warehouses = summary['total_warehouses']
            primary_coverage = summary['primary_warehouse_coverage']
            linked_coverage = summary['linked_warehouses_coverage']
            external_needed = summary['external_warehouses_needed']
            total_products = distribution_plan['total_products']

            # توصيات بناءً على عدد المستودعات
            if total_warehouses == 1:
                recommendations.append("✅ ممتاز! جميع المنتجات متوفرة في مستودع واحد")
            elif total_warehouses <= 3:
                recommendations.append("✅ جيد! المنتجات موزعة على عدد قليل من المستودعات")
            else:
                recommendations.append("⚠️ تحذير: المنتجات موزعة على عدد كبير من المستودعات")

            # توصيات بناءً على التغطية
            primary_percentage = (primary_coverage / total_products) * 100 if total_products > 0 else 0
            if primary_percentage >= 80:
                recommendations.append("✅ ممتاز! معظم المنتجات متوفرة في المستودع الأساسي")
            elif primary_percentage >= 50:
                recommendations.append("✅ جيد! نصف المنتجات متوفرة في المستودع الأساسي")
            else:
                recommendations.append("⚠️ اقتراح: فكر في تحديث المخزون في المستودع الأساسي")

            # توصيات للمستودعات الخارجية
            if external_needed > 0:
                recommendations.append(f"💡 اقتراح: ربط {external_needed} مستودع إضافي بالفرع لتحسين التوزيع")

            # توصيات للتحسين
            if total_warehouses > 2:
                recommendations.append("💡 اقتراح: فكر في توحيد المخزون لتقليل عدد المستودعات المطلوبة")

            return recommendations

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء التوصيات: {e}")
            return ["❌ خطأ في إنشاء التوصيات"]

    def get_branch_distribution_analytics(self, branch_id: int,
                                        days_back: int = 30) -> Dict[str, Any]:
        """
        الحصول على تحليلات التوزيع للفرع

        Args:
            branch_id: معرف الفرع
            days_back: عدد الأيام للتحليل

        Returns:
            تحليلات التوزيع
        """
        try:
            # الحصول على إحصائيات التحويلات
            from datetime import timedelta
            date_from = get_tripoli_now() - timedelta(days=days_back)

            transfer_stats_query = text("""
                SELECT
                    COUNT(*) as total_transfers,
                    COUNT(CASE WHEN tr.status = 'completed' THEN 1 END) as completed_transfers,
                    COUNT(CASE WHEN tr.status = 'pending' THEN 1 END) as pending_transfers,
                    COUNT(CASE WHEN tr.status = 'approved' THEN 1 END) as approved_transfers,
                    AVG(CASE
                        WHEN tr.completed_at IS NOT NULL AND tr.requested_at IS NOT NULL
                        THEN EXTRACT(EPOCH FROM (tr.completed_at - tr.requested_at))/3600
                    END) as avg_completion_hours
                FROM transfer_requests tr
                INNER JOIN warehouses tw ON tr.to_warehouse_id = tw.id
                INNER JOIN branch_warehouses bw ON tw.id = bw.warehouse_id
                WHERE bw.branch_id = :branch_id
                AND tr.requested_at >= :date_from
            """)

            transfer_stats = self.db_session.execute(transfer_stats_query, {
                "branch_id": branch_id,
                "date_from": date_from
            }).fetchone()

            # إحصائيات المستودعات المرتبطة
            warehouse_stats_query = text("""
                SELECT
                    COUNT(*) as total_linked_warehouses,
                    COUNT(CASE WHEN bw.is_primary THEN 1 END) as primary_warehouses,
                    COUNT(CASE WHEN w.is_active THEN 1 END) as active_warehouses,
                    AVG(bw.priority) as avg_priority
                FROM branch_warehouses bw
                INNER JOIN warehouses w ON bw.warehouse_id = w.id
                WHERE bw.branch_id = :branch_id
            """)

            warehouse_stats = self.db_session.execute(warehouse_stats_query, {
                "branch_id": branch_id
            }).fetchone()

            # تحليل كفاءة التوزيع
            efficiency_score = self._calculate_distribution_efficiency(
                transfer_stats, warehouse_stats
            )

            analytics = {
                'branch_id': branch_id,
                'analysis_period_days': days_back,
                'transfer_statistics': {
                    'total_transfers': getattr(transfer_stats, 'total_transfers', 0) or 0,
                    'completed_transfers': getattr(transfer_stats, 'completed_transfers', 0) or 0,
                    'pending_transfers': getattr(transfer_stats, 'pending_transfers', 0) or 0,
                    'approved_transfers': getattr(transfer_stats, 'approved_transfers', 0) or 0,
                    'completion_rate': round(
                        (getattr(transfer_stats, 'completed_transfers', 0) or 0) / max(1, getattr(transfer_stats, 'total_transfers', 1) or 1) * 100, 2
                    ),
                    'avg_completion_hours': round(getattr(transfer_stats, 'avg_completion_hours', 0) or 0, 2)
                },
                'warehouse_statistics': {
                    'total_linked_warehouses': getattr(warehouse_stats, 'total_linked_warehouses', 0) or 0,
                    'primary_warehouses': getattr(warehouse_stats, 'primary_warehouses', 0) or 0,
                    'active_warehouses': getattr(warehouse_stats, 'active_warehouses', 0) or 0,
                    'avg_priority': round(getattr(warehouse_stats, 'avg_priority', 0) or 0, 2)
                },
                'efficiency_analysis': efficiency_score,
                'recommendations': self._generate_efficiency_recommendations(efficiency_score)
            }

            logger.info(f"✅ تم إنشاء تحليلات التوزيع للفرع {branch_id}")

            return {
                'success': True,
                'analytics': analytics
            }

        except Exception as e:
            logger.error(f"❌ خطأ في تحليلات التوزيع: {e}")
            return {
                'success': False,
                'error': f'خطأ في تحليلات التوزيع: {str(e)}'
            }

    def _calculate_distribution_efficiency(self, transfer_stats: Any,
                                         warehouse_stats: Any) -> Dict[str, Any]:
        """
        حساب كفاءة التوزيع

        Args:
            transfer_stats: إحصائيات التحويلات
            warehouse_stats: إحصائيات المستودعات

        Returns:
            نقاط الكفاءة والتفاصيل
        """
        try:
            efficiency_score = 0.0
            details = {}

            # نقاط معدل الإكمال (40 نقطة)
            completion_rate = (transfer_stats.completed_transfers or 0) / max(1, transfer_stats.total_transfers or 1) * 100
            completion_score = min(40, completion_rate * 0.4)
            efficiency_score += completion_score
            details['completion_score'] = round(completion_score, 2)

            # نقاط سرعة الإكمال (30 نقطة)
            avg_hours = transfer_stats.avg_completion_hours or 48
            if avg_hours <= 24:
                speed_score = 30
            elif avg_hours <= 48:
                speed_score = 20
            elif avg_hours <= 72:
                speed_score = 10
            else:
                speed_score = 5
            efficiency_score += speed_score
            details['speed_score'] = speed_score

            # نقاط تنظيم المستودعات (30 نقطة)
            total_warehouses = warehouse_stats.total_linked_warehouses or 0
            primary_warehouses = warehouse_stats.primary_warehouses or 0

            if total_warehouses > 0:
                if primary_warehouses >= 1:
                    organization_score = 20
                else:
                    organization_score = 10

                # نقاط إضافية للتنظيم الجيد
                if total_warehouses <= 5:
                    organization_score += 10
                elif total_warehouses <= 10:
                    organization_score += 5

                efficiency_score += organization_score
                details['organization_score'] = organization_score
            else:
                details['organization_score'] = 0

            # تحديد مستوى الكفاءة
            if efficiency_score >= 80:
                efficiency_level = 'ممتاز'
            elif efficiency_score >= 60:
                efficiency_level = 'جيد'
            elif efficiency_score >= 40:
                efficiency_level = 'متوسط'
            else:
                efficiency_level = 'يحتاج تحسين'

            return {
                'total_score': round(efficiency_score, 2),
                'efficiency_level': efficiency_level,
                'details': details
            }

        except Exception as e:
            logger.error(f"❌ خطأ في حساب كفاءة التوزيع: {e}")
            return {
                'total_score': 0,
                'efficiency_level': 'خطأ في الحساب',
                'details': {}
            }

    def _generate_efficiency_recommendations(self, efficiency_score: Dict[str, Any]) -> List[str]:
        """
        إنشاء توصيات لتحسين الكفاءة

        Args:
            efficiency_score: نقاط الكفاءة

        Returns:
            قائمة التوصيات
        """
        recommendations = []

        try:
            total_score = efficiency_score.get('total_score', 0)
            details = efficiency_score.get('details', {})

            if total_score >= 80:
                recommendations.append("✅ ممتاز! نظام التوزيع يعمل بكفاءة عالية")
            elif total_score >= 60:
                recommendations.append("✅ جيد! نظام التوزيع يعمل بشكل مقبول")
            else:
                recommendations.append("⚠️ يحتاج تحسين! نظام التوزيع يحتاج إلى تطوير")

            # توصيات محددة
            completion_score = details.get('completion_score', 0)
            if completion_score < 30:
                recommendations.append("💡 اقتراح: تحسين عملية متابعة وإكمال التحويلات")

            speed_score = details.get('speed_score', 0)
            if speed_score < 20:
                recommendations.append("💡 اقتراح: تسريع عملية معالجة طلبات التحويل")

            organization_score = details.get('organization_score', 0)
            if organization_score < 20:
                recommendations.append("💡 اقتراح: تحسين تنظيم المستودعات وتحديد المستودع الأساسي")

            return recommendations

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء توصيات الكفاءة: {e}")
            return ["❌ خطأ في إنشاء التوصيات"]


def get_smart_distribution_service(db_session: Session) -> SmartDistributionService:
    """
    دالة مساعدة للحصول على instance من خدمة التوزيع الذكي

    Args:
        db_session: جلسة قاعدة البيانات

    Returns:
        instance من SmartDistributionService
    """
    return SmartDistributionService.get_instance(db_session)
