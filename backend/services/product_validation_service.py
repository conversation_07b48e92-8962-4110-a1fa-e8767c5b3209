"""
خدمة التحقق من صحة بيانات المنتج
تطبق مبادئ البرمجة الكائنية مع نمط Singleton
"""

import re
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, date
from decimal import Decimal, InvalidOperation
from sqlalchemy.orm import Session

from models.product import Product
from models.category import Category
from models.category import Subcategory
from models.brand import Brand
from models.unit import Unit
from models.warehouse import Warehouse
from services.barcode_service import BarcodeService
from services.slug_service import SlugService
import logging

logger = logging.getLogger(__name__)


class ProductValidationService:
    """
    خدمة التحقق من صحة بيانات المنتج المتقدمة
    تطبق مبادئ البرمجة الكائنية مع نمط Singleton
    """
    
    _instance: Optional['ProductValidationService'] = None
    
    # قواعد التحقق
    VALIDATION_RULES = {
        'name': {
            'required': True,
            'min_length': 2,
            'max_length': 100,
            'pattern': r'^[\u0600-\u06FF\w\s\-\.\(\)\[\]\/\&\+\%\#\@\!]+$'
        },
        'description': {
            'required': False,
            'max_length': 500,
            'max_words': 60
        },
        'price': {
            'required': True,
            'min_value': 0.01,
            'max_value': 999999.99,
            'decimal_places': 2
        },
        'cost_price': {
            'required': True,
            'min_value': 0,
            'max_value': 999999.99,
            'decimal_places': 2
        },
        'quantity': {
            'required': True,
            'min_value': 0,
            'max_value': 999999,
            'integer_only': True
        },
        'min_quantity': {
            'required': False,
            'min_value': 0,
            'max_value': 999999,
            'integer_only': True
        },
        'sku': {
            'required': False,
            'min_length': 3,
            'max_length': 50,
            'pattern': r'^[A-Z0-9\-\_]+$'
        }
    }
    
    def __init__(self, db_session: Optional[Session] = None):
        """تهيئة خدمة التحقق من صحة البيانات"""
        self.db_session = db_session
        self.barcode_service = BarcodeService.get_instance(db_session)
        self.slug_service = SlugService.get_instance(db_session)
        logger.info("تم تهيئة خدمة التحقق من صحة بيانات المنتج بنجاح")
    
    @classmethod
    def get_instance(cls, db_session: Optional[Session] = None) -> 'ProductValidationService':
        """الحصول على مثيل وحيد من الخدمة"""
        if cls._instance is None:
            cls._instance = cls(db_session)
        elif db_session and cls._instance.db_session != db_session:
            cls._instance.db_session = db_session
            cls._instance.barcode_service = BarcodeService.get_instance(db_session)
            cls._instance.slug_service = SlugService.get_instance(db_session)
        return cls._instance
    
    def validate_text_field(self, value: str, field_name: str, rules: Dict[str, Any]) -> Dict[str, Any]:
        """التحقق من صحة الحقول النصية"""
        try:
            # التحقق من الإجبارية
            if rules.get('required', False) and (not value or not value.strip()):
                return {
                    'valid': False,
                    'error': f'{field_name} مطلوب'
                }
            
            if not value:
                return {'valid': True}
            
            value = value.strip()
            
            # التحقق من الطول الأدنى
            if 'min_length' in rules and len(value) < rules['min_length']:
                return {
                    'valid': False,
                    'error': f'{field_name} يجب أن يكون على الأقل {rules["min_length"]} أحرف'
                }
            
            # التحقق من الطول الأقصى
            if 'max_length' in rules and len(value) > rules['max_length']:
                return {
                    'valid': False,
                    'error': f'{field_name} يجب أن يكون أقل من {rules["max_length"]} حرف'
                }
            
            # التحقق من النمط
            if 'pattern' in rules and not re.match(rules['pattern'], value):
                return {
                    'valid': False,
                    'error': f'{field_name} يحتوي على أحرف غير مسموحة'
                }
            
            # التحقق من عدد الكلمات (للوصف)
            if 'max_words' in rules:
                word_count = len(value.split())
                if word_count > rules['max_words']:
                    return {
                        'valid': False,
                        'error': f'{field_name} يجب أن يكون أقل من {rules["max_words"]} كلمة'
                    }
            
            return {'valid': True, 'value': value}
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من الحقل النصي {field_name}: {e}")
            return {
                'valid': False,
                'error': f'خطأ في التحقق من {field_name}'
            }
    
    def validate_numeric_field(self, value: Union[str, int, float, Decimal, None], field_name: str, rules: Dict[str, Any]) -> Dict[str, Any]:
        """التحقق من صحة الحقول الرقمية"""
        try:
            # التحقق من الإجبارية
            if rules.get('required', False) and (value is None or value == ''):
                return {
                    'valid': False,
                    'error': f'{field_name} مطلوب'
                }
            
            if value is None or value == '':
                return {'valid': True, 'value': 0}
            
            # تحويل إلى رقم
            try:
                if rules.get('integer_only', False):
                    numeric_value = int(float(str(value)))
                else:
                    numeric_value = float(str(value))
            except (ValueError, TypeError):
                return {
                    'valid': False,
                    'error': f'{field_name} يجب أن يكون رقماً صحيحاً'
                }
            
            # التحقق من القيمة الدنيا
            if 'min_value' in rules and numeric_value < rules['min_value']:
                return {
                    'valid': False,
                    'error': f'{field_name} يجب أن يكون على الأقل {rules["min_value"]}'
                }
            
            # التحقق من القيمة العليا
            if 'max_value' in rules and numeric_value > rules['max_value']:
                return {
                    'valid': False,
                    'error': f'{field_name} يجب أن يكون أقل من {rules["max_value"]}'
                }
            
            # التحقق من عدد المنازل العشرية
            if 'decimal_places' in rules and not rules.get('integer_only', False):
                decimal_str = str(numeric_value)
                if '.' in decimal_str:
                    decimal_places = len(decimal_str.split('.')[1])
                    if decimal_places > rules['decimal_places']:
                        return {
                            'valid': False,
                            'error': f'{field_name} يجب أن يحتوي على {rules["decimal_places"]} منازل عشرية كحد أقصى'
                        }
            
            return {'valid': True, 'value': numeric_value}
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من الحقل الرقمي {field_name}: {e}")
            return {
                'valid': False,
                'error': f'خطأ في التحقق من {field_name}'
            }
    
    def validate_foreign_key(self, value: Optional[int], model_class, field_name: str, required: bool = False) -> Dict[str, Any]:
        """التحقق من صحة المفاتيح الخارجية"""
        try:
            if required and not value:
                return {
                    'valid': False,
                    'error': f'{field_name} مطلوب'
                }
            
            if not value:
                return {'valid': True, 'value': None}
            
            if not self.db_session:
                return {
                    'valid': False,
                    'error': 'جلسة قاعدة البيانات غير متوفرة'
                }
            
            # التحقق من وجود السجل
            record = self.db_session.query(model_class).filter(model_class.id == value).first()
            
            if not record:
                return {
                    'valid': False,
                    'error': f'{field_name} غير موجود'
                }
            
            # التحقق من حالة النشاط إذا كان الحقل موجود
            if hasattr(record, 'is_active') and not record.is_active:
                return {
                    'valid': False,
                    'error': f'{field_name} غير نشط'
                }
            
            return {'valid': True, 'value': value, 'record': record}
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من المفتاح الخارجي {field_name}: {e}")
            return {
                'valid': False,
                'error': f'خطأ في التحقق من {field_name}'
            }
    
    def validate_date_field(self, value: Union[str, date, datetime, None], field_name: str, required: bool = False) -> Dict[str, Any]:
        """التحقق من صحة حقول التاريخ"""
        try:
            if required and not value:
                return {
                    'valid': False,
                    'error': f'{field_name} مطلوب'
                }
            
            if not value:
                return {'valid': True, 'value': None}
            
            # تحويل إلى تاريخ
            if isinstance(value, str):
                try:
                    # محاولة تحليل التاريخ بصيغ مختلفة
                    for date_format in ['%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%Y-%m-%d %H:%M:%S']:
                        try:
                            parsed_date = datetime.strptime(value, date_format).date()
                            break
                        except ValueError:
                            continue
                    else:
                        return {
                            'valid': False,
                            'error': f'{field_name} تنسيق التاريخ غير صحيح'
                        }
                except ValueError:
                    return {
                        'valid': False,
                        'error': f'{field_name} تنسيق التاريخ غير صحيح'
                    }
            elif isinstance(value, datetime):
                parsed_date = value.date()
            elif isinstance(value, date):
                parsed_date = value
            else:
                return {
                    'valid': False,
                    'error': f'{field_name} نوع التاريخ غير صحيح'
                }
            
            return {'valid': True, 'value': parsed_date}
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من حقل التاريخ {field_name}: {e}")
            return {
                'valid': False,
                'error': f'خطأ في التحقق من {field_name}'
            }
    
    def validate_product_data(self, product_data: Dict[str, Any], exclude_product_id: Optional[int] = None) -> Dict[str, Any]:
        """التحقق الشامل من بيانات المنتج"""
        try:
            errors = {}
            validated_data = {}
            
            # التحقق من الاسم
            name_validation = self.validate_text_field(
                product_data.get('name', ''), 
                'اسم المنتج', 
                self.VALIDATION_RULES['name']
            )
            if not name_validation['valid']:
                errors['name'] = name_validation['error']
            else:
                validated_data['name'] = name_validation.get('value', product_data.get('name', ''))
            
            # التحقق من الوصف
            description_validation = self.validate_text_field(
                product_data.get('description', ''), 
                'الوصف', 
                self.VALIDATION_RULES['description']
            )
            if not description_validation['valid']:
                errors['description'] = description_validation['error']
            else:
                validated_data['description'] = description_validation.get('value', product_data.get('description'))
            
            # التحقق من السعر
            price_validation = self.validate_numeric_field(
                product_data.get('price'), 
                'السعر', 
                self.VALIDATION_RULES['price']
            )
            if not price_validation['valid']:
                errors['price'] = price_validation['error']
            else:
                validated_data['price'] = price_validation['value']
            
            # التحقق من سعر التكلفة
            cost_price_validation = self.validate_numeric_field(
                product_data.get('cost_price'), 
                'سعر التكلفة', 
                self.VALIDATION_RULES['cost_price']
            )
            if not cost_price_validation['valid']:
                errors['cost_price'] = cost_price_validation['error']
            else:
                validated_data['cost_price'] = cost_price_validation['value']
            
            # التحقق من منطقية الأسعار
            if ('price' not in errors and 'cost_price' not in errors and 
                validated_data.get('price', 0) < validated_data.get('cost_price', 0)):
                errors['price'] = 'السعر يجب أن يكون أكبر من أو يساوي سعر التكلفة'
            
            # التحقق من الكمية
            quantity_validation = self.validate_numeric_field(
                product_data.get('quantity'), 
                'الكمية', 
                self.VALIDATION_RULES['quantity']
            )
            if not quantity_validation['valid']:
                errors['quantity'] = quantity_validation['error']
            else:
                validated_data['quantity'] = quantity_validation['value']
            
            # التحقق من الحد الأدنى للكمية
            min_quantity_validation = self.validate_numeric_field(
                product_data.get('min_quantity', 0), 
                'الحد الأدنى للكمية', 
                self.VALIDATION_RULES['min_quantity']
            )
            if not min_quantity_validation['valid']:
                errors['min_quantity'] = min_quantity_validation['error']
            else:
                validated_data['min_quantity'] = min_quantity_validation['value']
            
            # التحقق من SKU
            if product_data.get('sku'):
                sku_validation = self.validate_text_field(
                    product_data.get('sku', ''), 
                    'SKU', 
                    self.VALIDATION_RULES['sku']
                )
                if not sku_validation['valid']:
                    errors['sku'] = sku_validation['error']
                else:
                    validated_data['sku'] = sku_validation['value']
            
            # التحقق من الباركود
            if product_data.get('item_barcode'):
                barcode_validation = self.barcode_service.validate_barcode(
                    product_data['item_barcode'],
                    product_data.get('barcode_symbology', 'CODE128')
                )
                if not barcode_validation['valid']:
                    errors['item_barcode'] = barcode_validation['error']
                else:
                    # التحقق من تفرد الباركود
                    uniqueness_check = self.barcode_service.check_barcode_uniqueness(
                        product_data['item_barcode'],
                        exclude_product_id
                    )
                    if not uniqueness_check['unique']:
                        errors['item_barcode'] = uniqueness_check['error']
                    else:
                        validated_data['barcode'] = product_data['item_barcode']
            
            # التحقق من المستودع
            warehouse_validation = self.validate_foreign_key(
                product_data.get('warehouse_id'), 
                Warehouse, 
                'المستودع', 
                required=True
            )
            if not warehouse_validation['valid']:
                errors['warehouse_id'] = warehouse_validation['error']
            else:
                validated_data['warehouse_id'] = warehouse_validation['value']
            
            # التحقق من الفئة
            if product_data.get('category_id'):
                category_validation = self.validate_foreign_key(
                    product_data.get('category_id'), 
                    Category, 
                    'الفئة'
                )
                if not category_validation['valid']:
                    errors['category_id'] = category_validation['error']
                else:
                    validated_data['category_id'] = category_validation['value']
            
            # التحقق من الفئة الفرعية
            if product_data.get('subcategory_id'):
                subcategory_validation = self.validate_foreign_key(
                    product_data.get('subcategory_id'), 
                    Subcategory, 
                    'الفئة الفرعية'
                )
                if not subcategory_validation['valid']:
                    errors['subcategory_id'] = subcategory_validation['error']
                else:
                    validated_data['subcategory_id'] = subcategory_validation['value']
            
            # التحقق من العلامة التجارية
            if product_data.get('brand_id'):
                brand_validation = self.validate_foreign_key(
                    product_data.get('brand_id'), 
                    Brand, 
                    'العلامة التجارية'
                )
                if not brand_validation['valid']:
                    errors['brand_id'] = brand_validation['error']
                else:
                    validated_data['brand_id'] = brand_validation['value']
            
            # التحقق من الوحدة
            if product_data.get('unit_id'):
                unit_validation = self.validate_foreign_key(
                    product_data.get('unit_id'), 
                    Unit, 
                    'الوحدة'
                )
                if not unit_validation['valid']:
                    errors['unit_id'] = unit_validation['error']
                else:
                    validated_data['unit_id'] = unit_validation['value']
            
            # التحقق من التواريخ
            if product_data.get('manufactured_date'):
                manufactured_date_validation = self.validate_date_field(
                    product_data.get('manufactured_date'), 
                    'تاريخ التصنيع'
                )
                if not manufactured_date_validation['valid']:
                    errors['manufactured_date'] = manufactured_date_validation['error']
                else:
                    validated_data['manufactured_date'] = manufactured_date_validation['value']
            
            if product_data.get('expiry_date'):
                expiry_date_validation = self.validate_date_field(
                    product_data.get('expiry_date'), 
                    'تاريخ انتهاء الصلاحية'
                )
                if not expiry_date_validation['valid']:
                    errors['expiry_date'] = expiry_date_validation['error']
                else:
                    validated_data['expiry_date'] = expiry_date_validation['value']
            
            # التحقق من منطقية التواريخ
            if ('manufactured_date' not in errors and 'expiry_date' not in errors and
                validated_data.get('manufactured_date') and validated_data.get('expiry_date')):
                if validated_data['manufactured_date'] >= validated_data['expiry_date']:
                    errors['expiry_date'] = 'تاريخ انتهاء الصلاحية يجب أن يكون بعد تاريخ التصنيع'
            
            return {
                'valid': len(errors) == 0,
                'errors': errors,
                'validated_data': validated_data
            }
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من بيانات المنتج: {e}")
            return {
                'valid': False,
                'errors': {'general': f'خطأ في التحقق من البيانات: {str(e)}'},
                'validated_data': {}
            }
