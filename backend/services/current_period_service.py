"""
خدمة جلب بيانات الفترة الحالية للمبيعات
هذا الملف مختص بجلب وتنظيم بيانات المبيعات للفترة الحالية فقط
يتعامل مع التاريخ والوقت بدقة عالية لضمان عدم تعارض البيانات
"""

import logging
from datetime import datetime, timezone
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import select, and_, func, cast
from sqlalchemy.sql.sqltypes import Numeric
from decimal import Decimal

from models.sale import Sale
from models.user import User
from utils.datetime_utils import (
    get_tripoli_now, convert_to_tripoli_time,
    get_hour_from_datetime, get_hour_from_datetime_with_settings,
    get_current_time_with_settings, convert_to_settings_timezone,
    get_previous_days, get_previous_months
)

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

class CurrentPeriodService:
    """
    خدمة جلب بيانات الفترة الحالية
    تتعامل مع جلب وتنظيم بيانات المبيعات للفترة الحالية بدقة
    """

    def __init__(self, db: Session, current_user: User):
        """
        تهيئة خدمة الفترة الحالية

        Args:
            db: جلسة قاعدة البيانات
            current_user: المستخدم الحالي
        """
        self.db = db
        self.current_user = current_user
        self.is_admin = current_user.role.name == "ADMIN"

        logger.info(f"تم تهيئة خدمة الفترة الحالية للمستخدم: {current_user.username}")
        logger.info(f"صلاحيات المستخدم: {'مدير' if self.is_admin else 'مستخدم عادي'}")

    def get_current_day_sales(self) -> List[Dict[str, Any]]:
        """
        جلب بيانات مبيعات اليوم الحالي (24 ساعة كاملة)

        Returns:
            قائمة بيانات المبيعات لكل ساعة في اليوم الحالي (24 ساعة كاملة)
        """
        try:
            logger.info("بدء جلب بيانات مبيعات اليوم الحالي (24 ساعة كاملة)")

            # الحصول على الوقت الحالي بالمنطقة الزمنية المحددة في الإعدادات
            current_time = get_current_time_with_settings(self.db)
            today = current_time.date()

            logger.info(f"التاريخ الحالي بالمنطقة الزمنية المحددة: {today} ({current_time.tzinfo})")

            # استخدام فلتر التاريخ بدلاً من النطاق الزمني لتجنب مشاكل التوقيت
            today_str = today.strftime('%Y-%m-%d')

            logger.info(f"فلتر التاريخ: {today_str}")

            # بناء الاستعلام باستخدام فلتر التاريخ - استخدام المبلغ المدفوع فعلياً
            query = select(
                Sale.created_at,
                cast(
                    Sale.amount_paid,
                    Numeric
                ).label("total")
            ).where(
                func.date(Sale.created_at) == today_str
            )

            # تطبيق فلتر المستخدم إذا لم يكن مديراً
            if not self.is_admin:
                query = query.where(Sale.user_id == self.current_user.id)
                logger.info("تم تطبيق فلتر المستخدم العادي")
            else:
                logger.info("عرض بيانات جميع المستخدمين (مدير)")

            # تنفيذ الاستعلام
            raw_sales = self.db.execute(query).all()
            logger.info(f"تم جلب {len(raw_sales)} عملية بيع")

            # تنظيم البيانات حسب الساعات
            hour_totals = {}

            # تهيئة جميع الساعات بقيمة صفر
            for hour in range(24):
                hour_key = f"{hour:02d}:00"
                hour_totals[hour_key] = 0.0

            # معالجة كل عملية بيع
            for sale in raw_sales:
                # تحويل وقت البيع إلى المنطقة الزمنية المحددة في الإعدادات
                sale_time_converted = convert_to_settings_timezone(sale.created_at, self.db)

                if sale_time_converted:
                    # استخراج الساعة حسب المنطقة الزمنية المحددة
                    hour_key = get_hour_from_datetime_with_settings(sale_time_converted, self.db)

                    # إضافة المبلغ إلى الساعة المناسبة
                    if hour_key in hour_totals:
                        hour_totals[hour_key] += float(Decimal(str(sale.total)))
                        logger.debug(f"أضيف {sale.total} إلى الساعة {hour_key}")

            # تحويل إلى قائمة مرتبة
            result = []
            for hour in range(24):
                hour_key = f"{hour:02d}:00"
                result.append({
                    "date": hour_key,
                    "amount": hour_totals[hour_key]
                })

            logger.info(f"تم تنظيم البيانات لـ {len(result)} ساعة")
            return result

        except Exception as e:
            logger.error(f"خطأ في جلب بيانات اليوم الحالي: {str(e)}")
            # إرجاع بيانات فارغة في حالة الخطأ
            return [{"date": f"{hour:02d}:00", "amount": 0.0} for hour in range(24)]

    def get_current_week_sales(self) -> List[Dict[str, Any]]:
        """
        جلب بيانات مبيعات الأسبوع الحالي (7 أيام)

        Returns:
            قائمة بيانات المبيعات لكل يوم في الأسبوع الحالي
        """
        try:
            logger.info("بدء جلب بيانات مبيعات الأسبوع الحالي")

            # الحصول على الوقت الحالي بالمنطقة الزمنية المحددة في الإعدادات
            current_time = get_current_time_with_settings(self.db)

            # الحصول على آخر 7 أيام
            days_list_str = get_previous_days(7)

            logger.info(f"أيام الأسبوع: {days_list_str}")

            # تحويل السلاسل النصية إلى كائنات تاريخ
            days_list = []
            for day_str in days_list_str:
                day_obj = datetime.strptime(day_str, '%Y-%m-%d').date()
                days_list.append(day_obj)

            # تحديد نطاق الأسبوع
            start_date = days_list[0]  # أقدم يوم
            end_date = current_time.date()  # اليوم الحالي

            logger.info(f"نطاق الأسبوع: من {start_date} إلى {end_date}")

            # بناء الاستعلام - استخدام المبلغ المدفوع فعلياً
            query = select(
                Sale.created_at,
                cast(
                    Sale.amount_paid,
                    Numeric
                ).label("total")
            ).where(
                and_(
                    Sale.created_at >= start_date.strftime('%Y-%m-%d 00:00:00'),
                    Sale.created_at <= end_date.strftime('%Y-%m-%d 23:59:59')
                )
            )

            # تطبيق فلتر المستخدم إذا لم يكن مديراً
            if not self.is_admin:
                query = query.where(Sale.user_id == self.current_user.id)
                logger.info("تم تطبيق فلتر المستخدم العادي")
            else:
                logger.info("عرض بيانات جميع المستخدمين (مدير)")

            # تنفيذ الاستعلام
            raw_sales = self.db.execute(query).all()
            logger.info(f"تم جلب {len(raw_sales)} عملية بيع")

            # تنظيم البيانات حسب الأيام
            day_totals = {}

            # تهيئة جميع الأيام بقيمة صفر
            for day in days_list:
                day_key = day.strftime('%Y-%m-%d')
                day_totals[day_key] = 0.0

            # معالجة كل عملية بيع
            for sale in raw_sales:
                # تحويل وقت البيع إلى المنطقة الزمنية المحددة في الإعدادات
                sale_time_converted = convert_to_settings_timezone(sale.created_at, self.db)

                if sale_time_converted:
                    # استخراج التاريخ
                    sale_date = sale_time_converted.date()
                    day_key = sale_date.strftime('%Y-%m-%d')

                    # إضافة المبلغ إلى اليوم المناسب
                    if day_key in day_totals:
                        day_totals[day_key] += float(Decimal(str(sale.total)))
                        logger.debug(f"أضيف {sale.total} إلى اليوم {day_key}")

            # تحويل إلى قائمة مرتبة
            result = []
            for day in days_list:
                day_key = day.strftime('%Y-%m-%d')
                result.append({
                    "date": day_key,
                    "amount": day_totals[day_key]
                })

            logger.info(f"تم تنظيم البيانات لـ {len(result)} يوم")
            return result

        except Exception as e:
            logger.error(f"خطأ في جلب بيانات الأسبوع الحالي: {str(e)}")
            # إرجاع بيانات فارغة في حالة الخطأ
            days_list_str = get_previous_days(7)
            return [{"date": day_str, "amount": 0.0} for day_str in days_list_str]

    def get_current_month_sales(self) -> List[Dict[str, Any]]:
        """
        جلب بيانات مبيعات الشهر الحالي (30 يوم)

        Returns:
            قائمة بيانات المبيعات لكل يوم في الشهر الحالي
        """
        try:
            logger.info("بدء جلب بيانات مبيعات الشهر الحالي")

            # الحصول على الوقت الحالي بالمنطقة الزمنية المحددة في الإعدادات
            current_time = get_current_time_with_settings(self.db)

            # الحصول على آخر 30 يوم
            days_list_str = get_previous_days(30)

            logger.info(f"نطاق الشهر: من {days_list_str[0]} إلى {days_list_str[-1]}")

            # تحويل السلاسل النصية إلى كائنات تاريخ
            days_list = []
            for day_str in days_list_str:
                day_obj = datetime.strptime(day_str, '%Y-%m-%d').date()
                days_list.append(day_obj)

            # تحديد نطاق الشهر
            start_date = days_list[0]  # أقدم يوم
            end_date = current_time.date()  # اليوم الحالي

            # بناء الاستعلام - استخدام المبلغ المدفوع فعلياً
            query = select(
                Sale.created_at,
                cast(
                    Sale.amount_paid,
                    Numeric
                ).label("total")
            ).where(
                and_(
                    Sale.created_at >= start_date.strftime('%Y-%m-%d 00:00:00'),
                    Sale.created_at <= end_date.strftime('%Y-%m-%d 23:59:59')
                )
            )

            # تطبيق فلتر المستخدم إذا لم يكن مديراً
            if not self.is_admin:
                query = query.where(Sale.user_id == self.current_user.id)
                logger.info("تم تطبيق فلتر المستخدم العادي")
            else:
                logger.info("عرض بيانات جميع المستخدمين (مدير)")

            # تنفيذ الاستعلام
            raw_sales = self.db.execute(query).all()
            logger.info(f"تم جلب {len(raw_sales)} عملية بيع")

            # تنظيم البيانات حسب الأيام
            day_totals = {}

            # تهيئة جميع الأيام بقيمة صفر
            for day in days_list:
                day_key = day.strftime('%Y-%m-%d')
                day_totals[day_key] = 0.0

            # معالجة كل عملية بيع
            for sale in raw_sales:
                # تحويل وقت البيع إلى المنطقة الزمنية المحددة في الإعدادات
                sale_time_converted = convert_to_settings_timezone(sale.created_at, self.db)

                if sale_time_converted:
                    # استخراج التاريخ
                    sale_date = sale_time_converted.date()
                    day_key = sale_date.strftime('%Y-%m-%d')

                    # إضافة المبلغ إلى اليوم المناسب
                    if day_key in day_totals:
                        day_totals[day_key] += float(Decimal(str(sale.total)))
                        logger.debug(f"أضيف {sale.total} إلى اليوم {day_key}")

            # تحويل إلى قائمة مرتبة
            result = []
            for day in days_list:
                day_key = day.strftime('%Y-%m-%d')
                result.append({
                    "date": day_key,
                    "amount": day_totals[day_key]
                })

            logger.info(f"تم تنظيم البيانات لـ {len(result)} يوم")
            return result

        except Exception as e:
            logger.error(f"خطأ في جلب بيانات الشهر الحالي: {str(e)}")
            # إرجاع بيانات فارغة في حالة الخطأ
            days_list_str = get_previous_days(30)
            return [{"date": day_str, "amount": 0.0} for day_str in days_list_str]

    def get_current_year_sales(self) -> List[Dict[str, Any]]:
        """
        جلب بيانات مبيعات السنة الحالية (12 شهر)

        Returns:
            قائمة بيانات المبيعات لكل شهر في السنة الحالية
        """
        try:
            logger.info("بدء جلب بيانات مبيعات السنة الحالية")

            # الحصول على الوقت الحالي بالمنطقة الزمنية المحددة في الإعدادات
            current_time = get_current_time_with_settings(self.db)

            # الحصول على آخر 12 شهر
            months_list_str = get_previous_months(12)

            logger.info(f"أشهر السنة: {months_list_str}")

            # تحويل السلاسل النصية إلى كائنات تاريخ
            months_list = []
            for month_str in months_list_str:
                month_obj = datetime.strptime(month_str + '-01', '%Y-%m-%d').date()
                months_list.append(month_obj)

            # تحديد نطاق السنة
            start_date = months_list[0]  # أقدم شهر

            # بناء استعلام محسن باستخدام SQL aggregation
            # استخدام DATE_TRUNC لتجميع البيانات حسب الشهر مباشرة في قاعدة البيانات
            query = select(
                func.to_char(Sale.created_at, 'YYYY-MM').label('month'),
                func.sum(cast(Sale.amount_paid, Numeric)).label('total_amount')
            ).where(
                Sale.created_at >= start_date.strftime('%Y-%m-%d 00:00:00')
            ).group_by(
                func.to_char(Sale.created_at, 'YYYY-MM')
            )

            # تطبيق فلتر المستخدم إذا لم يكن مديراً
            if not self.is_admin:
                query = query.where(Sale.user_id == self.current_user.id)
                logger.info("تم تطبيق فلتر المستخدم العادي")
            else:
                logger.info("عرض بيانات جميع المستخدمين (مدير)")

            # تنفيذ الاستعلام المحسن
            raw_results = self.db.execute(query).all()
            logger.info(f"تم جلب {len(raw_results)} شهر مجمع (بدلاً من معالجة آلاف العمليات)")

            # تنظيم البيانات
            month_totals = {}

            # تهيئة جميع الأشهر بقيمة صفر
            for month_str in months_list_str:
                month_totals[month_str] = 0.0

            # معالجة النتائج المجمعة (أسرع بكثير من معالجة كل عملية منفردة)
            for result in raw_results:
                month_key = result.month
                total_amount = float(result.total_amount) if result.total_amount else 0.0

                # إضافة المبلغ إلى الشهر المناسب
                if month_key in month_totals:
                    month_totals[month_key] = total_amount
                    logger.debug(f"شهر {month_key}: {total_amount}")

            # تحويل إلى قائمة مرتبة
            result = []
            for month_str in months_list_str:
                result.append({
                    "date": month_str,
                    "amount": month_totals[month_str]
                })

            logger.info(f"تم تنظيم البيانات لـ {len(result)} شهر")
            return result

        except Exception as e:
            logger.error(f"خطأ في جلب بيانات السنة الحالية: {str(e)}")
            # إرجاع بيانات فارغة في حالة الخطأ
            months_list_str = get_previous_months(12)
            return [{"date": month_str, "amount": 0.0} for month_str in months_list_str]

    def get_sales_by_period(self, period: str) -> List[Dict[str, Any]]:
        """
        جلب بيانات المبيعات حسب الفترة المحددة

        Args:
            period: نوع الفترة (day, week, month, year)

        Returns:
            قائمة بيانات المبيعات للفترة المحددة
        """
        logger.info(f"جلب بيانات الفترة الحالية: {period}")

        if period == "day":
            return self.get_current_day_sales()
        elif period == "week":
            return self.get_current_week_sales()
        elif period == "month":
            return self.get_current_month_sales()
        elif period == "year":
            return self.get_current_year_sales()
        else:
            logger.error(f"نوع فترة غير مدعوم: {period}")
            return []
