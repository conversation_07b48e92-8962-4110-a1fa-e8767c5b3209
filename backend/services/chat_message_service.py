"""
خدمة إدارة رسائل المحادثة الفورية
تدير إرسال واستقبال وتخزين الرسائل مع معالجة حالات القراءة
"""

import logging
from typing import List, Optional
from datetime import datetime
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import select, update, and_, or_, desc, func, case

from models.chat_message import ChatMessage, MessageStatus, MessageType
from models.user import User
from schemas.chat import MessageCreate, MessageResponse, MessagesListResponse, ConversationResponse
from services.chat_websocket_manager import chat_websocket_manager
from utils.datetime_utils import get_tripoli_now

logger = logging.getLogger(__name__)

class ChatMessageService:
    """
    خدمة إدارة رسائل المحادثة
    """

    def __init__(self, db: Session):
        self.db = db

    def _message_to_response(self, message: ChatMessage) -> MessageResponse:
        """تحويل كائن ChatMessage إلى MessageResponse"""
        return MessageResponse.model_validate(message)

    async def send_message(self, sender_id: int, message_data: MessageCreate) -> Optional[MessageResponse]:
        """
        إرسال رسالة جديدة
        """
        try:
            # التحقق من وجود المستقبل
            receiver_stmt = select(User).where(User.id == message_data.receiver_id)
            receiver = self.db.execute(receiver_stmt).scalar_one_or_none()
            
            if not receiver:
                logger.warning(f"المستقبل {message_data.receiver_id} غير موجود")
                return None
            
            # إنشاء الرسالة
            new_message = ChatMessage(
                sender_id=sender_id,
                receiver_id=message_data.receiver_id,
                content=message_data.content,
                message_type=message_data.message_type,
                status=MessageStatus.SENT,
                created_at=get_tripoli_now()
            )
            
            self.db.add(new_message)
            self.db.commit()
            self.db.refresh(new_message)
            
            # جلب معلومات المرسل للإرسال عبر WebSocket
            sender_stmt = select(User).where(User.id == sender_id)
            sender = self.db.execute(sender_stmt).scalar_one_or_none()
            
            # تحديث حالة الرسالة إلى "تم التسليم" إذا كان المستقبل متصلاً
            delivered_at = None
            if chat_websocket_manager.is_user_online(message_data.receiver_id):
                delivered_at = get_tripoli_now()
                # استخدام update statement بدلاً من تعديل الكائن مباشرة
                update_stmt = update(ChatMessage).where(ChatMessage.id == new_message.id).values(
                    status=MessageStatus.DELIVERED,
                    delivered_at=delivered_at
                )
                self.db.execute(update_stmt)
                self.db.commit()
                # إعادة جلب الرسالة المحدثة
                self.db.refresh(new_message)
                
                # إرسال الرسالة عبر WebSocket
                websocket_message = {
                    'type': 'new_message',
                    'message': {
                        'id': new_message.id,
                        'sender_id': sender_id,
                        'receiver_id': message_data.receiver_id,
                        'content': message_data.content,
                        'message_type': message_data.message_type.value,
                        'status': new_message.status.value,
                        'created_at': new_message.created_at.isoformat(),
                        'delivered_at': delivered_at.isoformat() if delivered_at else None,
                        'sender_username': sender.username if sender else None,
                        'sender_full_name': sender.full_name if sender else None,
                    },
                    'timestamp': datetime.now().isoformat()
                }
                
                await chat_websocket_manager.send_message_to_user(
                    message_data.receiver_id, 
                    websocket_message
                )
            
            # إنشاء استجابة
            response = self._message_to_response(new_message)
            
            logger.info(f"✅ تم إرسال رسالة من {sender_id} إلى {message_data.receiver_id}")
            return response
            
        except Exception as e:
            logger.error(f"خطأ في إرسال الرسالة: {e}")
            self.db.rollback()
            return None

    def get_messages_between_users(
        self,
        user1_id: int,
        user2_id: int,
        page: int = 1,
        limit: int = 20,  # تقليل الحد الافتراضي لضمان التحميل التدريجي
        before_message_id: Optional[int] = None,
        load_direction: str = "newer"  # "newer" للتحميل الأولي، "older" للرسائل الأقدم
    ) -> MessagesListResponse:
        """
        جلب الرسائل بين مستخدمين
        """
        try:
            # بناء الاستعلام الأساسي
            base_query = select(ChatMessage).options(
                joinedload(ChatMessage.sender),
                joinedload(ChatMessage.receiver)
            ).where(
                or_(
                    and_(ChatMessage.sender_id == user1_id, ChatMessage.receiver_id == user2_id),
                    and_(ChatMessage.sender_id == user2_id, ChatMessage.receiver_id == user1_id)
                )
            )
            
            # منطق التحميل المحسن
            if load_direction == "newer":
                # التحميل الأولي: جلب آخر N رسالة
                if before_message_id:
                    # جلب الرسائل الأحدث من before_message_id
                    base_query = base_query.where(ChatMessage.id > before_message_id)

                # ترتيب تنازلي للحصول على الأحدث أولاً، ثم نعكس النتيجة
                base_query = base_query.order_by(desc(ChatMessage.created_at))
            else:
                # تحميل الرسائل الأقدم
                if before_message_id:
                    base_query = base_query.where(ChatMessage.id < before_message_id)

                # ترتيب تنازلي للحصول على الأقدم من النقطة المرجعية
                base_query = base_query.order_by(desc(ChatMessage.created_at))
            
            # حساب العدد الإجمالي
            count_query = select(func.count(ChatMessage.id)).where(
                or_(
                    and_(ChatMessage.sender_id == user1_id, ChatMessage.receiver_id == user2_id),
                    and_(ChatMessage.sender_id == user2_id, ChatMessage.receiver_id == user1_id)
                )
            )
            
            if before_message_id:
                if load_direction == "older":
                    count_query = count_query.where(ChatMessage.id < before_message_id)
                else:
                    count_query = count_query.where(ChatMessage.id > before_message_id)
            
            total_count = self.db.execute(count_query).scalar()
            
            # تطبيق التصفح
            offset = (page - 1) * limit
            messages_query = base_query.offset(offset).limit(limit + 1)  # +1 للتحقق من وجود المزيد
            
            messages = self.db.execute(messages_query).scalars().all()
            
            # التحقق من وجود المزيد
            has_more = len(messages) > limit
            if has_more:
                messages = messages[:limit]

            # عكس الترتيب للحصول على الترتيب الصحيح (الأقدم أولاً)
            if messages:
                messages = list(reversed(messages))
            
            # تحويل إلى استجابة
            message_responses = []
            for message in messages:
                message_responses.append(MessageResponse(
                    id=message.id,
                    sender_id=message.sender_id,
                    receiver_id=message.receiver_id,
                    content=message.content,
                    message_type=message.message_type,
                    status=message.status,
                    is_edited=message.is_edited,
                    created_at=message.created_at,
                    delivered_at=message.delivered_at,
                    read_at=message.read_at,
                    sender_username=message.sender.username if message.sender else None,
                    sender_full_name=message.sender.full_name if message.sender else None,
                ))
            
            return MessagesListResponse(
                messages=message_responses,
                total_count=total_count or 0,
                has_more=has_more,
                page=page,
                limit=limit
            )
            
        except Exception as e:
            logger.error(f"خطأ في جلب الرسائل بين {user1_id} و {user2_id}: {e}")
            return MessagesListResponse(
                messages=[],
                total_count=0,
                has_more=False,
                page=page,
                limit=limit
            )

    async def mark_messages_as_read(self, user_id: int, other_user_id: int, message_ids: Optional[List[int]] = None) -> int:
        """
        تحديد الرسائل كمقروءة
        """
        try:
            current_time = get_tripoli_now()
            
            # بناء الاستعلام الأساسي - الرسائل المرسلة من المستخدم الآخر إلى المستخدم الحالي
            base_conditions = [
                ChatMessage.sender_id == other_user_id,
                ChatMessage.receiver_id == user_id,
                ChatMessage.status != MessageStatus.READ
            ]
            
            # إضافة شرط معرفات الرسائل إذا تم تحديدها
            if message_ids:
                base_conditions.append(ChatMessage.id.in_(message_ids))
            
            # تحديث الرسائل
            update_stmt = update(ChatMessage).where(
                and_(*base_conditions)
            ).values(
                status=MessageStatus.READ,
                read_at=current_time
            )
            
            result = self.db.execute(update_stmt)
            updated_count = getattr(result, 'rowcount', 0)
            self.db.commit()
            
            # إشعار المرسل بقراءة الرسائل
            if updated_count > 0:
                websocket_message = {
                    'type': 'messages_read',
                    'reader_id': user_id,
                    'count': updated_count,
                    'timestamp': current_time.isoformat()
                }
                
                await chat_websocket_manager.send_message_to_user(
                    other_user_id,
                    websocket_message
                )
            
            logger.info(f"✅ تم تحديد {updated_count} رسالة كمقروءة للمستخدم {user_id}")
            return updated_count
            
        except Exception as e:
            logger.error(f"خطأ في تحديد الرسائل كمقروءة: {e}")
            self.db.rollback()
            return 0

    def get_conversations_list(
        self,
        user_id: int,
        page: int = 1,
        limit: int = 20
    ) -> dict:
        """
        جلب قائمة المحادثات للمستخدم مع دعم التحميل التدريجي
        """
        try:
            # استعلام معقد لجلب آخر رسالة مع كل مستخدم
            subquery = select(
                ChatMessage.id,
                ChatMessage.sender_id,
                ChatMessage.receiver_id,
                ChatMessage.content,
                ChatMessage.message_type,
                ChatMessage.status,
                ChatMessage.created_at,
                func.row_number().over(
                    partition_by=
                        case(
                            (ChatMessage.sender_id == user_id, ChatMessage.receiver_id),
                            else_=ChatMessage.sender_id
                        ),
                    order_by=desc(ChatMessage.created_at)
                ).label('rn')
            ).where(
                or_(ChatMessage.sender_id == user_id, ChatMessage.receiver_id == user_id)
            ).subquery()
            
            # جلب آخر رسالة مع كل مستخدم
            last_messages_query = select(subquery).where(subquery.c.rn == 1)
            last_messages = self.db.execute(last_messages_query).all()
            
            conversations = []
            processed_users = set()
            
            for msg in last_messages:
                # تحديد المستخدم الآخر
                other_user_id = msg.receiver_id if msg.sender_id == user_id else msg.sender_id
                
                if other_user_id in processed_users:
                    continue
                processed_users.add(other_user_id)
                
                # جلب معلومات المستخدم الآخر
                user_stmt = select(User).where(User.id == other_user_id)
                other_user = self.db.execute(user_stmt).scalar_one_or_none()
                
                if not other_user:
                    continue
                
                # حساب عدد الرسائل غير المقروءة
                unread_count_stmt = select(func.count(ChatMessage.id)).where(
                    and_(
                        ChatMessage.sender_id == other_user_id,
                        ChatMessage.receiver_id == user_id,
                        ChatMessage.status != MessageStatus.READ
                    )
                )
                unread_count = self.db.execute(unread_count_stmt).scalar()
                
                # إنشاء استجابة آخر رسالة
                last_message = MessageResponse(
                    id=msg.id,
                    sender_id=msg.sender_id,
                    receiver_id=msg.receiver_id,
                    content=msg.content,
                    message_type=MessageType(msg.message_type),
                    status=MessageStatus(msg.status),
                    is_edited=False,  # سيتم تحسينه لاحقاً
                    created_at=msg.created_at,
                    delivered_at=None,  # سيتم تحسينه لاحقاً
                    read_at=None,  # سيتم تحسينه لاحقاً
                )
                
                # إنشاء استجابة المحادثة
                conversation = ConversationResponse(
                    user_id=other_user.id,
                    username=other_user.username,
                    full_name=other_user.full_name,
                    is_online=other_user.is_online,
                    last_seen=other_user.last_seen,
                    last_message=last_message,
                    unread_count=unread_count or 0
                )
                
                conversations.append(conversation)
            
            # ترتيب حسب آخر رسالة
            conversations.sort(key=lambda x: x.last_message.created_at if x.last_message else datetime.min, reverse=True)

            # تطبيق التحميل التدريجي
            total_conversations = len(conversations)
            start_index = (page - 1) * limit
            end_index = start_index + limit

            paginated_conversations = conversations[start_index:end_index]
            has_more = end_index < total_conversations

            return {
                "conversations": paginated_conversations,
                "total_count": total_conversations,
                "page": page,
                "limit": limit,
                "has_more": has_more,
                "total_pages": (total_conversations + limit - 1) // limit
            }
            
        except Exception as e:
            logger.error(f"خطأ في جلب قائمة المحادثات للمستخدم {user_id}: {e}")
            return []

    def get_unread_messages_count(self, user_id: int) -> int:
        """
        جلب عدد الرسائل غير المقروءة للمستخدم
        """
        try:
            count_stmt = select(func.count(ChatMessage.id)).where(
                and_(
                    ChatMessage.receiver_id == user_id,
                    ChatMessage.status != MessageStatus.READ
                )
            )
            
            count = self.db.execute(count_stmt).scalar()
            return count or 0
            
        except Exception as e:
            logger.error(f"خطأ في جلب عدد الرسائل غير المقروءة للمستخدم {user_id}: {e}")
            return 0

    def search_messages(self, user_id: int, query: str, limit: int = 20) -> List[MessageResponse]:
        """
        البحث في الرسائل
        """
        try:
            search_query = select(ChatMessage).options(
                joinedload(ChatMessage.sender),
                joinedload(ChatMessage.receiver)
            ).where(
                and_(
                    or_(ChatMessage.sender_id == user_id, ChatMessage.receiver_id == user_id),
                    ChatMessage.content.contains(query)
                )
            ).order_by(desc(ChatMessage.created_at)).limit(limit)
            
            messages = self.db.execute(search_query).scalars().all()
            
            message_responses = []
            for message in messages:
                message_responses.append(MessageResponse(
                    id=message.id,
                    sender_id=message.sender_id,
                    receiver_id=message.receiver_id,
                    content=message.content,
                    message_type=message.message_type,
                    status=message.status,
                    is_edited=message.is_edited,
                    created_at=message.created_at,
                    delivered_at=message.delivered_at,
                    read_at=message.read_at,
                    sender_username=message.sender.username if message.sender else None,
                    sender_full_name=message.sender.full_name if message.sender else None,
                ))
            
            return message_responses
            
        except Exception as e:
            logger.error(f"خطأ في البحث في الرسائل للمستخدم {user_id}: {e}")
            return []
