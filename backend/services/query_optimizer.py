"""
خدمة تحسين الاستعلامات للبيانات الكبيرة
تحتوي على دوال محسنة للاستعلامات المعقدة والتعامل مع البيانات الكبيرة
"""

from sqlalchemy.orm import Session, joinedload, selectinload
from sqlalchemy import select, func, and_, or_, text, case, desc, asc, String
from typing import List, Dict, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
import logging
from decimal import Decimal

from models.sale import Sale, SaleItem
from models.product import Product
from models.customer import Customer, CustomerDebt
from models.user import User

logger = logging.getLogger(__name__)

class QueryOptimizer:
    """
    خدمة تحسين الاستعلامات للبيانات الكبيرة
    تحتوي على استعلامات محسنة ومُفهرسة للأداء العالي
    """
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_sales_with_pagination(
        self,
        page: int = 1,
        limit: int = 50,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        user_id: Optional[int] = None,
        customer_id: Optional[int] = None,
        payment_method: Optional[str] = None,
        min_amount: Optional[float] = None,
        max_amount: Optional[float] = None,
        search: Optional[str] = None
    ) -> Tuple[List[Sale], int]:
        """
        جلب المبيعات مع pagination محسن وفلترة متقدمة
        """
        # بناء الاستعلام الأساسي مع eager loading
        query = select(Sale).options(
            selectinload(Sale.items).selectinload(SaleItem.product),
            selectinload(Sale.user),
            selectinload(Sale.customer)
        )
        
        # تطبيق الفلاتر
        conditions = []
        
        if start_date:
            conditions.append(Sale.created_at >= start_date)
        if end_date:
            conditions.append(Sale.created_at <= end_date)
        if user_id:
            conditions.append(Sale.user_id == user_id)
        if customer_id:
            conditions.append(Sale.customer_id == customer_id)
        if payment_method:
            conditions.append(Sale.payment_method == payment_method)
        if min_amount:
            conditions.append(Sale.total_amount >= min_amount)
        if max_amount:
            conditions.append(Sale.total_amount <= max_amount)
        
        # البحث في رقم البيع فقط
        if search:
            search_conditions = []

            # إذا كان البحث رقمي، ابحث في رقم البيع فقط
            if search.isdigit():
                search_id = int(search)
                # البحث بالرقم الدقيق
                search_conditions.append(Sale.id == search_id)
                # البحث برقم البيع كنص (للبحث الجزئي)
                search_conditions.append(func.cast(Sale.id, String).like(f"%{search}%"))

            if search_conditions:
                conditions.append(or_(*search_conditions))
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # ترتيب حسب التاريخ (الأحدث أولاً)
        query = query.order_by(desc(Sale.created_at))
        
        # حساب العدد الإجمالي
        count_query = select(func.count(Sale.id))
        if conditions:
            count_query = count_query.where(and_(*conditions))
        
        total_count = self.db.execute(count_query).scalar() or 0
        
        # تطبيق pagination
        offset = (page - 1) * limit
        query = query.offset(offset).limit(limit)
        
        # تنفيذ الاستعلام
        result = self.db.execute(query)
        sales = list(result.scalars().all())

        logger.info(f"Retrieved {len(sales)} sales from {total_count} total")
        return sales, total_count
    
    def get_products_with_analytics(
        self,
        page: int = 1,
        limit: int = 50,
        category: Optional[str] = None,
        search: Optional[str] = None,
        low_stock: bool = False,
        zero_stock: bool = False,
        is_active: Optional[bool] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        جلب المنتجات مع تحليلات المبيعات المحسنة
        """
        # بناء شروط التاريخ للـ join
        date_conditions = [SaleItem.sale_id == Sale.id]
        if start_date:
            date_conditions.append(Sale.created_at >= start_date)
        if end_date:
            date_conditions.append(Sale.created_at <= end_date)

        # استعلام محسن للمنتجات مع إحصائيات المبيعات
        base_query = select(
            Product.id,
            Product.name,
            Product.barcode,
            Product.category,
            Product.price,
            Product.cost_price,
            Product.quantity,
            Product.min_quantity,
            Product.unit,
            Product.is_active,
            Product.created_at,
            func.coalesce(func.sum(SaleItem.quantity), 0).label('total_sold'),
            func.coalesce(func.sum(SaleItem.subtotal), 0).label('total_revenue'),
            func.coalesce(
                func.sum((SaleItem.unit_price - Product.cost_price) * SaleItem.quantity),
                0
            ).label('total_profit'),
            func.count(Sale.id).label('sales_count')
        ).select_from(Product)\
        .outerjoin(SaleItem, Product.id == SaleItem.product_id)\
        .outerjoin(Sale, and_(*date_conditions))
        
        # تطبيق الفلاتر
        conditions = []
        
        if category:
            conditions.append(Product.category == category)
        if is_active is not None:
            conditions.append(Product.is_active == is_active)
        if low_stock:
            conditions.append(Product.quantity <= Product.min_quantity)
        if zero_stock:
            conditions.append(Product.quantity == 0)
        
        # البحث النصي
        if search:
            search_conditions = [
                Product.name.ilike(f"%{search}%"),
                Product.barcode.ilike(f"%{search}%"),
                Product.category.ilike(f"%{search}%")
            ]
            conditions.append(or_(*search_conditions))
        
        if conditions:
            base_query = base_query.where(and_(*conditions))
        
        # تجميع البيانات
        base_query = base_query.group_by(Product.id)
        
        # حساب العدد الإجمالي
        count_query = select(func.count(func.distinct(Product.id)))
        if conditions:
            count_query = count_query.where(and_(*conditions))
        
        total_count = self.db.execute(count_query).scalar() or 0
        
        # ترتيب وpagination
        base_query = base_query.order_by(desc(Product.created_at))
        offset = (page - 1) * limit
        base_query = base_query.offset(offset).limit(limit)
        
        # تنفيذ الاستعلام
        result = self.db.execute(base_query)
        products_data = []
        
        for row in result:
            profit_margin = 0
            if row.total_revenue > 0:
                profit_margin = (row.total_profit / row.total_revenue) * 100
            
            products_data.append({
                'id': row.id,
                'name': row.name,
                'barcode': row.barcode,
                'category': row.category,
                'price': float(row.price),
                'cost_price': float(row.cost_price),
                'quantity': row.quantity,
                'min_quantity': row.min_quantity,
                'unit': row.unit,
                'is_active': row.is_active,
                'created_at': row.created_at,
                'total_sold': int(row.total_sold),
                'total_revenue': float(row.total_revenue),
                'total_profit': float(row.total_profit),
                'profit_margin': round(profit_margin, 2),
                'sales_count': row.sales_count,
                'stock_status': self._get_stock_status(row.quantity, row.min_quantity)
            })
        
        logger.info(f"Retrieved {len(products_data)} products with analytics from {total_count} total")
        return products_data, total_count
    
    def get_dashboard_stats_optimized(
        self,
        user_id: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        جلب إحصائيات لوحة التحكم بطريقة محسنة
        """
        # استعلام واحد محسن لجميع الإحصائيات
        stats_query = select(
            func.count(Sale.id).label('total_sales'),
            func.coalesce(func.sum(Sale.amount_paid), 0).label('total_revenue'),
            func.coalesce(func.sum(
                case(
                    (Sale.created_at >= func.date('now'), Sale.amount_paid),
                    else_=0
                )
            ), 0).label('today_revenue'),
            func.count(
                case(
                    (Sale.created_at >= func.date('now'), Sale.id),
                    else_=None
                )
            ).label('today_sales_count')
        )
        
        # تطبيق الفلاتر
        conditions = []
        if user_id:
            conditions.append(Sale.user_id == user_id)
        if start_date:
            conditions.append(Sale.created_at >= start_date)
        if end_date:
            conditions.append(Sale.created_at <= end_date)
        
        if conditions:
            stats_query = stats_query.where(and_(*conditions))
        
        stats_result = self.db.execute(stats_query).first()
        
        # إحصائيات المخزون
        inventory_query = select(
            func.count(Product.id).label('total_products'),
            func.count(
                case(
                    (Product.quantity <= Product.min_quantity, Product.id),
                    else_=None
                )
            ).label('low_stock_count'),
            func.count(
                case(
                    (Product.quantity == 0, Product.id),
                    else_=None
                )
            ).label('out_of_stock_count')
        ).where(Product.is_active == True)
        
        inventory_result = self.db.execute(inventory_query).first()
        
        # إحصائيات الديون
        debt_query = select(
            func.coalesce(func.sum(CustomerDebt.remaining_amount), 0).label('total_debts'),
            func.count(CustomerDebt.id).label('debt_count')
        ).where(CustomerDebt.is_paid == False)
        
        debt_result = self.db.execute(debt_query).first()
        
        return {
            'total_sales': getattr(stats_result, 'total_sales', 0) or 0,
            'total_revenue': float(getattr(stats_result, 'total_revenue', 0) or 0),
            'today_revenue': float(getattr(stats_result, 'today_revenue', 0) or 0),
            'today_sales_count': getattr(stats_result, 'today_sales_count', 0) or 0,
            'total_products': getattr(inventory_result, 'total_products', 0) or 0,
            'low_stock_count': getattr(inventory_result, 'low_stock_count', 0) or 0,
            'out_of_stock_count': getattr(inventory_result, 'out_of_stock_count', 0) or 0,
            'total_debts': float(getattr(debt_result, 'total_debts', 0) or 0),
            'debt_count': getattr(debt_result, 'debt_count', 0) or 0
        }
    
    def _get_stock_status(self, quantity: int, min_quantity: int) -> str:
        """تحديد حالة المخزون"""
        if quantity == 0:
            return 'out_of_stock'
        elif quantity <= min_quantity:
            return 'low_stock'
        elif quantity > min_quantity * 3:
            return 'overstocked'
        else:
            return 'healthy'
    
    def optimize_database(self) -> Dict[str, Any]:
        """
        تحسين قاعدة البيانات وإعادة بناء الفهارس
        """
        try:
            # تحليل وتحسين الجداول
            self.db.execute(text("ANALYZE"))
            
            # إعادة بناء الفهارس
            self.db.execute(text("REINDEX"))
            
            # تنظيف قاعدة البيانات
            self.db.execute(text("VACUUM"))
            
            # تحسين الإحصائيات
            self.db.execute(text("PRAGMA optimize"))
            
            self.db.commit()
            
            logger.info("Database optimization completed successfully")
            return {"status": "success", "message": "Database optimized successfully"}
            
        except Exception as e:
            logger.error(f"Database optimization failed: {e}")
            self.db.rollback()
            return {"status": "error", "message": str(e)}
