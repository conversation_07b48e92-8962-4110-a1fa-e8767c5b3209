"""
خدمة التخزين المؤقت للبيانات الكبيرة
تحسين الأداء من خلال تخزين النتائج المتكررة
"""

import json
import hashlib
import time
from typing import Any, Dict, Optional, Callable
from datetime import datetime
import logging
from functools import wraps
import threading

logger = logging.getLogger(__name__)

class CacheService:
    """
    خدمة التخزين المؤقت المحسنة للبيانات الكبيرة
    تدعم TTL، تنظيف تلقائي، وإدارة الذاكرة
    """
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 300):
        """
        تهيئة خدمة التخزين المؤقت
        
        Args:
            max_size: الحد الأقصى لعدد العناصر المخزنة
            default_ttl: مدة البقاء الافتراضية بالثواني (5 دقائق)
        """
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.access_times: Dict[str, float] = {}
        self.lock = threading.RLock()
        
        # بدء عملية التنظيف التلقائي
        self._start_cleanup_thread()
        
        logger.info(f"Cache service initialized with max_size={max_size}, default_ttl={default_ttl}")
    
    def _generate_key(self, prefix: str, *args, **kwargs) -> str:
        """توليد مفتاح فريد للتخزين المؤقت"""
        # تحويل المعاملات إلى نص قابل للتجمع
        key_data = {
            'prefix': prefix,
            'args': args,
            'kwargs': sorted(kwargs.items()) if kwargs else {}
        }
        
        # إنشاء hash للمفتاح
        key_string = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def get(self, key: str) -> Optional[Any]:
        """جلب قيمة من التخزين المؤقت"""
        with self.lock:
            if key not in self.cache:
                return None
            
            entry = self.cache[key]
            
            # فحص انتهاء الصلاحية
            if entry['expires_at'] < time.time():
                del self.cache[key]
                if key in self.access_times:
                    del self.access_times[key]
                return None
            
            # تحديث وقت الوصول
            self.access_times[key] = time.time()
            
            logger.debug(f"Cache hit for key: {key[:16]}...")
            return entry['data']
    
    def set(self, key: str, data: Any, ttl: Optional[int] = None) -> None:
        """حفظ قيمة في التخزين المؤقت"""
        with self.lock:
            # استخدام TTL الافتراضي إذا لم يتم تحديده
            if ttl is None:
                ttl = self.default_ttl
            
            expires_at = time.time() + ttl
            
            # إضافة العنصر الجديد
            self.cache[key] = {
                'data': data,
                'created_at': time.time(),
                'expires_at': expires_at,
                'ttl': ttl
            }
            
            self.access_times[key] = time.time()
            
            # تنظيف إذا تجاوز الحد الأقصى
            if len(self.cache) > self.max_size:
                self._evict_lru()
            
            logger.debug(f"Cache set for key: {key[:16]}... (TTL: {ttl}s)")
    
    def delete(self, key: str) -> bool:
        """حذف عنصر من التخزين المؤقت"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                if key in self.access_times:
                    del self.access_times[key]
                logger.debug(f"Cache deleted for key: {key[:16]}...")
                return True
            return False
    
    def clear(self) -> None:
        """مسح جميع عناصر التخزين المؤقت"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
            logger.info("Cache cleared")
    
    def _evict_lru(self) -> None:
        """إزالة العنصر الأقل استخداماً (LRU)"""
        if not self.access_times:
            return
        
        # العثور على العنصر الأقل استخداماً
        lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        
        # حذف العنصر
        if lru_key in self.cache:
            del self.cache[lru_key]
        del self.access_times[lru_key]
        
        logger.debug(f"Evicted LRU item: {lru_key[:16]}...")
    
    def _cleanup_expired(self) -> int:
        """تنظيف العناصر المنتهية الصلاحية"""
        current_time = time.time()
        expired_keys = []
        
        with self.lock:
            for key, entry in self.cache.items():
                if entry['expires_at'] < current_time:
                    expired_keys.append(key)
            
            # حذف العناصر المنتهية الصلاحية
            for key in expired_keys:
                del self.cache[key]
                if key in self.access_times:
                    del self.access_times[key]
        
        if expired_keys:
            logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
        
        return len(expired_keys)
    
    def _start_cleanup_thread(self) -> None:
        """بدء خيط التنظيف التلقائي"""
        def cleanup_worker():
            while True:
                try:
                    time.sleep(60)  # تنظيف كل دقيقة
                    self._cleanup_expired()
                except Exception as e:
                    logger.error(f"Cache cleanup error: {e}")
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
        logger.info("Cache cleanup thread started")
    
    def get_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التخزين المؤقت"""
        with self.lock:
            current_time = time.time()
            expired_count = sum(
                1 for entry in self.cache.values() 
                if entry['expires_at'] < current_time
            )
            
            return {
                'total_items': len(self.cache),
                'expired_items': expired_count,
                'active_items': len(self.cache) - expired_count,
                'max_size': self.max_size,
                'memory_usage_percent': (len(self.cache) / self.max_size) * 100,
                'default_ttl': self.default_ttl
            }

# إنشاء مثيل عام للخدمة
cache_service = CacheService(max_size=2000, default_ttl=300)

def cached(prefix: str, ttl: Optional[int] = None):
    """
    ديكوريتر للتخزين المؤقت للدوال
    
    Args:
        prefix: بادئة المفتاح
        ttl: مدة البقاء (اختياري)
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # توليد مفتاح التخزين المؤقت
            cache_key = cache_service._generate_key(prefix, *args, **kwargs)
            
            # محاولة جلب النتيجة من التخزين المؤقت
            cached_result = cache_service.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # تنفيذ الدالة وحفظ النتيجة
            result = func(*args, **kwargs)
            cache_service.set(cache_key, result, ttl)
            
            return result
        
        return wrapper
    return decorator

def cache_key_for_user(user_id: int, operation: str, **params) -> str:
    """توليد مفتاح تخزين مؤقت خاص بالمستخدم"""
    return cache_service._generate_key(f"user_{user_id}_{operation}", **params)

def cache_key_for_date_range(start_date: datetime, end_date: datetime, operation: str) -> str:
    """توليد مفتاح تخزين مؤقت للفترات الزمنية"""
    return cache_service._generate_key(
        f"date_range_{operation}",
        start_date=start_date.isoformat(),
        end_date=end_date.isoformat()
    )

def invalidate_user_cache(user_id: int) -> None:
    """إلغاء جميع البيانات المؤقتة الخاصة بمستخدم معين"""
    with cache_service.lock:
        keys_to_delete = [
            key for key in cache_service.cache.keys()
            if f"user_{user_id}_" in key
        ]
        
        for key in keys_to_delete:
            cache_service.delete(key)
        
        logger.info(f"Invalidated {len(keys_to_delete)} cache entries for user {user_id}")

def invalidate_sales_cache() -> None:
    """إلغاء جميع البيانات المؤقتة المتعلقة بالمبيعات"""
    with cache_service.lock:
        keys_to_delete = [
            key for key in cache_service.cache.keys()
            if any(pattern in key for pattern in ['sales', 'dashboard', 'analytics'])
        ]
        
        for key in keys_to_delete:
            cache_service.delete(key)
        
        logger.info(f"Invalidated {len(keys_to_delete)} sales-related cache entries")

def invalidate_product_cache() -> None:
    """إلغاء جميع البيانات المؤقتة المتعلقة بالمنتجات"""
    with cache_service.lock:
        keys_to_delete = [
            key for key in cache_service.cache.keys()
            if any(pattern in key for pattern in ['product', 'inventory', 'analytics'])
        ]
        
        for key in keys_to_delete:
            cache_service.delete(key)
        
        logger.info(f"Invalidated {len(keys_to_delete)} product-related cache entries")
