import smtplib
import ssl
import json
import os
import sys
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging
from database.session import get_db
from models.setting import Setting

logger = logging.getLogger(__name__)

class EmailService:
    def __init__(self):
        self.smtp_server = "smtp.gmail.com"
        self.smtp_port = 587
        self.support_email = "<EMAIL>"
        self.sender_email = os.getenv("EMAIL_USER", "<EMAIL>")
        self.sender_password = os.getenv("EMAIL_PASS", "cqoi dpjk ojcu zbzz")

        # تفعيل الإرسال الحقيقي
        self.use_test_mode = False  # تم تعطيل وضع الاختبار

    def _get_store_settings(self) -> Dict[str, str]:
        """جلب إعدادات المتجر من قاعدة البيانات"""
        try:
            # إنشاء جلسة قاعدة بيانات
            db_gen = get_db()
            db = next(db_gen)

            # جلب إعدادات المتجر
            store_settings = {}
            settings = db.query(Setting).filter(
                Setting.key.in_([
                    'store_name', 'store_address', 'store_phone',
                    'store_email', 'currency_symbol'
                ])
            ).all()

            for setting in settings:
                store_settings[setting.key] = setting.value

            # إعدادات افتراضية في حالة عدم وجود البيانات
            default_settings = {
                'store_name': 'SmartPOS Libya',
                'store_address': 'طرابلس، ليبيا',
                'store_phone': '+218-91-1234567',
                'store_email': '<EMAIL>',
                'currency_symbol': 'د.ل'
            }

            # دمج الإعدادات مع القيم الافتراضية
            for key, default_value in default_settings.items():
                if key not in store_settings:
                    store_settings[key] = default_value

            db.close()
            return store_settings

        except Exception as e:
            logger.error(f"Error fetching store settings: {e}")
            # إرجاع إعدادات افتراضية في حالة الخطأ
            return {
                'store_name': 'SmartPOS Libya',
                'store_address': 'طرابلس، ليبيا',
                'store_phone': '+218-91-1234567',
                'store_email': '<EMAIL>',
                'currency_symbol': 'د.ل'
            }

    def _get_database_info(self) -> Dict[str, Any]:
        """جلب معلومات قاعدة البيانات للتشخيص"""
        try:
            from database.session import get_db
            from models.user import User
            from models.product import Product
            from models.sale import Sale
            from models.customer import Customer, CustomerDebt, DebtPayment
            from models.setting import Setting
            from sqlalchemy import func
            import os

            db = next(get_db())

            # معلومات أساسية عن قاعدة البيانات
            database_info = {
                'database_file': os.path.abspath('smartpos.db'),
                'database_size': 0,
                'tables_info': {},
                'system_stats': {},
                'recent_activity': {}
            }

            # حجم ملف قاعدة البيانات
            try:
                if os.path.exists('smartpos.db'):
                    database_info['database_size'] = os.path.getsize('smartpos.db')
                    database_info['database_size_mb'] = round(database_info['database_size'] / (1024 * 1024), 2)
            except Exception as e:
                logger.error(f"Error getting database size: {e}")

            # إحصائيات الجداول
            try:
                # عدد المستخدمين
                users_count = db.query(func.count(User.id)).scalar() or 0
                database_info['tables_info']['users'] = {
                    'count': users_count,
                    'active_users': db.query(func.count(User.id)).filter(User.is_active == True).scalar() or 0
                }

                # عدد المنتجات
                products_count = db.query(func.count(Product.id)).scalar() or 0
                database_info['tables_info']['products'] = {
                    'count': products_count,
                    'active_products': db.query(func.count(Product.id)).filter(Product.is_active == True).scalar() or 0,
                    'out_of_stock': db.query(func.count(Product.id)).filter(Product.quantity == 0).scalar() or 0
                }

                # عدد المبيعات
                sales_count = db.query(func.count(Sale.id)).scalar() or 0
                database_info['tables_info']['sales'] = {
                    'count': sales_count,
                    'total_amount': float(db.query(func.sum(Sale.total_amount)).scalar() or 0)
                }

                # عدد العملاء
                customers_count = db.query(func.count(Customer.id)).scalar() or 0
                database_info['tables_info']['customers'] = {
                    'count': customers_count,
                    'active_customers': db.query(func.count(Customer.id)).filter(Customer.is_active == True).scalar() or 0
                }

                # عدد الديون
                debts_count = db.query(func.count(CustomerDebt.id)).scalar() or 0
                database_info['tables_info']['customer_debts'] = {
                    'count': debts_count,
                    'unpaid_debts': db.query(func.count(CustomerDebt.id)).filter(CustomerDebt.is_paid == False).scalar() or 0,
                    'total_debt_amount': float(db.query(func.sum(CustomerDebt.amount)).scalar() or 0)
                }

                # عدد دفعات الديون
                payments_count = db.query(func.count(DebtPayment.id)).scalar() or 0
                database_info['tables_info']['debt_payments'] = {
                    'count': payments_count,
                    'total_payments': float(db.query(func.sum(DebtPayment.amount)).scalar() or 0)
                }

                # عدد الإعدادات
                settings_count = db.query(func.count(Setting.id)).scalar() or 0
                database_info['tables_info']['settings'] = {
                    'count': settings_count
                }

            except Exception as e:
                logger.error(f"Error getting table statistics: {e}")
                database_info['tables_info']['error'] = str(e)

            # معلومات النظام
            try:
                database_info['system_stats'] = {
                    'python_version': sys.version,
                    'database_engine': 'SQLite',
                    'total_tables': len(database_info['tables_info']),
                    'server_time': datetime.now().isoformat()
                }
            except Exception as e:
                logger.error(f"Error getting system stats: {e}")

            # النشاط الأخير
            try:
                # آخر مبيعة
                last_sale = db.query(Sale).order_by(Sale.created_at.desc()).first()
                if last_sale:
                    database_info['recent_activity']['last_sale'] = {
                        'id': last_sale.id,
                        'amount': float(getattr(last_sale, 'total_amount', 0) or 0),
                        'date': last_sale.created_at.isoformat() if getattr(last_sale, 'created_at', None) else None
                    }

                # آخر دين
                last_debt = db.query(CustomerDebt).order_by(CustomerDebt.created_at.desc()).first()
                if last_debt:
                    database_info['recent_activity']['last_debt'] = {
                        'id': last_debt.id,
                        'amount': float(getattr(last_debt, 'amount', 0) or 0),
                        'date': last_debt.created_at.isoformat() if getattr(last_debt, 'created_at', None) else None
                    }

            except Exception as e:
                logger.error(f"Error getting recent activity: {e}")
                database_info['recent_activity']['error'] = str(e)

            db.close()
            return database_info

        except Exception as e:
            logger.error(f"Error getting database info: {e}")
            return {
                'error': str(e),
                'message': 'فشل في جلب معلومات قاعدة البيانات'
            }

    def send_error_logs_to_support(self, logs: List[Dict], user_info: Optional[Dict] = None) -> Dict[str, Any]:
        """إرسال سجلات الأخطاء إلى الدعم عبر البريد الإلكتروني"""
        try:
            # التحقق من إعدادات البريد الإلكتروني
            if not self.sender_email or not self.sender_password:
                logger.error("❌ Missing email configuration")
                return {
                    "success": False,
                    "error": "إعدادات البريد الإلكتروني غير مكتملة",
                    "message": "يرجى التحقق من إعدادات البريد الإلكتروني في النظام"
                }

            # جلب بيانات المتجر
            store_info = self._get_store_settings()
            logger.info(f"📊 Store info loaded: {store_info.get('store_name', 'Unknown')}")

            # جلب بيانات قاعدة البيانات
            database_info = self._get_database_info()
            logger.info(f"🗄️ Database info loaded: {len(database_info)} keys")

            if self.use_test_mode:
                logger.info("🧪 Using test mode - simulating email send")
                return self._simulate_email_send(logs, user_info, store_info, database_info)

            logger.info(f"📧 Preparing to send {len(logs)} logs to {self.support_email}")

            # إنشاء رسالة البريد الإلكتروني
            message = MIMEMultipart("alternative")
            message["Subject"] = f"🚨 تقرير أخطاء النظام - {store_info.get('store_name', 'SmartPOS')}"
            message["From"] = self.sender_email
            message["To"] = self.support_email

            # إنشاء محتوى HTML
            logger.info("📝 Generating HTML content...")
            html_content = self._generate_html_content(logs, user_info, store_info, database_info)
            html_part = MIMEText(html_content, "html", "utf-8")
            message.attach(html_part)

            # إضافة المرفقات
            logger.info("📎 Generating attachments...")
            attachments = self._generate_attachments(logs, database_info)
            for attachment in attachments:
                message.attach(attachment)

            # إرسال البريد الإلكتروني
            logger.info(f"🚀 Connecting to SMTP server: {self.smtp_server}:{self.smtp_port}")
            context = ssl.create_default_context()

            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                logger.info("🔐 Starting TLS...")
                server.starttls(context=context)

                logger.info(f"🔑 Logging in with email: {self.sender_email}")
                server.login(self.sender_email, self.sender_password)

                logger.info(f"📤 Sending email from {self.sender_email} to {self.support_email}")
                result = server.sendmail(self.sender_email, self.support_email, message.as_string())

                if result:
                    logger.warning(f"⚠️ Some recipients failed: {result}")
                else:
                    logger.info("✅ Email sent successfully to all recipients")

            logger.info(f"✅ Error logs sent to support successfully: {len(logs)} logs")
            return {
                "success": True,
                "message": f"تم إرسال {len(logs)} سجل إلى الدعم بنجاح عبر البريد الإلكتروني",
                "logs_count": len(logs),
                "support_email": self.support_email,
                "sender_email": self.sender_email,
                "real_email": True
            }

        except smtplib.SMTPAuthenticationError as e:
            error_msg = f"خطأ في المصادقة: {str(e)}"
            logger.error(f"❌ SMTP Authentication Error: {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "message": "خطأ في مصادقة البريد الإلكتروني. يرجى التحقق من كلمة المرور أو إعدادات الأمان."
            }
        except smtplib.SMTPConnectError as e:
            error_msg = f"خطأ في الاتصال: {str(e)}"
            logger.error(f"❌ SMTP Connection Error: {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "message": "فشل في الاتصال بخادم البريد الإلكتروني. يرجى التحقق من اتصال الإنترنت."
            }
        except smtplib.SMTPException as e:
            error_msg = f"خطأ SMTP: {str(e)}"
            logger.error(f"❌ SMTP Error: {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "message": "خطأ في خادم البريد الإلكتروني. يرجى المحاولة مرة أخرى لاحقاً."
            }
        except Exception as e:
            error_msg = str(e)
            logger.error(f"❌ Failed to send error logs: {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "message": "فشل في إرسال السجلات إلى الدعم. يرجى التحقق من إعدادات النظام."
            }
    
    def _simulate_email_send(self, logs: List[Dict], user_info: Optional[Dict] = None, store_info: Optional[Dict] = None, database_info: Optional[Dict] = None) -> Dict[str, Any]:
        """محاكاة إرسال البريد الإلكتروني للاختبار"""
        try:
            # حفظ التقرير في ملف محلي
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_dir = os.path.join(os.path.dirname(__file__), '../email_reports')
            os.makedirs(report_dir, exist_ok=True)

            report_file = os.path.join(report_dir, f'email_report_{timestamp}.html')
            html_content = self._generate_html_content(logs, user_info, store_info, database_info)

            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(html_content)

            # حفظ البيانات كـ JSON أيضاً
            json_file = os.path.join(report_dir, f'email_report_{timestamp}.json')
            report_data = {
                "timestamp": datetime.now().isoformat(),
                "logs": logs,
                "user_info": user_info or {},
                "store_info": store_info or {},
                "database_info": database_info or {},
                "support_email": self.support_email
            }
            
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"📧 Email simulation: Report saved to {report_file}")
            return {
                "success": True,
                "message": "تم حفظ التقرير محلياً (وضع الاختبار)",
                "logs_count": len(logs),
                "support_email": self.support_email,
                "report_file": report_file,
                "test_mode": True
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to simulate email: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "فشل في محاكاة إرسال البريد الإلكتروني"
            }
    
    def _generate_html_content(self, logs: List[Dict], user_info: Optional[Dict] = None, store_info: Optional[Dict] = None, database_info: Optional[Dict] = None) -> str:
        """إنشاء محتوى HTML للبريد الإلكتروني"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        user_info = user_info or {}
        store_info = store_info or self._get_store_settings()
        database_info = database_info or {}

        # تصنيف السجلات
        critical_logs = [log for log in logs if log.get('level') == 'CRITICAL']
        error_logs = [log for log in logs if log.get('level') == 'ERROR']
        warning_logs = [log for log in logs if log.get('level') == 'WARNING']
        info_logs = [log for log in logs if log.get('level') == 'INFO']
        
        html_content = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>تقرير أخطاء النظام</title>
            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #f5f5f5;
                }}
                .container {{
                    background: white;
                    border-radius: 10px;
                    padding: 30px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }}
                .header {{
                    text-align: center;
                    border-bottom: 3px solid #dc3545;
                    padding-bottom: 20px;
                    margin-bottom: 30px;
                }}
                .header h1 {{
                    color: #dc3545;
                    margin: 0;
                    font-size: 28px;
                }}
                .summary {{
                    background: #f8f9fa;
                    border-left: 4px solid #007bff;
                    padding: 15px;
                    margin: 20px 0;
                    border-radius: 5px;
                }}
                .stats {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                    gap: 15px;
                    margin: 20px 0;
                }}
                .stat-card {{
                    text-align: center;
                    padding: 15px;
                    border-radius: 8px;
                    color: white;
                    font-weight: bold;
                }}
                .critical {{ background: #dc3545; }}
                .error {{ background: #fd7e14; }}
                .warning {{ background: #ffc107; color: #000; }}
                .info {{ background: #17a2b8; }}
                .logs-section {{
                    margin: 30px 0;
                }}
                .log-item {{
                    background: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 5px;
                    padding: 15px;
                    margin: 10px 0;
                }}
                .log-header {{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 10px;
                }}
                .log-level {{
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 12px;
                    font-weight: bold;
                    color: white;
                }}
                .log-source {{
                    background: #6c757d;
                    color: white;
                    padding: 2px 6px;
                    border-radius: 3px;
                    font-size: 11px;
                }}
                .log-message {{
                    font-weight: bold;
                    margin: 10px 0;
                }}
                .log-details {{
                    background: #e9ecef;
                    padding: 10px;
                    border-radius: 4px;
                    font-family: monospace;
                    font-size: 12px;
                    white-space: pre-wrap;
                    max-height: 200px;
                    overflow-y: auto;
                }}
                .footer {{
                    text-align: center;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #dee2e6;
                    color: #6c757d;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🚨 تقرير أخطاء النظام</h1>
                    <p><strong>{store_info.get('store_name', 'SmartPOS Libya')}</strong></p>
                    <p>📍 {store_info.get('store_address', 'طرابلس، ليبيا')}</p>
                    <p>📞 {store_info.get('store_phone', '+218-91-1234567')}</p>
                    <p>📧 {store_info.get('store_email', '<EMAIL>')}</p>
                    <p>تاريخ الإرسال: {timestamp}</p>
                    <p><strong>مرسل من:</strong> {self.sender_email}</p>
                    <p><strong>إلى الدعم:</strong> {self.support_email}</p>
                </div>

                <div class="summary">
                    <h3>📊 ملخص التقرير</h3>
                    <p><strong>اسم المؤسسة:</strong> {store_info.get('store_name', 'SmartPOS Libya')}</p>
                    <p><strong>عنوان المؤسسة:</strong> {store_info.get('store_address', 'طرابلس، ليبيا')}</p>
                    <p><strong>هاتف المتجر:</strong> {store_info.get('store_phone', '+218-91-1234567')}</p>
                    <p><strong>بريد المتجر:</strong> {store_info.get('store_email', '<EMAIL>')}</p>
                    <hr style="margin: 15px 0; border: 1px solid #dee2e6;">
                    <p><strong>إجمالي السجلات:</strong> {len(logs)}</p>
                    <p><strong>المستخدم المرسل:</strong> {user_info.get('username', 'غير محدد')}</p>
                    <p><strong>بريد المستخدم:</strong> {user_info.get('email', 'غير محدد')}</p>
                    <p><strong>دور المستخدم:</strong> {user_info.get('role', 'غير محدد')}</p>
                </div>

                <div class="stats">
                    <div class="stat-card critical">
                        <div style="font-size: 24px;">{len(critical_logs)}</div>
                        <div>أخطاء حرجة</div>
                    </div>
                    <div class="stat-card error">
                        <div style="font-size: 24px;">{len(error_logs)}</div>
                        <div>أخطاء</div>
                    </div>
                    <div class="stat-card warning">
                        <div style="font-size: 24px;">{len(warning_logs)}</div>
                        <div>تحذيرات</div>
                    </div>
                    <div class="stat-card info">
                        <div style="font-size: 24px;">{len(info_logs)}</div>
                        <div>معلومات</div>
                    </div>
                </div>

                {self._generate_database_section(database_info)}

                {self._generate_logs_section('🚨 الأخطاء الحرجة', critical_logs)}
                {self._generate_logs_section('❌ الأخطاء', error_logs)}
                {self._generate_logs_section('⚠️ التحذيرات', warning_logs)}
                {self._generate_logs_section('ℹ️ المعلومات', info_logs)}

                <div class="footer">
                    <p>تم إنشاء هذا التقرير تلقائياً بواسطة نظام SmartPOS</p>
                    <p><strong>المتجر:</strong> {store_info.get('store_name', 'SmartPOS Libya')}</p>
                    <p><strong>العنوان:</strong> {store_info.get('store_address', 'طرابلس، ليبيا')}</p>
                    <p><strong>الهاتف:</strong> {store_info.get('store_phone', '+218-91-1234567')}</p>
                    <p>للمساعدة، يرجى الرد على هذا البريد الإلكتروني</p>
                    <p><strong>بريد الدعم:</strong> {self.support_email}</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html_content

    def _generate_database_section(self, database_info: Dict) -> str:
        """إنشاء قسم بيانات قاعدة البيانات في HTML"""
        if not database_info or database_info.get('error'):
            return f"""
                <div class="logs-section">
                    <h3>🗄️ معلومات قاعدة البيانات</h3>
                    <div class="log-item">
                        <div class="log-message" style="color: #dc3545;">
                            ❌ فشل في جلب معلومات قاعدة البيانات: {database_info.get('error', 'خطأ غير معروف')}
                        </div>
                    </div>
                </div>
            """

        # معلومات أساسية
        database_size = database_info.get('database_size_mb', 0)
        tables_info = database_info.get('tables_info', {})
        system_stats = database_info.get('system_stats', {})
        recent_activity = database_info.get('recent_activity', {})

        # إنشاء جدول الإحصائيات
        tables_html = ""
        for table_name, table_data in tables_info.items():
            if table_name == 'error':
                continue

            if isinstance(table_data, dict):
                count = table_data.get('count', 0)
                extra_info = ""

                if table_name == 'users':
                    extra_info = f"النشطين: {table_data.get('active_users', 0)}"
                elif table_name == 'products':
                    extra_info = f"النشطة: {table_data.get('active_products', 0)}, نفدت: {table_data.get('out_of_stock', 0)}"
                elif table_name == 'sales':
                    extra_info = f"إجمالي المبلغ: {table_data.get('total_amount', 0):.2f} د.ل"
                elif table_name == 'customers':
                    extra_info = f"النشطين: {table_data.get('active_customers', 0)}"
                elif table_name == 'customer_debts':
                    extra_info = f"غير مدفوعة: {table_data.get('unpaid_debts', 0)}, إجمالي المبلغ: {table_data.get('total_debt_amount', 0):.2f} د.ل"
                elif table_name == 'debt_payments':
                    extra_info = f"إجمالي المدفوعات: {table_data.get('total_payments', 0):.2f} د.ل"

                tables_html += f"""
                    <tr>
                        <td style="padding: 8px; border: 1px solid #dee2e6; font-weight: bold;">{table_name}</td>
                        <td style="padding: 8px; border: 1px solid #dee2e6; text-align: center;">{count}</td>
                        <td style="padding: 8px; border: 1px solid #dee2e6; font-size: 12px;">{extra_info}</td>
                    </tr>
                """

        # النشاط الأخير
        activity_html = ""
        if recent_activity.get('last_sale'):
            sale = recent_activity['last_sale']
            activity_html += f"<p><strong>آخر مبيعة:</strong> #{sale.get('id')} - {sale.get('amount', 0):.2f} د.ل - {sale.get('date', 'غير محدد')}</p>"

        if recent_activity.get('last_debt'):
            debt = recent_activity['last_debt']
            activity_html += f"<p><strong>آخر دين:</strong> #{debt.get('id')} - {debt.get('amount', 0):.2f} د.ل - {debt.get('date', 'غير محدد')}</p>"

        return f"""
            <div class="logs-section">
                <h3>🗄️ معلومات قاعدة البيانات</h3>

                <div class="summary">
                    <h4>📊 معلومات عامة</h4>
                    <p><strong>مسار قاعدة البيانات:</strong> {database_info.get('database_file', 'غير محدد')}</p>
                    <p><strong>حجم قاعدة البيانات:</strong> {database_size} ميجابايت</p>
                    <p><strong>محرك قاعدة البيانات:</strong> {system_stats.get('database_engine', 'SQLite')}</p>
                    <p><strong>إصدار Python:</strong> {system_stats.get('python_version', 'غير محدد')}</p>
                    <p><strong>وقت الخادم:</strong> {system_stats.get('server_time', 'غير محدد')}</p>
                </div>

                <div class="summary">
                    <h4>📋 إحصائيات الجداول</h4>
                    <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="padding: 8px; border: 1px solid #dee2e6; text-align: right;">الجدول</th>
                                <th style="padding: 8px; border: 1px solid #dee2e6; text-align: center;">العدد</th>
                                <th style="padding: 8px; border: 1px solid #dee2e6; text-align: right;">تفاصيل إضافية</th>
                            </tr>
                        </thead>
                        <tbody>
                            {tables_html}
                        </tbody>
                    </table>
                </div>

                {f'''
                <div class="summary">
                    <h4>🕒 النشاط الأخير</h4>
                    {activity_html}
                </div>
                ''' if activity_html else ''}
            </div>
        """

    def _generate_logs_section(self, title: str, logs: List[Dict]) -> str:
        """إنشاء قسم السجلات في HTML"""
        if not logs:
            return ''
        
        logs_html = ""
        for log in logs:
            level_class = log.get('level', '').lower()
            timestamp = log.get('timestamp', '')
            if timestamp:
                try:
                    # تحويل التاريخ إلى تنسيق مقروء
                    if isinstance(timestamp, str):
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    else:
                        dt = timestamp
                    timestamp = dt.strftime("%Y-%m-%d %H:%M:%S")
                except:
                    timestamp = str(timestamp)
            
            details = log.get('details', '')
            if details and isinstance(details, str):
                try:
                    # محاولة تنسيق JSON
                    details_obj = json.loads(details)
                    details = json.dumps(details_obj, ensure_ascii=False, indent=2)
                except:
                    pass
            
            logs_html += f"""
                <div class="log-item">
                    <div class="log-header">
                        <span class="log-level {level_class}">{log.get('level', 'UNKNOWN')}</span>
                        <span class="log-source">{log.get('source', 'UNKNOWN')}</span>
                        <span style="font-size: 12px; color: #6c757d;">{timestamp}</span>
                    </div>
                    <div class="log-message">{log.get('message', 'رسالة غير محددة')}</div>
                    {f'<div class="log-details">{details}</div>' if details else ''}
                </div>
            """
        
        return f"""
            <div class="logs-section">
                <h3>{title} ({len(logs)})</h3>
                {logs_html}
            </div>
        """
    
    def _generate_attachments(self, logs: List[Dict], database_info: Optional[Dict] = None) -> List[MIMEBase]:
        """إنشاء مرفقات البريد الإلكتروني"""
        attachments = []

        try:
            # إنشاء ملف JSON شامل
            timestamp = datetime.now().strftime("%Y-%m-%d")

            # تجميع جميع البيانات
            complete_data = {
                "timestamp": datetime.now().isoformat(),
                "logs": logs,
                "database_info": database_info or {},
                "system_info": {
                    "python_version": __import__('sys').version,
                    "platform": __import__('sys').platform,
                    "total_logs": len(logs)
                }
            }

            json_data = json.dumps(complete_data, ensure_ascii=False, indent=2, default=str)
            
            json_attachment = MIMEBase('application', 'json')
            json_attachment.set_payload(json_data.encode('utf-8'))
            encoders.encode_base64(json_attachment)
            json_attachment.add_header(
                'Content-Disposition',
                f'attachment; filename="smartpos-error-logs-{timestamp}.json"'
            )
            attachments.append(json_attachment)
            
            # إنشاء ملف نصي
            text_report = self._generate_text_report(logs, database_info)
            text_attachment = MIMEBase('text', 'plain')
            text_attachment.set_payload(text_report.encode('utf-8'))
            encoders.encode_base64(text_attachment)
            text_attachment.add_header(
                'Content-Disposition',
                f'attachment; filename="smartpos-error-logs-{timestamp}.txt"'
            )
            attachments.append(text_attachment)
            
        except Exception as e:
            logger.error(f"Error generating attachments: {e}")
        
        return attachments

    def send_support_email_to_support(self, user_info: Dict, store_info: Dict) -> Dict[str, Any]:
        """إرسال رسالة دعم عامة إلى فريق الدعم"""
        try:
            if self.use_test_mode:
                return self._simulate_support_email_send(user_info, store_info)

            # إنشاء رسالة البريد الإلكتروني
            message = MIMEMultipart("alternative")
            message["Subject"] = f"💬 رسالة دعم من {store_info.get('store_name', 'SmartPOS')} - {user_info.get('subject', 'بدون موضوع')}"
            message["From"] = self.sender_email
            message["To"] = self.support_email
            message["Reply-To"] = user_info.get('sender_email', self.sender_email)

            # إنشاء محتوى HTML
            html_content = self._generate_support_html_content(user_info, store_info)
            html_part = MIMEText(html_content, "html", "utf-8")
            message.attach(html_part)

            # إرسال البريد الإلكتروني
            context = ssl.create_default_context()
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(self.sender_email, self.sender_password)
                server.sendmail(self.sender_email, self.support_email, message.as_string())

            logger.info(f"✅ Support email sent successfully from {user_info.get('sender_email')}")
            return {
                "success": True,
                "message": "تم إرسال رسالة الدعم بنجاح",
                "support_email": self.support_email,
                "sender_email": self.sender_email,
                "real_email": True,
                "message_id": f"support_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            }

        except Exception as e:
            error_str = str(e)
            logger.error(f"❌ Failed to send support email: {error_str}")

            # تحديد نوع الخطأ وإرجاع رسالة مناسبة
            if 'connection' in error_str.lower() or 'network' in error_str.lower():
                message = "فشل في الاتصال بخادم البريد الإلكتروني. يرجى التحقق من اتصال الإنترنت."
            elif 'authentication' in error_str.lower() or 'login' in error_str.lower():
                message = "خطأ في إعدادات البريد الإلكتروني. يرجى التواصل مع المدير."
            elif 'timeout' in error_str.lower():
                message = "انتهت مهلة الاتصال. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى."
            elif 'smtp' in error_str.lower():
                message = "خطأ في خادم البريد الإلكتروني. يرجى المحاولة مرة أخرى لاحقاً."
            else:
                message = "فشل في إرسال رسالة الدعم. يرجى التحقق من اتصال الإنترنت."

            return {
                "success": False,
                "error": error_str,
                "message": message
            }

    def _simulate_support_email_send(self, user_info: Dict, store_info: Dict) -> Dict[str, Any]:
        """محاكاة إرسال رسالة الدعم للاختبار"""
        try:
            # حفظ الرسالة في ملف محلي
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_dir = os.path.join(os.path.dirname(__file__), '../email_reports')
            os.makedirs(report_dir, exist_ok=True)

            report_file = os.path.join(report_dir, f'support_email_{timestamp}.html')
            html_content = self._generate_support_html_content(user_info, store_info)

            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(html_content)

            logger.info(f"📧 Support email simulation: Report saved to {report_file}")
            return {
                "success": True,
                "message": "تم حفظ رسالة الدعم محلياً (وضع الاختبار)",
                "support_email": self.support_email,
                "report_file": report_file,
                "test_mode": True
            }

        except Exception as e:
            logger.error(f"❌ Failed to simulate support email: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "فشل في محاكاة إرسال رسالة الدعم"
            }

    def _generate_support_html_content(self, user_info: Dict, store_info: Dict) -> str:
        """إنشاء محتوى HTML لرسالة الدعم"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        html_content = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>رسالة دعم - SmartPOS</title>
            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    background-color: #f5f5f5;
                    margin: 0;
                    padding: 20px;
                    direction: rtl;
                }}
                .container {{
                    max-width: 800px;
                    margin: 0 auto;
                    background-color: white;
                    border-radius: 10px;
                    box-shadow: 0 0 20px rgba(0,0,0,0.1);
                    overflow: hidden;
                }}
                .header {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 30px;
                    text-align: center;
                }}
                .header h1 {{
                    margin: 0;
                    font-size: 28px;
                    font-weight: bold;
                }}
                .content {{
                    padding: 30px;
                }}
                .info-section {{
                    background-color: #f8f9fa;
                    border-left: 4px solid #007bff;
                    padding: 20px;
                    margin: 20px 0;
                    border-radius: 5px;
                }}
                .store-section {{
                    background-color: #e8f5e8;
                    border-left: 4px solid #28a745;
                    padding: 20px;
                    margin: 20px 0;
                    border-radius: 5px;
                }}
                .message-section {{
                    background-color: #fff3cd;
                    border-left: 4px solid #ffc107;
                    padding: 20px;
                    margin: 20px 0;
                    border-radius: 5px;
                }}
                .footer {{
                    background-color: #343a40;
                    color: white;
                    padding: 20px;
                    text-align: center;
                    font-size: 14px;
                }}
                .label {{
                    font-weight: bold;
                    color: #495057;
                }}
                .value {{
                    color: #6c757d;
                }}
                .message-content {{
                    background-color: white;
                    padding: 15px;
                    border-radius: 5px;
                    border: 1px solid #dee2e6;
                    white-space: pre-wrap;
                    font-family: 'Courier New', monospace;
                    line-height: 1.5;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>💬 رسالة دعم جديدة</h1>
                    <p>نظام SmartPOS - فريق الدعم الفني</p>
                    <p>تاريخ الاستلام: {timestamp}</p>
                </div>

                <div class="content">
                    <!-- معلومات المرسل -->
                    <div class="info-section">
                        <h3>👤 معلومات المرسل</h3>
                        <p><span class="label">الاسم:</span> <span class="value">{user_info.get('sender_name', 'غير محدد')}</span></p>
                        <p><span class="label">البريد الإلكتروني:</span> <span class="value">{user_info.get('sender_email', 'غير محدد')}</span></p>
                        <p><span class="label">المستخدم في النظام:</span> <span class="value">{user_info.get('username', 'غير مسجل')}</span></p>
                        <p><span class="label">الدور:</span> <span class="value">{user_info.get('user_role', 'زائر')}</span></p>
                    </div>

                    <!-- معلومات المؤسسة -->
                    <div class="store-section">
                        <h3>🏪 معلومات المؤسسة</h3>
                        <p><span class="label">اسم المؤسسة:</span> <span class="value">{store_info.get('store_name', 'غير محدد')}</span></p>
                        <p><span class="label">العنوان:</span> <span class="value">{store_info.get('store_address', 'غير محدد')}</span></p>
                        <p><span class="label">الهاتف:</span> <span class="value">{store_info.get('store_phone', 'غير محدد')}</span></p>
                        <p><span class="label">البريد الإلكتروني:</span> <span class="value">{store_info.get('store_email', 'غير محدد')}</span></p>
                    </div>

                    <!-- موضوع ومحتوى الرسالة -->
                    <div class="message-section">
                        <h3>📝 تفاصيل الرسالة</h3>
                        <p><span class="label">الموضوع:</span> <span class="value">{user_info.get('subject', 'بدون موضوع')}</span></p>
                        <h4>المحتوى:</h4>
                        <div class="message-content">{user_info.get('message', 'لا يوجد محتوى')}</div>
                    </div>

                    <!-- تعليمات الرد -->
                    <div class="info-section">
                        <h3>📧 تعليمات الرد</h3>
                        <p>للرد على هذه الرسالة، يرجى استخدام عنوان البريد الإلكتروني التالي:</p>
                        <p><strong>{user_info.get('sender_email', 'غير محدد')}</strong></p>
                        <p>أو الرد مباشرة على هذا البريد الإلكتروني.</p>
                    </div>
                </div>

                <div class="footer">
                    <p>تم إنشاء هذه الرسالة تلقائياً بواسطة نظام SmartPOS</p>
                    <p><strong>المتجر:</strong> {store_info.get('store_name', 'SmartPOS Libya')}</p>
                    <p><strong>نظام الدعم الفني</strong> - {self.support_email}</p>
                </div>
            </div>
        </body>
        </html>
        """

        return html_content

    def _generate_text_report(self, logs: List[Dict], database_info: Optional[Dict] = None) -> str:
        """إنشاء تقرير نصي"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        store_info = self._get_store_settings()
        database_info = database_info or {}

        report = f"تقرير أخطاء نظام SmartPOS\n"
        report += f"{'=' * 50}\n"
        report += f"اسم المؤسسة: {store_info.get('store_name', 'SmartPOS Libya')}\n"
        report += f"عنوان المؤسسة: {store_info.get('store_address', 'طرابلس، ليبيا')}\n"
        report += f"هاتف المتجر: {store_info.get('store_phone', '+218-91-1234567')}\n"
        report += f"بريد المتجر: {store_info.get('store_email', '<EMAIL>')}\n"
        report += f"{'=' * 50}\n"
        report += f"تاريخ الإنشاء: {timestamp}\n"
        report += f"إجمالي السجلات: {len(logs)}\n"
        report += f"{'=' * 50}\n\n"

        # إضافة معلومات قاعدة البيانات
        if database_info and not database_info.get('error'):
            report += f"معلومات قاعدة البيانات\n"
            report += f"{'=' * 30}\n"
            report += f"مسار قاعدة البيانات: {database_info.get('database_file', 'غير محدد')}\n"
            report += f"حجم قاعدة البيانات: {database_info.get('database_size_mb', 0)} ميجابايت\n"

            # إحصائيات الجداول
            tables_info = database_info.get('tables_info', {})
            if tables_info:
                report += f"\nإحصائيات الجداول:\n"
                report += f"{'-' * 20}\n"
                for table_name, table_data in tables_info.items():
                    if table_name != 'error' and isinstance(table_data, dict):
                        count = table_data.get('count', 0)
                        report += f"{table_name}: {count} سجل\n"

            # النشاط الأخير
            recent_activity = database_info.get('recent_activity', {})
            if recent_activity:
                report += f"\nالنشاط الأخير:\n"
                report += f"{'-' * 15}\n"
                if recent_activity.get('last_sale'):
                    sale = recent_activity['last_sale']
                    report += f"آخر مبيعة: #{sale.get('id')} - {sale.get('amount', 0):.2f} د.ل\n"
                if recent_activity.get('last_debt'):
                    debt = recent_activity['last_debt']
                    report += f"آخر دين: #{debt.get('id')} - {debt.get('amount', 0):.2f} د.ل\n"

            report += f"{'=' * 50}\n\n"
        elif database_info and database_info.get('error'):
            report += f"خطأ في جلب معلومات قاعدة البيانات: {database_info.get('error')}\n"
            report += f"{'=' * 50}\n\n"
        
        for i, log in enumerate(logs, 1):
            report += f"{i}. [{log.get('level', 'UNKNOWN')}] {log.get('source', 'UNKNOWN')}\n"
            report += f"   الوقت: {log.get('timestamp', 'غير محدد')}\n"
            report += f"   الرسالة: {log.get('message', 'رسالة غير محددة')}\n"
            if log.get('details'):
                report += f"   التفاصيل: {log.get('details')}\n"
            report += f"   محلول: {'نعم' if log.get('resolved') else 'لا'}\n"
            report += f"{'-' * 30}\n\n"
        
        return report

# إنشاء مثيل واحد للاستخدام
email_service = EmailService()
