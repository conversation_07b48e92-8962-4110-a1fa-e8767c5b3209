"""
خدمة مخزون المستودعات
تطبق مبادئ البرمجة الكائنية مع معالجة شاملة للأخطاء
"""

import logging
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from decimal import Decimal

from models.warehouse import Warehouse, WarehouseInventory
from models.product import Product
from utils.datetime_utils import get_tripoli_now

logger = logging.getLogger(__name__)


class WarehouseInventoryService:
    """
    خدمة مخزون المستودعات
    تطبق مبادئ البرمجة الكائنية مع نمط Singleton
    """
    
    _instance: Optional['WarehouseInventoryService'] = None
    
    def __init__(self, db_session: Session):
        """تهيئة خدمة مخزون المستودعات"""
        self.db_session = db_session
        logger.info("تم تهيئة خدمة مخزون المستودعات بنجاح")
    
    @classmethod  # type: ignore
    def get_instance(cls, db_session: Session) -> 'WarehouseInventoryService':
        """الحصول على instance وحيد من الخدمة"""
        if cls._instance is None:
            cls._instance = cls(db_session)
        elif db_session and cls._instance.db_session != db_session:
            cls._instance.db_session = db_session
        return cls._instance
    
    def get_product_inventory_by_warehouse(self, product_id: int) -> Dict[str, Any]:
        """الحصول على مخزون المنتج في جميع المستودعات"""
        try:
            # التحقق من وجود المنتج
            product = self.db_session.query(Product).filter(
                Product.id == product_id
            ).first()
            
            if not product:
                return {
                    'success': False,
                    'error': 'المنتج غير موجود'
                }
            
            # جلب مخزون المنتج في جميع المستودعات
            inventory_query = self.db_session.query(
                WarehouseInventory,
                Warehouse.name.label('warehouse_name'),
                Warehouse.code.label('warehouse_code'),
                Warehouse.is_main.label('is_main_warehouse')
            ).join(
                Warehouse, WarehouseInventory.warehouse_id == Warehouse.id
            ).filter(
                and_(
                    WarehouseInventory.product_id == product_id,
                    Warehouse.is_active == True
                )
            ).order_by(
                Warehouse.is_main.desc(),
                Warehouse.name
            )
            
            inventory_data = []
            total_quantity = 0
            total_reserved = 0
            
            for inventory, warehouse_name, warehouse_code, is_main in inventory_query:
                quantity = float(inventory.quantity)
                reserved_quantity = float(inventory.reserved_quantity)
                available_quantity = quantity - reserved_quantity
                
                total_quantity += quantity
                total_reserved += reserved_quantity
                
                inventory_data.append({
                    'warehouse_id': inventory.warehouse_id,
                    'warehouse_name': warehouse_name,
                    'warehouse_code': warehouse_code,
                    'is_main_warehouse': is_main,
                    'quantity': quantity,
                    'reserved_quantity': reserved_quantity,
                    'available_quantity': available_quantity,
                    'min_stock_level': float(inventory.min_stock_level),
                    'max_stock_level': float(inventory.max_stock_level) if inventory.max_stock_level else None,
                    'location_code': inventory.location_code,
                    'last_updated': inventory.last_updated.isoformat() if inventory.last_updated else None,
                    'stock_status': self._get_stock_status(available_quantity, inventory.min_stock_level)
                })
            
            return {
                'success': True,
                'product': {
                    'id': product.id,
                    'name': product.name,
                    'barcode': product.barcode
                },
                'inventory': inventory_data,
                'summary': {
                    'total_quantity': total_quantity,
                    'total_reserved': total_reserved,
                    'total_available': total_quantity - total_reserved,
                    'warehouses_count': len(inventory_data)
                }
            }
            
        except Exception as e:
            logger.error(f"خطأ في جلب مخزون المنتج: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب مخزون المنتج: {str(e)}'
            }
    
    def get_warehouse_inventory(self, warehouse_id: int, filters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """الحصول على مخزون المستودع"""
        try:
            # التحقق من وجود المستودع
            warehouse = self.db_session.query(Warehouse).filter(
                Warehouse.id == warehouse_id
            ).first()
            
            if not warehouse:
                return {
                    'success': False,
                    'error': 'المستودع غير موجود'
                }
            
            # بناء الاستعلام
            query = self.db_session.query(
                WarehouseInventory,
                Product.name.label('product_name'),
                Product.barcode.label('product_barcode'),
                Product.price.label('product_price')
            ).join(
                Product, WarehouseInventory.product_id == Product.id
            ).filter(
                WarehouseInventory.warehouse_id == warehouse_id
            )
            
            # تطبيق الفلاتر
            if filters:
                if filters.get('low_stock_only'):
                    query = query.filter(
                        WarehouseInventory.quantity <= WarehouseInventory.min_stock_level
                    )
                
                if filters.get('search'):
                    search_term = f"%{filters['search']}%"
                    query = query.filter(
                        or_(
                            Product.name.ilike(search_term),
                            Product.barcode.ilike(search_term)
                        )
                    )
                
                if filters.get('min_quantity') is not None:
                    query = query.filter(
                        WarehouseInventory.quantity >= filters['min_quantity']
                    )
                
                if filters.get('max_quantity') is not None:
                    query = query.filter(
                        WarehouseInventory.quantity <= filters['max_quantity']
                    )
            
            # ترتيب النتائج
            query = query.order_by(Product.name)
            
            inventory_items = []
            total_value = 0
            low_stock_count = 0
            
            for inventory, product_name, product_barcode, product_price in query:
                quantity = float(inventory.quantity)
                reserved_quantity = float(inventory.reserved_quantity)
                available_quantity = quantity - reserved_quantity
                item_value = quantity * float(product_price)
                
                total_value += item_value
                
                stock_status = self._get_stock_status(available_quantity, inventory.min_stock_level)
                if stock_status in ['low', 'out_of_stock']:
                    low_stock_count += 1
                
                inventory_items.append({
                    'id': inventory.id,
                    'product_id': inventory.product_id,
                    'product_name': product_name,
                    'product_barcode': product_barcode,
                    'product_price': float(product_price),
                    'quantity': quantity,
                    'reserved_quantity': reserved_quantity,
                    'available_quantity': available_quantity,
                    'min_stock_level': float(inventory.min_stock_level),
                    'max_stock_level': float(inventory.max_stock_level) if inventory.max_stock_level else None,
                    'location_code': inventory.location_code,
                    'item_value': item_value,
                    'stock_status': stock_status,
                    'last_updated': inventory.last_updated.isoformat() if inventory.last_updated else None
                })
            
            return {
                'success': True,
                'warehouse': {
                    'id': warehouse.id,
                    'name': warehouse.name,
                    'code': warehouse.code
                },
                'inventory': inventory_items,
                'summary': {
                    'total_items': len(inventory_items),
                    'total_value': round(total_value, 2),
                    'low_stock_count': low_stock_count,
                    'normal_stock_count': len(inventory_items) - low_stock_count
                }
            }
            
        except Exception as e:
            logger.error(f"خطأ في جلب مخزون المستودع: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب مخزون المستودع: {str(e)}'
            }
    
    def update_stock_levels(self, warehouse_id: int, product_id: int, data: Dict[str, Any]) -> Dict[str, Any]:
        """تحديث مستويات المخزون"""
        try:
            # البحث عن سجل المخزون أو إنشاؤه
            inventory = self.db_session.query(WarehouseInventory).filter(
                and_(
                    WarehouseInventory.warehouse_id == warehouse_id,
                    WarehouseInventory.product_id == product_id
                )
            ).first()
            
            if not inventory:
                # إنشاء سجل مخزون جديد
                inventory = WarehouseInventory(
                    warehouse_id=warehouse_id,
                    product_id=product_id,
                    quantity=data.get('quantity', 0),
                    reserved_quantity=data.get('reserved_quantity', 0),
                    min_stock_level=data.get('min_stock_level', 0),
                    max_stock_level=data.get('max_stock_level'),
                    location_code=data.get('location_code')
                )
                self.db_session.add(inventory)
            else:
                # تحديث السجل الموجود
                for key, value in data.items():
                    if hasattr(inventory, key) and value is not None:
                        setattr(inventory, key, value)
                
                inventory.last_updated = get_tripoli_now()
            
            self.db_session.commit()
            self.db_session.refresh(inventory)
            
            logger.info(f"تم تحديث مستويات المخزون للمنتج {product_id} في المستودع {warehouse_id}")
            
            return {
                'success': True,
                'inventory': {
                    'id': inventory.id,
                    'warehouse_id': inventory.warehouse_id,
                    'product_id': inventory.product_id,
                    'quantity': float(inventory.quantity),
                    'reserved_quantity': float(inventory.reserved_quantity),
                    'min_stock_level': float(inventory.min_stock_level),
                    'max_stock_level': float(inventory.max_stock_level) if inventory.max_stock_level is not None else None,
                    'location_code': inventory.location_code,
                    'last_updated': inventory.last_updated.isoformat() if inventory.last_updated is not None else None
                }
            }
            
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"خطأ في تحديث مستويات المخزون: {e}")
            return {
                'success': False,
                'error': f'خطأ في تحديث مستويات المخزون: {str(e)}'
            }
    
    def check_stock_availability(self, warehouse_id: int, product_id: int, quantity: float) -> Dict[str, Any]:
        """التحقق من توفر المخزون"""
        try:
            inventory = self.db_session.query(WarehouseInventory).filter(
                and_(
                    WarehouseInventory.warehouse_id == warehouse_id,
                    WarehouseInventory.product_id == product_id
                )
            ).first()

            if not inventory:
                return {
                    'success': True,
                    'available': False,
                    'current_quantity': 0,
                    'available_quantity': 0,
                    'requested_quantity': quantity,
                    'message': 'المنتج غير موجود في هذا المستودع'
                }

            available_quantity = float(inventory.quantity) - float(inventory.reserved_quantity)
            is_available = available_quantity >= quantity

            return {
                'success': True,
                'available': is_available,
                'current_quantity': float(inventory.quantity),
                'reserved_quantity': float(inventory.reserved_quantity),
                'available_quantity': available_quantity,
                'requested_quantity': quantity,
                'shortage': max(0, quantity - available_quantity),
                'message': 'متوفر' if is_available else f'غير متوفر - نقص {quantity - available_quantity}'
            }

        except Exception as e:
            logger.error(f"خطأ في التحقق من توفر المخزون: {e}")
            return {
                'success': False,
                'error': f'خطأ في التحقق من توفر المخزون: {str(e)}'
            }

    def get_low_stock_items(self, warehouse_id: int) -> Dict[str, Any]:
        """الحصول على المنتجات قليلة المخزون"""
        try:
            query = self.db_session.query(
                WarehouseInventory,
                Product.name.label('product_name'),
                Product.barcode.label('product_barcode')
            ).join(
                Product, WarehouseInventory.product_id == Product.id
            ).filter(
                and_(
                    WarehouseInventory.warehouse_id == warehouse_id,
                    WarehouseInventory.quantity <= WarehouseInventory.min_stock_level,
                    WarehouseInventory.min_stock_level > 0
                )
            ).order_by(
                (WarehouseInventory.quantity - WarehouseInventory.min_stock_level).asc()
            )

            low_stock_items = []
            for inventory, product_name, product_barcode in query:
                available_quantity = float(inventory.quantity) - float(inventory.reserved_quantity)
                shortage = float(inventory.min_stock_level) - available_quantity

                low_stock_items.append({
                    'product_id': inventory.product_id,
                    'product_name': product_name,
                    'product_barcode': product_barcode,
                    'current_quantity': float(inventory.quantity),
                    'reserved_quantity': float(inventory.reserved_quantity),
                    'available_quantity': available_quantity,
                    'min_stock_level': float(inventory.min_stock_level),
                    'shortage': max(0, shortage),
                    'location_code': inventory.location_code,
                    'last_updated': inventory.last_updated.isoformat() if inventory.last_updated else None
                })

            return {
                'success': True,
                'low_stock_items': low_stock_items,
                'total_count': len(low_stock_items)
            }

        except Exception as e:
            logger.error(f"خطأ في جلب المنتجات قليلة المخزون: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب المنتجات قليلة المخزون: {str(e)}'
            }

    def reserve_stock(self, warehouse_id: int, product_id: int, quantity: float) -> Dict[str, Any]:
        """حجز المخزون"""
        try:
            inventory = self.db_session.query(WarehouseInventory).filter(
                and_(
                    WarehouseInventory.warehouse_id == warehouse_id,
                    WarehouseInventory.product_id == product_id
                )
            ).first()

            if not inventory:
                return {
                    'success': False,
                    'error': 'المنتج غير موجود في هذا المستودع'
                }

            available_quantity = float(inventory.quantity) - float(inventory.reserved_quantity)

            if available_quantity < quantity:
                return {
                    'success': False,
                    'error': f'الكمية المتاحة ({available_quantity}) أقل من المطلوب ({quantity})'
                }

            # حجز الكمية
            inventory.reserved_quantity = float(inventory.reserved_quantity) + quantity
            inventory.last_updated = get_tripoli_now()

            self.db_session.commit()

            logger.info(f"تم حجز {quantity} من المنتج {product_id} في المستودع {warehouse_id}")

            return {
                'success': True,
                'message': f'تم حجز {quantity} بنجاح',
                'reserved_quantity': float(inventory.reserved_quantity),
                'available_quantity': float(inventory.quantity) - float(inventory.reserved_quantity)
            }

        except Exception as e:
            self.db_session.rollback()
            logger.error(f"خطأ في حجز المخزون: {e}")
            return {
                'success': False,
                'error': f'خطأ في حجز المخزون: {str(e)}'
            }

    def release_reserved_stock(self, warehouse_id: int, product_id: int, quantity: float) -> Dict[str, Any]:
        """إلغاء حجز المخزون"""
        try:
            inventory = self.db_session.query(WarehouseInventory).filter(
                and_(
                    WarehouseInventory.warehouse_id == warehouse_id,
                    WarehouseInventory.product_id == product_id
                )
            ).first()

            if not inventory:
                return {
                    'success': False,
                    'error': 'المنتج غير موجود في هذا المستودع'
                }

            if float(inventory.reserved_quantity) < quantity:
                return {
                    'success': False,
                    'error': f'الكمية المحجوزة ({inventory.reserved_quantity}) أقل من المطلوب إلغاؤها ({quantity})'
                }

            # إلغاء حجز الكمية
            inventory.reserved_quantity = float(inventory.reserved_quantity) - quantity
            inventory.last_updated = get_tripoli_now()

            self.db_session.commit()

            logger.info(f"تم إلغاء حجز {quantity} من المنتج {product_id} في المستودع {warehouse_id}")

            return {
                'success': True,
                'message': f'تم إلغاء حجز {quantity} بنجاح',
                'reserved_quantity': float(inventory.reserved_quantity),
                'available_quantity': float(inventory.quantity) - float(inventory.reserved_quantity)
            }

        except Exception as e:
            self.db_session.rollback()
            logger.error(f"خطأ في إلغاء حجز المخزون: {e}")
            return {
                'success': False,
                'error': f'خطأ في إلغاء حجز المخزون: {str(e)}'
            }

    def _get_stock_status(self, available_quantity: float, min_stock_level: float) -> str:
        """تحديد حالة المخزون"""
        if available_quantity <= 0:
            return 'out_of_stock'
        elif available_quantity <= min_stock_level:
            return 'low'
        else:
            return 'normal'
