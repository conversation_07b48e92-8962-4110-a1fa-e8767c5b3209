"""
خدمة إدارة الباركود
تطبق مبادئ البرمجة الكائنية مع نمط Singleton
"""

import random
import string
import re
from typing import Dict, Any, Optional, List
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import text

from models.product import Product
from utils.datetime_utils import get_tripoli_now
import logging

logger = logging.getLogger(__name__)


class BarcodeService:
    """
    خدمة إدارة الباركود المتقدمة
    تطبق مبادئ البرمجة الكائنية مع نمط Singleton
    """
    
    _instance: Optional['BarcodeService'] = None
    
    # أنواع الباركود المدعومة
    SUPPORTED_SYMBOLOGIES = {
        'CODE128': {
            'name': 'Code 128',
            'min_length': 1,
            'max_length': 48,
            'pattern': r'^[\x00-\x7F]+$',  # ASCII characters
            'description': 'باركود متعدد الاستخدامات يدعم جميع الأحرف'
        },
        'UPC_A': {
            'name': 'UPC-A',
            'min_length': 12,
            'max_length': 12,
            'pattern': r'^\d{12}$',
            'description': 'باركود أمريكي للمنتجات الاستهلاكية'
        },
        'EAN_13': {
            'name': 'EAN-13',
            'min_length': 13,
            'max_length': 13,
            'pattern': r'^\d{13}$',
            'description': 'باركود أوروبي للمنتجات'
        },
        'EAN_8': {
            'name': 'EAN-8',
            'min_length': 8,
            'max_length': 8,
            'pattern': r'^\d{8}$',
            'description': 'باركود أوروبي مختصر'
        },
        'CODE39': {
            'name': 'Code 39',
            'min_length': 1,
            'max_length': 43,
            'pattern': r'^[A-Z0-9\-\.\$\/\+\%\s]+$',
            'description': 'باركود يدعم الأحرف والأرقام'
        },
        'CODE93': {
            'name': 'Code 93',
            'min_length': 1,
            'max_length': 47,
            'pattern': r'^[A-Z0-9\-\.\$\/\+\%\s]+$',
            'description': 'باركود محسن من Code 39'
        },
        'CODABAR': {
            'name': 'Codabar',
            'min_length': 3,
            'max_length': 16,
            'pattern': r'^[A-D][0-9\-\$\:\.\+\/]+[A-D]$',
            'description': 'باركود للمكتبات والبنوك'
        }
    }
    
    def __init__(self, db_session: Optional[Session] = None):
        """تهيئة خدمة الباركود"""
        self.db_session = db_session
        logger.info("تم تهيئة خدمة الباركود بنجاح")
    
    @classmethod
    def get_instance(cls, db_session: Optional[Session] = None) -> 'BarcodeService':
        """الحصول على مثيل وحيد من الخدمة"""
        if cls._instance is None:
            cls._instance = cls(db_session)
        elif db_session and cls._instance.db_session != db_session:
            cls._instance.db_session = db_session
        return cls._instance
    
    def generate_sku(self, prefix: str = "SKU") -> str:
        """توليد SKU فريد"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            random_part = ''.join(random.choices(string.digits, k=4))
            sku = f"{prefix}{timestamp}{random_part}"
            
            logger.info(f"تم توليد SKU جديد: {sku}")
            return sku
            
        except Exception as e:
            logger.error(f"خطأ في توليد SKU: {e}")
            # Fallback to simple generation
            return f"{prefix}{int(datetime.now().timestamp())}{random.randint(1000, 9999)}"
    
    def generate_barcode(self, symbology: str = "CODE128", length: Optional[int] = None) -> str:
        """توليد باركود حسب النوع المحدد"""
        try:
            if symbology not in self.SUPPORTED_SYMBOLOGIES:
                raise ValueError(f"نوع الباركود غير مدعوم: {symbology}")
            
            symbology_info = self.SUPPORTED_SYMBOLOGIES[symbology]
            
            # تحديد الطول
            if length is None:
                if symbology in ['UPC_A', 'EAN_13', 'EAN_8']:
                    length = symbology_info['max_length']
                else:
                    length = min(12, symbology_info['max_length'])
            
            # التحقق من صحة الطول
            if length < symbology_info['min_length'] or length > symbology_info['max_length']:
                raise ValueError(f"طول الباركود يجب أن يكون بين {symbology_info['min_length']} و {symbology_info['max_length']}")
            
            # التأكد من أن length لديها قيمة صالحة
            assert length is not None and isinstance(length, int)
            
            # توليد الباركود حسب النوع
            if symbology in ['UPC_A', 'EAN_13', 'EAN_8']:
                barcode = self._generate_numeric_barcode(length)
            elif symbology in ['CODE39', 'CODE93']:
                barcode = self._generate_alphanumeric_barcode(length)
            elif symbology == 'CODABAR':
                barcode = self._generate_codabar_barcode(length)
            else:  # CODE128
                barcode = self._generate_code128_barcode(length)
            
            logger.info(f"تم توليد باركود جديد: {barcode} (نوع: {symbology})")
            return barcode
            
        except Exception as e:
            logger.error(f"خطأ في توليد الباركود: {e}")
            # Fallback to simple numeric barcode
            return str(int(datetime.now().timestamp() * 1000))[-12:]
    
    def _generate_numeric_barcode(self, length: int) -> str:
        """توليد باركود رقمي"""
        # تجنب البدء بصفر
        first_digit = random.randint(1, 9)
        remaining_digits = ''.join(random.choices(string.digits, k=length-1))
        return str(first_digit) + remaining_digits
    
    def _generate_alphanumeric_barcode(self, length: int) -> str:
        """توليد باركود أبجدي رقمي"""
        chars = string.ascii_uppercase + string.digits + '-.$+% '
        return ''.join(random.choices(chars, k=length))
    
    def _generate_codabar_barcode(self, length: int) -> str:
        """توليد باركود Codabar"""
        start_stop = random.choice(['A', 'B', 'C', 'D'])
        middle_chars = string.digits + '-$:.+/'
        middle = ''.join(random.choices(middle_chars, k=length-2))
        return start_stop + middle + start_stop
    
    def _generate_code128_barcode(self, length: int) -> str:
        """توليد باركود Code 128"""
        # استخدام أحرف ASCII قابلة للطباعة
        chars = string.ascii_letters + string.digits + ' .-'
        return ''.join(random.choices(chars, k=length))
    
    def validate_barcode(self, barcode: str, symbology: str) -> Dict[str, Any]:
        """التحقق من صحة الباركود"""
        try:
            if symbology not in self.SUPPORTED_SYMBOLOGIES:
                return {
                    'valid': False,
                    'error': f'نوع الباركود غير مدعوم: {symbology}'
                }
            
            symbology_info = self.SUPPORTED_SYMBOLOGIES[symbology]
            
            # التحقق من الطول
            if len(barcode) < symbology_info['min_length'] or len(barcode) > symbology_info['max_length']:
                return {
                    'valid': False,
                    'error': f'طول الباركود يجب أن يكون بين {symbology_info["min_length"]} و {symbology_info["max_length"]}'
                }
            
            # التحقق من النمط
            if not re.match(symbology_info['pattern'], barcode):
                return {
                    'valid': False,
                    'error': f'تنسيق الباركود غير صحيح لنوع {symbology_info["name"]}'
                }
            
            return {
                'valid': True,
                'symbology': symbology,
                'symbology_name': symbology_info['name'],
                'length': len(barcode)
            }
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من الباركود: {e}")
            return {
                'valid': False,
                'error': f'خطأ في التحقق من الباركود: {str(e)}'
            }
    
    def check_barcode_uniqueness(self, barcode: str, exclude_product_id: Optional[int] = None) -> Dict[str, Any]:
        """التحقق من تفرد الباركود"""
        try:
            if not self.db_session:
                return {
                    'unique': False,
                    'error': 'جلسة قاعدة البيانات غير متوفرة'
                }
            
            query = self.db_session.query(Product).filter(Product.barcode == barcode)
            
            if exclude_product_id:
                query = query.filter(Product.id != exclude_product_id)
            
            existing_product = query.first()
            
            if existing_product:
                return {
                    'unique': False,
                    'error': f'الباركود مستخدم بالفعل في المنتج: {existing_product.name}',
                    'existing_product': {
                        'id': existing_product.id,
                        'name': existing_product.name
                    }
                }
            
            return {
                'unique': True,
                'message': 'الباركود متاح للاستخدام'
            }
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من تفرد الباركود: {e}")
            return {
                'unique': False,
                'error': f'خطأ في التحقق من تفرد الباركود: {str(e)}'
            }
    
    def get_supported_symbologies(self) -> List[Dict[str, Any]]:
        """الحصول على قائمة أنواع الباركود المدعومة"""
        try:
            symbologies = []
            for key, info in self.SUPPORTED_SYMBOLOGIES.items():
                symbologies.append({
                    'value': key,
                    'label': info['name'],
                    'description': info['description'],
                    'min_length': info['min_length'],
                    'max_length': info['max_length']
                })
            
            return symbologies
            
        except Exception as e:
            logger.error(f"خطأ في جلب أنواع الباركود: {e}")
            return []
    
    def generate_unique_barcode(self, symbology: str = "CODE128", max_attempts: int = 10) -> Dict[str, Any]:
        """توليد باركود فريد مع التحقق من التفرد"""
        try:
            for attempt in range(max_attempts):
                barcode = self.generate_barcode(symbology)
                uniqueness_check = self.check_barcode_uniqueness(barcode)
                
                if uniqueness_check.get('unique', False):
                    return {
                        'success': True,
                        'barcode': barcode,
                        'symbology': symbology,
                        'attempts': attempt + 1
                    }
            
            return {
                'success': False,
                'error': f'فشل في توليد باركود فريد بعد {max_attempts} محاولات'
            }
            
        except Exception as e:
            logger.error(f"خطأ في توليد باركود فريد: {e}")
            return {
                'success': False,
                'error': f'خطأ في توليد باركود فريد: {str(e)}'
            }
    
    def get_barcode_info(self, barcode: str) -> Dict[str, Any]:
        """الحصول على معلومات الباركود"""
        try:
            # محاولة تحديد نوع الباركود تلقائياً
            detected_symbology = None
            for symbology, info in self.SUPPORTED_SYMBOLOGIES.items():
                if (len(barcode) >= info['min_length'] and 
                    len(barcode) <= info['max_length'] and 
                    re.match(info['pattern'], barcode)):
                    detected_symbology = symbology
                    break
            
            if not detected_symbology:
                return {
                    'valid': False,
                    'error': 'لا يمكن تحديد نوع الباركود'
                }
            
            # التحقق من التفرد
            uniqueness_check = self.check_barcode_uniqueness(barcode)
            
            return {
                'valid': True,
                'barcode': barcode,
                'detected_symbology': detected_symbology,
                'symbology_name': self.SUPPORTED_SYMBOLOGIES[detected_symbology]['name'],
                'length': len(barcode),
                'unique': uniqueness_check.get('unique', False),
                'uniqueness_info': uniqueness_check
            }
            
        except Exception as e:
            logger.error(f"خطأ في جلب معلومات الباركود: {e}")
            return {
                'valid': False,
                'error': f'خطأ في جلب معلومات الباركود: {str(e)}'
            }
