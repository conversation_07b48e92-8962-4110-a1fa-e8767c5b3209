"""
خدمة إدارة قيم الضرائب - Tax Rate Service
تتعامل مع جميع العمليات المتعلقة بقيم الضرائب
"""

import logging
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import or_, func, and_
from datetime import date
from decimal import Decimal

from models.user import User
from models.tax_type import TaxType
from models.tax_rate import TaxRate
from schemas.tax import (
    TaxRateCreate,
    TaxRateUpdate,
    TaxRateFilters
)

logger = logging.getLogger(__name__)


class TaxRateService:
    """
    خدمة إدارة قيم الضرائب
    تتعامل مع جميع العمليات المتعلقة بقيم الضرائب
    """

    def __init__(self, db: Session, current_user: User):
        """
        تهيئة خدمة قيم الضرائب

        Args:
            db: جلسة قاعدة البيانات
            current_user: المستخدم الحالي
        """
        self.db = db
        self.current_user = current_user
        self.is_admin = current_user.role.name == "ADMIN"

        logger.info(f"تم تهيئة خدمة قيم الضرائب للمستخدم: {current_user.username}")

    def get_tax_rates(self, filters: Optional[TaxRateFilters] = None) -> List[TaxRate]:
        """
        جلب قيم الضرائب مع الفلاتر

        Args:
            filters: فلاتر البحث والتصفية

        Returns:
            قائمة بقيم الضرائب
        """
        try:
            logger.info("🔄 جلب قيم الضرائب...")

            query = self.db.query(TaxRate).join(TaxType)

            # تطبيق الفلاتر
            if filters:
                if filters.search:
                    search_term = f"%{filters.search}%"
                    query = query.filter(
                        or_(
                            TaxRate.name.ilike(search_term),
                            TaxRate.description.ilike(search_term),
                            TaxRate.tax_code.ilike(search_term),
                            TaxType.name_ar.ilike(search_term)
                        )
                    )

                if filters.status and filters.status != 'all':
                    is_active = filters.status == 'active'
                    query = query.filter(TaxRate.is_active == is_active)

                if filters.tax_type_id:
                    query = query.filter(TaxRate.tax_type_id == filters.tax_type_id)

                if filters.applies_to and filters.applies_to != 'all':
                    query = query.filter(TaxRate.applies_to == filters.applies_to)

                if filters.is_default is not None:
                    query = query.filter(TaxRate.is_default == filters.is_default)

                if filters.effective_only:
                    today = date.today()
                    query = query.filter(
                        and_(
                            or_(TaxRate.effective_from.is_(None), TaxRate.effective_from <= today),
                            or_(TaxRate.effective_to.is_(None), TaxRate.effective_to >= today)
                        )
                    )

            # ترتيب النتائج
            query = query.order_by(TaxRate.sort_order.asc(), TaxRate.name.asc())

            tax_rates = query.all()

            logger.info(f"✅ تم جلب {len(tax_rates)} قيمة ضريبية")

            return tax_rates

        except Exception as e:
            logger.error(f"❌ خطأ في جلب قيم الضرائب: {str(e)}")
            raise

    def get_tax_rate_by_id(self, tax_rate_id: int) -> Optional[TaxRate]:
        """
        جلب قيمة ضريبية بالمعرف

        Args:
            tax_rate_id: معرف القيمة الضريبية

        Returns:
            القيمة الضريبية أو None
        """
        try:
            logger.info(f"🔄 جلب القيمة الضريبية: {tax_rate_id}")

            tax_rate = self.db.query(TaxRate).filter(TaxRate.id == tax_rate_id).first()

            if tax_rate:
                logger.info(f"✅ تم جلب القيمة الضريبية: {tax_rate.name}")
            else:
                logger.warning(f"⚠️ القيمة الضريبية غير موجودة: {tax_rate_id}")

            return tax_rate

        except Exception as e:
            logger.error(f"❌ خطأ في جلب القيمة الضريبية: {str(e)}")
            raise

    def create_tax_rate(self, tax_rate_data: TaxRateCreate) -> TaxRate:
        """
        إنشاء قيمة ضريبية جديدة

        Args:
            tax_rate_data: بيانات القيمة الضريبية الجديدة

        Returns:
            القيمة الضريبية المنشأة
        """
        try:
            logger.info(f"🔄 إنشاء قيمة ضريبية جديدة: {tax_rate_data.name}")

            # التحقق من وجود نوع الضريبة
            tax_type = self.db.query(TaxType).filter(TaxType.id == tax_rate_data.tax_type_id).first()
            if not tax_type:
                raise ValueError(f"نوع الضريبة غير موجود: {tax_rate_data.tax_type_id}")

            # التحقق من عدم وجود قيمة ضريبية بنفس الاسم لنفس النوع
            existing_rate = self.db.query(TaxRate).filter(
                and_(
                    TaxRate.tax_type_id == tax_rate_data.tax_type_id,
                    TaxRate.name == tax_rate_data.name
                )
            ).first()

            if existing_rate:
                raise ValueError(f"قيمة ضريبية بنفس الاسم موجودة بالفعل لهذا النوع: {tax_rate_data.name}")

            # إذا كانت القيمة افتراضية، إلغاء الافتراضية من القيم الأخرى
            if tax_rate_data.is_default:
                self.db.query(TaxRate).filter(
                    and_(
                        TaxRate.tax_type_id == tax_rate_data.tax_type_id,
                        TaxRate.is_default == True
                    )
                ).update({'is_default': False})

            # إنشاء القيمة الضريبية الجديدة
            tax_rate = TaxRate(
                **tax_rate_data.model_dump(),
                created_by=self.current_user.id
            )

            self.db.add(tax_rate)
            self.db.commit()
            self.db.refresh(tax_rate)

            logger.info(f"✅ تم إنشاء القيمة الضريبية بنجاح: {tax_rate.name} (ID: {tax_rate.id})")

            return tax_rate

        except Exception as e:
            self.db.rollback()
            logger.error(f"❌ خطأ في إنشاء القيمة الضريبية: {str(e)}")
            raise

    def update_tax_rate(self, tax_rate_id: int, tax_rate_data: TaxRateUpdate) -> TaxRate:
        """
        تحديث قيمة ضريبية موجودة

        Args:
            tax_rate_id: معرف القيمة الضريبية
            tax_rate_data: بيانات التحديث

        Returns:
            القيمة الضريبية المحدثة
        """
        try:
            logger.info(f"🔄 تحديث القيمة الضريبية: {tax_rate_id}")

            # جلب القيمة الضريبية
            tax_rate = self.get_tax_rate_by_id(tax_rate_id)
            if not tax_rate:
                raise ValueError(f"القيمة الضريبية غير موجودة: {tax_rate_id}")

            # التحقق من عدم تكرار الاسم (إذا تم تغييره)
            update_data = tax_rate_data.model_dump(exclude_unset=True)
            if 'name' in update_data:
                existing_rate = self.db.query(TaxRate).filter(
                    and_(
                        TaxRate.id != tax_rate_id,
                        TaxRate.tax_type_id == tax_rate.tax_type_id,
                        TaxRate.name == update_data['name']
                    )
                ).first()

                if existing_rate:
                    raise ValueError(f"قيمة ضريبية بنفس الاسم موجودة بالفعل لهذا النوع")

            # إذا كانت القيمة ستصبح افتراضية، إلغاء الافتراضية من القيم الأخرى
            if update_data.get('is_default', False):
                self.db.query(TaxRate).filter(
                    and_(
                        TaxRate.tax_type_id == tax_rate.tax_type_id,
                        TaxRate.id != tax_rate_id,
                        TaxRate.is_default == True
                    )
                ).update({'is_default': False})

            # تطبيق التحديثات
            for field, value in update_data.items():
                setattr(tax_rate, field, value)

            tax_rate.updated_by = self.current_user.id

            self.db.commit()
            self.db.refresh(tax_rate)

            logger.info(f"✅ تم تحديث القيمة الضريبية بنجاح: {tax_rate.name}")

            return tax_rate

        except Exception as e:
            self.db.rollback()
            logger.error(f"❌ خطأ في تحديث القيمة الضريبية: {str(e)}")
            raise

    def delete_tax_rate(self, tax_rate_id: int) -> bool:
        """
        حذف قيمة ضريبية

        Args:
            tax_rate_id: معرف القيمة الضريبية

        Returns:
            True إذا تم الحذف بنجاح
        """
        try:
            logger.info(f"🔄 حذف القيمة الضريبية: {tax_rate_id}")

            # جلب القيمة الضريبية
            tax_rate = self.get_tax_rate_by_id(tax_rate_id)
            if not tax_rate:
                raise ValueError(f"القيمة الضريبية غير موجودة: {tax_rate_id}")

            # TODO: التحقق من وجود منتجات أو مبيعات مرتبطة بهذه القيمة الضريبية
            # يمكن إضافة هذا التحقق لاحقاً عند ربط النظام بالمنتجات والمبيعات

            # حذف القيمة الضريبية
            self.db.delete(tax_rate)
            self.db.commit()

            logger.info(f"✅ تم حذف القيمة الضريبية بنجاح: {tax_rate.name}")

            return True

        except Exception as e:
            self.db.rollback()
            logger.error(f"❌ خطأ في حذف القيمة الضريبية: {str(e)}")
            raise

    def calculate_tax(self, tax_rate_id: int, base_amount: Decimal) -> Dict[str, Any]:
        """
        حساب الضريبة على أساس القيمة الضريبية والمبلغ الأساسي

        Args:
            tax_rate_id: معرف القيمة الضريبية
            base_amount: المبلغ الأساسي

        Returns:
            نتيجة حساب الضريبة
        """
        try:
            logger.info(f"🔄 حساب الضريبة للقيمة: {tax_rate_id} والمبلغ: {base_amount}")

            # جلب القيمة الضريبية
            tax_rate = self.get_tax_rate_by_id(tax_rate_id)
            if not tax_rate:
                raise ValueError(f"القيمة الضريبية غير موجودة: {tax_rate_id}")

            # حساب مبلغ الضريبة
            tax_amount = tax_rate.calculate_tax_amount(base_amount)
            total_amount = base_amount + tax_amount

            result = {
                'base_amount': float(base_amount),
                'tax_amount': float(tax_amount),
                'total_amount': float(total_amount),
                'tax_rate': tax_rate.to_dict()
            }

            logger.info(f"✅ تم حساب الضريبة: {result}")

            return result

        except Exception as e:
            logger.error(f"❌ خطأ في حساب الضريبة: {str(e)}")
            raise
