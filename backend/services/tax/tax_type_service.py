"""
خدمة إدارة أنواع الضرائب - Tax Type Service
تتعامل مع جميع العمليات المتعلقة بأنواع الضرائب
"""

import logging
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import or_, func, and_

from models.user import User
from models.tax_type import TaxType
from models.tax_rate import TaxRate
from schemas.tax import (
    TaxTypeCreate,
    TaxTypeUpdate,
    TaxTypeFilters
)

logger = logging.getLogger(__name__)


class TaxTypeService:
    """
    خدمة إدارة أنواع الضرائب
    تتعامل مع جميع العمليات المتعلقة بأنواع الضرائب
    """

    def __init__(self, db: Session, current_user: User):
        """
        تهيئة خدمة أنواع الضرائب

        Args:
            db: جلسة قاعدة البيانات
            current_user: المستخدم الحالي
        """
        self.db = db
        self.current_user = current_user
        self.is_admin = current_user.role.name == "ADMIN"

        logger.info(f"تم تهيئة خدمة أنواع الضرائب للمستخدم: {current_user.username}")

    def get_tax_types(self, filters: Optional[TaxTypeFilters] = None) -> List[TaxType]:
        """
        جلب أنواع الضرائب مع الفلاتر

        Args:
            filters: فلاتر البحث والتصفية

        Returns:
            قائمة بأنواع الضرائب
        """
        try:
            logger.info("🔄 جلب أنواع الضرائب...")

            query = self.db.query(TaxType)

            # تطبيق الفلاتر
            if filters:
                if filters.search:
                    search_term = f"%{filters.search}%"
                    query = query.filter(
                        or_(
                            TaxType.name.ilike(search_term),
                            TaxType.name_ar.ilike(search_term),
                            TaxType.description.ilike(search_term)
                        )
                    )

                if filters.status and filters.status != 'all':
                    is_active = filters.status == 'active'
                    query = query.filter(TaxType.is_active == is_active)

                if filters.tax_category and filters.tax_category != 'all':
                    query = query.filter(TaxType.tax_category == filters.tax_category)

                if filters.calculation_method and filters.calculation_method != 'all':
                    query = query.filter(TaxType.calculation_method == filters.calculation_method)

                if filters.is_compound is not None:
                    query = query.filter(TaxType.is_compound == filters.is_compound)

            # ترتيب النتائج
            query = query.order_by(TaxType.sort_order.asc(), TaxType.name_ar.asc())

            tax_types = query.all()

            logger.info(f"✅ تم جلب {len(tax_types)} نوع ضريبة")

            return tax_types

        except Exception as e:
            logger.error(f"❌ خطأ في جلب أنواع الضرائب: {str(e)}")
            raise

    def get_tax_type_by_id(self, tax_type_id: int) -> Optional[TaxType]:
        """
        جلب نوع ضريبة بالمعرف

        Args:
            tax_type_id: معرف نوع الضريبة

        Returns:
            نوع الضريبة أو None
        """
        try:
            logger.info(f"🔄 جلب نوع الضريبة: {tax_type_id}")

            tax_type = self.db.query(TaxType).filter(TaxType.id == tax_type_id).first()

            if tax_type:
                logger.info(f"✅ تم جلب نوع الضريبة: {tax_type.name_ar}")
            else:
                logger.warning(f"⚠️ نوع الضريبة غير موجود: {tax_type_id}")

            return tax_type

        except Exception as e:
            logger.error(f"❌ خطأ في جلب نوع الضريبة: {str(e)}")
            raise

    def create_tax_type(self, tax_type_data: TaxTypeCreate) -> TaxType:
        """
        إنشاء نوع ضريبة جديد

        Args:
            tax_type_data: بيانات نوع الضريبة الجديد

        Returns:
            نوع الضريبة المنشأ
        """
        try:
            logger.info(f"🔄 إنشاء نوع ضريبة جديد: {tax_type_data.name_ar}")

            # التحقق من عدم وجود نوع ضريبة بنفس الاسم
            existing_type = self.db.query(TaxType).filter(
                or_(
                    TaxType.name == tax_type_data.name,
                    TaxType.name_ar == tax_type_data.name_ar
                )
            ).first()

            if existing_type:
                raise ValueError(f"نوع ضريبة بنفس الاسم موجود بالفعل: {tax_type_data.name_ar}")

            # إنشاء نوع الضريبة الجديد
            tax_type = TaxType(
                **tax_type_data.model_dump(),
                created_by=self.current_user.id
            )

            self.db.add(tax_type)
            self.db.commit()
            self.db.refresh(tax_type)

            logger.info(f"✅ تم إنشاء نوع الضريبة بنجاح: {tax_type.name_ar} (ID: {tax_type.id})")

            return tax_type

        except Exception as e:
            self.db.rollback()
            logger.error(f"❌ خطأ في إنشاء نوع الضريبة: {str(e)}")
            raise

    def update_tax_type(self, tax_type_id: int, tax_type_data: TaxTypeUpdate) -> TaxType:
        """
        تحديث نوع ضريبة موجود

        Args:
            tax_type_id: معرف نوع الضريبة
            tax_type_data: بيانات التحديث

        Returns:
            نوع الضريبة المحدث
        """
        try:
            logger.info(f"🔄 تحديث نوع الضريبة: {tax_type_id}")

            # جلب نوع الضريبة
            tax_type = self.get_tax_type_by_id(tax_type_id)
            if not tax_type:
                raise ValueError(f"نوع الضريبة غير موجود: {tax_type_id}")

            # التحقق من عدم تكرار الاسم (إذا تم تغييره)
            update_data = tax_type_data.model_dump(exclude_unset=True)
            if 'name' in update_data or 'name_ar' in update_data:
                existing_type = self.db.query(TaxType).filter(
                    and_(
                        TaxType.id != tax_type_id,
                        or_(
                            TaxType.name == update_data.get('name', tax_type.name),
                            TaxType.name_ar == update_data.get('name_ar', tax_type.name_ar)
                        )
                    )
                ).first()

                if existing_type:
                    raise ValueError(f"نوع ضريبة بنفس الاسم موجود بالفعل")

            # تطبيق التحديثات
            for field, value in update_data.items():
                setattr(tax_type, field, value)

            tax_type.updated_by = self.current_user.id

            self.db.commit()
            self.db.refresh(tax_type)

            logger.info(f"✅ تم تحديث نوع الضريبة بنجاح: {tax_type.name_ar}")

            return tax_type

        except Exception as e:
            self.db.rollback()
            logger.error(f"❌ خطأ في تحديث نوع الضريبة: {str(e)}")
            raise

    def delete_tax_type(self, tax_type_id: int) -> bool:
        """
        حذف نوع ضريبة

        Args:
            tax_type_id: معرف نوع الضريبة

        Returns:
            True إذا تم الحذف بنجاح
        """
        try:
            logger.info(f"🔄 حذف نوع الضريبة: {tax_type_id}")

            # جلب نوع الضريبة
            tax_type = self.get_tax_type_by_id(tax_type_id)
            if not tax_type:
                raise ValueError(f"نوع الضريبة غير موجود: {tax_type_id}")

            # التحقق من وجود قيم ضريبية مرتبطة
            rates_count = self.db.query(TaxRate).filter(TaxRate.tax_type_id == tax_type_id).count()
            if rates_count > 0:
                raise ValueError(f"لا يمكن حذف نوع الضريبة لأنه يحتوي على {rates_count} قيمة ضريبية")

            # حذف نوع الضريبة
            self.db.delete(tax_type)
            self.db.commit()

            logger.info(f"✅ تم حذف نوع الضريبة بنجاح: {tax_type.name_ar}")

            return True

        except Exception as e:
            self.db.rollback()
            logger.error(f"❌ خطأ في حذف نوع الضريبة: {str(e)}")
            raise

    def get_tax_type_statistics(self) -> Dict[str, Any]:
        """
        جلب إحصائيات أنواع الضرائب

        Returns:
            إحصائيات أنواع الضرائب
        """
        try:
            logger.info("🔄 جلب إحصائيات أنواع الضرائب...")

            total_types = self.db.query(TaxType).count()
            active_types = self.db.query(TaxType).filter(TaxType.is_active == True).count()
            inactive_types = total_types - active_types

            # إحصائيات حسب فئة الضريبة
            category_stats = self.db.query(
                TaxType.tax_category,
                func.count(TaxType.id).label('count')
            ).group_by(TaxType.tax_category).all()

            # إحصائيات حسب طريقة الحساب
            method_stats = self.db.query(
                TaxType.calculation_method,
                func.count(TaxType.id).label('count')
            ).group_by(TaxType.calculation_method).all()

            stats = {
                'total_types': total_types,
                'active_types': active_types,
                'inactive_types': inactive_types,
                'category_stats': {stat.tax_category: stat.count for stat in category_stats},
                'method_stats': {stat.calculation_method: stat.count for stat in method_stats}
            }

            logger.info(f"✅ تم جلب إحصائيات أنواع الضرائب: {stats}")

            return stats

        except Exception as e:
            logger.error(f"❌ خطأ في جلب إحصائيات أنواع الضرائب: {str(e)}")
            raise
