"""
خدمة طلبات التحويل
تطبق مبادئ البرمجة الكائنية مع معالجة شاملة للأخطاء
تدعم النظام الجديد للعلاقات مع الفروع
"""

import logging
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import and_, func, text, or_
from decimal import Decimal

from models.warehouse import (
    Warehouse, WarehouseInventory, TransferRequest,
    TransferRequestItem, TransferStatus
)
from models.branch import Branch, branch_warehouses
from models.product import Product
from models.user import User
from utils.datetime_utils import get_tripoli_now

logger = logging.getLogger(__name__)


class TransferRequestService:
    """
    خدمة طلبات التحويل
    تطبق مبادئ البرمجة الكائنية مع نمط Singleton
    """
    
    _instance: Optional['TransferRequestService'] = None
    
    def __init__(self, db_session: Session):
        """تهيئة خدمة طلبات التحويل"""
        self.db_session = db_session
        logger.info("تم تهيئة خدمة طلبات التحويل بنجاح")
    
    @classmethod  # type: ignore
    def get_instance(cls, db_session: Session) -> 'TransferRequestService':
        """الحصول على instance وحيد من الخدمة"""
        if cls._instance is None:
            cls._instance = cls(db_session)
        elif db_session and cls._instance.db_session != db_session:
            cls._instance.db_session = db_session
        return cls._instance
    
    def create_transfer_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """إنشاء طلب تحويل جديد"""
        try:
            # التحقق من صحة البيانات
            validation_result = self._validate_transfer_request_data(request_data)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': validation_result['error']
                }
            
            # توليد رقم الطلب
            request_number = self._generate_request_number()
            
            # إنشاء طلب التحويل
            transfer_request = TransferRequest(
                request_number=request_number,
                from_warehouse_id=request_data['from_warehouse_id'],
                to_warehouse_id=request_data['to_warehouse_id'],
                requested_by=request_data['requested_by'],
                notes=request_data.get('notes')
            )
            
            self.db_session.add(transfer_request)
            self.db_session.flush()  # للحصول على ID
            
            # إضافة عناصر الطلب
            total_items = 0
            for item_data in request_data['items']:
                # التحقق من توفر المخزون
                availability_check = self._check_item_availability(
                    request_data['from_warehouse_id'],
                    item_data['product_id'],
                    item_data['requested_quantity']
                )
                
                if not availability_check['available']:
                    self.db_session.rollback()
                    return {
                        'success': False,
                        'error': f'المنتج {availability_check["product_name"]} غير متوفر بالكمية المطلوبة'
                    }
                
                # إنشاء عنصر الطلب
                request_item = TransferRequestItem(
                    transfer_request_id=transfer_request.id,
                    product_id=item_data['product_id'],
                    requested_quantity=Decimal(str(item_data['requested_quantity'])),
                    unit_cost=Decimal(str(item_data.get('unit_cost', 0))),
                    notes=item_data.get('notes')
                )
                
                self.db_session.add(request_item)
                total_items += 1
            
            self.db_session.commit()
            self.db_session.refresh(transfer_request)
            
            logger.info(f"تم إنشاء طلب تحويل جديد: {request_number}")
            
            return {
                'success': True,
                'transfer_request': {
                    'id': transfer_request.id,
                    'request_number': transfer_request.request_number,
                    'from_warehouse_id': transfer_request.from_warehouse_id,
                    'to_warehouse_id': transfer_request.to_warehouse_id,
                    'status': transfer_request.status.value,
                    'requested_by': transfer_request.requested_by,
                    'notes': transfer_request.notes,
                    'requested_at': transfer_request.requested_at.isoformat() if transfer_request.requested_at is not None else None,
                    'total_items': total_items
                }
            }
            
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"خطأ في إنشاء طلب التحويل: {e}")
            return {
                'success': False,
                'error': f'خطأ في إنشاء طلب التحويل: {str(e)}'
            }
    
    def approve_transfer_request(self, request_id: int, approved_by: int, approval_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """الموافقة على طلب التحويل"""
        try:
            # جلب طلب التحويل
            transfer_request = self.db_session.query(TransferRequest).filter(
                TransferRequest.id == request_id
            ).first()
            
            if not transfer_request:
                return {
                    'success': False,
                    'error': 'طلب التحويل غير موجود'
                }
            
            if transfer_request.status != TransferStatus.PENDING:
                return {
                    'success': False,
                    'error': f'لا يمكن الموافقة على طلب بحالة: {transfer_request.status.value}'
                }
            
            # تحديث حالة الطلب
            transfer_request.status = TransferStatus.APPROVED
            transfer_request.approved_by = approved_by
            transfer_request.approved_at = get_tripoli_now()
            
            # تحديث الكميات المعتمدة للعناصر
            if approval_data and 'items' in approval_data:
                for item_approval in approval_data['items']:
                    item = self.db_session.query(TransferRequestItem).filter(
                        and_(
                            TransferRequestItem.transfer_request_id == request_id,
                            TransferRequestItem.product_id == item_approval['product_id']
                        )
                    ).first()
                    
                    if item:
                        item.approved_quantity = Decimal(str(item_approval.get('approved_quantity', item.requested_quantity)))
            else:
                # الموافقة على جميع الكميات المطلوبة
                items = self.db_session.query(TransferRequestItem).filter(
                    TransferRequestItem.transfer_request_id == request_id
                ).all()
                
                for item in items:
                    item.approved_quantity = item.requested_quantity
            
            self.db_session.commit()
            
            logger.info(f"تم الموافقة على طلب التحويل: {transfer_request.request_number}")
            
            return {
                'success': True,
                'message': f'تم الموافقة على طلب التحويل {transfer_request.request_number}',
                'transfer_request': {
                    'id': transfer_request.id,
                    'request_number': transfer_request.request_number,
                    'status': transfer_request.status.value,
                    'approved_by': transfer_request.approved_by,
                    'approved_at': transfer_request.approved_at.isoformat() if transfer_request.approved_at else None
                }
            }
            
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"خطأ في الموافقة على طلب التحويل: {e}")
            return {
                'success': False,
                'error': f'خطأ في الموافقة على طلب التحويل: {str(e)}'
            }
    
    def process_transfer(self, request_id: int) -> Dict[str, Any]:
        """معالجة التحويل (تغيير الحالة إلى في الطريق)"""
        try:
            transfer_request = self.db_session.query(TransferRequest).filter(
                TransferRequest.id == request_id
            ).first()
            
            if not transfer_request:
                return {
                    'success': False,
                    'error': 'طلب التحويل غير موجود'
                }
            
            if transfer_request.status != TransferStatus.APPROVED:
                return {
                    'success': False,
                    'error': f'لا يمكن معالجة طلب بحالة: {transfer_request.status.value}'
                }
            
            # تحديث حالة الطلب
            transfer_request.status = TransferStatus.IN_TRANSIT
            
            # حجز المخزون في المستودع المصدر
            items = self.db_session.query(TransferRequestItem).filter(
                TransferRequestItem.transfer_request_id == request_id
            ).all()
            
            for item in items:
                if item.approved_quantity and item.approved_quantity > 0:
                    # حجز الكمية
                    inventory = self.db_session.query(WarehouseInventory).filter(
                        and_(
                            WarehouseInventory.warehouse_id == transfer_request.from_warehouse_id,
                            WarehouseInventory.product_id == item.product_id
                        )
                    ).first()
                    
                    if inventory:
                        inventory.reserved_quantity = float(inventory.reserved_quantity) + float(item.approved_quantity)
                        inventory.last_updated = get_tripoli_now()
            
            self.db_session.commit()
            
            logger.info(f"تم بدء معالجة طلب التحويل: {transfer_request.request_number}")
            
            return {
                'success': True,
                'message': f'تم بدء معالجة طلب التحويل {transfer_request.request_number}',
                'transfer_request': {
                    'id': transfer_request.id,
                    'request_number': transfer_request.request_number,
                    'status': transfer_request.status.value
                }
            }
            
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"خطأ في معالجة طلب التحويل: {e}")
            return {
                'success': False,
                'error': f'خطأ في معالجة طلب التحويل: {str(e)}'
            }

    def complete_transfer(self, request_id: int) -> Dict[str, Any]:
        """إكمال التحويل"""
        try:
            transfer_request = self.db_session.query(TransferRequest).filter(
                TransferRequest.id == request_id
            ).first()

            if not transfer_request:
                return {
                    'success': False,
                    'error': 'طلب التحويل غير موجود'
                }

            if transfer_request.status != TransferStatus.IN_TRANSIT:
                return {
                    'success': False,
                    'error': f'لا يمكن إكمال طلب بحالة: {transfer_request.status.value}'
                }

            # تنفيذ التحويل الفعلي
            from services.warehouse_movement_service import WarehouseMovementService
            movement_service = WarehouseMovementService.get_instance(self.db_session)

            items = self.db_session.query(TransferRequestItem).filter(
                TransferRequestItem.transfer_request_id == request_id
            ).all()

            for item in items:
                if item.approved_quantity and item.approved_quantity > 0:
                    # تسجيل حركة التحويل
                    movement_data = {
                        'movement_type': 'TRANSFER',
                        'from_warehouse_id': transfer_request.from_warehouse_id,
                        'to_warehouse_id': transfer_request.to_warehouse_id,
                        'product_id': item.product_id,
                        'quantity': float(item.approved_quantity),
                        'unit_cost': float(item.unit_cost) if item.unit_cost else None,
                        'reference_type': 'TRANSFER',
                        'reference_id': transfer_request.id,
                        'notes': f'تحويل من طلب رقم {transfer_request.request_number}',
                        'created_by': transfer_request.approved_by
                    }

                    movement_result = movement_service.record_movement(movement_data)
                    if not movement_result['success']:
                        self.db_session.rollback()
                        return {
                            'success': False,
                            'error': f'خطأ في تسجيل حركة التحويل: {movement_result["error"]}'
                        }

                    # تحديث الكمية المحولة
                    item.transferred_quantity = item.approved_quantity

                    # إلغاء حجز المخزون
                    inventory = self.db_session.query(WarehouseInventory).filter(
                        and_(
                            WarehouseInventory.warehouse_id == transfer_request.from_warehouse_id,
                            WarehouseInventory.product_id == item.product_id
                        )
                    ).first()

                    if inventory:
                        inventory.reserved_quantity = max(0, float(inventory.reserved_quantity) - float(item.approved_quantity))
                        inventory.last_updated = get_tripoli_now()

            # تحديث حالة الطلب
            transfer_request.status = TransferStatus.COMPLETED
            transfer_request.completed_at = get_tripoli_now()

            self.db_session.commit()

            logger.info(f"تم إكمال طلب التحويل: {transfer_request.request_number}")

            return {
                'success': True,
                'message': f'تم إكمال طلب التحويل {transfer_request.request_number}',
                'transfer_request': {
                    'id': transfer_request.id,
                    'request_number': transfer_request.request_number,
                    'status': transfer_request.status.value,
                    'completed_at': transfer_request.completed_at.isoformat() if transfer_request.completed_at else None
                }
            }

        except Exception as e:
            self.db_session.rollback()
            logger.error(f"خطأ في إكمال طلب التحويل: {e}")
            return {
                'success': False,
                'error': f'خطأ في إكمال طلب التحويل: {str(e)}'
            }

    def cancel_transfer_request(self, request_id: int, reason: str) -> Dict[str, Any]:
        """إلغاء طلب التحويل"""
        try:
            transfer_request = self.db_session.query(TransferRequest).filter(
                TransferRequest.id == request_id
            ).first()

            if not transfer_request:
                return {
                    'success': False,
                    'error': 'طلب التحويل غير موجود'
                }

            if transfer_request.status == TransferStatus.COMPLETED:
                return {
                    'success': False,
                    'error': 'لا يمكن إلغاء طلب مكتمل'
                }

            # إلغاء حجز المخزون إذا كان في الطريق
            if transfer_request.status == TransferStatus.IN_TRANSIT:
                items = self.db_session.query(TransferRequestItem).filter(
                    TransferRequestItem.transfer_request_id == request_id
                ).all()

                for item in items:
                    if item.approved_quantity and item.approved_quantity > 0:
                        inventory = self.db_session.query(WarehouseInventory).filter(
                            and_(
                                WarehouseInventory.warehouse_id == transfer_request.from_warehouse_id,
                                WarehouseInventory.product_id == item.product_id
                            )
                        ).first()

                        if inventory:
                            inventory.reserved_quantity = max(0, float(inventory.reserved_quantity) - float(item.approved_quantity))
                            inventory.last_updated = get_tripoli_now()

            # تحديث حالة الطلب
            transfer_request.status = TransferStatus.CANCELLED
            transfer_request.notes = f"{transfer_request.notes or ''}\nسبب الإلغاء: {reason}".strip()

            self.db_session.commit()

            logger.info(f"تم إلغاء طلب التحويل: {transfer_request.request_number}")

            return {
                'success': True,
                'message': f'تم إلغاء طلب التحويل {transfer_request.request_number}',
                'transfer_request': {
                    'id': transfer_request.id,
                    'request_number': transfer_request.request_number,
                    'status': transfer_request.status.value,
                    'notes': transfer_request.notes
                }
            }

        except Exception as e:
            self.db_session.rollback()
            logger.error(f"خطأ في إلغاء طلب التحويل: {e}")
            return {
                'success': False,
                'error': f'خطأ في إلغاء طلب التحويل: {str(e)}'
            }

    def get_pending_transfers(self) -> Dict[str, Any]:
        """الحصول على طلبات التحويل المعلقة"""
        try:
            query = self.db_session.query(
                TransferRequest,
                Warehouse.name.label('from_warehouse_name'),
                Warehouse.code.label('from_warehouse_code'),
                User.username.label('requested_by_username')
            ).join(
                Warehouse, TransferRequest.from_warehouse_id == Warehouse.id
            ).outerjoin(
                User, TransferRequest.requested_by == User.id
            ).filter(
                TransferRequest.status == TransferStatus.PENDING
            ).order_by(
                TransferRequest.requested_at.desc()
            )

            pending_transfers = []
            for transfer, from_warehouse_name, from_warehouse_code, requested_by_username in query:
                # حساب إجمالي العناصر
                items_count = self.db_session.query(TransferRequestItem).filter(
                    TransferRequestItem.transfer_request_id == transfer.id
                ).count()

                pending_transfers.append({
                    'id': transfer.id,
                    'request_number': transfer.request_number,
                    'from_warehouse_id': transfer.from_warehouse_id,
                    'from_warehouse_name': from_warehouse_name,
                    'from_warehouse_code': from_warehouse_code,
                    'to_warehouse_id': transfer.to_warehouse_id,
                    'status': transfer.status.value,
                    'requested_by': transfer.requested_by,
                    'requested_by_username': requested_by_username,
                    'notes': transfer.notes,
                    'requested_at': transfer.requested_at.isoformat() if transfer.requested_at else None,
                    'items_count': items_count
                })

            return {
                'success': True,
                'pending_transfers': pending_transfers,
                'total_count': len(pending_transfers)
            }

        except Exception as e:
            logger.error(f"خطأ في جلب طلبات التحويل المعلقة: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب طلبات التحويل المعلقة: {str(e)}'
            }

    def get_transfer_history(self, filters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """الحصول على تاريخ طلبات التحويل"""
        try:
            query = self.db_session.query(
                TransferRequest,
                Warehouse.name.label('from_warehouse_name'),
                Warehouse.code.label('from_warehouse_code'),
                User.username.label('requested_by_username')
            ).join(
                Warehouse, TransferRequest.from_warehouse_id == Warehouse.id
            ).outerjoin(
                User, TransferRequest.requested_by == User.id
            )

            # تطبيق الفلاتر
            if filters:
                if filters.get('status'):
                    query = query.filter(
                        TransferRequest.status == TransferStatus(filters['status'])
                    )

                if filters.get('from_warehouse_id'):
                    query = query.filter(
                        TransferRequest.from_warehouse_id == filters['from_warehouse_id']
                    )

                if filters.get('to_warehouse_id'):
                    query = query.filter(
                        TransferRequest.to_warehouse_id == filters['to_warehouse_id']
                    )

                if filters.get('date_from'):
                    query = query.filter(
                        TransferRequest.requested_at >= filters['date_from']
                    )

                if filters.get('date_to'):
                    query = query.filter(
                        TransferRequest.requested_at <= filters['date_to']
                    )

            # ترتيب النتائج
            query = query.order_by(TransferRequest.requested_at.desc())

            # تطبيق التصفح
            page = filters.get('page', 1) if filters else 1
            per_page = filters.get('per_page', 20) if filters else 20
            offset = (page - 1) * per_page

            total_count = query.count()
            transfers_query = query.offset(offset).limit(per_page)

            transfers = []
            for transfer, from_warehouse_name, from_warehouse_code, requested_by_username in transfers_query:
                # حساب إجمالي العناصر
                items_count = self.db_session.query(TransferRequestItem).filter(
                    TransferRequestItem.transfer_request_id == transfer.id
                ).count()

                transfers.append({
                    'id': transfer.id,
                    'request_number': transfer.request_number,
                    'from_warehouse_id': transfer.from_warehouse_id,
                    'from_warehouse_name': from_warehouse_name,
                    'from_warehouse_code': from_warehouse_code,
                    'to_warehouse_id': transfer.to_warehouse_id,
                    'status': transfer.status.value,
                    'requested_by': transfer.requested_by,
                    'requested_by_username': requested_by_username,
                    'approved_by': transfer.approved_by,
                    'notes': transfer.notes,
                    'requested_at': transfer.requested_at.isoformat() if transfer.requested_at else None,
                    'approved_at': transfer.approved_at.isoformat() if transfer.approved_at else None,
                    'completed_at': transfer.completed_at.isoformat() if transfer.completed_at else None,
                    'items_count': items_count
                })

            return {
                'success': True,
                'transfers': transfers,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total_count': total_count,
                    'total_pages': (total_count + per_page - 1) // per_page
                }
            }

        except Exception as e:
            logger.error(f"خطأ في جلب تاريخ طلبات التحويل: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب تاريخ طلبات التحويل: {str(e)}'
            }

    def _validate_transfer_request_data(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """التحقق من صحة بيانات طلب التحويل"""
        try:
            # التحقق من الحقول المطلوبة
            required_fields = ['from_warehouse_id', 'to_warehouse_id', 'requested_by', 'items']
            for field in required_fields:
                if field not in request_data:
                    return {
                        'valid': False,
                        'error': f'الحقل {field} مطلوب'
                    }

            # التحقق من عدم التحويل لنفس المستودع
            if request_data['from_warehouse_id'] == request_data['to_warehouse_id']:
                return {
                    'valid': False,
                    'error': 'لا يمكن التحويل من وإلى نفس المستودع'
                }

            # التحقق من وجود عناصر
            if not request_data['items'] or len(request_data['items']) == 0:
                return {
                    'valid': False,
                    'error': 'يجب إضافة عنصر واحد على الأقل للطلب'
                }

            # التحقق من صحة العناصر
            for item in request_data['items']:
                if 'product_id' not in item or 'requested_quantity' not in item:
                    return {
                        'valid': False,
                        'error': 'معرف المنتج والكمية مطلوبان لكل عنصر'
                    }

                try:
                    quantity = float(item['requested_quantity'])
                    if quantity <= 0:
                        return {
                            'valid': False,
                            'error': 'الكمية يجب أن تكون أكبر من صفر'
                        }
                except (ValueError, TypeError):
                    return {
                        'valid': False,
                        'error': 'الكمية يجب أن تكون رقم صحيح'
                    }

            return {
                'valid': True
            }

        except Exception as e:
            return {
                'valid': False,
                'error': f'خطأ في التحقق من البيانات: {str(e)}'
            }

    def _generate_request_number(self) -> str:
        """توليد رقم طلب التحويل"""
        try:
            # الحصول على آخر رقم طلب
            last_request = self.db_session.query(TransferRequest).order_by(
                TransferRequest.id.desc()
            ).first()

            if last_request:
                # استخراج الرقم من آخر طلب
                last_number = int(last_request.request_number.split('-')[-1])
                new_number = last_number + 1
            else:
                new_number = 1

            # تكوين رقم الطلب الجديد
            current_date = get_tripoli_now()
            request_number = f"TR-{current_date.strftime('%Y%m%d')}-{new_number:04d}"

            return request_number

        except Exception as e:
            logger.error(f"خطأ في توليد رقم الطلب: {e}")
            # رقم احتياطي
            current_date = get_tripoli_now()
            return f"TR-{current_date.strftime('%Y%m%d%H%M%S')}"

    def _check_item_availability(self, warehouse_id: int, product_id: int, quantity: float) -> Dict[str, Any]:
        """التحقق من توفر العنصر"""
        try:
            # جلب معلومات المنتج
            product = self.db_session.query(Product).filter(
                Product.id == product_id
            ).first()

            if not product:
                return {
                    'available': False,
                    'product_name': 'منتج غير موجود',
                    'error': 'المنتج غير موجود'
                }

            # جلب المخزون
            inventory = self.db_session.query(WarehouseInventory).filter(
                and_(
                    WarehouseInventory.warehouse_id == warehouse_id,
                    WarehouseInventory.product_id == product_id
                )
            ).first()

            if not inventory:
                return {
                    'available': False,
                    'product_name': product.name,
                    'current_quantity': 0,
                    'available_quantity': 0,
                    'error': 'المنتج غير موجود في هذا المستودع'
                }

            available_quantity = float(inventory.quantity) - float(inventory.reserved_quantity)
            is_available = available_quantity >= quantity

            return {
                'available': is_available,
                'product_name': product.name,
                'current_quantity': float(inventory.quantity),
                'reserved_quantity': float(inventory.reserved_quantity),
                'available_quantity': available_quantity,
                'requested_quantity': quantity,
                'shortage': max(0, quantity - available_quantity) if not is_available else 0
            }

        except Exception as e:
            return {
                'available': False,
                'product_name': 'خطأ',
                'error': f'خطأ في التحقق من التوفر: {str(e)}'
            }

    # ==================== طرق دعم النظام الجديد للفروع ====================

    def create_branch_to_branch_transfer(self, from_branch_id: int, to_branch_id: int,
                                       items: List[Dict[str, Any]], requested_by: int,
                                       notes: Optional[str] = None) -> Dict[str, Any]:
        """
        إنشاء طلب تحويل بين فرعين (يختار النظام المستودعات الأمثل تلقائياً)

        Args:
            from_branch_id: معرف الفرع المصدر
            to_branch_id: معرف الفرع الوجهة
            items: قائمة المنتجات والكميات
            requested_by: معرف المستخدم الطالب
            notes: ملاحظات إضافية

        Returns:
            نتيجة إنشاء طلب التحويل
        """
        try:
            # التحقق من وجود الفروع
            from_branch = self.db_session.query(Branch).filter(
                Branch.id == from_branch_id, Branch.is_active == True
            ).first()

            to_branch = self.db_session.query(Branch).filter(
                Branch.id == to_branch_id, Branch.is_active == True
            ).first()

            if not from_branch:
                return {
                    'success': False,
                    'error': 'الفرع المصدر غير موجود أو غير نشط'
                }

            if not to_branch:
                return {
                    'success': False,
                    'error': 'فرع الوجهة غير موجود أو غير نشط'
                }

            # الحصول على المستودع الأمثل للفرع المصدر
            from services.warehouse_service import get_warehouse_service
            warehouse_service = get_warehouse_service(self.db_session)

            # البحث عن أفضل مستودع للفرع المصدر يحتوي على المنتجات المطلوبة
            optimal_from_warehouse = None
            optimal_to_warehouse = None

            for item in items:
                product_id = item.get('product_id')
                quantity = item.get('quantity', 0)

                # التحقق من صحة البيانات
                if not product_id or not isinstance(product_id, int):
                    continue

                # البحث عن مستودع في الفرع المصدر
                from_warehouse_result = warehouse_service.get_optimal_warehouse_for_branch(
                    from_branch_id, product_id, quantity
                )

                if from_warehouse_result['success']:
                    optimal_from_warehouse = from_warehouse_result['warehouse']
                    break

            if not optimal_from_warehouse:
                return {
                    'success': False,
                    'error': 'لا يوجد مستودع متاح في الفرع المصدر يحتوي على المنتجات المطلوبة'
                }

            # الحصول على المستودع الأساسي للفرع الوجهة
            from services.branch_warehouse_service import get_branch_warehouse_service
            branch_warehouse_service = get_branch_warehouse_service(self.db_session)

            primary_warehouse_result = branch_warehouse_service.get_primary_warehouse_for_branch(to_branch_id)

            if primary_warehouse_result['success'] and primary_warehouse_result['warehouse']:
                optimal_to_warehouse = primary_warehouse_result['warehouse']
            else:
                # إذا لم يوجد مستودع أساسي، اختر أول مستودع متاح
                warehouses_result = branch_warehouse_service.get_warehouses_for_branch(to_branch_id)
                if warehouses_result['success'] and warehouses_result['warehouses']:
                    optimal_to_warehouse = warehouses_result['warehouses'][0]

            if not optimal_to_warehouse:
                return {
                    'success': False,
                    'error': 'لا يوجد مستودع متاح في فرع الوجهة'
                }

            # إنشاء طلب التحويل العادي بين المستودعين
            transfer_data = {
                'from_warehouse_id': optimal_from_warehouse['id'],
                'to_warehouse_id': optimal_to_warehouse['id'],
                'requested_by': requested_by,
                'notes': f"تحويل بين الفروع - من: {from_branch.name} إلى: {to_branch.name}" +
                        (f" - {notes}" if notes else ""),
                'items': items
            }

            result = self.create_transfer_request(transfer_data)

            if result['success']:
                # إضافة معلومات الفروع للنتيجة
                result['transfer_request']['from_branch'] = {
                    'id': from_branch.id,
                    'name': from_branch.name,
                    'code': from_branch.code
                }
                result['transfer_request']['to_branch'] = {
                    'id': to_branch.id,
                    'name': to_branch.name,
                    'code': to_branch.code
                }
                result['transfer_request']['selected_from_warehouse'] = optimal_from_warehouse
                result['transfer_request']['selected_to_warehouse'] = optimal_to_warehouse

            logger.info(f"✅ تم إنشاء طلب تحويل بين الفروع من {from_branch.name} إلى {to_branch.name}")

            return result

        except Exception as e:
            self.db_session.rollback()
            logger.error(f"❌ خطأ في إنشاء طلب تحويل بين الفروع: {e}")
            return {
                'success': False,
                'error': f'خطأ في إنشاء طلب تحويل بين الفروع: {str(e)}'
            }

    def get_transfer_suggestions_for_branch(self, branch_id: int,
                                          low_stock_threshold: float = 10.0) -> Dict[str, Any]:
        """
        الحصول على اقتراحات التحويل للفرع بناءً على المخزون المنخفض

        Args:
            branch_id: معرف الفرع
            low_stock_threshold: حد المخزون المنخفض

        Returns:
            قائمة اقتراحات التحويل
        """
        try:
            # الحصول على المنتجات ذات المخزون المنخفض في مستودعات الفرع
            query = text("""
                SELECT
                    p.id as product_id,
                    p.name as product_name,
                    p.code as product_code,
                    w.id as warehouse_id,
                    w.name as warehouse_name,
                    wi.quantity as current_quantity,
                    wi.reserved_quantity,
                    (wi.quantity - wi.reserved_quantity) as available_quantity
                FROM products p
                INNER JOIN warehouse_inventory wi ON p.id = wi.product_id
                INNER JOIN warehouses w ON wi.warehouse_id = w.id
                INNER JOIN branch_warehouses bw ON w.id = bw.warehouse_id
                WHERE bw.branch_id = :branch_id
                AND w.is_active = true
                AND (wi.quantity - wi.reserved_quantity) <= :threshold
                AND (wi.quantity - wi.reserved_quantity) > 0
                ORDER BY (wi.quantity - wi.reserved_quantity) ASC, p.name
            """)

            low_stock_items = self.db_session.execute(query, {
                "branch_id": branch_id,
                "threshold": low_stock_threshold
            }).fetchall()

            suggestions = []

            for item in low_stock_items:
                # البحث عن مستودعات أخرى تحتوي على هذا المنتج
                available_sources_query = text("""
                    SELECT
                        w.id as warehouse_id,
                        w.name as warehouse_name,
                        w.code as warehouse_code,
                        wi.quantity,
                        wi.reserved_quantity,
                        (wi.quantity - wi.reserved_quantity) as available_quantity,
                        CASE
                            WHEN bw.branch_id IS NOT NULL THEN b.name
                            ELSE 'غير مرتبط بفرع'
                        END as branch_name
                    FROM warehouse_inventory wi
                    INNER JOIN warehouses w ON wi.warehouse_id = w.id
                    LEFT JOIN branch_warehouses bw ON w.id = bw.warehouse_id
                    LEFT JOIN branches b ON bw.branch_id = b.id
                    WHERE wi.product_id = :product_id
                    AND w.is_active = true
                    AND w.id != :current_warehouse_id
                    AND (wi.quantity - wi.reserved_quantity) > :min_quantity
                    ORDER BY (wi.quantity - wi.reserved_quantity) DESC
                    LIMIT 5
                """)

                available_sources = self.db_session.execute(available_sources_query, {
                    "product_id": item.product_id,
                    "current_warehouse_id": item.warehouse_id,
                    "min_quantity": 5.0  # الحد الأدنى للكمية المتاحة
                }).fetchall()

                if available_sources:
                    suggestion = {
                        'product_id': item.product_id,
                        'product_name': item.product_name,
                        'product_code': item.product_code,
                        'current_warehouse': {
                            'id': item.warehouse_id,
                            'name': item.warehouse_name,
                            'current_quantity': float(item.current_quantity),
                            'available_quantity': float(item.available_quantity)
                        },
                        'suggested_sources': []
                    }

                    for source in available_sources:
                        suggestion['suggested_sources'].append({
                            'warehouse_id': source.warehouse_id,
                            'warehouse_name': source.warehouse_name,
                            'warehouse_code': source.warehouse_code,
                            'available_quantity': float(source.available_quantity),
                            'branch_name': source.branch_name,
                            'suggested_transfer_quantity': min(
                                float(source.available_quantity) * 0.5,  # نقل 50% من المتاح
                                50.0  # أو حد أقصى 50 قطعة
                            )
                        })

                    suggestions.append(suggestion)

            logger.info(f"✅ تم إنشاء {len(suggestions)} اقتراح تحويل للفرع {branch_id}")

            return {
                'success': True,
                'suggestions': suggestions,
                'total_count': len(suggestions),
                'branch_id': branch_id,
                'threshold_used': low_stock_threshold
            }

        except Exception as e:
            logger.error(f"❌ خطأ في جلب اقتراحات التحويل: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب اقتراحات التحويل: {str(e)}'
            }

    def get_branch_transfer_history(self, branch_id: int,
                                  date_from: Optional[str] = None,
                                  date_to: Optional[str] = None) -> Dict[str, Any]:
        """
        الحصول على تاريخ التحويلات للفرع

        Args:
            branch_id: معرف الفرع
            date_from: تاريخ البداية
            date_to: تاريخ النهاية

        Returns:
            تاريخ التحويلات
        """
        try:
            # بناء الاستعلام الأساسي
            base_query = """
                SELECT DISTINCT
                    tr.id, tr.request_number, tr.status, tr.notes,
                    tr.requested_at, tr.approved_at, tr.completed_at,
                    fw.name as from_warehouse_name, fw.code as from_warehouse_code,
                    tw.name as to_warehouse_name, tw.code as to_warehouse_code,
                    fb.name as from_branch_name, fb.code as from_branch_code,
                    tb.name as to_branch_name, tb.code as to_branch_code,
                    u1.username as requested_by_username,
                    u2.username as approved_by_username,
                    COUNT(tri.id) as items_count
                FROM transfer_requests tr
                INNER JOIN warehouses fw ON tr.from_warehouse_id = fw.id
                INNER JOIN warehouses tw ON tr.to_warehouse_id = tw.id
                LEFT JOIN branch_warehouses bw1 ON fw.id = bw1.warehouse_id
                LEFT JOIN branch_warehouses bw2 ON tw.id = bw2.warehouse_id
                LEFT JOIN branches fb ON bw1.branch_id = fb.id
                LEFT JOIN branches tb ON bw2.branch_id = tb.id
                LEFT JOIN users u1 ON tr.requested_by = u1.id
                LEFT JOIN users u2 ON tr.approved_by = u2.id
                LEFT JOIN transfer_request_items tri ON tr.id = tri.transfer_request_id
                WHERE (bw1.branch_id = :branch_id OR bw2.branch_id = :branch_id)
            """

            params = {"branch_id": branch_id}

            # إضافة فلاتر التاريخ
            if date_from:
                base_query += " AND tr.requested_at >= :date_from"
                # تحويل التاريخ إلى datetime إذا كان string
                if isinstance(date_from, str):
                    from datetime import datetime
                    params["date_from"] = datetime.fromisoformat(date_from.replace('Z', '+00:00'))
                else:
                    params["date_from"] = date_from

            if date_to:
                base_query += " AND tr.requested_at <= :date_to"
                # تحويل التاريخ إلى datetime إذا كان string
                if isinstance(date_to, str):
                    from datetime import datetime
                    params["date_to"] = datetime.fromisoformat(date_to.replace('Z', '+00:00'))
                else:
                    params["date_to"] = date_to

            base_query += """
                GROUP BY tr.id, tr.request_number, tr.status, tr.notes,
                         tr.requested_at, tr.approved_at, tr.completed_at,
                         fw.name, fw.code, tw.name, tw.code,
                         fb.name, fb.code, tb.name, tb.code,
                         u1.username, u2.username
                ORDER BY tr.requested_at DESC
            """

            result = self.db_session.execute(text(base_query), params).fetchall()

            transfers = []
            for row in result:
                transfer_data = {
                    'id': row.id,
                    'request_number': row.request_number,
                    'status': row.status,
                    'notes': row.notes,
                    'requested_at': row.requested_at.isoformat() if row.requested_at else None,
                    'approved_at': row.approved_at.isoformat() if row.approved_at else None,
                    'completed_at': row.completed_at.isoformat() if row.completed_at else None,
                    'from_warehouse': {
                        'name': row.from_warehouse_name,
                        'code': row.from_warehouse_code
                    },
                    'to_warehouse': {
                        'name': row.to_warehouse_name,
                        'code': row.to_warehouse_code
                    },
                    'from_branch': {
                        'name': row.from_branch_name,
                        'code': row.from_branch_code
                    } if row.from_branch_name else None,
                    'to_branch': {
                        'name': row.to_branch_name,
                        'code': row.to_branch_code
                    } if row.to_branch_name else None,
                    'requested_by_username': row.requested_by_username,
                    'approved_by_username': row.approved_by_username,
                    'items_count': row.items_count,
                    'direction': 'outgoing' if row.from_branch_name else 'incoming'
                }
                transfers.append(transfer_data)

            logger.info(f"✅ تم جلب {len(transfers)} تحويل للفرع {branch_id}")

            return {
                'success': True,
                'transfers': transfers,
                'total_count': len(transfers),
                'branch_id': branch_id
            }

        except Exception as e:
            logger.error(f"❌ خطأ في جلب تاريخ التحويلات: {e}")
            return {
                'success': False,
                'error': f'خطأ في جلب تاريخ التحويلات: {str(e)}'
            }


def get_transfer_request_service(db_session: Session) -> TransferRequestService:
    """
    دالة مساعدة للحصول على instance من خدمة طلبات التحويل

    Args:
        db_session: جلسة قاعدة البيانات

    Returns:
        instance من TransferRequestService
    """
    return TransferRequestService.get_instance(db_session)
