"""
خدمة البصمة الموحدة - SmartPOS
خدمة شاملة ومحسنة لإدارة بصمات الأجهزة مع تجنب التكرار
"""

import hashlib
import logging
import socket

from typing import Dict, Any, Optional, Tuple, List
from sqlalchemy.orm import Session
from sqlalchemy import select

from models.device_fingerprint import DeviceFingerprint, DeviceFingerprintHistory
# from utils.device_detection import device_detector  # تم إزالة هذا الملف
from utils.datetime_utils import get_tripoli_now

logger = logging.getLogger(__name__)


class UnifiedFingerprintService:
    """
    خدمة البصمة الموحدة - تجمع جميع وظائف إدارة البصمات في مكان واحد
    """

    def __init__(self, db: Session):
        self.db = db
        self._cache = {}  # cache للبصمات المستخدمة مؤخراً
        self._history_service = None  # سيتم تهيئتها عند الحاجة

    def _get_history_service(self):
        """الحصول على خدمة التاريخ (Lazy Loading)"""
        if self._history_service is None:
            from services.device_fingerprint_history_service import DeviceFingerprintHistoryService
            self._history_service = DeviceFingerprintHistoryService(self.db)
        return self._history_service

    def create_fingerprint_id(self, hardware_fp: str, storage_fp: str) -> str:
        """إنشاء معرف بصمة فريد وثابت"""
        try:
            # استخدام البصمة الأساسية فقط لضمان الثبات
            combined = f"{hardware_fp}_{storage_fp}"
            hash_obj = hashlib.sha256(combined.encode())
            fingerprint_id = f"fp_{hash_obj.hexdigest()[:16]}"
            
            logger.debug(f"تم إنشاء معرف البصمة: {fingerprint_id}")
            return fingerprint_id
        except Exception as e:
            logger.error(f"خطأ في إنشاء معرف البصمة: {e}")
            # معرف آمن للبصمة المتقدمة
            safe_hash = hashlib.md5(f"{hardware_fp[:10]}_{storage_fp[:10]}".encode()).hexdigest()
            return f"fp_safe_{safe_hash[:12]}"

    def find_existing_fingerprint(self, hardware_fp: str, storage_fp: str) -> Optional[DeviceFingerprint]:
        """البحث عن بصمة موجودة بناءً على البصمة الأساسية"""
        try:
            # البحث في الـ cache أولاً
            cache_key = f"{hardware_fp}_{storage_fp}"
            if cache_key in self._cache:
                logger.debug(f"تم العثور على البصمة في الـ cache: {cache_key[:16]}...")
                return self._cache[cache_key]

            # البحث في قاعدة البيانات
            stmt = select(DeviceFingerprint).where(
                DeviceFingerprint.hardware_fingerprint == hardware_fp,
                DeviceFingerprint.storage_fingerprint == storage_fp,
                DeviceFingerprint.is_active == True
            )
            
            fingerprint = self.db.execute(stmt).scalar_one_or_none()
            
            if fingerprint:
                # حفظ في الـ cache
                self._cache[cache_key] = fingerprint
                logger.debug(f"تم العثور على البصمة في قاعدة البيانات: {fingerprint.fingerprint_id}")
            
            return fingerprint
            
        except Exception as e:
            logger.error(f"خطأ في البحث عن البصمة: {e}")
            return None

    def create_new_fingerprint(
        self,
        hardware_fp: str,
        storage_fp: str,
        screen_fp: Optional[str] = None,
        system_fp: Optional[str] = None,
        # network_fp: Optional[str] = None,  # محذوف حسب المتطلبات الجديدة
        additional_info: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        auto_approve: bool = False
    ) -> DeviceFingerprint:
        """إنشاء بصمة جديدة مع معلومات شاملة"""
        try:
            fingerprint_id = self.create_fingerprint_id(hardware_fp, storage_fp)

            logger.info(f"🔄 إنشاء بصمة جديدة: {fingerprint_id}")
            logger.info(f"   - hardware_fp: {hardware_fp[:8]}...")
            logger.info(f"   - storage_fp: {storage_fp[:8]}...")
            logger.info(f"   - ip_address: {ip_address}")
            logger.info(f"   - auto_approve: {auto_approve}")

            # الحصول على الوقت الحالي لطرابلس
            current_time = get_tripoli_now()

            fingerprint = DeviceFingerprint(
                fingerprint_id=fingerprint_id,
                hardware_fingerprint=hardware_fp,
                storage_fingerprint=storage_fp,
                screen_fingerprint=screen_fp or 'stable',
                system_fingerprint=system_fp or self._detect_system_fingerprint(user_agent),
                # network_fingerprint=network_fp or '',  # محذوف حسب المتطلبات الجديدة
                last_ip=ip_address,
                last_user_agent=user_agent,
                is_active=True,
                auto_approved=auto_approve,
                created_at=current_time,
                updated_at=current_time,
                last_seen_at=current_time
            )

            if additional_info:
                fingerprint.update_additional_info(additional_info)

            self.db.add(fingerprint)
            self.db.commit()
            self.db.refresh(fingerprint)

            # إضافة إلى الـ cache
            cache_key = f"{hardware_fp}_{storage_fp}"
            self._cache[cache_key] = fingerprint

            # تسجيل إنشاء البصمة الأولى في التاريخ
            self._log_fingerprint_event(
                fingerprint_id,
                'fingerprint_created',
                ip_address,
                user_agent,
                {
                    'hardware_fp': hardware_fp[:8] + '...',
                    'storage_fp': storage_fp[:8] + '...',
                    'auto_approved': auto_approve,
                    'is_first_creation': True,
                    'creation_method': 'advanced_fingerprint'
                }
            )

            logger.info(f"✅ تم إنشاء البصمة بنجاح: {fingerprint_id}")
            return fingerprint

        except Exception as e:
            logger.error(f"خطأ في إنشاء البصمة الجديدة: {e}")
            self.db.rollback()
            raise

    def update_fingerprint_access(
        self,
        fingerprint: DeviceFingerprint,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        additional_info: Optional[Dict[str, Any]] = None
    ) -> DeviceFingerprint:
        """تحديث معلومات الوصول للبصمة"""
        try:
            fingerprint.update_last_seen(ip_address, user_agent)

            if additional_info:
                fingerprint.update_additional_info(additional_info)

            self.db.commit()
            self.db.refresh(fingerprint)

            # تحديث الـ cache
            cache_key = f"{fingerprint.hardware_fingerprint}_{fingerprint.storage_fingerprint}"
            self._cache[cache_key] = fingerprint

            # تسجيل الوصول
            self._log_fingerprint_event(
                str(fingerprint.fingerprint_id),
                'accessed',
                ip_address,
                user_agent,
                {'updated_info': bool(additional_info)}
            )

            logger.debug(f"🔄 تم تحديث بصمة الوصول: {fingerprint.fingerprint_id}")
            return fingerprint

        except Exception as e:
            logger.error(f"خطأ في تحديث البصمة: {e}")
            self.db.rollback()
            raise

    def get_or_create_fingerprint(
        self,
        hardware_fp: str,
        storage_fp: str,
        screen_fp: Optional[str] = None,
        system_fp: Optional[str] = None,
        # تم إزالة network_fp نهائياً حسب متطلبات النظام الجديدة
        additional_info: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        auto_approve: bool = False
    ) -> Tuple[DeviceFingerprint, bool]:
        """الحصول على بصمة موجودة أو إنشاء جديدة"""
        
        # البحث عن بصمة موجودة
        existing_fingerprint = self.find_existing_fingerprint(hardware_fp, storage_fp)

        if existing_fingerprint:
            # تحديث معلومات الوصول
            updated_fingerprint = self.update_fingerprint_access(
                existing_fingerprint, ip_address, user_agent, additional_info
            )
            return updated_fingerprint, False
        else:
            # إنشاء بصمة جديدة
            new_fingerprint = self.create_new_fingerprint(
                hardware_fp, storage_fp, screen_fp, system_fp,
                # network_fp,  # محذوف حسب المتطلبات الجديدة
                additional_info, ip_address, user_agent, auto_approve
            )
            return new_fingerprint, True

    def generate_unified_device_id(
        self,
        client_ip: str,
        user_agent: Optional[str] = None,
        request_headers: Optional[Dict[str, str]] = None,
        device_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        إنشاء معرف جهاز موحد مع دعم البصمات المتقدمة
        """
        try:
            # تطبيع IP
            normalized_ip = self._normalize_ip(client_ip)
            
            # تحديد نوع الجهاز
            is_main_server = self._is_main_server(normalized_ip)
            
            if is_main_server:
                return self._generate_main_server_id(normalized_ip, user_agent, device_info)
            else:
                return self._generate_remote_device_id(
                    normalized_ip, user_agent, request_headers
                )
                
        except Exception as e:
            logger.error(f"خطأ في إنشاء معرف الجهاز الموحد: {e}")
            # رفض الجهاز في حالة الخطأ - البصمة المتقدمة مطلوبة
            return {
                'device_id': None,
                'device_type': 'error',
                'is_main_server': False,
                'has_advanced_fingerprint': False,
                'ip_address': client_ip,
                'user_agent': user_agent,
                'rejection_reason': f'خطأ في معالجة البصمة الموحدة: {str(e)}'
            }

    def _detect_system_fingerprint(self, user_agent: Optional[str]) -> str:
        """كشف بصمة النظام من user agent"""
        if not user_agent:
            return 'unknown_stable'
            
        if 'Windows' in user_agent:
            return 'Windows_stable'
        elif 'Mac' in user_agent:
            return 'Mac_stable'
        elif 'Linux' in user_agent:
            return 'Linux_stable'
        elif 'Android' in user_agent:
            return 'Android_stable'
        elif 'iOS' in user_agent:
            return 'iOS_stable'
        else:
            return 'unknown_stable'

    def _log_fingerprint_event(
        self,
        fingerprint_id: str,
        event_type: str,
        ip_address: Optional[str],
        user_agent: Optional[str],
        additional_data: Optional[Dict[str, Any]] = None
    ):
        """تسجيل أحداث البصمة باستخدام الخدمة الموحدة"""
        try:
            history_service = self._get_history_service()

            # تحديد نوع الحدث وتسجيله
            if event_type == "created":
                history_service.log_fingerprint_stored(
                    fingerprint_id=fingerprint_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    is_new_fingerprint=True,
                    additional_data=additional_data
                )
            elif event_type == "accessed":
                history_service.log_device_access(
                    fingerprint_id=fingerprint_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    additional_data=additional_data
                )
            else:
                # للأحداث الأخرى، استخدم الطريقة العامة
                history_service._log_event(
                    fingerprint_id=fingerprint_id,
                    event_type=event_type,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    additional_data=additional_data
                )

        except Exception as e:
            logger.warning(f"فشل في تسجيل حدث البصمة: {e}")

    def _normalize_ip(self, ip: str) -> str:
        """تطبيع عنوان IP"""
        if not ip:
            return "127.0.0.1"
        
        # إزالة المنافذ
        if ':' in ip and not ip.startswith('['):
            ip = ip.split(':')[0]
        
        # معالجة IPv6
        if ip.startswith('::ffff:'):
            ip = ip[7:]
        
        return ip.strip()

    def _is_main_server(self, ip: str) -> bool:
        """تحديد ما إذا كان الجهاز هو الخادم الرئيسي"""
        try:
            # استخدام url_manager للحصول على قائمة IPs الخادم الرئيسي
            from utils.url_manager import url_manager

            if url_manager.is_main_server_ip(ip):
                logger.debug(f"✅ تم التعرف على الخادم الرئيسي: {ip}")
                return True

            # فحص إضافي من قاعدة البيانات
            try:
                from database.session import get_db
                from models.device_security import ApprovedDevice
                from sqlalchemy import select

                db = next(get_db())
                stmt = select(ApprovedDevice).where(
                    ApprovedDevice.client_ip == ip,
                    ApprovedDevice.device_id == 'main_server_primary',
                    ApprovedDevice.is_active == True
                )
                device = db.execute(stmt).scalar_one_or_none()

                if device:
                    logger.debug(f"✅ تم التعرف على الخادم الرئيسي من قاعدة البيانات: {ip}")
                    return True
            except Exception:
                pass

        except Exception as e:
            logger.debug(f"خطأ في فحص IP الخادم: {e}")

        return False

    def _generate_main_server_id(
        self,
        normalized_ip: str,
        user_agent: Optional[str],
        device_info: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """إنشاء معرف للخادم الرئيسي"""
        try:
            # الحصول على معلومات الجهاز الفعلية
            if device_info is None:
                device_info = {
                    'hostname': socket.gethostname(),
                    'fingerprint': 'main_server_primary'
                }

            # إنشاء معرف مستقر للخادم الرئيسي
            fingerprint = device_info.get('fingerprint', 'main_server_primary')
            hostname = device_info.get('hostname', socket.gethostname())

            # استخدام معلومات ثابتة للخادم الرئيسي
            server_signature = f"main_server_{hostname}_{fingerprint}"
            server_hash = hashlib.sha256(server_signature.encode()).hexdigest()
            device_id = f"main_server_{server_hash[:12]}"

            return {
                'device_id': device_id,
                'device_type': 'main_server',
                'is_main_server': True,
                'device_info': device_info,
                'fingerprint': fingerprint,
                'hostname': hostname,
                'ip_address': normalized_ip
            }

        except Exception as e:
            logger.error(f"خطأ في إنشاء معرف الخادم الرئيسي: {e}")
            # للخادم الرئيسي، نعيد معرف افتراضي آمن
            return {
                'device_id': 'main_server_primary',
                'device_type': 'main_server',
                'is_main_server': True,
                'has_advanced_fingerprint': True,
                'ip_address': normalized_ip,
                'user_agent': user_agent,
                'error_recovery_reason': f'خطأ في معالجة بصمة الخادم الرئيسي: {str(e)}'
            }

    def _generate_remote_device_id(
        self,
        normalized_ip: str,
        user_agent: Optional[str],
        request_headers: Optional[Dict[str, str]]
    ) -> Dict[str, Any]:
        """إنشاء معرف للجهاز البعيد"""
        try:
            # فحص إضافي للتأكد من أن هذا ليس الخادم الرئيسي
            if self._is_main_server(normalized_ip):
                logger.info(f"🔄 إعادة توجيه للخادم الرئيسي: {normalized_ip}")
                return self._generate_main_server_id(normalized_ip, user_agent, None)

            # استخدام البصمة المتقدمة فقط للأجهزة البعيدة
            if request_headers:
                hardware_fp = request_headers.get('x-device-hardware')
                storage_fp = request_headers.get('x-device-storage')

                if hardware_fp and storage_fp:
                    # استخدام البصمة المتقدمة
                    fingerprint_id = self.create_fingerprint_id(hardware_fp, storage_fp)

                    return {
                        'device_id': fingerprint_id,
                        'device_type': 'remote_device',
                        'is_main_server': False,
                        'has_advanced_fingerprint': True,
                        'hardware_fingerprint': hardware_fp,
                        'storage_fingerprint': storage_fp,
                        'ip_address': normalized_ip,
                        'user_agent': user_agent
                    }

            # إذا لم تتوفر البصمة المتقدمة، رفض الجهاز
            logger.warning(f"🚫 جهاز بعيد بدون بصمة متقدمة: {normalized_ip}")
            return {
                'device_id': None,
                'device_type': 'rejected',
                'is_main_server': False,
                'has_advanced_fingerprint': False,
                'ip_address': normalized_ip,
                'user_agent': user_agent,
                'rejection_reason': 'البصمة المتقدمة مطلوبة للأجهزة البعيدة'
            }

        except Exception as e:
            logger.error(f"خطأ في إنشاء معرف الجهاز البعيد: {e}")
            # رفض الجهاز في حالة الخطأ - البصمة المتقدمة مطلوبة
            return {
                'device_id': None,
                'device_type': 'error',
                'is_main_server': False,
                'has_advanced_fingerprint': False,
                'ip_address': normalized_ip,
                'user_agent': user_agent,
                'rejection_reason': f'خطأ في معالجة البصمة: {str(e)}'
            }



    def clear_cache(self):
        """مسح الـ cache"""
        self._cache.clear()
        logger.info("تم مسح cache البصمات")

    def get_cache_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الـ cache"""
        return {
            'cache_size': len(self._cache),
            'cached_fingerprints': list(self._cache.keys())[:10]  # أول 10 فقط
        }

    def get_all_fingerprints(self, limit: int = 100) -> List[Dict[str, Any]]:
        """الحصول على جميع البصمات النشطة"""
        try:
            from sqlalchemy import select
            stmt = select(DeviceFingerprint).limit(limit)
            fingerprints = self.db.execute(stmt).scalars().all()

            return [
                {
                    'id': fp.id,
                    'device_id': fp.device_id,
                    'hardware_fingerprint': fp.hardware_fingerprint,
                    'storage_fingerprint': fp.storage_fingerprint,
                    'system_fingerprint': fp.system_fingerprint,
                    'ip_address': fp.ip_address,
                    'user_agent': fp.user_agent,
                    'created_at': fp.created_at.isoformat() if fp.created_at else None,
                    'last_seen': fp.last_seen.isoformat() if fp.last_seen else None,
                    'access_count': fp.access_count
                }
                for fp in fingerprints
            ]
        except Exception as e:
            logger.error(f"❌ Error getting all fingerprints: {e}")
            return []

    def get_fingerprint_by_id(self, fingerprint_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على بصمة بواسطة المعرف"""
        try:
            from sqlalchemy import select
            stmt = select(DeviceFingerprint).where(DeviceFingerprint.id == fingerprint_id)
            fingerprint = self.db.execute(stmt).scalar_one_or_none()

            if fingerprint:
                return {
                    'id': fingerprint.id,
                    'device_id': fingerprint.device_id,
                    'hardware_fingerprint': fingerprint.hardware_fingerprint,
                    'storage_fingerprint': fingerprint.storage_fingerprint,
                    'system_fingerprint': fingerprint.system_fingerprint,
                    'ip_address': fingerprint.ip_address,
                    'user_agent': fingerprint.user_agent,
                    'created_at': fingerprint.created_at.isoformat() if fingerprint.created_at else None,
                    'last_seen': fingerprint.last_seen.isoformat() if fingerprint.last_seen else None,
                    'access_count': fingerprint.access_count
                }
            return None
        except Exception as e:
            logger.error(f"❌ Error getting fingerprint by ID: {e}")
            return None

    def get_fingerprint_analytics(self, device_id: str) -> Dict[str, Any]:
        """الحصول على تحليلات البصمة"""
        try:
            from sqlalchemy import select
            from datetime import datetime

            # البحث عن البصمة
            stmt = select(DeviceFingerprint).where(DeviceFingerprint.device_id == device_id)
            fingerprint = self.db.execute(stmt).scalar_one_or_none()

            if not fingerprint:
                return {'error': 'Fingerprint not found'}

            # إحصائيات أساسية
            analytics = {
                'device_id': device_id,
                'fingerprint_id': fingerprint.id,
                'created_at': fingerprint.created_at.isoformat() if fingerprint.created_at else None,
                'last_seen': fingerprint.last_seen.isoformat() if fingerprint.last_seen else None,
                'access_count': fingerprint.access_count,
                'stability_score': 95,  # نسبة ثبات افتراضية
                'risk_level': 'low',
                'device_type': 'desktop' if 'Windows' in (fingerprint.user_agent or '') else 'mobile',
                'location_consistency': True,
                'fingerprint_strength': 'strong'
            }

            # حساب عدد الأيام منذ الإنشاء
            if fingerprint.created_at:
                days_active = (datetime.now() - fingerprint.created_at).days
                analytics['days_active'] = days_active

            return analytics

        except Exception as e:
            logger.error(f"❌ Error getting fingerprint analytics: {e}")
            return {'error': str(e)}

    def validate_fingerprint_stability(self, hardware_fp: str, storage_fp: str) -> Dict[str, Any]:
        """التحقق من ثبات البصمة"""
        try:
            # البحث عن البصمة الموجودة
            existing = self.find_existing_fingerprint(hardware_fp, storage_fp)

            if existing:
                # حساب نسبة الثبات بناءً على عدد الوصولات
                stability_score = min(95, 70 + (existing.access_count * 2))

                return {
                    'is_stable': True,
                    'stability_score': stability_score,
                    'access_count': existing.access_count,
                    'first_seen': existing.created_at.isoformat() if hasattr(existing.created_at, 'isoformat') else None,
                    'last_seen': existing.last_seen.isoformat() if hasattr(existing.last_seen, 'isoformat') else None,
                    'consistency': 'high' if stability_score > 85 else 'medium'
                }
            else:
                return {
                    'is_stable': False,
                    'stability_score': 0,
                    'access_count': 0,
                    'first_seen': None,
                    'last_seen': None,
                    'consistency': 'new'
                }

        except Exception as e:
            logger.error(f"❌ Error validating fingerprint stability: {e}")
            return {'error': str(e)}

    def delete_fingerprint(self, fingerprint_id: str) -> bool:
        """حذف بصمة"""
        try:
            from sqlalchemy import select
            stmt = select(DeviceFingerprint).where(DeviceFingerprint.id == fingerprint_id)
            fingerprint = self.db.execute(stmt).scalar_one_or_none()

            if fingerprint:
                self.db.delete(fingerprint)
                self.db.commit()
                logger.info(f"✅ Deleted fingerprint: {fingerprint_id}")
                return True
            return False

        except Exception as e:
            logger.error(f"❌ Error deleting fingerprint: {e}")
            self.db.rollback()
            return False


# إنشاء instance مشترك للاستخدام العام
from database.session import get_db

def get_unified_fingerprint_service(db: Optional[Session] = None) -> UnifiedFingerprintService:
    """الحصول على instance من خدمة البصمة الموحدة"""
    if db is None:
        db = next(get_db())
    return UnifiedFingerprintService(db)

# Instance مشترك للاستخدام السريع
unified_fingerprint_service = get_unified_fingerprint_service()
