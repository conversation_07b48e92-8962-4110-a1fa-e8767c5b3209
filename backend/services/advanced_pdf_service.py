"""
خدمة تصدير PDF المتقدمة للخلفية
تستخدم WeasyPrint لإنشاء ملفات PDF عالية الجودة مع دعم كامل للغة العربية
تطبق مبادئ البرمجة الكائنية وتدمج مع خدمات النظام الموجودة

<AUTHOR> Team
@version 1.0.0
@date 2025-06-30
"""

import os
import tempfile
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import json
import logging

try:
    from weasyprint import HTML, CSS
    from weasyprint.text.fonts import FontConfiguration
    WEASYPRINT_AVAILABLE = True
except ImportError:
    WEASYPRINT_AVAILABLE = False
    logging.warning("WeasyPrint not available. PDF export will use fallback method.")

try:
    from reportlab.lib.pagesizes import A4, letter
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    logging.warning("ReportLab not available. PDF export will use fallback method.")

from utils.datetime_utils import get_tripoli_now, format_date


class AdvancedPDFService:
    """
    خدمة تصدير PDF متقدمة مع دعم كامل للغة العربية
    تستخدم WeasyPrint كخيار أول و ReportLab كخيار بديل
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        تهيئة خدمة PDF المتقدمة
        
        Args:
            config: إعدادات الخدمة
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # إعدادات افتراضية
        self.default_config = {
            'page_size': 'A4',
            'orientation': 'portrait',
            'margins': {'top': 20, 'right': 20, 'bottom': 20, 'left': 20},
            'font_family': 'Arial, sans-serif',
            'font_size': 12,
            'title_font_size': 18,
            'header_font_size': 14,
            'include_header': True,
            'include_footer': True,
            'company_name': 'نظام SmartPOS',
            'theme': 'light'
        }
        
        # دمج الإعدادات
        self.settings = {**self.default_config, **self.config}
        
        # تحديد المحرك المتاح
        self.engine = self._determine_engine()
        
        self.logger.info(f"تم تهيئة خدمة PDF المتقدمة باستخدام محرك: {self.engine}")

    def _safe_format_datetime(self, dt_value, default: str = 'غير معروف') -> str:
        """تنسيق آمن للتاريخ والوقت"""
        if dt_value is None:
            return default

        if hasattr(dt_value, 'strftime'):
            try:
                return dt_value.strftime('%Y-%m-%d %H:%M:%S')
            except Exception as e:
                self.logger.warning(f"خطأ في تنسيق التاريخ: {e}")
                return default

        if isinstance(dt_value, str):
            try:
                from datetime import datetime
                # محاولة تحويل النص إلى تاريخ
                if 'T' in dt_value:
                    parsed_date = datetime.fromisoformat(dt_value.replace('Z', '+00:00'))
                else:
                    parsed_date = datetime.strptime(dt_value, '%Y-%m-%d %H:%M:%S')
                return parsed_date.strftime('%Y-%m-%d %H:%M:%S')
            except Exception as e:
                self.logger.warning(f"خطأ في تحويل النص إلى تاريخ: {e}")
                return str(dt_value)

        return str(dt_value) if dt_value else default

    def _determine_engine(self) -> str:
        """تحديد محرك PDF المتاح"""
        if WEASYPRINT_AVAILABLE:
            return 'weasyprint'
        elif REPORTLAB_AVAILABLE:
            return 'reportlab'
        else:
            return 'fallback'
    
    def export_device_access_log(
        self,
        device_info: Dict[str, Any],
        access_history: List[Dict[str, Any]],
        output_path: Optional[str] = None
    ) -> str:
        """
        تصدير سجل وصول الجهاز إلى PDF
        
        Args:
            device_info: معلومات الجهاز
            access_history: سجل الوصول
            output_path: مسار الملف (اختياري)
            
        Returns:
            مسار الملف المُصدر
        """
        try:
            # إنشاء اسم الملف (بدون أحرف عربية لتجنب مشاكل encoding)
            if not output_path:
                timestamp = get_tripoli_now().strftime('%Y%m%d_%H%M%S')
                device_name = device_info.get('hostname', 'unknown_device')

                # تحويل الأحرف العربية إلى نص آمن
                # تحويل الأحرف العربية إلى نص إنجليزي آمن
                import re
                import unicodedata

                if 'جهاز' in device_name:
                    if 'Windows' in device_name:
                        device_name = 'Windows_Device'
                    elif 'Mac' in device_name:
                        device_name = 'Mac_Device'
                    elif 'Linux' in device_name:
                        device_name = 'Linux_Device'
                    elif 'Android' in device_name:
                        device_name = 'Android_Device'
                    elif 'iOS' in device_name:
                        device_name = 'iOS_Device'
                    else:
                        device_name = 'Unknown_Device'
                elif 'متصفح' in device_name:
                    if 'Chrome' in device_name:
                        device_name = 'Chrome_Browser'
                    elif 'Firefox' in device_name:
                        device_name = 'Firefox_Browser'
                    elif 'Safari' in device_name:
                        device_name = 'Safari_Browser'
                    else:
                        device_name = 'Web_Browser'
                elif device_name == 'غير معروف':
                    device_name = 'unknown_device'
                else:
                    # إزالة الأحرف غير ASCII وتحويل إلى ASCII آمن
                    try:
                        # تحويل Unicode إلى ASCII
                        device_name = unicodedata.normalize('NFKD', device_name)
                        device_name = device_name.encode('ascii', 'ignore').decode('ascii')
                        # إزالة الأحرف غير الآمنة
                        device_name = re.sub(r'[^\w\-_.]', '_', device_name)
                        if not device_name or device_name == '_':
                            device_name = 'unknown_device'
                    except:
                        device_name = 'unknown_device'

                device_name = device_name.replace(' ', '_').replace('/', '_')
                filename = f"device_access_log_{device_name}_{timestamp}.pdf"
                output_path = os.path.join(tempfile.gettempdir(), filename)

            # التأكد من أن output_path هو string
            final_output_path: str = str(output_path)
            
            # تحضير البيانات
            title = f"سجل وصول الجهاز - {device_info.get('hostname', 'غير معروف')}"
            
            # إحصائيات سريعة
            total_access = len([h for h in access_history if h.get('event_type') in ['device_access', 'accessed', 'fingerprint_updated']])
            total_created = len([h for h in access_history if h.get('event_type') in ['fingerprint_created', 'created']])
            
            additional_info = {
                'معلومات الجهاز': {
                    'اسم الجهاز': device_info.get('hostname', 'غير معروف'),
                    'عنوان IP': device_info.get('client_ip', 'غير معروف'),
                    'نوع الجهاز': device_info.get('device_type', 'غير معروف'),
                    'آخر وصول': self._safe_format_datetime(device_info.get('last_access'))
                },
                'الإحصائيات': {
                    'إجمالي مرات الوصول': total_access,
                    'مرات الإنشاء': total_created,
                    'إجمالي السجلات': len(access_history)
                }
            }
            
            # تصدير حسب المحرك المتاح
            if self.engine == 'weasyprint':
                return self._export_with_weasyprint(title, access_history, additional_info, final_output_path)
            elif self.engine == 'reportlab':
                return self._export_with_reportlab(title, access_history, additional_info, final_output_path)
            else:
                return self._export_fallback(title, access_history, additional_info, final_output_path)
                
        except Exception as e:
            self.logger.error(f"خطأ في تصدير سجل وصول الجهاز: {e}")
            raise Exception(f"فشل في تصدير PDF: {str(e)}")
    
    def _export_with_weasyprint(
        self,
        title: str,
        data: List[Dict[str, Any]],
        additional_info: Dict[str, Any],
        output_path: str
    ) -> str:
        """تصدير PDF باستخدام WeasyPrint"""
        try:
            # إنشاء HTML مع دعم RTL والعربية
            html_content = self._generate_html_content(title, data, additional_info)
            
            # إنشاء CSS مع دعم الخطوط العربية
            css_content = self._generate_css_content()
            
            # تكوين الخطوط
            font_config = FontConfiguration()
            
            # إنشاء PDF
            html_doc = HTML(string=html_content)
            css_doc = CSS(string=css_content, font_config=font_config)
            
            html_doc.write_pdf(output_path, stylesheets=[css_doc], font_config=font_config)
            
            self.logger.info(f"تم إنشاء PDF بنجاح باستخدام WeasyPrint: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"خطأ في WeasyPrint: {e}")
            # التراجع إلى ReportLab
            if REPORTLAB_AVAILABLE:
                return self._export_with_reportlab(title, data, additional_info, output_path)
            else:
                raise Exception(f"فشل في تصدير PDF مع WeasyPrint: {str(e)}")
    
    def _export_with_reportlab(
        self,
        title: str,
        data: List[Dict[str, Any]],
        additional_info: Dict[str, Any],
        output_path: str
    ) -> str:
        """تصدير PDF باستخدام ReportLab"""
        try:
            # إنشاء مستند PDF
            doc = SimpleDocTemplate(
                output_path,
                pagesize=A4,
                rightMargin=self.settings['margins']['right'],
                leftMargin=self.settings['margins']['left'],
                topMargin=self.settings['margins']['top'],
                bottomMargin=self.settings['margins']['bottom']
            )
            
            # إعداد الأنماط
            styles = getSampleStyleSheet()
            
            # نمط العنوان الرئيسي
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=self.settings['title_font_size'],
                spaceAfter=20,
                alignment=1,  # وسط
                fontName='Helvetica-Bold'
            )
            
            # نمط النص العادي
            normal_style = ParagraphStyle(
                'CustomNormal',
                parent=styles['Normal'],
                fontSize=self.settings['font_size'],
                spaceAfter=10,
                fontName='Helvetica'
            )
            
            # بناء المحتوى
            story = []
            
            # العنوان
            story.append(Paragraph(title, title_style))
            story.append(Spacer(1, 20))
            
            # المعلومات الإضافية
            if additional_info:
                for section_title, section_data in additional_info.items():
                    story.append(Paragraph(f"<b>{section_title}</b>", normal_style))
                    
                    if isinstance(section_data, dict):
                        for key, value in section_data.items():
                            story.append(Paragraph(f"{key}: {value}", normal_style))
                    
                    story.append(Spacer(1, 10))
            
            # جدول البيانات
            if data:
                # تحضير بيانات الجدول
                table_data = [['التاريخ والوقت', 'نوع الحدث', 'عنوان IP', 'تفاصيل إضافية']]
                
                for entry in data:
                    row = [
                        format_date(entry.get('created_at'), '%Y-%m-%d %H:%M:%S') if entry.get('created_at') else 'غير محدد',
                        self._get_event_type_arabic(entry.get('event_type', '')),
                        entry.get('ip_address', 'غير معروف'),
                        entry.get('user_agent', '')[:50] + '...' if len(entry.get('user_agent', '')) > 50 else entry.get('user_agent', '')
                    ]
                    table_data.append(row)
                
                # إنشاء الجدول
                table = Table(table_data, colWidths=[2*inch, 1.5*inch, 1.5*inch, 2*inch])
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('FONTSIZE', (0, 1), (-1, -1), 8),
                    ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey])
                ]))
                
                story.append(table)
            
            # إضافة الفوتر
            story.append(Spacer(1, 30))
            footer_text = f"تم إنشاء التقرير في: {get_tripoli_now().strftime('%Y-%m-%d %H:%M:%S')}"
            story.append(Paragraph(footer_text, normal_style))
            
            # بناء PDF
            doc.build(story)
            
            self.logger.info(f"تم إنشاء PDF بنجاح باستخدام ReportLab: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"خطأ في ReportLab: {e}")
            return self._export_fallback(title, data, additional_info, output_path)

    def _export_fallback(
        self,
        title: str,
        data: List[Dict[str, Any]],
        additional_info: Dict[str, Any],
        output_path: str
    ) -> str:
        """طريقة احتياطية لتصدير PDF (JSON + HTML)"""
        try:
            # إنشاء ملف JSON مع البيانات
            json_data = {
                'title': title,
                'generated_at': get_tripoli_now().strftime('%Y-%m-%d %H:%M:%S'),
                'additional_info': additional_info,
                'data': data,
                'note': 'تم إنشاء هذا الملف كبديل لـ PDF. يمكن تحويله لاحقاً.'
            }

            # حفظ كملف JSON
            json_path = output_path.replace('.pdf', '.json')
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2, default=str)

            self.logger.warning(f"تم إنشاء ملف JSON كبديل: {json_path}")
            return json_path

        except Exception as e:
            self.logger.error(f"فشل في الطريقة الاحتياطية: {e}")
            raise Exception(f"فشل في جميع طرق التصدير: {str(e)}")

    def _generate_html_content(
        self,
        title: str,
        data: List[Dict[str, Any]],
        additional_info: Dict[str, Any]
    ) -> str:
        """إنشاء محتوى HTML مع دعم RTL والعربية"""

        # بناء المعلومات الإضافية (معلومات الجهاز)
        additional_sections = ""
        if additional_info:
            for section_title, section_data in additional_info.items():
                if isinstance(section_data, dict):
                    additional_sections += '<table class="info-table">'
                    for key, value in section_data.items():
                        additional_sections += f"""
                        <tr>
                            <td class="info-label">{key}</td>
                            <td class="info-value">{value}</td>
                        </tr>
                        """
                    additional_sections += '</table>'

        # بناء جدول البيانات
        table_rows = ""
        if data:
            table_rows = """
            <table class="log-table">
                <thead>
                    <tr>
                        <th>التاريخ والوقت</th>
                        <th>نوع الحدث</th>
                        <th>عنوان IP</th>
                        <th>تفاصيل المتصفح</th>
                    </tr>
                </thead>
                <tbody>
            """

            for entry in data:
                table_rows += f"""
                    <tr>
                        <td>{self._safe_format_datetime(entry.get('created_at'), 'غير محدد')}</td>
                        <td><span class="event-type">{self._get_event_type_arabic(entry.get('event_type', ''))}</span></td>
                        <td>{entry.get('ip_address', 'غير معروف')}</td>
                        <td class="user-agent">{entry.get('user_agent', '')[:80]}{'...' if len(entry.get('user_agent', '')) > 80 else ''}</td>
                    </tr>
                """

            table_rows += """
                </tbody>
            </table>
            """

        html_content = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تقرير سجل وصول الجهاز</title>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>تقرير سجل وصول الجهاز</h1>
                    <p>نظام SmartPOS</p>
                    <p>تاريخ الإنشاء: {get_tripoli_now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>

                <div class="device-info">
                    <h2>معلومات الجهاز</h2>
                    {additional_sections}
                </div>

                <div class="access-log">
                    <h2>سجل الوصول ({len(data)} سجل)</h2>
                    {table_rows if data else '<p class="no-data">لا توجد سجلات وصول متاحة</p>'}
                </div>

                <div class="footer">
                    <p>تم إنشاء هذا التقرير تلقائياً بواسطة نظام SmartPOS</p>
                </div>
            </div>
        </body>
        </html>
        """

        return html_content

    def _generate_css_content(self) -> str:
        """إنشاء CSS بسيط وعملي"""
        return """
        @import url('https://fonts.googleapis.com/css2?family=almarai:wght@400;600&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'almarai', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            direction: rtl;
            text-align: right;
            font-size: 14px;
            background: white;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 30px;
        }

        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .header p {
            color: #666;
            font-size: 14px;
            margin: 5px 0;
        }

        .device-info {
            margin-bottom: 30px;
        }

        .device-info h2 {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 8px;
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .info-table td {
            padding: 8px 12px;
            border: 1px solid #ddd;
            font-size: 14px;
        }

        .info-label {
            background: #f5f5f5;
            font-weight: 600;
            width: 30%;
        }

        .info-value {
            background: white;
        }

        .access-log h2 {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 8px;
        }

        .log-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .log-table th {
            background: #f5f5f5;
            color: #333;
            padding: 12px 8px;
            text-align: right;
            font-weight: 600;
            border: 1px solid #ddd;
            font-size: 14px;
        }

        .log-table td {
            padding: 10px 8px;
            border: 1px solid #ddd;
            font-size: 13px;
            text-align: right;
        }

        .log-table tr:nth-child(even) {
            background: #f9f9f9;
        }

        .event-type {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .no-data {
            text-align: center;
            padding: 40px;
            color: #666;
            font-style: italic;
        }

        .footer {
            text-align: center;
            border-top: 1px solid #ddd;
            padding-top: 20px;
            margin-top: 30px;
            color: #666;
            font-size: 12px;
        }

        .user-agent {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        @media print {
            .container {
                padding: 20px;
            }
            body {
                font-size: 12px;
            }
        }

        @page {
            size: A4;
            margin: 2cm;
        """

    def _get_event_type_arabic(self, event_type: str) -> str:
        """تحويل نوع الحدث إلى العربية"""
        event_types = {
            'fingerprint_created': 'إنشاء البصمة',
            'device_access': 'وصول الجهاز',
            'fingerprint_updated': 'تحديث البصمة',
            'device_approved': 'موافقة على الجهاز',
            'device_blocked': 'حظر الجهاز',
            'created': 'إنشاء',
            'accessed': 'وصول',
            'updated': 'تحديث'
        }
        return event_types.get(event_type, event_type)

    def _get_event_type_class(self, event_type: str) -> str:
        """تحديد فئة CSS لنوع الحدث"""
        if event_type in ['fingerprint_created', 'created']:
            return 'created'
        elif event_type in ['device_access', 'accessed']:
            return 'access'
        elif event_type in ['fingerprint_updated', 'updated']:
            return 'updated'
        elif event_type == 'device_approved':
            return 'approved'
        elif event_type == 'device_blocked':
            return 'blocked'
        else:
            return 'access'
