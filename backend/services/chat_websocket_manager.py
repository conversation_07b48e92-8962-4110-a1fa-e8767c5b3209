"""
خدمة إدارة WebSocket للمحادثة الفورية
تدعم الاتصالات المتعددة وإدارة حالة المستخدمين
"""

import asyncio
import json
import logging
from typing import Dict, Optional, Any, List
from datetime import datetime
from fastapi import WebSocket
from sqlalchemy import select, update

from database.session import get_db
from models.user import User
from models.chat_message import UserOnlineStatus
from utils.datetime_utils import get_tripoli_now

logger = logging.getLogger(__name__)

class ChatWebSocketManager:
    """
    مدير WebSocket للمحادثة الفورية
    يدير الاتصالات والرسائل والحالات
    """

    def __init__(self):
        # قاموس الاتصالات النشطة: {user_id: {websocket, last_ping, connection_id}}
        self.active_connections: Dict[int, Dict[str, Any]] = {}

        # قاموس معكوس للبحث السريع: {websocket: user_id}
        self.websocket_to_user: Dict[WebSocket, int] = {}

        # مهمة تنظيف الاتصالات المنقطعة
        self.cleanup_task: Optional[asyncio.Task] = None

        # إعدادات محسنة
        self.heartbeat_interval = 45  # زيادة إلى 45 ثانية
        self.connection_timeout = 90  # زيادة إلى 90 ثانية
        self.max_missed_pings = 2  # عدد ping المفقودة المسموح بها

        # إحصائيات
        self.connection_stats = {
            'total_connections': 0,
            'active_connections': 0,
            'disconnections': 0,
            'reconnections': 0
        }

    async def connect(self, websocket: WebSocket, user_id: int) -> bool:
        """
        إضافة اتصال WebSocket جديد للمستخدم مع تحسينات لمنع الاتصالات المتكررة
        """
        try:
            # التحقق من وجود اتصال نشط للمستخدم
            if user_id in self.active_connections:
                existing_connection = self.active_connections[user_id]
                old_websocket = existing_connection['websocket']

                # التحقق من حالة الاتصال القديم
                try:
                    # محاولة إرسال ping للتحقق من حالة الاتصال
                    await old_websocket.send_text('{"type": "connection_check"}')
                    # إذا نجح الإرسال، فالاتصال لا يزال نشطاً
                    logger.warning(f"⚠️ المستخدم {user_id} لديه اتصال نشط بالفعل")
                    await websocket.close(code=4003, reason="اتصال موجود بالفعل")
                    return False
                except:
                    # الاتصال القديم معطل، يمكن استبداله
                    logger.info(f"🔄 استبدال اتصال معطل للمستخدم {user_id}")
                    await self._cleanup_user_connection(user_id)

            # قبول الاتصال الجديد
            await websocket.accept()

            # إنشاء معرف فريد للاتصال
            connection_id = f"{user_id}_{datetime.now().timestamp()}"

            # إضافة الاتصال الجديد
            self.active_connections[user_id] = {
                'websocket': websocket,
                'last_ping': datetime.now(),
                'connected_at': datetime.now(),
                'connection_id': connection_id,
                'missed_pings': 0,
                'is_healthy': True
            }

            self.websocket_to_user[websocket] = user_id

            # تحديث الإحصائيات
            self.connection_stats['total_connections'] += 1
            self.connection_stats['active_connections'] = len(self.active_connections)

            # تحديث حالة المستخدم في قاعدة البيانات
            await self._update_user_online_status(user_id, True, websocket)

            # إشعار المستخدمين الآخرين بالاتصال
            await self._broadcast_user_status_change(user_id, True)

            # بدء مهمة التنظيف إذا لم تكن تعمل
            if not self.cleanup_task or self.cleanup_task.done():
                self.cleanup_task = asyncio.create_task(self._cleanup_connections())

            logger.info(f"✅ تم اتصال المستخدم {user_id} بنجاح (ID: {connection_id})")
            return True

        except Exception as e:
            logger.error(f"خطأ في اتصال المستخدم {user_id}: {e}")
            return False

    async def disconnect(self, websocket: WebSocket):
        """
        إزالة اتصال WebSocket مع تحسينات
        """
        user_id = self.websocket_to_user.get(websocket)
        if not user_id:
            return

        try:
            # الحصول على معلومات الاتصال قبل الحذف
            connection_info = self.active_connections.get(user_id)
            connection_id = connection_info.get('connection_id', 'unknown') if connection_info else 'unknown'

            # إزالة من القواميس
            self.active_connections.pop(user_id, None)
            self.websocket_to_user.pop(websocket, None)

            # تحديث الإحصائيات
            self.connection_stats['disconnections'] += 1
            self.connection_stats['active_connections'] = len(self.active_connections)

            # تحديث حالة المستخدم في قاعدة البيانات
            await self._update_user_online_status(user_id, False)

            # إشعار المستخدمين الآخرين بقطع الاتصال
            await self._broadcast_user_status_change(user_id, False)

            logger.info(f"🔌 تم قطع اتصال المستخدم {user_id} (ID: {connection_id})")

        except Exception as e:
            logger.error(f"خطأ في قطع اتصال المستخدم {user_id}: {e}")

    async def _cleanup_user_connection(self, user_id: int):
        """
        تنظيف اتصال مستخدم محدد
        """
        try:
            if user_id in self.active_connections:
                old_websocket = self.active_connections[user_id]['websocket']
                try:
                    await old_websocket.close(code=4004, reason="استبدال الاتصال")
                except:
                    pass
                # إزالة من القواميس
                self.active_connections.pop(user_id, None)
                self.websocket_to_user.pop(old_websocket, None)

                logger.info(f"🧹 تم تنظيف اتصال المستخدم {user_id}")
        except Exception as e:
            logger.error(f"خطأ في تنظيف اتصال المستخدم {user_id}: {e}")

    async def send_message_to_user(self, user_id: int, message: Dict[str, Any]) -> bool:
        """
        إرسال رسالة لمستخدم محدد
        """
        if user_id not in self.active_connections:
            logger.debug(f"المستخدم {user_id} غير متصل")
            return False
        
        try:
            websocket = self.active_connections[user_id]['websocket']
            message_json = json.dumps(message, ensure_ascii=False, default=str)
            await websocket.send_text(message_json)
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إرسال رسالة للمستخدم {user_id}: {e}")
            # إزالة الاتصال المعطل
            await self.disconnect(self.active_connections[user_id]['websocket'])
            return False

    async def broadcast_to_all(self, message: Dict[str, Any], exclude_user_id: Optional[int] = None):
        """
        إرسال رسالة لجميع المستخدمين المتصلين
        """
        message_json = json.dumps(message, ensure_ascii=False, default=str)
        disconnected_users = []
        
        for user_id, connection_info in self.active_connections.items():
            if exclude_user_id and user_id == exclude_user_id:
                continue
                
            try:
                websocket = connection_info['websocket']
                await websocket.send_text(message_json)
                
            except Exception as e:
                logger.warning(f"فشل في إرسال رسالة للمستخدم {user_id}: {e}")
                disconnected_users.append(websocket)
        
        # إزالة الاتصالات المعطلة
        for websocket in disconnected_users:
            await self.disconnect(websocket)

    async def handle_message(self, websocket: WebSocket, message_data: str):
        """
        معالجة الرسائل الواردة من العملاء مع تحسينات
        """
        user_id = self.websocket_to_user.get(websocket)
        if not user_id:
            logger.warning("تم استقبال رسالة من websocket غير مسجل")
            return

        try:
            data = json.loads(message_data)
            message_type = data.get('type', '')

            if message_type == 'ping':
                # تحديث وقت آخر ping وإعادة تعيين عداد ping المفقودة
                if user_id in self.active_connections:
                    connection_info = self.active_connections[user_id]
                    connection_info['last_ping'] = datetime.now()
                    connection_info['missed_pings'] = 0
                    connection_info['is_healthy'] = True

                # إرسال pong مع معلومات إضافية
                await websocket.send_text(json.dumps({
                    'type': 'pong',
                    'timestamp': datetime.now().isoformat(),
                    'server_time': datetime.now().isoformat(),
                    'connection_healthy': True
                }, ensure_ascii=False))

            elif message_type == 'connection_check':
                # فحص حالة الاتصال
                await websocket.send_text(json.dumps({
                    'type': 'connection_status',
                    'status': 'healthy',
                    'timestamp': datetime.now().isoformat()
                }, ensure_ascii=False))

            elif message_type == 'typing':
                # إشعار بالكتابة
                receiver_id = data.get('receiver_id')
                if receiver_id and receiver_id != user_id:  # منع إرسال إشعار للنفس
                    await self.send_message_to_user(receiver_id, {
                        'type': 'user_typing',
                        'user_id': user_id,
                        'timestamp': datetime.now().isoformat()
                    })

            elif message_type == 'stop_typing':
                # إشعار بتوقف الكتابة
                receiver_id = data.get('receiver_id')
                if receiver_id and receiver_id != user_id:  # منع إرسال إشعار للنفس
                    await self.send_message_to_user(receiver_id, {
                        'type': 'user_stop_typing',
                        'user_id': user_id,
                        'timestamp': datetime.now().isoformat()
                    })
            else:
                logger.debug(f"نوع رسالة غير معروف: {message_type} من المستخدم {user_id}")

        except json.JSONDecodeError:
            logger.warning(f"رسالة JSON غير صحيحة من المستخدم {user_id}: {message_data[:100]}")
        except Exception as e:
            logger.error(f"خطأ في معالجة رسالة من المستخدم {user_id}: {e}")

    async def _update_user_online_status(self, user_id: int, is_online: bool, websocket: Optional[WebSocket] = None):
        """
        تحديث حالة اتصال المستخدم في قاعدة البيانات
        """
        try:
            db = next(get_db())
            current_time = get_tripoli_now()
            
            # البحث عن حالة المستخدم الحالية
            status_stmt = select(UserOnlineStatus).where(UserOnlineStatus.user_id == user_id)
            user_status = db.execute(status_stmt).scalar_one_or_none()
            
            if user_status:
                # تحديث الحالة الموجودة
                user_status.is_online = is_online
                user_status.last_seen = current_time
                user_status.updated_at = current_time
                if websocket:
                    user_status.socket_id = str(id(websocket))
                else:
                    user_status.socket_id = None
            else:
                # إنشاء حالة جديدة
                user_status = UserOnlineStatus(
                    user_id=user_id,
                    is_online=is_online,
                    last_seen=current_time,
                    socket_id=str(id(websocket)) if websocket else None
                )
                db.add(user_status)
            
            # تحديث حقل is_online في جدول المستخدمين أيضاً
            user_update_stmt = update(User).where(User.id == user_id).values(
                is_online=is_online,
                last_seen=current_time
            )
            db.execute(user_update_stmt)
            
            db.commit()
            
        except Exception as e:
            logger.error(f"خطأ في تحديث حالة المستخدم {user_id}: {e}")
            if 'db' in locals():
                db.rollback()

    async def _broadcast_user_status_change(self, user_id: int, is_online: bool):
        """
        إشعار جميع المستخدمين بتغيير حالة مستخدم
        """
        try:
            # جلب معلومات المستخدم
            db = next(get_db())
            user_stmt = select(User).where(User.id == user_id)
            user = db.execute(user_stmt).scalar_one_or_none()
            
            if user:
                message = {
                    'type': 'user_status_change',
                    'user_id': user_id,
                    'username': user.username,
                    'full_name': user.full_name,
                    'is_online': is_online,
                    'timestamp': datetime.now().isoformat()
                }
                
                await self.broadcast_to_all(message, exclude_user_id=user_id)
                
        except Exception as e:
            logger.error(f"خطأ في إشعار تغيير حالة المستخدم {user_id}: {e}")

    async def _cleanup_connections(self):
        """
        تنظيف الاتصالات المنقطعة دورياً مع تحسينات
        """
        logger.info("🧹 بدء مهمة تنظيف الاتصالات")

        while True:
            try:
                await asyncio.sleep(self.heartbeat_interval)

                current_time = datetime.now()
                disconnected_users = []
                unhealthy_users = []

                # فحص جميع الاتصالات
                for user_id, connection_info in list(self.active_connections.items()):
                    last_ping = connection_info['last_ping']
                    time_since_ping = (current_time - last_ping).total_seconds()

                    if time_since_ping > self.connection_timeout:
                        # الاتصال منقطع تماماً
                        disconnected_users.append((user_id, connection_info['websocket']))
                        logger.warning(f"⏰ انتهت مهلة الاتصال للمستخدم {user_id} ({time_since_ping:.1f}s)")

                    elif time_since_ping > self.heartbeat_interval:
                        # الاتصال قد يكون غير صحي، زيادة عداد ping المفقودة
                        connection_info['missed_pings'] += 1

                        if connection_info['missed_pings'] >= self.max_missed_pings:
                            unhealthy_users.append((user_id, connection_info['websocket']))
                            logger.warning(f"💔 اتصال غير صحي للمستخدم {user_id} (missed pings: {connection_info['missed_pings']})")
                        else:
                            connection_info['is_healthy'] = False
                            logger.debug(f"⚠️ ping مفقود للمستخدم {user_id} (#{connection_info['missed_pings']})")

                # إزالة الاتصالات المنقطعة
                for user_id, websocket in disconnected_users:
                    await self.disconnect(websocket)

                # إزالة الاتصالات غير الصحية
                for user_id, websocket in unhealthy_users:
                    try:
                        await websocket.close(code=4005, reason="اتصال غير صحي")
                    except:
                        pass
                    await self.disconnect(websocket)

                # طباعة إحصائيات دورية
                if len(self.active_connections) > 0:
                    logger.debug(f"📊 الاتصالات النشطة: {len(self.active_connections)}")

            except Exception as e:
                logger.error(f"خطأ في تنظيف الاتصالات: {e}")
                # انتظار قصير قبل المحاولة مرة أخرى
                await asyncio.sleep(5)

    def get_online_users(self) -> List[int]:
        """
        الحصول على قائمة المستخدمين المتصلين
        """
        return list(self.active_connections.keys())

    def get_connection_count(self) -> int:
        """
        الحصول على عدد الاتصالات النشطة
        """
        return len(self.active_connections)

    def get_connection_stats(self) -> Dict[str, Any]:
        """
        الحصول على إحصائيات مفصلة للاتصالات
        """
        healthy_connections = sum(1 for conn in self.active_connections.values() if conn.get('is_healthy', True))

        return {
            **self.connection_stats,
            'active_connections': len(self.active_connections),
            'healthy_connections': healthy_connections,
            'unhealthy_connections': len(self.active_connections) - healthy_connections,
            'cleanup_task_running': self.cleanup_task and not self.cleanup_task.done() if self.cleanup_task else False
        }

    def is_user_online(self, user_id: int) -> bool:
        """
        التحقق من حالة اتصال المستخدم
        """
        if user_id not in self.active_connections:
            return False

        connection_info = self.active_connections[user_id]
        return connection_info.get('is_healthy', True)

# إنشاء مثيل عام من المدير
chat_websocket_manager = ChatWebSocketManager()
