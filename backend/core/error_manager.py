"""
مدير الأخطاء المركزي - SmartPOS
نظام شامل لإدارة ومعالجة الأخطاء في النظام
"""

import logging
import traceback
from datetime import datetime
from typing import Dict, Any, Optional, List, Callable
from enum import Enum
from dataclasses import dataclass

# from config.app_config import app_config  # سيتم استخدامه لاحقاً

logger = logging.getLogger(__name__)


class ErrorLevel(Enum):
    """مستويات الأخطاء"""
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class ErrorCategory(Enum):
    """فئات الأخطاء"""
    SYSTEM = "SYSTEM"
    DATABASE = "DATABASE"
    NETWORK = "NETWORK"
    SECURITY = "SECURITY"
    VALIDATION = "VALIDATION"
    BUSINESS = "BUSINESS"
    EXTERNAL = "EXTERNAL"


@dataclass
class ErrorInfo:
    """معلومات الخطأ"""
    level: ErrorLevel
    category: ErrorCategory
    message: str
    id: Optional[str] = None
    details: Optional[str] = None
    stack_trace: Optional[str] = None
    timestamp: Optional[datetime] = None
    source: Optional[str] = None
    user_id: Optional[str] = None
    request_id: Optional[str] = None
    resolved: bool = False
    resolution_notes: Optional[str] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.id is None:
            timestamp_val = self.timestamp if self.timestamp else datetime.now()
            self.id = f"err_{int(timestamp_val.timestamp())}"


class ErrorHandler:
    """معالج الأخطاء الأساسي"""
    
    def __init__(self, category: ErrorCategory):
        self.category = category
        self.logger = logging.getLogger(f"ErrorHandler.{category.value}")
    
    def handle(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> ErrorInfo:
        """معالجة الخطأ"""
        error_info = ErrorInfo(
            id=None,
            level=self._determine_level(error),
            category=self.category,
            message=str(error),
            details=self._extract_details(error, context),
            stack_trace=traceback.format_exc(),
            source=context.get('source') if context else None,
            user_id=context.get('user_id') if context else None,
            request_id=context.get('request_id') if context else None
        )
        
        # تسجيل الخطأ
        self._log_error(error_info)
        
        return error_info
    
    def _determine_level(self, error: Exception) -> ErrorLevel:
        """تحديد مستوى الخطأ"""
        if isinstance(error, (SystemExit, KeyboardInterrupt)):
            return ErrorLevel.CRITICAL
        elif isinstance(error, (ConnectionError, TimeoutError)):
            return ErrorLevel.ERROR
        elif isinstance(error, (ValueError, TypeError)):
            return ErrorLevel.WARNING
        else:
            return ErrorLevel.ERROR
    
    def _extract_details(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """استخراج تفاصيل الخطأ"""
        details = []
        
        if hasattr(error, '__dict__'):
            details.append(f"Error attributes: {error.__dict__}")
        
        if context:
            details.append(f"Context: {context}")
        
        return "\n".join(details) if details else None
    
    def _log_error(self, error_info: ErrorInfo):
        """تسجيل الخطأ في السجلات"""
        log_message = f"[{error_info.category.value}] {error_info.message}"
        
        if error_info.level == ErrorLevel.CRITICAL:
            self.logger.critical(log_message)
        elif error_info.level == ErrorLevel.ERROR:
            self.logger.error(log_message)
        elif error_info.level == ErrorLevel.WARNING:
            self.logger.warning(log_message)
        else:
            self.logger.info(log_message)


class ErrorManager:
    """
    مدير الأخطاء المركزي
    يدير جميع أنواع الأخطاء في النظام
    """
    
    def __init__(self):
        self.handlers: Dict[ErrorCategory, ErrorHandler] = {}
        self.error_history: List[ErrorInfo] = []
        self.error_callbacks: Dict[ErrorLevel, List[Callable]] = {
            level: [] for level in ErrorLevel
        }
        self.max_history_size = 1000
        self.logger = logging.getLogger(__name__)
        
        # تهيئة المعالجات
        self._initialize_handlers()
    
    def _initialize_handlers(self):
        """تهيئة معالجات الأخطاء"""
        for category in ErrorCategory:
            self.handlers[category] = ErrorHandler(category)
    
    def handle_error(
        self, 
        error: Exception, 
        category: ErrorCategory = ErrorCategory.SYSTEM,
        context: Optional[Dict[str, Any]] = None
    ) -> ErrorInfo:
        """معالجة خطأ"""
        try:
            handler = self.handlers.get(category)
            if not handler:
                handler = self.handlers[ErrorCategory.SYSTEM]
            
            error_info = handler.handle(error, context)
            
            # إضافة للتاريخ
            self._add_to_history(error_info)
            
            # تنفيذ callbacks
            self._execute_callbacks(error_info)
            
            return error_info
            
        except Exception as e:
            # خطأ في معالجة الخطأ!
            self.logger.critical(f"Error in error handling: {e}")
            return ErrorInfo(
                id=None,
                level=ErrorLevel.CRITICAL,
                category=ErrorCategory.SYSTEM,
                message=f"Error handler failed: {str(e)}"
            )
    
    def _add_to_history(self, error_info: ErrorInfo):
        """إضافة الخطأ للتاريخ"""
        self.error_history.append(error_info)
        
        # تنظيف التاريخ إذا تجاوز الحد الأقصى
        if len(self.error_history) > self.max_history_size:
            self.error_history = self.error_history[-self.max_history_size:]
    
    def _execute_callbacks(self, error_info: ErrorInfo):
        """تنفيذ callbacks للخطأ"""
        callbacks = self.error_callbacks.get(error_info.level, [])
        for callback in callbacks:
            try:
                callback(error_info)
            except Exception as e:
                self.logger.error(f"Error in callback execution: {e}")
    
    def register_callback(self, level: ErrorLevel, callback: Callable):
        """تسجيل callback للخطأ"""
        if level not in self.error_callbacks:
            self.error_callbacks[level] = []
        self.error_callbacks[level].append(callback)
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأخطاء"""
        if not self.error_history:
            return {
                "total_errors": 0,
                "by_level": {},
                "by_category": {},
                "recent_errors": 0
            }
        
        # إحصائيات حسب المستوى
        by_level = {}
        for level in ErrorLevel:
            by_level[level.value] = len([e for e in self.error_history if e.level == level])
        
        # إحصائيات حسب الفئة
        by_category = {}
        for category in ErrorCategory:
            by_category[category.value] = len([e for e in self.error_history if e.category == category])
        
        # الأخطاء الحديثة (آخر ساعة)
        recent_threshold = datetime.now().timestamp() - 3600
        recent_errors = len([
            e for e in self.error_history
            if e.timestamp and e.timestamp.timestamp() > recent_threshold
        ])
        
        return {
            "total_errors": len(self.error_history),
            "by_level": by_level,
            "by_category": by_category,
            "recent_errors": recent_errors,
            "last_error": self.error_history[-1].timestamp.isoformat() if self.error_history and self.error_history[-1].timestamp else None
        }
    
    def get_recent_errors(self, limit: int = 50) -> List[Dict[str, Any]]:
        """الحصول على الأخطاء الحديثة"""
        recent = self.error_history[-limit:] if self.error_history else []
        return [
            {
                "id": error.id,
                "level": error.level.value,
                "category": error.category.value,
                "message": error.message,
                "timestamp": error.timestamp.isoformat() if error.timestamp else None,
                "resolved": error.resolved
            }
            for error in reversed(recent)
        ]
    
    def resolve_error(self, error_id: str, resolution_notes: Optional[str] = None) -> bool:
        """حل خطأ معين"""
        for error in self.error_history:
            if error.id == error_id:
                error.resolved = True
                error.resolution_notes = resolution_notes
                self.logger.info(f"Error {error_id} marked as resolved")
                return True
        return False
    
    def clear_history(self):
        """مسح تاريخ الأخطاء"""
        self.error_history.clear()
        self.logger.info("Error history cleared")


# إنشاء instance عامة لمدير الأخطاء
error_manager = ErrorManager()


# دوال مساعدة للاستخدام السهل
def handle_error(
    error: Exception, 
    category: ErrorCategory = ErrorCategory.SYSTEM,
    context: Optional[Dict[str, Any]] = None
) -> ErrorInfo:
    """دالة مساعدة لمعالجة الأخطاء"""
    return error_manager.handle_error(error, category, context)


def register_error_callback(level: ErrorLevel, callback: Callable):
    """دالة مساعدة لتسجيل callback"""
    error_manager.register_callback(level, callback)


def get_error_stats() -> Dict[str, Any]:
    """دالة مساعدة للحصول على إحصائيات الأخطاء"""
    return error_manager.get_error_statistics()


# Decorator لمعالجة الأخطاء تلقائياً
def handle_exceptions(category: ErrorCategory = ErrorCategory.SYSTEM):
    """Decorator لمعالجة الأخطاء تلقائياً"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_manager.handle_error(e, category, {
                    'function': func.__name__,
                    'args': str(args)[:100],
                    'kwargs': str(kwargs)[:100]
                })
                raise
        return wrapper
    return decorator
