"""
مدير الأداء الموحد - SmartPOS
نظام شامل لمراقبة وتحسين أداء النظام بمبادئ البرمجة الكائنية
"""

import time
import psutil
import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
from collections import deque
from sqlalchemy.orm import Session
from sqlalchemy import text

# from config.app_config import app_config  # سيتم استخدامه لاحقاً
from database.session import get_db
# from core.service_manager import BaseService, service  # سيتم استخدامه لاحقاً

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetric:
    """مقياس أداء فردي"""
    name: str
    value: float
    unit: str
    status: str = "normal"  # normal, warning, critical
    timestamp: datetime = field(default_factory=datetime.now)
    threshold_warning: float = 70.0
    threshold_critical: float = 90.0
    
    def __post_init__(self):
        """تحديد حالة المقياس بناءً على القيمة"""
        if self.value >= self.threshold_critical:
            self.status = "critical"
        elif self.value >= self.threshold_warning:
            self.status = "warning"
        else:
            self.status = "normal"


@dataclass
class SystemPerformance:
    """أداء النظام الشامل"""
    cpu: PerformanceMetric
    memory: PerformanceMetric
    disk: PerformanceMetric
    database: PerformanceMetric
    network: PerformanceMetric
    overall: PerformanceMetric
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        return {
            "cpu": {
                "value": self.cpu.value,
                "status": self.cpu.status,
                "unit": self.cpu.unit
            },
            "memory": {
                "value": self.memory.value,
                "status": self.memory.status,
                "unit": self.memory.unit
            },
            "disk": {
                "value": self.disk.value,
                "status": self.disk.status,
                "unit": self.disk.unit
            },
            "database": {
                "value": self.database.value,
                "status": self.database.status,
                "unit": self.database.unit
            },
            "network": {
                "value": self.network.value,
                "status": self.network.status,
                "unit": self.network.unit
            },
            "overall": {
                "value": self.overall.value,
                "status": self.overall.status,
                "unit": self.overall.unit
            },
            "timestamp": self.timestamp.isoformat()
        }


class PerformanceCollector(ABC):
    """كلاس أساسي لجمع مقاييس الأداء"""
    
    @abstractmethod
    async def collect(self) -> PerformanceMetric:
        """جمع مقياس الأداء"""
        pass


class CPUCollector(PerformanceCollector):
    """جامع مقاييس المعالج"""
    
    def __init__(self):
        self.last_measurement = None
        
    async def collect(self) -> PerformanceMetric:
        """جمع مقياس استخدام المعالج"""
        try:
            # قياس لمدة ثانية واحدة للحصول على قراءة دقيقة
            cpu_percent = psutil.cpu_percent(interval=1)

            # التأكد من أن القيمة رقم
            if isinstance(cpu_percent, (int, float)):
                cpu_value = float(cpu_percent)
            else:
                cpu_value = 0.0

            return PerformanceMetric(
                name="CPU Usage",
                value=cpu_value,
                unit="%",
                threshold_warning=70.0,
                threshold_critical=90.0
            )
        except Exception as e:
            logger.error(f"Error collecting CPU metrics: {e}")
            return PerformanceMetric(
                name="CPU Usage",
                value=0.0,
                unit="%",
                status="error"
            )


class MemoryCollector(PerformanceCollector):
    """جامع مقاييس الذاكرة"""
    
    async def collect(self) -> PerformanceMetric:
        """جمع مقياس استخدام الذاكرة"""
        try:
            memory = psutil.virtual_memory()
            
            return PerformanceMetric(
                name="Memory Usage",
                value=memory.percent,
                unit="%",
                threshold_warning=80.0,
                threshold_critical=95.0
            )
        except Exception as e:
            logger.error(f"Error collecting memory metrics: {e}")
            return PerformanceMetric(
                name="Memory Usage",
                value=0.0,
                unit="%",
                status="error"
            )


class DiskCollector(PerformanceCollector):
    """جامع مقاييس القرص"""
    
    async def collect(self) -> PerformanceMetric:
        """جمع مقياس استخدام القرص"""
        try:
            disk = psutil.disk_usage('/')
            
            return PerformanceMetric(
                name="Disk Usage",
                value=disk.percent,
                unit="%",
                threshold_warning=80.0,
                threshold_critical=95.0
            )
        except Exception as e:
            logger.error(f"Error collecting disk metrics: {e}")
            return PerformanceMetric(
                name="Disk Usage",
                value=0.0,
                unit="%",
                status="error"
            )


class DatabaseCollector(PerformanceCollector):
    """جامع مقاييس قاعدة البيانات"""
    
    def __init__(self):
        self.db = None
        
    async def collect(self) -> PerformanceMetric:
        """جمع مقياس أداء قاعدة البيانات"""
        try:
            if not self.db:
                self.db = next(get_db())
            
            # قياس وقت استعلام بسيط
            start_time = time.time()
            _ = self.db.execute(text("SELECT 1")).fetchone()
            query_time = (time.time() - start_time) * 1000  # بالميلي ثانية
            
            # تحويل وقت الاستجابة إلى نسبة مئوية (0-100)
            # أقل من 10ms = ممتاز (0-30%)
            # 10-50ms = جيد (30-70%)
            # أكثر من 50ms = بطيء (70-100%)
            if query_time < 10:
                performance_percent = (query_time / 10) * 30
            elif query_time < 50:
                performance_percent = 30 + ((query_time - 10) / 40) * 40
            else:
                performance_percent = min(70 + ((query_time - 50) / 50) * 30, 100)
            
            return PerformanceMetric(
                name="Database Response",
                value=performance_percent,
                unit="%",
                threshold_warning=70.0,
                threshold_critical=90.0
            )
        except Exception as e:
            logger.error(f"Error collecting database metrics: {e}")
            return PerformanceMetric(
                name="Database Response",
                value=100.0,  # أسوأ حالة
                unit="%",
                status="error"
            )


class NetworkCollector(PerformanceCollector):
    """جامع مقاييس الشبكة"""

    def __init__(self):
        self.last_stats = None
        self.last_time = None

    async def collect(self) -> PerformanceMetric:
        """جمع مقياس أداء الشبكة"""
        try:
            # تبسيط جمع مقاييس الشبكة
            # سنستخدم مقياس بسيط بناءً على عدد الاتصالات
            connections = len(psutil.net_connections())

            # تحويل عدد الاتصالات إلى نسبة مئوية (افتراض أن 100 اتصال = 100%)
            network_percent = min((connections / 100) * 100, 100)

            return PerformanceMetric(
                name="Network Activity",
                value=network_percent,
                unit="%",
                threshold_warning=70.0,
                threshold_critical=90.0
            )
        except Exception as e:
            logger.error(f"Error collecting network metrics: {e}")
            return PerformanceMetric(
                name="Network Activity",
                value=0.0,
                unit="%",
                status="error"
            )


class PerformanceManager:
    """
    مدير الأداء الموحد
    يجمع ويحلل جميع مقاييس الأداء في النظام
    """
    
    def __init__(self, db: Optional[Session] = None):
        self.db = db or next(get_db())
        self.logger = logging.getLogger(__name__)
        
        # جامعي المقاييس
        self.collectors = {
            "cpu": CPUCollector(),
            "memory": MemoryCollector(),
            "disk": DiskCollector(),
            "database": DatabaseCollector(),
            "network": NetworkCollector()
        }
        
        # تاريخ الأداء
        self.performance_history: deque = deque(maxlen=100)
        
        # إعدادات المراقبة
        self.monitoring_interval = 30  # ثانية
        self.is_monitoring = False
        self.monitoring_task = None
        
        # callbacks للتنبيهات
        self.alert_callbacks: List[Callable] = []
        
        # إحصائيات الطلبات
        self.request_stats = {
            'total_requests': 0,
            'slow_requests': 0,
            'failed_requests': 0,
            'average_response_time': 0.0,
            'peak_concurrent_requests': 0,
            'last_reset': datetime.now()
        }
        
        self._initialized = False
    
    def initialize(self) -> bool:
        """تهيئة مدير الأداء"""
        try:
            # تهيئة جامعي المقاييس
            for collector in self.collectors.values():
                if hasattr(collector, 'initialize'):
                    collector.initialize()
            
            self._initialized = True
            self.logger.info("✅ تم تهيئة مدير الأداء بنجاح")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ فشل في تهيئة مدير الأداء: {e}")
            return False
    
    def cleanup(self) -> bool:
        """تنظيف موارد مدير الأداء"""
        try:
            # إيقاف المراقبة
            if self.is_monitoring:
                self.stop_monitoring()
            
            # تنظيف جامعي المقاييس
            for collector in self.collectors.values():
                if hasattr(collector, 'cleanup'):
                    collector.cleanup()
            
            self._initialized = False
            self.logger.info("✅ تم تنظيف مدير الأداء بنجاح")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ فشل في تنظيف مدير الأداء: {e}")
            return False

    async def collect_performance_data(self) -> SystemPerformance:
        """جمع جميع مقاييس الأداء"""
        try:
            # جمع المقاييس من جميع الجامعين
            metrics = {}
            for name, collector in self.collectors.items():
                metrics[name] = await collector.collect()

            # حساب الأداء العام
            overall_value = sum(metric.value for metric in metrics.values()) / len(metrics)
            overall_status = "normal"

            # تحديد الحالة العامة
            critical_count = sum(1 for metric in metrics.values() if metric.status == "critical")
            warning_count = sum(1 for metric in metrics.values() if metric.status == "warning")

            if critical_count > 0:
                overall_status = "critical"
            elif warning_count > 0:
                overall_status = "warning"

            overall_metric = PerformanceMetric(
                name="Overall Performance",
                value=overall_value,
                unit="%",
                status=overall_status
            )

            # إنشاء كائن الأداء الشامل
            performance = SystemPerformance(
                cpu=metrics["cpu"],
                memory=metrics["memory"],
                disk=metrics["disk"],
                database=metrics["database"],
                network=metrics["network"],
                overall=overall_metric
            )

            # إضافة للتاريخ
            self.performance_history.append(performance)

            # فحص التنبيهات
            await self._check_alerts(performance)

            return performance

        except Exception as e:
            self.logger.error(f"❌ خطأ في جمع بيانات الأداء: {e}")
            # إرجاع بيانات افتراضية في حالة الخطأ
            return self._get_default_performance()

    def _get_default_performance(self) -> SystemPerformance:
        """الحصول على بيانات أداء افتراضية في حالة الخطأ"""
        default_metric = PerformanceMetric(
            name="Unknown",
            value=0.0,
            unit="%",
            status="error"
        )

        return SystemPerformance(
            cpu=default_metric,
            memory=default_metric,
            disk=default_metric,
            database=default_metric,
            network=default_metric,
            overall=default_metric
        )

    async def _check_alerts(self, performance: SystemPerformance):
        """فحص التنبيهات بناءً على الأداء"""
        try:
            critical_metrics = []
            warning_metrics = []

            for metric_name in ["cpu", "memory", "disk", "database", "network"]:
                metric = getattr(performance, metric_name)
                if metric.status == "critical":
                    critical_metrics.append(metric)
                elif metric.status == "warning":
                    warning_metrics.append(metric)

            # تنفيذ callbacks للتنبيهات
            if critical_metrics or warning_metrics:
                for callback in self.alert_callbacks:
                    try:
                        await callback(performance, critical_metrics, warning_metrics)
                    except Exception as e:
                        self.logger.error(f"خطأ في تنفيذ callback التنبيه: {e}")

        except Exception as e:
            self.logger.error(f"خطأ في فحص التنبيهات: {e}")

    def start_monitoring(self, interval: Optional[int] = None):
        """بدء مراقبة الأداء"""
        if self.is_monitoring:
            self.logger.warning("المراقبة قيد التشغيل بالفعل")
            return

        if interval:
            self.monitoring_interval = interval

        self.is_monitoring = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        self.logger.info(f"✅ تم بدء مراقبة الأداء (كل {self.monitoring_interval} ثانية)")

    def stop_monitoring(self):
        """إيقاف مراقبة الأداء"""
        if not self.is_monitoring:
            return

        self.is_monitoring = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            self.monitoring_task = None

        self.logger.info("✅ تم إيقاف مراقبة الأداء")

    async def _monitoring_loop(self):
        """حلقة المراقبة الرئيسية"""
        while self.is_monitoring:
            try:
                await self.collect_performance_data()
                await asyncio.sleep(self.monitoring_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"خطأ في حلقة المراقبة: {e}")
                await asyncio.sleep(5)  # انتظار قصير قبل المحاولة مرة أخرى

    def add_alert_callback(self, callback: Callable):
        """إضافة callback للتنبيهات"""
        self.alert_callbacks.append(callback)

    def remove_alert_callback(self, callback: Callable):
        """إزالة callback للتنبيهات"""
        if callback in self.alert_callbacks:
            self.alert_callbacks.remove(callback)

    def get_performance_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """الحصول على تاريخ الأداء"""
        history = list(self.performance_history)[-limit:]
        return [perf.to_dict() for perf in history]

    def get_performance_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأداء"""
        if not self.performance_history:
            return {"error": "لا توجد بيانات أداء متاحة"}

        recent_performance = list(self.performance_history)[-10:]  # آخر 10 قياسات

        # حساب المتوسطات
        avg_cpu = sum(p.cpu.value for p in recent_performance) / len(recent_performance)
        avg_memory = sum(p.memory.value for p in recent_performance) / len(recent_performance)
        avg_disk = sum(p.disk.value for p in recent_performance) / len(recent_performance)
        avg_database = sum(p.database.value for p in recent_performance) / len(recent_performance)
        avg_network = sum(p.network.value for p in recent_performance) / len(recent_performance)
        avg_overall = sum(p.overall.value for p in recent_performance) / len(recent_performance)

        # حساب الحد الأقصى
        max_cpu = max(p.cpu.value for p in recent_performance)
        max_memory = max(p.memory.value for p in recent_performance)
        max_disk = max(p.disk.value for p in recent_performance)

        return {
            "averages": {
                "cpu": round(avg_cpu, 2),
                "memory": round(avg_memory, 2),
                "disk": round(avg_disk, 2),
                "database": round(avg_database, 2),
                "network": round(avg_network, 2),
                "overall": round(avg_overall, 2)
            },
            "peaks": {
                "cpu": round(max_cpu, 2),
                "memory": round(max_memory, 2),
                "disk": round(max_disk, 2)
            },
            "monitoring": {
                "is_active": self.is_monitoring,
                "interval": self.monitoring_interval,
                "data_points": len(self.performance_history)
            },
            "requests": self.request_stats,
            "timestamp": datetime.now().isoformat()
        }

    def update_request_stats(self, response_time: float, status_code: int):
        """تحديث إحصائيات الطلبات"""
        self.request_stats['total_requests'] += 1

        if response_time > 5.0:  # أكثر من 5 ثوان
            self.request_stats['slow_requests'] += 1

        if status_code >= 400:
            self.request_stats['failed_requests'] += 1

        # حساب متوسط وقت الاستجابة
        total = self.request_stats['total_requests']
        current_avg = self.request_stats['average_response_time']
        self.request_stats['average_response_time'] = (
            (current_avg * (total - 1) + response_time) / total
        )

    def reset_request_stats(self):
        """إعادة تعيين إحصائيات الطلبات"""
        self.request_stats = {
            'total_requests': 0,
            'slow_requests': 0,
            'failed_requests': 0,
            'average_response_time': 0.0,
            'peak_concurrent_requests': 0,
            'last_reset': datetime.now()
        }
        self.logger.info("✅ تم إعادة تعيين إحصائيات الطلبات")


# إنشاء instance عامة لمدير الأداء
performance_manager = PerformanceManager()


# دوال مساعدة للاستخدام السهل
async def get_current_performance() -> SystemPerformance:
    """دالة مساعدة للحصول على الأداء الحالي"""
    return await performance_manager.collect_performance_data()


def get_performance_stats() -> Dict[str, Any]:
    """دالة مساعدة للحصول على إحصائيات الأداء"""
    return performance_manager.get_performance_statistics()


def start_performance_monitoring(interval: int = 30):
    """دالة مساعدة لبدء مراقبة الأداء"""
    performance_manager.start_monitoring(interval)


def stop_performance_monitoring():
    """دالة مساعدة لإيقاف مراقبة الأداء"""
    performance_manager.stop_monitoring()
