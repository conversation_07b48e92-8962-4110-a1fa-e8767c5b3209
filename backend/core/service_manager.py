"""
مدير الخدمات المركزي - SmartPOS
يدير جميع خدمات النظام بطريقة منظمة وكائنية
"""

import logging
from typing import Dict, Any, Optional, Type
from abc import ABC, abstractmethod
from sqlalchemy.orm import Session

# from config.app_config import app_config  # سيتم استخدامه لاحقاً
from database.session import get_db

logger = logging.getLogger(__name__)


class BaseService(ABC):
    """
    الكلاس الأساسي لجميع الخدمات
    """
    
    def __init__(self, db: Optional[Session] = None):
        self.db = db or next(get_db())
        self.config = {}  # سيتم تحديثه لاحقاً
        self.logger = logging.getLogger(self.__class__.__name__)
        self._initialized = False
    
    @abstractmethod
    def initialize(self) -> bool:
        """تهيئة الخدمة"""
        pass
    
    @abstractmethod
    def cleanup(self) -> bool:
        """تنظيف موارد الخدمة"""
        pass
    
    def get_status(self) -> Dict[str, Any]:
        """الحصول على حالة الخدمة"""
        return {
            "service": self.__class__.__name__,
            "status": "active",
            "initialized": hasattr(self, '_initialized') and self._initialized
        }


class ServiceManager:
    """
    مدير الخدمات المركزي
    يدير دورة حياة جميع خدمات النظام
    """
    
    def __init__(self):
        self.services: Dict[str, BaseService] = {}
        self.service_registry: Dict[str, Type[BaseService]] = {}
        self.logger = logging.getLogger(__name__)
        self._initialized = False
    
    def register_service(self, name: str, service_class: Type[BaseService]):
        """تسجيل خدمة جديدة"""
        try:
            self.service_registry[name] = service_class
            self.logger.info(f"✅ تم تسجيل الخدمة: {name}")
        except Exception as e:
            self.logger.error(f"❌ فشل في تسجيل الخدمة {name}: {e}")
    
    def get_service(self, name: str) -> Optional[BaseService]:
        """الحصول على خدمة معينة"""
        if name in self.services:
            return self.services[name]
        
        if name in self.service_registry:
            try:
                service_class = self.service_registry[name]
                service = service_class()
                
                if service.initialize():
                    self.services[name] = service
                    self.logger.info(f"✅ تم تهيئة الخدمة: {name}")
                    return service
                else:
                    self.logger.error(f"❌ فشل في تهيئة الخدمة: {name}")
            except Exception as e:
                self.logger.error(f"❌ خطأ في إنشاء الخدمة {name}: {e}")
        
        return None
    
    def initialize_all_services(self) -> bool:
        """تهيئة جميع الخدمات المسجلة"""
        try:
            success_count = 0
            total_count = len(self.service_registry)
            
            for name in self.service_registry:
                if self.get_service(name):
                    success_count += 1
            
            self._initialized = True
            self.logger.info(f"✅ تم تهيئة {success_count}/{total_count} خدمة بنجاح")
            return success_count == total_count
            
        except Exception as e:
            self.logger.error(f"❌ فشل في تهيئة الخدمات: {e}")
            return False
    
    def cleanup_all_services(self) -> bool:
        """تنظيف جميع الخدمات"""
        try:
            success_count = 0
            
            for name, service in self.services.items():
                try:
                    if service.cleanup():
                        success_count += 1
                        self.logger.info(f"✅ تم تنظيف الخدمة: {name}")
                    else:
                        self.logger.warning(f"⚠️ فشل في تنظيف الخدمة: {name}")
                except Exception as e:
                    self.logger.error(f"❌ خطأ في تنظيف الخدمة {name}: {e}")
            
            self.services.clear()
            self._initialized = False
            
            self.logger.info(f"✅ تم تنظيف {success_count} خدمة")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ فشل في تنظيف الخدمات: {e}")
            return False
    
    def restart_service(self, name: str) -> bool:
        """إعادة تشغيل خدمة معينة"""
        try:
            if name in self.services:
                service = self.services[name]
                service.cleanup()
                del self.services[name]
            
            new_service = self.get_service(name)
            return new_service is not None
            
        except Exception as e:
            self.logger.error(f"❌ فشل في إعادة تشغيل الخدمة {name}: {e}")
            return False
    
    def get_all_services_status(self) -> Dict[str, Any]:
        """الحصول على حالة جميع الخدمات"""
        status = {
            "manager_initialized": self._initialized,
            "total_registered": len(self.service_registry),
            "total_active": len(self.services),
            "services": {}
        }
        
        for name, service in self.services.items():
            try:
                status["services"][name] = service.get_status()
            except Exception as e:
                status["services"][name] = {
                    "service": name,
                    "status": "error",
                    "error": str(e)
                }
        
        return status
    
    def health_check(self) -> Dict[str, Any]:
        """فحص صحة جميع الخدمات"""
        health_status = {
            "overall_health": "healthy",
            "services_count": len(self.services),
            "healthy_services": 0,
            "unhealthy_services": 0,
            "services": {}
        }
        
        for name, service in self.services.items():
            try:
                service_status = service.get_status()
                is_healthy = service_status.get("status") == "active"
                
                health_status["services"][name] = {
                    "healthy": is_healthy,
                    "status": service_status
                }
                
                if is_healthy:
                    health_status["healthy_services"] += 1
                else:
                    health_status["unhealthy_services"] += 1
                    
            except Exception as e:
                health_status["services"][name] = {
                    "healthy": False,
                    "error": str(e)
                }
                health_status["unhealthy_services"] += 1
        
        # تحديد الحالة العامة
        if health_status["unhealthy_services"] > 0:
            if health_status["unhealthy_services"] > health_status["healthy_services"]:
                health_status["overall_health"] = "critical"
            else:
                health_status["overall_health"] = "warning"
        
        return health_status


# إنشاء instance عامة لمدير الخدمات
service_manager = ServiceManager()


# دالة مساعدة للحصول على خدمة
def get_service(name: str) -> Optional[BaseService]:
    """دالة مساعدة للحصول على خدمة"""
    return service_manager.get_service(name)


# دالة مساعدة لتسجيل خدمة
def register_service(name: str, service_class: Type[BaseService]):
    """دالة مساعدة لتسجيل خدمة"""
    service_manager.register_service(name, service_class)


# دالة مساعدة لفحص صحة الخدمات
def health_check() -> Dict[str, Any]:
    """دالة مساعدة لفحص صحة الخدمات"""
    return service_manager.health_check()


# Decorator لتسجيل الخدمات تلقائياً
def service(name: str):
    """Decorator لتسجيل الخدمات تلقائياً"""
    def decorator(cls):
        register_service(name, cls)
        return cls
    return decorator
