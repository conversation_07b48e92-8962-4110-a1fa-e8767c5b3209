"""Add indexes for analytics performance

Revision ID: add_analytics_indexes
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_analytics_indexes'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    """Add indexes to improve analytics query performance."""
    
    # Index for sales date filtering (most common filter)
    op.create_index(
        'idx_sales_created_at',
        'sales',
        ['created_at'],
        postgresql_using='btree'
    )
    
    # Index for sale items product lookup
    op.create_index(
        'idx_sale_items_product_id',
        'sale_items',
        ['product_id'],
        postgresql_using='btree'
    )
    
    # Index for sale items sale lookup
    op.create_index(
        'idx_sale_items_sale_id',
        'sale_items',
        ['sale_id'],
        postgresql_using='btree'
    )
    
    # Composite index for sales date and sale items join
    op.create_index(
        'idx_sales_date_items',
        'sales',
        ['created_at', 'id'],
        postgresql_using='btree'
    )
    
    # Index for products active status
    op.create_index(
        'idx_products_is_active',
        'products',
        ['is_active'],
        postgresql_using='btree'
    )
    
    # Index for products category filtering
    op.create_index(
        'idx_products_category',
        'products',
        ['category'],
        postgresql_using='btree'
    )
    
    # Composite index for products active and category
    op.create_index(
        'idx_products_active_category',
        'products',
        ['is_active', 'category'],
        postgresql_using='btree'
    )
    
    # Index for products quantity (for stock analysis)
    op.create_index(
        'idx_products_quantity',
        'products',
        ['quantity'],
        postgresql_using='btree'
    )
    
    # Index for products created_at (for days in stock calculation)
    op.create_index(
        'idx_products_created_at',
        'products',
        ['created_at'],
        postgresql_using='btree'
    )


def downgrade():
    """Remove the analytics indexes."""
    
    op.drop_index('idx_products_created_at', table_name='products')
    op.drop_index('idx_products_quantity', table_name='products')
    op.drop_index('idx_products_active_category', table_name='products')
    op.drop_index('idx_products_category', table_name='products')
    op.drop_index('idx_products_is_active', table_name='products')
    op.drop_index('idx_sales_date_items', table_name='sales')
    op.drop_index('idx_sale_items_sale_id', table_name='sale_items')
    op.drop_index('idx_sale_items_product_id', table_name='sale_items')
    op.drop_index('idx_sales_created_at', table_name='sales')
