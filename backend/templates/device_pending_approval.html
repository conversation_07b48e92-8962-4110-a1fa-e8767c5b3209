<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>في انتظار الموافقة - نظام نقاط البيع الذكي</title>
    <!-- استخدام Tailwind CSS المحلي بدلاً من CDN للإنتاج -->
    <style>
        /* Tailwind CSS classes - نسخة مبسطة للإنتاج */
        .min-h-screen { min-height: 100vh; }
        .flex { display: flex; }
        .items-center { align-items: center; }
        .justify-center { justify-content: center; }
        .p-4 { padding: 1rem; }
        .max-w-2xl { max-width: 42rem; }
        .w-full { width: 100%; }
        .text-center { text-align: center; }
        .mb-8 { margin-bottom: 2rem; }
        .mb-6 { margin-bottom: 1.5rem; }
        .mb-4 { margin-bottom: 1rem; }
        .mb-2 { margin-bottom: 0.5rem; }
        .ml-2 { margin-left: 0.5rem; }
        .mr-2 { margin-right: 0.5rem; }
        .inline-flex { display: inline-flex; }
        .w-28 { width: 7rem; }
        .h-28 { height: 7rem; }
        .w-12 { width: 3rem; }
        .h-12 { height: 3rem; }
        .w-8 { width: 2rem; }
        .h-8 { height: 2rem; }
        .w-6 { width: 1.5rem; }
        .h-6 { height: 1.5rem; }
        .bg-white { background-color: white; }
        .rounded-full { border-radius: 9999px; }
        .rounded-2xl { border-radius: 1rem; }
        .rounded-xl { border-radius: 0.75rem; }
        .rounded-lg { border-radius: 0.5rem; }
        .shadow-2xl { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }
        .shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }
        .shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
        .p-8 { padding: 2rem; }
        .p-6 { padding: 1.5rem; }
        .p-4 { padding: 1rem; }
        .p-3 { padding: 0.75rem; }
        .border { border-width: 1px; }
        .text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
        .text-2xl { font-size: 1.5rem; line-height: 2rem; }
        .text-xl { font-size: 1.25rem; line-height: 1.75rem; }
        .text-lg { font-size: 1.125rem; line-height: 1.75rem; }
        .text-sm { font-size: 0.875rem; line-height: 1.25rem; }
        .text-xs { font-size: 0.75rem; line-height: 1rem; }
        .font-bold { font-weight: 700; }
        .font-semibold { font-weight: 600; }
        .font-medium { font-weight: 500; }
        .text-white { color: white; }
        .text-gray-800 { color: #1f2937; }
        .text-gray-600 { color: #4b5563; }
        .text-gray-700 { color: #374151; }
        .text-blue-600 { color: #2563eb; }
        .text-blue-700 { color: #1d4ed8; }
        .text-blue-800 { color: #1e40af; }
        .text-amber-600 { color: #d97706; }
        .text-green-600 { color: #059669; }
        .text-purple-600 { color: #9333ea; }
        .text-purple-700 { color: #7c3aed; }
        .bg-blue-100 { background-color: #dbeafe; }
        .bg-amber-100 { background-color: #fef3c7; }
        .bg-green-100 { background-color: #dcfce7; }
        .bg-purple-100 { background-color: #f3e8ff; }
        .font-mono { font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace; }
        .border-blue-100 { border-color: #dbeafe; }
        .border-white { border-color: white; }
        .drop-shadow-lg { filter: drop-shadow(0 10px 8px rgba(0, 0, 0, 0.04)) drop-shadow(0 4px 3px rgba(0, 0, 0, 0.1)); }
        .drop-shadow { filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1)) drop-shadow(0 1px 1px rgba(0, 0, 0, 0.06)); }
        .backdrop-blur-sm { backdrop-filter: blur(4px); }
        .leading-relaxed { line-height: 1.625; }
        .space-x-4 > :not([hidden]) ~ :not([hidden]) { margin-right: 1rem; }
        .space-x-reverse > :not([hidden]) ~ :not([hidden]) { margin-right: 1rem; margin-left: 0; }
        .flex-shrink-0 { flex-shrink: 0; }
        .flex-1 { flex: 1 1 0%; }
        .items-start { align-items: flex-start; }
        .grid { display: grid; }
        .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
        .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
        .gap-4 { gap: 1rem; }
        .space-y-4 > :not([hidden]) ~ :not([hidden]) { margin-top: 1rem; }
        .space-y-6 > :not([hidden]) ~ :not([hidden]) { margin-top: 1.5rem; }
        @media (min-width: 768px) {
            .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
        }
    </style>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=almarai:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'almarai', 'almarai', 'Arial', sans-serif;
            direction: rtl;
        }
        /* تأكد من استخدام خط almarai في جميع العناصر */
        * {
            font-family: 'almarai', 'almarai', 'Arial', sans-serif !important;
        }
        .pulse-animation {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        .fade-in {
            animation: fadeIn 0.8s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .gradient-bg {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 50%, #faf5ff 100%);
        }
        .dark .gradient-bg {
            background: linear-gradient(135deg, #1f2937 0%, #374151 50%, #1f2937 100%);
        }
        .countdown-circle {
            animation: countdown 30s linear infinite;
        }
        @keyframes countdown {
            from { stroke-dashoffset: 0; }
            to { stroke-dashoffset: 283; }
        }

        /* أيقونات SVG مخصصة محسنة */
        .icon {
            display: inline-block;
            vertical-align: middle;
            fill: none;
            stroke: currentColor;
            transition: all 0.2s ease-in-out;
        }

        .icon-sm {
            width: 0.875rem;
            height: 0.875rem;
        }

        .icon-md {
            width: 1.125rem;
            height: 1.125rem;
        }

        .icon-lg {
            width: 1.375rem;
            height: 1.375rem;
        }

        .icon-xl {
            width: 1.75rem;
            height: 1.75rem;
        }

        .icon-2xl {
            width: 2.5rem;
            height: 2.5rem;
        }

        .icon-spin {
            animation: spin 2s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* تحسينات بصرية للأيقونات */
        .icon-container {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .icon-container:hover {
            transform: scale(1.05);
        }

        /* Background Pattern */
        .bg-pattern {
            position: absolute;
            inset: 0;
            opacity: 0.05;
        }

        .bg-pattern::before {
            content: '';
            position: absolute;
            top: 2.5rem;
            left: 2.5rem;
            width: 5rem;
            height: 5rem;
            background: #3b82f6;
            border-radius: 50%;
            filter: blur(40px);
        }

        .bg-pattern::after {
            content: '';
            position: absolute;
            top: 10rem;
            right: 5rem;
            width: 8rem;
            height: 8rem;
            background: #8b5cf6;
            border-radius: 50%;
            filter: blur(40px);
        }

        .primary-gradient {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }

        .text-primary {
            background: linear-gradient(135deg, #1d4ed8 0%, #8b5cf6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center p-4 relative">
    <!-- Background Pattern -->
    <div class="bg-pattern"></div>

    <div class="max-w-2xl w-full fade-in relative z-10">
        <!-- الشعار والعنوان الرئيسي -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-20 h-20 primary-gradient rounded-2xl shadow-lg mb-4 pulse-animation">
                <!-- أيقونة درع الحماية -->
                <svg class="icon icon-2xl text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                    <path d="M9 12l2 2 4-4" stroke-width="1.5" opacity="0.7"/>
                </svg>
            </div>
            <h1 class="text-3xl font-bold text-primary mb-2">
                النظام قيد وصول جهازك
            </h1>
            <p class="text-gray-600 text-sm font-medium">
                نظام نقاط البيع الذكي محمي وآمن. جهازك قيد المراجعة حتى يتم التحقق من صلاحياتك والموافقة على وصولك للنظام.
            </p>
        </div>

        <!-- البطاقة الرئيسية -->
        <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl p-8 border border-white/20">
            <!-- رسالة الانتظار -->
            <div class="flex items-start space-x-4 space-x-reverse mb-8">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center shadow-lg">
                        <!-- أيقونة الدرع مع التحقق -->
                        <svg class="icon icon-xl text-blue-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                            <path d="M9 12l2 2 4-4" stroke-width="1.5" opacity="0.7"/>
                        </svg>
                    </div>
                </div>
                <div class="flex-1">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">
                        نظام محمي وآمن
                    </h2>
                    <p class="text-gray-600 text-lg leading-relaxed">
                        جهازك قيد المراجعة الأمنية. النظام يتحقق من صلاحياتك وسيتم منحك الوصول فور الموافقة على طلبك.
                    </p>
                </div>
            </div>

            <!-- معلومات الجهاز -->
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 mb-8 border border-blue-100 shadow-sm">
                <div class="flex items-center mb-4">
                    <!-- أيقونة المعلومات المحسنة -->
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center ml-2">
                        <svg class="icon icon-md text-blue-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"/>
                            <path d="M12 16v-4"/>
                            <path d="M12 8h.01"/>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-blue-800">
                        معلومات الجهاز قيد المراجعة
                    </h3>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div class="flex items-center p-3 bg-white/50 rounded-lg">
                        <!-- أيقونة الشبكة العامة -->
                        <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center ml-2">
                            <svg class="icon icon-sm text-blue-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="10"/>
                                <line x1="2" y1="12" x2="22" y2="12"/>
                                <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"/>
                            </svg>
                        </div>
                        <span class="font-medium text-blue-700">العنوان العام:</span>
                        <span class="text-blue-600 mr-2 font-mono text-xs" id="device-public-ip">جاري التحميل...</span>
                    </div>
                    <div class="flex items-center p-3 bg-white/50 rounded-lg">
                        <!-- أيقونة الشبكة المحلية -->
                        <div class="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center ml-2">
                            <svg class="icon icon-sm text-orange-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M1 9l2 2c4.97-4.97 13.03-4.97 18 0l2-2C16.93 2.93 7.07 2.93 1 9z"/>
                                <path d="M5 13l2 2c2.76-2.76 7.24-2.76 10 0l2-2C15.24 9.24 8.76 9.24 5 13z"/>
                                <path d="M9 17l2 2c.87-.87 2.13-.87 3 0l2-2c-1.73-1.73-4.27-1.73-6 0z"/>
                                <circle cx="12" cy="21" r="1"/>
                            </svg>
                        </div>
                        <span class="font-medium text-orange-700">العنوان المحلي:</span>
                        <span class="text-orange-600 mr-2 font-mono text-xs" id="device-local-ip">جاري التحميل...</span>
                    </div>
                    <div class="flex items-center p-3 bg-white/50 rounded-lg">
                        <!-- أيقونة الساعة المحسنة -->
                        <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center ml-2">
                            <svg class="icon icon-sm text-blue-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="10"/>
                                <polyline points="12,6 12,12 16,14"/>
                            </svg>
                        </div>
                        <span class="font-medium text-blue-700">وقت الطلب:</span>
                        <span class="text-blue-600 mr-2" id="request-time">جاري التحميل...</span>
                    </div>
                    <div class="flex items-center p-3 bg-white/50 rounded-lg">
                        <!-- أيقونة الحالة المحسنة -->
                        <div class="w-6 h-6 bg-amber-100 rounded-full flex items-center justify-center ml-2">
                            <svg class="icon icon-sm text-amber-600 icon-spin" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M21 12a9 9 0 11-6.219-8.56"/>
                            </svg>
                        </div>
                        <span class="font-medium text-blue-700">حالة الجهاز:</span>
                        <span class="text-amber-600 mr-2 font-medium">قيد المراجعة الأمنية</span>
                    </div>
                    <div class="flex items-center p-3 bg-white/50 rounded-lg">
                        <!-- أيقونة المعرف المحسنة -->
                        <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center ml-2">
                            <svg class="icon icon-sm text-blue-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
                                <line x1="16" y1="2" x2="16" y2="6"/>
                                <line x1="8" y1="2" x2="8" y2="6"/>
                                <line x1="3" y1="10" x2="21" y2="10"/>
                            </svg>
                        </div>
                        <span class="font-medium text-blue-700">معرف الطلب:</span>
                        <span class="text-blue-600 mr-2 font-mono text-xs" id="request-id">جاري التحميل...</span>
                    </div>
                    <div class="flex items-center p-3 bg-white/50 rounded-lg">
                        <!-- أيقونة البصمة -->
                        <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center ml-2">
                            <svg class="icon icon-sm text-purple-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 10a4 4 0 1 0 0-8 4 4 0 0 0 0 8z"/>
                                <path d="M12 14s-4 2-4 6h8c0-4-4-6-4-6z"/>
                            </svg>
                        </div>
                        <span class="font-medium text-purple-700">معرف الجهاز:</span>
                        <span class="text-purple-600 mr-2 font-mono text-xs" id="device-fingerprint-id">جاري التحميل...</span>
                    </div>
                </div>
            </div>

            <!-- معلومات الفحص اليدوي -->
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 mb-8 border border-blue-100 shadow-sm">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <!-- أيقونة الفحص اليدوي -->
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center ml-3">
                            <svg class="icon icon-lg text-blue-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M9 12l2 2 4-4"/>
                                <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9c2.35 0 4.48.9 6.08 2.38"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-blue-800">
                                فحص حالة المراجعة الأمنية
                            </h3>
                            <p class="text-blue-600 text-sm">
                                استخدم زر "فحص الحالة" للتحقق من حالة المراجعة الأمنية لجهازك
                            </p>
                        </div>
                    </div>
                    <div class="relative w-16 h-16">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="icon icon-xl text-blue-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 2v20m0-20l4 4m-4-4L8 6"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- زر فحص الحالة -->
            <div class="flex justify-center">
                <button
                    id="manual-check-btn"
                    class="inline-flex items-center justify-center px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                >
                    <!-- أيقونة الفحص المحسنة -->
                    <div class="w-5 h-5 bg-white/20 rounded-full flex items-center justify-center ml-2">
                        <svg class="icon icon-sm text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M9 12l2 2 4-4"/>
                            <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9c2.35 0 4.48.9 6.08 2.38"/>
                        </svg>
                    </div>
                    فحص الحالة الآن
                </button>
            </div>
        </div>

        <!-- تذييل -->
        <div class="text-center mt-8">
            <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
                <div class="flex items-center justify-center mb-2">
                    <!-- أيقونة الدرع المحسنة -->
                    <div class="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center ml-2">
                        <svg class="icon icon-sm text-white/90" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                            <circle cx="12" cy="12" r="3" fill="currentColor" opacity="0.3"/>
                        </svg>
                    </div>
                    <p class="text-white/90 text-sm font-medium">
                        نظام نقاط البيع الذكي - نظام أمان متقدم
                    </p>
                </div>
                <p class="text-white/70 text-xs">
                    رمز الحالة: DEVICE_PENDING_APPROVAL
                </p>
            </div>
        </div>
    </div>

    <script src="/static/js/datetime-service.js"></script>
    <script src="/static/js/device-pending-approval.js"></script>
</body>
</html>
