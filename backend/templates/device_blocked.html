<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تم حظر الجهاز - نظام نقاط البيع الذكي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=almarai:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'almarai', 'almarai', 'Arial', sans-serif;
            direction: rtl;
        }
        /* تأكد من استخدام خط almarai في جميع العناصر */
        * {
            font-family: 'almarai', 'almarai', 'Arial', sans-serif !important;
        }
        .shake-animation {
            animation: shake 0.5s ease-in-out;
        }
        .fade-in {
            animation: fadeIn 0.8s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        .gradient-bg-red {
            background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);
        }
    </style>
</head>
<body class="gradient-bg-red min-h-screen flex items-center justify-center p-4">
    <div class="max-w-2xl w-full fade-in">
        <!-- الشعار والعنوان الرئيسي -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-28 h-28 bg-white/20 backdrop-blur-sm rounded-full mb-6 shake-animation">
                <i class="fas fa-ban text-5xl text-white"></i>
            </div>
            <h1 class="text-4xl font-bold text-white mb-4 drop-shadow-lg">
                جهاز محظور
            </h1>
            <p class="text-xl text-white/90 drop-shadow">
                تم منع الوصول من هذا الجهاز
            </p>
        </div>

        <!-- البطاقة الرئيسية -->
        <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl p-8 border border-white/20">
            <!-- رسالة التحذير -->
            <div class="flex items-start space-x-4 space-x-reverse mb-8">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                    </div>
                </div>
                <div class="flex-1">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">
                        الوصول مرفوض
                    </h2>
                    <p class="text-gray-600 text-lg leading-relaxed">
                        تم منع جهازك من الوصول إلى نظام نقاط البيع الذكي لأسباب أمنية.
                        يرجى التواصل مع مدير النظام للحصول على المساعدة.
                    </p>
                </div>
            </div>

            <!-- معلومات الجهاز -->
            <div class="bg-gradient-to-r from-red-50 to-pink-50 rounded-xl p-6 mb-8 border border-red-100">
                <div class="flex items-center mb-4">
                    <i class="fas fa-info-circle text-red-600 text-lg ml-2"></i>
                    <h3 class="text-lg font-semibold text-red-800">
                        معلومات الجهاز المحظور
                    </h3>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div class="flex items-center">
                        <i class="fas fa-network-wired text-red-500 w-5 ml-2"></i>
                        <span class="font-medium text-red-700">عنوان IP:</span>
                        <span class="text-red-600 mr-2 font-mono" id="device-ip">جاري التحميل...</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-clock text-red-500 w-5 ml-2"></i>
                        <span class="font-medium text-red-700">وقت الحظر:</span>
                        <span class="text-red-600 mr-2" id="block-time">جاري التحميل...</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-ban text-red-500 w-5 ml-2"></i>
                        <span class="font-medium text-red-700">الحالة:</span>
                        <span class="text-red-600 mr-2 font-medium">محظور</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-hashtag text-red-500 w-5 ml-2"></i>
                        <span class="font-medium text-red-700">رمز الخطأ:</span>
                        <span class="text-red-600 mr-2 font-mono text-xs">DEVICE_BLOCKED_403</span>
                    </div>
                </div>
            </div>

            <!-- الإجراءات المقترحة -->
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 mb-8 border border-blue-100">
                <div class="flex items-center mb-4">
                    <i class="fas fa-lightbulb text-blue-600 text-lg ml-2"></i>
                    <h3 class="text-lg font-semibold text-blue-800">
                        ماذا يمكنك فعله؟
                    </h3>
                </div>
                <div class="space-y-4 text-sm">
                    <div class="flex items-start space-x-3 space-x-reverse">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-user-tie text-blue-600 text-sm"></i>
                        </div>
                        <div>
                            <p class="font-medium text-blue-800">تواصل مع مدير النظام</p>
                            <p class="text-blue-600 text-xs mt-1">اطلب إلغاء الحظر عن جهازك</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3 space-x-reverse">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-desktop text-blue-600 text-sm"></i>
                        </div>
                        <div>
                            <p class="font-medium text-blue-800">استخدم جهاز مصرح به</p>
                            <p class="text-blue-600 text-xs mt-1">جرب الوصول من جهاز آخر</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار العمل -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                    onclick="window.location.reload()"
                    class="inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                >
                    <i class="fas fa-sync-alt ml-2"></i>
                    المحاولة مرة أخرى
                </button>
                <button
                    onclick="window.history.back()"
                    class="inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                >
                    <i class="fas fa-arrow-right ml-2"></i>
                    العودة
                </button>
            </div>
        </div>

        <!-- تذييل -->
        <div class="text-center mt-8">
            <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
                <div class="flex items-center justify-center mb-2">
                    <i class="fas fa-shield-alt text-white/80 ml-2"></i>
                    <p class="text-white/90 text-sm font-medium">
                        نظام نقاط البيع الذكي - نظام أمان متقدم
                    </p>
                </div>
                <p class="text-white/70 text-xs">
                    رمز الحالة: DEVICE_BLOCKED_403
                </p>
            </div>
        </div>
    </div>

    <script src="/static/js/datetime-service.js"></script>
    <script src="/static/js/device-blocked.js"></script>
</body>
</html>
